// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// UserCredit is the model entity for the UserCredit schema.
type UserCredit struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// CurrentCredits holds the value of the "current_credits" field.
	CurrentCredits int `json:"current_credits,omitempty"`
	// TotalCredits holds the value of the "total_credits" field.
	TotalCredits int `json:"total_credits,omitempty"`
	// PlanID holds the value of the "plan_id" field.
	PlanID string `json:"plan_id,omitempty"`
	// PlanName holds the value of the "plan_name" field.
	PlanName string `json:"plan_name,omitempty"`
	// PlanExpiresAt holds the value of the "plan_expires_at" field.
	PlanExpiresAt time.Time `json:"plan_expires_at,omitempty"`
	// Status holds the value of the "status" field.
	Status usercredit.Status `json:"status,omitempty"`
	// MonthlyLimit holds the value of the "monthly_limit" field.
	MonthlyLimit int `json:"monthly_limit,omitempty"`
	// MonthlyUsed holds the value of the "monthly_used" field.
	MonthlyUsed int `json:"monthly_used,omitempty"`
	// MonthlyResetAt holds the value of the "monthly_reset_at" field.
	MonthlyResetAt time.Time `json:"monthly_reset_at,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UserCredit) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case usercredit.FieldMetadata:
			values[i] = new([]byte)
		case usercredit.FieldCurrentCredits, usercredit.FieldTotalCredits, usercredit.FieldMonthlyLimit, usercredit.FieldMonthlyUsed:
			values[i] = new(sql.NullInt64)
		case usercredit.FieldPlanID, usercredit.FieldPlanName, usercredit.FieldStatus:
			values[i] = new(sql.NullString)
		case usercredit.FieldPlanExpiresAt, usercredit.FieldMonthlyResetAt, usercredit.FieldCreatedAt, usercredit.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case usercredit.FieldID, usercredit.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UserCredit fields.
func (uc *UserCredit) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case usercredit.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				uc.ID = *value
			}
		case usercredit.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				uc.UserID = *value
			}
		case usercredit.FieldCurrentCredits:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field current_credits", values[i])
			} else if value.Valid {
				uc.CurrentCredits = int(value.Int64)
			}
		case usercredit.FieldTotalCredits:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field total_credits", values[i])
			} else if value.Valid {
				uc.TotalCredits = int(value.Int64)
			}
		case usercredit.FieldPlanID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field plan_id", values[i])
			} else if value.Valid {
				uc.PlanID = value.String
			}
		case usercredit.FieldPlanName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field plan_name", values[i])
			} else if value.Valid {
				uc.PlanName = value.String
			}
		case usercredit.FieldPlanExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field plan_expires_at", values[i])
			} else if value.Valid {
				uc.PlanExpiresAt = value.Time
			}
		case usercredit.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				uc.Status = usercredit.Status(value.String)
			}
		case usercredit.FieldMonthlyLimit:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field monthly_limit", values[i])
			} else if value.Valid {
				uc.MonthlyLimit = int(value.Int64)
			}
		case usercredit.FieldMonthlyUsed:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field monthly_used", values[i])
			} else if value.Valid {
				uc.MonthlyUsed = int(value.Int64)
			}
		case usercredit.FieldMonthlyResetAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field monthly_reset_at", values[i])
			} else if value.Valid {
				uc.MonthlyResetAt = value.Time
			}
		case usercredit.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &uc.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case usercredit.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				uc.CreatedAt = value.Time
			}
		case usercredit.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				uc.UpdatedAt = value.Time
			}
		default:
			uc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UserCredit.
// This includes values selected through modifiers, order, etc.
func (uc *UserCredit) Value(name string) (ent.Value, error) {
	return uc.selectValues.Get(name)
}

// Update returns a builder for updating this UserCredit.
// Note that you need to call UserCredit.Unwrap() before calling this method if this UserCredit
// was returned from a transaction, and the transaction was committed or rolled back.
func (uc *UserCredit) Update() *UserCreditUpdateOne {
	return NewUserCreditClient(uc.config).UpdateOne(uc)
}

// Unwrap unwraps the UserCredit entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (uc *UserCredit) Unwrap() *UserCredit {
	_tx, ok := uc.config.driver.(*txDriver)
	if !ok {
		panic("ent: UserCredit is not a transactional entity")
	}
	uc.config.driver = _tx.drv
	return uc
}

// String implements the fmt.Stringer.
func (uc *UserCredit) String() string {
	var builder strings.Builder
	builder.WriteString("UserCredit(")
	builder.WriteString(fmt.Sprintf("id=%v, ", uc.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", uc.UserID))
	builder.WriteString(", ")
	builder.WriteString("current_credits=")
	builder.WriteString(fmt.Sprintf("%v", uc.CurrentCredits))
	builder.WriteString(", ")
	builder.WriteString("total_credits=")
	builder.WriteString(fmt.Sprintf("%v", uc.TotalCredits))
	builder.WriteString(", ")
	builder.WriteString("plan_id=")
	builder.WriteString(uc.PlanID)
	builder.WriteString(", ")
	builder.WriteString("plan_name=")
	builder.WriteString(uc.PlanName)
	builder.WriteString(", ")
	builder.WriteString("plan_expires_at=")
	builder.WriteString(uc.PlanExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", uc.Status))
	builder.WriteString(", ")
	builder.WriteString("monthly_limit=")
	builder.WriteString(fmt.Sprintf("%v", uc.MonthlyLimit))
	builder.WriteString(", ")
	builder.WriteString("monthly_used=")
	builder.WriteString(fmt.Sprintf("%v", uc.MonthlyUsed))
	builder.WriteString(", ")
	builder.WriteString("monthly_reset_at=")
	builder.WriteString(uc.MonthlyResetAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", uc.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(uc.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(uc.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// UserCredits is a parsable slice of UserCredit.
type UserCredits []*UserCredit

package generation

import (
	"context"

	"github.com/social-content-ai/ai-content-service/ent"
)

// UseCase defines the content generation business logic interface
type UseCase interface {
	// Content generation
	GenerateContent(ctx context.Context, req *GenerateContentRequest) (*ent.Generation, error)
	RegenerateContent(ctx context.Context, req *RegenerateContentRequest) (*ent.Generation, error)
	
	// Generation management
	GetGeneration(ctx context.Context, id string) (*ent.Generation, error)
	ListGenerations(ctx context.Context, req *ListGenerationsRequest) ([]*ent.Generation, int, error)
	DeleteGeneration(ctx context.Context, id string) error
	
	// Generation analytics
	GetGenerationStats(ctx context.Context, userID string, dateRange *DateRange) (*GenerationStats, error)
	GetPopularTopics(ctx context.Context, limit int) ([]*TopicStats, error)
}

// GenerateContentRequest represents content generation request
type GenerateContentRequest struct {
	UserID      string            `json:"user_id" validate:"required,uuid"`
	Topic       string            `json:"topic" validate:"required,min=3,max=500"`
	ContentType string            `json:"content_type" validate:"required,oneof=educational promotional entertainment informational news tutorial"`
	Tone        string            `json:"tone" validate:"required,oneof=professional friendly humorous formal casual enthusiastic authoritative"`
	Length      string            `json:"length" validate:"required,oneof=short medium long"`
	Platforms   []string          `json:"platforms,omitempty"`
	Model       string            `json:"model" validate:"required,oneof=gpt-4 gpt-3.5-turbo claude-3 claude-2 gemini-pro"`
	TemplateID  string            `json:"template_id,omitempty" validate:"omitempty,uuid"`
	Variables   map[string]string `json:"variables,omitempty"`
	WorkspaceID string            `json:"workspace_id,omitempty" validate:"omitempty,uuid"`
	Context     string            `json:"context,omitempty" validate:"omitempty,max=1000"`
}

// RegenerateContentRequest represents content regeneration request
type RegenerateContentRequest struct {
	GenerationID string `json:"generation_id" validate:"required,uuid"`
	NewTopic     string `json:"new_topic,omitempty" validate:"omitempty,min=3,max=500"`
	NewTone      string `json:"new_tone,omitempty" validate:"omitempty,oneof=professional friendly humorous formal casual enthusiastic authoritative"`
	NewLength    string `json:"new_length,omitempty" validate:"omitempty,oneof=short medium long"`
	NewModel     string `json:"new_model,omitempty" validate:"omitempty,oneof=gpt-4 gpt-3.5-turbo claude-3 claude-2 gemini-pro"`
}

// ListGenerationsRequest represents generation listing request
type ListGenerationsRequest struct {
	UserID      string `json:"user_id" validate:"required,uuid"`
	Page        int    `json:"page" validate:"min=1"`
	Limit       int    `json:"limit" validate:"min=1,max=100"`
	Model       string `json:"model,omitempty" validate:"omitempty,oneof=gpt-4 gpt-3.5-turbo claude-3 claude-2 gemini-pro"`
	ContentType string `json:"content_type,omitempty" validate:"omitempty,oneof=educational promotional entertainment informational news tutorial"`
	Status      string `json:"status,omitempty" validate:"omitempty,oneof=pending completed failed"`
	WorkspaceID string `json:"workspace_id,omitempty" validate:"omitempty,uuid"`
	DateFrom    string `json:"date_from,omitempty" validate:"omitempty,datetime=2006-01-02"`
	DateTo      string `json:"date_to,omitempty" validate:"omitempty,datetime=2006-01-02"`
	SortBy      string `json:"sort_by,omitempty" validate:"omitempty,oneof=created_at updated_at credits_used generation_time"`
	SortOrder   string `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc"`
}

// DateRange represents a date range filter
type DateRange struct {
	From string `json:"from" validate:"required,datetime=2006-01-02"`
	To   string `json:"to" validate:"required,datetime=2006-01-02"`
}

// GenerationStats represents generation statistics
type GenerationStats struct {
	TotalGenerations    int                    `json:"total_generations"`
	TotalCreditsUsed    int                    `json:"total_credits_used"`
	AverageGenerationTime float64             `json:"average_generation_time"`
	SuccessRate         float64               `json:"success_rate"`
	ModelUsage          map[string]int        `json:"model_usage"`
	ContentTypeUsage    map[string]int        `json:"content_type_usage"`
	ToneUsage           map[string]int        `json:"tone_usage"`
	PlatformUsage       map[string]int        `json:"platform_usage"`
	DailyStats          []*DailyGenerationStat `json:"daily_stats"`
}

// DailyGenerationStat represents daily generation statistics
type DailyGenerationStat struct {
	Date        string `json:"date"`
	Count       int    `json:"count"`
	CreditsUsed int    `json:"credits_used"`
}

// TopicStats represents topic popularity statistics
type TopicStats struct {
	Topic string `json:"topic"`
	Count int    `json:"count"`
	Score float64 `json:"score"`
}

// GenerationResponse represents generation response
type GenerationResponse struct {
	ID                string            `json:"id"`
	UserID            string            `json:"user_id"`
	Topic             string            `json:"topic"`
	ContentType       string            `json:"content_type"`
	Tone              string            `json:"tone"`
	Length            string            `json:"length"`
	Platforms         []string          `json:"platforms,omitempty"`
	Model             string            `json:"model"`
	TemplateID        string            `json:"template_id,omitempty"`
	Variables         map[string]string `json:"variables,omitempty"`
	WorkspaceID       string            `json:"workspace_id,omitempty"`
	Content           string            `json:"content"`
	Hashtags          []string          `json:"hashtags,omitempty"`
	SuggestedImages   []string          `json:"suggested_images,omitempty"`
	CreditsUsed       int               `json:"credits_used"`
	GenerationTime    float64           `json:"generation_time"`
	ModelUsed         string            `json:"model_used"`
	Status            string            `json:"status"`
	ErrorMessage      string            `json:"error_message,omitempty"`
	CreatedAt         string            `json:"created_at"`
	UpdatedAt         string            `json:"updated_at"`
}

// ToGenerationResponse converts ent.Generation to GenerationResponse
func ToGenerationResponse(generation *ent.Generation) *GenerationResponse {
	if generation == nil {
		return nil
	}

	response := &GenerationResponse{
		ID:              generation.ID.String(),
		UserID:          generation.UserID.String(),
		Topic:           generation.Topic,
		ContentType:     string(generation.ContentType),
		Tone:            string(generation.Tone),
		Length:          string(generation.Length),
		Model:           generation.Model,
		TemplateID:      generation.TemplateID,
		WorkspaceID:     generation.WorkspaceID,
		Content:         generation.Content,
		CreditsUsed:     generation.CreditsUsed,
		GenerationTime:  generation.GenerationTime,
		ModelUsed:       generation.ModelUsed,
		Status:          string(generation.Status),
		ErrorMessage:    generation.ErrorMessage,
		CreatedAt:       generation.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:       generation.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Handle JSON fields
	if generation.Platforms != nil {
		if platforms, ok := generation.Platforms.([]string); ok {
			response.Platforms = platforms
		}
	}

	if generation.Variables != nil {
		if variables, ok := generation.Variables.(map[string]string); ok {
			response.Variables = variables
		}
	}

	if generation.Hashtags != nil {
		if hashtags, ok := generation.Hashtags.([]string); ok {
			response.Hashtags = hashtags
		}
	}

	if generation.SuggestedImages != nil {
		if images, ok := generation.SuggestedImages.([]string); ok {
			response.SuggestedImages = images
		}
	}

	return response
}

package models

import "time"

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// BaseModel represents common fields for all models
type BaseModel struct {
	ID        string     `json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
}

// FileInfo represents file information
type FileInfo struct {
	Name      string `json:"name"`
	Size      int64  `json:"size"`
	MimeType  string `json:"mime_type"`
	Extension string `json:"extension"`
	Hash      string `json:"hash,omitempty"`
}

// ImageInfo represents image-specific information
type ImageInfo struct {
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Format      string `json:"format"`
	ColorSpace  string `json:"color_space,omitempty"`
	HasAlpha    bool   `json:"has_alpha"`
	Orientation int    `json:"orientation,omitempty"`
}

// VideoInfo represents video-specific information
type VideoInfo struct {
	Duration    float64 `json:"duration"`
	Width       int     `json:"width"`
	Height      int     `json:"height"`
	FrameRate   float64 `json:"frame_rate"`
	Bitrate     int64   `json:"bitrate"`
	Codec       string  `json:"codec"`
	AudioCodec  string  `json:"audio_codec,omitempty"`
}

// DocumentInfo represents document-specific information
type DocumentInfo struct {
	PageCount    int    `json:"page_count,omitempty"`
	WordCount    int    `json:"word_count,omitempty"`
	Language     string `json:"language,omitempty"`
	Author       string `json:"author,omitempty"`
	Title        string `json:"title,omitempty"`
	Subject      string `json:"subject,omitempty"`
	Keywords     string `json:"keywords,omitempty"`
	CreatedDate  string `json:"created_date,omitempty"`
	ModifiedDate string `json:"modified_date,omitempty"`
}

// ProcessingStatus represents asset processing status
type ProcessingStatus struct {
	Status      string    `json:"status"` // pending, processing, completed, failed
	Progress    int       `json:"progress"` // 0-100
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Error       string    `json:"error,omitempty"`
	Steps       []ProcessingStep `json:"steps,omitempty"`
}

// ProcessingStep represents a processing step
type ProcessingStep struct {
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	Progress    int       `json:"progress"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Error       string    `json:"error,omitempty"`
}

// AssetVariant represents different variants of an asset
type AssetVariant struct {
	ID       string `json:"id"`
	Type     string `json:"type"` // thumbnail, small, medium, large, original
	URL      string `json:"url"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	Size     int64  `json:"size"`
	Format   string `json:"format,omitempty"`
}

// StorageInfo represents storage information
type StorageInfo struct {
	Provider   string `json:"provider"` // s3, minio, local
	Bucket     string `json:"bucket"`
	Key        string `json:"key"`
	Region     string `json:"region,omitempty"`
	URL        string `json:"url"`
	PublicURL  string `json:"public_url,omitempty"`
	CDNUrl     string `json:"cdn_url,omitempty"`
}

// SecurityInfo represents security scan information
type SecurityInfo struct {
	Scanned     bool      `json:"scanned"`
	ScannedAt   *time.Time `json:"scanned_at,omitempty"`
	Safe        bool      `json:"safe"`
	Threats     []string  `json:"threats,omitempty"`
	ScanEngine  string    `json:"scan_engine,omitempty"`
	ScanVersion string    `json:"scan_version,omitempty"`
}

// AccessControl represents access control settings
type AccessControl struct {
	IsPublic    bool     `json:"is_public"`
	SharedWith  []string `json:"shared_with,omitempty"`
	Permissions []string `json:"permissions,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// BulkOperationResult represents result of bulk operations
type BulkOperationResult struct {
	Total     int                    `json:"total"`
	Success   int                    `json:"success"`
	Failed    int                    `json:"failed"`
	Errors    []BulkOperationError   `json:"errors,omitempty"`
	Results   []interface{}          `json:"results,omitempty"`
}

// BulkOperationError represents an error in bulk operations
type BulkOperationError struct {
	ID     string `json:"id,omitempty"`
	Index  int    `json:"index"`
	Error  string `json:"error"`
	Reason string `json:"reason"`
}

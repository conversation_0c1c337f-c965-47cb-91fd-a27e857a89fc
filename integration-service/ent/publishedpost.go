// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// PublishedPost is the model entity for the PublishedPost schema.
type PublishedPost struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who published the post
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Original post ID from content management service
	PostID string `json:"post_id,omitempty"`
	// Platform integration ID used for publishing
	IntegrationID string `json:"integration_id,omitempty"`
	// Platform where the post was published
	Platform publishedpost.Platform `json:"platform,omitempty"`
	// Post ID on the platform
	PlatformPostID string `json:"platform_post_id,omitempty"`
	// URL of the post on the platform
	PlatformPostURL string `json:"platform_post_url,omitempty"`
	// Content that was published
	Content string `json:"content,omitempty"`
	// Media URLs that were published
	MediaUrls []string `json:"media_urls,omitempty"`
	// Hashtags used in the post
	Hashtags []string `json:"hashtags,omitempty"`
	// Publishing status
	Status publishedpost.Status `json:"status,omitempty"`
	// Error message if publishing failed
	ErrorMessage string `json:"error_message,omitempty"`
	// When the post was scheduled to be published
	ScheduledAt time.Time `json:"scheduled_at,omitempty"`
	// When the post was actually published
	PublishedAt time.Time `json:"published_at,omitempty"`
	// Response from the platform API
	PlatformResponse map[string]interface{} `json:"platform_response,omitempty"`
	// Post analytics data
	Analytics map[string]interface{} `json:"analytics,omitempty"`
	// Last time analytics were synced
	LastAnalyticsSync time.Time `json:"last_analytics_sync,omitempty"`
	// Number of retry attempts
	RetryCount int `json:"retry_count,omitempty"`
	// When to retry publishing if failed
	NextRetryAt time.Time `json:"next_retry_at,omitempty"`
	// Platform-specific publishing options
	PublishOptions map[string]interface{} `json:"publish_options,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PublishedPost) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case publishedpost.FieldMediaUrls, publishedpost.FieldHashtags, publishedpost.FieldPlatformResponse, publishedpost.FieldAnalytics, publishedpost.FieldPublishOptions, publishedpost.FieldMetadata:
			values[i] = new([]byte)
		case publishedpost.FieldRetryCount:
			values[i] = new(sql.NullInt64)
		case publishedpost.FieldPostID, publishedpost.FieldIntegrationID, publishedpost.FieldPlatform, publishedpost.FieldPlatformPostID, publishedpost.FieldPlatformPostURL, publishedpost.FieldContent, publishedpost.FieldStatus, publishedpost.FieldErrorMessage:
			values[i] = new(sql.NullString)
		case publishedpost.FieldScheduledAt, publishedpost.FieldPublishedAt, publishedpost.FieldLastAnalyticsSync, publishedpost.FieldNextRetryAt, publishedpost.FieldCreatedAt, publishedpost.FieldUpdatedAt, publishedpost.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case publishedpost.FieldID, publishedpost.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PublishedPost fields.
func (pp *PublishedPost) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case publishedpost.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				pp.ID = *value
			}
		case publishedpost.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				pp.UserID = *value
			}
		case publishedpost.FieldPostID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field post_id", values[i])
			} else if value.Valid {
				pp.PostID = value.String
			}
		case publishedpost.FieldIntegrationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field integration_id", values[i])
			} else if value.Valid {
				pp.IntegrationID = value.String
			}
		case publishedpost.FieldPlatform:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform", values[i])
			} else if value.Valid {
				pp.Platform = publishedpost.Platform(value.String)
			}
		case publishedpost.FieldPlatformPostID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_post_id", values[i])
			} else if value.Valid {
				pp.PlatformPostID = value.String
			}
		case publishedpost.FieldPlatformPostURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_post_url", values[i])
			} else if value.Valid {
				pp.PlatformPostURL = value.String
			}
		case publishedpost.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				pp.Content = value.String
			}
		case publishedpost.FieldMediaUrls:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field media_urls", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.MediaUrls); err != nil {
					return fmt.Errorf("unmarshal field media_urls: %w", err)
				}
			}
		case publishedpost.FieldHashtags:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field hashtags", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.Hashtags); err != nil {
					return fmt.Errorf("unmarshal field hashtags: %w", err)
				}
			}
		case publishedpost.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pp.Status = publishedpost.Status(value.String)
			}
		case publishedpost.FieldErrorMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_message", values[i])
			} else if value.Valid {
				pp.ErrorMessage = value.String
			}
		case publishedpost.FieldScheduledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field scheduled_at", values[i])
			} else if value.Valid {
				pp.ScheduledAt = value.Time
			}
		case publishedpost.FieldPublishedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field published_at", values[i])
			} else if value.Valid {
				pp.PublishedAt = value.Time
			}
		case publishedpost.FieldPlatformResponse:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field platform_response", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.PlatformResponse); err != nil {
					return fmt.Errorf("unmarshal field platform_response: %w", err)
				}
			}
		case publishedpost.FieldAnalytics:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field analytics", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.Analytics); err != nil {
					return fmt.Errorf("unmarshal field analytics: %w", err)
				}
			}
		case publishedpost.FieldLastAnalyticsSync:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_analytics_sync", values[i])
			} else if value.Valid {
				pp.LastAnalyticsSync = value.Time
			}
		case publishedpost.FieldRetryCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field retry_count", values[i])
			} else if value.Valid {
				pp.RetryCount = int(value.Int64)
			}
		case publishedpost.FieldNextRetryAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field next_retry_at", values[i])
			} else if value.Valid {
				pp.NextRetryAt = value.Time
			}
		case publishedpost.FieldPublishOptions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field publish_options", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.PublishOptions); err != nil {
					return fmt.Errorf("unmarshal field publish_options: %w", err)
				}
			}
		case publishedpost.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pp.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case publishedpost.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pp.CreatedAt = value.Time
			}
		case publishedpost.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pp.UpdatedAt = value.Time
			}
		case publishedpost.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				pp.DeletedAt = value.Time
			}
		default:
			pp.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PublishedPost.
// This includes values selected through modifiers, order, etc.
func (pp *PublishedPost) Value(name string) (ent.Value, error) {
	return pp.selectValues.Get(name)
}

// Update returns a builder for updating this PublishedPost.
// Note that you need to call PublishedPost.Unwrap() before calling this method if this PublishedPost
// was returned from a transaction, and the transaction was committed or rolled back.
func (pp *PublishedPost) Update() *PublishedPostUpdateOne {
	return NewPublishedPostClient(pp.config).UpdateOne(pp)
}

// Unwrap unwraps the PublishedPost entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pp *PublishedPost) Unwrap() *PublishedPost {
	_tx, ok := pp.config.driver.(*txDriver)
	if !ok {
		panic("ent: PublishedPost is not a transactional entity")
	}
	pp.config.driver = _tx.drv
	return pp
}

// String implements the fmt.Stringer.
func (pp *PublishedPost) String() string {
	var builder strings.Builder
	builder.WriteString("PublishedPost(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pp.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pp.UserID))
	builder.WriteString(", ")
	builder.WriteString("post_id=")
	builder.WriteString(pp.PostID)
	builder.WriteString(", ")
	builder.WriteString("integration_id=")
	builder.WriteString(pp.IntegrationID)
	builder.WriteString(", ")
	builder.WriteString("platform=")
	builder.WriteString(fmt.Sprintf("%v", pp.Platform))
	builder.WriteString(", ")
	builder.WriteString("platform_post_id=")
	builder.WriteString(pp.PlatformPostID)
	builder.WriteString(", ")
	builder.WriteString("platform_post_url=")
	builder.WriteString(pp.PlatformPostURL)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(pp.Content)
	builder.WriteString(", ")
	builder.WriteString("media_urls=")
	builder.WriteString(fmt.Sprintf("%v", pp.MediaUrls))
	builder.WriteString(", ")
	builder.WriteString("hashtags=")
	builder.WriteString(fmt.Sprintf("%v", pp.Hashtags))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pp.Status))
	builder.WriteString(", ")
	builder.WriteString("error_message=")
	builder.WriteString(pp.ErrorMessage)
	builder.WriteString(", ")
	builder.WriteString("scheduled_at=")
	builder.WriteString(pp.ScheduledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("published_at=")
	builder.WriteString(pp.PublishedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("platform_response=")
	builder.WriteString(fmt.Sprintf("%v", pp.PlatformResponse))
	builder.WriteString(", ")
	builder.WriteString("analytics=")
	builder.WriteString(fmt.Sprintf("%v", pp.Analytics))
	builder.WriteString(", ")
	builder.WriteString("last_analytics_sync=")
	builder.WriteString(pp.LastAnalyticsSync.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("retry_count=")
	builder.WriteString(fmt.Sprintf("%v", pp.RetryCount))
	builder.WriteString(", ")
	builder.WriteString("next_retry_at=")
	builder.WriteString(pp.NextRetryAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("publish_options=")
	builder.WriteString(fmt.Sprintf("%v", pp.PublishOptions))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", pp.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pp.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pp.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(pp.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// PublishedPosts is a parsable slice of PublishedPost.
type PublishedPosts []*PublishedPost

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
)

// TemplateCreate is the builder for creating a Template entity.
type TemplateCreate struct {
	config
	mutation *TemplateMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (tc *TemplateCreate) SetUserID(u uuid.UUID) *TemplateCreate {
	tc.mutation.SetUserID(u)
	return tc
}

// SetWorkspaceID sets the "workspace_id" field.
func (tc *TemplateCreate) SetWorkspaceID(s string) *TemplateCreate {
	tc.mutation.SetWorkspaceID(s)
	return tc
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableWorkspaceID(s *string) *TemplateCreate {
	if s != nil {
		tc.SetWorkspaceID(*s)
	}
	return tc
}

// SetName sets the "name" field.
func (tc *TemplateCreate) SetName(s string) *TemplateCreate {
	tc.mutation.SetName(s)
	return tc
}

// SetDescription sets the "description" field.
func (tc *TemplateCreate) SetDescription(s string) *TemplateCreate {
	tc.mutation.SetDescription(s)
	return tc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableDescription(s *string) *TemplateCreate {
	if s != nil {
		tc.SetDescription(*s)
	}
	return tc
}

// SetContent sets the "content" field.
func (tc *TemplateCreate) SetContent(s string) *TemplateCreate {
	tc.mutation.SetContent(s)
	return tc
}

// SetVariables sets the "variables" field.
func (tc *TemplateCreate) SetVariables(s []string) *TemplateCreate {
	tc.mutation.SetVariables(s)
	return tc
}

// SetVariableDescriptions sets the "variable_descriptions" field.
func (tc *TemplateCreate) SetVariableDescriptions(m map[string]string) *TemplateCreate {
	tc.mutation.SetVariableDescriptions(m)
	return tc
}

// SetCategory sets the "category" field.
func (tc *TemplateCreate) SetCategory(t template.Category) *TemplateCreate {
	tc.mutation.SetCategory(t)
	return tc
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableCategory(t *template.Category) *TemplateCreate {
	if t != nil {
		tc.SetCategory(*t)
	}
	return tc
}

// SetPlatforms sets the "platforms" field.
func (tc *TemplateCreate) SetPlatforms(s []string) *TemplateCreate {
	tc.mutation.SetPlatforms(s)
	return tc
}

// SetTone sets the "tone" field.
func (tc *TemplateCreate) SetTone(t template.Tone) *TemplateCreate {
	tc.mutation.SetTone(t)
	return tc
}

// SetNillableTone sets the "tone" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableTone(t *template.Tone) *TemplateCreate {
	if t != nil {
		tc.SetTone(*t)
	}
	return tc
}

// SetContentType sets the "content_type" field.
func (tc *TemplateCreate) SetContentType(tt template.ContentType) *TemplateCreate {
	tc.mutation.SetContentType(tt)
	return tc
}

// SetNillableContentType sets the "content_type" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableContentType(tt *template.ContentType) *TemplateCreate {
	if tt != nil {
		tc.SetContentType(*tt)
	}
	return tc
}

// SetHashtags sets the "hashtags" field.
func (tc *TemplateCreate) SetHashtags(s []string) *TemplateCreate {
	tc.mutation.SetHashtags(s)
	return tc
}

// SetIsPublic sets the "is_public" field.
func (tc *TemplateCreate) SetIsPublic(b bool) *TemplateCreate {
	tc.mutation.SetIsPublic(b)
	return tc
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableIsPublic(b *bool) *TemplateCreate {
	if b != nil {
		tc.SetIsPublic(*b)
	}
	return tc
}

// SetIsFeatured sets the "is_featured" field.
func (tc *TemplateCreate) SetIsFeatured(b bool) *TemplateCreate {
	tc.mutation.SetIsFeatured(b)
	return tc
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableIsFeatured(b *bool) *TemplateCreate {
	if b != nil {
		tc.SetIsFeatured(*b)
	}
	return tc
}

// SetUsageCount sets the "usage_count" field.
func (tc *TemplateCreate) SetUsageCount(i int) *TemplateCreate {
	tc.mutation.SetUsageCount(i)
	return tc
}

// SetNillableUsageCount sets the "usage_count" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableUsageCount(i *int) *TemplateCreate {
	if i != nil {
		tc.SetUsageCount(*i)
	}
	return tc
}

// SetRating sets the "rating" field.
func (tc *TemplateCreate) SetRating(f float64) *TemplateCreate {
	tc.mutation.SetRating(f)
	return tc
}

// SetNillableRating sets the "rating" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableRating(f *float64) *TemplateCreate {
	if f != nil {
		tc.SetRating(*f)
	}
	return tc
}

// SetRatingCount sets the "rating_count" field.
func (tc *TemplateCreate) SetRatingCount(i int) *TemplateCreate {
	tc.mutation.SetRatingCount(i)
	return tc
}

// SetNillableRatingCount sets the "rating_count" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableRatingCount(i *int) *TemplateCreate {
	if i != nil {
		tc.SetRatingCount(*i)
	}
	return tc
}

// SetTags sets the "tags" field.
func (tc *TemplateCreate) SetTags(s []string) *TemplateCreate {
	tc.mutation.SetTags(s)
	return tc
}

// SetLanguage sets the "language" field.
func (tc *TemplateCreate) SetLanguage(s string) *TemplateCreate {
	tc.mutation.SetLanguage(s)
	return tc
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableLanguage(s *string) *TemplateCreate {
	if s != nil {
		tc.SetLanguage(*s)
	}
	return tc
}

// SetTrainingFiles sets the "training_files" field.
func (tc *TemplateCreate) SetTrainingFiles(s []string) *TemplateCreate {
	tc.mutation.SetTrainingFiles(s)
	return tc
}

// SetRagTrained sets the "rag_trained" field.
func (tc *TemplateCreate) SetRagTrained(b bool) *TemplateCreate {
	tc.mutation.SetRagTrained(b)
	return tc
}

// SetNillableRagTrained sets the "rag_trained" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableRagTrained(b *bool) *TemplateCreate {
	if b != nil {
		tc.SetRagTrained(*b)
	}
	return tc
}

// SetRagTrainedAt sets the "rag_trained_at" field.
func (tc *TemplateCreate) SetRagTrainedAt(t time.Time) *TemplateCreate {
	tc.mutation.SetRagTrainedAt(t)
	return tc
}

// SetNillableRagTrainedAt sets the "rag_trained_at" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableRagTrainedAt(t *time.Time) *TemplateCreate {
	if t != nil {
		tc.SetRagTrainedAt(*t)
	}
	return tc
}

// SetMetadata sets the "metadata" field.
func (tc *TemplateCreate) SetMetadata(m map[string]interface{}) *TemplateCreate {
	tc.mutation.SetMetadata(m)
	return tc
}

// SetCreatedAt sets the "created_at" field.
func (tc *TemplateCreate) SetCreatedAt(t time.Time) *TemplateCreate {
	tc.mutation.SetCreatedAt(t)
	return tc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableCreatedAt(t *time.Time) *TemplateCreate {
	if t != nil {
		tc.SetCreatedAt(*t)
	}
	return tc
}

// SetUpdatedAt sets the "updated_at" field.
func (tc *TemplateCreate) SetUpdatedAt(t time.Time) *TemplateCreate {
	tc.mutation.SetUpdatedAt(t)
	return tc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableUpdatedAt(t *time.Time) *TemplateCreate {
	if t != nil {
		tc.SetUpdatedAt(*t)
	}
	return tc
}

// SetDeletedAt sets the "deleted_at" field.
func (tc *TemplateCreate) SetDeletedAt(t time.Time) *TemplateCreate {
	tc.mutation.SetDeletedAt(t)
	return tc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableDeletedAt(t *time.Time) *TemplateCreate {
	if t != nil {
		tc.SetDeletedAt(*t)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TemplateCreate) SetID(u uuid.UUID) *TemplateCreate {
	tc.mutation.SetID(u)
	return tc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (tc *TemplateCreate) SetNillableID(u *uuid.UUID) *TemplateCreate {
	if u != nil {
		tc.SetID(*u)
	}
	return tc
}

// Mutation returns the TemplateMutation object of the builder.
func (tc *TemplateCreate) Mutation() *TemplateMutation {
	return tc.mutation
}

// Save creates the Template in the database.
func (tc *TemplateCreate) Save(ctx context.Context) (*Template, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TemplateCreate) SaveX(ctx context.Context) *Template {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TemplateCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TemplateCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TemplateCreate) defaults() {
	if _, ok := tc.mutation.Category(); !ok {
		v := template.DefaultCategory
		tc.mutation.SetCategory(v)
	}
	if _, ok := tc.mutation.Tone(); !ok {
		v := template.DefaultTone
		tc.mutation.SetTone(v)
	}
	if _, ok := tc.mutation.ContentType(); !ok {
		v := template.DefaultContentType
		tc.mutation.SetContentType(v)
	}
	if _, ok := tc.mutation.IsPublic(); !ok {
		v := template.DefaultIsPublic
		tc.mutation.SetIsPublic(v)
	}
	if _, ok := tc.mutation.IsFeatured(); !ok {
		v := template.DefaultIsFeatured
		tc.mutation.SetIsFeatured(v)
	}
	if _, ok := tc.mutation.UsageCount(); !ok {
		v := template.DefaultUsageCount
		tc.mutation.SetUsageCount(v)
	}
	if _, ok := tc.mutation.Rating(); !ok {
		v := template.DefaultRating
		tc.mutation.SetRating(v)
	}
	if _, ok := tc.mutation.RatingCount(); !ok {
		v := template.DefaultRatingCount
		tc.mutation.SetRatingCount(v)
	}
	if _, ok := tc.mutation.Language(); !ok {
		v := template.DefaultLanguage
		tc.mutation.SetLanguage(v)
	}
	if _, ok := tc.mutation.RagTrained(); !ok {
		v := template.DefaultRagTrained
		tc.mutation.SetRagTrained(v)
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		v := template.DefaultCreatedAt()
		tc.mutation.SetCreatedAt(v)
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		v := template.DefaultUpdatedAt()
		tc.mutation.SetUpdatedAt(v)
	}
	if _, ok := tc.mutation.ID(); !ok {
		v := template.DefaultID()
		tc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TemplateCreate) check() error {
	if _, ok := tc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Template.user_id"`)}
	}
	if v, ok := tc.mutation.WorkspaceID(); ok {
		if err := template.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Template.workspace_id": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Template.name"`)}
	}
	if v, ok := tc.mutation.Name(); ok {
		if err := template.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Template.name": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "Template.content"`)}
	}
	if _, ok := tc.mutation.Category(); !ok {
		return &ValidationError{Name: "category", err: errors.New(`ent: missing required field "Template.category"`)}
	}
	if v, ok := tc.mutation.Category(); ok {
		if err := template.CategoryValidator(v); err != nil {
			return &ValidationError{Name: "category", err: fmt.Errorf(`ent: validator failed for field "Template.category": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Tone(); !ok {
		return &ValidationError{Name: "tone", err: errors.New(`ent: missing required field "Template.tone"`)}
	}
	if v, ok := tc.mutation.Tone(); ok {
		if err := template.ToneValidator(v); err != nil {
			return &ValidationError{Name: "tone", err: fmt.Errorf(`ent: validator failed for field "Template.tone": %w`, err)}
		}
	}
	if _, ok := tc.mutation.ContentType(); !ok {
		return &ValidationError{Name: "content_type", err: errors.New(`ent: missing required field "Template.content_type"`)}
	}
	if v, ok := tc.mutation.ContentType(); ok {
		if err := template.ContentTypeValidator(v); err != nil {
			return &ValidationError{Name: "content_type", err: fmt.Errorf(`ent: validator failed for field "Template.content_type": %w`, err)}
		}
	}
	if _, ok := tc.mutation.IsPublic(); !ok {
		return &ValidationError{Name: "is_public", err: errors.New(`ent: missing required field "Template.is_public"`)}
	}
	if _, ok := tc.mutation.IsFeatured(); !ok {
		return &ValidationError{Name: "is_featured", err: errors.New(`ent: missing required field "Template.is_featured"`)}
	}
	if _, ok := tc.mutation.UsageCount(); !ok {
		return &ValidationError{Name: "usage_count", err: errors.New(`ent: missing required field "Template.usage_count"`)}
	}
	if _, ok := tc.mutation.Rating(); !ok {
		return &ValidationError{Name: "rating", err: errors.New(`ent: missing required field "Template.rating"`)}
	}
	if v, ok := tc.mutation.Rating(); ok {
		if err := template.RatingValidator(v); err != nil {
			return &ValidationError{Name: "rating", err: fmt.Errorf(`ent: validator failed for field "Template.rating": %w`, err)}
		}
	}
	if _, ok := tc.mutation.RatingCount(); !ok {
		return &ValidationError{Name: "rating_count", err: errors.New(`ent: missing required field "Template.rating_count"`)}
	}
	if _, ok := tc.mutation.Language(); !ok {
		return &ValidationError{Name: "language", err: errors.New(`ent: missing required field "Template.language"`)}
	}
	if v, ok := tc.mutation.Language(); ok {
		if err := template.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Template.language": %w`, err)}
		}
	}
	if _, ok := tc.mutation.RagTrained(); !ok {
		return &ValidationError{Name: "rag_trained", err: errors.New(`ent: missing required field "Template.rag_trained"`)}
	}
	if _, ok := tc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Template.created_at"`)}
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Template.updated_at"`)}
	}
	return nil
}

func (tc *TemplateCreate) sqlSave(ctx context.Context) (*Template, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TemplateCreate) createSpec() (*Template, *sqlgraph.CreateSpec) {
	var (
		_node = &Template{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(template.Table, sqlgraph.NewFieldSpec(template.FieldID, field.TypeUUID))
	)
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := tc.mutation.UserID(); ok {
		_spec.SetField(template.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := tc.mutation.WorkspaceID(); ok {
		_spec.SetField(template.FieldWorkspaceID, field.TypeString, value)
		_node.WorkspaceID = value
	}
	if value, ok := tc.mutation.Name(); ok {
		_spec.SetField(template.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := tc.mutation.Description(); ok {
		_spec.SetField(template.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := tc.mutation.Content(); ok {
		_spec.SetField(template.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := tc.mutation.Variables(); ok {
		_spec.SetField(template.FieldVariables, field.TypeJSON, value)
		_node.Variables = value
	}
	if value, ok := tc.mutation.VariableDescriptions(); ok {
		_spec.SetField(template.FieldVariableDescriptions, field.TypeJSON, value)
		_node.VariableDescriptions = value
	}
	if value, ok := tc.mutation.Category(); ok {
		_spec.SetField(template.FieldCategory, field.TypeEnum, value)
		_node.Category = value
	}
	if value, ok := tc.mutation.Platforms(); ok {
		_spec.SetField(template.FieldPlatforms, field.TypeJSON, value)
		_node.Platforms = value
	}
	if value, ok := tc.mutation.Tone(); ok {
		_spec.SetField(template.FieldTone, field.TypeEnum, value)
		_node.Tone = value
	}
	if value, ok := tc.mutation.ContentType(); ok {
		_spec.SetField(template.FieldContentType, field.TypeEnum, value)
		_node.ContentType = value
	}
	if value, ok := tc.mutation.Hashtags(); ok {
		_spec.SetField(template.FieldHashtags, field.TypeJSON, value)
		_node.Hashtags = value
	}
	if value, ok := tc.mutation.IsPublic(); ok {
		_spec.SetField(template.FieldIsPublic, field.TypeBool, value)
		_node.IsPublic = value
	}
	if value, ok := tc.mutation.IsFeatured(); ok {
		_spec.SetField(template.FieldIsFeatured, field.TypeBool, value)
		_node.IsFeatured = value
	}
	if value, ok := tc.mutation.UsageCount(); ok {
		_spec.SetField(template.FieldUsageCount, field.TypeInt, value)
		_node.UsageCount = value
	}
	if value, ok := tc.mutation.Rating(); ok {
		_spec.SetField(template.FieldRating, field.TypeFloat64, value)
		_node.Rating = value
	}
	if value, ok := tc.mutation.RatingCount(); ok {
		_spec.SetField(template.FieldRatingCount, field.TypeInt, value)
		_node.RatingCount = value
	}
	if value, ok := tc.mutation.Tags(); ok {
		_spec.SetField(template.FieldTags, field.TypeJSON, value)
		_node.Tags = value
	}
	if value, ok := tc.mutation.Language(); ok {
		_spec.SetField(template.FieldLanguage, field.TypeString, value)
		_node.Language = value
	}
	if value, ok := tc.mutation.TrainingFiles(); ok {
		_spec.SetField(template.FieldTrainingFiles, field.TypeJSON, value)
		_node.TrainingFiles = value
	}
	if value, ok := tc.mutation.RagTrained(); ok {
		_spec.SetField(template.FieldRagTrained, field.TypeBool, value)
		_node.RagTrained = value
	}
	if value, ok := tc.mutation.RagTrainedAt(); ok {
		_spec.SetField(template.FieldRagTrainedAt, field.TypeTime, value)
		_node.RagTrainedAt = value
	}
	if value, ok := tc.mutation.Metadata(); ok {
		_spec.SetField(template.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := tc.mutation.CreatedAt(); ok {
		_spec.SetField(template.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tc.mutation.UpdatedAt(); ok {
		_spec.SetField(template.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tc.mutation.DeletedAt(); ok {
		_spec.SetField(template.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// TemplateCreateBulk is the builder for creating many Template entities in bulk.
type TemplateCreateBulk struct {
	config
	err      error
	builders []*TemplateCreate
}

// Save creates the Template entities in the database.
func (tcb *TemplateCreateBulk) Save(ctx context.Context) ([]*Template, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Template, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TemplateMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TemplateCreateBulk) SaveX(ctx context.Context) []*Template {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TemplateCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TemplateCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

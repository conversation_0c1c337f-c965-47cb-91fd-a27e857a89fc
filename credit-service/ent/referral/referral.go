// Code generated by ent, DO NOT EDIT.

package referral

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the referral type in the database.
	Label = "referral"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldReferrerID holds the string denoting the referrer_id field in the database.
	FieldReferrerID = "referrer_id"
	// FieldReferredID holds the string denoting the referred_id field in the database.
	FieldReferredID = "referred_id"
	// FieldReferralCode holds the string denoting the referral_code field in the database.
	FieldReferralCode = "referral_code"
	// FieldReferredEmail holds the string denoting the referred_email field in the database.
	FieldReferredEmail = "referred_email"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldReferrerCreditsEarned holds the string denoting the referrer_credits_earned field in the database.
	FieldReferrerCreditsEarned = "referrer_credits_earned"
	// FieldReferredCreditsEarned holds the string denoting the referred_credits_earned field in the database.
	FieldReferredCreditsEarned = "referred_credits_earned"
	// FieldReferralType holds the string denoting the referral_type field in the database.
	FieldReferralType = "referral_type"
	// FieldCompletedAt holds the string denoting the completed_at field in the database.
	FieldCompletedAt = "completed_at"
	// FieldExpiresAt holds the string denoting the expires_at field in the database.
	FieldExpiresAt = "expires_at"
	// FieldReferralConditions holds the string denoting the referral_conditions field in the database.
	FieldReferralConditions = "referral_conditions"
	// FieldTrackingData holds the string denoting the tracking_data field in the database.
	FieldTrackingData = "tracking_data"
	// FieldCampaignID holds the string denoting the campaign_id field in the database.
	FieldCampaignID = "campaign_id"
	// FieldSource holds the string denoting the source field in the database.
	FieldSource = "source"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the referral in the database.
	Table = "referrals"
)

// Columns holds all SQL columns for referral fields.
var Columns = []string{
	FieldID,
	FieldReferrerID,
	FieldReferredID,
	FieldReferralCode,
	FieldReferredEmail,
	FieldStatus,
	FieldReferrerCreditsEarned,
	FieldReferredCreditsEarned,
	FieldReferralType,
	FieldCompletedAt,
	FieldExpiresAt,
	FieldReferralConditions,
	FieldTrackingData,
	FieldCampaignID,
	FieldSource,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// ReferralCodeValidator is a validator for the "referral_code" field. It is called by the builders before save.
	ReferralCodeValidator func(string) error
	// ReferredEmailValidator is a validator for the "referred_email" field. It is called by the builders before save.
	ReferredEmailValidator func(string) error
	// DefaultReferrerCreditsEarned holds the default value on creation for the "referrer_credits_earned" field.
	DefaultReferrerCreditsEarned int
	// ReferrerCreditsEarnedValidator is a validator for the "referrer_credits_earned" field. It is called by the builders before save.
	ReferrerCreditsEarnedValidator func(int) error
	// DefaultReferredCreditsEarned holds the default value on creation for the "referred_credits_earned" field.
	DefaultReferredCreditsEarned int
	// ReferredCreditsEarnedValidator is a validator for the "referred_credits_earned" field. It is called by the builders before save.
	ReferredCreditsEarnedValidator func(int) error
	// DefaultReferralType holds the default value on creation for the "referral_type" field.
	DefaultReferralType string
	// ReferralTypeValidator is a validator for the "referral_type" field. It is called by the builders before save.
	ReferralTypeValidator func(string) error
	// CampaignIDValidator is a validator for the "campaign_id" field. It is called by the builders before save.
	CampaignIDValidator func(string) error
	// SourceValidator is a validator for the "source" field. It is called by the builders before save.
	SourceValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending   Status = "pending"
	StatusCompleted Status = "completed"
	StatusExpired   Status = "expired"
	StatusCancelled Status = "cancelled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusCompleted, StatusExpired, StatusCancelled:
		return nil
	default:
		return fmt.Errorf("referral: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Referral queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByReferrerID orders the results by the referrer_id field.
func ByReferrerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferrerID, opts...).ToFunc()
}

// ByReferredID orders the results by the referred_id field.
func ByReferredID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferredID, opts...).ToFunc()
}

// ByReferralCode orders the results by the referral_code field.
func ByReferralCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferralCode, opts...).ToFunc()
}

// ByReferredEmail orders the results by the referred_email field.
func ByReferredEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferredEmail, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByReferrerCreditsEarned orders the results by the referrer_credits_earned field.
func ByReferrerCreditsEarned(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferrerCreditsEarned, opts...).ToFunc()
}

// ByReferredCreditsEarned orders the results by the referred_credits_earned field.
func ByReferredCreditsEarned(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferredCreditsEarned, opts...).ToFunc()
}

// ByReferralType orders the results by the referral_type field.
func ByReferralType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferralType, opts...).ToFunc()
}

// ByCompletedAt orders the results by the completed_at field.
func ByCompletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCompletedAt, opts...).ToFunc()
}

// ByExpiresAt orders the results by the expires_at field.
func ByExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresAt, opts...).ToFunc()
}

// ByCampaignID orders the results by the campaign_id field.
func ByCampaignID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCampaignID, opts...).ToFunc()
}

// BySource orders the results by the source field.
func BySource(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSource, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

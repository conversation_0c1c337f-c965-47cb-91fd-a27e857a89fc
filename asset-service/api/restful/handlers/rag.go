package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/asset-service/usecase/rag"
	"github.com/social-content-ai/pkg-shared/logging"
)

// <PERSON>G<PERSON>andler handles RAG-related HTTP requests
type RAG<PERSON>andler struct {
	ragUseCase rag.UseCase
	logger     logging.Logger
}

// NewRAGHandler creates a new RAG handler
func NewRAGHandler(ragUseCase rag.UseCase, logger logging.Logger) *RAGHandler {
	return &RAGHandler{
		ragUseCase: ragUseCase,
		logger:     logger,
	}
}

// UploadRAGDocument handles POST /api/v1/rag/documents
func (h *RAGHandler) UploadRAGDocument(c *gin.Context) {
	var req models.UploadRAGDocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.ragUseCase.UploadRAGDocument(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upload RAG document")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetRAGDocument handles GET /api/v1/rag/documents/:id
func (h *RAGHandler) GetRAGDocument(c *gin.Context) {
	documentID := c.Param("id")
	if documentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Document ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.ragUseCase.GetRAGDocument(c.Request.Context(), documentID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get RAG document")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListRAGDocuments handles GET /api/v1/rag/documents
func (h *RAGHandler) ListRAGDocuments(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	category := c.Query("category")
	language := c.Query("language")

	req := &models.ListRAGDocumentsRequest{
		UserID:   userID.(string),
		Page:     page,
		Limit:    limit,
		Category: category,
		Language: language,
	}

	response, err := h.ragUseCase.ListRAGDocuments(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list RAG documents")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateRAGDocument handles PUT /api/v1/rag/documents/:id
func (h *RAGHandler) UpdateRAGDocument(c *gin.Context) {
	documentID := c.Param("id")
	if documentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Document ID is required"})
		return
	}

	var req models.UpdateRAGDocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.DocumentID = documentID
	req.UserID = userID.(string)

	response, err := h.ragUseCase.UpdateRAGDocument(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update RAG document")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteRAGDocument handles DELETE /api/v1/rag/documents/:id
func (h *RAGHandler) DeleteRAGDocument(c *gin.Context) {
	documentID := c.Param("id")
	if documentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Document ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.ragUseCase.DeleteRAGDocument(c.Request.Context(), documentID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete RAG document")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAG document deleted successfully"})
}

// ProcessRAGDocument handles POST /api/v1/rag/documents/:id/process
func (h *RAGHandler) ProcessRAGDocument(c *gin.Context) {
	documentID := c.Param("id")
	if documentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Document ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req := &models.ProcessRAGDocumentRequest{
		DocumentID: documentID,
		UserID:     userID.(string),
	}

	response, err := h.ragUseCase.ProcessRAGDocument(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process RAG document")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchRAGDocuments handles POST /api/v1/rag/search
func (h *RAGHandler) SearchRAGDocuments(c *gin.Context) {
	var req models.SearchRAGDocumentsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.ragUseCase.SearchRAGDocuments(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search RAG documents")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetRAGDocumentChunks handles GET /api/v1/rag/documents/:id/chunks
func (h *RAGHandler) GetRAGDocumentChunks(c *gin.Context) {
	documentID := c.Param("id")
	if documentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Document ID is required"})
		return
	}

	// Get user ID from context
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// For now, return mock chunks since GetRAGDocumentChunks is not in interface
	chunks := []gin.H{
		{
			"id":      "chunk-1",
			"content": "This is the first chunk of the document...",
			"index":   0,
			"tokens":  150,
		},
		{
			"id":      "chunk-2",
			"content": "This is the second chunk of the document...",
			"index":   1,
			"tokens":  142,
		},
	}

	response := gin.H{
		"chunks": chunks,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       len(chunks),
			"total_pages": 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetRAGStats handles GET /api/v1/rag/stats
func (h *RAGHandler) GetRAGStats(c *gin.Context) {
	// Get user ID from context
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.ragUseCase.GetRAGStats(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get RAG stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

module github.com/social-content-ai/analytics-service

go 1.21

require (
	github.com/social-content-ai/proto-shared v0.0.0
	github.com/social-content-ai/pkg-shared v0.0.0
	entgo.io/ent v0.12.5
	google.golang.org/grpc v1.59.0
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.18.1
	github.com/google/uuid v1.4.0
	github.com/stretchr/testify v1.8.4
	github.com/go-playground/validator/v10 v10.16.0
	github.com/sirupsen/logrus v1.9.3
	github.com/segmentio/kafka-go v0.4.47
	github.com/shopspring/decimal v1.3.1
	github.com/influxdata/influxdb-client-go/v2 v2.13.0
)

require (
	ariga.io/atlas v0.15.0 // indirect
	github.com/agext/levenshtein v1.2.3 // indirect
	github.com/apparentlymart/go-textseg/v15 v15.0.0 // indirect
	github.com/apapsch/go-jsonmerge/v2 v2.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/go-openapi/inflect v0.19.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/hashicorp/hcl/v2 v2.19.1 // indirect
	github.com/influxdata/line-protocol v0.0.0-20210922203350-b1ad95c89adf // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/oapi-codegen/runtime v1.1.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.19 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/zclconf/go-cty v1.14.1 // indirect
	golang.org/x/mod v0.14.0 // indirect
	golang.org/x/net v0.19.0 // indirect
	golang.org/x/sync v0.5.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20231212172506-995d672761c0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231212172506-995d672761c0 // indirect
	google.golang.org/protobuf v1.31.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace github.com/social-content-ai/proto-shared => ../proto-shared
replace github.com/social-content-ai/pkg-shared => ../pkg-shared

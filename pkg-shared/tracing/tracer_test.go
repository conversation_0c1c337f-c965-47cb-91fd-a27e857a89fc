package tracing

import (
	"testing"
)

func TestNewTracer(t *testing.T) {
	// Test creating a new tracer
	tracer, err := NewTracer("test-service")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if tracer == nil {
		t.<PERSON><PERSON>("Expected tracer to be non-nil")
	}

	// Test closing the tracer
	err = tracer.Close()
	if err != nil {
		t.Fatalf("Expected no error when closing tracer, got %v", err)
	}
}

func TestNoOpTracer(t *testing.T) {
	// Test NoOpTracer directly
	tracer := &NoOpTracer{serviceName: "test"}
	
	err := tracer.Close()
	if err != nil {
		t.Fatalf("Expected no error when closing NoOpTracer, got %v", err)
	}
}

func TestTracerWithEmptyServiceName(t *testing.T) {
	// Test with empty service name
	tracer, err := NewTracer("")
	if err != nil {
		t.Fatalf("Expected no error with empty service name, got %v", err)
	}

	if tracer == nil {
		t.<PERSON><PERSON>("Expected tracer to be non-nil even with empty service name")
	}

	err = tracer.Close()
	if err != nil {
		t.Fatalf("Expected no error when closing tracer, got %v", err)
	}
}

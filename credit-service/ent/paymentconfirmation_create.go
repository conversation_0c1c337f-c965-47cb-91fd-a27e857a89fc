// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
)

// PaymentConfirmationCreate is the builder for creating a PaymentConfirmation entity.
type PaymentConfirmationCreate struct {
	config
	mutation *PaymentConfirmationMutation
	hooks    []Hook
}

// SetBankTransferID sets the "bank_transfer_id" field.
func (pcc *PaymentConfirmationCreate) SetBankTransferID(u uuid.UUID) *PaymentConfirmationCreate {
	pcc.mutation.SetBankTransferID(u)
	return pcc
}

// SetNillableBankTransferID sets the "bank_transfer_id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableBankTransferID(u *uuid.UUID) *PaymentConfirmationCreate {
	if u != nil {
		pcc.SetBankTransferID(*u)
	}
	return pcc
}

// SetOrderID sets the "order_id" field.
func (pcc *PaymentConfirmationCreate) SetOrderID(u uuid.UUID) *PaymentConfirmationCreate {
	pcc.mutation.SetOrderID(u)
	return pcc
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableOrderID(u *uuid.UUID) *PaymentConfirmationCreate {
	if u != nil {
		pcc.SetOrderID(*u)
	}
	return pcc
}

// SetSubscriptionID sets the "subscription_id" field.
func (pcc *PaymentConfirmationCreate) SetSubscriptionID(u uuid.UUID) *PaymentConfirmationCreate {
	pcc.mutation.SetSubscriptionID(u)
	return pcc
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableSubscriptionID(u *uuid.UUID) *PaymentConfirmationCreate {
	if u != nil {
		pcc.SetSubscriptionID(*u)
	}
	return pcc
}

// SetPaymentMethod sets the "payment_method" field.
func (pcc *PaymentConfirmationCreate) SetPaymentMethod(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetPaymentMethod(s)
	return pcc
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (pcc *PaymentConfirmationCreate) SetExternalPaymentID(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetExternalPaymentID(s)
	return pcc
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableExternalPaymentID(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetExternalPaymentID(*s)
	}
	return pcc
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (pcc *PaymentConfirmationCreate) SetBankTransactionID(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetBankTransactionID(s)
	return pcc
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableBankTransactionID(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetBankTransactionID(*s)
	}
	return pcc
}

// SetAmountPaid sets the "amount_paid" field.
func (pcc *PaymentConfirmationCreate) SetAmountPaid(i int64) *PaymentConfirmationCreate {
	pcc.mutation.SetAmountPaid(i)
	return pcc
}

// SetCurrency sets the "currency" field.
func (pcc *PaymentConfirmationCreate) SetCurrency(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetCurrency(s)
	return pcc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableCurrency(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetCurrency(*s)
	}
	return pcc
}

// SetStatus sets the "status" field.
func (pcc *PaymentConfirmationCreate) SetStatus(pa paymentconfirmation.Status) *PaymentConfirmationCreate {
	pcc.mutation.SetStatus(pa)
	return pcc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableStatus(pa *paymentconfirmation.Status) *PaymentConfirmationCreate {
	if pa != nil {
		pcc.SetStatus(*pa)
	}
	return pcc
}

// SetConfirmedBy sets the "confirmed_by" field.
func (pcc *PaymentConfirmationCreate) SetConfirmedBy(u uuid.UUID) *PaymentConfirmationCreate {
	pcc.mutation.SetConfirmedBy(u)
	return pcc
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableConfirmedBy(u *uuid.UUID) *PaymentConfirmationCreate {
	if u != nil {
		pcc.SetConfirmedBy(*u)
	}
	return pcc
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (pcc *PaymentConfirmationCreate) SetConfirmationMethod(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetConfirmationMethod(s)
	return pcc
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableConfirmationMethod(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetConfirmationMethod(*s)
	}
	return pcc
}

// SetConfirmationNotes sets the "confirmation_notes" field.
func (pcc *PaymentConfirmationCreate) SetConfirmationNotes(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetConfirmationNotes(s)
	return pcc
}

// SetNillableConfirmationNotes sets the "confirmation_notes" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableConfirmationNotes(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetConfirmationNotes(*s)
	}
	return pcc
}

// SetPaymentDetails sets the "payment_details" field.
func (pcc *PaymentConfirmationCreate) SetPaymentDetails(m map[string]interface{}) *PaymentConfirmationCreate {
	pcc.mutation.SetPaymentDetails(m)
	return pcc
}

// SetWebhookData sets the "webhook_data" field.
func (pcc *PaymentConfirmationCreate) SetWebhookData(m map[string]interface{}) *PaymentConfirmationCreate {
	pcc.mutation.SetWebhookData(m)
	return pcc
}

// SetPaymentDate sets the "payment_date" field.
func (pcc *PaymentConfirmationCreate) SetPaymentDate(t time.Time) *PaymentConfirmationCreate {
	pcc.mutation.SetPaymentDate(t)
	return pcc
}

// SetNillablePaymentDate sets the "payment_date" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillablePaymentDate(t *time.Time) *PaymentConfirmationCreate {
	if t != nil {
		pcc.SetPaymentDate(*t)
	}
	return pcc
}

// SetConfirmedAt sets the "confirmed_at" field.
func (pcc *PaymentConfirmationCreate) SetConfirmedAt(t time.Time) *PaymentConfirmationCreate {
	pcc.mutation.SetConfirmedAt(t)
	return pcc
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableConfirmedAt(t *time.Time) *PaymentConfirmationCreate {
	if t != nil {
		pcc.SetConfirmedAt(*t)
	}
	return pcc
}

// SetFailureReason sets the "failure_reason" field.
func (pcc *PaymentConfirmationCreate) SetFailureReason(s string) *PaymentConfirmationCreate {
	pcc.mutation.SetFailureReason(s)
	return pcc
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableFailureReason(s *string) *PaymentConfirmationCreate {
	if s != nil {
		pcc.SetFailureReason(*s)
	}
	return pcc
}

// SetMetadata sets the "metadata" field.
func (pcc *PaymentConfirmationCreate) SetMetadata(m map[string]interface{}) *PaymentConfirmationCreate {
	pcc.mutation.SetMetadata(m)
	return pcc
}

// SetCreatedAt sets the "created_at" field.
func (pcc *PaymentConfirmationCreate) SetCreatedAt(t time.Time) *PaymentConfirmationCreate {
	pcc.mutation.SetCreatedAt(t)
	return pcc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableCreatedAt(t *time.Time) *PaymentConfirmationCreate {
	if t != nil {
		pcc.SetCreatedAt(*t)
	}
	return pcc
}

// SetUpdatedAt sets the "updated_at" field.
func (pcc *PaymentConfirmationCreate) SetUpdatedAt(t time.Time) *PaymentConfirmationCreate {
	pcc.mutation.SetUpdatedAt(t)
	return pcc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableUpdatedAt(t *time.Time) *PaymentConfirmationCreate {
	if t != nil {
		pcc.SetUpdatedAt(*t)
	}
	return pcc
}

// SetID sets the "id" field.
func (pcc *PaymentConfirmationCreate) SetID(u uuid.UUID) *PaymentConfirmationCreate {
	pcc.mutation.SetID(u)
	return pcc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (pcc *PaymentConfirmationCreate) SetNillableID(u *uuid.UUID) *PaymentConfirmationCreate {
	if u != nil {
		pcc.SetID(*u)
	}
	return pcc
}

// Mutation returns the PaymentConfirmationMutation object of the builder.
func (pcc *PaymentConfirmationCreate) Mutation() *PaymentConfirmationMutation {
	return pcc.mutation
}

// Save creates the PaymentConfirmation in the database.
func (pcc *PaymentConfirmationCreate) Save(ctx context.Context) (*PaymentConfirmation, error) {
	pcc.defaults()
	return withHooks(ctx, pcc.sqlSave, pcc.mutation, pcc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pcc *PaymentConfirmationCreate) SaveX(ctx context.Context) *PaymentConfirmation {
	v, err := pcc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcc *PaymentConfirmationCreate) Exec(ctx context.Context) error {
	_, err := pcc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcc *PaymentConfirmationCreate) ExecX(ctx context.Context) {
	if err := pcc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pcc *PaymentConfirmationCreate) defaults() {
	if _, ok := pcc.mutation.Currency(); !ok {
		v := paymentconfirmation.DefaultCurrency
		pcc.mutation.SetCurrency(v)
	}
	if _, ok := pcc.mutation.Status(); !ok {
		v := paymentconfirmation.DefaultStatus
		pcc.mutation.SetStatus(v)
	}
	if _, ok := pcc.mutation.ConfirmationMethod(); !ok {
		v := paymentconfirmation.DefaultConfirmationMethod
		pcc.mutation.SetConfirmationMethod(v)
	}
	if _, ok := pcc.mutation.CreatedAt(); !ok {
		v := paymentconfirmation.DefaultCreatedAt()
		pcc.mutation.SetCreatedAt(v)
	}
	if _, ok := pcc.mutation.UpdatedAt(); !ok {
		v := paymentconfirmation.DefaultUpdatedAt()
		pcc.mutation.SetUpdatedAt(v)
	}
	if _, ok := pcc.mutation.ID(); !ok {
		v := paymentconfirmation.DefaultID()
		pcc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pcc *PaymentConfirmationCreate) check() error {
	if _, ok := pcc.mutation.PaymentMethod(); !ok {
		return &ValidationError{Name: "payment_method", err: errors.New(`ent: missing required field "PaymentConfirmation.payment_method"`)}
	}
	if v, ok := pcc.mutation.PaymentMethod(); ok {
		if err := paymentconfirmation.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.payment_method": %w`, err)}
		}
	}
	if v, ok := pcc.mutation.ExternalPaymentID(); ok {
		if err := paymentconfirmation.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.external_payment_id": %w`, err)}
		}
	}
	if v, ok := pcc.mutation.BankTransactionID(); ok {
		if err := paymentconfirmation.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.bank_transaction_id": %w`, err)}
		}
	}
	if _, ok := pcc.mutation.AmountPaid(); !ok {
		return &ValidationError{Name: "amount_paid", err: errors.New(`ent: missing required field "PaymentConfirmation.amount_paid"`)}
	}
	if v, ok := pcc.mutation.AmountPaid(); ok {
		if err := paymentconfirmation.AmountPaidValidator(v); err != nil {
			return &ValidationError{Name: "amount_paid", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.amount_paid": %w`, err)}
		}
	}
	if _, ok := pcc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "PaymentConfirmation.currency"`)}
	}
	if v, ok := pcc.mutation.Currency(); ok {
		if err := paymentconfirmation.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.currency": %w`, err)}
		}
	}
	if _, ok := pcc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "PaymentConfirmation.status"`)}
	}
	if v, ok := pcc.mutation.Status(); ok {
		if err := paymentconfirmation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.status": %w`, err)}
		}
	}
	if _, ok := pcc.mutation.ConfirmationMethod(); !ok {
		return &ValidationError{Name: "confirmation_method", err: errors.New(`ent: missing required field "PaymentConfirmation.confirmation_method"`)}
	}
	if v, ok := pcc.mutation.ConfirmationMethod(); ok {
		if err := paymentconfirmation.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.confirmation_method": %w`, err)}
		}
	}
	if v, ok := pcc.mutation.FailureReason(); ok {
		if err := paymentconfirmation.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.failure_reason": %w`, err)}
		}
	}
	if _, ok := pcc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "PaymentConfirmation.created_at"`)}
	}
	if _, ok := pcc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "PaymentConfirmation.updated_at"`)}
	}
	return nil
}

func (pcc *PaymentConfirmationCreate) sqlSave(ctx context.Context) (*PaymentConfirmation, error) {
	if err := pcc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pcc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pcc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	pcc.mutation.id = &_node.ID
	pcc.mutation.done = true
	return _node, nil
}

func (pcc *PaymentConfirmationCreate) createSpec() (*PaymentConfirmation, *sqlgraph.CreateSpec) {
	var (
		_node = &PaymentConfirmation{config: pcc.config}
		_spec = sqlgraph.NewCreateSpec(paymentconfirmation.Table, sqlgraph.NewFieldSpec(paymentconfirmation.FieldID, field.TypeUUID))
	)
	if id, ok := pcc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := pcc.mutation.BankTransferID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransferID, field.TypeUUID, value)
		_node.BankTransferID = value
	}
	if value, ok := pcc.mutation.OrderID(); ok {
		_spec.SetField(paymentconfirmation.FieldOrderID, field.TypeUUID, value)
		_node.OrderID = value
	}
	if value, ok := pcc.mutation.SubscriptionID(); ok {
		_spec.SetField(paymentconfirmation.FieldSubscriptionID, field.TypeUUID, value)
		_node.SubscriptionID = value
	}
	if value, ok := pcc.mutation.PaymentMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentMethod, field.TypeString, value)
		_node.PaymentMethod = value
	}
	if value, ok := pcc.mutation.ExternalPaymentID(); ok {
		_spec.SetField(paymentconfirmation.FieldExternalPaymentID, field.TypeString, value)
		_node.ExternalPaymentID = value
	}
	if value, ok := pcc.mutation.BankTransactionID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransactionID, field.TypeString, value)
		_node.BankTransactionID = value
	}
	if value, ok := pcc.mutation.AmountPaid(); ok {
		_spec.SetField(paymentconfirmation.FieldAmountPaid, field.TypeInt64, value)
		_node.AmountPaid = value
	}
	if value, ok := pcc.mutation.Currency(); ok {
		_spec.SetField(paymentconfirmation.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := pcc.mutation.Status(); ok {
		_spec.SetField(paymentconfirmation.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := pcc.mutation.ConfirmedBy(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedBy, field.TypeUUID, value)
		_node.ConfirmedBy = value
	}
	if value, ok := pcc.mutation.ConfirmationMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationMethod, field.TypeString, value)
		_node.ConfirmationMethod = value
	}
	if value, ok := pcc.mutation.ConfirmationNotes(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationNotes, field.TypeString, value)
		_node.ConfirmationNotes = value
	}
	if value, ok := pcc.mutation.PaymentDetails(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDetails, field.TypeJSON, value)
		_node.PaymentDetails = value
	}
	if value, ok := pcc.mutation.WebhookData(); ok {
		_spec.SetField(paymentconfirmation.FieldWebhookData, field.TypeJSON, value)
		_node.WebhookData = value
	}
	if value, ok := pcc.mutation.PaymentDate(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDate, field.TypeTime, value)
		_node.PaymentDate = value
	}
	if value, ok := pcc.mutation.ConfirmedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedAt, field.TypeTime, value)
		_node.ConfirmedAt = value
	}
	if value, ok := pcc.mutation.FailureReason(); ok {
		_spec.SetField(paymentconfirmation.FieldFailureReason, field.TypeString, value)
		_node.FailureReason = value
	}
	if value, ok := pcc.mutation.Metadata(); ok {
		_spec.SetField(paymentconfirmation.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := pcc.mutation.CreatedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pcc.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// PaymentConfirmationCreateBulk is the builder for creating many PaymentConfirmation entities in bulk.
type PaymentConfirmationCreateBulk struct {
	config
	err      error
	builders []*PaymentConfirmationCreate
}

// Save creates the PaymentConfirmation entities in the database.
func (pccb *PaymentConfirmationCreateBulk) Save(ctx context.Context) ([]*PaymentConfirmation, error) {
	if pccb.err != nil {
		return nil, pccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pccb.builders))
	nodes := make([]*PaymentConfirmation, len(pccb.builders))
	mutators := make([]Mutator, len(pccb.builders))
	for i := range pccb.builders {
		func(i int, root context.Context) {
			builder := pccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PaymentConfirmationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pccb *PaymentConfirmationCreateBulk) SaveX(ctx context.Context) []*PaymentConfirmation {
	v, err := pccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pccb *PaymentConfirmationCreateBulk) Exec(ctx context.Context) error {
	_, err := pccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pccb *PaymentConfirmationCreateBulk) ExecX(ctx context.Context) {
	if err := pccb.Exec(ctx); err != nil {
		panic(err)
	}
}

package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	sharedconfig "github.com/social-content-ai/pkg-shared/config"
	"github.com/spf13/viper"
)

// Config extends the shared config with user service specific configuration
type Config struct {
	*sharedconfig.Config

	// Override database config with extended version
	Database DatabaseConfig `mapstructure:"database"`

	// User service specific configurations
	Security   SecurityConfig   `mapstructure:"security"`
	RateLimit  RateLimitConfig  `mapstructure:"rate_limit"`
	Email      EmailConfig      `mapstructure:"email"`
	Features   FeaturesConfig   `mapstructure:"features"`
	FileUpload FileUploadConfig `mapstructure:"file_upload"`
	CORS       CORSConfig       `mapstructure:"cors"`
	Validation ValidationConfig `mapstructure:"validation"`
	Cleanup    CleanupConfig    `mapstructure:"cleanup"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig

	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	BcryptCost               int           `mapstructure:"bcrypt_cost"`
	PasswordMinLength        int           `mapstructure:"password_min_length"`
	SessionDuration          time.Duration `mapstructure:"session_duration"`
	MaxSessionsPerUser       int           `mapstructure:"max_sessions_per_user"`
	RequireEmailVerification bool          `mapstructure:"require_email_verification"`
	Enable2FA                bool          `mapstructure:"enable_2fa"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled           bool `mapstructure:"enabled"`
	RequestsPerMinute int  `mapstructure:"requests_per_minute"`
	Burst             int  `mapstructure:"burst"`
}

// EmailConfig holds email service configuration
type EmailConfig struct {
	SMTPHost  string `mapstructure:"smtp_host"`
	SMTPPort  int    `mapstructure:"smtp_port"`
	Username  string `mapstructure:"username"`
	Password  string `mapstructure:"password"`
	FromEmail string `mapstructure:"from_email"`
	FromName  string `mapstructure:"from_name"`
}

// FeaturesConfig holds feature flags
type FeaturesConfig struct {
	UserRegistration  bool `mapstructure:"user_registration"`
	PasswordReset     bool `mapstructure:"password_reset"`
	EmailVerification bool `mapstructure:"email_verification"`
	SocialLogin       bool `mapstructure:"social_login"`
	AdminPanel        bool `mapstructure:"admin_panel"`
	UserAnalytics     bool `mapstructure:"user_analytics"`
}

// FileUploadConfig holds file upload configuration
type FileUploadConfig struct {
	MaxSize      string   `mapstructure:"max_size"`
	AllowedTypes []string `mapstructure:"allowed_types"`
	UploadPath   string   `mapstructure:"upload_path"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	ExposedHeaders   []string `mapstructure:"exposed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// ValidationConfig holds validation configuration
type ValidationConfig struct {
	EmailRegex    string `mapstructure:"email_regex"`
	PhoneRegex    string `mapstructure:"phone_regex"`
	PasswordRegex string `mapstructure:"password_regex"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	MetricsPort     int    `mapstructure:"metrics_port"`
	HealthCheckPath string `mapstructure:"health_check_path"`
	MetricsPath     string `mapstructure:"metrics_path"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Load environment-specific config
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	// Try to read environment-specific config
	viper.SetConfigName(env)
	if err := viper.MergeInConfig(); err != nil {
		// It's okay if environment-specific config doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read environment config: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Load shared config
	sharedCfg, err := sharedconfig.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}
	config.Config = sharedCfg

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.sqlite_path", "./data/user_service.db")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.name", "user_service_db")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// Security defaults
	viper.SetDefault("security.bcrypt_cost", 12)
	viper.SetDefault("security.password_min_length", 8)
	viper.SetDefault("security.session_duration", "24h")
	viper.SetDefault("security.max_sessions_per_user", 5)
	viper.SetDefault("security.require_email_verification", true)
	viper.SetDefault("security.enable_2fa", false)

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.requests_per_minute", 60)
	viper.SetDefault("rate_limit.burst", 10)

	// Email defaults
	viper.SetDefault("email.smtp_host", "smtp.gmail.com")
	viper.SetDefault("email.smtp_port", 587)
	viper.SetDefault("email.from_email", "<EMAIL>")
	viper.SetDefault("email.from_name", "Social Content AI")

	// Features defaults
	viper.SetDefault("features.user_registration", true)
	viper.SetDefault("features.password_reset", true)
	viper.SetDefault("features.email_verification", true)
	viper.SetDefault("features.social_login", false)
	viper.SetDefault("features.admin_panel", true)
	viper.SetDefault("features.user_analytics", true)

	// File upload defaults
	viper.SetDefault("file_upload.max_size", "10MB")
	viper.SetDefault("file_upload.allowed_types", []string{"image/jpeg", "image/png", "image/gif"})
	viper.SetDefault("file_upload.upload_path", "/tmp/uploads")

	// CORS defaults
	viper.SetDefault("cors.allowed_origins", []string{"http://localhost:3000"})
	viper.SetDefault("cors.allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("cors.allowed_headers", []string{"Content-Type", "Authorization", "X-Requested-With"})
	viper.SetDefault("cors.allow_credentials", true)
	viper.SetDefault("cors.max_age", 86400)

	// Validation defaults
	viper.SetDefault("validation.email_regex", "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
	viper.SetDefault("validation.phone_regex", "^\\+?[1-9]\\d{1,14}$")
	viper.SetDefault("validation.password_regex", "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$")

	// Monitoring defaults
	viper.SetDefault("monitoring.enabled", true)
	viper.SetDefault("monitoring.metrics_port", 9090)
	viper.SetDefault("monitoring.health_check_path", "/health")
	viper.SetDefault("monitoring.metrics_path", "/metrics")

	// Cleanup defaults
	viper.SetDefault("cleanup.enabled", true)
	viper.SetDefault("cleanup.auto_start", true)
	viper.SetDefault("cleanup.unverified_user_ttl", "24h")
	viper.SetDefault("cleanup.cleanup_interval", "1h")
	viper.SetDefault("cleanup.batch_size", 100)
	viper.SetDefault("cleanup.enable_notifications", true)
	viper.SetDefault("cleanup.warning_time", "2h")
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// GetDriverName returns the database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres"
	}
}

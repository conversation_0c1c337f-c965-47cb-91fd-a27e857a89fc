package events

import (
	"context"
	"fmt"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaConsumer defines interface for consuming Kafka messages
type KafkaConsumer interface {
	Subscribe(topics []string) error
	Consume(ctx context.Context, handler MessageHandler) error
	Close() error
}

// MessageHandler defines interface for handling Kafka messages
type MessageHandler interface {
	HandleMessage(ctx context.Context, topic string, message []byte) error
}

// SubscriptionEventConsumer consumes subscription events from Kafka
type SubscriptionEventConsumer struct {
	consumer KafkaConsumer
	handler  *SubscriptionEventHandler
	logger   logging.Logger
}

// NewSubscriptionEventConsumer creates a new subscription event consumer
func NewSubscriptionEventConsumer(consumer KafkaConsumer, handler *SubscriptionEventHandler, logger logging.Logger) *SubscriptionEventConsumer {
	return &SubscriptionEventConsumer{
		consumer: consumer,
		handler:  handler,
		logger:   logger,
	}
}

// Start starts consuming subscription events from all topics
func (c *SubscriptionEventConsumer) Start(ctx context.Context) error {
	c.logger.Info("Starting subscription event consumer")

	// Subscribe to all subscription topics
	topics := []string{
		// TopicSubscriptionEvents,
		TopicSubscriptionPaymentCompleted,
		TopicSubscriptionPaymentFailed,
		TopicSubscriptionActivated,
		TopicSubscriptionCancelled,
	}

	if err := c.consumer.Subscribe(topics); err != nil {
		return fmt.Errorf("failed to subscribe to topics: %w", err)
	}

	c.logger.WithField("topics", topics).Info("Subscribed to subscription topics")

	// Start consuming messages
	return c.consumer.Consume(ctx, c)
}

// HandleMessage implements MessageHandler interface
func (c *SubscriptionEventConsumer) HandleMessage(ctx context.Context, topic string, message []byte) error {
	c.logger.WithFields(map[string]interface{}{
		"topic":        topic,
		"message_size": len(message),
	}).Info("Received subscription event message")

	// Route message to appropriate handler based on topic
	switch topic {
	case TopicSubscriptionEvents:
		return c.handler.HandleSubscriptionEvents(ctx, message)
	case TopicSubscriptionPaymentCompleted:
		return c.handler.HandlePaymentCompletedEvents(ctx, message)
	case TopicSubscriptionPaymentFailed:
		return c.handler.HandlePaymentFailedEvents(ctx, message)
	case TopicSubscriptionActivated:
		return c.handler.HandleSubscriptionActivatedEvents(ctx, message)
	case TopicSubscriptionCancelled:
		return c.handler.HandleSubscriptionCancelledEvents(ctx, message)
	default:
		c.logger.WithField("topic", topic).Warn("Unknown subscription topic")
		return fmt.Errorf("unknown subscription topic: %s", topic)
	}
}

// Stop stops the consumer
func (c *SubscriptionEventConsumer) Stop() error {
	c.logger.Info("Stopping subscription event consumer")
	return c.consumer.Close()
}

// EventProcessingStats tracks event processing statistics
type EventProcessingStats struct {
	TotalProcessed        int64     `json:"total_processed"`
	PaymentCompleted      int64     `json:"payment_completed"`
	PaymentFailed         int64     `json:"payment_failed"`
	SubscriptionActivated int64     `json:"subscription_activated"`
	SubscriptionCancelled int64     `json:"subscription_cancelled"`
	GeneralEvents         int64     `json:"general_events"`
	LastProcessedAt       time.Time `json:"last_processed_at"`
	Errors                int64     `json:"errors"`
}

// StatsTracker tracks event processing statistics
type StatsTracker struct {
	stats  *EventProcessingStats
	logger logging.Logger
}

// NewStatsTracker creates a new stats tracker
func NewStatsTracker(logger logging.Logger) *StatsTracker {
	return &StatsTracker{
		stats: &EventProcessingStats{
			LastProcessedAt: time.Now(),
		},
		logger: logger,
	}
}

// TrackEvent tracks an event processing
func (s *StatsTracker) TrackEvent(topic string, success bool) {
	s.stats.TotalProcessed++
	s.stats.LastProcessedAt = time.Now()

	if !success {
		s.stats.Errors++
		return
	}

	switch topic {
	case TopicSubscriptionPaymentCompleted:
		s.stats.PaymentCompleted++
	case TopicSubscriptionPaymentFailed:
		s.stats.PaymentFailed++
	case TopicSubscriptionActivated:
		s.stats.SubscriptionActivated++
	case TopicSubscriptionCancelled:
		s.stats.SubscriptionCancelled++
	case TopicSubscriptionEvents:
		s.stats.GeneralEvents++
	}

	// Log stats every 100 events
	if s.stats.TotalProcessed%100 == 0 {
		s.logger.WithFields(map[string]interface{}{
			"total_processed":        s.stats.TotalProcessed,
			"payment_completed":      s.stats.PaymentCompleted,
			"payment_failed":         s.stats.PaymentFailed,
			"subscription_activated": s.stats.SubscriptionActivated,
			"subscription_cancelled": s.stats.SubscriptionCancelled,
			"general_events":         s.stats.GeneralEvents,
			"errors":                 s.stats.Errors,
		}).Info("Event processing statistics")
	}
}

// GetStats returns current statistics
func (s *StatsTracker) GetStats() *EventProcessingStats {
	return s.stats
}

// Reset resets the statistics
func (s *StatsTracker) Reset() {
	s.stats = &EventProcessingStats{
		LastProcessedAt: time.Now(),
	}
	s.logger.Info("Event processing statistics reset")
}

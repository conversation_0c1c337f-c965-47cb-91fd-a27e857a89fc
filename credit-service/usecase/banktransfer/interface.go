package banktransfer

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the bank transfer use case interface
type UseCase interface {
	// InitiateBankTransfer initiates a bank transfer payment
	InitiateBankTransfer(ctx context.Context, req *models.InitiateBankTransferRequest) (*models.InitiateBankTransferResponse, error)

	// GetBankTransferStatus gets the status of a bank transfer
	GetBankTransferStatus(ctx context.Context, transferID, userID string) (*models.BankTransferInfo, error)

	// ConfirmBankTransfer confirms a bank transfer payment (admin/webhook)
	ConfirmBankTransfer(ctx context.Context, req *models.ConfirmBankTransferRequest) (*models.ConfirmBankTransferResponse, error)

	// CancelBankTransfer cancels a pending bank transfer
	CancelBankTransfer(ctx context.Context, transferID, userID string) error

	// ListBankTransfers lists bank transfers for a user
	ListBankTransfers(ctx context.Context, req *models.ListBankTransfersRequest) (*models.ListBankTransfersResponse, error)

	// ProcessExpiredTransfers processes expired bank transfers
	ProcessExpiredTransfers(ctx context.Context) error

	// GenerateQRCode generates QR code for bank transfer
	GenerateQRCode(ctx context.Context, transferID string) (string, error)

	// ValidateBankTransfer validates bank transfer details
	ValidateBankTransfer(ctx context.Context, referenceCode string, amount int64) (*models.BankTransferValidationResult, error)

	// GetBankTransferByReference gets bank transfer by reference code
	GetBankTransferByReference(ctx context.Context, referenceCode string) (*models.BankTransferInfo, error)

	// UpdateBankTransferStatus updates bank transfer status
	UpdateBankTransferStatus(ctx context.Context, transferID string, status string, metadata map[string]interface{}) error

	// GetBankTransferStatistics gets bank transfer statistics
	GetBankTransferStatistics(ctx context.Context, req *models.BankTransferStatsRequest) (*models.BankTransferStatsResponse, error)

	// Admin methods
	AdminListBankTransfers(ctx context.Context, req *models.AdminListBankTransfersRequest) (*models.AdminBankTransfersResponse, error)
	AdminConfirmBankTransfer(ctx context.Context, req *models.AdminConfirmBankTransferRequest) (*models.ConfirmBankTransferResponse, error)
	AdminCancelBankTransfer(ctx context.Context, transferID string, reason string) error
	AdminGetBankTransferDetails(ctx context.Context, transferID string) (*models.BankTransferInfo, error)

	// Webhook methods
	HandleBankWebhook(ctx context.Context, req *models.BankWebhookRequest) (*models.BankWebhookResponse, error)
	ProcessBankWebhookEvent(ctx context.Context, event *models.BankWebhookEvent) error

	// Internal methods
	InternalConfirmBankTransfer(ctx context.Context, transferID string, actualAmount int64, bankTransactionID string) (*models.ConfirmBankTransferResponse, error)
	InternalGetBankTransfer(ctx context.Context, transferID string) (*models.BankTransferInfo, error)
	InternalUpdateBankTransfer(ctx context.Context, transferID string, updates map[string]interface{}) error
}

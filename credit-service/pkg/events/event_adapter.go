package events

import (
	"time"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
)

// EventAdapter adapts local credit service events to shared events
type EventAdapter struct{}

// NewEventAdapter creates a new event adapter
func NewEventAdapter() *EventAdapter {
	return &EventAdapter{}
}

// CreditAddedToSharedEvent converts CreditAddedEvent to shared BillingEvent
func (a *EventAdapter) CreditAddedToSharedEvent(event CreditAddedEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeCreditPurchased, event.UserID)
	
	// Map specific fields
	sharedEvent.Credits = event.Amount
	sharedEvent.WithData("amount", event.Amount)
	sharedEvent.WithData("new_balance", event.NewBalance)
	sharedEvent.WithData("source", event.Source_)
	sharedEvent.WithData("description", event.Description)
	sharedEvent.WithData("transaction_id", event.TransactionID)
	sharedEvent.WithData("reference_id", event.ReferenceID)
	sharedEvent.WithData("reference_type", event.ReferenceType)
	
	// Set priority based on amount
	if event.Amount >= 1000 {
		sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	} else {
		sharedEvent.WithPriority(sharedKafka.PriorityNormal)
	}
	
	return sharedEvent
}

// CreditDeductedToSharedEvent converts CreditDeductedEvent to shared BillingEvent
func (a *EventAdapter) CreditDeductedToSharedEvent(event CreditDeductedEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeCreditConsumed, event.UserID)
	
	// Map specific fields
	sharedEvent.Credits = event.Amount
	sharedEvent.WithData("amount", event.Amount)
	sharedEvent.WithData("new_balance", event.NewBalance)
	sharedEvent.WithData("purpose", event.Purpose)
	sharedEvent.WithData("description", event.Description)
	sharedEvent.WithData("transaction_id", event.TransactionID)
	sharedEvent.WithData("reference_id", event.ReferenceID)
	sharedEvent.WithData("reference_type", event.ReferenceType)
	
	return sharedEvent
}

// CreditTransferredToSharedEvent converts CreditTransferredEvent to shared BillingEvent
func (a *EventAdapter) CreditTransferredToSharedEvent(event CreditTransferredEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeCreditConsumed, event.FromUserID)
	
	// Map specific fields
	sharedEvent.Credits = event.Amount
	sharedEvent.WithData("amount", event.Amount)
	sharedEvent.WithData("from_user_id", event.FromUserID)
	sharedEvent.WithData("to_user_id", event.ToUserID)
	sharedEvent.WithData("reason", event.Reason)
	sharedEvent.WithData("transaction_id", event.TransactionID)
	sharedEvent.WithData("reference_id", event.ReferenceID)
	sharedEvent.WithData("reference_type", event.ReferenceType)
	sharedEvent.WithData("transfer_type", "user_to_user")
	
	return sharedEvent
}

// SubscriptionActivatedToSharedEvent converts SubscriptionActivatedEvent to shared BillingEvent
func (a *EventAdapter) SubscriptionActivatedToSharedEvent(event SubscriptionActivatedEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeSubscriptionRenewed, event.UserID)
	
	// Map specific fields
	sharedEvent.SubscriptionID = event.SubscriptionID
	sharedEvent.PlanName = event.PlanName
	sharedEvent.Credits = event.CreditsAdded
	sharedEvent.PaymentID = event.PaymentID
	
	sharedEvent.WithData("plan_id", event.PlanID)
	sharedEvent.WithData("plan_name", event.PlanName)
	sharedEvent.WithData("status", event.Status)
	sharedEvent.WithData("starts_at", event.StartsAt.Format(time.RFC3339))
	sharedEvent.WithData("ends_at", event.EndsAt.Format(time.RFC3339))
	sharedEvent.WithData("credits_added", event.CreditsAdded)
	sharedEvent.WithData("payment_id", event.PaymentID)
	
	sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	
	return sharedEvent
}

// SubscriptionCancelledToSharedEvent converts SubscriptionCancelledEvent to shared BillingEvent
func (a *EventAdapter) SubscriptionCancelledToSharedEvent(event SubscriptionCancelledEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeSubscriptionExpired, event.UserID)
	
	// Map specific fields
	sharedEvent.SubscriptionID = event.SubscriptionID
	sharedEvent.PlanName = event.PlanName
	
	sharedEvent.WithData("plan_id", event.PlanID)
	sharedEvent.WithData("plan_name", event.PlanName)
	sharedEvent.WithData("cancelled_at", event.CancelledAt.Format(time.RFC3339))
	sharedEvent.WithData("reason", event.Reason)
	sharedEvent.WithData("cancel_at_period_end", event.CancelAtPeriodEnd)
	
	sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	
	return sharedEvent
}

// SubscriptionRenewedToSharedEvent converts SubscriptionRenewedEvent to shared BillingEvent
func (a *EventAdapter) SubscriptionRenewedToSharedEvent(event SubscriptionRenewedEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeSubscriptionRenewed, event.UserID)
	
	// Map specific fields
	sharedEvent.SubscriptionID = event.SubscriptionID
	sharedEvent.PlanName = event.PlanName
	sharedEvent.Credits = event.CreditsAdded
	
	sharedEvent.WithData("plan_id", event.PlanID)
	sharedEvent.WithData("plan_name", event.PlanName)
	sharedEvent.WithData("new_period_start", event.NewPeriodStart.Format(time.RFC3339))
	sharedEvent.WithData("new_period_end", event.NewPeriodEnd.Format(time.RFC3339))
	sharedEvent.WithData("credits_added", event.CreditsAdded)
	
	sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	
	return sharedEvent
}

// PaymentSucceededToSharedEvent converts PaymentSucceededEvent to shared BillingEvent
func (a *EventAdapter) PaymentSucceededToSharedEvent(event PaymentSucceededEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypePaymentSucceeded, event.UserID)
	
	// Map specific fields
	sharedEvent.PaymentID = event.PaymentID
	sharedEvent.Amount = float64(event.Amount) / 100 // Convert cents to dollars
	sharedEvent.Currency = event.Currency
	sharedEvent.SubscriptionID = event.SubscriptionID
	
	sharedEvent.WithData("payment_id", event.PaymentID)
	sharedEvent.WithData("amount", event.Amount)
	sharedEvent.WithData("currency", event.Currency)
	sharedEvent.WithData("payment_method", event.PaymentMethod)
	sharedEvent.WithData("subscription_id", event.SubscriptionID)
	sharedEvent.WithData("invoice_id", event.InvoiceID)
	sharedEvent.WithData("purpose", event.Purpose)
	
	sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	
	return sharedEvent
}

// PaymentFailedToSharedEvent converts PaymentFailedEvent to shared BillingEvent
func (a *EventAdapter) PaymentFailedToSharedEvent(event PaymentFailedEvent) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypePaymentFailed, event.UserID)
	
	// Map specific fields
	sharedEvent.PaymentID = event.PaymentID
	sharedEvent.Amount = float64(event.Amount) / 100 // Convert cents to dollars
	sharedEvent.Currency = event.Currency
	sharedEvent.SubscriptionID = event.SubscriptionID
	
	sharedEvent.WithData("payment_id", event.PaymentID)
	sharedEvent.WithData("amount", event.Amount)
	sharedEvent.WithData("currency", event.Currency)
	sharedEvent.WithData("payment_method", event.PaymentMethod)
	sharedEvent.WithData("failure_code", event.FailureCode)
	sharedEvent.WithData("failure_message", event.FailureMessage)
	sharedEvent.WithData("subscription_id", event.SubscriptionID)
	sharedEvent.WithData("invoice_id", event.InvoiceID)
	
	sharedEvent.WithPriority(sharedKafka.PriorityCritical)
	
	return sharedEvent
}

// NotificationToSharedEvent converts NotificationEvent to shared BaseEvent
func (a *EventAdapter) NotificationToSharedEvent(event NotificationEvent) *sharedKafka.BaseEvent {
	sharedEvent := sharedKafka.NewBaseEvent(sharedKafka.EventTypeNotificationSent, "credit-service")
	sharedEvent.WithUserID(event.UserID)
	
	// Map notification data
	sharedEvent.WithData("type", event.Type)
	sharedEvent.WithData("template", event.Template)
	sharedEvent.WithData("subject", event.Subject)
	sharedEvent.WithData("message", event.Message)
	sharedEvent.WithData("notification_data", event.Data)
	
	// Set priority based on notification priority
	switch event.Priority {
	case "urgent":
		sharedEvent.WithPriority(sharedKafka.PriorityCritical)
	case "high":
		sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	case "normal":
		sharedEvent.WithPriority(sharedKafka.PriorityNormal)
	default:
		sharedEvent.WithPriority(sharedKafka.PriorityLow)
	}
	
	// Add scheduled time if present
	if event.ScheduledAt != nil {
		sharedEvent.WithData("scheduled_at", event.ScheduledAt.Format(time.RFC3339))
	}
	
	return sharedEvent
}

// CreditLowToSharedEvent creates a credit low warning event
func (a *EventAdapter) CreditLowToSharedEvent(userID string, currentCredits, threshold int) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeCreditLow, userID)
	
	sharedEvent.Credits = currentCredits
	sharedEvent.WithData("current_credits", currentCredits)
	sharedEvent.WithData("threshold_credits", threshold)
	sharedEvent.WithData("warning_type", "low_credits")
	
	sharedEvent.WithPriority(sharedKafka.PriorityHigh)
	
	return sharedEvent
}

// SubscriptionExpiredToSharedEvent creates a subscription expired event
func (a *EventAdapter) SubscriptionExpiredToSharedEvent(userID, subscriptionID, planName string, expiredAt time.Time) *sharedKafka.BillingEvent {
	sharedEvent := sharedKafka.NewBillingEvent(sharedKafka.EventTypeSubscriptionExpired, userID)
	
	sharedEvent.SubscriptionID = subscriptionID
	sharedEvent.PlanName = planName
	sharedEvent.WithData("subscription_id", subscriptionID)
	sharedEvent.WithData("plan_name", planName)
	sharedEvent.WithData("expired_at", expiredAt.Format(time.RFC3339))
	
	sharedEvent.WithPriority(sharedKafka.PriorityCritical)
	
	return sharedEvent
}

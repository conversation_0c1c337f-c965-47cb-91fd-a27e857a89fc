// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/predicate"
)

// PlatformIntegrationDelete is the builder for deleting a PlatformIntegration entity.
type PlatformIntegrationDelete struct {
	config
	hooks    []Hook
	mutation *PlatformIntegrationMutation
}

// Where appends a list predicates to the PlatformIntegrationDelete builder.
func (pid *PlatformIntegrationDelete) Where(ps ...predicate.PlatformIntegration) *PlatformIntegrationDelete {
	pid.mutation.Where(ps...)
	return pid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (pid *PlatformIntegrationDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, pid.sqlExec, pid.mutation, pid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (pid *PlatformIntegrationDelete) ExecX(ctx context.Context) int {
	n, err := pid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (pid *PlatformIntegrationDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(platformintegration.Table, sqlgraph.NewFieldSpec(platformintegration.FieldID, field.TypeUUID))
	if ps := pid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, pid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	pid.mutation.done = true
	return affected, err
}

// PlatformIntegrationDeleteOne is the builder for deleting a single PlatformIntegration entity.
type PlatformIntegrationDeleteOne struct {
	pid *PlatformIntegrationDelete
}

// Where appends a list predicates to the PlatformIntegrationDelete builder.
func (pido *PlatformIntegrationDeleteOne) Where(ps ...predicate.PlatformIntegration) *PlatformIntegrationDeleteOne {
	pido.pid.mutation.Where(ps...)
	return pido
}

// Exec executes the deletion query.
func (pido *PlatformIntegrationDeleteOne) Exec(ctx context.Context) error {
	n, err := pido.pid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{platformintegration.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (pido *PlatformIntegrationDeleteOne) ExecX(ctx context.Context) {
	if err := pido.Exec(ctx); err != nil {
		panic(err)
	}
}

package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/asset-service/usecase/asset"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Asset<PERSON>andler handles asset-related HTTP requests
type AssetHandler struct {
	assetUseCase asset.UseCase
	logger       logging.Logger
}

// NewAssetHandler creates a new asset handler
func NewAssetHandler(assetUseCase asset.UseCase, logger logging.Logger) *AssetHandler {
	return &AssetHandler{
		assetUseCase: assetUseCase,
		logger:       logger,
	}
}

// RequestUpload handles POST /api/v1/assets/upload/request
func (h *AssetHandler) RequestUpload(c *gin.Context) {
	var req models.RequestUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.assetUseCase.RequestUpload(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to request upload")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ConfirmUpload handles POST /api/v1/assets/upload/confirm
func (h *AssetHandler) ConfirmUpload(c *gin.Context) {
	var req models.ConfirmUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.assetUseCase.ConfirmUpload(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm upload")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetAsset handles GET /api/v1/assets/:id
func (h *AssetHandler) GetAsset(c *gin.Context) {
	assetID := c.Param("id")
	if assetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.assetUseCase.GetAsset(c.Request.Context(), assetID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get asset")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListAssets handles GET /api/v1/assets
func (h *AssetHandler) ListAssets(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	assetType := c.Query("type")
	status := c.Query("status")
	folderID := c.Query("folder_id")

	req := &models.ListAssetsRequest{
		UserID:   userID.(string),
		Page:     page,
		Limit:    limit,
		Type:     assetType,
		Status:   status,
		FolderID: folderID,
	}

	response, err := h.assetUseCase.ListAssets(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list assets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateAsset handles PUT /api/v1/assets/:id
func (h *AssetHandler) UpdateAsset(c *gin.Context) {
	assetID := c.Param("id")
	if assetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset ID is required"})
		return
	}

	var req models.UpdateAssetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.AssetID = assetID
	req.UserID = userID.(string)

	response, err := h.assetUseCase.UpdateAsset(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update asset")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteAsset handles DELETE /api/v1/assets/:id
func (h *AssetHandler) DeleteAsset(c *gin.Context) {
	assetID := c.Param("id")
	if assetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.assetUseCase.DeleteAsset(c.Request.Context(), assetID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete asset")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Asset deleted successfully"})
}

// GetAssetDownloadURL handles GET /api/v1/assets/:id/download
func (h *AssetHandler) GetAssetDownloadURL(c *gin.Context) {
	assetID := c.Param("id")
	if assetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get download URL from usecase
	downloadURL, err := h.assetUseCase.DownloadAsset(c.Request.Context(), assetID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get download URL")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"download_url": downloadURL,
		"expires_at":   time.Now().Add(time.Hour).Format(time.RFC3339),
	})
}

// GetAssetMetadata handles GET /api/v1/assets/:id/metadata
func (h *AssetHandler) GetAssetMetadata(c *gin.Context) {
	assetID := c.Param("id")
	if assetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get asset details (which includes metadata)
	assetResponse, err := h.assetUseCase.GetAsset(c.Request.Context(), assetID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get asset metadata")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Extract metadata
	metadata := gin.H{
		"id":         assetResponse.ID,
		"name":       assetResponse.Name,
		"type":       assetResponse.Type,
		"size":       assetResponse.FileInfo.Size,
		"mime_type":  assetResponse.FileInfo.MimeType,
		"created_at": assetResponse.CreatedAt,
		"updated_at": assetResponse.UpdatedAt,
		"status":     assetResponse.Status,
		"is_public":  assetResponse.AccessControl.IsPublic,
		"folder_id":  assetResponse.FolderID,
		"tags":       assetResponse.Tags,
		"metadata":   assetResponse.Metadata,
	}

	// Add image-specific metadata if available
	if assetResponse.ImageInfo != nil {
		metadata["image_info"] = gin.H{
			"width":  assetResponse.ImageInfo.Width,
			"height": assetResponse.ImageInfo.Height,
			"format": assetResponse.ImageInfo.Format,
		}
	}

	c.JSON(http.StatusOK, metadata)
}

// BulkDeleteAssets handles POST /api/v1/assets/bulk-delete
func (h *AssetHandler) BulkDeleteAssets(c *gin.Context) {
	var req models.BulkDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.assetUseCase.BulkDelete(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to bulk delete assets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CleanupStorage handles POST /api/v1/assets/cleanup
func (h *AssetHandler) CleanupStorage(c *gin.Context) {
	// This endpoint might be admin-only, add appropriate auth check
	response, err := h.assetUseCase.CleanupStorage(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to cleanup storage")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

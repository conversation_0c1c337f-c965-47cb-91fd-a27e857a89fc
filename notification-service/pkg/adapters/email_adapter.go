package adapters

import (
	"context"

	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/notification-service/pkg/email"
)

// EmailAdapter adapts email.SMTPSender to channels.EmailSender interface
type EmailAdapter struct {
	sender *email.SMTPSender
}

// NewEmailAdapter creates a new email adapter
func NewEmailAdapter(sender *email.SMTPSender) channels.EmailSender {
	return &EmailAdapter{
		sender: sender,
	}
}

// SendEmail sends an email by converting channels.EmailMessage to email.EmailMessage
func (a *EmailAdapter) SendEmail(ctx context.Context, message *channels.EmailMessage) error {
	emailMsg := &email.EmailMessage{
		To:      []string{message.To},
		Subject: message.Subject,
		Body:    message.Body,
	}

	// Set HTML body if message is HTML
	if message.IsHTML {
		emailMsg.HTMLBody = message.Body
		emailMsg.Body = "" // Clear text body when using HTML
	}

	return a.sender.SendEmail(ctx, emailMsg)
}

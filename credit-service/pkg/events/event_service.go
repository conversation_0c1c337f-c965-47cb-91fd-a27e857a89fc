package events

import (
	"context"
	"fmt"

	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// EventService manages subscription event processing
type EventService struct {
	logger          logging.Logger
	subscriptionUC  subscription.UseCase
	creditUC        credit.UseCase
	transactionUC   TransactionService
	notificationSvc NotificationService
	eventHandler    MessageHandler
	eventConsumer   *SubscriptionEventConsumer
	statsTracker    *StatsTracker
	kafkaConsumer   KafkaConsumer
	config          *EventServiceConfig
	isRunning       bool
}

// EventServiceConfig holds configuration for event service
type EventServiceConfig struct {
	KafkaConfig *sharedKafka.Config `json:"kafka_config"`
	EnableMock  bool                `json:"enable_mock"`
	ServiceName string              `json:"service_name"`
	Topics      []string            `json:"topics"`
}

// NewEventService creates a new event service with all dependencies
func NewEventService(
	config *EventServiceConfig,
	logger logging.Logger,
	subscriptionUC subscription.UseCase,
	creditUC credit.UseCase,
	transactionUC TransactionService,
	notificationSvc NotificationService,
) (*EventService, error) {

	// Create Kafka consumer using pkg-shared or mock
	var kafkaConsumer KafkaConsumer
	if config.EnableMock {
		kafkaConsumer = NewMockKafkaConsumer(logger)
	} else {
		// Use KafkaAdapter with pkg-shared/kafka
		kafkaAdapter := NewKafkaAdapter(config.KafkaConfig, logger)
		kafkaConsumer = kafkaAdapter
	}

	// Create notification service if not provided
	if notificationSvc == nil {
		notificationSvc = NewMockNotificationService(logger)
	}

	// Create event handler with dependencies
	eventHandler := NewSubscriptionEventHandler(logger, subscriptionUC, creditUC, transactionUC, notificationSvc)

	// Create event consumer
	eventConsumer := NewSubscriptionEventConsumer(kafkaConsumer, eventHandler, logger)

	// Create stats tracker
	statsTracker := NewStatsTracker(logger)

	return &EventService{
		logger:          logger,
		subscriptionUC:  subscriptionUC,
		creditUC:        creditUC,
		transactionUC:   transactionUC,
		notificationSvc: notificationSvc,
		eventHandler:    eventHandler,
		eventConsumer:   eventConsumer,
		statsTracker:    statsTracker,
		kafkaConsumer:   kafkaConsumer,
		config:          config,
	}, nil
}

// Start starts the event service
func (s *EventService) Start(ctx context.Context) error {
	s.logger.Info("Starting subscription event service")
	s.isRunning = true

	// Start event consumer
	go func() {
		if err := s.eventConsumer.Start(ctx); err != nil {
			if err != context.Canceled {
				s.logger.WithError(err).Error("Event consumer error")
			}
		}
		s.isRunning = false
	}()

	s.logger.Info("Subscription event service started successfully")
	return nil
}

// Stop stops the event service
func (s *EventService) Stop() error {
	s.logger.Info("Stopping subscription event service")

	// Stop event consumer
	if err := s.eventConsumer.Stop(); err != nil {
		s.logger.WithError(err).Error("Failed to stop event consumer")
		return fmt.Errorf("failed to stop event consumer: %w", err)
	}

	s.logger.Info("Subscription event service stopped successfully")
	s.isRunning = false
	return nil
}

// IsRunning returns whether the event service is currently running
func (s *EventService) IsRunning() bool {
	return s.isRunning
}

// GetStats returns event processing statistics
func (s *EventService) GetStats() *EventProcessingStats {
	return s.statsTracker.GetStats()
}

// ResetStats resets event processing statistics
func (s *EventService) ResetStats() {
	s.statsTracker.Reset()
}

// GetEventHandler returns the event handler for direct access
func (s *EventService) GetEventHandler() MessageHandler {
	return s.eventHandler
}

// ProcessEvent processes a single event directly (for testing or manual processing)
func (s *EventService) ProcessEvent(ctx context.Context, topic string, message []byte) error {
	s.logger.WithFields(map[string]interface{}{
		"topic":        topic,
		"message_size": len(message),
	}).Info("Processing event directly")

	// Track event processing
	err := s.eventHandler.HandleMessage(ctx, topic, message)
	s.statsTracker.TrackEvent(topic, err == nil)

	if err != nil {
		s.logger.WithError(err).Error("Failed to process event")
		return fmt.Errorf("failed to process event: %w", err)
	}

	return nil
}

// HandleMessage implements MessageHandler interface for stats tracking
func (s *EventService) HandleMessage(ctx context.Context, topic string, message []byte) error {
	// Track event processing
	err := s.eventHandler.HandleMessage(ctx, topic, message)
	s.statsTracker.TrackEvent(topic, err == nil)
	return err
}

// PublishEvent publishes an event using pkg-shared producer (if available)
func (s *EventService) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	// Check if we have a KafkaAdapter
	if kafkaAdapter, ok := s.kafkaConsumer.(*KafkaAdapter); ok {
		return kafkaAdapter.PublishEvent(ctx, topic, event)
	}

	// Fallback to logging for mock consumer
	s.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"event": event,
	}).Info("Mock: Publishing event")

	return nil
}

// GetKafkaAdapter returns the Kafka adapter if available
func (s *EventService) GetKafkaAdapter() *KafkaAdapter {
	if kafkaAdapter, ok := s.kafkaConsumer.(*KafkaAdapter); ok {
		return kafkaAdapter
	}
	return nil
}

// DefaultEventServiceConfig returns default configuration
func DefaultEventServiceConfig() *EventServiceConfig {
	kafkaConfig := sharedKafka.NewDefaultConfig()
	kafkaConfig.Brokers = []string{"localhost:9092"}
	kafkaConfig.Consumer.GroupID = "credit-service-subscription-events"

	return &EventServiceConfig{
		KafkaConfig: kafkaConfig,
		EnableMock:  true, // Default to mock for development
		ServiceName: "credit-service-subscription-events",
		Topics: []string{
			TopicSubscriptionEvents,
			TopicSubscriptionPaymentCompleted,
			TopicSubscriptionPaymentFailed,
			TopicSubscriptionActivated,
			TopicSubscriptionCancelled,
		},
	}
}

// ProductionEventServiceConfig returns production configuration
func ProductionEventServiceConfig(kafkaBrokers []string, groupID string) *EventServiceConfig {
	kafkaConfig := sharedKafka.NewDefaultConfig()
	kafkaConfig.Brokers = kafkaBrokers
	kafkaConfig.Consumer.GroupID = groupID

	return &EventServiceConfig{
		KafkaConfig: kafkaConfig,
		EnableMock:  false, // Use real Kafka in production
		ServiceName: "credit-service-subscription-events",
		Topics: []string{
			TopicSubscriptionEvents,
			TopicSubscriptionPaymentCompleted,
			TopicSubscriptionPaymentFailed,
			TopicSubscriptionActivated,
			TopicSubscriptionCancelled,
		},
	}
}

// EventServiceBuilder helps build EventService with optional dependencies
type EventServiceBuilder struct {
	config          *EventServiceConfig
	logger          logging.Logger
	subscriptionUC  subscription.UseCase
	creditUC        credit.UseCase
	transactionUC   TransactionService
	notificationSvc NotificationService
}

// NewEventServiceBuilder creates a new event service builder
func NewEventServiceBuilder() *EventServiceBuilder {
	return &EventServiceBuilder{
		config: DefaultEventServiceConfig(),
	}
}

// WithConfig sets the configuration
func (b *EventServiceBuilder) WithConfig(config *EventServiceConfig) *EventServiceBuilder {
	b.config = config
	return b
}

// WithLogger sets the logger
func (b *EventServiceBuilder) WithLogger(logger logging.Logger) *EventServiceBuilder {
	b.logger = logger
	return b
}

// WithSubscriptionUseCase sets the subscription use case
func (b *EventServiceBuilder) WithSubscriptionUseCase(uc subscription.UseCase) *EventServiceBuilder {
	b.subscriptionUC = uc
	return b
}

// WithCreditUseCase sets the credit use case
func (b *EventServiceBuilder) WithCreditUseCase(uc credit.UseCase) *EventServiceBuilder {
	b.creditUC = uc
	return b
}

// WithTransactionUseCase sets the transaction use case
func (b *EventServiceBuilder) WithTransactionUseCase(uc TransactionService) *EventServiceBuilder {
	b.transactionUC = uc
	return b
}

// WithNotificationService sets the notification service
func (b *EventServiceBuilder) WithNotificationService(svc NotificationService) *EventServiceBuilder {
	b.notificationSvc = svc
	return b
}

// Build builds the event service
func (b *EventServiceBuilder) Build() (*EventService, error) {
	if b.logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	return NewEventService(
		b.config,
		b.logger,
		b.subscriptionUC,
		b.creditUC,
		b.transactionUC,
		b.notificationSvc,
	)
}

// HealthCheck checks the health of the event service
func (s *EventService) HealthCheck() error {
	// Check if event handler is available
	if s.eventHandler == nil {
		return fmt.Errorf("event handler is not available")
	}

	// Check if event consumer is available
	if s.eventConsumer == nil {
		return fmt.Errorf("event consumer is not available")
	}

	// Check if stats tracker is available
	if s.statsTracker == nil {
		return fmt.Errorf("stats tracker is not available")
	}

	s.logger.Info("Event service health check passed")
	return nil
}

// GetTopics returns the list of topics this service handles
func (s *EventService) GetTopics() []string {
	return []string{
		TopicSubscriptionEvents,
		TopicSubscriptionPaymentCompleted,
		TopicSubscriptionPaymentFailed,
		TopicSubscriptionActivated,
		TopicSubscriptionCancelled,
	}
}

// GetServiceInfo returns information about the event service
func (s *EventService) GetServiceInfo() map[string]interface{} {
	stats := s.GetStats()
	return map[string]interface{}{
		"service_name":         "subscription-event-service",
		"topics":               s.GetTopics(),
		"total_processed":      stats.TotalProcessed,
		"last_processed_at":    stats.LastProcessedAt,
		"errors":               stats.Errors,
		"has_subscription_uc":  s.subscriptionUC != nil,
		"has_credit_uc":        s.creditUC != nil,
		"has_notification_svc": s.notificationSvc != nil,
	}
}

# Credit Service Environment Variables
# Copy this file to .env and update the values for your environment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
CREDIT_SERVICE_SERVER_ENV=development
CREDIT_SERVICE_SERVER_GRPC_PORT=50053
CREDIT_SERVICE_SERVER_HTTP_PORT=8083
CREDIT_SERVICE_SERVER_HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database type: "postgres" or "sqlite"
CREDIT_SERVICE_DATABASE_TYPE=postgres

# PostgreSQL configuration (when using postgres)
CREDIT_SERVICE_DATABASE_HOST=localhost
CREDIT_SERVICE_DATABASE_PORT=5432
CREDIT_SERVICE_DATABASE_USER=postgres
CREDIT_SERVICE_DATABASE_PASSWORD=postgres
CREDIT_SERVICE_DATABASE_NAME=credit_service_db
CREDIT_SERVICE_DATABASE_SSL_MODE=disable

# SQLite configuration (when using sqlite)
CREDIT_SERVICE_DATABASE_SQLITE_PATH=./data/credit_service.db

# Connection pool settings
CREDIT_SERVICE_DATABASE_MAX_OPEN_CONNS=25
CREDIT_SERVICE_DATABASE_MAX_IDLE_CONNS=5
CREDIT_SERVICE_DATABASE_CONN_MAX_LIFETIME=5m
CREDIT_SERVICE_DATABASE_CONN_MAX_IDLE_TIME=5m

# =============================================================================
# PAYMENT GATEWAY CONFIGURATION
# =============================================================================
# Stripe configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# PayPal configuration
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_MODE=sandbox
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id_here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
CREDIT_SERVICE_SERVICES_USER_SERVICE=localhost:50051

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
CREDIT_SERVICE_JWT_TOKEN_DURATION=24h
CREDIT_SERVICE_JWT_ISSUER=credit-service

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
CREDIT_SERVICE_KAFKA_BROKERS=localhost:9092
CREDIT_SERVICE_KAFKA_GROUP_ID=credit-service

# Kafka topics
CREDIT_SERVICE_KAFKA_TOPICS_CREDIT_EVENTS=credit.events
CREDIT_SERVICE_KAFKA_TOPICS_SUBSCRIPTION_EVENTS=subscription.events
CREDIT_SERVICE_KAFKA_TOPICS_PAYMENT_EVENTS=payment.events
CREDIT_SERVICE_KAFKA_TOPICS_NOTIFICATION_EVENTS=notification.events

# Kafka producer settings
CREDIT_SERVICE_KAFKA_PRODUCER_BATCH_SIZE=100
CREDIT_SERVICE_KAFKA_PRODUCER_BATCH_TIMEOUT=10ms
CREDIT_SERVICE_KAFKA_PRODUCER_COMPRESSION=snappy

# Kafka consumer settings
CREDIT_SERVICE_KAFKA_CONSUMER_MIN_BYTES=10240
CREDIT_SERVICE_KAFKA_CONSUMER_MAX_BYTES=10485760
CREDIT_SERVICE_KAFKA_CONSUMER_COMMIT_INTERVAL=1s

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
CREDIT_SERVICE_LOGGING_LEVEL=info
CREDIT_SERVICE_LOGGING_FORMAT=json
CREDIT_SERVICE_LOGGING_OUTPUT=stdout

# =============================================================================
# METRICS AND MONITORING
# =============================================================================
CREDIT_SERVICE_METRICS_PORT=9093

# Prometheus configuration
CREDIT_SERVICE_MONITORING_PROMETHEUS_ENABLED=true

# Jaeger tracing configuration
CREDIT_SERVICE_MONITORING_TRACING_ENABLED=false
CREDIT_SERVICE_MONITORING_TRACING_JAEGER_ENDPOINT=http://localhost:14268/api/traces
CREDIT_SERVICE_MONITORING_TRACING_SERVICE_NAME=credit-service

# =============================================================================
# CACHE CONFIGURATION (Redis)
# =============================================================================
CREDIT_SERVICE_CACHE_ENABLED=false
CREDIT_SERVICE_CACHE_REDIS_HOST=localhost
CREDIT_SERVICE_CACHE_REDIS_PORT=6379
CREDIT_SERVICE_CACHE_REDIS_PASSWORD=
CREDIT_SERVICE_CACHE_REDIS_DB=0
CREDIT_SERVICE_CACHE_REDIS_POOL_SIZE=10

# Cache TTL settings
CREDIT_SERVICE_CACHE_TTL_USER_CREDITS=5m
CREDIT_SERVICE_CACHE_TTL_SUBSCRIPTION_PLANS=1h
CREDIT_SERVICE_CACHE_TTL_PAYMENT_STATUS=30s

# =============================================================================
# FEATURE FLAGS
# =============================================================================
CREDIT_SERVICE_FEATURES_PAYPAL_ENABLED=false
CREDIT_SERVICE_FEATURES_BANK_TRANSFER_ENABLED=false
CREDIT_SERVICE_FEATURES_REFERRAL_SYSTEM_ENABLED=true
CREDIT_SERVICE_FEATURES_CREDIT_EXPIRATION_ENABLED=false
CREDIT_SERVICE_FEATURES_AUTO_SUBSCRIPTION_RENEWAL=true

# =============================================================================
# CREDIT SYSTEM CONFIGURATION
# =============================================================================
# Default credit amounts
CREDIT_SERVICE_CREDIT_DEFAULTS_WELCOME_BONUS=100
CREDIT_SERVICE_CREDIT_DEFAULTS_REFERRAL_BONUS=50
CREDIT_SERVICE_CREDIT_DEFAULTS_DAILY_LOGIN_BONUS=5

# Credit limits
CREDIT_SERVICE_CREDIT_LIMITS_MAX_DAILY_EARN=500
CREDIT_SERVICE_CREDIT_LIMITS_MAX_TRANSFER_AMOUNT=1000
CREDIT_SERVICE_CREDIT_LIMITS_MIN_TRANSFER_AMOUNT=10

# Credit expiration
CREDIT_SERVICE_CREDIT_EXPIRATION_ENABLED=false
CREDIT_SERVICE_CREDIT_EXPIRATION_DAYS=365

# =============================================================================
# SUBSCRIPTION CONFIGURATION
# =============================================================================
# Trial settings
CREDIT_SERVICE_SUBSCRIPTION_TRIAL_ENABLED=true
CREDIT_SERVICE_SUBSCRIPTION_TRIAL_DEFAULT_DAYS=7

# Grace period and renewal
CREDIT_SERVICE_SUBSCRIPTION_GRACE_PERIOD_DAYS=3
CREDIT_SERVICE_SUBSCRIPTION_AUTO_RENEWAL_ENABLED=true
CREDIT_SERVICE_SUBSCRIPTION_AUTO_RENEWAL_RETRY_ATTEMPTS=3
CREDIT_SERVICE_SUBSCRIPTION_AUTO_RENEWAL_RETRY_INTERVAL=24h

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS settings
CREDIT_SERVICE_SECURITY_CORS_ENABLED=true
CREDIT_SERVICE_SECURITY_CORS_ALLOWED_ORIGINS=http://localhost:3000,https://app.socialcontentai.com

# Request validation
CREDIT_SERVICE_SECURITY_VALIDATION_MAX_REQUEST_SIZE=10MB
CREDIT_SERVICE_SECURITY_VALIDATION_TIMEOUT=30s

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
CREDIT_SERVICE_BACKUP_ENABLED=false
CREDIT_SERVICE_BACKUP_SCHEDULE="0 2 * * *"
CREDIT_SERVICE_BACKUP_RETENTION_DAYS=30

# S3 backup configuration
BACKUP_S3_BUCKET=your-backup-bucket
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
CREDIT_SERVICE_DEVELOPMENT_DEBUG=true
CREDIT_SERVICE_DEVELOPMENT_MOCK_SERVICES_USER_SERVICE=false
CREDIT_SERVICE_DEVELOPMENT_MOCK_SERVICES_PAYMENT_GATEWAY=false
CREDIT_SERVICE_DEVELOPMENT_SEED_DATA=true
CREDIT_SERVICE_DEVELOPMENT_SWAGGER_ENABLED=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
CREDIT_SERVICE_PRODUCTION_PERFORMANCE_ENABLE_GZIP=true
CREDIT_SERVICE_PRODUCTION_PERFORMANCE_ENABLE_CACHING=true
CREDIT_SERVICE_PRODUCTION_SECURITY_HIDE_SERVER_HEADER=true
CREDIT_SERVICE_PRODUCTION_SECURITY_ENABLE_RATE_LIMITING=true
CREDIT_SERVICE_PRODUCTION_SECURITY_REQUIRE_HTTPS=true

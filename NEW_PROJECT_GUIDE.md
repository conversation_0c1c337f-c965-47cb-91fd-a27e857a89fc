
# 📚 Hướng Dẫn Khởi Tạo Go Microservice – REST & gRPC API

> **Mục tiêu**: <PERSON><PERSON> cấp một tài liệu “một‑phát‑ăn‑ngay” cho AI assistant và lập trình viên mới, gi<PERSON><PERSON> tạo nhanh bộ khung (boilerplate) **Clean Architecture** với **REST API** (Chi router) và **gRPC API** (Protocol Buffers) trong Go.

---

## 1. Kiến trúc & C<PERSON>u trúc thư mục

```text
your‑service/
├── api/                     # ⭐ Presentation layer
│   ├── restful/             #   ├─ REST API (Chi)
│   │   └── ...handlers.go
│   ├── grpc/                #   └─ gRPC API (pb + handlers)
│   │   ├── proto/           #       .proto files
│   │   └── ...handler.go
│   └── http.go              #   HTTP server config
├── ent/                     # ⭐ Data layer (Ent ORM)
│   ├── schema/              #   schema *.go
│   └── migrate/
├── usecase/                 # ⭐ Domain / Business logic
│   └── <domain>/            #   interface.go, service.go …
├── pkg/                     # ⭐ Shared utilities & models
│   ├── models/
│   └── util/
├── server.go                #   gRPC bootstrap
├── web.go                   #   REST / GraphQL gateway
├── Makefile                 #   Automation scripts
└── Dockerfile               #   Containerisation
```

> *Folder layout kế thừa & rút gọn từ các template sản xuất đã kiểm chứng* fileciteturn0file3

---

## 2. Thiết lập dự án mới ‑ _Từ số 0 tới chạy được_

### Bước 1  Khởi tạo module & thư mục

```bash
mkdir your‑service && cd $_
go mod init gitlab.com/your‑org/your‑service
mkdir -p api/{restful,grpc/proto} usecase ent/schema pkg/{models,util} kubernetes
```

### Bước 2  Cài dependencies bắt buộc

```bash
go get go-micro.dev/v4        github.com/go-chi/chi/v5        entgo.io/ent/cmd/ent        github.com/99designs/gqlgen        google.golang.org/protobuf/cmd/protoc-gen-go        google.golang.org/grpc/cmd/protoc-gen-go-grpc        github.com/jackc/pgx/v4        github.com/stretchr/testify
```

> Tham khảo ma trận phiên bản và tại sao chọn các package này trong **Documentation Index** fileciteturn0file0

### Bước 3  Khai báo Entity đầu tiên (Ent)

```bash
go run entgo.io/ent/cmd/ent init User
go run entgo.io/ent/cmd/ent generate --feature privacy ./ent/schema
```

### Bước 4  Định nghĩa gRPC service

```proto
// api/grpc/proto/user.proto
syntax = "proto3";
package user.v1;

service UserService {
  rpc Create (CreateUserRequest) returns (User) {}
  rpc Get    (GetUserRequest)    returns (User) {}
}

message CreateUserRequest { string name = 1; string email = 2; }
message GetUserRequest    { uint64 id = 1; }
message User { uint64 id = 1; string name = 2; string email = 3; }
```

```bash
protoc --go_out=. --go-grpc_out=. -I . api/grpc/proto/*.proto
```

### Bước 5  Tạo REST handler tương đương

```go
// api/restful/user.go
func (h *Handler) CreateUser(w http.ResponseWriter, r *http.Request) {
    var req CreateUserReq
    _ = json.NewDecoder(r.Body).Decode(&req)
    user, err := h.userUC.Create(r.Context(), &req)
    respondJSON(w, user, err)
}
```

> Quy ước đặt handler, request model & validate kế thừa từ **Quick Start Guide** fileciteturn0file2

### Bước 6  Hiện thực UseCase layer

```go
type UseCase interface {
    Create(ctx context.Context, req *CreateUserReq) (*ent.User, error)
    Get(ctx context.Context, id uint64) (*ent.User, error)
}

type service struct{ read, write *ent.Client }
```

### Bước 7  Khởi chạy server

```go
func main() {
    service := micro.NewService(micro.Name("grpc-your-service"))
    service.Init()

    // gRPC
    proto.RegisterUserServiceHandler(service.Server(), grpc.NewUserGrpc(userUC))

    // REST
    webSrv, _ := InitWebService(":8080")
    webSrv.Handle("/api/", restful.Router(userUC))

    service.Run()
}
```

### Bước 8  Viết Makefile & Dockerfile

Snippet Makefile tối giản: (xem thêm template) fileciteturn0file1  
Snippet Dockerfile (Alpine + health‑probe) fileciteturn0file4

---

## 3. Lệnh thường dùng

```bash
make ent-go        # regenerate Ent code
make proto         # protoc generate
make run           # run locally
make build-image   # docker build
make test          # unit + integration tests
```

## 4. Phần mở rộng

| Tính năng | File bắt đầu | Ghi chú |
|-----------|--------------|---------|
| **JWT Auth** | `usecase/auth` | Tham khảo Auth Service template fileciteturn0file4 |
| **GraphQL** | `api/graphql/` | Kích hoạt nếu cần client web realtime |
| **CI/CD** | `.gitlab-ci.yml` | Dựa trên pipeline mẫu trong README template fileciteturn0file1 |

---

## 5. Checklist hoàn thiện MVP

- [ ] Entity + migration ban đầu
- [ ] REST + gRPC endpoints tối thiểu
- [ ] Unit test coverage ≥ 80 %
- [ ] Docker image multi‑stage < 25 MB
- [ ] README cập nhật hướng dẫn local dev & prod deploy

---

> **Bạn đã sẵn sàng!** Từ đây có thể fork, đổi namespace, và hack tiếp 🚀

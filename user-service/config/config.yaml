# User Service Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50051
  http_port: 8080
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"

  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "user_service_db"
  ssl_mode: "disable"

  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/user_service.db"

  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# JWT configuration
jwt:
  secret_key: "your-super-secret-jwt-key-change-this-in-production"
  token_duration: "24h"
  refresh_duration: "168h" # 7 days
  issuer: "user-service"
  audience: "social-content-ai"

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Redis configuration (for session storage)
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  pool_timeout: "4s"
  idle_timeout: "5m"

# Email configuration (for password reset, etc.)
email:
  smtp_host: "smtp.gmail.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-app-password"
  from_email: "<EMAIL>"
  from_name: "Social Content AI"

# Rate limiting configuration
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# Security configuration
security:
  bcrypt_cost: 12
  password_min_length: 8
  session_duration: "24h"
  max_sessions_per_user: 5
  require_email_verification: true
  enable_2fa: false

# Monitoring configuration
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_path: "/health"
  metrics_path: "/metrics"

# Feature flags
features:
  user_registration: true
  password_reset: true
  email_verification: true
  social_login: false
  admin_panel: true
  user_analytics: true

# External services
external_services:
  notification_service:
    host: "localhost"
    port: 50052
    timeout: "10s"
  
  content_service:
    host: "localhost"
    port: 50053
    timeout: "10s"
  
  billing_service:
    host: "localhost"
    port: 50054
    timeout: "10s"

# Kafka configuration (for event publishing)
kafka:
  brokers:
    - "localhost:9092"
  consumer:
    group_id: "user-service-consumer"
    auto_offset_reset: "earliest"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: 1
    buffer_memory: 33554432

# File upload configuration
file_upload:
  max_size: "10MB"
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
  upload_path: "/tmp/uploads"
  
# CORS configuration
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "https://app.social-content-ai.com"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  expose_headers:
    - "X-Total-Count"
  allow_credentials: true
  max_age: 86400

# Validation configuration
validation:
  email_regex: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  phone_regex: "^\\+?[1-9]\\d{1,14}$"
  password_regex: "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$"

# Cleanup configuration
cleanup:
  # General cleanup settings
  expired_sessions_interval: "1h"
  old_sessions_retention: "30d"
  deleted_users_retention: "90d"

  # Unverified user cleanup settings
  enabled: true
  auto_start: true
  unverified_user_ttl: "24h"      # Delete unverified users after 24 hours
  cleanup_interval: "1h"          # Run cleanup every hour
  batch_size: 100                 # Process 100 users per batch
  enable_notifications: true      # Send warning emails before deletion
  warning_time: "2h"              # Send warning 2 hours before deletion

package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	sharedconfig "github.com/social-content-ai/pkg-shared/config"
	"github.com/spf13/viper"
)

// Config extends the shared config with notification service specific configuration
type Config struct {
	*sharedconfig.Config

	// Override database config with extended version
	Database DatabaseConfig `mapstructure:"database"`

	// Notification service specific configurations
	Email      EmailConfig      `mapstructure:"email"`
	SMS        SMSConfig        `mapstructure:"sms"`
	Push       PushConfig       `mapstructure:"push"`
	WebSocket  WebSocketConfig  `mapstructure:"websocket"`
	Kafka      KafkaConfig      `mapstructure:"kafka"`
	Templates  TemplatesConfig  `mapstructure:"templates"`
	RateLimit  RateLimitConfig  `mapstructure:"rate_limit"`
	Features   FeaturesConfig   `mapstructure:"features"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig

	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// EmailConfig holds email service configuration
type EmailConfig struct {
	SMTPHost      string `mapstructure:"smtp_host"`
	SMTPPort      int    `mapstructure:"smtp_port"`
	Username      string `mapstructure:"username"`
	Password      string `mapstructure:"password"`
	FromEmail     string `mapstructure:"from_email"`
	FromName      string `mapstructure:"from_name"`
	UseTLS        bool   `mapstructure:"use_tls"`
	RateLimit     int    `mapstructure:"rate_limit"`
	RetryAttempts int    `mapstructure:"retry_attempts"`
}

// SMSConfig holds SMS service configuration
type SMSConfig struct {
	Provider      string `mapstructure:"provider"`
	APIKey        string `mapstructure:"api_key"`
	APISecret     string `mapstructure:"api_secret"`
	FromNumber    string `mapstructure:"from_number"`
	RateLimit     int    `mapstructure:"rate_limit"`
	RetryAttempts int    `mapstructure:"retry_attempts"`
}

// PushConfig holds push notification configuration
type PushConfig struct {
	Provider      string `mapstructure:"provider"`
	APIKey        string `mapstructure:"api_key"`
	RateLimit     int    `mapstructure:"rate_limit"`
	RetryAttempts int    `mapstructure:"retry_attempts"`
}

// WebSocketConfig holds WebSocket configuration
type WebSocketConfig struct {
	Enabled        bool          `mapstructure:"enabled"`
	MaxConnections int           `mapstructure:"max_connections"`
	PingInterval   time.Duration `mapstructure:"ping_interval"`
	WriteTimeout   time.Duration `mapstructure:"write_timeout"`
	ReadTimeout    time.Duration `mapstructure:"read_timeout"`
}

// KafkaConfig holds Kafka configuration
type KafkaConfig struct {
	Brokers []string `mapstructure:"brokers"`
	GroupID string   `mapstructure:"group_id"`
	Topics  struct {
		NotificationEvents string `mapstructure:"notification_events"`
		UserEvents         string `mapstructure:"user_events"`
		ContentEvents      string `mapstructure:"content_events"`
		BillingEvents      string `mapstructure:"billing_events"`
		DataEvents         string `mapstructure:"data_events"`
		PlatformEvents     string `mapstructure:"platform_events"`
		AssetEvents        string `mapstructure:"asset_events"`
		DeadLetterQueue    string `mapstructure:"dead_letter_queue"`
	} `mapstructure:"topics"`
}

// TemplatesConfig holds template configuration
type TemplatesConfig struct {
	DefaultLanguage string        `mapstructure:"default_language"`
	SupportedLangs  []string      `mapstructure:"supported_languages"`
	TemplatesPath   string        `mapstructure:"templates_path"`
	CacheEnabled    bool          `mapstructure:"cache_enabled"`
	CacheTTL        time.Duration `mapstructure:"cache_ttl"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled           bool `mapstructure:"enabled"`
	RequestsPerMinute int  `mapstructure:"requests_per_minute"`
	Burst             int  `mapstructure:"burst"`
}

// FeaturesConfig holds feature flags
type FeaturesConfig struct {
	EmailNotifications       bool `mapstructure:"email_notifications"`
	SMSNotifications         bool `mapstructure:"sms_notifications"`
	PushNotifications        bool `mapstructure:"push_notifications"`
	WebSocketNotifications   bool `mapstructure:"websocket_notifications"`
	EventDrivenNotifications bool `mapstructure:"event_driven_notifications"`
	TemplateSystem           bool `mapstructure:"template_system"`
	UserPreferences          bool `mapstructure:"user_preferences"`
	Analytics                bool `mapstructure:"analytics"`
	AdminPanel               bool `mapstructure:"admin_panel"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	MetricsPort     int    `mapstructure:"metrics_port"`
	HealthCheckPath string `mapstructure:"health_check_path"`
	MetricsPath     string `mapstructure:"metrics_path"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Load environment-specific config
	env := os.Getenv("NOTIFICATION_SERVICE_SERVER_ENV")
	if env == "" {
		env = "development"
	}

	// Try to read environment-specific config
	viper.SetConfigName(env)
	if err := viper.MergeInConfig(); err != nil {
		// It's okay if environment-specific config doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read environment config: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Load shared config
	sharedCfg, err := sharedconfig.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}
	config.Config = sharedCfg

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Database defaults
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.sqlite_path", "./data/notification_service.db")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.name", "notification_service_db")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.ssl_mode", "disable")

	// Email defaults
	viper.SetDefault("email.smtp_host", "smtp.gmail.com")
	viper.SetDefault("email.smtp_port", 587)
	viper.SetDefault("email.from_email", "<EMAIL>")
	viper.SetDefault("email.from_name", "Social Content AI")
	viper.SetDefault("email.use_tls", true)
	viper.SetDefault("email.rate_limit", 100)
	viper.SetDefault("email.retry_attempts", 3)

	// SMS defaults
	viper.SetDefault("sms.provider", "twilio")
	viper.SetDefault("sms.rate_limit", 50)
	viper.SetDefault("sms.retry_attempts", 3)

	// Push defaults
	viper.SetDefault("push.provider", "firebase")
	viper.SetDefault("push.rate_limit", 200)
	viper.SetDefault("push.retry_attempts", 3)

	// WebSocket defaults
	viper.SetDefault("websocket.enabled", true)
	viper.SetDefault("websocket.max_connections", 1000)
	viper.SetDefault("websocket.ping_interval", "30s")
	viper.SetDefault("websocket.write_timeout", "10s")
	viper.SetDefault("websocket.read_timeout", "60s")

	// Kafka defaults
	viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
	viper.SetDefault("kafka.group_id", "notification-service")
	viper.SetDefault("kafka.topics.notification_events", "notification.events")
	viper.SetDefault("kafka.topics.user_events", "user.events")
	viper.SetDefault("kafka.topics.content_events", "content.events")
	viper.SetDefault("kafka.topics.billing_events", "billing.events")
	viper.SetDefault("kafka.topics.data_events", "data.events")
	viper.SetDefault("kafka.topics.platform_events", "platform.events")
	viper.SetDefault("kafka.topics.asset_events", "asset.events")
	viper.SetDefault("kafka.topics.dead_letter_queue", "notification.dlq")

	// Templates defaults
	viper.SetDefault("templates.default_language", "en")
	viper.SetDefault("templates.supported_languages", []string{"en", "vi"})
	viper.SetDefault("templates.templates_path", "./templates")
	viper.SetDefault("templates.cache_enabled", true)
	viper.SetDefault("templates.cache_ttl", "1h")

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.requests_per_minute", 60)
	viper.SetDefault("rate_limit.burst", 10)

	// Features defaults
	viper.SetDefault("features.email_notifications", true)
	viper.SetDefault("features.sms_notifications", false)
	viper.SetDefault("features.push_notifications", false)
	viper.SetDefault("features.websocket_notifications", true)
	viper.SetDefault("features.event_driven_notifications", false)
	viper.SetDefault("features.template_system", true)
	viper.SetDefault("features.user_preferences", true)
	viper.SetDefault("features.analytics", true)
	viper.SetDefault("features.admin_panel", true)

	// Monitoring defaults
	viper.SetDefault("monitoring.enabled", true)
	viper.SetDefault("monitoring.metrics_port", 9096)
	viper.SetDefault("monitoring.health_check_path", "/health")
	viper.SetDefault("monitoring.metrics_path", "/metrics")
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to sqlite
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	}
}

// GetDriverName returns the database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "sqlite3"
	}
}

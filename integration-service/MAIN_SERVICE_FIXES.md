# Main Service Fixes - Integration Service

## 📋 Overview

Sau khi refactoring usecase thành interface và service implementation, có một số vấn đề cần fix trong `main.go` để service có thể chạy được.

## 🚨 Các vấn đề hiện tại

### 1. Database Connection Issue
**Lỗi**: `undefined: database.NewPostgresConnection`
**Nguyên nhân**: pkg-shared database có thể không có function này
**Giải pháp**: Sử dụng database connection từ internal/database hoặc tạo connection trực tiếp

### 2. gRPC Handler Type Mismatch
**Lỗi**: Type mismatch giữa usecase interfaces và handler interfaces
**Nguyên nhân**: 
- `integration.UseCase` vs `handlers.IntegrationUseCase`
- `platform.UseCase` vs `handlers.PlatformUseCase`  
- `publishing.UseCase` vs `handlers.PublishingUseCase`

**Giải pháp**: Có 2 cách:
1. **Adapter Pattern**: Tạo adapter để convert giữa các types
2. **Update Handlers**: Cập nhật handlers để sử dụng usecase interfaces trực tiếp

### 3. Proto Registration Issue
**Lỗi**: `undefined: integrationv1.RegisterIntegrationServiceServer`
**Nguyên nhân**: Proto service registration function không tồn tại
**Giải pháp**: Kiểm tra proto-shared và sử dụng đúng function name

### 4. OAuth/Publisher Interface Issues
**Lỗi**: OAuth providers và publishers không implement đúng interfaces
**Nguyên nhân**: Return types không match với interface definitions
**Giải pháp**: Fix OAuth providers và publishers để implement đúng interfaces

## 🔧 Giải pháp được đề xuất

### Giải pháp 1: Adapter Pattern (Recommended)

Tạo adapters để bridge giữa usecase interfaces và handler interfaces:

```go
// internal/adapters/integration_adapter.go
type IntegrationAdapter struct {
    usecase integration.UseCase
}

func (a *IntegrationAdapter) ConnectAccount(ctx context.Context, req *handlers.ConnectAccountRequest) (*handlers.ConnectAccountResponse, error) {
    // Convert handlers.ConnectAccountRequest to integration.ConnectAccountRequest
    usecaseReq := &integration.ConnectAccountRequest{
        UserID:      req.UserID,
        Platform:    req.Platform,
        // ... other fields
    }
    
    // Call usecase
    usecaseResp, err := a.usecase.ConnectAccount(ctx, usecaseReq)
    if err != nil {
        return nil, err
    }
    
    // Convert integration.ConnectAccountResponse to handlers.ConnectAccountResponse
    return &handlers.ConnectAccountResponse{
        AccountID: usecaseResp.AccountID,
        Success:   usecaseResp.Success,
        // ... other fields
    }, nil
}
```

### Giải pháp 2: Update Handlers (Alternative)

Cập nhật handlers để sử dụng usecase interfaces trực tiếp:

```go
// api/grpc/handlers/integration_handler.go
type IntegrationHandler struct {
    integrationUC integration.UseCase
    platformUC    platform.UseCase
    publishingUC  publishing.UseCase
    logger        logging.Logger
}

func (h *IntegrationHandler) ConnectAccount(ctx context.Context, req *integrationv1.ConnectAccountRequest) (*integrationv1.ConnectAccountResponse, error) {
    // Convert proto request to usecase request
    usecaseReq := &integration.ConnectAccountRequest{
        UserID:   req.UserId,
        Platform: req.Platform,
        // ... other fields
    }
    
    // Call usecase
    usecaseResp, err := h.integrationUC.ConnectAccount(ctx, usecaseReq)
    if err != nil {
        return nil, err
    }
    
    // Convert usecase response to proto response
    return &integrationv1.ConnectAccountResponse{
        AccountId: usecaseResp.AccountID,
        Success:   usecaseResp.Success,
        // ... other fields
    }, nil
}
```

## 🚀 Implementation Plan

### Phase 1: Fix Database Connection
1. Tạo database connection function trong internal/database
2. Hoặc sử dụng sql.Open trực tiếp với postgres driver

### Phase 2: Fix gRPC Handlers
1. **Option A**: Implement Adapter Pattern
   - Tạo adapters cho integration, platform, publishing
   - Update main.go để sử dụng adapters
   
2. **Option B**: Update Handlers
   - Cập nhật handlers để sử dụng usecase interfaces
   - Remove duplicate type definitions

### Phase 3: Fix Proto Registration
1. Kiểm tra proto-shared integration service definition
2. Sử dụng đúng registration function name
3. Ensure proto files được generate đúng

### Phase 4: Fix OAuth/Publisher Interfaces
1. Update OAuth providers để return oauth.TokenResponse
2. Update Publishers để implement publishers.Publisher interface
3. Uncomment registration code trong main.go

## 📝 Temporary Workarounds

Để service có thể chạy được ngay lập tức:

1. **Comment out problematic code**:
   ```go
   // Comment out gRPC handler registration
   // integrationv1.RegisterIntegrationServiceServer(grpcServer, ...)
   
   // Comment out OAuth/Publisher registrations (already done)
   ```

2. **Use simple database connection**:
   ```go
   import "database/sql"
   import _ "github.com/lib/pq"
   
   db, err := sql.Open("postgres", cfg.Database.URL)
   ```

3. **Create minimal handlers**:
   ```go
   // Create handlers that just return "not implemented" errors
   ```

## 🎯 Recommended Approach

**Sử dụng Adapter Pattern** vì:
1. Giữ được separation of concerns
2. Usecase interfaces không bị ảnh hưởng
3. Handlers có thể focus vào proto conversion
4. Dễ test và maintain
5. Có thể reuse adapters cho REST API

## 📊 Effort Estimation

- **Phase 1**: 1-2 hours
- **Phase 2**: 4-6 hours (Adapter Pattern)
- **Phase 3**: 1-2 hours  
- **Phase 4**: 3-4 hours

**Total**: 9-14 hours để fix tất cả issues

## 🔍 Next Steps

1. Chọn approach (Adapter Pattern recommended)
2. Implement Phase 1 (Database fix)
3. Implement Phase 2 (Handler fix)
4. Test service startup
5. Implement Phase 3 & 4 (Proto & OAuth/Publisher fixes)

Việc refactoring usecase đã tạo ra một kiến trúc tốt, chỉ cần fix integration issues để service hoạt động! 🚀

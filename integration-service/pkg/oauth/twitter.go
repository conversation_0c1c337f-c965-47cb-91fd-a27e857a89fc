package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// TwitterOAuth handles Twitter OAuth flow
type TwitterOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewTwitterOAuth creates a new Twitter OAuth handler
func NewTwitterOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) Provider {
	return &TwitterOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// TwitterTokenResponse represents Twitter token response
type TwitterTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// TwitterUserProfile represents Twitter user profile
type TwitterUserProfile struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Picture  string `json:"profile_image_url"`
}

// GetAuthURL generates Twitter OAuth authorization URL
func (t *TwitterOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://twitter.com/i/oauth2/authorize"

	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", t.clientID)
	params.Set("redirect_uri", t.redirectURL)
	params.Set("state", state)
	params.Set("code_challenge", "challenge") // TODO: Implement PKCE
	params.Set("code_challenge_method", "plain")

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, " "))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "tweet.read tweet.write users.read offline.access")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (t *TwitterOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://api.twitter.com/2/oauth2/token"

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("client_id", t.clientID)
	data.Set("client_secret", t.clientSecret)
	data.Set("redirect_uri", t.redirectURL)
	data.Set("code", code)
	data.Set("code_verifier", "challenge") // TODO: Implement PKCE

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp TwitterTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	t.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.ExpiresIn,
		"scope":      tokenResp.Scope,
	}).Info("Twitter token exchanged successfully")

	// Convert TwitterTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RefreshToken refreshes Twitter access token
func (t *TwitterOAuth) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	tokenURL := "https://api.twitter.com/2/oauth2/token"

	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("client_id", t.clientID)
	data.Set("client_secret", t.clientSecret)
	data.Set("refresh_token", refreshToken)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp TwitterTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert TwitterTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RevokeToken revokes Twitter access token
func (t *TwitterOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	revokeURL := "https://api.twitter.com/2/oauth2/revoke"

	data := url.Values{}
	data.Set("token", accessToken)
	data.Set("client_id", t.clientID)
	data.Set("client_secret", t.clientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", revokeURL, strings.NewReader(data.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("token revocation failed with status: %d", resp.StatusCode)
	}

	t.logger.Info("Twitter token revoked successfully")
	return nil
}

// GetUserProfile implements Provider interface
func (t *TwitterOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	userURL := "https://api.twitter.com/2/users/me"

	params := url.Values{}
	params.Set("user.fields", "id,username,name,email,profile_image_url")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", userURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var userResp struct {
		Data TwitterUserProfile `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userResp); err != nil {
		return nil, fmt.Errorf("failed to decode user profile: %w", err)
	}

	return &UserProfile{
		ID:          userResp.Data.ID,
		Username:    userResp.Data.Username,
		DisplayName: userResp.Data.Name,
		Email:       userResp.Data.Email,
		AvatarURL:   userResp.Data.Picture,
	}, nil
}

// GetPermissions implements Provider interface
func (t *TwitterOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	// Twitter doesn't have a specific permissions endpoint
	// Return default permissions based on successful authentication
	return []string{"tweet.read", "tweet.write", "users.read"}, nil
}

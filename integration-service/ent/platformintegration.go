// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
)

// PlatformIntegration is the model entity for the PlatformIntegration schema.
type PlatformIntegration struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who owns this integration
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Workspace this integration belongs to
	WorkspaceID string `json:"workspace_id,omitempty"`
	// Social media platform
	Platform platformintegration.Platform `json:"platform,omitempty"`
	// User ID on the platform
	PlatformUserID string `json:"platform_user_id,omitempty"`
	// Username on the platform
	PlatformUsername string `json:"platform_username,omitempty"`
	// Display name on the platform
	PlatformDisplayName string `json:"platform_display_name,omitempty"`
	// Avatar URL on the platform
	PlatformAvatarURL string `json:"platform_avatar_url,omitempty"`
	// OAuth access token
	AccessToken string `json:"-"`
	// OAuth refresh token
	RefreshToken string `json:"-"`
	// When the access token expires
	TokenExpiresAt time.Time `json:"token_expires_at,omitempty"`
	// OAuth scopes granted
	Scopes []string `json:"scopes,omitempty"`
	// Additional platform-specific data
	PlatformData map[string]interface{} `json:"platform_data,omitempty"`
	// Integration status
	Status platformintegration.Status `json:"status,omitempty"`
	// Last error message if status is error
	ErrorMessage string `json:"error_message,omitempty"`
	// Last time data was synced from platform
	LastSyncAt time.Time `json:"last_sync_at,omitempty"`
	// Last time a post was published to this platform
	LastPostAt time.Time `json:"last_post_at,omitempty"`
	// Platform capabilities (post_text, post_image, post_video, etc.)
	Capabilities []string `json:"capabilities,omitempty"`
	// User-defined integration settings
	Settings map[string]interface{} `json:"settings,omitempty"`
	// Whether to auto-publish posts to this platform
	AutoPublish bool `json:"auto_publish,omitempty"`
	// Whether this is the primary account for this platform
	IsPrimary bool `json:"is_primary,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PlatformIntegration) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case platformintegration.FieldScopes, platformintegration.FieldPlatformData, platformintegration.FieldCapabilities, platformintegration.FieldSettings, platformintegration.FieldMetadata:
			values[i] = new([]byte)
		case platformintegration.FieldAutoPublish, platformintegration.FieldIsPrimary:
			values[i] = new(sql.NullBool)
		case platformintegration.FieldWorkspaceID, platformintegration.FieldPlatform, platformintegration.FieldPlatformUserID, platformintegration.FieldPlatformUsername, platformintegration.FieldPlatformDisplayName, platformintegration.FieldPlatformAvatarURL, platformintegration.FieldAccessToken, platformintegration.FieldRefreshToken, platformintegration.FieldStatus, platformintegration.FieldErrorMessage:
			values[i] = new(sql.NullString)
		case platformintegration.FieldTokenExpiresAt, platformintegration.FieldLastSyncAt, platformintegration.FieldLastPostAt, platformintegration.FieldCreatedAt, platformintegration.FieldUpdatedAt, platformintegration.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case platformintegration.FieldID, platformintegration.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PlatformIntegration fields.
func (pi *PlatformIntegration) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case platformintegration.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				pi.ID = *value
			}
		case platformintegration.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				pi.UserID = *value
			}
		case platformintegration.FieldWorkspaceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field workspace_id", values[i])
			} else if value.Valid {
				pi.WorkspaceID = value.String
			}
		case platformintegration.FieldPlatform:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform", values[i])
			} else if value.Valid {
				pi.Platform = platformintegration.Platform(value.String)
			}
		case platformintegration.FieldPlatformUserID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_user_id", values[i])
			} else if value.Valid {
				pi.PlatformUserID = value.String
			}
		case platformintegration.FieldPlatformUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_username", values[i])
			} else if value.Valid {
				pi.PlatformUsername = value.String
			}
		case platformintegration.FieldPlatformDisplayName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_display_name", values[i])
			} else if value.Valid {
				pi.PlatformDisplayName = value.String
			}
		case platformintegration.FieldPlatformAvatarURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform_avatar_url", values[i])
			} else if value.Valid {
				pi.PlatformAvatarURL = value.String
			}
		case platformintegration.FieldAccessToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token", values[i])
			} else if value.Valid {
				pi.AccessToken = value.String
			}
		case platformintegration.FieldRefreshToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field refresh_token", values[i])
			} else if value.Valid {
				pi.RefreshToken = value.String
			}
		case platformintegration.FieldTokenExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field token_expires_at", values[i])
			} else if value.Valid {
				pi.TokenExpiresAt = value.Time
			}
		case platformintegration.FieldScopes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field scopes", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pi.Scopes); err != nil {
					return fmt.Errorf("unmarshal field scopes: %w", err)
				}
			}
		case platformintegration.FieldPlatformData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field platform_data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pi.PlatformData); err != nil {
					return fmt.Errorf("unmarshal field platform_data: %w", err)
				}
			}
		case platformintegration.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pi.Status = platformintegration.Status(value.String)
			}
		case platformintegration.FieldErrorMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_message", values[i])
			} else if value.Valid {
				pi.ErrorMessage = value.String
			}
		case platformintegration.FieldLastSyncAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_sync_at", values[i])
			} else if value.Valid {
				pi.LastSyncAt = value.Time
			}
		case platformintegration.FieldLastPostAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_post_at", values[i])
			} else if value.Valid {
				pi.LastPostAt = value.Time
			}
		case platformintegration.FieldCapabilities:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field capabilities", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pi.Capabilities); err != nil {
					return fmt.Errorf("unmarshal field capabilities: %w", err)
				}
			}
		case platformintegration.FieldSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pi.Settings); err != nil {
					return fmt.Errorf("unmarshal field settings: %w", err)
				}
			}
		case platformintegration.FieldAutoPublish:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field auto_publish", values[i])
			} else if value.Valid {
				pi.AutoPublish = value.Bool
			}
		case platformintegration.FieldIsPrimary:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_primary", values[i])
			} else if value.Valid {
				pi.IsPrimary = value.Bool
			}
		case platformintegration.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pi.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case platformintegration.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pi.CreatedAt = value.Time
			}
		case platformintegration.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pi.UpdatedAt = value.Time
			}
		case platformintegration.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				pi.DeletedAt = value.Time
			}
		default:
			pi.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PlatformIntegration.
// This includes values selected through modifiers, order, etc.
func (pi *PlatformIntegration) Value(name string) (ent.Value, error) {
	return pi.selectValues.Get(name)
}

// Update returns a builder for updating this PlatformIntegration.
// Note that you need to call PlatformIntegration.Unwrap() before calling this method if this PlatformIntegration
// was returned from a transaction, and the transaction was committed or rolled back.
func (pi *PlatformIntegration) Update() *PlatformIntegrationUpdateOne {
	return NewPlatformIntegrationClient(pi.config).UpdateOne(pi)
}

// Unwrap unwraps the PlatformIntegration entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pi *PlatformIntegration) Unwrap() *PlatformIntegration {
	_tx, ok := pi.config.driver.(*txDriver)
	if !ok {
		panic("ent: PlatformIntegration is not a transactional entity")
	}
	pi.config.driver = _tx.drv
	return pi
}

// String implements the fmt.Stringer.
func (pi *PlatformIntegration) String() string {
	var builder strings.Builder
	builder.WriteString("PlatformIntegration(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pi.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pi.UserID))
	builder.WriteString(", ")
	builder.WriteString("workspace_id=")
	builder.WriteString(pi.WorkspaceID)
	builder.WriteString(", ")
	builder.WriteString("platform=")
	builder.WriteString(fmt.Sprintf("%v", pi.Platform))
	builder.WriteString(", ")
	builder.WriteString("platform_user_id=")
	builder.WriteString(pi.PlatformUserID)
	builder.WriteString(", ")
	builder.WriteString("platform_username=")
	builder.WriteString(pi.PlatformUsername)
	builder.WriteString(", ")
	builder.WriteString("platform_display_name=")
	builder.WriteString(pi.PlatformDisplayName)
	builder.WriteString(", ")
	builder.WriteString("platform_avatar_url=")
	builder.WriteString(pi.PlatformAvatarURL)
	builder.WriteString(", ")
	builder.WriteString("access_token=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("refresh_token=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("token_expires_at=")
	builder.WriteString(pi.TokenExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("scopes=")
	builder.WriteString(fmt.Sprintf("%v", pi.Scopes))
	builder.WriteString(", ")
	builder.WriteString("platform_data=")
	builder.WriteString(fmt.Sprintf("%v", pi.PlatformData))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pi.Status))
	builder.WriteString(", ")
	builder.WriteString("error_message=")
	builder.WriteString(pi.ErrorMessage)
	builder.WriteString(", ")
	builder.WriteString("last_sync_at=")
	builder.WriteString(pi.LastSyncAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("last_post_at=")
	builder.WriteString(pi.LastPostAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("capabilities=")
	builder.WriteString(fmt.Sprintf("%v", pi.Capabilities))
	builder.WriteString(", ")
	builder.WriteString("settings=")
	builder.WriteString(fmt.Sprintf("%v", pi.Settings))
	builder.WriteString(", ")
	builder.WriteString("auto_publish=")
	builder.WriteString(fmt.Sprintf("%v", pi.AutoPublish))
	builder.WriteString(", ")
	builder.WriteString("is_primary=")
	builder.WriteString(fmt.Sprintf("%v", pi.IsPrimary))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", pi.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pi.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pi.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(pi.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// PlatformIntegrations is a parsable slice of PlatformIntegration.
type PlatformIntegrations []*PlatformIntegration

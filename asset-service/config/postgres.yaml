# PostgreSQL Configuration for Asset Service

# Server configuration
server:
  env: "development"
  grpc_port: 50054
  http_port: 8084
  host: "0.0.0.0"

# PostgreSQL database configuration
database:
  type: "postgres"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "asset_service_dev"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Storage configuration
storage:
  provider: "minio"
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  bucket: "assets-dev"
  use_ssl: false
  public_url: "http://localhost:9000"
  region: "us-east-1"

# Cleanup configuration
cleanup:
  enabled: true
  pending_upload_ttl: "30m"
  orphaned_file_ttl: "2h"
  deleted_asset_ttl: "24h"
  cleanup_interval: "10m"
  batch_size: 50
  enable_storage_cleanup: true
  dry_run: false

# Security configuration
security:
  max_file_size: 52428800  # 50MB
  allowed_mime_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"
    - "application/json"
    - "text/markdown"
    - "text/csv"
  enable_virus_scanning: false
  quarantine_ttl: "24h"

# Feature flags
features:
  enable_image_processing: true
  enable_rag: true
  enable_folders: true
  enable_versioning: true

# Logging configuration
logging:
  level: "debug"
  format: "text"
  output: "stdout"

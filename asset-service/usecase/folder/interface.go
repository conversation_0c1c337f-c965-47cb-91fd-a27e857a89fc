package folder

import (
	"context"

	"github.com/social-content-ai/asset-service/pkg/models"
)

// UseCase defines the folder management business logic interface
type UseCase interface {
	// Basic CRUD operations
	CreateFolder(ctx context.Context, req *models.CreateFolderRequest) (*models.FolderResponse, error)
	GetFolder(ctx context.Context, folderID, userID string) (*models.FolderResponse, error)
	UpdateFolder(ctx context.Context, req *models.UpdateFolderRequest) (*models.FolderResponse, error)
	DeleteFolder(ctx context.Context, folderID, userID string, force bool) error
	ListFolders(ctx context.Context, req *models.ListFoldersRequest) (*models.ListFoldersResponse, error)

	// Folder operations
	MoveFolder(ctx context.Context, req *models.MoveFolderRequest) (*models.FolderResponse, error)
	CopyFolder(ctx context.Context, req *models.CopyFolderRequest) (*models.FolderResponse, error)
	GetFolderAssets(ctx context.Context, folderID, userID string, page, limit int) (*models.ListAssetsResponse, error)
	ShareFolder(ctx context.Context, req *models.ShareFolderRequest) (*models.FolderResponse, error)

	// Folder tree and navigation
	GetFolderTree(ctx context.Context, userID string, rootFolderID string) (*models.FolderTree, error)
	GetFolderBreadcrumb(ctx context.Context, folderID, userID string) ([]models.FolderBreadcrumb, error)
	GetFolderPath(ctx context.Context, folderID, userID string) (string, error)

	// Folder permissions
	GetFolderPermissions(ctx context.Context, folderID, userID string) ([]models.FolderPermission, error)
	UpdateFolderPermissions(ctx context.Context, folderID, userID string, permissions []models.FolderPermission) error
	CheckFolderAccess(ctx context.Context, folderID, userID string, permission string) (bool, error)
}

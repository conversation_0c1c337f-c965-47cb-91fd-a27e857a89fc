package events

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// SubscriptionEventHandler handles subscription-related events from Kafka
type SubscriptionEventHandler struct {
	logger         logging.Logger
	subscriptionUC subscription.UseCase
	creditUC       credit.UseCase
	notificationUC NotificationService
}

// NotificationService defines interface for sending notifications
type NotificationService interface {
	SendSubscriptionActivatedNotification(ctx context.Context, userID, subscriptionID, planName string, creditsAdded int) error
	SendPaymentFailedNotification(ctx context.Context, userID, subscriptionID, reason string, amount int64, currency string) error
	SendSubscriptionCancelledNotification(ctx context.Context, userID, subscriptionID, planName, reason string) error
}

// NewSubscriptionEventHandler creates a new subscription event handler
func NewSubscriptionEventHandler(logger logging.Logger, subscriptionUC subscription.UseCase, creditUC credit.UseCase, notificationUC NotificationService) *SubscriptionEventHandler {
	return &SubscriptionEventHandler{
		logger:         logger,
		subscriptionUC: subscriptionUC,
		creditUC:       creditUC,
		notificationUC: notificationUC,
	}
}

// HandleSubscriptionEvents handles general subscription events from subscription.events topic
func (h *SubscriptionEventHandler) HandleSubscriptionEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.events message")

	var event GeneralSubscriptionEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription event")
		return fmt.Errorf("failed to unmarshal subscription event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"event_type":      event.EventType,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"status":          event.Status,
	}).Info("Processing general subscription event")

	// Handle different event types
	switch event.EventType {
	case EventTypeSubscriptionPaymentCompleted:
		return h.handlePaymentCompletedFromGeneral(ctx, &event)
	case EventTypeSubscriptionPaymentFailed:
		return h.handlePaymentFailedFromGeneral(ctx, &event)
	case EventTypeSubscriptionActivatedKafka:
		return h.handleActivationFromGeneral(ctx, &event)
	case EventTypeSubscriptionCancelledKafka:
		return h.handleCancellationFromGeneral(ctx, &event)
	default:
		h.logger.WithField("event_type", event.EventType).Info("Unhandled subscription event type")
	}

	return nil
}

// HandlePaymentCompletedEvents handles payment completed events from subscription.payment.completed topic
func (h *SubscriptionEventHandler) HandlePaymentCompletedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.payment.completed message")

	var event SubscriptionPaymentCompletedEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal payment completed event")
		return fmt.Errorf("failed to unmarshal payment completed event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"payment_id":      event.PaymentID,
		"amount":          event.Amount,
		"currency":        event.Currency,
	}).Info("Processing payment completed event")

	// 1. Handle payment success in subscription service
	if h.subscriptionUC != nil {
		err := h.subscriptionUC.HandlePaymentSuccess(ctx, event.PaymentID, event.UserID)
		if err != nil {
			h.logger.WithError(err).Error("Failed to handle payment success in subscription service")
			return fmt.Errorf("failed to handle payment success: %w", err)
		}
	}

	// 2. Add credits to user account if specified
	if event.CreditsAdded > 0 && h.creditUC != nil {
		addCreditsReq := &models.AddCreditsRequest{
			UserID:        event.UserID,
			Amount:        event.CreditsAdded,
			Source:        "subscription_payment",
			Description:   fmt.Sprintf("Credits added from subscription payment %s", event.PaymentID),
			ReferenceID:   event.PaymentID,
			ReferenceType: "payment",
		}

		_, err := h.creditUC.AddCredits(ctx, addCreditsReq)
		if err != nil {
			h.logger.WithError(err).Error("Failed to add credits to user account")
			// Don't fail the entire process if credit addition fails
		} else {
			h.logger.WithFields(map[string]interface{}{
				"user_id":       event.UserID,
				"credits_added": event.CreditsAdded,
				"payment_id":    event.PaymentID,
			}).Info("Successfully added credits to user account")
		}
	}

	// 3. Send notification to user
	if h.notificationUC != nil {
		err := h.notificationUC.SendSubscriptionActivatedNotification(
			ctx,
			event.UserID,
			event.SubscriptionID,
			event.PlanName,
			event.CreditsAdded,
		)
		if err != nil {
			h.logger.WithError(err).Error("Failed to send subscription activated notification")
			// Don't fail the entire process if notification fails
		}
	}

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription activated due to successful payment")
	return nil
}

// HandlePaymentFailedEvents handles payment failed events from subscription.payment.failed topic
func (h *SubscriptionEventHandler) HandlePaymentFailedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.payment.failed message")

	var event SubscriptionPaymentFailedEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal payment failed event")
		return fmt.Errorf("failed to unmarshal payment failed event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"payment_id":      event.PaymentID,
		"failure_reason":  event.FailureReason,
		"amount":          event.Amount,
	}).Info("Processing payment failed event")

	// 1. Handle payment failure in subscription service
	if h.subscriptionUC != nil {
		err := h.subscriptionUC.HandlePaymentFailure(ctx, event.PaymentID, event.FailureReason)
		if err != nil {
			h.logger.WithError(err).Error("Failed to handle payment failure in subscription service")
			return fmt.Errorf("failed to handle payment failure: %w", err)
		}
	}

	// 2. Send notification to user about payment failure
	if h.notificationUC != nil {
		err := h.notificationUC.SendPaymentFailedNotification(
			ctx,
			event.UserID,
			event.SubscriptionID,
			event.FailureReason,
			event.Amount,
			event.Currency,
		)
		if err != nil {
			h.logger.WithError(err).Error("Failed to send payment failed notification")
			// Don't fail the entire process if notification fails
		}
	}

	// 3. Log payment failure for monitoring and potential retry logic
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": event.SubscriptionID,
		"payment_id":      event.PaymentID,
		"failure_reason":  event.FailureReason,
		"failure_code":    event.FailureCode,
		"amount":          event.Amount,
		"currency":        event.Currency,
	}).Warn("Payment failed - subscription may be suspended")

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription suspended due to payment failure")
	return nil
}

// HandleSubscriptionActivatedEvents handles subscription activated events from subscription.activated topic
func (h *SubscriptionEventHandler) HandleSubscriptionActivatedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.activated message")

	var event SubscriptionActivatedEventKafka
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription activated event")
		return fmt.Errorf("failed to unmarshal subscription activated event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"plan_name":       event.PlanName,
		"credits_added":   event.CreditsAdded,
	}).Info("Processing subscription activated event")

	// 1. Handle subscription activation (if not already handled by payment success)
	if h.subscriptionUC != nil && event.PaymentID != "" {
		err := h.subscriptionUC.HandlePaymentSuccess(ctx, event.PaymentID, event.UserID)
		if err != nil {
			h.logger.WithError(err).Error("Failed to handle subscription activation")
			return fmt.Errorf("failed to handle subscription activation: %w", err)
		}
	}

	// 2. Add plan credits to user account
	if event.CreditsAdded > 0 && h.creditUC != nil {
		addCreditsReq := &models.AddCreditsRequest{
			UserID:        event.UserID,
			Amount:        event.CreditsAdded,
			Source:        "subscription_activation",
			Description:   fmt.Sprintf("Credits added from %s subscription activation", event.PlanName),
			ReferenceID:   event.SubscriptionID,
			ReferenceType: "subscription",
		}

		_, err := h.creditUC.AddCredits(ctx, addCreditsReq)
		if err != nil {
			h.logger.WithError(err).Error("Failed to add plan credits to user account")
			// Don't fail the entire process if credit addition fails
		} else {
			h.logger.WithFields(map[string]interface{}{
				"user_id":       event.UserID,
				"credits_added": event.CreditsAdded,
				"plan_name":     event.PlanName,
			}).Info("Successfully added plan credits to user account")
		}
	}

	// 3. Send welcome notification
	if h.notificationUC != nil {
		err := h.notificationUC.SendSubscriptionActivatedNotification(
			ctx,
			event.UserID,
			event.SubscriptionID,
			event.PlanName,
			event.CreditsAdded,
		)
		if err != nil {
			h.logger.WithError(err).Error("Failed to send subscription activated notification")
			// Don't fail the entire process if notification fails
		}
	}

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription activated successfully")
	return nil
}

// HandleSubscriptionCancelledEvents handles subscription cancelled events from subscription.cancelled topic
func (h *SubscriptionEventHandler) HandleSubscriptionCancelledEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.cancelled message")

	var event SubscriptionCancelledEventKafka
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription cancelled event")
		return fmt.Errorf("failed to unmarshal subscription cancelled event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"reason":          event.Reason,
		"cancelled_at":    event.CancelledAt,
	}).Info("Processing subscription cancelled event")

	// 1. Handle subscription cancellation in subscription service
	if h.subscriptionUC != nil {
		// Use HandlePaymentFailure to suspend/cancel the subscription
		err := h.subscriptionUC.HandlePaymentFailure(ctx, event.SubscriptionID, event.Reason)
		if err != nil {
			h.logger.WithError(err).Error("Failed to handle subscription cancellation")
			return fmt.Errorf("failed to handle subscription cancellation: %w", err)
		}
	}

	// 2. Send cancellation confirmation notification
	if h.notificationUC != nil {
		err := h.notificationUC.SendSubscriptionCancelledNotification(
			ctx,
			event.UserID,
			event.SubscriptionID,
			event.PlanName,
			event.Reason,
		)
		if err != nil {
			h.logger.WithError(err).Error("Failed to send subscription cancelled notification")
			// Don't fail the entire process if notification fails
		}
	}

	// 3. Log cancellation details for audit and analytics
	h.logger.WithFields(map[string]interface{}{
		"subscription_id":      event.SubscriptionID,
		"plan_id":              event.PlanID,
		"plan_name":            event.PlanName,
		"cancellation_reason":  event.Reason,
		"cancelled_at":         event.CancelledAt,
		"cancel_at_period_end": event.CancelAtPeriodEnd,
	}).Info("Subscription cancellation processed")

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription cancelled successfully")
	return nil
}

// HandleMessage implements MessageHandler interface for routing messages to appropriate handlers
func (h *SubscriptionEventHandler) HandleMessage(ctx context.Context, topic string, message []byte) error {
	h.logger.WithFields(map[string]interface{}{
		"topic":        topic,
		"message_size": len(message),
	}).Info("Routing message to appropriate handler")

	// Route message to appropriate handler based on topic
	switch topic {
	case TopicSubscriptionEvents:
		return h.HandleSubscriptionEvents(ctx, message)
	case TopicSubscriptionPaymentCompleted:
		return h.HandlePaymentCompletedEvents(ctx, message)
	case TopicSubscriptionPaymentFailed:
		return h.HandlePaymentFailedEvents(ctx, message)
	case TopicSubscriptionActivated:
		return h.HandleSubscriptionActivatedEvents(ctx, message)
	case TopicSubscriptionCancelled:
		return h.HandleSubscriptionCancelledEvents(ctx, message)
	default:
		h.logger.WithField("topic", topic).Warn("Unknown subscription topic")
		return fmt.Errorf("unknown subscription topic: %s", topic)
	}
}

// Helper methods for handling events from general topic

func (h *SubscriptionEventHandler) handlePaymentCompletedFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling payment completed from general topic")

	// Convert to specific event type and process
	completedEvent := &SubscriptionPaymentCompletedEvent{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		PaymentID:      event.PaymentID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Amount:         event.Amount,
		Currency:       event.Currency,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as payment completed event
	message, _ := json.Marshal(completedEvent)
	return h.HandlePaymentCompletedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handlePaymentFailedFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling payment failed from general topic")

	// Convert to specific event type and process
	failedEvent := &SubscriptionPaymentFailedEvent{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		PaymentID:      event.PaymentID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Amount:         event.Amount,
		Currency:       event.Currency,
		FailureReason:  event.FailureReason,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as payment failed event
	message, _ := json.Marshal(failedEvent)
	return h.HandlePaymentFailedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handleActivationFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling activation from general topic")

	// Convert to specific event type and process
	activatedEvent := &SubscriptionActivatedEventKafka{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Status:         event.Status,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as activation event
	message, _ := json.Marshal(activatedEvent)
	return h.HandleSubscriptionActivatedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handleCancellationFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling cancellation from general topic")

	// Convert to specific event type and process
	cancelledEvent := &SubscriptionCancelledEventKafka{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Status:         event.Status,
		Reason:         event.FailureReason, // Use failure reason as cancellation reason
		CancelledAt:    event.Timestamp,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as cancellation event
	message, _ := json.Marshal(cancelledEvent)
	return h.HandleSubscriptionCancelledEvents(ctx, message)
}

// MockNotificationService is a mock implementation of NotificationService for testing
type MockNotificationService struct {
	logger logging.Logger
}

// NewMockNotificationService creates a new mock notification service
func NewMockNotificationService(logger logging.Logger) *MockNotificationService {
	return &MockNotificationService{
		logger: logger,
	}
}

// SendSubscriptionActivatedNotification implements NotificationService interface
func (m *MockNotificationService) SendSubscriptionActivatedNotification(ctx context.Context, userID, subscriptionID, planName string, creditsAdded int) error {
	m.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"subscription_id": subscriptionID,
		"plan_name":       planName,
		"credits_added":   creditsAdded,
	}).Info("Mock: Sending subscription activated notification")

	// Simulate notification sending
	return nil
}

// SendPaymentFailedNotification implements NotificationService interface
func (m *MockNotificationService) SendPaymentFailedNotification(ctx context.Context, userID, subscriptionID, reason string, amount int64, currency string) error {
	m.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"subscription_id": subscriptionID,
		"reason":          reason,
		"amount":          amount,
		"currency":        currency,
	}).Info("Mock: Sending payment failed notification")

	// Simulate notification sending
	return nil
}

// SendSubscriptionCancelledNotification implements NotificationService interface
func (m *MockNotificationService) SendSubscriptionCancelledNotification(ctx context.Context, userID, subscriptionID, planName, reason string) error {
	m.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"subscription_id": subscriptionID,
		"plan_name":       planName,
		"reason":          reason,
	}).Info("Mock: Sending subscription cancelled notification")

	// Simulate notification sending
	return nil
}

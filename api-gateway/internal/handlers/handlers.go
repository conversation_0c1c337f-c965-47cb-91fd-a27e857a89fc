package handlers

import (
	"github.com/social-content-ai/api-gateway/internal/services"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	services *services.ServiceClients
	logger   logging.Logger
}

// NewHandlers creates a new handlers instance
func NewHandlers(services *services.ServiceClients, logger logging.Logger) *Handlers {
	return &Handlers{
		services: services,
		logger:   logger,
	}
}

// Common response structures
type ErrorResponse struct {
	Error   string      `json:"error"`
	Details interface{} `json:"details,omitempty"`
	Code    string      `json:"code,omitempty"`
}

type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Pagination Pagination  `json:"pagination"`
}

type Pagination struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int   `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// Helper function to calculate pagination
func CalculatePagination(page, limit, total int) Pagination {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}

	totalPages := (total + limit - 1) / limit
	hasNext := page < totalPages
	hasPrev := page > 1

	return Pagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}
}

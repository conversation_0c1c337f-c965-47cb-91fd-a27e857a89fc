package handlers

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/content-mgmt-service/usecase/post"
	"github.com/social-content-ai/content-mgmt-service/usecase/template"
	"github.com/social-content-ai/content-mgmt-service/usecase/workspace"
	"github.com/social-content-ai/pkg-shared/logging"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	contentmgmtv1 "github.com/social-content-ai/proto-shared/content-mgmt/v1"
)

// PostHandler implements the PostService gRPC interface
type PostHandler struct {
	contentmgmtv1.UnimplementedPostServiceServer
	postUseCase post.UseCase
	logger      logging.Logger
}

// Template<PERSON>and<PERSON> implements the TemplateService gRPC interface
type Template<PERSON>andler struct {
	contentmgmtv1.UnimplementedTemplateServiceServer
	templateUseCase template.UseCase
	logger          logging.Logger
}

// WorkspaceHandler implements the WorkspaceService gRPC interface
type WorkspaceHandler struct {
	contentmgmtv1.UnimplementedWorkspaceServiceServer
	workspaceUseCase workspace.UseCase
	logger           logging.Logger
}

// NewPostHandler creates a new PostHandler
func NewPostHandler(postUseCase post.UseCase, logger logging.Logger) *PostHandler {
	return &PostHandler{
		postUseCase: postUseCase,
		logger:      logger,
	}
}

// NewTemplateHandler creates a new TemplateHandler
func NewTemplateHandler(templateUseCase template.UseCase, logger logging.Logger) *TemplateHandler {
	return &TemplateHandler{
		templateUseCase: templateUseCase,
		logger:          logger,
	}
}

// NewWorkspaceHandler creates a new WorkspaceHandler
func NewWorkspaceHandler(workspaceUseCase workspace.UseCase, logger logging.Logger) *WorkspaceHandler {
	return &WorkspaceHandler{
		workspaceUseCase: workspaceUseCase,
		logger:           logger,
	}
}

// CreatePost creates a new post
func (h *PostHandler) CreatePost(ctx context.Context, req *contentmgmtv1.CreatePostRequest) (*contentmgmtv1.Post, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC CreatePost called")

	// Convert proto request to domain model
	createReq := &models.CreatePostRequest{
		UserID:      req.UserId,
		WorkspaceID: req.WorkspaceId,
		Title:       req.Title,
		Content:     req.Content,
		Type:        req.Type,
		Platforms:   req.Platforms,
		Hashtags:    req.Hashtags,
		Tags:        req.Tags,
	}

	// Convert media assets
	if len(req.Images) > 0 {
		createReq.MediaAssets = make([]models.MediaAsset, len(req.Images))
		for i, img := range req.Images {
			createReq.MediaAssets[i] = models.MediaAsset{
				URL:  img,
				Type: "image",
			}
		}
	}

	// Call use case
	postResponse, err := h.postUseCase.CreatePost(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create post")
		return nil, status.Errorf(codes.Internal, "failed to create post: %v", err)
	}

	// Convert response to proto
	return h.convertPostToProto(postResponse), nil
}

// GetPost retrieves a post by ID
func (h *PostHandler) GetPost(ctx context.Context, req *contentmgmtv1.GetPostRequest) (*contentmgmtv1.Post, error) {
	h.logger.WithField("post_id", req.Id).Info("gRPC GetPost called")

	// Note: GetPost proto only has ID, we need to get user_id from context or modify proto
	// For now, we'll pass empty user_id and handle authorization in the use case
	postResponse, err := h.postUseCase.GetPost(ctx, req.Id, "")
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post")
		return nil, status.Errorf(codes.NotFound, "post not found: %v", err)
	}

	// Convert response to proto
	return h.convertPostToProto(postResponse), nil
}

// UpdatePost updates an existing post
func (h *PostHandler) UpdatePost(ctx context.Context, req *contentmgmtv1.UpdatePostRequest) (*contentmgmtv1.Post, error) {
	h.logger.WithField("post_id", req.Id).Info("gRPC UpdatePost called")

	// Convert proto request to domain model
	updateReq := &models.UpdatePostRequest{
		ID:        req.Id,
		UserID:    "", // TODO: Get from context or modify proto
		Platforms: req.Platforms,
		Hashtags:  req.Hashtags,
		Tags:      req.Tags,
	}

	// Handle optional fields
	if req.Title != nil {
		updateReq.Title = *req.Title
	}
	if req.Content != nil {
		updateReq.Content = *req.Content
	}

	// Convert media assets
	if len(req.Images) > 0 {
		updateReq.MediaAssets = make([]models.MediaAsset, len(req.Images))
		for i, img := range req.Images {
			updateReq.MediaAssets[i] = models.MediaAsset{
				URL:  img,
				Type: "image",
			}
		}
	}

	// Call use case
	postResponse, err := h.postUseCase.UpdatePost(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update post")
		return nil, status.Errorf(codes.Internal, "failed to update post: %v", err)
	}

	// Convert response to proto
	return h.convertPostToProto(postResponse), nil
}

// ListPosts lists posts for a user
func (h *PostHandler) ListPosts(ctx context.Context, req *contentmgmtv1.ListPostsRequest) (*contentmgmtv1.ListPostsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC ListPosts called")

	// Convert proto request to domain model
	listReq := &models.ListPostsRequest{
		UserID:      req.UserId,
		WorkspaceID: req.WorkspaceId,
		Status:      req.Status,
		DateFrom:    req.DateFrom,
		DateTo:      req.DateTo,
		Page:        int(req.Pagination.Page),
		Limit:       int(req.Pagination.Limit),
	}

	// Handle platforms filter (proto has repeated platforms)
	if len(req.Platforms) > 0 {
		listReq.Platform = req.Platforms[0] // Use first platform for now
	}

	// Call use case
	listResponse, err := h.postUseCase.ListPosts(ctx, listReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list posts")
		return nil, status.Errorf(codes.Internal, "failed to list posts: %v", err)
	}

	// Convert response to proto
	posts := make([]*contentmgmtv1.Post, len(listResponse.Posts))
	for i, p := range listResponse.Posts {
		posts[i] = h.convertPostToProto(&p)
	}

	return &contentmgmtv1.ListPostsResponse{
		Posts: posts,
		Pagination: &commonv1.PaginationResponse{
			Page:       int32(listResponse.Pagination.Page),
			Limit:      int32(listResponse.Pagination.Limit),
			TotalItems: int32(listResponse.Pagination.Total),
			TotalPages: int32(listResponse.Pagination.TotalPages),
			HasNext:    listResponse.Pagination.Page < listResponse.Pagination.TotalPages,
			HasPrev:    listResponse.Pagination.Page > 1,
		},
	}, nil
}

// DeletePost deletes a post
func (h *PostHandler) DeletePost(ctx context.Context, req *contentmgmtv1.DeletePostRequest) (*contentmgmtv1.Empty, error) {
	h.logger.WithField("post_id", req.Id).Info("gRPC DeletePost called")

	// Call use case (TODO: Get user_id from context)
	err := h.postUseCase.DeletePost(ctx, req.Id, "")
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete post")
		return nil, status.Errorf(codes.Internal, "failed to delete post: %v", err)
	}

	return &contentmgmtv1.Empty{}, nil
}

// PublishPost publishes a post immediately
func (h *PostHandler) PublishPost(ctx context.Context, req *contentmgmtv1.PublishPostRequest) (*contentmgmtv1.Post, error) {
	h.logger.WithField("post_id", req.Id).Info("gRPC PublishPost called")

	// Convert proto request to domain model
	publishReq := &models.PublishPostRequest{
		PostID:    req.Id,
		UserID:    "", // TODO: Get from context
		Platforms: req.Platforms,
		Immediate: true, // PublishPost is always immediate
	}

	// Call use case
	postResponse, err := h.postUseCase.PublishPost(ctx, publishReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish post")
		return nil, status.Errorf(codes.Internal, "failed to publish post: %v", err)
	}

	// Convert response to proto
	return h.convertPostToProto(postResponse), nil
}

// SchedulePost schedules a post for publication
func (h *PostHandler) SchedulePost(ctx context.Context, req *contentmgmtv1.SchedulePostRequest) (*contentmgmtv1.Post, error) {
	h.logger.WithField("post_id", req.Id).Info("gRPC SchedulePost called")

	// Convert proto request to domain model
	scheduleReq := &models.SchedulePostRequest{
		PostID:      req.Id,
		UserID:      "", // TODO: Get from context
		ScheduledAt: req.ScheduledAt.AsTime(),
		Platforms:   req.Platforms,
	}

	// Call use case
	postResponse, err := h.postUseCase.SchedulePost(ctx, scheduleReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to schedule post")
		return nil, status.Errorf(codes.Internal, "failed to schedule post: %v", err)
	}

	// Convert response to proto
	return h.convertPostToProto(postResponse), nil
}

// convertPostToProto converts domain model to proto
func (h *PostHandler) convertPostToProto(post *models.PostResponse) *contentmgmtv1.Post {
	protoPost := &contentmgmtv1.Post{
		Id:          post.ID,
		UserId:      post.UserID,
		WorkspaceId: post.WorkspaceID,
		Title:       post.Title,
		Content:     post.Content,
		Type:        post.Type,
		Status:      post.Status,
		Platforms:   post.Platforms,
		Hashtags:    post.Hashtags,
		Tags:        post.Tags,
		Timestamps: &commonv1.TimestampInfo{
			CreatedAt: timestamppb.New(post.CreatedAt),
			UpdatedAt: timestamppb.New(post.UpdatedAt),
		},
	}

	// Set scheduled time if present
	if post.ScheduledAt != nil && !post.ScheduledAt.IsZero() {
		protoPost.ScheduledAt = timestamppb.New(*post.ScheduledAt)
	}

	// Convert media assets to images
	if len(post.MediaAssets) > 0 {
		protoPost.Images = make([]string, 0, len(post.MediaAssets))
		for _, asset := range post.MediaAssets {
			if asset.Type == "image" {
				protoPost.Images = append(protoPost.Images, asset.URL)
			}
		}
	}

	return protoPost
}

// convertTemplateToProto converts domain model to proto
func (h *TemplateHandler) convertTemplateToProto(template *models.TemplateResponse) *contentmgmtv1.Template {
	return &contentmgmtv1.Template{
		Id:                   template.ID,
		UserId:               template.UserID,
		Name:                 template.Name,
		Description:          template.Description,
		Content:              template.Content,
		Category:             template.Category,
		Platforms:            template.Platforms,
		IsPublic:             template.IsPublic,
		RagFiles:             template.RAGFiles,
		VariablePlaceholders: template.VariablePlaceholders,
		IsPremium:            template.IsPremium,
		CreditCost:           int32(template.CreditCost),
		Rating:               float32(template.Rating),
		Timestamps: &commonv1.TimestampInfo{
			CreatedAt: timestamppb.New(template.CreatedAt),
			UpdatedAt: timestamppb.New(template.UpdatedAt),
		},
	}
}

// Template Service Methods

// CreateTemplate creates a new template
func (h *TemplateHandler) CreateTemplate(ctx context.Context, req *contentmgmtv1.CreateTemplateRequest) (*contentmgmtv1.Template, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC CreateTemplate called")

	// Convert proto request to domain model
	createReq := &models.CreateTemplateRequest{
		UserID:               req.UserId,
		Name:                 req.Name,
		Description:          req.Description,
		Content:              req.Content,
		Category:             req.Category,
		Platforms:            req.Platforms,
		IsPublic:             req.IsPublic,
		RAGFiles:             req.RagFiles,
		VariablePlaceholders: req.VariablePlaceholders,
		IsPremium:            req.IsPremium,
		CreditCost:           int(req.CreditCost),
	}

	// Call use case
	templateResponse, err := h.templateUseCase.CreateTemplate(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create template")
		return nil, status.Errorf(codes.Internal, "failed to create template: %v", err)
	}

	// Convert response to proto
	return h.convertTemplateToProto(templateResponse), nil
}

// GetTemplate retrieves a template by ID
func (h *TemplateHandler) GetTemplate(ctx context.Context, req *contentmgmtv1.GetTemplateRequest) (*contentmgmtv1.Template, error) {
	h.logger.WithField("template_id", req.Id).Info("gRPC GetTemplate called")

	// Call use case (TODO: Get user_id from context)
	templateResponse, err := h.templateUseCase.GetTemplate(ctx, req.Id, "")
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template")
		return nil, status.Errorf(codes.NotFound, "template not found: %v", err)
	}

	// Convert response to proto
	return h.convertTemplateToProto(templateResponse), nil
}

// UpdateTemplate updates an existing template
func (h *TemplateHandler) UpdateTemplate(ctx context.Context, req *contentmgmtv1.UpdateTemplateRequest) (*contentmgmtv1.Template, error) {
	h.logger.WithField("template_id", req.Id).Info("gRPC UpdateTemplate called")

	// Convert proto request to domain model
	updateReq := &models.UpdateTemplateRequest{
		ID:                   req.Id,
		UserID:               "", // TODO: Get from context
		Platforms:            req.Platforms,
		RAGFiles:             req.RagFiles,
		VariablePlaceholders: req.VariablePlaceholders,
	}

	// Handle IsPublic optional field
	if req.IsPublic != nil {
		updateReq.IsPublic = *req.IsPublic
	}

	// Handle optional fields
	if req.Name != nil {
		updateReq.Name = *req.Name
	}
	if req.Description != nil {
		updateReq.Description = *req.Description
	}
	if req.Content != nil {
		updateReq.Content = *req.Content
	}
	if req.Category != nil {
		updateReq.Category = *req.Category
	}

	// Call use case
	templateResponse, err := h.templateUseCase.UpdateTemplate(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update template")
		return nil, status.Errorf(codes.Internal, "failed to update template: %v", err)
	}

	// Convert response to proto
	return h.convertTemplateToProto(templateResponse), nil
}

// ListTemplates lists templates for a user
func (h *TemplateHandler) ListTemplates(ctx context.Context, req *contentmgmtv1.ListTemplatesRequest) (*contentmgmtv1.ListTemplatesResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC ListTemplates called")

	// Convert proto request to domain model
	listReq := &models.ListTemplatesRequest{
		UserID:   req.UserId,
		Type:     req.Type,
		Category: req.Category,
		Search:   req.Search,
		Page:     int(req.Pagination.Page),
		Limit:    int(req.Pagination.Limit),
	}

	// Handle platforms filter (proto has repeated platforms)
	if len(req.Platforms) > 0 {
		listReq.Platform = req.Platforms[0] // Use first platform for now
	}

	// Call use case
	listResponse, err := h.templateUseCase.ListTemplates(ctx, listReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list templates")
		return nil, status.Errorf(codes.Internal, "failed to list templates: %v", err)
	}

	// Convert response to proto
	templates := make([]*contentmgmtv1.Template, len(listResponse.Templates))
	for i, t := range listResponse.Templates {
		templates[i] = h.convertTemplateToProto(&t)
	}

	return &contentmgmtv1.ListTemplatesResponse{
		Templates: templates,
		Pagination: &commonv1.PaginationResponse{
			Page:       int32(listResponse.Pagination.Page),
			Limit:      int32(listResponse.Pagination.Limit),
			TotalItems: int32(listResponse.Pagination.Total),
			TotalPages: int32(listResponse.Pagination.TotalPages),
			HasNext:    listResponse.Pagination.Page < listResponse.Pagination.TotalPages,
			HasPrev:    listResponse.Pagination.Page > 1,
		},
	}, nil
}

// UseTemplate uses a template to generate content
func (h *TemplateHandler) UseTemplate(ctx context.Context, req *contentmgmtv1.UseTemplateRequest) (*contentmgmtv1.UseTemplateResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"template_id": req.Id,
		"user_id":     req.UserId,
	}).Info("gRPC UseTemplate called")

	// Convert variables from map[string]string to map[string]interface{}
	variables := make(map[string]interface{})
	for k, v := range req.Variables {
		variables[k] = v
	}

	// Convert proto request to domain model
	useReq := &models.UseTemplateRequest{
		TemplateID: req.Id,
		UserID:     req.UserId,
		Variables:  variables,
	}

	// Call use case
	useResponse, err := h.templateUseCase.UseTemplate(ctx, useReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to use template")
		return nil, status.Errorf(codes.Internal, "failed to use template: %v", err)
	}

	// Convert response to proto
	return &contentmgmtv1.UseTemplateResponse{
		ProcessedContent: useResponse.GeneratedContent,
		ProcessedPrompt:  "", // TODO: Add processed prompt if needed
		CreditsCharged:   int32(useResponse.CreditCost),
	}, nil
}

// DeleteTemplate deletes a template
func (h *TemplateHandler) DeleteTemplate(ctx context.Context, req *contentmgmtv1.DeleteTemplateRequest) (*contentmgmtv1.Empty, error) {
	h.logger.WithField("template_id", req.Id).Info("gRPC DeleteTemplate called")

	// Call use case (TODO: Get user_id from context)
	err := h.templateUseCase.DeleteTemplate(ctx, req.Id, "")
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete template")
		return nil, status.Errorf(codes.Internal, "failed to delete template: %v", err)
	}

	return &contentmgmtv1.Empty{}, nil
}

// RateTemplate rates a template
func (h *TemplateHandler) RateTemplate(ctx context.Context, req *contentmgmtv1.RateTemplateRequest) (*contentmgmtv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"template_id": req.Id,
		"user_id":     req.UserId,
		"rating":      req.Rating,
	}).Info("gRPC RateTemplate called")

	// Convert proto request to domain model
	rateReq := &models.RateTemplateRequest{
		TemplateID: req.Id,
		UserID:     req.UserId,
		Rating:     int(req.Rating),
		Review:     req.Review,
	}

	// Call use case
	err := h.templateUseCase.RateTemplate(ctx, rateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to rate template")
		return nil, status.Errorf(codes.Internal, "failed to rate template: %v", err)
	}

	return &contentmgmtv1.Empty{}, nil
}

// TODO: GetTemplateRatings - Cần implement sau khi tạo models.GetTemplateRatingsRequest
// func (h *TemplateHandler) GetTemplateRatings(ctx context.Context, req *contentmgmtv1.GetTemplateRatingsRequest) (*contentmgmtv1.GetTemplateRatingsResponse, error) {
//     return nil, status.Errorf(codes.Unimplemented, "GetTemplateRatings not implemented yet")
// }

// TODO: PurchaseTemplate - Cần implement sau khi tạo models.PurchaseTemplateRequest
// func (h *TemplateHandler) PurchaseTemplate(ctx context.Context, req *contentmgmtv1.PurchaseTemplateRequest) (*contentmgmtv1.PurchaseTemplateResponse, error) {
//     return nil, status.Errorf(codes.Unimplemented, "PurchaseTemplate not implemented yet")
// }

// TODO: GetPurchasedTemplates - Cần implement sau khi tạo models.GetPurchasedTemplatesRequest
// func (h *TemplateHandler) GetPurchasedTemplates(ctx context.Context, req *contentmgmtv1.GetPurchasedTemplatesRequest) (*contentmgmtv1.GetPurchasedTemplatesResponse, error) {
//     return nil, status.Errorf(codes.Unimplemented, "GetPurchasedTemplates not implemented yet")
// }

// TODO: GetTemplateRevenue - Cần implement sau khi tạo models.GetTemplateRevenueRequest
// func (h *TemplateHandler) GetTemplateRevenue(ctx context.Context, req *contentmgmtv1.GetTemplateRevenueRequest) (*contentmgmtv1.GetTemplateRevenueResponse, error) {
//     return nil, status.Errorf(codes.Unimplemented, "GetTemplateRevenue not implemented yet")
// }

// TODO: Workspace Service Methods - Cần implement các method sau:
//
// WORKSPACE MANAGEMENT:
// - CreateWorkspace(CreateWorkspaceRequest) returns (Workspace)
// - GetWorkspace(GetWorkspaceRequest) returns (Workspace)
// - UpdateWorkspace(UpdateWorkspaceRequest) returns (Workspace)
// - ListWorkspaces(ListWorkspacesRequest) returns (ListWorkspacesResponse)
// - DeleteWorkspace(DeleteWorkspaceRequest) returns (Empty)
//
// MEMBER MANAGEMENT:
// - InviteMember(InviteMemberRequest) returns (InviteMemberResponse)
// - AcceptInvitation(AcceptInvitationRequest) returns (AcceptInvitationResponse)
// - UpdateMemberRole(UpdateMemberRoleRequest) returns (WorkspaceMember)
// - RemoveMember(RemoveMemberRequest) returns (Empty)
// - ListMembers(ListMembersRequest) returns (ListMembersResponse)
//
// INVITATION MANAGEMENT:
// - ListInvitations(ListInvitationsRequest) returns (ListInvitationsResponse)
//
// ANALYTICS:
// - GetWorkspaceStats(GetWorkspaceStatsRequest) returns (WorkspaceStats)
//
// Cần tạo các domain models tương ứng trong pkg/models và implement trong usecase/workspace

// TODO: Cần implement helper methods cho workspace conversion:
// - convertWorkspaceToProto(workspace *models.WorkspaceResponse) *contentmgmtv1.Workspace
// - convertWorkspaceSettingsToProto(settings models.WorkspaceSettings) *contentmgmtv1.WorkspaceSettings
// - convertWorkspaceSettingsFromProto(settings *contentmgmtv1.WorkspaceSettings) models.WorkspaceSettings

// =============================================================================
// SUMMARY: CÁC gRPC METHODS CẦN IMPLEMENT THEO PROTO-SHARED
// =============================================================================
//
// 📝 TEMPLATE SERVICE - CÒN THIẾU:
// ✅ CreateTemplate, GetTemplate, UpdateTemplate, ListTemplates, UseTemplate, DeleteTemplate (đã có)
// ❌ RateTemplate - cần models.RateTemplateRequest và templateUseCase.RateTemplate()
// ❌ GetTemplateRatings - cần models.GetTemplateRatingsRequest và templateUseCase.GetTemplateRatings()
// ❌ PurchaseTemplate - cần models.PurchaseTemplateRequest và templateUseCase.PurchaseTemplate()
// ❌ GetPurchasedTemplates - cần models.GetPurchasedTemplatesRequest và templateUseCase.GetPurchasedTemplates()
// ❌ GetTemplateRevenue - cần models.GetTemplateRevenueRequest và templateUseCase.GetTemplateRevenue()
//
// 🏢 WORKSPACE SERVICE - CÒN THIẾU TẤT CẢ:
// ❌ CreateWorkspace, GetWorkspace, UpdateWorkspace, ListWorkspaces, DeleteWorkspace
// ❌ InviteMember, AcceptInvitation, UpdateMemberRole, RemoveMember, ListMembers
// ❌ ListInvitations, GetWorkspaceStats
//
// 📊 POST SERVICE - ĐÃ CÓ ĐẦY ĐỦ:
// ✅ CreatePost, GetPost, UpdatePost, ListPosts, DeletePost, PublishPost, SchedulePost
//
// 🔧 CẦN TẠO TRONG PKG/MODELS:
// - RateTemplateRequest, GetTemplateRatingsRequest, TemplateRating
// - PurchaseTemplateRequest, GetPurchasedTemplatesRequest, PurchaseInfo
// - GetTemplateRevenueRequest, TemplateRevenue
// - CreateWorkspaceRequest, UpdateWorkspaceRequest, WorkspaceResponse, WorkspaceSettings
// - InviteMemberRequest, WorkspaceMember, WorkspaceStats
//
// 🎯 CẦN IMPLEMENT TRONG USECASE:
// - template.UseCase: RateTemplate, GetTemplateRatings, PurchaseTemplate, GetPurchasedTemplates, GetTemplateRevenue
// - workspace.UseCase: tất cả methods cho workspace management
//
// =============================================================================

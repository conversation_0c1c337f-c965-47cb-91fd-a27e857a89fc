// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: ai-content/v1/model.proto

package aicontentv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AIModel represents an available AI model
type AIModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                   // Display name (e.g., "GPT-4 Turbo")
	Provider        string   `protobuf:"bytes,3,opt,name=provider,proto3" json:"provider,omitempty"`                                           // openai, anthropic, google
	Type            string   `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                                                   // text, image, multimodal
	CreditCost      int32    `protobuf:"varint,5,opt,name=credit_cost,json=creditCost,proto3" json:"credit_cost,omitempty"`                    // Credits required per request
	IsAvailable     bool     `protobuf:"varint,6,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`                 // Whether model is currently available
	Description     string   `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`                                     // Model description
	Capabilities    []string `protobuf:"bytes,8,rep,name=capabilities,proto3" json:"capabilities,omitempty"`                                   // List of capabilities
	MaxTokens       int32    `protobuf:"varint,9,opt,name=max_tokens,json=maxTokens,proto3" json:"max_tokens,omitempty"`                       // Maximum tokens supported
	ResponseTimeAvg float32  `protobuf:"fixed32,10,opt,name=response_time_avg,json=responseTimeAvg,proto3" json:"response_time_avg,omitempty"` // Average response time in seconds
}

func (x *AIModel) Reset() {
	*x = AIModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AIModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIModel) ProtoMessage() {}

func (x *AIModel) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIModel.ProtoReflect.Descriptor instead.
func (*AIModel) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{0}
}

func (x *AIModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AIModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AIModel) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *AIModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AIModel) GetCreditCost() int32 {
	if x != nil {
		return x.CreditCost
	}
	return 0
}

func (x *AIModel) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *AIModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AIModel) GetCapabilities() []string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

func (x *AIModel) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

func (x *AIModel) GetResponseTimeAvg() float32 {
	if x != nil {
		return x.ResponseTimeAvg
	}
	return 0
}

// GetModelsRequest for listing available models
type GetModelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type          string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                         // Filter by type (text, image, multimodal)
	AvailableOnly bool   `protobuf:"varint,2,opt,name=available_only,json=availableOnly,proto3" json:"available_only,omitempty"` // Only return available models
}

func (x *GetModelsRequest) Reset() {
	*x = GetModelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelsRequest) ProtoMessage() {}

func (x *GetModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelsRequest.ProtoReflect.Descriptor instead.
func (*GetModelsRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{1}
}

func (x *GetModelsRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetModelsRequest) GetAvailableOnly() bool {
	if x != nil {
		return x.AvailableOnly
	}
	return false
}

// GetModelsResponse with list of models
type GetModelsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*AIModel `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *GetModelsResponse) Reset() {
	*x = GetModelsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelsResponse) ProtoMessage() {}

func (x *GetModelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelsResponse.ProtoReflect.Descriptor instead.
func (*GetModelsResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{2}
}

func (x *GetModelsResponse) GetModels() []*AIModel {
	if x != nil {
		return x.Models
	}
	return nil
}

// GetModelRequest for getting specific model info
type GetModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetModelRequest) Reset() {
	*x = GetModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelRequest) ProtoMessage() {}

func (x *GetModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelRequest.ProtoReflect.Descriptor instead.
func (*GetModelRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{3}
}

func (x *GetModelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ModelUsageStats for model usage statistics
type ModelUsageStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId          string  `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelName        string  `protobuf:"bytes,2,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	TotalRequests    int32   `protobuf:"varint,3,opt,name=total_requests,json=totalRequests,proto3" json:"total_requests,omitempty"`
	TotalCreditsUsed int32   `protobuf:"varint,4,opt,name=total_credits_used,json=totalCreditsUsed,proto3" json:"total_credits_used,omitempty"`
	AvgResponseTime  float32 `protobuf:"fixed32,5,opt,name=avg_response_time,json=avgResponseTime,proto3" json:"avg_response_time,omitempty"`
	SuccessRate      float32 `protobuf:"fixed32,6,opt,name=success_rate,json=successRate,proto3" json:"success_rate,omitempty"` // Percentage of successful requests
}

func (x *ModelUsageStats) Reset() {
	*x = ModelUsageStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelUsageStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelUsageStats) ProtoMessage() {}

func (x *ModelUsageStats) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelUsageStats.ProtoReflect.Descriptor instead.
func (*ModelUsageStats) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{4}
}

func (x *ModelUsageStats) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelUsageStats) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelUsageStats) GetTotalRequests() int32 {
	if x != nil {
		return x.TotalRequests
	}
	return 0
}

func (x *ModelUsageStats) GetTotalCreditsUsed() int32 {
	if x != nil {
		return x.TotalCreditsUsed
	}
	return 0
}

func (x *ModelUsageStats) GetAvgResponseTime() float32 {
	if x != nil {
		return x.AvgResponseTime
	}
	return 0
}

func (x *ModelUsageStats) GetSuccessRate() float32 {
	if x != nil {
		return x.SuccessRate
	}
	return 0
}

// GetModelStatsRequest for model statistics
type GetModelStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // Optional: stats for specific user
	DateFrom string `protobuf:"bytes,2,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"` // Format: YYYY-MM-DD
	DateTo   string `protobuf:"bytes,3,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`       // Format: YYYY-MM-DD
}

func (x *GetModelStatsRequest) Reset() {
	*x = GetModelStatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelStatsRequest) ProtoMessage() {}

func (x *GetModelStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelStatsRequest.ProtoReflect.Descriptor instead.
func (*GetModelStatsRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{5}
}

func (x *GetModelStatsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetModelStatsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *GetModelStatsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

// GetModelStatsResponse with model statistics
type GetModelStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stats            []*ModelUsageStats `protobuf:"bytes,1,rep,name=stats,proto3" json:"stats,omitempty"`
	TotalRequests    int32              `protobuf:"varint,2,opt,name=total_requests,json=totalRequests,proto3" json:"total_requests,omitempty"`
	TotalCreditsUsed int32              `protobuf:"varint,3,opt,name=total_credits_used,json=totalCreditsUsed,proto3" json:"total_credits_used,omitempty"`
}

func (x *GetModelStatsResponse) Reset() {
	*x = GetModelStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModelStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelStatsResponse) ProtoMessage() {}

func (x *GetModelStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelStatsResponse.ProtoReflect.Descriptor instead.
func (*GetModelStatsResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_model_proto_rawDescGZIP(), []int{6}
}

func (x *GetModelStatsResponse) GetStats() []*ModelUsageStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetModelStatsResponse) GetTotalRequests() int32 {
	if x != nil {
		return x.TotalRequests
	}
	return 0
}

func (x *GetModelStatsResponse) GetTotalCreditsUsed() int32 {
	if x != nil {
		return x.TotalCreditsUsed
	}
	return 0
}

var File_ai_content_v1_model_proto protoreflect.FileDescriptor

var file_ai_content_v1_model_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x69, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22, 0xb2, 0x02, 0x0a, 0x07, 0x41,
	0x49, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x76, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x67, 0x22,
	0x4d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0x43,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x49, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xef, 0x01, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x76, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x61, 0x76, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x61, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x22,
	0xa2, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x55, 0x73, 0x65, 0x64, 0x32, 0x80, 0x02, 0x0a, 0x0e, 0x41, 0x49, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4e, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x1e, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x49, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x5a, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x61,
	0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x2d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x2d, 0x61, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x2f, 0x61, 0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x69, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ai_content_v1_model_proto_rawDescOnce sync.Once
	file_ai_content_v1_model_proto_rawDescData = file_ai_content_v1_model_proto_rawDesc
)

func file_ai_content_v1_model_proto_rawDescGZIP() []byte {
	file_ai_content_v1_model_proto_rawDescOnce.Do(func() {
		file_ai_content_v1_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_content_v1_model_proto_rawDescData)
	})
	return file_ai_content_v1_model_proto_rawDescData
}

var file_ai_content_v1_model_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_ai_content_v1_model_proto_goTypes = []interface{}{
	(*AIModel)(nil),               // 0: ai_content.v1.AIModel
	(*GetModelsRequest)(nil),      // 1: ai_content.v1.GetModelsRequest
	(*GetModelsResponse)(nil),     // 2: ai_content.v1.GetModelsResponse
	(*GetModelRequest)(nil),       // 3: ai_content.v1.GetModelRequest
	(*ModelUsageStats)(nil),       // 4: ai_content.v1.ModelUsageStats
	(*GetModelStatsRequest)(nil),  // 5: ai_content.v1.GetModelStatsRequest
	(*GetModelStatsResponse)(nil), // 6: ai_content.v1.GetModelStatsResponse
}
var file_ai_content_v1_model_proto_depIdxs = []int32{
	0, // 0: ai_content.v1.GetModelsResponse.models:type_name -> ai_content.v1.AIModel
	4, // 1: ai_content.v1.GetModelStatsResponse.stats:type_name -> ai_content.v1.ModelUsageStats
	1, // 2: ai_content.v1.AIModelService.GetModels:input_type -> ai_content.v1.GetModelsRequest
	3, // 3: ai_content.v1.AIModelService.GetModel:input_type -> ai_content.v1.GetModelRequest
	5, // 4: ai_content.v1.AIModelService.GetModelStats:input_type -> ai_content.v1.GetModelStatsRequest
	2, // 5: ai_content.v1.AIModelService.GetModels:output_type -> ai_content.v1.GetModelsResponse
	0, // 6: ai_content.v1.AIModelService.GetModel:output_type -> ai_content.v1.AIModel
	6, // 7: ai_content.v1.AIModelService.GetModelStats:output_type -> ai_content.v1.GetModelStatsResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_ai_content_v1_model_proto_init() }
func file_ai_content_v1_model_proto_init() {
	if File_ai_content_v1_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ai_content_v1_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AIModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelUsageStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelStatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModelStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_content_v1_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_content_v1_model_proto_goTypes,
		DependencyIndexes: file_ai_content_v1_model_proto_depIdxs,
		MessageInfos:      file_ai_content_v1_model_proto_msgTypes,
	}.Build()
	File_ai_content_v1_model_proto = out.File
	file_ai_content_v1_model_proto_rawDesc = nil
	file_ai_content_v1_model_proto_goTypes = nil
	file_ai_content_v1_model_proto_depIdxs = nil
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// PaymentConfirmation holds the schema definition for the PaymentConfirmation entity.
type PaymentConfirmation struct {
	ent.Schema
}

// Fields of the PaymentConfirmation.
func (PaymentConfirmation) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("bank_transfer_id", uuid.UUID{}).
			Optional(), // Reference to bank transfer
		field.UUID("order_id", uuid.UUID{}).
			Optional(), // Reference to order
		field.UUID("subscription_id", uuid.UUID{}).
			Optional(), // Reference to subscription
		field.String("payment_method").
			MaxLen(50), // 'bank_transfer', 'stripe', 'paypal', etc.
		field.String("external_payment_id").
			MaxLen(100).
			Optional(), // Payment provider's transaction ID
		field.String("bank_transaction_id").
			MaxLen(100).
			Optional(), // Bank's transaction ID
		field.Int64("amount_paid"). // Amount actually paid
			Min(0),
		field.String("currency").
			MaxLen(3).
			Default("VND"),
		field.Enum("status").
			Values("pending", "confirmed", "failed", "disputed").
			Default("pending"),
		field.UUID("confirmed_by", uuid.UUID{}).
			Optional(), // Admin user ID (for manual confirmations)
		field.String("confirmation_method").
			MaxLen(20).
			Default("manual"), // 'manual', 'auto_webhook', 'api'
		field.Text("confirmation_notes").
			Optional(), // Admin notes for confirmation
		field.JSON("payment_details", map[string]interface{}{}).
			Optional(), // Additional payment details
		field.JSON("webhook_data", map[string]interface{}{}).
			Optional(), // Webhook payload data
		field.Time("payment_date").
			Optional(), // When payment was made
		field.Time("confirmed_at").
			Optional(),
		field.String("failure_reason").
			MaxLen(500).
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the PaymentConfirmation.
func (PaymentConfirmation) Edges() []ent.Edge {
	return nil
}

// Indexes of the PaymentConfirmation.
func (PaymentConfirmation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("bank_transfer_id"),
		index.Fields("order_id"),
		index.Fields("subscription_id"),
		index.Fields("payment_method"),
		index.Fields("external_payment_id"),
		index.Fields("bank_transaction_id"),
		index.Fields("status"),
		index.Fields("confirmed_at"),
		index.Fields("payment_date"),
		index.Fields("created_at"),
	}
}

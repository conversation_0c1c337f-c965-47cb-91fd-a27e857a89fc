package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Notification holds the schema definition for the Notification entity.
type Notification struct {
	ent.Schema
}

// Fields of the Notification.
func (Notification) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who should receive this notification"),
		field.Enum("type").
			Values(
				"content_generated",
				"content_improved", 
				"post_published",
				"post_scheduled",
				"post_failed",
				"credit_low",
				"credit_purchased",
				"subscription_renewed",
				"subscription_expired",
				"integration_connected",
				"integration_failed",
				"analytics_report",
				"system_maintenance",
				"welcome",
				"password_reset",
				"email_verification",
				"security_alert",
			).
			Comment("Type of notification"),
		field.String("title").
			MaxLen(200).
			Comment("Notification title"),
		field.Text("message").
			Comment("Notification message content"),
		field.JSON("data", map[string]interface{}{}).
			Optional().
			Comment("Additional notification data"),
		field.Enum("priority").
			Values("low", "normal", "high", "urgent").
			Default("normal").
			Comment("Notification priority"),
		field.JSON("channels", []string{}).
			Optional().
			Comment("Delivery channels (email, push, in_app, sms)"),
		field.Enum("status").
			Values("pending", "sent", "delivered", "failed", "read").
			Default("pending").
			Comment("Notification status"),
		field.String("reference_id").
			MaxLen(100).
			Optional().
			Comment("Reference to related entity"),
		field.String("reference_type").
			MaxLen(50).
			Optional().
			Comment("Type of referenced entity"),
		field.JSON("delivery_status", map[string]interface{}{}).
			Optional().
			Comment("Status for each delivery channel"),
		field.Time("scheduled_at").
			Optional().
			Comment("When the notification should be sent"),
		field.Time("sent_at").
			Optional().
			Comment("When the notification was sent"),
		field.Time("delivered_at").
			Optional().
			Comment("When the notification was delivered"),
		field.Time("read_at").
			Optional().
			Comment("When the notification was read"),
		field.Time("expires_at").
			Optional().
			Comment("When the notification expires"),
		field.String("action_url").
			MaxLen(500).
			Optional().
			Comment("URL for notification action"),
		field.JSON("action_data", map[string]interface{}{}).
			Optional().
			Comment("Data for notification action"),
		field.Int("retry_count").
			Default(0).
			Comment("Number of delivery retry attempts"),
		field.Time("next_retry_at").
			Optional().
			Comment("When to retry delivery"),
		field.String("error_message").
			MaxLen(1000).
			Optional().
			Comment("Last error message if delivery failed"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the Notification.
func (Notification) Edges() []ent.Edge {
	return nil
}

// Indexes of the Notification.
func (Notification) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("type"),
		index.Fields("status"),
		index.Fields("priority"),
		index.Fields("reference_id"),
		index.Fields("reference_type"),
		index.Fields("scheduled_at"),
		index.Fields("sent_at"),
		index.Fields("read_at"),
		index.Fields("expires_at"),
		index.Fields("next_retry_at"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "status"),
		index.Fields("user_id", "type"),
		index.Fields("user_id", "read_at"),
	}
}

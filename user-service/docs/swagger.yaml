basePath: /
definitions:
  auth.LoginRequest:
    properties:
      device_info:
        type: string
      email:
        type: string
      ip_address:
        type: string
      password:
        type: string
      remember:
        type: boolean
    required:
    - email
    - password
    type: object
  models.AuthAnalyticsResponse:
    properties:
      daily_stats:
        items:
          $ref: '#/definitions/models.DailyAuthStats'
        type: array
      device_stats:
        items:
          $ref: '#/definitions/models.DeviceStats'
        type: array
      failed_logins:
        type: integer
      period:
        type: string
      top_countries:
        items:
          $ref: '#/definitions/models.CountryStats'
        type: array
      total_logins:
        type: integer
      total_registrations:
        type: integer
      unique_users:
        type: integer
    type: object
  models.ChangePasswordRequest:
    properties:
      current_password:
        type: string
      new_password:
        minLength: 8
        type: string
      user_id:
        type: integer
    required:
    - current_password
    - new_password
    type: object
  models.CountryStats:
    properties:
      count:
        type: integer
      country:
        type: string
    type: object
  models.DailyAuthStats:
    properties:
      date:
        type: string
      failed_logins:
        type: integer
      logins:
        type: integer
      registrations:
        type: integer
    type: object
  models.DailySessionStats:
    properties:
      active_sessions:
        type: integer
      avg_duration:
        type: number
      date:
        type: string
      new_sessions:
        type: integer
    type: object
  models.DeviceStats:
    properties:
      count:
        type: integer
      device:
        type: string
    type: object
  models.ErrorResponse:
    properties:
      details:
        type: string
      error:
        type: string
      message:
        type: string
    type: object
  models.ForgotPasswordRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  models.ListSessionsResponse:
    properties:
      sessions:
        description: Can be []Session or []*SessionResponse
      total:
        type: integer
    type: object
  models.ListUsersResponse:
    properties:
      pagination:
        $ref: '#/definitions/models.PaginationMeta'
      users:
        items:
          $ref: '#/definitions/models.UserResponse'
        type: array
    type: object
  models.LocationStats:
    properties:
      city:
        type: string
      count:
        type: integer
      country:
        type: string
    type: object
  models.LockUserRequest:
    properties:
      reason:
        type: string
      user_id:
        type: integer
    required:
    - reason
    type: object
  models.LoginResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      last_login_at:
        type: string
      refresh_token:
        type: string
      token_type:
        type: string
      user:
        $ref: '#/definitions/models.UserInfo'
    type: object
  models.PaginationMeta:
    properties:
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  models.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  models.RegisterRequest:
    properties:
      email:
        type: string
      first_name:
        maxLength: 50
        minLength: 2
        type: string
      ip_address:
        type: string
      last_name:
        maxLength: 50
        minLength: 2
        type: string
      password:
        minLength: 8
        type: string
      user_agent:
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    type: object
  models.RegisterResponse:
    properties:
      email:
        type: string
      email_verified:
        type: boolean
      first_name:
        type: string
      last_name:
        type: string
      message:
        type: string
      user_id:
        type: integer
      verification_sent:
        type: boolean
    type: object
  models.ResendVerificationRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  models.ResetPasswordRequest:
    properties:
      new_password:
        minLength: 8
        type: string
      token:
        type: string
    required:
    - new_password
    - token
    type: object
  models.SessionAnalyticsResponse:
    properties:
      active_sessions:
        type: integer
      average_session_duration:
        type: number
      daily_stats:
        items:
          $ref: '#/definitions/models.DailySessionStats'
        type: array
      device_stats:
        items:
          $ref: '#/definitions/models.DeviceStats'
        type: array
      expired_sessions:
        type: integer
      location_stats:
        items:
          $ref: '#/definitions/models.LocationStats'
        type: array
      period:
        type: string
      total_sessions:
        type: integer
    type: object
  models.SuccessResponse:
    properties:
      data: {}
      message:
        type: string
    type: object
  models.TokenResponse:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
  models.UpdateUserRequest:
    properties:
      first_name:
        type: string
      id:
        type: integer
      last_name:
        type: string
      preferences:
        additionalProperties: true
        type: object
      profile:
        $ref: '#/definitions/models.UserProfile'
      role:
        type: string
      status:
        type: string
    type: object
  models.UploadAvatarResponse:
    properties:
      avatar_url:
        type: string
      message:
        type: string
    type: object
  models.UserInfo:
    properties:
      avatar_url:
        type: string
      created_at:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      first_name:
        type: string
      id:
        type: integer
      last_name:
        type: string
      role:
        type: string
      status:
        type: string
    type: object
  models.UserProfile:
    properties:
      bio:
        type: string
      company:
        type: string
      date_of_birth:
        type: string
      gender:
        type: string
      job_title:
        type: string
      language:
        type: string
      location:
        type: string
      phone:
        type: string
      timezone:
        type: string
      website:
        type: string
    type: object
  models.UserResponse:
    properties:
      avatar_url:
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      first_name:
        type: string
      id:
        type: integer
      last_login_at:
        type: string
      last_name:
        type: string
      preferences:
        additionalProperties: true
        type: object
      profile:
        $ref: '#/definitions/models.UserProfile'
      role:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  models.VerifyEmailRequest:
    properties:
      token:
        type: string
    required:
    - token
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: https://social-content-ai.com/support
  description: |-
    User Service API for Social Content AI platform
    This service handles user authentication, authorization, and profile management.

    ## Authentication
    Most endpoints require authentication using Bearer tokens.
    Include the token in the Authorization header: `Authorization: Bearer <token>`

    ## Rate Limiting
    API requests are rate limited to prevent abuse.
    Default limits: 60 requests per minute per user.

    ## Error Handling
    All errors follow a consistent format with error codes and messages.
    HTTP status codes indicate the type of error (4xx for client errors, 5xx for server errors).
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  title: User Service API
  version: "1.0"
paths:
  /admin/v1/analytics/auth:
    get:
      consumes:
      - application/json
      description: Get detailed authentication analytics including login patterns,
        failed attempts, and user registration trends. Admin access required.
      parameters:
      - default: week
        description: Time period for analytics
        enum:
        - day
        - week
        - month
        - year
        in: query
        name: period
        type: string
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Authentication analytics data
          schema:
            $ref: '#/definitions/models.AuthAnalyticsResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Admin access required
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "501":
          description: Feature not implemented
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get authentication analytics
      tags:
      - admin
      - analytics
      - auth
  /admin/v1/analytics/sessions:
    get:
      consumes:
      - application/json
      description: Get detailed session analytics including active sessions, login
        patterns, and security metrics. Admin access required.
      parameters:
      - default: week
        description: Time period for analytics
        enum:
        - day
        - week
        - month
        - year
        in: query
        name: period
        type: string
      - description: Start date (YYYY-MM-DD)
        format: date
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        format: date
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Session analytics data
          schema:
            $ref: '#/definitions/models.SessionAnalyticsResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Admin access required
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get session analytics
      tags:
      - admin
      - analytics
      - sessions
  /api/v1/auth/forgot-password:
    post:
      consumes:
      - application/json
      description: Send a password reset email to the user. For security, the response
        is the same whether the email exists or not.
      parameters:
      - description: Email address for password reset
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ForgotPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password reset email sent (if email exists)
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Request password reset
      tags:
      - auth
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and password. Returns access and refresh
        tokens on success.
      parameters:
      - description: User login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/auth.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful with tokens
          schema:
            $ref: '#/definitions/models.LoginResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Invalid credentials, account locked, or email not verified
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: User login
      tags:
      - auth
  /api/v1/auth/logout:
    post:
      consumes:
      - application/json
      description: Logout the authenticated user and invalidate the current session.
        The access token will be blacklisted.
      produces:
      - application/json
      responses:
        "200":
          description: Successfully logged out
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: User logout
      tags:
      - auth
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: Generate a new access token using a valid refresh token. The refresh
        token must not be expired.
      parameters:
      - description: Refresh token details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: New access token generated
          schema:
            $ref: '#/definitions/models.TokenResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Invalid or expired refresh token
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Refresh access token
      tags:
      - auth
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: Create a new user account with email verification. Password must
        meet security requirements.
      parameters:
      - description: User registration details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User successfully registered
          schema:
            $ref: '#/definitions/models.RegisterResponse'
        "400":
          description: Invalid request data or weak password
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "409":
          description: Email already exists
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Register a new user
      tags:
      - auth
  /api/v1/auth/resend-verification:
    post:
      consumes:
      - application/json
      description: Resend email verification link to the user. For security, the response
        is the same whether the email exists or not.
      parameters:
      - description: Email address for verification resend
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ResendVerificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Verification email sent (if email exists)
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "501":
          description: Feature not implemented
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Resend email verification
      tags:
      - auth
  /api/v1/auth/reset-password:
    post:
      consumes:
      - application/json
      description: Reset user password using a valid reset token. The new password
        must meet security requirements.
      parameters:
      - description: Reset token and new password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password successfully reset
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid request, token, or weak password
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Reset password
      tags:
      - auth
  /api/v1/auth/verify-email:
    post:
      consumes:
      - application/json
      description: Verify user email address using the verification token sent during
        registration.
      parameters:
      - description: Email verification token
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.VerifyEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Email successfully verified
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid request or verification token
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Verify email address
      tags:
      - auth
  /api/v1/profile:
    get:
      description: Get current user profile information
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - profile
    put:
      consumes:
      - application/json
      description: Update current user profile information
      parameters:
      - description: Update profile request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update user profile
      tags:
      - profile
  /api/v1/sessions:
    delete:
      consumes:
      - application/json
      description: Revoke all sessions for the authenticated user except the current
        session. This is useful for security purposes when user suspects account compromise.
      produces:
      - application/json
      responses:
        "200":
          description: All sessions successfully revoked
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Revoke all sessions
      tags:
      - sessions
    get:
      consumes:
      - application/json
      description: Get a paginated list of all active sessions for the authenticated
        user
      parameters:
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 50
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Session status filter
        enum:
        - active
        - expired
        - revoked
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved sessions
          schema:
            $ref: '#/definitions/models.ListSessionsResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List user sessions
      tags:
      - sessions
  /api/v1/sessions/{id}:
    delete:
      consumes:
      - application/json
      description: Revoke a specific session by its unique identifier. The session
        will be immediately invalidated.
      parameters:
      - description: Session ID
        example: '"550e8400-e29b-41d4-a716-************"'
        format: uuid
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Session successfully revoked
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid session ID format
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Not authorized to revoke this session
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Session not found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Revoke a session
      tags:
      - sessions
  /api/v1/users:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all users with optional search and filtering.
        Admin access required.
      parameters:
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Search term (name, email)
        in: query
        name: search
        type: string
      - description: User status filter
        enum:
        - active
        - inactive
        - locked
        - pending
        in: query
        name: status
        type: string
      - description: User role filter
        enum:
        - user
        - admin
        - moderator
        in: query
        name: role
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Users retrieved successfully
          schema:
            $ref: '#/definitions/models.ListUsersResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Admin access required
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List users
      tags:
      - admin
      - users
  /api/v1/users/{id}:
    delete:
      description: Delete user account (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete user
      tags:
      - users
    get:
      description: Get user information by ID (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by ID
      tags:
      - users
    put:
      consumes:
      - application/json
      description: Update user information (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Update user request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - users
  /api/v1/users/{id}/lock:
    post:
      consumes:
      - application/json
      description: Lock user account (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Lock user request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.LockUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Lock user account
      tags:
      - users
  /api/v1/users/{id}/reset-password:
    post:
      consumes:
      - application/json
      description: Reset user password (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Reset password request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Admin reset user password
      tags:
      - users
  /api/v1/users/{id}/unlock:
    post:
      description: Unlock user account (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Unlock user account
      tags:
      - users
  /api/v1/users/me:
    delete:
      consumes:
      - application/json
      description: Permanently delete the current authenticated user account. This
        action cannot be undone.
      produces:
      - application/json
      responses:
        "200":
          description: User account successfully deleted
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete current user
      tags:
      - users
    get:
      consumes:
      - application/json
      description: Get detailed information about the currently authenticated user
        including profile data and settings.
      produces:
      - application/json
      responses:
        "200":
          description: User information retrieved successfully
          schema:
            $ref: '#/definitions/models.UserResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user
      tags:
      - users
    put:
      consumes:
      - application/json
      description: Update profile information for the currently authenticated user.
        Only provided fields will be updated.
      parameters:
      - description: User profile update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User successfully updated
          schema:
            $ref: '#/definitions/models.UserResponse'
        "400":
          description: Invalid request data
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update current user
      tags:
      - users
  /api/v1/users/me/change-password:
    post:
      consumes:
      - application/json
      description: Change the current user's password. Requires current password for
        verification and new password must meet security requirements.
      parameters:
      - description: Current and new password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password successfully changed
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Invalid current password or weak new password
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Change password
      tags:
      - users
  /api/v1/users/me/upload-avatar:
    post:
      consumes:
      - multipart/form-data
      description: 'Upload a new avatar image for the current user. Supported formats:
        JPEG, PNG, GIF. Maximum file size: 5MB.'
      parameters:
      - description: Avatar image file (JPEG, PNG, GIF, max 5MB)
        in: formData
        name: avatar
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Avatar successfully uploaded
          schema:
            $ref: '#/definitions/models.UploadAvatarResponse'
        "400":
          description: Invalid file type, size, or missing file
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Upload user avatar
      tags:
      - users
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization operations
  name: auth
- description: User profile and account management operations
  name: users
- description: User session management operations
  name: sessions
- description: Administrative operations (admin access required)
  name: admin
- description: Analytics and reporting operations
  name: analytics

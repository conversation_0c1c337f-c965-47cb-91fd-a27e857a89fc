// Code generated by ent, DO NOT EDIT.

package banktransfer

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the banktransfer type in the database.
	Label = "bank_transfer"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldPlanID holds the string denoting the plan_id field in the database.
	FieldPlanID = "plan_id"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldBankAccount holds the string denoting the bank_account field in the database.
	FieldBankAccount = "bank_account"
	// FieldReferenceCode holds the string denoting the reference_code field in the database.
	FieldReferenceCode = "reference_code"
	// FieldTransferInstructions holds the string denoting the transfer_instructions field in the database.
	FieldTransferInstructions = "transfer_instructions"
	// FieldConfirmedAt holds the string denoting the confirmed_at field in the database.
	FieldConfirmedAt = "confirmed_at"
	// FieldExpiresAt holds the string denoting the expires_at field in the database.
	FieldExpiresAt = "expires_at"
	// FieldCreditsToAdd holds the string denoting the credits_to_add field in the database.
	FieldCreditsToAdd = "credits_to_add"
	// FieldBankTransactionID holds the string denoting the bank_transaction_id field in the database.
	FieldBankTransactionID = "bank_transaction_id"
	// FieldActualAmountReceived holds the string denoting the actual_amount_received field in the database.
	FieldActualAmountReceived = "actual_amount_received"
	// FieldConfirmationMethod holds the string denoting the confirmation_method field in the database.
	FieldConfirmationMethod = "confirmation_method"
	// FieldConfirmedBy holds the string denoting the confirmed_by field in the database.
	FieldConfirmedBy = "confirmed_by"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the banktransfer in the database.
	Table = "bank_transfers"
)

// Columns holds all SQL columns for banktransfer fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldPlanID,
	FieldAmount,
	FieldCurrency,
	FieldStatus,
	FieldBankAccount,
	FieldReferenceCode,
	FieldTransferInstructions,
	FieldConfirmedAt,
	FieldExpiresAt,
	FieldCreditsToAdd,
	FieldBankTransactionID,
	FieldActualAmountReceived,
	FieldConfirmationMethod,
	FieldConfirmedBy,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	AmountValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// BankAccountValidator is a validator for the "bank_account" field. It is called by the builders before save.
	BankAccountValidator func(string) error
	// ReferenceCodeValidator is a validator for the "reference_code" field. It is called by the builders before save.
	ReferenceCodeValidator func(string) error
	// DefaultCreditsToAdd holds the default value on creation for the "credits_to_add" field.
	DefaultCreditsToAdd int
	// CreditsToAddValidator is a validator for the "credits_to_add" field. It is called by the builders before save.
	CreditsToAddValidator func(int) error
	// BankTransactionIDValidator is a validator for the "bank_transaction_id" field. It is called by the builders before save.
	BankTransactionIDValidator func(string) error
	// ActualAmountReceivedValidator is a validator for the "actual_amount_received" field. It is called by the builders before save.
	ActualAmountReceivedValidator func(int64) error
	// DefaultConfirmationMethod holds the default value on creation for the "confirmation_method" field.
	DefaultConfirmationMethod string
	// ConfirmationMethodValidator is a validator for the "confirmation_method" field. It is called by the builders before save.
	ConfirmationMethodValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending   Status = "pending"
	StatusConfirmed Status = "confirmed"
	StatusFailed    Status = "failed"
	StatusExpired   Status = "expired"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusConfirmed, StatusFailed, StatusExpired:
		return nil
	default:
		return fmt.Errorf("banktransfer: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the BankTransfer queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByPlanID orders the results by the plan_id field.
func ByPlanID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanID, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByBankAccount orders the results by the bank_account field.
func ByBankAccount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankAccount, opts...).ToFunc()
}

// ByReferenceCode orders the results by the reference_code field.
func ByReferenceCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceCode, opts...).ToFunc()
}

// ByTransferInstructions orders the results by the transfer_instructions field.
func ByTransferInstructions(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransferInstructions, opts...).ToFunc()
}

// ByConfirmedAt orders the results by the confirmed_at field.
func ByConfirmedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmedAt, opts...).ToFunc()
}

// ByExpiresAt orders the results by the expires_at field.
func ByExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresAt, opts...).ToFunc()
}

// ByCreditsToAdd orders the results by the credits_to_add field.
func ByCreditsToAdd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreditsToAdd, opts...).ToFunc()
}

// ByBankTransactionID orders the results by the bank_transaction_id field.
func ByBankTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankTransactionID, opts...).ToFunc()
}

// ByActualAmountReceived orders the results by the actual_amount_received field.
func ByActualAmountReceived(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActualAmountReceived, opts...).ToFunc()
}

// ByConfirmationMethod orders the results by the confirmation_method field.
func ByConfirmationMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmationMethod, opts...).ToFunc()
}

// ByConfirmedBy orders the results by the confirmed_by field.
func ByConfirmedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmedBy, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

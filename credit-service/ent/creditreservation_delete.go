// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditReservationDelete is the builder for deleting a CreditReservation entity.
type CreditReservationDelete struct {
	config
	hooks    []Hook
	mutation *CreditReservationMutation
}

// Where appends a list predicates to the CreditReservationDelete builder.
func (crd *CreditReservationDelete) Where(ps ...predicate.CreditReservation) *CreditReservationDelete {
	crd.mutation.Where(ps...)
	return crd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (crd *CreditReservationDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, crd.sqlExec, crd.mutation, crd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (crd *CreditReservationDelete) ExecX(ctx context.Context) int {
	n, err := crd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (crd *CreditReservationDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(creditreservation.Table, sqlgraph.NewFieldSpec(creditreservation.FieldID, field.TypeUUID))
	if ps := crd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, crd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	crd.mutation.done = true
	return affected, err
}

// CreditReservationDeleteOne is the builder for deleting a single CreditReservation entity.
type CreditReservationDeleteOne struct {
	crd *CreditReservationDelete
}

// Where appends a list predicates to the CreditReservationDelete builder.
func (crdo *CreditReservationDeleteOne) Where(ps ...predicate.CreditReservation) *CreditReservationDeleteOne {
	crdo.crd.mutation.Where(ps...)
	return crdo
}

// Exec executes the deletion query.
func (crdo *CreditReservationDeleteOne) Exec(ctx context.Context) error {
	n, err := crdo.crd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{creditreservation.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (crdo *CreditReservationDeleteOne) ExecX(ctx context.Context) {
	if err := crdo.Exec(ctx); err != nil {
		panic(err)
	}
}

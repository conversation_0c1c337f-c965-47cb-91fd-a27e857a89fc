package publishers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// TwitterPublisher handles Twitter content publishing
type TwitterPublisher struct {
	httpClient *http.Client
	logger     logging.Logger
}

// NewTwitterPublisher creates a new Twitter publisher
func NewTwitterPublisher(logger logging.Logger) Publisher {
	return &TwitterPublisher{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// TwitterTweetRequest represents a tweet request
type TwitterTweetRequest struct {
	Text       string   `json:"text"`
	MediaIDs   []string `json:"media_ids,omitempty"`
	ReplyTo    string   `json:"reply_to,omitempty"`
	QuoteTweet string   `json:"quote_tweet_id,omitempty"`
}

// TwitterTweetResponse represents a tweet response
type TwitterTweetResponse struct {
	Data struct {
		ID   string `json:"id"`
		Text string `json:"text"`
	} `json:"data"`
}

// TwitterMediaUploadResponse represents media upload response
type TwitterMediaUploadResponse struct {
	MediaID          int64  `json:"media_id"`
	MediaIDString    string `json:"media_id_string"`
	Size             int    `json:"size"`
	ExpiresAfterSecs int    `json:"expires_after_secs"`
	ProcessingInfo   struct {
		State string `json:"state"`
	} `json:"processing_info"`
}

// TwitterErrorResponse represents Twitter error response
type TwitterErrorResponse struct {
	Errors []struct {
		Message string `json:"message"`
		Code    int    `json:"code"`
	} `json:"errors"`
}

// PublishPost publishes content to Twitter
func (t *TwitterPublisher) PublishPost(ctx context.Context, req *PublishRequest) (*PublishResponse, error) {
	t.logger.Info("Publishing tweet to Twitter")

	// Upload media if provided
	var mediaIDs []string
	if len(req.ImageURLs) > 0 {
		for _, imageURL := range req.ImageURLs {
			mediaID, err := t.uploadMedia(ctx, req.AccessToken, imageURL)
			if err != nil {
				t.logger.WithError(err).Error("Failed to upload media")
				return &PublishResponse{
					Success:      false,
					ErrorMessage: fmt.Sprintf("Failed to upload media: %v", err),
				}, nil
			}
			mediaIDs = append(mediaIDs, mediaID)
		}
	}

	// Prepare tweet content
	content := req.Content
	if len(req.Hashtags) > 0 {
		hashtags := make([]string, len(req.Hashtags))
		for i, tag := range req.Hashtags {
			if !strings.HasPrefix(tag, "#") {
				hashtags[i] = "#" + tag
			} else {
				hashtags[i] = tag
			}
		}
		content += " " + strings.Join(hashtags, " ")
	}

	// Create tweet request
	tweetReq := TwitterTweetRequest{
		Text:     content,
		MediaIDs: mediaIDs,
	}

	// Handle scheduled tweets
	if req.ScheduledAt > 0 {
		// Twitter API v2 doesn't support scheduled tweets directly
		// This would need to be handled by a scheduler service
		t.logger.Info("Scheduled tweets not supported directly, would need scheduler")
	}

	// Post tweet
	tweetURL := "https://api.twitter.com/2/tweets"

	jsonData, err := json.Marshal(tweetReq)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to marshal tweet request: %v", err),
		}, nil
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", tweetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create request: %v", err),
		}, nil
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := t.httpClient.Do(httpReq)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to post tweet: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		var errorResp TwitterErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		errorMsg := "Unknown error"
		if len(errorResp.Errors) > 0 {
			errorMsg = errorResp.Errors[0].Message
		}
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Twitter API error: %s", errorMsg),
		}, nil
	}

	var tweetResp TwitterTweetResponse
	if err := json.NewDecoder(resp.Body).Decode(&tweetResp); err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to decode response: %v", err),
		}, nil
	}

	platformURL := fmt.Sprintf("https://twitter.com/i/web/status/%s", tweetResp.Data.ID)

	return &PublishResponse{
		PlatformPostID: tweetResp.Data.ID,
		PlatformURL:    platformURL,
		Success:        true,
		PublishedAt:    time.Now().Unix(),
		Metadata: map[string]string{
			"tweet_text": tweetResp.Data.Text,
		},
	}, nil
}

// uploadMedia uploads media to Twitter
func (t *TwitterPublisher) uploadMedia(ctx context.Context, accessToken, imageURL string) (string, error) {
	// Download image
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	// Create multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add media file
	part, err := writer.CreateFormFile("media", "image.jpg")
	if err != nil {
		return "", fmt.Errorf("failed to create form file: %w", err)
	}

	// Copy image data
	if _, err := part.Write([]byte{}); err != nil { // TODO: Copy actual image data
		return "", fmt.Errorf("failed to write image data: %w", err)
	}

	writer.Close()

	// Upload to Twitter
	uploadURL := "https://upload.twitter.com/1.1/media/upload.json"

	req, err := http.NewRequestWithContext(ctx, "POST", uploadURL, &buf)
	if err != nil {
		return "", fmt.Errorf("failed to create upload request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))
	req.Header.Set("Content-Type", writer.FormDataContentType())

	uploadResp, err := t.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to upload media: %w", err)
	}
	defer uploadResp.Body.Close()

	if uploadResp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("media upload failed with status: %d", uploadResp.StatusCode)
	}

	var mediaResp TwitterMediaUploadResponse
	if err := json.NewDecoder(uploadResp.Body).Decode(&mediaResp); err != nil {
		return "", fmt.Errorf("failed to decode upload response: %w", err)
	}

	return mediaResp.MediaIDString, nil
}

// GetPostAnalytics retrieves analytics for a tweet
func (t *TwitterPublisher) GetPostAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error) {
	t.logger.WithField("tweet_id", req.PlatformPostID).Info("Getting tweet analytics")

	// Twitter API v2 analytics endpoint
	analyticsURL := fmt.Sprintf("https://api.twitter.com/2/tweets/%s", req.PlatformPostID)

	params := url.Values{}
	params.Set("tweet.fields", "public_metrics,created_at")

	httpReq, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", analyticsURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))

	resp, err := t.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("analytics request failed with status: %d", resp.StatusCode)
	}

	var analyticsResp struct {
		Data struct {
			ID            string `json:"id"`
			PublicMetrics struct {
				RetweetCount int64 `json:"retweet_count"`
				LikeCount    int64 `json:"like_count"`
				ReplyCount   int64 `json:"reply_count"`
				QuoteCount   int64 `json:"quote_count"`
			} `json:"public_metrics"`
			CreatedAt string `json:"created_at"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&analyticsResp); err != nil {
		return nil, fmt.Errorf("failed to decode analytics response: %w", err)
	}

	metrics := map[string]int64{
		"retweets":   analyticsResp.Data.PublicMetrics.RetweetCount,
		"likes":      analyticsResp.Data.PublicMetrics.LikeCount,
		"replies":    analyticsResp.Data.PublicMetrics.ReplyCount,
		"quotes":     analyticsResp.Data.PublicMetrics.QuoteCount,
		"engagement": analyticsResp.Data.PublicMetrics.RetweetCount + analyticsResp.Data.PublicMetrics.LikeCount + analyticsResp.Data.PublicMetrics.ReplyCount,
	}

	return &AnalyticsResponse{
		PlatformPostID: req.PlatformPostID,
		Metrics:        metrics,
		CollectedAt:    time.Now().Unix(),
	}, nil
}

// DeletePost deletes a tweet
func (t *TwitterPublisher) DeletePost(ctx context.Context, req *DeleteRequest) error {
	t.logger.WithField("tweet_id", req.PlatformPostID).Info("Deleting tweet")

	deleteURL := fmt.Sprintf("https://api.twitter.com/2/tweets/%s", req.PlatformPostID)

	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", deleteURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))

	resp, err := t.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to delete tweet: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("delete request failed with status: %d", resp.StatusCode)
	}

	return nil
}

// GetPlatformLimits gets Twitter rate limits
func (t *TwitterPublisher) GetPlatformLimits(ctx context.Context) (*PlatformLimits, error) {
	// Twitter rate limits (approximate)
	return &PlatformLimits{
		RateLimits: map[string]int32{
			"tweets":    300, // 300 tweets per 15 minutes
			"api_calls": 900, // 900 API calls per 15 minutes
		},
		CurrentUsage: map[string]int32{
			"tweets":    0,
			"api_calls": 0,
		},
		ResetTimes: map[string]int64{
			"tweets":    time.Now().Add(15 * time.Minute).Unix(),
			"api_calls": time.Now().Add(15 * time.Minute).Unix(),
		},
	}, nil
}

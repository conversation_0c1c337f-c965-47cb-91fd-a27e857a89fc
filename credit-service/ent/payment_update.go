// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/payment"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// PaymentUpdate is the builder for updating Payment entities.
type PaymentUpdate struct {
	config
	hooks    []Hook
	mutation *PaymentMutation
}

// Where appends a list predicates to the PaymentUpdate builder.
func (pu *PaymentUpdate) Where(ps ...predicate.Payment) *PaymentUpdate {
	pu.mutation.Where(ps...)
	return pu
}

// SetUserID sets the "user_id" field.
func (pu *PaymentUpdate) SetUserID(u uuid.UUID) *PaymentUpdate {
	pu.mutation.SetUserID(u)
	return pu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableUserID(u *uuid.UUID) *PaymentUpdate {
	if u != nil {
		pu.SetUserID(*u)
	}
	return pu
}

// SetOrderID sets the "order_id" field.
func (pu *PaymentUpdate) SetOrderID(u uuid.UUID) *PaymentUpdate {
	pu.mutation.SetOrderID(u)
	return pu
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableOrderID(u *uuid.UUID) *PaymentUpdate {
	if u != nil {
		pu.SetOrderID(*u)
	}
	return pu
}

// ClearOrderID clears the value of the "order_id" field.
func (pu *PaymentUpdate) ClearOrderID() *PaymentUpdate {
	pu.mutation.ClearOrderID()
	return pu
}

// SetSubscriptionID sets the "subscription_id" field.
func (pu *PaymentUpdate) SetSubscriptionID(u uuid.UUID) *PaymentUpdate {
	pu.mutation.SetSubscriptionID(u)
	return pu
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableSubscriptionID(u *uuid.UUID) *PaymentUpdate {
	if u != nil {
		pu.SetSubscriptionID(*u)
	}
	return pu
}

// ClearSubscriptionID clears the value of the "subscription_id" field.
func (pu *PaymentUpdate) ClearSubscriptionID() *PaymentUpdate {
	pu.mutation.ClearSubscriptionID()
	return pu
}

// SetPaymentMethod sets the "payment_method" field.
func (pu *PaymentUpdate) SetPaymentMethod(s string) *PaymentUpdate {
	pu.mutation.SetPaymentMethod(s)
	return pu
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillablePaymentMethod(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetPaymentMethod(*s)
	}
	return pu
}

// SetPaymentProvider sets the "payment_provider" field.
func (pu *PaymentUpdate) SetPaymentProvider(s string) *PaymentUpdate {
	pu.mutation.SetPaymentProvider(s)
	return pu
}

// SetNillablePaymentProvider sets the "payment_provider" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillablePaymentProvider(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetPaymentProvider(*s)
	}
	return pu
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (pu *PaymentUpdate) SetExternalPaymentID(s string) *PaymentUpdate {
	pu.mutation.SetExternalPaymentID(s)
	return pu
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableExternalPaymentID(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetExternalPaymentID(*s)
	}
	return pu
}

// ClearExternalPaymentID clears the value of the "external_payment_id" field.
func (pu *PaymentUpdate) ClearExternalPaymentID() *PaymentUpdate {
	pu.mutation.ClearExternalPaymentID()
	return pu
}

// SetAmount sets the "amount" field.
func (pu *PaymentUpdate) SetAmount(i int64) *PaymentUpdate {
	pu.mutation.ResetAmount()
	pu.mutation.SetAmount(i)
	return pu
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableAmount(i *int64) *PaymentUpdate {
	if i != nil {
		pu.SetAmount(*i)
	}
	return pu
}

// AddAmount adds i to the "amount" field.
func (pu *PaymentUpdate) AddAmount(i int64) *PaymentUpdate {
	pu.mutation.AddAmount(i)
	return pu
}

// SetCurrency sets the "currency" field.
func (pu *PaymentUpdate) SetCurrency(s string) *PaymentUpdate {
	pu.mutation.SetCurrency(s)
	return pu
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableCurrency(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetCurrency(*s)
	}
	return pu
}

// SetStatus sets the "status" field.
func (pu *PaymentUpdate) SetStatus(pa payment.Status) *PaymentUpdate {
	pu.mutation.SetStatus(pa)
	return pu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableStatus(pa *payment.Status) *PaymentUpdate {
	if pa != nil {
		pu.SetStatus(*pa)
	}
	return pu
}

// SetPaymentIntentID sets the "payment_intent_id" field.
func (pu *PaymentUpdate) SetPaymentIntentID(s string) *PaymentUpdate {
	pu.mutation.SetPaymentIntentID(s)
	return pu
}

// SetNillablePaymentIntentID sets the "payment_intent_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillablePaymentIntentID(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetPaymentIntentID(*s)
	}
	return pu
}

// ClearPaymentIntentID clears the value of the "payment_intent_id" field.
func (pu *PaymentUpdate) ClearPaymentIntentID() *PaymentUpdate {
	pu.mutation.ClearPaymentIntentID()
	return pu
}

// SetChargeID sets the "charge_id" field.
func (pu *PaymentUpdate) SetChargeID(s string) *PaymentUpdate {
	pu.mutation.SetChargeID(s)
	return pu
}

// SetNillableChargeID sets the "charge_id" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableChargeID(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetChargeID(*s)
	}
	return pu
}

// ClearChargeID clears the value of the "charge_id" field.
func (pu *PaymentUpdate) ClearChargeID() *PaymentUpdate {
	pu.mutation.ClearChargeID()
	return pu
}

// SetBillingDetails sets the "billing_details" field.
func (pu *PaymentUpdate) SetBillingDetails(m map[string]interface{}) *PaymentUpdate {
	pu.mutation.SetBillingDetails(m)
	return pu
}

// ClearBillingDetails clears the value of the "billing_details" field.
func (pu *PaymentUpdate) ClearBillingDetails() *PaymentUpdate {
	pu.mutation.ClearBillingDetails()
	return pu
}

// SetPaymentMethodDetails sets the "payment_method_details" field.
func (pu *PaymentUpdate) SetPaymentMethodDetails(m map[string]interface{}) *PaymentUpdate {
	pu.mutation.SetPaymentMethodDetails(m)
	return pu
}

// ClearPaymentMethodDetails clears the value of the "payment_method_details" field.
func (pu *PaymentUpdate) ClearPaymentMethodDetails() *PaymentUpdate {
	pu.mutation.ClearPaymentMethodDetails()
	return pu
}

// SetPaidAt sets the "paid_at" field.
func (pu *PaymentUpdate) SetPaidAt(t time.Time) *PaymentUpdate {
	pu.mutation.SetPaidAt(t)
	return pu
}

// SetNillablePaidAt sets the "paid_at" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillablePaidAt(t *time.Time) *PaymentUpdate {
	if t != nil {
		pu.SetPaidAt(*t)
	}
	return pu
}

// ClearPaidAt clears the value of the "paid_at" field.
func (pu *PaymentUpdate) ClearPaidAt() *PaymentUpdate {
	pu.mutation.ClearPaidAt()
	return pu
}

// SetFailedAt sets the "failed_at" field.
func (pu *PaymentUpdate) SetFailedAt(t time.Time) *PaymentUpdate {
	pu.mutation.SetFailedAt(t)
	return pu
}

// SetNillableFailedAt sets the "failed_at" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableFailedAt(t *time.Time) *PaymentUpdate {
	if t != nil {
		pu.SetFailedAt(*t)
	}
	return pu
}

// ClearFailedAt clears the value of the "failed_at" field.
func (pu *PaymentUpdate) ClearFailedAt() *PaymentUpdate {
	pu.mutation.ClearFailedAt()
	return pu
}

// SetFailureCode sets the "failure_code" field.
func (pu *PaymentUpdate) SetFailureCode(s string) *PaymentUpdate {
	pu.mutation.SetFailureCode(s)
	return pu
}

// SetNillableFailureCode sets the "failure_code" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableFailureCode(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetFailureCode(*s)
	}
	return pu
}

// ClearFailureCode clears the value of the "failure_code" field.
func (pu *PaymentUpdate) ClearFailureCode() *PaymentUpdate {
	pu.mutation.ClearFailureCode()
	return pu
}

// SetFailureMessage sets the "failure_message" field.
func (pu *PaymentUpdate) SetFailureMessage(s string) *PaymentUpdate {
	pu.mutation.SetFailureMessage(s)
	return pu
}

// SetNillableFailureMessage sets the "failure_message" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableFailureMessage(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetFailureMessage(*s)
	}
	return pu
}

// ClearFailureMessage clears the value of the "failure_message" field.
func (pu *PaymentUpdate) ClearFailureMessage() *PaymentUpdate {
	pu.mutation.ClearFailureMessage()
	return pu
}

// SetRefundedAmount sets the "refunded_amount" field.
func (pu *PaymentUpdate) SetRefundedAmount(i int64) *PaymentUpdate {
	pu.mutation.ResetRefundedAmount()
	pu.mutation.SetRefundedAmount(i)
	return pu
}

// SetNillableRefundedAmount sets the "refunded_amount" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableRefundedAmount(i *int64) *PaymentUpdate {
	if i != nil {
		pu.SetRefundedAmount(*i)
	}
	return pu
}

// AddRefundedAmount adds i to the "refunded_amount" field.
func (pu *PaymentUpdate) AddRefundedAmount(i int64) *PaymentUpdate {
	pu.mutation.AddRefundedAmount(i)
	return pu
}

// SetRefundedAt sets the "refunded_at" field.
func (pu *PaymentUpdate) SetRefundedAt(t time.Time) *PaymentUpdate {
	pu.mutation.SetRefundedAt(t)
	return pu
}

// SetNillableRefundedAt sets the "refunded_at" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableRefundedAt(t *time.Time) *PaymentUpdate {
	if t != nil {
		pu.SetRefundedAt(*t)
	}
	return pu
}

// ClearRefundedAt clears the value of the "refunded_at" field.
func (pu *PaymentUpdate) ClearRefundedAt() *PaymentUpdate {
	pu.mutation.ClearRefundedAt()
	return pu
}

// SetRefundReason sets the "refund_reason" field.
func (pu *PaymentUpdate) SetRefundReason(s string) *PaymentUpdate {
	pu.mutation.SetRefundReason(s)
	return pu
}

// SetNillableRefundReason sets the "refund_reason" field if the given value is not nil.
func (pu *PaymentUpdate) SetNillableRefundReason(s *string) *PaymentUpdate {
	if s != nil {
		pu.SetRefundReason(*s)
	}
	return pu
}

// ClearRefundReason clears the value of the "refund_reason" field.
func (pu *PaymentUpdate) ClearRefundReason() *PaymentUpdate {
	pu.mutation.ClearRefundReason()
	return pu
}

// SetWebhookData sets the "webhook_data" field.
func (pu *PaymentUpdate) SetWebhookData(m map[string]interface{}) *PaymentUpdate {
	pu.mutation.SetWebhookData(m)
	return pu
}

// ClearWebhookData clears the value of the "webhook_data" field.
func (pu *PaymentUpdate) ClearWebhookData() *PaymentUpdate {
	pu.mutation.ClearWebhookData()
	return pu
}

// SetMetadata sets the "metadata" field.
func (pu *PaymentUpdate) SetMetadata(m map[string]interface{}) *PaymentUpdate {
	pu.mutation.SetMetadata(m)
	return pu
}

// ClearMetadata clears the value of the "metadata" field.
func (pu *PaymentUpdate) ClearMetadata() *PaymentUpdate {
	pu.mutation.ClearMetadata()
	return pu
}

// SetUpdatedAt sets the "updated_at" field.
func (pu *PaymentUpdate) SetUpdatedAt(t time.Time) *PaymentUpdate {
	pu.mutation.SetUpdatedAt(t)
	return pu
}

// Mutation returns the PaymentMutation object of the builder.
func (pu *PaymentUpdate) Mutation() *PaymentMutation {
	return pu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pu *PaymentUpdate) Save(ctx context.Context) (int, error) {
	pu.defaults()
	return withHooks(ctx, pu.sqlSave, pu.mutation, pu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pu *PaymentUpdate) SaveX(ctx context.Context) int {
	affected, err := pu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pu *PaymentUpdate) Exec(ctx context.Context) error {
	_, err := pu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pu *PaymentUpdate) ExecX(ctx context.Context) {
	if err := pu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pu *PaymentUpdate) defaults() {
	if _, ok := pu.mutation.UpdatedAt(); !ok {
		v := payment.UpdateDefaultUpdatedAt()
		pu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pu *PaymentUpdate) check() error {
	if v, ok := pu.mutation.PaymentMethod(); ok {
		if err := payment.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_method": %w`, err)}
		}
	}
	if v, ok := pu.mutation.PaymentProvider(); ok {
		if err := payment.PaymentProviderValidator(v); err != nil {
			return &ValidationError{Name: "payment_provider", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_provider": %w`, err)}
		}
	}
	if v, ok := pu.mutation.ExternalPaymentID(); ok {
		if err := payment.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "Payment.external_payment_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Amount(); ok {
		if err := payment.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Payment.amount": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Currency(); ok {
		if err := payment.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Payment.currency": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Status(); ok {
		if err := payment.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Payment.status": %w`, err)}
		}
	}
	if v, ok := pu.mutation.PaymentIntentID(); ok {
		if err := payment.PaymentIntentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_intent_id", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_intent_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.ChargeID(); ok {
		if err := payment.ChargeIDValidator(v); err != nil {
			return &ValidationError{Name: "charge_id", err: fmt.Errorf(`ent: validator failed for field "Payment.charge_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.FailureCode(); ok {
		if err := payment.FailureCodeValidator(v); err != nil {
			return &ValidationError{Name: "failure_code", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_code": %w`, err)}
		}
	}
	if v, ok := pu.mutation.FailureMessage(); ok {
		if err := payment.FailureMessageValidator(v); err != nil {
			return &ValidationError{Name: "failure_message", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_message": %w`, err)}
		}
	}
	if v, ok := pu.mutation.RefundedAmount(); ok {
		if err := payment.RefundedAmountValidator(v); err != nil {
			return &ValidationError{Name: "refunded_amount", err: fmt.Errorf(`ent: validator failed for field "Payment.refunded_amount": %w`, err)}
		}
	}
	if v, ok := pu.mutation.RefundReason(); ok {
		if err := payment.RefundReasonValidator(v); err != nil {
			return &ValidationError{Name: "refund_reason", err: fmt.Errorf(`ent: validator failed for field "Payment.refund_reason": %w`, err)}
		}
	}
	return nil
}

func (pu *PaymentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(payment.Table, payment.Columns, sqlgraph.NewFieldSpec(payment.FieldID, field.TypeUUID))
	if ps := pu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pu.mutation.UserID(); ok {
		_spec.SetField(payment.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := pu.mutation.OrderID(); ok {
		_spec.SetField(payment.FieldOrderID, field.TypeUUID, value)
	}
	if pu.mutation.OrderIDCleared() {
		_spec.ClearField(payment.FieldOrderID, field.TypeUUID)
	}
	if value, ok := pu.mutation.SubscriptionID(); ok {
		_spec.SetField(payment.FieldSubscriptionID, field.TypeUUID, value)
	}
	if pu.mutation.SubscriptionIDCleared() {
		_spec.ClearField(payment.FieldSubscriptionID, field.TypeUUID)
	}
	if value, ok := pu.mutation.PaymentMethod(); ok {
		_spec.SetField(payment.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := pu.mutation.PaymentProvider(); ok {
		_spec.SetField(payment.FieldPaymentProvider, field.TypeString, value)
	}
	if value, ok := pu.mutation.ExternalPaymentID(); ok {
		_spec.SetField(payment.FieldExternalPaymentID, field.TypeString, value)
	}
	if pu.mutation.ExternalPaymentIDCleared() {
		_spec.ClearField(payment.FieldExternalPaymentID, field.TypeString)
	}
	if value, ok := pu.mutation.Amount(); ok {
		_spec.SetField(payment.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := pu.mutation.AddedAmount(); ok {
		_spec.AddField(payment.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := pu.mutation.Currency(); ok {
		_spec.SetField(payment.FieldCurrency, field.TypeString, value)
	}
	if value, ok := pu.mutation.Status(); ok {
		_spec.SetField(payment.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := pu.mutation.PaymentIntentID(); ok {
		_spec.SetField(payment.FieldPaymentIntentID, field.TypeString, value)
	}
	if pu.mutation.PaymentIntentIDCleared() {
		_spec.ClearField(payment.FieldPaymentIntentID, field.TypeString)
	}
	if value, ok := pu.mutation.ChargeID(); ok {
		_spec.SetField(payment.FieldChargeID, field.TypeString, value)
	}
	if pu.mutation.ChargeIDCleared() {
		_spec.ClearField(payment.FieldChargeID, field.TypeString)
	}
	if value, ok := pu.mutation.BillingDetails(); ok {
		_spec.SetField(payment.FieldBillingDetails, field.TypeJSON, value)
	}
	if pu.mutation.BillingDetailsCleared() {
		_spec.ClearField(payment.FieldBillingDetails, field.TypeJSON)
	}
	if value, ok := pu.mutation.PaymentMethodDetails(); ok {
		_spec.SetField(payment.FieldPaymentMethodDetails, field.TypeJSON, value)
	}
	if pu.mutation.PaymentMethodDetailsCleared() {
		_spec.ClearField(payment.FieldPaymentMethodDetails, field.TypeJSON)
	}
	if value, ok := pu.mutation.PaidAt(); ok {
		_spec.SetField(payment.FieldPaidAt, field.TypeTime, value)
	}
	if pu.mutation.PaidAtCleared() {
		_spec.ClearField(payment.FieldPaidAt, field.TypeTime)
	}
	if value, ok := pu.mutation.FailedAt(); ok {
		_spec.SetField(payment.FieldFailedAt, field.TypeTime, value)
	}
	if pu.mutation.FailedAtCleared() {
		_spec.ClearField(payment.FieldFailedAt, field.TypeTime)
	}
	if value, ok := pu.mutation.FailureCode(); ok {
		_spec.SetField(payment.FieldFailureCode, field.TypeString, value)
	}
	if pu.mutation.FailureCodeCleared() {
		_spec.ClearField(payment.FieldFailureCode, field.TypeString)
	}
	if value, ok := pu.mutation.FailureMessage(); ok {
		_spec.SetField(payment.FieldFailureMessage, field.TypeString, value)
	}
	if pu.mutation.FailureMessageCleared() {
		_spec.ClearField(payment.FieldFailureMessage, field.TypeString)
	}
	if value, ok := pu.mutation.RefundedAmount(); ok {
		_spec.SetField(payment.FieldRefundedAmount, field.TypeInt64, value)
	}
	if value, ok := pu.mutation.AddedRefundedAmount(); ok {
		_spec.AddField(payment.FieldRefundedAmount, field.TypeInt64, value)
	}
	if value, ok := pu.mutation.RefundedAt(); ok {
		_spec.SetField(payment.FieldRefundedAt, field.TypeTime, value)
	}
	if pu.mutation.RefundedAtCleared() {
		_spec.ClearField(payment.FieldRefundedAt, field.TypeTime)
	}
	if value, ok := pu.mutation.RefundReason(); ok {
		_spec.SetField(payment.FieldRefundReason, field.TypeString, value)
	}
	if pu.mutation.RefundReasonCleared() {
		_spec.ClearField(payment.FieldRefundReason, field.TypeString)
	}
	if value, ok := pu.mutation.WebhookData(); ok {
		_spec.SetField(payment.FieldWebhookData, field.TypeJSON, value)
	}
	if pu.mutation.WebhookDataCleared() {
		_spec.ClearField(payment.FieldWebhookData, field.TypeJSON)
	}
	if value, ok := pu.mutation.Metadata(); ok {
		_spec.SetField(payment.FieldMetadata, field.TypeJSON, value)
	}
	if pu.mutation.MetadataCleared() {
		_spec.ClearField(payment.FieldMetadata, field.TypeJSON)
	}
	if value, ok := pu.mutation.UpdatedAt(); ok {
		_spec.SetField(payment.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, pu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{payment.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pu.mutation.done = true
	return n, nil
}

// PaymentUpdateOne is the builder for updating a single Payment entity.
type PaymentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PaymentMutation
}

// SetUserID sets the "user_id" field.
func (puo *PaymentUpdateOne) SetUserID(u uuid.UUID) *PaymentUpdateOne {
	puo.mutation.SetUserID(u)
	return puo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableUserID(u *uuid.UUID) *PaymentUpdateOne {
	if u != nil {
		puo.SetUserID(*u)
	}
	return puo
}

// SetOrderID sets the "order_id" field.
func (puo *PaymentUpdateOne) SetOrderID(u uuid.UUID) *PaymentUpdateOne {
	puo.mutation.SetOrderID(u)
	return puo
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableOrderID(u *uuid.UUID) *PaymentUpdateOne {
	if u != nil {
		puo.SetOrderID(*u)
	}
	return puo
}

// ClearOrderID clears the value of the "order_id" field.
func (puo *PaymentUpdateOne) ClearOrderID() *PaymentUpdateOne {
	puo.mutation.ClearOrderID()
	return puo
}

// SetSubscriptionID sets the "subscription_id" field.
func (puo *PaymentUpdateOne) SetSubscriptionID(u uuid.UUID) *PaymentUpdateOne {
	puo.mutation.SetSubscriptionID(u)
	return puo
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableSubscriptionID(u *uuid.UUID) *PaymentUpdateOne {
	if u != nil {
		puo.SetSubscriptionID(*u)
	}
	return puo
}

// ClearSubscriptionID clears the value of the "subscription_id" field.
func (puo *PaymentUpdateOne) ClearSubscriptionID() *PaymentUpdateOne {
	puo.mutation.ClearSubscriptionID()
	return puo
}

// SetPaymentMethod sets the "payment_method" field.
func (puo *PaymentUpdateOne) SetPaymentMethod(s string) *PaymentUpdateOne {
	puo.mutation.SetPaymentMethod(s)
	return puo
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillablePaymentMethod(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetPaymentMethod(*s)
	}
	return puo
}

// SetPaymentProvider sets the "payment_provider" field.
func (puo *PaymentUpdateOne) SetPaymentProvider(s string) *PaymentUpdateOne {
	puo.mutation.SetPaymentProvider(s)
	return puo
}

// SetNillablePaymentProvider sets the "payment_provider" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillablePaymentProvider(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetPaymentProvider(*s)
	}
	return puo
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (puo *PaymentUpdateOne) SetExternalPaymentID(s string) *PaymentUpdateOne {
	puo.mutation.SetExternalPaymentID(s)
	return puo
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableExternalPaymentID(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetExternalPaymentID(*s)
	}
	return puo
}

// ClearExternalPaymentID clears the value of the "external_payment_id" field.
func (puo *PaymentUpdateOne) ClearExternalPaymentID() *PaymentUpdateOne {
	puo.mutation.ClearExternalPaymentID()
	return puo
}

// SetAmount sets the "amount" field.
func (puo *PaymentUpdateOne) SetAmount(i int64) *PaymentUpdateOne {
	puo.mutation.ResetAmount()
	puo.mutation.SetAmount(i)
	return puo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableAmount(i *int64) *PaymentUpdateOne {
	if i != nil {
		puo.SetAmount(*i)
	}
	return puo
}

// AddAmount adds i to the "amount" field.
func (puo *PaymentUpdateOne) AddAmount(i int64) *PaymentUpdateOne {
	puo.mutation.AddAmount(i)
	return puo
}

// SetCurrency sets the "currency" field.
func (puo *PaymentUpdateOne) SetCurrency(s string) *PaymentUpdateOne {
	puo.mutation.SetCurrency(s)
	return puo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableCurrency(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetCurrency(*s)
	}
	return puo
}

// SetStatus sets the "status" field.
func (puo *PaymentUpdateOne) SetStatus(pa payment.Status) *PaymentUpdateOne {
	puo.mutation.SetStatus(pa)
	return puo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableStatus(pa *payment.Status) *PaymentUpdateOne {
	if pa != nil {
		puo.SetStatus(*pa)
	}
	return puo
}

// SetPaymentIntentID sets the "payment_intent_id" field.
func (puo *PaymentUpdateOne) SetPaymentIntentID(s string) *PaymentUpdateOne {
	puo.mutation.SetPaymentIntentID(s)
	return puo
}

// SetNillablePaymentIntentID sets the "payment_intent_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillablePaymentIntentID(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetPaymentIntentID(*s)
	}
	return puo
}

// ClearPaymentIntentID clears the value of the "payment_intent_id" field.
func (puo *PaymentUpdateOne) ClearPaymentIntentID() *PaymentUpdateOne {
	puo.mutation.ClearPaymentIntentID()
	return puo
}

// SetChargeID sets the "charge_id" field.
func (puo *PaymentUpdateOne) SetChargeID(s string) *PaymentUpdateOne {
	puo.mutation.SetChargeID(s)
	return puo
}

// SetNillableChargeID sets the "charge_id" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableChargeID(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetChargeID(*s)
	}
	return puo
}

// ClearChargeID clears the value of the "charge_id" field.
func (puo *PaymentUpdateOne) ClearChargeID() *PaymentUpdateOne {
	puo.mutation.ClearChargeID()
	return puo
}

// SetBillingDetails sets the "billing_details" field.
func (puo *PaymentUpdateOne) SetBillingDetails(m map[string]interface{}) *PaymentUpdateOne {
	puo.mutation.SetBillingDetails(m)
	return puo
}

// ClearBillingDetails clears the value of the "billing_details" field.
func (puo *PaymentUpdateOne) ClearBillingDetails() *PaymentUpdateOne {
	puo.mutation.ClearBillingDetails()
	return puo
}

// SetPaymentMethodDetails sets the "payment_method_details" field.
func (puo *PaymentUpdateOne) SetPaymentMethodDetails(m map[string]interface{}) *PaymentUpdateOne {
	puo.mutation.SetPaymentMethodDetails(m)
	return puo
}

// ClearPaymentMethodDetails clears the value of the "payment_method_details" field.
func (puo *PaymentUpdateOne) ClearPaymentMethodDetails() *PaymentUpdateOne {
	puo.mutation.ClearPaymentMethodDetails()
	return puo
}

// SetPaidAt sets the "paid_at" field.
func (puo *PaymentUpdateOne) SetPaidAt(t time.Time) *PaymentUpdateOne {
	puo.mutation.SetPaidAt(t)
	return puo
}

// SetNillablePaidAt sets the "paid_at" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillablePaidAt(t *time.Time) *PaymentUpdateOne {
	if t != nil {
		puo.SetPaidAt(*t)
	}
	return puo
}

// ClearPaidAt clears the value of the "paid_at" field.
func (puo *PaymentUpdateOne) ClearPaidAt() *PaymentUpdateOne {
	puo.mutation.ClearPaidAt()
	return puo
}

// SetFailedAt sets the "failed_at" field.
func (puo *PaymentUpdateOne) SetFailedAt(t time.Time) *PaymentUpdateOne {
	puo.mutation.SetFailedAt(t)
	return puo
}

// SetNillableFailedAt sets the "failed_at" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableFailedAt(t *time.Time) *PaymentUpdateOne {
	if t != nil {
		puo.SetFailedAt(*t)
	}
	return puo
}

// ClearFailedAt clears the value of the "failed_at" field.
func (puo *PaymentUpdateOne) ClearFailedAt() *PaymentUpdateOne {
	puo.mutation.ClearFailedAt()
	return puo
}

// SetFailureCode sets the "failure_code" field.
func (puo *PaymentUpdateOne) SetFailureCode(s string) *PaymentUpdateOne {
	puo.mutation.SetFailureCode(s)
	return puo
}

// SetNillableFailureCode sets the "failure_code" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableFailureCode(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetFailureCode(*s)
	}
	return puo
}

// ClearFailureCode clears the value of the "failure_code" field.
func (puo *PaymentUpdateOne) ClearFailureCode() *PaymentUpdateOne {
	puo.mutation.ClearFailureCode()
	return puo
}

// SetFailureMessage sets the "failure_message" field.
func (puo *PaymentUpdateOne) SetFailureMessage(s string) *PaymentUpdateOne {
	puo.mutation.SetFailureMessage(s)
	return puo
}

// SetNillableFailureMessage sets the "failure_message" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableFailureMessage(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetFailureMessage(*s)
	}
	return puo
}

// ClearFailureMessage clears the value of the "failure_message" field.
func (puo *PaymentUpdateOne) ClearFailureMessage() *PaymentUpdateOne {
	puo.mutation.ClearFailureMessage()
	return puo
}

// SetRefundedAmount sets the "refunded_amount" field.
func (puo *PaymentUpdateOne) SetRefundedAmount(i int64) *PaymentUpdateOne {
	puo.mutation.ResetRefundedAmount()
	puo.mutation.SetRefundedAmount(i)
	return puo
}

// SetNillableRefundedAmount sets the "refunded_amount" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableRefundedAmount(i *int64) *PaymentUpdateOne {
	if i != nil {
		puo.SetRefundedAmount(*i)
	}
	return puo
}

// AddRefundedAmount adds i to the "refunded_amount" field.
func (puo *PaymentUpdateOne) AddRefundedAmount(i int64) *PaymentUpdateOne {
	puo.mutation.AddRefundedAmount(i)
	return puo
}

// SetRefundedAt sets the "refunded_at" field.
func (puo *PaymentUpdateOne) SetRefundedAt(t time.Time) *PaymentUpdateOne {
	puo.mutation.SetRefundedAt(t)
	return puo
}

// SetNillableRefundedAt sets the "refunded_at" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableRefundedAt(t *time.Time) *PaymentUpdateOne {
	if t != nil {
		puo.SetRefundedAt(*t)
	}
	return puo
}

// ClearRefundedAt clears the value of the "refunded_at" field.
func (puo *PaymentUpdateOne) ClearRefundedAt() *PaymentUpdateOne {
	puo.mutation.ClearRefundedAt()
	return puo
}

// SetRefundReason sets the "refund_reason" field.
func (puo *PaymentUpdateOne) SetRefundReason(s string) *PaymentUpdateOne {
	puo.mutation.SetRefundReason(s)
	return puo
}

// SetNillableRefundReason sets the "refund_reason" field if the given value is not nil.
func (puo *PaymentUpdateOne) SetNillableRefundReason(s *string) *PaymentUpdateOne {
	if s != nil {
		puo.SetRefundReason(*s)
	}
	return puo
}

// ClearRefundReason clears the value of the "refund_reason" field.
func (puo *PaymentUpdateOne) ClearRefundReason() *PaymentUpdateOne {
	puo.mutation.ClearRefundReason()
	return puo
}

// SetWebhookData sets the "webhook_data" field.
func (puo *PaymentUpdateOne) SetWebhookData(m map[string]interface{}) *PaymentUpdateOne {
	puo.mutation.SetWebhookData(m)
	return puo
}

// ClearWebhookData clears the value of the "webhook_data" field.
func (puo *PaymentUpdateOne) ClearWebhookData() *PaymentUpdateOne {
	puo.mutation.ClearWebhookData()
	return puo
}

// SetMetadata sets the "metadata" field.
func (puo *PaymentUpdateOne) SetMetadata(m map[string]interface{}) *PaymentUpdateOne {
	puo.mutation.SetMetadata(m)
	return puo
}

// ClearMetadata clears the value of the "metadata" field.
func (puo *PaymentUpdateOne) ClearMetadata() *PaymentUpdateOne {
	puo.mutation.ClearMetadata()
	return puo
}

// SetUpdatedAt sets the "updated_at" field.
func (puo *PaymentUpdateOne) SetUpdatedAt(t time.Time) *PaymentUpdateOne {
	puo.mutation.SetUpdatedAt(t)
	return puo
}

// Mutation returns the PaymentMutation object of the builder.
func (puo *PaymentUpdateOne) Mutation() *PaymentMutation {
	return puo.mutation
}

// Where appends a list predicates to the PaymentUpdate builder.
func (puo *PaymentUpdateOne) Where(ps ...predicate.Payment) *PaymentUpdateOne {
	puo.mutation.Where(ps...)
	return puo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (puo *PaymentUpdateOne) Select(field string, fields ...string) *PaymentUpdateOne {
	puo.fields = append([]string{field}, fields...)
	return puo
}

// Save executes the query and returns the updated Payment entity.
func (puo *PaymentUpdateOne) Save(ctx context.Context) (*Payment, error) {
	puo.defaults()
	return withHooks(ctx, puo.sqlSave, puo.mutation, puo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (puo *PaymentUpdateOne) SaveX(ctx context.Context) *Payment {
	node, err := puo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (puo *PaymentUpdateOne) Exec(ctx context.Context) error {
	_, err := puo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (puo *PaymentUpdateOne) ExecX(ctx context.Context) {
	if err := puo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (puo *PaymentUpdateOne) defaults() {
	if _, ok := puo.mutation.UpdatedAt(); !ok {
		v := payment.UpdateDefaultUpdatedAt()
		puo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (puo *PaymentUpdateOne) check() error {
	if v, ok := puo.mutation.PaymentMethod(); ok {
		if err := payment.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_method": %w`, err)}
		}
	}
	if v, ok := puo.mutation.PaymentProvider(); ok {
		if err := payment.PaymentProviderValidator(v); err != nil {
			return &ValidationError{Name: "payment_provider", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_provider": %w`, err)}
		}
	}
	if v, ok := puo.mutation.ExternalPaymentID(); ok {
		if err := payment.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "Payment.external_payment_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Amount(); ok {
		if err := payment.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Payment.amount": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Currency(); ok {
		if err := payment.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Payment.currency": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Status(); ok {
		if err := payment.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Payment.status": %w`, err)}
		}
	}
	if v, ok := puo.mutation.PaymentIntentID(); ok {
		if err := payment.PaymentIntentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_intent_id", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_intent_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.ChargeID(); ok {
		if err := payment.ChargeIDValidator(v); err != nil {
			return &ValidationError{Name: "charge_id", err: fmt.Errorf(`ent: validator failed for field "Payment.charge_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.FailureCode(); ok {
		if err := payment.FailureCodeValidator(v); err != nil {
			return &ValidationError{Name: "failure_code", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_code": %w`, err)}
		}
	}
	if v, ok := puo.mutation.FailureMessage(); ok {
		if err := payment.FailureMessageValidator(v); err != nil {
			return &ValidationError{Name: "failure_message", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_message": %w`, err)}
		}
	}
	if v, ok := puo.mutation.RefundedAmount(); ok {
		if err := payment.RefundedAmountValidator(v); err != nil {
			return &ValidationError{Name: "refunded_amount", err: fmt.Errorf(`ent: validator failed for field "Payment.refunded_amount": %w`, err)}
		}
	}
	if v, ok := puo.mutation.RefundReason(); ok {
		if err := payment.RefundReasonValidator(v); err != nil {
			return &ValidationError{Name: "refund_reason", err: fmt.Errorf(`ent: validator failed for field "Payment.refund_reason": %w`, err)}
		}
	}
	return nil
}

func (puo *PaymentUpdateOne) sqlSave(ctx context.Context) (_node *Payment, err error) {
	if err := puo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(payment.Table, payment.Columns, sqlgraph.NewFieldSpec(payment.FieldID, field.TypeUUID))
	id, ok := puo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Payment.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := puo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, payment.FieldID)
		for _, f := range fields {
			if !payment.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != payment.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := puo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := puo.mutation.UserID(); ok {
		_spec.SetField(payment.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := puo.mutation.OrderID(); ok {
		_spec.SetField(payment.FieldOrderID, field.TypeUUID, value)
	}
	if puo.mutation.OrderIDCleared() {
		_spec.ClearField(payment.FieldOrderID, field.TypeUUID)
	}
	if value, ok := puo.mutation.SubscriptionID(); ok {
		_spec.SetField(payment.FieldSubscriptionID, field.TypeUUID, value)
	}
	if puo.mutation.SubscriptionIDCleared() {
		_spec.ClearField(payment.FieldSubscriptionID, field.TypeUUID)
	}
	if value, ok := puo.mutation.PaymentMethod(); ok {
		_spec.SetField(payment.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := puo.mutation.PaymentProvider(); ok {
		_spec.SetField(payment.FieldPaymentProvider, field.TypeString, value)
	}
	if value, ok := puo.mutation.ExternalPaymentID(); ok {
		_spec.SetField(payment.FieldExternalPaymentID, field.TypeString, value)
	}
	if puo.mutation.ExternalPaymentIDCleared() {
		_spec.ClearField(payment.FieldExternalPaymentID, field.TypeString)
	}
	if value, ok := puo.mutation.Amount(); ok {
		_spec.SetField(payment.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := puo.mutation.AddedAmount(); ok {
		_spec.AddField(payment.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := puo.mutation.Currency(); ok {
		_spec.SetField(payment.FieldCurrency, field.TypeString, value)
	}
	if value, ok := puo.mutation.Status(); ok {
		_spec.SetField(payment.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := puo.mutation.PaymentIntentID(); ok {
		_spec.SetField(payment.FieldPaymentIntentID, field.TypeString, value)
	}
	if puo.mutation.PaymentIntentIDCleared() {
		_spec.ClearField(payment.FieldPaymentIntentID, field.TypeString)
	}
	if value, ok := puo.mutation.ChargeID(); ok {
		_spec.SetField(payment.FieldChargeID, field.TypeString, value)
	}
	if puo.mutation.ChargeIDCleared() {
		_spec.ClearField(payment.FieldChargeID, field.TypeString)
	}
	if value, ok := puo.mutation.BillingDetails(); ok {
		_spec.SetField(payment.FieldBillingDetails, field.TypeJSON, value)
	}
	if puo.mutation.BillingDetailsCleared() {
		_spec.ClearField(payment.FieldBillingDetails, field.TypeJSON)
	}
	if value, ok := puo.mutation.PaymentMethodDetails(); ok {
		_spec.SetField(payment.FieldPaymentMethodDetails, field.TypeJSON, value)
	}
	if puo.mutation.PaymentMethodDetailsCleared() {
		_spec.ClearField(payment.FieldPaymentMethodDetails, field.TypeJSON)
	}
	if value, ok := puo.mutation.PaidAt(); ok {
		_spec.SetField(payment.FieldPaidAt, field.TypeTime, value)
	}
	if puo.mutation.PaidAtCleared() {
		_spec.ClearField(payment.FieldPaidAt, field.TypeTime)
	}
	if value, ok := puo.mutation.FailedAt(); ok {
		_spec.SetField(payment.FieldFailedAt, field.TypeTime, value)
	}
	if puo.mutation.FailedAtCleared() {
		_spec.ClearField(payment.FieldFailedAt, field.TypeTime)
	}
	if value, ok := puo.mutation.FailureCode(); ok {
		_spec.SetField(payment.FieldFailureCode, field.TypeString, value)
	}
	if puo.mutation.FailureCodeCleared() {
		_spec.ClearField(payment.FieldFailureCode, field.TypeString)
	}
	if value, ok := puo.mutation.FailureMessage(); ok {
		_spec.SetField(payment.FieldFailureMessage, field.TypeString, value)
	}
	if puo.mutation.FailureMessageCleared() {
		_spec.ClearField(payment.FieldFailureMessage, field.TypeString)
	}
	if value, ok := puo.mutation.RefundedAmount(); ok {
		_spec.SetField(payment.FieldRefundedAmount, field.TypeInt64, value)
	}
	if value, ok := puo.mutation.AddedRefundedAmount(); ok {
		_spec.AddField(payment.FieldRefundedAmount, field.TypeInt64, value)
	}
	if value, ok := puo.mutation.RefundedAt(); ok {
		_spec.SetField(payment.FieldRefundedAt, field.TypeTime, value)
	}
	if puo.mutation.RefundedAtCleared() {
		_spec.ClearField(payment.FieldRefundedAt, field.TypeTime)
	}
	if value, ok := puo.mutation.RefundReason(); ok {
		_spec.SetField(payment.FieldRefundReason, field.TypeString, value)
	}
	if puo.mutation.RefundReasonCleared() {
		_spec.ClearField(payment.FieldRefundReason, field.TypeString)
	}
	if value, ok := puo.mutation.WebhookData(); ok {
		_spec.SetField(payment.FieldWebhookData, field.TypeJSON, value)
	}
	if puo.mutation.WebhookDataCleared() {
		_spec.ClearField(payment.FieldWebhookData, field.TypeJSON)
	}
	if value, ok := puo.mutation.Metadata(); ok {
		_spec.SetField(payment.FieldMetadata, field.TypeJSON, value)
	}
	if puo.mutation.MetadataCleared() {
		_spec.ClearField(payment.FieldMetadata, field.TypeJSON)
	}
	if value, ok := puo.mutation.UpdatedAt(); ok {
		_spec.SetField(payment.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &Payment{config: puo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, puo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{payment.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	puo.mutation.done = true
	return _node, nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
)

// SubscriptionPlanCreate is the builder for creating a SubscriptionPlan entity.
type SubscriptionPlanCreate struct {
	config
	mutation *SubscriptionPlanMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (spc *SubscriptionPlanCreate) SetName(s string) *SubscriptionPlanCreate {
	spc.mutation.SetName(s)
	return spc
}

// SetDisplayName sets the "display_name" field.
func (spc *SubscriptionPlanCreate) SetDisplayName(s string) *SubscriptionPlanCreate {
	spc.mutation.SetDisplayName(s)
	return spc
}

// SetDescription sets the "description" field.
func (spc *SubscriptionPlanCreate) SetDescription(s string) *SubscriptionPlanCreate {
	spc.mutation.SetDescription(s)
	return spc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableDescription(s *string) *SubscriptionPlanCreate {
	if s != nil {
		spc.SetDescription(*s)
	}
	return spc
}

// SetPrice sets the "price" field.
func (spc *SubscriptionPlanCreate) SetPrice(i int64) *SubscriptionPlanCreate {
	spc.mutation.SetPrice(i)
	return spc
}

// SetCurrency sets the "currency" field.
func (spc *SubscriptionPlanCreate) SetCurrency(s string) *SubscriptionPlanCreate {
	spc.mutation.SetCurrency(s)
	return spc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableCurrency(s *string) *SubscriptionPlanCreate {
	if s != nil {
		spc.SetCurrency(*s)
	}
	return spc
}

// SetBillingInterval sets the "billing_interval" field.
func (spc *SubscriptionPlanCreate) SetBillingInterval(si subscriptionplan.BillingInterval) *SubscriptionPlanCreate {
	spc.mutation.SetBillingInterval(si)
	return spc
}

// SetNillableBillingInterval sets the "billing_interval" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableBillingInterval(si *subscriptionplan.BillingInterval) *SubscriptionPlanCreate {
	if si != nil {
		spc.SetBillingInterval(*si)
	}
	return spc
}

// SetCreditsIncluded sets the "credits_included" field.
func (spc *SubscriptionPlanCreate) SetCreditsIncluded(i int) *SubscriptionPlanCreate {
	spc.mutation.SetCreditsIncluded(i)
	return spc
}

// SetNillableCreditsIncluded sets the "credits_included" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableCreditsIncluded(i *int) *SubscriptionPlanCreate {
	if i != nil {
		spc.SetCreditsIncluded(*i)
	}
	return spc
}

// SetMonthlyCredits sets the "monthly_credits" field.
func (spc *SubscriptionPlanCreate) SetMonthlyCredits(i int) *SubscriptionPlanCreate {
	spc.mutation.SetMonthlyCredits(i)
	return spc
}

// SetNillableMonthlyCredits sets the "monthly_credits" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableMonthlyCredits(i *int) *SubscriptionPlanCreate {
	if i != nil {
		spc.SetMonthlyCredits(*i)
	}
	return spc
}

// SetIsUnlimited sets the "is_unlimited" field.
func (spc *SubscriptionPlanCreate) SetIsUnlimited(b bool) *SubscriptionPlanCreate {
	spc.mutation.SetIsUnlimited(b)
	return spc
}

// SetNillableIsUnlimited sets the "is_unlimited" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableIsUnlimited(b *bool) *SubscriptionPlanCreate {
	if b != nil {
		spc.SetIsUnlimited(*b)
	}
	return spc
}

// SetFeatures sets the "features" field.
func (spc *SubscriptionPlanCreate) SetFeatures(s []string) *SubscriptionPlanCreate {
	spc.mutation.SetFeatures(s)
	return spc
}

// SetLimits sets the "limits" field.
func (spc *SubscriptionPlanCreate) SetLimits(m map[string]interface{}) *SubscriptionPlanCreate {
	spc.mutation.SetLimits(m)
	return spc
}

// SetMaxUsers sets the "max_users" field.
func (spc *SubscriptionPlanCreate) SetMaxUsers(i int) *SubscriptionPlanCreate {
	spc.mutation.SetMaxUsers(i)
	return spc
}

// SetNillableMaxUsers sets the "max_users" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableMaxUsers(i *int) *SubscriptionPlanCreate {
	if i != nil {
		spc.SetMaxUsers(*i)
	}
	return spc
}

// SetMaxWorkspaces sets the "max_workspaces" field.
func (spc *SubscriptionPlanCreate) SetMaxWorkspaces(i int) *SubscriptionPlanCreate {
	spc.mutation.SetMaxWorkspaces(i)
	return spc
}

// SetNillableMaxWorkspaces sets the "max_workspaces" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableMaxWorkspaces(i *int) *SubscriptionPlanCreate {
	if i != nil {
		spc.SetMaxWorkspaces(*i)
	}
	return spc
}

// SetIsActive sets the "is_active" field.
func (spc *SubscriptionPlanCreate) SetIsActive(b bool) *SubscriptionPlanCreate {
	spc.mutation.SetIsActive(b)
	return spc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableIsActive(b *bool) *SubscriptionPlanCreate {
	if b != nil {
		spc.SetIsActive(*b)
	}
	return spc
}

// SetIsFeatured sets the "is_featured" field.
func (spc *SubscriptionPlanCreate) SetIsFeatured(b bool) *SubscriptionPlanCreate {
	spc.mutation.SetIsFeatured(b)
	return spc
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableIsFeatured(b *bool) *SubscriptionPlanCreate {
	if b != nil {
		spc.SetIsFeatured(*b)
	}
	return spc
}

// SetSortOrder sets the "sort_order" field.
func (spc *SubscriptionPlanCreate) SetSortOrder(i int) *SubscriptionPlanCreate {
	spc.mutation.SetSortOrder(i)
	return spc
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableSortOrder(i *int) *SubscriptionPlanCreate {
	if i != nil {
		spc.SetSortOrder(*i)
	}
	return spc
}

// SetStripePriceID sets the "stripe_price_id" field.
func (spc *SubscriptionPlanCreate) SetStripePriceID(s string) *SubscriptionPlanCreate {
	spc.mutation.SetStripePriceID(s)
	return spc
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableStripePriceID(s *string) *SubscriptionPlanCreate {
	if s != nil {
		spc.SetStripePriceID(*s)
	}
	return spc
}

// SetStripeProductID sets the "stripe_product_id" field.
func (spc *SubscriptionPlanCreate) SetStripeProductID(s string) *SubscriptionPlanCreate {
	spc.mutation.SetStripeProductID(s)
	return spc
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableStripeProductID(s *string) *SubscriptionPlanCreate {
	if s != nil {
		spc.SetStripeProductID(*s)
	}
	return spc
}

// SetMetadata sets the "metadata" field.
func (spc *SubscriptionPlanCreate) SetMetadata(m map[string]interface{}) *SubscriptionPlanCreate {
	spc.mutation.SetMetadata(m)
	return spc
}

// SetCreatedAt sets the "created_at" field.
func (spc *SubscriptionPlanCreate) SetCreatedAt(t time.Time) *SubscriptionPlanCreate {
	spc.mutation.SetCreatedAt(t)
	return spc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableCreatedAt(t *time.Time) *SubscriptionPlanCreate {
	if t != nil {
		spc.SetCreatedAt(*t)
	}
	return spc
}

// SetUpdatedAt sets the "updated_at" field.
func (spc *SubscriptionPlanCreate) SetUpdatedAt(t time.Time) *SubscriptionPlanCreate {
	spc.mutation.SetUpdatedAt(t)
	return spc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableUpdatedAt(t *time.Time) *SubscriptionPlanCreate {
	if t != nil {
		spc.SetUpdatedAt(*t)
	}
	return spc
}

// SetID sets the "id" field.
func (spc *SubscriptionPlanCreate) SetID(u uuid.UUID) *SubscriptionPlanCreate {
	spc.mutation.SetID(u)
	return spc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (spc *SubscriptionPlanCreate) SetNillableID(u *uuid.UUID) *SubscriptionPlanCreate {
	if u != nil {
		spc.SetID(*u)
	}
	return spc
}

// Mutation returns the SubscriptionPlanMutation object of the builder.
func (spc *SubscriptionPlanCreate) Mutation() *SubscriptionPlanMutation {
	return spc.mutation
}

// Save creates the SubscriptionPlan in the database.
func (spc *SubscriptionPlanCreate) Save(ctx context.Context) (*SubscriptionPlan, error) {
	spc.defaults()
	return withHooks(ctx, spc.sqlSave, spc.mutation, spc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (spc *SubscriptionPlanCreate) SaveX(ctx context.Context) *SubscriptionPlan {
	v, err := spc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (spc *SubscriptionPlanCreate) Exec(ctx context.Context) error {
	_, err := spc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (spc *SubscriptionPlanCreate) ExecX(ctx context.Context) {
	if err := spc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (spc *SubscriptionPlanCreate) defaults() {
	if _, ok := spc.mutation.Currency(); !ok {
		v := subscriptionplan.DefaultCurrency
		spc.mutation.SetCurrency(v)
	}
	if _, ok := spc.mutation.BillingInterval(); !ok {
		v := subscriptionplan.DefaultBillingInterval
		spc.mutation.SetBillingInterval(v)
	}
	if _, ok := spc.mutation.CreditsIncluded(); !ok {
		v := subscriptionplan.DefaultCreditsIncluded
		spc.mutation.SetCreditsIncluded(v)
	}
	if _, ok := spc.mutation.MonthlyCredits(); !ok {
		v := subscriptionplan.DefaultMonthlyCredits
		spc.mutation.SetMonthlyCredits(v)
	}
	if _, ok := spc.mutation.IsUnlimited(); !ok {
		v := subscriptionplan.DefaultIsUnlimited
		spc.mutation.SetIsUnlimited(v)
	}
	if _, ok := spc.mutation.MaxUsers(); !ok {
		v := subscriptionplan.DefaultMaxUsers
		spc.mutation.SetMaxUsers(v)
	}
	if _, ok := spc.mutation.MaxWorkspaces(); !ok {
		v := subscriptionplan.DefaultMaxWorkspaces
		spc.mutation.SetMaxWorkspaces(v)
	}
	if _, ok := spc.mutation.IsActive(); !ok {
		v := subscriptionplan.DefaultIsActive
		spc.mutation.SetIsActive(v)
	}
	if _, ok := spc.mutation.IsFeatured(); !ok {
		v := subscriptionplan.DefaultIsFeatured
		spc.mutation.SetIsFeatured(v)
	}
	if _, ok := spc.mutation.SortOrder(); !ok {
		v := subscriptionplan.DefaultSortOrder
		spc.mutation.SetSortOrder(v)
	}
	if _, ok := spc.mutation.CreatedAt(); !ok {
		v := subscriptionplan.DefaultCreatedAt()
		spc.mutation.SetCreatedAt(v)
	}
	if _, ok := spc.mutation.UpdatedAt(); !ok {
		v := subscriptionplan.DefaultUpdatedAt()
		spc.mutation.SetUpdatedAt(v)
	}
	if _, ok := spc.mutation.ID(); !ok {
		v := subscriptionplan.DefaultID()
		spc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (spc *SubscriptionPlanCreate) check() error {
	if _, ok := spc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "SubscriptionPlan.name"`)}
	}
	if v, ok := spc.mutation.Name(); ok {
		if err := subscriptionplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.name": %w`, err)}
		}
	}
	if _, ok := spc.mutation.DisplayName(); !ok {
		return &ValidationError{Name: "display_name", err: errors.New(`ent: missing required field "SubscriptionPlan.display_name"`)}
	}
	if v, ok := spc.mutation.DisplayName(); ok {
		if err := subscriptionplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.display_name": %w`, err)}
		}
	}
	if _, ok := spc.mutation.Price(); !ok {
		return &ValidationError{Name: "price", err: errors.New(`ent: missing required field "SubscriptionPlan.price"`)}
	}
	if v, ok := spc.mutation.Price(); ok {
		if err := subscriptionplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.price": %w`, err)}
		}
	}
	if _, ok := spc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "SubscriptionPlan.currency"`)}
	}
	if v, ok := spc.mutation.Currency(); ok {
		if err := subscriptionplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.currency": %w`, err)}
		}
	}
	if _, ok := spc.mutation.BillingInterval(); !ok {
		return &ValidationError{Name: "billing_interval", err: errors.New(`ent: missing required field "SubscriptionPlan.billing_interval"`)}
	}
	if v, ok := spc.mutation.BillingInterval(); ok {
		if err := subscriptionplan.BillingIntervalValidator(v); err != nil {
			return &ValidationError{Name: "billing_interval", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.billing_interval": %w`, err)}
		}
	}
	if _, ok := spc.mutation.CreditsIncluded(); !ok {
		return &ValidationError{Name: "credits_included", err: errors.New(`ent: missing required field "SubscriptionPlan.credits_included"`)}
	}
	if v, ok := spc.mutation.CreditsIncluded(); ok {
		if err := subscriptionplan.CreditsIncludedValidator(v); err != nil {
			return &ValidationError{Name: "credits_included", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.credits_included": %w`, err)}
		}
	}
	if _, ok := spc.mutation.MonthlyCredits(); !ok {
		return &ValidationError{Name: "monthly_credits", err: errors.New(`ent: missing required field "SubscriptionPlan.monthly_credits"`)}
	}
	if v, ok := spc.mutation.MonthlyCredits(); ok {
		if err := subscriptionplan.MonthlyCreditsValidator(v); err != nil {
			return &ValidationError{Name: "monthly_credits", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.monthly_credits": %w`, err)}
		}
	}
	if _, ok := spc.mutation.IsUnlimited(); !ok {
		return &ValidationError{Name: "is_unlimited", err: errors.New(`ent: missing required field "SubscriptionPlan.is_unlimited"`)}
	}
	if _, ok := spc.mutation.MaxUsers(); !ok {
		return &ValidationError{Name: "max_users", err: errors.New(`ent: missing required field "SubscriptionPlan.max_users"`)}
	}
	if v, ok := spc.mutation.MaxUsers(); ok {
		if err := subscriptionplan.MaxUsersValidator(v); err != nil {
			return &ValidationError{Name: "max_users", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_users": %w`, err)}
		}
	}
	if _, ok := spc.mutation.MaxWorkspaces(); !ok {
		return &ValidationError{Name: "max_workspaces", err: errors.New(`ent: missing required field "SubscriptionPlan.max_workspaces"`)}
	}
	if v, ok := spc.mutation.MaxWorkspaces(); ok {
		if err := subscriptionplan.MaxWorkspacesValidator(v); err != nil {
			return &ValidationError{Name: "max_workspaces", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_workspaces": %w`, err)}
		}
	}
	if _, ok := spc.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "SubscriptionPlan.is_active"`)}
	}
	if _, ok := spc.mutation.IsFeatured(); !ok {
		return &ValidationError{Name: "is_featured", err: errors.New(`ent: missing required field "SubscriptionPlan.is_featured"`)}
	}
	if _, ok := spc.mutation.SortOrder(); !ok {
		return &ValidationError{Name: "sort_order", err: errors.New(`ent: missing required field "SubscriptionPlan.sort_order"`)}
	}
	if v, ok := spc.mutation.StripePriceID(); ok {
		if err := subscriptionplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := spc.mutation.StripeProductID(); ok {
		if err := subscriptionplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_product_id": %w`, err)}
		}
	}
	if _, ok := spc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SubscriptionPlan.created_at"`)}
	}
	if _, ok := spc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SubscriptionPlan.updated_at"`)}
	}
	return nil
}

func (spc *SubscriptionPlanCreate) sqlSave(ctx context.Context) (*SubscriptionPlan, error) {
	if err := spc.check(); err != nil {
		return nil, err
	}
	_node, _spec := spc.createSpec()
	if err := sqlgraph.CreateNode(ctx, spc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	spc.mutation.id = &_node.ID
	spc.mutation.done = true
	return _node, nil
}

func (spc *SubscriptionPlanCreate) createSpec() (*SubscriptionPlan, *sqlgraph.CreateSpec) {
	var (
		_node = &SubscriptionPlan{config: spc.config}
		_spec = sqlgraph.NewCreateSpec(subscriptionplan.Table, sqlgraph.NewFieldSpec(subscriptionplan.FieldID, field.TypeUUID))
	)
	if id, ok := spc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := spc.mutation.Name(); ok {
		_spec.SetField(subscriptionplan.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := spc.mutation.DisplayName(); ok {
		_spec.SetField(subscriptionplan.FieldDisplayName, field.TypeString, value)
		_node.DisplayName = value
	}
	if value, ok := spc.mutation.Description(); ok {
		_spec.SetField(subscriptionplan.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := spc.mutation.Price(); ok {
		_spec.SetField(subscriptionplan.FieldPrice, field.TypeInt64, value)
		_node.Price = value
	}
	if value, ok := spc.mutation.Currency(); ok {
		_spec.SetField(subscriptionplan.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := spc.mutation.BillingInterval(); ok {
		_spec.SetField(subscriptionplan.FieldBillingInterval, field.TypeEnum, value)
		_node.BillingInterval = value
	}
	if value, ok := spc.mutation.CreditsIncluded(); ok {
		_spec.SetField(subscriptionplan.FieldCreditsIncluded, field.TypeInt, value)
		_node.CreditsIncluded = value
	}
	if value, ok := spc.mutation.MonthlyCredits(); ok {
		_spec.SetField(subscriptionplan.FieldMonthlyCredits, field.TypeInt, value)
		_node.MonthlyCredits = value
	}
	if value, ok := spc.mutation.IsUnlimited(); ok {
		_spec.SetField(subscriptionplan.FieldIsUnlimited, field.TypeBool, value)
		_node.IsUnlimited = value
	}
	if value, ok := spc.mutation.Features(); ok {
		_spec.SetField(subscriptionplan.FieldFeatures, field.TypeJSON, value)
		_node.Features = value
	}
	if value, ok := spc.mutation.Limits(); ok {
		_spec.SetField(subscriptionplan.FieldLimits, field.TypeJSON, value)
		_node.Limits = value
	}
	if value, ok := spc.mutation.MaxUsers(); ok {
		_spec.SetField(subscriptionplan.FieldMaxUsers, field.TypeInt, value)
		_node.MaxUsers = value
	}
	if value, ok := spc.mutation.MaxWorkspaces(); ok {
		_spec.SetField(subscriptionplan.FieldMaxWorkspaces, field.TypeInt, value)
		_node.MaxWorkspaces = value
	}
	if value, ok := spc.mutation.IsActive(); ok {
		_spec.SetField(subscriptionplan.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := spc.mutation.IsFeatured(); ok {
		_spec.SetField(subscriptionplan.FieldIsFeatured, field.TypeBool, value)
		_node.IsFeatured = value
	}
	if value, ok := spc.mutation.SortOrder(); ok {
		_spec.SetField(subscriptionplan.FieldSortOrder, field.TypeInt, value)
		_node.SortOrder = value
	}
	if value, ok := spc.mutation.StripePriceID(); ok {
		_spec.SetField(subscriptionplan.FieldStripePriceID, field.TypeString, value)
		_node.StripePriceID = value
	}
	if value, ok := spc.mutation.StripeProductID(); ok {
		_spec.SetField(subscriptionplan.FieldStripeProductID, field.TypeString, value)
		_node.StripeProductID = value
	}
	if value, ok := spc.mutation.Metadata(); ok {
		_spec.SetField(subscriptionplan.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := spc.mutation.CreatedAt(); ok {
		_spec.SetField(subscriptionplan.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := spc.mutation.UpdatedAt(); ok {
		_spec.SetField(subscriptionplan.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// SubscriptionPlanCreateBulk is the builder for creating many SubscriptionPlan entities in bulk.
type SubscriptionPlanCreateBulk struct {
	config
	err      error
	builders []*SubscriptionPlanCreate
}

// Save creates the SubscriptionPlan entities in the database.
func (spcb *SubscriptionPlanCreateBulk) Save(ctx context.Context) ([]*SubscriptionPlan, error) {
	if spcb.err != nil {
		return nil, spcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(spcb.builders))
	nodes := make([]*SubscriptionPlan, len(spcb.builders))
	mutators := make([]Mutator, len(spcb.builders))
	for i := range spcb.builders {
		func(i int, root context.Context) {
			builder := spcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SubscriptionPlanMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, spcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, spcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, spcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (spcb *SubscriptionPlanCreateBulk) SaveX(ctx context.Context) []*SubscriptionPlan {
	v, err := spcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (spcb *SubscriptionPlanCreateBulk) Exec(ctx context.Context) error {
	_, err := spcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (spcb *SubscriptionPlanCreateBulk) ExecX(ctx context.Context) {
	if err := spcb.Exec(ctx); err != nil {
		panic(err)
	}
}

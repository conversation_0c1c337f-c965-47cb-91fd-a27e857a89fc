// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
)

// BankTransferCreate is the builder for creating a BankTransfer entity.
type BankTransferCreate struct {
	config
	mutation *BankTransferMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (btc *BankTransferCreate) SetUserID(u uuid.UUID) *BankTransferCreate {
	btc.mutation.SetUserID(u)
	return btc
}

// SetPlanID sets the "plan_id" field.
func (btc *BankTransferCreate) SetPlanID(u uuid.UUID) *BankTransferCreate {
	btc.mutation.SetPlanID(u)
	return btc
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillablePlanID(u *uuid.UUID) *BankTransferCreate {
	if u != nil {
		btc.SetPlanID(*u)
	}
	return btc
}

// SetAmount sets the "amount" field.
func (btc *BankTransferCreate) SetAmount(i int64) *BankTransferCreate {
	btc.mutation.SetAmount(i)
	return btc
}

// SetCurrency sets the "currency" field.
func (btc *BankTransferCreate) SetCurrency(s string) *BankTransferCreate {
	btc.mutation.SetCurrency(s)
	return btc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableCurrency(s *string) *BankTransferCreate {
	if s != nil {
		btc.SetCurrency(*s)
	}
	return btc
}

// SetStatus sets the "status" field.
func (btc *BankTransferCreate) SetStatus(b banktransfer.Status) *BankTransferCreate {
	btc.mutation.SetStatus(b)
	return btc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableStatus(b *banktransfer.Status) *BankTransferCreate {
	if b != nil {
		btc.SetStatus(*b)
	}
	return btc
}

// SetBankAccount sets the "bank_account" field.
func (btc *BankTransferCreate) SetBankAccount(s string) *BankTransferCreate {
	btc.mutation.SetBankAccount(s)
	return btc
}

// SetReferenceCode sets the "reference_code" field.
func (btc *BankTransferCreate) SetReferenceCode(s string) *BankTransferCreate {
	btc.mutation.SetReferenceCode(s)
	return btc
}

// SetTransferInstructions sets the "transfer_instructions" field.
func (btc *BankTransferCreate) SetTransferInstructions(s string) *BankTransferCreate {
	btc.mutation.SetTransferInstructions(s)
	return btc
}

// SetConfirmedAt sets the "confirmed_at" field.
func (btc *BankTransferCreate) SetConfirmedAt(t time.Time) *BankTransferCreate {
	btc.mutation.SetConfirmedAt(t)
	return btc
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableConfirmedAt(t *time.Time) *BankTransferCreate {
	if t != nil {
		btc.SetConfirmedAt(*t)
	}
	return btc
}

// SetExpiresAt sets the "expires_at" field.
func (btc *BankTransferCreate) SetExpiresAt(t time.Time) *BankTransferCreate {
	btc.mutation.SetExpiresAt(t)
	return btc
}

// SetCreditsToAdd sets the "credits_to_add" field.
func (btc *BankTransferCreate) SetCreditsToAdd(i int) *BankTransferCreate {
	btc.mutation.SetCreditsToAdd(i)
	return btc
}

// SetNillableCreditsToAdd sets the "credits_to_add" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableCreditsToAdd(i *int) *BankTransferCreate {
	if i != nil {
		btc.SetCreditsToAdd(*i)
	}
	return btc
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (btc *BankTransferCreate) SetBankTransactionID(s string) *BankTransferCreate {
	btc.mutation.SetBankTransactionID(s)
	return btc
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableBankTransactionID(s *string) *BankTransferCreate {
	if s != nil {
		btc.SetBankTransactionID(*s)
	}
	return btc
}

// SetActualAmountReceived sets the "actual_amount_received" field.
func (btc *BankTransferCreate) SetActualAmountReceived(i int64) *BankTransferCreate {
	btc.mutation.SetActualAmountReceived(i)
	return btc
}

// SetNillableActualAmountReceived sets the "actual_amount_received" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableActualAmountReceived(i *int64) *BankTransferCreate {
	if i != nil {
		btc.SetActualAmountReceived(*i)
	}
	return btc
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (btc *BankTransferCreate) SetConfirmationMethod(s string) *BankTransferCreate {
	btc.mutation.SetConfirmationMethod(s)
	return btc
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableConfirmationMethod(s *string) *BankTransferCreate {
	if s != nil {
		btc.SetConfirmationMethod(*s)
	}
	return btc
}

// SetConfirmedBy sets the "confirmed_by" field.
func (btc *BankTransferCreate) SetConfirmedBy(u uuid.UUID) *BankTransferCreate {
	btc.mutation.SetConfirmedBy(u)
	return btc
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableConfirmedBy(u *uuid.UUID) *BankTransferCreate {
	if u != nil {
		btc.SetConfirmedBy(*u)
	}
	return btc
}

// SetMetadata sets the "metadata" field.
func (btc *BankTransferCreate) SetMetadata(m map[string]interface{}) *BankTransferCreate {
	btc.mutation.SetMetadata(m)
	return btc
}

// SetCreatedAt sets the "created_at" field.
func (btc *BankTransferCreate) SetCreatedAt(t time.Time) *BankTransferCreate {
	btc.mutation.SetCreatedAt(t)
	return btc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableCreatedAt(t *time.Time) *BankTransferCreate {
	if t != nil {
		btc.SetCreatedAt(*t)
	}
	return btc
}

// SetUpdatedAt sets the "updated_at" field.
func (btc *BankTransferCreate) SetUpdatedAt(t time.Time) *BankTransferCreate {
	btc.mutation.SetUpdatedAt(t)
	return btc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableUpdatedAt(t *time.Time) *BankTransferCreate {
	if t != nil {
		btc.SetUpdatedAt(*t)
	}
	return btc
}

// SetID sets the "id" field.
func (btc *BankTransferCreate) SetID(u uuid.UUID) *BankTransferCreate {
	btc.mutation.SetID(u)
	return btc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (btc *BankTransferCreate) SetNillableID(u *uuid.UUID) *BankTransferCreate {
	if u != nil {
		btc.SetID(*u)
	}
	return btc
}

// Mutation returns the BankTransferMutation object of the builder.
func (btc *BankTransferCreate) Mutation() *BankTransferMutation {
	return btc.mutation
}

// Save creates the BankTransfer in the database.
func (btc *BankTransferCreate) Save(ctx context.Context) (*BankTransfer, error) {
	btc.defaults()
	return withHooks(ctx, btc.sqlSave, btc.mutation, btc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (btc *BankTransferCreate) SaveX(ctx context.Context) *BankTransfer {
	v, err := btc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (btc *BankTransferCreate) Exec(ctx context.Context) error {
	_, err := btc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (btc *BankTransferCreate) ExecX(ctx context.Context) {
	if err := btc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (btc *BankTransferCreate) defaults() {
	if _, ok := btc.mutation.Currency(); !ok {
		v := banktransfer.DefaultCurrency
		btc.mutation.SetCurrency(v)
	}
	if _, ok := btc.mutation.Status(); !ok {
		v := banktransfer.DefaultStatus
		btc.mutation.SetStatus(v)
	}
	if _, ok := btc.mutation.CreditsToAdd(); !ok {
		v := banktransfer.DefaultCreditsToAdd
		btc.mutation.SetCreditsToAdd(v)
	}
	if _, ok := btc.mutation.ConfirmationMethod(); !ok {
		v := banktransfer.DefaultConfirmationMethod
		btc.mutation.SetConfirmationMethod(v)
	}
	if _, ok := btc.mutation.CreatedAt(); !ok {
		v := banktransfer.DefaultCreatedAt()
		btc.mutation.SetCreatedAt(v)
	}
	if _, ok := btc.mutation.UpdatedAt(); !ok {
		v := banktransfer.DefaultUpdatedAt()
		btc.mutation.SetUpdatedAt(v)
	}
	if _, ok := btc.mutation.ID(); !ok {
		v := banktransfer.DefaultID()
		btc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (btc *BankTransferCreate) check() error {
	if _, ok := btc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "BankTransfer.user_id"`)}
	}
	if _, ok := btc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "BankTransfer.amount"`)}
	}
	if v, ok := btc.mutation.Amount(); ok {
		if err := banktransfer.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.amount": %w`, err)}
		}
	}
	if _, ok := btc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "BankTransfer.currency"`)}
	}
	if v, ok := btc.mutation.Currency(); ok {
		if err := banktransfer.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.currency": %w`, err)}
		}
	}
	if _, ok := btc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "BankTransfer.status"`)}
	}
	if v, ok := btc.mutation.Status(); ok {
		if err := banktransfer.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.status": %w`, err)}
		}
	}
	if _, ok := btc.mutation.BankAccount(); !ok {
		return &ValidationError{Name: "bank_account", err: errors.New(`ent: missing required field "BankTransfer.bank_account"`)}
	}
	if v, ok := btc.mutation.BankAccount(); ok {
		if err := banktransfer.BankAccountValidator(v); err != nil {
			return &ValidationError{Name: "bank_account", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_account": %w`, err)}
		}
	}
	if _, ok := btc.mutation.ReferenceCode(); !ok {
		return &ValidationError{Name: "reference_code", err: errors.New(`ent: missing required field "BankTransfer.reference_code"`)}
	}
	if v, ok := btc.mutation.ReferenceCode(); ok {
		if err := banktransfer.ReferenceCodeValidator(v); err != nil {
			return &ValidationError{Name: "reference_code", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.reference_code": %w`, err)}
		}
	}
	if _, ok := btc.mutation.TransferInstructions(); !ok {
		return &ValidationError{Name: "transfer_instructions", err: errors.New(`ent: missing required field "BankTransfer.transfer_instructions"`)}
	}
	if _, ok := btc.mutation.ExpiresAt(); !ok {
		return &ValidationError{Name: "expires_at", err: errors.New(`ent: missing required field "BankTransfer.expires_at"`)}
	}
	if _, ok := btc.mutation.CreditsToAdd(); !ok {
		return &ValidationError{Name: "credits_to_add", err: errors.New(`ent: missing required field "BankTransfer.credits_to_add"`)}
	}
	if v, ok := btc.mutation.CreditsToAdd(); ok {
		if err := banktransfer.CreditsToAddValidator(v); err != nil {
			return &ValidationError{Name: "credits_to_add", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.credits_to_add": %w`, err)}
		}
	}
	if v, ok := btc.mutation.BankTransactionID(); ok {
		if err := banktransfer.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_transaction_id": %w`, err)}
		}
	}
	if v, ok := btc.mutation.ActualAmountReceived(); ok {
		if err := banktransfer.ActualAmountReceivedValidator(v); err != nil {
			return &ValidationError{Name: "actual_amount_received", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.actual_amount_received": %w`, err)}
		}
	}
	if _, ok := btc.mutation.ConfirmationMethod(); !ok {
		return &ValidationError{Name: "confirmation_method", err: errors.New(`ent: missing required field "BankTransfer.confirmation_method"`)}
	}
	if v, ok := btc.mutation.ConfirmationMethod(); ok {
		if err := banktransfer.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.confirmation_method": %w`, err)}
		}
	}
	if _, ok := btc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "BankTransfer.created_at"`)}
	}
	if _, ok := btc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "BankTransfer.updated_at"`)}
	}
	return nil
}

func (btc *BankTransferCreate) sqlSave(ctx context.Context) (*BankTransfer, error) {
	if err := btc.check(); err != nil {
		return nil, err
	}
	_node, _spec := btc.createSpec()
	if err := sqlgraph.CreateNode(ctx, btc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	btc.mutation.id = &_node.ID
	btc.mutation.done = true
	return _node, nil
}

func (btc *BankTransferCreate) createSpec() (*BankTransfer, *sqlgraph.CreateSpec) {
	var (
		_node = &BankTransfer{config: btc.config}
		_spec = sqlgraph.NewCreateSpec(banktransfer.Table, sqlgraph.NewFieldSpec(banktransfer.FieldID, field.TypeUUID))
	)
	if id, ok := btc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := btc.mutation.UserID(); ok {
		_spec.SetField(banktransfer.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := btc.mutation.PlanID(); ok {
		_spec.SetField(banktransfer.FieldPlanID, field.TypeUUID, value)
		_node.PlanID = value
	}
	if value, ok := btc.mutation.Amount(); ok {
		_spec.SetField(banktransfer.FieldAmount, field.TypeInt64, value)
		_node.Amount = value
	}
	if value, ok := btc.mutation.Currency(); ok {
		_spec.SetField(banktransfer.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := btc.mutation.Status(); ok {
		_spec.SetField(banktransfer.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := btc.mutation.BankAccount(); ok {
		_spec.SetField(banktransfer.FieldBankAccount, field.TypeString, value)
		_node.BankAccount = value
	}
	if value, ok := btc.mutation.ReferenceCode(); ok {
		_spec.SetField(banktransfer.FieldReferenceCode, field.TypeString, value)
		_node.ReferenceCode = value
	}
	if value, ok := btc.mutation.TransferInstructions(); ok {
		_spec.SetField(banktransfer.FieldTransferInstructions, field.TypeString, value)
		_node.TransferInstructions = value
	}
	if value, ok := btc.mutation.ConfirmedAt(); ok {
		_spec.SetField(banktransfer.FieldConfirmedAt, field.TypeTime, value)
		_node.ConfirmedAt = value
	}
	if value, ok := btc.mutation.ExpiresAt(); ok {
		_spec.SetField(banktransfer.FieldExpiresAt, field.TypeTime, value)
		_node.ExpiresAt = value
	}
	if value, ok := btc.mutation.CreditsToAdd(); ok {
		_spec.SetField(banktransfer.FieldCreditsToAdd, field.TypeInt, value)
		_node.CreditsToAdd = value
	}
	if value, ok := btc.mutation.BankTransactionID(); ok {
		_spec.SetField(banktransfer.FieldBankTransactionID, field.TypeString, value)
		_node.BankTransactionID = value
	}
	if value, ok := btc.mutation.ActualAmountReceived(); ok {
		_spec.SetField(banktransfer.FieldActualAmountReceived, field.TypeInt64, value)
		_node.ActualAmountReceived = value
	}
	if value, ok := btc.mutation.ConfirmationMethod(); ok {
		_spec.SetField(banktransfer.FieldConfirmationMethod, field.TypeString, value)
		_node.ConfirmationMethod = value
	}
	if value, ok := btc.mutation.ConfirmedBy(); ok {
		_spec.SetField(banktransfer.FieldConfirmedBy, field.TypeUUID, value)
		_node.ConfirmedBy = value
	}
	if value, ok := btc.mutation.Metadata(); ok {
		_spec.SetField(banktransfer.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := btc.mutation.CreatedAt(); ok {
		_spec.SetField(banktransfer.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := btc.mutation.UpdatedAt(); ok {
		_spec.SetField(banktransfer.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// BankTransferCreateBulk is the builder for creating many BankTransfer entities in bulk.
type BankTransferCreateBulk struct {
	config
	err      error
	builders []*BankTransferCreate
}

// Save creates the BankTransfer entities in the database.
func (btcb *BankTransferCreateBulk) Save(ctx context.Context) ([]*BankTransfer, error) {
	if btcb.err != nil {
		return nil, btcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(btcb.builders))
	nodes := make([]*BankTransfer, len(btcb.builders))
	mutators := make([]Mutator, len(btcb.builders))
	for i := range btcb.builders {
		func(i int, root context.Context) {
			builder := btcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*BankTransferMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, btcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, btcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, btcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (btcb *BankTransferCreateBulk) SaveX(ctx context.Context) []*BankTransfer {
	v, err := btcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (btcb *BankTransferCreateBulk) Exec(ctx context.Context) error {
	_, err := btcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (btcb *BankTransferCreateBulk) ExecX(ctx context.Context) {
	if err := btcb.Exec(ctx); err != nil {
		panic(err)
	}
}

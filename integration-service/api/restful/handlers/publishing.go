package handlers

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/integration-service/usecase/publishing"
)

// PublishingHandler handles publishing REST API endpoints
type PublishingHandler struct {
	*BaseHandler
}

// NewPublishingHandler creates a new publishing handler
func NewPublishingHandler(base *BaseHandler) *PublishingHandler {
	return &PublishingHandler{
		BaseHandler: base,
	}
}

// PublishPost publishes content to a platform
// @Summary Publish post to platform
// @Description Publish content to a connected platform account
// @Tags publishing
// @Accept json
// @Produce json
// @Param request body PublishPostRequest true "Publish post request"
// @Success 200 {object} PublishPostResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/publishing/posts [post]
func (h *PublishingHandler) PublishPost(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	var req PublishPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if req.AccountID == "" || req.Content == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_required_fields",
			Message: "account_id and content are required",
		})
		return
	}

	// Convert platform options to interface map
	platformOptions := make(map[string]interface{})
	for k, v := range req.PlatformOptions {
		platformOptions[k] = v
	}

	// Convert to usecase request
	useCaseReq := &publishing.PublishPostRequest{
		AccountID:       req.AccountID,
		UserID:          userID,
		PostID:          req.PostID,
		Content:         req.Content,
		ImageURLs:       req.ImageURLs,
		Hashtags:        req.Hashtags,
		PlatformOptions: platformOptions,
		ScheduledAt:     req.ScheduledAt,
	}

	response, err := h.publishingUseCase.PublishPost(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish post")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "publish_failed",
			Message: "Failed to publish post",
		})
		return
	}

	c.JSON(http.StatusOK, PublishPostResponse{
		Success:        response.Success,
		PlatformPostID: response.PlatformPostID,
		PlatformURL:    response.PlatformURL,
		ErrorMessage:   response.ErrorMessage,
		PublishedAt:    response.PublishedAt,
	})
}

// GetPostAnalytics retrieves analytics for a published post
// @Summary Get post analytics
// @Description Get analytics data for a published post
// @Tags publishing
// @Accept json
// @Produce json
// @Param request body GetPostAnalyticsRequest true "Post analytics request"
// @Success 200 {object} GetPostAnalyticsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/publishing/posts/analytics [post]
func (h *PublishingHandler) GetPostAnalytics(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	var req GetPostAnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if req.AccountID == "" || req.PlatformPostID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_required_fields",
			Message: "account_id and platform_post_id are required",
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &publishing.GetPostAnalyticsRequest{
		AccountID:      req.AccountID,
		PlatformPostID: req.PlatformPostID,
		UserID:         userID,
		Metrics:        req.Metrics,
	}

	response, err := h.publishingUseCase.GetPostAnalytics(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post analytics")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "analytics_failed",
			Message: "Failed to get post analytics",
		})
		return
	}

	c.JSON(http.StatusOK, GetPostAnalyticsResponse{
		PlatformPostID:  response.PlatformPostID,
		Platform:        response.Platform,
		Metrics:         response.Metrics,
		CollectedAt:     response.CollectedAt,
		PostPublishedAt: response.PostPublishedAt,
	})
}

// GetAccountAnalytics retrieves analytics for a platform account
// @Summary Get account analytics
// @Description Get analytics data for a platform account
// @Tags publishing
// @Accept json
// @Produce json
// @Param request body GetAccountAnalyticsRequest true "Account analytics request"
// @Success 200 {object} GetAccountAnalyticsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/publishing/accounts/analytics [post]
func (h *PublishingHandler) GetAccountAnalytics(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	var req GetAccountAnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if req.AccountID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_required_fields",
			Message: "account_id is required",
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &publishing.GetAccountAnalyticsRequest{
		AccountID: req.AccountID,
		UserID:    userID,
		DateFrom:  req.DateFrom,
		DateTo:    req.DateTo,
		Metrics:   req.Metrics,
	}

	response, err := h.publishingUseCase.GetAccountAnalytics(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get account analytics")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "analytics_failed",
			Message: "Failed to get account analytics",
		})
		return
	}

	c.JSON(http.StatusOK, GetAccountAnalyticsResponse{
		AccountID:   response.AccountID,
		Platform:    response.Platform,
		Metrics:     response.Metrics,
		TimeSeries:  response.TimeSeries,
		CollectedAt: response.CollectedAt,
	})
}

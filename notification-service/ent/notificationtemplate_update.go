// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationTemplateUpdate is the builder for updating NotificationTemplate entities.
type NotificationTemplateUpdate struct {
	config
	hooks    []Hook
	mutation *NotificationTemplateMutation
}

// Where appends a list predicates to the NotificationTemplateUpdate builder.
func (ntu *NotificationTemplateUpdate) Where(ps ...predicate.NotificationTemplate) *NotificationTemplateUpdate {
	ntu.mutation.Where(ps...)
	return ntu
}

// SetEventType sets the "event_type" field.
func (ntu *NotificationTemplateUpdate) SetEventType(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetEventType(s)
	return ntu
}

// SetNillableEventType sets the "event_type" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableEventType(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetEventType(*s)
	}
	return ntu
}

// SetName sets the "name" field.
func (ntu *NotificationTemplateUpdate) SetName(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetName(s)
	return ntu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableName(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetName(*s)
	}
	return ntu
}

// SetDescription sets the "description" field.
func (ntu *NotificationTemplateUpdate) SetDescription(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetDescription(s)
	return ntu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableDescription(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetDescription(*s)
	}
	return ntu
}

// ClearDescription clears the value of the "description" field.
func (ntu *NotificationTemplateUpdate) ClearDescription() *NotificationTemplateUpdate {
	ntu.mutation.ClearDescription()
	return ntu
}

// SetTemplates sets the "templates" field.
func (ntu *NotificationTemplateUpdate) SetTemplates(m map[string]interface{}) *NotificationTemplateUpdate {
	ntu.mutation.SetTemplates(m)
	return ntu
}

// SetIsActive sets the "is_active" field.
func (ntu *NotificationTemplateUpdate) SetIsActive(b bool) *NotificationTemplateUpdate {
	ntu.mutation.SetIsActive(b)
	return ntu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableIsActive(b *bool) *NotificationTemplateUpdate {
	if b != nil {
		ntu.SetIsActive(*b)
	}
	return ntu
}

// SetVariables sets the "variables" field.
func (ntu *NotificationTemplateUpdate) SetVariables(s []string) *NotificationTemplateUpdate {
	ntu.mutation.SetVariables(s)
	return ntu
}

// AppendVariables appends s to the "variables" field.
func (ntu *NotificationTemplateUpdate) AppendVariables(s []string) *NotificationTemplateUpdate {
	ntu.mutation.AppendVariables(s)
	return ntu
}

// ClearVariables clears the value of the "variables" field.
func (ntu *NotificationTemplateUpdate) ClearVariables() *NotificationTemplateUpdate {
	ntu.mutation.ClearVariables()
	return ntu
}

// SetDefaultChannels sets the "default_channels" field.
func (ntu *NotificationTemplateUpdate) SetDefaultChannels(s []string) *NotificationTemplateUpdate {
	ntu.mutation.SetDefaultChannels(s)
	return ntu
}

// AppendDefaultChannels appends s to the "default_channels" field.
func (ntu *NotificationTemplateUpdate) AppendDefaultChannels(s []string) *NotificationTemplateUpdate {
	ntu.mutation.AppendDefaultChannels(s)
	return ntu
}

// ClearDefaultChannels clears the value of the "default_channels" field.
func (ntu *NotificationTemplateUpdate) ClearDefaultChannels() *NotificationTemplateUpdate {
	ntu.mutation.ClearDefaultChannels()
	return ntu
}

// SetDefaultPriority sets the "default_priority" field.
func (ntu *NotificationTemplateUpdate) SetDefaultPriority(np notificationtemplate.DefaultPriority) *NotificationTemplateUpdate {
	ntu.mutation.SetDefaultPriority(np)
	return ntu
}

// SetNillableDefaultPriority sets the "default_priority" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableDefaultPriority(np *notificationtemplate.DefaultPriority) *NotificationTemplateUpdate {
	if np != nil {
		ntu.SetDefaultPriority(*np)
	}
	return ntu
}

// SetLanguage sets the "language" field.
func (ntu *NotificationTemplateUpdate) SetLanguage(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetLanguage(s)
	return ntu
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableLanguage(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetLanguage(*s)
	}
	return ntu
}

// SetVersion sets the "version" field.
func (ntu *NotificationTemplateUpdate) SetVersion(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetVersion(s)
	return ntu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableVersion(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetVersion(*s)
	}
	return ntu
}

// SetMetadata sets the "metadata" field.
func (ntu *NotificationTemplateUpdate) SetMetadata(m map[string]interface{}) *NotificationTemplateUpdate {
	ntu.mutation.SetMetadata(m)
	return ntu
}

// ClearMetadata clears the value of the "metadata" field.
func (ntu *NotificationTemplateUpdate) ClearMetadata() *NotificationTemplateUpdate {
	ntu.mutation.ClearMetadata()
	return ntu
}

// SetCreatedBy sets the "created_by" field.
func (ntu *NotificationTemplateUpdate) SetCreatedBy(s string) *NotificationTemplateUpdate {
	ntu.mutation.SetCreatedBy(s)
	return ntu
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableCreatedBy(s *string) *NotificationTemplateUpdate {
	if s != nil {
		ntu.SetCreatedBy(*s)
	}
	return ntu
}

// ClearCreatedBy clears the value of the "created_by" field.
func (ntu *NotificationTemplateUpdate) ClearCreatedBy() *NotificationTemplateUpdate {
	ntu.mutation.ClearCreatedBy()
	return ntu
}

// SetUpdatedAt sets the "updated_at" field.
func (ntu *NotificationTemplateUpdate) SetUpdatedAt(t time.Time) *NotificationTemplateUpdate {
	ntu.mutation.SetUpdatedAt(t)
	return ntu
}

// SetDeletedAt sets the "deleted_at" field.
func (ntu *NotificationTemplateUpdate) SetDeletedAt(t time.Time) *NotificationTemplateUpdate {
	ntu.mutation.SetDeletedAt(t)
	return ntu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ntu *NotificationTemplateUpdate) SetNillableDeletedAt(t *time.Time) *NotificationTemplateUpdate {
	if t != nil {
		ntu.SetDeletedAt(*t)
	}
	return ntu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ntu *NotificationTemplateUpdate) ClearDeletedAt() *NotificationTemplateUpdate {
	ntu.mutation.ClearDeletedAt()
	return ntu
}

// Mutation returns the NotificationTemplateMutation object of the builder.
func (ntu *NotificationTemplateUpdate) Mutation() *NotificationTemplateMutation {
	return ntu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ntu *NotificationTemplateUpdate) Save(ctx context.Context) (int, error) {
	ntu.defaults()
	return withHooks(ctx, ntu.sqlSave, ntu.mutation, ntu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ntu *NotificationTemplateUpdate) SaveX(ctx context.Context) int {
	affected, err := ntu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ntu *NotificationTemplateUpdate) Exec(ctx context.Context) error {
	_, err := ntu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ntu *NotificationTemplateUpdate) ExecX(ctx context.Context) {
	if err := ntu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ntu *NotificationTemplateUpdate) defaults() {
	if _, ok := ntu.mutation.UpdatedAt(); !ok {
		v := notificationtemplate.UpdateDefaultUpdatedAt()
		ntu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ntu *NotificationTemplateUpdate) check() error {
	if v, ok := ntu.mutation.EventType(); ok {
		if err := notificationtemplate.EventTypeValidator(v); err != nil {
			return &ValidationError{Name: "event_type", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.event_type": %w`, err)}
		}
	}
	if v, ok := ntu.mutation.Name(); ok {
		if err := notificationtemplate.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.name": %w`, err)}
		}
	}
	if v, ok := ntu.mutation.DefaultPriority(); ok {
		if err := notificationtemplate.DefaultPriorityValidator(v); err != nil {
			return &ValidationError{Name: "default_priority", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.default_priority": %w`, err)}
		}
	}
	if v, ok := ntu.mutation.Language(); ok {
		if err := notificationtemplate.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.language": %w`, err)}
		}
	}
	if v, ok := ntu.mutation.Version(); ok {
		if err := notificationtemplate.VersionValidator(v); err != nil {
			return &ValidationError{Name: "version", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.version": %w`, err)}
		}
	}
	if v, ok := ntu.mutation.CreatedBy(); ok {
		if err := notificationtemplate.CreatedByValidator(v); err != nil {
			return &ValidationError{Name: "created_by", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.created_by": %w`, err)}
		}
	}
	return nil
}

func (ntu *NotificationTemplateUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ntu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationtemplate.Table, notificationtemplate.Columns, sqlgraph.NewFieldSpec(notificationtemplate.FieldID, field.TypeUUID))
	if ps := ntu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ntu.mutation.EventType(); ok {
		_spec.SetField(notificationtemplate.FieldEventType, field.TypeString, value)
	}
	if value, ok := ntu.mutation.Name(); ok {
		_spec.SetField(notificationtemplate.FieldName, field.TypeString, value)
	}
	if value, ok := ntu.mutation.Description(); ok {
		_spec.SetField(notificationtemplate.FieldDescription, field.TypeString, value)
	}
	if ntu.mutation.DescriptionCleared() {
		_spec.ClearField(notificationtemplate.FieldDescription, field.TypeString)
	}
	if value, ok := ntu.mutation.Templates(); ok {
		_spec.SetField(notificationtemplate.FieldTemplates, field.TypeJSON, value)
	}
	if value, ok := ntu.mutation.IsActive(); ok {
		_spec.SetField(notificationtemplate.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := ntu.mutation.Variables(); ok {
		_spec.SetField(notificationtemplate.FieldVariables, field.TypeJSON, value)
	}
	if value, ok := ntu.mutation.AppendedVariables(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notificationtemplate.FieldVariables, value)
		})
	}
	if ntu.mutation.VariablesCleared() {
		_spec.ClearField(notificationtemplate.FieldVariables, field.TypeJSON)
	}
	if value, ok := ntu.mutation.DefaultChannels(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultChannels, field.TypeJSON, value)
	}
	if value, ok := ntu.mutation.AppendedDefaultChannels(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notificationtemplate.FieldDefaultChannels, value)
		})
	}
	if ntu.mutation.DefaultChannelsCleared() {
		_spec.ClearField(notificationtemplate.FieldDefaultChannels, field.TypeJSON)
	}
	if value, ok := ntu.mutation.DefaultPriority(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultPriority, field.TypeEnum, value)
	}
	if value, ok := ntu.mutation.Language(); ok {
		_spec.SetField(notificationtemplate.FieldLanguage, field.TypeString, value)
	}
	if value, ok := ntu.mutation.Version(); ok {
		_spec.SetField(notificationtemplate.FieldVersion, field.TypeString, value)
	}
	if value, ok := ntu.mutation.Metadata(); ok {
		_spec.SetField(notificationtemplate.FieldMetadata, field.TypeJSON, value)
	}
	if ntu.mutation.MetadataCleared() {
		_spec.ClearField(notificationtemplate.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ntu.mutation.CreatedBy(); ok {
		_spec.SetField(notificationtemplate.FieldCreatedBy, field.TypeString, value)
	}
	if ntu.mutation.CreatedByCleared() {
		_spec.ClearField(notificationtemplate.FieldCreatedBy, field.TypeString)
	}
	if value, ok := ntu.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationtemplate.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ntu.mutation.DeletedAt(); ok {
		_spec.SetField(notificationtemplate.FieldDeletedAt, field.TypeTime, value)
	}
	if ntu.mutation.DeletedAtCleared() {
		_spec.ClearField(notificationtemplate.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ntu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationtemplate.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ntu.mutation.done = true
	return n, nil
}

// NotificationTemplateUpdateOne is the builder for updating a single NotificationTemplate entity.
type NotificationTemplateUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *NotificationTemplateMutation
}

// SetEventType sets the "event_type" field.
func (ntuo *NotificationTemplateUpdateOne) SetEventType(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetEventType(s)
	return ntuo
}

// SetNillableEventType sets the "event_type" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableEventType(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetEventType(*s)
	}
	return ntuo
}

// SetName sets the "name" field.
func (ntuo *NotificationTemplateUpdateOne) SetName(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetName(s)
	return ntuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableName(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetName(*s)
	}
	return ntuo
}

// SetDescription sets the "description" field.
func (ntuo *NotificationTemplateUpdateOne) SetDescription(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetDescription(s)
	return ntuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableDescription(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetDescription(*s)
	}
	return ntuo
}

// ClearDescription clears the value of the "description" field.
func (ntuo *NotificationTemplateUpdateOne) ClearDescription() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearDescription()
	return ntuo
}

// SetTemplates sets the "templates" field.
func (ntuo *NotificationTemplateUpdateOne) SetTemplates(m map[string]interface{}) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetTemplates(m)
	return ntuo
}

// SetIsActive sets the "is_active" field.
func (ntuo *NotificationTemplateUpdateOne) SetIsActive(b bool) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetIsActive(b)
	return ntuo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableIsActive(b *bool) *NotificationTemplateUpdateOne {
	if b != nil {
		ntuo.SetIsActive(*b)
	}
	return ntuo
}

// SetVariables sets the "variables" field.
func (ntuo *NotificationTemplateUpdateOne) SetVariables(s []string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetVariables(s)
	return ntuo
}

// AppendVariables appends s to the "variables" field.
func (ntuo *NotificationTemplateUpdateOne) AppendVariables(s []string) *NotificationTemplateUpdateOne {
	ntuo.mutation.AppendVariables(s)
	return ntuo
}

// ClearVariables clears the value of the "variables" field.
func (ntuo *NotificationTemplateUpdateOne) ClearVariables() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearVariables()
	return ntuo
}

// SetDefaultChannels sets the "default_channels" field.
func (ntuo *NotificationTemplateUpdateOne) SetDefaultChannels(s []string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetDefaultChannels(s)
	return ntuo
}

// AppendDefaultChannels appends s to the "default_channels" field.
func (ntuo *NotificationTemplateUpdateOne) AppendDefaultChannels(s []string) *NotificationTemplateUpdateOne {
	ntuo.mutation.AppendDefaultChannels(s)
	return ntuo
}

// ClearDefaultChannels clears the value of the "default_channels" field.
func (ntuo *NotificationTemplateUpdateOne) ClearDefaultChannels() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearDefaultChannels()
	return ntuo
}

// SetDefaultPriority sets the "default_priority" field.
func (ntuo *NotificationTemplateUpdateOne) SetDefaultPriority(np notificationtemplate.DefaultPriority) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetDefaultPriority(np)
	return ntuo
}

// SetNillableDefaultPriority sets the "default_priority" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableDefaultPriority(np *notificationtemplate.DefaultPriority) *NotificationTemplateUpdateOne {
	if np != nil {
		ntuo.SetDefaultPriority(*np)
	}
	return ntuo
}

// SetLanguage sets the "language" field.
func (ntuo *NotificationTemplateUpdateOne) SetLanguage(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetLanguage(s)
	return ntuo
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableLanguage(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetLanguage(*s)
	}
	return ntuo
}

// SetVersion sets the "version" field.
func (ntuo *NotificationTemplateUpdateOne) SetVersion(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetVersion(s)
	return ntuo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableVersion(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetVersion(*s)
	}
	return ntuo
}

// SetMetadata sets the "metadata" field.
func (ntuo *NotificationTemplateUpdateOne) SetMetadata(m map[string]interface{}) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetMetadata(m)
	return ntuo
}

// ClearMetadata clears the value of the "metadata" field.
func (ntuo *NotificationTemplateUpdateOne) ClearMetadata() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearMetadata()
	return ntuo
}

// SetCreatedBy sets the "created_by" field.
func (ntuo *NotificationTemplateUpdateOne) SetCreatedBy(s string) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetCreatedBy(s)
	return ntuo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableCreatedBy(s *string) *NotificationTemplateUpdateOne {
	if s != nil {
		ntuo.SetCreatedBy(*s)
	}
	return ntuo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (ntuo *NotificationTemplateUpdateOne) ClearCreatedBy() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearCreatedBy()
	return ntuo
}

// SetUpdatedAt sets the "updated_at" field.
func (ntuo *NotificationTemplateUpdateOne) SetUpdatedAt(t time.Time) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetUpdatedAt(t)
	return ntuo
}

// SetDeletedAt sets the "deleted_at" field.
func (ntuo *NotificationTemplateUpdateOne) SetDeletedAt(t time.Time) *NotificationTemplateUpdateOne {
	ntuo.mutation.SetDeletedAt(t)
	return ntuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ntuo *NotificationTemplateUpdateOne) SetNillableDeletedAt(t *time.Time) *NotificationTemplateUpdateOne {
	if t != nil {
		ntuo.SetDeletedAt(*t)
	}
	return ntuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ntuo *NotificationTemplateUpdateOne) ClearDeletedAt() *NotificationTemplateUpdateOne {
	ntuo.mutation.ClearDeletedAt()
	return ntuo
}

// Mutation returns the NotificationTemplateMutation object of the builder.
func (ntuo *NotificationTemplateUpdateOne) Mutation() *NotificationTemplateMutation {
	return ntuo.mutation
}

// Where appends a list predicates to the NotificationTemplateUpdate builder.
func (ntuo *NotificationTemplateUpdateOne) Where(ps ...predicate.NotificationTemplate) *NotificationTemplateUpdateOne {
	ntuo.mutation.Where(ps...)
	return ntuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ntuo *NotificationTemplateUpdateOne) Select(field string, fields ...string) *NotificationTemplateUpdateOne {
	ntuo.fields = append([]string{field}, fields...)
	return ntuo
}

// Save executes the query and returns the updated NotificationTemplate entity.
func (ntuo *NotificationTemplateUpdateOne) Save(ctx context.Context) (*NotificationTemplate, error) {
	ntuo.defaults()
	return withHooks(ctx, ntuo.sqlSave, ntuo.mutation, ntuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ntuo *NotificationTemplateUpdateOne) SaveX(ctx context.Context) *NotificationTemplate {
	node, err := ntuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ntuo *NotificationTemplateUpdateOne) Exec(ctx context.Context) error {
	_, err := ntuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ntuo *NotificationTemplateUpdateOne) ExecX(ctx context.Context) {
	if err := ntuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ntuo *NotificationTemplateUpdateOne) defaults() {
	if _, ok := ntuo.mutation.UpdatedAt(); !ok {
		v := notificationtemplate.UpdateDefaultUpdatedAt()
		ntuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ntuo *NotificationTemplateUpdateOne) check() error {
	if v, ok := ntuo.mutation.EventType(); ok {
		if err := notificationtemplate.EventTypeValidator(v); err != nil {
			return &ValidationError{Name: "event_type", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.event_type": %w`, err)}
		}
	}
	if v, ok := ntuo.mutation.Name(); ok {
		if err := notificationtemplate.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.name": %w`, err)}
		}
	}
	if v, ok := ntuo.mutation.DefaultPriority(); ok {
		if err := notificationtemplate.DefaultPriorityValidator(v); err != nil {
			return &ValidationError{Name: "default_priority", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.default_priority": %w`, err)}
		}
	}
	if v, ok := ntuo.mutation.Language(); ok {
		if err := notificationtemplate.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.language": %w`, err)}
		}
	}
	if v, ok := ntuo.mutation.Version(); ok {
		if err := notificationtemplate.VersionValidator(v); err != nil {
			return &ValidationError{Name: "version", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.version": %w`, err)}
		}
	}
	if v, ok := ntuo.mutation.CreatedBy(); ok {
		if err := notificationtemplate.CreatedByValidator(v); err != nil {
			return &ValidationError{Name: "created_by", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.created_by": %w`, err)}
		}
	}
	return nil
}

func (ntuo *NotificationTemplateUpdateOne) sqlSave(ctx context.Context) (_node *NotificationTemplate, err error) {
	if err := ntuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationtemplate.Table, notificationtemplate.Columns, sqlgraph.NewFieldSpec(notificationtemplate.FieldID, field.TypeUUID))
	id, ok := ntuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "NotificationTemplate.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ntuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationtemplate.FieldID)
		for _, f := range fields {
			if !notificationtemplate.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notificationtemplate.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ntuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ntuo.mutation.EventType(); ok {
		_spec.SetField(notificationtemplate.FieldEventType, field.TypeString, value)
	}
	if value, ok := ntuo.mutation.Name(); ok {
		_spec.SetField(notificationtemplate.FieldName, field.TypeString, value)
	}
	if value, ok := ntuo.mutation.Description(); ok {
		_spec.SetField(notificationtemplate.FieldDescription, field.TypeString, value)
	}
	if ntuo.mutation.DescriptionCleared() {
		_spec.ClearField(notificationtemplate.FieldDescription, field.TypeString)
	}
	if value, ok := ntuo.mutation.Templates(); ok {
		_spec.SetField(notificationtemplate.FieldTemplates, field.TypeJSON, value)
	}
	if value, ok := ntuo.mutation.IsActive(); ok {
		_spec.SetField(notificationtemplate.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := ntuo.mutation.Variables(); ok {
		_spec.SetField(notificationtemplate.FieldVariables, field.TypeJSON, value)
	}
	if value, ok := ntuo.mutation.AppendedVariables(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notificationtemplate.FieldVariables, value)
		})
	}
	if ntuo.mutation.VariablesCleared() {
		_spec.ClearField(notificationtemplate.FieldVariables, field.TypeJSON)
	}
	if value, ok := ntuo.mutation.DefaultChannels(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultChannels, field.TypeJSON, value)
	}
	if value, ok := ntuo.mutation.AppendedDefaultChannels(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notificationtemplate.FieldDefaultChannels, value)
		})
	}
	if ntuo.mutation.DefaultChannelsCleared() {
		_spec.ClearField(notificationtemplate.FieldDefaultChannels, field.TypeJSON)
	}
	if value, ok := ntuo.mutation.DefaultPriority(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultPriority, field.TypeEnum, value)
	}
	if value, ok := ntuo.mutation.Language(); ok {
		_spec.SetField(notificationtemplate.FieldLanguage, field.TypeString, value)
	}
	if value, ok := ntuo.mutation.Version(); ok {
		_spec.SetField(notificationtemplate.FieldVersion, field.TypeString, value)
	}
	if value, ok := ntuo.mutation.Metadata(); ok {
		_spec.SetField(notificationtemplate.FieldMetadata, field.TypeJSON, value)
	}
	if ntuo.mutation.MetadataCleared() {
		_spec.ClearField(notificationtemplate.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ntuo.mutation.CreatedBy(); ok {
		_spec.SetField(notificationtemplate.FieldCreatedBy, field.TypeString, value)
	}
	if ntuo.mutation.CreatedByCleared() {
		_spec.ClearField(notificationtemplate.FieldCreatedBy, field.TypeString)
	}
	if value, ok := ntuo.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationtemplate.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ntuo.mutation.DeletedAt(); ok {
		_spec.SetField(notificationtemplate.FieldDeletedAt, field.TypeTime, value)
	}
	if ntuo.mutation.DeletedAtCleared() {
		_spec.ClearField(notificationtemplate.FieldDeletedAt, field.TypeTime)
	}
	_node = &NotificationTemplate{config: ntuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ntuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationtemplate.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ntuo.mutation.done = true
	return _node, nil
}

package restful

import (
	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/api/restful/handlers"
	"github.com/social-content-ai/content-mgmt-service/api/restful/middleware"
	"github.com/social-content-ai/content-mgmt-service/usecase/post"
	"github.com/social-content-ai/content-mgmt-service/usecase/schedule"
	"github.com/social-content-ai/content-mgmt-service/usecase/template"
	"github.com/social-content-ai/content-mgmt-service/usecase/workspace"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// SetupRoutes configures all API routes for the content management service
func SetupRoutes(
	router *gin.Engine,
	postUseCase post.UseCase,
	templateUseCase template.UseCase,
	workspaceUseCase workspace.UseCase,
	scheduleUseCase schedule.UseCase,
	userClient userv1.AuthServiceClient,
	logger logging.Logger,
) {
	// Initialize handlers
	postHandler := handlers.NewPostHandler(postUseCase, logger)
	templateHandler := handlers.NewTemplateHandler(templateUseCase, logger)
	workspaceHandler := handlers.NewWorkspaceHandler(workspaceUseCase, logger)
	scheduleHandler := handlers.NewScheduleHandler(scheduleUseCase, logger)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(userClient, logger)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "content-mgmt-service",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Post management routes
		posts := v1.Group("/posts")
		posts.Use(authMiddleware.RequireAuth())
		{
			posts.POST("", postHandler.CreatePost)
			posts.GET("", postHandler.ListPosts)
			posts.GET("/:id", postHandler.GetPost)
			posts.PUT("/:id", postHandler.UpdatePost)
			posts.DELETE("/:id", postHandler.DeletePost)

			// Post scheduling and publishing
			posts.POST("/:id/schedule", postHandler.SchedulePost)
			posts.POST("/:id/publish", postHandler.PublishPost)
			posts.POST("/:id/cancel", postHandler.CancelScheduledPost)
			posts.GET("/:id/status", postHandler.GetPostStatus)

			// NOTE: Post analytics endpoints đã được chuyển sang Analytics Service
			// Sử dụng Analytics Service để lấy thống kê:
			// - GET /analytics/posts/:id - Chi tiết analytics của post
			// - GET /analytics/posts/:id/engagement - Thống kê tương tác của post
		}

		// Template management routes
		templates := v1.Group("/templates")
		templates.Use(authMiddleware.RequireAuth())
		{
			// Basic CRUD
			templates.POST("", templateHandler.CreateTemplate)
			templates.GET("", templateHandler.ListTemplates)
			templates.GET("/:id", templateHandler.GetTemplate)
			templates.PUT("/:id", templateHandler.UpdateTemplate)
			templates.DELETE("/:id", templateHandler.DeleteTemplate)

			// Template usage
			templates.POST("/:id/use", templateHandler.UseTemplate)
			templates.POST("/:id/rate", templateHandler.RateTemplate)
			templates.POST("/:id/favorite", templateHandler.FavoriteTemplate)
			templates.DELETE("/:id/favorite", templateHandler.UnfavoriteTemplate)

			// Template marketplace
			templates.GET("/marketplace", templateHandler.ListMarketplaceTemplates)
			templates.GET("/marketplace/featured", templateHandler.ListFeaturedTemplates)
			templates.GET("/marketplace/categories", templateHandler.GetTemplateCategories)
			templates.GET("/marketplace/search", templateHandler.SearchTemplates)

			// NOTE: Template analytics endpoints đã được chuyển sang Analytics Service
			// Sử dụng Analytics Service để lấy thống kê:
			// - GET /analytics/templates/:id - Chi tiết analytics của template
			// - GET /analytics/templates/:id/usage - Thống kê sử dụng template
		}

		// Workspace management routes
		workspaces := v1.Group("/workspaces")
		workspaces.Use(authMiddleware.RequireAuth())
		{
			// Basic CRUD
			workspaces.POST("", workspaceHandler.CreateWorkspace)
			workspaces.GET("", workspaceHandler.ListWorkspaces)
			workspaces.GET("/:id", workspaceHandler.GetWorkspace)
			workspaces.PUT("/:id", workspaceHandler.UpdateWorkspace)
			workspaces.DELETE("/:id", workspaceHandler.DeleteWorkspace)

			// Member management
			workspaces.POST("/:id/members", workspaceHandler.AddMember)
			workspaces.GET("/:id/members", workspaceHandler.ListMembers)
			workspaces.PUT("/:id/members/:user_id", workspaceHandler.UpdateMemberRole)
			workspaces.DELETE("/:id/members/:user_id", workspaceHandler.RemoveMember)

			// Workspace content
			workspaces.GET("/:id/posts", workspaceHandler.GetWorkspacePosts)
			workspaces.GET("/:id/templates", workspaceHandler.GetWorkspaceTemplates)
			// NOTE: Workspace analytics endpoint đã được chuyển sang Analytics Service
			// Sử dụng Analytics Service: GET /analytics/workspaces/:id

			// Workspace settings
			workspaces.PUT("/:id/settings", workspaceHandler.UpdateWorkspaceSettings)
			workspaces.GET("/:id/settings", workspaceHandler.GetWorkspaceSettings)
		}

		// Schedule management routes
		schedules := v1.Group("/schedules")
		schedules.Use(authMiddleware.RequireAuth())
		{
			// Basic CRUD
			schedules.POST("", scheduleHandler.CreateSchedule)
			schedules.GET("", scheduleHandler.ListSchedules)
			schedules.GET("/:id", scheduleHandler.GetSchedule)
			schedules.PUT("/:id", scheduleHandler.UpdateSchedule)
			schedules.DELETE("/:id", scheduleHandler.DeleteSchedule)

			// Schedule operations
			schedules.POST("/:id/execute", scheduleHandler.ExecuteSchedule)
			schedules.POST("/:id/enable", scheduleHandler.EnableSchedule)
			schedules.POST("/:id/disable", scheduleHandler.DisableSchedule)

			// Schedule views
			schedules.GET("/calendar", scheduleHandler.GetScheduleCalendar)
			schedules.GET("/upcoming", scheduleHandler.GetUpcomingSchedules)
			// NOTE: Schedule analytics endpoint đã được chuyển sang Analytics Service
			// Sử dụng Analytics Service: GET /analytics/schedules

			// Bulk operations
			schedules.POST("/bulk", scheduleHandler.BulkOperations)
		}

		// NOTE: Analytics endpoints đã được chuyển sang Analytics Service
		// theo kiến trúc microservices. Content Management Service chỉ quản lý:
		// - Posts CRUD operations
		// - Templates CRUD operations
		// - Workspaces CRUD operations
		// - Scheduling operations
		//
		// Analytics endpoints hiện tại có thể truy cập qua Analytics Service:
		// - GET /analytics/posts - Thống kê bài viết
		// - GET /analytics/templates - Thống kê template
		// - GET /analytics/workspaces - Thống kê workspace
		// - GET /analytics/performance - Thống kê hiệu suất
		// - GET /analytics/engagement - Thống kê tương tác
	}

	// Admin routes
	// admin := router.Group("/admin/v1")
	// admin.Use(authMiddleware.RequireAuth())
	// admin.Use(authMiddleware.RequireRole("admin"))
	// {
	// 	// Admin post management
	// 	admin.GET("/posts", postHandler.AdminListPosts)
	// 	admin.GET("/posts/:id", postHandler.AdminGetPost)
	// 	admin.DELETE("/posts/:id", postHandler.AdminDeletePost)

	// 	// Admin template management
	// 	admin.GET("/templates", templateHandler.AdminListTemplates)
	// 	admin.PUT("/templates/:id/verify", templateHandler.AdminVerifyTemplate)
	// 	admin.PUT("/templates/:id/feature", templateHandler.AdminFeatureTemplate)
	// 	admin.DELETE("/templates/:id", templateHandler.AdminDeleteTemplate)

	// 	// Admin workspace management
	// 	admin.GET("/workspaces", workspaceHandler.AdminListWorkspaces)
	// 	admin.GET("/workspaces/:id", workspaceHandler.AdminGetWorkspace)
	// 	admin.DELETE("/workspaces/:id", workspaceHandler.AdminDeleteWorkspace)

	// 	// Admin analytics
	// 	admin.GET("/analytics/overview", postHandler.GetAdminAnalytics)
	// 	admin.GET("/analytics/templates", templateHandler.GetAdminTemplateAnalytics)
	// 	admin.GET("/analytics/workspaces", workspaceHandler.GetAdminWorkspaceAnalytics)
	// }
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: analytics/v1/analytics.proto

package analyticsv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AnalyticsServiceClient is the client API for AnalyticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnalyticsServiceClient interface {
	// Metric recording operations
	RecordMetric(ctx context.Context, in *RecordMetricRequest, opts ...grpc.CallOption) (*Empty, error)
	RecordMetrics(ctx context.Context, in *RecordMetricsRequest, opts ...grpc.CallOption) (*Empty, error)
	// Metric retrieval operations
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
	GetUserAnalytics(ctx context.Context, in *GetUserAnalyticsRequest, opts ...grpc.CallOption) (*UserAnalytics, error)
	GetWorkspaceAnalytics(ctx context.Context, in *GetWorkspaceAnalyticsRequest, opts ...grpc.CallOption) (*WorkspaceAnalytics, error)
	// Security monitoring operations
	RecordSecurityEvent(ctx context.Context, in *RecordSecurityEventRequest, opts ...grpc.CallOption) (*Empty, error)
	GetSecurityEvents(ctx context.Context, in *GetSecurityEventsRequest, opts ...grpc.CallOption) (*GetSecurityEventsResponse, error)
	GetSecurityMetrics(ctx context.Context, in *GetSecurityMetricsRequest, opts ...grpc.CallOption) (*SecurityMetrics, error)
	// Dashboard operations
	CreateDashboard(ctx context.Context, in *CreateDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error)
	GetDashboard(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error)
	UpdateDashboard(ctx context.Context, in *UpdateDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error)
	ListDashboards(ctx context.Context, in *ListDashboardsRequest, opts ...grpc.CallOption) (*ListDashboardsResponse, error)
	GetDashboardData(ctx context.Context, in *GetDashboardDataRequest, opts ...grpc.CallOption) (*DashboardData, error)
	DeleteDashboard(ctx context.Context, in *DeleteDashboardRequest, opts ...grpc.CallOption) (*Empty, error)
}

type analyticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticsServiceClient(cc grpc.ClientConnInterface) AnalyticsServiceClient {
	return &analyticsServiceClient{cc}
}

func (c *analyticsServiceClient) RecordMetric(ctx context.Context, in *RecordMetricRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/RecordMetric", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) RecordMetrics(ctx context.Context, in *RecordMetricsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/RecordMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetUserAnalytics(ctx context.Context, in *GetUserAnalyticsRequest, opts ...grpc.CallOption) (*UserAnalytics, error) {
	out := new(UserAnalytics)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetUserAnalytics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetWorkspaceAnalytics(ctx context.Context, in *GetWorkspaceAnalyticsRequest, opts ...grpc.CallOption) (*WorkspaceAnalytics, error) {
	out := new(WorkspaceAnalytics)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetWorkspaceAnalytics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) RecordSecurityEvent(ctx context.Context, in *RecordSecurityEventRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/RecordSecurityEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetSecurityEvents(ctx context.Context, in *GetSecurityEventsRequest, opts ...grpc.CallOption) (*GetSecurityEventsResponse, error) {
	out := new(GetSecurityEventsResponse)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetSecurityEvents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetSecurityMetrics(ctx context.Context, in *GetSecurityMetricsRequest, opts ...grpc.CallOption) (*SecurityMetrics, error) {
	out := new(SecurityMetrics)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetSecurityMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) CreateDashboard(ctx context.Context, in *CreateDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error) {
	out := new(Dashboard)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/CreateDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetDashboard(ctx context.Context, in *GetDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error) {
	out := new(Dashboard)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) UpdateDashboard(ctx context.Context, in *UpdateDashboardRequest, opts ...grpc.CallOption) (*Dashboard, error) {
	out := new(Dashboard)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/UpdateDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) ListDashboards(ctx context.Context, in *ListDashboardsRequest, opts ...grpc.CallOption) (*ListDashboardsResponse, error) {
	out := new(ListDashboardsResponse)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/ListDashboards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetDashboardData(ctx context.Context, in *GetDashboardDataRequest, opts ...grpc.CallOption) (*DashboardData, error) {
	out := new(DashboardData)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/GetDashboardData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) DeleteDashboard(ctx context.Context, in *DeleteDashboardRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/analytics.v1.AnalyticsService/DeleteDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticsServiceServer is the server API for AnalyticsService service.
// All implementations must embed UnimplementedAnalyticsServiceServer
// for forward compatibility
type AnalyticsServiceServer interface {
	// Metric recording operations
	RecordMetric(context.Context, *RecordMetricRequest) (*Empty, error)
	RecordMetrics(context.Context, *RecordMetricsRequest) (*Empty, error)
	// Metric retrieval operations
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
	GetUserAnalytics(context.Context, *GetUserAnalyticsRequest) (*UserAnalytics, error)
	GetWorkspaceAnalytics(context.Context, *GetWorkspaceAnalyticsRequest) (*WorkspaceAnalytics, error)
	// Security monitoring operations
	RecordSecurityEvent(context.Context, *RecordSecurityEventRequest) (*Empty, error)
	GetSecurityEvents(context.Context, *GetSecurityEventsRequest) (*GetSecurityEventsResponse, error)
	GetSecurityMetrics(context.Context, *GetSecurityMetricsRequest) (*SecurityMetrics, error)
	// Dashboard operations
	CreateDashboard(context.Context, *CreateDashboardRequest) (*Dashboard, error)
	GetDashboard(context.Context, *GetDashboardRequest) (*Dashboard, error)
	UpdateDashboard(context.Context, *UpdateDashboardRequest) (*Dashboard, error)
	ListDashboards(context.Context, *ListDashboardsRequest) (*ListDashboardsResponse, error)
	GetDashboardData(context.Context, *GetDashboardDataRequest) (*DashboardData, error)
	DeleteDashboard(context.Context, *DeleteDashboardRequest) (*Empty, error)
	mustEmbedUnimplementedAnalyticsServiceServer()
}

// UnimplementedAnalyticsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAnalyticsServiceServer struct {
}

func (UnimplementedAnalyticsServiceServer) RecordMetric(context.Context, *RecordMetricRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordMetric not implemented")
}
func (UnimplementedAnalyticsServiceServer) RecordMetrics(context.Context, *RecordMetricsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetUserAnalytics(context.Context, *GetUserAnalyticsRequest) (*UserAnalytics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAnalytics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetWorkspaceAnalytics(context.Context, *GetWorkspaceAnalyticsRequest) (*WorkspaceAnalytics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkspaceAnalytics not implemented")
}
func (UnimplementedAnalyticsServiceServer) RecordSecurityEvent(context.Context, *RecordSecurityEventRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordSecurityEvent not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetSecurityEvents(context.Context, *GetSecurityEventsRequest) (*GetSecurityEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityEvents not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetSecurityMetrics(context.Context, *GetSecurityMetricsRequest) (*SecurityMetrics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) CreateDashboard(context.Context, *CreateDashboardRequest) (*Dashboard, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetDashboard(context.Context, *GetDashboardRequest) (*Dashboard, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) UpdateDashboard(context.Context, *UpdateDashboardRequest) (*Dashboard, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) ListDashboards(context.Context, *ListDashboardsRequest) (*ListDashboardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDashboards not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetDashboardData(context.Context, *GetDashboardDataRequest) (*DashboardData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDashboardData not implemented")
}
func (UnimplementedAnalyticsServiceServer) DeleteDashboard(context.Context, *DeleteDashboardRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDashboard not implemented")
}
func (UnimplementedAnalyticsServiceServer) mustEmbedUnimplementedAnalyticsServiceServer() {}

// UnsafeAnalyticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticsServiceServer will
// result in compilation errors.
type UnsafeAnalyticsServiceServer interface {
	mustEmbedUnimplementedAnalyticsServiceServer()
}

func RegisterAnalyticsServiceServer(s grpc.ServiceRegistrar, srv AnalyticsServiceServer) {
	s.RegisterService(&AnalyticsService_ServiceDesc, srv)
}

func _AnalyticsService_RecordMetric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordMetricRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).RecordMetric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/RecordMetric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).RecordMetric(ctx, req.(*RecordMetricRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_RecordMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).RecordMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/RecordMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).RecordMetrics(ctx, req.(*RecordMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetUserAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetUserAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetUserAnalytics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetUserAnalytics(ctx, req.(*GetUserAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetWorkspaceAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkspaceAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetWorkspaceAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetWorkspaceAnalytics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetWorkspaceAnalytics(ctx, req.(*GetWorkspaceAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_RecordSecurityEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSecurityEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).RecordSecurityEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/RecordSecurityEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).RecordSecurityEvent(ctx, req.(*RecordSecurityEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetSecurityEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetSecurityEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetSecurityEvents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetSecurityEvents(ctx, req.(*GetSecurityEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetSecurityMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetSecurityMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetSecurityMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetSecurityMetrics(ctx, req.(*GetSecurityMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_CreateDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).CreateDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/CreateDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).CreateDashboard(ctx, req.(*CreateDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetDashboard(ctx, req.(*GetDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_UpdateDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).UpdateDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/UpdateDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).UpdateDashboard(ctx, req.(*UpdateDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_ListDashboards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDashboardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).ListDashboards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/ListDashboards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).ListDashboards(ctx, req.(*ListDashboardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetDashboardData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDashboardDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetDashboardData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/GetDashboardData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetDashboardData(ctx, req.(*GetDashboardDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_DeleteDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).DeleteDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/analytics.v1.AnalyticsService/DeleteDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).DeleteDashboard(ctx, req.(*DeleteDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticsService_ServiceDesc is the grpc.ServiceDesc for AnalyticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "analytics.v1.AnalyticsService",
	HandlerType: (*AnalyticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordMetric",
			Handler:    _AnalyticsService_RecordMetric_Handler,
		},
		{
			MethodName: "RecordMetrics",
			Handler:    _AnalyticsService_RecordMetrics_Handler,
		},
		{
			MethodName: "GetMetrics",
			Handler:    _AnalyticsService_GetMetrics_Handler,
		},
		{
			MethodName: "GetUserAnalytics",
			Handler:    _AnalyticsService_GetUserAnalytics_Handler,
		},
		{
			MethodName: "GetWorkspaceAnalytics",
			Handler:    _AnalyticsService_GetWorkspaceAnalytics_Handler,
		},
		{
			MethodName: "RecordSecurityEvent",
			Handler:    _AnalyticsService_RecordSecurityEvent_Handler,
		},
		{
			MethodName: "GetSecurityEvents",
			Handler:    _AnalyticsService_GetSecurityEvents_Handler,
		},
		{
			MethodName: "GetSecurityMetrics",
			Handler:    _AnalyticsService_GetSecurityMetrics_Handler,
		},
		{
			MethodName: "CreateDashboard",
			Handler:    _AnalyticsService_CreateDashboard_Handler,
		},
		{
			MethodName: "GetDashboard",
			Handler:    _AnalyticsService_GetDashboard_Handler,
		},
		{
			MethodName: "UpdateDashboard",
			Handler:    _AnalyticsService_UpdateDashboard_Handler,
		},
		{
			MethodName: "ListDashboards",
			Handler:    _AnalyticsService_ListDashboards_Handler,
		},
		{
			MethodName: "GetDashboardData",
			Handler:    _AnalyticsService_GetDashboardData_Handler,
		},
		{
			MethodName: "DeleteDashboard",
			Handler:    _AnalyticsService_DeleteDashboard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "analytics/v1/analytics.proto",
}

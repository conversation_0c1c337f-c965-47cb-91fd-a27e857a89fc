// Code generated by ent, DO NOT EDIT.

package order

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the order type in the database.
	Label = "order"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldOrderNumber holds the string denoting the order_number field in the database.
	FieldOrderNumber = "order_number"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldPackageID holds the string denoting the package_id field in the database.
	FieldPackageID = "package_id"
	// FieldPlanID holds the string denoting the plan_id field in the database.
	FieldPlanID = "plan_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldPaymentMethod holds the string denoting the payment_method field in the database.
	FieldPaymentMethod = "payment_method"
	// FieldPaymentID holds the string denoting the payment_id field in the database.
	FieldPaymentID = "payment_id"
	// FieldInvoiceID holds the string denoting the invoice_id field in the database.
	FieldInvoiceID = "invoice_id"
	// FieldCredits holds the string denoting the credits field in the database.
	FieldCredits = "credits"
	// FieldBillingAddress holds the string denoting the billing_address field in the database.
	FieldBillingAddress = "billing_address"
	// FieldItems holds the string denoting the items field in the database.
	FieldItems = "items"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldProcessedAt holds the string denoting the processed_at field in the database.
	FieldProcessedAt = "processed_at"
	// FieldExpiresAt holds the string denoting the expires_at field in the database.
	FieldExpiresAt = "expires_at"
	// FieldFailureReason holds the string denoting the failure_reason field in the database.
	FieldFailureReason = "failure_reason"
	// FieldExternalOrderID holds the string denoting the external_order_id field in the database.
	FieldExternalOrderID = "external_order_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the order in the database.
	Table = "orders"
)

// Columns holds all SQL columns for order fields.
var Columns = []string{
	FieldID,
	FieldOrderNumber,
	FieldUserID,
	FieldPackageID,
	FieldPlanID,
	FieldType,
	FieldAmount,
	FieldCurrency,
	FieldStatus,
	FieldPaymentMethod,
	FieldPaymentID,
	FieldInvoiceID,
	FieldCredits,
	FieldBillingAddress,
	FieldItems,
	FieldMetadata,
	FieldProcessedAt,
	FieldExpiresAt,
	FieldFailureReason,
	FieldExternalOrderID,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// OrderNumberValidator is a validator for the "order_number" field. It is called by the builders before save.
	OrderNumberValidator func(string) error
	// AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	AmountValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	PaymentMethodValidator func(string) error
	// PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	PaymentIDValidator func(string) error
	// InvoiceIDValidator is a validator for the "invoice_id" field. It is called by the builders before save.
	InvoiceIDValidator func(string) error
	// DefaultCredits holds the default value on creation for the "credits" field.
	DefaultCredits int
	// CreditsValidator is a validator for the "credits" field. It is called by the builders before save.
	CreditsValidator func(int) error
	// FailureReasonValidator is a validator for the "failure_reason" field. It is called by the builders before save.
	FailureReasonValidator func(string) error
	// ExternalOrderIDValidator is a validator for the "external_order_id" field. It is called by the builders before save.
	ExternalOrderIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Type defines the type for the "type" enum field.
type Type string

// TypeCreditPurchase is the default value of the Type enum.
const DefaultType = TypeCreditPurchase

// Type values.
const (
	TypeCreditPurchase Type = "credit_purchase"
	TypeSubscription   Type = "subscription"
	TypeUpgrade        Type = "upgrade"
	TypeDowngrade      Type = "downgrade"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case TypeCreditPurchase, TypeSubscription, TypeUpgrade, TypeDowngrade:
		return nil
	default:
		return fmt.Errorf("order: invalid enum value for type field: %q", _type)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending    Status = "pending"
	StatusProcessing Status = "processing"
	StatusCompleted  Status = "completed"
	StatusFailed     Status = "failed"
	StatusCancelled  Status = "cancelled"
	StatusRefunded   Status = "refunded"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusProcessing, StatusCompleted, StatusFailed, StatusCancelled, StatusRefunded:
		return nil
	default:
		return fmt.Errorf("order: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Order queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByOrderNumber orders the results by the order_number field.
func ByOrderNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderNumber, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByPackageID orders the results by the package_id field.
func ByPackageID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPackageID, opts...).ToFunc()
}

// ByPlanID orders the results by the plan_id field.
func ByPlanID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByPaymentMethod orders the results by the payment_method field.
func ByPaymentMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentMethod, opts...).ToFunc()
}

// ByPaymentID orders the results by the payment_id field.
func ByPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentID, opts...).ToFunc()
}

// ByInvoiceID orders the results by the invoice_id field.
func ByInvoiceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInvoiceID, opts...).ToFunc()
}

// ByCredits orders the results by the credits field.
func ByCredits(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCredits, opts...).ToFunc()
}

// ByProcessedAt orders the results by the processed_at field.
func ByProcessedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProcessedAt, opts...).ToFunc()
}

// ByExpiresAt orders the results by the expires_at field.
func ByExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresAt, opts...).ToFunc()
}

// ByFailureReason orders the results by the failure_reason field.
func ByFailureReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailureReason, opts...).ToFunc()
}

// ByExternalOrderID orders the results by the external_order_id field.
func ByExternalOrderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExternalOrderID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

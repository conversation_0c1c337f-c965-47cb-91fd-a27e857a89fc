package handlers

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/integration-service/usecase/integration"
	"github.com/social-content-ai/integration-service/usecase/platform"
	"github.com/social-content-ai/integration-service/usecase/publishing"
	"github.com/social-content-ai/pkg-shared/logging"
	integrationv1 "github.com/social-content-ai/proto-shared/integration/v1"
)

// IntegrationHandler implements the gRPC PlatformIntegrationService
type IntegrationHandler struct {
	integrationv1.UnimplementedPlatformIntegrationServiceServer
	integrationUseCase IntegrationUseCase
	platformUseCase    PlatformUseCase
	publishingUseCase  PublishingUseCase
	logger             logging.Logger
}

// IntegrationUseCase defines the integration business logic interface
type IntegrationUseCase interface {
	GetOAuthURL(ctx context.Context, req *integration.GetOAuthURLRequest) (*integration.GetOAuthURLResponse, error)
	ConnectAccount(ctx context.Context, req *integration.ConnectAccountRequest) (*integration.ConnectAccountResponse, error)
	RefreshToken(ctx context.Context, req *integration.RefreshTokenRequest) (*integration.RefreshTokenResponse, error)
	DisconnectAccount(ctx context.Context, req *integration.DisconnectAccountRequest) error
	ListAccounts(ctx context.Context, req *integration.ListAccountsRequest) (*integration.ListAccountsResponse, error)
	ValidateAccount(ctx context.Context, req *integration.ValidateAccountRequest) (*integration.ValidateAccountResponse, error)
}

// PlatformUseCase defines the platform-specific operations interface
type PlatformUseCase interface {
	GetPlatformLimits(ctx context.Context, req *platform.GetPlatformLimitsRequest) (*platform.GetPlatformLimitsResponse, error)
	ProcessWebhook(ctx context.Context, req *platform.ProcessWebhookRequest) (*platform.ProcessWebhookResponse, error)
}

// PublishingUseCase defines the publishing operations interface
type PublishingUseCase interface {
	PublishPost(ctx context.Context, req *publishing.PublishPostRequest) (*publishing.PublishPostResponse, error)
	GetPostAnalytics(ctx context.Context, req *publishing.GetPostAnalyticsRequest) (*publishing.GetPostAnalyticsResponse, error)
	GetAccountAnalytics(ctx context.Context, req *publishing.GetAccountAnalyticsRequest) (*publishing.GetAccountAnalyticsResponse, error)
}

// NewIntegrationHandler creates a new integration handler
func NewIntegrationHandler(
	integrationUseCase IntegrationUseCase,
	platformUseCase PlatformUseCase,
	publishingUseCase PublishingUseCase,
	logger logging.Logger,
) *IntegrationHandler {
	return &IntegrationHandler{
		integrationUseCase: integrationUseCase,
		platformUseCase:    platformUseCase,
		publishingUseCase:  publishingUseCase,
		logger:             logger,
	}
}

// GetOAuthUrl generates OAuth authorization URL
func (h *IntegrationHandler) GetOAuthUrl(ctx context.Context, req *integrationv1.GetOAuthUrlRequest) (*integrationv1.GetOAuthUrlResponse, error) {
	h.logger.WithField("platform", req.Platform).Info("gRPC GetOAuthUrl called")

	// Convert proto request to use case request
	useCaseReq := &integration.GetOAuthURLRequest{
		UserID:               req.UserId,
		Platform:             req.Platform,
		RedirectURI:          req.RedirectUri,
		RequestedPermissions: req.RequestedPermissions,
		State:                req.State,
	}

	// Call use case
	response, err := h.integrationUseCase.GetOAuthURL(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get OAuth URL")
		return nil, status.Errorf(codes.Internal, "failed to get OAuth URL: %v", err)
	}

	// Convert response to proto
	return &integrationv1.GetOAuthUrlResponse{
		AuthUrl:     response.AuthURL,
		State:       response.State,
		Permissions: response.Permissions,
	}, nil
}

// ConnectAccount connects a social media account
func (h *IntegrationHandler) ConnectAccount(ctx context.Context, req *integrationv1.ConnectAccountRequest) (*integrationv1.ConnectAccountResponse, error) {
	h.logger.WithField("platform", req.Platform).Info("gRPC ConnectAccount called")

	// Convert proto request to use case request
	useCaseReq := &integration.ConnectAccountRequest{
		UserID:               req.UserId,
		WorkspaceID:          req.WorkspaceId,
		Platform:             req.Platform,
		OAuthCode:            req.OauthCode,
		RedirectURI:          req.RedirectUri,
		RequestedPermissions: req.RequestedPermissions,
	}

	// Call use case
	response, err := h.integrationUseCase.ConnectAccount(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to connect account")
		return nil, status.Errorf(codes.Internal, "failed to connect account: %v", err)
	}

	// Convert response to proto
	protoResponse := &integrationv1.ConnectAccountResponse{
		AccountId:    response.AccountID,
		Success:      response.Success,
		ErrorMessage: response.ErrorMessage,
	}

	if response.Account != nil {
		protoResponse.Account = convertPlatformAccountToProto(response.Account)
	}

	return protoResponse, nil
}

// RefreshToken refreshes access token for an account
func (h *IntegrationHandler) RefreshToken(ctx context.Context, req *integrationv1.RefreshTokenRequest) (*integrationv1.RefreshTokenResponse, error) {
	h.logger.WithField("account_id", req.AccountId).Info("gRPC RefreshToken called")

	// Convert proto request to use case request
	useCaseReq := &integration.RefreshTokenRequest{
		AccountID:    req.AccountId,
		UserID:       req.UserId,
		ForceRefresh: req.ForceRefresh,
	}

	// Call use case
	response, err := h.integrationUseCase.RefreshToken(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to refresh token")
		return nil, status.Errorf(codes.Internal, "failed to refresh token: %v", err)
	}

	// Convert response to proto
	protoResponse := &integrationv1.RefreshTokenResponse{
		Success:      response.Success,
		ErrorMessage: response.ErrorMessage,
	}

	if response.NewExpiresAt > 0 {
		protoResponse.NewExpiresAt = timestampFromUnix(response.NewExpiresAt)
	}

	return protoResponse, nil
}

// DisconnectAccount disconnects a social media account
func (h *IntegrationHandler) DisconnectAccount(ctx context.Context, req *integrationv1.DisconnectAccountRequest) (*integrationv1.Empty, error) {
	h.logger.WithField("account_id", req.AccountId).Info("gRPC DisconnectAccount called")

	// Convert proto request to use case request
	useCaseReq := &integration.DisconnectAccountRequest{
		AccountID:   req.AccountId,
		UserID:      req.UserId,
		RevokeToken: req.RevokeToken,
	}

	// Call use case
	err := h.integrationUseCase.DisconnectAccount(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to disconnect account")
		return nil, status.Errorf(codes.Internal, "failed to disconnect account: %v", err)
	}

	return &integrationv1.Empty{}, nil
}

// ListAccounts lists connected accounts for a user
func (h *IntegrationHandler) ListAccounts(ctx context.Context, req *integrationv1.ListAccountsRequest) (*integrationv1.ListAccountsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC ListAccounts called")

	// Convert proto request to use case request
	useCaseReq := &integration.ListAccountsRequest{
		UserID:      req.UserId,
		WorkspaceID: req.WorkspaceId,
		Platform:    req.Platform,
		Status:      req.Status,
	}

	if req.Pagination != nil {
		useCaseReq.Limit = req.Pagination.Limit
		// Calculate offset from page (page is 1-based)
		if req.Pagination.Page > 0 {
			useCaseReq.Offset = (req.Pagination.Page - 1) * req.Pagination.Limit
		}
	}

	// Call use case
	response, err := h.integrationUseCase.ListAccounts(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list accounts")
		return nil, status.Errorf(codes.Internal, "failed to list accounts: %v", err)
	}

	// Convert response to proto
	protoAccounts := make([]*integrationv1.PlatformAccount, len(response.Accounts))
	for i, account := range response.Accounts {
		protoAccounts[i] = convertPlatformAccountToProto(account)
	}

	return &integrationv1.ListAccountsResponse{
		Accounts: protoAccounts,
		// Note: Pagination will be handled by the proto definition
	}, nil
}

// ValidateAccount validates account status and permissions
func (h *IntegrationHandler) ValidateAccount(ctx context.Context, req *integrationv1.ValidateAccountRequest) (*integrationv1.ValidateAccountResponse, error) {
	h.logger.WithField("account_id", req.AccountId).Info("gRPC ValidateAccount called")

	// Convert proto request to use case request
	useCaseReq := &integration.ValidateAccountRequest{
		AccountID:        req.AccountId,
		UserID:           req.UserId,
		CheckPermissions: req.CheckPermissions,
	}

	// Call use case
	response, err := h.integrationUseCase.ValidateAccount(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate account")
		return nil, status.Errorf(codes.Internal, "failed to validate account: %v", err)
	}

	// Convert response to proto
	protoResponse := &integrationv1.ValidateAccountResponse{
		IsValid:            response.IsValid,
		TokenExpired:       response.TokenExpired,
		MissingPermissions: response.MissingPermissions,
		ErrorMessage:       response.ErrorMessage,
	}

	if response.LastValidated > 0 {
		protoResponse.LastValidated = timestampFromUnix(response.LastValidated)
	}

	return protoResponse, nil
}

// PublishPost publishes content to a platform
func (h *IntegrationHandler) PublishPost(ctx context.Context, req *integrationv1.PublishPostRequest) (*integrationv1.PublishPostResponse, error) {
	h.logger.WithField("account_id", req.AccountId).Info("gRPC PublishPost called")

	// Convert proto request to use case request
	useCaseReq := &publishing.PublishPostRequest{
		AccountID:       req.AccountId,
		UserID:          req.UserId,
		PostID:          req.PostId,
		Content:         req.Content,
		ImageURLs:       req.ImageUrls,
		Hashtags:        req.Hashtags,
		PlatformOptions: convertStringMapToInterface(req.PlatformOptions),
	}

	if req.ScheduledAt != nil {
		useCaseReq.ScheduledAt = req.ScheduledAt.Seconds
	}

	// Call use case
	response, err := h.publishingUseCase.PublishPost(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish post")
		return nil, status.Errorf(codes.Internal, "failed to publish post: %v", err)
	}

	// Convert response to proto
	protoResponse := &integrationv1.PublishPostResponse{
		PlatformPostId: response.PlatformPostID,
		PlatformUrl:    response.PlatformURL,
		Success:        response.Success,
		ErrorMessage:   response.ErrorMessage,
	}

	if response.PublishedAt > 0 {
		protoResponse.PublishedAt = timestampFromUnix(response.PublishedAt)
	}

	return protoResponse, nil
}

// GetPostAnalytics retrieves analytics for a published post
func (h *IntegrationHandler) GetPostAnalytics(ctx context.Context, req *integrationv1.GetPostAnalyticsRequest) (*integrationv1.PostAnalytics, error) {
	h.logger.WithField("platform_post_id", req.PlatformPostId).Info("gRPC GetPostAnalytics called")

	// Convert proto request to use case request
	useCaseReq := &publishing.GetPostAnalyticsRequest{
		AccountID:      req.AccountId,
		PlatformPostID: req.PlatformPostId,
		UserID:         req.UserId,
		Metrics:        req.Metrics,
	}

	// Call use case
	response, err := h.publishingUseCase.GetPostAnalytics(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post analytics")
		return nil, status.Errorf(codes.Internal, "failed to get post analytics: %v", err)
	}

	// Convert response to proto
	protoResponse := &integrationv1.PostAnalytics{
		PlatformPostId: response.PlatformPostID,
		Platform:       response.Platform,
		Metrics:        response.Metrics,
	}

	if response.CollectedAt > 0 {
		protoResponse.CollectedAt = timestampFromUnix(response.CollectedAt)
	}

	if response.PostPublishedAt > 0 {
		protoResponse.PostPublishedAt = timestampFromUnix(response.PostPublishedAt)
	}

	return protoResponse, nil
}

// GetAccountAnalytics retrieves analytics for an account
func (h *IntegrationHandler) GetAccountAnalytics(ctx context.Context, req *integrationv1.GetAccountAnalyticsRequest) (*integrationv1.AccountAnalytics, error) {
	h.logger.WithField("account_id", req.AccountId).Info("gRPC GetAccountAnalytics called")

	// Convert proto request to use case request
	useCaseReq := &publishing.GetAccountAnalyticsRequest{
		AccountID: req.AccountId,
		UserID:    req.UserId,
		DateFrom:  req.DateFrom,
		DateTo:    req.DateTo,
		Metrics:   req.Metrics,
	}

	// Call use case
	response, err := h.publishingUseCase.GetAccountAnalytics(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get account analytics")
		return nil, status.Errorf(codes.Internal, "failed to get account analytics: %v", err)
	}

	// Convert response to proto
	protoTimeSeries := make(map[string]*integrationv1.TimeSeriesValues)
	for metric, values := range response.TimeSeries {
		protoTimeSeries[metric] = &integrationv1.TimeSeriesValues{
			Values: values,
		}
	}

	protoResponse := &integrationv1.AccountAnalytics{
		AccountId:  response.AccountID,
		Platform:   response.Platform,
		Metrics:    response.Metrics,
		TimeSeries: protoTimeSeries,
	}

	if response.CollectedAt > 0 {
		protoResponse.CollectedAt = timestampFromUnix(response.CollectedAt)
	}

	return protoResponse, nil
}

// GetPlatformLimits retrieves platform rate limits
func (h *IntegrationHandler) GetPlatformLimits(ctx context.Context, req *integrationv1.GetPlatformLimitsRequest) (*integrationv1.PlatformLimits, error) {
	h.logger.WithField("platform", req.Platform).Info("gRPC GetPlatformLimits called")

	// Convert proto request to use case request
	useCaseReq := &platform.GetPlatformLimitsRequest{
		Platform:  req.Platform,
		AccountID: req.AccountId,
	}

	// Call use case
	response, err := h.platformUseCase.GetPlatformLimits(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get platform limits")
		return nil, status.Errorf(codes.Internal, "failed to get platform limits: %v", err)
	}

	// Convert response to proto
	protoResetTimes := make(map[string]*timestamppb.Timestamp)
	for operation, resetTime := range response.ResetTimes {
		protoResetTimes[operation] = timestampFromUnix(resetTime)
	}

	return &integrationv1.PlatformLimits{
		Platform:     response.Platform,
		RateLimits:   response.RateLimits,
		CurrentUsage: response.CurrentUsage,
		ResetTimes:   protoResetTimes,
	}, nil
}

// ProcessWebhook processes webhook events from platforms
func (h *IntegrationHandler) ProcessWebhook(ctx context.Context, req *integrationv1.ProcessWebhookRequest) (*integrationv1.ProcessWebhookResponse, error) {
	h.logger.WithField("platform", req.Platform).Info("gRPC ProcessWebhook called")

	// Convert proto request to use case request
	useCaseReq := &platform.ProcessWebhookRequest{
		Platform:  req.Platform,
		Signature: req.Signature,
		Payload:   req.Payload,
		Headers:   req.Headers,
	}

	// Call use case
	response, err := h.platformUseCase.ProcessWebhook(ctx, useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process webhook")
		return nil, status.Errorf(codes.Internal, "failed to process webhook: %v", err)
	}

	// Convert response to proto
	return &integrationv1.ProcessWebhookResponse{
		Success:      response.Success,
		EventId:      response.EventID,
		EventType:    response.EventType,
		ErrorMessage: response.ErrorMessage,
	}, nil
}

// Helper functions

// timestampFromUnix converts Unix timestamp to protobuf timestamp
func timestampFromUnix(unixTime int64) *timestamppb.Timestamp {
	return timestamppb.New(time.Unix(unixTime, 0))
}

// convertStringMapToInterface converts map[string]string to map[string]interface{}
func convertStringMapToInterface(input map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range input {
		result[k] = v
	}
	return result
}

// convertPlatformAccountToProto converts domain model to proto
func convertPlatformAccountToProto(account *integration.PlatformAccount) *integrationv1.PlatformAccount {
	protoAccount := &integrationv1.PlatformAccount{
		Id:          account.ID,
		UserId:      account.UserID,
		WorkspaceId: account.WorkspaceID,
		Platform:    account.Platform,
		AccountId:   account.AccountID,
		Username:    account.Username,
		DisplayName: account.DisplayName,
		AvatarUrl:   account.AvatarURL,
		Status:      account.Status,
		Permissions: account.Permissions,
		Metadata:    account.Metadata,
	}

	if account.ConnectedAt > 0 {
		protoAccount.ConnectedAt = timestampFromUnix(account.ConnectedAt)
	}

	if account.LastUsed > 0 {
		protoAccount.LastUsed = timestampFromUnix(account.LastUsed)
	}

	return protoAccount
}

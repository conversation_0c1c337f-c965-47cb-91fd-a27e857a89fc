syntax = "proto3";

package content_mgmt.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";
import "common/v1/timestamp.proto";
import "content-mgmt/v1/post.proto";

option go_package = "github.com/social-content-ai/proto-shared/content-mgmt/v1;contentmgmtv1";

// Template represents a content template
message Template {
  string id = 1;
  string user_id = 2;
  string name = 3;
  string description = 4;
  string content = 5;
  string category = 6;
  repeated string platforms = 7;
  string prompt = 8;
  bool is_public = 9;
  string type = 10;          // my, marketplace, premium
  string thumbnail_url = 11;
  repeated string rag_files = 12;
  repeated string variable_placeholders = 13;
  bool is_verified = 14;
  bool is_featured = 15;
  bool is_premium = 16;
  int32 credit_cost = 17;
  float rating = 18;
  int32 download_count = 19;
  common.v1.TimestampInfo timestamps = 20;
}

// CreateTemplateRequest for creating a new template
message CreateTemplateRequest {
  string user_id = 1;
  string name = 2;
  string description = 3;
  string content = 4;
  string category = 5;
  repeated string platforms = 6;
  string prompt = 7;
  bool is_public = 8;
  string thumbnail_url = 9;
  repeated string rag_files = 10;
  repeated string variable_placeholders = 11;
  bool is_premium = 12;
  int32 credit_cost = 13;
}

// UpdateTemplateRequest for updating a template
message UpdateTemplateRequest {
  string id = 1;
  optional string name = 2;
  optional string description = 3;
  optional string content = 4;
  optional string category = 5;
  repeated string platforms = 6;
  optional string prompt = 7;
  optional bool is_public = 8;
  optional string thumbnail_url = 9;
  repeated string rag_files = 10;
  repeated string variable_placeholders = 11;
  optional int32 credit_cost = 12;
}

// GetTemplateRequest for retrieving a template
message GetTemplateRequest {
  string id = 1;
}

// ListTemplatesRequest for listing templates
message ListTemplatesRequest {
  string user_id = 1;        // Optional: filter by user
  common.v1.PaginationRequest pagination = 2;
  string type = 3;           // Filter by type
  string category = 4;       // Filter by category
  repeated string platforms = 5; // Filter by platforms
  bool public_only = 6;      // Only public templates
  bool premium_only = 7;     // Only premium templates
  string search = 8;         // Search query
}

// ListTemplatesResponse with paginated templates
message ListTemplatesResponse {
  repeated Template templates = 1;
  common.v1.PaginationResponse pagination = 2;
}

// UseTemplateRequest for using a template
message UseTemplateRequest {
  string id = 1;
  string user_id = 2;
  map<string, string> variables = 3; // Variable values
}

// UseTemplateResponse with processed template
message UseTemplateResponse {
  string processed_content = 1;
  string processed_prompt = 2;
  int32 credits_charged = 3;
}

// RateTemplateRequest for rating a template
message RateTemplateRequest {
  string id = 1;
  string user_id = 2;
  int32 rating = 3;          // 1-5 stars
  string review = 4;         // Optional review text
}

// TemplateRating represents a template rating
message TemplateRating {
  string id = 1;
  string template_id = 2;
  string user_id = 3;
  int32 rating = 4;          // 1-5 stars
  string review = 5;
  google.protobuf.Timestamp created_at = 6;
}

// GetTemplateRatingsRequest for getting template ratings
message GetTemplateRatingsRequest {
  string template_id = 1;
  common.v1.PaginationRequest pagination = 2;
  int32 min_rating = 3;      // Filter by minimum rating
}

// GetTemplateRatingsResponse with paginated ratings
message GetTemplateRatingsResponse {
  repeated TemplateRating ratings = 1;
  common.v1.PaginationResponse pagination = 2;
  float average_rating = 3;
  int32 total_ratings = 4;
  map<int32, int32> rating_distribution = 5; // rating -> count
}

// PurchaseTemplateRequest for purchasing a premium template
message PurchaseTemplateRequest {
  string template_id = 1;
  string user_id = 2;
  string workspace_id = 3;   // Optional workspace context
  bool validate_credits = 4; // Whether to validate credits first
}

// PurchaseTemplateResponse with purchase result
message PurchaseTemplateResponse {
  string purchase_id = 1;
  string template_id = 2;
  int32 credits_charged = 3;
  string transaction_id = 4; // Credit transaction reference
  google.protobuf.Timestamp purchased_at = 5;
  Template template = 6;     // Full template access
}

// GetPurchasedTemplatesRequest for listing purchased templates
message GetPurchasedTemplatesRequest {
  string user_id = 1;
  string workspace_id = 2;   // Optional workspace filter
  common.v1.PaginationRequest pagination = 3;
  string category = 4;       // Filter by category
}

// GetPurchasedTemplatesResponse with purchased templates
message GetPurchasedTemplatesResponse {
  repeated Template templates = 1;
  common.v1.PaginationResponse pagination = 2;
  repeated PurchaseInfo purchases = 3; // Purchase information
}

// PurchaseInfo contains purchase details
message PurchaseInfo {
  string purchase_id = 1;
  string template_id = 2;
  int32 credits_charged = 3;
  google.protobuf.Timestamp purchased_at = 4;
}

// TemplateRevenue represents template revenue information
message TemplateRevenue {
  string template_id = 1;
  string template_name = 2;
  int32 total_purchases = 3;
  int32 total_revenue = 4;   // Total credits earned
  int32 monthly_purchases = 5;
  int32 monthly_revenue = 6;
  float average_rating = 7;
  google.protobuf.Timestamp last_purchase = 8;
}

// GetTemplateRevenueRequest for getting template revenue
message GetTemplateRevenueRequest {
  string user_id = 1;        // Template creator
  string template_id = 2;    // Optional specific template
  string date_from = 3;      // Format: YYYY-MM-DD
  string date_to = 4;        // Format: YYYY-MM-DD
  common.v1.PaginationRequest pagination = 5;
}

// GetTemplateRevenueResponse with revenue data
message GetTemplateRevenueResponse {
  repeated TemplateRevenue revenues = 1;
  common.v1.PaginationResponse pagination = 2;
  int32 total_revenue = 3;   // Total credits earned
  int32 total_purchases = 4; // Total purchases across all templates
}

// DeleteTemplateRequest for deleting a template
message DeleteTemplateRequest {
  string id = 1;
}

// TemplateService provides template management operations
service TemplateService {
  // Basic template operations
  rpc CreateTemplate(CreateTemplateRequest) returns (Template);
  rpc GetTemplate(GetTemplateRequest) returns (Template);
  rpc UpdateTemplate(UpdateTemplateRequest) returns (Template);
  rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse);
  rpc UseTemplate(UseTemplateRequest) returns (UseTemplateResponse);
  rpc DeleteTemplate(DeleteTemplateRequest) returns (Empty);

  // Rating and review operations
  rpc RateTemplate(RateTemplateRequest) returns (Empty);
  rpc GetTemplateRatings(GetTemplateRatingsRequest) returns (GetTemplateRatingsResponse);

  // Marketplace operations
  rpc PurchaseTemplate(PurchaseTemplateRequest) returns (PurchaseTemplateResponse);
  rpc GetPurchasedTemplates(GetPurchasedTemplatesRequest) returns (GetPurchasedTemplatesResponse);

  // Revenue tracking operations
  rpc GetTemplateRevenue(GetTemplateRevenueRequest) returns (GetTemplateRevenueResponse);
}

package kafka

import (
	"github.com/social-content-ai/pkg-shared/kafka"
)

// Re-export pkg-shared Kafka types for convenience
type (
	Config           = kafka.Config
	ConsumerConfig   = kafka.ConsumerConfig
	ProducerConfig   = kafka.ProducerConfig
	TopicConfig      = kafka.TopicConfig
	SecurityConfig   = kafka.SecurityConfig
	Consumer         = kafka.Consumer
	Producer         = kafka.Producer
	MessageValidator = kafka.MessageValidator
)

// Re-export functions from pkg-shared
var (
	NewDefaultConfig = kafka.NewDefaultConfig
	NewConsumer      = kafka.NewConsumer
	NewProducer      = kafka.NewProducer
)

// NewNotificationConfig creates a notification service specific Kafka configuration
func NewNotificationConfig() *Config {
	config := NewDefaultConfig()

	// Customize for notification service
	config.Consumer.GroupID = "notification-service"
	config.Topics.NotificationEvents = "notification.events"
	config.Topics.DeadLetterQueue = "notification.dlq"
	config.Topics.RetryQueue = "notification.retry"

	return config
}

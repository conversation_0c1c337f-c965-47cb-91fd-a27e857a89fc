// Code generated by ent, DO NOT EDIT.

package platformintegration

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the platformintegration type in the database.
	Label = "platform_integration"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldWorkspaceID holds the string denoting the workspace_id field in the database.
	FieldWorkspaceID = "workspace_id"
	// FieldPlatform holds the string denoting the platform field in the database.
	FieldPlatform = "platform"
	// FieldPlatformUserID holds the string denoting the platform_user_id field in the database.
	FieldPlatformUserID = "platform_user_id"
	// FieldPlatformUsername holds the string denoting the platform_username field in the database.
	FieldPlatformUsername = "platform_username"
	// FieldPlatformDisplayName holds the string denoting the platform_display_name field in the database.
	FieldPlatformDisplayName = "platform_display_name"
	// FieldPlatformAvatarURL holds the string denoting the platform_avatar_url field in the database.
	FieldPlatformAvatarURL = "platform_avatar_url"
	// FieldAccessToken holds the string denoting the access_token field in the database.
	FieldAccessToken = "access_token"
	// FieldRefreshToken holds the string denoting the refresh_token field in the database.
	FieldRefreshToken = "refresh_token"
	// FieldTokenExpiresAt holds the string denoting the token_expires_at field in the database.
	FieldTokenExpiresAt = "token_expires_at"
	// FieldScopes holds the string denoting the scopes field in the database.
	FieldScopes = "scopes"
	// FieldPlatformData holds the string denoting the platform_data field in the database.
	FieldPlatformData = "platform_data"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldErrorMessage holds the string denoting the error_message field in the database.
	FieldErrorMessage = "error_message"
	// FieldLastSyncAt holds the string denoting the last_sync_at field in the database.
	FieldLastSyncAt = "last_sync_at"
	// FieldLastPostAt holds the string denoting the last_post_at field in the database.
	FieldLastPostAt = "last_post_at"
	// FieldCapabilities holds the string denoting the capabilities field in the database.
	FieldCapabilities = "capabilities"
	// FieldSettings holds the string denoting the settings field in the database.
	FieldSettings = "settings"
	// FieldAutoPublish holds the string denoting the auto_publish field in the database.
	FieldAutoPublish = "auto_publish"
	// FieldIsPrimary holds the string denoting the is_primary field in the database.
	FieldIsPrimary = "is_primary"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the platformintegration in the database.
	Table = "platform_integrations"
)

// Columns holds all SQL columns for platformintegration fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldWorkspaceID,
	FieldPlatform,
	FieldPlatformUserID,
	FieldPlatformUsername,
	FieldPlatformDisplayName,
	FieldPlatformAvatarURL,
	FieldAccessToken,
	FieldRefreshToken,
	FieldTokenExpiresAt,
	FieldScopes,
	FieldPlatformData,
	FieldStatus,
	FieldErrorMessage,
	FieldLastSyncAt,
	FieldLastPostAt,
	FieldCapabilities,
	FieldSettings,
	FieldAutoPublish,
	FieldIsPrimary,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	WorkspaceIDValidator func(string) error
	// PlatformUserIDValidator is a validator for the "platform_user_id" field. It is called by the builders before save.
	PlatformUserIDValidator func(string) error
	// PlatformUsernameValidator is a validator for the "platform_username" field. It is called by the builders before save.
	PlatformUsernameValidator func(string) error
	// PlatformDisplayNameValidator is a validator for the "platform_display_name" field. It is called by the builders before save.
	PlatformDisplayNameValidator func(string) error
	// PlatformAvatarURLValidator is a validator for the "platform_avatar_url" field. It is called by the builders before save.
	PlatformAvatarURLValidator func(string) error
	// ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	ErrorMessageValidator func(string) error
	// DefaultAutoPublish holds the default value on creation for the "auto_publish" field.
	DefaultAutoPublish bool
	// DefaultIsPrimary holds the default value on creation for the "is_primary" field.
	DefaultIsPrimary bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Platform defines the type for the "platform" enum field.
type Platform string

// Platform values.
const (
	PlatformFacebook  Platform = "facebook"
	PlatformInstagram Platform = "instagram"
	PlatformTwitter   Platform = "twitter"
	PlatformLinkedin  Platform = "linkedin"
	PlatformTiktok    Platform = "tiktok"
	PlatformYoutube   Platform = "youtube"
	PlatformPinterest Platform = "pinterest"
	PlatformSnapchat  Platform = "snapchat"
)

func (pl Platform) String() string {
	return string(pl)
}

// PlatformValidator is a validator for the "platform" field enum values. It is called by the builders before save.
func PlatformValidator(pl Platform) error {
	switch pl {
	case PlatformFacebook, PlatformInstagram, PlatformTwitter, PlatformLinkedin, PlatformTiktok, PlatformYoutube, PlatformPinterest, PlatformSnapchat:
		return nil
	default:
		return fmt.Errorf("platformintegration: invalid enum value for platform field: %q", pl)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusActive  Status = "active"
	StatusExpired Status = "expired"
	StatusRevoked Status = "revoked"
	StatusError   Status = "error"
	StatusPending Status = "pending"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusActive, StatusExpired, StatusRevoked, StatusError, StatusPending:
		return nil
	default:
		return fmt.Errorf("platformintegration: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the PlatformIntegration queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByWorkspaceID orders the results by the workspace_id field.
func ByWorkspaceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkspaceID, opts...).ToFunc()
}

// ByPlatform orders the results by the platform field.
func ByPlatform(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatform, opts...).ToFunc()
}

// ByPlatformUserID orders the results by the platform_user_id field.
func ByPlatformUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformUserID, opts...).ToFunc()
}

// ByPlatformUsername orders the results by the platform_username field.
func ByPlatformUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformUsername, opts...).ToFunc()
}

// ByPlatformDisplayName orders the results by the platform_display_name field.
func ByPlatformDisplayName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformDisplayName, opts...).ToFunc()
}

// ByPlatformAvatarURL orders the results by the platform_avatar_url field.
func ByPlatformAvatarURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformAvatarURL, opts...).ToFunc()
}

// ByAccessToken orders the results by the access_token field.
func ByAccessToken(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessToken, opts...).ToFunc()
}

// ByRefreshToken orders the results by the refresh_token field.
func ByRefreshToken(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRefreshToken, opts...).ToFunc()
}

// ByTokenExpiresAt orders the results by the token_expires_at field.
func ByTokenExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTokenExpiresAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByErrorMessage orders the results by the error_message field.
func ByErrorMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorMessage, opts...).ToFunc()
}

// ByLastSyncAt orders the results by the last_sync_at field.
func ByLastSyncAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastSyncAt, opts...).ToFunc()
}

// ByLastPostAt orders the results by the last_post_at field.
func ByLastPostAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastPostAt, opts...).ToFunc()
}

// ByAutoPublish orders the results by the auto_publish field.
func ByAutoPublish(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAutoPublish, opts...).ToFunc()
}

// ByIsPrimary orders the results by the is_primary field.
func ByIsPrimary(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPrimary, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

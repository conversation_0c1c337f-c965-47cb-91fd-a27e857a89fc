package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Post holds the schema definition for the Post entity.
type Post struct {
	ent.Schema
}

// Fields of the Post.
func (Post) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.String("workspace_id").
			MaxLen(36).
			Optional(),
		field.String("template_id").
			MaxLen(36).
			Optional(),
		field.String("generation_id").
			MaxLen(36).
			Optional(),
		field.String("title").
			MaxLen(500),
		field.Text("content"),
		field.Text("original_content").
			Optional().
			Comment("Content before any improvements"),
		field.JSON("platforms", []string{}).
			Optional().
			Comment("Target platforms for this post"),
		field.Enum("status").
			Values("draft", "scheduled", "published", "failed", "archived").
			Default("draft"),
		field.Time("scheduled_at").
			Optional().
			Comment("When the post is scheduled to be published"),
		field.Time("published_at").
			Optional().
			Comment("When the post was actually published"),
		field.JSON("platform_posts", map[string]interface{}{}).
			Optional().
			Comment("Platform-specific post IDs and metadata"),
		field.JSON("hashtags", []string{}).
			Optional(),
		field.JSON("mentions", []string{}).
			Optional(),
		field.JSON("media_urls", []string{}).
			Optional().
			Comment("URLs of attached media files"),
		field.JSON("variables", map[string]string{}).
			Optional().
			Comment("Template variables used"),
		field.Enum("visibility").
			Values("public", "private", "unlisted").
			Default("public"),
		field.String("language").
			MaxLen(5).
			Default("en").
			Comment("Content language code"),
		field.JSON("analytics", map[string]interface{}{}).
			Optional().
			Comment("Post performance analytics"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the Post.
func (Post) Edges() []ent.Edge {
	return nil
}

// Indexes of the Post.
func (Post) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("workspace_id"),
		index.Fields("template_id"),
		index.Fields("status"),
		index.Fields("scheduled_at"),
		index.Fields("published_at"),
		index.Fields("visibility"),
		index.Fields("language"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "status"),
		index.Fields("user_id", "created_at"),
	}
}

package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/jackc/pgx/v5/stdlib"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/integration-service/config"
	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/internal/repository"
	"github.com/social-content-ai/integration-service/pkg/oauth"
	"github.com/social-content-ai/integration-service/pkg/publishers"
	"github.com/social-content-ai/integration-service/usecase/integration"
	"github.com/social-content-ai/integration-service/usecase/platform"
	"github.com/social-content-ai/integration-service/usecase/publishing"

	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/pkg-shared/metrics"
	"github.com/social-content-ai/pkg-shared/tracing"

	"github.com/social-content-ai/integration-service/api/grpc/handlers"
	"github.com/social-content-ai/integration-service/api/restful"
	integrationv1 "github.com/social-content-ai/proto-shared/integration/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

const (
	serviceName = "integration-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithField("service", serviceName).Info("Starting integration service")

	// Initialize tracing
	tracer, err := tracing.NewTracer("integration-service")
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize tracer")
	}
	defer tracer.Close()

	// Initialize metrics
	metricsServer := metrics.NewServer(cfg.Monitoring.MetricsPort)
	go func() {
		if err := metricsServer.Start(); err != nil {
			logger.WithError(err).Error("Failed to start metrics server")
		}
	}()

	// Initialize database connections
	readDB, writeDB, err := initDatabases(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize databases")
	}
	defer readDB.Close()
	defer writeDB.Close()

	// Initialize OAuth providers (commented out until interface issues are fixed)
	// TODO: Fix OAuth providers to implement oauth.Provider interface correctly

	facebookOAuth := oauth.NewFacebookOAuth(
		cfg.OAuth.Facebook.ClientID,
		cfg.OAuth.Facebook.ClientSecret,
		cfg.OAuth.Facebook.RedirectURL,
		logger,
	)

	twitterOAuth := oauth.NewTwitterOAuth(
		cfg.OAuth.Twitter.ClientID,
		cfg.OAuth.Twitter.ClientSecret,
		cfg.OAuth.Twitter.RedirectURL,
		logger,
	)

	instagramOAuth := oauth.NewInstagramOAuth(
		cfg.OAuth.Instagram.ClientID,
		cfg.OAuth.Instagram.ClientSecret,
		cfg.OAuth.Instagram.RedirectURL,
		logger,
	)

	linkedinOAuth := oauth.NewLinkedInOAuth(
		cfg.OAuth.LinkedIn.ClientID,
		cfg.OAuth.LinkedIn.ClientSecret,
		cfg.OAuth.LinkedIn.RedirectURL,
		logger,
	)

	youtubeOAuth := oauth.NewYouTubeOAuth(
		cfg.OAuth.YouTube.ClientID,
		cfg.OAuth.YouTube.ClientSecret,
		cfg.OAuth.YouTube.RedirectURL,
		logger,
	)

	tiktokOAuth := oauth.NewTikTokOAuth(
		cfg.OAuth.TikTok.ClientID,
		cfg.OAuth.TikTok.ClientSecret,
		cfg.OAuth.TikTok.RedirectURL,
		logger,
	)

	// Initialize publishers (commented out until interface issues are fixed)
	// TODO: Fix publishers to implement publishers.Publisher interface correctly

	facebookPublisher := publishers.NewFacebookPublisher(logger)
	twitterPublisher := publishers.NewTwitterPublisher(logger)
	instagramPublisher := publishers.NewInstagramPublisher(logger)
	linkedinPublisher := publishers.NewLinkedInPublisher(logger)
	youtubePublisher := publishers.NewYouTubePublisher(logger)
	tiktokPublisher := publishers.NewTikTokPublisher(logger)

	// Initialize external service clients
	userConn, err := grpc.Dial(cfg.Services.UserService, grpc.WithInsecure())
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to user service")
	}
	defer userConn.Close()
	userClient := userv1.NewAuthServiceClient(userConn)

	// Initialize repositories
	integrationRepo := repository.NewPlatformIntegrationRepository(readDB, logger)
	publishedPostRepo := repository.NewPublishedPostRepository(writeDB, logger)
	publishingRepo := repository.NewPublishingRepository(integrationRepo, publishedPostRepo)

	// Initialize use cases
	integrationUseCase := integration.NewService(integrationRepo, integrationRepo, logger)
	platformUseCase := platform.NewService(integrationRepo, integrationRepo, logger)
	publishingUseCase := publishing.NewService(publishingRepo, publishingRepo, logger)

	// Register OAuth providers (commented out until interface issues are fixed)
	// TODO: Fix OAuth providers to implement oauth.Provider interface correctly
	integrationUseCase.RegisterOAuthProvider("facebook", facebookOAuth)
	integrationUseCase.RegisterOAuthProvider("twitter", twitterOAuth)
	integrationUseCase.RegisterOAuthProvider("instagram", instagramOAuth)
	integrationUseCase.RegisterOAuthProvider("linkedin", linkedinOAuth)
	integrationUseCase.RegisterOAuthProvider("youtube", youtubeOAuth)
	integrationUseCase.RegisterOAuthProvider("tiktok", tiktokOAuth)

	// Register publishers (commented out until interface issues are fixed)
	// TODO: Fix publishers to implement publishers.Publisher interface correctly
	publishingUseCase.RegisterPublisher("facebook", facebookPublisher)
	publishingUseCase.RegisterPublisher("twitter", twitterPublisher)
	publishingUseCase.RegisterPublisher("instagram", instagramPublisher)
	publishingUseCase.RegisterPublisher("linkedin", linkedinPublisher)
	publishingUseCase.RegisterPublisher("youtube", youtubePublisher)
	publishingUseCase.RegisterPublisher("tiktok", tiktokPublisher)

	// Initialize gRPC server
	grpcServer := grpc.NewServer()
	integrationv1.RegisterPlatformIntegrationServiceServer(grpcServer, handlers.NewIntegrationHandler(
		integrationUseCase,
		platformUseCase,
		publishingUseCase,
		logger,
	))

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())

	// TODO: Fix REST API setup - interface mismatches
	restful.SetupRoutes(httpRouter, integrationUseCase, platformUseCase, publishingUseCase, userClient, logger)

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start HTTP server
	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler: httpRouter,
	}

	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down servers...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Servers stopped")
}

// initDatabases initializes read and write database connections
func initDatabases(cfg *config.Config, logger logging.Logger) (*ent.Client, *ent.Client, error) {
	// Get database driver and DSN based on configuration
	driverName := cfg.Database.GetDriverName()
	dsn := cfg.Database.GetDSN()

	logger.WithFields(map[string]interface{}{
		"driver": driverName,
		"type":   cfg.Database.Type,
	}).Info("Initializing database connection")

	// Create data directory for SQLite if needed
	if cfg.Database.Type == "sqlite" {
		if err := os.MkdirAll("./data", 0755); err != nil {
			logger.WithError(err).Error("Failed to create data directory")
			return nil, nil, fmt.Errorf("failed to create data directory: %w", err)
		}
	}

	// Create Ent client
	client, err := ent.Open(driverName, dsn)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"driver": driverName,
			"type":   cfg.Database.Type,
		}).Error("Failed opening database connection")
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// For simplicity, use the same client for read and write
	// In production, you might want separate read replicas
	logger.WithField("type", cfg.Database.Type).Info("Database connection established")

	// Run database migrations
	if err := client.Schema.Create(context.Background()); err != nil {
		logger.WithError(err).Error("Failed to create schema")
		return nil, nil, fmt.Errorf("failed to create schema: %w", err)
	}

	logger.Info("Database schema created/updated successfully")
	return client, client, nil
}

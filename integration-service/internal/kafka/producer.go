package kafka

import (
	"context"
	"fmt"

	"github.com/social-content-ai/integration-service/internal/events"
	"github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Producer handles publishing events to Kafka
type Producer struct {
	producer *kafka.Producer
	topic    string
	logger   logging.Logger
}

// ProducerConfig holds Kafka producer configuration
type ProducerConfig struct {
	Brokers []string `yaml:"brokers" env:"KAFKA_BROKERS" env-default:"localhost:9092"`
	Topic   string   `yaml:"topic" env:"KAFKA_TOPIC" env-default:"integration.events"`
}

// NewProducer creates a new Kafka producer
func NewProducer(cfg ProducerConfig, logger logging.Logger) (*Producer, error) {
	kafkaConfig := &kafka.Config{
		Brokers: cfg.Brokers,
	}

	producer := kafka.NewProducer(kafkaConfig, logger)

	logger.WithFields(map[string]interface{}{
		"brokers": cfg.Brokers,
		"topic":   cfg.Topic,
	}).Info("Kafka producer created")

	return &Producer{
		producer: producer,
		topic:    cfg.Topic,
		logger:   logger,
	}, nil
}

// PublishPlatformConnected publishes a platform connected event
func (p *Producer) PublishPlatformConnected(ctx context.Context, event *events.PlatformConnectedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// PublishPlatformDisconnected publishes a platform disconnected event
func (p *Producer) PublishPlatformDisconnected(ctx context.Context, event *events.PlatformDisconnectedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// PublishPostPublished publishes a post published event
func (p *Producer) PublishPostPublished(ctx context.Context, event *events.PostPublishedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// PublishTokenRefreshed publishes a token refreshed event
func (p *Producer) PublishTokenRefreshed(ctx context.Context, event *events.TokenRefreshedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// PublishPublishFailed publishes a publish failed event
func (p *Producer) PublishPublishFailed(ctx context.Context, event *events.PublishFailedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// PublishAnalyticsUpdated publishes an analytics updated event
func (p *Producer) PublishAnalyticsUpdated(ctx context.Context, event *events.AnalyticsUpdatedEvent) error {
	return p.publishEvent(ctx, event.ID, event)
}

// publishEvent publishes an event to Kafka using IntegrationEvent
func (p *Producer) publishEvent(ctx context.Context, key string, event interface{}) error {
	// Convert to IntegrationEvent for pkg-shared kafka
	integrationEvent := p.convertToIntegrationEvent(event)
	if integrationEvent == nil {
		return fmt.Errorf("failed to convert event to IntegrationEvent")
	}

	// Publish integration event
	err := p.producer.PublishIntegrationEvent(ctx, integrationEvent)
	if err != nil {
		p.logger.WithError(err).WithField("event_key", key).Error("Failed to publish event")
		return fmt.Errorf("failed to publish event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_key":  key,
		"event_type": integrationEvent.Type,
		"user_id":    integrationEvent.UserID,
		"platform":   integrationEvent.PlatformName,
	}).Debug("Event published successfully")

	return nil
}

// convertToIntegrationEvent converts internal events to pkg-shared IntegrationEvent
func (p *Producer) convertToIntegrationEvent(event interface{}) *kafka.IntegrationEvent {
	switch e := event.(type) {
	case *events.PlatformConnectedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypeIntegrationConnected, e.UserID)
		integrationEvent.WithWorkspaceID(e.WorkspaceID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.AccountName = e.Username
		integrationEvent.WithData("account_id", e.AccountID)
		integrationEvent.WithData("display_name", e.DisplayName)
		integrationEvent.WithData("avatar_url", e.AvatarURL)
		integrationEvent.WithData("permissions", e.Permissions)
		integrationEvent.WithData("connected_at", e.ConnectedAt)
		return integrationEvent

	case *events.PlatformDisconnectedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypeIntegrationDisconnected, e.UserID)
		integrationEvent.WithWorkspaceID(e.WorkspaceID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.WithData("account_id", e.AccountID)
		integrationEvent.WithData("reason", e.Reason)
		integrationEvent.WithData("disconnected_at", e.DisconnectedAt)
		return integrationEvent

	case *events.PostPublishedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypePostPublished, e.UserID)
		integrationEvent.WithWorkspaceID(e.WorkspaceID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.WithData("post_id", e.PostID)
		integrationEvent.WithData("platform_post_id", e.PlatformPostID)
		integrationEvent.WithData("platform_url", e.PlatformURL)
		integrationEvent.WithData("content", e.Content)
		integrationEvent.WithData("media_urls", e.MediaURLs)
		integrationEvent.WithData("hashtags", e.Hashtags)
		integrationEvent.WithData("published_at", e.PublishedAt)
		if e.ScheduledAt != nil {
			integrationEvent.WithData("scheduled_at", *e.ScheduledAt)
		}
		return integrationEvent

	case *events.TokenRefreshedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypeIntegrationConnected, e.UserID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.WithData("account_id", e.AccountID)
		integrationEvent.WithData("expires_at", e.ExpiresAt)
		integrationEvent.WithData("refreshed_at", e.RefreshedAt)
		integrationEvent.WithData("action", "token_refreshed")
		return integrationEvent

	case *events.PublishFailedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypeIntegrationFailed, e.UserID)
		integrationEvent.WithWorkspaceID(e.WorkspaceID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.Error = e.ErrorMessage
		integrationEvent.WithData("post_id", e.PostID)
		integrationEvent.WithData("content", e.Content)
		integrationEvent.WithData("error_code", e.ErrorCode)
		integrationEvent.WithData("retry_count", e.RetryCount)
		integrationEvent.WithData("failed_at", e.FailedAt)
		return integrationEvent

	case *events.AnalyticsUpdatedEvent:
		integrationEvent := kafka.NewIntegrationEvent(kafka.EventTypeAnalyticsReport, e.UserID)
		integrationEvent.PlatformName = e.Platform
		integrationEvent.WithData("platform_post_id", e.PlatformPostID)
		integrationEvent.WithData("post_id", e.PostID)
		integrationEvent.WithData("metrics", e.Metrics)
		integrationEvent.WithData("previous_metrics", e.PreviousMetrics)
		integrationEvent.WithData("collected_at", e.CollectedAt)
		integrationEvent.WithData("post_published_at", e.PostPublishedAt)
		return integrationEvent

	default:
		p.logger.WithField("event_type", fmt.Sprintf("%T", event)).Warn("Unknown event type")
		return nil
	}
}

// Close closes the Kafka producer
func (p *Producer) Close() error {
	if p.producer == nil {
		return nil
	}

	err := p.producer.Close()
	if err != nil {
		p.logger.WithError(err).Error("Failed to close Kafka producer")
		return err
	}

	p.logger.Info("Kafka producer closed")
	return nil
}

// HealthCheck performs a health check on the Kafka producer
func (p *Producer) HealthCheck(ctx context.Context) error {
	if p.producer == nil {
		return fmt.Errorf("kafka producer is not initialized")
	}

	// Use the producer's health check
	return p.producer.HealthCheck(ctx)
}

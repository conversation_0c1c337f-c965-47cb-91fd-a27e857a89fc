// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditTransactionQuery is the builder for querying CreditTransaction entities.
type CreditTransactionQuery struct {
	config
	ctx        *QueryContext
	order      []credittransaction.OrderOption
	inters     []Interceptor
	predicates []predicate.CreditTransaction
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CreditTransactionQuery builder.
func (ctq *CreditTransactionQuery) Where(ps ...predicate.CreditTransaction) *CreditTransactionQuery {
	ctq.predicates = append(ctq.predicates, ps...)
	return ctq
}

// Limit the number of records to be returned by this query.
func (ctq *CreditTransactionQuery) Limit(limit int) *CreditTransactionQuery {
	ctq.ctx.Limit = &limit
	return ctq
}

// Offset to start from.
func (ctq *CreditTransactionQuery) Offset(offset int) *CreditTransactionQuery {
	ctq.ctx.Offset = &offset
	return ctq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ctq *CreditTransactionQuery) Unique(unique bool) *CreditTransactionQuery {
	ctq.ctx.Unique = &unique
	return ctq
}

// Order specifies how the records should be ordered.
func (ctq *CreditTransactionQuery) Order(o ...credittransaction.OrderOption) *CreditTransactionQuery {
	ctq.order = append(ctq.order, o...)
	return ctq
}

// First returns the first CreditTransaction entity from the query.
// Returns a *NotFoundError when no CreditTransaction was found.
func (ctq *CreditTransactionQuery) First(ctx context.Context) (*CreditTransaction, error) {
	nodes, err := ctq.Limit(1).All(setContextOp(ctx, ctq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{credittransaction.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ctq *CreditTransactionQuery) FirstX(ctx context.Context) *CreditTransaction {
	node, err := ctq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CreditTransaction ID from the query.
// Returns a *NotFoundError when no CreditTransaction ID was found.
func (ctq *CreditTransactionQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ctq.Limit(1).IDs(setContextOp(ctx, ctq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{credittransaction.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ctq *CreditTransactionQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ctq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CreditTransaction entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CreditTransaction entity is found.
// Returns a *NotFoundError when no CreditTransaction entities are found.
func (ctq *CreditTransactionQuery) Only(ctx context.Context) (*CreditTransaction, error) {
	nodes, err := ctq.Limit(2).All(setContextOp(ctx, ctq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{credittransaction.Label}
	default:
		return nil, &NotSingularError{credittransaction.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ctq *CreditTransactionQuery) OnlyX(ctx context.Context) *CreditTransaction {
	node, err := ctq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CreditTransaction ID in the query.
// Returns a *NotSingularError when more than one CreditTransaction ID is found.
// Returns a *NotFoundError when no entities are found.
func (ctq *CreditTransactionQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ctq.Limit(2).IDs(setContextOp(ctx, ctq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{credittransaction.Label}
	default:
		err = &NotSingularError{credittransaction.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ctq *CreditTransactionQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ctq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CreditTransactions.
func (ctq *CreditTransactionQuery) All(ctx context.Context) ([]*CreditTransaction, error) {
	ctx = setContextOp(ctx, ctq.ctx, ent.OpQueryAll)
	if err := ctq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CreditTransaction, *CreditTransactionQuery]()
	return withInterceptors[[]*CreditTransaction](ctx, ctq, qr, ctq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ctq *CreditTransactionQuery) AllX(ctx context.Context) []*CreditTransaction {
	nodes, err := ctq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CreditTransaction IDs.
func (ctq *CreditTransactionQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ctq.ctx.Unique == nil && ctq.path != nil {
		ctq.Unique(true)
	}
	ctx = setContextOp(ctx, ctq.ctx, ent.OpQueryIDs)
	if err = ctq.Select(credittransaction.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ctq *CreditTransactionQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ctq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ctq *CreditTransactionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ctq.ctx, ent.OpQueryCount)
	if err := ctq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ctq, querierCount[*CreditTransactionQuery](), ctq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ctq *CreditTransactionQuery) CountX(ctx context.Context) int {
	count, err := ctq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ctq *CreditTransactionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ctq.ctx, ent.OpQueryExist)
	switch _, err := ctq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ctq *CreditTransactionQuery) ExistX(ctx context.Context) bool {
	exist, err := ctq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CreditTransactionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ctq *CreditTransactionQuery) Clone() *CreditTransactionQuery {
	if ctq == nil {
		return nil
	}
	return &CreditTransactionQuery{
		config:     ctq.config,
		ctx:        ctq.ctx.Clone(),
		order:      append([]credittransaction.OrderOption{}, ctq.order...),
		inters:     append([]Interceptor{}, ctq.inters...),
		predicates: append([]predicate.CreditTransaction{}, ctq.predicates...),
		// clone intermediate query.
		sql:  ctq.sql.Clone(),
		path: ctq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CreditTransaction.Query().
//		GroupBy(credittransaction.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ctq *CreditTransactionQuery) GroupBy(field string, fields ...string) *CreditTransactionGroupBy {
	ctq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CreditTransactionGroupBy{build: ctq}
	grbuild.flds = &ctq.ctx.Fields
	grbuild.label = credittransaction.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.CreditTransaction.Query().
//		Select(credittransaction.FieldUserID).
//		Scan(ctx, &v)
func (ctq *CreditTransactionQuery) Select(fields ...string) *CreditTransactionSelect {
	ctq.ctx.Fields = append(ctq.ctx.Fields, fields...)
	sbuild := &CreditTransactionSelect{CreditTransactionQuery: ctq}
	sbuild.label = credittransaction.Label
	sbuild.flds, sbuild.scan = &ctq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CreditTransactionSelect configured with the given aggregations.
func (ctq *CreditTransactionQuery) Aggregate(fns ...AggregateFunc) *CreditTransactionSelect {
	return ctq.Select().Aggregate(fns...)
}

func (ctq *CreditTransactionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ctq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ctq); err != nil {
				return err
			}
		}
	}
	for _, f := range ctq.ctx.Fields {
		if !credittransaction.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ctq.path != nil {
		prev, err := ctq.path(ctx)
		if err != nil {
			return err
		}
		ctq.sql = prev
	}
	return nil
}

func (ctq *CreditTransactionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CreditTransaction, error) {
	var (
		nodes = []*CreditTransaction{}
		_spec = ctq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CreditTransaction).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CreditTransaction{config: ctq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ctq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ctq *CreditTransactionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ctq.querySpec()
	_spec.Node.Columns = ctq.ctx.Fields
	if len(ctq.ctx.Fields) > 0 {
		_spec.Unique = ctq.ctx.Unique != nil && *ctq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ctq.driver, _spec)
}

func (ctq *CreditTransactionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(credittransaction.Table, credittransaction.Columns, sqlgraph.NewFieldSpec(credittransaction.FieldID, field.TypeUUID))
	_spec.From = ctq.sql
	if unique := ctq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ctq.path != nil {
		_spec.Unique = true
	}
	if fields := ctq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, credittransaction.FieldID)
		for i := range fields {
			if fields[i] != credittransaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ctq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ctq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ctq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ctq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ctq *CreditTransactionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ctq.driver.Dialect())
	t1 := builder.Table(credittransaction.Table)
	columns := ctq.ctx.Fields
	if len(columns) == 0 {
		columns = credittransaction.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ctq.sql != nil {
		selector = ctq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ctq.ctx.Unique != nil && *ctq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ctq.predicates {
		p(selector)
	}
	for _, p := range ctq.order {
		p(selector)
	}
	if offset := ctq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ctq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CreditTransactionGroupBy is the group-by builder for CreditTransaction entities.
type CreditTransactionGroupBy struct {
	selector
	build *CreditTransactionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ctgb *CreditTransactionGroupBy) Aggregate(fns ...AggregateFunc) *CreditTransactionGroupBy {
	ctgb.fns = append(ctgb.fns, fns...)
	return ctgb
}

// Scan applies the selector query and scans the result into the given value.
func (ctgb *CreditTransactionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ctgb.build.ctx, ent.OpQueryGroupBy)
	if err := ctgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditTransactionQuery, *CreditTransactionGroupBy](ctx, ctgb.build, ctgb, ctgb.build.inters, v)
}

func (ctgb *CreditTransactionGroupBy) sqlScan(ctx context.Context, root *CreditTransactionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ctgb.fns))
	for _, fn := range ctgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ctgb.flds)+len(ctgb.fns))
		for _, f := range *ctgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ctgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ctgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CreditTransactionSelect is the builder for selecting fields of CreditTransaction entities.
type CreditTransactionSelect struct {
	*CreditTransactionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cts *CreditTransactionSelect) Aggregate(fns ...AggregateFunc) *CreditTransactionSelect {
	cts.fns = append(cts.fns, fns...)
	return cts
}

// Scan applies the selector query and scans the result into the given value.
func (cts *CreditTransactionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cts.ctx, ent.OpQuerySelect)
	if err := cts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditTransactionQuery, *CreditTransactionSelect](ctx, cts.CreditTransactionQuery, cts, cts.inters, v)
}

func (cts *CreditTransactionSelect) sqlScan(ctx context.Context, root *CreditTransactionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cts.fns))
	for _, fn := range cts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

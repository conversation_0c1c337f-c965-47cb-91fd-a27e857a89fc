package handlers

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// SubscriptionServiceHandler handles subscription-related gRPC operations
type SubscriptionServiceHandler struct {
	subscriptionUseCase subscription.UseCase
	planUseCase         plan.UseCase
	logger              logging.Logger
}

// NewSubscriptionServiceHandler creates a new subscription service handler
func NewSubscriptionServiceHandler(
	subscriptionUseCase subscription.UseCase,
	planUseCase plan.UseCase,
	logger logging.Logger,
) *SubscriptionServiceHandler {
	return &SubscriptionServiceHandler{
		subscriptionUseCase: subscriptionUseCase,
		planUseCase:         planUseCase,
		logger:              logger,
	}
}

// CreateSubscription creates a new subscription
func (h *SubscriptionServiceHandler) CreateSubscription(ctx context.Context, req *creditv1.CreateSubscriptionRequest) (*creditv1.CreateSubscriptionResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserId,
		"plan_id":      req.PlanId,
		"payment_type": req.PaymentType,
	}).Info("Creating subscription")

	// Convert proto request to internal model
	createReq := &models.CreateSubscriptionRequest{
		UserID:          req.UserId,
		PlanID:          req.PlanId,
		PaymentMethodID: req.PaymentMethodId,
		Metadata:        convertProtoMapToInterface(req.Metadata),
	}

	// Create subscription
	subscription, err := h.subscriptionUseCase.CreateSubscription(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create subscription")
		return nil, status.Errorf(codes.Internal, "failed to create subscription: %v", err)
	}

	// Convert to proto response
	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
		PaymentMethod:      req.PaymentType,
		Metadata:           req.Metadata,
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	response := &creditv1.CreateSubscriptionResponse{
		Subscription: protoSubscription,
	}

	// Handle bank transfer payment type
	if req.PaymentType == "bank_transfer" {
		// TODO: Create bank transfer payment record
		response.RequiresConfirmation = true
	}

	return response, nil
}

// GetSubscription retrieves a user's subscription
func (h *SubscriptionServiceHandler) GetSubscription(ctx context.Context, req *creditv1.GetSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting subscription")

	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get subscription")
		return nil, status.Errorf(codes.Internal, "failed to get subscription: %v", err)
	}

	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	return protoSubscription, nil
}

// ListSubscriptions lists user subscriptions
func (h *SubscriptionServiceHandler) ListSubscriptions(ctx context.Context, req *creditv1.ListSubscriptionsRequest) (*creditv1.ListSubscriptionsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Listing subscriptions")

	// For now, return single subscription since we typically have one active subscription per user
	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get subscription")
		return nil, status.Errorf(codes.Internal, "failed to get subscription: %v", err)
	}

	var subscriptions []*creditv1.Subscription
	if subscription != nil {
		protoSubscription := &creditv1.Subscription{
			Id:                 subscription.ID,
			UserId:             subscription.UserID,
			PlanId:             subscription.PlanID,
			PlanName:           subscription.PlanName,
			Status:             subscription.Status,
			CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
			PricePerMonth:      int32(subscription.Amount),
			Currency:           subscription.Currency,
			CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
			CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
			CreatedAt:          timestamppb.New(subscription.CreatedAt),
		}

		if subscription.CanceledAt != nil {
			protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
		}

		subscriptions = append(subscriptions, protoSubscription)
	}

	pagination := &commonv1.PaginationResponse{
		Page:       1,
		Limit:      10,
		TotalItems: int32(len(subscriptions)),
		TotalPages: 1,
		HasNext:    false,
		HasPrev:    false,
	}

	return &creditv1.ListSubscriptionsResponse{
		Subscriptions: subscriptions,
		Pagination:    pagination,
	}, nil
}

// UpdateSubscription updates a subscription
func (h *SubscriptionServiceHandler) UpdateSubscription(ctx context.Context, req *creditv1.UpdateSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":     req.UserId,
		"new_plan_id": req.NewPlanId,
	}).Info("Updating subscription")

	// Convert to internal request
	updateReq := &models.UpdateSubscriptionRequest{
		SubscriptionID:  req.UserId, // Using user_id to find subscription
		PlanID:          req.NewPlanId,
		PaymentMethodID: req.PaymentMethodId,
	}

	subscription, err := h.subscriptionUseCase.UpdateSubscription(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update subscription")
		return nil, status.Errorf(codes.Internal, "failed to update subscription: %v", err)
	}

	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	return protoSubscription, nil
}

// CancelSubscription cancels a subscription
func (h *SubscriptionServiceHandler) CancelSubscription(ctx context.Context, req *creditv1.CancelSubscriptionRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"user_id":         req.UserId,
		"immediate":       req.Immediate,
		"reason":          req.Reason,
	}).Info("Cancelling subscription")

	cancelReq := &models.CancelSubscriptionRequest{
		SubscriptionID: req.SubscriptionId,
		Reason:         req.Reason,
		Immediate:      req.Immediate,
	}

	_, err := h.subscriptionUseCase.CancelSubscription(ctx, cancelReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel subscription")
		return nil, status.Errorf(codes.Internal, "failed to cancel subscription: %v", err)
	}

	return &creditv1.Empty{}, nil
}

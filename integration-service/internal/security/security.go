package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// TokenEncryption handles encryption and decryption of access tokens
type TokenEncryption struct {
	key    []byte
	logger logging.Logger
}

// NewTokenEncryption creates a new token encryption instance
func NewTokenEncryption(encryptionKey string, logger logging.Logger) (*TokenEncryption, error) {
	key, err := hex.DecodeString(encryptionKey)
	if err != nil {
		return nil, fmt.Errorf("invalid encryption key: %w", err)
	}

	if len(key) != 32 { // AES-256 requires 32 bytes
		return nil, fmt.Errorf("encryption key must be 32 bytes (64 hex characters)")
	}

	return &TokenEncryption{
		key:    key,
		logger: logger,
	}, nil
}

// Encrypt encrypts a token using AES-GCM
func (te *TokenEncryption) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", fmt.Errorf("plaintext cannot be empty")
	}

	block, err := aes.NewCipher(te.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt decrypts a token using AES-GCM
func (te *TokenEncryption) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", fmt.Errorf("ciphertext cannot be empty")
	}

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	block, err := aes.NewCipher(te.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}

	return string(plaintext), nil
}

// WebhookVerifier handles webhook signature verification
type WebhookVerifier struct {
	logger logging.Logger
}

// NewWebhookVerifier creates a new webhook verifier
func NewWebhookVerifier(logger logging.Logger) *WebhookVerifier {
	return &WebhookVerifier{
		logger: logger,
	}
}

// VerifyFacebookSignature verifies Facebook webhook signature
func (wv *WebhookVerifier) VerifyFacebookSignature(payload []byte, signature, secret string) bool {
	if signature == "" || secret == "" {
		wv.logger.Warn("Missing signature or secret for Facebook webhook verification")
		return false
	}

	// Facebook sends signature as "sha256=<hash>"
	if !strings.HasPrefix(signature, "sha256=") {
		wv.logger.Warn("Invalid Facebook signature format")
		return false
	}

	expectedSignature := signature[7:] // Remove "sha256=" prefix
	
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(expectedSignature), []byte(computedSignature))
}

// VerifyTwitterSignature verifies Twitter webhook signature
func (wv *WebhookVerifier) VerifyTwitterSignature(payload []byte, signature, secret string) bool {
	if signature == "" || secret == "" {
		wv.logger.Warn("Missing signature or secret for Twitter webhook verification")
		return false
	}

	// Twitter sends signature as "sha256=<hash>"
	if !strings.HasPrefix(signature, "sha256=") {
		wv.logger.Warn("Invalid Twitter signature format")
		return false
	}

	expectedSignature := signature[7:] // Remove "sha256=" prefix
	
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(expectedSignature), []byte(computedSignature))
}

// VerifyInstagramSignature verifies Instagram webhook signature (same as Facebook)
func (wv *WebhookVerifier) VerifyInstagramSignature(payload []byte, signature, secret string) bool {
	return wv.VerifyFacebookSignature(payload, signature, secret)
}

// VerifyLinkedInSignature verifies LinkedIn webhook signature
func (wv *WebhookVerifier) VerifyLinkedInSignature(payload []byte, signature, secret string) bool {
	if signature == "" || secret == "" {
		wv.logger.Warn("Missing signature or secret for LinkedIn webhook verification")
		return false
	}

	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	computedSignature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(computedSignature))
}

// VerifyTikTokSignature verifies TikTok webhook signature
func (wv *WebhookVerifier) VerifyTikTokSignature(payload []byte, signature, secret string) bool {
	if signature == "" || secret == "" {
		wv.logger.Warn("Missing signature or secret for TikTok webhook verification")
		return false
	}

	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(computedSignature))
}

// RateLimiter handles rate limiting for API calls
type RateLimiter struct {
	limits map[string]*RateLimit
	logger logging.Logger
}

// RateLimit represents a rate limit configuration
type RateLimit struct {
	Requests  int           `json:"requests"`
	Window    time.Duration `json:"window"`
	LastReset time.Time     `json:"last_reset"`
	Count     int           `json:"count"`
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(logger logging.Logger) *RateLimiter {
	return &RateLimiter{
		limits: make(map[string]*RateLimit),
		logger: logger,
	}
}

// SetLimit sets a rate limit for a specific key
func (rl *RateLimiter) SetLimit(key string, requests int, window time.Duration) {
	rl.limits[key] = &RateLimit{
		Requests:  requests,
		Window:    window,
		LastReset: time.Now(),
		Count:     0,
	}
}

// CheckLimit checks if a request is within the rate limit
func (rl *RateLimiter) CheckLimit(key string) bool {
	limit, exists := rl.limits[key]
	if !exists {
		return true // No limit set, allow request
	}

	now := time.Now()
	
	// Reset counter if window has passed
	if now.Sub(limit.LastReset) >= limit.Window {
		limit.Count = 0
		limit.LastReset = now
	}

	// Check if within limit
	if limit.Count >= limit.Requests {
		rl.logger.WithFields(map[string]interface{}{
			"key":      key,
			"count":    limit.Count,
			"limit":    limit.Requests,
			"window":   limit.Window,
		}).Warn("Rate limit exceeded")
		return false
	}

	limit.Count++
	return true
}

// GetRemainingRequests returns the number of remaining requests for a key
func (rl *RateLimiter) GetRemainingRequests(key string) int {
	limit, exists := rl.limits[key]
	if !exists {
		return -1 // No limit set
	}

	now := time.Now()
	
	// Reset counter if window has passed
	if now.Sub(limit.LastReset) >= limit.Window {
		return limit.Requests
	}

	remaining := limit.Requests - limit.Count
	if remaining < 0 {
		remaining = 0
	}
	
	return remaining
}

// GetResetTime returns when the rate limit will reset
func (rl *RateLimiter) GetResetTime(key string) time.Time {
	limit, exists := rl.limits[key]
	if !exists {
		return time.Time{} // No limit set
	}

	return limit.LastReset.Add(limit.Window)
}

// SanitizeInput sanitizes user input to prevent injection attacks
func SanitizeInput(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")
	
	// Remove control characters except newlines and tabs
	var result strings.Builder
	for _, r := range input {
		if r == '\n' || r == '\t' || r >= 32 {
			result.WriteRune(r)
		}
	}
	
	return result.String()
}

// ValidateOrigin validates the origin of a request
func ValidateOrigin(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return false
	}

	for _, allowed := range allowedOrigins {
		if origin == allowed {
			return true
		}
		
		// Support wildcard subdomains
		if strings.HasPrefix(allowed, "*.") {
			domain := allowed[2:]
			if strings.HasSuffix(origin, "."+domain) || origin == domain {
				return true
			}
		}
	}

	return false
}

// GenerateSecureToken generates a cryptographically secure random token
func GenerateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate secure token: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

// HashPassword hashes a password using SHA-256 (for webhook secrets, not user passwords)
func HashPassword(password, salt string) string {
	hash := sha256.New()
	hash.Write([]byte(password + salt))
	return hex.EncodeToString(hash.Sum(nil))
}

syntax = "proto3";

package user.v1;

import "google/protobuf/timestamp.proto";
import "user/v1/user.proto";

option go_package = "github.com/social-content-ai/proto-shared/user/v1;userv1";

// LoginRequest for user authentication
message LoginRequest {
  string email = 1;
  string password = 2;
  string device_info = 3;    // Browser/device information
  string ip_address = 4;     // Client IP address
}

// LoginResponse with authentication tokens
message LoginResponse {
  string access_token = 1;
  string refresh_token = 2;
  google.protobuf.Timestamp expires_at = 3;
  User user = 4;
  bool requires_2fa = 5;     // Whether 2FA is required
  string session_id = 6;
}

// RefreshTokenRequest for token refresh
message RefreshTokenRequest {
  string refresh_token = 1;
}

// LogoutRequest for user logout
message LogoutRequest {
  string session_id = 1;
}

// ChangePasswordRequest for password change
message ChangePasswordRequest {
  string user_id = 1;
  string current_password = 2;
  string new_password = 3;
}

// ResetPasswordRequest for password reset
message ResetPasswordRequest {
  string email = 1;
}

// ConfirmPasswordResetRequest for confirming password reset
message ConfirmPasswordResetRequest {
  string token = 1;
  string new_password = 2;
}

// ValidateTokenRequest for token validation
message ValidateTokenRequest {
  string token = 1;
}

// ValidateTokenResponse with token validation result
message ValidateTokenResponse {
  bool valid = 1;
  string user_id = 2;
  google.protobuf.Timestamp expires_at = 3;
  map<string, string> claims = 4;
}

// SetupTwoFARequest for setting up 2FA
message SetupTwoFARequest {
  string user_id = 1;
}

// SetupTwoFAResponse with 2FA setup information
message SetupTwoFAResponse {
  string secret = 1;
  string qr_code_url = 2;
  bytes qr_code = 3;
  repeated string backup_codes = 4;
}

// EnableTwoFARequest for enabling 2FA
message EnableTwoFARequest {
  string user_id = 1;
  string token = 2;
}

// EnableTwoFAResponse with 2FA enable result
message EnableTwoFAResponse {
  bool enabled = 1;
  repeated string backup_codes = 2;
  google.protobuf.Timestamp enabled_at = 3;
}

// DisableTwoFARequest for disabling 2FA
message DisableTwoFARequest {
  string user_id = 1;
  string password = 2;
  string token = 3; // TOTP token or backup code
}

// VerifyTwoFARequest for verifying 2FA during login
message VerifyTwoFARequest {
  string user_id = 1;
  string token = 2;
}

// VerifyTwoFAResponse with 2FA verification result
message VerifyTwoFAResponse {
  bool valid = 1;
  bool backup_code_used = 2;
  int32 remaining_codes = 3;
  string message = 4;
}

// GenerateBackupCodesRequest for generating new backup codes
message GenerateBackupCodesRequest {
  string user_id = 1;
  string password = 2;
}

// GenerateBackupCodesResponse with new backup codes
message GenerateBackupCodesResponse {
  repeated string backup_codes = 1;
  google.protobuf.Timestamp generated_at = 2;
}

// GetTwoFAStatusRequest for getting 2FA status
message GetTwoFAStatusRequest {
  string user_id = 1;
}

// GetTwoFAStatusResponse with 2FA status
message GetTwoFAStatusResponse {
  string user_id = 1;
  bool two_fa_enabled = 2;
  google.protobuf.Timestamp enabled_at = 3;
  int32 backup_codes_count = 4;
  bool has_backup_codes = 5;
  google.protobuf.Timestamp last_verification_at = 6;
}

// AuthService provides authentication operations
service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (LoginResponse);
  rpc Logout(LogoutRequest) returns (Empty);
  rpc ChangePassword(ChangePasswordRequest) returns (Empty);
  rpc ResetPassword(ResetPasswordRequest) returns (Empty);
  rpc ConfirmPasswordReset(ConfirmPasswordResetRequest) returns (Empty);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);

  // Two-Factor Authentication operations
  rpc SetupTwoFA(SetupTwoFARequest) returns (SetupTwoFAResponse);
  rpc EnableTwoFA(EnableTwoFARequest) returns (EnableTwoFAResponse);
  rpc DisableTwoFA(DisableTwoFARequest) returns (Empty);
  rpc VerifyTwoFA(VerifyTwoFARequest) returns (VerifyTwoFAResponse);
  rpc GenerateBackupCodes(GenerateBackupCodesRequest) returns (GenerateBackupCodesResponse);
  rpc GetTwoFAStatus(GetTwoFAStatusRequest) returns (GetTwoFAStatusResponse);
}

# Development Environment Configuration

# Override server settings for development
server:
  env: "development"
  grpc_port: 50051
  http_port: 8080

# Development database settings
database:
  # Use SQLite for development for easier setup
  type: "sqlite"
  sqlite_path: "./data/user_service_dev.db"

  # PostgreSQL fallback configuration
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "user_service_dev"
  ssl_mode: "disable"

# Development JWT settings (less secure for easier testing)
jwt:
  secret_key: "dev-secret-key-not-for-production"
  token_duration: "24h"
  refresh_duration: "168h"

# Development logging (more verbose)
logging:
  level: "debug"
  format: "text"
  output: "stdout"

# Development Redis settings
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 1  # Use different DB for dev

# Development email settings (use fake SMTP for testing)
email:
  smtp_host: "localhost"
  smtp_port: 1025  # MailHog port
  username: ""
  password: ""
  from_email: "<EMAIL>"
  from_name: "Social Content AI Dev"

# Relaxed rate limiting for development
rate_limit:
  enabled: false
  requests_per_minute: 1000
  burst: 100

# Development security settings
security:
  bcrypt_cost: 4  # Faster for development
  password_min_length: 6
  session_duration: "24h"
  max_sessions_per_user: 10
  require_email_verification: false
  enable_2fa: false

# Enable all features in development
features:
  user_registration: true
  password_reset: true
  email_verification: true
  social_login: true
  admin_panel: true
  user_analytics: true

# Development external services
external_services:
  notification_service:
    host: "localhost"
    port: 50052
    timeout: "30s"  # Longer timeout for debugging
  
  content_service:
    host: "localhost"
    port: 50053
    timeout: "30s"
  
  billing_service:
    host: "localhost"
    port: 50054
    timeout: "30s"

# Development Kafka settings
kafka:
  brokers:
    - "localhost:9092"
  consumer:
    group_id: "user-service-dev-consumer"
    auto_offset_reset: "earliest"

# Relaxed file upload limits for development
file_upload:
  max_size: "50MB"
  upload_path: "/tmp/dev-uploads"

# Permissive CORS for development
cors:
  allowed_origins:
    - "*"
  allowed_methods:
    - "*"
  allowed_headers:
    - "*"
  allow_credentials: true

# More frequent cleanup in development
cleanup:
  expired_sessions_interval: "10m"
  old_sessions_retention: "7d"
  deleted_users_retention: "30d"

  # Development unverified user cleanup settings (for testing)
  enabled: true
  auto_start: false              # Don't auto-start in development
  unverified_user_ttl: "1h"      # Shorter TTL for testing (1 hour)
  cleanup_interval: "10m"        # More frequent cleanup for testing
  batch_size: 10                 # Smaller batches for testing
  enable_notifications: false    # Disable notifications in development
  warning_time: "10m"            # Shorter warning time for testing

# 🏗️ Tài liệu Phân tích và Thiết kế Kiến trúc Microservices
## Social Content AI Platform

### 📋 Tổng quan
Tài liệu này phân tích và đề xuất cách phân chia hệ thống Social Content AI thành các microservices độc lập, dựa trên phân tích business domain, data model và API endpoints hiện có.

---

## 🎯 Nguyên tắc Thiết kế Microservices

### 1. Domain-Driven Design (DDD)
- Mỗi service quản lý một bounded context riêng biệt
- Tách biệt rõ ràng về business logic và data ownership
- Minimize cross-service dependencies

### 2. Single Responsibility Principle
- Mỗi service có một trách nhiệm chính
- High cohesion, low coupling
- Independent deployment và scaling

### 3. Data Ownership
- Mỗi service sở hữu database riêng
- Không chia sẻ database giữa các services
- Communication qua API hoặc events

---

## 🏢 Phân tích Business Domains

### Core Domains (Quan trọng nhất)
1. **User Management & Authentication** - Quản lý người dùng và xác thực
2. **Content Generation** - Tạo nội dung bằng AI
3. **Content Management** - Quản lý bài viết, template, workspace và lên lịch
4. **Social Platform Integration** - Tích hợp với các nền tảng MXH

### Supporting Domains (Hỗ trợ)
5. **Asset Management** - Quản lý file, ảnh, tài liệu trên S3/MinIO
6. **Analytics & Reporting** - Thống kê và báo cáo
7. **Credit & Billing** - Hệ thống credits và thanh toán

### Generic Domains (Chung)
8. **Notification Service** - Gửi thông báo
9. **Audit & Security** - Bảo mật và audit trail

---

## 🔧 Chi tiết các Microservices

### 1. 👤 User Service
**Trách nhiệm:** Quản lý người dùng, xác thực và phân quyền

**Database Tables:**
- `users` - Thông tin người dùng cơ bản
- `sessions` - Quản lý phiên đăng nhập
- `two_factor_auth` - Xác thực 2 bước
- `trusted_devices` - Thiết bị đáng tin cậy
- `login_history` - Lịch sử đăng nhập
- `account_deletions` - Yêu cầu xóa tài khoản
- `security_events` - Log sự kiện bảo mật

**gRPC Services:**
- `AuthService` - Đăng nhập, đăng ký, 2FA, token validation
  - `Login(LoginRequest) returns (LoginResponse)`
  - `RefreshToken(RefreshTokenRequest) returns (LoginResponse)`
  - `Logout(LogoutRequest) returns (Empty)`
  - `ChangePassword(ChangePasswordRequest) returns (Empty)`
  - `ResetPassword(ResetPasswordRequest) returns (Empty)`
  - `ConfirmPasswordReset(ConfirmPasswordResetRequest) returns (Empty)`
  - `ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse)`

- `UserService` - Quản lý profile, CRUD operations
  - `CreateUser(CreateUserRequest) returns (User)`
  - `GetUser(GetUserRequest) returns (User)`
  - `UpdateUser(UpdateUserRequest) returns (User)`
  - `ListUsers(ListUsersRequest) returns (ListUsersResponse)`
  - `DeleteUser(DeleteUserRequest) returns (Empty)`
  - `VerifyUser(GetUserRequest) returns (User)`

- `SessionService` - Quản lý phiên đăng nhập
  - `CreateSession(CreateSessionRequest) returns (Session)`
  - `GetSession(GetSessionRequest) returns (Session)`
  - `ListSessions(ListSessionsRequest) returns (ListSessionsResponse)`
  - `RevokeSession(RevokeSessionRequest) returns (Empty)`
  - `RevokeAllSessions(RevokeAllSessionsRequest) returns (Empty)`

**Technologies:**
- JWT cho authentication
- Redis cho session storage
- bcrypt cho password hashing
- TOTP cho 2FA
- gRPC cho internal communication

### 2. 🤖 AI Content Service
**Trách nhiệm:** Tạo và cải thiện nội dung bằng AI

**Database Tables:**
- `ai_generations` - Lịch sử tạo nội dung AI
- `ai_improvements` - Cải thiện nội dung
- `model_credit_config` - Cấu hình phí theo model

**gRPC Services:**
- `ContentGenerationService` - Tạo nội dung mới với RAG và credit validation
  - `GenerateContent(ContentGenerationRequest) returns (ContentGenerationResponse)`
  - `GetGeneration(GetGenerationRequest) returns (ContentGenerationResponse)`
  - `ListGenerations(ListGenerationsRequest) returns (ListGenerationsResponse)`
  - `RegenerateContent(RegenerateContentRequest) returns (ContentGenerationResponse)`
  - `ValidateCredits(ValidateCreditsRequest) returns (ValidateCreditsResponse)`
  - `GetRAGContext(RAGContextRequest) returns (RAGContextResponse)`
  - `GenerateWithRAG(GenerateWithRAGRequest) returns (GenerateWithRAGResponse)`

- `ContentImprovementService` - Cải thiện nội dung
  - `ImproveContent(ImproveContentRequest) returns (ImproveContentResponse)`
  - `GetImprovement(GetImprovementRequest) returns (ImproveContentResponse)`
  - `ListImprovements(ListImprovementsRequest) returns (ListImprovementsResponse)`

- `ImageGenerationService` - Tạo ảnh AI
  - `GenerateImage(ImageGenerationRequest) returns (ImageGenerationResponse)`
  - `GetImageGeneration(GetImageGenerationRequest) returns (ImageGenerationResponse)`
  - `ListImageGenerations(ListImageGenerationsRequest) returns (ListImageGenerationsResponse)`

**Technologies:**
- OpenAI GPT-4, Claude, Gemini APIs
- **LangChain framework** cho prompt engineering và RAG orchestration
- Vector database cho RAG (via LangChain RAG Processing Service)
- Apache Kafka cho async processing
- Rate limiting cho API calls
- gRPC cho internal communication
- gRPC integration với LangChain RAG Processing Service

### 3. 📝 Content Management Service
**Trách nhiệm:** Quản lý bài viết, template, workspace và lên lịch đăng

**Database Tables:**
- `posts` - Bài viết chính
- `post_schedules` - Lịch đăng chi tiết
- `post_template_usage` - Sử dụng template
- `templates` - Template nội dung
- `template_revenue` - Doanh thu template
- `template_ratings` - Đánh giá template
- `workspaces` - Thông tin workspace
- `workspace_members` - Thành viên và quyền
- `workspace_invitations` - Lời mời tham gia

**gRPC Services:**
- `PostService` - CRUD bài viết
- `ScheduleService` - Lên lịch đăng bài
- `TemplateService` - Quản lý template
- `MarketplaceService` - Template marketplace
- `WorkspaceService` - Quản lý workspace
- `CollaborationService` - Cộng tác nhóm

**Technologies:**
- Apache Kafka cho scheduled posting
- Full-text search cho template
- Payment processing integration
- Content moderation system
- Role-based access control (RBAC)
- gRPC cho internal communication

### 4. 🔗 Integration Service
**Trách nhiệm:** Tích hợp với các nền tảng mạng xã hội

**Database Tables:**
- `integrations` - Kết nối tài khoản MXH
- `post_analytics` - Thống kê từ các nền tảng

**gRPC Services:**
- `PlatformIntegrationService` - Quản lý kết nối
- `OAuthService` - OAuth flow
- `PublishingService` - Đăng bài lên platforms
- `AnalyticsCollectionService` - Thu thập metrics

**Technologies:**
- OAuth 2.0 cho authentication
- Platform-specific SDKs
- Webhook handlers
- Rate limiting per platform
- Apache Kafka cho event streaming
- gRPC cho internal communication

---

## 📋 Complete gRPC Service Definitions

### Proto-shared Structure
```
proto-shared/
├── common/v1/           # Shared types (pagination, timestamps, errors)
├── user/v1/             # User management and authentication
├── ai-content/v1/       # AI content generation
├── content-mgmt/v1/     # Posts, templates, workspaces
├── integration/v1/      # Social platform integrations
├── asset/v1/            # File and asset management
├── credit/v1/           # Credits and billing
├── analytics/v1/        # Analytics and monitoring
├── notification/v1/     # Notifications
├── rag-processing/v1/   # RAG and document processing
└── data-mgmt/v1/        # Data backup and GDPR compliance
```

### Key gRPC Service Operations

#### User Service (user/v1/)
```protobuf
service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (LoginResponse);
  rpc ChangePassword(ChangePasswordRequest) returns (Empty);
  rpc ResetPassword(ResetPasswordRequest) returns (Empty);
}

service UserService {
  rpc CreateUser(CreateUserRequest) returns (User);
  rpc GetUser(GetUserRequest) returns (User);
  rpc UpdateUser(UpdateUserRequest) returns (User);
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse);
}

service SessionService {
  rpc CreateSession(CreateSessionRequest) returns (Session);
  rpc RevokeSession(RevokeSessionRequest) returns (Empty);
  rpc RevokeAllSessions(RevokeAllSessionsRequest) returns (Empty);
}
```

#### AI Content Service (ai-content/v1/)
```protobuf
service ContentGenerationService {
  rpc GenerateContent(ContentGenerationRequest) returns (ContentGenerationResponse);
  rpc ValidateCredits(ValidateCreditsRequest) returns (ValidateCreditsResponse);
  rpc GetRAGContext(RAGContextRequest) returns (RAGContextResponse);
  rpc GenerateWithRAG(GenerateWithRAGRequest) returns (GenerateWithRAGResponse);
}
```

#### Content Management Service (content-mgmt/v1/)
```protobuf
service PostService {
  rpc CreatePost(CreatePostRequest) returns (Post);
  rpc GetPost(GetPostRequest) returns (Post);
  rpc UpdatePost(UpdatePostRequest) returns (Post);
  rpc ListPosts(ListPostsRequest) returns (ListPostsResponse);
  rpc PublishPost(PublishPostRequest) returns (PublishPostResponse);
  rpc SchedulePost(SchedulePostRequest) returns (SchedulePostResponse);
}

service TemplateService {
  rpc CreateTemplate(CreateTemplateRequest) returns (Template);
  rpc PurchaseTemplate(PurchaseTemplateRequest) returns (PurchaseTemplateResponse);
  rpc GetTemplateRevenue(GetTemplateRevenueRequest) returns (GetTemplateRevenueResponse);
  rpc RateTemplate(RateTemplateRequest) returns (Empty);
}

service WorkspaceService {
  rpc CreateWorkspace(CreateWorkspaceRequest) returns (Workspace);
  rpc InviteMember(InviteMemberRequest) returns (InviteMemberResponse);
  rpc UpdateMemberRole(UpdateMemberRoleRequest) returns (WorkspaceMember);
  rpc GetWorkspaceStats(GetWorkspaceStatsRequest) returns (WorkspaceStats);
}
```

#### Integration Service (integration/v1/)
```protobuf
service PlatformIntegrationService {
  rpc GetOAuthUrl(GetOAuthUrlRequest) returns (GetOAuthUrlResponse);
  rpc ConnectAccount(ConnectAccountRequest) returns (ConnectAccountResponse);
  rpc PublishPost(PublishPostRequest) returns (PublishPostResponse);
  rpc GetPostAnalytics(GetPostAnalyticsRequest) returns (PostAnalytics);
  rpc GetAccountAnalytics(GetAccountAnalyticsRequest) returns (AccountAnalytics);
}
```

#### Asset Service (asset/v1/)
```protobuf
service AssetService {
  rpc RequestUpload(UploadRequest) returns (UploadResponse);
  rpc ConfirmUpload(ConfirmUploadRequest) returns (ConfirmUploadResponse);
  rpc ValidateAsset(ValidateAssetRequest) returns (ValidationResult);
  rpc ProcessAsset(ProcessAssetRequest) returns (ProcessAssetResponse);
  rpc GetDownloadUrl(GetDownloadUrlRequest) returns (GetDownloadUrlResponse);
}
```

#### Credit Service (credit/v1/)
```protobuf
service CreditService {
  rpc GetUserCredit(GetUserCreditRequest) returns (UserCredit);
  rpc ValidateCredit(ValidateCreditRequest) returns (ValidateCreditResponse);
  rpc DeductCredits(DeductCreditsRequest) returns (UserCredit);
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  rpc ConfirmBankTransfer(ConfirmBankTransferRequest) returns (ConfirmBankTransferResponse);
}
```

#### RAG Processing Service (rag-processing/v1/)
```protobuf
service RAGProcessingService {
  rpc ProcessDocument(ProcessDocumentRequest) returns (ProcessDocumentResponse);
  rpc RetrieveContext(ContextRetrievalRequest) returns (ContextRetrievalResponse);
  rpc GraphRAGQuery(GraphQueryRequest) returns (GraphQueryResponse);
  rpc SearchSimilarDocuments(SimilaritySearchRequest) returns (SimilaritySearchResponse);
}
```

#### Analytics Service (analytics/v1/)
```protobuf
service AnalyticsService {
  rpc RecordMetric(RecordMetricRequest) returns (Empty);
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse);
  rpc RecordSecurityEvent(RecordSecurityEventRequest) returns (Empty);
  rpc GetSecurityMetrics(GetSecurityMetricsRequest) returns (SecurityMetrics);
  rpc CreateDashboard(CreateDashboardRequest) returns (Dashboard);
}
```

#### Notification Service (notification/v1/)
```protobuf
service NotificationService {
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc SendBulkNotification(BulkNotificationRequest) returns (BulkNotificationResponse);
  rpc ProcessKafkaEvent(KafkaEventRequest) returns (KafkaEventResponse);
  rpc UpdatePreferences(UpdatePreferencesRequest) returns (NotificationPreferences);
}
```

#### Data Management Service (data-mgmt/v1/)
```protobuf
service BackupService {
  rpc CreateBackup(CreateBackupRequest) returns (CreateBackupResponse);
  rpc ExportData(DataExportRequest) returns (DataExportResponse);
  rpc RequestAccountDeletion(AccountDeletionRequest) returns (AccountDeletionResponse);
  rpc CleanupExpired(CleanupExpiredRequest) returns (CleanupExpiredResponse);
}
```

## 🌐 Service Communication Patterns

### 1. Internal Communication - gRPC
**gRPC** cho tất cả internal service communication:
- High performance binary protocol
- Strong typing với Protocol Buffers
- Built-in load balancing và health checking
- Streaming support cho real-time data

**Example gRPC calls:**
- User Service → Credit Service: `ValidateCredit(ValidateCreditRequest)`
- Content Management → AI Service: `GenerateContent(ContentGenerationRequest)`
- Integration Service → Content Management: `GetPost(GetPostRequest)`
- Asset Service → RAG Processing: `ProcessDocument(ProcessDocumentRequest)`

### 2. External Communication - REST APIs
**REST APIs** cho client-facing operations:
- Web/Mobile app authentication
- Public API endpoints
- Webhook callbacks từ social platforms

### 3. Asynchronous Communication - Apache Kafka
**Event-driven** với Kafka topics:
- `user.events` - User registered, updated, deleted
- `content.events` - Post created, published, scheduled
- `billing.events` - Credit consumed, payment completed
- `analytics.events` - Metrics collected, reports generated

### 4. Data Consistency Patterns
**Eventual Consistency** với Saga pattern:
- Template purchase workflow
- Multi-platform posting
- Credit deduction across services

---

## 📊 Data Architecture

### Database per Service
```
User Service → PostgreSQL (User data, sessions)
AI Content Service → PostgreSQL + Vector DB (Content, embeddings)
Content Management Service → PostgreSQL + Search Engine (Posts, templates, workspaces)
Integration Service → PostgreSQL (Connections, analytics)
Asset Service → PostgreSQL + S3/MinIO (File metadata + object storage)
Credit Service → PostgreSQL (Transactions, billing)
Analytics Service → Time-series DB (Metrics, reports)
Notification Service → PostgreSQL + Message Queue
```

### Object Storage Architecture
```
S3/MinIO Buckets:
├── socialai-assets-prod/
│   ├── images/
│   │   ├── user-uploads/
│   │   ├── ai-generated/
│   │   └── stock-photos/
│   ├── documents/
│   │   ├── rag-files/
│   │   └── user-docs/
│   └── templates/
│       └── thumbnails/
└── socialai-backups/
    ├── database-backups/
    └── user-data-exports/
```

### Shared Data Challenges
**User ID** được chia sẻ across services:
- Solution: User Service làm source of truth
- Other services cache user info với TTL
- Event-driven updates khi user thay đổi

---

## 🚀 Deployment Strategy

### Containerization
```yaml
# Docker containers cho mỗi service
user-service:
  image: socialai/user-service:latest

ai-content-service:
  image: socialai/ai-content-service:latest

content-management-service:
  image: socialai/content-management-service:latest

integration-service:
  image: socialai/integration-service:latest

asset-service:
  image: socialai/asset-service:latest

credit-service:
  image: socialai/credit-service:latest

analytics-service:
  image: socialai/analytics-service:latest

notification-service:
  image: socialai/notification-service:latest
```

### Orchestration
**Kubernetes** cho production:
- Auto-scaling based on load
- Health checks và self-healing
- Service discovery và load balancing
- Rolling deployments

### API Gateway
**Kong/Nginx** làm entry point:
- Request routing to services (no authentication)
- Rate limiting per client/IP
- Load balancing to service instances
- API versioning và path rewriting
- gRPC-Web proxy cho browser clients
- CORS handling
- Request/Response logging

### Message Broker - Apache Kafka
**Kafka Cluster** cho event streaming:
- High throughput message processing
- Event sourcing và CQRS patterns
- Dead letter queues cho error handling
- Schema registry cho message validation

---

## 🔄 Migration Strategy

### Phase 1: Extract User Service (2-3 tuần)
1. Tách authentication logic
2. Setup separate user database
3. Implement gRPC services cho internal calls
4. Setup Kafka topics cho user events
5. Migrate user data

### Phase 2: Extract AI Content Service (3-4 tuần)
1. Isolate AI generation logic
2. Setup vector database cho RAG
3. Implement Kafka consumers cho async processing
4. Setup gRPC services cho content generation
5. Migrate AI-related data

### Phase 3: Extract Content Management Service (4-5 tuần)
1. Merge post, template, và workspace logic
2. Setup unified database schema
3. Implement gRPC services cho content operations
4. Setup Kafka producers cho content events
5. Migrate posts, templates, workspace data

### Phase 4: Extract Integration & Supporting Services (6-8 tuần)
1. Extract social platform integrations
2. Setup Asset service với S3/MinIO integration
3. Extract Analytics & Credit services
4. Setup Notification service với Kafka consumers
5. Complete gRPC + Kafka architecture

---

## 📈 Benefits của Microservices Architecture

### Scalability
- Scale AI service independently khi có nhiều requests
- Scale Content Management service theo số lượng posts/templates
- Scale Integration service theo số lượng platforms
- Scale Analytics service theo data volume

### Technology Diversity
- AI Service: Python với ML libraries
- User Service: Node.js với high concurrency
- Content Management Service: Java/Kotlin với Spring Boot
- Analytics Service: Go với time-series processing
- Asset Service: Go với S3/MinIO integration

### Team Independence
- Frontend team: React application với gRPC-Web
- AI team: Content generation service
- Content team: Posts, templates, workspace management
- Integration team: Platform connectors
- DevOps team: Infrastructure và deployment

### Fault Isolation
- AI service down → Users vẫn có thể manage posts/templates
- Integration service issue → Không ảnh hưởng content creation
- Asset service maintenance → Core features vẫn hoạt động
- Kafka message queues ensure eventual consistency

### Performance Benefits
- gRPC binary protocol faster than REST
- Kafka high-throughput message processing
- S3/MinIO distributed object storage
- Independent database scaling per service

---

## ⚠️ Challenges và Solutions

### 1. Distributed Transactions
**Challenge:** Template purchase cần update credits và template usage
**Solution:** Saga pattern với Kafka events và compensation logic

### 2. Data Consistency
**Challenge:** User profile changes cần sync across services
**Solution:** Kafka event streaming với eventual consistency

### 3. Service Discovery & Communication
**Challenge:** gRPC services cần tìm và gọi nhau
**Solution:** Service mesh (Istio) với gRPC load balancing

### 4. Message Ordering & Delivery
**Challenge:** Kafka message processing order và exactly-once delivery
**Solution:** Kafka partitioning strategy và idempotent consumers

### 5. File Storage Consistency
**Challenge:** S3/MinIO file uploads và database metadata sync
**Solution:** Two-phase commit với rollback mechanisms

### 6. Monitoring & Debugging
**Challenge:** Trace requests across gRPC services và Kafka topics
**Solution:** Distributed tracing (Jaeger) với gRPC interceptors

---

## 🔐 Security Flows - 2FA và Password Management

### 1. 🛡️ Two-Factor Authentication (2FA) Setup Flow

#### **Bước 1: Khởi tạo 2FA Setup**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant R as Redis

    U->>FE: Click "Enable 2FA"
    FE->>AG: POST /auth/2fa/setup
    AG->>US: Route request

    US->>US: Validate user session
    US->>US: Generate TOTP secret
    US->>US: Create QR code data
    US->>R: Store temp setup data (TTL: 10 min)

    US->>AG: Return setup response
    AG->>FE: Setup data + QR code
    FE->>U: Display QR code + backup codes

    Note over U,FE: User scans QR with authenticator app
```

#### **Bước 2: Xác thực và Kích hoạt 2FA**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka

    U->>FE: Enter 6-digit code from app
    FE->>AG: POST /auth/2fa/verify-setup
    AG->>US: Route verification request

    US->>US: Validate setup token
    US->>US: Verify TOTP code

    alt Code is valid
        US->>US: Save 2FA settings to DB
        US->>US: Generate backup codes
        US->>K: Publish TwoFactorEnabled event

        K->>NS: Process security notification
        NS->>NS: Send 2FA enabled email

        US->>AG: Return success + backup codes
        AG->>FE: 2FA enabled successfully
        FE->>U: Show success + backup codes
    else Code is invalid
        US->>AG: Return error
        AG->>FE: Invalid code error
        FE->>U: Show error message
    end
```

#### **Bước 3: 2FA Login Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka

    U->>FE: Login with email/password
    FE->>AG: POST /auth/login
    AG->>US: Route login request

    US->>US: Validate credentials
    US->>US: Check if 2FA enabled

    alt 2FA enabled
        US->>US: Generate login token (temp)
        US->>AG: Return 2FA required
        AG->>FE: 2FA challenge required
        FE->>U: Show 2FA input form

        U->>FE: Enter 2FA code
        FE->>AG: POST /auth/2fa/verify
        AG->>US: Route 2FA verification

        US->>US: Validate login token
        US->>US: Verify TOTP/backup code

        alt 2FA code valid
            US->>US: Generate JWT tokens
            US->>US: Create session
            US->>K: Publish LoginSuccess event

            K->>NS: Process login notification
            NS->>NS: Send login alert (if new device)

            US->>AG: Return JWT + user data
            AG->>FE: Login successful
            FE->>U: Redirect to dashboard
        else 2FA code invalid
            US->>AG: Return 2FA error
            AG->>FE: Invalid 2FA code
            FE->>U: Show error + retry
        end
    else 2FA disabled
        US->>US: Generate JWT tokens
        US->>US: Create session
        US->>AG: Return JWT + user data
        AG->>FE: Login successful
        FE->>U: Redirect to dashboard
    end
```

### 2. 🔑 Password Change Flow

#### **Bước 1: Yêu cầu Đổi Mật khẩu**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka

    U->>FE: Go to Security Settings
    FE->>FE: Load change password form

    U->>FE: Enter current + new password
    FE->>FE: Validate password strength
    FE->>AG: POST /users/change-password
    AG->>US: Route password change request

    US->>US: Validate JWT token
    US->>US: Verify current password

    alt Current password correct
        US->>US: Validate new password rules

        alt New password valid
            US->>US: Hash new password
            US->>US: Update password in DB
            US->>US: Invalidate all sessions (except current)

            US->>K: Publish PasswordChanged event
            K->>NS: Process security notification
            NS->>NS: Send password change confirmation

            US->>AG: Return success
            AG->>FE: Password changed successfully
            FE->>U: Show success message
        else New password invalid
            US->>AG: Return validation error
            AG->>FE: Password validation failed
            FE->>U: Show validation errors
        end
    else Current password incorrect
        US->>AG: Return authentication error
        AG->>FE: Current password incorrect
        FE->>U: Show error message
    end
```

#### **Bước 2: Forgot Password Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant R as Redis

    U->>FE: Click "Forgot Password"
    FE->>FE: Show email input form

    U->>FE: Enter email address
    FE->>AG: POST /auth/forgot-password
    AG->>US: Route forgot password request

    US->>US: Check if email exists

    alt Email exists
        US->>US: Generate reset token
        US->>R: Store reset token (TTL: 1 hour)
        US->>NS: Send reset email

        NS->>NS: Generate reset link
        NS->>NS: Send email with reset link

        US->>AG: Return success (generic message)
        AG->>FE: Reset email sent
        FE->>U: Check email for reset link
    else Email not found
        US->>AG: Return success (security - don't reveal)
        AG->>FE: Reset email sent
        FE->>U: Check email for reset link
    end

    Note over U,NS: User clicks reset link in email

    U->>FE: Click reset link
    FE->>FE: Extract token from URL
    FE->>AG: GET /auth/reset-password/{token}
    AG->>US: Validate reset token

    US->>R: Check token validity

    alt Token valid
        US->>AG: Return token validation success
        AG->>FE: Show new password form
        FE->>U: Enter new password

        U->>FE: Submit new password
        FE->>AG: POST /auth/reset-password
        AG->>US: Process password reset

        US->>US: Hash new password
        US->>US: Update password in DB
        US->>US: Invalidate all sessions
        US->>R: Delete reset token

        US->>AG: Return success
        AG->>FE: Password reset successful
        FE->>U: Redirect to login
    else Token invalid/expired
        US->>AG: Return token error
        AG->>FE: Invalid/expired token
        FE->>U: Show error + request new reset
    end
```

### 3. 🔓 Disable 2FA Flow

#### **Tắt 2FA với Password + Current 2FA Code**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka

    U->>FE: Go to Security Settings
    FE->>FE: Show "Disable 2FA" option

    U->>FE: Click "Disable 2FA"
    FE->>FE: Show confirmation form

    U->>FE: Enter password + 2FA code
    FE->>AG: POST /auth/2fa/disable
    AG->>US: Route disable request

    US->>US: Validate JWT token
    US->>US: Verify password
    US->>US: Verify 2FA code

    alt All validations pass
        US->>US: Remove 2FA settings from DB
        US->>US: Invalidate backup codes
        US->>K: Publish TwoFactorDisabled event

        K->>NS: Process security notification
        NS->>NS: Send 2FA disabled alert

        US->>AG: Return success
        AG->>FE: 2FA disabled successfully
        FE->>U: Show success + security warning
    else Validation fails
        US->>AG: Return validation error
        AG->>FE: Invalid credentials/code
        FE->>U: Show error message
    end
```

### 4. 🚨 Security Event Flows

#### **Suspicious Login Detection**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka
    participant AS as Analytics Service

    U->>FE: Attempt login from new location
    FE->>AG: POST /auth/login
    AG->>US: Route login request

    US->>US: Validate credentials
    US->>US: Check login patterns
    US->>AS: Analyze login behavior

    AS->>AS: Check IP geolocation
    AS->>AS: Check device fingerprint
    AS->>AS: Check login frequency

    alt Suspicious activity detected
        AS->>US: Return risk score: HIGH
        US->>US: Flag as suspicious
        US->>K: Publish SuspiciousActivity event

        K->>NS: Process security alert
        NS->>NS: Send immediate security alert
        NS->>NS: Send SMS/email warning

        US->>AG: Return login challenge
        AG->>FE: Additional verification required
        FE->>U: Show security challenge

        U->>FE: Complete additional verification
        FE->>AG: POST /auth/verify-security-challenge
        AG->>US: Process verification

        alt Verification successful
            US->>US: Complete login
            US->>K: Publish LoginSuccess event
            US->>AG: Return JWT tokens
            AG->>FE: Login successful
            FE->>U: Access granted
        else Verification failed
            US->>US: Block login attempt
            US->>K: Publish LoginBlocked event
            US->>AG: Return access denied
            AG->>FE: Access denied
            FE->>U: Show security message
        end
    else Normal login pattern
        AS->>US: Return risk score: LOW
        US->>US: Proceed with normal login
        US->>AG: Return JWT tokens
        AG->>FE: Login successful
        FE->>U: Access granted
    end
```

#### **Account Lockout Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant NS as Notification Service
    participant K as Kafka
    participant R as Redis

    loop Multiple failed attempts
        U->>FE: Enter wrong password
        FE->>AG: POST /auth/login
        AG->>US: Route login request

        US->>US: Validate credentials (fail)
        US->>R: Increment failed attempts counter
        US->>R: Check attempt count

        alt Attempts < threshold (5)
            US->>AG: Return login failed
            AG->>FE: Invalid credentials
            FE->>U: Show error + remaining attempts
        else Attempts >= threshold
            US->>R: Set account lockout (TTL: 30 min)
            US->>K: Publish AccountLocked event

            K->>NS: Process security notification
            NS->>NS: Send account locked alert
            NS->>NS: Send unlock instructions

            US->>AG: Return account locked
            AG->>FE: Account temporarily locked
            FE->>U: Show lockout message + unlock options
        end
    end

    Note over U,NS: User waits or requests unlock

    alt Wait for auto-unlock
        R->>R: TTL expires (30 minutes)
        R->>US: Lockout automatically removed
    else Request manual unlock
        U->>FE: Click "Unlock Account"
        FE->>AG: POST /auth/unlock-account
        AG->>US: Route unlock request

        US->>NS: Send unlock verification email
        NS->>NS: Generate unlock token
        NS->>NS: Send email with unlock link

        U->>FE: Click unlock link in email
        FE->>AG: GET /auth/unlock/{token}
        AG->>US: Validate unlock token

        US->>R: Remove lockout
        US->>K: Publish AccountUnlocked event
        US->>AG: Return success
        AG->>FE: Account unlocked
        FE->>U: Show success + login prompt
    end
```

### 5. 🔐 Session Management Flow

#### **Multi-Device Session Control**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant US as User Service
    participant R as Redis
    participant NS as Notification Service

    U->>FE: Go to Security Settings
    FE->>AG: GET /auth/sessions
    AG->>US: Route sessions request

    US->>R: Get active sessions
    US->>US: Enrich with device info
    US->>AG: Return sessions list
    AG->>FE: Display active sessions
    FE->>U: Show sessions with device details

    U->>FE: Click "Logout from device"
    FE->>AG: DELETE /auth/sessions/{sessionId}
    AG->>US: Route session termination

    US->>R: Invalidate specific session
    US->>US: Log security event
    US->>NS: Send device logout notification

    US->>AG: Return success
    AG->>FE: Session terminated
    FE->>U: Show confirmation

    Note over U,FE: Option to logout from all devices

    U->>FE: Click "Logout from all devices"
    FE->>AG: POST /auth/sessions/revoke-all
    AG->>US: Route mass logout

    US->>R: Invalidate all sessions (except current)
    US->>US: Log security event
    US->>NS: Send mass logout notification

    US->>AG: Return revoked count
    AG->>FE: All devices logged out
    FE->>U: Show confirmation + security advice
```

### 6. 🛠️ Implementation Details

#### **2FA Security Configuration**
```yaml
# 2FA Settings
two_factor_auth:
  totp:
    issuer: "SocialAI"
    algorithm: "SHA1"
    digits: 6
    period: 30
    window: 1  # Allow 1 time step tolerance

  backup_codes:
    count: 8
    length: 8
    format: "numeric"
    single_use: true

  setup:
    qr_code_size: 256
    setup_token_ttl: 600  # 10 minutes
    max_setup_attempts: 3

# Password Policy
password_policy:
  min_length: 8
  max_length: 128
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_special_chars: true
  forbidden_patterns:
    - "password"
    - "123456"
    - "qwerty"
  history_check: 5  # Don't reuse last 5 passwords

# Account Security
account_security:
  max_login_attempts: 5
  lockout_duration: 1800  # 30 minutes
  password_reset_ttl: 3600  # 1 hour
  session_timeout: 86400  # 24 hours
  max_concurrent_sessions: 5

  suspicious_activity:
    new_location_threshold: 100  # km
    rapid_login_threshold: 5  # attempts per minute
    unusual_time_threshold: 2  # hours outside normal pattern
```

#### **Security Event Types và Responses**
```yaml
security_events:
  login_success:
    log_level: "info"
    notification: false
    retention: "90d"

  login_failed:
    log_level: "warn"
    notification: false
    retention: "90d"

  login_from_new_device:
    log_level: "warn"
    notification: true
    channels: ["email", "push"]
    retention: "1y"

  login_from_new_location:
    log_level: "warn"
    notification: true
    channels: ["email", "sms"]
    retention: "1y"

  account_locked:
    log_level: "error"
    notification: true
    channels: ["email", "sms"]
    retention: "1y"

  password_changed:
    log_level: "warn"
    notification: true
    channels: ["email"]
    retention: "1y"

  two_factor_enabled:
    log_level: "info"
    notification: true
    channels: ["email"]
    retention: "1y"

  two_factor_disabled:
    log_level: "warn"
    notification: true
    channels: ["email", "sms"]
    retention: "1y"

  suspicious_activity:
    log_level: "error"
    notification: true
    channels: ["email", "sms", "push"]
    retention: "2y"

  session_hijack_detected:
    log_level: "critical"
    notification: true
    channels: ["email", "sms", "push"]
    retention: "2y"
    action: "force_logout_all"
```

#### **Error Handling và Recovery**
```yaml
error_scenarios:
  2fa_setup_failed:
    causes:
      - "Invalid QR code scan"
      - "Time sync issues"
      - "Network connectivity"
    recovery:
      - "Regenerate QR code"
      - "Provide manual entry option"
      - "Show troubleshooting guide"

  2fa_verification_failed:
    causes:
      - "Incorrect code"
      - "Time drift"
      - "Used backup code"
    recovery:
      - "Allow retry with fresh code"
      - "Suggest time sync"
      - "Offer backup code option"

  password_reset_failed:
    causes:
      - "Expired token"
      - "Invalid token"
      - "Email delivery issues"
    recovery:
      - "Generate new reset token"
      - "Alternative contact methods"
      - "Manual support escalation"

  account_lockout:
    causes:
      - "Multiple failed logins"
      - "Suspicious activity"
      - "Security policy violation"
    recovery:
      - "Time-based auto-unlock"
      - "Email verification unlock"
      - "Support ticket resolution"
```

### 7. 📊 Security Monitoring và Analytics

#### **Security Metrics Tracking**
```yaml
security_metrics:
  authentication:
    - login_success_rate
    - login_failure_rate
    - 2fa_adoption_rate
    - 2fa_success_rate
    - password_reset_frequency

  account_security:
    - account_lockout_frequency
    - suspicious_activity_detection
    - session_hijack_attempts
    - concurrent_session_violations

  user_behavior:
    - login_location_patterns
    - login_time_patterns
    - device_usage_patterns
    - security_setting_changes

  system_security:
    - failed_authentication_attempts
    - brute_force_attack_detection
    - rate_limiting_triggers
    - security_policy_violations
```

#### **Real-time Security Dashboard**
```yaml
dashboard_widgets:
  current_threats:
    - active_lockouts
    - suspicious_activities
    - failed_login_spikes
    - new_device_logins

  security_health:
    - 2fa_enabled_percentage
    - strong_password_compliance
    - session_security_score
    - account_security_rating

  incident_response:
    - pending_security_reviews
    - escalated_support_tickets
    - automated_responses_triggered
    - manual_interventions_required
```

### 8. 🔄 Integration với Other Services

#### **User Service → Credit Service**
```protobuf
// Security-related credit operations
service SecurityCreditService {
  rpc LogSecurityEvent(SecurityEventRequest) returns (SecurityEventResponse);
  rpc CheckSecurityCompliance(ComplianceCheckRequest) returns (ComplianceCheckResponse);
}

message SecurityEventRequest {
  string user_id = 1;
  string event_type = 2;
  string event_details = 3;
  string ip_address = 4;
  string user_agent = 5;
  string location = 6;
  int64 timestamp = 7;
}
```

#### **User Service → Notification Service**
```protobuf
// Security notification integration
service SecurityNotificationService {
  rpc SendSecurityAlert(SecurityAlertRequest) returns (SecurityAlertResponse);
  rpc SendSecurityDigest(SecurityDigestRequest) returns (SecurityDigestResponse);
}

message SecurityAlertRequest {
  string user_id = 1;
  string alert_type = 2;
  map<string, string> alert_data = 3;
  repeated string channels = 4;
  string priority = 5;
  bool immediate = 6;
}
```

#### **User Service → Analytics Service**
```protobuf
// Security analytics integration
service SecurityAnalyticsService {
  rpc TrackSecurityEvent(SecurityEventTrackingRequest) returns (SecurityEventTrackingResponse);
  rpc AnalyzeUserBehavior(UserBehaviorAnalysisRequest) returns (UserBehaviorAnalysisResponse);
  rpc DetectAnomalies(AnomalyDetectionRequest) returns (AnomalyDetectionResponse);
}

message UserBehaviorAnalysisRequest {
  string user_id = 1;
  string analysis_type = 2; // "login_pattern", "device_usage", "location_pattern"
  int64 time_window_start = 3;
  int64 time_window_end = 4;
}

message AnomalyDetectionResponse {
  bool anomaly_detected = 1;
  string anomaly_type = 2;
  float risk_score = 3; // 0.0 to 1.0
  repeated string risk_factors = 4;
  string recommended_action = 5;
}
```

## 🧠 RAG Processing Service Flows

### 1. 📄 Document Processing Pipeline Flow

#### **Document Upload và Processing Trigger**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant CMS as Content Management Service
    participant AS as Asset Service
    participant K as Kafka
    participant RPS as RAG Processing Service
    participant R as Redis

    Note over U,R: Scenario A: Template Creation với RAG Training

    Note over U,S3: Step 1: Request Upload cho Training Document
    U->>FE: Select training document file
    FE->>AG: POST /assets/request-upload
    AG->>AS: RequestUpload(filename, filesize, filetype, purpose=rag_training)

    AS->>US: ValidateToken() + CheckPermission()
    AS->>CS: CheckStorageQuota(user_id, filesize)
    AS->>AS: Create pending_upload record
    AS->>S3: GeneratePresignedURL(bucket, key, expiry=15min)
    AS->>AG: UploadResponse(presigned_url, upload_id, s3_key)
    AG->>FE: Return upload URL + upload_id

    U->>S3: Direct upload using presigned URL

    Note over U,AS: Step 2: Confirm Upload và Validate File
    FE->>AG: POST /assets/confirm-upload/{upload_id}
    AG->>AS: ConfirmUpload(upload_id)

    AS->>S3: Verify file exists
    AS->>AS: Download và validate file
    AS->>AS: Check file integrity, scan malware
    AS->>AS: Verify not garbage/corrupted

    alt File validation successful
        AS->>AS: Create asset record
        AS->>AG: Return asset_id + validation results
        AG->>FE: File validated successfully

        Note over U,CMS: Step 3: Create Template với Validated File
        U->>FE: Create template (with training_asset_id)
        FE->>AG: POST /templates {training_asset_id, template_data}
        AG->>CMS: Route template creation

        CMS->>AS: Verify asset exists and valid
        CMS->>CMS: Create template record
        CMS->>CMS: Auto-detect training needed (asset_id provided)
        CMS->>K: Publish RAGTrainingRequested event
        CMS->>AG: Return success + training_triggered: true

        K->>RPS: Consume RAGTrainingRequested event
        RPS->>RPS: Start async processing
    else File validation failed
        AS->>S3: Delete invalid file
        AS->>AG: Return validation errors
        AG->>FE: Show validation errors
    end

    Note over U,R: Scenario B: Template Update với File Change
    U->>FE: Select new training file
    FE->>AG: POST /assets/request-upload
    AG->>AS: RequestUpload(template_id={id})

    AS->>AS: Get existing template file hash
    AS->>S3: GeneratePresignedURL()
    AS->>AG: Return upload URL

    U->>S3: Direct upload new file
    FE->>AG: POST /assets/confirm-upload/{upload_id}
    AG->>AS: ConfirmUpload()

    AS->>AS: Validate new file
    AS->>AS: Compare file hashes

    alt File content changed and valid
        AS->>AG: Return new asset_id + change detected
        FE->>AG: PUT /templates/{id} {training_asset_id: new_asset_id}
        AG->>CMS: Route template update

        CMS->>CMS: Compare old vs new training asset
        CMS->>CMS: Auto-detect retraining needed
        CMS->>K: Publish RAGRetrainingRequested event
        CMS->>AG: Return success + retraining_triggered: true

        K->>RPS: Consume retraining event
        RPS->>RPS: Cleanup old RAG data
        RPS->>RPS: Start reprocessing
    else File unchanged
        AS->>S3: Delete duplicate file
        AS->>AG: Return unchanged status
        AG->>FE: No retraining needed
    else File invalid
        AS->>S3: Delete invalid file
        AS->>AG: Return validation errors
    end
```

#### **RAG Document Processing Pipeline**
```mermaid
sequenceDiagram
    participant RPS as RAG Processing Service
    participant S3 as S3/MinIO Storage
    participant Neo4j as Neo4j Graph DB
    participant Qdrant as Qdrant Vector DB
    participant K as Kafka
    participant NS as Notification Service

    RPS->>S3: Download document file
    S3->>RPS: Return file content

    RPS->>RPS: Parse document (PDF/DOCX/TXT)
    RPS->>RPS: Extract text content
    RPS->>RPS: Clean and preprocess text

    Note over RPS: Text Chunking Phase
    RPS->>RPS: Split text into chunks (1000 chars, 200 overlap)
    RPS->>RPS: Create chunk metadata

    Note over RPS: Embedding Generation Phase
    RPS->>RPS: Generate embeddings for chunks
    RPS->>RPS: Generate document-level embedding

    Note over RPS: Entity Extraction Phase
    RPS->>RPS: Extract named entities (spaCy)
    RPS->>RPS: Extract concepts and topics
    RPS->>RPS: Calculate entity confidence scores

    Note over RPS: Relationship Extraction Phase
    RPS->>RPS: Identify entity relationships
    RPS->>RPS: Extract semantic relationships
    RPS->>RPS: Build knowledge graph structure

    Note over RPS: Storage Phase
    par Store in Neo4j
        RPS->>Neo4j: Create Document node
        RPS->>Neo4j: Create Entity nodes
        RPS->>Neo4j: Create Concept nodes
        RPS->>Neo4j: Create relationships
        RPS->>Neo4j: Create indexes
    and Store in Qdrant
        RPS->>Qdrant: Store chunk embeddings
        RPS->>Qdrant: Store entity embeddings
        RPS->>Qdrant: Store concept embeddings
        RPS->>Qdrant: Create collection indexes
    end

    RPS->>K: Publish RAGProcessingCompleted event
    K->>NS: Send processing completion notification

    Note over RPS: Processing completed successfully
```

### 2. 🔍 RAG Retrieval Flow

#### **Semantic Search và Context Retrieval**
```mermaid
sequenceDiagram
    participant ACS as AI Content Service
    participant RPS as RAG Processing Service
    participant Qdrant as Qdrant Vector DB
    participant Neo4j as Neo4j Graph DB
    participant R as Redis

    ACS->>RPS: gRPC RetrieveContext(query, user_id)
    RPS->>RPS: Generate query embedding

    Note over RPS: Vector Search Phase
    RPS->>Qdrant: Search similar chunks
    Qdrant->>RPS: Return top-k similar chunks

    RPS->>Qdrant: Search similar entities
    Qdrant->>RPS: Return related entities

    Note over RPS: Graph Traversal Phase
    RPS->>Neo4j: Find connected entities
    Neo4j->>RPS: Return entity relationships

    RPS->>Neo4j: Get concept hierarchy
    Neo4j->>RPS: Return concept relationships

    Note over RPS: Context Assembly Phase
    RPS->>RPS: Rank and filter results
    RPS->>RPS: Assemble context chunks
    RPS->>RPS: Add entity information
    RPS->>RPS: Include relationship context

    RPS->>R: Cache retrieval results (TTL: 1h)
    RPS->>ACS: Return enriched context

    Note over ACS: Use context for AI generation
```

### 3. 🕸️ Knowledge Graph Construction Flow

#### **Entity và Relationship Extraction**
```mermaid
sequenceDiagram
    participant RPS as RAG Processing Service
    participant NLP as NLP Pipeline
    participant ML as ML Models
    participant Neo4j as Neo4j Graph DB

    RPS->>NLP: Process document chunks

    Note over NLP: Named Entity Recognition
    NLP->>ML: spaCy NER model
    ML->>NLP: Return entities with confidence

    Note over NLP: Concept Extraction
    NLP->>ML: Topic modeling (LDA/BERT)
    ML->>NLP: Return topics and concepts

    Note over NLP: Relationship Extraction
    NLP->>ML: Dependency parsing
    ML->>NLP: Return syntactic relationships

    NLP->>ML: Semantic relationship detection
    ML->>NLP: Return semantic relationships

    NLP->>RPS: Return extraction results

    Note over RPS: Graph Construction
    RPS->>RPS: Merge duplicate entities
    RPS->>RPS: Resolve entity references
    RPS->>RPS: Filter low-confidence relationships
    RPS->>RPS: Create graph structure

    RPS->>Neo4j: Batch insert nodes
    RPS->>Neo4j: Batch insert relationships
    RPS->>Neo4j: Update graph statistics

    Note over Neo4j: Knowledge graph ready for queries
```

### 4. 📊 Graph RAG Query Flow

#### **Multi-hop Reasoning với Graph Traversal**
```mermaid
sequenceDiagram
    participant ACS as AI Content Service
    participant RPS as RAG Processing Service
    participant Neo4j as Neo4j Graph DB
    participant Qdrant as Qdrant Vector DB
    participant LLM as Language Model

    ACS->>RPS: gRPC GraphRAGQuery(complex_question)
    RPS->>RPS: Parse question entities

    Note over RPS: Entity Resolution
    RPS->>Qdrant: Find question entities
    Qdrant->>RPS: Return entity matches

    Note over RPS: Graph Traversal
    RPS->>Neo4j: Multi-hop traversal query
    Neo4j->>RPS: Return connected subgraph

    RPS->>Neo4j: Get relationship paths
    Neo4j->>RPS: Return reasoning paths

    Note over RPS: Context Enrichment
    RPS->>RPS: Collect relevant documents
    RPS->>RPS: Assemble reasoning chain
    RPS->>RPS: Add supporting evidence

    RPS->>ACS: Return graph context + reasoning paths

    ACS->>LLM: Generate answer with graph context
    LLM->>ACS: Return reasoned answer

    Note over ACS: Answer includes reasoning chain
```

### 5. 🛠️ RAG Processing Service Implementation

#### **Service Architecture**
```yaml
rag_processing_service:
  components:
    document_processor:
      - text_extraction
      - chunking_strategy
      - metadata_extraction

    embedding_service:
      - sentence_transformers
      - openai_embeddings
      - custom_models

    entity_extractor:
      - spacy_ner
      - custom_entity_models
      - confidence_scoring

    graph_builder:
      - neo4j_operations
      - relationship_inference
      - graph_optimization

    vector_store:
      - qdrant_operations
      - similarity_search
      - collection_management
```

#### **Processing Configuration**
```yaml
processing_config:
  chunking:
    strategy: "recursive_character"
    chunk_size: 1000
    chunk_overlap: 200
    separators: ["\n\n", "\n", " ", ""]

  embedding:
    model: "sentence-transformers/all-MiniLM-L6-v2"
    dimension: 384
    batch_size: 32
    normalize: true

  entity_extraction:
    models:
      - "en_core_web_sm"  # spaCy
      - "dbmdz/bert-large-cased-finetuned-conll03-english"
    confidence_threshold: 0.8
    max_entities_per_chunk: 50

  relationship_extraction:
    methods:
      - "dependency_parsing"
      - "pattern_matching"
      - "semantic_similarity"
    confidence_threshold: 0.7

  graph_construction:
    max_depth: 3
    min_relationship_strength: 0.6
    entity_merge_threshold: 0.9
```

#### **Database Schemas**

**Neo4j Cypher Schemas:**
```cypher
// Create constraints
CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE;
CREATE CONSTRAINT entity_name_type IF NOT EXISTS FOR (e:Entity) REQUIRE (e.name, e.type) IS UNIQUE;
CREATE CONSTRAINT concept_name IF NOT EXISTS FOR (c:Concept) REQUIRE c.name IS UNIQUE;

// Create indexes
CREATE INDEX document_user IF NOT EXISTS FOR (d:Document) ON (d.user_id);
CREATE INDEX document_workspace IF NOT EXISTS FOR (d:Document) ON (d.workspace_id);
CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type);
CREATE FULLTEXT INDEX document_content IF NOT EXISTS FOR (d:Document) ON EACH [d.title, d.content];
CREATE FULLTEXT INDEX entity_search IF NOT EXISTS FOR (e:Entity) ON EACH [e.name, e.description];

// Sample queries
MATCH (d:Document)-[:MENTIONS]->(e:Entity)-[:RELATES_TO]->(e2:Entity)
WHERE d.user_id = $user_id
RETURN d, e, e2 LIMIT 100;

MATCH path = (e1:Entity)-[:RELATES_TO*1..3]-(e2:Entity)
WHERE e1.name = $entity_name
RETURN path ORDER BY length(path) LIMIT 50;
```

**Qdrant Collection Configurations:**
```python
collections_config = {
    "documents": {
        "vectors": {
            "size": 384,
            "distance": "Cosine"
        },
        "payload_schema": {
            "document_id": "keyword",
            "chunk_id": "keyword",
            "user_id": "keyword",
            "workspace_id": "keyword",
            "content": "text",
            "metadata": "object"
        },
        "optimizers_config": {
            "default_segment_number": 2,
            "max_segment_size": 20000
        }
    },
    "entities": {
        "vectors": {
            "size": 384,
            "distance": "Cosine"
        },
        "payload_schema": {
            "entity_name": "keyword",
            "entity_type": "keyword",
            "confidence": "float",
            "graph_id": "keyword"
        }
    }
}
```

### 6. 🔄 Integration với Other Services

#### **gRPC Service Definitions**
```protobuf
// RAG Processing Service
service RAGProcessingService {
  rpc ProcessDocument(ProcessDocumentRequest) returns (ProcessDocumentResponse);
  rpc GetProcessingStatus(ProcessingStatusRequest) returns (ProcessingStatusResponse);
  rpc RetrieveContext(ContextRetrievalRequest) returns (ContextRetrievalResponse);
  rpc GraphRAGQuery(GraphQueryRequest) returns (GraphQueryResponse);
  rpc SearchSimilarDocuments(SimilaritySearchRequest) returns (SimilaritySearchResponse);
}

message ProcessDocumentRequest {
  string document_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string file_path = 4;
  string document_type = 5;
  map<string, string> processing_options = 6;
}

message ContextRetrievalRequest {
  string query = 1;
  string user_id = 2;
  string workspace_id = 3;
  int32 max_chunks = 4;
  float similarity_threshold = 5;
  repeated string entity_types = 6;
}

message GraphQueryRequest {
  string question = 1;
  string user_id = 2;
  repeated string focus_entities = 3;
  int32 max_hops = 4;
  bool include_reasoning_path = 5;
}

message TemplateRAGRequest {
  string template_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string query = 4;
  int32 max_chunks = 5;
  float similarity_threshold = 6;
}

message TemplateRAGResponse {
  string template_id = 1;
  repeated ContextChunk chunks = 2;
  repeated Entity entities = 3;
  repeated Relationship relationships = 4;
  float relevance_score = 5;
  string reasoning_path = 6;
}
```

#### **Kafka Event Integration**
```yaml
kafka_events:
  consumed_topics:
    - topic: "asset.events"
      events:
        - DocumentUploaded
        - DocumentDeleted
        - DocumentUpdated
    - topic: "content.events"
      events:
        - TemplateCreated
        - TemplateUpdated
        - RAGTrainingRequested
        - RAGRetrainingRequested

  produced_topics:
    - topic: "rag.events"
      events:
        - ProcessingStarted
        - ProcessingCompleted
        - ProcessingFailed
        - RetrainingStarted
        - RetrainingCompleted
        - KnowledgeGraphUpdated
        - EmbeddingsGenerated
        - OldDataCleanedUp

event_schemas:
  RAGTrainingRequested:
    template_id: string
    document_id: string
    user_id: string
    workspace_id: string
    file_path: string
    document_type: string
    training_purpose: string  # "template_creation", "template_update", "direct_upload"
    priority: integer

  RAGRetrainingRequested:
    template_id: string
    old_document_id: string
    new_document_id: string
    user_id: string
    workspace_id: string
    change_reason: string  # "file_content_changed", "file_replaced"
    cleanup_required: boolean

  ProcessingCompleted:
    document_id: string
    template_id: string  # optional
    user_id: string
    workspace_id: string
    chunks_count: integer
    entities_count: integer
    relationships_count: integer
    processing_time: float
    training_purpose: string
```

### 7. 📈 Performance Optimization

#### **Caching Strategy**
```yaml
caching:
  redis_layers:
    embeddings_cache:
      ttl: 86400  # 24 hours
      key_pattern: "emb:{model}:{hash}"

    query_results_cache:
      ttl: 3600   # 1 hour
      key_pattern: "query:{user_id}:{query_hash}"

    entity_cache:
      ttl: 43200  # 12 hours
      key_pattern: "entity:{name}:{type}"

  processing_cache:
    document_chunks:
      ttl: 7200   # 2 hours
      key_pattern: "chunks:{doc_id}"
```

#### **Batch Processing Optimization**
```yaml
batch_processing:
  embedding_generation:
    batch_size: 32
    max_concurrent_batches: 4
    timeout_per_batch: 30

  neo4j_operations:
    batch_size: 1000
    transaction_timeout: 60
    retry_attempts: 3

  qdrant_operations:
    batch_size: 100
    parallel_uploads: 8
    connection_pool_size: 10
```

## 🎯 Next Steps

1. **Proof of Concept:** Implement User Service với gRPC
2. **Infrastructure Setup:** Kubernetes cluster, Kafka, S3/MinIO
3. **Team Training:** gRPC, Kafka, microservices best practices
4. **Gradual Migration:** Theo từng phase đã định
5. **Performance Testing:** Load testing gRPC services và Kafka throughput
6. **Documentation:** gRPC contracts, Kafka schemas, service dependencies

## 📊 Architecture Summary

### Service Count: 10 Services
1. **User Service** - Authentication & user management
2. **AI Content Service** - Content generation với AI
3. **Content Management Service** - Posts + Templates + Workspaces (merged)
4. **Integration Service** - Social platform connections
5. **Asset Service** - File management với S3/MinIO
6. **Credit & Billing Service** - Payment & subscription
7. **Analytics Service** - Metrics & reporting
8. **Notification Service** - Multi-channel notifications
9. **LangChain RAG Processing Service** - Training data processing với LangChain, knowledge graph, vector embeddings
10. **Data Management Service** - Data backup, account deletion, GDPR compliance

### Key Technology Decisions
- **Internal Communication:** gRPC cho high performance
- **Async Processing:** Apache Kafka cho event streaming
- **File Storage:** S3/MinIO cho scalable object storage
- **Database:** PostgreSQL per service với specialized stores
- **API Gateway:** Simple routing, no authentication
- **Authentication:** Service-based validation với User Service
- **Credit Management:** Two-phase commit pattern (Reserve → Consume/Refund)
- **Free Operations:** Post publishing, scheduling, và management không tốn credits
- **Unified Publishing:** Schedule Post Flow gọi qua Publish Immediately Flow để đồng nhất code

## 🎯 Benefits của Service-based Authentication Architecture:

### **Service Autonomy:**
- **Each service validates its own requests** - không phụ thuộc API Gateway
- **Service biết chính xác user context** cho business logic
- **Independent deployment** - services có thể update auth logic riêng
- **Fault isolation** - API Gateway down không ảnh hưởng auth

### **Performance:**
- **Redis caching tại service level** giảm User Service calls
- **gRPC binary protocol** faster than REST cho internal calls
- **Circuit breaker** prevent cascade failures
- **Local caching** reduces network latency

### **Security:**
- **JWT validation tại service** đảm bảo token integrity
- **Fine-grained permissions** per service endpoint
- **Service-specific permission checks** cho business logic
- **Audit trail** với service context trong logs

### **Scalability:**
- **Distributed authentication** - không có single point of failure
- **Service-level caching** scales với service instances
- **Independent rate limiting** per service
- **Load balancing** không bị bottleneck bởi auth

### **Developer Experience:**
- **Service ownership** của authentication logic
- **Clear middleware pattern** dễ implement và test
- **User context available** trong toàn bộ business logic
- **Consistent error handling** across services

### **Operational Benefits:**
- **API Gateway simplicity** - chỉ làm routing
- **Service-specific monitoring** của auth metrics
- **Independent troubleshooting** per service
- **Flexible auth policies** per service requirements

### Credit Cost Configuration
```yaml
# Model costs in credits
ai_models:
  text_generation:
    gpt-4-turbo: 5
    claude-3.5-sonnet: 3
    gemini-pro: 2
    gpt-3.5-turbo: 1

  image_generation:
    dall-e-3: 10
    dall-e-2: 5
    stable-diffusion: 3
    midjourney: 8

  content_improvement:
    grammar_check: 1
    style_enhancement: 2
    engagement_optimization: 3

# Template costs
template_marketplace:
  free_templates: 0
  premium_templates: 1-50  # Set by template creator

# Other operations (NOT post publishing)
operations:
  rag_document_processing: 2
  bulk_post_scheduling: 1
  advanced_analytics_report: 5

# Free operations (no credits required)
free_operations:
  post_publishing: 0        # Đăng bài lên social platforms
  post_scheduling: 0        # Lên lịch đăng bài
  post_management: 0        # CRUD operations cho posts
  workspace_collaboration: 0 # Team collaboration features
  basic_analytics: 0        # Basic performance metrics
```

---

## 📋 Service Specifications Chi tiết

### 5. 📁 Asset Service
**Trách nhiệm:** Quản lý file, ảnh, tài liệu và RAG documents trên S3/MinIO

**Database Tables:**
- `asset_folders` - Cấu trúc thư mục
- `assets` - File metadata (S3 keys, URLs, etc.)
- `rag_documents` - Tài liệu cho RAG system

**gRPC Services:**
- `AssetService` - Upload, download, manage files
- `FolderService` - Quản lý thư mục
- `RAGDocumentService` - RAG document processing
- `ImageProcessingService` - Resize, optimize images

**Technologies:**
- AWS S3/MinIO cho object storage
- ImageMagick cho image processing
- Vector database cho document embeddings
- CDN cho fast delivery
- gRPC streaming cho large file uploads
- Apache Kafka cho asset events

### 6. 💳 Credit & Billing Service
**Trách nhiệm:** Quản lý credits, gói dịch vụ và thanh toán

**Database Tables:**
- `credit_plans` - Các gói dịch vụ
- `user_credits` - Credits hiện tại của user
- `credit_transactions` - Lịch sử giao dịch
- `subscriptions` - Đăng ký gói
- `payments` - Thanh toán
- `bank_transfers` - Chuyển khoản ngân hàng
- `payment_confirmations` - Xác nhận thanh toán
- `referrals` - Chương trình giới thiệu

**gRPC Services:**
- `CreditService` - Quản lý credits
- `BillingService` - Xử lý thanh toán
- `BankTransferService` - Xử lý chuyển khoản ngân hàng
- `SubscriptionService` - Quản lý subscription
- `PaymentConfirmationService` - Xác nhận thanh toán
- `ReferralService` - Chương trình giới thiệu

**Technologies:**
- Stripe/PayPal cho online payment processing
- Bank API integration cho chuyển khoản
- Apache Kafka cho billing events
- Event sourcing cho transaction history
- gRPC cho internal credit checks
- Redis cho payment session management

### 7. 📊 Analytics Service
**Trách nhiệm:** Thu thập, xử lý và báo cáo thống kê

**Database Tables:**
- `post_analytics` - Metrics từ social platforms
- `user_activity` - Hoạt động người dùng
- `system_metrics` - Metrics hệ thống

**gRPC Services:**
- `AnalyticsCollectionService` - Thu thập metrics
- `ReportingService` - Tạo báo cáo
- `MetricsService` - System metrics

**Technologies:**
- InfluxDB/TimescaleDB cho time-series data
- Apache Kafka cho real-time streaming
- Data pipeline với Apache Airflow
- gRPC streaming cho real-time metrics

### 8. 🔔 Notification Service
**Trách nhiệm:** Gửi thông báo qua email, SMS, push notification

**Database Tables:**
- `notification_templates` - Template thông báo
- `notification_queue` - Hàng đợi gửi
- `notification_history` - Lịch sử gửi
- `user_preferences` - Tùy chọn thông báo
- `notification_channels` - Cấu hình kênh gửi
- `notification_rules` - Rules cho auto-notification

**gRPC Services:**
- `NotificationService` - Gửi thông báo
- `TemplateService` - Quản lý template
- `PreferenceService` - Cài đặt user
- `ChannelService` - Quản lý kênh gửi

**Kafka Consumers:**
- `notification.events` - Primary notification events
- `user.events` - User lifecycle events
- `content.events` - Content-related events
- `billing.events` - Payment và subscription events
- `security.events` - Security alerts

**Technologies:**
- SendGrid/AWS SES cho email
- Twilio cho SMS
- Firebase cho push notifications
- WebSocket cho real-time notifications
- Apache Kafka consumers cho event processing
- gRPC cho internal notification calls
- Redis cho notification queuing và rate limiting

### 9. 🧠 LangChain RAG Processing Service (Python)
**Trách nhiệm:** Xử lý training data cho RAG và Graph RAG với LangChain framework, lưu trữ vào Neo4j và Qdrant

**Database Tables:**
- `rag_documents` - Metadata của documents được xử lý
- `processing_jobs` - Trạng thái xử lý documents
- `extraction_results` - Kết quả entity và relationship extraction
- `embedding_models` - Cấu hình các embedding models

**Neo4j Graph Schema:**
```cypher
// Nodes
(:Document {id, title, content, source, created_at, user_id, workspace_id})
(:Entity {name, type, description, confidence})
(:Concept {name, definition, category})
(:Topic {name, description, domain})
(:Chunk {id, content, chunk_index, start_char, end_char})

// Relationships
(Document)-[:CONTAINS]->(Chunk)
(Document)-[:MENTIONS]->(Entity)
(Document)-[:DISCUSSES]->(Topic)
(Entity)-[:RELATES_TO]->(Entity)
(Entity)-[:BELONGS_TO]->(Concept)
(Topic)-[:SUBTOPIC_OF]->(Topic)
(Chunk)-[:NEXT_CHUNK]->(Chunk)
```

**Qdrant Collections:**
- `documents` - Document chunk embeddings
- `entities` - Entity embeddings
- `concepts` - Concept embeddings

**LangChain Core Components:**
- **Document Loaders:** PyPDFLoader, Docx2txtLoader, TextLoader, UnstructuredHTMLLoader
- **Text Splitters:** RecursiveCharacterTextSplitter, SemanticChunker, MarkdownHeaderTextSplitter
- **Embeddings:** OpenAIEmbeddings, HuggingFaceEmbeddings, SentenceTransformerEmbeddings
- **Vector Stores:** Qdrant integration, Neo4j Vector Index
- **Chains:** RetrievalQA, GraphCypherQAChain, ConversationalRetrievalChain, Custom RAG pipelines

**gRPC Services:**
- `LangChainRAGService` - Document processing và retrieval với LangChain
- `GraphRAGService` - Graph-based question answering
- `HybridRAGService` - Combined vector + graph retrieval
- `EntityExtractionService` - LLM-based entity extraction
- `EmbeddingService` - Generate và manage embeddings
- `VectorSearchService` - Semantic search với LangChain retrievers

**Technologies:**
- **Core Framework:** LangChain 0.1.0
- **Language:** Python 3.11+
- **LLM Integration:** OpenAI API, HuggingFace Transformers
- **Graph DB:** Neo4j 5.15.0 với vector support
- **Vector DB:** Qdrant 1.7.0 client
- **NLP:** spaCy, Sentence Transformers
- **Document Processing:** LangChain document loaders
- **Message Queue:** Kafka với confluent-kafka
- **API:** FastAPI + gRPC
- **Monitoring:** Prometheus metrics

### 10. 🗂️ Data Management Service (Golang)
**Trách nhiệm:** Data backup, account deletion, GDPR compliance

**Database Tables:**
- `data_backups` - User data backup records
- `backup_downloads` - Download tracking
- `account_deletion_requests` - Account deletion requests
- `service_deletion_results` - Per-service deletion results
- `data_retention_policies` - Data retention configuration
- `gdpr_compliance_log` - GDPR compliance audit log

**Core Features:**
- **HTML Export:** Generate HTML pages từ posts với embedded images
- **ZIP Packaging:** Package posts, images, metadata thành ZIP file
- **S3 Integration:** Upload backup files với presigned download URLs
- **Multi-Service Deletion:** Coordinate deletion across all services
- **Grace Period Management:** 7-day grace period cho account deletion
- **GDPR Compliance:** Data export và deletion theo GDPR requirements

**gRPC Services:**
- `DataBackupService` - Create và manage data backups
- `AccountDeletionService` - Handle account deletion requests
- `GDPRComplianceService` - GDPR compliance operations
- `DataRetentionService` - Manage data retention policies

**Technologies:**
- **Language:** Golang 1.24+
- **HTML Generation:** Go templates, CSS styling
- **File Processing:** Archive/zip, image processing
- **S3 Integration:** AWS SDK for Go
- **Database:** PostgreSQL với GORM
- **Message Queue:** Kafka với Sarama
- **API:** gRPC + REST endpoints
- **Monitoring:** Prometheus metrics

---

## 🔄 Event-Driven Architecture

### Kafka Topics và Event Flows

#### User Events Topic: `user.events`
```
UserRegistered → Notification Service (Welcome email) + Credit Service (Initial credits)
UserUpgraded → Credit Service (Update limits) + Analytics Service (Track upgrade)
UserDeleted → All Services (Cleanup data) + Analytics Service (Update metrics)
```

#### Content Events Topic: `content.events`
```
PostCreated → Analytics Service (Initialize metrics) + Notification Service (Team notify)
PostPublished → Integration Service (Sync platforms) + Analytics Service (Track publish)
PostScheduled → Integration Service (Schedule job) + Notification Service (Confirm)
TemplateCreated → Analytics Service (Track creation) + Asset Service (Process thumbnail)
TemplatePublished → Notification Service (Notify followers) + Analytics Service (Update)
WorkspaceCreated → Notification Service (Welcome team) + Analytics Service (Track)
```

#### Billing Events Topic: `billing.events`
```
CreditReserved → Analytics Service (Track reservation) + Audit Service (Log)
CreditConsumed → Analytics Service (Usage tracking) + Notification Service (Low balance alert)
CreditRefunded → Analytics Service (Track refund) + Audit Service (Log)
CreditsAdded → Analytics Service (Track addition) + Notification Service (Balance update)

BankTransferInitiated → Notification Service (Send instructions) + Analytics Service (Track)
BankTransferConfirmed → Credit Service (Add credits) + Subscription Service (Activate plan)
BankTransferExpired → Notification Service (Expiry alert) + Analytics Service (Track)
BankTransferCancelled → Notification Service (Cancellation notice) + Analytics Service (Track)

PaymentCompleted → Credit Service (Add credits) + Notification Service (Receipt)
PaymentFailed → Notification Service (Failure alert) + Analytics Service (Track failure)

SubscriptionActivated → User Service (Update plan) + Notification Service (Welcome)
SubscriptionUpgraded → Credit Service (Add credits) + Notification Service (Upgrade notice)
SubscriptionDowngraded → User Service (Update limits) + Notification Service (Downgrade notice)
SubscriptionExpired → Credit Service (Limit access) + Notification Service (Renewal reminder)
SubscriptionCancelled → User Service (Revert to free) + Notification Service (Cancellation)

InsufficientCredits → Notification Service (Upgrade prompt) + Analytics Service (Track)

Note: Post publishing và scheduling KHÔNG tạo billing events vì không tốn credits
```

#### Data Management Events Topic: `data.events`
```
DataBackupRequested → Backup Service (Process backup) + Analytics Service (Track request)
DataBackupStarted → Notification Service (Processing notification) + Analytics Service (Track start)
DataBackupCompleted → Notification Service (Send download link) + Analytics Service (Track completion)
DataBackupFailed → Notification Service (Error alert) + Analytics Service (Track failure)
DataBackupExpired → Backup Service (Cleanup S3) + Analytics Service (Track expiry)
DataBackupDownloaded → Analytics Service (Track download) + Audit Service (Log access)

AccountDeletionRequested → Deletion Service (Start grace period) + Analytics Service (Track request)
AccountMarkedForDeletion → Notification Service (Grace period notice) + Analytics Service (Track marking)
AccountDeletionCancelled → Notification Service (Cancellation notice) + Analytics Service (Track cancellation)
AccountDeletionStarted → All Services (Begin data cleanup) + Analytics Service (Track start)
AccountDeletionCompleted → Notification Service (Final confirmation) + Analytics Service (Track completion)
AccountDeletionFailed → Notification Service (Error alert) + Analytics Service (Track failure)
```

#### Platform Integration Events Topic: `platform.events`
```
PlatformConnectionInitiated → Analytics Service (Track connection attempts) + Audit Service (Log)
PlatformConnected → Notification Service (Connection success) + Analytics Service (Track success)
PlatformConnectionFailed → Notification Service (Connection error) + Analytics Service (Track failure)
PlatformDisconnected → Notification Service (Disconnection notice) + Analytics Service (Track)
PlatformTokenRefreshed → Analytics Service (Track refresh) + Audit Service (Log)
PlatformTokenExpired → Notification Service (Reconnection prompt) + Analytics Service (Track)

MultiPlatformPublishStarted → Analytics Service (Track publish attempts) + Audit Service (Log)
MultiPlatformPublishCompleted → Notification Service (Publish results) + Analytics Service (Track results)
PlatformPublishFailed → Notification Service (Failure alert) + Analytics Service (Track failure)
PlatformRateLimitHit → Analytics Service (Track rate limits) + Retry Service (Schedule retry)

PlatformAnalyticsSynced → Analytics Service (Process metrics) + Dashboard Service (Update data)
PlatformAnalyticsFailed → Analytics Service (Track sync failures) + Notification Service (Alert)
PlatformContentModerated → Notification Service (Moderation alert) + Analytics Service (Track)
PlatformAccountSuspended → Notification Service (Suspension alert) + Analytics Service (Track)
```

#### Asset Events Topic: `asset.events`
```
UploadRequested → Analytics Service (Track upload attempts) + Audit Service (Log)
FileUploaded → Asset Service (Process) + Analytics Service (Track storage) + Notification Service (Confirm)
FileProcessingStarted → Analytics Service (Track processing) + Notification Service (Status update)
FileProcessingCompleted → Analytics Service (Update metrics) + Notification Service (Ready notification)
FileProcessingFailed → Analytics Service (Track failures) + Notification Service (Error alert)
ImageGenerated → Asset Service (Store) + Content Management (Link to post)
ThumbnailGenerated → Asset Service (Update metadata) + Content Management (Update preview)
RAGDocumentProcessed → AI Content Service (Update embeddings) + Notification Service (Ready for use)
VirusScanCompleted → Asset Service (Update status) + Security Service (Log scan results)
VirusDetected → Asset Service (Quarantine) + Security Service (Alert) + Notification Service (Security warning)
AssetDeleted → Analytics Service (Update usage) + Audit Service (Log deletion)
StorageQuotaExceeded → Notification Service (Quota warning) + Analytics Service (Track overage)
OrphanedFileDetected → Asset Service (Cleanup) + Analytics Service (Track cleanup)
```

#### Asset Event Schemas:
```json
{
  "eventType": "FileUploaded",
  "data": {
    "assetId": "asset_123",
    "uploadId": "upload_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "filename": "marketing-plan.pdf",
    "originalFilename": "Marketing Plan 2024.pdf",
    "filesize": 2048576,
    "contentType": "application/pdf",
    "s3Key": "users/user_789/documents/2024/02/asset_123.pdf",
    "s3Bucket": "socialai-assets-prod",
    "purpose": "rag_document",
    "checksum": "d41d8cd98f00b204e9800998ecf8427e",
    "uploadedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "file_uploaded_success"
  }
}

{
  "eventType": "RAGDocumentProcessed",
  "data": {
    "assetId": "asset_123",
    "userId": "user_789",
    "filename": "marketing-plan.pdf",
    "processingId": "proc_456",
    "totalChunks": 25,
    "totalCharacters": 15420,
    "textPreview": "This marketing plan outlines our strategy for 2024...",
    "embeddingModel": "openai-text-embedding-ada-002",
    "vectorCount": 25,
    "processingTime": 45.2,
    "processedAt": "2024-02-15T10:32:15Z"
  },
  "notificationConfig": {
    "channels": ["push", "websocket"],
    "priority": "normal",
    "template": "rag_document_ready"
  }
}

{
  "eventType": "VirusDetected",
  "data": {
    "assetId": "asset_789",
    "uploadId": "upload_101",
    "userId": "user_456",
    "filename": "suspicious-file.exe",
    "virusName": "Trojan.Generic.123456",
    "scanEngine": "ClamAV",
    "detectedAt": "2024-02-15T10:30:00Z",
    "quarantined": true,
    "s3Key": "quarantine/2024/02/15/asset_789.exe"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "critical",
    "template": "virus_detected_alert"
  }
}

{
  "eventType": "StorageQuotaExceeded",
  "data": {
    "userId": "user_123",
    "currentUsage": 1073741824,
    "quotaLimit": 1000000000,
    "overageBytes": 73741824,
    "plan": "pro",
    "failedUpload": {
      "filename": "large-video.mp4",
      "filesize": 104857600
    },
    "detectedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "storage_quota_exceeded"
  }
}
```

#### Notification Events Topic: `notification.events`
```
UserWelcome → Notification Service (Send welcome email + setup guide)
UserUpgraded → Notification Service (Send upgrade confirmation + new features)
UserDowngraded → Notification Service (Send downgrade notice + limitations)
CreditLowWarning → Notification Service (Send low credit alert + upgrade prompt)
CreditExpired → Notification Service (Send expired notice + renewal link)
PaymentSuccess → Notification Service (Send receipt + credit added confirmation)
PaymentFailed → Notification Service (Send failure notice + retry instructions)
SubscriptionExpiring → Notification Service (Send renewal reminder)
SubscriptionCancelled → Notification Service (Send cancellation confirmation)

PostPublished → Notification Service (Notify team members + social confirmation)
PostPublishedImmediately → Notification Service (Send immediate publish results)
ScheduledPostPublished → Notification Service (Send scheduled publish results)
ScheduledPostFailed → Notification Service (Send scheduled publish failure alert)
ScheduledPostPartialSuccess → Notification Service (Send partial success notification)
ScheduledPostRetry → Notification Service (Send retry notification)
PostScheduled → Notification Service (Send schedule confirmation)
ScheduleUpdated → Notification Service (Send schedule change notification)
ScheduleCancelled → Notification Service (Send cancellation confirmation)
PostFailed → Notification Service (Send failure alert + retry options)
PostAnalyticsReady → Notification Service (Send performance report)

TemplatePublished → Notification Service (Notify followers + marketplace update)
TemplatePurchased → Notification Service (Send purchase confirmation + usage guide)
TemplateApproved → Notification Service (Send approval notice + marketplace live)
TemplateRejected → Notification Service (Send rejection notice + improvement tips)
TemplateEarningsReady → Notification Service (Send earnings report)

WorkspaceInvitation → Notification Service (Send invitation email + join link)
WorkspaceMemberJoined → Notification Service (Notify team + welcome new member)
WorkspaceMemberLeft → Notification Service (Notify team + member departure)
WorkspaceRoleChanged → Notification Service (Send role change notification)

AIGenerationComplete → Notification Service (Send completion notice + result preview)
AIGenerationFailed → Notification Service (Send failure notice + support link)
RAGDocumentProcessed → Notification Service (Send processing complete + ready to use)

SecurityAlert → Notification Service (Send security warning + action required)
LoginFromNewDevice → Notification Service (Send security notification + device info)
PasswordChanged → Notification Service (Send change confirmation)
TwoFactorEnabled → Notification Service (Send 2FA enabled confirmation)
SuspiciousActivity → Notification Service (Send security alert + account review)

SystemMaintenance → Notification Service (Send maintenance notice + downtime info)
SystemUpdate → Notification Service (Send update notice + new features)
ServiceOutage → Notification Service (Send outage alert + status updates)
```

### Kafka Message Schema

#### Base Event Schema:
```json
{
  "eventId": "evt_123",
  "eventType": "PostPublished",
  "aggregateId": "post_456",
  "aggregateType": "Post",
  "version": 1,
  "timestamp": "2024-02-15T10:30:00Z",
  "source": "content-management-service",
  "data": {
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "platforms": ["facebook", "instagram"],
    "scheduledAt": "2024-02-15T10:30:00Z",
    "creditsUsed": 5
  },
  "metadata": {
    "correlationId": "corr_123",
    "causationId": "cmd_456",
    "userId": "user_789",
    "traceId": "trace_789"
  }
}
```

#### Notification Event Schemas:

##### User Events:
```json
{
  "eventType": "UserWelcome",
  "data": {
    "userId": "user_123",
    "email": "<EMAIL>",
    "fullName": "John Doe",
    "registrationDate": "2024-02-15T10:30:00Z",
    "initialCredits": 50,
    "plan": "free"
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "normal",
    "template": "user_welcome",
    "language": "vi"
  }
}

{
  "eventType": "CreditLowWarning",
  "data": {
    "userId": "user_123",
    "currentCredits": 5,
    "warningThreshold": 10,
    "plan": "pro",
    "renewalDate": "2024-03-01"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "credit_low_warning"
  }
}
```

##### Content Events:
```json
{
  "eventType": "PostPublished",
  "data": {
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "title": "Marketing Tips for 2024",
    "platforms": ["facebook", "instagram"],
    "publishedAt": "2024-02-15T10:30:00Z",
    "teamMembers": ["user_101", "user_102"]
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "normal",
    "template": "post_published",
    "recipients": ["workspace_members"]
  }
}

{
  "eventType": "AIGenerationComplete",
  "data": {
    "generationId": "gen_789",
    "userId": "user_123",
    "contentType": "post",
    "model": "gpt-4",
    "creditsUsed": 5,
    "contentPreview": "Discover the top 10 marketing strategies...",
    "generatedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push", "websocket"],
    "priority": "normal",
    "template": "ai_generation_complete"
  }
}
```

##### Billing Events:
```json
{
  "eventType": "PaymentSuccess",
  "data": {
    "userId": "user_123",
    "paymentId": "pay_456",
    "amount": 2900,
    "currency": "VND",
    "creditsAdded": 500,
    "plan": "pro",
    "paymentMethod": "card",
    "receiptUrl": "https://receipts.socialai.com/pay_456"
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "normal",
    "template": "payment_success"
  }
}

{
  "eventType": "SubscriptionExpiring",
  "data": {
    "userId": "user_123",
    "subscriptionId": "sub_789",
    "plan": "premium",
    "expiresAt": "2024-03-15T00:00:00Z",
    "daysRemaining": 7,
    "renewalUrl": "https://socialai.com/billing/renew"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "subscription_expiring"
  }
}
```

##### Workspace Events:
```json
{
  "eventType": "WorkspaceInvitation",
  "data": {
    "invitationId": "inv_123",
    "workspaceId": "ws_456",
    "workspaceName": "Marketing Team",
    "inviterUserId": "user_789",
    "inviterName": "Jane Smith",
    "inviteeEmail": "<EMAIL>",
    "role": "editor",
    "inviteUrl": "https://socialai.com/invite/inv_123",
    "expiresAt": "2024-02-22T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "normal",
    "template": "workspace_invitation",
    "recipients": ["invitee"]
  }
}

{
  "eventType": "WorkspaceMemberJoined",
  "data": {
    "workspaceId": "ws_456",
    "workspaceName": "Marketing Team",
    "newMemberId": "user_101",
    "newMemberName": "Bob Johnson",
    "newMemberEmail": "<EMAIL>",
    "role": "editor",
    "joinedAt": "2024-02-15T10:30:00Z",
    "teamMembers": ["user_789", "user_102"]
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "normal",
    "template": "workspace_member_joined",
    "recipients": ["workspace_members"]
  }
}
```

##### Security Events:
```json
{
  "eventType": "LoginFromNewDevice",
  "data": {
    "userId": "user_123",
    "deviceInfo": "Chrome on Windows 11",
    "ipAddress": "*************",
    "location": "Ho Chi Minh City, Vietnam",
    "loginTime": "2024-02-15T10:30:00Z",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "login_new_device"
  }
}

{
  "eventType": "SuspiciousActivity",
  "data": {
    "userId": "user_123",
    "activityType": "multiple_failed_logins",
    "attemptCount": 5,
    "ipAddress": "*************",
    "location": "Unknown",
    "detectedAt": "2024-02-15T10:30:00Z",
    "actionRequired": "review_account_security"
  },
  "notificationConfig": {
    "channels": ["email", "sms", "push"],
    "priority": "critical",
    "template": "suspicious_activity"
  }
}
```

### Notification Service Processing Workflow

#### Event Processing Flow:
```
1. Kafka Event → Notification Service Consumer
2. Event Validation → Check event schema và required fields
3. User Preference Check → Get user notification preferences
4. Template Selection → Choose appropriate template based on event type
5. Channel Determination → Decide which channels to use (email, SMS, push)
6. Message Rendering → Render template với event data
7. Queue Management → Add to appropriate channel queues
8. Delivery Processing → Send via respective channel providers
9. Delivery Tracking → Log delivery status và metrics
10. Retry Logic → Handle failed deliveries với exponential backoff
```

#### Multi-Channel Delivery Strategy:
```
Priority Levels:
- Critical: SMS + Email + Push (immediate)
- High: Email + Push (within 5 minutes)
- Normal: Email hoặc Push (within 15 minutes)
- Low: Email only (within 1 hour)

Channel Selection Logic:
- User Preferences: Respect user's channel preferences
- Event Type: Security events → All channels
- Time of Day: Night time → Push only (no SMS)
- Delivery History: Fallback to alternative channels if primary fails
```

#### Notification Templates:
```yaml
# Template Configuration
templates:
  user_welcome:
    channels: [email]
    subject: "Chào mừng đến với SocialAI! 🎉"
    variables: [fullName, initialCredits, setupGuideUrl]

  credit_low_warning:
    channels: [email, push]
    subject: "⚠️ Credits sắp hết - Nâng cấp ngay!"
    variables: [currentCredits, warningThreshold, upgradeUrl]

  post_published:
    channels: [push, websocket]
    subject: "✅ Bài viết đã được đăng thành công"
    variables: [postTitle, platforms, publishedAt]

  payment_success:
    channels: [email]
    subject: "💳 Thanh toán thành công - Credits đã được cộng"
    variables: [amount, creditsAdded, receiptUrl]

  workspace_invitation:
    channels: [email]
    subject: "📨 Lời mời tham gia workspace: {workspaceName}"
    variables: [workspaceName, inviterName, role, inviteUrl]

  login_new_device:
    channels: [email, push]
    subject: "🔐 Đăng nhập từ thiết bị mới"
    variables: [deviceInfo, location, loginTime]

  suspicious_activity:
    channels: [email, sms, push]
    subject: "🚨 Cảnh báo bảo mật - Hoạt động đáng ngờ"
    variables: [activityType, attemptCount, location]
```

#### Rate Limiting & Throttling:
```yaml
rate_limits:
  per_user:
    email: 50/hour
    sms: 10/hour
    push: 100/hour

  per_template:
    credit_low_warning: 1/day
    subscription_expiring: 1/week
    login_new_device: 5/day

  global:
    email: 10000/hour
    sms: 1000/hour
    push: 50000/hour
```

#### Delivery Retry Logic:
```yaml
retry_strategy:
  email:
    max_attempts: 3
    backoff: exponential
    delays: [1m, 5m, 30m]

  sms:
    max_attempts: 2
    backoff: linear
    delays: [2m, 10m]

  push:
    max_attempts: 5
    backoff: exponential
    delays: [30s, 2m, 10m, 30m, 2h]

  dead_letter_queue: true
  manual_retry: true
```

### gRPC Service Definitions
```protobuf
// user-service.proto
service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
}

service AuthService {
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  rpc VerifyPermission(VerifyPermissionRequest) returns (VerifyPermissionResponse);
  rpc GetUserContext(GetUserContextRequest) returns (GetUserContextResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc RevokeToken(RevokeTokenRequest) returns (RevokeTokenResponse);
}

message ValidateTokenRequest {
  string jwt_token = 1;
  string service_name = 2; // Which service is requesting validation
  string endpoint = 3; // Optional: specific endpoint being accessed
}

message ValidateTokenResponse {
  bool is_valid = 1;
  string user_id = 2;
  string email = 3;
  string role = 4; // "user", "admin", "premium"
  repeated string permissions = 5;
  int64 expires_at = 6; // Unix timestamp
  string error_message = 7;
  UserContext user_context = 8;
}

message VerifyPermissionRequest {
  string user_id = 1;
  string permission = 2; // "create_post", "manage_workspace", "admin_access"
  string resource_id = 3; // Optional: specific resource (workspace_id, post_id)
  string workspace_id = 4; // Optional: workspace context
}

message VerifyPermissionResponse {
  bool has_permission = 1;
  string role_in_context = 2; // "owner", "admin", "editor", "viewer"
  repeated string available_permissions = 3;
  string error_message = 4;
}

message GetUserContextRequest {
  string user_id = 1;
  string workspace_id = 2; // Optional: get context for specific workspace
}

message GetUserContextResponse {
  UserContext user_context = 1;
  repeated WorkspaceContext workspaces = 2;
  string error_message = 3;
}

message UserContext {
  string user_id = 1;
  string email = 2;
  string full_name = 3;
  string avatar_url = 4;
  string role = 5; // Global role
  bool is_verified = 6;
  bool two_factor_enabled = 7;
  int32 current_credits = 8;
  string plan = 9; // "free", "pro", "premium"
  repeated string global_permissions = 10;
}

message WorkspaceContext {
  string workspace_id = 1;
  string workspace_name = 2;
  string role_in_workspace = 3; // "owner", "admin", "editor", "viewer"
  repeated string workspace_permissions = 4;
  bool is_active_member = 5;
}

message RefreshTokenRequest {
  string refresh_token = 1;
  string user_id = 2;
}

message RefreshTokenResponse {
  bool success = 1;
  string new_access_token = 2;
  string new_refresh_token = 3;
  int64 expires_at = 4;
  string error_message = 5;
}

// credit-billing-service.proto
service CreditService {
  rpc CheckUserCredits(CheckCreditsRequest) returns (CheckCreditsResponse);
  rpc ReserveCredits(ReserveCreditsRequest) returns (ReserveCreditsResponse);
  rpc ConsumeCredits(ConsumeCreditsRequest) returns (ConsumeCreditsResponse);
  rpc RefundCredits(RefundCreditsRequest) returns (RefundCreditsResponse);
  rpc GetUserCreditBalance(GetCreditBalanceRequest) returns (GetCreditBalanceResponse);
  rpc AddCredits(AddCreditsRequest) returns (AddCreditsResponse);
  rpc GetCreditHistory(GetCreditHistoryRequest) returns (GetCreditHistoryResponse);
}

service BankTransferService {
  rpc InitiateBankTransfer(InitiateBankTransferRequest) returns (InitiateBankTransferResponse);
  rpc GetBankTransferStatus(GetBankTransferStatusRequest) returns (GetBankTransferStatusResponse);
  rpc ConfirmBankTransfer(ConfirmBankTransferRequest) returns (ConfirmBankTransferResponse);
  rpc ListBankTransfers(ListBankTransfersRequest) returns (ListBankTransfersResponse);
  rpc CancelBankTransfer(CancelBankTransferRequest) returns (CancelBankTransferResponse);
}

service SubscriptionService {
  rpc GetAvailablePlans(GetAvailablePlansRequest) returns (GetAvailablePlansResponse);
  rpc ActivateSubscription(ActivateSubscriptionRequest) returns (ActivateSubscriptionResponse);
  rpc GetUserSubscription(GetUserSubscriptionRequest) returns (GetUserSubscriptionResponse);
  rpc UpgradeSubscription(UpgradeSubscriptionRequest) returns (UpgradeSubscriptionResponse);
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);
  rpc GetSubscriptionHistory(GetSubscriptionHistoryRequest) returns (GetSubscriptionHistoryResponse);
}

service PaymentConfirmationService {
  rpc ProcessBankWebhook(ProcessBankWebhookRequest) returns (ProcessBankWebhookResponse);
  rpc ManualPaymentConfirmation(ManualPaymentConfirmationRequest) returns (ManualPaymentConfirmationResponse);
  rpc GetPendingConfirmations(GetPendingConfirmationsRequest) returns (GetPendingConfirmationsResponse);
}

service DataBackupService {
  rpc RequestDataBackup(RequestDataBackupRequest) returns (RequestDataBackupResponse);
  rpc GetBackupStatus(GetBackupStatusRequest) returns (GetBackupStatusResponse);
  rpc ListUserBackups(ListUserBackupsRequest) returns (ListUserBackupsResponse);
  rpc DownloadBackup(DownloadBackupRequest) returns (DownloadBackupResponse);
  rpc DeleteBackup(DeleteBackupRequest) returns (DeleteBackupResponse);
}

service AccountDeletionService {
  rpc RequestAccountDeletion(RequestAccountDeletionRequest) returns (RequestAccountDeletionResponse);
  rpc CancelAccountDeletion(CancelAccountDeletionRequest) returns (CancelAccountDeletionResponse);
  rpc GetDeletionStatus(GetDeletionStatusRequest) returns (GetDeletionStatusResponse);
  rpc ExecuteAccountDeletion(ExecuteAccountDeletionRequest) returns (ExecuteAccountDeletionResponse);
}

service PlatformIntegrationService {
  rpc InitiatePlatformConnection(InitiatePlatformConnectionRequest) returns (InitiatePlatformConnectionResponse);
  rpc CompletePlatformConnection(CompletePlatformConnectionRequest) returns (CompletePlatformConnectionResponse);
  rpc DisconnectPlatform(DisconnectPlatformRequest) returns (DisconnectPlatformResponse);
  rpc GetPlatformConnections(GetPlatformConnectionsRequest) returns (GetPlatformConnectionsResponse);
  rpc RefreshPlatformToken(RefreshPlatformTokenRequest) returns (RefreshPlatformTokenResponse);
  rpc ValidatePlatformConnections(ValidatePlatformConnectionsRequest) returns (ValidatePlatformConnectionsResponse);
}

service MultiPlatformPublishService {
  rpc PublishToMultiplePlatforms(PublishToMultiplePlatformsRequest) returns (PublishToMultiplePlatformsResponse);
  rpc GetPublishStatus(GetPublishStatusRequest) returns (GetPublishStatusResponse);
  rpc RetryFailedPublish(RetryFailedPublishRequest) returns (RetryFailedPublishResponse);
  rpc GetPlatformCapabilities(GetPlatformCapabilitiesRequest) returns (GetPlatformCapabilitiesResponse);
}

service PlatformAnalyticsService {
  rpc SyncPlatformAnalytics(SyncPlatformAnalyticsRequest) returns (SyncPlatformAnalyticsResponse);
  rpc GetPlatformMetrics(GetPlatformMetricsRequest) returns (GetPlatformMetricsResponse);
  rpc GetCrossplatformAnalytics(GetCrossplatformAnalyticsRequest) returns (GetCrossplatformAnalyticsResponse);
}

message CheckCreditsRequest {
  string user_id = 1;
  int32 required_credits = 2;
  string operation_type = 3; // "content_generation", "image_generation", "content_improvement"
}

message CheckCreditsResponse {
  bool has_sufficient_credits = 1;
  int32 current_balance = 2;
  int32 required_credits = 3;
  string message = 4;
}

message ReserveCreditsRequest {
  string user_id = 1;
  int32 credits_to_reserve = 2;
  string operation_id = 3; // Unique ID for this operation
  string operation_type = 4;
}

message ReserveCreditsResponse {
  bool success = 1;
  string reservation_id = 2;
  int32 reserved_credits = 3;
  string message = 4;
}

message ConsumeCreditsRequest {
  string user_id = 1;
  string reservation_id = 2; // From ReserveCredits
  int32 actual_credits_used = 3;
  string operation_details = 4;
}

message AddCreditsRequest {
  string user_id = 1;
  int32 credits_to_add = 2;
  string source = 3; // "bank_transfer", "subscription", "bonus", "refund"
  string reference_id = 4; // Payment ID, transfer ID, etc.
  string description = 5;
  map<string, string> metadata = 6;
}

message AddCreditsResponse {
  bool success = 1;
  int32 credits_added = 2;
  int32 new_balance = 3;
  string transaction_id = 4;
  string error_message = 5;
}

// Bank Transfer Messages
message InitiateBankTransferRequest {
  string user_id = 1;
  string plan_id = 2; // Optional: if purchasing a plan
  int64 amount = 3; // Amount in VND (smallest currency unit)
  string currency = 4; // "VND"
  string purpose = 5; // "subscription", "credit_topup"
  map<string, string> user_info = 6; // Name, phone, email for transfer reference
}

message InitiateBankTransferResponse {
  bool success = 1;
  string transfer_id = 2;
  string reference_code = 3; // Unique code for bank transfer
  BankTransferInstructions instructions = 4;
  string expires_at = 5; // ISO 8601 timestamp
  string error_message = 6;
}

message BankTransferInstructions {
  string bank_name = 1; // "Vietcombank", "BIDV", "Techcombank"
  string account_number = 2;
  string account_name = 3;
  int64 amount = 4;
  string transfer_content = 5; // "SOCIALAI [reference_code] [user_name]"
  string reference_code = 6;
  string qr_code_url = 7; // QR code for mobile banking
  repeated string instructions = 8; // Step-by-step instructions
}

message GetBankTransferStatusRequest {
  string transfer_id = 1;
  string user_id = 2;
}

message GetBankTransferStatusResponse {
  bool success = 1;
  BankTransferInfo transfer_info = 2;
  string error_message = 3;
}

message BankTransferInfo {
  string transfer_id = 1;
  string user_id = 2;
  string plan_id = 3;
  int64 amount = 4;
  string currency = 5;
  string status = 6; // "pending", "confirmed", "expired", "cancelled"
  string reference_code = 7;
  BankTransferInstructions instructions = 8;
  string created_at = 9;
  string expires_at = 10;
  string confirmed_at = 11;
  string bank_transaction_id = 12;
  int64 actual_amount_received = 13;
}

message ConfirmBankTransferRequest {
  string transfer_id = 1;
  string reference_code = 2;
  int64 actual_amount = 3;
  string bank_transaction_id = 4;
  string confirmed_by = 5; // Admin user ID
  string confirmation_notes = 6;
  repeated string proof_images = 7; // URLs to bank statement images
}

message ConfirmBankTransferResponse {
  bool success = 1;
  string message = 2;
  SubscriptionInfo activated_subscription = 3;
  CreditInfo added_credits = 4;
  string error_message = 5;
}

// Subscription Messages
message GetAvailablePlansRequest {
  string user_id = 1;
  string currency = 2; // "VND"
  bool include_current_plan = 3;
}

message GetAvailablePlansResponse {
  bool success = 1;
  repeated SubscriptionPlan plans = 2;
  SubscriptionPlan current_plan = 3;
  string error_message = 4;
}

message SubscriptionPlan {
  string plan_id = 1;
  string name = 2; // "Free", "Pro", "Premium", "Enterprise"
  string description = 3;
  int64 price = 4; // Price in VND
  string currency = 5;
  string billing_cycle = 6; // "monthly", "yearly"
  int32 credits_included = 7;
  repeated string features = 8;
  PlanLimits limits = 9;
  bool is_popular = 10;
  int32 discount_percentage = 11; // For promotions
}

message PlanLimits {
  int32 max_workspaces = 1;
  int32 max_team_members = 2;
  int64 storage_limit_bytes = 3;
  int32 ai_generations_per_month = 4;
  int32 social_accounts_limit = 5;
  bool advanced_analytics = 6;
  bool priority_support = 7;
}

message ActivateSubscriptionRequest {
  string user_id = 1;
  string plan_id = 2;
  string payment_id = 3; // Bank transfer ID or other payment reference
  string payment_method = 4; // "bank_transfer", "stripe", "paypal"
  bool prorate_existing = 5; // For upgrades/downgrades
}

message ActivateSubscriptionResponse {
  bool success = 1;
  SubscriptionInfo subscription = 2;
  CreditInfo credits_added = 3;
  string error_message = 4;
}

message SubscriptionInfo {
  string subscription_id = 1;
  string user_id = 2;
  string plan_id = 3;
  string plan_name = 4;
  string status = 5; // "active", "cancelled", "expired", "pending"
  string starts_at = 6;
  string ends_at = 7;
  string next_billing_date = 8;
  int64 amount_paid = 9;
  string currency = 10;
  bool auto_renew = 11;
  string created_at = 12;
}

message CreditInfo {
  string user_id = 1;
  int32 current_balance = 2;
  int32 credits_added = 3;
  string transaction_id = 4;
  string source = 5;
  string created_at = 6;
}

// Data Backup Messages
message RequestDataBackupRequest {
  string user_id = 1;
  BackupOptions options = 2;
  string backup_reason = 3; // "user_request", "account_deletion", "compliance"
  bool include_analytics = 4;
  bool include_transactions = 5;
}

message BackupOptions {
  bool include_posts = 1;
  bool include_images = 2;
  bool include_templates = 3;
  bool include_connections = 4;
  bool include_user_profile = 5;
  bool generate_html = 6; // Generate HTML pages for posts
  string format = 7; // "json", "html", "both"
  repeated string date_range = 8; // ["2024-01-01", "2024-12-31"]
}

message RequestDataBackupResponse {
  bool success = 1;
  string backup_id = 2;
  string estimated_completion_time = 3; // ISO 8601 timestamp
  int64 estimated_size_bytes = 4;
  string status = 5; // "queued", "processing", "completed", "failed"
  string error_message = 6;
}

message GetBackupStatusRequest {
  string backup_id = 1;
  string user_id = 2;
}

message GetBackupStatusResponse {
  bool success = 1;
  BackupInfo backup_info = 2;
  string error_message = 3;
}

message BackupInfo {
  string backup_id = 1;
  string user_id = 2;
  string status = 3; // "queued", "processing", "completed", "failed", "expired"
  BackupOptions options = 4;
  string created_at = 5;
  string completed_at = 6;
  string expires_at = 7; // Download link expiry (24h)
  int64 file_size_bytes = 8;
  string download_url = 9; // Presigned S3 URL
  BackupStats stats = 10;
  string error_message = 11;
}

message BackupStats {
  int32 total_posts = 1;
  int32 total_images = 2;
  int32 total_templates = 3;
  int32 total_files = 4;
  int64 total_size_bytes = 5;
  int32 html_pages_generated = 6;
  map<string, int32> file_type_counts = 7;
}

message ListUserBackupsRequest {
  string user_id = 1;
  int32 page = 2;
  int32 limit = 3;
  string status_filter = 4; // Optional filter by status
}

message ListUserBackupsResponse {
  bool success = 1;
  repeated BackupInfo backups = 2;
  int32 total_count = 3;
  int32 page = 4;
  int32 limit = 5;
  string error_message = 6;
}

message DownloadBackupRequest {
  string backup_id = 1;
  string user_id = 2;
}

message DownloadBackupResponse {
  bool success = 1;
  string download_url = 2; // Fresh presigned URL
  string expires_at = 3;
  int64 file_size_bytes = 4;
  string error_message = 5;
}

// Account Deletion Messages
message RequestAccountDeletionRequest {
  string user_id = 1;
  string deletion_reason = 2; // "privacy_concerns", "no_longer_needed", "other"
  string additional_notes = 3;
  bool request_data_backup = 4; // Offer backup before deletion
  bool confirm_understanding = 5; // User confirms they understand deletion is permanent
}

message RequestAccountDeletionResponse {
  bool success = 1;
  string deletion_id = 2;
  string grace_period_ends = 3; // ISO 8601 timestamp (7 days)
  string backup_id = 4; // If backup was requested
  DeletionInfo deletion_info = 5;
  string error_message = 6;
}

message DeletionInfo {
  string deletion_id = 1;
  string user_id = 2;
  string status = 3; // "pending", "grace_period", "processing", "completed", "cancelled"
  string deletion_reason = 4;
  string requested_at = 5;
  string grace_period_ends = 6;
  string scheduled_deletion_at = 7;
  string completed_at = 8;
  bool backup_requested = 9;
  string backup_id = 10;
  DeletionStats estimated_data = 11;
}

message DeletionStats {
  int32 posts_to_delete = 1;
  int32 images_to_delete = 2;
  int32 templates_to_delete = 3;
  int32 connections_to_remove = 4;
  int64 total_storage_bytes = 5;
  repeated string affected_services = 6;
}

message CancelAccountDeletionRequest {
  string deletion_id = 1;
  string user_id = 2;
  string cancellation_reason = 3;
}

message CancelAccountDeletionResponse {
  bool success = 1;
  string message = 2;
  DeletionInfo updated_deletion_info = 3;
  string error_message = 4;
}

message GetDeletionStatusRequest {
  string deletion_id = 1;
  string user_id = 2;
}

message GetDeletionStatusResponse {
  bool success = 1;
  DeletionInfo deletion_info = 2;
  string error_message = 3;
}

message ExecuteAccountDeletionRequest {
  string deletion_id = 1;
  string admin_user_id = 2; // Admin executing the deletion
  bool force_immediate = 3; // Skip grace period (admin only)
}

message ExecuteAccountDeletionResponse {
  bool success = 1;
  string message = 2;
  DeletionExecutionResult result = 3;
  string error_message = 4;
}

message DeletionExecutionResult {
  string deletion_id = 1;
  string executed_at = 2;
  repeated ServiceDeletionResult service_results = 3;
  DeletionStats actual_deleted_data = 4;
  bool completed_successfully = 5;
  repeated string errors = 6;
}

message ServiceDeletionResult {
  string service_name = 1;
  bool success = 2;
  int32 records_deleted = 3;
  int64 storage_freed_bytes = 4;
  string error_message = 5;
  string completed_at = 6;
}

// Platform Integration Messages
message InitiatePlatformConnectionRequest {
  string user_id = 1;
  string platform = 2; // "facebook", "instagram", "twitter", "youtube", "tiktok"
  repeated string scopes = 3; // Platform-specific permissions
  string callback_url = 4;
  map<string, string> connection_options = 5;
}

message InitiatePlatformConnectionResponse {
  bool success = 1;
  string connection_id = 2;
  string oauth_url = 3; // URL to redirect user for OAuth
  string state = 4; // OAuth state parameter
  string error_message = 5;
}

message CompletePlatformConnectionRequest {
  string connection_id = 1;
  string user_id = 2;
  string authorization_code = 3;
  string state = 4;
  map<string, string> oauth_params = 5;
}

message CompletePlatformConnectionResponse {
  bool success = 1;
  PlatformConnection connection = 2;
  string error_message = 3;
}

message PlatformConnection {
  string connection_id = 1;
  string user_id = 2;
  string platform = 3;
  string status = 4; // "active", "expired", "revoked", "error"
  string platform_user_id = 5;
  string platform_username = 6;
  string platform_display_name = 7;
  string profile_image_url = 8;
  repeated string granted_scopes = 9;
  string connected_at = 10;
  string expires_at = 11;
  string last_used_at = 12;
  PlatformCapabilities capabilities = 13;
}

message PlatformCapabilities {
  bool can_publish_text = 1;
  bool can_publish_images = 2;
  bool can_publish_videos = 3;
  bool can_schedule_posts = 4;
  bool can_get_analytics = 5;
  bool supports_hashtags = 6;
  bool supports_mentions = 7;
  int32 max_text_length = 8;
  int32 max_images_per_post = 9;
  int64 max_video_size_bytes = 10;
  repeated string supported_video_formats = 11;
  repeated string supported_image_formats = 12;
}

message DisconnectPlatformRequest {
  string user_id = 1;
  string platform = 2;
  string disconnection_reason = 3;
}

message DisconnectPlatformResponse {
  bool success = 1;
  string message = 2;
  string error_message = 3;
}

message GetPlatformConnectionsRequest {
  string user_id = 1;
  string workspace_id = 2; // Optional filter
  repeated string platforms = 3; // Optional filter
  bool include_expired = 4;
}

message GetPlatformConnectionsResponse {
  bool success = 1;
  repeated PlatformConnection connections = 2;
  int32 total_count = 3;
  string error_message = 4;
}

message ValidatePlatformConnectionsRequest {
  string user_id = 1;
  repeated string platforms = 2;
}

message ValidatePlatformConnectionsResponse {
  bool all_valid = 1;
  repeated PlatformValidationResult validations = 2;
  string error_message = 3;
}

message PlatformValidationResult {
  string platform = 1;
  bool is_connected = 2;
  bool is_valid = 3;
  bool needs_refresh = 4;
  string status = 5;
  string error_message = 6;
}

// Multi-Platform Publishing Messages
message PublishToMultiplePlatformsRequest {
  string user_id = 1;
  string workspace_id = 2;
  PostContent content = 3;
  repeated string platforms = 4;
  map<string, PlatformSpecificOptions> platform_options = 5;
  bool schedule_publish = 6;
  string scheduled_time = 7; // ISO 8601 if scheduling
}

message PostContent {
  string title = 1;
  string text_content = 2;
  repeated string image_urls = 3;
  repeated string video_urls = 4;
  repeated string hashtags = 5;
  repeated string mentions = 6;
  string link_url = 7;
  string link_title = 8;
  string link_description = 9;
  map<string, string> metadata = 10;
}

message PlatformSpecificOptions {
  string platform = 1;
  map<string, string> options = 2; // Platform-specific settings
  bool auto_adapt_content = 3;
  string custom_text = 4; // Override main text for this platform
  repeated string custom_hashtags = 5; // Platform-specific hashtags
}

message PublishToMultiplePlatformsResponse {
  bool success = 1;
  string publish_id = 2;
  repeated PlatformPublishResult results = 3;
  repeated string successful_platforms = 4;
  repeated string failed_platforms = 5;
  bool overall_success = 6;
  string error_message = 7;
}

message PlatformPublishResult {
  string platform = 1;
  bool success = 2;
  string platform_post_id = 3;
  string platform_post_url = 4;
  string error_message = 5;
  string error_type = 6; // "rate_limit", "auth_expired", "content_violation", "network_error"
  string published_at = 7;
  string retry_after = 8; // For rate limit errors
  map<string, string> platform_metadata = 9;
}

// Platform Analytics Messages
message SyncPlatformAnalyticsRequest {
  string user_id = 1;
  repeated string platforms = 2;
  string date_range_start = 3; // ISO 8601 date
  string date_range_end = 4;
  bool force_refresh = 5;
}

message SyncPlatformAnalyticsResponse {
  bool success = 1;
  repeated PlatformSyncResult sync_results = 2;
  string last_synced_at = 3;
  string error_message = 4;
}

message PlatformSyncResult {
  string platform = 1;
  bool success = 2;
  int32 posts_synced = 3;
  string last_sync_date = 4;
  string error_message = 5;
}

message GetPlatformMetricsRequest {
  string user_id = 1;
  string platform = 2;
  string date_range_start = 3;
  string date_range_end = 4;
  repeated string metric_types = 5; // "engagement", "reach", "impressions", "clicks"
}

message GetPlatformMetricsResponse {
  bool success = 1;
  string platform = 2;
  repeated PlatformMetric metrics = 3;
  PlatformSummary summary = 4;
  string error_message = 5;
}

message PlatformMetric {
  string metric_name = 1; // "likes", "shares", "comments", "views", "reach"
  int64 value = 2;
  string date = 3; // YYYY-MM-DD
  string post_id = 4; // Optional, for post-specific metrics
}

message PlatformSummary {
  string platform = 1;
  int64 total_posts = 2;
  int64 total_engagement = 3;
  int64 total_reach = 4;
  int64 total_impressions = 5;
  double engagement_rate = 6;
  string top_performing_post_id = 7;
  repeated string trending_hashtags = 8;
}

// ai-content-service.proto
service ContentGenerationService {
  rpc GenerateContent(GenerateContentRequest) returns (GenerateContentResponse);
  rpc ImproveContent(ImproveContentRequest) returns (ImproveContentResponse);
  rpc GenerateImage(GenerateImageRequest) returns (GenerateImageResponse);
  rpc GetModelCosts(GetModelCostsRequest) returns (GetModelCostsResponse);
}

message GenerateContentRequest {
  string user_id = 1;
  string topic = 2;
  string content_type = 3; // "educational", "promotional", etc.
  string tone = 4; // "professional", "friendly", etc.
  string length = 5; // "short", "medium", "long"
  string model = 6; // "gpt-4", "claude-3.5-sonnet", "gemini-pro"
  repeated string platforms = 7;
  string context = 8;
  string workspace_id = 9;
}

message GenerateContentResponse {
  bool success = 1;
  string content = 2;
  repeated string hashtags = 3;
  repeated string suggested_images = 4;
  int32 credits_used = 5;
  string generation_id = 6;
  string model_used = 7;
  string error_message = 8;
}

message GenerateImageRequest {
  string user_id = 1;
  string prompt = 2;
  string style = 3; // "realistic", "cartoon", "abstract"
  string size = 4; // "1024x1024", "512x512"
  string model = 5; // "dall-e-3", "midjourney", "stable-diffusion"
  int32 quantity = 6;
}

message GenerateImageResponse {
  bool success = 1;
  repeated string image_urls = 2;
  int32 credits_used = 3;
  string generation_id = 4;
  string error_message = 5;
}

// content-management-service.proto
service PostService {
  rpc CreatePost(CreatePostRequest) returns (CreatePostResponse);
  rpc GetPost(GetPostRequest) returns (GetPostResponse);
  rpc SchedulePost(SchedulePostRequest) returns (SchedulePostResponse);
}

service TemplateService {
  rpc CreateTemplate(CreateTemplateRequest) returns (CreateTemplateResponse);
  rpc GetTemplate(GetTemplateRequest) returns (GetTemplateResponse);
  rpc SearchTemplates(SearchTemplatesRequest) returns (SearchTemplatesResponse);
  rpc PurchaseTemplate(PurchaseTemplateRequest) returns (PurchaseTemplateResponse);
}

service WorkspaceService {
  rpc CreateWorkspace(CreateWorkspaceRequest) returns (CreateWorkspaceResponse);
  rpc AddMember(AddMemberRequest) returns (AddMemberResponse);
  rpc CheckPermission(CheckPermissionRequest) returns (CheckPermissionResponse);
}

service ScheduleService {
  rpc SchedulePost(SchedulePostRequest) returns (SchedulePostResponse);
  rpc UpdateSchedule(UpdateScheduleRequest) returns (UpdateScheduleResponse);
  rpc CancelSchedule(CancelScheduleRequest) returns (CancelScheduleResponse);
  rpc GetSchedule(GetScheduleRequest) returns (GetScheduleResponse);
  rpc ListSchedules(ListSchedulesRequest) returns (ListSchedulesResponse);
  rpc GetScheduleAnalytics(GetScheduleAnalyticsRequest) returns (GetScheduleAnalyticsResponse);
}

service PublishService {
  rpc PublishPostNow(PublishPostNowRequest) returns (PublishPostNowResponse);
  rpc GetPublishStatus(GetPublishStatusRequest) returns (GetPublishStatusResponse);
  rpc RetryFailedPublish(RetryFailedPublishRequest) returns (RetryFailedPublishResponse);
}

message SchedulePostRequest {
  string post_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string scheduled_time = 4; // ISO 8601 format in user's timezone
  string timezone = 5; // e.g., "Asia/Ho_Chi_Minh"
  repeated string platforms = 6; // ["facebook", "instagram", "twitter"]
  map<string, string> platform_options = 7; // Platform-specific options
}

message SchedulePostResponse {
  bool success = 1;
  string schedule_id = 2;
  string estimated_publish_time_utc = 3;
  string status = 4;
  repeated PlatformValidation platform_validations = 5;
  string error_message = 6;
}

message PlatformValidation {
  string platform = 1;
  bool is_connected = 2;
  bool has_permission = 3;
  string error_message = 4;
  string connection_status = 5; // "active", "expired", "revoked"
}

message UpdateScheduleRequest {
  string schedule_id = 1;
  string user_id = 2;
  string new_scheduled_time = 3; // Optional
  string new_timezone = 4; // Optional
  repeated string new_platforms = 5; // Optional
  map<string, string> new_platform_options = 6; // Optional
}

message UpdateScheduleResponse {
  bool success = 1;
  ScheduleInfo updated_schedule = 2;
  string error_message = 3;
}

message CancelScheduleRequest {
  string schedule_id = 1;
  string user_id = 2;
  string reason = 3; // Optional cancellation reason
}

message CancelScheduleResponse {
  bool success = 1;
  string message = 2;
  string error_message = 3;
}

message GetScheduleRequest {
  string schedule_id = 1;
  string user_id = 2;
  bool include_results = 3; // Include platform publish results
}

message GetScheduleResponse {
  bool success = 1;
  ScheduleInfo schedule = 2;
  repeated PlatformResult platform_results = 3;
  string error_message = 4;
}

message ListSchedulesRequest {
  string user_id = 1;
  string workspace_id = 2; // Optional filter
  string status = 3; // Optional filter: "pending", "completed", "failed"
  string start_date = 4; // Optional date range filter
  string end_date = 5;
  int32 page = 6;
  int32 limit = 7;
  string sort_by = 8; // "scheduled_time", "created_at", "status"
  string sort_order = 9; // "asc", "desc"
}

message ListSchedulesResponse {
  bool success = 1;
  repeated ScheduleInfo schedules = 2;
  int32 total_count = 3;
  int32 page = 4;
  int32 limit = 5;
  ScheduleStats stats = 6;
  string error_message = 7;
}

message ScheduleInfo {
  string schedule_id = 1;
  string post_id = 2;
  string user_id = 3;
  string workspace_id = 4;
  string scheduled_time_utc = 5;
  string original_timezone = 6;
  repeated string platforms = 7;
  string status = 8;
  int32 retry_attempt = 9;
  string original_schedule_id = 10; // For retry records
  string created_at = 11;
  string updated_at = 12;
  PostInfo post_info = 13; // Basic post information
}

message PostInfo {
  string post_id = 1;
  string title = 2;
  string content_preview = 3; // First 100 characters
  repeated string image_urls = 4;
  string post_type = 5; // "text", "image", "video", "carousel"
}

message PlatformResult {
  string platform = 1;
  bool success = 2;
  string platform_post_id = 3;
  string platform_post_url = 4;
  string error_message = 5;
  string error_type = 6; // "rate_limit", "auth_expired", "content_violation"
  string published_at = 7;
  string retry_after = 8; // For rate limit errors
}

message ScheduleStats {
  int32 total_scheduled = 1;
  int32 pending = 2;
  int32 completed = 3;
  int32 failed = 4;
  int32 cancelled = 5;
  map<string, int32> platform_breakdown = 6; // Platform -> count
}

message GetScheduleAnalyticsRequest {
  string user_id = 1;
  string workspace_id = 2; // Optional
  string start_date = 3; // YYYY-MM-DD format
  string end_date = 4;
  string group_by = 5; // "day", "week", "month"
}

message GetScheduleAnalyticsResponse {
  bool success = 1;
  repeated ScheduleAnalytics analytics = 2;
  ScheduleSummary summary = 3;
  string error_message = 4;
}

message ScheduleAnalytics {
  string date = 1; // YYYY-MM-DD
  int32 total_scheduled = 2;
  int32 successful_publishes = 3;
  int32 failed_publishes = 4;
  double success_rate = 5;
  map<string, PlatformStats> platform_breakdown = 6;
}

message PlatformStats {
  int32 scheduled = 1;
  int32 successful = 2;
  int32 failed = 3;
  double success_rate = 4;
  repeated string common_errors = 5;
}

message ScheduleSummary {
  int32 total_scheduled = 1;
  int32 total_successful = 2;
  int32 total_failed = 3;
  double overall_success_rate = 4;
  string best_performing_platform = 5;
  string most_common_error = 6;
  repeated string recommendations = 7;
}

// Immediate Publishing Messages
message PublishPostNowRequest {
  string post_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  repeated string platforms = 4; // ["facebook", "instagram", "twitter"]
  map<string, string> publish_options = 5; // Platform-specific options
  bool notify_team = 6; // Notify workspace members
}

message PublishPostNowResponse {
  bool success = 1;
  string publish_id = 2;
  repeated PlatformPublishResult platform_results = 3;
  repeated string successful_platforms = 4;
  repeated string failed_platforms = 5;
  bool overall_success = 6;
  string published_at = 7;
  string error_message = 8;
}

message PlatformPublishResult {
  string platform = 1;
  bool success = 2;
  string platform_post_id = 3;
  string platform_post_url = 4;
  string error_message = 5;
  string error_type = 6; // "rate_limit", "auth_expired", "content_violation"
  string published_at = 7;
  map<string, string> platform_metadata = 8; // Platform-specific data
}

message GetPublishStatusRequest {
  string publish_id = 1;
  string user_id = 2;
}

message GetPublishStatusResponse {
  bool success = 1;
  PublishInfo publish_info = 2;
  string error_message = 3;
}

message PublishInfo {
  string publish_id = 1;
  string post_id = 2;
  string user_id = 3;
  string workspace_id = 4;
  repeated string platforms = 5;
  string status = 6; // "publishing", "completed", "failed", "partial"
  repeated PlatformPublishResult platform_results = 7;
  string created_at = 8;
  string updated_at = 9;
  PostInfo post_info = 10;
}

message RetryFailedPublishRequest {
  string publish_id = 1;
  string user_id = 2;
  repeated string retry_platforms = 3; // Only retry specific platforms
}

message RetryFailedPublishResponse {
  bool success = 1;
  string new_publish_id = 2;
  repeated PlatformPublishResult retry_results = 3;
  string error_message = 4;
}

// Schedule Update Messages (Enhanced)
message UpdateScheduleRequest {
  string schedule_id = 1;
  string user_id = 2;
  string new_scheduled_time = 3; // Optional: ISO 8601 format
  string new_timezone = 4; // Optional: e.g., "Asia/Ho_Chi_Minh"
  repeated string new_platforms = 5; // Optional: update platforms
  map<string, string> new_platform_options = 6; // Optional: platform settings
  string update_reason = 7; // Optional: reason for update
}

message UpdateScheduleResponse {
  bool success = 1;
  ScheduleInfo updated_schedule = 2;
  ScheduleChangeInfo change_info = 3;
  string error_message = 4;
}

message ScheduleChangeInfo {
  string old_scheduled_time = 1;
  string new_scheduled_time = 2;
  repeated string old_platforms = 3;
  repeated string new_platforms = 4;
  string updated_at = 5;
  string update_reason = 6;
}

message CancelScheduleRequest {
  string schedule_id = 1;
  string user_id = 2;
  string reason = 3; // Optional: cancellation reason
  bool notify_team = 4; // Notify workspace members
}

message CancelScheduleResponse {
  bool success = 1;
  string message = 2;
  ScheduleInfo cancelled_schedule = 3;
  string error_message = 4;
}

message PurchaseTemplateRequest {
  string user_id = 1;
  string template_id = 2;
  string workspace_id = 3;
}

message PurchaseTemplateResponse {
  bool success = 1;
  int32 credits_charged = 2;
  string transaction_id = 3;
  string error_message = 4;
}

// notification-service.proto
service NotificationService {
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc SendBulkNotification(SendBulkNotificationRequest) returns (SendBulkNotificationResponse);
  rpc GetNotificationHistory(GetNotificationHistoryRequest) returns (GetNotificationHistoryResponse);
  rpc GetDeliveryStatus(GetDeliveryStatusRequest) returns (GetDeliveryStatusResponse);
}

service NotificationTemplateService {
  rpc GetTemplate(GetTemplateRequest) returns (GetTemplateResponse);
  rpc CreateTemplate(CreateTemplateRequest) returns (CreateTemplateResponse);
  rpc UpdateTemplate(UpdateTemplateRequest) returns (UpdateTemplateResponse);
  rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse);
}

service NotificationPreferenceService {
  rpc GetUserPreferences(GetUserPreferencesRequest) returns (GetUserPreferencesResponse);
  rpc UpdateUserPreferences(UpdateUserPreferencesRequest) returns (UpdateUserPreferencesResponse);
  rpc GetChannelSettings(GetChannelSettingsRequest) returns (GetChannelSettingsResponse);
}

message SendNotificationRequest {
  string user_id = 1;
  string template_id = 2;
  repeated string channels = 3; // ["email", "sms", "push", "websocket"]
  string priority = 4; // "low", "normal", "high", "critical"
  map<string, string> variables = 5; // Template variables
  string scheduled_at = 6; // Optional: schedule for later
  string correlation_id = 7; // For tracking
}

message SendNotificationResponse {
  bool success = 1;
  string notification_id = 2;
  repeated ChannelResult channel_results = 3;
  string error_message = 4;
}

message ChannelResult {
  string channel = 1;
  bool success = 2;
  string message_id = 3;
  string error_message = 4;
  string delivery_status = 5; // "sent", "delivered", "failed", "pending"
}

message SendBulkNotificationRequest {
  repeated string user_ids = 1;
  string template_id = 2;
  repeated string channels = 3;
  string priority = 4;
  map<string, string> variables = 5;
  string scheduled_at = 6;
  string correlation_id = 7;
}

message SendBulkNotificationResponse {
  bool success = 1;
  string bulk_notification_id = 2;
  int32 total_recipients = 3;
  int32 successful_sends = 4;
  int32 failed_sends = 5;
  repeated UserNotificationResult user_results = 6;
}

message UserNotificationResult {
  string user_id = 1;
  bool success = 2;
  string notification_id = 3;
  repeated ChannelResult channel_results = 4;
  string error_message = 5;
}

message GetNotificationHistoryRequest {
  string user_id = 1;
  string template_id = 2; // Optional filter
  repeated string channels = 3; // Optional filter
  string start_date = 4;
  string end_date = 5;
  int32 page = 6;
  int32 limit = 7;
}

message GetNotificationHistoryResponse {
  repeated NotificationRecord notifications = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 limit = 4;
}

message NotificationRecord {
  string notification_id = 1;
  string user_id = 2;
  string template_id = 3;
  string template_name = 4;
  repeated string channels = 5;
  string priority = 6;
  string status = 7; // "sent", "delivered", "failed", "pending"
  string created_at = 8;
  string delivered_at = 9;
  map<string, string> variables = 10;
  repeated ChannelResult channel_results = 11;
}

message GetUserPreferencesRequest {
  string user_id = 1;
}

message GetUserPreferencesResponse {
  UserNotificationPreferences preferences = 1;
}

message UserNotificationPreferences {
  string user_id = 1;
  bool email_enabled = 2;
  bool sms_enabled = 3;
  bool push_enabled = 4;
  bool websocket_enabled = 5;

  // Event-specific preferences
  map<string, ChannelPreferences> event_preferences = 6;

  // Quiet hours
  string quiet_hours_start = 7; // "22:00"
  string quiet_hours_end = 8;   // "08:00"
  string timezone = 9;

  // Frequency limits
  int32 max_emails_per_day = 10;
  int32 max_sms_per_day = 11;
  int32 max_push_per_hour = 12;
}

message ChannelPreferences {
  bool email = 1;
  bool sms = 2;
  bool push = 3;
  bool websocket = 4;
}

// asset-service.proto
service AssetService {
  rpc RequestUpload(RequestUploadRequest) returns (RequestUploadResponse);
  rpc ConfirmUpload(ConfirmUploadRequest) returns (ConfirmUploadResponse);
  rpc GetAsset(GetAssetRequest) returns (GetAssetResponse);
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse);
  rpc DeleteAsset(DeleteAssetRequest) returns (DeleteAssetResponse);
  rpc GetUploadStatus(GetUploadStatusRequest) returns (GetUploadStatusResponse);
}

service AssetProcessingService {
  rpc ProcessRAGDocument(ProcessRAGDocumentRequest) returns (ProcessRAGDocumentResponse);
  rpc GenerateThumbnail(GenerateThumbnailRequest) returns (GenerateThumbnailResponse);
  rpc ExtractMetadata(ExtractMetadataRequest) returns (ExtractMetadataResponse);
  rpc ScanForVirus(ScanForVirusRequest) returns (ScanForVirusResponse);
}

message RequestUploadRequest {
  string user_id = 1;
  string filename = 2;
  int64 filesize = 3;
  string content_type = 4;
  string purpose = 5; // "profile_image", "post_image", "rag_document", "template_thumbnail"
  string workspace_id = 6; // Optional: for workspace assets
  map<string, string> metadata = 7; // Additional metadata
}

message RequestUploadResponse {
  bool success = 1;
  string upload_id = 2;
  string presigned_url = 3;
  string s3_key = 4;
  int32 expires_in = 5; // Seconds until presigned URL expires
  string error_message = 6;
  UploadLimits limits = 7;
}

message UploadLimits {
  int64 max_file_size = 1;
  int64 remaining_quota = 2;
  repeated string allowed_types = 3;
}

message ConfirmUploadRequest {
  string upload_id = 1;
  string user_id = 2;
  int64 actual_filesize = 3;
  string checksum = 4; // MD5 or SHA256
  map<string, string> final_metadata = 5;
}

message ConfirmUploadResponse {
  bool success = 1;
  string asset_id = 2;
  string public_url = 3;
  string cdn_url = 4;
  AssetInfo asset_info = 5;
  string error_message = 6;
}

message AssetInfo {
  string asset_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string filename = 4;
  string original_filename = 5;
  int64 filesize = 6;
  string content_type = 7;
  string s3_key = 8;
  string s3_bucket = 9;
  string public_url = 10;
  string cdn_url = 11;
  string purpose = 12;
  string status = 13; // "pending", "processing", "completed", "failed"
  string created_at = 14;
  string updated_at = 15;
  map<string, string> metadata = 16;
  ProcessingInfo processing_info = 17;
}

message ProcessingInfo {
  bool virus_scanned = 1;
  bool virus_clean = 2;
  bool thumbnails_generated = 3;
  bool text_extracted = 4;
  bool embeddings_created = 5;
  repeated string thumbnail_urls = 6;
  string extracted_text_preview = 7;
  map<string, string> exif_data = 8;
}

message GetAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  bool include_processing_info = 3;
  bool generate_signed_url = 4;
  int32 signed_url_expires = 5; // Seconds
}

message GetAssetResponse {
  bool success = 1;
  AssetInfo asset = 2;
  string signed_url = 3;
  string error_message = 4;
}

message ListAssetsRequest {
  string user_id = 1;
  string workspace_id = 2; // Optional filter
  string purpose = 3; // Optional filter
  string content_type = 4; // Optional filter
  string status = 5; // Optional filter
  int32 page = 6;
  int32 limit = 7;
  string sort_by = 8; // "created_at", "filename", "filesize"
  string sort_order = 9; // "asc", "desc"
}

message ListAssetsResponse {
  bool success = 1;
  repeated AssetInfo assets = 2;
  int32 total_count = 3;
  int32 page = 4;
  int32 limit = 5;
  StorageUsage storage_usage = 6;
  string error_message = 7;
}

message StorageUsage {
  int64 used_bytes = 1;
  int64 quota_bytes = 2;
  int32 file_count = 3;
  map<string, int64> usage_by_type = 4; // content_type -> bytes
}

message DeleteAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  bool permanent_delete = 3; // true = immediate delete, false = soft delete
}

message DeleteAssetResponse {
  bool success = 1;
  string message = 2;
  string error_message = 3;
}

message ProcessRAGDocumentRequest {
  string asset_id = 1;
  string s3_key = 2;
  string content_type = 3;
  ProcessingOptions options = 4;
}

message ProcessingOptions {
  int32 chunk_size = 1; // Characters per chunk
  int32 chunk_overlap = 2; // Overlap between chunks
  string embedding_model = 3; // "openai", "sentence-bert"
  bool extract_images = 4;
  bool ocr_enabled = 5;
}

message ProcessRAGDocumentResponse {
  bool success = 1;
  string processing_id = 2;
  int32 total_chunks = 3;
  int32 total_characters = 4;
  string text_preview = 5;
  repeated string extracted_images = 6;
  string error_message = 7;
}

{
  "eventType": "PostPublishedImmediately",
  "data": {
    "publishId": "pub_123",
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "title": "Marketing Tips for 2024",
    "platforms": ["facebook", "instagram", "twitter"],
    "platformResults": {
      "facebook": {
        "success": true,
        "platformPostId": "fb_********9",
        "platformPostUrl": "https://facebook.com/posts/********9",
        "publishedAt": "2024-02-15T10:30:12Z"
      },
      "instagram": {
        "success": true,
        "platformPostId": "ig_*********",
        "platformPostUrl": "https://instagram.com/p/*********",
        "publishedAt": "2024-02-15T10:30:15Z"
      },
      "twitter": {
        "success": false,
        "error": "rate_limit_exceeded",
        "errorType": "rate_limit",
        "retryAfter": "2024-02-15T11:00:00Z"
      }
    },
    "successfulPlatforms": ["facebook", "instagram"],
    "failedPlatforms": ["twitter"],
    "overallSuccess": false,
    "publishedAt": "2024-02-15T10:30:00Z",
    "teamMembers": ["user_101", "user_102"]
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "high",
    "template": "post_published_immediately",
    "recipients": ["user", "workspace_members"]
  }
}

{
  "eventType": "ScheduleUpdated",
  "data": {
    "scheduleId": "sch_123",
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "changeInfo": {
      "oldScheduledTime": "2024-02-15T14:30:00Z",
      "newScheduledTime": "2024-02-16T09:00:00Z",
      "oldPlatforms": ["facebook", "instagram"],
      "newPlatforms": ["facebook", "instagram", "twitter"],
      "updateReason": "Better engagement time",
      "updatedAt": "2024-02-15T10:30:00Z"
    },
    "updatedBy": {
      "userId": "user_789",
      "userName": "John Doe"
    }
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "normal",
    "template": "schedule_updated",
    "recipients": ["user", "workspace_members"]
  }
}

{
  "eventType": "ScheduleCancelled",
  "data": {
    "scheduleId": "sch_123",
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "postTitle": "Marketing Tips for 2024",
    "originalScheduledTime": "2024-02-15T14:30:00Z",
    "platforms": ["facebook", "instagram"],
    "cancellationReason": "Content needs revision",
    "cancelledAt": "2024-02-15T10:30:00Z",
    "cancelledBy": {
      "userId": "user_789",
      "userName": "John Doe"
    },
    "notifyTeam": true
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "normal",
    "template": "schedule_cancelled",
    "recipients": ["user", "workspace_members"]
  }
}

{
  "eventType": "PostRetryScheduled",
  "data": {
    "originalPublishId": "pub_123",
    "retryPublishId": "pub_124",
    "postId": "post_456",
    "userId": "user_789",
    "failedPlatforms": ["twitter"],
    "retryPlatforms": ["twitter"],
    "retryReason": "rate_limit_exceeded",
    "originalAttemptAt": "2024-02-15T10:30:00Z",
    "retryScheduledFor": "2024-02-15T11:00:00Z",
    "retryAttempt": 1,
    "maxRetryAttempts": 3
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "low",
    "template": "post_retry_scheduled"
  }
}

{
  "eventType": "ScheduledPostPublished",
  "data": {
    "scheduleId": "sch_123",
    "publishId": "pub_456",
    "postId": "post_789",
    "userId": "user_101",
    "workspaceId": "ws_202",
    "title": "Marketing Tips for 2024",
    "originalScheduledTime": "2024-02-15T14:30:00Z",
    "executedAt": "2024-02-15T14:30:15Z",
    "platforms": ["facebook", "instagram", "twitter"],
    "platformResults": {
      "facebook": {
        "success": true,
        "platformPostId": "fb_********9",
        "platformPostUrl": "https://facebook.com/posts/********9",
        "publishedAt": "2024-02-15T14:30:12Z"
      },
      "instagram": {
        "success": true,
        "platformPostId": "ig_*********",
        "platformPostUrl": "https://instagram.com/p/*********",
        "publishedAt": "2024-02-15T14:30:15Z"
      },
      "twitter": {
        "success": true,
        "platformPostId": "tw_555666777",
        "platformPostUrl": "https://twitter.com/user/status/555666777",
        "publishedAt": "2024-02-15T14:30:18Z"
      }
    },
    "successfulPlatforms": ["facebook", "instagram", "twitter"],
    "failedPlatforms": [],
    "overallSuccess": true,
    "status": "completed"
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "normal",
    "template": "scheduled_post_published",
    "recipients": ["user", "workspace_members"]
  }
}

{
  "eventType": "ScheduledPostFailed",
  "data": {
    "scheduleId": "sch_124",
    "postId": "post_790",
    "userId": "user_101",
    "workspaceId": "ws_202",
    "originalScheduledTime": "2024-02-15T15:00:00Z",
    "executedAt": "2024-02-15T15:00:05Z",
    "platforms": ["facebook", "instagram"],
    "errorMessage": "All platforms failed to publish",
    "platformResults": {
      "facebook": {
        "success": false,
        "error": "auth_expired",
        "errorType": "auth_expired"
      },
      "instagram": {
        "success": false,
        "error": "rate_limit_exceeded",
        "errorType": "rate_limit"
      }
    },
    "successfulPlatforms": [],
    "failedPlatforms": ["facebook", "instagram"],
    "overallSuccess": false,
    "status": "failed"
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "high",
    "template": "scheduled_post_failed",
    "recipients": ["user", "workspace_members"]
  }
}

{
  "eventType": "ScheduledPostPartialSuccess",
  "data": {
    "scheduleId": "sch_125",
    "publishId": "pub_458",
    "postId": "post_791",
    "userId": "user_101",
    "originalScheduledTime": "2024-02-15T16:00:00Z",
    "executedAt": "2024-02-15T16:00:10Z",
    "platforms": ["facebook", "instagram", "twitter"],
    "platformResults": {
      "facebook": {
        "success": true,
        "platformPostUrl": "https://facebook.com/posts/123456790"
      },
      "instagram": {
        "success": true,
        "platformPostUrl": "https://instagram.com/p/987654322"
      },
      "twitter": {
        "success": false,
        "error": "rate_limit_exceeded",
        "errorType": "rate_limit",
        "retryAfter": "2024-02-15T16:30:00Z"
      }
    },
    "successfulPlatforms": ["facebook", "instagram"],
    "failedPlatforms": ["twitter"],
    "overallSuccess": false,
    "status": "partial",
    "retryScheduled": true
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "scheduled_post_partial_success"
  }
}

{
  "eventType": "ScheduledPostRetry",
  "data": {
    "originalScheduleId": "sch_125",
    "retryScheduleId": "sch_126",
    "postId": "post_791",
    "userId": "user_101",
    "platform": "twitter",
    "retryAttempt": 1,
    "maxRetryAttempts": 3,
    "retryScheduledFor": "2024-02-15T16:30:00Z",
    "errorType": "rate_limit",
    "retryReason": "Rate limit exceeded, retrying in 30 minutes"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "low",
    "template": "scheduled_post_retry"
  }
}

#### Bank Transfer & Payment Event Schemas:
```json
{
  "eventType": "BankTransferInitiated",
  "data": {
    "transferId": "bt_123456",
    "userId": "user_789",
    "planId": "plan_pro_monthly",
    "amount": ********, // 290,000 VND in smallest unit
    "currency": "VND",
    "referenceCode": "SOCIALAI123456",
    "purpose": "subscription",
    "bankInstructions": {
      "bankName": "Vietcombank",
      "accountNumber": "********90",
      "accountName": "CONG TY SOCIALAI",
      "transferContent": "SOCIALAI SOCIALAI123456 Nguyen Van A",
      "qrCodeUrl": "https://api.socialai.com/qr/bt_123456"
    },
    "expiresAt": "2024-02-16T14:30:00Z",
    "userInfo": {
      "fullName": "Nguyen Van A",
      "email": "<EMAIL>",
      "phone": "+***********"
    }
  },
  "notificationConfig": {
    "channels": ["email", "sms"],
    "priority": "high",
    "template": "bank_transfer_instructions"
  }
}

{
  "eventType": "BankTransferConfirmed",
  "data": {
    "transferId": "bt_123456",
    "userId": "user_789",
    "planId": "plan_pro_monthly",
    "amount": ********,
    "actualAmountReceived": ********,
    "currency": "VND",
    "referenceCode": "SOCIALAI123456",
    "bankTransactionId": "VCB20240215********9",
    "confirmedAt": "2024-02-15T15:30:00Z",
    "confirmedBy": "admin_001",
    "confirmationMethod": "manual", // "manual" or "auto_webhook"
    "subscriptionActivated": {
      "subscriptionId": "sub_789",
      "planName": "Pro Monthly",
      "startsAt": "2024-02-15T15:30:00Z",
      "endsAt": "2024-03-15T15:30:00Z"
    },
    "creditsAdded": {
      "amount": 500,
      "newBalance": 750,
      "transactionId": "ct_456789"
    }
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "payment_confirmed_subscription_activated"
  }
}

{
  "eventType": "SubscriptionActivated",
  "data": {
    "subscriptionId": "sub_789",
    "userId": "user_789",
    "planId": "plan_pro_monthly",
    "planName": "Pro Monthly",
    "previousPlan": "free",
    "amount": ********,
    "currency": "VND",
    "paymentMethod": "bank_transfer",
    "paymentId": "bt_123456",
    "billingCycle": "monthly",
    "startsAt": "2024-02-15T15:30:00Z",
    "endsAt": "2024-03-15T15:30:00Z",
    "nextBillingDate": "2024-03-15T15:30:00Z",
    "creditsIncluded": 500,
    "features": [
      "unlimited_posts",
      "advanced_ai_models",
      "team_collaboration",
      "priority_support"
    ],
    "limits": {
      "maxWorkspaces": 5,
      "maxTeamMembers": 10,
      "storageLimitGB": 10,
      "aiGenerationsPerMonth": 1000
    }
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "normal",
    "template": "subscription_activated_welcome"
  }
}

{
  "eventType": "CreditsAdded",
  "data": {
    "userId": "user_789",
    "creditsAdded": 500,
    "previousBalance": 250,
    "newBalance": 750,
    "source": "subscription_activation",
    "referenceId": "sub_789",
    "transactionId": "ct_456789",
    "description": "Credits from Pro Monthly subscription",
    "addedAt": "2024-02-15T15:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "credits_added"
  }
}

#### Data Management Event Schemas:
```json
{
  "eventType": "DataBackupRequested",
  "data": {
    "backupId": "backup_123456",
    "userId": "user_789",
    "backupReason": "user_request",
    "options": {
      "includePosts": true,
      "includeImages": true,
      "includeTemplates": true,
      "includeConnections": true,
      "generateHtml": true,
      "format": "html",
      "dateRange": ["2024-01-01", "2024-12-31"]
    },
    "estimatedSizeBytes": 52428800,
    "estimatedCompletionTime": "2024-02-15T11:00:00Z",
    "requestedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "normal",
    "template": "data_backup_started"
  }
}

{
  "eventType": "DataBackupCompleted",
  "data": {
    "backupId": "backup_123456",
    "userId": "user_789",
    "status": "completed",
    "fileSizeBytes": ********,
    "downloadUrl": "https://socialai-backups.s3.amazonaws.com/user_789/backup_123456.zip?X-Amz-Expires=86400",
    "expiresAt": "2024-02-16T10:30:00Z",
    "s3Key": "user_789/backup_123456.zip",
    "s3Bucket": "socialai-backups",
    "stats": {
      "totalPosts": 45,
      "totalImages": 123,
      "totalTemplates": 8,
      "totalFiles": 176,
      "htmlPagesGenerated": 46,
      "processingTimeSeconds": 127
    },
    "completedAt": "2024-02-15T10:32:07Z"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "data_backup_ready_download"
  }
}

{
  "eventType": "AccountDeletionRequested",
  "data": {
    "deletionId": "del_789123",
    "userId": "user_456",
    "deletionReason": "privacy_concerns",
    "additionalNotes": "Moving to different platform",
    "requestDataBackup": true,
    "backupId": "backup_789123",
    "gracePeriodEnds": "2024-02-22T10:30:00Z",
    "scheduledDeletionAt": "2024-02-22T10:30:00Z",
    "estimatedData": {
      "postsToDelete": 23,
      "imagesToDelete": 67,
      "templatesToDelete": 3,
      "connectionsToRemove": 4,
      "totalStorageBytes": *********,
      "affectedServices": [
        "content-management",
        "asset-service",
        "integration-service",
        "credit-service",
        "analytics-service"
      ]
    },
    "requestedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "critical",
    "template": "account_deletion_grace_period"
  }
}

{
  "eventType": "AccountDeletionCompleted",
  "data": {
    "deletionId": "del_789123",
    "userId": "user_456",
    "executedAt": "2024-02-22T10:30:15Z",
    "serviceResults": [
      {
        "serviceName": "content-management",
        "success": true,
        "recordsDeleted": 23,
        "storageFreedBytes": ********,
        "completedAt": "2024-02-22T10:30:05Z"
      },
      {
        "serviceName": "asset-service",
        "success": true,
        "recordsDeleted": 67,
        "storageFreedBytes": *********,
        "completedAt": "2024-02-22T10:30:10Z"
      },
      {
        "serviceName": "integration-service",
        "success": true,
        "recordsDeleted": 4,
        "storageFreedBytes": 1024,
        "completedAt": "2024-02-22T10:30:12Z"
      }
    ],
    "actualDeletedData": {
      "postsDeleted": 23,
      "imagesDeleted": 67,
      "templatesDeleted": 3,
      "connectionsRemoved": 4,
      "totalStorageFreed": *********
    },
    "completedSuccessfully": true,
    "processingTimeSeconds": 15
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "normal",
    "template": "account_deletion_completed"
  }
}

{
  "eventType": "DataBackupExpired",
  "data": {
    "backupId": "backup_123456",
    "userId": "user_789",
    "s3Key": "user_789/backup_123456.zip",
    "s3Bucket": "socialai-backups",
    "fileSizeBytes": ********,
    "createdAt": "2024-02-15T10:30:00Z",
    "expiredAt": "2024-02-16T10:30:00Z",
    "wasDownloaded": true,
    "downloadCount": 2,
    "lastDownloadAt": "2024-02-15T14:22:33Z"
  },
  "notificationConfig": {
    "channels": ["email"],
    "priority": "low",
    "template": "data_backup_expired"
  }
}

#### Platform Integration Event Schemas:
```json
{
  "eventType": "PlatformConnected",
  "data": {
    "connectionId": "conn_123456",
    "userId": "user_789",
    "platform": "facebook",
    "platformUserId": "fb_*********",
    "platformUsername": "john.doe",
    "platformDisplayName": "John Doe",
    "profileImageUrl": "https://graph.facebook.com/*********/picture",
    "grantedScopes": [
      "pages_manage_posts",
      "pages_read_engagement",
      "pages_show_list"
    ],
    "capabilities": {
      "canPublishText": true,
      "canPublishImages": true,
      "canPublishVideos": true,
      "canSchedulePosts": true,
      "canGetAnalytics": true,
      "maxTextLength": 63206,
      "maxImagesPerPost": 10
    },
    "connectedAt": "2024-02-15T10:30:00Z",
    "expiresAt": "2024-05-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "normal",
    "template": "platform_connected_success"
  }
}

{
  "eventType": "MultiPlatformPublishCompleted",
  "data": {
    "publishId": "pub_123456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "postContent": {
      "title": "Marketing Tips for 2024",
      "textContent": "Here are the top 5 marketing strategies...",
      "imageUrls": ["https://assets.socialai.com/img_123.jpg"],
      "hashtags": ["#marketing", "#2024trends", "#business"]
    },
    "platforms": ["facebook", "instagram", "twitter"],
    "platformResults": {
      "facebook": {
        "success": true,
        "platformPostId": "fb_********9",
        "platformPostUrl": "https://facebook.com/posts/********9",
        "publishedAt": "2024-02-15T10:30:12Z",
        "platformMetadata": {
          "pageId": "page_123",
          "pageName": "My Business Page"
        }
      },
      "instagram": {
        "success": true,
        "platformPostId": "ig_*********",
        "platformPostUrl": "https://instagram.com/p/*********",
        "publishedAt": "2024-02-15T10:30:15Z",
        "platformMetadata": {
          "mediaType": "IMAGE",
          "permalink": "https://instagram.com/p/*********"
        }
      },
      "twitter": {
        "success": false,
        "errorMessage": "Rate limit exceeded",
        "errorType": "rate_limit",
        "retryAfter": "2024-02-15T11:00:00Z"
      }
    },
    "successfulPlatforms": ["facebook", "instagram"],
    "failedPlatforms": ["twitter"],
    "overallSuccess": false,
    "publishedAt": "2024-02-15T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "high",
    "template": "multi_platform_publish_results"
  }
}

{
  "eventType": "PlatformTokenExpired",
  "data": {
    "connectionId": "conn_123456",
    "userId": "user_789",
    "platform": "instagram",
    "platformUsername": "john.doe",
    "expiredAt": "2024-02-15T10:30:00Z",
    "lastUsedAt": "2024-02-14T15:22:33Z",
    "affectedFeatures": [
      "post_publishing",
      "analytics_sync",
      "scheduled_posts"
    ],
    "reconnectionUrl": "https://app.socialai.com/integrations/instagram/reconnect",
    "gracePeriod": "7 days"
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "high",
    "template": "platform_token_expired"
  }
}

{
  "eventType": "PlatformAnalyticsSynced",
  "data": {
    "userId": "user_789",
    "platform": "youtube",
    "syncId": "sync_123456",
    "dateRangeStart": "2024-02-01",
    "dateRangeEnd": "2024-02-15",
    "syncResults": {
      "videosSynced": 12,
      "metricsCollected": [
        "views",
        "watch_time",
        "likes",
        "comments",
        "subscribers_gained"
      ],
      "totalViews": 15420,
      "totalWatchTimeMinutes": 8765,
      "totalLikes": 342,
      "totalComments": 89,
      "subscribersGained": 23
    },
    "topPerformingContent": {
      "videoId": "yt_video_123",
      "title": "How to Create Engaging Content",
      "views": 5420,
      "engagementRate": 0.087
    },
    "syncedAt": "2024-02-15T10:30:00Z",
    "nextSyncScheduled": "2024-02-16T10:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "low",
    "template": "analytics_synced"
  }
}

{
  "eventType": "PlatformRateLimitHit",
  "data": {
    "userId": "user_789",
    "platform": "tiktok",
    "operation": "publish_video",
    "rateLimitType": "hourly_upload_limit",
    "currentUsage": 10,
    "limit": 10,
    "resetTime": "2024-02-15T11:00:00Z",
    "retryAfter": "2024-02-15T11:00:00Z",
    "affectedOperations": [
      "video_upload",
      "post_publishing"
    ],
    "recommendedAction": "wait_for_reset"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "platform_rate_limit"
  }
}

{
  "eventType": "PlatformContentModerated",
  "data": {
    "userId": "user_789",
    "platform": "facebook",
    "postId": "post_123456",
    "platformPostId": "fb_*********",
    "moderationAction": "content_removed",
    "moderationReason": "community_guidelines_violation",
    "violationCategory": "spam",
    "moderatedAt": "2024-02-15T10:30:00Z",
    "appealUrl": "https://facebook.com/help/appeals/123456",
    "canAppeal": true,
    "appealDeadline": "2024-02-22T10:30:00Z",
    "postContent": {
      "title": "Original post title",
      "textContent": "Original post content...",
      "hashtags": ["#marketing", "#business"]
    }
  },
  "notificationConfig": {
    "channels": ["email", "push"],
    "priority": "critical",
    "template": "content_moderated_alert"
  }
}
```

### Authentication Workflow

#### Service-based Authentication Flow:
```
1. Client → API Gateway: Request with JWT token in Authorization header
2. API Gateway → Target Service: Forward request with JWT token (no validation)
3. Target Service Middleware → User Service: ValidateToken(jwt_token, service_name)
4. User Service → Target Service: ValidateTokenResponse(user_context)
5. Target Service Middleware → User Service: VerifyPermission(user_id, permission, resource_id)
6. User Service → Target Service: VerifyPermissionResponse(has_permission)
7. Target Service: Process request with user_context or return 401/403
8. Target Service → Client: Response
```

#### Service Middleware Authentication Pattern:
```
1. Request arrives at service with JWT token
2. Service Auth Middleware extracts JWT from header
3. Middleware → User Service: ValidateToken(jwt_token, current_service_name)
4. User Service → Middleware: ValidateTokenResponse(user_context)
5. If valid: Middleware adds user_context to request context
6. If invalid: Middleware returns 401 Unauthorized immediately
7. Business logic accesses user_context from request context
8. For specific operations: Business logic → User Service: VerifyPermission()
```

#### Workspace-based Permission Check:
```
1. Content Management Service receives request to create post in workspace
2. Content Management → User Service: ValidateToken(jwt_token)
3. User Service → Content Management: ValidateTokenResponse(user_context)
4. Content Management → User Service: VerifyPermission(user_id, "create_post", workspace_id)
5. User Service → Content Management: VerifyPermissionResponse(role="editor", has_permission=true)
6. Content Management: Allow post creation
```

#### Token Refresh Flow:
```
1. Service detects expired token (ValidateTokenResponse.is_valid=false)
2. Service → User Service: RefreshToken(refresh_token, user_id)
3. User Service → Service: RefreshTokenResponse(new_access_token)
4. Service: Retry original request with new token
```

### Credit Usage Policy

#### Operations That Consume Credits:
```yaml
ai_content_generation:
  text_generation: 1-5 credits (depending on model)
  image_generation: 3-10 credits (depending on model)
  content_improvement: 1-3 credits

premium_features:
  template_purchases: 1-50 credits (set by creator)
  rag_document_processing: 2 credits per document
  advanced_analytics_reports: 5 credits per report
  bulk_operations: 1 credit per batch
```

#### Operations That Are FREE (No Credits):
```yaml
core_features:
  post_publishing: FREE     # Đăng bài lên social platforms
  post_scheduling: FREE     # Lên lịch đăng bài
  post_management: FREE     # Create, edit, delete posts

collaboration:
  workspace_management: FREE # Create, manage workspaces
  team_collaboration: FREE   # Invite members, assign roles
  workspace_sharing: FREE    # Share posts within workspace

basic_analytics:
  post_performance: FREE     # Basic metrics (likes, shares, etc.)
  engagement_stats: FREE     # Basic engagement analytics
  platform_insights: FREE   # Basic platform performance

account_management:
  profile_management: FREE   # Update profile, settings
  platform_connections: FREE # Connect/disconnect social accounts
  notification_settings: FREE # Configure notifications
```

#### Rationale:
```
Core Value Proposition:
- Social media management và publishing là core features → FREE
- AI-powered content creation là premium features → CREDITS
- Advanced analytics và automation là premium features → CREDITS

Business Model:
- Users có thể manage và publish content miễn phí
- Credits chỉ cần khi sử dụng AI features
- Encourage adoption với free core features
- Monetize advanced AI capabilities
```

### Credit Check Workflow

#### Content Generation Flow:
```
1. Client → AI Content Service: GenerateContent()
2. AI Content Service → Credit Service: CheckUserCredits(user_id, required_credits)
3. Credit Service → AI Content Service: CheckCreditsResponse(has_sufficient=true/false)
4. If sufficient:
   a. AI Content Service → Credit Service: ReserveCredits(user_id, credits, operation_id)
   b. Credit Service → AI Content Service: ReserveCreditsResponse(reservation_id)
   c. AI Content Service → OpenAI/Claude/Gemini: Generate content
   d. AI Content Service → Credit Service: ConsumeCredits(reservation_id, actual_credits)
   e. AI Content Service → Client: GenerateContentResponse(content, credits_used)
5. If insufficient:
   a. AI Content Service → Client: Error(insufficient_credits)
```

#### Image Generation Flow:
```
1. Client → AI Content Service: GenerateImage()
2. AI Content Service → Credit Service: CheckUserCredits(user_id, estimated_credits)
3. Credit Service → AI Content Service: CheckCreditsResponse(has_sufficient=true/false)
4. If sufficient:
   a. AI Content Service → Credit Service: ReserveCredits(user_id, max_credits, operation_id)
   b. AI Content Service → DALL-E/Midjourney: Generate images
   c. AI Content Service → Asset Service: StoreImages(images, metadata)
   d. AI Content Service → Credit Service: ConsumeCredits(reservation_id, actual_credits)
   e. AI Content Service → Client: GenerateImageResponse(image_urls, credits_used)
```

#### Template Purchase Flow:
```
1. Client → Content Management Service: PurchaseTemplate()
2. Content Management Service → Credit Service: CheckUserCredits(user_id, template_cost)
3. Credit Service → Content Management Service: CheckCreditsResponse(has_sufficient=true/false)
4. If sufficient:
   a. Content Management Service → Credit Service: ConsumeCredits(user_id, template_cost)
   b. Content Management Service → Database: Add template to user library
   c. Content Management Service → Kafka: Publish TemplatePurchased event
   d. Content Management Service → Client: PurchaseTemplateResponse(success=true)
```

#### Post Publishing Flow (No Credits Required):
```
1. Client → Content Management Service: PublishPost() hoặc SchedulePost()
2. Content Management Service → User Service: ValidateToken() + VerifyPermission()
3. Content Management Service → Integration Service: ValidatePlatformConnections(user_id, platforms)
4. Content Management Service → Database: Create/Update post record
5. Content Management Service → Integration Service: PublishToMultiplePlatforms() (if immediate)
6. Content Management Service → Schedule Processor: AddToSchedule() (if scheduled)
7. Content Management Service → Kafka: Publish PostCreated/PostScheduled event
8. Content Management Service → Client: Success response

Note: Post publishing và scheduling hoàn toàn miễn phí, không tốn credits
```

### Social Platform Integration Flow

#### **Platform Connection Flow:**
```
1. Client → Integration Service: InitiatePlatformConnection(platform, user_id)
2. Integration Service → User Service: ValidateToken() + VerifyPermission()
3. Integration Service → Platform OAuth: RedirectToOAuth(platform, scopes, callback_url)
4. User → Platform OAuth: Authenticate và grant permissions
5. Platform OAuth → Integration Service: OAuth callback với authorization_code
6. Integration Service → Platform API: ExchangeCodeForTokens(authorization_code)
7. Platform API → Integration Service: Return access_token + refresh_token
8. Integration Service → Database: Store encrypted tokens
9. Integration Service → Platform API: GetUserProfile() để verify connection
10. Integration Service → Database: Update connection status='active'
11. Integration Service → Kafka: Publish PlatformConnected event
12. Integration Service → Client: ConnectionResponse(success, platform_info)
13. Notification Service → User: Send connection confirmation
```

#### **Multi-Platform Publishing Flow:**
```
1. Client → Integration Service: PublishToMultiplePlatforms(post_content, platforms)
2. Integration Service → User Service: ValidateToken() + VerifyPermission()
3. Integration Service → Database: Validate platform connections are active
4. Integration Service → Content Processor: AdaptContentForPlatforms(content, platforms)

Parallel Platform Publishing:
5a. Integration Service → Facebook API: PublishPost(adapted_content)
5b. Integration Service → Instagram API: PublishPost(adapted_content)
5c. Integration Service → Twitter API: PublishTweet(adapted_content)
5d. Integration Service → YouTube API: UploadVideo(adapted_content)
5e. Integration Service → TikTok API: PublishVideo(adapted_content)

6. Platform APIs → Integration Service: Return publish results
7. Integration Service → Database: Store publish results per platform
8. Integration Service → Kafka: Publish MultiPlatformPublishCompleted event
9. Integration Service → Client: PublishResponse(platform_results)
10. Notification Service → User: Send publish status summary
```

#### **Platform-Specific Content Adaptation:**
```
Facebook Flow:
1. Content Processor → Facebook Adapter: AdaptForFacebook(content)
2. Facebook Adapter → Facebook Adapter: Optimize text length (63,206 chars max)
3. Facebook Adapter → Facebook Adapter: Process images (max 10 images)
4. Facebook Adapter → Facebook Adapter: Add hashtags và mentions
5. Facebook Adapter → Facebook API: POST /me/feed

Instagram Flow:
1. Content Processor → Instagram Adapter: AdaptForInstagram(content)
2. Instagram Adapter → Instagram Adapter: Optimize for visual content
3. Instagram Adapter → Instagram Adapter: Process images (1080x1080 optimal)
4. Instagram Adapter → Instagram Adapter: Limit text (2,200 chars max)
5. Instagram Adapter → Instagram Adapter: Add hashtags (max 30)
6. Instagram Adapter → Instagram API: POST /media

Twitter Flow:
1. Content Processor → Twitter Adapter: AdaptForTwitter(content)
2. Twitter Adapter → Twitter Adapter: Split long content into threads
3. Twitter Adapter → Twitter Adapter: Optimize text (280 chars per tweet)
4. Twitter Adapter → Twitter Adapter: Process images (max 4 images)
5. Twitter Adapter → Twitter API: POST /tweets

YouTube Flow:
1. Content Processor → YouTube Adapter: AdaptForYouTube(content)
2. YouTube Adapter → YouTube Adapter: Prepare video metadata
3. YouTube Adapter → YouTube Adapter: Generate thumbnail from content
4. YouTube Adapter → YouTube Adapter: Set video description (5,000 chars max)
5. YouTube Adapter → YouTube API: POST /upload/youtube/v3/videos

TikTok Flow:
1. Content Processor → TikTok Adapter: AdaptForTikTok(content)
2. TikTok Adapter → TikTok Adapter: Optimize for vertical video (9:16)
3. TikTok Adapter → TikTok Adapter: Add trending hashtags
4. TikTok Adapter → TikTok Adapter: Set video description (2,200 chars max)
5. TikTok Adapter → TikTok API: POST /share/video/upload/
```

#### **Token Refresh & Error Handling Flow:**
```
Token Refresh Flow:
1. Integration Service → Platform API: Make API call với access_token
2. Platform API → Integration Service: Return 401 Unauthorized (token expired)
3. Integration Service → Database: Get stored refresh_token
4. Integration Service → Platform OAuth: RefreshAccessToken(refresh_token)
5. Platform OAuth → Integration Service: Return new access_token + refresh_token
6. Integration Service → Database: Update stored tokens
7. Integration Service → Platform API: Retry original API call
8. Integration Service → Kafka: Publish TokenRefreshed event

Connection Error Flow:
1. Integration Service → Platform API: Attempt API call
2. Platform API → Integration Service: Return error (rate limit, auth revoked, etc.)
3. Integration Service → Error Handler: ProcessPlatformError(error_type, platform)
4. Error Handler → Database: Update connection status based on error
5. Error Handler → Kafka: Publish PlatformError event
6. Error Handler → Retry Scheduler: Schedule retry if applicable
7. Notification Service → User: Send error notification với resolution steps
```

#### **Platform Analytics Sync Flow:**
```
1. Analytics Sync Service → Scheduler: Trigger daily analytics sync
2. Analytics Sync Service → Database: Get all active platform connections
3. Analytics Sync Service → Platform APIs: Fetch analytics data (parallel)

Facebook Analytics:
4a. Analytics Sync → Facebook API: GET /insights (page insights)
4b. Facebook API → Analytics Sync: Return engagement metrics

Instagram Analytics:
4c. Analytics Sync → Instagram API: GET /insights (media insights)
4d. Instagram API → Analytics Sync: Return reach, impressions, engagement

Twitter Analytics:
4e. Analytics Sync → Twitter API: GET /tweets/:id/metrics
4f. Twitter API → Analytics Sync: Return retweets, likes, replies

YouTube Analytics:
4g. Analytics Sync → YouTube API: GET /analytics/reports
4h. YouTube API → Analytics Sync: Return views, watch time, subscribers

TikTok Analytics:
4i. Analytics Sync → TikTok API: GET /video/list/ với analytics
4j. TikTok API → Analytics Sync: Return views, likes, shares, comments

5. Analytics Sync Service → Database: Store normalized analytics data
6. Analytics Sync Service → Kafka: Publish AnalyticsSynced event
7. Analytics Service → Analytics Processing: Process và aggregate data
8. Analytics Service → Dashboard: Update real-time metrics
```

#### **Platform Disconnection Flow:**
```
1. Client → Integration Service: DisconnectPlatform(platform, user_id)
2. Integration Service → User Service: ValidateToken() + VerifyPermission()
3. Integration Service → Platform API: RevokeAccessToken() (if supported)
4. Integration Service → Database: Update connection status='disconnected'
5. Integration Service → Database: Archive connection history
6. Integration Service → Cache: Clear platform-specific cache
7. Integration Service → Kafka: Publish PlatformDisconnected event
8. Integration Service → Client: DisconnectionResponse(success)
9. Notification Service → User: Send disconnection confirmation
```

### Data Deletion & Backup Flow

#### **User Data Backup Flow:**
```
1. Client → User Service: RequestDataBackup(user_id, include_options)
2. User Service → User Service: ValidateToken() + VerifyPermission()
3. User Service → Database: Create backup_request record
4. User Service → Kafka: Publish DataBackupRequested event
5. User Service → Client: BackupRequestResponse(backup_id, estimated_time)

Background Processing:
6. Backup Service → Kafka Consumer: Process DataBackupRequested event
7. Backup Service → Content Management: GetUserPosts(user_id)
8. Backup Service → Asset Service: GetUserAssets(user_id, include_images=true)
9. Backup Service → Integration Service: GetUserConnections(user_id)
10. Backup Service → Credit Service: GetUserTransactions(user_id)
11. Backup Service → Analytics Service: GetUserAnalytics(user_id)

HTML Generation:
12. Backup Service → HTML Generator: CreateHTMLPages(posts, images, metadata)
13. HTML Generator → HTML Generator: Generate post pages với embedded images
14. HTML Generator → HTML Generator: Create index.html với navigation
15. HTML Generator → HTML Generator: Add CSS styling và responsive design
16. HTML Generator → ZIP Creator: Package HTML + images into ZIP

S3 Upload:
17. Backup Service → S3: Upload ZIP file với unique key
18. Backup Service → S3: Generate presigned download URL (24h expiry)
19. Backup Service → Database: Update backup status='completed'
20. Backup Service → Kafka: Publish DataBackupCompleted event
21. Notification Service → User: Send download link via email
```

#### **User Account Deletion Flow:**
```
1. Client → User Service: RequestAccountDeletion(user_id, deletion_reason)
2. User Service → User Service: ValidateToken() + VerifyPermission()
3. User Service → Database: Create deletion_request record
4. User Service → Kafka: Publish AccountDeletionRequested event
5. User Service → Client: DeletionRequestResponse(deletion_id, grace_period)

Grace Period (7 days):
6. User Service → Database: Mark account as 'pending_deletion'
7. User Service → Kafka: Publish AccountMarkedForDeletion event
8. Notification Service → User: Send deletion confirmation email
9. User Service → User Service: Schedule deletion job for 7 days later

Optional Data Backup:
10. User Service → User: Offer data backup before deletion
11. If accepted → Execute Data Backup Flow (steps 1-21 above)

Account Deletion Execution:
12. Deletion Service → Kafka Consumer: Process AccountDeletionRequested event (after grace period)
13. Deletion Service → Content Management: DeleteUserPosts(user_id)
14. Deletion Service → Asset Service: DeleteUserAssets(user_id)
15. Deletion Service → Integration Service: DisconnectUserAccounts(user_id)
16. Deletion Service → Credit Service: FinalizeUserBilling(user_id)
17. Deletion Service → Analytics Service: AnonymizeUserData(user_id)
18. Deletion Service → RAG Processing Service: DeleteUserDocuments(user_id)
19. Deletion Service → User Service: DeleteUserAccount(user_id)
20. Deletion Service → Database: Update deletion status='completed'
21. Deletion Service → Kafka: Publish AccountDeleted event
22. Notification Service → User: Send deletion confirmation (final email)
```

#### **Data Backup HTML Structure:**
```
backup_[user_id]_[timestamp].zip
├── index.html                 # Main navigation page
├── posts/
│   ├── post_[id].html         # Individual post pages
│   ├── post_[id]_images/      # Post-specific images
│   └── ...
├── images/
│   ├── profile/               # Profile images
│   ├── assets/                # User uploaded assets
│   └── generated/             # AI generated images
├── data/
│   ├── user_profile.json      # User profile data
│   ├── connections.json       # Social platform connections
│   ├── analytics.json         # User analytics data
│   └── transactions.json      # Credit transactions
├── styles/
│   ├── main.css              # Main stylesheet
│   └── responsive.css        # Mobile responsive styles
└── README.txt                # Instructions for viewing backup
```

### Bank Transfer & Payment Flow

#### **Bank Transfer Payment Flow:**
```
1. Client → Credit Service: InitiateBankTransfer(plan_id, amount, user_info)
2. Credit Service → User Service: ValidateToken() + GetUserInfo()
3. Credit Service → Database: Create pending_bank_transfer record
4. Credit Service → Bank API: GenerateTransferInstructions(amount, reference_code)
5. Credit Service → Database: Save transfer instructions
6. Credit Service → Client: BankTransferResponse(transfer_instructions, reference_code, expiry)
7. Credit Service → Kafka: Publish BankTransferInitiated event
8. Notification Service → User: Send transfer instructions via email/SMS

Manual Confirmation Flow:
9. Admin → Credit Service: ConfirmBankTransfer(reference_code, actual_amount, bank_transaction_id)
10. Credit Service → Database: Update transfer status='confirmed'
11. Credit Service → SubscriptionService: ActivateSubscription(user_id, plan_id)
12. Credit Service → CreditService: AddCredits(user_id, credit_amount)
13. Credit Service → Database: Create credit_transaction record
14. Credit Service → Kafka: Publish PaymentConfirmed + SubscriptionActivated events
15. Notification Service → User: Send activation confirmation + receipt

Auto Confirmation Flow (with Bank API):
9. Bank Webhook → Credit Service: BankTransferReceived(reference_code, amount, transaction_id)
10. Credit Service → Database: Verify transfer details
11. Credit Service → Auto-execute steps 10-15 from manual flow
```

#### **Subscription Activation Flow:**
```
1. Credit Service → SubscriptionService: ActivateSubscription(user_id, plan_id, payment_id)
2. SubscriptionService → Database: Get plan details
3. SubscriptionService → Database: Check existing active subscriptions
4. SubscriptionService → Database: Deactivate old subscription (if upgrade/downgrade)
5. SubscriptionService → Database: Create new subscription record
6. SubscriptionService → CreditService: AddPlanCredits(user_id, plan_credits)
7. SubscriptionService → Database: Update user plan status
8. SubscriptionService → Kafka: Publish SubscriptionActivated event
9. SubscriptionService → Credit Service: SubscriptionResponse(success, subscription_details)
```

#### **Credit Addition Flow:**
```
1. Credit Service → CreditService: AddCredits(user_id, amount, source, reference_id)
2. CreditService → Database: Get current user credits
3. CreditService → Database: Create credit_transaction record
4. CreditService → Database: Update user_credits balance
5. CreditService → Cache: Update Redis credit cache
6. CreditService → Kafka: Publish CreditsAdded event
7. CreditService → Analytics Service: Track credit addition
8. CreditService → Credit Service: AddCreditsResponse(new_balance, transaction_id)
```

#### Error Handling & Compensation:
```
- If AI generation fails after credit reservation:
  AI Content Service → Credit Service: RefundCredits(reservation_id)

- If network timeout during credit consumption:
  Implement idempotent operations with operation_id

- If partial image generation (requested 4, got 2):
  Calculate actual cost and consume only used credits
```

### Asset Upload Workflow (S3/MinIO)

#### Secure Upload Flow với Pre-signed URLs:
```
1. Client → Asset Service: RequestUpload(filename, filesize, filetype, purpose)
2. Asset Service → User Service: ValidateToken() + CheckPermission()
3. Asset Service → Credit Service: CheckStorageQuota(user_id, filesize)
4. Asset Service → Database: Create pending_upload record
5. Asset Service → S3/MinIO: GeneratePresignedURL(bucket, key, expiry=15min)
6. Asset Service → Client: UploadResponse(presigned_url, upload_id, s3_key)
7. Client → S3/MinIO: Direct upload using presigned URL
8. Client → Asset Service: ConfirmUpload(upload_id, final_filesize, checksum)
9. Asset Service → S3/MinIO: VerifyFileExists(s3_key) + ValidateChecksum()
10. Asset Service → Database: Update upload status to 'completed'
11. Asset Service → Kafka: Publish FileUploaded event
12. Asset Service → Client: UploadConfirmResponse(asset_id, public_url)
```

#### Upload Cleanup & Garbage Collection:
```
Scheduled Jobs (every 30 minutes):
1. Find pending uploads older than 1 hour
2. Check if file exists in S3/MinIO
3. If exists but not confirmed → Delete from S3 + Mark as 'expired'
4. If not exists → Mark as 'failed'
5. Cleanup orphaned database records older than 24 hours

Lifecycle Policies:
- Incomplete multipart uploads: Auto-delete after 1 day
- Temporary files: Auto-delete after 7 days if not referenced
- Deleted assets: Move to 'deleted' prefix, auto-delete after 30 days
```

#### File Processing Pipeline:
```
1. FileUploaded event → Asset Processing Service
2. Virus Scanning → ClamAV/VirusTotal integration
3. File Type Validation → Magic number verification
4. Image Processing → Generate thumbnails, resize variants
5. Document Processing → Extract text for RAG (PDF, DOC, TXT)
6. Metadata Extraction → EXIF data, document properties
7. Content Moderation → AI-based content scanning
8. Final Storage → Move to permanent bucket location
9. Database Update → Update asset record with processed info
10. Kafka Event → AssetProcessingComplete
```

#### RAG Document Processing Flow:
```
1. Document uploaded → Asset Service validates format
2. Asset Service → AI Content Service: ProcessRAGDocument(asset_id, s3_key)
3. AI Content Service → S3: Download document
4. Text Extraction → PyPDF2, python-docx, etc.
5. Text Chunking → Split into semantic chunks
6. Vector Embedding → Generate embeddings using OpenAI/Sentence-BERT
7. Vector Storage → Store in Pinecone/Weaviate/Qdrant
8. AI Content Service → Database: Save document metadata + chunk references
9. AI Content Service → Kafka: Publish RAGDocumentProcessed event
10. Notification Service → User: "Document ready for AI generation"
```

#### Storage Quota Management:
```
Per-User Quotas:
- Free Plan: 100MB total, 10MB per file
- Pro Plan: 1GB total, 50MB per file
- Premium Plan: 10GB total, 200MB per file

Quota Enforcement:
1. Check current usage before upload
2. Reserve space during upload process
3. Confirm space usage after successful upload
4. Release reserved space if upload fails
5. Background job to recalculate actual usage daily
```

#### File Security & Access Control:
```
S3 Bucket Structure:
socialai-assets-prod/
├── users/{user_id}/
│   ├── images/{year}/{month}/
│   ├── documents/{year}/{month}/
│   └── temp/{upload_id}/
├── public/
│   ├── stock-images/
│   └── templates/
└── deleted/{date}/

Access Patterns:
- Private files: Signed URLs with 1-hour expiry
- Public files: CloudFront CDN distribution
- Temp files: Auto-delete after 24 hours
- User isolation: IAM policies prevent cross-user access
```

### Permission Matrix

#### Global Permissions:
```yaml
user:
  - read_own_profile
  - update_own_profile
  - create_post
  - read_own_posts
  - use_free_templates
  - basic_ai_generation

premium_user:
  - all user permissions
  - advanced_ai_generation
  - premium_templates
  - bulk_operations
  - advanced_analytics

admin:
  - all premium_user permissions
  - manage_users
  - moderate_templates
  - system_analytics
  - manage_billing
```

#### Workspace Permissions:
```yaml
owner:
  - all workspace operations
  - manage_members
  - delete_workspace
  - billing_management

admin:
  - manage_content
  - manage_members (except owner)
  - workspace_settings
  - view_analytics

editor:
  - create_posts
  - edit_posts
  - use_templates
  - schedule_posts

viewer:
  - read_posts
  - view_analytics
  - comment_on_posts
```

### Authentication Caching Strategy

#### Redis Cache Structure:
```
Key Pattern: "auth:token:{token_hash}"
Value: {
  "user_id": "user_123",
  "email": "<EMAIL>",
  "role": "premium_user",
  "permissions": ["create_post", "advanced_ai"],
  "workspaces": {
    "ws_1": {"role": "owner", "permissions": ["all"]},
    "ws_2": {"role": "editor", "permissions": ["create_post"]}
  },
  "expires_at": 1640995200
}
TTL: 15 minutes

Key Pattern: "auth:user:{user_id}"
Value: {user_context}
TTL: 5 minutes

Key Pattern: "auth:permission:{user_id}:{permission}:{resource_id}"
Value: {"has_permission": true, "role": "editor"}
TTL: 2 minutes
```

#### Cache Invalidation Events:
```
- User role changed → Invalidate "auth:user:{user_id}"
- User added/removed from workspace → Invalidate workspace permissions
- Token revoked → Invalidate "auth:token:{token_hash}"
- User deleted → Invalidate all user-related cache
```

### Service-to-Service Communication

#### User Service Client in Each Service:
```go
type UserServiceClient struct {
    client     UserServiceGRPCClient
    cache      *redis.Client
    circuitBreaker *breaker.CircuitBreaker
}

func (c *UserServiceClient) ValidateToken(ctx context.Context, token string) (*UserContext, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("auth:token:%s", hashToken(token))
    if cached, err := c.cache.Get(ctx, cacheKey).Result(); err == nil {
        var userCtx UserContext
        if json.Unmarshal([]byte(cached), &userCtx) == nil {
            return &userCtx, nil
        }
    }

    // Call User Service via gRPC with circuit breaker
    resp, err := c.circuitBreaker.Execute(func() (interface{}, error) {
        return c.client.ValidateToken(ctx, &ValidateTokenRequest{
            JwtToken:    token,
            ServiceName: getCurrentServiceName(),
        })
    })

    if err != nil {
        return nil, err
    }

    validateResp := resp.(*ValidateTokenResponse)
    if !validateResp.IsValid {
        return nil, errors.New("invalid token")
    }

    // Cache the result
    userCtxJSON, _ := json.Marshal(validateResp.UserContext)
    c.cache.Set(ctx, cacheKey, userCtxJSON, 15*time.Minute)

    return validateResp.UserContext, nil
}
```

#### Service Registration Pattern:
```go
// Each service registers itself with User Service
func RegisterService(userServiceClient UserServiceClient) error {
    return userServiceClient.RegisterService(context.Background(), &RegisterServiceRequest{
        ServiceName: getCurrentServiceName(),
        ServiceUrl:  getCurrentServiceURL(),
        PublicEndpoints: []string{"/health", "/metrics"},
        RequiredPermissions: map[string]string{
            "/api/posts":     "create_post",
            "/api/templates": "use_template",
        },
    })
}
```

---

## 🏗️ Infrastructure Architecture

### Container Orchestration
```yaml
# Kubernetes Deployment Example
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: socialai/user-service:v1.2.0
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: user-db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### Service Mesh với Istio
```yaml
# Service Mesh Configuration
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service
spec:
  http:
  - match:
    - uri:
        prefix: /api/v1/auth
    route:
    - destination:
        host: user-service
        port:
          number: 3000
  - fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
```

### Monitoring Stack
```yaml
# Prometheus + Grafana + Jaeger
monitoring:
  prometheus:
    - metrics collection từ tất cả services
    - Custom metrics cho business logic
  grafana:
    - Dashboard cho system health
    - Business metrics visualization
  jaeger:
    - Distributed tracing
    - Request flow analysis
  elk-stack:
    - Centralized logging
    - Log aggregation và search
```

---

## 🔒 Security Architecture

### Authentication Flow
```
1. User login → User Service validates credentials
2. User Service generates JWT + Refresh token
3. Client includes JWT in Authorization header for all requests
4. API Gateway forwards request to target service (no auth validation)
5. Target Service Middleware → User Service: ValidateToken()
6. User Service → Target Service: Returns user_context
7. Target Service processes request with user_context
8. For protected operations → User Service: VerifyPermission()
```

### Service Authentication Middleware

#### API Gateway (Simple Routing):
```go
// API Gateway chỉ làm routing, không validate token
func RouteHandler(w http.ResponseWriter, r *http.Request) {
    // Extract service from path
    serviceName := extractServiceFromPath(r.URL.Path)

    // Forward request to target service with original headers
    targetURL := getServiceURL(serviceName)
    proxy := httputil.NewSingleHostReverseProxy(targetURL)
    proxy.ServeHTTP(w, r)
}
```

#### Service Authentication Middleware (REST):
```go
func AuthMiddleware(userServiceClient UserServiceClient) func(http.Handler) http.Handler {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            // Skip auth for health checks and public endpoints
            if isPublicEndpoint(r.URL.Path) {
                next.ServeHTTP(w, r)
                return
            }

            // Extract JWT token from Authorization header
            authHeader := r.Header.Get("Authorization")
            if authHeader == "" {
                http.Error(w, "Missing Authorization header", http.StatusUnauthorized)
                return
            }

            token := strings.TrimPrefix(authHeader, "Bearer ")

            // Validate token with User Service
            ctx := r.Context()
            resp, err := userServiceClient.ValidateToken(ctx, &ValidateTokenRequest{
                JwtToken:    token,
                ServiceName: "current-service-name",
                Endpoint:    r.URL.Path,
            })

            if err != nil || !resp.IsValid {
                http.Error(w, "Invalid token", http.StatusUnauthorized)
                return
            }

            // Add user context to request context
            ctx = context.WithValue(ctx, "user_context", resp.UserContext)
            r = r.WithContext(ctx)

            next.ServeHTTP(w, r)
        })
    }
}
```

#### Service gRPC Interceptor:
```go
func AuthInterceptor(userServiceClient UserServiceClient) grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo,
        handler grpc.UnaryHandler) (interface{}, error) {

        // Skip auth for health checks and internal service calls
        if isInternalCall(info.FullMethod) {
            return handler(ctx, req)
        }

        // Extract token from gRPC metadata
        md, ok := metadata.FromIncomingContext(ctx)
        if !ok {
            return nil, status.Error(codes.Unauthenticated, "missing metadata")
        }

        tokens := md.Get("authorization")
        if len(tokens) == 0 {
            return nil, status.Error(codes.Unauthenticated, "missing authorization token")
        }

        token := strings.TrimPrefix(tokens[0], "Bearer ")

        // Validate with User Service
        resp, err := userServiceClient.ValidateToken(ctx, &ValidateTokenRequest{
            JwtToken:    token,
            ServiceName: getCurrentServiceName(),
            Endpoint:    info.FullMethod,
        })

        if err != nil || !resp.IsValid {
            return nil, status.Error(codes.Unauthenticated, "invalid token")
        }

        // Add user context to gRPC context
        ctx = context.WithValue(ctx, "user_context", resp.UserContext)

        return handler(ctx, req)
    }
}
```

#### Business Logic Usage:
```go
func (s *PostService) CreatePost(ctx context.Context, req *CreatePostRequest) (*CreatePostResponse, error) {
    // Get user context from middleware
    userCtx, ok := ctx.Value("user_context").(*UserContext)
    if !ok {
        return nil, status.Error(codes.Internal, "user context not found")
    }

    // Check specific permission for workspace
    if req.WorkspaceId != "" {
        permResp, err := s.userServiceClient.VerifyPermission(ctx, &VerifyPermissionRequest{
            UserId:      userCtx.UserId,
            Permission:  "create_post",
            ResourceId:  req.WorkspaceId,
            WorkspaceId: req.WorkspaceId,
        })

        if err != nil || !permResp.HasPermission {
            return nil, status.Error(codes.PermissionDenied, "insufficient permissions")
        }
    }

    // Process request with user context
    post := &Post{
        UserId:      userCtx.UserId,
        WorkspaceId: req.WorkspaceId,
        Title:       req.Title,
        Content:     req.Content,
    }

    // Save post and return response
    return s.savePost(ctx, post)
}
```

### Authorization Matrix
```
Service          | Public | User | Admin | System
-----------------|--------|------|-------|--------
User Service     | Login  | Profile | All | Internal
AI Content       | -      | Generate | Config | Internal
Post Service     | -      | Own Posts | All | Internal
Template Service | Browse | Own+Buy | Moderate | Internal
```

### Data Encryption
- **At Rest:** AES-256 cho sensitive data
- **In Transit:** TLS 1.3 cho tất cả communications
- **Application Level:** Field-level encryption cho PII

---

## 📊 Performance & Scalability

### Caching Strategy
```
Level 1: Browser Cache (Static assets)
Level 2: CDN Cache (Images, public content)
Level 3: API Gateway Cache (Public APIs)
Level 4: Application Cache (Redis)
Level 5: Database Query Cache
```

### Auto-scaling Rules
```yaml
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-content-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-content-service
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Scaling
- **Read Replicas:** Cho analytics và reporting
- **Sharding:** User data theo geographic regions
- **Connection Pooling:** PgBouncer cho PostgreSQL
- **Query Optimization:** Indexes và query analysis

---

## 🧪 Testing Strategy

### Unit Testing
- Mỗi service có test coverage > 80%
- Mock external dependencies
- Test business logic isolation

### Integration Testing
- Contract testing giữa services
- Database integration tests
- API endpoint testing

### End-to-End Testing
- User journey testing
- Cross-service workflow testing
- Performance testing under load

### Chaos Engineering
- Random service failures
- Network partition simulation
- Database failover testing

---

## 💻 Asset Service Implementation Example

### Secure Upload Flow Implementation:

```go
// Asset Service - Upload Request Handler
func (s *AssetService) RequestUpload(ctx context.Context, req *RequestUploadRequest) (*RequestUploadResponse, error) {
    // 1. Validate user authentication
    userCtx := ctx.Value("user_context").(*UserContext)
    if userCtx.UserId != req.UserId {
        return nil, status.Error(codes.PermissionDenied, "unauthorized")
    }

    // 2. Check storage quota
    quotaResp, err := s.creditServiceClient.CheckStorageQuota(ctx, &CheckStorageQuotaRequest{
        UserId: req.UserId,
        AdditionalBytes: req.Filesize,
    })
    if err != nil || !quotaResp.HasCapacity {
        return nil, status.Error(codes.ResourceExhausted, "storage quota exceeded")
    }

    // 3. Validate file type and size
    if !s.isAllowedFileType(req.ContentType, req.Purpose) {
        return nil, status.Error(codes.InvalidArgument, "file type not allowed")
    }

    maxSize := s.getMaxFileSize(userCtx.Plan, req.Purpose)
    if req.Filesize > maxSize {
        return nil, status.Error(codes.InvalidArgument, "file too large")
    }

    // 4. Generate unique S3 key
    uploadId := uuid.New().String()
    s3Key := s.generateS3Key(req.UserId, req.Purpose, uploadId, req.Filename)

    // 5. Create pending upload record
    upload := &PendingUpload{
        UploadId:    uploadId,
        UserId:      req.UserId,
        WorkspaceId: req.WorkspaceId,
        Filename:    req.Filename,
        Filesize:    req.Filesize,
        ContentType: req.ContentType,
        Purpose:     req.Purpose,
        S3Key:       s3Key,
        Status:      "pending",
        ExpiresAt:   time.Now().Add(15 * time.Minute),
        CreatedAt:   time.Now(),
    }

    if err := s.db.Create(upload).Error; err != nil {
        return nil, status.Error(codes.Internal, "failed to create upload record")
    }

    // 6. Generate presigned URL
    presignedURL, err := s.s3Client.GeneratePresignedURL(s3Key, 15*time.Minute)
    if err != nil {
        return nil, status.Error(codes.Internal, "failed to generate presigned URL")
    }

    // 7. Publish event
    s.eventBus.Publish("UploadRequested", map[string]interface{}{
        "uploadId":    uploadId,
        "userId":      req.UserId,
        "filename":    req.Filename,
        "filesize":    req.Filesize,
        "contentType": req.ContentType,
        "purpose":     req.Purpose,
    })

    return &RequestUploadResponse{
        Success:      true,
        UploadId:     uploadId,
        PresignedUrl: presignedURL,
        S3Key:        s3Key,
        ExpiresIn:    900, // 15 minutes
        Limits: &UploadLimits{
            MaxFileSize:    maxSize,
            RemainingQuota: quotaResp.RemainingBytes,
            AllowedTypes:   s.getAllowedTypes(req.Purpose),
        },
    }, nil
}

// Cleanup Job - Runs every 30 minutes
func (s *AssetService) CleanupOrphanedFiles() {
    // 1. Find expired pending uploads
    var expiredUploads []PendingUpload
    s.db.Where("expires_at < ? AND status = 'pending'", time.Now()).Find(&expiredUploads)

    for _, upload := range expiredUploads {
        // 2. Check if file exists in S3
        exists, _, err := s.s3Client.VerifyFileExists(upload.S3Key)
        if err != nil {
            continue
        }

        if exists {
            // 3. Delete orphaned file from S3
            if err := s.s3Client.DeleteFile(upload.S3Key); err != nil {
                log.Printf("Failed to delete orphaned file %s: %v", upload.S3Key, err)
                continue
            }

            // 4. Publish cleanup event
            s.eventBus.Publish("OrphanedFileDetected", map[string]interface{}{
                "uploadId": upload.UploadId,
                "userId":   upload.UserId,
                "s3Key":    upload.S3Key,
                "filesize": upload.Filesize,
                "cleanedAt": time.Now(),
            })
        }

        // 5. Update upload status
        s.db.Model(&upload).Update("status", "expired")
    }

    // 6. Cleanup old records
    s.db.Where("created_at < ? AND status IN ('expired', 'failed')",
        time.Now().Add(-24*time.Hour)).Delete(&PendingUpload{})
}
```

## ⏰ Schedule Post Flow Design

### Distributed Scheduling Architecture

#### Overview:
```
Thay vì dùng traditional cron jobs, hệ thống sử dụng:
1. Kafka-based event scheduling
2. Distributed task processing
3. Fault-tolerant execution
4. Multi-timezone support
5. Platform-specific scheduling
```

### 1. **Schedule Post Creation Flow:**

```
1. Client → Content Management Service: SchedulePost(post_id, schedule_time, platforms, timezone)
2. Content Management → User Service: ValidateToken() + VerifyPermission()
3. Content Management → Integration Service: ValidatePlatformConnections(user_id, platforms)
4. Content Management → Database: Create schedule_record với status='pending'
5. Content Management → Kafka: Publish PostScheduled event
6. Schedule Processor → Kafka Consumer: Process PostScheduled event
7. Schedule Processor → Redis: Add to time-based sorted set
8. Schedule Processor → Database: Update schedule status='queued'
9. Content Management → Client: ScheduleResponse(schedule_id, estimated_publish_time)
```

### 2. **Distributed Schedule Processing:**

#### **Redis-based Time Scheduler:**
```
Redis Sorted Sets Structure - Multiple Granularity Levels:

1. Minute-level precision (Primary):
Key: "scheduled_posts:minute:{YYYY-MM-DD-HH-mm}"
Score: Unix timestamp (seconds precision)
Value: JSON payload với post details

2. Hour-level buckets (Secondary):
Key: "scheduled_posts:hour:{YYYY-MM-DD-HH}"
Score: Unix timestamp
Value: JSON payload

3. Day-level overview (Tertiary):
Key: "scheduled_posts:day:{YYYY-MM-DD}"
Score: Unix timestamp
Value: JSON payload

Examples:
ZADD scheduled_posts:minute:2024-02-15-14-30 1708012800 '{"schedule_id":"sch_123","post_id":"post_456","scheduled_time":"2024-02-15T14:30:00Z"}'
ZADD scheduled_posts:hour:2024-02-15-14 1708012800 '{"schedule_id":"sch_123","post_id":"post_456","scheduled_time":"2024-02-15T14:30:00Z"}'
ZADD scheduled_posts:day:2024-02-15 1708012800 '{"schedule_id":"sch_123","post_id":"post_456","scheduled_time":"2024-02-15T14:30:00Z"}'
```

#### **Schedule Processor Service:**
```go
// Schedule Processor - Runs every 30 seconds for high precision
func (s *ScheduleProcessor) ProcessScheduledPosts() {
    now := time.Now()
    currentMinute := now.Format("2006-01-02-15-04")
    nextMinute := now.Add(1 * time.Minute).Format("2006-01-02-15-04")

    // Process current minute and next minute
    minuteKeys := []string{
        fmt.Sprintf("scheduled_posts:minute:%s", currentMinute),
        fmt.Sprintf("scheduled_posts:minute:%s", nextMinute),
    }

    for _, key := range minuteKeys {
        s.processMinuteBucket(key, now)
    }

    // Also check hour bucket for any missed posts
    currentHour := now.Format("2006-01-02-15")
    s.processHourBucket(fmt.Sprintf("scheduled_posts:hour:%s", currentHour), now)
}

func (s *ScheduleProcessor) processMinuteBucket(key string, now time.Time) {
    // Get posts scheduled for current time window (±30 seconds)
    startTime := now.Add(-30 * time.Second).Unix()
    endTime := now.Add(90 * time.Second).Unix() // 1.5 minutes buffer

    posts, err := s.redis.ZRangeByScore(context.Background(), key, &redis.ZRangeBy{
        Min: fmt.Sprintf("%d", startTime),
        Max: fmt.Sprintf("%d", endTime),
    }).Result()

    if err != nil {
        log.Printf("Error getting scheduled posts from %s: %v", key, err)
        return
    }

    for _, postData := range posts {
        var schedule ScheduledPost
        if err := json.Unmarshal([]byte(postData), &schedule); err != nil {
            log.Printf("Error unmarshaling schedule data: %v", err)
            continue
        }

        // Check if it's time to publish (within 30 seconds)
        scheduledTime := time.Unix(schedule.ScheduledTimestamp, 0)
        timeDiff := scheduledTime.Sub(now)

        if timeDiff <= 30*time.Second && timeDiff >= -30*time.Second {
            // Process in goroutine for parallel execution
            go s.processScheduledPostViaPublishFlow(schedule)

            // Remove from all Redis buckets after processing
            s.removeFromAllBuckets(schedule)
        }
    }
}

func (s *ScheduleProcessor) processHourBucket(key string, now time.Time) {
    // Fallback: check hour bucket for any posts that might have been missed
    startTime := now.Add(-5 * time.Minute).Unix()
    endTime := now.Add(5 * time.Minute).Unix()

    posts, err := s.redis.ZRangeByScore(context.Background(), key, &redis.ZRangeBy{
        Min: fmt.Sprintf("%d", startTime),
        Max: fmt.Sprintf("%d", endTime),
    }).Result()

    for _, postData := range posts {
        var schedule ScheduledPost
        json.Unmarshal([]byte(postData), &schedule)

        // Check if this post was already processed
        if !s.isAlreadyProcessed(schedule.ScheduleId) {
            go s.processScheduledPost(schedule)
            s.removeFromAllBuckets(schedule)
        }
    }
}

func (s *ScheduleProcessor) removeFromAllBuckets(schedule ScheduledPost) {
    scheduledTime := time.Unix(schedule.ScheduledTimestamp, 0)

    // Remove from minute bucket
    minuteKey := fmt.Sprintf("scheduled_posts:minute:%s", scheduledTime.Format("2006-01-02-15-04"))
    s.redis.ZRem(context.Background(), minuteKey, schedule.OriginalData)

    // Remove from hour bucket
    hourKey := fmt.Sprintf("scheduled_posts:hour:%s", scheduledTime.Format("2006-01-02-15"))
    s.redis.ZRem(context.Background(), hourKey, schedule.OriginalData)

    // Remove from day bucket
    dayKey := fmt.Sprintf("scheduled_posts:day:%s", scheduledTime.Format("2006-01-02"))
    s.redis.ZRem(context.Background(), dayKey, schedule.OriginalData)
}

// Process scheduled post by calling unified PublishPostNow flow
func (s *ScheduleProcessor) processScheduledPostViaPublishFlow(schedule ScheduledPost) {
    ctx := context.Background()

    log.Printf("Processing scheduled post %s for post %s", schedule.ScheduleId, schedule.PostId)

    // Call unified PublishPostNow flow
    publishResp, err := s.contentManagementClient.PublishPostNow(ctx, &PublishPostNowRequest{
        PostId:      schedule.PostId,
        UserId:      schedule.UserId,
        WorkspaceId: schedule.WorkspaceId,
        Platforms:   schedule.Platforms,
        PublishOptions: map[string]string{
            "source":      "scheduled",
            "schedule_id": schedule.ScheduleId,
        },
        NotifyTeam: true,
    })

    // Update schedule record based on publish results
    s.updateScheduleWithPublishResults(schedule, publishResp, err)
}

func (s *ScheduleProcessor) updateScheduleWithPublishResults(schedule ScheduledPost, publishResp *PublishPostNowResponse, publishErr error) {
    ctx := context.Background()

    // Determine overall status
    var status string
    var errorMessage string

    if publishErr != nil {
        status = "failed"
        errorMessage = publishErr.Error()
        log.Printf("Scheduled post %s failed: %v", schedule.ScheduleId, publishErr)
    } else if publishResp.Success && publishResp.OverallSuccess {
        status = "completed"
        log.Printf("Scheduled post %s completed successfully", schedule.ScheduleId)
    } else if publishResp.Success && !publishResp.OverallSuccess {
        status = "partial"
        log.Printf("Scheduled post %s completed with partial success", schedule.ScheduleId)
    } else {
        status = "failed"
        errorMessage = publishResp.ErrorMessage
        log.Printf("Scheduled post %s failed: %s", schedule.ScheduleId, publishResp.ErrorMessage)
    }

    // Update schedule record in database
    updates := map[string]interface{}{
        "status":      status,
        "executed_at": time.Now(),
        "updated_at":  time.Now(),
    }

    if publishResp != nil {
        updates["platform_results"] = publishResp.PlatformResults
        updates["successful_platforms"] = publishResp.SuccessfulPlatforms
        updates["failed_platforms"] = publishResp.FailedPlatforms
        updates["publish_id"] = publishResp.PublishId
    }

    if errorMessage != "" {
        updates["error_message"] = errorMessage
    }

    if err := s.db.Table("post_schedules").Where("schedule_id = ?", schedule.ScheduleId).Updates(updates).Error; err != nil {
        log.Printf("Failed to update schedule %s: %v", schedule.ScheduleId, err)
        return
    }

    // Publish appropriate event
    eventType := "ScheduledPostPublished"
    if status == "failed" {
        eventType = "ScheduledPostFailed"
    } else if status == "partial" {
        eventType = "ScheduledPostPartialSuccess"
    }

    eventData := map[string]interface{}{
        "schedule_id":          schedule.ScheduleId,
        "post_id":             schedule.PostId,
        "user_id":             schedule.UserId,
        "workspace_id":        schedule.WorkspaceId,
        "platforms":           schedule.Platforms,
        "status":              status,
        "executed_at":         time.Now(),
        "original_scheduled_time": time.Unix(schedule.ScheduledTimestamp, 0),
    }

    if publishResp != nil {
        eventData["publish_id"] = publishResp.PublishId
        eventData["platform_results"] = publishResp.PlatformResults
        eventData["successful_platforms"] = publishResp.SuccessfulPlatforms
        eventData["failed_platforms"] = publishResp.FailedPlatforms
        eventData["overall_success"] = publishResp.OverallSuccess
    }

    if errorMessage != "" {
        eventData["error_message"] = errorMessage
    }

    s.eventBus.Publish(eventType, eventData)

    // Handle retry logic for failed platforms
    if publishResp != nil && len(publishResp.FailedPlatforms) > 0 {
        s.scheduleRetryForFailedPlatforms(schedule, publishResp.FailedPlatforms, publishResp.PlatformResults)
    }
}

func (s *ScheduleProcessor) scheduleRetryForFailedPlatforms(originalSchedule ScheduledPost, failedPlatforms []string, platformResults []*PlatformPublishResult) {
    for _, platform := range failedPlatforms {
        // Find the error for this platform
        var retryDelay time.Duration = 5 * time.Minute // Default retry delay
        var errorType string

        for _, result := range platformResults {
            if result.Platform == platform && !result.Success {
                errorType = result.ErrorType

                // Determine retry delay based on error type
                switch errorType {
                case "rate_limit":
                    retryDelay = 15 * time.Minute
                case "network_error":
                    retryDelay = 2 * time.Minute
                case "auth_expired":
                    // Don't retry auth errors automatically
                    continue
                default:
                    retryDelay = 5 * time.Minute
                }
                break
            }
        }

        // Create retry schedule
        retryTime := time.Now().Add(retryDelay)
        retrySchedule := ScheduledPost{
            ScheduleId:         uuid.New().String(),
            PostId:            originalSchedule.PostId,
            UserId:            originalSchedule.UserId,
            WorkspaceId:       originalSchedule.WorkspaceId,
            Platforms:         []string{platform}, // Only retry this platform
            ScheduledTimestamp: retryTime.Unix(),
            RetryAttempt:      originalSchedule.RetryAttempt + 1,
            OriginalScheduleId: originalSchedule.ScheduleId,
            Status:            "retry_scheduled",
        }

        // Don't retry more than 3 times
        if retrySchedule.RetryAttempt <= 3 {
            s.addRetryToScheduler(retrySchedule)

            // Publish retry event
            s.eventBus.Publish("ScheduledPostRetry", map[string]interface{}{
                "original_schedule_id": originalSchedule.ScheduleId,
                "retry_schedule_id":    retrySchedule.ScheduleId,
                "platform":             platform,
                "retry_attempt":        retrySchedule.RetryAttempt,
                "retry_scheduled_for":  retryTime,
                "error_type":           errorType,
            })
        }
    }
}
```

### 3. **Schedule Post Update Flow:**

#### **Update Existing Schedule:**
```
1. Client → Content Management Service: UpdateSchedule(schedule_id, new_time, new_platforms)
2. Content Management → User Service: ValidateToken() + VerifyPermission()
3. Content Management → Database: Get existing schedule record
4. Content Management → Validation: Check if schedule is still pending/modifiable
5. Content Management → Redis: Remove old schedule from all buckets
6. Content Management → Database: Update schedule record với new details
7. Content Management → Redis: Add updated schedule to new time buckets
8. Content Management → Kafka: Publish ScheduleUpdated event
9. Content Management → Client: UpdateScheduleResponse(success, new_estimated_time)
```

#### **Cancel Schedule Flow:**
```
1. Client → Content Management Service: CancelSchedule(schedule_id, reason)
2. Content Management → User Service: ValidateToken() + VerifyPermission()
3. Content Management → Database: Get schedule record
4. Content Management → Validation: Check if schedule is cancellable
5. Content Management → Redis: Remove schedule from all buckets
6. Content Management → Database: Update schedule status='cancelled'
7. Content Management → Kafka: Publish ScheduleCancelled event
8. Content Management → Client: CancelScheduleResponse(success, message)
```

#### **Update Implementation:**
```go
func (s *ContentManagementService) UpdateSchedule(ctx context.Context, req *UpdateScheduleRequest) (*UpdateScheduleResponse, error) {
    // 1. Validate user permission
    userCtx := ctx.Value("user_context").(*UserContext)

    // 2. Get existing schedule
    var existingSchedule PostSchedule
    if err := s.db.Where("schedule_id = ? AND user_id = ?", req.ScheduleId, userCtx.UserId).First(&existingSchedule).Error; err != nil {
        return nil, status.Error(codes.NotFound, "schedule not found")
    }

    // 3. Check if schedule is modifiable
    if existingSchedule.Status != "pending" && existingSchedule.Status != "queued" {
        return nil, status.Error(codes.FailedPrecondition, "schedule cannot be modified")
    }

    // 4. Parse new schedule time if provided
    var newScheduleTime time.Time
    if req.NewScheduledTime != "" {
        loc, err := time.LoadLocation(req.NewTimezone)
        if err != nil {
            return nil, status.Error(codes.InvalidArgument, "invalid timezone")
        }

        parsedTime, err := time.Parse(time.RFC3339, req.NewScheduledTime)
        if err != nil {
            return nil, status.Error(codes.InvalidArgument, "invalid schedule time format")
        }

        newScheduleTime = parsedTime.In(loc).UTC()

        // Validate new time is in future
        if newScheduleTime.Before(time.Now().UTC()) {
            return nil, status.Error(codes.InvalidArgument, "new schedule time must be in future")
        }
    } else {
        newScheduleTime = existingSchedule.ScheduledTimeUTC
    }

    // 5. Remove old schedule from Redis
    s.removeFromScheduler(&existingSchedule)

    // 6. Update schedule record
    updates := map[string]interface{}{
        "updated_at": time.Now(),
    }

    if req.NewScheduledTime != "" {
        updates["scheduled_time_utc"] = newScheduleTime
        updates["original_timezone"] = req.NewTimezone
    }

    if len(req.NewPlatforms) > 0 {
        updates["platforms"] = req.NewPlatforms
    }

    if err := s.db.Model(&existingSchedule).Updates(updates).Error; err != nil {
        return nil, status.Error(codes.Internal, "failed to update schedule")
    }

    // 7. Add updated schedule to Redis
    updatedSchedule := existingSchedule
    updatedSchedule.ScheduledTimeUTC = newScheduleTime
    if len(req.NewPlatforms) > 0 {
        updatedSchedule.Platforms = req.NewPlatforms
    }

    s.addToScheduler(&updatedSchedule)

    // 8. Publish event
    s.eventBus.Publish("ScheduleUpdated", map[string]interface{}{
        "schedule_id":         req.ScheduleId,
        "user_id":            userCtx.UserId,
        "old_scheduled_time":  existingSchedule.ScheduledTimeUTC,
        "new_scheduled_time":  newScheduleTime,
        "old_platforms":       existingSchedule.Platforms,
        "new_platforms":       updatedSchedule.Platforms,
        "updated_at":         time.Now(),
    })

    return &UpdateScheduleResponse{
        Success: true,
        UpdatedSchedule: s.buildScheduleInfo(&updatedSchedule),
    }, nil
}
```

### 4. **Immediate Post Publishing Flow:**

#### **Publish Post Immediately:**
```
1. Client → Content Management Service: PublishPostNow(post_id, platforms, publish_options)
2. Content Management → User Service: ValidateToken() + VerifyPermission()
3. Content Management → Database: Get post content và validate ownership
4. Content Management → Integration Service: ValidatePlatformConnections(user_id, platforms)
5. Content Management → Database: Create immediate_publish record
6. Content Management → Integration Service: PublishToMultiplePlatforms(post_content, platforms)
7. Integration Service → Platform APIs: Parallel publishing calls (Facebook, Instagram, Twitter, etc.)
8. Integration Service → Content Management: PublishResults per platform
9. Content Management → Database: Update post status + platform results
10. Content Management → Kafka: Publish PostPublishedImmediately event
11. Content Management → Client: PublishResponse(success, platform_results, post_urls)
12. Notification Service → User: Send publish confirmation với platform links
```

#### **Immediate Publishing Implementation:**
```go
func (s *ContentManagementService) PublishPostNow(ctx context.Context, req *PublishPostNowRequest) (*PublishPostNowResponse, error) {
    // 1. Validate user permission
    userCtx := ctx.Value("user_context").(*UserContext)

    // 2. Get post content
    var post Post
    if err := s.db.Where("id = ? AND user_id = ?", req.PostId, userCtx.UserId).First(&post).Error; err != nil {
        return nil, status.Error(codes.NotFound, "post not found")
    }

    // 3. Validate post is publishable
    if post.Status == "published" {
        return nil, status.Error(codes.FailedPrecondition, "post already published")
    }

    // 4. Validate platform connections
    connectionsResp, err := s.integrationServiceClient.ValidatePlatformConnections(ctx, &ValidatePlatformConnectionsRequest{
        UserId:    userCtx.UserId,
        Platforms: req.Platforms,
    })

    if err != nil || !connectionsResp.AllValid {
        return nil, status.Error(codes.FailedPrecondition, "invalid platform connections")
    }

    // 5. Create immediate publish record
    publishRecord := &ImmediatePublish{
        PublishId:   uuid.New().String(),
        PostId:      req.PostId,
        UserId:      userCtx.UserId,
        WorkspaceId: post.WorkspaceId,
        Platforms:   req.Platforms,
        Status:      "publishing",
        CreatedAt:   time.Now(),
    }

    if err := s.db.Create(publishRecord).Error; err != nil {
        return nil, status.Error(codes.Internal, "failed to create publish record")
    }

    // 6. Prepare post content for publishing
    postContent := &PostContent{
        Title:       post.Title,
        Content:     post.Content,
        ImageUrls:   post.ImageUrls,
        VideoUrls:   post.VideoUrls,
        Hashtags:    post.Hashtags,
        PostType:    post.PostType,
        PublishOptions: req.PublishOptions,
    }

    // 7. Publish to platforms
    publishResp, err := s.integrationServiceClient.PublishToMultiplePlatforms(ctx, &PublishToMultiplePlatformsRequest{
        PublishId:   publishRecord.PublishId,
        PostContent: postContent,
        Platforms:   req.Platforms,
        UserId:      userCtx.UserId,
        WorkspaceId: post.WorkspaceId,
    })

    if err != nil {
        // Update publish record as failed
        s.db.Model(publishRecord).Updates(map[string]interface{}{
            "status":     "failed",
            "error":      err.Error(),
            "updated_at": time.Now(),
        })
        return nil, status.Error(codes.Internal, "failed to publish to platforms")
    }

    // 8. Update records with results
    tx := s.db.Begin()

    // Update post status
    postUpdates := map[string]interface{}{
        "status":       "published",
        "published_at": time.Now(),
        "updated_at":   time.Now(),
    }

    if err := tx.Model(&post).Updates(postUpdates).Error; err != nil {
        tx.Rollback()
        return nil, status.Error(codes.Internal, "failed to update post")
    }

    // Update publish record
    publishUpdates := map[string]interface{}{
        "status":            "completed",
        "platform_results":  publishResp.Results,
        "successful_platforms": publishResp.SuccessfulPlatforms,
        "failed_platforms":     publishResp.FailedPlatforms,
        "updated_at":        time.Now(),
    }

    if err := tx.Model(publishRecord).Updates(publishUpdates).Error; err != nil {
        tx.Rollback()
        return nil, status.Error(codes.Internal, "failed to update publish record")
    }

    tx.Commit()

    // 9. Publish appropriate event based on source
    eventType := "PostPublishedImmediately"
    eventData := map[string]interface{}{
        "publish_id":           publishRecord.PublishId,
        "post_id":             req.PostId,
        "user_id":             userCtx.UserId,
        "workspace_id":        post.WorkspaceId,
        "platforms":           req.Platforms,
        "platform_results":    publishResp.Results,
        "successful_platforms": publishResp.SuccessfulPlatforms,
        "failed_platforms":     publishResp.FailedPlatforms,
        "published_at":        time.Now(),
        "overall_success":     len(publishResp.FailedPlatforms) == 0,
    }

    // Check if this is called from scheduled flow
    if source, exists := req.PublishOptions["source"]; exists && source == "scheduled" {
        eventType = "ScheduledPostPublished"
        if scheduleId, exists := req.PublishOptions["schedule_id"]; exists {
            eventData["schedule_id"] = scheduleId
        }
    }

    s.eventBus.Publish(eventType, eventData)

    return &PublishPostNowResponse{
        Success:             true,
        PublishId:           publishRecord.PublishId,
        PlatformResults:     publishResp.Results,
        SuccessfulPlatforms: publishResp.SuccessfulPlatforms,
        FailedPlatforms:     publishResp.FailedPlatforms,
        OverallSuccess:      len(publishResp.FailedPlatforms) == 0,
        PublishedAt:         time.Now().Format(time.RFC3339),
    }, nil
}
```

### 5. **Unified Post Publishing Execution:**

#### **Schedule Post Flow → Publish Immediately Flow:**
```
1. Schedule Processor → Detects scheduled post ready for publishing
2. Schedule Processor → Content Management: PublishPostNow(post_id, platforms, options)
3. Content Management → Executes Publish Post Immediately Flow (steps 2-11 from section 4)
4. Content Management → Schedule Processor: PublishResponse(results)
5. Schedule Processor → Database: Update schedule status='completed' + results
6. Schedule Processor → Kafka: Publish ScheduledPostPublished event
7. Notification Service → User: Send scheduled publish confirmation
```

#### **Unified Publishing Flow (Used by Both Immediate & Scheduled):**
```
Core Publishing Logic (PublishPostNow):
1. Content Management → User Service: ValidateToken() + VerifyPermission()
2. Content Management → Database: Get post content và validate ownership
3. Content Management → Integration Service: ValidatePlatformConnections(user_id, platforms)
4. Content Management → Database: Create immediate_publish record
5. Content Management → Integration Service: PublishToMultiplePlatforms(post_content, platforms)
6. Integration Service → Platform APIs: Parallel publishing calls
7. Integration Service → Content Management: PublishResults per platform
8. Content Management → Database: Update post status + platform results
9. Content Management → Kafka: Publish appropriate event (PostPublishedImmediately or ScheduledPostPublished)
10. Content Management → Caller: Return PublishResponse(success, platform_results, post_urls)

This unified flow is reused by:
- Direct client calls (immediate publishing)
- Schedule Processor calls (scheduled publishing)
```

#### **Platform-Specific Publishing:**
```go
func (s *IntegrationService) PublishToMultiplePlatforms(ctx context.Context, req *PublishRequest) (*PublishResponse, error) {
    var wg sync.WaitGroup
    results := make(map[string]*PlatformResult)
    mu := sync.Mutex{}

    for _, platform := range req.Platforms {
        wg.Add(1)
        go func(p string) {
            defer wg.Done()

            result := s.publishToPlatform(ctx, p, req.PostContent, req.UserId)

            mu.Lock()
            results[p] = result
            mu.Unlock()
        }(platform)
    }

    wg.Wait()

    // Aggregate results
    response := &PublishResponse{
        ScheduleId: req.ScheduleId,
        Results:    results,
        PublishedAt: time.Now(),
    }

    return response, nil
}

func (s *IntegrationService) publishToPlatform(ctx context.Context, platform string, content *PostContent, userId string) *PlatformResult {
    switch platform {
    case "facebook":
        return s.publishToFacebook(ctx, content, userId)
    case "instagram":
        return s.publishToInstagram(ctx, content, userId)
    case "twitter":
        return s.publishToTwitter(ctx, content, userId)
    case "linkedin":
        return s.publishToLinkedIn(ctx, content, userId)
    default:
        return &PlatformResult{
            Platform: platform,
            Success:  false,
            Error:    "unsupported platform",
        }
    }
}
```

### 4. **Timezone Handling:**

#### **Multi-Timezone Support:**
```go
type ScheduleRequest struct {
    PostId       string    `json:"post_id"`
    ScheduleTime time.Time `json:"schedule_time"` // Client's local time
    Timezone     string    `json:"timezone"`      // e.g., "Asia/Ho_Chi_Minh"
    Platforms    []string  `json:"platforms"`
}

func (s *ContentManagementService) SchedulePost(ctx context.Context, req *ScheduleRequest) (*ScheduleResponse, error) {
    // Parse timezone
    loc, err := time.LoadLocation(req.Timezone)
    if err != nil {
        return nil, status.Error(codes.InvalidArgument, "invalid timezone")
    }

    // Convert to UTC for storage
    utcTime := req.ScheduleTime.In(loc).UTC()

    // Validate schedule time (must be in future)
    if utcTime.Before(time.Now().UTC()) {
        return nil, status.Error(codes.InvalidArgument, "schedule time must be in future")
    }

    // Create schedule record
    schedule := &PostSchedule{
        ScheduleId:      uuid.New().String(),
        PostId:          req.PostId,
        UserId:          getUserId(ctx),
        ScheduledTimeUTC: utcTime,
        OriginalTimezone: req.Timezone,
        Platforms:       req.Platforms,
        Status:          "pending",
        CreatedAt:       time.Now(),
    }

    // Save to database
    if err := s.db.Create(schedule).Error; err != nil {
        return nil, status.Error(codes.Internal, "failed to create schedule")
    }

    // Add to Redis scheduler với multiple granularity levels
    s.addToScheduler(schedule)

    return &ScheduleResponse{
        ScheduleId:           schedule.ScheduleId,
        EstimatedPublishTime: utcTime,
        Status:              "scheduled",
    }, nil
}

// Add to multiple Redis buckets for different granularity levels
func (s *ContentManagementService) addToScheduler(schedule *PostSchedule) error {
    scheduleData := map[string]interface{}{
        "schedule_id":         schedule.ScheduleId,
        "post_id":            schedule.PostId,
        "user_id":            schedule.UserId,
        "workspace_id":       schedule.WorkspaceId,
        "scheduled_timestamp": schedule.ScheduledTimeUTC.Unix(),
        "scheduled_time":     schedule.ScheduledTimeUTC.Format(time.RFC3339),
        "original_timezone":  schedule.OriginalTimezone,
        "platforms":          schedule.Platforms,
        "status":             schedule.Status,
        "retry_attempt":      schedule.RetryAttempt,
    }

    scheduleJSON, err := json.Marshal(scheduleData)
    if err != nil {
        return err
    }

    timestamp := schedule.ScheduledTimeUTC.Unix()

    // Add to minute-level bucket (primary)
    minuteKey := fmt.Sprintf("scheduled_posts:minute:%s",
        schedule.ScheduledTimeUTC.Format("2006-01-02-15-04"))
    s.redis.ZAdd(context.Background(), minuteKey, &redis.Z{
        Score:  float64(timestamp),
        Member: string(scheduleJSON),
    })

    // Add to hour-level bucket (secondary)
    hourKey := fmt.Sprintf("scheduled_posts:hour:%s",
        schedule.ScheduledTimeUTC.Format("2006-01-02-15"))
    s.redis.ZAdd(context.Background(), hourKey, &redis.Z{
        Score:  float64(timestamp),
        Member: string(scheduleJSON),
    })

    // Add to day-level bucket (tertiary)
    dayKey := fmt.Sprintf("scheduled_posts:day:%s",
        schedule.ScheduledTimeUTC.Format("2006-01-02"))
    s.redis.ZAdd(context.Background(), dayKey, &redis.Z{
        Score:  float64(timestamp),
        Member: string(scheduleJSON),
    })

    // Set expiration for cleanup (7 days after scheduled time)
    expiration := 7 * 24 * time.Hour
    s.redis.Expire(context.Background(), minuteKey, expiration)
    s.redis.Expire(context.Background(), hourKey, expiration)
    s.redis.Expire(context.Background(), dayKey, expiration)

    return nil
}
```

### 5. **Failure Handling & Retry Logic:**

#### **Retry Strategy:**
```yaml
retry_policy:
  max_attempts: 3
  backoff_strategy: exponential
  base_delay: 2m
  max_delay: 30m

  platform_specific:
    facebook:
      rate_limit_retry: true
      retry_delays: [5m, 15m, 60m]

    instagram:
      rate_limit_retry: true
      retry_delays: [10m, 30m, 120m]

    twitter:
      rate_limit_retry: true
      retry_delays: [15m, 60m, 240m]
```

#### **Failure Recovery:**
```go
func (s *ScheduleProcessor) handlePublishFailure(schedule *ScheduledPost, platformResults map[string]*PlatformResult) {
    for platform, result := range platformResults {
        if !result.Success {
            switch result.ErrorType {
            case "rate_limit":
                // Reschedule with delay
                s.rescheduleWithDelay(schedule, platform, result.RetryAfter)

            case "auth_expired":
                // Notify user to reconnect account
                s.notifyAuthExpired(schedule.UserId, platform)

            case "content_violation":
                // Mark as failed, notify user
                s.markAsFailed(schedule, platform, "content_violation")

            case "network_error":
                // Retry with exponential backoff
                s.scheduleRetry(schedule, platform)

            default:
                // Log error and mark as failed
                s.markAsFailed(schedule, platform, result.Error)
            }
        }
    }
}

func (s *ScheduleProcessor) rescheduleWithDelay(schedule *ScheduledPost, platform string, delay time.Duration) {
    newScheduleTime := time.Now().Add(delay)

    // Create new schedule record for retry
    retrySchedule := &ScheduledPost{
        ScheduleId:    uuid.New().String(),
        OriginalId:    schedule.ScheduleId,
        PostId:        schedule.PostId,
        UserId:        schedule.UserId,
        Platforms:     []string{platform}, // Only retry failed platform
        ScheduledTime: newScheduleTime,
        RetryAttempt:  schedule.RetryAttempt + 1,
        Status:        "retry_scheduled",
    }

    // Add to Redis scheduler
    s.addToScheduler(retrySchedule)

    // Publish retry event
    s.eventBus.Publish("PostScheduleRetry", map[string]interface{}{
        "original_schedule_id": schedule.ScheduleId,
        "retry_schedule_id":    retrySchedule.ScheduleId,
        "platform":             platform,
        "retry_attempt":        retrySchedule.RetryAttempt,
        "scheduled_for":        newScheduleTime,
    })
}
```

### 6. **Kafka Events for Scheduling:**

#### **Schedule-Related Events:**
```json
{
  "eventType": "PostScheduled",
  "data": {
    "scheduleId": "sch_123",
    "postId": "post_456",
    "userId": "user_789",
    "workspaceId": "ws_101",
    "scheduledTimeUTC": "2024-02-15T14:30:00Z",
    "originalTimezone": "Asia/Ho_Chi_Minh",
    "platforms": ["facebook", "instagram"],
    "estimatedPublishTime": "2024-02-15T14:30:00Z"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "post_scheduled_confirmation"
  }
}

{
  "eventType": "PostPublishCompleted",
  "data": {
    "scheduleId": "sch_123",
    "postId": "post_456",
    "userId": "user_789",
    "publishedAt": "2024-02-15T14:30:15Z",
    "platformResults": {
      "facebook": {
        "success": true,
        "postUrl": "https://facebook.com/posts/123456",
        "publishedAt": "2024-02-15T14:30:12Z"
      },
      "instagram": {
        "success": false,
        "error": "rate_limit_exceeded",
        "retryAfter": "2024-02-15T15:00:00Z"
      }
    },
    "overallSuccess": false,
    "successfulPlatforms": ["facebook"],
    "failedPlatforms": ["instagram"]
  },
  "notificationConfig": {
    "channels": ["push", "email"],
    "priority": "high",
    "template": "post_publish_results"
  }
}

{
  "eventType": "PostScheduleRetry",
  "data": {
    "originalScheduleId": "sch_123",
    "retryScheduleId": "sch_124",
    "postId": "post_456",
    "userId": "user_789",
    "platform": "instagram",
    "retryAttempt": 2,
    "scheduledFor": "2024-02-15T15:00:00Z",
    "reason": "rate_limit_exceeded"
  },
  "notificationConfig": {
    "channels": ["push"],
    "priority": "normal",
    "template": "post_retry_scheduled"
  }
}
```

### 7. **Database Schema for Scheduling:**

```sql
-- Post Schedules Table
CREATE TABLE post_schedules (
  schedule_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL REFERENCES posts(id),
  user_id UUID NOT NULL,
  workspace_id UUID,
  scheduled_time_utc TIMESTAMPTZ NOT NULL,
  original_timezone VARCHAR(50) NOT NULL,
  platforms TEXT[] NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- pending, queued, processing, completed, failed, cancelled
  retry_attempt INTEGER DEFAULT 0,
  original_schedule_id UUID, -- For retry records
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_scheduled_time (scheduled_time_utc),
  INDEX idx_user_schedules (user_id, scheduled_time_utc),
  INDEX idx_status_time (status, scheduled_time_utc)
);

-- Platform Publish Results Table
CREATE TABLE platform_publish_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_id UUID NOT NULL REFERENCES post_schedules(schedule_id),
  platform VARCHAR(20) NOT NULL,
  success BOOLEAN NOT NULL,
  platform_post_id VARCHAR(255), -- ID from platform (Facebook post ID, etc.)
  platform_post_url TEXT,
  error_message TEXT,
  error_type VARCHAR(50), -- rate_limit, auth_expired, content_violation, network_error
  published_at TIMESTAMPTZ,
  retry_after TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Schedule Analytics Table
CREATE TABLE schedule_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  date DATE NOT NULL,
  total_scheduled INTEGER DEFAULT 0,
  successful_publishes INTEGER DEFAULT 0,
  failed_publishes INTEGER DEFAULT 0,
  platform_breakdown JSONB, -- {"facebook": {"success": 5, "failed": 1}, ...}
  created_at TIMESTAMPTZ DEFAULT now(),

  UNIQUE(user_id, date)
);

-- Immediate Publishing Table
CREATE TABLE immediate_publishes (
  publish_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL REFERENCES posts(id),
  user_id UUID NOT NULL,
  workspace_id UUID,
  platforms TEXT[] NOT NULL,
  status VARCHAR(20) DEFAULT 'publishing', -- publishing, completed, failed, partial
  platform_results JSONB, -- Results from each platform
  successful_platforms TEXT[],
  failed_platforms TEXT[],
  overall_success BOOLEAN,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_publishes (user_id, created_at),
  INDEX idx_post_publishes (post_id),
  INDEX idx_status_publishes (status, created_at)
);

-- Schedule Change History Table
CREATE TABLE schedule_change_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_id UUID NOT NULL REFERENCES post_schedules(schedule_id),
  change_type VARCHAR(20) NOT NULL, -- 'created', 'updated', 'cancelled'
  old_values JSONB, -- Previous values
  new_values JSONB, -- New values
  change_reason TEXT,
  changed_by UUID NOT NULL, -- User who made the change
  changed_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_schedule_history (schedule_id, changed_at),
  INDEX idx_change_type (change_type, changed_at)
);

-- Publishing Retry Table
CREATE TABLE publish_retries (
  retry_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  original_publish_id UUID NOT NULL REFERENCES immediate_publishes(publish_id),
  retry_publish_id UUID NOT NULL REFERENCES immediate_publishes(publish_id),
  retry_platforms TEXT[] NOT NULL,
  retry_attempt INTEGER NOT NULL,
  retry_reason VARCHAR(50), -- 'rate_limit', 'network_error', 'manual'
  scheduled_for TIMESTAMPTZ,
  status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_original_publish (original_publish_id),
  INDEX idx_retry_schedule (scheduled_for, status)
);

-- Bank Transfer Tables
CREATE TABLE bank_transfers (
  transfer_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  plan_id UUID, -- Optional: if purchasing a subscription
  amount BIGINT NOT NULL, -- Amount in smallest currency unit (VND)
  currency VARCHAR(3) DEFAULT 'VND',
  purpose VARCHAR(20) NOT NULL, -- 'subscription', 'credit_topup'
  reference_code VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- pending, confirmed, expired, cancelled
  bank_instructions JSONB NOT NULL, -- Bank transfer instructions
  user_info JSONB NOT NULL, -- User information for transfer
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_transfers (user_id, created_at),
  INDEX idx_reference_code (reference_code),
  INDEX idx_status_expiry (status, expires_at)
);

-- Bank Transfer Confirmations
CREATE TABLE bank_transfer_confirmations (
  confirmation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transfer_id UUID NOT NULL REFERENCES bank_transfers(transfer_id),
  bank_transaction_id VARCHAR(100), -- Bank's transaction ID
  actual_amount_received BIGINT NOT NULL,
  confirmed_at TIMESTAMPTZ DEFAULT now(),
  confirmed_by UUID, -- Admin user ID (for manual confirmations)
  confirmation_method VARCHAR(20) DEFAULT 'manual', -- 'manual', 'auto_webhook'
  confirmation_notes TEXT,
  proof_images TEXT[], -- URLs to bank statement images
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_transfer_confirmation (transfer_id),
  INDEX idx_bank_transaction (bank_transaction_id)
);

-- Subscription Plans
CREATE TABLE subscription_plans (
  plan_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL, -- 'Free', 'Pro', 'Premium', 'Enterprise'
  description TEXT,
  price BIGINT NOT NULL, -- Price in smallest currency unit
  currency VARCHAR(3) DEFAULT 'VND',
  billing_cycle VARCHAR(20) NOT NULL, -- 'monthly', 'yearly'
  credits_included INTEGER DEFAULT 0,
  features JSONB, -- Array of feature names
  limits JSONB, -- Plan limits (workspaces, team members, etc.)
  is_active BOOLEAN DEFAULT true,
  is_popular BOOLEAN DEFAULT false,
  discount_percentage INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_active_plans (is_active, billing_cycle)
);

-- User Subscriptions
CREATE TABLE user_subscriptions (
  subscription_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  plan_id UUID NOT NULL REFERENCES subscription_plans(plan_id),
  status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, pending
  starts_at TIMESTAMPTZ NOT NULL,
  ends_at TIMESTAMPTZ NOT NULL,
  next_billing_date TIMESTAMPTZ,
  amount_paid BIGINT NOT NULL,
  currency VARCHAR(3) DEFAULT 'VND',
  payment_method VARCHAR(20), -- 'bank_transfer', 'stripe', 'paypal'
  payment_id VARCHAR(100), -- Reference to payment record
  auto_renew BOOLEAN DEFAULT false,
  cancelled_at TIMESTAMPTZ,
  cancellation_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_subscriptions (user_id, status, ends_at),
  INDEX idx_billing_date (next_billing_date, status),
  INDEX idx_payment_reference (payment_method, payment_id)
);

-- Enhanced Credit Transactions
CREATE TABLE credit_transactions (
  transaction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  transaction_type VARCHAR(20) NOT NULL, -- 'add', 'consume', 'reserve', 'refund'
  amount INTEGER NOT NULL, -- Positive for add/refund, negative for consume
  balance_before INTEGER NOT NULL,
  balance_after INTEGER NOT NULL,
  source VARCHAR(30) NOT NULL, -- 'subscription', 'bank_transfer', 'ai_generation', 'template_purchase'
  reference_id VARCHAR(100), -- Payment ID, operation ID, etc.
  description TEXT,
  metadata JSONB, -- Additional transaction details
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_transactions (user_id, created_at),
  INDEX idx_transaction_type (transaction_type, created_at),
  INDEX idx_reference (source, reference_id)
);

-- Payment Processing Log
CREATE TABLE payment_processing_log (
  log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transfer_id UUID REFERENCES bank_transfers(transfer_id),
  subscription_id UUID REFERENCES user_subscriptions(subscription_id),
  processing_step VARCHAR(50) NOT NULL, -- 'initiated', 'confirmed', 'subscription_activated', 'credits_added'
  status VARCHAR(20) NOT NULL, -- 'success', 'failed', 'pending'
  error_message TEXT,
  processing_data JSONB, -- Step-specific data
  processed_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_transfer_log (transfer_id, processed_at),
  INDEX idx_processing_step (processing_step, status)
);

-- Data Backup Tables
CREATE TABLE data_backups (
  backup_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  backup_reason VARCHAR(50) NOT NULL, -- 'user_request', 'account_deletion', 'compliance'
  status VARCHAR(20) DEFAULT 'queued', -- queued, processing, completed, failed, expired
  backup_options JSONB NOT NULL, -- Backup configuration options
  file_size_bytes BIGINT,
  s3_bucket VARCHAR(100),
  s3_key VARCHAR(500),
  download_url TEXT, -- Presigned S3 URL
  expires_at TIMESTAMPTZ, -- Download link expiry (24h)
  backup_stats JSONB, -- Statistics about backed up data
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,

  INDEX idx_user_backups (user_id, created_at),
  INDEX idx_status_expiry (status, expires_at),
  INDEX idx_backup_reason (backup_reason, created_at)
);

-- Data Backup Downloads Tracking
CREATE TABLE backup_downloads (
  download_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_id UUID NOT NULL REFERENCES data_backups(backup_id),
  user_id UUID NOT NULL,
  download_ip INET,
  user_agent TEXT,
  downloaded_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_backup_downloads (backup_id, downloaded_at),
  INDEX idx_user_downloads (user_id, downloaded_at)
);

-- Account Deletion Requests
CREATE TABLE account_deletion_requests (
  deletion_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  deletion_reason VARCHAR(100) NOT NULL,
  additional_notes TEXT,
  status VARCHAR(20) DEFAULT 'pending', -- pending, grace_period, processing, completed, cancelled
  request_data_backup BOOLEAN DEFAULT false,
  backup_id UUID REFERENCES data_backups(backup_id),
  grace_period_ends TIMESTAMPTZ,
  scheduled_deletion_at TIMESTAMPTZ,
  estimated_data JSONB, -- Statistics about data to be deleted
  actual_deleted_data JSONB, -- Statistics about actually deleted data
  cancellation_reason TEXT,
  cancelled_at TIMESTAMPTZ,
  executed_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_deletions (user_id, created_at),
  INDEX idx_status_schedule (status, scheduled_deletion_at),
  INDEX idx_grace_period (grace_period_ends, status)
);

-- Service Deletion Results
CREATE TABLE service_deletion_results (
  result_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deletion_id UUID NOT NULL REFERENCES account_deletion_requests(deletion_id),
  service_name VARCHAR(50) NOT NULL,
  success BOOLEAN NOT NULL,
  records_deleted INTEGER DEFAULT 0,
  storage_freed_bytes BIGINT DEFAULT 0,
  error_message TEXT,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_deletion_results (deletion_id, service_name),
  INDEX idx_service_performance (service_name, completed_at)
);

-- Data Retention Policy
CREATE TABLE data_retention_policies (
  policy_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  data_type VARCHAR(50) NOT NULL, -- 'user_data', 'backups', 'logs', 'analytics'
  retention_period_days INTEGER NOT NULL,
  auto_cleanup BOOLEAN DEFAULT true,
  policy_description TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  UNIQUE(data_type)
);

-- GDPR Compliance Log
CREATE TABLE gdpr_compliance_log (
  log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  action_type VARCHAR(50) NOT NULL, -- 'data_export', 'data_deletion', 'consent_given', 'consent_withdrawn'
  action_details JSONB,
  ip_address INET,
  user_agent TEXT,
  compliance_officer UUID, -- Admin user who performed action
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_compliance (user_id, created_at),
  INDEX idx_action_type (action_type, created_at),
  INDEX idx_compliance_officer (compliance_officer, created_at)
);

-- Platform Integration Tables
CREATE TABLE platform_connections (
  connection_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  workspace_id UUID,
  platform VARCHAR(20) NOT NULL, -- 'facebook', 'instagram', 'twitter', 'youtube', 'tiktok'
  platform_user_id VARCHAR(100) NOT NULL,
  platform_username VARCHAR(100),
  platform_display_name VARCHAR(200),
  profile_image_url TEXT,
  status VARCHAR(20) DEFAULT 'active', -- active, expired, revoked, error, disconnected
  access_token_encrypted TEXT NOT NULL, -- Encrypted access token
  refresh_token_encrypted TEXT, -- Encrypted refresh token
  token_expires_at TIMESTAMPTZ,
  granted_scopes TEXT[], -- Array of granted permissions
  capabilities JSONB, -- Platform capabilities and limits
  last_used_at TIMESTAMPTZ,
  last_token_refresh TIMESTAMPTZ,
  connection_metadata JSONB, -- Platform-specific metadata
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_platforms (user_id, platform),
  INDEX idx_platform_user (platform, platform_user_id),
  INDEX idx_status_expiry (status, token_expires_at),
  INDEX idx_workspace_connections (workspace_id, platform),
  UNIQUE(user_id, platform, platform_user_id)
);

-- Platform Publishing History
CREATE TABLE platform_publishes (
  publish_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  workspace_id UUID,
  post_id UUID, -- Reference to original post
  platforms TEXT[] NOT NULL, -- Platforms published to
  publish_type VARCHAR(20) DEFAULT 'immediate', -- immediate, scheduled
  scheduled_for TIMESTAMPTZ,
  status VARCHAR(20) DEFAULT 'processing', -- processing, completed, failed, partial
  platform_results JSONB, -- Results per platform
  successful_platforms TEXT[],
  failed_platforms TEXT[],
  overall_success BOOLEAN,
  error_message TEXT,
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_publishes (user_id, created_at),
  INDEX idx_post_publishes (post_id),
  INDEX idx_platform_publishes (platforms, created_at),
  INDEX idx_status_publishes (status, created_at)
);

-- Platform-Specific Post Records
CREATE TABLE platform_posts (
  platform_post_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  publish_id UUID NOT NULL REFERENCES platform_publishes(publish_id),
  user_id UUID NOT NULL,
  platform VARCHAR(20) NOT NULL,
  platform_specific_id VARCHAR(200), -- Platform's post ID
  platform_post_url TEXT,
  post_content JSONB, -- Adapted content for this platform
  publish_status VARCHAR(20) DEFAULT 'published', -- published, failed, moderated, deleted
  platform_metadata JSONB, -- Platform-specific data
  analytics_data JSONB, -- Cached analytics data
  last_analytics_sync TIMESTAMPTZ,
  moderation_status VARCHAR(20), -- approved, flagged, removed
  moderation_details JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_platform_posts (platform, platform_specific_id),
  INDEX idx_user_platform_posts (user_id, platform, created_at),
  INDEX idx_publish_platform_posts (publish_id, platform),
  INDEX idx_analytics_sync (last_analytics_sync, platform)
);

-- Platform Analytics Data
CREATE TABLE platform_analytics (
  analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform_post_id UUID NOT NULL REFERENCES platform_posts(platform_post_id),
  user_id UUID NOT NULL,
  platform VARCHAR(20) NOT NULL,
  metric_date DATE NOT NULL,
  metrics JSONB NOT NULL, -- Platform-specific metrics
  engagement_rate DECIMAL(5,4),
  reach INTEGER,
  impressions INTEGER,
  clicks INTEGER,
  likes INTEGER,
  shares INTEGER,
  comments INTEGER,
  saves INTEGER, -- For platforms that support saves
  video_views INTEGER, -- For video content
  video_completion_rate DECIMAL(5,4), -- For video content
  synced_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_platform_analytics (platform, metric_date),
  INDEX idx_user_analytics (user_id, metric_date),
  INDEX idx_post_analytics (platform_post_id, metric_date),
  UNIQUE(platform_post_id, metric_date)
);

-- Platform Rate Limits Tracking
CREATE TABLE platform_rate_limits (
  rate_limit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  platform VARCHAR(20) NOT NULL,
  limit_type VARCHAR(50) NOT NULL, -- 'hourly_posts', 'daily_uploads', 'api_calls'
  current_usage INTEGER DEFAULT 0,
  limit_value INTEGER NOT NULL,
  reset_time TIMESTAMPTZ NOT NULL,
  last_hit_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_rate_limits (user_id, platform),
  INDEX idx_platform_limits (platform, limit_type),
  INDEX idx_reset_time (reset_time, platform),
  UNIQUE(user_id, platform, limit_type)
);

-- Platform Connection Audit Log
CREATE TABLE platform_connection_audit (
  audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  connection_id UUID REFERENCES platform_connections(connection_id),
  user_id UUID NOT NULL,
  platform VARCHAR(20) NOT NULL,
  action VARCHAR(50) NOT NULL, -- 'connected', 'disconnected', 'token_refreshed', 'token_expired'
  action_details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_connection_audit (connection_id, created_at),
  INDEX idx_user_platform_audit (user_id, platform, created_at),
  INDEX idx_action_audit (action, created_at)
);

-- Platform Error Log
CREATE TABLE platform_errors (
  error_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  platform VARCHAR(20) NOT NULL,
  operation VARCHAR(50) NOT NULL, -- 'publish', 'analytics_sync', 'token_refresh'
  error_type VARCHAR(50) NOT NULL, -- 'rate_limit', 'auth_error', 'api_error', 'network_error'
  error_message TEXT,
  error_details JSONB,
  retry_count INTEGER DEFAULT 0,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),

  INDEX idx_user_errors (user_id, created_at),
  INDEX idx_platform_errors (platform, error_type, created_at),
  INDEX idx_unresolved_errors (resolved, created_at)
);
```

### 8. **Performance & Monitoring:**

#### **Schedule Processing Configuration:**
```yaml
schedule_processor:
  # High-precision scheduling
  processing_interval: "30s"  # Run every 30 seconds
  time_window_buffer: "90s"   # Process posts within 90 seconds window
  precision_tolerance: "30s"  # Accept posts within ±30 seconds

  # Parallel processing
  max_concurrent_posts: 100
  platform_timeout: "30s"
  retry_delay: "2m"

  # Cron expression for Kubernetes CronJob
  cron_schedule: "*/30 * * * * *"  # Every 30 seconds

redis_configuration:
  # Separate Redis instances for different purposes
  scheduler_redis:
    instance: "redis-scheduler"
    memory: "4GB"
    persistence: "AOF" # Append-only file for durability

  cache_redis:
    instance: "redis-cache"
    memory: "2GB"
    persistence: "RDB" # Snapshot for cache

  # Multi-granularity partitioning strategy
  partitioning:
    by_minute: true    # Primary: minute-level precision
    by_hour: true      # Secondary: hour-level fallback
    by_day: true       # Tertiary: day-level overview
    by_timezone: false # Keep all timezones in same instance

  # Cleanup policies
  cleanup:
    minute_buckets: "2 hours"     # Clean minute buckets after 2 hours
    hour_buckets: "48 hours"      # Clean hour buckets after 2 days
    day_buckets: "7 days"         # Clean day buckets after 7 days
    expired_schedules: "7 days"   # Clean expired schedules
    completed_schedules: "30 days" # Archive completed schedules
```

#### **Monitoring Metrics:**
```yaml
schedule_metrics:
  # Performance metrics
  - schedule_processing_time_seconds
  - schedule_queue_size
  - redis_memory_usage_bytes
  - failed_schedule_rate

  # Business metrics
  - posts_scheduled_total
  - posts_published_total
  - platform_success_rate_by_platform
  - average_schedule_delay_seconds

  # Error metrics
  - platform_api_errors_total
  - auth_expired_errors_total
  - rate_limit_errors_total
  - retry_attempts_total
```

#### **Alerting Rules:**
```yaml
alerts:
  - name: HighScheduleFailureRate
    condition: failed_schedule_rate > 0.1
    duration: 5m
    severity: warning

  - name: ScheduleProcessingDelay
    condition: schedule_queue_size > 1000
    duration: 2m
    severity: critical

  - name: PlatformAPIDown
    condition: platform_success_rate_by_platform < 0.5
    duration: 5m
    severity: critical

  - name: RedisMemoryHigh
    condition: redis_memory_usage_bytes > 3GB
    duration: 1m
    severity: warning
```

### 9. **Scalability Considerations:**

#### **Horizontal Scaling:**
```yaml
scaling_strategy:
  schedule_processor:
    min_replicas: 2
    max_replicas: 10
    target_cpu: 70%
    target_memory: 80%

  integration_service:
    min_replicas: 3
    max_replicas: 20
    target_cpu: 60%
    custom_metrics:
      - platform_api_requests_per_second

  content_management:
    min_replicas: 2
    max_replicas: 15
    target_cpu: 70%
```

#### **Load Distribution:**
```go
// Distribute processing across multiple instances
func (s *ScheduleProcessor) getProcessorInstance(scheduleId string) string {
    // Use consistent hashing to distribute load
    hash := crc32.ChecksumIEEE([]byte(scheduleId))
    instanceCount := s.config.ProcessorInstances
    return fmt.Sprintf("processor-%d", hash%uint32(instanceCount))
}

// Redis key partitioning
func (s *ScheduleProcessor) getRedisKey(date time.Time, partition int) string {
    dateStr := date.Format("2006-01-02")
    return fmt.Sprintf("scheduled_posts:%s:p%d", dateStr, partition)
}
```

### 10. **Testing Strategy:**

#### **Unit Tests:**
```go
func TestSchedulePost(t *testing.T) {
    tests := []struct {
        name           string
        request        *SchedulePostRequest
        expectedError  error
        expectedStatus string
    }{
        {
            name: "valid_schedule",
            request: &SchedulePostRequest{
                PostId:        "post_123",
                UserId:        "user_456",
                ScheduledTime: time.Now().Add(1 * time.Hour).Format(time.RFC3339),
                Timezone:      "Asia/Ho_Chi_Minh",
                Platforms:     []string{"facebook", "instagram"},
            },
            expectedError:  nil,
            expectedStatus: "scheduled",
        },
        {
            name: "past_schedule_time",
            request: &SchedulePostRequest{
                PostId:        "post_123",
                UserId:        "user_456",
                ScheduledTime: time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
                Timezone:      "Asia/Ho_Chi_Minh",
                Platforms:     []string{"facebook"},
            },
            expectedError: status.Error(codes.InvalidArgument, "schedule time must be in future"),
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            response, err := scheduleService.SchedulePost(context.Background(), tt.request)

            if tt.expectedError != nil {
                assert.Error(t, err)
                assert.Equal(t, tt.expectedError.Error(), err.Error())
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expectedStatus, response.Status)
            }
        })
    }
}
```

#### **Integration Tests:**
```go
func TestSchedulePostIntegration(t *testing.T) {
    // Setup test environment
    testDB := setupTestDatabase()
    testRedis := setupTestRedis()
    testKafka := setupTestKafka()

    // Create test post
    post := createTestPost(testDB)

    // Schedule post
    scheduleReq := &SchedulePostRequest{
        PostId:        post.ID,
        UserId:        post.UserID,
        ScheduledTime: time.Now().Add(5 * time.Minute).Format(time.RFC3339),
        Timezone:      "UTC",
        Platforms:     []string{"facebook"},
    }

    response, err := scheduleService.SchedulePost(context.Background(), scheduleReq)
    assert.NoError(t, err)
    assert.True(t, response.Success)

    // Verify Redis entry
    exists := testRedis.ZScore(context.Background(), "scheduled_posts:"+time.Now().Format("2006-01-02"), response.ScheduleId)
    assert.NoError(t, exists.Err())

    // Verify Kafka event
    event := waitForKafkaEvent(testKafka, "content.events", 5*time.Second)
    assert.Equal(t, "PostScheduled", event.EventType)

    // Cleanup
    cleanupTestData(testDB, testRedis)
}
```

### 11. **Disaster Recovery:**

#### **Backup Strategy:**
```yaml
backup_strategy:
  redis_scheduler:
    backup_frequency: "every 1 hour"
    retention: "7 days"
    backup_location: "s3://socialai-backups/redis-scheduler/"

  database_schedules:
    backup_frequency: "every 6 hours"
    retention: "30 days"
    backup_location: "s3://socialai-backups/postgres-schedules/"

  recovery_procedures:
    redis_failure:
      - restore_from_latest_backup
      - rebuild_from_database_schedules
      - resume_processing

    database_failure:
      - failover_to_read_replica
      - restore_from_backup
      - verify_data_integrity
```

#### **Circuit Breaker Pattern:**
```go
type PlatformCircuitBreaker struct {
    breakers map[string]*breaker.CircuitBreaker
    mu       sync.RWMutex
}

func (p *PlatformCircuitBreaker) PublishWithCircuitBreaker(platform string, publishFunc func() error) error {
    p.mu.RLock()
    cb, exists := p.breakers[platform]
    p.mu.RUnlock()

    if !exists {
        p.mu.Lock()
        cb = breaker.New(3, 1, 30*time.Second) // 3 failures, 1 success to close, 30s timeout
        p.breakers[platform] = cb
        p.mu.Unlock()
    }

    result, err := cb.Execute(publishFunc)
    if err != nil {
        return fmt.Errorf("circuit breaker open for %s: %w", platform, err)
    }

    return result.(error)
}
```

### 12. **Kubernetes Deployment cho Schedule Processor:**

#### **CronJob Configuration:**
```yaml
# k8s/schedule-processor-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: schedule-processor
  namespace: socialai-prod
spec:
  # Run every 30 seconds (using multiple jobs)
  schedule: "*/30 * * * * *"
  concurrencyPolicy: Allow
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: schedule-processor
            image: socialai/schedule-processor:latest
            env:
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: url
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: url
            - name: KAFKA_BROKERS
              value: "kafka-service:9092"
            resources:
              requests:
                memory: "256Mi"
                cpu: "250m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          restartPolicy: OnFailure

---
# Alternative: Deployment with internal cron
apiVersion: apps/v1
kind: Deployment
metadata:
  name: schedule-processor-daemon
  namespace: socialai-prod
spec:
  replicas: 2  # For high availability
  selector:
    matchLabels:
      app: schedule-processor-daemon
  template:
    metadata:
      labels:
        app: schedule-processor-daemon
    spec:
      containers:
      - name: schedule-processor
        image: socialai/schedule-processor-daemon:latest
        env:
        - name: PROCESSING_INTERVAL
          value: "30s"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### **Schedule Processor Daemon Implementation:**
```go
// Main daemon process
func main() {
    processor := NewScheduleProcessor()

    // Start HTTP server for health checks
    go func() {
        http.HandleFunc("/health", processor.HealthCheck)
        http.HandleFunc("/ready", processor.ReadinessCheck)
        http.HandleFunc("/metrics", processor.MetricsHandler)
        log.Fatal(http.ListenAndServe(":8080", nil))
    }()

    // Start main processing loop
    processor.Start()
}

func (s *ScheduleProcessor) Start() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    log.Println("Schedule processor started, running every 30 seconds")

    for {
        select {
        case <-ticker.C:
            start := time.Now()
            s.ProcessScheduledPosts()
            duration := time.Since(start)

            // Record metrics
            s.recordProcessingMetrics(duration)

            log.Printf("Processing cycle completed in %v", duration)

        case <-s.shutdownChan:
            log.Println("Schedule processor shutting down...")
            return
        }
    }
}

func (s *ScheduleProcessor) recordProcessingMetrics(duration time.Duration) {
    // Prometheus metrics
    processingDuration.Observe(duration.Seconds())
    processingCycles.Inc()

    if duration > 25*time.Second {
        log.Printf("WARNING: Processing took %v, approaching 30s limit", duration)
        slowProcessingCycles.Inc()
    }
}
```

#### **High Availability Setup:**
```yaml
# Multiple instances with leader election
schedule_processor_ha:
  replicas: 3
  leader_election:
    enabled: true
    lease_duration: "15s"
    renew_deadline: "10s"
    retry_period: "2s"

  # Only leader processes schedules
  processing_strategy: "leader_only"

  # Fallback: if leader fails, others take over within 15s
  failover_timeout: "15s"
```

---

*Tài liệu này sẽ được cập nhật theo tiến độ implementation và feedback từ team.*

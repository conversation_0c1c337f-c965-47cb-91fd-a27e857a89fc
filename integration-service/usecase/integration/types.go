package integration

// Request/Response types

// GetOAuthURLRequest represents a request to get OAuth authorization URL
type GetOAuthURLRequest struct {
	UserID               string   `json:"user_id" validate:"required"`
	Platform             string   `json:"platform" validate:"required"`
	RedirectURI          string   `json:"redirect_uri" validate:"required,url"`
	RequestedPermissions []string `json:"requested_permissions"`
	State                string   `json:"state"`
}

// GetOAuthURLResponse represents the response with OAuth authorization URL
type GetOAuthURLResponse struct {
	AuthURL     string   `json:"auth_url"`
	State       string   `json:"state"`
	Permissions []string `json:"permissions"`
}

// ConnectAccountRequest represents a request to connect a social media account
type ConnectAccountRequest struct {
	UserID               string   `json:"user_id" validate:"required"`
	WorkspaceID          string   `json:"workspace_id"`
	Platform             string   `json:"platform" validate:"required"`
	OAuthCode            string   `json:"oauth_code" validate:"required"`
	RedirectURI          string   `json:"redirect_uri" validate:"required,url"`
	RequestedPermissions []string `json:"requested_permissions"`
}

// ConnectAccountResponse represents the response after connecting an account
type ConnectAccountResponse struct {
	AccountID    string           `json:"account_id"`
	Account      *PlatformAccount `json:"account"`
	Success      bool             `json:"success"`
	ErrorMessage string           `json:"error_message,omitempty"`
}

// PlatformAccount represents a connected social media account
type PlatformAccount struct {
	ID          string            `json:"id"`
	UserID      string            `json:"user_id"`
	WorkspaceID string            `json:"workspace_id,omitempty"`
	Platform    string            `json:"platform"`
	AccountID   string            `json:"account_id"`
	Username    string            `json:"username"`
	DisplayName string            `json:"display_name"`
	AvatarURL   string            `json:"avatar_url"`
	Status      string            `json:"status"`
	Permissions []string          `json:"permissions"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	ConnectedAt int64             `json:"connected_at"`
	LastUsed    int64             `json:"last_used"`
}

// RefreshTokenRequest represents a request to refresh access token
type RefreshTokenRequest struct {
	AccountID    string `json:"account_id" validate:"required"`
	UserID       string `json:"user_id" validate:"required"`
	ForceRefresh bool   `json:"force_refresh"`
}

// RefreshTokenResponse represents the response after token refresh
type RefreshTokenResponse struct {
	Success      bool   `json:"success"`
	NewExpiresAt int64  `json:"new_expires_at,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// DisconnectAccountRequest represents a request to disconnect an account
type DisconnectAccountRequest struct {
	AccountID   string `json:"account_id" validate:"required"`
	UserID      string `json:"user_id" validate:"required"`
	RevokeToken bool   `json:"revoke_token"`
}

// ListAccountsRequest represents a request to list connected accounts
type ListAccountsRequest struct {
	UserID      string `json:"user_id" validate:"required"`
	WorkspaceID string `json:"workspace_id"`
	Platform    string `json:"platform"`
	Status      string `json:"status"`
	Limit       int32  `json:"limit"`
	Offset      int32  `json:"offset"`
}

// ListAccountsResponse represents the response with list of accounts
type ListAccountsResponse struct {
	Accounts   []*PlatformAccount `json:"accounts"`
	TotalCount int32              `json:"total_count"`
}

// ValidateAccountRequest represents a request to validate account
type ValidateAccountRequest struct {
	AccountID        string `json:"account_id" validate:"required"`
	UserID           string `json:"user_id" validate:"required"`
	CheckPermissions bool   `json:"check_permissions"`
}

// ValidateAccountResponse represents the response after account validation
type ValidateAccountResponse struct {
	IsValid            bool     `json:"is_valid"`
	TokenExpired       bool     `json:"token_expired"`
	MissingPermissions []string `json:"missing_permissions,omitempty"`
	ErrorMessage       string   `json:"error_message,omitempty"`
	LastValidated      int64    `json:"last_validated"`
}

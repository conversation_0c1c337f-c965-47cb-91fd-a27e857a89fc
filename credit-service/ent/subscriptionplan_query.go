// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
)

// SubscriptionPlanQuery is the builder for querying SubscriptionPlan entities.
type SubscriptionPlanQuery struct {
	config
	ctx        *QueryContext
	order      []subscriptionplan.OrderOption
	inters     []Interceptor
	predicates []predicate.SubscriptionPlan
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SubscriptionPlanQuery builder.
func (spq *SubscriptionPlanQuery) Where(ps ...predicate.SubscriptionPlan) *SubscriptionPlanQuery {
	spq.predicates = append(spq.predicates, ps...)
	return spq
}

// Limit the number of records to be returned by this query.
func (spq *SubscriptionPlanQuery) Limit(limit int) *SubscriptionPlanQuery {
	spq.ctx.Limit = &limit
	return spq
}

// Offset to start from.
func (spq *SubscriptionPlanQuery) Offset(offset int) *SubscriptionPlanQuery {
	spq.ctx.Offset = &offset
	return spq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (spq *SubscriptionPlanQuery) Unique(unique bool) *SubscriptionPlanQuery {
	spq.ctx.Unique = &unique
	return spq
}

// Order specifies how the records should be ordered.
func (spq *SubscriptionPlanQuery) Order(o ...subscriptionplan.OrderOption) *SubscriptionPlanQuery {
	spq.order = append(spq.order, o...)
	return spq
}

// First returns the first SubscriptionPlan entity from the query.
// Returns a *NotFoundError when no SubscriptionPlan was found.
func (spq *SubscriptionPlanQuery) First(ctx context.Context) (*SubscriptionPlan, error) {
	nodes, err := spq.Limit(1).All(setContextOp(ctx, spq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{subscriptionplan.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) FirstX(ctx context.Context) *SubscriptionPlan {
	node, err := spq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SubscriptionPlan ID from the query.
// Returns a *NotFoundError when no SubscriptionPlan ID was found.
func (spq *SubscriptionPlanQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = spq.Limit(1).IDs(setContextOp(ctx, spq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{subscriptionplan.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := spq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SubscriptionPlan entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SubscriptionPlan entity is found.
// Returns a *NotFoundError when no SubscriptionPlan entities are found.
func (spq *SubscriptionPlanQuery) Only(ctx context.Context) (*SubscriptionPlan, error) {
	nodes, err := spq.Limit(2).All(setContextOp(ctx, spq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{subscriptionplan.Label}
	default:
		return nil, &NotSingularError{subscriptionplan.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) OnlyX(ctx context.Context) *SubscriptionPlan {
	node, err := spq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SubscriptionPlan ID in the query.
// Returns a *NotSingularError when more than one SubscriptionPlan ID is found.
// Returns a *NotFoundError when no entities are found.
func (spq *SubscriptionPlanQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = spq.Limit(2).IDs(setContextOp(ctx, spq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{subscriptionplan.Label}
	default:
		err = &NotSingularError{subscriptionplan.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := spq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SubscriptionPlans.
func (spq *SubscriptionPlanQuery) All(ctx context.Context) ([]*SubscriptionPlan, error) {
	ctx = setContextOp(ctx, spq.ctx, ent.OpQueryAll)
	if err := spq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SubscriptionPlan, *SubscriptionPlanQuery]()
	return withInterceptors[[]*SubscriptionPlan](ctx, spq, qr, spq.inters)
}

// AllX is like All, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) AllX(ctx context.Context) []*SubscriptionPlan {
	nodes, err := spq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SubscriptionPlan IDs.
func (spq *SubscriptionPlanQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if spq.ctx.Unique == nil && spq.path != nil {
		spq.Unique(true)
	}
	ctx = setContextOp(ctx, spq.ctx, ent.OpQueryIDs)
	if err = spq.Select(subscriptionplan.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := spq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (spq *SubscriptionPlanQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, spq.ctx, ent.OpQueryCount)
	if err := spq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, spq, querierCount[*SubscriptionPlanQuery](), spq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) CountX(ctx context.Context) int {
	count, err := spq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (spq *SubscriptionPlanQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, spq.ctx, ent.OpQueryExist)
	switch _, err := spq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (spq *SubscriptionPlanQuery) ExistX(ctx context.Context) bool {
	exist, err := spq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SubscriptionPlanQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (spq *SubscriptionPlanQuery) Clone() *SubscriptionPlanQuery {
	if spq == nil {
		return nil
	}
	return &SubscriptionPlanQuery{
		config:     spq.config,
		ctx:        spq.ctx.Clone(),
		order:      append([]subscriptionplan.OrderOption{}, spq.order...),
		inters:     append([]Interceptor{}, spq.inters...),
		predicates: append([]predicate.SubscriptionPlan{}, spq.predicates...),
		// clone intermediate query.
		sql:  spq.sql.Clone(),
		path: spq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SubscriptionPlan.Query().
//		GroupBy(subscriptionplan.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (spq *SubscriptionPlanQuery) GroupBy(field string, fields ...string) *SubscriptionPlanGroupBy {
	spq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SubscriptionPlanGroupBy{build: spq}
	grbuild.flds = &spq.ctx.Fields
	grbuild.label = subscriptionplan.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.SubscriptionPlan.Query().
//		Select(subscriptionplan.FieldName).
//		Scan(ctx, &v)
func (spq *SubscriptionPlanQuery) Select(fields ...string) *SubscriptionPlanSelect {
	spq.ctx.Fields = append(spq.ctx.Fields, fields...)
	sbuild := &SubscriptionPlanSelect{SubscriptionPlanQuery: spq}
	sbuild.label = subscriptionplan.Label
	sbuild.flds, sbuild.scan = &spq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SubscriptionPlanSelect configured with the given aggregations.
func (spq *SubscriptionPlanQuery) Aggregate(fns ...AggregateFunc) *SubscriptionPlanSelect {
	return spq.Select().Aggregate(fns...)
}

func (spq *SubscriptionPlanQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range spq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, spq); err != nil {
				return err
			}
		}
	}
	for _, f := range spq.ctx.Fields {
		if !subscriptionplan.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if spq.path != nil {
		prev, err := spq.path(ctx)
		if err != nil {
			return err
		}
		spq.sql = prev
	}
	return nil
}

func (spq *SubscriptionPlanQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SubscriptionPlan, error) {
	var (
		nodes = []*SubscriptionPlan{}
		_spec = spq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SubscriptionPlan).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SubscriptionPlan{config: spq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, spq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (spq *SubscriptionPlanQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := spq.querySpec()
	_spec.Node.Columns = spq.ctx.Fields
	if len(spq.ctx.Fields) > 0 {
		_spec.Unique = spq.ctx.Unique != nil && *spq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, spq.driver, _spec)
}

func (spq *SubscriptionPlanQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(subscriptionplan.Table, subscriptionplan.Columns, sqlgraph.NewFieldSpec(subscriptionplan.FieldID, field.TypeUUID))
	_spec.From = spq.sql
	if unique := spq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if spq.path != nil {
		_spec.Unique = true
	}
	if fields := spq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, subscriptionplan.FieldID)
		for i := range fields {
			if fields[i] != subscriptionplan.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := spq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := spq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := spq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := spq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (spq *SubscriptionPlanQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(spq.driver.Dialect())
	t1 := builder.Table(subscriptionplan.Table)
	columns := spq.ctx.Fields
	if len(columns) == 0 {
		columns = subscriptionplan.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if spq.sql != nil {
		selector = spq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if spq.ctx.Unique != nil && *spq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range spq.predicates {
		p(selector)
	}
	for _, p := range spq.order {
		p(selector)
	}
	if offset := spq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := spq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SubscriptionPlanGroupBy is the group-by builder for SubscriptionPlan entities.
type SubscriptionPlanGroupBy struct {
	selector
	build *SubscriptionPlanQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (spgb *SubscriptionPlanGroupBy) Aggregate(fns ...AggregateFunc) *SubscriptionPlanGroupBy {
	spgb.fns = append(spgb.fns, fns...)
	return spgb
}

// Scan applies the selector query and scans the result into the given value.
func (spgb *SubscriptionPlanGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, spgb.build.ctx, ent.OpQueryGroupBy)
	if err := spgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SubscriptionPlanQuery, *SubscriptionPlanGroupBy](ctx, spgb.build, spgb, spgb.build.inters, v)
}

func (spgb *SubscriptionPlanGroupBy) sqlScan(ctx context.Context, root *SubscriptionPlanQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(spgb.fns))
	for _, fn := range spgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*spgb.flds)+len(spgb.fns))
		for _, f := range *spgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*spgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := spgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SubscriptionPlanSelect is the builder for selecting fields of SubscriptionPlan entities.
type SubscriptionPlanSelect struct {
	*SubscriptionPlanQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (sps *SubscriptionPlanSelect) Aggregate(fns ...AggregateFunc) *SubscriptionPlanSelect {
	sps.fns = append(sps.fns, fns...)
	return sps
}

// Scan applies the selector query and scans the result into the given value.
func (sps *SubscriptionPlanSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sps.ctx, ent.OpQuerySelect)
	if err := sps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SubscriptionPlanQuery, *SubscriptionPlanSelect](ctx, sps.SubscriptionPlanQuery, sps, sps.inters, v)
}

func (sps *SubscriptionPlanSelect) sqlScan(ctx context.Context, root *SubscriptionPlanQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(sps.fns))
	for _, fn := range sps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*sps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

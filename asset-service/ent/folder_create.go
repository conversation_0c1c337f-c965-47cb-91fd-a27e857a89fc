// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
)

// FolderCreate is the builder for creating a Folder entity.
type FolderCreate struct {
	config
	mutation *FolderMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (fc *FolderCreate) SetUserID(u uuid.UUID) *FolderCreate {
	fc.mutation.SetUserID(u)
	return fc
}

// SetParentID sets the "parent_id" field.
func (fc *FolderCreate) SetParentID(u uuid.UUID) *FolderCreate {
	fc.mutation.SetParentID(u)
	return fc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (fc *FolderCreate) SetNillableParentID(u *uuid.UUID) *FolderCreate {
	if u != nil {
		fc.SetParentID(*u)
	}
	return fc
}

// SetName sets the "name" field.
func (fc *FolderCreate) SetName(s string) *FolderCreate {
	fc.mutation.SetName(s)
	return fc
}

// SetDescription sets the "description" field.
func (fc *FolderCreate) SetDescription(s string) *FolderCreate {
	fc.mutation.SetDescription(s)
	return fc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (fc *FolderCreate) SetNillableDescription(s *string) *FolderCreate {
	if s != nil {
		fc.SetDescription(*s)
	}
	return fc
}

// SetPath sets the "path" field.
func (fc *FolderCreate) SetPath(s string) *FolderCreate {
	fc.mutation.SetPath(s)
	return fc
}

// SetLevel sets the "level" field.
func (fc *FolderCreate) SetLevel(i int) *FolderCreate {
	fc.mutation.SetLevel(i)
	return fc
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (fc *FolderCreate) SetNillableLevel(i *int) *FolderCreate {
	if i != nil {
		fc.SetLevel(*i)
	}
	return fc
}

// SetIsShared sets the "is_shared" field.
func (fc *FolderCreate) SetIsShared(b bool) *FolderCreate {
	fc.mutation.SetIsShared(b)
	return fc
}

// SetNillableIsShared sets the "is_shared" field if the given value is not nil.
func (fc *FolderCreate) SetNillableIsShared(b *bool) *FolderCreate {
	if b != nil {
		fc.SetIsShared(*b)
	}
	return fc
}

// SetSharedWith sets the "shared_with" field.
func (fc *FolderCreate) SetSharedWith(s []string) *FolderCreate {
	fc.mutation.SetSharedWith(s)
	return fc
}

// SetPermissions sets the "permissions" field.
func (fc *FolderCreate) SetPermissions(s []string) *FolderCreate {
	fc.mutation.SetPermissions(s)
	return fc
}

// SetAssetCount sets the "asset_count" field.
func (fc *FolderCreate) SetAssetCount(i int64) *FolderCreate {
	fc.mutation.SetAssetCount(i)
	return fc
}

// SetNillableAssetCount sets the "asset_count" field if the given value is not nil.
func (fc *FolderCreate) SetNillableAssetCount(i *int64) *FolderCreate {
	if i != nil {
		fc.SetAssetCount(*i)
	}
	return fc
}

// SetTotalSize sets the "total_size" field.
func (fc *FolderCreate) SetTotalSize(i int64) *FolderCreate {
	fc.mutation.SetTotalSize(i)
	return fc
}

// SetNillableTotalSize sets the "total_size" field if the given value is not nil.
func (fc *FolderCreate) SetNillableTotalSize(i *int64) *FolderCreate {
	if i != nil {
		fc.SetTotalSize(*i)
	}
	return fc
}

// SetCreatedAt sets the "created_at" field.
func (fc *FolderCreate) SetCreatedAt(t time.Time) *FolderCreate {
	fc.mutation.SetCreatedAt(t)
	return fc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (fc *FolderCreate) SetNillableCreatedAt(t *time.Time) *FolderCreate {
	if t != nil {
		fc.SetCreatedAt(*t)
	}
	return fc
}

// SetUpdatedAt sets the "updated_at" field.
func (fc *FolderCreate) SetUpdatedAt(t time.Time) *FolderCreate {
	fc.mutation.SetUpdatedAt(t)
	return fc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (fc *FolderCreate) SetNillableUpdatedAt(t *time.Time) *FolderCreate {
	if t != nil {
		fc.SetUpdatedAt(*t)
	}
	return fc
}

// SetDeletedAt sets the "deleted_at" field.
func (fc *FolderCreate) SetDeletedAt(t time.Time) *FolderCreate {
	fc.mutation.SetDeletedAt(t)
	return fc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fc *FolderCreate) SetNillableDeletedAt(t *time.Time) *FolderCreate {
	if t != nil {
		fc.SetDeletedAt(*t)
	}
	return fc
}

// SetID sets the "id" field.
func (fc *FolderCreate) SetID(u uuid.UUID) *FolderCreate {
	fc.mutation.SetID(u)
	return fc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (fc *FolderCreate) SetNillableID(u *uuid.UUID) *FolderCreate {
	if u != nil {
		fc.SetID(*u)
	}
	return fc
}

// SetParent sets the "parent" edge to the Folder entity.
func (fc *FolderCreate) SetParent(f *Folder) *FolderCreate {
	return fc.SetParentID(f.ID)
}

// AddChildIDs adds the "children" edge to the Folder entity by IDs.
func (fc *FolderCreate) AddChildIDs(ids ...uuid.UUID) *FolderCreate {
	fc.mutation.AddChildIDs(ids...)
	return fc
}

// AddChildren adds the "children" edges to the Folder entity.
func (fc *FolderCreate) AddChildren(f ...*Folder) *FolderCreate {
	ids := make([]uuid.UUID, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return fc.AddChildIDs(ids...)
}

// AddAssetIDs adds the "assets" edge to the Asset entity by IDs.
func (fc *FolderCreate) AddAssetIDs(ids ...uuid.UUID) *FolderCreate {
	fc.mutation.AddAssetIDs(ids...)
	return fc
}

// AddAssets adds the "assets" edges to the Asset entity.
func (fc *FolderCreate) AddAssets(a ...*Asset) *FolderCreate {
	ids := make([]uuid.UUID, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return fc.AddAssetIDs(ids...)
}

// Mutation returns the FolderMutation object of the builder.
func (fc *FolderCreate) Mutation() *FolderMutation {
	return fc.mutation
}

// Save creates the Folder in the database.
func (fc *FolderCreate) Save(ctx context.Context) (*Folder, error) {
	fc.defaults()
	return withHooks(ctx, fc.sqlSave, fc.mutation, fc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (fc *FolderCreate) SaveX(ctx context.Context) *Folder {
	v, err := fc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fc *FolderCreate) Exec(ctx context.Context) error {
	_, err := fc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fc *FolderCreate) ExecX(ctx context.Context) {
	if err := fc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fc *FolderCreate) defaults() {
	if _, ok := fc.mutation.Level(); !ok {
		v := folder.DefaultLevel
		fc.mutation.SetLevel(v)
	}
	if _, ok := fc.mutation.IsShared(); !ok {
		v := folder.DefaultIsShared
		fc.mutation.SetIsShared(v)
	}
	if _, ok := fc.mutation.AssetCount(); !ok {
		v := folder.DefaultAssetCount
		fc.mutation.SetAssetCount(v)
	}
	if _, ok := fc.mutation.TotalSize(); !ok {
		v := folder.DefaultTotalSize
		fc.mutation.SetTotalSize(v)
	}
	if _, ok := fc.mutation.CreatedAt(); !ok {
		v := folder.DefaultCreatedAt()
		fc.mutation.SetCreatedAt(v)
	}
	if _, ok := fc.mutation.UpdatedAt(); !ok {
		v := folder.DefaultUpdatedAt()
		fc.mutation.SetUpdatedAt(v)
	}
	if _, ok := fc.mutation.ID(); !ok {
		v := folder.DefaultID()
		fc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fc *FolderCreate) check() error {
	if _, ok := fc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Folder.user_id"`)}
	}
	if _, ok := fc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Folder.name"`)}
	}
	if v, ok := fc.mutation.Name(); ok {
		if err := folder.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Folder.name": %w`, err)}
		}
	}
	if v, ok := fc.mutation.Description(); ok {
		if err := folder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "Folder.description": %w`, err)}
		}
	}
	if _, ok := fc.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "Folder.path"`)}
	}
	if v, ok := fc.mutation.Path(); ok {
		if err := folder.PathValidator(v); err != nil {
			return &ValidationError{Name: "path", err: fmt.Errorf(`ent: validator failed for field "Folder.path": %w`, err)}
		}
	}
	if _, ok := fc.mutation.Level(); !ok {
		return &ValidationError{Name: "level", err: errors.New(`ent: missing required field "Folder.level"`)}
	}
	if _, ok := fc.mutation.IsShared(); !ok {
		return &ValidationError{Name: "is_shared", err: errors.New(`ent: missing required field "Folder.is_shared"`)}
	}
	if _, ok := fc.mutation.AssetCount(); !ok {
		return &ValidationError{Name: "asset_count", err: errors.New(`ent: missing required field "Folder.asset_count"`)}
	}
	if _, ok := fc.mutation.TotalSize(); !ok {
		return &ValidationError{Name: "total_size", err: errors.New(`ent: missing required field "Folder.total_size"`)}
	}
	if _, ok := fc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Folder.created_at"`)}
	}
	if _, ok := fc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Folder.updated_at"`)}
	}
	return nil
}

func (fc *FolderCreate) sqlSave(ctx context.Context) (*Folder, error) {
	if err := fc.check(); err != nil {
		return nil, err
	}
	_node, _spec := fc.createSpec()
	if err := sqlgraph.CreateNode(ctx, fc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	fc.mutation.id = &_node.ID
	fc.mutation.done = true
	return _node, nil
}

func (fc *FolderCreate) createSpec() (*Folder, *sqlgraph.CreateSpec) {
	var (
		_node = &Folder{config: fc.config}
		_spec = sqlgraph.NewCreateSpec(folder.Table, sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID))
	)
	if id, ok := fc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := fc.mutation.UserID(); ok {
		_spec.SetField(folder.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := fc.mutation.Name(); ok {
		_spec.SetField(folder.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := fc.mutation.Description(); ok {
		_spec.SetField(folder.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := fc.mutation.Path(); ok {
		_spec.SetField(folder.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := fc.mutation.Level(); ok {
		_spec.SetField(folder.FieldLevel, field.TypeInt, value)
		_node.Level = value
	}
	if value, ok := fc.mutation.IsShared(); ok {
		_spec.SetField(folder.FieldIsShared, field.TypeBool, value)
		_node.IsShared = value
	}
	if value, ok := fc.mutation.SharedWith(); ok {
		_spec.SetField(folder.FieldSharedWith, field.TypeJSON, value)
		_node.SharedWith = value
	}
	if value, ok := fc.mutation.Permissions(); ok {
		_spec.SetField(folder.FieldPermissions, field.TypeJSON, value)
		_node.Permissions = value
	}
	if value, ok := fc.mutation.AssetCount(); ok {
		_spec.SetField(folder.FieldAssetCount, field.TypeInt64, value)
		_node.AssetCount = value
	}
	if value, ok := fc.mutation.TotalSize(); ok {
		_spec.SetField(folder.FieldTotalSize, field.TypeInt64, value)
		_node.TotalSize = value
	}
	if value, ok := fc.mutation.CreatedAt(); ok {
		_spec.SetField(folder.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := fc.mutation.UpdatedAt(); ok {
		_spec.SetField(folder.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := fc.mutation.DeletedAt(); ok {
		_spec.SetField(folder.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	if nodes := fc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   folder.ParentTable,
			Columns: []string{folder.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := fc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := fc.mutation.AssetsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// FolderCreateBulk is the builder for creating many Folder entities in bulk.
type FolderCreateBulk struct {
	config
	err      error
	builders []*FolderCreate
}

// Save creates the Folder entities in the database.
func (fcb *FolderCreateBulk) Save(ctx context.Context) ([]*Folder, error) {
	if fcb.err != nil {
		return nil, fcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(fcb.builders))
	nodes := make([]*Folder, len(fcb.builders))
	mutators := make([]Mutator, len(fcb.builders))
	for i := range fcb.builders {
		func(i int, root context.Context) {
			builder := fcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FolderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, fcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, fcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, fcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (fcb *FolderCreateBulk) SaveX(ctx context.Context) []*Folder {
	v, err := fcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fcb *FolderCreateBulk) Exec(ctx context.Context) error {
	_, err := fcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fcb *FolderCreateBulk) ExecX(ctx context.Context) {
	if err := fcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// BankTransferQuery is the builder for querying BankTransfer entities.
type BankTransferQuery struct {
	config
	ctx        *QueryContext
	order      []banktransfer.OrderOption
	inters     []Interceptor
	predicates []predicate.BankTransfer
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the BankTransferQuery builder.
func (btq *BankTransferQuery) Where(ps ...predicate.BankTransfer) *BankTransferQuery {
	btq.predicates = append(btq.predicates, ps...)
	return btq
}

// Limit the number of records to be returned by this query.
func (btq *BankTransferQuery) Limit(limit int) *BankTransferQuery {
	btq.ctx.Limit = &limit
	return btq
}

// Offset to start from.
func (btq *BankTransferQuery) Offset(offset int) *BankTransferQuery {
	btq.ctx.Offset = &offset
	return btq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (btq *BankTransferQuery) Unique(unique bool) *BankTransferQuery {
	btq.ctx.Unique = &unique
	return btq
}

// Order specifies how the records should be ordered.
func (btq *BankTransferQuery) Order(o ...banktransfer.OrderOption) *BankTransferQuery {
	btq.order = append(btq.order, o...)
	return btq
}

// First returns the first BankTransfer entity from the query.
// Returns a *NotFoundError when no BankTransfer was found.
func (btq *BankTransferQuery) First(ctx context.Context) (*BankTransfer, error) {
	nodes, err := btq.Limit(1).All(setContextOp(ctx, btq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{banktransfer.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (btq *BankTransferQuery) FirstX(ctx context.Context) *BankTransfer {
	node, err := btq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first BankTransfer ID from the query.
// Returns a *NotFoundError when no BankTransfer ID was found.
func (btq *BankTransferQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = btq.Limit(1).IDs(setContextOp(ctx, btq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{banktransfer.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (btq *BankTransferQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := btq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single BankTransfer entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one BankTransfer entity is found.
// Returns a *NotFoundError when no BankTransfer entities are found.
func (btq *BankTransferQuery) Only(ctx context.Context) (*BankTransfer, error) {
	nodes, err := btq.Limit(2).All(setContextOp(ctx, btq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{banktransfer.Label}
	default:
		return nil, &NotSingularError{banktransfer.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (btq *BankTransferQuery) OnlyX(ctx context.Context) *BankTransfer {
	node, err := btq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only BankTransfer ID in the query.
// Returns a *NotSingularError when more than one BankTransfer ID is found.
// Returns a *NotFoundError when no entities are found.
func (btq *BankTransferQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = btq.Limit(2).IDs(setContextOp(ctx, btq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{banktransfer.Label}
	default:
		err = &NotSingularError{banktransfer.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (btq *BankTransferQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := btq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of BankTransfers.
func (btq *BankTransferQuery) All(ctx context.Context) ([]*BankTransfer, error) {
	ctx = setContextOp(ctx, btq.ctx, ent.OpQueryAll)
	if err := btq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*BankTransfer, *BankTransferQuery]()
	return withInterceptors[[]*BankTransfer](ctx, btq, qr, btq.inters)
}

// AllX is like All, but panics if an error occurs.
func (btq *BankTransferQuery) AllX(ctx context.Context) []*BankTransfer {
	nodes, err := btq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of BankTransfer IDs.
func (btq *BankTransferQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if btq.ctx.Unique == nil && btq.path != nil {
		btq.Unique(true)
	}
	ctx = setContextOp(ctx, btq.ctx, ent.OpQueryIDs)
	if err = btq.Select(banktransfer.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (btq *BankTransferQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := btq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (btq *BankTransferQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, btq.ctx, ent.OpQueryCount)
	if err := btq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, btq, querierCount[*BankTransferQuery](), btq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (btq *BankTransferQuery) CountX(ctx context.Context) int {
	count, err := btq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (btq *BankTransferQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, btq.ctx, ent.OpQueryExist)
	switch _, err := btq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (btq *BankTransferQuery) ExistX(ctx context.Context) bool {
	exist, err := btq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the BankTransferQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (btq *BankTransferQuery) Clone() *BankTransferQuery {
	if btq == nil {
		return nil
	}
	return &BankTransferQuery{
		config:     btq.config,
		ctx:        btq.ctx.Clone(),
		order:      append([]banktransfer.OrderOption{}, btq.order...),
		inters:     append([]Interceptor{}, btq.inters...),
		predicates: append([]predicate.BankTransfer{}, btq.predicates...),
		// clone intermediate query.
		sql:  btq.sql.Clone(),
		path: btq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.BankTransfer.Query().
//		GroupBy(banktransfer.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (btq *BankTransferQuery) GroupBy(field string, fields ...string) *BankTransferGroupBy {
	btq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &BankTransferGroupBy{build: btq}
	grbuild.flds = &btq.ctx.Fields
	grbuild.label = banktransfer.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.BankTransfer.Query().
//		Select(banktransfer.FieldUserID).
//		Scan(ctx, &v)
func (btq *BankTransferQuery) Select(fields ...string) *BankTransferSelect {
	btq.ctx.Fields = append(btq.ctx.Fields, fields...)
	sbuild := &BankTransferSelect{BankTransferQuery: btq}
	sbuild.label = banktransfer.Label
	sbuild.flds, sbuild.scan = &btq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a BankTransferSelect configured with the given aggregations.
func (btq *BankTransferQuery) Aggregate(fns ...AggregateFunc) *BankTransferSelect {
	return btq.Select().Aggregate(fns...)
}

func (btq *BankTransferQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range btq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, btq); err != nil {
				return err
			}
		}
	}
	for _, f := range btq.ctx.Fields {
		if !banktransfer.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if btq.path != nil {
		prev, err := btq.path(ctx)
		if err != nil {
			return err
		}
		btq.sql = prev
	}
	return nil
}

func (btq *BankTransferQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*BankTransfer, error) {
	var (
		nodes = []*BankTransfer{}
		_spec = btq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*BankTransfer).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &BankTransfer{config: btq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, btq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (btq *BankTransferQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := btq.querySpec()
	_spec.Node.Columns = btq.ctx.Fields
	if len(btq.ctx.Fields) > 0 {
		_spec.Unique = btq.ctx.Unique != nil && *btq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, btq.driver, _spec)
}

func (btq *BankTransferQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(banktransfer.Table, banktransfer.Columns, sqlgraph.NewFieldSpec(banktransfer.FieldID, field.TypeUUID))
	_spec.From = btq.sql
	if unique := btq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if btq.path != nil {
		_spec.Unique = true
	}
	if fields := btq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, banktransfer.FieldID)
		for i := range fields {
			if fields[i] != banktransfer.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := btq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := btq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := btq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := btq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (btq *BankTransferQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(btq.driver.Dialect())
	t1 := builder.Table(banktransfer.Table)
	columns := btq.ctx.Fields
	if len(columns) == 0 {
		columns = banktransfer.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if btq.sql != nil {
		selector = btq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if btq.ctx.Unique != nil && *btq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range btq.predicates {
		p(selector)
	}
	for _, p := range btq.order {
		p(selector)
	}
	if offset := btq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := btq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// BankTransferGroupBy is the group-by builder for BankTransfer entities.
type BankTransferGroupBy struct {
	selector
	build *BankTransferQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (btgb *BankTransferGroupBy) Aggregate(fns ...AggregateFunc) *BankTransferGroupBy {
	btgb.fns = append(btgb.fns, fns...)
	return btgb
}

// Scan applies the selector query and scans the result into the given value.
func (btgb *BankTransferGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, btgb.build.ctx, ent.OpQueryGroupBy)
	if err := btgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*BankTransferQuery, *BankTransferGroupBy](ctx, btgb.build, btgb, btgb.build.inters, v)
}

func (btgb *BankTransferGroupBy) sqlScan(ctx context.Context, root *BankTransferQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(btgb.fns))
	for _, fn := range btgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*btgb.flds)+len(btgb.fns))
		for _, f := range *btgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*btgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := btgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// BankTransferSelect is the builder for selecting fields of BankTransfer entities.
type BankTransferSelect struct {
	*BankTransferQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (bts *BankTransferSelect) Aggregate(fns ...AggregateFunc) *BankTransferSelect {
	bts.fns = append(bts.fns, fns...)
	return bts
}

// Scan applies the selector query and scans the result into the given value.
func (bts *BankTransferSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, bts.ctx, ent.OpQuerySelect)
	if err := bts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*BankTransferQuery, *BankTransferSelect](ctx, bts.BankTransferQuery, bts, bts.inters, v)
}

func (bts *BankTransferSelect) sqlScan(ctx context.Context, root *BankTransferQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(bts.fns))
	for _, fn := range bts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*bts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := bts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

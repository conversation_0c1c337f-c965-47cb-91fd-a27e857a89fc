// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

// FolderUpdate is the builder for updating Folder entities.
type FolderUpdate struct {
	config
	hooks    []Hook
	mutation *FolderMutation
}

// Where appends a list predicates to the FolderUpdate builder.
func (fu *FolderUpdate) Where(ps ...predicate.Folder) *FolderUpdate {
	fu.mutation.Where(ps...)
	return fu
}

// SetUserID sets the "user_id" field.
func (fu *FolderUpdate) SetUserID(u uuid.UUID) *FolderUpdate {
	fu.mutation.SetUserID(u)
	return fu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableUserID(u *uuid.UUID) *FolderUpdate {
	if u != nil {
		fu.SetUserID(*u)
	}
	return fu
}

// SetParentID sets the "parent_id" field.
func (fu *FolderUpdate) SetParentID(u uuid.UUID) *FolderUpdate {
	fu.mutation.SetParentID(u)
	return fu
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableParentID(u *uuid.UUID) *FolderUpdate {
	if u != nil {
		fu.SetParentID(*u)
	}
	return fu
}

// ClearParentID clears the value of the "parent_id" field.
func (fu *FolderUpdate) ClearParentID() *FolderUpdate {
	fu.mutation.ClearParentID()
	return fu
}

// SetName sets the "name" field.
func (fu *FolderUpdate) SetName(s string) *FolderUpdate {
	fu.mutation.SetName(s)
	return fu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableName(s *string) *FolderUpdate {
	if s != nil {
		fu.SetName(*s)
	}
	return fu
}

// SetDescription sets the "description" field.
func (fu *FolderUpdate) SetDescription(s string) *FolderUpdate {
	fu.mutation.SetDescription(s)
	return fu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableDescription(s *string) *FolderUpdate {
	if s != nil {
		fu.SetDescription(*s)
	}
	return fu
}

// ClearDescription clears the value of the "description" field.
func (fu *FolderUpdate) ClearDescription() *FolderUpdate {
	fu.mutation.ClearDescription()
	return fu
}

// SetPath sets the "path" field.
func (fu *FolderUpdate) SetPath(s string) *FolderUpdate {
	fu.mutation.SetPath(s)
	return fu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (fu *FolderUpdate) SetNillablePath(s *string) *FolderUpdate {
	if s != nil {
		fu.SetPath(*s)
	}
	return fu
}

// SetLevel sets the "level" field.
func (fu *FolderUpdate) SetLevel(i int) *FolderUpdate {
	fu.mutation.ResetLevel()
	fu.mutation.SetLevel(i)
	return fu
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableLevel(i *int) *FolderUpdate {
	if i != nil {
		fu.SetLevel(*i)
	}
	return fu
}

// AddLevel adds i to the "level" field.
func (fu *FolderUpdate) AddLevel(i int) *FolderUpdate {
	fu.mutation.AddLevel(i)
	return fu
}

// SetIsShared sets the "is_shared" field.
func (fu *FolderUpdate) SetIsShared(b bool) *FolderUpdate {
	fu.mutation.SetIsShared(b)
	return fu
}

// SetNillableIsShared sets the "is_shared" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableIsShared(b *bool) *FolderUpdate {
	if b != nil {
		fu.SetIsShared(*b)
	}
	return fu
}

// SetSharedWith sets the "shared_with" field.
func (fu *FolderUpdate) SetSharedWith(s []string) *FolderUpdate {
	fu.mutation.SetSharedWith(s)
	return fu
}

// AppendSharedWith appends s to the "shared_with" field.
func (fu *FolderUpdate) AppendSharedWith(s []string) *FolderUpdate {
	fu.mutation.AppendSharedWith(s)
	return fu
}

// ClearSharedWith clears the value of the "shared_with" field.
func (fu *FolderUpdate) ClearSharedWith() *FolderUpdate {
	fu.mutation.ClearSharedWith()
	return fu
}

// SetPermissions sets the "permissions" field.
func (fu *FolderUpdate) SetPermissions(s []string) *FolderUpdate {
	fu.mutation.SetPermissions(s)
	return fu
}

// AppendPermissions appends s to the "permissions" field.
func (fu *FolderUpdate) AppendPermissions(s []string) *FolderUpdate {
	fu.mutation.AppendPermissions(s)
	return fu
}

// ClearPermissions clears the value of the "permissions" field.
func (fu *FolderUpdate) ClearPermissions() *FolderUpdate {
	fu.mutation.ClearPermissions()
	return fu
}

// SetAssetCount sets the "asset_count" field.
func (fu *FolderUpdate) SetAssetCount(i int64) *FolderUpdate {
	fu.mutation.ResetAssetCount()
	fu.mutation.SetAssetCount(i)
	return fu
}

// SetNillableAssetCount sets the "asset_count" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableAssetCount(i *int64) *FolderUpdate {
	if i != nil {
		fu.SetAssetCount(*i)
	}
	return fu
}

// AddAssetCount adds i to the "asset_count" field.
func (fu *FolderUpdate) AddAssetCount(i int64) *FolderUpdate {
	fu.mutation.AddAssetCount(i)
	return fu
}

// SetTotalSize sets the "total_size" field.
func (fu *FolderUpdate) SetTotalSize(i int64) *FolderUpdate {
	fu.mutation.ResetTotalSize()
	fu.mutation.SetTotalSize(i)
	return fu
}

// SetNillableTotalSize sets the "total_size" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableTotalSize(i *int64) *FolderUpdate {
	if i != nil {
		fu.SetTotalSize(*i)
	}
	return fu
}

// AddTotalSize adds i to the "total_size" field.
func (fu *FolderUpdate) AddTotalSize(i int64) *FolderUpdate {
	fu.mutation.AddTotalSize(i)
	return fu
}

// SetUpdatedAt sets the "updated_at" field.
func (fu *FolderUpdate) SetUpdatedAt(t time.Time) *FolderUpdate {
	fu.mutation.SetUpdatedAt(t)
	return fu
}

// SetDeletedAt sets the "deleted_at" field.
func (fu *FolderUpdate) SetDeletedAt(t time.Time) *FolderUpdate {
	fu.mutation.SetDeletedAt(t)
	return fu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fu *FolderUpdate) SetNillableDeletedAt(t *time.Time) *FolderUpdate {
	if t != nil {
		fu.SetDeletedAt(*t)
	}
	return fu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (fu *FolderUpdate) ClearDeletedAt() *FolderUpdate {
	fu.mutation.ClearDeletedAt()
	return fu
}

// SetParent sets the "parent" edge to the Folder entity.
func (fu *FolderUpdate) SetParent(f *Folder) *FolderUpdate {
	return fu.SetParentID(f.ID)
}

// AddChildIDs adds the "children" edge to the Folder entity by IDs.
func (fu *FolderUpdate) AddChildIDs(ids ...uuid.UUID) *FolderUpdate {
	fu.mutation.AddChildIDs(ids...)
	return fu
}

// AddChildren adds the "children" edges to the Folder entity.
func (fu *FolderUpdate) AddChildren(f ...*Folder) *FolderUpdate {
	ids := make([]uuid.UUID, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return fu.AddChildIDs(ids...)
}

// AddAssetIDs adds the "assets" edge to the Asset entity by IDs.
func (fu *FolderUpdate) AddAssetIDs(ids ...uuid.UUID) *FolderUpdate {
	fu.mutation.AddAssetIDs(ids...)
	return fu
}

// AddAssets adds the "assets" edges to the Asset entity.
func (fu *FolderUpdate) AddAssets(a ...*Asset) *FolderUpdate {
	ids := make([]uuid.UUID, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return fu.AddAssetIDs(ids...)
}

// Mutation returns the FolderMutation object of the builder.
func (fu *FolderUpdate) Mutation() *FolderMutation {
	return fu.mutation
}

// ClearParent clears the "parent" edge to the Folder entity.
func (fu *FolderUpdate) ClearParent() *FolderUpdate {
	fu.mutation.ClearParent()
	return fu
}

// ClearChildren clears all "children" edges to the Folder entity.
func (fu *FolderUpdate) ClearChildren() *FolderUpdate {
	fu.mutation.ClearChildren()
	return fu
}

// RemoveChildIDs removes the "children" edge to Folder entities by IDs.
func (fu *FolderUpdate) RemoveChildIDs(ids ...uuid.UUID) *FolderUpdate {
	fu.mutation.RemoveChildIDs(ids...)
	return fu
}

// RemoveChildren removes "children" edges to Folder entities.
func (fu *FolderUpdate) RemoveChildren(f ...*Folder) *FolderUpdate {
	ids := make([]uuid.UUID, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return fu.RemoveChildIDs(ids...)
}

// ClearAssets clears all "assets" edges to the Asset entity.
func (fu *FolderUpdate) ClearAssets() *FolderUpdate {
	fu.mutation.ClearAssets()
	return fu
}

// RemoveAssetIDs removes the "assets" edge to Asset entities by IDs.
func (fu *FolderUpdate) RemoveAssetIDs(ids ...uuid.UUID) *FolderUpdate {
	fu.mutation.RemoveAssetIDs(ids...)
	return fu
}

// RemoveAssets removes "assets" edges to Asset entities.
func (fu *FolderUpdate) RemoveAssets(a ...*Asset) *FolderUpdate {
	ids := make([]uuid.UUID, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return fu.RemoveAssetIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (fu *FolderUpdate) Save(ctx context.Context) (int, error) {
	fu.defaults()
	return withHooks(ctx, fu.sqlSave, fu.mutation, fu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fu *FolderUpdate) SaveX(ctx context.Context) int {
	affected, err := fu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (fu *FolderUpdate) Exec(ctx context.Context) error {
	_, err := fu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fu *FolderUpdate) ExecX(ctx context.Context) {
	if err := fu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fu *FolderUpdate) defaults() {
	if _, ok := fu.mutation.UpdatedAt(); !ok {
		v := folder.UpdateDefaultUpdatedAt()
		fu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fu *FolderUpdate) check() error {
	if v, ok := fu.mutation.Name(); ok {
		if err := folder.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Folder.name": %w`, err)}
		}
	}
	if v, ok := fu.mutation.Description(); ok {
		if err := folder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "Folder.description": %w`, err)}
		}
	}
	if v, ok := fu.mutation.Path(); ok {
		if err := folder.PathValidator(v); err != nil {
			return &ValidationError{Name: "path", err: fmt.Errorf(`ent: validator failed for field "Folder.path": %w`, err)}
		}
	}
	return nil
}

func (fu *FolderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := fu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(folder.Table, folder.Columns, sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID))
	if ps := fu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fu.mutation.UserID(); ok {
		_spec.SetField(folder.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := fu.mutation.Name(); ok {
		_spec.SetField(folder.FieldName, field.TypeString, value)
	}
	if value, ok := fu.mutation.Description(); ok {
		_spec.SetField(folder.FieldDescription, field.TypeString, value)
	}
	if fu.mutation.DescriptionCleared() {
		_spec.ClearField(folder.FieldDescription, field.TypeString)
	}
	if value, ok := fu.mutation.Path(); ok {
		_spec.SetField(folder.FieldPath, field.TypeString, value)
	}
	if value, ok := fu.mutation.Level(); ok {
		_spec.SetField(folder.FieldLevel, field.TypeInt, value)
	}
	if value, ok := fu.mutation.AddedLevel(); ok {
		_spec.AddField(folder.FieldLevel, field.TypeInt, value)
	}
	if value, ok := fu.mutation.IsShared(); ok {
		_spec.SetField(folder.FieldIsShared, field.TypeBool, value)
	}
	if value, ok := fu.mutation.SharedWith(); ok {
		_spec.SetField(folder.FieldSharedWith, field.TypeJSON, value)
	}
	if value, ok := fu.mutation.AppendedSharedWith(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, folder.FieldSharedWith, value)
		})
	}
	if fu.mutation.SharedWithCleared() {
		_spec.ClearField(folder.FieldSharedWith, field.TypeJSON)
	}
	if value, ok := fu.mutation.Permissions(); ok {
		_spec.SetField(folder.FieldPermissions, field.TypeJSON, value)
	}
	if value, ok := fu.mutation.AppendedPermissions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, folder.FieldPermissions, value)
		})
	}
	if fu.mutation.PermissionsCleared() {
		_spec.ClearField(folder.FieldPermissions, field.TypeJSON)
	}
	if value, ok := fu.mutation.AssetCount(); ok {
		_spec.SetField(folder.FieldAssetCount, field.TypeInt64, value)
	}
	if value, ok := fu.mutation.AddedAssetCount(); ok {
		_spec.AddField(folder.FieldAssetCount, field.TypeInt64, value)
	}
	if value, ok := fu.mutation.TotalSize(); ok {
		_spec.SetField(folder.FieldTotalSize, field.TypeInt64, value)
	}
	if value, ok := fu.mutation.AddedTotalSize(); ok {
		_spec.AddField(folder.FieldTotalSize, field.TypeInt64, value)
	}
	if value, ok := fu.mutation.UpdatedAt(); ok {
		_spec.SetField(folder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := fu.mutation.DeletedAt(); ok {
		_spec.SetField(folder.FieldDeletedAt, field.TypeTime, value)
	}
	if fu.mutation.DeletedAtCleared() {
		_spec.ClearField(folder.FieldDeletedAt, field.TypeTime)
	}
	if fu.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   folder.ParentTable,
			Columns: []string{folder.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   folder.ParentTable,
			Columns: []string{folder.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fu.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !fu.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fu.mutation.AssetsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.RemovedAssetsIDs(); len(nodes) > 0 && !fu.mutation.AssetsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.AssetsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, fu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{folder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	fu.mutation.done = true
	return n, nil
}

// FolderUpdateOne is the builder for updating a single Folder entity.
type FolderUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FolderMutation
}

// SetUserID sets the "user_id" field.
func (fuo *FolderUpdateOne) SetUserID(u uuid.UUID) *FolderUpdateOne {
	fuo.mutation.SetUserID(u)
	return fuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableUserID(u *uuid.UUID) *FolderUpdateOne {
	if u != nil {
		fuo.SetUserID(*u)
	}
	return fuo
}

// SetParentID sets the "parent_id" field.
func (fuo *FolderUpdateOne) SetParentID(u uuid.UUID) *FolderUpdateOne {
	fuo.mutation.SetParentID(u)
	return fuo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableParentID(u *uuid.UUID) *FolderUpdateOne {
	if u != nil {
		fuo.SetParentID(*u)
	}
	return fuo
}

// ClearParentID clears the value of the "parent_id" field.
func (fuo *FolderUpdateOne) ClearParentID() *FolderUpdateOne {
	fuo.mutation.ClearParentID()
	return fuo
}

// SetName sets the "name" field.
func (fuo *FolderUpdateOne) SetName(s string) *FolderUpdateOne {
	fuo.mutation.SetName(s)
	return fuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableName(s *string) *FolderUpdateOne {
	if s != nil {
		fuo.SetName(*s)
	}
	return fuo
}

// SetDescription sets the "description" field.
func (fuo *FolderUpdateOne) SetDescription(s string) *FolderUpdateOne {
	fuo.mutation.SetDescription(s)
	return fuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableDescription(s *string) *FolderUpdateOne {
	if s != nil {
		fuo.SetDescription(*s)
	}
	return fuo
}

// ClearDescription clears the value of the "description" field.
func (fuo *FolderUpdateOne) ClearDescription() *FolderUpdateOne {
	fuo.mutation.ClearDescription()
	return fuo
}

// SetPath sets the "path" field.
func (fuo *FolderUpdateOne) SetPath(s string) *FolderUpdateOne {
	fuo.mutation.SetPath(s)
	return fuo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillablePath(s *string) *FolderUpdateOne {
	if s != nil {
		fuo.SetPath(*s)
	}
	return fuo
}

// SetLevel sets the "level" field.
func (fuo *FolderUpdateOne) SetLevel(i int) *FolderUpdateOne {
	fuo.mutation.ResetLevel()
	fuo.mutation.SetLevel(i)
	return fuo
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableLevel(i *int) *FolderUpdateOne {
	if i != nil {
		fuo.SetLevel(*i)
	}
	return fuo
}

// AddLevel adds i to the "level" field.
func (fuo *FolderUpdateOne) AddLevel(i int) *FolderUpdateOne {
	fuo.mutation.AddLevel(i)
	return fuo
}

// SetIsShared sets the "is_shared" field.
func (fuo *FolderUpdateOne) SetIsShared(b bool) *FolderUpdateOne {
	fuo.mutation.SetIsShared(b)
	return fuo
}

// SetNillableIsShared sets the "is_shared" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableIsShared(b *bool) *FolderUpdateOne {
	if b != nil {
		fuo.SetIsShared(*b)
	}
	return fuo
}

// SetSharedWith sets the "shared_with" field.
func (fuo *FolderUpdateOne) SetSharedWith(s []string) *FolderUpdateOne {
	fuo.mutation.SetSharedWith(s)
	return fuo
}

// AppendSharedWith appends s to the "shared_with" field.
func (fuo *FolderUpdateOne) AppendSharedWith(s []string) *FolderUpdateOne {
	fuo.mutation.AppendSharedWith(s)
	return fuo
}

// ClearSharedWith clears the value of the "shared_with" field.
func (fuo *FolderUpdateOne) ClearSharedWith() *FolderUpdateOne {
	fuo.mutation.ClearSharedWith()
	return fuo
}

// SetPermissions sets the "permissions" field.
func (fuo *FolderUpdateOne) SetPermissions(s []string) *FolderUpdateOne {
	fuo.mutation.SetPermissions(s)
	return fuo
}

// AppendPermissions appends s to the "permissions" field.
func (fuo *FolderUpdateOne) AppendPermissions(s []string) *FolderUpdateOne {
	fuo.mutation.AppendPermissions(s)
	return fuo
}

// ClearPermissions clears the value of the "permissions" field.
func (fuo *FolderUpdateOne) ClearPermissions() *FolderUpdateOne {
	fuo.mutation.ClearPermissions()
	return fuo
}

// SetAssetCount sets the "asset_count" field.
func (fuo *FolderUpdateOne) SetAssetCount(i int64) *FolderUpdateOne {
	fuo.mutation.ResetAssetCount()
	fuo.mutation.SetAssetCount(i)
	return fuo
}

// SetNillableAssetCount sets the "asset_count" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableAssetCount(i *int64) *FolderUpdateOne {
	if i != nil {
		fuo.SetAssetCount(*i)
	}
	return fuo
}

// AddAssetCount adds i to the "asset_count" field.
func (fuo *FolderUpdateOne) AddAssetCount(i int64) *FolderUpdateOne {
	fuo.mutation.AddAssetCount(i)
	return fuo
}

// SetTotalSize sets the "total_size" field.
func (fuo *FolderUpdateOne) SetTotalSize(i int64) *FolderUpdateOne {
	fuo.mutation.ResetTotalSize()
	fuo.mutation.SetTotalSize(i)
	return fuo
}

// SetNillableTotalSize sets the "total_size" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableTotalSize(i *int64) *FolderUpdateOne {
	if i != nil {
		fuo.SetTotalSize(*i)
	}
	return fuo
}

// AddTotalSize adds i to the "total_size" field.
func (fuo *FolderUpdateOne) AddTotalSize(i int64) *FolderUpdateOne {
	fuo.mutation.AddTotalSize(i)
	return fuo
}

// SetUpdatedAt sets the "updated_at" field.
func (fuo *FolderUpdateOne) SetUpdatedAt(t time.Time) *FolderUpdateOne {
	fuo.mutation.SetUpdatedAt(t)
	return fuo
}

// SetDeletedAt sets the "deleted_at" field.
func (fuo *FolderUpdateOne) SetDeletedAt(t time.Time) *FolderUpdateOne {
	fuo.mutation.SetDeletedAt(t)
	return fuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fuo *FolderUpdateOne) SetNillableDeletedAt(t *time.Time) *FolderUpdateOne {
	if t != nil {
		fuo.SetDeletedAt(*t)
	}
	return fuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (fuo *FolderUpdateOne) ClearDeletedAt() *FolderUpdateOne {
	fuo.mutation.ClearDeletedAt()
	return fuo
}

// SetParent sets the "parent" edge to the Folder entity.
func (fuo *FolderUpdateOne) SetParent(f *Folder) *FolderUpdateOne {
	return fuo.SetParentID(f.ID)
}

// AddChildIDs adds the "children" edge to the Folder entity by IDs.
func (fuo *FolderUpdateOne) AddChildIDs(ids ...uuid.UUID) *FolderUpdateOne {
	fuo.mutation.AddChildIDs(ids...)
	return fuo
}

// AddChildren adds the "children" edges to the Folder entity.
func (fuo *FolderUpdateOne) AddChildren(f ...*Folder) *FolderUpdateOne {
	ids := make([]uuid.UUID, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return fuo.AddChildIDs(ids...)
}

// AddAssetIDs adds the "assets" edge to the Asset entity by IDs.
func (fuo *FolderUpdateOne) AddAssetIDs(ids ...uuid.UUID) *FolderUpdateOne {
	fuo.mutation.AddAssetIDs(ids...)
	return fuo
}

// AddAssets adds the "assets" edges to the Asset entity.
func (fuo *FolderUpdateOne) AddAssets(a ...*Asset) *FolderUpdateOne {
	ids := make([]uuid.UUID, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return fuo.AddAssetIDs(ids...)
}

// Mutation returns the FolderMutation object of the builder.
func (fuo *FolderUpdateOne) Mutation() *FolderMutation {
	return fuo.mutation
}

// ClearParent clears the "parent" edge to the Folder entity.
func (fuo *FolderUpdateOne) ClearParent() *FolderUpdateOne {
	fuo.mutation.ClearParent()
	return fuo
}

// ClearChildren clears all "children" edges to the Folder entity.
func (fuo *FolderUpdateOne) ClearChildren() *FolderUpdateOne {
	fuo.mutation.ClearChildren()
	return fuo
}

// RemoveChildIDs removes the "children" edge to Folder entities by IDs.
func (fuo *FolderUpdateOne) RemoveChildIDs(ids ...uuid.UUID) *FolderUpdateOne {
	fuo.mutation.RemoveChildIDs(ids...)
	return fuo
}

// RemoveChildren removes "children" edges to Folder entities.
func (fuo *FolderUpdateOne) RemoveChildren(f ...*Folder) *FolderUpdateOne {
	ids := make([]uuid.UUID, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return fuo.RemoveChildIDs(ids...)
}

// ClearAssets clears all "assets" edges to the Asset entity.
func (fuo *FolderUpdateOne) ClearAssets() *FolderUpdateOne {
	fuo.mutation.ClearAssets()
	return fuo
}

// RemoveAssetIDs removes the "assets" edge to Asset entities by IDs.
func (fuo *FolderUpdateOne) RemoveAssetIDs(ids ...uuid.UUID) *FolderUpdateOne {
	fuo.mutation.RemoveAssetIDs(ids...)
	return fuo
}

// RemoveAssets removes "assets" edges to Asset entities.
func (fuo *FolderUpdateOne) RemoveAssets(a ...*Asset) *FolderUpdateOne {
	ids := make([]uuid.UUID, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return fuo.RemoveAssetIDs(ids...)
}

// Where appends a list predicates to the FolderUpdate builder.
func (fuo *FolderUpdateOne) Where(ps ...predicate.Folder) *FolderUpdateOne {
	fuo.mutation.Where(ps...)
	return fuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (fuo *FolderUpdateOne) Select(field string, fields ...string) *FolderUpdateOne {
	fuo.fields = append([]string{field}, fields...)
	return fuo
}

// Save executes the query and returns the updated Folder entity.
func (fuo *FolderUpdateOne) Save(ctx context.Context) (*Folder, error) {
	fuo.defaults()
	return withHooks(ctx, fuo.sqlSave, fuo.mutation, fuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fuo *FolderUpdateOne) SaveX(ctx context.Context) *Folder {
	node, err := fuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (fuo *FolderUpdateOne) Exec(ctx context.Context) error {
	_, err := fuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fuo *FolderUpdateOne) ExecX(ctx context.Context) {
	if err := fuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fuo *FolderUpdateOne) defaults() {
	if _, ok := fuo.mutation.UpdatedAt(); !ok {
		v := folder.UpdateDefaultUpdatedAt()
		fuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fuo *FolderUpdateOne) check() error {
	if v, ok := fuo.mutation.Name(); ok {
		if err := folder.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Folder.name": %w`, err)}
		}
	}
	if v, ok := fuo.mutation.Description(); ok {
		if err := folder.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "Folder.description": %w`, err)}
		}
	}
	if v, ok := fuo.mutation.Path(); ok {
		if err := folder.PathValidator(v); err != nil {
			return &ValidationError{Name: "path", err: fmt.Errorf(`ent: validator failed for field "Folder.path": %w`, err)}
		}
	}
	return nil
}

func (fuo *FolderUpdateOne) sqlSave(ctx context.Context) (_node *Folder, err error) {
	if err := fuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(folder.Table, folder.Columns, sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID))
	id, ok := fuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Folder.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := fuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, folder.FieldID)
		for _, f := range fields {
			if !folder.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != folder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := fuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fuo.mutation.UserID(); ok {
		_spec.SetField(folder.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := fuo.mutation.Name(); ok {
		_spec.SetField(folder.FieldName, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Description(); ok {
		_spec.SetField(folder.FieldDescription, field.TypeString, value)
	}
	if fuo.mutation.DescriptionCleared() {
		_spec.ClearField(folder.FieldDescription, field.TypeString)
	}
	if value, ok := fuo.mutation.Path(); ok {
		_spec.SetField(folder.FieldPath, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Level(); ok {
		_spec.SetField(folder.FieldLevel, field.TypeInt, value)
	}
	if value, ok := fuo.mutation.AddedLevel(); ok {
		_spec.AddField(folder.FieldLevel, field.TypeInt, value)
	}
	if value, ok := fuo.mutation.IsShared(); ok {
		_spec.SetField(folder.FieldIsShared, field.TypeBool, value)
	}
	if value, ok := fuo.mutation.SharedWith(); ok {
		_spec.SetField(folder.FieldSharedWith, field.TypeJSON, value)
	}
	if value, ok := fuo.mutation.AppendedSharedWith(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, folder.FieldSharedWith, value)
		})
	}
	if fuo.mutation.SharedWithCleared() {
		_spec.ClearField(folder.FieldSharedWith, field.TypeJSON)
	}
	if value, ok := fuo.mutation.Permissions(); ok {
		_spec.SetField(folder.FieldPermissions, field.TypeJSON, value)
	}
	if value, ok := fuo.mutation.AppendedPermissions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, folder.FieldPermissions, value)
		})
	}
	if fuo.mutation.PermissionsCleared() {
		_spec.ClearField(folder.FieldPermissions, field.TypeJSON)
	}
	if value, ok := fuo.mutation.AssetCount(); ok {
		_spec.SetField(folder.FieldAssetCount, field.TypeInt64, value)
	}
	if value, ok := fuo.mutation.AddedAssetCount(); ok {
		_spec.AddField(folder.FieldAssetCount, field.TypeInt64, value)
	}
	if value, ok := fuo.mutation.TotalSize(); ok {
		_spec.SetField(folder.FieldTotalSize, field.TypeInt64, value)
	}
	if value, ok := fuo.mutation.AddedTotalSize(); ok {
		_spec.AddField(folder.FieldTotalSize, field.TypeInt64, value)
	}
	if value, ok := fuo.mutation.UpdatedAt(); ok {
		_spec.SetField(folder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := fuo.mutation.DeletedAt(); ok {
		_spec.SetField(folder.FieldDeletedAt, field.TypeTime, value)
	}
	if fuo.mutation.DeletedAtCleared() {
		_spec.ClearField(folder.FieldDeletedAt, field.TypeTime)
	}
	if fuo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   folder.ParentTable,
			Columns: []string{folder.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   folder.ParentTable,
			Columns: []string{folder.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fuo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !fuo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.ChildrenTable,
			Columns: []string{folder.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fuo.mutation.AssetsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.RemovedAssetsIDs(); len(nodes) > 0 && !fuo.mutation.AssetsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.AssetsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   folder.AssetsTable,
			Columns: []string{folder.AssetsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Folder{config: fuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, fuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{folder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	fuo.mutation.done = true
	return _node, nil
}

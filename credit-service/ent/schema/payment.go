package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Payment holds the schema definition for the Payment entity.
type Payment struct {
	ent.Schema
}

// Fields of the Payment.
func (Payment) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.UUID("order_id", uuid.UUID{}).
			Optional(), // Reference to order
		field.UUID("subscription_id", uuid.UUID{}).
			Optional(), // Reference to subscription
		field.String("payment_method").
			MaxLen(50), // 'card', 'bank_transfer', 'paypal', 'momo', 'zalopay'
		field.String("payment_provider").
			MaxLen(50), // 'stripe', 'paypal', 'momo', 'zalopay', 'manual'
		field.String("external_payment_id").
			MaxLen(100).
			Optional(), // Payment provider's transaction ID
		field.Int64("amount"). // Amount in cents
			Min(0),
		field.String("currency").
			MaxLen(3).
			Default("USD"),
		field.Enum("status").
			Values("pending", "processing", "completed", "failed", "cancelled", "refunded").
			Default("pending"),
		field.String("payment_intent_id").
			MaxLen(100).
			Optional(), // Stripe payment intent ID
		field.String("charge_id").
			MaxLen(100).
			Optional(), // Stripe charge ID
		field.JSON("billing_details", map[string]interface{}{}).
			Optional(), // Billing address, name, etc.
		field.JSON("payment_method_details", map[string]interface{}{}).
			Optional(), // Card details, bank details, etc.
		field.Time("paid_at").
			Optional(),
		field.Time("failed_at").
			Optional(),
		field.String("failure_code").
			MaxLen(50).
			Optional(),
		field.String("failure_message").
			MaxLen(500).
			Optional(),
		field.Int64("refunded_amount").
			Default(0).
			Min(0),
		field.Time("refunded_at").
			Optional(),
		field.String("refund_reason").
			MaxLen(500).
			Optional(),
		field.JSON("webhook_data", map[string]interface{}{}).
			Optional(), // Webhook payload data
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Payment.
func (Payment) Edges() []ent.Edge {
	return nil
}

// Indexes of the Payment.
func (Payment) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("order_id"),
		index.Fields("subscription_id"),
		index.Fields("payment_method"),
		index.Fields("payment_provider"),
		index.Fields("external_payment_id"),
		index.Fields("status"),
		index.Fields("payment_intent_id"),
		index.Fields("charge_id"),
		index.Fields("paid_at"),
		index.Fields("failed_at"),
		index.Fields("created_at"),
	}
}

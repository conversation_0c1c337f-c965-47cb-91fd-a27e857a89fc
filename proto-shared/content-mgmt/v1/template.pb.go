// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: content-mgmt/v1/template.proto

package contentmgmtv1

import (
	v1 "github.com/social-content-ai/proto-shared/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Template represents a content template
type Template struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId               string            `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Name                 string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description          string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Content              string            `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Category             string            `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	Platforms            []string          `protobuf:"bytes,7,rep,name=platforms,proto3" json:"platforms,omitempty"`
	Prompt               string            `protobuf:"bytes,8,opt,name=prompt,proto3" json:"prompt,omitempty"`
	IsPublic             bool              `protobuf:"varint,9,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	Type                 string            `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"` // my, marketplace, premium
	ThumbnailUrl         string            `protobuf:"bytes,11,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	RagFiles             []string          `protobuf:"bytes,12,rep,name=rag_files,json=ragFiles,proto3" json:"rag_files,omitempty"`
	VariablePlaceholders []string          `protobuf:"bytes,13,rep,name=variable_placeholders,json=variablePlaceholders,proto3" json:"variable_placeholders,omitempty"`
	IsVerified           bool              `protobuf:"varint,14,opt,name=is_verified,json=isVerified,proto3" json:"is_verified,omitempty"`
	IsFeatured           bool              `protobuf:"varint,15,opt,name=is_featured,json=isFeatured,proto3" json:"is_featured,omitempty"`
	IsPremium            bool              `protobuf:"varint,16,opt,name=is_premium,json=isPremium,proto3" json:"is_premium,omitempty"`
	CreditCost           int32             `protobuf:"varint,17,opt,name=credit_cost,json=creditCost,proto3" json:"credit_cost,omitempty"`
	Rating               float32           `protobuf:"fixed32,18,opt,name=rating,proto3" json:"rating,omitempty"`
	DownloadCount        int32             `protobuf:"varint,19,opt,name=download_count,json=downloadCount,proto3" json:"download_count,omitempty"`
	Timestamps           *v1.TimestampInfo `protobuf:"bytes,20,opt,name=timestamps,proto3" json:"timestamps,omitempty"`
}

func (x *Template) Reset() {
	*x = Template{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Template) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Template) ProtoMessage() {}

func (x *Template) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Template.ProtoReflect.Descriptor instead.
func (*Template) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{0}
}

func (x *Template) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Template) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Template) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Template) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Template) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Template) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *Template) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *Template) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *Template) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *Template) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Template) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *Template) GetRagFiles() []string {
	if x != nil {
		return x.RagFiles
	}
	return nil
}

func (x *Template) GetVariablePlaceholders() []string {
	if x != nil {
		return x.VariablePlaceholders
	}
	return nil
}

func (x *Template) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *Template) GetIsFeatured() bool {
	if x != nil {
		return x.IsFeatured
	}
	return false
}

func (x *Template) GetIsPremium() bool {
	if x != nil {
		return x.IsPremium
	}
	return false
}

func (x *Template) GetCreditCost() int32 {
	if x != nil {
		return x.CreditCost
	}
	return 0
}

func (x *Template) GetRating() float32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *Template) GetDownloadCount() int32 {
	if x != nil {
		return x.DownloadCount
	}
	return 0
}

func (x *Template) GetTimestamps() *v1.TimestampInfo {
	if x != nil {
		return x.Timestamps
	}
	return nil
}

// CreateTemplateRequest for creating a new template
type CreateTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description          string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Category             string   `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	Platforms            []string `protobuf:"bytes,6,rep,name=platforms,proto3" json:"platforms,omitempty"`
	Prompt               string   `protobuf:"bytes,7,opt,name=prompt,proto3" json:"prompt,omitempty"`
	IsPublic             bool     `protobuf:"varint,8,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	ThumbnailUrl         string   `protobuf:"bytes,9,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	RagFiles             []string `protobuf:"bytes,10,rep,name=rag_files,json=ragFiles,proto3" json:"rag_files,omitempty"`
	VariablePlaceholders []string `protobuf:"bytes,11,rep,name=variable_placeholders,json=variablePlaceholders,proto3" json:"variable_placeholders,omitempty"`
	IsPremium            bool     `protobuf:"varint,12,opt,name=is_premium,json=isPremium,proto3" json:"is_premium,omitempty"`
	CreditCost           int32    `protobuf:"varint,13,opt,name=credit_cost,json=creditCost,proto3" json:"credit_cost,omitempty"`
}

func (x *CreateTemplateRequest) Reset() {
	*x = CreateTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateRequest) ProtoMessage() {}

func (x *CreateTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{1}
}

func (x *CreateTemplateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateTemplateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTemplateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTemplateRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateTemplateRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CreateTemplateRequest) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *CreateTemplateRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *CreateTemplateRequest) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *CreateTemplateRequest) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *CreateTemplateRequest) GetRagFiles() []string {
	if x != nil {
		return x.RagFiles
	}
	return nil
}

func (x *CreateTemplateRequest) GetVariablePlaceholders() []string {
	if x != nil {
		return x.VariablePlaceholders
	}
	return nil
}

func (x *CreateTemplateRequest) GetIsPremium() bool {
	if x != nil {
		return x.IsPremium
	}
	return false
}

func (x *CreateTemplateRequest) GetCreditCost() int32 {
	if x != nil {
		return x.CreditCost
	}
	return 0
}

// UpdateTemplateRequest for updating a template
type UpdateTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 *string  `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Description          *string  `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	Content              *string  `protobuf:"bytes,4,opt,name=content,proto3,oneof" json:"content,omitempty"`
	Category             *string  `protobuf:"bytes,5,opt,name=category,proto3,oneof" json:"category,omitempty"`
	Platforms            []string `protobuf:"bytes,6,rep,name=platforms,proto3" json:"platforms,omitempty"`
	Prompt               *string  `protobuf:"bytes,7,opt,name=prompt,proto3,oneof" json:"prompt,omitempty"`
	IsPublic             *bool    `protobuf:"varint,8,opt,name=is_public,json=isPublic,proto3,oneof" json:"is_public,omitempty"`
	ThumbnailUrl         *string  `protobuf:"bytes,9,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	RagFiles             []string `protobuf:"bytes,10,rep,name=rag_files,json=ragFiles,proto3" json:"rag_files,omitempty"`
	VariablePlaceholders []string `protobuf:"bytes,11,rep,name=variable_placeholders,json=variablePlaceholders,proto3" json:"variable_placeholders,omitempty"`
	CreditCost           *int32   `protobuf:"varint,12,opt,name=credit_cost,json=creditCost,proto3,oneof" json:"credit_cost,omitempty"`
}

func (x *UpdateTemplateRequest) Reset() {
	*x = UpdateTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateRequest) ProtoMessage() {}

func (x *UpdateTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTemplateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateTemplateRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateTemplateRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateTemplateRequest) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *UpdateTemplateRequest) GetCategory() string {
	if x != nil && x.Category != nil {
		return *x.Category
	}
	return ""
}

func (x *UpdateTemplateRequest) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *UpdateTemplateRequest) GetPrompt() string {
	if x != nil && x.Prompt != nil {
		return *x.Prompt
	}
	return ""
}

func (x *UpdateTemplateRequest) GetIsPublic() bool {
	if x != nil && x.IsPublic != nil {
		return *x.IsPublic
	}
	return false
}

func (x *UpdateTemplateRequest) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *UpdateTemplateRequest) GetRagFiles() []string {
	if x != nil {
		return x.RagFiles
	}
	return nil
}

func (x *UpdateTemplateRequest) GetVariablePlaceholders() []string {
	if x != nil {
		return x.VariablePlaceholders
	}
	return nil
}

func (x *UpdateTemplateRequest) GetCreditCost() int32 {
	if x != nil && x.CreditCost != nil {
		return *x.CreditCost
	}
	return 0
}

// GetTemplateRequest for retrieving a template
type GetTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTemplateRequest) Reset() {
	*x = GetTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRequest) ProtoMessage() {}

func (x *GetTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRequest.ProtoReflect.Descriptor instead.
func (*GetTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{3}
}

func (x *GetTemplateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ListTemplatesRequest for listing templates
type ListTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Optional: filter by user
	Pagination  *v1.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Type        string                `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                   // Filter by type
	Category    string                `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`                           // Filter by category
	Platforms   []string              `protobuf:"bytes,5,rep,name=platforms,proto3" json:"platforms,omitempty"`                         // Filter by platforms
	PublicOnly  bool                  `protobuf:"varint,6,opt,name=public_only,json=publicOnly,proto3" json:"public_only,omitempty"`    // Only public templates
	PremiumOnly bool                  `protobuf:"varint,7,opt,name=premium_only,json=premiumOnly,proto3" json:"premium_only,omitempty"` // Only premium templates
	Search      string                `protobuf:"bytes,8,opt,name=search,proto3" json:"search,omitempty"`                               // Search query
}

func (x *ListTemplatesRequest) Reset() {
	*x = ListTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesRequest) ProtoMessage() {}

func (x *ListTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{4}
}

func (x *ListTemplatesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListTemplatesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListTemplatesRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListTemplatesRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ListTemplatesRequest) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *ListTemplatesRequest) GetPublicOnly() bool {
	if x != nil {
		return x.PublicOnly
	}
	return false
}

func (x *ListTemplatesRequest) GetPremiumOnly() bool {
	if x != nil {
		return x.PremiumOnly
	}
	return false
}

func (x *ListTemplatesRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// ListTemplatesResponse with paginated templates
type ListTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Templates  []*Template            `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	Pagination *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListTemplatesResponse) Reset() {
	*x = ListTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesResponse) ProtoMessage() {}

func (x *ListTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{5}
}

func (x *ListTemplatesResponse) GetTemplates() []*Template {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *ListTemplatesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// UseTemplateRequest for using a template
type UseTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId    string            `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Variables map[string]string `protobuf:"bytes,3,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Variable values
}

func (x *UseTemplateRequest) Reset() {
	*x = UseTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseTemplateRequest) ProtoMessage() {}

func (x *UseTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseTemplateRequest.ProtoReflect.Descriptor instead.
func (*UseTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{6}
}

func (x *UseTemplateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UseTemplateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UseTemplateRequest) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

// UseTemplateResponse with processed template
type UseTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessedContent string `protobuf:"bytes,1,opt,name=processed_content,json=processedContent,proto3" json:"processed_content,omitempty"`
	ProcessedPrompt  string `protobuf:"bytes,2,opt,name=processed_prompt,json=processedPrompt,proto3" json:"processed_prompt,omitempty"`
	CreditsCharged   int32  `protobuf:"varint,3,opt,name=credits_charged,json=creditsCharged,proto3" json:"credits_charged,omitempty"`
}

func (x *UseTemplateResponse) Reset() {
	*x = UseTemplateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseTemplateResponse) ProtoMessage() {}

func (x *UseTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseTemplateResponse.ProtoReflect.Descriptor instead.
func (*UseTemplateResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{7}
}

func (x *UseTemplateResponse) GetProcessedContent() string {
	if x != nil {
		return x.ProcessedContent
	}
	return ""
}

func (x *UseTemplateResponse) GetProcessedPrompt() string {
	if x != nil {
		return x.ProcessedPrompt
	}
	return ""
}

func (x *UseTemplateResponse) GetCreditsCharged() int32 {
	if x != nil {
		return x.CreditsCharged
	}
	return 0
}

// RateTemplateRequest for rating a template
type RateTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Rating int32  `protobuf:"varint,3,opt,name=rating,proto3" json:"rating,omitempty"` // 1-5 stars
	Review string `protobuf:"bytes,4,opt,name=review,proto3" json:"review,omitempty"`  // Optional review text
}

func (x *RateTemplateRequest) Reset() {
	*x = RateTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateTemplateRequest) ProtoMessage() {}

func (x *RateTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateTemplateRequest.ProtoReflect.Descriptor instead.
func (*RateTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{8}
}

func (x *RateTemplateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RateTemplateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RateTemplateRequest) GetRating() int32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *RateTemplateRequest) GetReview() string {
	if x != nil {
		return x.Review
	}
	return ""
}

// TemplateRating represents a template rating
type TemplateRating struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId string                 `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	UserId     string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Rating     int32                  `protobuf:"varint,4,opt,name=rating,proto3" json:"rating,omitempty"` // 1-5 stars
	Review     string                 `protobuf:"bytes,5,opt,name=review,proto3" json:"review,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *TemplateRating) Reset() {
	*x = TemplateRating{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateRating) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateRating) ProtoMessage() {}

func (x *TemplateRating) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateRating.ProtoReflect.Descriptor instead.
func (*TemplateRating) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{9}
}

func (x *TemplateRating) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TemplateRating) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *TemplateRating) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TemplateRating) GetRating() int32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *TemplateRating) GetReview() string {
	if x != nil {
		return x.Review
	}
	return ""
}

func (x *TemplateRating) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// GetTemplateRatingsRequest for getting template ratings
type GetTemplateRatingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId string                `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	Pagination *v1.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	MinRating  int32                 `protobuf:"varint,3,opt,name=min_rating,json=minRating,proto3" json:"min_rating,omitempty"` // Filter by minimum rating
}

func (x *GetTemplateRatingsRequest) Reset() {
	*x = GetTemplateRatingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRatingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRatingsRequest) ProtoMessage() {}

func (x *GetTemplateRatingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRatingsRequest.ProtoReflect.Descriptor instead.
func (*GetTemplateRatingsRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{10}
}

func (x *GetTemplateRatingsRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *GetTemplateRatingsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetTemplateRatingsRequest) GetMinRating() int32 {
	if x != nil {
		return x.MinRating
	}
	return 0
}

// GetTemplateRatingsResponse with paginated ratings
type GetTemplateRatingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ratings            []*TemplateRating      `protobuf:"bytes,1,rep,name=ratings,proto3" json:"ratings,omitempty"`
	Pagination         *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	AverageRating      float32                `protobuf:"fixed32,3,opt,name=average_rating,json=averageRating,proto3" json:"average_rating,omitempty"`
	TotalRatings       int32                  `protobuf:"varint,4,opt,name=total_ratings,json=totalRatings,proto3" json:"total_ratings,omitempty"`
	RatingDistribution map[int32]int32        `protobuf:"bytes,5,rep,name=rating_distribution,json=ratingDistribution,proto3" json:"rating_distribution,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // rating -> count
}

func (x *GetTemplateRatingsResponse) Reset() {
	*x = GetTemplateRatingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRatingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRatingsResponse) ProtoMessage() {}

func (x *GetTemplateRatingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRatingsResponse.ProtoReflect.Descriptor instead.
func (*GetTemplateRatingsResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{11}
}

func (x *GetTemplateRatingsResponse) GetRatings() []*TemplateRating {
	if x != nil {
		return x.Ratings
	}
	return nil
}

func (x *GetTemplateRatingsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetTemplateRatingsResponse) GetAverageRating() float32 {
	if x != nil {
		return x.AverageRating
	}
	return 0
}

func (x *GetTemplateRatingsResponse) GetTotalRatings() int32 {
	if x != nil {
		return x.TotalRatings
	}
	return 0
}

func (x *GetTemplateRatingsResponse) GetRatingDistribution() map[int32]int32 {
	if x != nil {
		return x.RatingDistribution
	}
	return nil
}

// PurchaseTemplateRequest for purchasing a premium template
type PurchaseTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId      string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	UserId          string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId     string `protobuf:"bytes,3,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`              // Optional workspace context
	ValidateCredits bool   `protobuf:"varint,4,opt,name=validate_credits,json=validateCredits,proto3" json:"validate_credits,omitempty"` // Whether to validate credits first
}

func (x *PurchaseTemplateRequest) Reset() {
	*x = PurchaseTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseTemplateRequest) ProtoMessage() {}

func (x *PurchaseTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseTemplateRequest.ProtoReflect.Descriptor instead.
func (*PurchaseTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{12}
}

func (x *PurchaseTemplateRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *PurchaseTemplateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PurchaseTemplateRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *PurchaseTemplateRequest) GetValidateCredits() bool {
	if x != nil {
		return x.ValidateCredits
	}
	return false
}

// PurchaseTemplateResponse with purchase result
type PurchaseTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PurchaseId     string                 `protobuf:"bytes,1,opt,name=purchase_id,json=purchaseId,proto3" json:"purchase_id,omitempty"`
	TemplateId     string                 `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	CreditsCharged int32                  `protobuf:"varint,3,opt,name=credits_charged,json=creditsCharged,proto3" json:"credits_charged,omitempty"`
	TransactionId  string                 `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // Credit transaction reference
	PurchasedAt    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=purchased_at,json=purchasedAt,proto3" json:"purchased_at,omitempty"`
	Template       *Template              `protobuf:"bytes,6,opt,name=template,proto3" json:"template,omitempty"` // Full template access
}

func (x *PurchaseTemplateResponse) Reset() {
	*x = PurchaseTemplateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseTemplateResponse) ProtoMessage() {}

func (x *PurchaseTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseTemplateResponse.ProtoReflect.Descriptor instead.
func (*PurchaseTemplateResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{13}
}

func (x *PurchaseTemplateResponse) GetPurchaseId() string {
	if x != nil {
		return x.PurchaseId
	}
	return ""
}

func (x *PurchaseTemplateResponse) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *PurchaseTemplateResponse) GetCreditsCharged() int32 {
	if x != nil {
		return x.CreditsCharged
	}
	return 0
}

func (x *PurchaseTemplateResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *PurchaseTemplateResponse) GetPurchasedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PurchasedAt
	}
	return nil
}

func (x *PurchaseTemplateResponse) GetTemplate() *Template {
	if x != nil {
		return x.Template
	}
	return nil
}

// GetPurchasedTemplatesRequest for listing purchased templates
type GetPurchasedTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId string                `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"` // Optional workspace filter
	Pagination  *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Category    string                `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"` // Filter by category
}

func (x *GetPurchasedTemplatesRequest) Reset() {
	*x = GetPurchasedTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPurchasedTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchasedTemplatesRequest) ProtoMessage() {}

func (x *GetPurchasedTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchasedTemplatesRequest.ProtoReflect.Descriptor instead.
func (*GetPurchasedTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{14}
}

func (x *GetPurchasedTemplatesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetPurchasedTemplatesRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *GetPurchasedTemplatesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetPurchasedTemplatesRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

// GetPurchasedTemplatesResponse with purchased templates
type GetPurchasedTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Templates  []*Template            `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	Pagination *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Purchases  []*PurchaseInfo        `protobuf:"bytes,3,rep,name=purchases,proto3" json:"purchases,omitempty"` // Purchase information
}

func (x *GetPurchasedTemplatesResponse) Reset() {
	*x = GetPurchasedTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPurchasedTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchasedTemplatesResponse) ProtoMessage() {}

func (x *GetPurchasedTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchasedTemplatesResponse.ProtoReflect.Descriptor instead.
func (*GetPurchasedTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{15}
}

func (x *GetPurchasedTemplatesResponse) GetTemplates() []*Template {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *GetPurchasedTemplatesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetPurchasedTemplatesResponse) GetPurchases() []*PurchaseInfo {
	if x != nil {
		return x.Purchases
	}
	return nil
}

// PurchaseInfo contains purchase details
type PurchaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PurchaseId     string                 `protobuf:"bytes,1,opt,name=purchase_id,json=purchaseId,proto3" json:"purchase_id,omitempty"`
	TemplateId     string                 `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	CreditsCharged int32                  `protobuf:"varint,3,opt,name=credits_charged,json=creditsCharged,proto3" json:"credits_charged,omitempty"`
	PurchasedAt    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=purchased_at,json=purchasedAt,proto3" json:"purchased_at,omitempty"`
}

func (x *PurchaseInfo) Reset() {
	*x = PurchaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseInfo) ProtoMessage() {}

func (x *PurchaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseInfo.ProtoReflect.Descriptor instead.
func (*PurchaseInfo) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{16}
}

func (x *PurchaseInfo) GetPurchaseId() string {
	if x != nil {
		return x.PurchaseId
	}
	return ""
}

func (x *PurchaseInfo) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *PurchaseInfo) GetCreditsCharged() int32 {
	if x != nil {
		return x.CreditsCharged
	}
	return 0
}

func (x *PurchaseInfo) GetPurchasedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PurchasedAt
	}
	return nil
}

// TemplateRevenue represents template revenue information
type TemplateRevenue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId       string                 `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	TemplateName     string                 `protobuf:"bytes,2,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`
	TotalPurchases   int32                  `protobuf:"varint,3,opt,name=total_purchases,json=totalPurchases,proto3" json:"total_purchases,omitempty"`
	TotalRevenue     int32                  `protobuf:"varint,4,opt,name=total_revenue,json=totalRevenue,proto3" json:"total_revenue,omitempty"` // Total credits earned
	MonthlyPurchases int32                  `protobuf:"varint,5,opt,name=monthly_purchases,json=monthlyPurchases,proto3" json:"monthly_purchases,omitempty"`
	MonthlyRevenue   int32                  `protobuf:"varint,6,opt,name=monthly_revenue,json=monthlyRevenue,proto3" json:"monthly_revenue,omitempty"`
	AverageRating    float32                `protobuf:"fixed32,7,opt,name=average_rating,json=averageRating,proto3" json:"average_rating,omitempty"`
	LastPurchase     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_purchase,json=lastPurchase,proto3" json:"last_purchase,omitempty"`
}

func (x *TemplateRevenue) Reset() {
	*x = TemplateRevenue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateRevenue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateRevenue) ProtoMessage() {}

func (x *TemplateRevenue) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateRevenue.ProtoReflect.Descriptor instead.
func (*TemplateRevenue) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{17}
}

func (x *TemplateRevenue) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *TemplateRevenue) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *TemplateRevenue) GetTotalPurchases() int32 {
	if x != nil {
		return x.TotalPurchases
	}
	return 0
}

func (x *TemplateRevenue) GetTotalRevenue() int32 {
	if x != nil {
		return x.TotalRevenue
	}
	return 0
}

func (x *TemplateRevenue) GetMonthlyPurchases() int32 {
	if x != nil {
		return x.MonthlyPurchases
	}
	return 0
}

func (x *TemplateRevenue) GetMonthlyRevenue() int32 {
	if x != nil {
		return x.MonthlyRevenue
	}
	return 0
}

func (x *TemplateRevenue) GetAverageRating() float32 {
	if x != nil {
		return x.AverageRating
	}
	return 0
}

func (x *TemplateRevenue) GetLastPurchase() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPurchase
	}
	return nil
}

// GetTemplateRevenueRequest for getting template revenue
type GetTemplateRevenueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`             // Template creator
	TemplateId string                `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"` // Optional specific template
	DateFrom   string                `protobuf:"bytes,3,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`       // Format: YYYY-MM-DD
	DateTo     string                `protobuf:"bytes,4,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`             // Format: YYYY-MM-DD
	Pagination *v1.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetTemplateRevenueRequest) Reset() {
	*x = GetTemplateRevenueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRevenueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRevenueRequest) ProtoMessage() {}

func (x *GetTemplateRevenueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRevenueRequest.ProtoReflect.Descriptor instead.
func (*GetTemplateRevenueRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{18}
}

func (x *GetTemplateRevenueRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetTemplateRevenueRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *GetTemplateRevenueRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *GetTemplateRevenueRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *GetTemplateRevenueRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetTemplateRevenueResponse with revenue data
type GetTemplateRevenueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Revenues       []*TemplateRevenue     `protobuf:"bytes,1,rep,name=revenues,proto3" json:"revenues,omitempty"`
	Pagination     *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	TotalRevenue   int32                  `protobuf:"varint,3,opt,name=total_revenue,json=totalRevenue,proto3" json:"total_revenue,omitempty"`       // Total credits earned
	TotalPurchases int32                  `protobuf:"varint,4,opt,name=total_purchases,json=totalPurchases,proto3" json:"total_purchases,omitempty"` // Total purchases across all templates
}

func (x *GetTemplateRevenueResponse) Reset() {
	*x = GetTemplateRevenueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateRevenueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateRevenueResponse) ProtoMessage() {}

func (x *GetTemplateRevenueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateRevenueResponse.ProtoReflect.Descriptor instead.
func (*GetTemplateRevenueResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{19}
}

func (x *GetTemplateRevenueResponse) GetRevenues() []*TemplateRevenue {
	if x != nil {
		return x.Revenues
	}
	return nil
}

func (x *GetTemplateRevenueResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetTemplateRevenueResponse) GetTotalRevenue() int32 {
	if x != nil {
		return x.TotalRevenue
	}
	return 0
}

func (x *GetTemplateRevenueResponse) GetTotalPurchases() int32 {
	if x != nil {
		return x.TotalPurchases
	}
	return 0
}

// DeleteTemplateRequest for deleting a template
type DeleteTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteTemplateRequest) Reset() {
	*x = DeleteTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_template_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplateRequest) ProtoMessage() {}

func (x *DeleteTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_template_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteTemplateRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_template_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteTemplateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_content_mgmt_v1_template_proto protoreflect.FileDescriptor

var file_content_mgmt_v1_template_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x04, 0x0a, 0x08, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c,
	0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x33, 0x0a, 0x15, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x14, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72,
	0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50,
	0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73,
	0x22, 0xa6, 0x03, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x67, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x67, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x15, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x14, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x22, 0x93, 0x04, 0x0a, 0x15, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x73, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x04, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x88, 0x01, 0x01, 0x12, 0x20,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x05, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x88, 0x01, 0x01,
	0x12, 0x28, 0x0a, 0x0d, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0c, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61,
	0x67, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x61, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x15, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x07, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x22,
	0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x97, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6f, 0x6e,
	0x6c, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x5f,
	0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x6d,
	0x69, 0x75, 0x6d, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x22,
	0x8f, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xcd, 0x01, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x50, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x96, 0x01, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x22, 0x6e, 0x0a, 0x13, 0x52, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x22, 0xc5, 0x01, 0x0a, 0x0e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x99, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x9f,
	0x03, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x07, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x07, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0d, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x74, 0x0a, 0x13, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x45, 0x0a, 0x17, 0x52, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xa1, 0x01, 0x0a, 0x17, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x18, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x22, 0xd4, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x09, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x0c, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x41, 0x74, 0x22, 0xe3, 0x02, 0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x08, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x22, 0x27,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x32, 0xa3, 0x08, 0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x26, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x4d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x53, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x5e, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x4c, 0x0a, 0x0c, 0x52, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6d,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2b, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a,
	0x10, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x2d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76,
	0x65, 0x6e, 0x75, 0x65, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2b, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x49, 0x5a,
	0x47, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x61, 0x69, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x6d, 0x67, 0x6d, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_content_mgmt_v1_template_proto_rawDescOnce sync.Once
	file_content_mgmt_v1_template_proto_rawDescData = file_content_mgmt_v1_template_proto_rawDesc
)

func file_content_mgmt_v1_template_proto_rawDescGZIP() []byte {
	file_content_mgmt_v1_template_proto_rawDescOnce.Do(func() {
		file_content_mgmt_v1_template_proto_rawDescData = protoimpl.X.CompressGZIP(file_content_mgmt_v1_template_proto_rawDescData)
	})
	return file_content_mgmt_v1_template_proto_rawDescData
}

var file_content_mgmt_v1_template_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_content_mgmt_v1_template_proto_goTypes = []interface{}{
	(*Template)(nil),                      // 0: content_mgmt.v1.Template
	(*CreateTemplateRequest)(nil),         // 1: content_mgmt.v1.CreateTemplateRequest
	(*UpdateTemplateRequest)(nil),         // 2: content_mgmt.v1.UpdateTemplateRequest
	(*GetTemplateRequest)(nil),            // 3: content_mgmt.v1.GetTemplateRequest
	(*ListTemplatesRequest)(nil),          // 4: content_mgmt.v1.ListTemplatesRequest
	(*ListTemplatesResponse)(nil),         // 5: content_mgmt.v1.ListTemplatesResponse
	(*UseTemplateRequest)(nil),            // 6: content_mgmt.v1.UseTemplateRequest
	(*UseTemplateResponse)(nil),           // 7: content_mgmt.v1.UseTemplateResponse
	(*RateTemplateRequest)(nil),           // 8: content_mgmt.v1.RateTemplateRequest
	(*TemplateRating)(nil),                // 9: content_mgmt.v1.TemplateRating
	(*GetTemplateRatingsRequest)(nil),     // 10: content_mgmt.v1.GetTemplateRatingsRequest
	(*GetTemplateRatingsResponse)(nil),    // 11: content_mgmt.v1.GetTemplateRatingsResponse
	(*PurchaseTemplateRequest)(nil),       // 12: content_mgmt.v1.PurchaseTemplateRequest
	(*PurchaseTemplateResponse)(nil),      // 13: content_mgmt.v1.PurchaseTemplateResponse
	(*GetPurchasedTemplatesRequest)(nil),  // 14: content_mgmt.v1.GetPurchasedTemplatesRequest
	(*GetPurchasedTemplatesResponse)(nil), // 15: content_mgmt.v1.GetPurchasedTemplatesResponse
	(*PurchaseInfo)(nil),                  // 16: content_mgmt.v1.PurchaseInfo
	(*TemplateRevenue)(nil),               // 17: content_mgmt.v1.TemplateRevenue
	(*GetTemplateRevenueRequest)(nil),     // 18: content_mgmt.v1.GetTemplateRevenueRequest
	(*GetTemplateRevenueResponse)(nil),    // 19: content_mgmt.v1.GetTemplateRevenueResponse
	(*DeleteTemplateRequest)(nil),         // 20: content_mgmt.v1.DeleteTemplateRequest
	nil,                                   // 21: content_mgmt.v1.UseTemplateRequest.VariablesEntry
	nil,                                   // 22: content_mgmt.v1.GetTemplateRatingsResponse.RatingDistributionEntry
	(*v1.TimestampInfo)(nil),              // 23: common.v1.TimestampInfo
	(*v1.PaginationRequest)(nil),          // 24: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),         // 25: common.v1.PaginationResponse
	(*timestamppb.Timestamp)(nil),         // 26: google.protobuf.Timestamp
	(*Empty)(nil),                         // 27: content_mgmt.v1.Empty
}
var file_content_mgmt_v1_template_proto_depIdxs = []int32{
	23, // 0: content_mgmt.v1.Template.timestamps:type_name -> common.v1.TimestampInfo
	24, // 1: content_mgmt.v1.ListTemplatesRequest.pagination:type_name -> common.v1.PaginationRequest
	0,  // 2: content_mgmt.v1.ListTemplatesResponse.templates:type_name -> content_mgmt.v1.Template
	25, // 3: content_mgmt.v1.ListTemplatesResponse.pagination:type_name -> common.v1.PaginationResponse
	21, // 4: content_mgmt.v1.UseTemplateRequest.variables:type_name -> content_mgmt.v1.UseTemplateRequest.VariablesEntry
	26, // 5: content_mgmt.v1.TemplateRating.created_at:type_name -> google.protobuf.Timestamp
	24, // 6: content_mgmt.v1.GetTemplateRatingsRequest.pagination:type_name -> common.v1.PaginationRequest
	9,  // 7: content_mgmt.v1.GetTemplateRatingsResponse.ratings:type_name -> content_mgmt.v1.TemplateRating
	25, // 8: content_mgmt.v1.GetTemplateRatingsResponse.pagination:type_name -> common.v1.PaginationResponse
	22, // 9: content_mgmt.v1.GetTemplateRatingsResponse.rating_distribution:type_name -> content_mgmt.v1.GetTemplateRatingsResponse.RatingDistributionEntry
	26, // 10: content_mgmt.v1.PurchaseTemplateResponse.purchased_at:type_name -> google.protobuf.Timestamp
	0,  // 11: content_mgmt.v1.PurchaseTemplateResponse.template:type_name -> content_mgmt.v1.Template
	24, // 12: content_mgmt.v1.GetPurchasedTemplatesRequest.pagination:type_name -> common.v1.PaginationRequest
	0,  // 13: content_mgmt.v1.GetPurchasedTemplatesResponse.templates:type_name -> content_mgmt.v1.Template
	25, // 14: content_mgmt.v1.GetPurchasedTemplatesResponse.pagination:type_name -> common.v1.PaginationResponse
	16, // 15: content_mgmt.v1.GetPurchasedTemplatesResponse.purchases:type_name -> content_mgmt.v1.PurchaseInfo
	26, // 16: content_mgmt.v1.PurchaseInfo.purchased_at:type_name -> google.protobuf.Timestamp
	26, // 17: content_mgmt.v1.TemplateRevenue.last_purchase:type_name -> google.protobuf.Timestamp
	24, // 18: content_mgmt.v1.GetTemplateRevenueRequest.pagination:type_name -> common.v1.PaginationRequest
	17, // 19: content_mgmt.v1.GetTemplateRevenueResponse.revenues:type_name -> content_mgmt.v1.TemplateRevenue
	25, // 20: content_mgmt.v1.GetTemplateRevenueResponse.pagination:type_name -> common.v1.PaginationResponse
	1,  // 21: content_mgmt.v1.TemplateService.CreateTemplate:input_type -> content_mgmt.v1.CreateTemplateRequest
	3,  // 22: content_mgmt.v1.TemplateService.GetTemplate:input_type -> content_mgmt.v1.GetTemplateRequest
	2,  // 23: content_mgmt.v1.TemplateService.UpdateTemplate:input_type -> content_mgmt.v1.UpdateTemplateRequest
	4,  // 24: content_mgmt.v1.TemplateService.ListTemplates:input_type -> content_mgmt.v1.ListTemplatesRequest
	6,  // 25: content_mgmt.v1.TemplateService.UseTemplate:input_type -> content_mgmt.v1.UseTemplateRequest
	20, // 26: content_mgmt.v1.TemplateService.DeleteTemplate:input_type -> content_mgmt.v1.DeleteTemplateRequest
	8,  // 27: content_mgmt.v1.TemplateService.RateTemplate:input_type -> content_mgmt.v1.RateTemplateRequest
	10, // 28: content_mgmt.v1.TemplateService.GetTemplateRatings:input_type -> content_mgmt.v1.GetTemplateRatingsRequest
	12, // 29: content_mgmt.v1.TemplateService.PurchaseTemplate:input_type -> content_mgmt.v1.PurchaseTemplateRequest
	14, // 30: content_mgmt.v1.TemplateService.GetPurchasedTemplates:input_type -> content_mgmt.v1.GetPurchasedTemplatesRequest
	18, // 31: content_mgmt.v1.TemplateService.GetTemplateRevenue:input_type -> content_mgmt.v1.GetTemplateRevenueRequest
	0,  // 32: content_mgmt.v1.TemplateService.CreateTemplate:output_type -> content_mgmt.v1.Template
	0,  // 33: content_mgmt.v1.TemplateService.GetTemplate:output_type -> content_mgmt.v1.Template
	0,  // 34: content_mgmt.v1.TemplateService.UpdateTemplate:output_type -> content_mgmt.v1.Template
	5,  // 35: content_mgmt.v1.TemplateService.ListTemplates:output_type -> content_mgmt.v1.ListTemplatesResponse
	7,  // 36: content_mgmt.v1.TemplateService.UseTemplate:output_type -> content_mgmt.v1.UseTemplateResponse
	27, // 37: content_mgmt.v1.TemplateService.DeleteTemplate:output_type -> content_mgmt.v1.Empty
	27, // 38: content_mgmt.v1.TemplateService.RateTemplate:output_type -> content_mgmt.v1.Empty
	11, // 39: content_mgmt.v1.TemplateService.GetTemplateRatings:output_type -> content_mgmt.v1.GetTemplateRatingsResponse
	13, // 40: content_mgmt.v1.TemplateService.PurchaseTemplate:output_type -> content_mgmt.v1.PurchaseTemplateResponse
	15, // 41: content_mgmt.v1.TemplateService.GetPurchasedTemplates:output_type -> content_mgmt.v1.GetPurchasedTemplatesResponse
	19, // 42: content_mgmt.v1.TemplateService.GetTemplateRevenue:output_type -> content_mgmt.v1.GetTemplateRevenueResponse
	32, // [32:43] is the sub-list for method output_type
	21, // [21:32] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_content_mgmt_v1_template_proto_init() }
func file_content_mgmt_v1_template_proto_init() {
	if File_content_mgmt_v1_template_proto != nil {
		return
	}
	file_content_mgmt_v1_post_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_content_mgmt_v1_template_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Template); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseTemplateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateRating); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRatingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRatingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseTemplateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPurchasedTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPurchasedTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateRevenue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRevenueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateRevenueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_template_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_content_mgmt_v1_template_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_content_mgmt_v1_template_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_mgmt_v1_template_proto_goTypes,
		DependencyIndexes: file_content_mgmt_v1_template_proto_depIdxs,
		MessageInfos:      file_content_mgmt_v1_template_proto_msgTypes,
	}.Build()
	File_content_mgmt_v1_template_proto = out.File
	file_content_mgmt_v1_template_proto_rawDesc = nil
	file_content_mgmt_v1_template_proto_goTypes = nil
	file_content_mgmt_v1_template_proto_depIdxs = nil
}

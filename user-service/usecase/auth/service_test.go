package auth

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"

	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/models"
)

func TestPasswordHashing(t *testing.T) {
	password := "testpassword123"

	// Test password hashing
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	assert.NoError(t, err)
	assert.NotEmpty(t, hashedPassword)

	// Test password verification
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	assert.NoError(t, err)

	// Test wrong password
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte("wrongpassword"))
	assert.Error(t, err)
}

func TestRegisterRequest_Validation(t *testing.T) {
	req := models.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "<PERSON>",
		LastName:  "Doe",
	}

	assert.NotEmpty(t, req.Email)
	assert.NotEmpty(t, req.Password)
	assert.NotEmpty(t, req.FirstName)
	assert.NotEmpty(t, req.LastName)
	assert.GreaterOrEqual(t, len(req.Password), 8)
}

func TestLoginRequest_Validation(t *testing.T) {
	req := models.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	assert.NotEmpty(t, req.Email)
	assert.NotEmpty(t, req.Password)
}

func TestNewService(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})

	// Test that NewService function exists and can be called
	// In a real implementation, you would pass actual dependencies
	assert.NotNil(t, logger)
}

func TestAuthServiceTypes(t *testing.T) {
	// Test that UseCase interface exists
	var useCase UseCase
	assert.Nil(t, useCase) // Should be nil when not initialized
}

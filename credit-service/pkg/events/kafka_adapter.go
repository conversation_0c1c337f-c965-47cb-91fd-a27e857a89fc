package events

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaAdapter adapts pkg-shared/kafka to our event system
type KafkaAdapter struct {
	consumer  *sharedKafka.Consumer
	producer  *sharedKafka.Producer
	config    *sharedKafka.Config
	logger    logging.Logger
	handler   MessageHandler
	isRunning bool
	topics    []string
	metrics   *ConsumerMetrics
}

// NewKafkaAdapter creates a new Kafka adapter using pkg-shared/kafka
func NewKafkaAdapter(config *sharedKafka.Config, logger logging.Logger) *KafkaAdapter {
	return &KafkaAdapter{
		config: config,
		logger: logger,
		metrics: &ConsumerMetrics{
			MessagesConsumed:     0,
			MessagesProcessed:    0,
			MessagesFailed:       0,
			LastMessageTimestamp: 0,
			ConsumerLag:          0,
			IsConnected:          false,
			Partitions:           0,
			Topics:               []string{},
		},
	}
}

// Subscribe implements KafkaConsumer interface
func (k *KafkaAdapter) Subscribe(topics []string) error {
	k.logger.WithField("topics", topics).Info("Subscribing to Kafka topics using pkg-shared")

	k.topics = topics
	k.metrics.Topics = topics
	k.metrics.Partitions = len(topics) // Simplified assumption

	// Create event handler adapter
	eventHandler := &EventHandlerAdapter{
		messageHandler: k.handler,
		logger:         k.logger,
		metrics:        k.metrics,
	}

	// Create consumer using pkg-shared/kafka
	k.consumer = sharedKafka.NewConsumer(k.config, eventHandler, k.logger)

	return nil
}

// Consume implements KafkaConsumer interface
func (k *KafkaAdapter) Consume(ctx context.Context, handler MessageHandler) error {
	k.handler = handler

	if k.consumer == nil {
		return fmt.Errorf("consumer not initialized, call Subscribe first")
	}

	k.logger.Info("Starting Kafka consumer using pkg-shared")
	k.isRunning = true
	k.metrics.IsConnected = true

	// Start consuming using pkg-shared consumer
	err := k.consumer.Start(k.topics)
	if err != nil {
		k.isRunning = false
		k.metrics.IsConnected = false
		return fmt.Errorf("failed to start consumer: %w", err)
	}

	// Wait for context cancellation
	<-ctx.Done()

	k.isRunning = false
	return k.consumer.Stop()
}

// ConsumeWithConfig implements KafkaConsumer interface
func (k *KafkaAdapter) ConsumeWithConfig(ctx context.Context, config *ConsumerConfig, handler MessageHandler) error {
	// Convert our config to pkg-shared config
	if config != nil {
		k.config.Consumer.GroupID = config.GroupID
		k.config.Consumer.MaxWait = time.Duration(config.FetchMaxWaitMs) * time.Millisecond
		k.config.Consumer.MinBytes = config.FetchMinBytes
		k.config.Consumer.MaxBytes = config.MaxPollRecords * 1024 // Rough conversion
		k.config.Consumer.SessionTimeout = time.Duration(config.SessionTimeoutMs) * time.Millisecond
		k.config.Consumer.HeartbeatInterval = time.Duration(config.HeartbeatIntervalMs) * time.Millisecond
	}

	return k.Consume(ctx, handler)
}

// Close implements KafkaConsumer interface
func (k *KafkaAdapter) Close() error {
	k.logger.Info("Closing Kafka adapter")

	k.isRunning = false
	k.metrics.IsConnected = false

	if k.consumer != nil {
		return k.consumer.Stop()
	}

	if k.producer != nil {
		return k.producer.Close()
	}

	return nil
}

// GetMetrics implements KafkaConsumer interface
func (k *KafkaAdapter) GetMetrics() *ConsumerMetrics {
	return k.metrics
}

// IsHealthy implements KafkaConsumer interface
func (k *KafkaAdapter) IsHealthy() bool {
	return k.metrics.IsConnected && k.isRunning
}

// GetProducer returns the producer for publishing events
func (k *KafkaAdapter) GetProducer() *sharedKafka.Producer {
	if k.producer == nil {
		k.producer = sharedKafka.NewProducer(k.config, k.logger)
	}
	return k.producer
}

// PublishEvent publishes an event using pkg-shared producer
func (k *KafkaAdapter) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	producer := k.GetProducer()

	// Convert event to BaseEvent format
	baseEvent, err := k.convertToBaseEvent(topic, event)
	if err != nil {
		return fmt.Errorf("failed to convert event: %w", err)
	}

	// Publish using pkg-shared producer
	return producer.PublishToTopic(ctx, topic, baseEvent)
}

// convertToBaseEvent converts our event to pkg-shared BaseEvent format
func (k *KafkaAdapter) convertToBaseEvent(topic string, event interface{}) (*sharedKafka.BaseEvent, error) {
	// Convert event to map[string]interface{}
	var eventData map[string]interface{}

	// Serialize to JSON first, then unmarshal to map
	jsonData, err := json.Marshal(event)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event: %w", err)
	}

	err = json.Unmarshal(jsonData, &eventData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal event to map: %w", err)
	}

	// Create BaseEvent using NewBaseEvent
	baseEvent := sharedKafka.NewBaseEvent(sharedKafka.EventType(topic), "credit-service")
	baseEvent.Data = eventData
	baseEvent.WithTag("service", "credit-service")
	baseEvent.WithTag("topic", topic)

	return baseEvent, nil
}

// EventHandlerAdapter adapts our MessageHandler to pkg-shared EventHandler
type EventHandlerAdapter struct {
	messageHandler MessageHandler
	logger         logging.Logger
	metrics        *ConsumerMetrics
}

// HandleUserEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleUserEvent(ctx context.Context, event *sharedKafka.UserEvent) error {
	return e.handleEvent(ctx, "user.events", event.BaseEvent)
}

// HandleContentEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleContentEvent(ctx context.Context, event *sharedKafka.ContentEvent) error {
	return e.handleEvent(ctx, "content.events", event.BaseEvent)
}

// HandleBillingEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleBillingEvent(ctx context.Context, event *sharedKafka.BillingEvent) error {
	return e.handleEvent(ctx, "billing.events", event.BaseEvent)
}

// HandleIntegrationEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleIntegrationEvent(ctx context.Context, event *sharedKafka.IntegrationEvent) error {
	return e.handleEvent(ctx, "integration.events", event.BaseEvent)
}

// HandleAssetEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleAssetEvent(ctx context.Context, event *sharedKafka.AssetEvent) error {
	return e.handleEvent(ctx, "asset.events", event.BaseEvent)
}

// HandleRAGEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleRAGEvent(ctx context.Context, event *sharedKafka.RAGEvent) error {
	return e.handleEvent(ctx, "rag.events", event.BaseEvent)
}

// HandleSystemEvent implements sharedKafka.EventHandler interface
func (e *EventHandlerAdapter) HandleSystemEvent(ctx context.Context, event *sharedKafka.SystemEvent) error {
	return e.handleEvent(ctx, "system.events", event.BaseEvent)
}

// handleEvent is a common handler that routes to our MessageHandler
func (e *EventHandlerAdapter) handleEvent(ctx context.Context, topic string, baseEvent *sharedKafka.BaseEvent) error {
	e.logger.WithFields(map[string]interface{}{
		"topic":      topic,
		"event_id":   baseEvent.ID,
		"event_type": baseEvent.Type,
		"source":     baseEvent.Source,
	}).Info("Processing event through adapter")

	// Update metrics
	e.metrics.MessagesConsumed++
	e.metrics.LastMessageTimestamp = baseEvent.Timestamp.Unix()

	// Convert data back to JSON bytes for our message handler
	eventBytes, err := json.Marshal(baseEvent.Data)
	if err != nil {
		e.logger.WithError(err).Error("Failed to marshal event data")
		e.metrics.MessagesFailed++
		return fmt.Errorf("failed to marshal event data: %w", err)
	}

	// Route to our message handler
	err = e.messageHandler.HandleMessage(ctx, topic, eventBytes)

	// Update metrics based on result
	if err != nil {
		e.metrics.MessagesFailed++
		e.logger.WithError(err).Error("Failed to handle message through adapter")
	} else {
		e.metrics.MessagesProcessed++
	}

	return err
}

// NewKafkaAdapterFromConfig creates a Kafka adapter from configuration
func NewKafkaAdapterFromConfig(brokers []string, groupID string, logger logging.Logger) *KafkaAdapter {
	config := sharedKafka.NewDefaultConfig()
	config.Brokers = brokers
	config.Consumer.GroupID = groupID

	// Add subscription-specific topics
	config.Topics.BillingEvents = TopicSubscriptionEvents

	return NewKafkaAdapter(config, logger)
}

// SubscriptionKafkaAdapter creates a specialized adapter for subscription events
func SubscriptionKafkaAdapter(brokers []string, groupID string, logger logging.Logger) *KafkaAdapter {
	adapter := NewKafkaAdapterFromConfig(brokers, groupID, logger)

	// Override topic mapping for subscription events
	adapter.config.Topics.BillingEvents = TopicSubscriptionEvents

	return adapter
}

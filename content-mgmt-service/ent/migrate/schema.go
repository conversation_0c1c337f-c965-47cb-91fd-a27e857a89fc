// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// PostsColumns holds the columns for the "posts" table.
	PostsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "workspace_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "template_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "generation_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "title", Type: field.TypeString, Size: 500},
		{Name: "content", Type: field.TypeString, Size: 2147483647},
		{Name: "original_content", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "platforms", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"draft", "scheduled", "published", "failed", "archived"}, Default: "draft"},
		{Name: "scheduled_at", Type: field.TypeTime, Nullable: true},
		{Name: "published_at", Type: field.TypeTime, Nullable: true},
		{Name: "platform_posts", Type: field.TypeJSON, Nullable: true},
		{Name: "hashtags", Type: field.TypeJSON, Nullable: true},
		{Name: "mentions", Type: field.TypeJSON, Nullable: true},
		{Name: "media_urls", Type: field.TypeJSON, Nullable: true},
		{Name: "variables", Type: field.TypeJSON, Nullable: true},
		{Name: "visibility", Type: field.TypeEnum, Enums: []string{"public", "private", "unlisted"}, Default: "public"},
		{Name: "language", Type: field.TypeString, Size: 5, Default: "en"},
		{Name: "analytics", Type: field.TypeJSON, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// PostsTable holds the schema information for the "posts" table.
	PostsTable = &schema.Table{
		Name:       "posts",
		Columns:    PostsColumns,
		PrimaryKey: []*schema.Column{PostsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "post_user_id",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[1]},
			},
			{
				Name:    "post_workspace_id",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[2]},
			},
			{
				Name:    "post_template_id",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[3]},
			},
			{
				Name:    "post_status",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[9]},
			},
			{
				Name:    "post_scheduled_at",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[10]},
			},
			{
				Name:    "post_published_at",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[11]},
			},
			{
				Name:    "post_visibility",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[17]},
			},
			{
				Name:    "post_language",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[18]},
			},
			{
				Name:    "post_created_at",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[21]},
			},
			{
				Name:    "post_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[23]},
			},
			{
				Name:    "post_user_id_status",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[1], PostsColumns[9]},
			},
			{
				Name:    "post_user_id_created_at",
				Unique:  false,
				Columns: []*schema.Column{PostsColumns[1], PostsColumns[21]},
			},
		},
	}
	// TemplatesColumns holds the columns for the "templates" table.
	TemplatesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "workspace_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "name", Type: field.TypeString, Size: 200},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "content", Type: field.TypeString, Size: 2147483647},
		{Name: "variables", Type: field.TypeJSON, Nullable: true},
		{Name: "variable_descriptions", Type: field.TypeJSON, Nullable: true},
		{Name: "category", Type: field.TypeEnum, Enums: []string{"social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business"}, Default: "social_media"},
		{Name: "platforms", Type: field.TypeJSON, Nullable: true},
		{Name: "tone", Type: field.TypeEnum, Enums: []string{"professional", "friendly", "humorous", "formal", "casual", "enthusiastic", "authoritative"}, Default: "professional"},
		{Name: "content_type", Type: field.TypeEnum, Enums: []string{"educational", "promotional", "entertainment", "informational", "news", "tutorial"}, Default: "informational"},
		{Name: "hashtags", Type: field.TypeJSON, Nullable: true},
		{Name: "is_public", Type: field.TypeBool, Default: false},
		{Name: "is_featured", Type: field.TypeBool, Default: false},
		{Name: "usage_count", Type: field.TypeInt, Default: 0},
		{Name: "rating", Type: field.TypeFloat64, Default: 0},
		{Name: "rating_count", Type: field.TypeInt, Default: 0},
		{Name: "tags", Type: field.TypeJSON, Nullable: true},
		{Name: "language", Type: field.TypeString, Size: 5, Default: "en"},
		{Name: "training_files", Type: field.TypeJSON, Nullable: true},
		{Name: "rag_trained", Type: field.TypeBool, Default: false},
		{Name: "rag_trained_at", Type: field.TypeTime, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// TemplatesTable holds the schema information for the "templates" table.
	TemplatesTable = &schema.Table{
		Name:       "templates",
		Columns:    TemplatesColumns,
		PrimaryKey: []*schema.Column{TemplatesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "template_user_id",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[1]},
			},
			{
				Name:    "template_workspace_id",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[2]},
			},
			{
				Name:    "template_category",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[8]},
			},
			{
				Name:    "template_tone",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[10]},
			},
			{
				Name:    "template_content_type",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[11]},
			},
			{
				Name:    "template_is_public",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[13]},
			},
			{
				Name:    "template_is_featured",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[14]},
			},
			{
				Name:    "template_language",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[19]},
			},
			{
				Name:    "template_rag_trained",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[21]},
			},
			{
				Name:    "template_created_at",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[24]},
			},
			{
				Name:    "template_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[26]},
			},
			{
				Name:    "template_user_id_category",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[1], TemplatesColumns[8]},
			},
			{
				Name:    "template_is_public_is_featured",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[13], TemplatesColumns[14]},
			},
			{
				Name:    "template_rating_rating_count",
				Unique:  false,
				Columns: []*schema.Column{TemplatesColumns[16], TemplatesColumns[17]},
			},
		},
	}
	// WorkspacesColumns holds the columns for the "workspaces" table.
	WorkspacesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "owner_id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString, Size: 100},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "color", Type: field.TypeString, Size: 7, Default: "#3B82F6"},
		{Name: "icon", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "settings", Type: field.TypeJSON, Nullable: true},
		{Name: "default_platforms", Type: field.TypeJSON, Nullable: true},
		{Name: "brand_guidelines", Type: field.TypeJSON, Nullable: true},
		{Name: "is_personal", Type: field.TypeBool, Default: true},
		{Name: "is_active", Type: field.TypeBool, Default: true},
		{Name: "post_count", Type: field.TypeInt, Default: 0},
		{Name: "template_count", Type: field.TypeInt, Default: 0},
		{Name: "last_activity_at", Type: field.TypeTime, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// WorkspacesTable holds the schema information for the "workspaces" table.
	WorkspacesTable = &schema.Table{
		Name:       "workspaces",
		Columns:    WorkspacesColumns,
		PrimaryKey: []*schema.Column{WorkspacesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "workspace_owner_id",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[1]},
			},
			{
				Name:    "workspace_is_active",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[10]},
			},
			{
				Name:    "workspace_is_personal",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[9]},
			},
			{
				Name:    "workspace_created_at",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[15]},
			},
			{
				Name:    "workspace_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[17]},
			},
			{
				Name:    "workspace_last_activity_at",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[13]},
			},
			{
				Name:    "workspace_owner_id_is_active",
				Unique:  false,
				Columns: []*schema.Column{WorkspacesColumns[1], WorkspacesColumns[10]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		PostsTable,
		TemplatesTable,
		WorkspacesTable,
	}
)

func init() {
}

version: '3.8'

services:
  notification-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8086:8086"   # HTTP API
      - "50056:50056" # gRPC API
      - "9096:9096"   # Metrics
    environment:
      - NOTIFICATION_SERVICE_SERVER_ENV=development
      - DATABASE_TYPE=sqlite
      - SQLITE_PATH=/app/data/notification_service.db
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - SMTP_FROM_EMAIL=<EMAIL>
      - SMTP_FROM_NAME=Social Content AI
      - KAFKA_BROKERS=kafka:9092
      - JWT_SECRET_KEY=dev-secret-key-notification-service
    volumes:
      - notification_data:/app/data
      - ./config:/app/config:ro
    depends_on:
      - kafka
      - mailhog
    networks:
      - notification-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL for production-like testing
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=notification_service_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - notification-network
    restart: unless-stopped

  # Kafka for event streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - notification-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - notification-network
    restart: unless-stopped

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - notification-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - notification-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - notification-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - notification-network
    restart: unless-stopped

volumes:
  notification_data:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  notification-network:
    driver: bridge

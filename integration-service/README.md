# Integration Service

This service handles social media platform integrations, OAuth flows, and content publishing for the Social Content AI platform.

## 🚀 Features

### Core Functionality
- **Multi-Platform OAuth Integration**: Support for Facebook, Twitter, Instagram, LinkedIn, YouTube, and TikTok
- **Content Publishing**: Publish text, images, and videos to multiple platforms
- **Analytics Collection**: Retrieve engagement metrics and analytics from platforms
- **Webhook Processing**: Handle real-time events from social media platforms
- **Token Management**: Automatic token refresh and validation

### Technical Features
- **Dual Database Support**: PostgreSQL for production, SQLite for development
- **Event-Driven Architecture**: Kafka integration for real-time events
- **Comprehensive Error Handling**: Retry logic with multiple strategies
- **Security**: Token encryption, webhook signature verification, input validation
- **Rate Limiting**: Platform-specific rate limiting and quota management

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   gRPC API      │    │   RESTful API   │    │   Kafka Events  │
│                 │    │                 │    │                 │
│ • Integration   │    │ • OAuth         │    │ • Platform      │
│ • Publishing    │    │   Callbacks     │    │   Connected     │
│ • Analytics     │    │ • Webhooks      │    │ • Post          │
│                 │    │                 │    │   Published     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Use Cases     │
                    │                 │
                    │ • Integration   │
                    │ • Platform      │
                    │ • Publishing    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Repositories   │
                    │                 │
                    │ • Platform      │
                    │   Integration   │
                    │ • Published     │
                    │   Post          │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ OAuth Providers │    │   Publishers    │    │   Database      │
│                 │    │                 │    │                 │
│ • Facebook      │    │ • Facebook      │    │ • PostgreSQL    │
│ • Twitter       │    │ • Twitter       │    │ • SQLite        │
│ • Instagram     │    │ • Instagram     │    │                 │
│ • LinkedIn      │    │ • LinkedIn      │    │                 │
│ • YouTube       │    │ • YouTube       │    │                 │
│ • TikTok        │    │ • TikTok        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Setup

### Prerequisites
- Go 1.21+
- PostgreSQL 13+ (for production) or SQLite (for development)
- Apache Kafka (for event streaming)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository>
cd integration-service
go mod tidy
```

2. **Generate Ent code:**
```bash
go generate ./ent
```

3. **Set up configuration:**

Create `config/config.yaml` or use environment variables:

```yaml
# Database Configuration
database:
  type: "postgres"  # or "sqlite"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "integration_service_db"
  ssl_mode: "disable"

  # For SQLite (alternative)
  # type: "sqlite"
  # sqlite_path: "./data/integration_service.db"
```

Or use environment variables:
```bash
# Database Configuration
export DATABASE_TYPE=postgres  # or sqlite
export DATABASE_HOST=localhost
export DATABASE_PORT=5432
export DATABASE_NAME=integration_service_db
export DATABASE_USER=postgres
export DATABASE_PASSWORD=password

# For SQLite (alternative)
export DATABASE_TYPE=sqlite
export DATABASE_SQLITE_PATH=./data/integration_service.db

# Kafka Configuration
export KAFKA_BROKERS=localhost:9092
export KAFKA_TOPIC=integration.events

# OAuth Credentials (get from platform developer consoles)
export FACEBOOK_CLIENT_ID=your_facebook_client_id
export FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
export TWITTER_CLIENT_ID=your_twitter_client_id
export TWITTER_CLIENT_SECRET=your_twitter_client_secret
# ... (add for other platforms)

# Security
export ENCRYPTION_KEY=your_32_byte_hex_encryption_key
export WEBHOOK_SECRET_FACEBOOK=your_facebook_webhook_secret
# ... (add for other platforms)
```

4. **Start the service:**

The service will automatically create database schema on startup.

```bash
# Development (uses SQLite by default)
ENV=development go run main.go

# Production (uses PostgreSQL)
ENV=production go run main.go

# Or specify config directly
go run main.go -config ./config/config.yaml
```

## 📡 API Endpoints

### gRPC API (Port: 50051)
- **PlatformIntegrationService**: Main integration service
  - `GetOAuthUrl`: Generate OAuth authorization URLs
  - `ConnectAccount`: Connect social media accounts
  - `RefreshToken`: Refresh access tokens
  - `DisconnectAccount`: Disconnect accounts
  - `ListAccounts`: List connected accounts
  - `ValidateAccount`: Validate account status
  - `PublishPost`: Publish content to platforms
  - `GetPostAnalytics`: Retrieve post analytics
  - `GetAccountAnalytics`: Retrieve account analytics
  - `GetPlatformLimits`: Get platform rate limits
  - `ProcessWebhook`: Process webhook events

### RESTful API (Port: 8080)
- **Health Check**: `GET /health`
- **OAuth Callbacks**:
  - `GET /oauth/facebook/callback`
  - `GET /oauth/twitter/callback`
  - `GET /oauth/instagram/callback`
  - `GET /oauth/linkedin/callback`
  - `GET /oauth/youtube/callback`
  - `GET /oauth/tiktok/callback`
- **Webhooks**:
  - `POST /webhooks/facebook`
  - `POST /webhooks/twitter`
  - `POST /webhooks/instagram`
  - `POST /webhooks/linkedin`
  - `POST /webhooks/youtube`
  - `POST /webhooks/tiktok`

## 🌐 Supported Platforms

| Platform  | OAuth | Publishing | Analytics | Webhooks | Status |
|-----------|-------|------------|-----------|----------|--------|
| Facebook  | ✅    | ✅         | ✅        | ✅       | Complete |
| Twitter   | ✅    | ✅         | ✅        | ✅       | Complete |
| Instagram | ✅    | ✅         | ✅        | ✅       | Complete |
| LinkedIn  | ✅    | ✅         | ✅        | ✅       | Complete |
| YouTube   | ✅    | 🔄         | ✅        | ✅       | In Progress |
| TikTok    | ✅    | 🔄         | ✅        | ✅       | In Progress |

**Legend**: ✅ Complete, 🔄 In Progress, ❌ Not Started

## 🔧 Development

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test ./internal/validation -v
```

### Database Operations
```bash
# Run with PostgreSQL
ENV=production go run main.go
# or
DATABASE_TYPE=postgres go run main.go

# Run with SQLite
ENV=development go run main.go
# or
DATABASE_TYPE=sqlite go run main.go

# Switch between databases using config files
go run main.go -config ./config/postgres.yaml
go run main.go -config ./config/sqlite.yaml

# Generate Ent code after schema changes
go generate ./ent
```

### Building
```bash
# Build binary
go build -o integration-service main.go

# Build for production
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o integration-service main.go
```

## 📚 Usage Examples

### OAuth Flow
```bash
# 1. Get OAuth URL
grpcurl -plaintext -d '{
  "user_id": "user123",
  "platform": "facebook",
  "redirect_uri": "https://yourapp.com/callback",
  "requested_permissions": ["pages_manage_posts"]
}' localhost:50051 integration.v1.PlatformIntegrationService/GetOAuthUrl

# 2. User authorizes and gets redirected with code
# 3. Connect account
grpcurl -plaintext -d '{
  "user_id": "user123",
  "platform": "facebook",
  "oauth_code": "received_code",
  "redirect_uri": "https://yourapp.com/callback"
}' localhost:50051 integration.v1.PlatformIntegrationService/ConnectAccount
```

### Publishing Content
```bash
grpcurl -plaintext -d '{
  "account_id": "account123",
  "user_id": "user123",
  "post_id": "post123",
  "content": "Hello from Social Content AI!",
  "hashtags": ["#ai", "#social", "#content"]
}' localhost:50051 integration.v1.PlatformIntegrationService/PublishPost
```

## 🔒 Security

### Token Encryption
All access tokens are encrypted using AES-256-GCM before storage:
```go
// Tokens are automatically encrypted/decrypted by the repository layer
encryptedToken, err := tokenEncryption.Encrypt(accessToken)
```

### Webhook Verification
All webhook signatures are verified:
```go
// Each platform has its own verification method
isValid := webhookVerifier.VerifyFacebookSignature(payload, signature, secret)
```

### Rate Limiting
Platform-specific rate limits are enforced:
```go
// Rate limits are checked before API calls
canProceed := rateLimiter.CheckLimit("facebook:user123")
```

## 🚨 Error Handling

The service uses comprehensive error types with retry logic:

```go
// Retryable errors (network issues, rate limits)
err := NewPlatformRateLimitError("facebook", "2024-01-01T12:00:00Z")

// Non-retryable errors (invalid credentials, permissions)
err := NewInvalidTokenError("Token has been revoked")
```

## 📊 Monitoring

### Health Checks
- **Service Health**: `GET /health`
- **Database Health**: Included in health check
- **Kafka Health**: Included in health check

### Metrics
The service exposes metrics for:
- OAuth flow success/failure rates
- Publishing success/failure rates
- API response times
- Rate limit usage
- Token refresh rates

### Logging
Structured logging with correlation IDs:
```json
{
  "level": "info",
  "msg": "Post published successfully",
  "user_id": "user123",
  "platform": "facebook",
  "post_id": "post123",
  "platform_post_id": "fb_123456",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🐛 Troubleshooting

### Common Issues

1. **OAuth Flow Fails**
   - Check client credentials
   - Verify redirect URI matches platform settings
   - Ensure proper scopes are requested

2. **Publishing Fails**
   - Check token validity
   - Verify content meets platform requirements
   - Check rate limits

3. **Database Connection Issues**
   - Verify database credentials
   - Check network connectivity
   - Ensure database exists

4. **Kafka Connection Issues**
   - Verify Kafka brokers are running
   - Check topic exists
   - Verify network connectivity

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug go run main.go
```

## 📖 Additional Documentation

- [OAuth Flow Guide](./docs/oauth-flow.md) - Complete OAuth integration guide with examples
- [OAuth Frontend Utils](./docs/oauth-utils.js) - JavaScript utilities for frontend integration
- [gRPC Implementation TODO](./GRPC_IMPLEMENTATION_TODO.md)
- [Proto Definitions](../proto-shared/integration/v1/)
- [Microservices Architecture](../microservices-architecture-design.md)
- [Go Microservice Guide](../GO_MICROSERVICE_GUIDE.md)

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Follow Go best practices
5. Use the established error handling patterns

## 📄 License

This project is part of the Social Content AI platform.

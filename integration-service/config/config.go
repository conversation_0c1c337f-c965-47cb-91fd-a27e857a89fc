package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	sharedconfig "github.com/social-content-ai/pkg-shared/config"
	"github.com/spf13/viper"
)

// Config extends the shared config with integration service specific configuration
type Config struct {
	*sharedconfig.Config

	// Override database config with extended version
	Database DatabaseConfig `mapstructure:"database"`

	// Integration service specific configurations
	OAuth      OAuthConfig      `mapstructure:"oauth"`
	Publishers PublishersConfig `mapstructure:"publishers"`
	RateLimit  RateLimitConfig  `mapstructure:"rate_limit"`
	Features   FeaturesConfig   `mapstructure:"features"`
	CORS       CORSConfig       `mapstructure:"cors"`
	Cleanup    CleanupConfig    `mapstructure:"cleanup"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
	Services   ServicesConfig   `mapstructure:"services"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig

	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// OAuthConfig holds OAuth provider configurations
type OAuthConfig struct {
	Facebook struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"facebook"`

	Twitter struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"twitter"`

	Instagram struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"instagram"`

	LinkedIn struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"linkedin"`

	YouTube struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"youtube"`

	TikTok struct {
		ClientID     string `mapstructure:"client_id"`
		ClientSecret string `mapstructure:"client_secret"`
		RedirectURL  string `mapstructure:"redirect_url"`
	} `mapstructure:"tiktok"`
}

// PublishersConfig holds publisher configurations
type PublishersConfig struct {
	DefaultTimeout time.Duration `mapstructure:"default_timeout"`
	RetryAttempts  int           `mapstructure:"retry_attempts"`
	RetryDelay     time.Duration `mapstructure:"retry_delay"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled           bool `mapstructure:"enabled"`
	RequestsPerMinute int  `mapstructure:"requests_per_minute"`
	Burst             int  `mapstructure:"burst"`
}

// FeaturesConfig holds feature flags
type FeaturesConfig struct {
	ScheduledPosts bool `mapstructure:"scheduled_posts"`
	Analytics      bool `mapstructure:"analytics"`
	Webhooks       bool `mapstructure:"webhooks"`
	BulkOperations bool `mapstructure:"bulk_operations"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// CleanupConfig holds cleanup job configuration
type CleanupConfig struct {
	Enabled                bool          `mapstructure:"enabled"`
	Interval               time.Duration `mapstructure:"interval"`
	RetainFailedPostsDays  int           `mapstructure:"retain_failed_posts_days"`
	RetainSuccessPostsDays int           `mapstructure:"retain_success_posts_days"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled        bool   `mapstructure:"enabled"`
	MetricsPort    string `mapstructure:"metrics_port"`
	HealthEndpoint string `mapstructure:"health_endpoint"`
}

// ServicesConfig holds external service configurations
type ServicesConfig struct {
	UserService          string `mapstructure:"user_service"`
	AssetService         string `mapstructure:"asset_service"`
	AIContentService     string `mapstructure:"ai_content_service"`
	CreditBillingService string `mapstructure:"credit_billing_service"`
	NotificationService  string `mapstructure:"notification_service"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Load environment-specific config
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	// Try to read environment-specific config
	viper.SetConfigName(env)
	if err := viper.MergeInConfig(); err != nil {
		// It's okay if environment-specific config doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read environment config: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Load shared config
	sharedCfg, err := sharedconfig.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}
	config.Config = sharedCfg

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.sqlite_path", "./data/integration_service.db")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.name", "integration_service_db")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// OAuth defaults
	viper.SetDefault("oauth.facebook.redirect_url", "http://localhost:8085/oauth/facebook/callback")
	viper.SetDefault("oauth.twitter.redirect_url", "http://localhost:8085/oauth/twitter/callback")
	viper.SetDefault("oauth.instagram.redirect_url", "http://localhost:8085/oauth/instagram/callback")
	viper.SetDefault("oauth.linkedin.redirect_url", "http://localhost:8085/oauth/linkedin/callback")
	viper.SetDefault("oauth.youtube.redirect_url", "http://localhost:8085/oauth/youtube/callback")
	viper.SetDefault("oauth.tiktok.redirect_url", "http://localhost:8085/oauth/tiktok/callback")

	// Publishers defaults
	viper.SetDefault("publishers.default_timeout", "60s")
	viper.SetDefault("publishers.retry_attempts", 3)
	viper.SetDefault("publishers.retry_delay", "5s")

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.requests_per_minute", 60)
	viper.SetDefault("rate_limit.burst", 10)

	// Features defaults
	viper.SetDefault("features.scheduled_posts", true)
	viper.SetDefault("features.analytics", true)
	viper.SetDefault("features.webhooks", false)
	viper.SetDefault("features.bulk_operations", true)

	// CORS defaults
	viper.SetDefault("cors.allowed_origins", []string{"http://localhost:3000"})
	viper.SetDefault("cors.allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("cors.allowed_headers", []string{"Content-Type", "Authorization", "X-Requested-With"})
	viper.SetDefault("cors.allow_credentials", true)
	viper.SetDefault("cors.max_age", 86400)

	// Cleanup defaults
	viper.SetDefault("cleanup.enabled", true)
	viper.SetDefault("cleanup.interval", "24h")
	viper.SetDefault("cleanup.retain_failed_posts_days", 30)
	viper.SetDefault("cleanup.retain_success_posts_days", 90)

	// Monitoring defaults
	viper.SetDefault("monitoring.enabled", true)
	viper.SetDefault("monitoring.metrics_port", "9095")
	viper.SetDefault("monitoring.health_endpoint", "/health")

	// Services defaults
	viper.SetDefault("services.user_service", "localhost:50051")
	viper.SetDefault("services.asset_service", "localhost:50052")
	viper.SetDefault("services.ai_content_service", "localhost:50053")
	viper.SetDefault("services.credit_billing_service", "localhost:50054")
	viper.SetDefault("services.notification_service", "localhost:50056")
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// GetDriverName returns the database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres"
	}
}

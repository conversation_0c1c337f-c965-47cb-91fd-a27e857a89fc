// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

// FolderQuery is the builder for querying Folder entities.
type FolderQuery struct {
	config
	ctx          *QueryContext
	order        []folder.OrderOption
	inters       []Interceptor
	predicates   []predicate.Folder
	withParent   *FolderQuery
	withChildren *FolderQuery
	withAssets   *AssetQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the FolderQuery builder.
func (fq *FolderQuery) Where(ps ...predicate.Folder) *FolderQuery {
	fq.predicates = append(fq.predicates, ps...)
	return fq
}

// Limit the number of records to be returned by this query.
func (fq *FolderQuery) Limit(limit int) *FolderQuery {
	fq.ctx.Limit = &limit
	return fq
}

// Offset to start from.
func (fq *FolderQuery) Offset(offset int) *FolderQuery {
	fq.ctx.Offset = &offset
	return fq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (fq *FolderQuery) Unique(unique bool) *FolderQuery {
	fq.ctx.Unique = &unique
	return fq
}

// Order specifies how the records should be ordered.
func (fq *FolderQuery) Order(o ...folder.OrderOption) *FolderQuery {
	fq.order = append(fq.order, o...)
	return fq
}

// QueryParent chains the current query on the "parent" edge.
func (fq *FolderQuery) QueryParent() *FolderQuery {
	query := (&FolderClient{config: fq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := fq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := fq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, selector),
			sqlgraph.To(folder.Table, folder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, folder.ParentTable, folder.ParentColumn),
		)
		fromU = sqlgraph.SetNeighbors(fq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryChildren chains the current query on the "children" edge.
func (fq *FolderQuery) QueryChildren() *FolderQuery {
	query := (&FolderClient{config: fq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := fq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := fq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, selector),
			sqlgraph.To(folder.Table, folder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, folder.ChildrenTable, folder.ChildrenColumn),
		)
		fromU = sqlgraph.SetNeighbors(fq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryAssets chains the current query on the "assets" edge.
func (fq *FolderQuery) QueryAssets() *AssetQuery {
	query := (&AssetClient{config: fq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := fq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := fq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, selector),
			sqlgraph.To(asset.Table, asset.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, folder.AssetsTable, folder.AssetsColumn),
		)
		fromU = sqlgraph.SetNeighbors(fq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Folder entity from the query.
// Returns a *NotFoundError when no Folder was found.
func (fq *FolderQuery) First(ctx context.Context) (*Folder, error) {
	nodes, err := fq.Limit(1).All(setContextOp(ctx, fq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{folder.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (fq *FolderQuery) FirstX(ctx context.Context) *Folder {
	node, err := fq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Folder ID from the query.
// Returns a *NotFoundError when no Folder ID was found.
func (fq *FolderQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = fq.Limit(1).IDs(setContextOp(ctx, fq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{folder.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (fq *FolderQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := fq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Folder entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Folder entity is found.
// Returns a *NotFoundError when no Folder entities are found.
func (fq *FolderQuery) Only(ctx context.Context) (*Folder, error) {
	nodes, err := fq.Limit(2).All(setContextOp(ctx, fq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{folder.Label}
	default:
		return nil, &NotSingularError{folder.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (fq *FolderQuery) OnlyX(ctx context.Context) *Folder {
	node, err := fq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Folder ID in the query.
// Returns a *NotSingularError when more than one Folder ID is found.
// Returns a *NotFoundError when no entities are found.
func (fq *FolderQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = fq.Limit(2).IDs(setContextOp(ctx, fq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{folder.Label}
	default:
		err = &NotSingularError{folder.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (fq *FolderQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := fq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Folders.
func (fq *FolderQuery) All(ctx context.Context) ([]*Folder, error) {
	ctx = setContextOp(ctx, fq.ctx, ent.OpQueryAll)
	if err := fq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Folder, *FolderQuery]()
	return withInterceptors[[]*Folder](ctx, fq, qr, fq.inters)
}

// AllX is like All, but panics if an error occurs.
func (fq *FolderQuery) AllX(ctx context.Context) []*Folder {
	nodes, err := fq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Folder IDs.
func (fq *FolderQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if fq.ctx.Unique == nil && fq.path != nil {
		fq.Unique(true)
	}
	ctx = setContextOp(ctx, fq.ctx, ent.OpQueryIDs)
	if err = fq.Select(folder.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (fq *FolderQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := fq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (fq *FolderQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, fq.ctx, ent.OpQueryCount)
	if err := fq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, fq, querierCount[*FolderQuery](), fq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (fq *FolderQuery) CountX(ctx context.Context) int {
	count, err := fq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (fq *FolderQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, fq.ctx, ent.OpQueryExist)
	switch _, err := fq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (fq *FolderQuery) ExistX(ctx context.Context) bool {
	exist, err := fq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the FolderQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (fq *FolderQuery) Clone() *FolderQuery {
	if fq == nil {
		return nil
	}
	return &FolderQuery{
		config:       fq.config,
		ctx:          fq.ctx.Clone(),
		order:        append([]folder.OrderOption{}, fq.order...),
		inters:       append([]Interceptor{}, fq.inters...),
		predicates:   append([]predicate.Folder{}, fq.predicates...),
		withParent:   fq.withParent.Clone(),
		withChildren: fq.withChildren.Clone(),
		withAssets:   fq.withAssets.Clone(),
		// clone intermediate query.
		sql:  fq.sql.Clone(),
		path: fq.path,
	}
}

// WithParent tells the query-builder to eager-load the nodes that are connected to
// the "parent" edge. The optional arguments are used to configure the query builder of the edge.
func (fq *FolderQuery) WithParent(opts ...func(*FolderQuery)) *FolderQuery {
	query := (&FolderClient{config: fq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	fq.withParent = query
	return fq
}

// WithChildren tells the query-builder to eager-load the nodes that are connected to
// the "children" edge. The optional arguments are used to configure the query builder of the edge.
func (fq *FolderQuery) WithChildren(opts ...func(*FolderQuery)) *FolderQuery {
	query := (&FolderClient{config: fq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	fq.withChildren = query
	return fq
}

// WithAssets tells the query-builder to eager-load the nodes that are connected to
// the "assets" edge. The optional arguments are used to configure the query builder of the edge.
func (fq *FolderQuery) WithAssets(opts ...func(*AssetQuery)) *FolderQuery {
	query := (&AssetClient{config: fq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	fq.withAssets = query
	return fq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Folder.Query().
//		GroupBy(folder.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (fq *FolderQuery) GroupBy(field string, fields ...string) *FolderGroupBy {
	fq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &FolderGroupBy{build: fq}
	grbuild.flds = &fq.ctx.Fields
	grbuild.label = folder.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.Folder.Query().
//		Select(folder.FieldUserID).
//		Scan(ctx, &v)
func (fq *FolderQuery) Select(fields ...string) *FolderSelect {
	fq.ctx.Fields = append(fq.ctx.Fields, fields...)
	sbuild := &FolderSelect{FolderQuery: fq}
	sbuild.label = folder.Label
	sbuild.flds, sbuild.scan = &fq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a FolderSelect configured with the given aggregations.
func (fq *FolderQuery) Aggregate(fns ...AggregateFunc) *FolderSelect {
	return fq.Select().Aggregate(fns...)
}

func (fq *FolderQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range fq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, fq); err != nil {
				return err
			}
		}
	}
	for _, f := range fq.ctx.Fields {
		if !folder.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if fq.path != nil {
		prev, err := fq.path(ctx)
		if err != nil {
			return err
		}
		fq.sql = prev
	}
	return nil
}

func (fq *FolderQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Folder, error) {
	var (
		nodes       = []*Folder{}
		_spec       = fq.querySpec()
		loadedTypes = [3]bool{
			fq.withParent != nil,
			fq.withChildren != nil,
			fq.withAssets != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Folder).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Folder{config: fq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, fq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := fq.withParent; query != nil {
		if err := fq.loadParent(ctx, query, nodes, nil,
			func(n *Folder, e *Folder) { n.Edges.Parent = e }); err != nil {
			return nil, err
		}
	}
	if query := fq.withChildren; query != nil {
		if err := fq.loadChildren(ctx, query, nodes,
			func(n *Folder) { n.Edges.Children = []*Folder{} },
			func(n *Folder, e *Folder) { n.Edges.Children = append(n.Edges.Children, e) }); err != nil {
			return nil, err
		}
	}
	if query := fq.withAssets; query != nil {
		if err := fq.loadAssets(ctx, query, nodes,
			func(n *Folder) { n.Edges.Assets = []*Asset{} },
			func(n *Folder, e *Asset) { n.Edges.Assets = append(n.Edges.Assets, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (fq *FolderQuery) loadParent(ctx context.Context, query *FolderQuery, nodes []*Folder, init func(*Folder), assign func(*Folder, *Folder)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*Folder)
	for i := range nodes {
		if nodes[i].ParentID == nil {
			continue
		}
		fk := *nodes[i].ParentID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(folder.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "parent_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (fq *FolderQuery) loadChildren(ctx context.Context, query *FolderQuery, nodes []*Folder, init func(*Folder), assign func(*Folder, *Folder)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uuid.UUID]*Folder)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(folder.FieldParentID)
	}
	query.Where(predicate.Folder(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(folder.ChildrenColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ParentID
		if fk == nil {
			return fmt.Errorf(`foreign-key "parent_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "parent_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (fq *FolderQuery) loadAssets(ctx context.Context, query *AssetQuery, nodes []*Folder, init func(*Folder), assign func(*Folder, *Asset)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uuid.UUID]*Folder)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(asset.FieldFolderID)
	}
	query.Where(predicate.Asset(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(folder.AssetsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.FolderID
		if fk == nil {
			return fmt.Errorf(`foreign-key "folder_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "folder_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (fq *FolderQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := fq.querySpec()
	_spec.Node.Columns = fq.ctx.Fields
	if len(fq.ctx.Fields) > 0 {
		_spec.Unique = fq.ctx.Unique != nil && *fq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, fq.driver, _spec)
}

func (fq *FolderQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(folder.Table, folder.Columns, sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID))
	_spec.From = fq.sql
	if unique := fq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if fq.path != nil {
		_spec.Unique = true
	}
	if fields := fq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, folder.FieldID)
		for i := range fields {
			if fields[i] != folder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if fq.withParent != nil {
			_spec.Node.AddColumnOnce(folder.FieldParentID)
		}
	}
	if ps := fq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := fq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := fq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := fq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (fq *FolderQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(fq.driver.Dialect())
	t1 := builder.Table(folder.Table)
	columns := fq.ctx.Fields
	if len(columns) == 0 {
		columns = folder.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if fq.sql != nil {
		selector = fq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if fq.ctx.Unique != nil && *fq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range fq.predicates {
		p(selector)
	}
	for _, p := range fq.order {
		p(selector)
	}
	if offset := fq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := fq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// FolderGroupBy is the group-by builder for Folder entities.
type FolderGroupBy struct {
	selector
	build *FolderQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (fgb *FolderGroupBy) Aggregate(fns ...AggregateFunc) *FolderGroupBy {
	fgb.fns = append(fgb.fns, fns...)
	return fgb
}

// Scan applies the selector query and scans the result into the given value.
func (fgb *FolderGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, fgb.build.ctx, ent.OpQueryGroupBy)
	if err := fgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FolderQuery, *FolderGroupBy](ctx, fgb.build, fgb, fgb.build.inters, v)
}

func (fgb *FolderGroupBy) sqlScan(ctx context.Context, root *FolderQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(fgb.fns))
	for _, fn := range fgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*fgb.flds)+len(fgb.fns))
		for _, f := range *fgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*fgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := fgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// FolderSelect is the builder for selecting fields of Folder entities.
type FolderSelect struct {
	*FolderQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (fs *FolderSelect) Aggregate(fns ...AggregateFunc) *FolderSelect {
	fs.fns = append(fs.fns, fns...)
	return fs
}

// Scan applies the selector query and scans the result into the given value.
func (fs *FolderSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, fs.ctx, ent.OpQuerySelect)
	if err := fs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FolderQuery, *FolderSelect](ctx, fs.FolderQuery, fs, fs.inters, v)
}

func (fs *FolderSelect) sqlScan(ctx context.Context, root *FolderQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(fs.fns))
	for _, fn := range fs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*fs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := fs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

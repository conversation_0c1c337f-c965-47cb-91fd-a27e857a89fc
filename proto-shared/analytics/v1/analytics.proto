syntax = "proto3";

package analytics.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/analytics/v1;analyticsv1";

// MetricType represents different types of metrics
enum MetricType {
  METRIC_TYPE_UNSPECIFIED = 0;
  COUNTER = 1;               // Incrementing counter
  GAUGE = 2;                 // Current value
  HISTOGRAM = 3;             // Distribution of values
  TIMER = 4;                 // Duration measurements
}

// Metric represents a single metric data point
message Metric {
  string name = 1;           // Metric name
  MetricType type = 2;       // Metric type
  double value = 3;          // Metric value
  map<string, string> labels = 4; // Metric labels/tags
  google.protobuf.Timestamp timestamp = 5;
  string unit = 6;           // Metric unit (bytes, seconds, count, etc.)
}

// TimeSeriesData represents time series metric data
message TimeSeriesData {
  string metric_name = 1;
  repeated TimeSeriesPoint points = 2;
  map<string, string> labels = 3;
  string aggregation = 4;    // sum, avg, max, min, count
}

// TimeSeriesPoint represents a single point in time series
message TimeSeriesPoint {
  google.protobuf.Timestamp timestamp = 1;
  double value = 2;
}

// UserAnalytics represents user-specific analytics
message UserAnalytics {
  string user_id = 1;
  string workspace_id = 2;   // Optional workspace context
  int32 posts_created = 3;
  int32 posts_published = 4;
  int32 ai_generations = 5;
  int32 credits_used = 6;
  int32 templates_used = 7;
  int32 templates_created = 8;
  map<string, int32> platform_usage = 9; // platform -> post count
  google.protobuf.Timestamp period_start = 10;
  google.protobuf.Timestamp period_end = 11;
}

// WorkspaceAnalytics represents workspace-specific analytics
message WorkspaceAnalytics {
  string workspace_id = 1;
  int32 total_members = 2;
  int32 active_members = 3;
  int32 total_posts = 4;
  int32 published_posts = 5;
  int32 total_templates = 6;
  int32 ai_generations = 7;
  int32 credits_consumed = 8;
  int64 storage_used = 9;
  map<string, int32> member_activity = 10; // user_id -> activity_count
  google.protobuf.Timestamp period_start = 11;
  google.protobuf.Timestamp period_end = 12;
}

// SecurityEvent represents a security-related event
message SecurityEvent {
  string id = 1;
  string user_id = 2;
  string event_type = 3;     // login_success, login_failed, suspicious_activity, etc.
  string severity = 4;       // low, medium, high, critical
  string source_ip = 5;
  string user_agent = 6;
  string location = 7;       // Geographic location
  map<string, string> details = 8; // Event-specific details
  google.protobuf.Timestamp timestamp = 9;
  bool resolved = 10;        // Whether event has been resolved
  string resolution_notes = 11;
}

// SecurityMetrics represents security-related metrics
message SecurityMetrics {
  int32 total_login_attempts = 1;
  int32 successful_logins = 2;
  int32 failed_logins = 3;
  int32 suspicious_activities = 4;
  int32 account_lockouts = 5;
  int32 password_resets = 6;
  int32 two_factor_enabled = 7;
  map<string, int32> login_by_location = 8; // location -> count
  map<string, int32> events_by_severity = 9; // severity -> count
  google.protobuf.Timestamp period_start = 10;
  google.protobuf.Timestamp period_end = 11;
}

// DashboardWidget represents a dashboard widget configuration
message DashboardWidget {
  string id = 1;
  string name = 2;
  string type = 3;           // chart, table, metric, alert
  string data_source = 4;    // Metric or data source name
  map<string, string> config = 5; // Widget-specific configuration
  int32 position_x = 6;      // Widget position
  int32 position_y = 7;
  int32 width = 8;           // Widget dimensions
  int32 height = 9;
  bool is_active = 10;
}

// Dashboard represents a analytics dashboard
message Dashboard {
  string id = 1;
  string name = 2;
  string user_id = 3;        // Dashboard owner
  string workspace_id = 4;   // Optional workspace context
  string type = 5;           // user, workspace, security, system
  repeated DashboardWidget widgets = 6;
  bool is_public = 7;        // Whether dashboard is publicly accessible
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}

// RecordMetricRequest for recording a metric
message RecordMetricRequest {
  Metric metric = 1;
  string user_id = 2;        // Optional user context
  string workspace_id = 3;   // Optional workspace context
}

// RecordMetricsRequest for recording multiple metrics
message RecordMetricsRequest {
  repeated Metric metrics = 1;
  string user_id = 2;        // Optional user context
  string workspace_id = 3;   // Optional workspace context
}

// GetMetricsRequest for retrieving metrics
message GetMetricsRequest {
  string metric_name = 1;
  string user_id = 2;        // Optional user filter
  string workspace_id = 3;   // Optional workspace filter
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  map<string, string> labels = 6; // Label filters
  string aggregation = 7;    // sum, avg, max, min, count
  string interval = 8;       // 1m, 5m, 1h, 1d (aggregation interval)
}

// GetMetricsResponse with metric data
message GetMetricsResponse {
  repeated TimeSeriesData time_series = 1;
  map<string, string> metadata = 2;
}

// GetUserAnalyticsRequest for user analytics
message GetUserAnalyticsRequest {
  string user_id = 1;
  string workspace_id = 2;   // Optional workspace filter
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

// GetWorkspaceAnalyticsRequest for workspace analytics
message GetWorkspaceAnalyticsRequest {
  string workspace_id = 1;
  string user_id = 2;        // For access control
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

// RecordSecurityEventRequest for recording security events
message RecordSecurityEventRequest {
  SecurityEvent event = 1;
}

// GetSecurityEventsRequest for retrieving security events
message GetSecurityEventsRequest {
  string user_id = 1;        // Optional user filter
  string event_type = 2;     // Optional event type filter
  string severity = 3;       // Optional severity filter
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  common.v1.PaginationRequest pagination = 6;
  bool unresolved_only = 7;  // Only unresolved events
}

// GetSecurityEventsResponse with paginated events
message GetSecurityEventsResponse {
  repeated SecurityEvent events = 1;
  common.v1.PaginationResponse pagination = 2;
}

// GetSecurityMetricsRequest for security metrics
message GetSecurityMetricsRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  string user_id = 3;        // Optional user filter
}

// CreateDashboardRequest for creating a dashboard
message CreateDashboardRequest {
  string name = 1;
  string user_id = 2;        // Dashboard owner
  string workspace_id = 3;   // Optional workspace context
  string type = 4;           // user, workspace, security, system
  repeated DashboardWidget widgets = 5;
  bool is_public = 6;
}

// UpdateDashboardRequest for updating a dashboard
message UpdateDashboardRequest {
  string dashboard_id = 1;
  string user_id = 2;        // For access control
  optional string name = 3;
  repeated DashboardWidget widgets = 4;
  optional bool is_public = 5;
}

// GetDashboardRequest for retrieving a dashboard
message GetDashboardRequest {
  string dashboard_id = 1;
  string user_id = 2;        // For access control
}

// ListDashboardsRequest for listing dashboards
message ListDashboardsRequest {
  string user_id = 1;
  string workspace_id = 2;   // Optional workspace filter
  string type = 3;           // Optional type filter
  common.v1.PaginationRequest pagination = 4;
  bool public_only = 5;      // Only public dashboards
}

// ListDashboardsResponse with paginated dashboards
message ListDashboardsResponse {
  repeated Dashboard dashboards = 1;
  common.v1.PaginationResponse pagination = 2;
}

// GetDashboardDataRequest for real-time dashboard data
message GetDashboardDataRequest {
  string dashboard_id = 1;
  string user_id = 2;        // For access control
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

// DashboardData represents real-time dashboard data
message DashboardData {
  string dashboard_id = 1;
  map<string, TimeSeriesData> widget_data = 2; // widget_id -> data
  google.protobuf.Timestamp last_updated = 3;
}

// DeleteDashboardRequest for deleting a dashboard
message DeleteDashboardRequest {
  string dashboard_id = 1;
  string user_id = 2;        // For access control
}

// Empty response
message Empty {}

// AnalyticsService provides analytics and monitoring operations
service AnalyticsService {
  // Metric recording operations
  rpc RecordMetric(RecordMetricRequest) returns (Empty);
  rpc RecordMetrics(RecordMetricsRequest) returns (Empty);
  
  // Metric retrieval operations
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse);
  rpc GetUserAnalytics(GetUserAnalyticsRequest) returns (UserAnalytics);
  rpc GetWorkspaceAnalytics(GetWorkspaceAnalyticsRequest) returns (WorkspaceAnalytics);
  
  // Security monitoring operations
  rpc RecordSecurityEvent(RecordSecurityEventRequest) returns (Empty);
  rpc GetSecurityEvents(GetSecurityEventsRequest) returns (GetSecurityEventsResponse);
  rpc GetSecurityMetrics(GetSecurityMetricsRequest) returns (SecurityMetrics);
  
  // Dashboard operations
  rpc CreateDashboard(CreateDashboardRequest) returns (Dashboard);
  rpc GetDashboard(GetDashboardRequest) returns (Dashboard);
  rpc UpdateDashboard(UpdateDashboardRequest) returns (Dashboard);
  rpc ListDashboards(ListDashboardsRequest) returns (ListDashboardsResponse);
  rpc GetDashboardData(GetDashboardDataRequest) returns (DashboardData);
  rpc DeleteDashboard(DeleteDashboardRequest) returns (Empty);
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/referral"
)

// Referral is the model entity for the Referral schema.
type Referral struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// ReferrerID holds the value of the "referrer_id" field.
	ReferrerID uuid.UUID `json:"referrer_id,omitempty"`
	// ReferredID holds the value of the "referred_id" field.
	ReferredID uuid.UUID `json:"referred_id,omitempty"`
	// ReferralCode holds the value of the "referral_code" field.
	ReferralCode string `json:"referral_code,omitempty"`
	// ReferredEmail holds the value of the "referred_email" field.
	ReferredEmail string `json:"referred_email,omitempty"`
	// Status holds the value of the "status" field.
	Status referral.Status `json:"status,omitempty"`
	// ReferrerCreditsEarned holds the value of the "referrer_credits_earned" field.
	ReferrerCreditsEarned int `json:"referrer_credits_earned,omitempty"`
	// ReferredCreditsEarned holds the value of the "referred_credits_earned" field.
	ReferredCreditsEarned int `json:"referred_credits_earned,omitempty"`
	// ReferralType holds the value of the "referral_type" field.
	ReferralType string `json:"referral_type,omitempty"`
	// CompletedAt holds the value of the "completed_at" field.
	CompletedAt time.Time `json:"completed_at,omitempty"`
	// ExpiresAt holds the value of the "expires_at" field.
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	// ReferralConditions holds the value of the "referral_conditions" field.
	ReferralConditions map[string]interface{} `json:"referral_conditions,omitempty"`
	// TrackingData holds the value of the "tracking_data" field.
	TrackingData map[string]interface{} `json:"tracking_data,omitempty"`
	// CampaignID holds the value of the "campaign_id" field.
	CampaignID string `json:"campaign_id,omitempty"`
	// Source holds the value of the "source" field.
	Source string `json:"source,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Referral) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case referral.FieldReferralConditions, referral.FieldTrackingData, referral.FieldMetadata:
			values[i] = new([]byte)
		case referral.FieldReferrerCreditsEarned, referral.FieldReferredCreditsEarned:
			values[i] = new(sql.NullInt64)
		case referral.FieldReferralCode, referral.FieldReferredEmail, referral.FieldStatus, referral.FieldReferralType, referral.FieldCampaignID, referral.FieldSource:
			values[i] = new(sql.NullString)
		case referral.FieldCompletedAt, referral.FieldExpiresAt, referral.FieldCreatedAt, referral.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case referral.FieldID, referral.FieldReferrerID, referral.FieldReferredID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Referral fields.
func (r *Referral) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case referral.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				r.ID = *value
			}
		case referral.FieldReferrerID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field referrer_id", values[i])
			} else if value != nil {
				r.ReferrerID = *value
			}
		case referral.FieldReferredID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field referred_id", values[i])
			} else if value != nil {
				r.ReferredID = *value
			}
		case referral.FieldReferralCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field referral_code", values[i])
			} else if value.Valid {
				r.ReferralCode = value.String
			}
		case referral.FieldReferredEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field referred_email", values[i])
			} else if value.Valid {
				r.ReferredEmail = value.String
			}
		case referral.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				r.Status = referral.Status(value.String)
			}
		case referral.FieldReferrerCreditsEarned:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field referrer_credits_earned", values[i])
			} else if value.Valid {
				r.ReferrerCreditsEarned = int(value.Int64)
			}
		case referral.FieldReferredCreditsEarned:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field referred_credits_earned", values[i])
			} else if value.Valid {
				r.ReferredCreditsEarned = int(value.Int64)
			}
		case referral.FieldReferralType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field referral_type", values[i])
			} else if value.Valid {
				r.ReferralType = value.String
			}
		case referral.FieldCompletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field completed_at", values[i])
			} else if value.Valid {
				r.CompletedAt = value.Time
			}
		case referral.FieldExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_at", values[i])
			} else if value.Valid {
				r.ExpiresAt = value.Time
			}
		case referral.FieldReferralConditions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field referral_conditions", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &r.ReferralConditions); err != nil {
					return fmt.Errorf("unmarshal field referral_conditions: %w", err)
				}
			}
		case referral.FieldTrackingData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field tracking_data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &r.TrackingData); err != nil {
					return fmt.Errorf("unmarshal field tracking_data: %w", err)
				}
			}
		case referral.FieldCampaignID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field campaign_id", values[i])
			} else if value.Valid {
				r.CampaignID = value.String
			}
		case referral.FieldSource:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field source", values[i])
			} else if value.Valid {
				r.Source = value.String
			}
		case referral.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &r.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case referral.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				r.CreatedAt = value.Time
			}
		case referral.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				r.UpdatedAt = value.Time
			}
		default:
			r.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Referral.
// This includes values selected through modifiers, order, etc.
func (r *Referral) Value(name string) (ent.Value, error) {
	return r.selectValues.Get(name)
}

// Update returns a builder for updating this Referral.
// Note that you need to call Referral.Unwrap() before calling this method if this Referral
// was returned from a transaction, and the transaction was committed or rolled back.
func (r *Referral) Update() *ReferralUpdateOne {
	return NewReferralClient(r.config).UpdateOne(r)
}

// Unwrap unwraps the Referral entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (r *Referral) Unwrap() *Referral {
	_tx, ok := r.config.driver.(*txDriver)
	if !ok {
		panic("ent: Referral is not a transactional entity")
	}
	r.config.driver = _tx.drv
	return r
}

// String implements the fmt.Stringer.
func (r *Referral) String() string {
	var builder strings.Builder
	builder.WriteString("Referral(")
	builder.WriteString(fmt.Sprintf("id=%v, ", r.ID))
	builder.WriteString("referrer_id=")
	builder.WriteString(fmt.Sprintf("%v", r.ReferrerID))
	builder.WriteString(", ")
	builder.WriteString("referred_id=")
	builder.WriteString(fmt.Sprintf("%v", r.ReferredID))
	builder.WriteString(", ")
	builder.WriteString("referral_code=")
	builder.WriteString(r.ReferralCode)
	builder.WriteString(", ")
	builder.WriteString("referred_email=")
	builder.WriteString(r.ReferredEmail)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", r.Status))
	builder.WriteString(", ")
	builder.WriteString("referrer_credits_earned=")
	builder.WriteString(fmt.Sprintf("%v", r.ReferrerCreditsEarned))
	builder.WriteString(", ")
	builder.WriteString("referred_credits_earned=")
	builder.WriteString(fmt.Sprintf("%v", r.ReferredCreditsEarned))
	builder.WriteString(", ")
	builder.WriteString("referral_type=")
	builder.WriteString(r.ReferralType)
	builder.WriteString(", ")
	builder.WriteString("completed_at=")
	builder.WriteString(r.CompletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("expires_at=")
	builder.WriteString(r.ExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("referral_conditions=")
	builder.WriteString(fmt.Sprintf("%v", r.ReferralConditions))
	builder.WriteString(", ")
	builder.WriteString("tracking_data=")
	builder.WriteString(fmt.Sprintf("%v", r.TrackingData))
	builder.WriteString(", ")
	builder.WriteString("campaign_id=")
	builder.WriteString(r.CampaignID)
	builder.WriteString(", ")
	builder.WriteString("source=")
	builder.WriteString(r.Source)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", r.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(r.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(r.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Referrals is a parsable slice of Referral.
type Referrals []*Referral

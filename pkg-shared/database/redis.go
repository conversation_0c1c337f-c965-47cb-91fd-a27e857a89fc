package database

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisConfig holds Redis connection configuration
type RedisConfig struct {
	Host         string
	Port         int
	Password     string
	Database     int
	PoolSize     int
	MinIdleConns int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// RedisClient wraps redis.Client with additional functionality
type RedisClient struct {
	Client *redis.Client
	config *RedisConfig
}

// NewRedisClient creates a new Redis client
func NewRedisClient(config *RedisConfig) (*RedisClient, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password:     config.Password,
		DB:           config.Database,
		PoolSize:     config.PoolSize,
		MinIdleConns: config.MinIdleConns,
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	})

	client := &RedisClient{
		Client: rdb,
		config: config,
	}

	// Test connection
	if err := client.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}

	return client, nil
}

// Ping tests the Redis connection
func (c *RedisClient) Ping(ctx context.Context) error {
	return c.Client.Ping(ctx).Err()
}

// Close closes the Redis connection
func (c *RedisClient) Close() error {
	return c.Client.Close()
}

// Set stores a key-value pair with expiration
func (c *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return c.Client.Set(ctx, key, value, expiration).Err()
}

// Get retrieves a value by key
func (c *RedisClient) Get(ctx context.Context, key string) (string, error) {
	return c.Client.Get(ctx, key).Result()
}

// GetJSON retrieves and unmarshals JSON value
func (c *RedisClient) GetJSON(ctx context.Context, key string, dest interface{}) error {
	val, err := c.Get(ctx, key)
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), dest)
}

// SetJSON marshals and stores JSON value
func (c *RedisClient) SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}
	return c.Set(ctx, key, data, expiration)
}

// Delete removes a key
func (c *RedisClient) Delete(ctx context.Context, keys ...string) error {
	return c.Client.Del(ctx, keys...).Err()
}

// Exists checks if key exists
func (c *RedisClient) Exists(ctx context.Context, key string) (bool, error) {
	result, err := c.Client.Exists(ctx, key).Result()
	return result > 0, err
}

// Expire sets expiration for a key
func (c *RedisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return c.Client.Expire(ctx, key, expiration).Err()
}

// TTL returns time to live for a key
func (c *RedisClient) TTL(ctx context.Context, key string) (time.Duration, error) {
	return c.Client.TTL(ctx, key).Result()
}

// Increment increments a numeric value
func (c *RedisClient) Increment(ctx context.Context, key string) (int64, error) {
	return c.Client.Incr(ctx, key).Result()
}

// IncrementBy increments a numeric value by amount
func (c *RedisClient) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return c.Client.IncrBy(ctx, key, value).Result()
}

// HSet sets hash field
func (c *RedisClient) HSet(ctx context.Context, key string, values ...interface{}) error {
	return c.Client.HSet(ctx, key, values...).Err()
}

// HGet gets hash field
func (c *RedisClient) HGet(ctx context.Context, key, field string) (string, error) {
	return c.Client.HGet(ctx, key, field).Result()
}

// HGetAll gets all hash fields
func (c *RedisClient) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return c.Client.HGetAll(ctx, key).Result()
}

// HDel deletes hash fields
func (c *RedisClient) HDel(ctx context.Context, key string, fields ...string) error {
	return c.Client.HDel(ctx, key, fields...).Err()
}

// LPush pushes elements to the left of a list
func (c *RedisClient) LPush(ctx context.Context, key string, values ...interface{}) error {
	return c.Client.LPush(ctx, key, values...).Err()
}

// RPop pops element from the right of a list
func (c *RedisClient) RPop(ctx context.Context, key string) (string, error) {
	return c.Client.RPop(ctx, key).Result()
}

// LLen returns list length
func (c *RedisClient) LLen(ctx context.Context, key string) (int64, error) {
	return c.Client.LLen(ctx, key).Result()
}

// DefaultRedisConfig returns default Redis configuration
func DefaultRedisConfig() *RedisConfig {
	return &RedisConfig{
		Host:         "localhost",
		Port:         6379,
		Password:     "",
		Database:     0,
		PoolSize:     10,
		MinIdleConns: 5,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		IdleTimeout:  5 * time.Minute,
	}
}

// NewRedisClientFromEnv creates a Redis client from environment variables
func NewRedisClientFromEnv() (*RedisClient, error) {
	config := &RedisConfig{
		Host:         getEnvOrDefault("REDIS_HOST", "localhost"),
		Port:         getEnvIntOrDefault("REDIS_PORT", 6379),
		Password:     getEnvOrDefault("REDIS_PASSWORD", ""),
		Database:     getEnvIntOrDefault("REDIS_DB", 0),
		PoolSize:     getEnvIntOrDefault("REDIS_POOL_SIZE", 10),
		MinIdleConns: getEnvIntOrDefault("REDIS_MIN_IDLE_CONNS", 5),
		DialTimeout:  time.Duration(getEnvIntOrDefault("REDIS_DIAL_TIMEOUT", 5)) * time.Second,
		ReadTimeout:  time.Duration(getEnvIntOrDefault("REDIS_READ_TIMEOUT", 3)) * time.Second,
		WriteTimeout: time.Duration(getEnvIntOrDefault("REDIS_WRITE_TIMEOUT", 3)) * time.Second,
		IdleTimeout:  time.Duration(getEnvIntOrDefault("REDIS_IDLE_TIMEOUT", 300)) * time.Second,
	}

	return NewRedisClient(config)
}

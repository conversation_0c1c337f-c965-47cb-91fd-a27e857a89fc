-- Initialize databases for Social Content AI Platform

-- Create databases for each service
CREATE DATABASE IF NOT EXISTS socialai_users;
CREATE DATABASE IF NOT EXISTS socialai_ai_content;
CREATE DATABASE IF NOT EXISTS socialai_content_mgmt;
CREATE DATABASE IF NOT EXISTS socialai_credit;
CREATE DATABASE IF NOT EXISTS socialai_asset;
CREATE DATABASE IF NOT EXISTS socialai_integration;
CREATE DATABASE IF NOT EXISTS socialai_notification;
CREATE DATABASE IF NOT EXISTS socialai_analytics;

-- Create users for each service (optional, for better security)
-- In production, each service should have its own database user

-- User Service
CREATE USER IF NOT EXISTS 'user_service'@'%' IDENTIFIED BY 'user_service_password';
GRANT ALL PRIVILEGES ON socialai_users.* TO 'user_service'@'%';

-- AI Content Service
CREATE USER IF NOT EXISTS 'ai_content_service'@'%' IDENTIFIED BY 'ai_content_service_password';
GRANT ALL PRIVILEGES ON socialai_ai_content.* TO 'ai_content_service'@'%';

-- Content Management Service
CREATE USER IF NOT EXISTS 'content_mgmt_service'@'%' IDENTIFIED BY 'content_mgmt_service_password';
GRANT ALL PRIVILEGES ON socialai_content_mgmt.* TO 'content_mgmt_service'@'%';

-- Credit Service
CREATE USER IF NOT EXISTS 'credit_service'@'%' IDENTIFIED BY 'credit_service_password';
GRANT ALL PRIVILEGES ON socialai_credit.* TO 'credit_service'@'%';

-- Asset Service
CREATE USER IF NOT EXISTS 'asset_service'@'%' IDENTIFIED BY 'asset_service_password';
GRANT ALL PRIVILEGES ON socialai_asset.* TO 'asset_service'@'%';

-- Integration Service
CREATE USER IF NOT EXISTS 'integration_service'@'%' IDENTIFIED BY 'integration_service_password';
GRANT ALL PRIVILEGES ON socialai_integration.* TO 'integration_service'@'%';

-- Notification Service
CREATE USER IF NOT EXISTS 'notification_service'@'%' IDENTIFIED BY 'notification_service_password';
GRANT ALL PRIVILEGES ON socialai_notification.* TO 'notification_service'@'%';

-- Analytics Service
CREATE USER IF NOT EXISTS 'analytics_service'@'%' IDENTIFIED BY 'analytics_service_password';
GRANT ALL PRIVILEGES ON socialai_analytics.* TO 'analytics_service'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Create extensions if using PostgreSQL
-- Enable UUID extension for all databases
\c socialai_users;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_ai_content;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_content_mgmt;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_credit;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_asset;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_integration;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_notification;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c socialai_analytics;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

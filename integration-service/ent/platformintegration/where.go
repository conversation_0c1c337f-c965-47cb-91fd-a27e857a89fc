// Code generated by ent, DO NOT EDIT.

package platformintegration

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldUserID, v))
}

// WorkspaceID applies equality check predicate on the "workspace_id" field. It's identical to WorkspaceIDEQ.
func WorkspaceID(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldWorkspaceID, v))
}

// PlatformUserID applies equality check predicate on the "platform_user_id" field. It's identical to PlatformUserIDEQ.
func PlatformUserID(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformUserID, v))
}

// PlatformUsername applies equality check predicate on the "platform_username" field. It's identical to PlatformUsernameEQ.
func PlatformUsername(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformUsername, v))
}

// PlatformDisplayName applies equality check predicate on the "platform_display_name" field. It's identical to PlatformDisplayNameEQ.
func PlatformDisplayName(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformDisplayName, v))
}

// PlatformAvatarURL applies equality check predicate on the "platform_avatar_url" field. It's identical to PlatformAvatarURLEQ.
func PlatformAvatarURL(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformAvatarURL, v))
}

// AccessToken applies equality check predicate on the "access_token" field. It's identical to AccessTokenEQ.
func AccessToken(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldAccessToken, v))
}

// RefreshToken applies equality check predicate on the "refresh_token" field. It's identical to RefreshTokenEQ.
func RefreshToken(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldRefreshToken, v))
}

// TokenExpiresAt applies equality check predicate on the "token_expires_at" field. It's identical to TokenExpiresAtEQ.
func TokenExpiresAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldTokenExpiresAt, v))
}

// ErrorMessage applies equality check predicate on the "error_message" field. It's identical to ErrorMessageEQ.
func ErrorMessage(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldErrorMessage, v))
}

// LastSyncAt applies equality check predicate on the "last_sync_at" field. It's identical to LastSyncAtEQ.
func LastSyncAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldLastSyncAt, v))
}

// LastPostAt applies equality check predicate on the "last_post_at" field. It's identical to LastPostAtEQ.
func LastPostAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldLastPostAt, v))
}

// AutoPublish applies equality check predicate on the "auto_publish" field. It's identical to AutoPublishEQ.
func AutoPublish(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldAutoPublish, v))
}

// IsPrimary applies equality check predicate on the "is_primary" field. It's identical to IsPrimaryEQ.
func IsPrimary(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldIsPrimary, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldUserID, v))
}

// WorkspaceIDEQ applies the EQ predicate on the "workspace_id" field.
func WorkspaceIDEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldWorkspaceID, v))
}

// WorkspaceIDNEQ applies the NEQ predicate on the "workspace_id" field.
func WorkspaceIDNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldWorkspaceID, v))
}

// WorkspaceIDIn applies the In predicate on the "workspace_id" field.
func WorkspaceIDIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDNotIn applies the NotIn predicate on the "workspace_id" field.
func WorkspaceIDNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDGT applies the GT predicate on the "workspace_id" field.
func WorkspaceIDGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldWorkspaceID, v))
}

// WorkspaceIDGTE applies the GTE predicate on the "workspace_id" field.
func WorkspaceIDGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldWorkspaceID, v))
}

// WorkspaceIDLT applies the LT predicate on the "workspace_id" field.
func WorkspaceIDLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldWorkspaceID, v))
}

// WorkspaceIDLTE applies the LTE predicate on the "workspace_id" field.
func WorkspaceIDLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldWorkspaceID, v))
}

// WorkspaceIDContains applies the Contains predicate on the "workspace_id" field.
func WorkspaceIDContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldWorkspaceID, v))
}

// WorkspaceIDHasPrefix applies the HasPrefix predicate on the "workspace_id" field.
func WorkspaceIDHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldWorkspaceID, v))
}

// WorkspaceIDHasSuffix applies the HasSuffix predicate on the "workspace_id" field.
func WorkspaceIDHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldWorkspaceID, v))
}

// WorkspaceIDIsNil applies the IsNil predicate on the "workspace_id" field.
func WorkspaceIDIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldWorkspaceID))
}

// WorkspaceIDNotNil applies the NotNil predicate on the "workspace_id" field.
func WorkspaceIDNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldWorkspaceID))
}

// WorkspaceIDEqualFold applies the EqualFold predicate on the "workspace_id" field.
func WorkspaceIDEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldWorkspaceID, v))
}

// WorkspaceIDContainsFold applies the ContainsFold predicate on the "workspace_id" field.
func WorkspaceIDContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldWorkspaceID, v))
}

// PlatformEQ applies the EQ predicate on the "platform" field.
func PlatformEQ(v Platform) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatform, v))
}

// PlatformNEQ applies the NEQ predicate on the "platform" field.
func PlatformNEQ(v Platform) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldPlatform, v))
}

// PlatformIn applies the In predicate on the "platform" field.
func PlatformIn(vs ...Platform) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldPlatform, vs...))
}

// PlatformNotIn applies the NotIn predicate on the "platform" field.
func PlatformNotIn(vs ...Platform) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldPlatform, vs...))
}

// PlatformUserIDEQ applies the EQ predicate on the "platform_user_id" field.
func PlatformUserIDEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformUserID, v))
}

// PlatformUserIDNEQ applies the NEQ predicate on the "platform_user_id" field.
func PlatformUserIDNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldPlatformUserID, v))
}

// PlatformUserIDIn applies the In predicate on the "platform_user_id" field.
func PlatformUserIDIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldPlatformUserID, vs...))
}

// PlatformUserIDNotIn applies the NotIn predicate on the "platform_user_id" field.
func PlatformUserIDNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldPlatformUserID, vs...))
}

// PlatformUserIDGT applies the GT predicate on the "platform_user_id" field.
func PlatformUserIDGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldPlatformUserID, v))
}

// PlatformUserIDGTE applies the GTE predicate on the "platform_user_id" field.
func PlatformUserIDGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldPlatformUserID, v))
}

// PlatformUserIDLT applies the LT predicate on the "platform_user_id" field.
func PlatformUserIDLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldPlatformUserID, v))
}

// PlatformUserIDLTE applies the LTE predicate on the "platform_user_id" field.
func PlatformUserIDLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldPlatformUserID, v))
}

// PlatformUserIDContains applies the Contains predicate on the "platform_user_id" field.
func PlatformUserIDContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldPlatformUserID, v))
}

// PlatformUserIDHasPrefix applies the HasPrefix predicate on the "platform_user_id" field.
func PlatformUserIDHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldPlatformUserID, v))
}

// PlatformUserIDHasSuffix applies the HasSuffix predicate on the "platform_user_id" field.
func PlatformUserIDHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldPlatformUserID, v))
}

// PlatformUserIDEqualFold applies the EqualFold predicate on the "platform_user_id" field.
func PlatformUserIDEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldPlatformUserID, v))
}

// PlatformUserIDContainsFold applies the ContainsFold predicate on the "platform_user_id" field.
func PlatformUserIDContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldPlatformUserID, v))
}

// PlatformUsernameEQ applies the EQ predicate on the "platform_username" field.
func PlatformUsernameEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformUsername, v))
}

// PlatformUsernameNEQ applies the NEQ predicate on the "platform_username" field.
func PlatformUsernameNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldPlatformUsername, v))
}

// PlatformUsernameIn applies the In predicate on the "platform_username" field.
func PlatformUsernameIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldPlatformUsername, vs...))
}

// PlatformUsernameNotIn applies the NotIn predicate on the "platform_username" field.
func PlatformUsernameNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldPlatformUsername, vs...))
}

// PlatformUsernameGT applies the GT predicate on the "platform_username" field.
func PlatformUsernameGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldPlatformUsername, v))
}

// PlatformUsernameGTE applies the GTE predicate on the "platform_username" field.
func PlatformUsernameGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldPlatformUsername, v))
}

// PlatformUsernameLT applies the LT predicate on the "platform_username" field.
func PlatformUsernameLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldPlatformUsername, v))
}

// PlatformUsernameLTE applies the LTE predicate on the "platform_username" field.
func PlatformUsernameLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldPlatformUsername, v))
}

// PlatformUsernameContains applies the Contains predicate on the "platform_username" field.
func PlatformUsernameContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldPlatformUsername, v))
}

// PlatformUsernameHasPrefix applies the HasPrefix predicate on the "platform_username" field.
func PlatformUsernameHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldPlatformUsername, v))
}

// PlatformUsernameHasSuffix applies the HasSuffix predicate on the "platform_username" field.
func PlatformUsernameHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldPlatformUsername, v))
}

// PlatformUsernameIsNil applies the IsNil predicate on the "platform_username" field.
func PlatformUsernameIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldPlatformUsername))
}

// PlatformUsernameNotNil applies the NotNil predicate on the "platform_username" field.
func PlatformUsernameNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldPlatformUsername))
}

// PlatformUsernameEqualFold applies the EqualFold predicate on the "platform_username" field.
func PlatformUsernameEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldPlatformUsername, v))
}

// PlatformUsernameContainsFold applies the ContainsFold predicate on the "platform_username" field.
func PlatformUsernameContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldPlatformUsername, v))
}

// PlatformDisplayNameEQ applies the EQ predicate on the "platform_display_name" field.
func PlatformDisplayNameEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameNEQ applies the NEQ predicate on the "platform_display_name" field.
func PlatformDisplayNameNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameIn applies the In predicate on the "platform_display_name" field.
func PlatformDisplayNameIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldPlatformDisplayName, vs...))
}

// PlatformDisplayNameNotIn applies the NotIn predicate on the "platform_display_name" field.
func PlatformDisplayNameNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldPlatformDisplayName, vs...))
}

// PlatformDisplayNameGT applies the GT predicate on the "platform_display_name" field.
func PlatformDisplayNameGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameGTE applies the GTE predicate on the "platform_display_name" field.
func PlatformDisplayNameGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameLT applies the LT predicate on the "platform_display_name" field.
func PlatformDisplayNameLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameLTE applies the LTE predicate on the "platform_display_name" field.
func PlatformDisplayNameLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameContains applies the Contains predicate on the "platform_display_name" field.
func PlatformDisplayNameContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameHasPrefix applies the HasPrefix predicate on the "platform_display_name" field.
func PlatformDisplayNameHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameHasSuffix applies the HasSuffix predicate on the "platform_display_name" field.
func PlatformDisplayNameHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameIsNil applies the IsNil predicate on the "platform_display_name" field.
func PlatformDisplayNameIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldPlatformDisplayName))
}

// PlatformDisplayNameNotNil applies the NotNil predicate on the "platform_display_name" field.
func PlatformDisplayNameNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldPlatformDisplayName))
}

// PlatformDisplayNameEqualFold applies the EqualFold predicate on the "platform_display_name" field.
func PlatformDisplayNameEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldPlatformDisplayName, v))
}

// PlatformDisplayNameContainsFold applies the ContainsFold predicate on the "platform_display_name" field.
func PlatformDisplayNameContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldPlatformDisplayName, v))
}

// PlatformAvatarURLEQ applies the EQ predicate on the "platform_avatar_url" field.
func PlatformAvatarURLEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLNEQ applies the NEQ predicate on the "platform_avatar_url" field.
func PlatformAvatarURLNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLIn applies the In predicate on the "platform_avatar_url" field.
func PlatformAvatarURLIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldPlatformAvatarURL, vs...))
}

// PlatformAvatarURLNotIn applies the NotIn predicate on the "platform_avatar_url" field.
func PlatformAvatarURLNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldPlatformAvatarURL, vs...))
}

// PlatformAvatarURLGT applies the GT predicate on the "platform_avatar_url" field.
func PlatformAvatarURLGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLGTE applies the GTE predicate on the "platform_avatar_url" field.
func PlatformAvatarURLGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLLT applies the LT predicate on the "platform_avatar_url" field.
func PlatformAvatarURLLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLLTE applies the LTE predicate on the "platform_avatar_url" field.
func PlatformAvatarURLLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLContains applies the Contains predicate on the "platform_avatar_url" field.
func PlatformAvatarURLContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLHasPrefix applies the HasPrefix predicate on the "platform_avatar_url" field.
func PlatformAvatarURLHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLHasSuffix applies the HasSuffix predicate on the "platform_avatar_url" field.
func PlatformAvatarURLHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLIsNil applies the IsNil predicate on the "platform_avatar_url" field.
func PlatformAvatarURLIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldPlatformAvatarURL))
}

// PlatformAvatarURLNotNil applies the NotNil predicate on the "platform_avatar_url" field.
func PlatformAvatarURLNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldPlatformAvatarURL))
}

// PlatformAvatarURLEqualFold applies the EqualFold predicate on the "platform_avatar_url" field.
func PlatformAvatarURLEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldPlatformAvatarURL, v))
}

// PlatformAvatarURLContainsFold applies the ContainsFold predicate on the "platform_avatar_url" field.
func PlatformAvatarURLContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldPlatformAvatarURL, v))
}

// AccessTokenEQ applies the EQ predicate on the "access_token" field.
func AccessTokenEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldAccessToken, v))
}

// AccessTokenNEQ applies the NEQ predicate on the "access_token" field.
func AccessTokenNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldAccessToken, v))
}

// AccessTokenIn applies the In predicate on the "access_token" field.
func AccessTokenIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldAccessToken, vs...))
}

// AccessTokenNotIn applies the NotIn predicate on the "access_token" field.
func AccessTokenNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldAccessToken, vs...))
}

// AccessTokenGT applies the GT predicate on the "access_token" field.
func AccessTokenGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldAccessToken, v))
}

// AccessTokenGTE applies the GTE predicate on the "access_token" field.
func AccessTokenGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldAccessToken, v))
}

// AccessTokenLT applies the LT predicate on the "access_token" field.
func AccessTokenLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldAccessToken, v))
}

// AccessTokenLTE applies the LTE predicate on the "access_token" field.
func AccessTokenLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldAccessToken, v))
}

// AccessTokenContains applies the Contains predicate on the "access_token" field.
func AccessTokenContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldAccessToken, v))
}

// AccessTokenHasPrefix applies the HasPrefix predicate on the "access_token" field.
func AccessTokenHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldAccessToken, v))
}

// AccessTokenHasSuffix applies the HasSuffix predicate on the "access_token" field.
func AccessTokenHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldAccessToken, v))
}

// AccessTokenEqualFold applies the EqualFold predicate on the "access_token" field.
func AccessTokenEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldAccessToken, v))
}

// AccessTokenContainsFold applies the ContainsFold predicate on the "access_token" field.
func AccessTokenContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldAccessToken, v))
}

// RefreshTokenEQ applies the EQ predicate on the "refresh_token" field.
func RefreshTokenEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldRefreshToken, v))
}

// RefreshTokenNEQ applies the NEQ predicate on the "refresh_token" field.
func RefreshTokenNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldRefreshToken, v))
}

// RefreshTokenIn applies the In predicate on the "refresh_token" field.
func RefreshTokenIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldRefreshToken, vs...))
}

// RefreshTokenNotIn applies the NotIn predicate on the "refresh_token" field.
func RefreshTokenNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldRefreshToken, vs...))
}

// RefreshTokenGT applies the GT predicate on the "refresh_token" field.
func RefreshTokenGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldRefreshToken, v))
}

// RefreshTokenGTE applies the GTE predicate on the "refresh_token" field.
func RefreshTokenGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldRefreshToken, v))
}

// RefreshTokenLT applies the LT predicate on the "refresh_token" field.
func RefreshTokenLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldRefreshToken, v))
}

// RefreshTokenLTE applies the LTE predicate on the "refresh_token" field.
func RefreshTokenLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldRefreshToken, v))
}

// RefreshTokenContains applies the Contains predicate on the "refresh_token" field.
func RefreshTokenContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldRefreshToken, v))
}

// RefreshTokenHasPrefix applies the HasPrefix predicate on the "refresh_token" field.
func RefreshTokenHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldRefreshToken, v))
}

// RefreshTokenHasSuffix applies the HasSuffix predicate on the "refresh_token" field.
func RefreshTokenHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldRefreshToken, v))
}

// RefreshTokenIsNil applies the IsNil predicate on the "refresh_token" field.
func RefreshTokenIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldRefreshToken))
}

// RefreshTokenNotNil applies the NotNil predicate on the "refresh_token" field.
func RefreshTokenNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldRefreshToken))
}

// RefreshTokenEqualFold applies the EqualFold predicate on the "refresh_token" field.
func RefreshTokenEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldRefreshToken, v))
}

// RefreshTokenContainsFold applies the ContainsFold predicate on the "refresh_token" field.
func RefreshTokenContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldRefreshToken, v))
}

// TokenExpiresAtEQ applies the EQ predicate on the "token_expires_at" field.
func TokenExpiresAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldTokenExpiresAt, v))
}

// TokenExpiresAtNEQ applies the NEQ predicate on the "token_expires_at" field.
func TokenExpiresAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldTokenExpiresAt, v))
}

// TokenExpiresAtIn applies the In predicate on the "token_expires_at" field.
func TokenExpiresAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldTokenExpiresAt, vs...))
}

// TokenExpiresAtNotIn applies the NotIn predicate on the "token_expires_at" field.
func TokenExpiresAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldTokenExpiresAt, vs...))
}

// TokenExpiresAtGT applies the GT predicate on the "token_expires_at" field.
func TokenExpiresAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldTokenExpiresAt, v))
}

// TokenExpiresAtGTE applies the GTE predicate on the "token_expires_at" field.
func TokenExpiresAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldTokenExpiresAt, v))
}

// TokenExpiresAtLT applies the LT predicate on the "token_expires_at" field.
func TokenExpiresAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldTokenExpiresAt, v))
}

// TokenExpiresAtLTE applies the LTE predicate on the "token_expires_at" field.
func TokenExpiresAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldTokenExpiresAt, v))
}

// TokenExpiresAtIsNil applies the IsNil predicate on the "token_expires_at" field.
func TokenExpiresAtIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldTokenExpiresAt))
}

// TokenExpiresAtNotNil applies the NotNil predicate on the "token_expires_at" field.
func TokenExpiresAtNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldTokenExpiresAt))
}

// ScopesIsNil applies the IsNil predicate on the "scopes" field.
func ScopesIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldScopes))
}

// ScopesNotNil applies the NotNil predicate on the "scopes" field.
func ScopesNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldScopes))
}

// PlatformDataIsNil applies the IsNil predicate on the "platform_data" field.
func PlatformDataIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldPlatformData))
}

// PlatformDataNotNil applies the NotNil predicate on the "platform_data" field.
func PlatformDataNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldPlatformData))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldStatus, vs...))
}

// ErrorMessageEQ applies the EQ predicate on the "error_message" field.
func ErrorMessageEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldErrorMessage, v))
}

// ErrorMessageNEQ applies the NEQ predicate on the "error_message" field.
func ErrorMessageNEQ(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldErrorMessage, v))
}

// ErrorMessageIn applies the In predicate on the "error_message" field.
func ErrorMessageIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldErrorMessage, vs...))
}

// ErrorMessageNotIn applies the NotIn predicate on the "error_message" field.
func ErrorMessageNotIn(vs ...string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldErrorMessage, vs...))
}

// ErrorMessageGT applies the GT predicate on the "error_message" field.
func ErrorMessageGT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldErrorMessage, v))
}

// ErrorMessageGTE applies the GTE predicate on the "error_message" field.
func ErrorMessageGTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldErrorMessage, v))
}

// ErrorMessageLT applies the LT predicate on the "error_message" field.
func ErrorMessageLT(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldErrorMessage, v))
}

// ErrorMessageLTE applies the LTE predicate on the "error_message" field.
func ErrorMessageLTE(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldErrorMessage, v))
}

// ErrorMessageContains applies the Contains predicate on the "error_message" field.
func ErrorMessageContains(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContains(FieldErrorMessage, v))
}

// ErrorMessageHasPrefix applies the HasPrefix predicate on the "error_message" field.
func ErrorMessageHasPrefix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasPrefix(FieldErrorMessage, v))
}

// ErrorMessageHasSuffix applies the HasSuffix predicate on the "error_message" field.
func ErrorMessageHasSuffix(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldHasSuffix(FieldErrorMessage, v))
}

// ErrorMessageIsNil applies the IsNil predicate on the "error_message" field.
func ErrorMessageIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldErrorMessage))
}

// ErrorMessageNotNil applies the NotNil predicate on the "error_message" field.
func ErrorMessageNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldErrorMessage))
}

// ErrorMessageEqualFold applies the EqualFold predicate on the "error_message" field.
func ErrorMessageEqualFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEqualFold(FieldErrorMessage, v))
}

// ErrorMessageContainsFold applies the ContainsFold predicate on the "error_message" field.
func ErrorMessageContainsFold(v string) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldContainsFold(FieldErrorMessage, v))
}

// LastSyncAtEQ applies the EQ predicate on the "last_sync_at" field.
func LastSyncAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldLastSyncAt, v))
}

// LastSyncAtNEQ applies the NEQ predicate on the "last_sync_at" field.
func LastSyncAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldLastSyncAt, v))
}

// LastSyncAtIn applies the In predicate on the "last_sync_at" field.
func LastSyncAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldLastSyncAt, vs...))
}

// LastSyncAtNotIn applies the NotIn predicate on the "last_sync_at" field.
func LastSyncAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldLastSyncAt, vs...))
}

// LastSyncAtGT applies the GT predicate on the "last_sync_at" field.
func LastSyncAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldLastSyncAt, v))
}

// LastSyncAtGTE applies the GTE predicate on the "last_sync_at" field.
func LastSyncAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldLastSyncAt, v))
}

// LastSyncAtLT applies the LT predicate on the "last_sync_at" field.
func LastSyncAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldLastSyncAt, v))
}

// LastSyncAtLTE applies the LTE predicate on the "last_sync_at" field.
func LastSyncAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldLastSyncAt, v))
}

// LastSyncAtIsNil applies the IsNil predicate on the "last_sync_at" field.
func LastSyncAtIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldLastSyncAt))
}

// LastSyncAtNotNil applies the NotNil predicate on the "last_sync_at" field.
func LastSyncAtNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldLastSyncAt))
}

// LastPostAtEQ applies the EQ predicate on the "last_post_at" field.
func LastPostAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldLastPostAt, v))
}

// LastPostAtNEQ applies the NEQ predicate on the "last_post_at" field.
func LastPostAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldLastPostAt, v))
}

// LastPostAtIn applies the In predicate on the "last_post_at" field.
func LastPostAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldLastPostAt, vs...))
}

// LastPostAtNotIn applies the NotIn predicate on the "last_post_at" field.
func LastPostAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldLastPostAt, vs...))
}

// LastPostAtGT applies the GT predicate on the "last_post_at" field.
func LastPostAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldLastPostAt, v))
}

// LastPostAtGTE applies the GTE predicate on the "last_post_at" field.
func LastPostAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldLastPostAt, v))
}

// LastPostAtLT applies the LT predicate on the "last_post_at" field.
func LastPostAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldLastPostAt, v))
}

// LastPostAtLTE applies the LTE predicate on the "last_post_at" field.
func LastPostAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldLastPostAt, v))
}

// LastPostAtIsNil applies the IsNil predicate on the "last_post_at" field.
func LastPostAtIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldLastPostAt))
}

// LastPostAtNotNil applies the NotNil predicate on the "last_post_at" field.
func LastPostAtNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldLastPostAt))
}

// CapabilitiesIsNil applies the IsNil predicate on the "capabilities" field.
func CapabilitiesIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldCapabilities))
}

// CapabilitiesNotNil applies the NotNil predicate on the "capabilities" field.
func CapabilitiesNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldCapabilities))
}

// SettingsIsNil applies the IsNil predicate on the "settings" field.
func SettingsIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldSettings))
}

// SettingsNotNil applies the NotNil predicate on the "settings" field.
func SettingsNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldSettings))
}

// AutoPublishEQ applies the EQ predicate on the "auto_publish" field.
func AutoPublishEQ(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldAutoPublish, v))
}

// AutoPublishNEQ applies the NEQ predicate on the "auto_publish" field.
func AutoPublishNEQ(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldAutoPublish, v))
}

// IsPrimaryEQ applies the EQ predicate on the "is_primary" field.
func IsPrimaryEQ(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldIsPrimary, v))
}

// IsPrimaryNEQ applies the NEQ predicate on the "is_primary" field.
func IsPrimaryNEQ(v bool) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldIsPrimary, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PlatformIntegration) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PlatformIntegration) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PlatformIntegration) predicate.PlatformIntegration {
	return predicate.PlatformIntegration(sql.NotPredicates(p))
}

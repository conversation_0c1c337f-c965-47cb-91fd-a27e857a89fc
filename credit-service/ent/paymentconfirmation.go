// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
)

// PaymentConfirmation is the model entity for the PaymentConfirmation schema.
type PaymentConfirmation struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// BankTransferID holds the value of the "bank_transfer_id" field.
	BankTransferID uuid.UUID `json:"bank_transfer_id,omitempty"`
	// OrderID holds the value of the "order_id" field.
	OrderID uuid.UUID `json:"order_id,omitempty"`
	// SubscriptionID holds the value of the "subscription_id" field.
	SubscriptionID uuid.UUID `json:"subscription_id,omitempty"`
	// PaymentMethod holds the value of the "payment_method" field.
	PaymentMethod string `json:"payment_method,omitempty"`
	// ExternalPaymentID holds the value of the "external_payment_id" field.
	ExternalPaymentID string `json:"external_payment_id,omitempty"`
	// BankTransactionID holds the value of the "bank_transaction_id" field.
	BankTransactionID string `json:"bank_transaction_id,omitempty"`
	// AmountPaid holds the value of the "amount_paid" field.
	AmountPaid int64 `json:"amount_paid,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// Status holds the value of the "status" field.
	Status paymentconfirmation.Status `json:"status,omitempty"`
	// ConfirmedBy holds the value of the "confirmed_by" field.
	ConfirmedBy uuid.UUID `json:"confirmed_by,omitempty"`
	// ConfirmationMethod holds the value of the "confirmation_method" field.
	ConfirmationMethod string `json:"confirmation_method,omitempty"`
	// ConfirmationNotes holds the value of the "confirmation_notes" field.
	ConfirmationNotes string `json:"confirmation_notes,omitempty"`
	// PaymentDetails holds the value of the "payment_details" field.
	PaymentDetails map[string]interface{} `json:"payment_details,omitempty"`
	// WebhookData holds the value of the "webhook_data" field.
	WebhookData map[string]interface{} `json:"webhook_data,omitempty"`
	// PaymentDate holds the value of the "payment_date" field.
	PaymentDate time.Time `json:"payment_date,omitempty"`
	// ConfirmedAt holds the value of the "confirmed_at" field.
	ConfirmedAt time.Time `json:"confirmed_at,omitempty"`
	// FailureReason holds the value of the "failure_reason" field.
	FailureReason string `json:"failure_reason,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PaymentConfirmation) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case paymentconfirmation.FieldPaymentDetails, paymentconfirmation.FieldWebhookData, paymentconfirmation.FieldMetadata:
			values[i] = new([]byte)
		case paymentconfirmation.FieldAmountPaid:
			values[i] = new(sql.NullInt64)
		case paymentconfirmation.FieldPaymentMethod, paymentconfirmation.FieldExternalPaymentID, paymentconfirmation.FieldBankTransactionID, paymentconfirmation.FieldCurrency, paymentconfirmation.FieldStatus, paymentconfirmation.FieldConfirmationMethod, paymentconfirmation.FieldConfirmationNotes, paymentconfirmation.FieldFailureReason:
			values[i] = new(sql.NullString)
		case paymentconfirmation.FieldPaymentDate, paymentconfirmation.FieldConfirmedAt, paymentconfirmation.FieldCreatedAt, paymentconfirmation.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case paymentconfirmation.FieldID, paymentconfirmation.FieldBankTransferID, paymentconfirmation.FieldOrderID, paymentconfirmation.FieldSubscriptionID, paymentconfirmation.FieldConfirmedBy:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PaymentConfirmation fields.
func (pc *PaymentConfirmation) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case paymentconfirmation.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				pc.ID = *value
			}
		case paymentconfirmation.FieldBankTransferID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field bank_transfer_id", values[i])
			} else if value != nil {
				pc.BankTransferID = *value
			}
		case paymentconfirmation.FieldOrderID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field order_id", values[i])
			} else if value != nil {
				pc.OrderID = *value
			}
		case paymentconfirmation.FieldSubscriptionID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field subscription_id", values[i])
			} else if value != nil {
				pc.SubscriptionID = *value
			}
		case paymentconfirmation.FieldPaymentMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_method", values[i])
			} else if value.Valid {
				pc.PaymentMethod = value.String
			}
		case paymentconfirmation.FieldExternalPaymentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field external_payment_id", values[i])
			} else if value.Valid {
				pc.ExternalPaymentID = value.String
			}
		case paymentconfirmation.FieldBankTransactionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field bank_transaction_id", values[i])
			} else if value.Valid {
				pc.BankTransactionID = value.String
			}
		case paymentconfirmation.FieldAmountPaid:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount_paid", values[i])
			} else if value.Valid {
				pc.AmountPaid = value.Int64
			}
		case paymentconfirmation.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				pc.Currency = value.String
			}
		case paymentconfirmation.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pc.Status = paymentconfirmation.Status(value.String)
			}
		case paymentconfirmation.FieldConfirmedBy:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field confirmed_by", values[i])
			} else if value != nil {
				pc.ConfirmedBy = *value
			}
		case paymentconfirmation.FieldConfirmationMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field confirmation_method", values[i])
			} else if value.Valid {
				pc.ConfirmationMethod = value.String
			}
		case paymentconfirmation.FieldConfirmationNotes:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field confirmation_notes", values[i])
			} else if value.Valid {
				pc.ConfirmationNotes = value.String
			}
		case paymentconfirmation.FieldPaymentDetails:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field payment_details", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pc.PaymentDetails); err != nil {
					return fmt.Errorf("unmarshal field payment_details: %w", err)
				}
			}
		case paymentconfirmation.FieldWebhookData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field webhook_data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pc.WebhookData); err != nil {
					return fmt.Errorf("unmarshal field webhook_data: %w", err)
				}
			}
		case paymentconfirmation.FieldPaymentDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field payment_date", values[i])
			} else if value.Valid {
				pc.PaymentDate = value.Time
			}
		case paymentconfirmation.FieldConfirmedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field confirmed_at", values[i])
			} else if value.Valid {
				pc.ConfirmedAt = value.Time
			}
		case paymentconfirmation.FieldFailureReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field failure_reason", values[i])
			} else if value.Valid {
				pc.FailureReason = value.String
			}
		case paymentconfirmation.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pc.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case paymentconfirmation.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pc.CreatedAt = value.Time
			}
		case paymentconfirmation.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pc.UpdatedAt = value.Time
			}
		default:
			pc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PaymentConfirmation.
// This includes values selected through modifiers, order, etc.
func (pc *PaymentConfirmation) Value(name string) (ent.Value, error) {
	return pc.selectValues.Get(name)
}

// Update returns a builder for updating this PaymentConfirmation.
// Note that you need to call PaymentConfirmation.Unwrap() before calling this method if this PaymentConfirmation
// was returned from a transaction, and the transaction was committed or rolled back.
func (pc *PaymentConfirmation) Update() *PaymentConfirmationUpdateOne {
	return NewPaymentConfirmationClient(pc.config).UpdateOne(pc)
}

// Unwrap unwraps the PaymentConfirmation entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pc *PaymentConfirmation) Unwrap() *PaymentConfirmation {
	_tx, ok := pc.config.driver.(*txDriver)
	if !ok {
		panic("ent: PaymentConfirmation is not a transactional entity")
	}
	pc.config.driver = _tx.drv
	return pc
}

// String implements the fmt.Stringer.
func (pc *PaymentConfirmation) String() string {
	var builder strings.Builder
	builder.WriteString("PaymentConfirmation(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pc.ID))
	builder.WriteString("bank_transfer_id=")
	builder.WriteString(fmt.Sprintf("%v", pc.BankTransferID))
	builder.WriteString(", ")
	builder.WriteString("order_id=")
	builder.WriteString(fmt.Sprintf("%v", pc.OrderID))
	builder.WriteString(", ")
	builder.WriteString("subscription_id=")
	builder.WriteString(fmt.Sprintf("%v", pc.SubscriptionID))
	builder.WriteString(", ")
	builder.WriteString("payment_method=")
	builder.WriteString(pc.PaymentMethod)
	builder.WriteString(", ")
	builder.WriteString("external_payment_id=")
	builder.WriteString(pc.ExternalPaymentID)
	builder.WriteString(", ")
	builder.WriteString("bank_transaction_id=")
	builder.WriteString(pc.BankTransactionID)
	builder.WriteString(", ")
	builder.WriteString("amount_paid=")
	builder.WriteString(fmt.Sprintf("%v", pc.AmountPaid))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(pc.Currency)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pc.Status))
	builder.WriteString(", ")
	builder.WriteString("confirmed_by=")
	builder.WriteString(fmt.Sprintf("%v", pc.ConfirmedBy))
	builder.WriteString(", ")
	builder.WriteString("confirmation_method=")
	builder.WriteString(pc.ConfirmationMethod)
	builder.WriteString(", ")
	builder.WriteString("confirmation_notes=")
	builder.WriteString(pc.ConfirmationNotes)
	builder.WriteString(", ")
	builder.WriteString("payment_details=")
	builder.WriteString(fmt.Sprintf("%v", pc.PaymentDetails))
	builder.WriteString(", ")
	builder.WriteString("webhook_data=")
	builder.WriteString(fmt.Sprintf("%v", pc.WebhookData))
	builder.WriteString(", ")
	builder.WriteString("payment_date=")
	builder.WriteString(pc.PaymentDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("confirmed_at=")
	builder.WriteString(pc.ConfirmedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("failure_reason=")
	builder.WriteString(pc.FailureReason)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", pc.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pc.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pc.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// PaymentConfirmations is a parsable slice of PaymentConfirmation.
type PaymentConfirmations []*PaymentConfirmation

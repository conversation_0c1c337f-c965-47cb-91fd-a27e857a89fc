// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: credit/v1/credit.proto

package creditv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CreditServiceClient is the client API for CreditService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CreditServiceClient interface {
	// Basic credit operations
	GetUserCredit(ctx context.Context, in *GetUserCreditRequest, opts ...grpc.CallOption) (*UserCredit, error)
	ValidateCredit(ctx context.Context, in *ValidateCreditRequest, opts ...grpc.CallOption) (*ValidateCreditResponse, error)
	AddCredits(ctx context.Context, in *AddCreditsRequest, opts ...grpc.CallOption) (*UserCredit, error)
	DeductCredits(ctx context.Context, in *DeductCreditsRequest, opts ...grpc.CallOption) (*UserCredit, error)
	ListTransactions(ctx context.Context, in *ListTransactionsRequest, opts ...grpc.CallOption) (*ListTransactionsResponse, error)
	// Subscription management
	CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error)
	GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*Subscription, error)
	ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error)
	UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*Subscription, error)
	CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*Empty, error)
	// Payment method management
	GetPaymentMethods(ctx context.Context, in *GetPaymentMethodsRequest, opts ...grpc.CallOption) (*GetPaymentMethodsResponse, error)
	AddPaymentMethod(ctx context.Context, in *AddPaymentMethodRequest, opts ...grpc.CallOption) (*PaymentMethod, error)
	RemovePaymentMethod(ctx context.Context, in *RemovePaymentMethodRequest, opts ...grpc.CallOption) (*Empty, error)
	// Bank transfer operations
	ConfirmBankTransfer(ctx context.Context, in *ConfirmBankTransferRequest, opts ...grpc.CallOption) (*ConfirmBankTransferResponse, error)
	// Recurring payment processing
	ProcessRecurringPayment(ctx context.Context, in *ProcessRecurringPaymentRequest, opts ...grpc.CallOption) (*ProcessRecurringPaymentResponse, error)
}

type creditServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCreditServiceClient(cc grpc.ClientConnInterface) CreditServiceClient {
	return &creditServiceClient{cc}
}

func (c *creditServiceClient) GetUserCredit(ctx context.Context, in *GetUserCreditRequest, opts ...grpc.CallOption) (*UserCredit, error) {
	out := new(UserCredit)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/GetUserCredit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) ValidateCredit(ctx context.Context, in *ValidateCreditRequest, opts ...grpc.CallOption) (*ValidateCreditResponse, error) {
	out := new(ValidateCreditResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/ValidateCredit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) AddCredits(ctx context.Context, in *AddCreditsRequest, opts ...grpc.CallOption) (*UserCredit, error) {
	out := new(UserCredit)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/AddCredits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) DeductCredits(ctx context.Context, in *DeductCreditsRequest, opts ...grpc.CallOption) (*UserCredit, error) {
	out := new(UserCredit)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/DeductCredits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) ListTransactions(ctx context.Context, in *ListTransactionsRequest, opts ...grpc.CallOption) (*ListTransactionsResponse, error) {
	out := new(ListTransactionsResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/ListTransactions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) CreateSubscription(ctx context.Context, in *CreateSubscriptionRequest, opts ...grpc.CallOption) (*CreateSubscriptionResponse, error) {
	out := new(CreateSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/CreateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) GetSubscription(ctx context.Context, in *GetSubscriptionRequest, opts ...grpc.CallOption) (*Subscription, error) {
	out := new(Subscription)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/GetSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) ListSubscriptions(ctx context.Context, in *ListSubscriptionsRequest, opts ...grpc.CallOption) (*ListSubscriptionsResponse, error) {
	out := new(ListSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/ListSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) UpdateSubscription(ctx context.Context, in *UpdateSubscriptionRequest, opts ...grpc.CallOption) (*Subscription, error) {
	out := new(Subscription)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/UpdateSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) CancelSubscription(ctx context.Context, in *CancelSubscriptionRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/CancelSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) GetPaymentMethods(ctx context.Context, in *GetPaymentMethodsRequest, opts ...grpc.CallOption) (*GetPaymentMethodsResponse, error) {
	out := new(GetPaymentMethodsResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/GetPaymentMethods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) AddPaymentMethod(ctx context.Context, in *AddPaymentMethodRequest, opts ...grpc.CallOption) (*PaymentMethod, error) {
	out := new(PaymentMethod)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/AddPaymentMethod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) RemovePaymentMethod(ctx context.Context, in *RemovePaymentMethodRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/RemovePaymentMethod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) ConfirmBankTransfer(ctx context.Context, in *ConfirmBankTransferRequest, opts ...grpc.CallOption) (*ConfirmBankTransferResponse, error) {
	out := new(ConfirmBankTransferResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/ConfirmBankTransfer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditServiceClient) ProcessRecurringPayment(ctx context.Context, in *ProcessRecurringPaymentRequest, opts ...grpc.CallOption) (*ProcessRecurringPaymentResponse, error) {
	out := new(ProcessRecurringPaymentResponse)
	err := c.cc.Invoke(ctx, "/credit.v1.CreditService/ProcessRecurringPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CreditServiceServer is the server API for CreditService service.
// All implementations must embed UnimplementedCreditServiceServer
// for forward compatibility
type CreditServiceServer interface {
	// Basic credit operations
	GetUserCredit(context.Context, *GetUserCreditRequest) (*UserCredit, error)
	ValidateCredit(context.Context, *ValidateCreditRequest) (*ValidateCreditResponse, error)
	AddCredits(context.Context, *AddCreditsRequest) (*UserCredit, error)
	DeductCredits(context.Context, *DeductCreditsRequest) (*UserCredit, error)
	ListTransactions(context.Context, *ListTransactionsRequest) (*ListTransactionsResponse, error)
	// Subscription management
	CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error)
	GetSubscription(context.Context, *GetSubscriptionRequest) (*Subscription, error)
	ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error)
	UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*Subscription, error)
	CancelSubscription(context.Context, *CancelSubscriptionRequest) (*Empty, error)
	// Payment method management
	GetPaymentMethods(context.Context, *GetPaymentMethodsRequest) (*GetPaymentMethodsResponse, error)
	AddPaymentMethod(context.Context, *AddPaymentMethodRequest) (*PaymentMethod, error)
	RemovePaymentMethod(context.Context, *RemovePaymentMethodRequest) (*Empty, error)
	// Bank transfer operations
	ConfirmBankTransfer(context.Context, *ConfirmBankTransferRequest) (*ConfirmBankTransferResponse, error)
	// Recurring payment processing
	ProcessRecurringPayment(context.Context, *ProcessRecurringPaymentRequest) (*ProcessRecurringPaymentResponse, error)
	mustEmbedUnimplementedCreditServiceServer()
}

// UnimplementedCreditServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCreditServiceServer struct {
}

func (UnimplementedCreditServiceServer) GetUserCredit(context.Context, *GetUserCreditRequest) (*UserCredit, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCredit not implemented")
}
func (UnimplementedCreditServiceServer) ValidateCredit(context.Context, *ValidateCreditRequest) (*ValidateCreditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCredit not implemented")
}
func (UnimplementedCreditServiceServer) AddCredits(context.Context, *AddCreditsRequest) (*UserCredit, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCredits not implemented")
}
func (UnimplementedCreditServiceServer) DeductCredits(context.Context, *DeductCreditsRequest) (*UserCredit, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeductCredits not implemented")
}
func (UnimplementedCreditServiceServer) ListTransactions(context.Context, *ListTransactionsRequest) (*ListTransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTransactions not implemented")
}
func (UnimplementedCreditServiceServer) CreateSubscription(context.Context, *CreateSubscriptionRequest) (*CreateSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubscription not implemented")
}
func (UnimplementedCreditServiceServer) GetSubscription(context.Context, *GetSubscriptionRequest) (*Subscription, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubscription not implemented")
}
func (UnimplementedCreditServiceServer) ListSubscriptions(context.Context, *ListSubscriptionsRequest) (*ListSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedCreditServiceServer) UpdateSubscription(context.Context, *UpdateSubscriptionRequest) (*Subscription, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubscription not implemented")
}
func (UnimplementedCreditServiceServer) CancelSubscription(context.Context, *CancelSubscriptionRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSubscription not implemented")
}
func (UnimplementedCreditServiceServer) GetPaymentMethods(context.Context, *GetPaymentMethodsRequest) (*GetPaymentMethodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentMethods not implemented")
}
func (UnimplementedCreditServiceServer) AddPaymentMethod(context.Context, *AddPaymentMethodRequest) (*PaymentMethod, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPaymentMethod not implemented")
}
func (UnimplementedCreditServiceServer) RemovePaymentMethod(context.Context, *RemovePaymentMethodRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePaymentMethod not implemented")
}
func (UnimplementedCreditServiceServer) ConfirmBankTransfer(context.Context, *ConfirmBankTransferRequest) (*ConfirmBankTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmBankTransfer not implemented")
}
func (UnimplementedCreditServiceServer) ProcessRecurringPayment(context.Context, *ProcessRecurringPaymentRequest) (*ProcessRecurringPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRecurringPayment not implemented")
}
func (UnimplementedCreditServiceServer) mustEmbedUnimplementedCreditServiceServer() {}

// UnsafeCreditServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CreditServiceServer will
// result in compilation errors.
type UnsafeCreditServiceServer interface {
	mustEmbedUnimplementedCreditServiceServer()
}

func RegisterCreditServiceServer(s grpc.ServiceRegistrar, srv CreditServiceServer) {
	s.RegisterService(&CreditService_ServiceDesc, srv)
}

func _CreditService_GetUserCredit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCreditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).GetUserCredit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/GetUserCredit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).GetUserCredit(ctx, req.(*GetUserCreditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_ValidateCredit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCreditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).ValidateCredit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/ValidateCredit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).ValidateCredit(ctx, req.(*ValidateCreditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_AddCredits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCreditsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).AddCredits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/AddCredits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).AddCredits(ctx, req.(*AddCreditsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_DeductCredits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductCreditsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).DeductCredits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/DeductCredits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).DeductCredits(ctx, req.(*DeductCreditsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_ListTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).ListTransactions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/ListTransactions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).ListTransactions(ctx, req.(*ListTransactionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_CreateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).CreateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/CreateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).CreateSubscription(ctx, req.(*CreateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_GetSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).GetSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/GetSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).GetSubscription(ctx, req.(*GetSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/ListSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).ListSubscriptions(ctx, req.(*ListSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_UpdateSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).UpdateSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/UpdateSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).UpdateSubscription(ctx, req.(*UpdateSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_CancelSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).CancelSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/CancelSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).CancelSubscription(ctx, req.(*CancelSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_GetPaymentMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentMethodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).GetPaymentMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/GetPaymentMethods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).GetPaymentMethods(ctx, req.(*GetPaymentMethodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_AddPaymentMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPaymentMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).AddPaymentMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/AddPaymentMethod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).AddPaymentMethod(ctx, req.(*AddPaymentMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_RemovePaymentMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePaymentMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).RemovePaymentMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/RemovePaymentMethod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).RemovePaymentMethod(ctx, req.(*RemovePaymentMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_ConfirmBankTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmBankTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).ConfirmBankTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/ConfirmBankTransfer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).ConfirmBankTransfer(ctx, req.(*ConfirmBankTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditService_ProcessRecurringPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessRecurringPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditServiceServer).ProcessRecurringPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/credit.v1.CreditService/ProcessRecurringPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditServiceServer).ProcessRecurringPayment(ctx, req.(*ProcessRecurringPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CreditService_ServiceDesc is the grpc.ServiceDesc for CreditService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CreditService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "credit.v1.CreditService",
	HandlerType: (*CreditServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserCredit",
			Handler:    _CreditService_GetUserCredit_Handler,
		},
		{
			MethodName: "ValidateCredit",
			Handler:    _CreditService_ValidateCredit_Handler,
		},
		{
			MethodName: "AddCredits",
			Handler:    _CreditService_AddCredits_Handler,
		},
		{
			MethodName: "DeductCredits",
			Handler:    _CreditService_DeductCredits_Handler,
		},
		{
			MethodName: "ListTransactions",
			Handler:    _CreditService_ListTransactions_Handler,
		},
		{
			MethodName: "CreateSubscription",
			Handler:    _CreditService_CreateSubscription_Handler,
		},
		{
			MethodName: "GetSubscription",
			Handler:    _CreditService_GetSubscription_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _CreditService_ListSubscriptions_Handler,
		},
		{
			MethodName: "UpdateSubscription",
			Handler:    _CreditService_UpdateSubscription_Handler,
		},
		{
			MethodName: "CancelSubscription",
			Handler:    _CreditService_CancelSubscription_Handler,
		},
		{
			MethodName: "GetPaymentMethods",
			Handler:    _CreditService_GetPaymentMethods_Handler,
		},
		{
			MethodName: "AddPaymentMethod",
			Handler:    _CreditService_AddPaymentMethod_Handler,
		},
		{
			MethodName: "RemovePaymentMethod",
			Handler:    _CreditService_RemovePaymentMethod_Handler,
		},
		{
			MethodName: "ConfirmBankTransfer",
			Handler:    _CreditService_ConfirmBankTransfer_Handler,
		},
		{
			MethodName: "ProcessRecurringPayment",
			Handler:    _CreditService_ProcessRecurringPayment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "credit/v1/credit.proto",
}

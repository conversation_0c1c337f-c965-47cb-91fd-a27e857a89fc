package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Folder holds the schema definition for the Folder entity.
type Folder struct {
	ent.Schema
}

// Fields of the Folder.
func (Folder) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("Owner of the folder"),
		field.UUID("parent_id", uuid.UUID{}).
			Optional().
			Nillable().
			Comment("Parent folder ID for nested structure"),
		field.String("name").
			NotEmpty().
			MaxLen(100).
			Comment("Folder name"),
		field.String("description").
			Optional().
			MaxLen(500).
			Comment("Folder description"),
		field.String("path").
			NotEmpty().
			Comment("Full path of the folder"),
		field.Int("level").
			Default(0).
			Comment("Folder depth level"),
		field.Bool("is_shared").
			Default(false).
			Comment("Whether folder is shared with others"),
		field.JSON("shared_with", []string{}).
			Optional().
			Comment("List of user IDs folder is shared with"),
		field.JSON("permissions", []string{}).
			Optional().
			Comment("Folder permissions"),
		field.Int64("asset_count").
			Default(0).
			Comment("Number of assets in folder"),
		field.Int64("total_size").
			Default(0).
			Comment("Total size of assets in folder"),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional().
			Nillable(),
	}
}

// Edges of the Folder.
func (Folder) Edges() []ent.Edge {
	return []ent.Edge{
		// Self-referencing edge for parent-child relationship
		edge.To("children", Folder.Type).
			From("parent").
			Unique().
			Field("parent_id"),

		// Edge to assets in this folder
		edge.To("assets", Asset.Type),
	}
}

// Indexes of the Folder.
func (Folder) Indexes() []ent.Index {
	return []ent.Index{
		// Index for user folders
		index.Fields("user_id"),

		// Index for parent-child relationship
		index.Fields("parent_id"),

		// Index for folder path
		index.Fields("path"),

		// Composite index for user + parent
		index.Fields("user_id", "parent_id"),

		// Index for soft delete
		index.Fields("deleted_at"),

		// Unique constraint for folder name within same parent
		index.Fields("user_id", "parent_id", "name").
			Unique(),
	}
}

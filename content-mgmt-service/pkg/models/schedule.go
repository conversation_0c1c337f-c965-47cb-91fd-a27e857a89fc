package models

import (
	"time"
)

// Schedule represents a scheduled content publication
type Schedule struct {
	ID             string    `json:"id"`
	UserID         string    `json:"user_id"`
	WorkspaceID    string    `json:"workspace_id,omitempty"`
	PostID         string    `json:"post_id,omitempty"`
	TemplateID     string    `json:"template_id,omitempty"`
	Title          string    `json:"title"`
	Description    string    `json:"description,omitempty"`
	Content        string    `json:"content"`
	MediaURLs      []string  `json:"media_urls,omitempty"`
	Platforms      []string  `json:"platforms"`
	ScheduledAt    time.Time `json:"scheduled_at"`
	Status         string    `json:"status"` // pending, active, paused, completed, failed
	Timezone       string    `json:"timezone"`
	RecurrenceRule string    `json:"recurrence_rule,omitempty"` // RRULE format
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ScheduleExecution represents a schedule execution
type ScheduleExecution struct {
	ID          string                 `json:"id"`
	ScheduleID  string                 `json:"schedule_id"`
	Status      string                 `json:"status"` // pending, running, completed, failed
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Results     map[string]interface{} `json:"results,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}

// CreateScheduleRequest represents a request to create a schedule
type CreateScheduleRequest struct {
	UserID         string    `json:"user_id"`
	WorkspaceID    string    `json:"workspace_id,omitempty"`
	PostID         string    `json:"post_id,omitempty"`
	TemplateID     string    `json:"template_id,omitempty"`
	Title          string    `json:"title" binding:"required"`
	Description    string    `json:"description,omitempty"`
	Content        string    `json:"content" binding:"required"`
	MediaURLs      []string  `json:"media_urls,omitempty"`
	Platforms      []string  `json:"platforms" binding:"required"`
	ScheduledAt    time.Time `json:"scheduled_at" binding:"required"`
	Timezone       string    `json:"timezone"`
	RecurrenceRule string    `json:"recurrence_rule,omitempty"`
}

// UpdateScheduleRequest represents a request to update a schedule
type UpdateScheduleRequest struct {
	ScheduleID     string    `json:"schedule_id"`
	UserID         string    `json:"user_id"`
	Title          string    `json:"title,omitempty"`
	Description    string    `json:"description,omitempty"`
	Content        string    `json:"content,omitempty"`
	MediaURLs      []string  `json:"media_urls,omitempty"`
	Platforms      []string  `json:"platforms,omitempty"`
	ScheduledAt    time.Time `json:"scheduled_at,omitempty"`
	Timezone       string    `json:"timezone,omitempty"`
	RecurrenceRule string    `json:"recurrence_rule,omitempty"`
}

// ListSchedulesRequest represents a request to list schedules
type ListSchedulesRequest struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	Status      string `json:"status,omitempty"`
	Platform    string `json:"platform,omitempty"`
	Page        int    `json:"page"`
	Limit       int    `json:"limit"`
	SortBy      string `json:"sort_by,omitempty"`
	SortOrder   string `json:"sort_order,omitempty"`
}

// ScheduleResponse represents a schedule response
type ScheduleResponse struct {
	Schedule *Schedule `json:"schedule"`
}

// ListSchedulesResponse represents a list of schedules response
type ListSchedulesResponse struct {
	Schedules  []*Schedule `json:"schedules"`
	Total      int         `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// ScheduleExecutionResponse represents a schedule execution response
type ScheduleExecutionResponse struct {
	Execution *ScheduleExecution `json:"execution"`
}

// ListScheduleExecutionsResponse represents a list of schedule executions response
type ListScheduleExecutionsResponse struct {
	Executions []*ScheduleExecution `json:"executions"`
	Total      int                  `json:"total"`
	Page       int                  `json:"page"`
	Limit      int                  `json:"limit"`
	TotalPages int                  `json:"total_pages"`
}

// ScheduleStatusResponse represents a schedule status response
type ScheduleStatusResponse struct {
	ScheduleID string     `json:"schedule_id"`
	Status     string     `json:"status"`
	NextRun    *time.Time `json:"next_run,omitempty"`
	LastRun    *time.Time `json:"last_run,omitempty"`
	RunCount   int        `json:"run_count"`
}

// ScheduleCalendarRequest represents a request for schedule calendar
// Note: This extends the existing ScheduleCalendarRequest in post.go
// ScheduleCalendarResponse represents a schedule calendar response
// Note: This extends the existing ScheduleCalendarResponse in post.go

// ScheduleCalendarEvent represents a calendar event
type ScheduleCalendarEvent struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description,omitempty"`
	Start       time.Time `json:"start"`
	End         time.Time `json:"end"`
	Status      string    `json:"status"`
	Platforms   []string  `json:"platforms"`
}

// ScheduleTimelineRequest represents a request for schedule timeline
type ScheduleTimelineRequest struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	Period      string `json:"period"` // day, week, month
	Date        string `json:"date,omitempty"`
}

// ScheduleTimelineResponse represents a schedule timeline response
type ScheduleTimelineResponse struct {
	Timeline []ScheduleTimelineItem `json:"timeline"`
}

// ScheduleTimelineItem represents a timeline item
type ScheduleTimelineItem struct {
	Date      string      `json:"date"`
	Schedules []*Schedule `json:"schedules"`
	Count     int         `json:"count"`
}

// ScheduleAnalyticsRequest represents a request for schedule analytics
type ScheduleAnalyticsRequest struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	ScheduleID  string `json:"schedule_id,omitempty"`
	Period      string `json:"period"` // 7d, 30d, 90d, 1y
	Platform    string `json:"platform,omitempty"`
}

// ScheduleAnalyticsResponse represents schedule analytics response
type ScheduleAnalyticsResponse struct {
	TotalSchedules     int                        `json:"total_schedules"`
	ActiveSchedules    int                        `json:"active_schedules"`
	CompletedSchedules int                        `json:"completed_schedules"`
	FailedSchedules    int                        `json:"failed_schedules"`
	SuccessRate        float64                    `json:"success_rate"`
	PlatformStats      map[string]int             `json:"platform_stats"`
	DailyStats         []ScheduleDailyStat        `json:"daily_stats"`
	PerformanceMetrics SchedulePerformanceMetrics `json:"performance_metrics"`
}

// ScheduleDailyStat represents daily schedule statistics
type ScheduleDailyStat struct {
	Date      string `json:"date"`
	Scheduled int    `json:"scheduled"`
	Executed  int    `json:"executed"`
	Failed    int    `json:"failed"`
}

// SchedulePerformanceMetrics represents schedule performance metrics
type SchedulePerformanceMetrics struct {
	AverageExecutionTime float64 `json:"average_execution_time"`
	TotalExecutions      int     `json:"total_executions"`
	SuccessfulExecutions int     `json:"successful_executions"`
	FailedExecutions     int     `json:"failed_executions"`
}

// SchedulePerformanceResponse represents schedule performance response
type SchedulePerformanceResponse struct {
	ScheduleID string                     `json:"schedule_id"`
	Metrics    SchedulePerformanceMetrics `json:"metrics"`
	History    []ScheduleExecution        `json:"history"`
}

// BulkOperationResult is defined in common.go

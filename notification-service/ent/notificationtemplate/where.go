// Code generated by ent, DO NOT EDIT.

package notificationtemplate

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldID, id))
}

// EventType applies equality check predicate on the "event_type" field. It's identical to EventTypeEQ.
func EventType(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldEventType, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldDescription, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldIsActive, v))
}

// Language applies equality check predicate on the "language" field. It's identical to LanguageEQ.
func Language(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldLanguage, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldVersion, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldDeletedAt, v))
}

// EventTypeEQ applies the EQ predicate on the "event_type" field.
func EventTypeEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldEventType, v))
}

// EventTypeNEQ applies the NEQ predicate on the "event_type" field.
func EventTypeNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldEventType, v))
}

// EventTypeIn applies the In predicate on the "event_type" field.
func EventTypeIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldEventType, vs...))
}

// EventTypeNotIn applies the NotIn predicate on the "event_type" field.
func EventTypeNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldEventType, vs...))
}

// EventTypeGT applies the GT predicate on the "event_type" field.
func EventTypeGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldEventType, v))
}

// EventTypeGTE applies the GTE predicate on the "event_type" field.
func EventTypeGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldEventType, v))
}

// EventTypeLT applies the LT predicate on the "event_type" field.
func EventTypeLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldEventType, v))
}

// EventTypeLTE applies the LTE predicate on the "event_type" field.
func EventTypeLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldEventType, v))
}

// EventTypeContains applies the Contains predicate on the "event_type" field.
func EventTypeContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldEventType, v))
}

// EventTypeHasPrefix applies the HasPrefix predicate on the "event_type" field.
func EventTypeHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldEventType, v))
}

// EventTypeHasSuffix applies the HasSuffix predicate on the "event_type" field.
func EventTypeHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldEventType, v))
}

// EventTypeEqualFold applies the EqualFold predicate on the "event_type" field.
func EventTypeEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldEventType, v))
}

// EventTypeContainsFold applies the ContainsFold predicate on the "event_type" field.
func EventTypeContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldEventType, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldDescription, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldIsActive, v))
}

// VariablesIsNil applies the IsNil predicate on the "variables" field.
func VariablesIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldVariables))
}

// VariablesNotNil applies the NotNil predicate on the "variables" field.
func VariablesNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldVariables))
}

// DefaultChannelsIsNil applies the IsNil predicate on the "default_channels" field.
func DefaultChannelsIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldDefaultChannels))
}

// DefaultChannelsNotNil applies the NotNil predicate on the "default_channels" field.
func DefaultChannelsNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldDefaultChannels))
}

// DefaultPriorityEQ applies the EQ predicate on the "default_priority" field.
func DefaultPriorityEQ(v DefaultPriority) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldDefaultPriority, v))
}

// DefaultPriorityNEQ applies the NEQ predicate on the "default_priority" field.
func DefaultPriorityNEQ(v DefaultPriority) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldDefaultPriority, v))
}

// DefaultPriorityIn applies the In predicate on the "default_priority" field.
func DefaultPriorityIn(vs ...DefaultPriority) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldDefaultPriority, vs...))
}

// DefaultPriorityNotIn applies the NotIn predicate on the "default_priority" field.
func DefaultPriorityNotIn(vs ...DefaultPriority) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldDefaultPriority, vs...))
}

// LanguageEQ applies the EQ predicate on the "language" field.
func LanguageEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldLanguage, v))
}

// LanguageNEQ applies the NEQ predicate on the "language" field.
func LanguageNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldLanguage, v))
}

// LanguageIn applies the In predicate on the "language" field.
func LanguageIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldLanguage, vs...))
}

// LanguageNotIn applies the NotIn predicate on the "language" field.
func LanguageNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldLanguage, vs...))
}

// LanguageGT applies the GT predicate on the "language" field.
func LanguageGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldLanguage, v))
}

// LanguageGTE applies the GTE predicate on the "language" field.
func LanguageGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldLanguage, v))
}

// LanguageLT applies the LT predicate on the "language" field.
func LanguageLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldLanguage, v))
}

// LanguageLTE applies the LTE predicate on the "language" field.
func LanguageLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldLanguage, v))
}

// LanguageContains applies the Contains predicate on the "language" field.
func LanguageContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldLanguage, v))
}

// LanguageHasPrefix applies the HasPrefix predicate on the "language" field.
func LanguageHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldLanguage, v))
}

// LanguageHasSuffix applies the HasSuffix predicate on the "language" field.
func LanguageHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldLanguage, v))
}

// LanguageEqualFold applies the EqualFold predicate on the "language" field.
func LanguageEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldLanguage, v))
}

// LanguageContainsFold applies the ContainsFold predicate on the "language" field.
func LanguageContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldLanguage, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldVersion, v))
}

// VersionContains applies the Contains predicate on the "version" field.
func VersionContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldVersion, v))
}

// VersionHasPrefix applies the HasPrefix predicate on the "version" field.
func VersionHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldVersion, v))
}

// VersionHasSuffix applies the HasSuffix predicate on the "version" field.
func VersionHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldVersion, v))
}

// VersionEqualFold applies the EqualFold predicate on the "version" field.
func VersionEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldVersion, v))
}

// VersionContainsFold applies the ContainsFold predicate on the "version" field.
func VersionContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldVersion, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldMetadata))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldCreatedBy))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldContainsFold(FieldCreatedBy, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.NotificationTemplate) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.NotificationTemplate) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.NotificationTemplate) predicate.NotificationTemplate {
	return predicate.NotificationTemplate(sql.NotPredicates(p))
}

package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// YouTubeOAuth handles YouTube OAuth flow
type YouTubeOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewYouTubeOAuth creates a new YouTube OAuth handler
func NewYouTubeOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) Provider {
	return &YouTubeOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// YouTubeTokenResponse represents YouTube token response
type YouTubeTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// YouTubeUserProfile represents YouTube user profile
type YouTubeUserProfile struct {
	ID      string `json:"id"`
	Snippet struct {
		Title       string `json:"title"`
		Description string `json:"description"`
		Thumbnails  struct {
			Default struct {
				URL string `json:"url"`
			} `json:"default"`
		} `json:"thumbnails"`
	} `json:"snippet"`
}

// GetAuthURL generates YouTube OAuth authorization URL
func (y *YouTubeOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://accounts.google.com/o/oauth2/v2/auth"

	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", y.clientID)
	params.Set("redirect_uri", y.redirectURL)
	params.Set("state", state)
	params.Set("access_type", "offline")
	params.Set("prompt", "consent")

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, " "))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "https://www.googleapis.com/auth/youtube.upload https://www.googleapis.com/auth/youtube.readonly")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (y *YouTubeOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://oauth2.googleapis.com/token"

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", y.redirectURL)
	data.Set("client_id", y.clientID)
	data.Set("client_secret", y.clientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := y.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp YouTubeTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	y.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.ExpiresIn,
		"scope":      tokenResp.Scope,
	}).Info("YouTube token exchanged successfully")

	// Convert YouTubeTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RefreshToken refreshes YouTube access token
func (y *YouTubeOAuth) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	tokenURL := "https://oauth2.googleapis.com/token"

	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)
	data.Set("client_id", y.clientID)
	data.Set("client_secret", y.clientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := y.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp YouTubeTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert YouTubeTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RevokeToken revokes YouTube access token
func (y *YouTubeOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	revokeURL := "https://oauth2.googleapis.com/revoke"

	params := url.Values{}
	params.Set("token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "POST", fmt.Sprintf("%s?%s", revokeURL, params.Encode()), nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := y.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("token revocation failed with status: %d", resp.StatusCode)
	}

	y.logger.Info("YouTube token revoked successfully")
	return nil
}

// GetUserProfile implements Provider interface
func (y *YouTubeOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	channelURL := "https://www.googleapis.com/youtube/v3/channels"

	params := url.Values{}
	params.Set("part", "snippet")
	params.Set("mine", "true")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", channelURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := y.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var channelResp struct {
		Items []YouTubeUserProfile `json:"items"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&channelResp); err != nil {
		return nil, fmt.Errorf("failed to decode user profile: %w", err)
	}

	if len(channelResp.Items) == 0 {
		return nil, fmt.Errorf("no YouTube channel found")
	}

	channel := channelResp.Items[0]

	return &UserProfile{
		ID:          channel.ID,
		Username:    channel.Snippet.Title,
		DisplayName: channel.Snippet.Title,
		AvatarURL:   channel.Snippet.Thumbnails.Default.URL,
	}, nil
}

// GetPermissions implements Provider interface
func (y *YouTubeOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	// YouTube doesn't have a specific permissions endpoint
	// Return default permissions based on successful authentication
	return []string{"https://www.googleapis.com/auth/youtube.upload", "https://www.googleapis.com/auth/youtube.readonly"}, nil
}

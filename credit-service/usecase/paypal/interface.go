package paypal

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the PayPal payment business logic interface
type UseCase interface {
	// Core payment operations
	InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error)
	ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error)
	GetPaymentStatus(ctx context.Context, req *models.GetPayPalPaymentStatusRequest) (*models.GetPayPalPaymentStatusResponse, error)
	RefundPayment(ctx context.Context, req *models.RefundPayPalPaymentRequest) (*models.RefundPayPalPaymentResponse, error)

	// Webhook handling
	HandleWebhook(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error)
}

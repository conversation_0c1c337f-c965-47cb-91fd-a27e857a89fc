// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
	"github.com/social-content-ai/content-mgmt-service/ent/schema"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	postFields := schema.Post{}.Fields()
	_ = postFields
	// postDescWorkspaceID is the schema descriptor for workspace_id field.
	postDescWorkspaceID := postFields[2].Descriptor()
	// post.WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	post.WorkspaceIDValidator = postDescWorkspaceID.Validators[0].(func(string) error)
	// postDescTemplateID is the schema descriptor for template_id field.
	postDescTemplateID := postFields[3].Descriptor()
	// post.TemplateIDValidator is a validator for the "template_id" field. It is called by the builders before save.
	post.TemplateIDValidator = postDescTemplateID.Validators[0].(func(string) error)
	// postDescGenerationID is the schema descriptor for generation_id field.
	postDescGenerationID := postFields[4].Descriptor()
	// post.GenerationIDValidator is a validator for the "generation_id" field. It is called by the builders before save.
	post.GenerationIDValidator = postDescGenerationID.Validators[0].(func(string) error)
	// postDescTitle is the schema descriptor for title field.
	postDescTitle := postFields[5].Descriptor()
	// post.TitleValidator is a validator for the "title" field. It is called by the builders before save.
	post.TitleValidator = postDescTitle.Validators[0].(func(string) error)
	// postDescLanguage is the schema descriptor for language field.
	postDescLanguage := postFields[18].Descriptor()
	// post.DefaultLanguage holds the default value on creation for the language field.
	post.DefaultLanguage = postDescLanguage.Default.(string)
	// post.LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	post.LanguageValidator = postDescLanguage.Validators[0].(func(string) error)
	// postDescCreatedAt is the schema descriptor for created_at field.
	postDescCreatedAt := postFields[21].Descriptor()
	// post.DefaultCreatedAt holds the default value on creation for the created_at field.
	post.DefaultCreatedAt = postDescCreatedAt.Default.(func() time.Time)
	// postDescUpdatedAt is the schema descriptor for updated_at field.
	postDescUpdatedAt := postFields[22].Descriptor()
	// post.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	post.DefaultUpdatedAt = postDescUpdatedAt.Default.(func() time.Time)
	// post.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	post.UpdateDefaultUpdatedAt = postDescUpdatedAt.UpdateDefault.(func() time.Time)
	// postDescID is the schema descriptor for id field.
	postDescID := postFields[0].Descriptor()
	// post.DefaultID holds the default value on creation for the id field.
	post.DefaultID = postDescID.Default.(func() uuid.UUID)
	templateFields := schema.Template{}.Fields()
	_ = templateFields
	// templateDescWorkspaceID is the schema descriptor for workspace_id field.
	templateDescWorkspaceID := templateFields[2].Descriptor()
	// template.WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	template.WorkspaceIDValidator = templateDescWorkspaceID.Validators[0].(func(string) error)
	// templateDescName is the schema descriptor for name field.
	templateDescName := templateFields[3].Descriptor()
	// template.NameValidator is a validator for the "name" field. It is called by the builders before save.
	template.NameValidator = templateDescName.Validators[0].(func(string) error)
	// templateDescIsPublic is the schema descriptor for is_public field.
	templateDescIsPublic := templateFields[13].Descriptor()
	// template.DefaultIsPublic holds the default value on creation for the is_public field.
	template.DefaultIsPublic = templateDescIsPublic.Default.(bool)
	// templateDescIsFeatured is the schema descriptor for is_featured field.
	templateDescIsFeatured := templateFields[14].Descriptor()
	// template.DefaultIsFeatured holds the default value on creation for the is_featured field.
	template.DefaultIsFeatured = templateDescIsFeatured.Default.(bool)
	// templateDescUsageCount is the schema descriptor for usage_count field.
	templateDescUsageCount := templateFields[15].Descriptor()
	// template.DefaultUsageCount holds the default value on creation for the usage_count field.
	template.DefaultUsageCount = templateDescUsageCount.Default.(int)
	// templateDescRating is the schema descriptor for rating field.
	templateDescRating := templateFields[16].Descriptor()
	// template.DefaultRating holds the default value on creation for the rating field.
	template.DefaultRating = templateDescRating.Default.(float64)
	// template.RatingValidator is a validator for the "rating" field. It is called by the builders before save.
	template.RatingValidator = templateDescRating.Validators[0].(func(float64) error)
	// templateDescRatingCount is the schema descriptor for rating_count field.
	templateDescRatingCount := templateFields[17].Descriptor()
	// template.DefaultRatingCount holds the default value on creation for the rating_count field.
	template.DefaultRatingCount = templateDescRatingCount.Default.(int)
	// templateDescLanguage is the schema descriptor for language field.
	templateDescLanguage := templateFields[19].Descriptor()
	// template.DefaultLanguage holds the default value on creation for the language field.
	template.DefaultLanguage = templateDescLanguage.Default.(string)
	// template.LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	template.LanguageValidator = templateDescLanguage.Validators[0].(func(string) error)
	// templateDescRagTrained is the schema descriptor for rag_trained field.
	templateDescRagTrained := templateFields[21].Descriptor()
	// template.DefaultRagTrained holds the default value on creation for the rag_trained field.
	template.DefaultRagTrained = templateDescRagTrained.Default.(bool)
	// templateDescCreatedAt is the schema descriptor for created_at field.
	templateDescCreatedAt := templateFields[24].Descriptor()
	// template.DefaultCreatedAt holds the default value on creation for the created_at field.
	template.DefaultCreatedAt = templateDescCreatedAt.Default.(func() time.Time)
	// templateDescUpdatedAt is the schema descriptor for updated_at field.
	templateDescUpdatedAt := templateFields[25].Descriptor()
	// template.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	template.DefaultUpdatedAt = templateDescUpdatedAt.Default.(func() time.Time)
	// template.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	template.UpdateDefaultUpdatedAt = templateDescUpdatedAt.UpdateDefault.(func() time.Time)
	// templateDescID is the schema descriptor for id field.
	templateDescID := templateFields[0].Descriptor()
	// template.DefaultID holds the default value on creation for the id field.
	template.DefaultID = templateDescID.Default.(func() uuid.UUID)
	workspaceFields := schema.Workspace{}.Fields()
	_ = workspaceFields
	// workspaceDescName is the schema descriptor for name field.
	workspaceDescName := workspaceFields[2].Descriptor()
	// workspace.NameValidator is a validator for the "name" field. It is called by the builders before save.
	workspace.NameValidator = workspaceDescName.Validators[0].(func(string) error)
	// workspaceDescColor is the schema descriptor for color field.
	workspaceDescColor := workspaceFields[4].Descriptor()
	// workspace.DefaultColor holds the default value on creation for the color field.
	workspace.DefaultColor = workspaceDescColor.Default.(string)
	// workspace.ColorValidator is a validator for the "color" field. It is called by the builders before save.
	workspace.ColorValidator = workspaceDescColor.Validators[0].(func(string) error)
	// workspaceDescIcon is the schema descriptor for icon field.
	workspaceDescIcon := workspaceFields[5].Descriptor()
	// workspace.IconValidator is a validator for the "icon" field. It is called by the builders before save.
	workspace.IconValidator = workspaceDescIcon.Validators[0].(func(string) error)
	// workspaceDescIsPersonal is the schema descriptor for is_personal field.
	workspaceDescIsPersonal := workspaceFields[9].Descriptor()
	// workspace.DefaultIsPersonal holds the default value on creation for the is_personal field.
	workspace.DefaultIsPersonal = workspaceDescIsPersonal.Default.(bool)
	// workspaceDescIsActive is the schema descriptor for is_active field.
	workspaceDescIsActive := workspaceFields[10].Descriptor()
	// workspace.DefaultIsActive holds the default value on creation for the is_active field.
	workspace.DefaultIsActive = workspaceDescIsActive.Default.(bool)
	// workspaceDescPostCount is the schema descriptor for post_count field.
	workspaceDescPostCount := workspaceFields[11].Descriptor()
	// workspace.DefaultPostCount holds the default value on creation for the post_count field.
	workspace.DefaultPostCount = workspaceDescPostCount.Default.(int)
	// workspaceDescTemplateCount is the schema descriptor for template_count field.
	workspaceDescTemplateCount := workspaceFields[12].Descriptor()
	// workspace.DefaultTemplateCount holds the default value on creation for the template_count field.
	workspace.DefaultTemplateCount = workspaceDescTemplateCount.Default.(int)
	// workspaceDescCreatedAt is the schema descriptor for created_at field.
	workspaceDescCreatedAt := workspaceFields[15].Descriptor()
	// workspace.DefaultCreatedAt holds the default value on creation for the created_at field.
	workspace.DefaultCreatedAt = workspaceDescCreatedAt.Default.(func() time.Time)
	// workspaceDescUpdatedAt is the schema descriptor for updated_at field.
	workspaceDescUpdatedAt := workspaceFields[16].Descriptor()
	// workspace.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workspace.DefaultUpdatedAt = workspaceDescUpdatedAt.Default.(func() time.Time)
	// workspace.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	workspace.UpdateDefaultUpdatedAt = workspaceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// workspaceDescID is the schema descriptor for id field.
	workspaceDescID := workspaceFields[0].Descriptor()
	// workspace.DefaultID holds the default value on creation for the id field.
	workspace.DefaultID = workspaceDescID.Default.(func() uuid.UUID)
}

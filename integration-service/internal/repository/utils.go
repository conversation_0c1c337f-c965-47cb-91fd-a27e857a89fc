package repository

import (
	"time"

	"github.com/google/uuid"
)

// parseUUID parses a string to UUID
func parseUUID(s string) uuid.UUID {
	id, err := uuid.Parse(s)
	if err != nil {
		return uuid.Nil
	}
	return id
}

// parseTime parses various time formats to time.Time
func parseTime(v interface{}) (time.Time, bool) {
	switch t := v.(type) {
	case time.Time:
		return t, true
	case *time.Time:
		if t != nil {
			return *t, true
		}
		return time.Time{}, false
	case string:
		if parsed, err := time.Parse(time.RFC3339, t); err == nil {
			return parsed, true
		}
		return time.Time{}, false
	case int64:
		return time.Unix(t, 0), true
	default:
		return time.Time{}, false
	}
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Subscription holds the schema definition for the Subscription entity.
type Subscription struct {
	ent.Schema
}

// Fields of the Subscription.
func (Subscription) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.UUID("plan_id", uuid.UUID{}),
		field.Enum("status").
			Values("active", "cancelled", "expired", "trial", "past_due").
			Default("trial"),
		field.Time("current_period_start"),
		field.Time("current_period_end"),
		field.Time("trial_start").
			Optional(),
		field.Time("trial_end").
			Optional(),
		field.Time("cancelled_at").
			Optional(),
		field.Bool("cancel_at_period_end").
			Default(false),
		field.String("payment_method").
			MaxLen(50),
		field.JSON("billing_address", map[string]interface{}{}).
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("last_billed_at").
			Optional(),
		field.Time("next_billing_at").
			Optional(),
		field.String("external_subscription_id").
			MaxLen(100).
			Optional(), // For payment provider subscription ID
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Subscription.
func (Subscription) Edges() []ent.Edge {
	return nil
}

// Indexes of the Subscription.
func (Subscription) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("plan_id"),
		index.Fields("status"),
		index.Fields("current_period_end"),
		index.Fields("trial_end"),
		index.Fields("next_billing_at"),
		index.Fields("external_subscription_id"),
		index.Fields("created_at"),
	}
}

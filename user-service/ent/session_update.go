// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/predicate"
	"github.com/social-content-ai/user-service/ent/session"
	"github.com/social-content-ai/user-service/ent/user"
)

// SessionUpdate is the builder for updating Session entities.
type SessionUpdate struct {
	config
	hooks    []Hook
	mutation *SessionMutation
}

// Where appends a list predicates to the SessionUpdate builder.
func (su *SessionUpdate) Where(ps ...predicate.Session) *SessionUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetUserID sets the "user_id" field.
func (su *SessionUpdate) SetUserID(u uuid.UUID) *SessionUpdate {
	su.mutation.SetUserID(u)
	return su
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (su *SessionUpdate) SetNillableUserID(u *uuid.UUID) *SessionUpdate {
	if u != nil {
		su.SetUserID(*u)
	}
	return su
}

// SetDeviceInfo sets the "device_info" field.
func (su *SessionUpdate) SetDeviceInfo(s string) *SessionUpdate {
	su.mutation.SetDeviceInfo(s)
	return su
}

// SetNillableDeviceInfo sets the "device_info" field if the given value is not nil.
func (su *SessionUpdate) SetNillableDeviceInfo(s *string) *SessionUpdate {
	if s != nil {
		su.SetDeviceInfo(*s)
	}
	return su
}

// ClearDeviceInfo clears the value of the "device_info" field.
func (su *SessionUpdate) ClearDeviceInfo() *SessionUpdate {
	su.mutation.ClearDeviceInfo()
	return su
}

// SetStatus sets the "status" field.
func (su *SessionUpdate) SetStatus(s session.Status) *SessionUpdate {
	su.mutation.SetStatus(s)
	return su
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (su *SessionUpdate) SetNillableStatus(s *session.Status) *SessionUpdate {
	if s != nil {
		su.SetStatus(*s)
	}
	return su
}

// SetIPAddress sets the "ip_address" field.
func (su *SessionUpdate) SetIPAddress(s string) *SessionUpdate {
	su.mutation.SetIPAddress(s)
	return su
}

// SetNillableIPAddress sets the "ip_address" field if the given value is not nil.
func (su *SessionUpdate) SetNillableIPAddress(s *string) *SessionUpdate {
	if s != nil {
		su.SetIPAddress(*s)
	}
	return su
}

// ClearIPAddress clears the value of the "ip_address" field.
func (su *SessionUpdate) ClearIPAddress() *SessionUpdate {
	su.mutation.ClearIPAddress()
	return su
}

// SetLoginTime sets the "login_time" field.
func (su *SessionUpdate) SetLoginTime(t time.Time) *SessionUpdate {
	su.mutation.SetLoginTime(t)
	return su
}

// SetNillableLoginTime sets the "login_time" field if the given value is not nil.
func (su *SessionUpdate) SetNillableLoginTime(t *time.Time) *SessionUpdate {
	if t != nil {
		su.SetLoginTime(*t)
	}
	return su
}

// SetLogoutTime sets the "logout_time" field.
func (su *SessionUpdate) SetLogoutTime(t time.Time) *SessionUpdate {
	su.mutation.SetLogoutTime(t)
	return su
}

// SetNillableLogoutTime sets the "logout_time" field if the given value is not nil.
func (su *SessionUpdate) SetNillableLogoutTime(t *time.Time) *SessionUpdate {
	if t != nil {
		su.SetLogoutTime(*t)
	}
	return su
}

// ClearLogoutTime clears the value of the "logout_time" field.
func (su *SessionUpdate) ClearLogoutTime() *SessionUpdate {
	su.mutation.ClearLogoutTime()
	return su
}

// SetExpiresAt sets the "expires_at" field.
func (su *SessionUpdate) SetExpiresAt(t time.Time) *SessionUpdate {
	su.mutation.SetExpiresAt(t)
	return su
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (su *SessionUpdate) SetNillableExpiresAt(t *time.Time) *SessionUpdate {
	if t != nil {
		su.SetExpiresAt(*t)
	}
	return su
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (su *SessionUpdate) ClearExpiresAt() *SessionUpdate {
	su.mutation.ClearExpiresAt()
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *SessionUpdate) SetUpdatedAt(t time.Time) *SessionUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// SetUser sets the "user" edge to the User entity.
func (su *SessionUpdate) SetUser(u *User) *SessionUpdate {
	return su.SetUserID(u.ID)
}

// Mutation returns the SessionMutation object of the builder.
func (su *SessionUpdate) Mutation() *SessionMutation {
	return su.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (su *SessionUpdate) ClearUser() *SessionUpdate {
	su.mutation.ClearUser()
	return su
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *SessionUpdate) Save(ctx context.Context) (int, error) {
	su.defaults()
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *SessionUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *SessionUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *SessionUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *SessionUpdate) defaults() {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		v := session.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (su *SessionUpdate) check() error {
	if v, ok := su.mutation.Status(); ok {
		if err := session.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Session.status": %w`, err)}
		}
	}
	if v, ok := su.mutation.IPAddress(); ok {
		if err := session.IPAddressValidator(v); err != nil {
			return &ValidationError{Name: "ip_address", err: fmt.Errorf(`ent: validator failed for field "Session.ip_address": %w`, err)}
		}
	}
	if su.mutation.UserCleared() && len(su.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Session.user"`)
	}
	return nil
}

func (su *SessionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := su.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(session.Table, session.Columns, sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.DeviceInfo(); ok {
		_spec.SetField(session.FieldDeviceInfo, field.TypeString, value)
	}
	if su.mutation.DeviceInfoCleared() {
		_spec.ClearField(session.FieldDeviceInfo, field.TypeString)
	}
	if value, ok := su.mutation.Status(); ok {
		_spec.SetField(session.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := su.mutation.IPAddress(); ok {
		_spec.SetField(session.FieldIPAddress, field.TypeString, value)
	}
	if su.mutation.IPAddressCleared() {
		_spec.ClearField(session.FieldIPAddress, field.TypeString)
	}
	if value, ok := su.mutation.LoginTime(); ok {
		_spec.SetField(session.FieldLoginTime, field.TypeTime, value)
	}
	if value, ok := su.mutation.LogoutTime(); ok {
		_spec.SetField(session.FieldLogoutTime, field.TypeTime, value)
	}
	if su.mutation.LogoutTimeCleared() {
		_spec.ClearField(session.FieldLogoutTime, field.TypeTime)
	}
	if value, ok := su.mutation.ExpiresAt(); ok {
		_spec.SetField(session.FieldExpiresAt, field.TypeTime, value)
	}
	if su.mutation.ExpiresAtCleared() {
		_spec.ClearField(session.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(session.FieldUpdatedAt, field.TypeTime, value)
	}
	if su.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   session.UserTable,
			Columns: []string{session.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   session.UserTable,
			Columns: []string{session.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{session.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// SessionUpdateOne is the builder for updating a single Session entity.
type SessionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SessionMutation
}

// SetUserID sets the "user_id" field.
func (suo *SessionUpdateOne) SetUserID(u uuid.UUID) *SessionUpdateOne {
	suo.mutation.SetUserID(u)
	return suo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableUserID(u *uuid.UUID) *SessionUpdateOne {
	if u != nil {
		suo.SetUserID(*u)
	}
	return suo
}

// SetDeviceInfo sets the "device_info" field.
func (suo *SessionUpdateOne) SetDeviceInfo(s string) *SessionUpdateOne {
	suo.mutation.SetDeviceInfo(s)
	return suo
}

// SetNillableDeviceInfo sets the "device_info" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableDeviceInfo(s *string) *SessionUpdateOne {
	if s != nil {
		suo.SetDeviceInfo(*s)
	}
	return suo
}

// ClearDeviceInfo clears the value of the "device_info" field.
func (suo *SessionUpdateOne) ClearDeviceInfo() *SessionUpdateOne {
	suo.mutation.ClearDeviceInfo()
	return suo
}

// SetStatus sets the "status" field.
func (suo *SessionUpdateOne) SetStatus(s session.Status) *SessionUpdateOne {
	suo.mutation.SetStatus(s)
	return suo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableStatus(s *session.Status) *SessionUpdateOne {
	if s != nil {
		suo.SetStatus(*s)
	}
	return suo
}

// SetIPAddress sets the "ip_address" field.
func (suo *SessionUpdateOne) SetIPAddress(s string) *SessionUpdateOne {
	suo.mutation.SetIPAddress(s)
	return suo
}

// SetNillableIPAddress sets the "ip_address" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableIPAddress(s *string) *SessionUpdateOne {
	if s != nil {
		suo.SetIPAddress(*s)
	}
	return suo
}

// ClearIPAddress clears the value of the "ip_address" field.
func (suo *SessionUpdateOne) ClearIPAddress() *SessionUpdateOne {
	suo.mutation.ClearIPAddress()
	return suo
}

// SetLoginTime sets the "login_time" field.
func (suo *SessionUpdateOne) SetLoginTime(t time.Time) *SessionUpdateOne {
	suo.mutation.SetLoginTime(t)
	return suo
}

// SetNillableLoginTime sets the "login_time" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableLoginTime(t *time.Time) *SessionUpdateOne {
	if t != nil {
		suo.SetLoginTime(*t)
	}
	return suo
}

// SetLogoutTime sets the "logout_time" field.
func (suo *SessionUpdateOne) SetLogoutTime(t time.Time) *SessionUpdateOne {
	suo.mutation.SetLogoutTime(t)
	return suo
}

// SetNillableLogoutTime sets the "logout_time" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableLogoutTime(t *time.Time) *SessionUpdateOne {
	if t != nil {
		suo.SetLogoutTime(*t)
	}
	return suo
}

// ClearLogoutTime clears the value of the "logout_time" field.
func (suo *SessionUpdateOne) ClearLogoutTime() *SessionUpdateOne {
	suo.mutation.ClearLogoutTime()
	return suo
}

// SetExpiresAt sets the "expires_at" field.
func (suo *SessionUpdateOne) SetExpiresAt(t time.Time) *SessionUpdateOne {
	suo.mutation.SetExpiresAt(t)
	return suo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableExpiresAt(t *time.Time) *SessionUpdateOne {
	if t != nil {
		suo.SetExpiresAt(*t)
	}
	return suo
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (suo *SessionUpdateOne) ClearExpiresAt() *SessionUpdateOne {
	suo.mutation.ClearExpiresAt()
	return suo
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *SessionUpdateOne) SetUpdatedAt(t time.Time) *SessionUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// SetUser sets the "user" edge to the User entity.
func (suo *SessionUpdateOne) SetUser(u *User) *SessionUpdateOne {
	return suo.SetUserID(u.ID)
}

// Mutation returns the SessionMutation object of the builder.
func (suo *SessionUpdateOne) Mutation() *SessionMutation {
	return suo.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (suo *SessionUpdateOne) ClearUser() *SessionUpdateOne {
	suo.mutation.ClearUser()
	return suo
}

// Where appends a list predicates to the SessionUpdate builder.
func (suo *SessionUpdateOne) Where(ps ...predicate.Session) *SessionUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *SessionUpdateOne) Select(field string, fields ...string) *SessionUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Session entity.
func (suo *SessionUpdateOne) Save(ctx context.Context) (*Session, error) {
	suo.defaults()
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *SessionUpdateOne) SaveX(ctx context.Context) *Session {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *SessionUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *SessionUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *SessionUpdateOne) defaults() {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		v := session.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (suo *SessionUpdateOne) check() error {
	if v, ok := suo.mutation.Status(); ok {
		if err := session.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Session.status": %w`, err)}
		}
	}
	if v, ok := suo.mutation.IPAddress(); ok {
		if err := session.IPAddressValidator(v); err != nil {
			return &ValidationError{Name: "ip_address", err: fmt.Errorf(`ent: validator failed for field "Session.ip_address": %w`, err)}
		}
	}
	if suo.mutation.UserCleared() && len(suo.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Session.user"`)
	}
	return nil
}

func (suo *SessionUpdateOne) sqlSave(ctx context.Context) (_node *Session, err error) {
	if err := suo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(session.Table, session.Columns, sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Session.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, session.FieldID)
		for _, f := range fields {
			if !session.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != session.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.DeviceInfo(); ok {
		_spec.SetField(session.FieldDeviceInfo, field.TypeString, value)
	}
	if suo.mutation.DeviceInfoCleared() {
		_spec.ClearField(session.FieldDeviceInfo, field.TypeString)
	}
	if value, ok := suo.mutation.Status(); ok {
		_spec.SetField(session.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := suo.mutation.IPAddress(); ok {
		_spec.SetField(session.FieldIPAddress, field.TypeString, value)
	}
	if suo.mutation.IPAddressCleared() {
		_spec.ClearField(session.FieldIPAddress, field.TypeString)
	}
	if value, ok := suo.mutation.LoginTime(); ok {
		_spec.SetField(session.FieldLoginTime, field.TypeTime, value)
	}
	if value, ok := suo.mutation.LogoutTime(); ok {
		_spec.SetField(session.FieldLogoutTime, field.TypeTime, value)
	}
	if suo.mutation.LogoutTimeCleared() {
		_spec.ClearField(session.FieldLogoutTime, field.TypeTime)
	}
	if value, ok := suo.mutation.ExpiresAt(); ok {
		_spec.SetField(session.FieldExpiresAt, field.TypeTime, value)
	}
	if suo.mutation.ExpiresAtCleared() {
		_spec.ClearField(session.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(session.FieldUpdatedAt, field.TypeTime, value)
	}
	if suo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   session.UserTable,
			Columns: []string{session.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   session.UserTable,
			Columns: []string{session.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Session{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{session.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}

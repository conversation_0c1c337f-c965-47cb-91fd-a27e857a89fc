syntax = "proto3";

package integration.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/integration/v1;integrationv1";

// PlatformAccount represents a connected social media account
message PlatformAccount {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;       // Optional workspace association
  string platform = 4;          // facebook, youtube, twitter, instagram, tiktok
  string account_id = 5;         // Platform-specific account ID
  string username = 6;           // Platform username/handle
  string display_name = 7;       // Account display name
  string avatar_url = 8;         // Profile picture URL
  string access_token = 9;       // Encrypted OAuth access token
  string refresh_token = 10;     // Encrypted OAuth refresh token
  google.protobuf.Timestamp token_expires_at = 11;
  string status = 12;            // active, expired, revoked, error
  repeated string permissions = 13; // Platform-specific permissions
  map<string, string> metadata = 14; // Platform-specific metadata
  google.protobuf.Timestamp connected_at = 15;
  google.protobuf.Timestamp last_used = 16;
}

// ConnectAccountRequest for connecting a social media account
message ConnectAccountRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace context
  string platform = 3;          // facebook, youtube, twitter, instagram, tiktok
  string oauth_code = 4;         // OAuth authorization code
  string redirect_uri = 5;       // OAuth redirect URI
  repeated string requested_permissions = 6; // Requested permissions
}

// ConnectAccountResponse with connection result
message ConnectAccountResponse {
  string account_id = 1;
  PlatformAccount account = 2;
  bool success = 3;
  string error_message = 4;      // If connection failed
}

// GetOAuthUrlRequest for getting OAuth authorization URL
message GetOAuthUrlRequest {
  string user_id = 1;
  string platform = 2;          // facebook, youtube, twitter, instagram, tiktok
  string redirect_uri = 3;       // OAuth redirect URI
  repeated string requested_permissions = 4; // Requested permissions
  string state = 5;              // OAuth state parameter
}

// GetOAuthUrlResponse with authorization URL
message GetOAuthUrlResponse {
  string auth_url = 1;           // OAuth authorization URL
  string state = 2;              // OAuth state parameter
  repeated string permissions = 3; // Available permissions
}

// RefreshTokenRequest for refreshing access token
message RefreshTokenRequest {
  string account_id = 1;
  string user_id = 2;
  bool force_refresh = 3;        // Force refresh even if not expired
}

// RefreshTokenResponse with new token info
message RefreshTokenResponse {
  bool success = 1;
  google.protobuf.Timestamp new_expires_at = 2;
  string error_message = 3;      // If refresh failed
}

// DisconnectAccountRequest for disconnecting account
message DisconnectAccountRequest {
  string account_id = 1;
  string user_id = 2;
  bool revoke_token = 3;         // Whether to revoke token on platform
}

// ListAccountsRequest for listing connected accounts
message ListAccountsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  string platform = 3;          // Optional platform filter
  string status = 4;             // Optional status filter
  common.v1.PaginationRequest pagination = 5;
}

// ListAccountsResponse with paginated accounts
message ListAccountsResponse {
  repeated PlatformAccount accounts = 1;
  common.v1.PaginationResponse pagination = 2;
}

// PublishPostRequest for publishing content to platform
message PublishPostRequest {
  string account_id = 1;
  string user_id = 2;
  string post_id = 3;            // Internal post ID
  string content = 4;            // Post content
  repeated string image_urls = 5; // Image URLs
  repeated string hashtags = 6;   // Hashtags
  map<string, string> platform_options = 7; // Platform-specific options
  google.protobuf.Timestamp scheduled_at = 8; // Optional scheduling
}

// PublishPostResponse with publishing result
message PublishPostResponse {
  string platform_post_id = 1;  // Platform-specific post ID
  string platform_url = 2;      // URL to view post on platform
  bool success = 3;
  string error_message = 4;      // If publishing failed
  google.protobuf.Timestamp published_at = 5;
}

// GetPostAnalyticsRequest for retrieving post analytics
message GetPostAnalyticsRequest {
  string account_id = 1;
  string platform_post_id = 2;  // Platform-specific post ID
  string user_id = 3;
  repeated string metrics = 4;   // Specific metrics to retrieve
}

// PostAnalytics contains post performance metrics
message PostAnalytics {
  string platform_post_id = 1;
  string platform = 2;
  map<string, int64> metrics = 3; // metric_name -> value
  google.protobuf.Timestamp collected_at = 4;
  google.protobuf.Timestamp post_published_at = 5;
}

// GetAccountAnalyticsRequest for account-level analytics
message GetAccountAnalyticsRequest {
  string account_id = 1;
  string user_id = 2;
  string date_from = 3;          // Format: YYYY-MM-DD
  string date_to = 4;            // Format: YYYY-MM-DD
  repeated string metrics = 5;   // Specific metrics to retrieve
}

// TimeSeriesValues represents time series values for a metric
message TimeSeriesValues {
  repeated int64 values = 1;
}

// AccountAnalytics contains account performance metrics
message AccountAnalytics {
  string account_id = 1;
  string platform = 2;
  map<string, int64> metrics = 3; // metric_name -> value
  map<string, TimeSeriesValues> time_series = 4; // metric_name -> daily values
  google.protobuf.Timestamp collected_at = 5;
}

// ValidateAccountRequest for checking account status
message ValidateAccountRequest {
  string account_id = 1;
  string user_id = 2;
  bool check_permissions = 3;    // Whether to check permissions
}

// ValidateAccountResponse with validation result
message ValidateAccountResponse {
  bool is_valid = 1;
  bool token_expired = 2;
  repeated string missing_permissions = 3;
  string error_message = 4;      // If validation failed
  google.protobuf.Timestamp last_validated = 5;
}

// GetPlatformLimitsRequest for platform rate limits
message GetPlatformLimitsRequest {
  string platform = 1;
  string account_id = 2;         // Optional specific account
}

// PlatformLimits contains platform rate limit information
message PlatformLimits {
  string platform = 1;
  map<string, int32> rate_limits = 2; // operation -> requests_per_hour
  map<string, int32> current_usage = 3; // operation -> current_requests
  map<string, google.protobuf.Timestamp> reset_times = 4; // operation -> reset_time
}

// WebhookEvent represents a webhook event from platform
message WebhookEvent {
  string id = 1;
  string platform = 2;
  string account_id = 3;
  string event_type = 4;         // post_published, comment_received, etc.
  map<string, string> data = 5;  // Event-specific data
  google.protobuf.Timestamp received_at = 6;
  bool processed = 7;
}

// ProcessWebhookRequest for processing webhook events
message ProcessWebhookRequest {
  string platform = 1;
  string signature = 2;          // Webhook signature for verification
  bytes payload = 3;             // Raw webhook payload
  map<string, string> headers = 4; // HTTP headers
}

// ProcessWebhookResponse with processing result
message ProcessWebhookResponse {
  bool success = 1;
  string event_id = 2;           // Generated event ID
  string event_type = 3;         // Detected event type
  string error_message = 4;      // If processing failed
}

// Empty response
message Empty {}

// PlatformIntegrationService provides social media platform integration
service PlatformIntegrationService {
  // Account connection operations
  rpc GetOAuthUrl(GetOAuthUrlRequest) returns (GetOAuthUrlResponse);
  rpc ConnectAccount(ConnectAccountRequest) returns (ConnectAccountResponse);
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc DisconnectAccount(DisconnectAccountRequest) returns (Empty);
  rpc ListAccounts(ListAccountsRequest) returns (ListAccountsResponse);
  rpc ValidateAccount(ValidateAccountRequest) returns (ValidateAccountResponse);
  
  // Publishing operations
  rpc PublishPost(PublishPostRequest) returns (PublishPostResponse);
  
  // Analytics operations
  rpc GetPostAnalytics(GetPostAnalyticsRequest) returns (PostAnalytics);
  rpc GetAccountAnalytics(GetAccountAnalyticsRequest) returns (AccountAnalytics);
  
  // Platform information
  rpc GetPlatformLimits(GetPlatformLimitsRequest) returns (PlatformLimits);
  
  // Webhook operations
  rpc ProcessWebhook(ProcessWebhookRequest) returns (ProcessWebhookResponse);
}

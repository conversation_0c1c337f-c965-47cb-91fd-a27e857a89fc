package main

import (
	"context"
	"fmt"
	"log"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/payment"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Example demonstrating PayPal processor integration with PayPal usecase
func main() {
	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  "debug",
		Format: "text",
	})

	// Create PayPal client configuration
	paypalConfig := paypal.Config{
		ClientID:     "your-paypal-client-id",
		ClientSecret: "your-paypal-client-secret",
		Environment:  "sandbox", // or "live" for production
		Timeout:      30,
	}

	// Create PayPal client
	paypalClient := paypal.NewClient(paypalConfig, logger)

	// Create PayPal usecase (this would normally be injected with dependencies)
	// For this example, we'll create a mock implementation
	paypalUseCase := &mockPayPalUseCase{
		logger: logger,
	}

	// Create payment processor factory with dependencies
	paymentConfig := payment.Config{
		DefaultProcessor: payment.ProcessorTypePayPal,
		PayPal: payment.PayPalConfig{
			ClientID:     paypalConfig.ClientID,
			ClientSecret: paypalConfig.ClientSecret,
			Environment:  paypalConfig.Environment,
			BaseURL:      "https://api.sandbox.paypal.com",
			ReturnURL:    "http://localhost:8080/payment/success",
			CancelURL:    "http://localhost:8080/payment/cancel",
		},
	}

	// Create event publisher (mock for example)
	eventPublisher := payment.NewMockEventPublisher(logger)

	// Create factory with dependencies including event publisher
	factory := payment.NewFactoryWithDependencies(
		paymentConfig,
		logger,
		paypalUseCase,
		paypalClient,
		eventPublisher,
	)

	// Example 1: Process a credit topup payment using new flow
	// Flow: Subscription -> Factory.CreatePayment -> PaypalProcess.InitiatePayment
	fmt.Println("=== Example 1: Credit Topup Payment (New Flow) ===")
	creditPaymentReq := &payment.PaymentRequest{
		UserID:        "user-123",
		Amount:        1000, // $10.00 in cents
		Currency:      "USD",
		PaymentMethod: "paypal",
		Description:   "Credit topup",
		OrderID:       "order-123",
		Metadata: map[string]interface{}{
			"purpose": "credit_topup",
		},
	}

	// Use Factory.CreatePayment instead of direct processor
	paymentResp, err := factory.CreatePayment(context.Background(), payment.ProcessorTypePayPal, creditPaymentReq)
	if err != nil {
		log.Printf("Failed to create credit payment: %v", err)
	} else {
		fmt.Printf("Credit Payment Response: %+v\n", paymentResp)
	}

	// Example 2: Process a subscription payment using new flow
	fmt.Println("\n=== Example 2: Subscription Payment (New Flow) ===")
	subscriptionPaymentReq := &payment.PaymentRequest{
		UserID:        "user-123",
		Amount:        2999, // $29.99 in cents
		Currency:      "USD",
		PaymentMethod: "paypal",
		Description:   "Monthly subscription",
		OrderID:       "order-456",
		Metadata: map[string]interface{}{
			"purpose": "subscription",
			"plan_id": "plan-premium",
		},
	}

	// Use Factory.CreatePayment for subscription
	subPaymentResp, err := factory.CreatePayment(context.Background(), payment.ProcessorTypePayPal, subscriptionPaymentReq)
	if err != nil {
		log.Printf("Failed to create subscription payment: %v", err)
	} else {
		fmt.Printf("Subscription Payment Response: %+v\n", subPaymentResp)
	}

	// Example 3: Webhook processing demonstration
	fmt.Println("\n=== Example 3: Webhook Processing (New Flow) ===")
	// Simulate PayPal webhook payload
	webhookPayload := []byte(`{
		"id": "WH-2WR32451HC0233532-67976317FL4543714",
		"event_type": "PAYMENT.CAPTURE.COMPLETED",
		"resource": {
			"id": "5O190127TN364715T",
			"status": "COMPLETED",
			"amount": {
				"currency_code": "USD",
				"value": "10.00"
			}
		}
	}`)
	webhookSignature := "mock-signature-123"

	// Process webhook through factory
	// Flow: HTTP -> Factory.WebHook -> PaypalProcess.WebHook -> data -> push event
	webhookResult, err := factory.WebHook(context.Background(), payment.ProcessorTypePayPal, webhookPayload, webhookSignature)
	if err != nil {
		log.Printf("Failed to process webhook: %v", err)
	} else {
		fmt.Printf("Webhook Result: %+v\n", webhookResult)
	}

	fmt.Println("\n=== PayPal Integration Example Complete ===")
}

// mockPayPalUseCase is a mock implementation of PayPal usecase for demonstration
// It implements the payment.PayPalUseCase interface
type mockPayPalUseCase struct {
	logger logging.Logger
}

func (m *mockPayPalUseCase) InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error) {
	m.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Mock: Initiating PayPal payment")

	return &models.InitiatePayPalPaymentResponse{
		Success:       true,
		PaymentID:     "mock-payment-123",
		PayPalOrderID: "mock-paypal-order-456",
		ApprovalURL:   "https://www.sandbox.paypal.com/checkoutnow?token=mock-token",
	}, nil
}

func (m *mockPayPalUseCase) ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Executing PayPal payment")

	return &models.ExecutePayPalPaymentResponse{
		Success:      true,
		PaymentID:    req.PaymentID,
		Status:       "completed",
		Message:      "Payment completed successfully",
		CreditsAdded: 1000,
		Amount:       1000,
		Currency:     "USD",
	}, nil
}

func (m *mockPayPalUseCase) GetPaymentStatus(ctx context.Context, req *models.GetPayPalPaymentStatusRequest) (*models.GetPayPalPaymentStatusResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Getting PayPal payment status")

	return &models.GetPayPalPaymentStatusResponse{
		PaymentID: req.PaymentID,
		OrderID:   "mock-order-123",
		Status:    "completed",
		Amount:    1000,
		Currency:  "USD",
		Purpose:   "credit_topup",
	}, nil
}

func (m *mockPayPalUseCase) RefundPayment(ctx context.Context, req *models.RefundPayPalPaymentRequest) (*models.RefundPayPalPaymentResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Refunding PayPal payment")

	return &models.RefundPayPalPaymentResponse{
		Success:   true,
		RefundID:  "mock-refund-123",
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Message:   "Refund processed successfully",
	}, nil
}

func (m *mockPayPalUseCase) HandleWebhook(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	m.logger.WithField("event_type", req.EventType).Info("Mock: Handling PayPal webhook")

	return &models.PayPalWebhookResponse{
		Success: true,
		Message: "Webhook processed successfully",
	}, nil
}

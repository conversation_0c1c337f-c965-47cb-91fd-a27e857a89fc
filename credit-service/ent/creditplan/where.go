// Code generated by ent, DO NOT EDIT.

package creditplan

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldName, v))
}

// DisplayName applies equality check predicate on the "display_name" field. It's identical to DisplayNameEQ.
func DisplayName(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDisplayName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDescription, v))
}

// Credits applies equality check predicate on the "credits" field. It's identical to CreditsEQ.
func Credits(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCredits, v))
}

// Price applies equality check predicate on the "price" field. It's identical to PriceEQ.
func Price(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldPrice, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCurrency, v))
}

// DiscountPercentage applies equality check predicate on the "discount_percentage" field. It's identical to DiscountPercentageEQ.
func DiscountPercentage(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDiscountPercentage, v))
}

// IsPopular applies equality check predicate on the "is_popular" field. It's identical to IsPopularEQ.
func IsPopular(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldIsPopular, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldIsActive, v))
}

// ValidUntil applies equality check predicate on the "valid_until" field. It's identical to ValidUntilEQ.
func ValidUntil(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldValidUntil, v))
}

// SortOrder applies equality check predicate on the "sort_order" field. It's identical to SortOrderEQ.
func SortOrder(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldSortOrder, v))
}

// StripePriceID applies equality check predicate on the "stripe_price_id" field. It's identical to StripePriceIDEQ.
func StripePriceID(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldStripePriceID, v))
}

// StripeProductID applies equality check predicate on the "stripe_product_id" field. It's identical to StripeProductIDEQ.
func StripeProductID(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldStripeProductID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldUpdatedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldName, v))
}

// DisplayNameEQ applies the EQ predicate on the "display_name" field.
func DisplayNameEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDisplayName, v))
}

// DisplayNameNEQ applies the NEQ predicate on the "display_name" field.
func DisplayNameNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldDisplayName, v))
}

// DisplayNameIn applies the In predicate on the "display_name" field.
func DisplayNameIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldDisplayName, vs...))
}

// DisplayNameNotIn applies the NotIn predicate on the "display_name" field.
func DisplayNameNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldDisplayName, vs...))
}

// DisplayNameGT applies the GT predicate on the "display_name" field.
func DisplayNameGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldDisplayName, v))
}

// DisplayNameGTE applies the GTE predicate on the "display_name" field.
func DisplayNameGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldDisplayName, v))
}

// DisplayNameLT applies the LT predicate on the "display_name" field.
func DisplayNameLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldDisplayName, v))
}

// DisplayNameLTE applies the LTE predicate on the "display_name" field.
func DisplayNameLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldDisplayName, v))
}

// DisplayNameContains applies the Contains predicate on the "display_name" field.
func DisplayNameContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldDisplayName, v))
}

// DisplayNameHasPrefix applies the HasPrefix predicate on the "display_name" field.
func DisplayNameHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldDisplayName, v))
}

// DisplayNameHasSuffix applies the HasSuffix predicate on the "display_name" field.
func DisplayNameHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldDisplayName, v))
}

// DisplayNameEqualFold applies the EqualFold predicate on the "display_name" field.
func DisplayNameEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldDisplayName, v))
}

// DisplayNameContainsFold applies the ContainsFold predicate on the "display_name" field.
func DisplayNameContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldDisplayName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldDescription, v))
}

// CreditsEQ applies the EQ predicate on the "credits" field.
func CreditsEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCredits, v))
}

// CreditsNEQ applies the NEQ predicate on the "credits" field.
func CreditsNEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldCredits, v))
}

// CreditsIn applies the In predicate on the "credits" field.
func CreditsIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldCredits, vs...))
}

// CreditsNotIn applies the NotIn predicate on the "credits" field.
func CreditsNotIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldCredits, vs...))
}

// CreditsGT applies the GT predicate on the "credits" field.
func CreditsGT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldCredits, v))
}

// CreditsGTE applies the GTE predicate on the "credits" field.
func CreditsGTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldCredits, v))
}

// CreditsLT applies the LT predicate on the "credits" field.
func CreditsLT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldCredits, v))
}

// CreditsLTE applies the LTE predicate on the "credits" field.
func CreditsLTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldCredits, v))
}

// PriceEQ applies the EQ predicate on the "price" field.
func PriceEQ(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldPrice, v))
}

// PriceNEQ applies the NEQ predicate on the "price" field.
func PriceNEQ(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldPrice, v))
}

// PriceIn applies the In predicate on the "price" field.
func PriceIn(vs ...int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldPrice, vs...))
}

// PriceNotIn applies the NotIn predicate on the "price" field.
func PriceNotIn(vs ...int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldPrice, vs...))
}

// PriceGT applies the GT predicate on the "price" field.
func PriceGT(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldPrice, v))
}

// PriceGTE applies the GTE predicate on the "price" field.
func PriceGTE(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldPrice, v))
}

// PriceLT applies the LT predicate on the "price" field.
func PriceLT(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldPrice, v))
}

// PriceLTE applies the LTE predicate on the "price" field.
func PriceLTE(v int64) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldPrice, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldCurrency, v))
}

// DiscountPercentageEQ applies the EQ predicate on the "discount_percentage" field.
func DiscountPercentageEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldDiscountPercentage, v))
}

// DiscountPercentageNEQ applies the NEQ predicate on the "discount_percentage" field.
func DiscountPercentageNEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldDiscountPercentage, v))
}

// DiscountPercentageIn applies the In predicate on the "discount_percentage" field.
func DiscountPercentageIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldDiscountPercentage, vs...))
}

// DiscountPercentageNotIn applies the NotIn predicate on the "discount_percentage" field.
func DiscountPercentageNotIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldDiscountPercentage, vs...))
}

// DiscountPercentageGT applies the GT predicate on the "discount_percentage" field.
func DiscountPercentageGT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldDiscountPercentage, v))
}

// DiscountPercentageGTE applies the GTE predicate on the "discount_percentage" field.
func DiscountPercentageGTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldDiscountPercentage, v))
}

// DiscountPercentageLT applies the LT predicate on the "discount_percentage" field.
func DiscountPercentageLT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldDiscountPercentage, v))
}

// DiscountPercentageLTE applies the LTE predicate on the "discount_percentage" field.
func DiscountPercentageLTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldDiscountPercentage, v))
}

// IsPopularEQ applies the EQ predicate on the "is_popular" field.
func IsPopularEQ(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldIsPopular, v))
}

// IsPopularNEQ applies the NEQ predicate on the "is_popular" field.
func IsPopularNEQ(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldIsPopular, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldIsActive, v))
}

// FeaturesIsNil applies the IsNil predicate on the "features" field.
func FeaturesIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldFeatures))
}

// FeaturesNotNil applies the NotNil predicate on the "features" field.
func FeaturesNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldFeatures))
}

// ValidUntilEQ applies the EQ predicate on the "valid_until" field.
func ValidUntilEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldValidUntil, v))
}

// ValidUntilNEQ applies the NEQ predicate on the "valid_until" field.
func ValidUntilNEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldValidUntil, v))
}

// ValidUntilIn applies the In predicate on the "valid_until" field.
func ValidUntilIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldValidUntil, vs...))
}

// ValidUntilNotIn applies the NotIn predicate on the "valid_until" field.
func ValidUntilNotIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldValidUntil, vs...))
}

// ValidUntilGT applies the GT predicate on the "valid_until" field.
func ValidUntilGT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldValidUntil, v))
}

// ValidUntilGTE applies the GTE predicate on the "valid_until" field.
func ValidUntilGTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldValidUntil, v))
}

// ValidUntilLT applies the LT predicate on the "valid_until" field.
func ValidUntilLT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldValidUntil, v))
}

// ValidUntilLTE applies the LTE predicate on the "valid_until" field.
func ValidUntilLTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldValidUntil, v))
}

// ValidUntilIsNil applies the IsNil predicate on the "valid_until" field.
func ValidUntilIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldValidUntil))
}

// ValidUntilNotNil applies the NotNil predicate on the "valid_until" field.
func ValidUntilNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldValidUntil))
}

// SortOrderEQ applies the EQ predicate on the "sort_order" field.
func SortOrderEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldSortOrder, v))
}

// SortOrderNEQ applies the NEQ predicate on the "sort_order" field.
func SortOrderNEQ(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldSortOrder, v))
}

// SortOrderIn applies the In predicate on the "sort_order" field.
func SortOrderIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldSortOrder, vs...))
}

// SortOrderNotIn applies the NotIn predicate on the "sort_order" field.
func SortOrderNotIn(vs ...int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldSortOrder, vs...))
}

// SortOrderGT applies the GT predicate on the "sort_order" field.
func SortOrderGT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldSortOrder, v))
}

// SortOrderGTE applies the GTE predicate on the "sort_order" field.
func SortOrderGTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldSortOrder, v))
}

// SortOrderLT applies the LT predicate on the "sort_order" field.
func SortOrderLT(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldSortOrder, v))
}

// SortOrderLTE applies the LTE predicate on the "sort_order" field.
func SortOrderLTE(v int) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldSortOrder, v))
}

// StripePriceIDEQ applies the EQ predicate on the "stripe_price_id" field.
func StripePriceIDEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldStripePriceID, v))
}

// StripePriceIDNEQ applies the NEQ predicate on the "stripe_price_id" field.
func StripePriceIDNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldStripePriceID, v))
}

// StripePriceIDIn applies the In predicate on the "stripe_price_id" field.
func StripePriceIDIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldStripePriceID, vs...))
}

// StripePriceIDNotIn applies the NotIn predicate on the "stripe_price_id" field.
func StripePriceIDNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldStripePriceID, vs...))
}

// StripePriceIDGT applies the GT predicate on the "stripe_price_id" field.
func StripePriceIDGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldStripePriceID, v))
}

// StripePriceIDGTE applies the GTE predicate on the "stripe_price_id" field.
func StripePriceIDGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldStripePriceID, v))
}

// StripePriceIDLT applies the LT predicate on the "stripe_price_id" field.
func StripePriceIDLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldStripePriceID, v))
}

// StripePriceIDLTE applies the LTE predicate on the "stripe_price_id" field.
func StripePriceIDLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldStripePriceID, v))
}

// StripePriceIDContains applies the Contains predicate on the "stripe_price_id" field.
func StripePriceIDContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldStripePriceID, v))
}

// StripePriceIDHasPrefix applies the HasPrefix predicate on the "stripe_price_id" field.
func StripePriceIDHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldStripePriceID, v))
}

// StripePriceIDHasSuffix applies the HasSuffix predicate on the "stripe_price_id" field.
func StripePriceIDHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldStripePriceID, v))
}

// StripePriceIDIsNil applies the IsNil predicate on the "stripe_price_id" field.
func StripePriceIDIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldStripePriceID))
}

// StripePriceIDNotNil applies the NotNil predicate on the "stripe_price_id" field.
func StripePriceIDNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldStripePriceID))
}

// StripePriceIDEqualFold applies the EqualFold predicate on the "stripe_price_id" field.
func StripePriceIDEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldStripePriceID, v))
}

// StripePriceIDContainsFold applies the ContainsFold predicate on the "stripe_price_id" field.
func StripePriceIDContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldStripePriceID, v))
}

// StripeProductIDEQ applies the EQ predicate on the "stripe_product_id" field.
func StripeProductIDEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldStripeProductID, v))
}

// StripeProductIDNEQ applies the NEQ predicate on the "stripe_product_id" field.
func StripeProductIDNEQ(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldStripeProductID, v))
}

// StripeProductIDIn applies the In predicate on the "stripe_product_id" field.
func StripeProductIDIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldStripeProductID, vs...))
}

// StripeProductIDNotIn applies the NotIn predicate on the "stripe_product_id" field.
func StripeProductIDNotIn(vs ...string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldStripeProductID, vs...))
}

// StripeProductIDGT applies the GT predicate on the "stripe_product_id" field.
func StripeProductIDGT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldStripeProductID, v))
}

// StripeProductIDGTE applies the GTE predicate on the "stripe_product_id" field.
func StripeProductIDGTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldStripeProductID, v))
}

// StripeProductIDLT applies the LT predicate on the "stripe_product_id" field.
func StripeProductIDLT(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldStripeProductID, v))
}

// StripeProductIDLTE applies the LTE predicate on the "stripe_product_id" field.
func StripeProductIDLTE(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldStripeProductID, v))
}

// StripeProductIDContains applies the Contains predicate on the "stripe_product_id" field.
func StripeProductIDContains(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContains(FieldStripeProductID, v))
}

// StripeProductIDHasPrefix applies the HasPrefix predicate on the "stripe_product_id" field.
func StripeProductIDHasPrefix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasPrefix(FieldStripeProductID, v))
}

// StripeProductIDHasSuffix applies the HasSuffix predicate on the "stripe_product_id" field.
func StripeProductIDHasSuffix(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldHasSuffix(FieldStripeProductID, v))
}

// StripeProductIDIsNil applies the IsNil predicate on the "stripe_product_id" field.
func StripeProductIDIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldStripeProductID))
}

// StripeProductIDNotNil applies the NotNil predicate on the "stripe_product_id" field.
func StripeProductIDNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldStripeProductID))
}

// StripeProductIDEqualFold applies the EqualFold predicate on the "stripe_product_id" field.
func StripeProductIDEqualFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEqualFold(FieldStripeProductID, v))
}

// StripeProductIDContainsFold applies the ContainsFold predicate on the "stripe_product_id" field.
func StripeProductIDContainsFold(v string) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldContainsFold(FieldStripeProductID, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CreditPlan {
	return predicate.CreditPlan(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CreditPlan) predicate.CreditPlan {
	return predicate.CreditPlan(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CreditPlan) predicate.CreditPlan {
	return predicate.CreditPlan(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CreditPlan) predicate.CreditPlan {
	return predicate.CreditPlan(sql.NotPredicates(p))
}

# Kafka Event Publishing Implementation

This document describes the implementation of Kafka event publishing for subscription update events in the payment system.

## Overview

The payment factory now publishes events to Kafka topics when webhook processing is completed, enabling event-driven architecture for subscription management.

## Implementation Components

### 1. EventPublisher Interface

```go
// EventPublisher defines interface for publishing events
type EventPublisher interface {
    PublishEvent(ctx context.Context, topic string, event interface{}) error
}
```

### 2. KafkaEventPublisher

**Real Kafka Implementation:**
```go
type KafkaEventPublisher struct {
    producer *sharedKafka.Producer
    logger   logging.Logger
}

func NewKafkaEventPublisher(producer *sharedKafka.Producer, logger logging.Logger) *KafkaEventPublisher {
    return &KafkaEventPublisher{
        producer: producer,
        logger:   logger,
    }
}
```

**Key Features:**
- Uses shared Kafka producer from `pkg-shared/kafka`
- Converts events to `BaseEvent` format
- Publishes to specific topics using `PublishToTopic()`
- Proper error handling and logging

### 3. MockEventPublisher

**Testing Implementation:**
```go
type MockEventPublisher struct {
    logger logging.Logger
    events []PublishedEvent
}

func NewMockEventPublisher(logger logging.Logger) *MockEventPublisher {
    return &MockEventPublisher{
        logger: logger,
        events: make([]PublishedEvent, 0),
    }
}
```

**Key Features:**
- Stores events in memory for testing
- Provides `GetPublishedEvents()` for test verification
- No actual Kafka dependency

### 4. SubscriptionUpdateEvent

**Event Structure:**
```go
type SubscriptionUpdateEvent struct {
    EventID        string                 `json:"event_id"`
    EventType      string                 `json:"event_type"`
    EventData      map[string]interface{} `json:"event_data"`
    ProcessedAt    string                 `json:"processed_at"`
    SubscriptionID string                 `json:"subscription_id,omitempty"`
    UserID         string                 `json:"user_id,omitempty"`
    PaymentID      string                 `json:"payment_id,omitempty"`
    Status         string                 `json:"status,omitempty"`
    Amount         int64                  `json:"amount,omitempty"`
    Currency       string                 `json:"currency,omitempty"`
}
```

## Event Publishing Flow

### 1. Webhook Processing Flow

```
HTTP Request → Factory.WebHook → PayPal.ProcessWebhook → Factory.pushSubscriptionUpdateEvent → Kafka
```

### 2. Event Publishing Implementation

```go
func (f *Factory) pushSubscriptionUpdateEvent(ctx context.Context, result *WebhookProcessResult) error {
    // Create subscription update event
    subscriptionEvent := &SubscriptionUpdateEvent{
        EventID:     result.EventID,
        EventType:   result.EventType,
        EventData:   result.EventData,
        ProcessedAt: result.ProcessedAt,
    }

    // Publish event to Kafka
    if f.eventPublisher != nil {
        err := f.eventPublisher.PublishEvent(ctx, "subscription.events", subscriptionEvent)
        if err != nil {
            f.logger.WithError(err).Error("Failed to publish subscription update event to Kafka")
            // Continue with local processing even if Kafka publish fails
        } else {
            f.logger.WithField("event_id", result.EventID).Info("Successfully published subscription update event to Kafka")
        }
    }
    
    // Handle specific event types
    switch result.EventType {
    case "PAYMENT.CAPTURE.COMPLETED":
        return f.handlePaymentCompletedEvent(ctx, result)
    case "PAYMENT.CAPTURE.DENIED":
        return f.handlePaymentFailedEvent(ctx, result)
    // ... other event types
    }

    return nil
}
```

### 3. Event Conversion to BaseEvent

```go
func (k *KafkaEventPublisher) convertToBaseEvent(event interface{}) *sharedKafka.BaseEvent {
    switch e := event.(type) {
    case *SubscriptionUpdateEvent:
        baseEvent := &sharedKafka.BaseEvent{
            ID:        e.EventID,
            Type:      sharedKafka.EventType(e.EventType),
            Source:    "credit-service",
            Version:   "1.0",
            Timestamp: k.parseTimestamp(e.ProcessedAt),
            Priority:  sharedKafka.PriorityNormal,
            Data:      make(map[string]interface{}),
            Metadata:  sharedKafka.EventMetadata{},
        }
        
        // Copy event data and add subscription-specific fields
        for key, value := range e.EventData {
            baseEvent.Data[key] = value
        }
        
        if e.SubscriptionID != "" {
            baseEvent.Data["subscription_id"] = e.SubscriptionID
        }
        if e.UserID != "" {
            baseEvent.Data["user_id"] = e.UserID
        }
        // ... other fields
        
        return baseEvent
    }
}
```

## Kafka Topics

### Primary Topics

1. **`subscription.events`** - General subscription update events
2. **`subscription.payment.completed`** - Payment completion events
3. **`subscription.payment.failed`** - Payment failure events
4. **`subscription.activated`** - Subscription activation events
5. **`subscription.cancelled`** - Subscription cancellation events

### Event Types

- `SUBSCRIPTION_PAYMENT_COMPLETED` - Payment successfully processed
- `SUBSCRIPTION_PAYMENT_FAILED` - Payment processing failed
- `SUBSCRIPTION_ACTIVATED` - Subscription activated
- `SUBSCRIPTION_CANCELLED` - Subscription cancelled

## Usage Examples

### 1. Factory with Kafka Publisher

```go
// Create Kafka producer
kafkaProducer, err := sharedKafka.NewProducer(kafkaConfig)
if err != nil {
    log.Fatal("Failed to create Kafka producer:", err)
}

// Create Kafka event publisher
eventPublisher := payment.NewKafkaEventPublisher(kafkaProducer, logger)

// Create factory with Kafka publisher
factory := payment.NewFactoryWithDependencies(
    paymentConfig,
    logger,
    paypalUseCase,
    paypalClient,
    eventPublisher,
)
```

### 2. Factory with Mock Publisher (Testing)

```go
// Create mock event publisher for testing
eventPublisher := payment.NewMockEventPublisher(logger)

// Create factory with mock publisher
factory := payment.NewFactoryWithDependencies(
    paymentConfig,
    logger,
    paypalUseCase,
    paypalClient,
    eventPublisher,
)

// Process webhook
result, err := factory.WebHook(ctx, payment.ProcessorTypePayPal, payload, signature)

// Verify events were published
publishedEvents := eventPublisher.GetPublishedEvents()
assert.Len(t, publishedEvents, 1)
assert.Equal(t, "subscription.events", publishedEvents[0].Topic)
```

### 3. Webhook Handler with Event Publishing

```go
func HandlePayPalWebhook(w http.ResponseWriter, r *http.Request) {
    payload, _ := ioutil.ReadAll(r.Body)
    signature := r.Header.Get("PAYPAL-TRANSMISSION-SIG")
    
    // Process webhook through factory (automatically publishes events)
    result, err := paymentFactory.WebHook(ctx, payment.ProcessorTypePayPal, payload, signature)
    if err != nil {
        http.Error(w, "Failed to process webhook", http.StatusInternalServerError)
        return
    }
    
    // Events are automatically published to Kafka
    // Subscription service will receive and process these events
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(result)
}
```

## Benefits

### 1. **Event-Driven Architecture**
- Decoupled subscription management
- Real-time subscription updates
- Scalable event processing

### 2. **Reliability**
- Graceful fallback if Kafka publishing fails
- Webhook processing continues even if event publishing fails
- Proper error handling and logging

### 3. **Testability**
- Mock event publisher for testing
- Event verification in tests
- No Kafka dependency in tests

### 4. **Monitoring**
- Comprehensive logging of event publishing
- Event tracking through Kafka topics
- Easy debugging and troubleshooting

## Error Handling

### 1. **Kafka Publishing Failures**
```go
err := f.eventPublisher.PublishEvent(ctx, "subscription.events", subscriptionEvent)
if err != nil {
    f.logger.WithError(err).Error("Failed to publish subscription update event to Kafka")
    // Continue with local processing even if Kafka publish fails
}
```

### 2. **Event Conversion Failures**
- Graceful handling of unknown event types
- Fallback to generic event structure
- Proper error logging

### 3. **Timestamp Parsing Failures**
- Fallback to current time
- RFC3339 format support
- Robust timestamp handling

This implementation provides a robust, scalable, and testable event publishing system that integrates seamlessly with the existing payment processing flow while maintaining reliability and performance.

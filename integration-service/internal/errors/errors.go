package errors

import (
	"fmt"
	"net/http"
)

// Error types for integration service
const (
	// Authentication errors
	ErrCodeInvalidToken     = "INVALID_TOKEN"
	ErrCodeTokenExpired     = "TOKEN_EXPIRED"
	ErrCodeTokenRefreshFail = "TOKEN_REFRESH_FAILED"
	ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"

	// Platform errors
	ErrCodePlatformNotSupported = "PLATFORM_NOT_SUPPORTED"
	ErrCodePlatformAPIError     = "PLATFORM_API_ERROR"
	ErrCodePlatformRateLimit    = "PLATFORM_RATE_LIMIT"
	ErrCodePlatformMaintenance  = "PLATFORM_MAINTENANCE"

	// Integration errors
	ErrCodeIntegrationNotFound    = "INTEGRATION_NOT_FOUND"
	ErrCodeIntegrationExists      = "INTEGRATION_EXISTS"
	ErrCodeIntegrationDisabled    = "INTEGRATION_DISABLED"
	ErrCodePermissionDenied       = "PERMISSION_DENIED"
	ErrCodeInsufficientPermissions = "INSUFFICIENT_PERMISSIONS"

	// Publishing errors
	ErrCodePublishFailed        = "PUBLISH_FAILED"
	ErrCodeContentTooLong       = "CONTENT_TOO_LONG"
	ErrCodeInvalidMediaFormat   = "INVALID_MEDIA_FORMAT"
	ErrCodeMediaUploadFailed    = "MEDIA_UPLOAD_FAILED"
	ErrCodeSchedulingFailed     = "SCHEDULING_FAILED"

	// Validation errors
	ErrCodeValidationFailed     = "VALIDATION_FAILED"
	ErrCodeInvalidInput         = "INVALID_INPUT"
	ErrCodeMissingRequiredField = "MISSING_REQUIRED_FIELD"

	// System errors
	ErrCodeInternalError        = "INTERNAL_ERROR"
	ErrCodeDatabaseError        = "DATABASE_ERROR"
	ErrCodeNetworkError         = "NETWORK_ERROR"
	ErrCodeTimeoutError         = "TIMEOUT_ERROR"
)

// IntegrationError represents an error in the integration service
type IntegrationError struct {
	Code       string            `json:"code"`
	Message    string            `json:"message"`
	Details    string            `json:"details,omitempty"`
	Platform   string            `json:"platform,omitempty"`
	UserID     string            `json:"user_id,omitempty"`
	HTTPStatus int               `json:"http_status"`
	Retryable  bool              `json:"retryable"`
	Metadata   map[string]string `json:"metadata,omitempty"`
	Cause      error             `json:"-"`
}

// Error implements the error interface
func (e *IntegrationError) Error() string {
	if e.Platform != "" {
		return fmt.Sprintf("[%s] %s (%s): %s", e.Code, e.Platform, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
}

// Unwrap returns the underlying error
func (e *IntegrationError) Unwrap() error {
	return e.Cause
}

// IsRetryable returns whether the error is retryable
func (e *IntegrationError) IsRetryable() bool {
	return e.Retryable
}

// WithMetadata adds metadata to the error
func (e *IntegrationError) WithMetadata(key, value string) *IntegrationError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]string)
	}
	e.Metadata[key] = value
	return e
}

// WithCause sets the underlying cause
func (e *IntegrationError) WithCause(cause error) *IntegrationError {
	e.Cause = cause
	return e
}

// NewIntegrationError creates a new integration error
func NewIntegrationError(code, message string) *IntegrationError {
	return &IntegrationError{
		Code:       code,
		Message:    message,
		HTTPStatus: http.StatusInternalServerError,
		Retryable:  false,
		Metadata:   make(map[string]string),
	}
}

// Authentication errors
func NewInvalidTokenError(details string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeInvalidToken,
		Message:    "Invalid access token",
		Details:    details,
		HTTPStatus: http.StatusUnauthorized,
		Retryable:  false,
	}
}

func NewTokenExpiredError(platform string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeTokenExpired,
		Message:    "Access token has expired",
		Platform:   platform,
		HTTPStatus: http.StatusUnauthorized,
		Retryable:  true, // Can retry after token refresh
	}
}

func NewTokenRefreshFailedError(platform string, cause error) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeTokenRefreshFail,
		Message:    "Failed to refresh access token",
		Platform:   platform,
		HTTPStatus: http.StatusUnauthorized,
		Retryable:  false,
		Cause:      cause,
	}
}

// Platform errors
func NewPlatformNotSupportedError(platform string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodePlatformNotSupported,
		Message:    "Platform not supported",
		Platform:   platform,
		HTTPStatus: http.StatusBadRequest,
		Retryable:  false,
	}
}

func NewPlatformAPIError(platform string, statusCode int, message string) *IntegrationError {
	retryable := statusCode >= 500 || statusCode == 429 // Server errors and rate limits are retryable
	
	return &IntegrationError{
		Code:       ErrCodePlatformAPIError,
		Message:    "Platform API error",
		Details:    message,
		Platform:   platform,
		HTTPStatus: statusCode,
		Retryable:  retryable,
		Metadata: map[string]string{
			"platform_status_code": fmt.Sprintf("%d", statusCode),
		},
	}
}

func NewPlatformRateLimitError(platform string, resetTime string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodePlatformRateLimit,
		Message:    "Platform rate limit exceeded",
		Platform:   platform,
		HTTPStatus: http.StatusTooManyRequests,
		Retryable:  true,
		Metadata: map[string]string{
			"reset_time": resetTime,
		},
	}
}

// Integration errors
func NewIntegrationNotFoundError(integrationID string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeIntegrationNotFound,
		Message:    "Integration not found",
		Details:    fmt.Sprintf("Integration ID: %s", integrationID),
		HTTPStatus: http.StatusNotFound,
		Retryable:  false,
	}
}

func NewPermissionDeniedError(userID, resource string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodePermissionDenied,
		Message:    "Permission denied",
		Details:    fmt.Sprintf("User %s does not have access to %s", userID, resource),
		UserID:     userID,
		HTTPStatus: http.StatusForbidden,
		Retryable:  false,
	}
}

func NewInsufficientPermissionsError(platform string, required []string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeInsufficientPermissions,
		Message:    "Insufficient permissions",
		Details:    fmt.Sprintf("Required permissions: %v", required),
		Platform:   platform,
		HTTPStatus: http.StatusForbidden,
		Retryable:  false,
	}
}

// Publishing errors
func NewPublishFailedError(platform, reason string, retryable bool) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodePublishFailed,
		Message:    "Failed to publish content",
		Details:    reason,
		Platform:   platform,
		HTTPStatus: http.StatusBadRequest,
		Retryable:  retryable,
	}
}

func NewContentTooLongError(platform string, maxLength, actualLength int) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeContentTooLong,
		Message:    "Content exceeds platform limits",
		Details:    fmt.Sprintf("Max: %d, Actual: %d", maxLength, actualLength),
		Platform:   platform,
		HTTPStatus: http.StatusBadRequest,
		Retryable:  false,
	}
}

func NewMediaUploadFailedError(platform, reason string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeMediaUploadFailed,
		Message:    "Failed to upload media",
		Details:    reason,
		Platform:   platform,
		HTTPStatus: http.StatusBadRequest,
		Retryable:  true,
	}
}

// Validation errors
func NewValidationError(field, reason string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeValidationFailed,
		Message:    "Validation failed",
		Details:    fmt.Sprintf("Field '%s': %s", field, reason),
		HTTPStatus: http.StatusBadRequest,
		Retryable:  false,
	}
}

// System errors
func NewInternalError(message string, cause error) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeInternalError,
		Message:    "Internal server error",
		Details:    message,
		HTTPStatus: http.StatusInternalServerError,
		Retryable:  true,
		Cause:      cause,
	}
}

func NewDatabaseError(operation string, cause error) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeDatabaseError,
		Message:    "Database operation failed",
		Details:    fmt.Sprintf("Operation: %s", operation),
		HTTPStatus: http.StatusInternalServerError,
		Retryable:  true,
		Cause:      cause,
	}
}

func NewNetworkError(target string, cause error) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeNetworkError,
		Message:    "Network error",
		Details:    fmt.Sprintf("Target: %s", target),
		HTTPStatus: http.StatusBadGateway,
		Retryable:  true,
		Cause:      cause,
	}
}

func NewTimeoutError(operation string, timeout string) *IntegrationError {
	return &IntegrationError{
		Code:       ErrCodeTimeoutError,
		Message:    "Operation timeout",
		Details:    fmt.Sprintf("Operation: %s, Timeout: %s", operation, timeout),
		HTTPStatus: http.StatusGatewayTimeout,
		Retryable:  true,
	}
}

-- =====================================================
-- USERS: Thông tin người dùng, bảo mật và giới thiệu
-- =====================================================
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  full_name VARCHAR(100),                        -- Tên đầy đủ người dùng
  email VARCHAR(255) UNIQUE NOT NULL,            -- Email đăng nhập
  phone VARCHAR(15),                             -- SĐT (tuỳ chọn)
  avatar_url TEXT,                               -- Link ảnh đại diện
  bio TEXT,                                      -- Mô tả bản thân
  company VARCHAR(100),                          -- Công ty
  industry VARCHAR(100),                         -- <PERSON><PERSON><PERSON> ng<PERSON><PERSON> hoạt động
  role VARCHAR(50) DEFAULT 'user',               -- <PERSON><PERSON><PERSON><PERSON> hệ thống: user/admin

  password_hash TEXT NOT NULL,                   -- <PERSON><PERSON><PERSON> khẩu mã hoá
  is_verified BOOLEAN DEFAULT false,             -- Đã xác thực email?
  two_factor_enabled BOOLEAN DEFAULT false,      -- 2FA bật hay chưa

  notification_settings JSONB DEFAULT '{}'::jsonb, -- Tùy chọn thông báo

  invite_code VARCHAR(20) UNIQUE,                -- Mã giới thiệu cá nhân
  aff_code VARCHAR(20),                          -- Mã người giới thiệu (nếu có)
  aff_joined_at TIMESTAMPTZ,                     -- Thời điểm tham gia theo mã giới thiệu

  created_at TIMESTAMPTZ DEFAULT now(),          -- Ngày tạo tài khoản
  updated_at TIMESTAMPTZ DEFAULT now(),          -- Ngày cập nhật gần nhất
  deleted_at TIMESTAMPTZ                         -- Đánh dấu đã xoá
);

-- =====================================================
-- SESSIONS: Quản lý phiên đăng nhập và bảo mật thiết bị
-- Lưu trữ thông tin các phiên đăng nhập để theo dõi và quản lý bảo mật
-- =====================================================
CREATE TABLE sessions (
  id UUID PRIMARY KEY,                           -- ID phiên đăng nhập
  user_id UUID REFERENCES users(id),             -- Người dùng sở hữu phiên
  device_info TEXT,                              -- Thông tin thiết bị (browser, OS, device type)
  status VARCHAR(20),                            -- Trạng thái: active, expired, revoked
  ip_address VARCHAR(45),                        -- Địa chỉ IP đăng nhập
  login_time TIMESTAMPTZ,                        -- Thời gian đăng nhập
  logout_time TIMESTAMPTZ                        -- Thời gian đăng xuất (null nếu chưa logout)
);

-- =====================================================
-- POSTS: Quản lý nội dung bài viết trên các nền tảng
-- =====================================================
CREATE TABLE posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  title TEXT,                                    -- Tiêu đề bài viết (tuỳ chọn)
  content TEXT,                                  -- Nội dung chính
  type VARCHAR(20) CHECK (type IN ('manual', 'ai-generated')),
  status VARCHAR(20) CHECK (status IN ('draft', 'scheduled', 'published', 'failed')),

  scheduled_at TIMESTAMPTZ,                      -- Thời gian dự kiến đăng
  platforms TEXT[],                              -- Danh sách nền tảng đăng
  hashtags TEXT[],                               -- Danh sách hashtag
  images TEXT[],                                 -- Danh sách URL ảnh
  emoji TEXT,                                    -- Emoji chính
  tags TEXT[],                                   -- Gắn tag phân loại
  is_immediate BOOLEAN DEFAULT false,            -- Đăng ngay hay không
  platform_accounts UUID[],                      -- Tài khoản cụ thể được dùng để đăng

  ai_model_used VARCHAR(100),                    -- Model AI dùng để tạo bài viết
  ai_generation_id UUID,                         -- Liên kết bản ghi sinh từ AI
  template_id UUID REFERENCES templates(id),     -- Template nào dùng để tạo bài viết

  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- POST_SCHEDULES: Lịch đăng từng bài viết theo tài khoản
-- Quản lý lịch đăng chi tiết cho từng nền tảng của mỗi bài viết
-- =====================================================
CREATE TABLE post_schedules (
  id UUID PRIMARY KEY,                           -- ID lịch đăng
  post_id UUID REFERENCES posts(id),             -- Bài viết được lên lịch
  platform VARCHAR(50),                         -- Nền tảng đăng (facebook, instagram, etc.)
  account_id UUID,                               -- Tài khoản cụ thể trên nền tảng
  scheduled_time TIMESTAMPTZ,                    -- Thời gian dự kiến đăng
  status VARCHAR(20),                            -- Trạng thái: pending, published, failed
  error_log TEXT,                                -- Log lỗi nếu đăng thất bại
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian tạo lịch
);

-- =====================================================
-- AI_GENERATIONS: Lưu các bản sinh nội dung từ AI
-- Theo dõi và lưu trữ tất cả nội dung được tạo bởi AI
-- =====================================================
CREATE TABLE ai_generations (
  id UUID PRIMARY KEY,                           -- ID bản sinh AI
  user_id UUID REFERENCES users(id),             -- Người dùng yêu cầu tạo nội dung
  topic TEXT,                                    -- Chủ đề được yêu cầu
  content_type VARCHAR(50),                      -- Loại nội dung: educational, promotional, etc.
  tone VARCHAR(50),                              -- Tone: friendly, professional, casual, etc.
  length VARCHAR(20),                            -- Độ dài: short, medium, long
  content TEXT,                                  -- Nội dung được tạo ra
  hashtags TEXT[],                               -- Danh sách hashtag được đề xuất
  suggested_images TEXT[],                       -- Gợi ý từ khóa tìm ảnh
  credits_used INTEGER,                          -- Số credits đã tiêu thụ
  generation_time FLOAT,                         -- Thời gian xử lý (giây)
  model_used VARCHAR(50),                        -- Model AI được sử dụng (GPT-4, Claude, etc.)
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian tạo
);

-- =====================================================
-- AI_IMPROVEMENTS: Cải tiến nội dung bằng AI (tái viết)
-- Lưu trữ quá trình cải thiện văn bản bằng AI với các metrics đánh giá
-- =====================================================
CREATE TABLE ai_improvements (
  id UUID PRIMARY KEY,                           -- ID bản cải thiện
  user_id UUID REFERENCES users(id),             -- Người dùng yêu cầu cải thiện
  original_text TEXT,                            -- Văn bản gốc
  improved_text TEXT,                            -- Văn bản sau khi cải thiện
  improvement_type VARCHAR(50),                  -- Loại cải thiện: grammar, style, engagement, etc.
  target_tone VARCHAR(50),                       -- Tone mục tiêu
  target_length VARCHAR(20),                     -- Độ dài mục tiêu: shorter, longer, same
  platform VARCHAR(50),                         -- Nền tảng tối ưu hóa
  context TEXT,                                  -- Ngữ cảnh hoặc chủ đề
  readability_score FLOAT,                       -- Điểm dễ đọc (1-10)
  engagement_potential FLOAT,                    -- Tiềm năng tương tác (1-10)
  sentiment_score FLOAT,                         -- Điểm tích cực (-1 đến 1)
  credits_used INTEGER,                          -- Credits tiêu thụ
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian cải thiện
);

-- =====================================================
-- TEMPLATES: Template sinh bài viết tự động và marketplace
-- Lưu trữ các template nội dung có thể tái sử dụng và chia sẻ
-- =====================================================
CREATE TABLE templates (
  id UUID PRIMARY KEY,                           -- ID template
  user_id UUID REFERENCES users(id),             -- Người tạo template
  name TEXT,                                     -- Tên template
  description TEXT,                              -- Mô tả chi tiết
  content TEXT,                                  -- Nội dung template với placeholders
  category VARCHAR(100),                         -- Danh mục: marketing, education, etc.
  platforms TEXT[],                              -- Nền tảng hỗ trợ
  prompt TEXT,                                   -- Prompt AI để tạo nội dung
  is_public BOOLEAN DEFAULT false,               -- Có public trên marketplace không
  type VARCHAR(20) DEFAULT 'my',                 -- Loại: my, marketplace, premium
  thumbnail_url TEXT,                            -- Ảnh thumbnail
  rag_files TEXT[],                              -- File RAG documents liên quan
  variable_placeholders TEXT[],                  -- Danh sách biến có thể thay thế
  is_verified BOOLEAN DEFAULT false,             -- Đã được kiểm duyệt chưa
  is_featured BOOLEAN DEFAULT false,             -- Template nổi bật
  is_premium BOOLEAN DEFAULT false,              -- Template premium (tính phí)
  credit_cost INTEGER DEFAULT 0,                -- Giá bằng credits (0 = miễn phí)
  rating FLOAT,                                  -- Đánh giá trung bình (1-5)
  download_count INTEGER DEFAULT 0,              -- Số lần tải về/sử dụng
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian tạo
);

-- =====================================================
-- POST_TEMPLATE_USAGE: Lịch sử sử dụng template
-- Theo dõi việc sử dụng template để tạo bài viết và analytics
-- =====================================================
CREATE TABLE post_template_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),  -- ID bản ghi sử dụng
  post_id UUID REFERENCES posts(id),              -- Bài viết được tạo
  template_id UUID REFERENCES templates(id),      -- Template được sử dụng
  user_id UUID REFERENCES users(id),              -- Người sử dụng template
  used_prompt TEXT,                               -- Prompt thực tế đã dùng
  used_variables JSONB,                           -- Giá trị các biến đã thay thế
  used_model VARCHAR(100),                        -- Model AI đã sử dụng
  created_at TIMESTAMPTZ DEFAULT now()            -- Thời gian sử dụng
);

-- =====================================================
-- ASSET_FOLDERS: Quản lý thư mục chứa file/ảnh
-- Tổ chức file và ảnh theo cấu trúc thư mục phân cấp
-- =====================================================
CREATE TABLE asset_folders (
  id UUID PRIMARY KEY,                           -- ID thư mục
  user_id UUID REFERENCES users(id),             -- Chủ sở hữu thư mục
  parent_id UUID REFERENCES asset_folders(id),   -- Thư mục cha (null = root)
  name TEXT,                                     -- Tên thư mục
  color VARCHAR(7),                              -- Màu sắc thư mục (hex code)
  is_private BOOLEAN DEFAULT false,              -- Thư mục riêng tư
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian tạo
);

-- =====================================================
-- ASSETS: Lưu trữ ảnh, tài liệu (hỗ trợ RAG)
-- Quản lý file media và documents với khả năng tìm kiếm AI
-- =====================================================
CREATE TABLE assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),  -- ID asset
  user_id UUID REFERENCES users(id),              -- Chủ sở hữu file
  folder_id UUID REFERENCES asset_folders(id),    -- Thư mục chứa (optional)
  name TEXT NOT NULL,                             -- Tên file
  description TEXT,                               -- Mô tả file
  type VARCHAR(20) CHECK (type IN ('image', 'document')), -- Loại: image, document
  url TEXT NOT NULL,                              -- URL truy cập file
  s3_key TEXT,                                    -- Key trong S3 storage
  file_format VARCHAR(10),                        -- Định dạng: jpg, png, pdf, etc.
  file_size INTEGER,                              -- Kích thước file (bytes)
  tags TEXT[],                                    -- Tags phân loại
  resized_versions JSONB,                         -- Các phiên bản resize (cho ảnh)
  is_public BOOLEAN DEFAULT false,                -- File công khai
  is_stock BOOLEAN DEFAULT false,                 -- Ảnh stock miễn phí
  is_rag BOOLEAN DEFAULT false,                   -- File dùng cho RAG
  extracted_text TEXT,                            -- Text trích xuất từ file
  embedding_vector VECTOR,                        -- Vector embedding cho tìm kiếm
  created_at TIMESTAMPTZ DEFAULT now()            -- Thời gian upload
);

-- =====================================================
-- INTEGRATIONS: Quản lý kết nối tài khoản mạng xã hội
-- Lưu trữ thông tin kết nối và đồng bộ với các nền tảng
-- =====================================================
CREATE TABLE integrations (
  id UUID PRIMARY KEY,                           -- ID integration
  user_id UUID REFERENCES users(id),             -- Người dùng sở hữu kết nối
  platform VARCHAR(50),                         -- Nền tảng: facebook, instagram, etc.
  account_id TEXT,                               -- ID tài khoản trên nền tảng
  account_name TEXT,                             -- Tên hiển thị tài khoản
  access_token TEXT,                             -- Token truy cập API
  permissions TEXT[],                            -- Quyền được cấp
  connected_at TIMESTAMPTZ,                      -- Thời gian kết nối
  is_connected BOOLEAN DEFAULT true,             -- Trạng thái kết nối
  webhook_url TEXT,                              -- URL webhook (nếu có)
  api_endpoint TEXT,                             -- Endpoint API của nền tảng
  sync_status VARCHAR(20),                       -- Trạng thái đồng bộ: active, error, etc.
  last_synced_at TIMESTAMPTZ                     -- Lần đồng bộ cuối
);

-- =====================================================
-- POST_ANALYTICS: Thống kê hiệu suất bài viết
-- Thu thập và lưu trữ metrics từ các nền tảng mạng xã hội
-- =====================================================
CREATE TABLE post_analytics (
  id UUID PRIMARY KEY,                           -- ID bản ghi analytics
  post_id UUID REFERENCES posts(id),             -- Bài viết được theo dõi
  platform VARCHAR(50),                         -- Nền tảng: facebook, instagram, etc.
  views INTEGER,                                 -- Số lượt xem
  likes INTEGER,                                 -- Số lượt thích
  comments INTEGER,                              -- Số bình luận
  shares INTEGER,                                -- Số lượt chia sẻ
  impressions INTEGER,                           -- Số lần hiển thị
  engagement_rate FLOAT,                         -- Tỷ lệ tương tác (%)
  click_through_rate FLOAT,                      -- Tỷ lệ click (%)
  recorded_at DATE                               -- Ngày ghi nhận số liệu
);

-- =====================================================
-- REFERRALS: Lưu lịch sử giới thiệu và affiliate
-- Theo dõi chương trình giới thiệu và tính thưởng
-- =====================================================
CREATE TABLE referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),  -- ID bản ghi giới thiệu
  referrer_id UUID REFERENCES users(id),          -- Người giới thiệu
  referred_id UUID REFERENCES users(id),          -- Người được giới thiệu
  aff_code_used VARCHAR(20),                      -- Mã giới thiệu đã sử dụng
  joined_at TIMESTAMPTZ DEFAULT now(),            -- Thời gian tham gia
  reward_credited BOOLEAN DEFAULT false           -- Đã tính thưởng chưa
);

-- =====================================================
-- SUBSCRIPTIONS & CREDITS: Gói dịch vụ, giao dịch credit
-- Quản lý hệ thống thanh toán và credits của người dùng
-- =====================================================

-- Các gói dịch vụ có sẵn
CREATE TABLE credit_plans (
  id UUID PRIMARY KEY,                           -- ID gói dịch vụ
  name TEXT,                                     -- Tên gói: Free, Pro, Premium
  credits_per_month INTEGER,                     -- Số credits mỗi tháng
  price INTEGER,                                 -- Giá gói (cents)
  currency VARCHAR(10)                           -- Đơn vị tiền tệ: VND, USD
);

-- Credits hiện tại của người dùng
CREATE TABLE user_credits (
  user_id UUID PRIMARY KEY REFERENCES users(id), -- Người dùng
  current_credits INTEGER,                       -- Credits hiện có
  total_credits INTEGER,                         -- Tổng credits đã có
  plan_id UUID REFERENCES credit_plans(id),      -- Gói đang sử dụng
  renewal_date DATE                              -- Ngày gia hạn tiếp theo
);

-- Lịch sử giao dịch credits
CREATE TABLE credit_transactions (
  id UUID PRIMARY KEY,                           -- ID giao dịch
  user_id UUID REFERENCES users(id),             -- Người dùng
  type VARCHAR(20),                              -- Loại: purchase, usage, bonus, refund
  amount INTEGER,                                -- Số lượng credits (+/-)
  description TEXT,                              -- Mô tả giao dịch
  created_at TIMESTAMPTZ DEFAULT now()           -- Thời gian giao dịch
);

-- Đăng ký gói dịch vụ
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY,                           -- ID subscription
  user_id UUID REFERENCES users(id),             -- Người dùng
  plan_id UUID REFERENCES credit_plans(id),      -- Gói đã đăng ký
  start_date DATE,                               -- Ngày bắt đầu
  end_date DATE,                                 -- Ngày kết thúc
  is_active BOOLEAN DEFAULT true,                -- Đang hoạt động
  auto_renew BOOLEAN DEFAULT true                -- Tự động gia hạn
);

-- =====================================================
-- PAYMENTS: Thanh toán dịch vụ
-- Lưu trữ thông tin thanh toán và giao dịch tài chính
-- =====================================================
CREATE TABLE payments (
  id UUID PRIMARY KEY,                           -- ID thanh toán
  user_id UUID REFERENCES users(id),             -- Người thanh toán
  amount INTEGER,                                -- Số tiền (cents)
  currency VARCHAR(10),                          -- Đơn vị tiền tệ
  method VARCHAR(20),                            -- Phương thức: card, bank_transfer, momo, zalopay
  status VARCHAR(20),                            -- Trạng thái: pending, completed, failed, refunded
  payment_date TIMESTAMPTZ                       -- Thời gian thanh toán
);

-- =====================================================
-- LOGIN_HISTORY: Lưu lịch sử truy cập người dùng
-- Audit trail cho việc đăng nhập và bảo mật tài khoản
-- =====================================================
CREATE TABLE login_history (
  id UUID PRIMARY KEY,                           -- ID bản ghi lịch sử
  user_id UUID REFERENCES users(id),             -- Người dùng đăng nhập
  device_info TEXT,                              -- Thông tin thiết bị và browser
  ip_address TEXT,                               -- Địa chỉ IP
  login_time TIMESTAMPTZ                         -- Thời gian đăng nhập
);

-- =====================================================
-- SUPPORT_REQUESTS: Lưu yêu cầu hỗ trợ từ người dùng
-- =====================================================
CREATE TABLE support_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID duy nhất của yêu cầu

  user_id UUID REFERENCES users(id),                    -- Người gửi yêu cầu
  request_type VARCHAR(20) CHECK (request_type IN ('bug', 'feature', 'question')), -- Loại yêu cầu: bug = báo lỗi, feature = đề xuất, question = hỏi đáp

  title TEXT NOT NULL,                                  -- Tiêu đề mô tả ngắn
  description TEXT NOT NULL,                            -- Mô tả chi tiết vấn đề hoặc đề xuất

  status VARCHAR(20) DEFAULT 'open',                    -- Trạng thái xử lý: open, in_progress, resolved, closed
  priority VARCHAR(20),                                 -- Độ ưu tiên: low, medium, high (tùy chọn)
  assigned_to UUID REFERENCES users(id),                -- Người xử lý yêu cầu (admin/support)

  created_at TIMESTAMPTZ DEFAULT now(),                 -- Ngày tạo yêu cầu
  updated_at TIMESTAMPTZ DEFAULT now(),                 -- Ngày cập nhật gần nhất
  resolved_at TIMESTAMPTZ                               -- Ngày đánh dấu hoàn thành (nếu có)
);

-- =====================================================
-- SUPPORT_COMMENTS: Ghi chú & trao đổi trong ticket hỗ trợ
-- =====================================================
CREATE TABLE support_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID ghi chú

  request_id UUID REFERENCES support_requests(id),      -- Liên kết tới yêu cầu hỗ trợ
  user_id UUID REFERENCES users(id),                    -- Người để lại ghi chú (người dùng hoặc admin)
  content TEXT NOT NULL,                                -- Nội dung ghi chú hoặc phản hồi
  created_at TIMESTAMPTZ DEFAULT now()                  -- Thời điểm comment
);

-- =====================================================
-- TWO_FACTOR_AUTH: Quản lý xác thực 2 bước
-- Lưu trữ thông tin 2FA và backup codes
-- =====================================================
CREATE TABLE two_factor_auth (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID bản ghi 2FA
  user_id UUID REFERENCES users(id) UNIQUE,             -- Người dùng (unique)
  secret_key TEXT NOT NULL,                             -- Secret key cho TOTP
  method VARCHAR(10) DEFAULT 'totp',                    -- Phương thức: totp, sms
  phone_number VARCHAR(15),                             -- SĐT cho SMS 2FA
  backup_codes TEXT[],                                  -- Mã backup khẩn cấp
  backup_codes_used INTEGER DEFAULT 0,                  -- Số mã backup đã dùng
  enabled_at TIMESTAMPTZ DEFAULT now(),                 -- Thời gian kích hoạt
  last_used_at TIMESTAMPTZ,                             -- Lần sử dụng cuối
  created_at TIMESTAMPTZ DEFAULT now()                  -- Thời gian tạo
);

-- =====================================================
-- TRUSTED_DEVICES: Thiết bị đáng tin cậy (2FA)
-- Lưu trữ thiết bị đã được tin cậy để bỏ qua 2FA
-- =====================================================
CREATE TABLE trusted_devices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID thiết bị
  user_id UUID REFERENCES users(id),                    -- Người dùng
  device_fingerprint TEXT NOT NULL,                     -- Fingerprint thiết bị
  device_name TEXT,                                     -- Tên thiết bị
  browser_info TEXT,                                    -- Thông tin browser
  ip_address VARCHAR(45),                               -- IP khi trust
  location TEXT,                                        -- Vị trí địa lý
  trusted_at TIMESTAMPTZ DEFAULT now(),                 -- Thời gian trust
  expires_at TIMESTAMPTZ,                               -- Thời gian hết hạn (30 ngày)
  last_used_at TIMESTAMPTZ,                             -- Lần sử dụng cuối
  is_active BOOLEAN DEFAULT true                        -- Còn hoạt động không
);

-- =====================================================
-- ACCOUNT_DELETIONS: Yêu cầu xóa tài khoản
-- Quản lý quá trình xóa tài khoản với grace period
-- =====================================================
CREATE TABLE account_deletions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID yêu cầu xóa
  user_id UUID REFERENCES users(id),                    -- Người dùng yêu cầu
  reason VARCHAR(50),                                   -- Lý do xóa
  reason_detail TEXT,                                   -- Chi tiết lý do
  scheduled_at TIMESTAMPTZ,                             -- Thời gian xóa dự kiến
  grace_period_days INTEGER DEFAULT 30,                 -- Số ngày grace period
  status VARCHAR(20) DEFAULT 'pending',                 -- Trạng thái: pending, cancelled, completed
  backup_created BOOLEAN DEFAULT false,                 -- Đã tạo backup chưa
  backup_url TEXT,                                      -- URL backup (nếu có)
  cancelled_at TIMESTAMPTZ,                             -- Thời gian hủy (nếu có)
  completed_at TIMESTAMPTZ,                             -- Thời gian hoàn thành
  created_at TIMESTAMPTZ DEFAULT now()                  -- Thời gian tạo yêu cầu
);

-- =====================================================
-- DATA_BACKUPS: Backup dữ liệu người dùng
-- Quản lý việc tạo và tải backup dữ liệu
-- =====================================================
CREATE TABLE data_backups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID backup
  user_id UUID REFERENCES users(id),                    -- Người dùng
  backup_type VARCHAR(20) DEFAULT 'manual',             -- Loại: manual, auto, deletion
  include_data TEXT[],                                  -- Loại dữ liệu: profile, posts, etc.
  format VARCHAR(10) DEFAULT 'zip',                     -- Định dạng: zip, json, csv
  file_size INTEGER,                                    -- Kích thước file (bytes)
  file_url TEXT,                                        -- URL tải file
  s3_key TEXT,                                          -- Key trong S3
  encrypted BOOLEAN DEFAULT true,                       -- File có mã hóa không
  status VARCHAR(20) DEFAULT 'processing',              -- Trạng thái: processing, completed, failed, expired
  progress INTEGER DEFAULT 0,                           -- Tiến độ (0-100%)
  error_message TEXT,                                   -- Thông báo lỗi (nếu có)
  download_count INTEGER DEFAULT 0,                     -- Số lần tải
  expires_at TIMESTAMPTZ,                               -- Thời gian hết hạn
  created_at TIMESTAMPTZ DEFAULT now(),                 -- Thời gian tạo
  completed_at TIMESTAMPTZ                              -- Thời gian hoàn thành
);

-- =====================================================
-- SECURITY_EVENTS: Log các sự kiện bảo mật
-- Audit trail cho tất cả hoạt động bảo mật
-- =====================================================
CREATE TABLE security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),        -- ID sự kiện
  user_id UUID REFERENCES users(id),                    -- Người dùng liên quan
  event_type VARCHAR(50) NOT NULL,                      -- Loại sự kiện
  event_description TEXT,                               -- Mô tả chi tiết
  severity VARCHAR(20) DEFAULT 'low',                   -- Mức độ: low, medium, high, critical
  ip_address VARCHAR(45),                               -- IP address
  user_agent TEXT,                                      -- User agent
  device_info TEXT,                                     -- Thông tin thiết bị
  location TEXT,                                        -- Vị trí địa lý
  metadata JSONB,                                       -- Dữ liệu bổ sung
  created_at TIMESTAMPTZ DEFAULT now()                  -- Thời gian xảy ra
);
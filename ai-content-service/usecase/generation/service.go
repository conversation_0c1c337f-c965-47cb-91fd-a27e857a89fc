package generation

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/ai-content-service/ent"
	"github.com/social-content-ai/ai-content-service/ent/generation"
	"github.com/social-content-ai/ai-content-service/pkg/ai"
	"github.com/social-content-ai/ai-content-service/pkg/prompt"
	"github.com/social-content-ai/ai-content-service/pkg/providers/anthropic"
	"github.com/social-content-ai/ai-content-service/pkg/providers/google"
	"github.com/social-content-ai/ai-content-service/pkg/providers/openai"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB          *ent.Client
	writeDB         *ent.Client
	aiService       ai.Service
	openaiClient    *openai.Client
	anthropicClient *anthropic.Client
	googleClient    *google.Client
	promptBuilder   *prompt.Builder
	logger          logging.Logger
}

// NewService creates a new generation service
func NewService(
	readDB, writeDB *ent.Client,
	aiService ai.Service,
	logger logging.Logger,
) UseCase {
	return &service{
		readDB:    readDB,
		writeDB:   writeDB,
		aiService: aiService,
		logger:    logger,
	}
}

// GenerateContent generates new content using AI
func (s *service) GenerateContent(ctx context.Context, req *GenerateContentRequest) (*ent.Generation, error) {
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Create generation record
	genBuilder := s.writeDB.Generation.Create().
		SetUserID(userID).
		SetTopic(req.Topic).
		SetContentType(generation.ContentType(req.ContentType)).
		SetTone(generation.Tone(req.Tone)).
		SetLength(generation.Length(req.Length)).
		SetModel(req.Model).
		SetStatus(generation.StatusPending)

	if len(req.Platforms) > 0 {
		genBuilder.SetPlatforms(req.Platforms)
	}
	if req.TemplateID != "" {
		genBuilder.SetTemplateID(req.TemplateID)
	}
	if len(req.Variables) > 0 {
		genBuilder.SetVariables(req.Variables)
	}
	if req.WorkspaceID != "" {
		genBuilder.SetWorkspaceID(req.WorkspaceID)
	}

	genRecord, err := genBuilder.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to create generation record")
		return nil, fmt.Errorf("failed to create generation record: %w", err)
	}

	// Generate content asynchronously
	go s.processGeneration(context.Background(), genRecord, req)

	return genRecord, nil
}

// processGeneration processes the content generation
func (s *service) processGeneration(ctx context.Context, genRecord *ent.Generation, req *GenerateContentRequest) {
	start := time.Now()

	// Build AI request
	aiReq := &ai.GenerationRequest{
		Topic:       req.Topic,
		ContentType: req.ContentType,
		Tone:        req.Tone,
		Length:      req.Length,
		Platforms:   req.Platforms,
		Model:       req.Model,
		Context:     req.Context,
		Variables:   req.Variables,
	}

	// Generate content
	result, err := s.aiService.GenerateContent(ctx, aiReq)
	if err != nil {
		s.logger.WithError(err).WithField("generation_id", genRecord.ID).Error("Failed to generate content")

		// Update record with error
		s.writeDB.Generation.UpdateOneID(genRecord.ID).
			SetStatus(generation.StatusFailed).
			SetErrorMessage(err.Error()).
			SetGenerationTime(float64(time.Since(start).Seconds())).
			Exec(ctx)
		return
	}

	// Calculate credits used based on model and content length
	creditsUsed := s.calculateCreditsUsed(req.Model, len(result.Content))

	// Update generation record
	updateBuilder := s.writeDB.Generation.UpdateOneID(genRecord.ID).
		SetContent(result.Content).
		SetCreditsUsed(creditsUsed).
		SetGenerationTime(float64(time.Since(start).Seconds())).
		SetModelUsed(req.Model).
		SetStatus(generation.StatusCompleted)

	if len(result.Hashtags) > 0 {
		updateBuilder.SetHashtags(result.Hashtags)
	}
	if len(result.SuggestedImages) > 0 {
		updateBuilder.SetSuggestedImages(result.SuggestedImages)
	}

	_, err = updateBuilder.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("generation_id", genRecord.ID).Error("Failed to update generation record")
		return
	}

	s.logger.WithFields(map[string]interface{}{
		"generation_id":   genRecord.ID,
		"user_id":         req.UserID,
		"credits_used":    creditsUsed,
		"generation_time": time.Since(start).Seconds(),
	}).Info("Content generated successfully")
}

// RegenerateContent regenerates content with new parameters
func (s *service) RegenerateContent(ctx context.Context, req *RegenerateContentRequest) (*ent.Generation, error) {
	generationID, err := uuid.Parse(req.GenerationID)
	if err != nil {
		return nil, fmt.Errorf("invalid generation ID: %w", err)
	}

	// Get original generation
	originalGen, err := s.readDB.Generation.Get(ctx, generationID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("generation not found")
		}
		s.logger.WithError(err).WithField("generation_id", req.GenerationID).Error("Failed to get generation")
		return nil, fmt.Errorf("failed to get generation: %w", err)
	}

	// Create new generation request based on original with updates
	newReq := &GenerateContentRequest{
		UserID:      originalGen.UserID.String(),
		Topic:       originalGen.Topic,
		ContentType: string(originalGen.ContentType),
		Tone:        string(originalGen.Tone),
		Length:      string(originalGen.Length),
		Model:       originalGen.Model,
		TemplateID:  originalGen.TemplateID,
		WorkspaceID: originalGen.WorkspaceID,
	}

	// Handle JSON fields
	if originalGen.Platforms != nil {
		if platforms, ok := originalGen.Platforms.([]string); ok {
			newReq.Platforms = platforms
		}
	}
	if originalGen.Variables != nil {
		if variables, ok := originalGen.Variables.(map[string]string); ok {
			newReq.Variables = variables
		}
	}

	// Apply updates
	if req.NewTopic != "" {
		newReq.Topic = req.NewTopic
	}
	if req.NewTone != "" {
		newReq.Tone = req.NewTone
	}
	if req.NewLength != "" {
		newReq.Length = req.NewLength
	}
	if req.NewModel != "" {
		newReq.Model = req.NewModel
	}

	// Generate new content
	return s.GenerateContent(ctx, newReq)
}

// GetGeneration retrieves a generation by ID
func (s *service) GetGeneration(ctx context.Context, id string) (*ent.Generation, error) {
	generationID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid generation ID: %w", err)
	}

	genRecord, err := s.readDB.Generation.Get(ctx, generationID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("generation not found")
		}
		s.logger.WithError(err).WithField("generation_id", id).Error("Failed to get generation")
		return nil, fmt.Errorf("failed to get generation: %w", err)
	}

	return genRecord, nil
}

// ListGenerations lists generations with pagination and filters
func (s *service) ListGenerations(ctx context.Context, req *ListGenerationsRequest) ([]*ent.Generation, int, error) {
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.readDB.Generation.Query().Where(generation.UserID(userID))

	// Apply filters
	if req.Model != "" {
		query = query.Where(generation.Model(req.Model))
	}
	if req.ContentType != "" {
		query = query.Where(generation.ContentType(generation.ContentType(req.ContentType)))
	}
	if req.Status != "" {
		query = query.Where(generation.Status(generation.Status(req.Status)))
	}
	if req.WorkspaceID != "" {
		workspaceUUID, err := uuid.Parse(req.WorkspaceID)
		if err == nil {
			query = query.Where(generation.WorkspaceID(workspaceUUID.String()))
		}
	}

	// Date range filter
	if req.DateFrom != "" {
		if dateFrom, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			query = query.Where(generation.CreatedAtGTE(dateFrom))
		}
	}
	if req.DateTo != "" {
		if dateTo, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			query = query.Where(generation.CreatedAtLTE(dateTo.Add(24 * time.Hour)))
		}
	}

	// Count total
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to count generations")
		return nil, 0, fmt.Errorf("failed to count generations: %w", err)
	}

	// Apply sorting
	switch req.SortBy {
	case "created_at":
		if req.SortOrder == "asc" {
			query = query.Order(ent.Asc(generation.FieldCreatedAt))
		} else {
			query = query.Order(ent.Desc(generation.FieldCreatedAt))
		}
	case "updated_at":
		if req.SortOrder == "asc" {
			query = query.Order(ent.Asc(generation.FieldUpdatedAt))
		} else {
			query = query.Order(ent.Desc(generation.FieldUpdatedAt))
		}
	case "credits_used":
		if req.SortOrder == "asc" {
			query = query.Order(ent.Asc(generation.FieldCreditsUsed))
		} else {
			query = query.Order(ent.Desc(generation.FieldCreditsUsed))
		}
	case "generation_time":
		if req.SortOrder == "asc" {
			query = query.Order(ent.Asc(generation.FieldGenerationTime))
		} else {
			query = query.Order(ent.Desc(generation.FieldGenerationTime))
		}
	default:
		query = query.Order(ent.Desc(generation.FieldCreatedAt))
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	generations, err := query.Offset(offset).Limit(req.Limit).All(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to list generations")
		return nil, 0, fmt.Errorf("failed to list generations: %w", err)
	}

	return generations, total, nil
}

// DeleteGeneration deletes a generation
func (s *service) DeleteGeneration(ctx context.Context, id string) error {
	generationID, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid generation ID: %w", err)
	}

	err = s.writeDB.Generation.DeleteOneID(generationID).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("generation not found")
		}
		s.logger.WithError(err).WithField("generation_id", id).Error("Failed to delete generation")
		return fmt.Errorf("failed to delete generation: %w", err)
	}

	s.logger.WithField("generation_id", id).Info("Generation deleted successfully")
	return nil
}

// GetGenerationStats gets generation statistics for user
func (s *service) GetGenerationStats(ctx context.Context, userID string, dateRange *DateRange) (*GenerationStats, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.readDB.Generation.Query().Where(generation.UserID(userUUID))

	// Apply date range filter
	if dateRange != nil {
		if dateFrom, err := time.Parse("2006-01-02", dateRange.From); err == nil {
			query = query.Where(generation.CreatedAtGTE(dateFrom))
		}
		if dateTo, err := time.Parse("2006-01-02", dateRange.To); err == nil {
			query = query.Where(generation.CreatedAtLTE(dateTo.Add(24 * time.Hour)))
		}
	}

	generations, err := query.All(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to get generations for stats")
		return nil, fmt.Errorf("failed to get generations: %w", err)
	}

	// Calculate statistics
	stats := &GenerationStats{
		ModelUsage:       make(map[string]int),
		ContentTypeUsage: make(map[string]int),
		ToneUsage:        make(map[string]int),
		PlatformUsage:    make(map[string]int),
		DailyStats:       make([]*DailyGenerationStat, 0),
	}

	totalCredits := 0
	totalTime := 0.0
	successCount := 0
	dailyStatsMap := make(map[string]*DailyGenerationStat)

	for _, gen := range generations {
		stats.TotalGenerations++
		totalCredits += gen.CreditsUsed
		totalTime += gen.GenerationTime

		if gen.Status == generation.StatusCompleted {
			successCount++
		}

		// Model usage
		stats.ModelUsage[gen.Model]++

		// Content type usage
		stats.ContentTypeUsage[string(gen.ContentType)]++

		// Tone usage
		stats.ToneUsage[string(gen.Tone)]++

		// Platform usage
		if gen.Platforms != nil {
			if platforms, ok := gen.Platforms.([]string); ok {
				for _, platform := range platforms {
					stats.PlatformUsage[platform]++
				}
			}
		}

		// Daily stats
		dateKey := gen.CreatedAt.Format("2006-01-02")
		if dailyStat, exists := dailyStatsMap[dateKey]; exists {
			dailyStat.Count++
			dailyStat.CreditsUsed += gen.CreditsUsed
		} else {
			dailyStatsMap[dateKey] = &DailyGenerationStat{
				Date:        dateKey,
				Count:       1,
				CreditsUsed: gen.CreditsUsed,
			}
		}
	}

	stats.TotalCreditsUsed = totalCredits
	if stats.TotalGenerations > 0 {
		stats.AverageGenerationTime = totalTime / float64(stats.TotalGenerations)
		stats.SuccessRate = float64(successCount) / float64(stats.TotalGenerations) * 100
	}

	// Convert daily stats map to slice
	for _, dailyStat := range dailyStatsMap {
		stats.DailyStats = append(stats.DailyStats, dailyStat)
	}

	return stats, nil
}

// GetPopularTopics gets popular topics across all users
func (s *service) GetPopularTopics(ctx context.Context, limit int) ([]*TopicStats, error) {
	// This is a simplified implementation
	// In a real system, you might want to use more sophisticated analytics

	generations, err := s.readDB.Generation.Query().
		Where(generation.Status(generation.StatusCompleted)).
		Order(ent.Desc(generation.FieldCreatedAt)).
		Limit(1000). // Analyze recent 1000 generations
		All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get generations for popular topics")
		return nil, fmt.Errorf("failed to get generations: %w", err)
	}

	topicCount := make(map[string]int)
	for _, gen := range generations {
		// Extract keywords from topic
		keywords := s.extractKeywords(gen.Topic)
		for _, keyword := range keywords {
			topicCount[keyword]++
		}
	}

	// Convert to TopicStats and sort by count
	var topicStats []*TopicStats
	for topic, count := range topicCount {
		if count >= 2 { // Only include topics with at least 2 occurrences
			topicStats = append(topicStats, &TopicStats{
				Topic: topic,
				Count: count,
				Score: float64(count), // Simple scoring based on count
			})
		}
	}

	// Sort by count (descending)
	for i := 0; i < len(topicStats)-1; i++ {
		for j := i + 1; j < len(topicStats); j++ {
			if topicStats[i].Count < topicStats[j].Count {
				topicStats[i], topicStats[j] = topicStats[j], topicStats[i]
			}
		}
	}

	// Limit results
	if len(topicStats) > limit {
		topicStats = topicStats[:limit]
	}

	return topicStats, nil
}

// calculateCreditsUsed calculates credits based on model and content length
func (s *service) calculateCreditsUsed(model string, contentLength int) int {
	baseCredits := 1

	// Model-based pricing
	switch model {
	case "gpt-4":
		baseCredits = 3
	case "claude-3":
		baseCredits = 3
	case "gemini-pro":
		baseCredits = 2
	case "gpt-3.5-turbo":
		baseCredits = 1
	case "claude-2":
		baseCredits = 2
	default:
		baseCredits = 1
	}

	// Length-based multiplier
	lengthMultiplier := 1.0
	if contentLength > 1000 {
		lengthMultiplier = 1.5
	} else if contentLength > 500 {
		lengthMultiplier = 1.2
	}

	return int(float64(baseCredits) * lengthMultiplier)
}

// extractKeywords extracts keywords from topic (simplified implementation)
func (s *service) extractKeywords(topic string) []string {
	// Simple keyword extraction - split by spaces and filter common words
	words := strings.Fields(strings.ToLower(topic))
	commonWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true,
		"but": true, "in": true, "on": true, "at": true, "to": true,
		"for": true, "of": true, "with": true, "by": true, "is": true,
		"are": true, "was": true, "were": true, "be": true, "been": true,
		"have": true, "has": true, "had": true, "do": true, "does": true,
		"did": true, "will": true, "would": true, "could": true, "should": true,
	}

	var keywords []string
	for _, word := range words {
		if len(word) > 2 && !commonWords[word] {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

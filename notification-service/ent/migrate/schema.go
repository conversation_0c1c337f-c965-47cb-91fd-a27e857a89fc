// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// NotificationsColumns holds the columns for the "notifications" table.
	NotificationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"content_generated", "content_improved", "post_published", "post_scheduled", "post_failed", "credit_low", "credit_purchased", "subscription_renewed", "subscription_expired", "integration_connected", "integration_failed", "analytics_report", "system_maintenance", "welcome", "password_reset", "email_verification", "security_alert"}},
		{Name: "title", Type: field.TypeString, Size: 200},
		{Name: "message", Type: field.TypeString, Size: 2147483647},
		{Name: "data", Type: field.TypeJSON, Nullable: true},
		{Name: "priority", Type: field.TypeEnum, Enums: []string{"low", "normal", "high", "urgent"}, Default: "normal"},
		{Name: "channels", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "sent", "delivered", "failed", "read"}, Default: "pending"},
		{Name: "reference_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "reference_type", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "delivery_status", Type: field.TypeJSON, Nullable: true},
		{Name: "scheduled_at", Type: field.TypeTime, Nullable: true},
		{Name: "sent_at", Type: field.TypeTime, Nullable: true},
		{Name: "delivered_at", Type: field.TypeTime, Nullable: true},
		{Name: "read_at", Type: field.TypeTime, Nullable: true},
		{Name: "expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "action_url", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "action_data", Type: field.TypeJSON, Nullable: true},
		{Name: "retry_count", Type: field.TypeInt, Default: 0},
		{Name: "next_retry_at", Type: field.TypeTime, Nullable: true},
		{Name: "error_message", Type: field.TypeString, Nullable: true, Size: 1000},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// NotificationsTable holds the schema information for the "notifications" table.
	NotificationsTable = &schema.Table{
		Name:       "notifications",
		Columns:    NotificationsColumns,
		PrimaryKey: []*schema.Column{NotificationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "notification_user_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[1]},
			},
			{
				Name:    "notification_type",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[2]},
			},
			{
				Name:    "notification_status",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[8]},
			},
			{
				Name:    "notification_priority",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[6]},
			},
			{
				Name:    "notification_reference_id",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[9]},
			},
			{
				Name:    "notification_reference_type",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[10]},
			},
			{
				Name:    "notification_scheduled_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[12]},
			},
			{
				Name:    "notification_sent_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[13]},
			},
			{
				Name:    "notification_read_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[15]},
			},
			{
				Name:    "notification_expires_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[16]},
			},
			{
				Name:    "notification_next_retry_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[20]},
			},
			{
				Name:    "notification_created_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[23]},
			},
			{
				Name:    "notification_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[25]},
			},
			{
				Name:    "notification_user_id_status",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[1], NotificationsColumns[8]},
			},
			{
				Name:    "notification_user_id_type",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[1], NotificationsColumns[2]},
			},
			{
				Name:    "notification_user_id_read_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationsColumns[1], NotificationsColumns[15]},
			},
		},
	}
	// NotificationPreferencesColumns holds the columns for the "notification_preferences" table.
	NotificationPreferencesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID, Unique: true},
		{Name: "email_enabled", Type: field.TypeBool, Default: true},
		{Name: "push_enabled", Type: field.TypeBool, Default: true},
		{Name: "in_app_enabled", Type: field.TypeBool, Default: true},
		{Name: "sms_enabled", Type: field.TypeBool, Default: false},
		{Name: "notification_types", Type: field.TypeJSON, Nullable: true},
		{Name: "quiet_hours", Type: field.TypeJSON, Nullable: true},
		{Name: "timezone", Type: field.TypeString, Size: 50, Default: "UTC"},
		{Name: "language", Type: field.TypeString, Size: 5, Default: "en"},
		{Name: "email_settings", Type: field.TypeJSON, Nullable: true},
		{Name: "push_settings", Type: field.TypeJSON, Nullable: true},
		{Name: "frequency_settings", Type: field.TypeJSON, Nullable: true},
		{Name: "marketing_emails", Type: field.TypeBool, Default: false},
		{Name: "product_updates", Type: field.TypeBool, Default: true},
		{Name: "security_alerts", Type: field.TypeBool, Default: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// NotificationPreferencesTable holds the schema information for the "notification_preferences" table.
	NotificationPreferencesTable = &schema.Table{
		Name:       "notification_preferences",
		Columns:    NotificationPreferencesColumns,
		PrimaryKey: []*schema.Column{NotificationPreferencesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "notificationpreference_user_id",
				Unique:  true,
				Columns: []*schema.Column{NotificationPreferencesColumns[1]},
			},
			{
				Name:    "notificationpreference_email_enabled",
				Unique:  false,
				Columns: []*schema.Column{NotificationPreferencesColumns[2]},
			},
			{
				Name:    "notificationpreference_push_enabled",
				Unique:  false,
				Columns: []*schema.Column{NotificationPreferencesColumns[3]},
			},
			{
				Name:    "notificationpreference_timezone",
				Unique:  false,
				Columns: []*schema.Column{NotificationPreferencesColumns[8]},
			},
			{
				Name:    "notificationpreference_language",
				Unique:  false,
				Columns: []*schema.Column{NotificationPreferencesColumns[9]},
			},
		},
	}
	// NotificationTemplatesColumns holds the columns for the "notification_templates" table.
	NotificationTemplatesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "event_type", Type: field.TypeString, Size: 100},
		{Name: "name", Type: field.TypeString, Size: 200},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "templates", Type: field.TypeJSON},
		{Name: "is_active", Type: field.TypeBool, Default: true},
		{Name: "variables", Type: field.TypeJSON, Nullable: true},
		{Name: "default_channels", Type: field.TypeJSON, Nullable: true},
		{Name: "default_priority", Type: field.TypeEnum, Enums: []string{"low", "normal", "high", "urgent"}, Default: "normal"},
		{Name: "language", Type: field.TypeString, Size: 5, Default: "en"},
		{Name: "version", Type: field.TypeString, Size: 20, Default: "1.0.0"},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_by", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// NotificationTemplatesTable holds the schema information for the "notification_templates" table.
	NotificationTemplatesTable = &schema.Table{
		Name:       "notification_templates",
		Columns:    NotificationTemplatesColumns,
		PrimaryKey: []*schema.Column{NotificationTemplatesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "notificationtemplate_event_type",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[1]},
			},
			{
				Name:    "notificationtemplate_is_active",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[5]},
			},
			{
				Name:    "notificationtemplate_language",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[9]},
			},
			{
				Name:    "notificationtemplate_version",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[10]},
			},
			{
				Name:    "notificationtemplate_created_by",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[12]},
			},
			{
				Name:    "notificationtemplate_created_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[13]},
			},
			{
				Name:    "notificationtemplate_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[15]},
			},
			{
				Name:    "notificationtemplate_event_type_is_active",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[1], NotificationTemplatesColumns[5]},
			},
			{
				Name:    "notificationtemplate_event_type_language",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[1], NotificationTemplatesColumns[9]},
			},
			{
				Name:    "notificationtemplate_is_active_language",
				Unique:  false,
				Columns: []*schema.Column{NotificationTemplatesColumns[5], NotificationTemplatesColumns[9]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		NotificationsTable,
		NotificationPreferencesTable,
		NotificationTemplatesTable,
	}
)

func init() {
}

package openai

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/social-content-ai/pkg-shared/logging"
)

func setupTestClient() (*Client, *httptest.Server) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Default handler - will be overridden in tests
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"id": "test-id",
			"object": "chat.completion",
			"created": 1234567890,
			"model": "gpt-3.5-turbo",
			"choices": []map[string]interface{}{
				{
					"index": 0,
					"message": map[string]interface{}{
						"role": "assistant",
						"content": "Test response",
					},
					"finish_reason": "stop",
				},
			},
			"usage": map[string]interface{}{
				"prompt_tokens": 10,
				"completion_tokens": 5,
				"total_tokens": 15,
			},
		})
	}))

	client := NewClient("test-api-key", server.URL, logger)
	return client, server
}

func TestClient_GenerateContent(t *testing.T) {
	client, server := setupTestClient()
	defer server.Close()

	t.Run("successful content generation", func(t *testing.T) {
		// Override server handler for this test
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify request
			assert.Equal(t, "POST", r.Method)
			assert.Equal(t, "/chat/completions", r.URL.Path)
			assert.Equal(t, "Bearer test-api-key", r.Header.Get("Authorization"))
			assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

			// Parse request body
			var reqBody GenerateContentRequest
			err := json.NewDecoder(r.Body).Decode(&reqBody)
			require.NoError(t, err)

			// Verify request content
			assert.Equal(t, "gpt-3.5-turbo", reqBody.Model)
			assert.Equal(t, 0.7, reqBody.Temperature)
			assert.Len(t, reqBody.Messages, 1)
			assert.Equal(t, "user", reqBody.Messages[0].Role)
			assert.Equal(t, "Test prompt", reqBody.Messages[0].Content)

			// Send response
			response := GenerateContentResponse{
				ID:      "chatcmpl-test123",
				Object:  "chat.completion",
				Created: 1234567890,
				Model:   "gpt-3.5-turbo",
				Choices: []Choice{
					{
						Index: 0,
						Message: Message{
							Role:    "assistant",
							Content: "Generated content response",
						},
						FinishReason: "stop",
					},
				},
				Usage: Usage{
					PromptTokens:     10,
					CompletionTokens: 20,
					TotalTokens:      30,
				},
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		})

		// Execute test
		req := &GenerateContentRequest{
			Model:       "gpt-3.5-turbo",
			Messages:    []Message{{Role: "user", Content: "Test prompt"}},
			Temperature: 0.7,
			MaxTokens:   100,
		}

		resp, err := client.GenerateContent(context.Background(), req)

		// Assertions
		require.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "chatcmpl-test123", resp.ID)
		assert.Equal(t, "gpt-3.5-turbo", resp.Model)
		assert.Len(t, resp.Choices, 1)
		assert.Equal(t, "Generated content response", resp.Choices[0].Message.Content)
		assert.Equal(t, 30, resp.Usage.TotalTokens)
	})

	t.Run("API error response", func(t *testing.T) {
		// Override server handler for error response
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusBadRequest)
			errorResp := ErrorResponse{
				Error: struct {
					Message string `json:"message"`
					Type    string `json:"type"`
					Code    string `json:"code"`
				}{
					Message: "Invalid request",
					Type:    "invalid_request_error",
					Code:    "invalid_api_key",
				},
			}
			json.NewEncoder(w).Encode(errorResp)
		})

		// Execute test
		req := &GenerateContentRequest{
			Messages: []Message{{Role: "user", Content: "Test prompt"}},
		}

		resp, err := client.GenerateContent(context.Background(), req)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "Invalid request")
	})

	t.Run("default model and temperature", func(t *testing.T) {
		// Override server handler to check defaults
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			var reqBody GenerateContentRequest
			json.NewDecoder(r.Body).Decode(&reqBody)

			// Verify defaults are set
			assert.Equal(t, "gpt-3.5-turbo", reqBody.Model)
			assert.Equal(t, 0.7, reqBody.Temperature)

			// Send minimal response
			response := GenerateContentResponse{
				ID: "test",
				Choices: []Choice{
					{Message: Message{Content: "response"}},
				},
			}
			json.NewEncoder(w).Encode(response)
		})

		// Execute test with minimal request
		req := &GenerateContentRequest{
			Messages: []Message{{Role: "user", Content: "Test"}},
		}

		_, err := client.GenerateContent(context.Background(), req)
		require.NoError(t, err)
	})
}

func TestClient_ImproveContent(t *testing.T) {
	client, server := setupTestClient()
	defer server.Close()

	t.Run("successful content improvement", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			var reqBody GenerateContentRequest
			json.NewDecoder(r.Body).Decode(&reqBody)

			// Verify system prompt is included
			assert.Len(t, reqBody.Messages, 2)
			assert.Equal(t, "system", reqBody.Messages[0].Role)
			assert.Contains(t, reqBody.Messages[0].Content, "expert content writer")
			assert.Equal(t, "user", reqBody.Messages[1].Role)
			assert.Contains(t, reqBody.Messages[1].Content, "Original content")
			assert.Contains(t, reqBody.Messages[1].Content, "Make it better")

			response := GenerateContentResponse{
				ID: "test",
				Choices: []Choice{
					{Message: Message{Content: "Improved content"}},
				},
			}
			json.NewEncoder(w).Encode(response)
		})

		resp, err := client.ImproveContent(context.Background(), "Original content", "Make it better")

		require.NoError(t, err)
		assert.Equal(t, "Improved content", resp.Choices[0].Message.Content)
	})
}

func TestClient_GenerateHashtags(t *testing.T) {
	client, server := setupTestClient()
	defer server.Close()

	t.Run("successful hashtag generation", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			var reqBody GenerateContentRequest
			json.NewDecoder(r.Body).Decode(&reqBody)

			// Verify request parameters
			assert.Equal(t, "gpt-3.5-turbo", reqBody.Model)
			assert.Equal(t, 0.8, reqBody.Temperature)
			assert.Equal(t, 200, reqBody.MaxTokens)

			// Verify prompt content
			assert.Len(t, reqBody.Messages, 2)
			assert.Contains(t, reqBody.Messages[1].Content, "Generate 5 hashtags")
			assert.Contains(t, reqBody.Messages[1].Content, "instagram")
			assert.Contains(t, reqBody.Messages[1].Content, "Test content")

			// Return hashtags response
			hashtagsResponse := `socialmedia
contentcreation
instagram
marketing
digitalmarketing`

			response := GenerateContentResponse{
				ID: "test",
				Choices: []Choice{
					{Message: Message{Content: hashtagsResponse}},
				},
			}
			json.NewEncoder(w).Encode(response)
		})

		hashtags, err := client.GenerateHashtags(context.Background(), "Test content", "instagram", 5)

		require.NoError(t, err)
		assert.Len(t, hashtags, 5)
		assert.Contains(t, hashtags, "socialmedia")
		assert.Contains(t, hashtags, "contentcreation")
		assert.Contains(t, hashtags, "instagram")
	})

	t.Run("default count", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			var reqBody GenerateContentRequest
			json.NewDecoder(r.Body).Decode(&reqBody)

			// Verify default count is used
			assert.Contains(t, reqBody.Messages[1].Content, "Generate 10 hashtags")

			response := GenerateContentResponse{
				ID: "test",
				Choices: []Choice{
					{Message: Message{Content: "hashtag1\nhashtag2"}},
				},
			}
			json.NewEncoder(w).Encode(response)
		})

		hashtags, err := client.GenerateHashtags(context.Background(), "Test content", "twitter", 0)

		require.NoError(t, err)
		assert.Len(t, hashtags, 2)
	})

	t.Run("no hashtags generated", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := GenerateContentResponse{
				ID:      "test",
				Choices: []Choice{}, // Empty choices
			}
			json.NewEncoder(w).Encode(response)
		})

		hashtags, err := client.GenerateHashtags(context.Background(), "Test content", "twitter", 5)

		assert.Error(t, err)
		assert.Nil(t, hashtags)
		assert.Contains(t, err.Error(), "no hashtags generated")
	})
}

func TestClient_HealthCheck(t *testing.T) {
	client, server := setupTestClient()
	defer server.Close()

	t.Run("healthy service", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Equal(t, "GET", r.Method)
			assert.Equal(t, "/models", r.URL.Path)
			assert.Equal(t, "Bearer test-api-key", r.Header.Get("Authorization"))

			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"data": []map[string]interface{}{
					{"id": "gpt-3.5-turbo"},
				},
			})
		})

		err := client.HealthCheck(context.Background())
		assert.NoError(t, err)
	})

	t.Run("unhealthy service", func(t *testing.T) {
		server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		})

		err := client.HealthCheck(context.Background())
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "health check failed")
	})
}

func TestParseHashtags(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "simple hashtags",
			input:    "hashtag1\nhashtag2\nhashtag3",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
		{
			name:     "hashtags with # symbol",
			input:    "#hashtag1\n#hashtag2\n#hashtag3",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
		{
			name:     "numbered list",
			input:    "1. hashtag1\n2. hashtag2\n3. hashtag3",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
		{
			name:     "mixed format",
			input:    "1. #hashtag1\n2. hashtag2\n#hashtag3",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
		{
			name:     "empty lines",
			input:    "hashtag1\n\nhashtag2\n\n\nhashtag3",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
		{
			name:     "whitespace",
			input:    "  hashtag1  \n  hashtag2  \n  hashtag3  ",
			expected: []string{"hashtag1", "hashtag2", "hashtag3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseHashtags(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestClient_NetworkError(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	
	// Create client with invalid URL to simulate network error
	client := NewClient("test-key", "http://invalid-url-that-does-not-exist", logger)

	req := &GenerateContentRequest{
		Messages: []Message{{Role: "user", Content: "Test"}},
	}

	resp, err := client.GenerateContent(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, strings.ToLower(err.Error()), "failed to make request")
}

func TestClient_InvalidJSON(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("invalid json response"))
	}))
	defer server.Close()

	client := NewClient("test-key", server.URL, logger)

	req := &GenerateContentRequest{
		Messages: []Message{{Role: "user", Content: "Test"}},
	}

	resp, err := client.GenerateContent(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "failed to unmarshal response")
}

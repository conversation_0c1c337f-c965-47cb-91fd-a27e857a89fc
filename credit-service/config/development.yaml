# Credit Service Development Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50053
  http_port: 8083
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration - SQLite for development
database:
  type: "sqlite"
  sqlite_path: "./data/credit_service_dev.db"
  
  # PostgreSQL fallback configuration
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "credit_service_dev"
  ssl_mode: "disable"
  
  # Connection pool settings
  max_open_conns: 10
  max_idle_conns: 2
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Payment configuration
payment:
  stripe:
    secret_key: "sk_test_..."
    publishable_key: "pk_test_..."
    webhook_secret: "whsec_..."

# External services configuration
services:
  user_service: "localhost:50051"

# Metrics configuration
metrics:
  port: 9093

# Logging configuration
logging:
  level: "debug"
  format: "text"
  output: "stdout"

# JWT configuration
jwt:
  secret_key: "dev-secret-key"
  token_duration: "24h"
  issuer: "credit-service-dev"

# Kafka configuration
kafka:
  brokers:
    - "localhost:9092"
  topics:
    credit_events: "credit.events.dev"
    billing_events: "billing.events.dev"

syntax = "proto3";

package user.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";
import "user/v1/user.proto";

option go_package = "github.com/social-content-ai/proto-shared/user/v1;userv1";

// Session represents a user session
message Session {
  string id = 1;
  string user_id = 2;
  string device_info = 3;
  string status = 4;         // active, expired, revoked
  string ip_address = 5;
  google.protobuf.Timestamp login_time = 6;
  google.protobuf.Timestamp logout_time = 7;
  google.protobuf.Timestamp expires_at = 8;
}

// CreateSessionRequest for creating a new session
message CreateSessionRequest {
  string user_id = 1;
  string device_info = 2;
  string ip_address = 3;
  int64 duration_seconds = 4; // Session duration in seconds
}

// GetSessionRequest for retrieving a session
message GetSessionRequest {
  string id = 1;
}

// ListSessionsRequest for listing user sessions
message ListSessionsRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string status = 3;         // Filter by status
}

// ListSessionsResponse with paginated sessions
message ListSessionsResponse {
  repeated Session sessions = 1;
  common.v1.PaginationResponse pagination = 2;
}

// RevokeSessionRequest for revoking a session
message RevokeSessionRequest {
  string id = 1;
  string reason = 2;         // Reason for revocation
}

// RevokeAllSessionsRequest for revoking all user sessions
message RevokeAllSessionsRequest {
  string user_id = 1;
  string except_session_id = 2; // Keep this session active
}

// SessionService provides session management operations
service SessionService {
  rpc CreateSession(CreateSessionRequest) returns (Session);
  rpc GetSession(GetSessionRequest) returns (Session);
  rpc ListSessions(ListSessionsRequest) returns (ListSessionsResponse);
  rpc RevokeSession(RevokeSessionRequest) returns (Empty);
  rpc RevokeAllSessions(RevokeAllSessionsRequest) returns (Empty);
}

package events

import (
	"time"

	"github.com/google/uuid"
)

// Event types for integration service
const (
	EventTypePlatformConnected    = "platform.connected"
	EventTypePlatformDisconnected = "platform.disconnected"
	EventTypePostPublished        = "post.published"
	EventTypeTokenRefreshed       = "token.refreshed"
	EventTypePublishFailed        = "publish.failed"
	EventTypeAnalyticsUpdated     = "analytics.updated"
)

// BaseEvent contains common fields for all events
type BaseEvent struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`
	Source    string    `json:"source"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
}

// PlatformConnectedEvent is published when a platform is connected
type PlatformConnectedEvent struct {
	BaseEvent
	UserID        string            `json:"user_id"`
	WorkspaceID   string            `json:"workspace_id,omitempty"`
	Platform      string            `json:"platform"`
	AccountID     string            `json:"account_id"`
	Username      string            `json:"username,omitempty"`
	DisplayName   string            `json:"display_name,omitempty"`
	AvatarURL     string            `json:"avatar_url,omitempty"`
	Permissions   []string          `json:"permissions"`
	Metadata      map[string]string `json:"metadata,omitempty"`
	ConnectedAt   time.Time         `json:"connected_at"`
}

// PlatformDisconnectedEvent is published when a platform is disconnected
type PlatformDisconnectedEvent struct {
	BaseEvent
	UserID        string    `json:"user_id"`
	WorkspaceID   string    `json:"workspace_id,omitempty"`
	Platform      string    `json:"platform"`
	AccountID     string    `json:"account_id"`
	Reason        string    `json:"reason,omitempty"`
	DisconnectedAt time.Time `json:"disconnected_at"`
}

// PostPublishedEvent is published when a post is successfully published
type PostPublishedEvent struct {
	BaseEvent
	UserID           string            `json:"user_id"`
	WorkspaceID      string            `json:"workspace_id,omitempty"`
	PostID           string            `json:"post_id"`
	Platform         string            `json:"platform"`
	PlatformPostID   string            `json:"platform_post_id"`
	PlatformURL      string            `json:"platform_url,omitempty"`
	Content          string            `json:"content"`
	MediaURLs        []string          `json:"media_urls,omitempty"`
	Hashtags         []string          `json:"hashtags,omitempty"`
	PublishOptions   map[string]string `json:"publish_options,omitempty"`
	PublishedAt      time.Time         `json:"published_at"`
	ScheduledAt      *time.Time        `json:"scheduled_at,omitempty"`
}

// TokenRefreshedEvent is published when an access token is refreshed
type TokenRefreshedEvent struct {
	BaseEvent
	UserID      string    `json:"user_id"`
	Platform    string    `json:"platform"`
	AccountID   string    `json:"account_id"`
	ExpiresAt   time.Time `json:"expires_at"`
	RefreshedAt time.Time `json:"refreshed_at"`
}

// PublishFailedEvent is published when post publishing fails
type PublishFailedEvent struct {
	BaseEvent
	UserID         string            `json:"user_id"`
	WorkspaceID    string            `json:"workspace_id,omitempty"`
	PostID         string            `json:"post_id"`
	Platform       string            `json:"platform"`
	Content        string            `json:"content"`
	ErrorMessage   string            `json:"error_message"`
	ErrorCode      string            `json:"error_code,omitempty"`
	RetryCount     int               `json:"retry_count"`
	PublishOptions map[string]string `json:"publish_options,omitempty"`
	FailedAt       time.Time         `json:"failed_at"`
}

// AnalyticsUpdatedEvent is published when post analytics are updated
type AnalyticsUpdatedEvent struct {
	BaseEvent
	UserID           string            `json:"user_id"`
	Platform         string            `json:"platform"`
	PlatformPostID   string            `json:"platform_post_id"`
	PostID           string            `json:"post_id,omitempty"`
	Metrics          map[string]int64  `json:"metrics"`
	PreviousMetrics  map[string]int64  `json:"previous_metrics,omitempty"`
	CollectedAt      time.Time         `json:"collected_at"`
	PostPublishedAt  time.Time         `json:"post_published_at"`
}

// NewBaseEvent creates a new base event
func NewBaseEvent(eventType string) BaseEvent {
	return BaseEvent{
		ID:        uuid.New().String(),
		Type:      eventType,
		Source:    "integration-service",
		Timestamp: time.Now(),
		Version:   "1.0",
	}
}

// NewPlatformConnectedEvent creates a new platform connected event
func NewPlatformConnectedEvent(userID, workspaceID, platform, accountID, username, displayName, avatarURL string, permissions []string, metadata map[string]string) *PlatformConnectedEvent {
	return &PlatformConnectedEvent{
		BaseEvent:     NewBaseEvent(EventTypePlatformConnected),
		UserID:        userID,
		WorkspaceID:   workspaceID,
		Platform:      platform,
		AccountID:     accountID,
		Username:      username,
		DisplayName:   displayName,
		AvatarURL:     avatarURL,
		Permissions:   permissions,
		Metadata:      metadata,
		ConnectedAt:   time.Now(),
	}
}

// NewPlatformDisconnectedEvent creates a new platform disconnected event
func NewPlatformDisconnectedEvent(userID, workspaceID, platform, accountID, reason string) *PlatformDisconnectedEvent {
	return &PlatformDisconnectedEvent{
		BaseEvent:      NewBaseEvent(EventTypePlatformDisconnected),
		UserID:         userID,
		WorkspaceID:    workspaceID,
		Platform:       platform,
		AccountID:      accountID,
		Reason:         reason,
		DisconnectedAt: time.Now(),
	}
}

// NewPostPublishedEvent creates a new post published event
func NewPostPublishedEvent(userID, workspaceID, postID, platform, platformPostID, platformURL, content string, mediaURLs, hashtags []string, publishOptions map[string]string, scheduledAt *time.Time) *PostPublishedEvent {
	return &PostPublishedEvent{
		BaseEvent:        NewBaseEvent(EventTypePostPublished),
		UserID:           userID,
		WorkspaceID:      workspaceID,
		PostID:           postID,
		Platform:         platform,
		PlatformPostID:   platformPostID,
		PlatformURL:      platformURL,
		Content:          content,
		MediaURLs:        mediaURLs,
		Hashtags:         hashtags,
		PublishOptions:   publishOptions,
		PublishedAt:      time.Now(),
		ScheduledAt:      scheduledAt,
	}
}

// NewTokenRefreshedEvent creates a new token refreshed event
func NewTokenRefreshedEvent(userID, platform, accountID string, expiresAt time.Time) *TokenRefreshedEvent {
	return &TokenRefreshedEvent{
		BaseEvent:   NewBaseEvent(EventTypeTokenRefreshed),
		UserID:      userID,
		Platform:    platform,
		AccountID:   accountID,
		ExpiresAt:   expiresAt,
		RefreshedAt: time.Now(),
	}
}

// NewPublishFailedEvent creates a new publish failed event
func NewPublishFailedEvent(userID, workspaceID, postID, platform, content, errorMessage, errorCode string, retryCount int, publishOptions map[string]string) *PublishFailedEvent {
	return &PublishFailedEvent{
		BaseEvent:      NewBaseEvent(EventTypePublishFailed),
		UserID:         userID,
		WorkspaceID:    workspaceID,
		PostID:         postID,
		Platform:       platform,
		Content:        content,
		ErrorMessage:   errorMessage,
		ErrorCode:      errorCode,
		RetryCount:     retryCount,
		PublishOptions: publishOptions,
		FailedAt:       time.Now(),
	}
}

// NewAnalyticsUpdatedEvent creates a new analytics updated event
func NewAnalyticsUpdatedEvent(userID, platform, platformPostID, postID string, metrics, previousMetrics map[string]int64, postPublishedAt time.Time) *AnalyticsUpdatedEvent {
	return &AnalyticsUpdatedEvent{
		BaseEvent:        NewBaseEvent(EventTypeAnalyticsUpdated),
		UserID:           userID,
		Platform:         platform,
		PlatformPostID:   platformPostID,
		PostID:           postID,
		Metrics:          metrics,
		PreviousMetrics:  previousMetrics,
		CollectedAt:      time.Now(),
		PostPublishedAt:  postPublishedAt,
	}
}

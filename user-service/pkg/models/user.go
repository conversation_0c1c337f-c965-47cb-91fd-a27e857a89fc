package models

import (
	"mime/multipart"
	"time"
)

// UserResponse represents user response
type UserResponse struct {
	BaseModel
	Email         string                 `json:"email"`
	FirstName     string                 `json:"first_name"`
	LastName      string                 `json:"last_name"`
	Role          string                 `json:"role"`
	Status        string                 `json:"status"`
	EmailVerified bool                   `json:"email_verified"`
	AvatarURL     string                 `json:"avatar_url,omitempty"`
	LastLoginAt   *time.Time             `json:"last_login_at,omitempty"`
	Preferences   map[string]interface{} `json:"preferences,omitempty"`
	Profile       *UserProfile           `json:"profile,omitempty"`
}

// UserProfile represents user profile information
type UserProfile struct {
	Bio         string `json:"bio,omitempty"`
	Website     string `json:"website,omitempty"`
	Location    string `json:"location,omitempty"`
	Company     string `json:"company,omitempty"`
	JobTitle    string `json:"job_title,omitempty"`
	Phone       string `json:"phone,omitempty"`
	DateOfBirth string `json:"date_of_birth,omitempty"`
	Gender      string `json:"gender,omitempty"`
	Timezone    string `json:"timezone,omitempty"`
	Language    string `json:"language,omitempty"`
}

// UpdateUserRequest represents update user request
type UpdateUserRequest struct {
	ID        uint64                 `json:"id"`
	FirstName string                 `json:"first_name,omitempty"`
	LastName  string                 `json:"last_name,omitempty"`
	Role      string                 `json:"role,omitempty"`
	Status    string                 `json:"status,omitempty"`
	Profile   *UserProfile           `json:"profile,omitempty"`
	Preferences map[string]interface{} `json:"preferences,omitempty"`
}

// ListUsersRequest represents list users request
type ListUsersRequest struct {
	Page   int    `json:"page"`
	Limit  int    `json:"limit"`
	Search string `json:"search,omitempty"`
	Status string `json:"status,omitempty"`
	Role   string `json:"role,omitempty"`
	SortBy string `json:"sort_by,omitempty"`
	Order  string `json:"order,omitempty"`
}

// ListUsersResponse represents list users response
type ListUsersResponse struct {
	Users      []UserResponse `json:"users"`
	Pagination PaginationMeta `json:"pagination"`
}

// LockUserRequest represents lock user request
type LockUserRequest struct {
	UserID uint64 `json:"user_id"`
	Reason string `json:"reason" validate:"required"`
}

// UploadAvatarRequest represents upload avatar request
type UploadAvatarRequest struct {
	UserID   uint64                `json:"user_id"`
	File     multipart.File        `json:"-"`
	Filename string                `json:"filename"`
	Size     int64                 `json:"size"`
}

// UploadAvatarResponse represents upload avatar response
type UploadAvatarResponse struct {
	AvatarURL string `json:"avatar_url"`
	Message   string `json:"message"`
}

// CreateUserRequest represents create user request (admin)
type CreateUserRequest struct {
	Email     string                 `json:"email" validate:"required,email"`
	Password  string                 `json:"password" validate:"required,min=8"`
	FirstName string                 `json:"first_name" validate:"required"`
	LastName  string                 `json:"last_name" validate:"required"`
	Role      string                 `json:"role" validate:"required"`
	Status    string                 `json:"status,omitempty"`
	Profile   *UserProfile           `json:"profile,omitempty"`
	Preferences map[string]interface{} `json:"preferences,omitempty"`
}

// BulkCreateUsersRequest represents bulk create users request
type BulkCreateUsersRequest struct {
	Users []CreateUserRequest `json:"users" validate:"required,min=1,max=100"`
}

// BulkCreateUsersResponse represents bulk create users response
type BulkCreateUsersResponse struct {
	Created []UserResponse `json:"created"`
	Failed  []BulkError    `json:"failed,omitempty"`
	Total   int            `json:"total"`
	Success int            `json:"success"`
	Errors  int            `json:"errors"`
}

// BulkUpdateUsersRequest represents bulk update users request
type BulkUpdateUsersRequest struct {
	UserIDs []uint64                 `json:"user_ids" validate:"required,min=1,max=100"`
	Updates map[string]interface{}   `json:"updates" validate:"required"`
}

// BulkUpdateUsersResponse represents bulk update users response
type BulkUpdateUsersResponse struct {
	Updated []UserResponse `json:"updated"`
	Failed  []BulkError    `json:"failed,omitempty"`
	Total   int            `json:"total"`
	Success int            `json:"success"`
	Errors  int            `json:"errors"`
}

// BulkDeleteUsersRequest represents bulk delete users request
type BulkDeleteUsersRequest struct {
	UserIDs []uint64 `json:"user_ids" validate:"required,min=1,max=100"`
}

// BulkDeleteUsersResponse represents bulk delete users response
type BulkDeleteUsersResponse struct {
	Deleted []uint64    `json:"deleted"`
	Failed  []BulkError `json:"failed,omitempty"`
	Total   int         `json:"total"`
	Success int         `json:"success"`
	Errors  int         `json:"errors"`
}

// BulkError represents an error in bulk operations
type BulkError struct {
	ID     uint64 `json:"id,omitempty"`
	Email  string `json:"email,omitempty"`
	Error  string `json:"error"`
	Reason string `json:"reason"`
}

// UserAnalyticsRequest represents user analytics request
type UserAnalyticsRequest struct {
	Period string `json:"period"`
}

// UserAnalyticsResponse represents user analytics response
type UserAnalyticsResponse struct {
	Period          string           `json:"period"`
	TotalUsers      int64            `json:"total_users"`
	ActiveUsers     int64            `json:"active_users"`
	NewUsers        int64            `json:"new_users"`
	VerifiedUsers   int64            `json:"verified_users"`
	LockedUsers     int64            `json:"locked_users"`
	DailyStats      []DailyUserStats `json:"daily_stats"`
	RoleDistribution []RoleStats     `json:"role_distribution"`
	StatusDistribution []StatusStats `json:"status_distribution"`
}

// DailyUserStats represents daily user statistics
type DailyUserStats struct {
	Date        string `json:"date"`
	NewUsers    int64  `json:"new_users"`
	ActiveUsers int64  `json:"active_users"`
}

// RoleStats represents role-based statistics
type RoleStats struct {
	Role  string `json:"role"`
	Count int64  `json:"count"`
}

// StatusStats represents status-based statistics
type StatusStats struct {
	Status string `json:"status"`
	Count  int64  `json:"count"`
}

// UpdatePreferencesRequest represents update preferences request
type UpdatePreferencesRequest struct {
	Preferences map[string]interface{} `json:"preferences" validate:"required"`
}

// GetActivityRequest represents get activity request
type GetActivityRequest struct {
	Page  int    `json:"page"`
	Limit int    `json:"limit"`
	Type  string `json:"type,omitempty"`
}

// GetActivityResponse represents get activity response
type GetActivityResponse struct {
	Activities []UserActivity `json:"activities"`
	Pagination PaginationMeta `json:"pagination"`
}

// UserActivity represents user activity
type UserActivity struct {
	ID          uint64                 `json:"id"`
	UserID      uint64                 `json:"user_id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	IPAddress   string                 `json:"ip_address,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}

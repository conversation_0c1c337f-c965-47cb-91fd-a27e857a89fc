// Code generated by ent, DO NOT EDIT.

package notificationpreference

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldUserID, v))
}

// EmailEnabled applies equality check predicate on the "email_enabled" field. It's identical to EmailEnabledEQ.
func EmailEnabled(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldEmailEnabled, v))
}

// PushEnabled applies equality check predicate on the "push_enabled" field. It's identical to PushEnabledEQ.
func PushEnabled(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldPushEnabled, v))
}

// InAppEnabled applies equality check predicate on the "in_app_enabled" field. It's identical to InAppEnabledEQ.
func InAppEnabled(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldInAppEnabled, v))
}

// SmsEnabled applies equality check predicate on the "sms_enabled" field. It's identical to SmsEnabledEQ.
func SmsEnabled(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldSmsEnabled, v))
}

// Timezone applies equality check predicate on the "timezone" field. It's identical to TimezoneEQ.
func Timezone(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldTimezone, v))
}

// Language applies equality check predicate on the "language" field. It's identical to LanguageEQ.
func Language(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldLanguage, v))
}

// MarketingEmails applies equality check predicate on the "marketing_emails" field. It's identical to MarketingEmailsEQ.
func MarketingEmails(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldMarketingEmails, v))
}

// ProductUpdates applies equality check predicate on the "product_updates" field. It's identical to ProductUpdatesEQ.
func ProductUpdates(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldProductUpdates, v))
}

// SecurityAlerts applies equality check predicate on the "security_alerts" field. It's identical to SecurityAlertsEQ.
func SecurityAlerts(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldSecurityAlerts, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldUserID, v))
}

// EmailEnabledEQ applies the EQ predicate on the "email_enabled" field.
func EmailEnabledEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldEmailEnabled, v))
}

// EmailEnabledNEQ applies the NEQ predicate on the "email_enabled" field.
func EmailEnabledNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldEmailEnabled, v))
}

// PushEnabledEQ applies the EQ predicate on the "push_enabled" field.
func PushEnabledEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldPushEnabled, v))
}

// PushEnabledNEQ applies the NEQ predicate on the "push_enabled" field.
func PushEnabledNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldPushEnabled, v))
}

// InAppEnabledEQ applies the EQ predicate on the "in_app_enabled" field.
func InAppEnabledEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldInAppEnabled, v))
}

// InAppEnabledNEQ applies the NEQ predicate on the "in_app_enabled" field.
func InAppEnabledNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldInAppEnabled, v))
}

// SmsEnabledEQ applies the EQ predicate on the "sms_enabled" field.
func SmsEnabledEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldSmsEnabled, v))
}

// SmsEnabledNEQ applies the NEQ predicate on the "sms_enabled" field.
func SmsEnabledNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldSmsEnabled, v))
}

// NotificationTypesIsNil applies the IsNil predicate on the "notification_types" field.
func NotificationTypesIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldNotificationTypes))
}

// NotificationTypesNotNil applies the NotNil predicate on the "notification_types" field.
func NotificationTypesNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldNotificationTypes))
}

// QuietHoursIsNil applies the IsNil predicate on the "quiet_hours" field.
func QuietHoursIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldQuietHours))
}

// QuietHoursNotNil applies the NotNil predicate on the "quiet_hours" field.
func QuietHoursNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldQuietHours))
}

// TimezoneEQ applies the EQ predicate on the "timezone" field.
func TimezoneEQ(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldTimezone, v))
}

// TimezoneNEQ applies the NEQ predicate on the "timezone" field.
func TimezoneNEQ(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldTimezone, v))
}

// TimezoneIn applies the In predicate on the "timezone" field.
func TimezoneIn(vs ...string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldTimezone, vs...))
}

// TimezoneNotIn applies the NotIn predicate on the "timezone" field.
func TimezoneNotIn(vs ...string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldTimezone, vs...))
}

// TimezoneGT applies the GT predicate on the "timezone" field.
func TimezoneGT(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldTimezone, v))
}

// TimezoneGTE applies the GTE predicate on the "timezone" field.
func TimezoneGTE(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldTimezone, v))
}

// TimezoneLT applies the LT predicate on the "timezone" field.
func TimezoneLT(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldTimezone, v))
}

// TimezoneLTE applies the LTE predicate on the "timezone" field.
func TimezoneLTE(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldTimezone, v))
}

// TimezoneContains applies the Contains predicate on the "timezone" field.
func TimezoneContains(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldContains(FieldTimezone, v))
}

// TimezoneHasPrefix applies the HasPrefix predicate on the "timezone" field.
func TimezoneHasPrefix(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldHasPrefix(FieldTimezone, v))
}

// TimezoneHasSuffix applies the HasSuffix predicate on the "timezone" field.
func TimezoneHasSuffix(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldHasSuffix(FieldTimezone, v))
}

// TimezoneEqualFold applies the EqualFold predicate on the "timezone" field.
func TimezoneEqualFold(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEqualFold(FieldTimezone, v))
}

// TimezoneContainsFold applies the ContainsFold predicate on the "timezone" field.
func TimezoneContainsFold(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldContainsFold(FieldTimezone, v))
}

// LanguageEQ applies the EQ predicate on the "language" field.
func LanguageEQ(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldLanguage, v))
}

// LanguageNEQ applies the NEQ predicate on the "language" field.
func LanguageNEQ(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldLanguage, v))
}

// LanguageIn applies the In predicate on the "language" field.
func LanguageIn(vs ...string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldLanguage, vs...))
}

// LanguageNotIn applies the NotIn predicate on the "language" field.
func LanguageNotIn(vs ...string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldLanguage, vs...))
}

// LanguageGT applies the GT predicate on the "language" field.
func LanguageGT(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldLanguage, v))
}

// LanguageGTE applies the GTE predicate on the "language" field.
func LanguageGTE(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldLanguage, v))
}

// LanguageLT applies the LT predicate on the "language" field.
func LanguageLT(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldLanguage, v))
}

// LanguageLTE applies the LTE predicate on the "language" field.
func LanguageLTE(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldLanguage, v))
}

// LanguageContains applies the Contains predicate on the "language" field.
func LanguageContains(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldContains(FieldLanguage, v))
}

// LanguageHasPrefix applies the HasPrefix predicate on the "language" field.
func LanguageHasPrefix(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldHasPrefix(FieldLanguage, v))
}

// LanguageHasSuffix applies the HasSuffix predicate on the "language" field.
func LanguageHasSuffix(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldHasSuffix(FieldLanguage, v))
}

// LanguageEqualFold applies the EqualFold predicate on the "language" field.
func LanguageEqualFold(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEqualFold(FieldLanguage, v))
}

// LanguageContainsFold applies the ContainsFold predicate on the "language" field.
func LanguageContainsFold(v string) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldContainsFold(FieldLanguage, v))
}

// EmailSettingsIsNil applies the IsNil predicate on the "email_settings" field.
func EmailSettingsIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldEmailSettings))
}

// EmailSettingsNotNil applies the NotNil predicate on the "email_settings" field.
func EmailSettingsNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldEmailSettings))
}

// PushSettingsIsNil applies the IsNil predicate on the "push_settings" field.
func PushSettingsIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldPushSettings))
}

// PushSettingsNotNil applies the NotNil predicate on the "push_settings" field.
func PushSettingsNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldPushSettings))
}

// FrequencySettingsIsNil applies the IsNil predicate on the "frequency_settings" field.
func FrequencySettingsIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldFrequencySettings))
}

// FrequencySettingsNotNil applies the NotNil predicate on the "frequency_settings" field.
func FrequencySettingsNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldFrequencySettings))
}

// MarketingEmailsEQ applies the EQ predicate on the "marketing_emails" field.
func MarketingEmailsEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldMarketingEmails, v))
}

// MarketingEmailsNEQ applies the NEQ predicate on the "marketing_emails" field.
func MarketingEmailsNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldMarketingEmails, v))
}

// ProductUpdatesEQ applies the EQ predicate on the "product_updates" field.
func ProductUpdatesEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldProductUpdates, v))
}

// ProductUpdatesNEQ applies the NEQ predicate on the "product_updates" field.
func ProductUpdatesNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldProductUpdates, v))
}

// SecurityAlertsEQ applies the EQ predicate on the "security_alerts" field.
func SecurityAlertsEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldSecurityAlerts, v))
}

// SecurityAlertsNEQ applies the NEQ predicate on the "security_alerts" field.
func SecurityAlertsNEQ(v bool) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldSecurityAlerts, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.NotificationPreference) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.NotificationPreference) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.NotificationPreference) predicate.NotificationPreference {
	return predicate.NotificationPreference(sql.NotPredicates(p))
}

package config

import (
	"fmt"
	"time"

	"github.com/social-content-ai/user-service/pkg/cleanup"
)

// CleanupConfig holds cleanup service configuration
type CleanupConfig struct {
	Enabled             bool          `mapstructure:"enabled" json:"enabled"`
	UnverifiedUserTTL   time.Duration `mapstructure:"unverified_user_ttl" json:"unverified_user_ttl"`
	CleanupInterval     time.Duration `mapstructure:"cleanup_interval" json:"cleanup_interval"`
	BatchSize           int           `mapstructure:"batch_size" json:"batch_size"`
	EnableNotifications bool          `mapstructure:"enable_notifications" json:"enable_notifications"`
	WarningTime         time.Duration `mapstructure:"warning_time" json:"warning_time"`
	AutoStart           bool          `mapstructure:"auto_start" json:"auto_start"`
}

// DefaultCleanupConfig returns default cleanup configuration
func DefaultCleanupConfig() *CleanupConfig {
	return &CleanupConfig{
		Enabled:             true,
		UnverifiedUserTTL:   24 * time.Hour,
		CleanupInterval:     1 * time.Hour,
		BatchSize:           100,
		EnableNotifications: true,
		WarningTime:         2 * time.Hour,
		AutoStart:           true,
	}
}

// ToCleanupServiceConfig converts to cleanup service config
func (c *CleanupConfig) ToCleanupServiceConfig() *cleanup.Config {
	return &cleanup.Config{
		UnverifiedUserTTL:   c.UnverifiedUserTTL,
		CleanupInterval:     c.CleanupInterval,
		BatchSize:           c.BatchSize,
		EnableNotifications: c.EnableNotifications,
		WarningTime:         c.WarningTime,
	}
}

// Validate validates the cleanup configuration
func (c *CleanupConfig) Validate() error {
	if c.UnverifiedUserTTL <= 0 {
		return fmt.Errorf("unverified_user_ttl must be positive")
	}

	if c.CleanupInterval <= 0 {
		return fmt.Errorf("cleanup_interval must be positive")
	}

	if c.BatchSize <= 0 {
		return fmt.Errorf("batch_size must be positive")
	}

	if c.WarningTime <= 0 {
		return fmt.Errorf("warning_time must be positive")
	}

	if c.WarningTime >= c.UnverifiedUserTTL {
		return fmt.Errorf("warning_time must be less than unverified_user_ttl")
	}

	return nil
}

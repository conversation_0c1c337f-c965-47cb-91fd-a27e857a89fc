package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/credit-service/usecase/payment"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PaymentWebhookHandler handles payment webhook requests from various providers
type PaymentWebhookHandler struct {
	subscriptionUC   subscription.UseCase
	processorManager *payment.ProcessorManager
	logger           logging.Logger
}

// WebhookResponse represents a simple webhook response
type WebhookResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	PaymentID    string `json:"payment_id,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// NewPaymentWebhookHandler creates a new payment webhook handler
func NewPaymentWebhookHandler(subscriptionUC subscription.UseCase, processorManager *payment.ProcessorManager, logger logging.Logger) *PaymentWebhookHandler {
	return &PaymentWebhookHandler{
		subscriptionUC:   subscriptionUC,
		processorManager: processorManager,
		logger:           logger,
	}
}

// HandlePaymentWebhook godoc
// @Summary Handle payment webhook
// @Description Handle payment webhook events from various providers
// @Tags webhooks
// @Accept json
// @Produce json
// @Param provider path string true "Payment provider (paypal, stripe, momo)"
// @Param payload body interface{} true "Webhook payload"
// @Success 200 {object} interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /webhooks/{provider} [post]
func (h *PaymentWebhookHandler) HandlePaymentWebhook(c *gin.Context) {
	provider := c.Param("provider")
	h.logger.WithField("provider", provider).Info("Received payment webhook")

	// Read raw body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook body")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
		})
		return
	}

	// Get appropriate processor
	var processor payment.Processor
	switch provider {
	case "paypal":
		processor, err = h.processorManager.GetProcessor(payment.ProcessorTypePayPal)
	case "stripe":
		processor, err = h.processorManager.GetProcessor(payment.ProcessorTypeStripe)
	case "momo":
		processor, err = h.processorManager.GetProcessor(payment.ProcessorTypeMomo)
	default:
		h.logger.WithField("provider", provider).Error("Unsupported payment provider")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "UNSUPPORTED_PROVIDER",
			Message: fmt.Sprintf("Payment provider '%s' is not supported", provider),
		})
		return
	}

	if err != nil {
		h.logger.WithError(err).WithField("provider", provider).Error("Failed to get payment processor")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "PROCESSOR_ERROR",
			Message: "Failed to initialize payment processor",
		})
		return
	}

	// Validate webhook
	signature := c.GetHeader("X-PayPal-Transmission-Signature") // PayPal specific, adjust for other providers
	if signature == "" {
		signature = c.GetHeader("Stripe-Signature") // Stripe specific
	}

	isValid, err := processor.ValidateWebhook(c.Request.Context(), body, signature)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate webhook")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "VALIDATION_FAILED",
			Message: "Webhook validation failed",
		})
		return
	}

	if !isValid {
		h.logger.Error("Invalid webhook signature")
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "INVALID_SIGNATURE",
			Message: "Webhook signature is invalid",
		})
		return
	}

	// Process webhook
	webhookEvent, err := processor.ProcessWebhook(c.Request.Context(), body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process webhook")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "PROCESSING_FAILED",
			Message: "Failed to process webhook",
		})
		return
	}

	// Handle webhook event based on type
	err = h.handleWebhookEvent(c.Request.Context(), provider, webhookEvent)
	if err != nil {
		h.logger.WithError(err).WithField("event_type", webhookEvent.Type).Error("Failed to handle webhook event")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "HANDLER_ERROR",
			Message: "Failed to handle webhook event",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "Webhook processed successfully",
		"event_id": webhookEvent.ID,
	})
}

// handleWebhookEvent handles webhook events from different providers
func (h *PaymentWebhookHandler) handleWebhookEvent(ctx context.Context, provider string, event *payment.WebhookEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"provider":   provider,
		"event_id":   event.ID,
		"event_type": event.Type,
	}).Info("Handling webhook event")

	switch provider {
	case "paypal":
		return h.handlePayPalEvent(ctx, event)
	case "stripe":
		return h.handleStripeEvent(ctx, event)
	case "momo":
		return h.handleMomoEvent(ctx, event)
	default:
		return fmt.Errorf("unsupported provider: %s", provider)
	}
}

// handlePayPalEvent handles PayPal webhook events
func (h *PaymentWebhookHandler) handlePayPalEvent(ctx context.Context, event *payment.WebhookEvent) error {
	switch event.Type {
	case "CHECKOUT.ORDER.COMPLETED", "PAYMENT.CAPTURE.COMPLETED":
		return h.handlePaymentSuccess(ctx, event)
	case "CHECKOUT.ORDER.CANCELLED", "PAYMENT.CAPTURE.DENIED":
		return h.handlePaymentFailure(ctx, event)
	default:
		h.logger.WithField("event_type", event.Type).Info("Unhandled PayPal event type")
		return nil
	}
}

// handleStripeEvent handles Stripe webhook events
func (h *PaymentWebhookHandler) handleStripeEvent(ctx context.Context, event *payment.WebhookEvent) error {
	h.logger.WithField("event_type", event.Type).Info("Stripe event handling not implemented - only PayPal is supported")
	return fmt.Errorf("stripe webhook handling not implemented")
}

// handleMomoEvent handles MoMo webhook events
func (h *PaymentWebhookHandler) handleMomoEvent(ctx context.Context, event *payment.WebhookEvent) error {
	h.logger.WithField("event_type", event.Type).Info("MoMo event handling not implemented - only PayPal is supported")
	return fmt.Errorf("momo webhook handling not implemented")
}

// handlePaymentSuccess handles successful payment events
func (h *PaymentWebhookHandler) handlePaymentSuccess(ctx context.Context, event *payment.WebhookEvent) error {
	// Extract payment ID and payer ID from event data
	paymentID, ok := event.Data["id"].(string)
	if !ok {
		return fmt.Errorf("missing payment ID in event data")
	}

	var payerID string
	if payer, ok := event.Data["payer"].(map[string]interface{}); ok {
		if id, ok := payer["payer_id"].(string); ok {
			payerID = id
		}
	}

	return h.subscriptionUC.HandlePaymentSuccess(ctx, paymentID, payerID)
}

// handlePaymentFailure handles failed payment events
func (h *PaymentWebhookHandler) handlePaymentFailure(ctx context.Context, event *payment.WebhookEvent) error {
	// Extract payment ID from event data
	paymentID, ok := event.Data["id"].(string)
	if !ok {
		return fmt.Errorf("missing payment ID in event data")
	}

	reason := "Payment failed"
	if statusDetails, ok := event.Data["status_details"].(map[string]interface{}); ok {
		if reasonCode, ok := statusDetails["reason"].(string); ok {
			reason = reasonCode
		}
	}

	return h.subscriptionUC.HandlePaymentFailure(ctx, paymentID, reason)
}

// HandlePayPalWebhook handles PayPal-specific webhook (legacy method)
func (h *PaymentWebhookHandler) HandlePayPalWebhook(c *gin.Context) {
	h.logger.Info("Received PayPal webhook")

	// Read raw body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook body")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
		})
		return
	}

	// Parse webhook request using PayPal API types
	var webhookReq paypal.WebhookEvent
	if err := json.Unmarshal(body, &webhookReq); err != nil {
		h.logger.WithError(err).Error("Failed to parse PayPal webhook")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Invalid JSON payload",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":   webhookReq.ID,
		"event_type": webhookReq.EventType,
		"summary":    webhookReq.Summary,
	}).Info("Processing PayPal webhook event")

	// Process webhook based on event type
	switch webhookReq.EventType {
	case "CHECKOUT.ORDER.APPROVED":
		h.handleOrderApproved(c, &webhookReq)
	case "CHECKOUT.ORDER.COMPLETED":
		h.handleOrderCompleted(c, &webhookReq)
	case "PAYMENT.CAPTURE.COMPLETED":
		h.handlePaymentCaptureCompleted(c, &webhookReq)
	case "PAYMENT.CAPTURE.DENIED":
		h.handlePaymentCaptureDenied(c, &webhookReq)
	case "CHECKOUT.ORDER.CANCELLED":
		h.handleOrderCancelled(c, &webhookReq)
	default:
		h.logger.WithField("event_type", webhookReq.EventType).Info("Unhandled PayPal webhook event type")
		c.JSON(http.StatusOK, WebhookResponse{
			Success: true,
			Message: "Event type not handled",
		})
	}
}

// handleOrderApproved handles order approved events
func (h *PaymentWebhookHandler) handleOrderApproved(c *gin.Context, webhook *paypal.WebhookEvent) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order approved event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, WebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// For order approved, we typically just log it
	// The actual processing happens on order completed
	h.logger.WithFields(map[string]interface{}{
		"order_id":   orderID,
		"event_type": webhook.EventType,
	}).Info("PayPal order approved")

	c.JSON(http.StatusOK, WebhookResponse{
		Success:   true,
		Message:   "Order approved event processed",
		PaymentID: orderID,
	})
}

// handleOrderCompleted handles order completed events
func (h *PaymentWebhookHandler) handleOrderCompleted(c *gin.Context, webhook *paypal.WebhookEvent) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order completed event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, WebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// Extract payer information
	var payerID string
	if payer, ok := webhook.Resource["payer"].(map[string]interface{}); ok {
		if id, ok := payer["payer_id"].(string); ok {
			payerID = id
		}
	}

	// Handle payment success
	err := h.subscriptionUC.HandlePaymentSuccess(c.Request.Context(), orderID, payerID)
	if err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to handle payment success")
		c.JSON(http.StatusInternalServerError, WebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment success",
			PaymentID:    orderID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"order_id": orderID,
		"payer_id": payerID,
	}).Info("Successfully processed PayPal order completed")

	c.JSON(http.StatusOK, WebhookResponse{
		Success:   true,
		Message:   "Order completed event processed",
		PaymentID: orderID,
	})
}

// handlePaymentCaptureCompleted handles payment capture completed events
func (h *PaymentWebhookHandler) handlePaymentCaptureCompleted(c *gin.Context, webhook *paypal.WebhookEvent) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling payment capture completed event")

	// Extract capture information from resource
	captureID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing capture ID in webhook resource")
		c.JSON(http.StatusBadRequest, WebhookResponse{
			Success:      false,
			ErrorMessage: "Missing capture ID",
		})
		return
	}

	// Extract custom ID (our order/subscription ID)
	var customID string
	if purchaseUnits, ok := webhook.Resource["purchase_units"].([]interface{}); ok && len(purchaseUnits) > 0 {
		if unit, ok := purchaseUnits[0].(map[string]interface{}); ok {
			if id, ok := unit["custom_id"].(string); ok {
				customID = id
			}
		}
	}

	// Handle payment success using custom ID as payment ID
	paymentID := customID
	if paymentID == "" {
		paymentID = captureID
	}

	err := h.subscriptionUC.HandlePaymentSuccess(c.Request.Context(), paymentID, "")
	if err != nil {
		h.logger.WithError(err).WithField("capture_id", captureID).Error("Failed to handle payment capture success")
		c.JSON(http.StatusInternalServerError, WebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment capture",
			PaymentID:    captureID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"capture_id": captureID,
		"payment_id": paymentID,
	}).Info("Successfully processed PayPal payment capture completed")

	c.JSON(http.StatusOK, WebhookResponse{
		Success:   true,
		Message:   "Payment capture completed event processed",
		PaymentID: captureID,
	})
}

// handlePaymentCaptureDenied handles payment capture denied events
func (h *PaymentWebhookHandler) handlePaymentCaptureDenied(c *gin.Context, webhook *paypal.WebhookEvent) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling payment capture denied event")

	// Extract capture information from resource
	captureID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing capture ID in webhook resource")
		c.JSON(http.StatusBadRequest, WebhookResponse{
			Success:      false,
			ErrorMessage: "Missing capture ID",
		})
		return
	}

	// Extract reason
	reason := "Payment capture denied"
	if statusDetails, ok := webhook.Resource["status_details"].(map[string]interface{}); ok {
		if reasonCode, ok := statusDetails["reason"].(string); ok {
			reason = reasonCode
		}
	}

	// Handle payment failure
	err := h.subscriptionUC.HandlePaymentFailure(c.Request.Context(), captureID, reason)
	if err != nil {
		h.logger.WithError(err).WithField("capture_id", captureID).Error("Failed to handle payment capture denial")
		c.JSON(http.StatusInternalServerError, WebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment denial",
			PaymentID:    captureID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"capture_id": captureID,
		"reason":     reason,
	}).Info("Successfully processed PayPal payment capture denied")

	c.JSON(http.StatusOK, WebhookResponse{
		Success:   true,
		Message:   "Payment capture denied event processed",
		PaymentID: captureID,
	})
}

// handleOrderCancelled handles order cancelled events
func (h *PaymentWebhookHandler) handleOrderCancelled(c *gin.Context, webhook *paypal.WebhookEvent) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order cancelled event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, WebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// Handle payment failure
	err := h.subscriptionUC.HandlePaymentFailure(c.Request.Context(), orderID, "Order cancelled by user")
	if err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to handle order cancellation")
		c.JSON(http.StatusInternalServerError, WebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process order cancellation",
			PaymentID:    orderID,
		})
		return
	}

	h.logger.WithField("order_id", orderID).Info("Successfully processed PayPal order cancelled")

	c.JSON(http.StatusOK, WebhookResponse{
		Success:   true,
		Message:   "Order cancelled event processed",
		PaymentID: orderID,
	})
}

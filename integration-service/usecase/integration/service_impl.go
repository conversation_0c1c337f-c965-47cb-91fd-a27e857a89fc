package integration

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/pkg/oauth"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service implements integration business logic
type Service struct {
	reader         Reader
	writer         Writer
	logger         logging.Logger
	oauthProviders map[string]oauth.Provider
}

// NewService creates a new integration service
func NewService(reader Reader, writer Writer, logger logging.Logger) UseCase {
	return &Service{
		reader:         reader,
		writer:         writer,
		logger:         logger,
		oauthProviders: make(map[string]oauth.Provider),
	}
}

// RegisterOAuthProvider registers an OAuth provider for a platform
func (s *Service) RegisterOAuthProvider(platform string, provider oauth.Provider) {
	s.oauthProviders[platform] = provider
	s.logger.WithField("platform", platform).Info("OAuth provider registered")
}

// GetOAuthURL generates OAuth authorization URL
func (s *Service) GetOAuthURL(ctx context.Context, req *GetOAuthURLRequest) (*GetOAuthURLResponse, error) {
	s.logger.WithField("platform", req.Platform).Info("Getting OAuth URL")

	// Get OAuth provider for platform
	provider, exists := s.oauthProviders[req.Platform]
	if !exists {
		return nil, fmt.Errorf("OAuth provider not found for platform: %s", req.Platform)
	}

	// Generate state if not provided
	state := req.State
	if state == "" {
		stateBytes := make([]byte, 16)
		if _, err := rand.Read(stateBytes); err != nil {
			return nil, fmt.Errorf("failed to generate state: %w", err)
		}
		state = hex.EncodeToString(stateBytes)
	}

	// Get authorization URL
	authURL := provider.GetAuthURL(state, req.RequestedPermissions)

	return &GetOAuthURLResponse{
		AuthURL:     authURL,
		State:       state,
		Permissions: req.RequestedPermissions,
	}, nil
}

// ConnectAccount connects a social media account
func (s *Service) ConnectAccount(ctx context.Context, req *ConnectAccountRequest) (*ConnectAccountResponse, error) {
	s.logger.WithField("platform", req.Platform).Info("Connecting account")

	// Get OAuth provider for platform
	provider, exists := s.oauthProviders[req.Platform]
	if !exists {
		return &ConnectAccountResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("OAuth provider not found for platform: %s", req.Platform),
		}, nil
	}

	// Exchange code for token
	tokenResponse, err := provider.ExchangeCodeForToken(ctx, req.OAuthCode)
	if err != nil {
		s.logger.WithError(err).Error("Failed to exchange code for token")
		return &ConnectAccountResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to exchange code for token: %v", err),
		}, nil
	}

	// Get user profile from platform
	profile, err := provider.GetUserProfile(ctx, tokenResponse.AccessToken)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get user profile")
		return &ConnectAccountResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to get user profile: %v", err),
		}, nil
	}

	// Check if integration already exists
	existingIntegration, err := s.reader.GetPlatformIntegrationByUserAndPlatform(ctx, req.UserID, req.Platform)
	if err == nil && existingIntegration != nil {
		// Update existing integration
		updates := map[string]interface{}{
			"platform_user_id":      profile.ID,
			"platform_username":     profile.Username,
			"platform_display_name": profile.DisplayName,
			"platform_avatar_url":   profile.AvatarURL,
			"access_token":          tokenResponse.AccessToken,
			"refresh_token":         tokenResponse.RefreshToken,
			"token_expires_at":      time.Unix(tokenResponse.ExpiresAt, 0),
			"scopes":                req.RequestedPermissions,
			"status":                "active",
			"error_message":         "",
			"last_sync_at":          time.Now(),
			"updated_at":            time.Now(),
		}

		updatedIntegration, err := s.writer.UpdatePlatformIntegration(ctx, existingIntegration.ID.String(), updates)
		if err != nil {
			s.logger.WithError(err).Error("Failed to update platform integration")
			return &ConnectAccountResponse{
				Success:      false,
				ErrorMessage: fmt.Sprintf("Failed to update platform integration: %v", err),
			}, nil
		}

		return &ConnectAccountResponse{
			AccountID: updatedIntegration.ID.String(),
			Account:   convertEntToPlatformAccount(updatedIntegration),
			Success:   true,
		}, nil
	}

	// Create new integration
	newIntegration := &ent.PlatformIntegration{
		ID:                  uuid.New(),
		UserID:              uuid.MustParse(req.UserID),
		WorkspaceID:         req.WorkspaceID,
		Platform:            platformintegration.Platform(req.Platform),
		PlatformUserID:      profile.ID,
		PlatformUsername:    profile.Username,
		PlatformDisplayName: profile.DisplayName,
		PlatformAvatarURL:   profile.AvatarURL,
		AccessToken:         tokenResponse.AccessToken,
		RefreshToken:        tokenResponse.RefreshToken,
		TokenExpiresAt:      time.Unix(tokenResponse.ExpiresAt, 0),
		Scopes:              req.RequestedPermissions,
		Status:              platformintegration.StatusActive,
		LastSyncAt:          time.Now(),
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	createdIntegration, err := s.writer.CreatePlatformIntegration(ctx, newIntegration)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create platform integration")
		return &ConnectAccountResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create platform integration: %v", err),
		}, nil
	}

	return &ConnectAccountResponse{
		AccountID: createdIntegration.ID.String(),
		Account:   convertEntToPlatformAccount(createdIntegration),
		Success:   true,
	}, nil
}

// RefreshToken refreshes access token for an account
func (s *Service) RefreshToken(ctx context.Context, req *RefreshTokenRequest) (*RefreshTokenResponse, error) {
	s.logger.WithField("account_id", req.AccountID).Info("Refreshing token")

	// Get integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return &RefreshTokenResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to get platform integration: %v", err),
		}, nil
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return &RefreshTokenResponse{
			Success:      false,
			ErrorMessage: "Access denied: integration does not belong to user",
		}, nil
	}

	// Get OAuth provider for platform
	provider, exists := s.oauthProviders[string(integration.Platform)]
	if !exists {
		return &RefreshTokenResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("OAuth provider not found for platform: %s", integration.Platform),
		}, nil
	}

	// Check if refresh is needed
	if !req.ForceRefresh && !integration.TokenExpiresAt.IsZero() && integration.TokenExpiresAt.After(time.Now().Add(5*time.Minute)) {
		return &RefreshTokenResponse{
			Success:      true,
			NewExpiresAt: integration.TokenExpiresAt.Unix(),
		}, nil
	}

	// Refresh token
	tokenResponse, err := provider.RefreshToken(ctx, integration.AccessToken)
	if err != nil {
		s.logger.WithError(err).Error("Failed to refresh token")

		// Update integration status to error
		updates := map[string]interface{}{
			"status":        "error",
			"error_message": fmt.Sprintf("Token refresh failed: %v", err),
			"updated_at":    time.Now(),
		}
		s.writer.UpdatePlatformIntegration(ctx, req.AccountID, updates)

		return &RefreshTokenResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to refresh token: %v", err),
		}, nil
	}

	// Update integration with new token
	updates := map[string]interface{}{
		"access_token":     tokenResponse.AccessToken,
		"refresh_token":    tokenResponse.RefreshToken,
		"token_expires_at": time.Unix(tokenResponse.ExpiresAt, 0),
		"status":           "active",
		"error_message":    "",
		"updated_at":       time.Now(),
	}

	_, err = s.writer.UpdatePlatformIntegration(ctx, req.AccountID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update platform integration")
		return &RefreshTokenResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to update platform integration: %v", err),
		}, nil
	}

	return &RefreshTokenResponse{
		Success:      true,
		NewExpiresAt: tokenResponse.ExpiresAt,
	}, nil
}

// DisconnectAccount disconnects a social media account
func (s *Service) DisconnectAccount(ctx context.Context, req *DisconnectAccountRequest) error {
	s.logger.WithField("account_id", req.AccountID).Info("Disconnecting account")

	// Get integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return fmt.Errorf("failed to get platform integration: %w", err)
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return fmt.Errorf("access denied: integration does not belong to user")
	}

	// Revoke token on platform if requested
	if req.RevokeToken {
		provider, exists := s.oauthProviders[string(integration.Platform)]
		if exists {
			if err := provider.RevokeToken(ctx, integration.AccessToken); err != nil {
				s.logger.WithError(err).Warn("Failed to revoke token on platform")
			}
		}
	}

	// Delete integration
	err = s.writer.DeletePlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to delete platform integration")
		return fmt.Errorf("failed to delete platform integration: %w", err)
	}

	return nil
}

// ListAccounts lists connected accounts for a user
func (s *Service) ListAccounts(ctx context.Context, req *ListAccountsRequest) (*ListAccountsResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing accounts")

	// Get integrations
	integrations, totalCount, err := s.reader.ListPlatformIntegrations(ctx, req.UserID, req.WorkspaceID, req.Platform, req.Status, req.Limit, req.Offset)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list platform integrations")
		return nil, fmt.Errorf("failed to list platform integrations: %w", err)
	}

	// Convert to response format
	accounts := make([]*PlatformAccount, len(integrations))
	for i, integration := range integrations {
		accounts[i] = convertEntToPlatformAccount(integration)
	}

	return &ListAccountsResponse{
		Accounts:   accounts,
		TotalCount: totalCount,
	}, nil
}

// ValidateAccount validates account status and permissions
func (s *Service) ValidateAccount(ctx context.Context, req *ValidateAccountRequest) (*ValidateAccountResponse, error) {
	s.logger.WithField("account_id", req.AccountID).Info("Validating account")

	// Get integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return &ValidateAccountResponse{
			IsValid:      false,
			ErrorMessage: fmt.Sprintf("Failed to get platform integration: %v", err),
		}, nil
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return &ValidateAccountResponse{
			IsValid:      false,
			ErrorMessage: "Access denied: integration does not belong to user",
		}, nil
	}

	// Check if token is expired
	tokenExpired := false
	if !integration.TokenExpiresAt.IsZero() && integration.TokenExpiresAt.Before(time.Now()) {
		tokenExpired = true
	}

	// Check permissions if requested
	var missingPermissions []string
	if req.CheckPermissions {
		// Get OAuth provider for platform
		provider, exists := s.oauthProviders[string(integration.Platform)]
		if exists {
			// Validate permissions with platform
			currentPermissions, err := provider.GetPermissions(ctx, integration.AccessToken)
			if err != nil {
				s.logger.WithError(err).Warn("Failed to get current permissions")
			} else {
				// Compare with stored permissions
				storedPermissions := integration.Scopes
				for _, stored := range storedPermissions {
					found := false
					for _, current := range currentPermissions {
						if stored == current {
							found = true
							break
						}
					}
					if !found {
						missingPermissions = append(missingPermissions, stored)
					}
				}
			}
		}
	}

	// Update last validated timestamp
	updates := map[string]interface{}{
		"last_sync_at": time.Now(),
		"updated_at":   time.Now(),
	}

	// Update status if token is expired
	if tokenExpired && integration.Status != "expired" {
		updates["status"] = "expired"
		updates["error_message"] = "Access token has expired"
	}

	s.writer.UpdatePlatformIntegration(ctx, req.AccountID, updates)

	return &ValidateAccountResponse{
		IsValid:            !tokenExpired && len(missingPermissions) == 0,
		TokenExpired:       tokenExpired,
		MissingPermissions: missingPermissions,
		LastValidated:      time.Now().Unix(),
	}, nil
}

// Helper functions

// convertEntToPlatformAccount converts Ent entity to domain model
func convertEntToPlatformAccount(integration *ent.PlatformIntegration) *PlatformAccount {
	account := &PlatformAccount{
		ID:          integration.ID.String(),
		UserID:      integration.UserID.String(),
		Platform:    string(integration.Platform),
		AccountID:   integration.PlatformUserID,
		Status:      string(integration.Status),
		Permissions: integration.Scopes,
		ConnectedAt: integration.CreatedAt.Unix(),
	}

	account.WorkspaceID = integration.WorkspaceID
	account.Username = integration.PlatformUsername
	account.DisplayName = integration.PlatformDisplayName
	account.AvatarURL = integration.PlatformAvatarURL
	account.LastUsed = integration.LastSyncAt.Unix()

	// Convert platform data to metadata
	if integration.PlatformData != nil {
		account.Metadata = make(map[string]string)
		for k, v := range integration.PlatformData {
			if str, ok := v.(string); ok {
				account.Metadata[k] = str
			}
		}
	}

	return account
}

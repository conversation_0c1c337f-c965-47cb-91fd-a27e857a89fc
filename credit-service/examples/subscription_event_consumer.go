package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/social-content-ai/credit-service/pkg/events"
	"github.com/social-content-ai/pkg-shared/logging"
)

func main() {
	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  "info",
		Format: "json",
	})

	logger.Info("Starting subscription event consumer example")

	// Create mock dependencies
	mockNotificationService := events.NewMockNotificationService(logger)
	mockTransactionService := events.NewMockTransactionService(logger)

	// Create subscription event handler with dependencies
	// Note: In production, you would inject real subscription, credit, and transaction use cases
	eventHandler := events.NewSubscriptionEventHandler(logger, nil, nil, mockTransactionService, mockNotificationService)

	// Create mock Kafka consumer for demonstration
	mockConsumer := events.NewMockKafkaConsumer(logger)

	// Create subscription event consumer
	consumer := events.NewSubscriptionEventConsumer(mockConsumer, eventHandler, logger)

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start consumer in goroutine
	go func() {
		logger.Info("Starting subscription event consumer")
		if err := consumer.Start(ctx); err != nil {
			if err != context.Canceled {
				logger.WithError(err).Error("Consumer error")
			}
		}
	}()

	// Demonstrate creating and publishing events
	go func() {
		time.Sleep(2 * time.Second)
		demonstrateEventCreation(logger)
	}()

	// Wait for shutdown signal
	<-sigChan
	logger.Info("Received shutdown signal, stopping consumer")

	// Cancel context to stop consumer
	cancel()

	// Stop consumer
	if err := consumer.Stop(); err != nil {
		logger.WithError(err).Error("Failed to stop consumer")
	}

	logger.Info("Subscription event consumer stopped")
}

func demonstrateEventCreation(logger logging.Logger) {
	logger.Info("Demonstrating event creation")

	// Create payment completed event
	paymentCompletedEvent := events.NewSubscriptionPaymentCompletedEvent(
		"user_123",
		"sub_456",
		"pay_789",
		2999, // $29.99 in cents
		"USD",
	)
	paymentCompletedEvent.PlanID = "plan_premium"
	paymentCompletedEvent.PlanName = "Premium Plan"
	paymentCompletedEvent.CreditsAdded = 1000

	logger.WithFields(map[string]interface{}{
		"event_id":        paymentCompletedEvent.EventID,
		"event_type":      paymentCompletedEvent.EventType,
		"user_id":         paymentCompletedEvent.UserID,
		"subscription_id": paymentCompletedEvent.SubscriptionID,
		"amount":          paymentCompletedEvent.Amount,
		"credits_added":   paymentCompletedEvent.CreditsAdded,
	}).Info("Created payment completed event")

	// Create payment failed event
	paymentFailedEvent := events.NewSubscriptionPaymentFailedEvent(
		"user_456",
		"sub_789",
		"pay_012",
		2999,
		"USD",
		"Insufficient funds",
	)
	paymentFailedEvent.PlanID = "plan_basic"
	paymentFailedEvent.PlanName = "Basic Plan"
	paymentFailedEvent.FailureCode = "insufficient_funds"

	logger.WithFields(map[string]interface{}{
		"event_id":        paymentFailedEvent.EventID,
		"event_type":      paymentFailedEvent.EventType,
		"user_id":         paymentFailedEvent.UserID,
		"subscription_id": paymentFailedEvent.SubscriptionID,
		"failure_reason":  paymentFailedEvent.FailureReason,
		"failure_code":    paymentFailedEvent.FailureCode,
	}).Info("Created payment failed event")

	// Create subscription activated event
	activatedEvent := events.NewSubscriptionActivatedEventKafka(
		"user_789",
		"sub_012",
		"plan_premium",
		"Premium Plan",
	)
	activatedEvent.CreditsAdded = 1000
	activatedEvent.PaymentID = "pay_345"

	logger.WithFields(map[string]interface{}{
		"event_id":        activatedEvent.EventID,
		"event_type":      activatedEvent.EventType,
		"user_id":         activatedEvent.UserID,
		"subscription_id": activatedEvent.SubscriptionID,
		"plan_name":       activatedEvent.PlanName,
		"credits_added":   activatedEvent.CreditsAdded,
	}).Info("Created subscription activated event")

	// Create subscription cancelled event
	cancelledEvent := events.NewSubscriptionCancelledEventKafka(
		"user_012",
		"sub_345",
		"plan_basic",
		"Basic Plan",
		"User requested cancellation",
	)

	logger.WithFields(map[string]interface{}{
		"event_id":        cancelledEvent.EventID,
		"event_type":      cancelledEvent.EventType,
		"user_id":         cancelledEvent.UserID,
		"subscription_id": cancelledEvent.SubscriptionID,
		"reason":          cancelledEvent.Reason,
		"cancelled_at":    cancelledEvent.CancelledAt,
	}).Info("Created subscription cancelled event")

	// Create general subscription event
	generalEvent := events.NewGeneralSubscriptionEvent(
		events.EventTypeSubscriptionPaymentCompleted,
		"user_345",
		"sub_678",
		"active",
	)
	generalEvent.PaymentID = "pay_678"
	generalEvent.Amount = 1999 // $19.99
	generalEvent.Currency = "USD"

	logger.WithFields(map[string]interface{}{
		"event_id":        generalEvent.EventID,
		"event_type":      generalEvent.EventType,
		"user_id":         generalEvent.UserID,
		"subscription_id": generalEvent.SubscriptionID,
		"status":          generalEvent.Status,
		"amount":          generalEvent.Amount,
	}).Info("Created general subscription event")

	// Demonstrate Kafka topics
	logger.WithFields(map[string]interface{}{
		"subscription_events":            events.TopicSubscriptionEvents,
		"subscription_payment_completed": events.TopicSubscriptionPaymentCompleted,
		"subscription_payment_failed":    events.TopicSubscriptionPaymentFailed,
		"subscription_activated":         events.TopicSubscriptionActivated,
		"subscription_cancelled":         events.TopicSubscriptionCancelled,
	}).Info("Available Kafka topics for subscription events")

	logger.Info("Event creation demonstration completed")
}

// Example of how to integrate with real Kafka consumer
func exampleRealKafkaIntegration() {
	// This is an example of how you would integrate with a real Kafka consumer
	// You would replace MockKafkaConsumer with a real implementation

	/*
		// Initialize Kafka consumer configuration
		kafkaConfig := &kafka.Config{
			Brokers: []string{"localhost:9092"},
			GroupID: "credit-service-subscription-events",
			Topics: []string{
				events.TopicSubscriptionEvents,
				events.TopicSubscriptionPaymentCompleted,
				events.TopicSubscriptionPaymentFailed,
				events.TopicSubscriptionActivated,
				events.TopicSubscriptionCancelled,
			},
		}

		// Create real Kafka consumer
		realConsumer, err := kafka.NewConsumer(kafkaConfig)
		if err != nil {
			log.Fatalf("Failed to create Kafka consumer: %v", err)
		}

		// Create subscription event handler
		eventHandler := events.NewSubscriptionEventHandler(logger)

		// Create subscription event consumer with real Kafka consumer
		consumer := events.NewSubscriptionEventConsumer(realConsumer, eventHandler, logger)

		// Start consuming events
		ctx := context.Background()
		if err := consumer.Start(ctx); err != nil {
			log.Fatalf("Failed to start consumer: %v", err)
		}
	*/
}

// Example of how to create a stats tracker
func exampleStatsTracking() {
	logger := logging.NewLogger(&logging.Config{
		Level:  "info",
		Format: "json",
	})

	// Create stats tracker
	statsTracker := events.NewStatsTracker(logger)

	// Simulate tracking events
	statsTracker.TrackEvent(events.TopicSubscriptionPaymentCompleted, true)
	statsTracker.TrackEvent(events.TopicSubscriptionPaymentFailed, true)
	statsTracker.TrackEvent(events.TopicSubscriptionActivated, true)
	statsTracker.TrackEvent(events.TopicSubscriptionCancelled, false) // Error case

	// Get current stats
	stats := statsTracker.GetStats()
	logger.WithFields(map[string]interface{}{
		"total_processed":        stats.TotalProcessed,
		"payment_completed":      stats.PaymentCompleted,
		"payment_failed":         stats.PaymentFailed,
		"subscription_activated": stats.SubscriptionActivated,
		"subscription_cancelled": stats.SubscriptionCancelled,
		"errors":                 stats.Errors,
		"last_processed_at":      stats.LastProcessedAt,
	}).Info("Event processing statistics")
}

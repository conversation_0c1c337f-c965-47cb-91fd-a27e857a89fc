package push

import (
	"context"
	"fmt"

	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockSender implements PushSender for testing and development
type MockSender struct {
	logger logging.Logger
}

// NewMockSender creates a new mock push sender
func NewMockSender(logger logging.Logger) channels.PushSender {
	return &MockSender{
		logger: logger,
	}
}

// SendPush simulates sending a push notification
func (m *MockSender) SendPush(ctx context.Context, message *channels.PushMessage) error {
	m.logger.WithFields(map[string]interface{}{
		"device_tokens": len(message.DeviceTokens),
		"title":         message.Title,
		"body":          message.Body,
		"data":          message.Data,
	}).Info("Mock push notification sent")

	// Validate message
	if len(message.DeviceTokens) == 0 {
		return fmt.Errorf("at least one device token is required")
	}
	if message.Title == "" && message.Body == "" {
		return fmt.Errorf("title or body is required")
	}

	// Simulate successful sending
	m.logger.WithField("device_count", len(message.DeviceTokens)).Info("Mock push notification sent successfully")
	return nil
}

package database

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"entgo.io/ent/dialect/sql"
	"github.com/social-content-ai/notification-service/config"
	"github.com/social-content-ai/notification-service/ent"
	"github.com/social-content-ai/pkg-shared/logging"

	// Import database drivers
	_ "github.com/lib/pq"           // PostgreSQL driver
	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

// DatabaseManager manages database connections and migrations
type DatabaseManager struct {
	config *config.DatabaseConfig
	logger logging.Logger
}

// NewDatabaseManager creates a new database manager
func NewDatabaseManager(cfg *config.DatabaseConfig, logger logging.Logger) *DatabaseManager {
	return &DatabaseManager{
		config: cfg,
		logger: logger,
	}
}

// Connect establishes database connection and returns ent client
func (dm *DatabaseManager) Connect(ctx context.Context) (*ent.Client, error) {
	dm.logger.WithField("db_type", dm.config.Type).Info("Connecting to database")

	// Prepare database connection
	if err := dm.prepareDatabase(); err != nil {
		return nil, fmt.Errorf("failed to prepare database: %w", err)
	}

	// Get database driver and DSN
	driverName := dm.config.GetDriverName()
	dsn := dm.config.GetDSN()

	// Open database connection
	drv, err := sql.Open(driverName, dsn)
	if err != nil {
		dm.logger.WithError(err).Error("Failed to open database connection")
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db := drv.DB()
	switch dm.config.Type {
	case "postgres":
		db.SetMaxOpenConns(25)
		db.SetMaxIdleConns(5)
	case "sqlite":
		// SQLite doesn't benefit from multiple connections
		db.SetMaxOpenConns(1)
		db.SetMaxIdleConns(1)
	}

	// Create ent client
	client := ent.NewClient(ent.Driver(drv))

	// Test connection
	if err := client.Schema.Create(ctx); err != nil {
		dm.logger.WithError(err).Error("Failed to create database schema")
		return nil, fmt.Errorf("failed to create schema: %w", err)
	}

	dm.logger.WithField("db_type", dm.config.Type).Info("Database connected successfully")
	return client, nil
}

// ConnectReadWrite establishes separate read and write database connections
func (dm *DatabaseManager) ConnectReadWrite(ctx context.Context) (*ent.Client, *ent.Client, error) {
	dm.logger.WithField("db_type", dm.config.Type).Info("Connecting to read/write databases")

	// For now, use the same connection for both read and write
	// In production, you might want to use read replicas
	writeDB, err := dm.Connect(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to connect to write database: %w", err)
	}

	readDB, err := dm.Connect(ctx)
	if err != nil {
		writeDB.Close()
		return nil, nil, fmt.Errorf("failed to connect to read database: %w", err)
	}

	return readDB, writeDB, nil
}

// Migrate runs database migrations
func (dm *DatabaseManager) Migrate(ctx context.Context, client *ent.Client) error {
	dm.logger.Info("Running database migrations")

	if err := client.Schema.Create(ctx); err != nil {
		dm.logger.WithError(err).Error("Failed to run migrations")
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	dm.logger.Info("Database migrations completed successfully")
	return nil
}

// prepareDatabase prepares the database (creates directories for SQLite, etc.)
func (dm *DatabaseManager) prepareDatabase() error {
	switch dm.config.Type {
	case "sqlite":
		// Create directory for SQLite database file
		dbDir := filepath.Dir(dm.config.SQLitePath)
		if err := os.MkdirAll(dbDir, 0755); err != nil {
			return fmt.Errorf("failed to create database directory: %w", err)
		}
		dm.logger.WithField("db_path", dm.config.SQLitePath).Info("SQLite database directory prepared")
	case "postgres":
		// For PostgreSQL, we assume the database server is already running
		dm.logger.WithFields(map[string]interface{}{
			"host": dm.config.Host,
			"port": dm.config.Port,
			"name": dm.config.Name,
		}).Info("PostgreSQL connection prepared")
	}

	return nil
}

// Close closes database connections
func (dm *DatabaseManager) Close(clients ...*ent.Client) error {
	for _, client := range clients {
		if client != nil {
			if err := client.Close(); err != nil {
				dm.logger.WithError(err).Error("Failed to close database connection")
				return err
			}
		}
	}
	dm.logger.Info("Database connections closed")
	return nil
}

// HealthCheck performs a database health check
func (dm *DatabaseManager) HealthCheck(ctx context.Context, client *ent.Client) error {
	// Try to execute a simple query
	_, err := client.Notification.Query().Count(ctx)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}
	return nil
}

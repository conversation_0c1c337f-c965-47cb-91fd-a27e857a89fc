package template

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockService implements the template UseCase interface with mock implementations
type MockService struct {
	logger logging.Logger
}

// NewMockService creates a new mock template service
func NewMockService(logger logging.Logger) UseCase {
	return &MockService{
		logger: logger,
	}
}

// ListTemplates lists notification templates
func (s *MockService) ListTemplates(ctx context.Context, req *ListTemplatesRequest) (*ListTemplatesResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"page":       req.Page,
		"limit":      req.Limit,
		"event_type": req.EventType,
	}).Debug("Listing templates (mock)")

	// Return mock templates
	templates := []TemplateResponse{
		{
			ID:          "welcome_email",
			EventType:   "user_registered",
			Name:        "Welcome Email",
			Description: "Welcome email template for new users",
			Templates: map[string]ChannelTemplate{
				"email": {
					Subject:  "Welcome to {{.app_name}}!",
					Body:     "Hello {{.user_name}}, welcome to our platform!",
					HTMLBody: "<h1>Hello {{.user_name}}</h1><p>Welcome to our platform!</p>",
				},
				"push": {
					Body: "Welcome {{.user_name}}!",
				},
			},
			IsActive:  true,
			Variables: []string{"app_name", "user_name"},
			CreatedAt: time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
			UpdatedAt: time.Now().Add(-7 * 24 * time.Hour).Format(time.RFC3339),
		},
		{
			ID:          "password_reset",
			EventType:   "password_reset_requested",
			Name:        "Password Reset",
			Description: "Password reset email template",
			Templates: map[string]ChannelTemplate{
				"email": {
					Subject:  "Reset your password",
					Body:     "Click here to reset your password: {{.reset_link}}",
					HTMLBody: "<p>Click <a href='{{.reset_link}}'>here</a> to reset your password</p>",
				},
				"sms": {
					Body: "Reset your password: {{.reset_link}}",
				},
			},
			IsActive:  true,
			Variables: []string{"reset_link"},
			CreatedAt: time.Now().Add(-20 * 24 * time.Hour).Format(time.RFC3339),
			UpdatedAt: time.Now().Add(-5 * 24 * time.Hour).Format(time.RFC3339),
		},
		{
			ID:          "content_generated",
			EventType:   "content_generated",
			Name:        "Content Generated",
			Description: "Notification when content is generated",
			Templates: map[string]ChannelTemplate{
				"push": {
					Body: "Your content '{{.content_title}}' has been generated!",
				},
				"websocket": {
					Body: "Content generation completed for '{{.content_title}}'",
				},
			},
			IsActive:  true,
			Variables: []string{"content_title"},
			CreatedAt: time.Now().Add(-10 * 24 * time.Hour).Format(time.RFC3339),
			UpdatedAt: time.Now().Add(-2 * 24 * time.Hour).Format(time.RFC3339),
		},
	}

	// Filter by event type if specified
	if req.EventType != "" {
		var filtered []TemplateResponse
		for _, template := range templates {
			if template.EventType == req.EventType {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	// Filter by active status if specified
	if req.IsActive != nil {
		var filtered []TemplateResponse
		for _, template := range templates {
			if template.IsActive == *req.IsActive {
				filtered = append(filtered, template)
			}
		}
		templates = filtered
	}

	// Apply pagination
	total := len(templates)
	start := (req.Page - 1) * req.Limit
	end := start + req.Limit
	if start >= len(templates) {
		templates = []TemplateResponse{}
	} else if end > len(templates) {
		templates = templates[start:]
	} else {
		templates = templates[start:end]
	}

	totalPages := (total + req.Limit - 1) / req.Limit

	return &ListTemplatesResponse{
		Templates: templates,
		Pagination: PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// CreateTemplate creates a new notification template
func (s *MockService) CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*TemplateResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"name":       req.Name,
		"event_type": req.EventType,
	}).Info("Creating template (mock)")

	templateID := uuid.New().String()

	return &TemplateResponse{
		ID:          templateID,
		EventType:   req.EventType,
		Name:        req.Name,
		Description: req.Description,
		Templates:   req.Templates,
		IsActive:    req.IsActive,
		Variables:   req.Variables,
		CreatedAt:   time.Now().Format(time.RFC3339),
		UpdatedAt:   time.Now().Format(time.RFC3339),
	}, nil
}

// GetTemplate gets a template by ID
func (s *MockService) GetTemplate(ctx context.Context, templateID string) (*TemplateResponse, error) {
	s.logger.WithField("template_id", templateID).Debug("Getting template (mock)")

	// Return mock template
	return &TemplateResponse{
		ID:          templateID,
		EventType:   "mock_event",
		Name:        "Mock Template",
		Description: "This is a mock template",
		Templates: map[string]ChannelTemplate{
			"email": {
				Subject:  "Mock Subject: {{.title}}",
				Body:     "Mock body: {{.message}}",
				HTMLBody: "<h1>{{.title}}</h1><p>{{.message}}</p>",
			},
			"push": {
				Body: "{{.title}}: {{.message}}",
			},
		},
		IsActive:  true,
		Variables: []string{"title", "message"},
		CreatedAt: time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		UpdatedAt: time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
	}, nil
}

// GetTemplateByEventType gets a template by event type
func (s *MockService) GetTemplateByEventType(ctx context.Context, eventType string) (*TemplateResponse, error) {
	s.logger.WithField("event_type", eventType).Debug("Getting template by event type (mock)")

	// Return mock template
	return &TemplateResponse{
		ID:          "template_" + eventType,
		EventType:   eventType,
		Name:        "Template for " + eventType,
		Description: "Mock template for event type: " + eventType,
		Templates: map[string]ChannelTemplate{
			"email": {
				Subject:  "Event: {{.event_type}}",
				Body:     "Event {{.event_type}} occurred: {{.message}}",
				HTMLBody: "<h1>Event: {{.event_type}}</h1><p>{{.message}}</p>",
			},
			"push": {
				Body: "{{.event_type}}: {{.message}}",
			},
		},
		IsActive:  true,
		Variables: []string{"event_type", "message"},
		CreatedAt: time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		UpdatedAt: time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
	}, nil
}

// UpdateTemplate updates a template
func (s *MockService) UpdateTemplate(ctx context.Context, req *UpdateTemplateRequest) (*TemplateResponse, error) {
	s.logger.WithField("template_id", req.ID).Info("Updating template (mock)")

	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	return &TemplateResponse{
		ID:          req.ID,
		EventType:   "updated_event",
		Name:        req.Name,
		Description: req.Description,
		Templates:   req.Templates,
		IsActive:    isActive,
		Variables:   req.Variables,
		CreatedAt:   time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		UpdatedAt:   time.Now().Format(time.RFC3339),
	}, nil
}

// DeleteTemplate deletes a template
func (s *MockService) DeleteTemplate(ctx context.Context, templateID string) error {
	s.logger.WithField("template_id", templateID).Info("Deleting template (mock)")
	return nil
}

// RenderTemplate renders a template with data
func (s *MockService) RenderTemplate(ctx context.Context, req *RenderTemplateRequest) (*RenderTemplateResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"template_id": req.TemplateID,
		"channel":     req.Channel,
	}).Debug("Rendering template (mock)")

	// Mock template rendering
	subject := "Mock Subject: Sample Title"
	body := "Mock body: Sample message content"
	htmlBody := "<h1>Sample Title</h1><p>Sample message content</p>"

	return &RenderTemplateResponse{
		Subject:  subject,
		Body:     body,
		HTMLBody: htmlBody,
		Metadata: map[string]string{
			"rendered_at": time.Now().Format(time.RFC3339),
		},
	}, nil
}

package channels

import "context"

// EmailSender interface for sending emails
type EmailSender interface {
	SendEmail(ctx context.Context, message *EmailMessage) error
}

// SMSSender interface for sending SMS
type SMSSender interface {
	SendSMS(ctx context.Context, message *SMSMessage) error
}

// PushSender interface for sending push notifications
type PushSender interface {
	SendPush(ctx context.Context, message *PushMessage) error
}

// EmailMessage represents an email message
type EmailMessage struct {
	To      string            `json:"to"`
	Subject string            `json:"subject"`
	Body    string            `json:"body"`
	IsHTML  bool              `json:"is_html"`
	Headers map[string]string `json:"headers,omitempty"`
}

// SMSMessage represents an SMS message
type SMSMessage struct {
	To   string `json:"to"`
	Body string `json:"body"`
}

// PushMessage represents a push notification message
type PushMessage struct {
	DeviceTokens []string          `json:"device_tokens"`
	Title        string            `json:"title"`
	Body         string            `json:"body"`
	Data         map[string]string `json:"data,omitempty"`
}

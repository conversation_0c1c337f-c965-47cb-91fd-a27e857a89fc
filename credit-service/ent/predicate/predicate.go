// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// BankTransfer is the predicate function for banktransfer builders.
type BankTransfer func(*sql.Selector)

// CreditPlan is the predicate function for creditplan builders.
type CreditPlan func(*sql.Selector)

// CreditReservation is the predicate function for creditreservation builders.
type CreditReservation func(*sql.Selector)

// CreditTransaction is the predicate function for credittransaction builders.
type CreditTransaction func(*sql.Selector)

// Order is the predicate function for order builders.
type Order func(*sql.Selector)

// Payment is the predicate function for payment builders.
type Payment func(*sql.Selector)

// PaymentConfirmation is the predicate function for paymentconfirmation builders.
type PaymentConfirmation func(*sql.Selector)

// Referral is the predicate function for referral builders.
type Referral func(*sql.Selector)

// Subscription is the predicate function for subscription builders.
type Subscription func(*sql.Selector)

// SubscriptionPlan is the predicate function for subscriptionplan builders.
type SubscriptionPlan func(*sql.Selector)

// UserCredit is the predicate function for usercredit builders.
type UserCredit func(*sql.Selector)

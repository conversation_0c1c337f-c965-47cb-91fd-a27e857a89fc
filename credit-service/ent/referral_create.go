// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/referral"
)

// ReferralCreate is the builder for creating a Referral entity.
type ReferralCreate struct {
	config
	mutation *ReferralMutation
	hooks    []Hook
}

// SetReferrerID sets the "referrer_id" field.
func (rc *ReferralCreate) SetReferrerID(u uuid.UUID) *ReferralCreate {
	rc.mutation.SetReferrerID(u)
	return rc
}

// SetReferredID sets the "referred_id" field.
func (rc *ReferralCreate) SetReferredID(u uuid.UUID) *ReferralCreate {
	rc.mutation.SetReferredID(u)
	return rc
}

// SetNillableReferredID sets the "referred_id" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableReferredID(u *uuid.UUID) *ReferralCreate {
	if u != nil {
		rc.SetReferredID(*u)
	}
	return rc
}

// SetReferralCode sets the "referral_code" field.
func (rc *ReferralCreate) SetReferralCode(s string) *ReferralCreate {
	rc.mutation.SetReferralCode(s)
	return rc
}

// SetReferredEmail sets the "referred_email" field.
func (rc *ReferralCreate) SetReferredEmail(s string) *ReferralCreate {
	rc.mutation.SetReferredEmail(s)
	return rc
}

// SetNillableReferredEmail sets the "referred_email" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableReferredEmail(s *string) *ReferralCreate {
	if s != nil {
		rc.SetReferredEmail(*s)
	}
	return rc
}

// SetStatus sets the "status" field.
func (rc *ReferralCreate) SetStatus(r referral.Status) *ReferralCreate {
	rc.mutation.SetStatus(r)
	return rc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableStatus(r *referral.Status) *ReferralCreate {
	if r != nil {
		rc.SetStatus(*r)
	}
	return rc
}

// SetReferrerCreditsEarned sets the "referrer_credits_earned" field.
func (rc *ReferralCreate) SetReferrerCreditsEarned(i int) *ReferralCreate {
	rc.mutation.SetReferrerCreditsEarned(i)
	return rc
}

// SetNillableReferrerCreditsEarned sets the "referrer_credits_earned" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableReferrerCreditsEarned(i *int) *ReferralCreate {
	if i != nil {
		rc.SetReferrerCreditsEarned(*i)
	}
	return rc
}

// SetReferredCreditsEarned sets the "referred_credits_earned" field.
func (rc *ReferralCreate) SetReferredCreditsEarned(i int) *ReferralCreate {
	rc.mutation.SetReferredCreditsEarned(i)
	return rc
}

// SetNillableReferredCreditsEarned sets the "referred_credits_earned" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableReferredCreditsEarned(i *int) *ReferralCreate {
	if i != nil {
		rc.SetReferredCreditsEarned(*i)
	}
	return rc
}

// SetReferralType sets the "referral_type" field.
func (rc *ReferralCreate) SetReferralType(s string) *ReferralCreate {
	rc.mutation.SetReferralType(s)
	return rc
}

// SetNillableReferralType sets the "referral_type" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableReferralType(s *string) *ReferralCreate {
	if s != nil {
		rc.SetReferralType(*s)
	}
	return rc
}

// SetCompletedAt sets the "completed_at" field.
func (rc *ReferralCreate) SetCompletedAt(t time.Time) *ReferralCreate {
	rc.mutation.SetCompletedAt(t)
	return rc
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableCompletedAt(t *time.Time) *ReferralCreate {
	if t != nil {
		rc.SetCompletedAt(*t)
	}
	return rc
}

// SetExpiresAt sets the "expires_at" field.
func (rc *ReferralCreate) SetExpiresAt(t time.Time) *ReferralCreate {
	rc.mutation.SetExpiresAt(t)
	return rc
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableExpiresAt(t *time.Time) *ReferralCreate {
	if t != nil {
		rc.SetExpiresAt(*t)
	}
	return rc
}

// SetReferralConditions sets the "referral_conditions" field.
func (rc *ReferralCreate) SetReferralConditions(m map[string]interface{}) *ReferralCreate {
	rc.mutation.SetReferralConditions(m)
	return rc
}

// SetTrackingData sets the "tracking_data" field.
func (rc *ReferralCreate) SetTrackingData(m map[string]interface{}) *ReferralCreate {
	rc.mutation.SetTrackingData(m)
	return rc
}

// SetCampaignID sets the "campaign_id" field.
func (rc *ReferralCreate) SetCampaignID(s string) *ReferralCreate {
	rc.mutation.SetCampaignID(s)
	return rc
}

// SetNillableCampaignID sets the "campaign_id" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableCampaignID(s *string) *ReferralCreate {
	if s != nil {
		rc.SetCampaignID(*s)
	}
	return rc
}

// SetSource sets the "source" field.
func (rc *ReferralCreate) SetSource(s string) *ReferralCreate {
	rc.mutation.SetSource(s)
	return rc
}

// SetNillableSource sets the "source" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableSource(s *string) *ReferralCreate {
	if s != nil {
		rc.SetSource(*s)
	}
	return rc
}

// SetMetadata sets the "metadata" field.
func (rc *ReferralCreate) SetMetadata(m map[string]interface{}) *ReferralCreate {
	rc.mutation.SetMetadata(m)
	return rc
}

// SetCreatedAt sets the "created_at" field.
func (rc *ReferralCreate) SetCreatedAt(t time.Time) *ReferralCreate {
	rc.mutation.SetCreatedAt(t)
	return rc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableCreatedAt(t *time.Time) *ReferralCreate {
	if t != nil {
		rc.SetCreatedAt(*t)
	}
	return rc
}

// SetUpdatedAt sets the "updated_at" field.
func (rc *ReferralCreate) SetUpdatedAt(t time.Time) *ReferralCreate {
	rc.mutation.SetUpdatedAt(t)
	return rc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableUpdatedAt(t *time.Time) *ReferralCreate {
	if t != nil {
		rc.SetUpdatedAt(*t)
	}
	return rc
}

// SetID sets the "id" field.
func (rc *ReferralCreate) SetID(u uuid.UUID) *ReferralCreate {
	rc.mutation.SetID(u)
	return rc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (rc *ReferralCreate) SetNillableID(u *uuid.UUID) *ReferralCreate {
	if u != nil {
		rc.SetID(*u)
	}
	return rc
}

// Mutation returns the ReferralMutation object of the builder.
func (rc *ReferralCreate) Mutation() *ReferralMutation {
	return rc.mutation
}

// Save creates the Referral in the database.
func (rc *ReferralCreate) Save(ctx context.Context) (*Referral, error) {
	rc.defaults()
	return withHooks(ctx, rc.sqlSave, rc.mutation, rc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (rc *ReferralCreate) SaveX(ctx context.Context) *Referral {
	v, err := rc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rc *ReferralCreate) Exec(ctx context.Context) error {
	_, err := rc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rc *ReferralCreate) ExecX(ctx context.Context) {
	if err := rc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (rc *ReferralCreate) defaults() {
	if _, ok := rc.mutation.Status(); !ok {
		v := referral.DefaultStatus
		rc.mutation.SetStatus(v)
	}
	if _, ok := rc.mutation.ReferrerCreditsEarned(); !ok {
		v := referral.DefaultReferrerCreditsEarned
		rc.mutation.SetReferrerCreditsEarned(v)
	}
	if _, ok := rc.mutation.ReferredCreditsEarned(); !ok {
		v := referral.DefaultReferredCreditsEarned
		rc.mutation.SetReferredCreditsEarned(v)
	}
	if _, ok := rc.mutation.ReferralType(); !ok {
		v := referral.DefaultReferralType
		rc.mutation.SetReferralType(v)
	}
	if _, ok := rc.mutation.CreatedAt(); !ok {
		v := referral.DefaultCreatedAt()
		rc.mutation.SetCreatedAt(v)
	}
	if _, ok := rc.mutation.UpdatedAt(); !ok {
		v := referral.DefaultUpdatedAt()
		rc.mutation.SetUpdatedAt(v)
	}
	if _, ok := rc.mutation.ID(); !ok {
		v := referral.DefaultID()
		rc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (rc *ReferralCreate) check() error {
	if _, ok := rc.mutation.ReferrerID(); !ok {
		return &ValidationError{Name: "referrer_id", err: errors.New(`ent: missing required field "Referral.referrer_id"`)}
	}
	if _, ok := rc.mutation.ReferralCode(); !ok {
		return &ValidationError{Name: "referral_code", err: errors.New(`ent: missing required field "Referral.referral_code"`)}
	}
	if v, ok := rc.mutation.ReferralCode(); ok {
		if err := referral.ReferralCodeValidator(v); err != nil {
			return &ValidationError{Name: "referral_code", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_code": %w`, err)}
		}
	}
	if v, ok := rc.mutation.ReferredEmail(); ok {
		if err := referral.ReferredEmailValidator(v); err != nil {
			return &ValidationError{Name: "referred_email", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_email": %w`, err)}
		}
	}
	if _, ok := rc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Referral.status"`)}
	}
	if v, ok := rc.mutation.Status(); ok {
		if err := referral.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Referral.status": %w`, err)}
		}
	}
	if _, ok := rc.mutation.ReferrerCreditsEarned(); !ok {
		return &ValidationError{Name: "referrer_credits_earned", err: errors.New(`ent: missing required field "Referral.referrer_credits_earned"`)}
	}
	if v, ok := rc.mutation.ReferrerCreditsEarned(); ok {
		if err := referral.ReferrerCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referrer_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referrer_credits_earned": %w`, err)}
		}
	}
	if _, ok := rc.mutation.ReferredCreditsEarned(); !ok {
		return &ValidationError{Name: "referred_credits_earned", err: errors.New(`ent: missing required field "Referral.referred_credits_earned"`)}
	}
	if v, ok := rc.mutation.ReferredCreditsEarned(); ok {
		if err := referral.ReferredCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referred_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_credits_earned": %w`, err)}
		}
	}
	if _, ok := rc.mutation.ReferralType(); !ok {
		return &ValidationError{Name: "referral_type", err: errors.New(`ent: missing required field "Referral.referral_type"`)}
	}
	if v, ok := rc.mutation.ReferralType(); ok {
		if err := referral.ReferralTypeValidator(v); err != nil {
			return &ValidationError{Name: "referral_type", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_type": %w`, err)}
		}
	}
	if v, ok := rc.mutation.CampaignID(); ok {
		if err := referral.CampaignIDValidator(v); err != nil {
			return &ValidationError{Name: "campaign_id", err: fmt.Errorf(`ent: validator failed for field "Referral.campaign_id": %w`, err)}
		}
	}
	if v, ok := rc.mutation.Source(); ok {
		if err := referral.SourceValidator(v); err != nil {
			return &ValidationError{Name: "source", err: fmt.Errorf(`ent: validator failed for field "Referral.source": %w`, err)}
		}
	}
	if _, ok := rc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Referral.created_at"`)}
	}
	if _, ok := rc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Referral.updated_at"`)}
	}
	return nil
}

func (rc *ReferralCreate) sqlSave(ctx context.Context) (*Referral, error) {
	if err := rc.check(); err != nil {
		return nil, err
	}
	_node, _spec := rc.createSpec()
	if err := sqlgraph.CreateNode(ctx, rc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	rc.mutation.id = &_node.ID
	rc.mutation.done = true
	return _node, nil
}

func (rc *ReferralCreate) createSpec() (*Referral, *sqlgraph.CreateSpec) {
	var (
		_node = &Referral{config: rc.config}
		_spec = sqlgraph.NewCreateSpec(referral.Table, sqlgraph.NewFieldSpec(referral.FieldID, field.TypeUUID))
	)
	if id, ok := rc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := rc.mutation.ReferrerID(); ok {
		_spec.SetField(referral.FieldReferrerID, field.TypeUUID, value)
		_node.ReferrerID = value
	}
	if value, ok := rc.mutation.ReferredID(); ok {
		_spec.SetField(referral.FieldReferredID, field.TypeUUID, value)
		_node.ReferredID = value
	}
	if value, ok := rc.mutation.ReferralCode(); ok {
		_spec.SetField(referral.FieldReferralCode, field.TypeString, value)
		_node.ReferralCode = value
	}
	if value, ok := rc.mutation.ReferredEmail(); ok {
		_spec.SetField(referral.FieldReferredEmail, field.TypeString, value)
		_node.ReferredEmail = value
	}
	if value, ok := rc.mutation.Status(); ok {
		_spec.SetField(referral.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := rc.mutation.ReferrerCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferrerCreditsEarned, field.TypeInt, value)
		_node.ReferrerCreditsEarned = value
	}
	if value, ok := rc.mutation.ReferredCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferredCreditsEarned, field.TypeInt, value)
		_node.ReferredCreditsEarned = value
	}
	if value, ok := rc.mutation.ReferralType(); ok {
		_spec.SetField(referral.FieldReferralType, field.TypeString, value)
		_node.ReferralType = value
	}
	if value, ok := rc.mutation.CompletedAt(); ok {
		_spec.SetField(referral.FieldCompletedAt, field.TypeTime, value)
		_node.CompletedAt = value
	}
	if value, ok := rc.mutation.ExpiresAt(); ok {
		_spec.SetField(referral.FieldExpiresAt, field.TypeTime, value)
		_node.ExpiresAt = value
	}
	if value, ok := rc.mutation.ReferralConditions(); ok {
		_spec.SetField(referral.FieldReferralConditions, field.TypeJSON, value)
		_node.ReferralConditions = value
	}
	if value, ok := rc.mutation.TrackingData(); ok {
		_spec.SetField(referral.FieldTrackingData, field.TypeJSON, value)
		_node.TrackingData = value
	}
	if value, ok := rc.mutation.CampaignID(); ok {
		_spec.SetField(referral.FieldCampaignID, field.TypeString, value)
		_node.CampaignID = value
	}
	if value, ok := rc.mutation.Source(); ok {
		_spec.SetField(referral.FieldSource, field.TypeString, value)
		_node.Source = value
	}
	if value, ok := rc.mutation.Metadata(); ok {
		_spec.SetField(referral.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := rc.mutation.CreatedAt(); ok {
		_spec.SetField(referral.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := rc.mutation.UpdatedAt(); ok {
		_spec.SetField(referral.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// ReferralCreateBulk is the builder for creating many Referral entities in bulk.
type ReferralCreateBulk struct {
	config
	err      error
	builders []*ReferralCreate
}

// Save creates the Referral entities in the database.
func (rcb *ReferralCreateBulk) Save(ctx context.Context) ([]*Referral, error) {
	if rcb.err != nil {
		return nil, rcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(rcb.builders))
	nodes := make([]*Referral, len(rcb.builders))
	mutators := make([]Mutator, len(rcb.builders))
	for i := range rcb.builders {
		func(i int, root context.Context) {
			builder := rcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ReferralMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, rcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, rcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, rcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (rcb *ReferralCreateBulk) SaveX(ctx context.Context) []*Referral {
	v, err := rcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rcb *ReferralCreateBulk) Exec(ctx context.Context) error {
	_, err := rcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rcb *ReferralCreateBulk) ExecX(ctx context.Context) {
	if err := rcb.Exec(ctx); err != nil {
		panic(err)
	}
}

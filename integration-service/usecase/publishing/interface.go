package publishing

import (
	"context"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/pkg/publishers"
)

// UseCase defines the publishing use case interface
type UseCase interface {
	// Publisher management
	RegisterPublisher(platform string, publisher publishers.Publisher)

	// Publishing operations
	PublishPost(ctx context.Context, req *PublishPostRequest) (*PublishPostResponse, error)
	GetPostAnalytics(ctx context.Context, req *GetPostAnalyticsRequest) (*GetPostAnalyticsResponse, error)
	GetAccountAnalytics(ctx context.Context, req *GetAccountAnalyticsRequest) (*GetAccountAnalyticsResponse, error)
}

// Repository defines the data access interface for publishing operations
type Repository interface {
	Reader
	Writer
}

// Reader defines read operations interface
type Reader interface {
	GetPlatformIntegration(ctx context.Context, id string) (*ent.PlatformIntegration, error)
	GetPublishedPost(ctx context.Context, id string) (*ent.PublishedPost, error)
	ListPublishedPosts(ctx context.Context, userID, platform string, limit, offset int32) ([]*ent.PublishedPost, int32, error)
}

// Writer defines write operations interface
type Writer interface {
	CreatePublishedPost(ctx context.Context, post *ent.PublishedPost) (*ent.PublishedPost, error)
	UpdatePublishedPost(ctx context.Context, id string, updates map[string]interface{}) (*ent.PublishedPost, error)
	DeletePublishedPost(ctx context.Context, id string) error
}

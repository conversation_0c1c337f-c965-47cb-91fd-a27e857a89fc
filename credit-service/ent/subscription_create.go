// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/subscription"
)

// SubscriptionCreate is the builder for creating a Subscription entity.
type SubscriptionCreate struct {
	config
	mutation *SubscriptionMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (sc *SubscriptionCreate) SetUserID(u uuid.UUID) *SubscriptionCreate {
	sc.mutation.SetUserID(u)
	return sc
}

// SetPlanID sets the "plan_id" field.
func (sc *SubscriptionCreate) SetPlanID(u uuid.UUID) *SubscriptionCreate {
	sc.mutation.SetPlanID(u)
	return sc
}

// SetStatus sets the "status" field.
func (sc *SubscriptionCreate) SetStatus(s subscription.Status) *SubscriptionCreate {
	sc.mutation.SetStatus(s)
	return sc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableStatus(s *subscription.Status) *SubscriptionCreate {
	if s != nil {
		sc.SetStatus(*s)
	}
	return sc
}

// SetCurrentPeriodStart sets the "current_period_start" field.
func (sc *SubscriptionCreate) SetCurrentPeriodStart(t time.Time) *SubscriptionCreate {
	sc.mutation.SetCurrentPeriodStart(t)
	return sc
}

// SetCurrentPeriodEnd sets the "current_period_end" field.
func (sc *SubscriptionCreate) SetCurrentPeriodEnd(t time.Time) *SubscriptionCreate {
	sc.mutation.SetCurrentPeriodEnd(t)
	return sc
}

// SetTrialStart sets the "trial_start" field.
func (sc *SubscriptionCreate) SetTrialStart(t time.Time) *SubscriptionCreate {
	sc.mutation.SetTrialStart(t)
	return sc
}

// SetNillableTrialStart sets the "trial_start" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableTrialStart(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetTrialStart(*t)
	}
	return sc
}

// SetTrialEnd sets the "trial_end" field.
func (sc *SubscriptionCreate) SetTrialEnd(t time.Time) *SubscriptionCreate {
	sc.mutation.SetTrialEnd(t)
	return sc
}

// SetNillableTrialEnd sets the "trial_end" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableTrialEnd(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetTrialEnd(*t)
	}
	return sc
}

// SetCancelledAt sets the "cancelled_at" field.
func (sc *SubscriptionCreate) SetCancelledAt(t time.Time) *SubscriptionCreate {
	sc.mutation.SetCancelledAt(t)
	return sc
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableCancelledAt(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetCancelledAt(*t)
	}
	return sc
}

// SetCancelAtPeriodEnd sets the "cancel_at_period_end" field.
func (sc *SubscriptionCreate) SetCancelAtPeriodEnd(b bool) *SubscriptionCreate {
	sc.mutation.SetCancelAtPeriodEnd(b)
	return sc
}

// SetNillableCancelAtPeriodEnd sets the "cancel_at_period_end" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableCancelAtPeriodEnd(b *bool) *SubscriptionCreate {
	if b != nil {
		sc.SetCancelAtPeriodEnd(*b)
	}
	return sc
}

// SetPaymentMethod sets the "payment_method" field.
func (sc *SubscriptionCreate) SetPaymentMethod(s string) *SubscriptionCreate {
	sc.mutation.SetPaymentMethod(s)
	return sc
}

// SetBillingAddress sets the "billing_address" field.
func (sc *SubscriptionCreate) SetBillingAddress(m map[string]interface{}) *SubscriptionCreate {
	sc.mutation.SetBillingAddress(m)
	return sc
}

// SetMetadata sets the "metadata" field.
func (sc *SubscriptionCreate) SetMetadata(m map[string]interface{}) *SubscriptionCreate {
	sc.mutation.SetMetadata(m)
	return sc
}

// SetLastBilledAt sets the "last_billed_at" field.
func (sc *SubscriptionCreate) SetLastBilledAt(t time.Time) *SubscriptionCreate {
	sc.mutation.SetLastBilledAt(t)
	return sc
}

// SetNillableLastBilledAt sets the "last_billed_at" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableLastBilledAt(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetLastBilledAt(*t)
	}
	return sc
}

// SetNextBillingAt sets the "next_billing_at" field.
func (sc *SubscriptionCreate) SetNextBillingAt(t time.Time) *SubscriptionCreate {
	sc.mutation.SetNextBillingAt(t)
	return sc
}

// SetNillableNextBillingAt sets the "next_billing_at" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableNextBillingAt(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetNextBillingAt(*t)
	}
	return sc
}

// SetExternalSubscriptionID sets the "external_subscription_id" field.
func (sc *SubscriptionCreate) SetExternalSubscriptionID(s string) *SubscriptionCreate {
	sc.mutation.SetExternalSubscriptionID(s)
	return sc
}

// SetNillableExternalSubscriptionID sets the "external_subscription_id" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableExternalSubscriptionID(s *string) *SubscriptionCreate {
	if s != nil {
		sc.SetExternalSubscriptionID(*s)
	}
	return sc
}

// SetPaymentID sets the "payment_id" field.
func (sc *SubscriptionCreate) SetPaymentID(s string) *SubscriptionCreate {
	sc.mutation.SetPaymentID(s)
	return sc
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillablePaymentID(s *string) *SubscriptionCreate {
	if s != nil {
		sc.SetPaymentID(*s)
	}
	return sc
}

// SetCreatedAt sets the "created_at" field.
func (sc *SubscriptionCreate) SetCreatedAt(t time.Time) *SubscriptionCreate {
	sc.mutation.SetCreatedAt(t)
	return sc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableCreatedAt(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetCreatedAt(*t)
	}
	return sc
}

// SetUpdatedAt sets the "updated_at" field.
func (sc *SubscriptionCreate) SetUpdatedAt(t time.Time) *SubscriptionCreate {
	sc.mutation.SetUpdatedAt(t)
	return sc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableUpdatedAt(t *time.Time) *SubscriptionCreate {
	if t != nil {
		sc.SetUpdatedAt(*t)
	}
	return sc
}

// SetID sets the "id" field.
func (sc *SubscriptionCreate) SetID(u uuid.UUID) *SubscriptionCreate {
	sc.mutation.SetID(u)
	return sc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (sc *SubscriptionCreate) SetNillableID(u *uuid.UUID) *SubscriptionCreate {
	if u != nil {
		sc.SetID(*u)
	}
	return sc
}

// Mutation returns the SubscriptionMutation object of the builder.
func (sc *SubscriptionCreate) Mutation() *SubscriptionMutation {
	return sc.mutation
}

// Save creates the Subscription in the database.
func (sc *SubscriptionCreate) Save(ctx context.Context) (*Subscription, error) {
	sc.defaults()
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *SubscriptionCreate) SaveX(ctx context.Context) *Subscription {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *SubscriptionCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *SubscriptionCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sc *SubscriptionCreate) defaults() {
	if _, ok := sc.mutation.Status(); !ok {
		v := subscription.DefaultStatus
		sc.mutation.SetStatus(v)
	}
	if _, ok := sc.mutation.CancelAtPeriodEnd(); !ok {
		v := subscription.DefaultCancelAtPeriodEnd
		sc.mutation.SetCancelAtPeriodEnd(v)
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		v := subscription.DefaultCreatedAt()
		sc.mutation.SetCreatedAt(v)
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		v := subscription.DefaultUpdatedAt()
		sc.mutation.SetUpdatedAt(v)
	}
	if _, ok := sc.mutation.ID(); !ok {
		v := subscription.DefaultID()
		sc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (sc *SubscriptionCreate) check() error {
	if _, ok := sc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Subscription.user_id"`)}
	}
	if _, ok := sc.mutation.PlanID(); !ok {
		return &ValidationError{Name: "plan_id", err: errors.New(`ent: missing required field "Subscription.plan_id"`)}
	}
	if _, ok := sc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Subscription.status"`)}
	}
	if v, ok := sc.mutation.Status(); ok {
		if err := subscription.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Subscription.status": %w`, err)}
		}
	}
	if _, ok := sc.mutation.CurrentPeriodStart(); !ok {
		return &ValidationError{Name: "current_period_start", err: errors.New(`ent: missing required field "Subscription.current_period_start"`)}
	}
	if _, ok := sc.mutation.CurrentPeriodEnd(); !ok {
		return &ValidationError{Name: "current_period_end", err: errors.New(`ent: missing required field "Subscription.current_period_end"`)}
	}
	if _, ok := sc.mutation.CancelAtPeriodEnd(); !ok {
		return &ValidationError{Name: "cancel_at_period_end", err: errors.New(`ent: missing required field "Subscription.cancel_at_period_end"`)}
	}
	if _, ok := sc.mutation.PaymentMethod(); !ok {
		return &ValidationError{Name: "payment_method", err: errors.New(`ent: missing required field "Subscription.payment_method"`)}
	}
	if v, ok := sc.mutation.PaymentMethod(); ok {
		if err := subscription.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Subscription.payment_method": %w`, err)}
		}
	}
	if v, ok := sc.mutation.ExternalSubscriptionID(); ok {
		if err := subscription.ExternalSubscriptionIDValidator(v); err != nil {
			return &ValidationError{Name: "external_subscription_id", err: fmt.Errorf(`ent: validator failed for field "Subscription.external_subscription_id": %w`, err)}
		}
	}
	if v, ok := sc.mutation.PaymentID(); ok {
		if err := subscription.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "Subscription.payment_id": %w`, err)}
		}
	}
	if _, ok := sc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Subscription.created_at"`)}
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Subscription.updated_at"`)}
	}
	return nil
}

func (sc *SubscriptionCreate) sqlSave(ctx context.Context) (*Subscription, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *SubscriptionCreate) createSpec() (*Subscription, *sqlgraph.CreateSpec) {
	var (
		_node = &Subscription{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(subscription.Table, sqlgraph.NewFieldSpec(subscription.FieldID, field.TypeUUID))
	)
	if id, ok := sc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := sc.mutation.UserID(); ok {
		_spec.SetField(subscription.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := sc.mutation.PlanID(); ok {
		_spec.SetField(subscription.FieldPlanID, field.TypeUUID, value)
		_node.PlanID = value
	}
	if value, ok := sc.mutation.Status(); ok {
		_spec.SetField(subscription.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := sc.mutation.CurrentPeriodStart(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodStart, field.TypeTime, value)
		_node.CurrentPeriodStart = value
	}
	if value, ok := sc.mutation.CurrentPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodEnd, field.TypeTime, value)
		_node.CurrentPeriodEnd = value
	}
	if value, ok := sc.mutation.TrialStart(); ok {
		_spec.SetField(subscription.FieldTrialStart, field.TypeTime, value)
		_node.TrialStart = value
	}
	if value, ok := sc.mutation.TrialEnd(); ok {
		_spec.SetField(subscription.FieldTrialEnd, field.TypeTime, value)
		_node.TrialEnd = value
	}
	if value, ok := sc.mutation.CancelledAt(); ok {
		_spec.SetField(subscription.FieldCancelledAt, field.TypeTime, value)
		_node.CancelledAt = value
	}
	if value, ok := sc.mutation.CancelAtPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCancelAtPeriodEnd, field.TypeBool, value)
		_node.CancelAtPeriodEnd = value
	}
	if value, ok := sc.mutation.PaymentMethod(); ok {
		_spec.SetField(subscription.FieldPaymentMethod, field.TypeString, value)
		_node.PaymentMethod = value
	}
	if value, ok := sc.mutation.BillingAddress(); ok {
		_spec.SetField(subscription.FieldBillingAddress, field.TypeJSON, value)
		_node.BillingAddress = value
	}
	if value, ok := sc.mutation.Metadata(); ok {
		_spec.SetField(subscription.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := sc.mutation.LastBilledAt(); ok {
		_spec.SetField(subscription.FieldLastBilledAt, field.TypeTime, value)
		_node.LastBilledAt = value
	}
	if value, ok := sc.mutation.NextBillingAt(); ok {
		_spec.SetField(subscription.FieldNextBillingAt, field.TypeTime, value)
		_node.NextBillingAt = value
	}
	if value, ok := sc.mutation.ExternalSubscriptionID(); ok {
		_spec.SetField(subscription.FieldExternalSubscriptionID, field.TypeString, value)
		_node.ExternalSubscriptionID = value
	}
	if value, ok := sc.mutation.PaymentID(); ok {
		_spec.SetField(subscription.FieldPaymentID, field.TypeString, value)
		_node.PaymentID = value
	}
	if value, ok := sc.mutation.CreatedAt(); ok {
		_spec.SetField(subscription.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sc.mutation.UpdatedAt(); ok {
		_spec.SetField(subscription.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// SubscriptionCreateBulk is the builder for creating many Subscription entities in bulk.
type SubscriptionCreateBulk struct {
	config
	err      error
	builders []*SubscriptionCreate
}

// Save creates the Subscription entities in the database.
func (scb *SubscriptionCreateBulk) Save(ctx context.Context) ([]*Subscription, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Subscription, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SubscriptionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *SubscriptionCreateBulk) SaveX(ctx context.Context) []*Subscription {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *SubscriptionCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *SubscriptionCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}

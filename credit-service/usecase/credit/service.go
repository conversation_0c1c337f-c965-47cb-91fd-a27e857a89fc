package credit

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/usercredit"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service implements the UseCase interface
type Service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new credit service
func NewService(writeDB *ent.Client, logger logging.Logger) UseCase {
	return &Service{
		readDB:  writeDB, // For simplicity, use same client for read and write
		writeDB: writeDB,
		logger:  logger,
	}
}

// GetCreditBalance gets the credit balance for a user
func (s *Service) GetCreditBalance(ctx context.Context, userID string) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Getting credit balance for user: %s", userID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.Errorf("Invalid user ID format: %v", err)
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get user credit record
	userCredit, err := s.readDB.UserCredit.Query().
		Where(usercredit.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Create default user credit record
			userCredit, err = s.writeDB.UserCredit.Create().
				SetUserID(userUUID).
				SetCurrentCredits(0).
				SetTotalCredits(0).
				SetMonthlyUsed(0).
				SetMonthlyLimit(1000). // Default monthly limit
				SetStatus(usercredit.StatusActive).
				Save(ctx)
			if err != nil {
				s.logger.Errorf("Failed to create user credit: %v", err)
				return nil, fmt.Errorf("failed to create user credit: %w", err)
			}
		} else {
			s.logger.Errorf("Failed to get user credit: %v", err)
			return nil, fmt.Errorf("failed to get user credit: %w", err)
		}
	}

	// Calculate monthly remaining
	monthlyRemaining := userCredit.MonthlyLimit - userCredit.MonthlyUsed
	if monthlyRemaining < 0 {
		monthlyRemaining = 0
	}

	response := &models.CreditBalanceResponse{
		UserID:           userCredit.UserID.String(),
		CurrentCredits:   userCredit.CurrentCredits,
		TotalCredits:     userCredit.TotalCredits,
		MonthlyUsed:      userCredit.MonthlyUsed,
		MonthlyLimit:     userCredit.MonthlyLimit,
		MonthlyRemaining: monthlyRemaining,
		Status:           string(userCredit.Status),
		LastUpdated:      userCredit.UpdatedAt,
	}

	// Add plan information if available
	if userCredit.PlanID != "" {
		response.PlanID = &userCredit.PlanID
		if !userCredit.PlanExpiresAt.IsZero() {
			response.PlanExpiresAt = &userCredit.PlanExpiresAt
		}
	}

	// Add monthly reset date if available
	if !userCredit.MonthlyResetAt.IsZero() {
		response.MonthlyResetAt = &userCredit.MonthlyResetAt
	}

	return response, nil
}

// GetCreditHistory gets the credit history for a user
func (s *Service) GetCreditHistory(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error) {
	s.logger.Infof("Getting credit history for user: %s", req.UserID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.Errorf("Invalid user ID format: %v", err)
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	query := s.readDB.CreditTransaction.Query().
		Where(credittransaction.UserID(userUUID))

	// Apply filters
	if req.Type != "" {
		query = query.Where(credittransaction.TypeEQ(credittransaction.Type(req.Type)))
	}
	if req.ReferenceType != "" {
		query = query.Where(credittransaction.ReferenceType(req.ReferenceType))
	}
	if req.DateFrom != nil {
		query = query.Where(credittransaction.CreatedAtGTE(*req.DateFrom))
	}
	if req.DateTo != nil {
		query = query.Where(credittransaction.CreatedAtLTE(*req.DateTo))
	}

	// Count total records
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.Errorf("Failed to count transactions: %v", err)
		return nil, fmt.Errorf("failed to count transactions: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortOrder == "asc" {
		query = query.Order(ent.Asc(credittransaction.FieldCreatedAt))
	} else {
		query = query.Order(ent.Desc(credittransaction.FieldCreatedAt))
	}

	transactions, err := query.All(ctx)
	if err != nil {
		s.logger.Errorf("Failed to get transactions: %v", err)
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// Convert to response format
	var transactionResponses []models.CreditTransactionResponse
	for _, tx := range transactions {
		var processedAt *time.Time
		if !tx.ProcessedAt.IsZero() {
			processedAt = &tx.ProcessedAt
		}

		transactionResponses = append(transactionResponses, models.CreditTransactionResponse{
			BaseModel: models.BaseModel{
				ID:        tx.ID.String(),
				CreatedAt: tx.CreatedAt,
				UpdatedAt: tx.UpdatedAt,
			},
			UserID:        tx.UserID.String(),
			Type:          string(tx.Type),
			Amount:        tx.Amount,
			Description:   tx.Description,
			ReferenceID:   tx.ReferenceID,
			ReferenceType: tx.ReferenceType,
			Status:        string(tx.Status),
			ProcessedAt:   processedAt,
		})
	}

	// Calculate pagination meta
	totalPages := (total + req.Limit - 1) / req.Limit
	pagination := models.PaginationMeta{
		Page:       req.Page,
		Limit:      req.Limit,
		Total:      total,
		TotalPages: totalPages,
	}

	// Calculate summary (simplified version)
	summary := models.CreditHistorySummary{
		TotalTransactions: total,
		// TODO: Calculate other summary fields
	}

	return &models.CreditHistoryResponse{
		Transactions: transactionResponses,
		Pagination:   pagination,
		Summary:      summary,
	}, nil
}

// ValidateCredits validates if user has enough credits
func (s *Service) ValidateCredits(ctx context.Context, req *models.ValidateCreditsRequest) (*models.CreditValidationResult, error) {
	s.logger.Infof("Validating credits for user: %s", req.UserID)

	balance, err := s.GetCreditBalance(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credit balance: %w", err)
	}

	result := &models.CreditValidationResult{
		CurrentCredits:       balance.CurrentCredits,
		RequiredCredits:      req.RequiredCredits,
		HasSufficientCredits: balance.CurrentCredits >= req.RequiredCredits,
		MonthlyUsed:          balance.MonthlyUsed,
		MonthlyLimit:         balance.MonthlyLimit,
		MonthlyRemaining:     balance.MonthlyRemaining,
	}

	result.Valid = result.HasSufficientCredits && balance.MonthlyRemaining >= req.RequiredCredits

	if !result.HasSufficientCredits {
		result.Shortage = req.RequiredCredits - balance.CurrentCredits
	}

	return result, nil
}

// ReserveCredits reserves credits for a transaction
func (s *Service) ReserveCredits(ctx context.Context, req *models.ReserveCreditsRequest) (*models.CreditReservation, error) {
	s.logger.Infof("Reserving credits for user: %s", req.UserID)

	// Validate credits first
	validation, err := s.ValidateCredits(ctx, &models.ValidateCreditsRequest{
		UserID:          req.UserID,
		RequiredCredits: req.Amount,
		Purpose:         req.Purpose,
		ReferenceID:     req.ReferenceID,
		ReferenceType:   req.ReferenceType,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to validate credits: %w", err)
	}

	if !validation.Valid {
		return nil, fmt.Errorf("insufficient credits")
	}

	// Set default expiration if not provided
	expiresIn := req.ExpiresIn
	if expiresIn == 0 {
		expiresIn = 300 // 5 minutes default
	}

	expiresAt := time.Now().Add(time.Duration(expiresIn) * time.Second)

	// Create reservation - Note: this is a placeholder since CreditReservation entity might not exist
	// You would need to implement this based on your actual ent schema
	return &models.CreditReservation{
		ID:            uuid.New().String(),
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       req.Purpose,
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Status:        "reserved",
		ExpiresAt:     expiresAt,
		CreatedAt:     time.Now(),
	}, nil
}

// ConsumeCredits consumes credits from user's balance
func (s *Service) ConsumeCredits(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Consuming credits for user: %s", req.UserID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get current balance
	userCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("user credit not found")
		}
		return nil, fmt.Errorf("failed to get user credit: %w", err)
	}

	// Check if user has enough credits
	if userCredit.CurrentCredits < req.Amount {
		return nil, fmt.Errorf("insufficient credits")
	}

	// Update user credit balance
	newCurrentCredits := userCredit.CurrentCredits - req.Amount
	newMonthlyUsed := userCredit.MonthlyUsed + req.Amount

	userCredit, err = tx.UserCredit.UpdateOne(userCredit).
		SetCurrentCredits(newCurrentCredits).
		SetMonthlyUsed(newMonthlyUsed).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update user credit: %w", err)
	}

	// Create transaction record
	_, err = tx.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType(credittransaction.TypeUsage).
		SetAmount(-req.Amount). // Negative for consumption
		SetDescription(fmt.Sprintf("Credit consumption: %s", req.Purpose)).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return updated balance
	return s.GetCreditBalance(ctx, req.UserID)
}

// RefundCredits refunds credits to user's balance
func (s *Service) RefundCredits(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Refunding credits for user: %s", req.UserID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get current balance
	userCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("user credit not found")
		}
		return nil, fmt.Errorf("failed to get user credit: %w", err)
	}

	// Update user credit balance
	newCurrentCredits := userCredit.CurrentCredits + req.Amount
	newMonthlyUsed := userCredit.MonthlyUsed - req.Amount
	if newMonthlyUsed < 0 {
		newMonthlyUsed = 0
	}

	userCredit, err = tx.UserCredit.UpdateOne(userCredit).
		SetCurrentCredits(newCurrentCredits).
		SetMonthlyUsed(newMonthlyUsed).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update user credit: %w", err)
	}

	// Create transaction record
	_, err = tx.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType(credittransaction.TypeRefund).
		SetAmount(req.Amount). // Positive for refund
		SetDescription(fmt.Sprintf("Credit refund: %s", req.Reason)).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return updated balance
	return s.GetCreditBalance(ctx, req.UserID)
}

// TransferCredits transfers credits between users
func (s *Service) TransferCredits(ctx context.Context, req *models.TransferCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Transferring credits from %s to %s", req.FromUserID, req.ToUserID)

	// Parse user IDs to UUIDs
	fromUserUUID, err := uuid.Parse(req.FromUserID)
	if err != nil {
		return nil, fmt.Errorf("invalid from user ID format: %w", err)
	}
	toUserUUID, err := uuid.Parse(req.ToUserID)
	if err != nil {
		return nil, fmt.Errorf("invalid to user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get from user credit
	fromUserCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(fromUserUUID)).
		First(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get from user credit: %w", err)
	}

	// Check if from user has enough credits
	if fromUserCredit.CurrentCredits < req.Amount {
		return nil, fmt.Errorf("insufficient credits for transfer")
	}

	// Get to user credit
	toUserCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(toUserUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Create credit record for recipient
			toUserCredit, err = tx.UserCredit.Create().
				SetUserID(toUserUUID).
				SetCurrentCredits(0).
				SetTotalCredits(0).
				SetMonthlyUsed(0).
				SetMonthlyLimit(1000).
				SetStatus(usercredit.StatusActive).
				Save(ctx)
			if err != nil {
				return nil, fmt.Errorf("failed to create recipient credit record: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get to user credit: %w", err)
		}
	}

	// Update from user balance
	newFromCredits := fromUserCredit.CurrentCredits - req.Amount
	_, err = tx.UserCredit.UpdateOne(fromUserCredit).
		SetCurrentCredits(newFromCredits).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update from user credit: %w", err)
	}

	// Update to user balance
	newToCredits := toUserCredit.CurrentCredits + req.Amount
	_, err = tx.UserCredit.UpdateOne(toUserCredit).
		SetCurrentCredits(newToCredits).
		SetTotalCredits(toUserCredit.TotalCredits + req.Amount).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update to user credit: %w", err)
	}

	// Create transaction records
	_, err = tx.CreditTransaction.Create().
		SetUserID(fromUserUUID).
		SetType(credittransaction.TypeAdjustment). // Using adjustment for transfer out
		SetAmount(-req.Amount).
		SetDescription(fmt.Sprintf("Credit transfer to %s: %s", req.ToUserID, req.Reason)).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create from transaction record: %w", err)
	}

	_, err = tx.CreditTransaction.Create().
		SetUserID(toUserUUID).
		SetType(credittransaction.TypeBonus). // Using bonus for transfer in
		SetAmount(req.Amount).
		SetDescription(fmt.Sprintf("Credit transfer from %s: %s", req.FromUserID, req.Reason)).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create to transaction record: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return from user's updated balance
	return s.GetCreditBalance(ctx, req.FromUserID)
}

// AdjustCredits adjusts user's credit balance (admin function)
func (s *Service) AdjustCredits(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Adjusting credits for user: %s", req.UserID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get current balance
	userCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Create credit record if it doesn't exist
			userCredit, err = tx.UserCredit.Create().
				SetUserID(userUUID).
				SetCurrentCredits(0).
				SetTotalCredits(0).
				SetMonthlyUsed(0).
				SetMonthlyLimit(1000).
				SetStatus(usercredit.StatusActive).
				Save(ctx)
			if err != nil {
				return nil, fmt.Errorf("failed to create user credit record: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get user credit: %w", err)
		}
	}

	// Calculate new balance
	newCurrentCredits := userCredit.CurrentCredits + req.Amount
	if newCurrentCredits < 0 {
		return nil, fmt.Errorf("adjustment would result in negative balance")
	}

	// Update total credits if positive adjustment
	newTotalCredits := userCredit.TotalCredits
	if req.Amount > 0 {
		newTotalCredits += req.Amount
	}

	// Update user credit balance
	userCredit, err = tx.UserCredit.UpdateOne(userCredit).
		SetCurrentCredits(newCurrentCredits).
		SetTotalCredits(newTotalCredits).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update user credit: %w", err)
	}

	// Create transaction record
	transactionType := credittransaction.TypeAdjustment
	transactionDescription := fmt.Sprintf("Credit adjustment: %s", req.Reason)
	if req.Amount > 0 {
		transactionDescription = fmt.Sprintf("Credit addition: %s", req.Reason)
	} else {
		transactionDescription = fmt.Sprintf("Credit deduction: %s", req.Reason)
	}

	_, err = tx.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType(transactionType).
		SetAmount(req.Amount).
		SetDescription(transactionDescription).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return updated balance
	return s.GetCreditBalance(ctx, req.UserID)
}

// AddCredits adds credits to user's balance
func (s *Service) AddCredits(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Adding credits for user: %s", req.UserID)

	adjustReq := &models.AdjustCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Reason:        fmt.Sprintf("%s: %s", req.Source, req.Description),
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata:      req.Metadata,
	}

	return s.AdjustCredits(ctx, adjustReq)
}

// DeductCredits deducts credits from user's balance
func (s *Service) DeductCredits(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditBalanceResponse, error) {
	s.logger.Infof("Deducting credits for user: %s", req.UserID)

	adjustReq := &models.AdjustCreditsRequest{
		UserID:        req.UserID,
		Amount:        -req.Amount, // Negative for deduction
		Reason:        fmt.Sprintf("%s: %s", req.Purpose, req.Description),
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata:      req.Metadata,
	}

	return s.AdjustCredits(ctx, adjustReq)
}

// GetMonthlyUsage gets monthly usage for a user
func (s *Service) GetMonthlyUsage(ctx context.Context, userID string) (*models.MonthlyUsageResponse, error) {
	s.logger.Infof("Getting monthly usage for user: %s", userID)

	balance, err := s.GetCreditBalance(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credit balance: %w", err)
	}

	// Get current month
	now := time.Now()
	currentMonth := now.Format("2006-01")

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get daily usage for current month
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1)

	// Query transactions for daily usage
	transactions, err := s.readDB.CreditTransaction.Query().
		Where(
			credittransaction.UserID(userUUID),
			credittransaction.TypeEQ(credittransaction.TypeUsage),
			credittransaction.CreatedAtGTE(startOfMonth),
			credittransaction.CreatedAtLTE(endOfMonth),
		).
		All(ctx)
	if err != nil {
		s.logger.Errorf("Failed to get monthly transactions: %v", err)
		return nil, fmt.Errorf("failed to get monthly transactions: %w", err)
	}

	// Calculate daily usage
	dailyUsageMap := make(map[string]*models.DailyUsage)
	usageByCategory := make(map[string]int)
	usageByModel := make(map[string]int)

	for _, tx := range transactions {
		day := tx.CreatedAt.Format("2006-01-02")
		if dailyUsageMap[day] == nil {
			dailyUsageMap[day] = &models.DailyUsage{
				Date:         day,
				Credits:      0,
				Transactions: 0,
			}
		}
		dailyUsageMap[day].Credits += -tx.Amount // Amount is negative for usage
		dailyUsageMap[day].Transactions++

		// Aggregate by category and model (simplified)
		if tx.ReferenceType != "" {
			usageByCategory[tx.ReferenceType] += -tx.Amount
		}
		// Note: Model information would need to be stored in metadata or reference fields
	}

	// Convert to slice
	var dailyUsage []models.DailyUsage
	for _, usage := range dailyUsageMap {
		dailyUsage = append(dailyUsage, *usage)
	}

	// Calculate reset date (first day of next month)
	resetDate := startOfMonth.AddDate(0, 1, 0)

	return &models.MonthlyUsageResponse{
		UserID:           userID,
		Month:            currentMonth,
		MonthlyUsed:      balance.MonthlyUsed,
		MonthlyLimit:     balance.MonthlyLimit,
		MonthlyRemaining: balance.MonthlyRemaining,
		DailyUsage:       dailyUsage,
		UsageByCategory:  usageByCategory,
		UsageByModel:     usageByModel,
		ResetDate:        resetDate,
	}, nil
}

// ResetMonthlyUsage resets monthly usage for a user
func (s *Service) ResetMonthlyUsage(ctx context.Context, req *models.ResetMonthlyUsageRequest) (*models.MonthlyUsageResponse, error) {
	s.logger.Infof("Resetting monthly usage for user: %s", req.UserID)

	// Parse userID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get current balance
	userCredit, err := tx.UserCredit.Query().
		Where(usercredit.UserID(userUUID)).
		First(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user credit: %w", err)
	}

	// Reset monthly usage
	now := time.Now()
	nextReset := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())

	_, err = tx.UserCredit.UpdateOne(userCredit).
		SetMonthlyUsed(0).
		SetMonthlyResetAt(nextReset).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to reset monthly usage: %w", err)
	}

	// Create transaction record
	_, err = tx.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType(credittransaction.TypeAdjustment). // Using adjustment for monthly reset
		SetAmount(0).
		SetDescription(fmt.Sprintf("Monthly usage reset: %s", req.Reason)).
		SetStatus(credittransaction.StatusCompleted).
		SetProcessedAt(time.Now()).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return updated monthly usage
	return s.GetMonthlyUsage(ctx, req.UserID)
}

// Placeholder implementations for remaining methods
// These would need to be implemented based on specific business requirements

func (s *Service) GetCreditStats(ctx context.Context, req *models.CreditStatsRequest) (*models.CreditStats, error) {
	// TODO: Implement credit statistics
	return &models.CreditStats{}, nil
}

func (s *Service) GetUsageReport(ctx context.Context, req *models.UsageReportRequest) (*models.UsageReport, error) {
	// TODO: Implement usage report
	return &models.UsageReport{}, nil
}

func (s *Service) GetCreditAnalytics(ctx context.Context, userID, period string) (*models.CreditAnalyticsResponse, error) {
	// TODO: Implement credit analytics
	return &models.CreditAnalyticsResponse{}, nil
}

func (s *Service) GetUsageAnalytics(ctx context.Context, userID, period string) (*models.UsageReport, error) {
	// TODO: Implement usage analytics
	return &models.UsageReport{}, nil
}

func (s *Service) GetCreditConfig(ctx context.Context, userID string) (*models.ModelCreditConfig, error) {
	// TODO: Implement credit configuration retrieval
	return &models.ModelCreditConfig{}, nil
}

func (s *Service) GetModelCreditConfig(ctx context.Context) (*models.ModelCreditConfig, error) {
	// TODO: Implement model credit configuration retrieval
	return &models.ModelCreditConfig{}, nil
}

// Admin operations
func (s *Service) AdminListUserCredits(ctx context.Context, page, limit int) (*models.CreditHistoryResponse, error) {
	// TODO: Implement admin list user credits
	return &models.CreditHistoryResponse{}, nil
}

func (s *Service) AdminGetUserCredit(ctx context.Context, userID string) (*models.CreditBalanceResponse, error) {
	return s.GetCreditBalance(ctx, userID)
}

func (s *Service) AdminAddCredits(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditBalanceResponse, error) {
	return s.AddCredits(ctx, req)
}

func (s *Service) AdminDeductCredits(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditBalanceResponse, error) {
	return s.DeductCredits(ctx, req)
}

func (s *Service) AdminAdjustCredits(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditBalanceResponse, error) {
	return s.AdjustCredits(ctx, req)
}

func (s *Service) GetAdminAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error) {
	// TODO: Implement admin analytics
	return &models.CreditAnalyticsResponse{}, nil
}

func (s *Service) GetAdminCreditAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error) {
	// TODO: Implement admin credit analytics
	return &models.CreditAnalyticsResponse{}, nil
}

func (s *Service) GetAdminUserAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error) {
	// TODO: Implement admin user analytics
	return &models.CreditAnalyticsResponse{}, nil
}

func (s *Service) AdminGetCreditConfig(ctx context.Context) (*models.ModelCreditConfig, error) {
	return s.GetModelCreditConfig(ctx)
}

func (s *Service) AdminUpdateCreditConfig(ctx context.Context, config *models.ModelCreditConfig) (*models.ModelCreditConfig, error) {
	// TODO: Implement admin update credit configuration
	return config, nil
}

// Internal operations (delegating to public methods for now)
func (s *Service) InternalValidateCredits(ctx context.Context, req *models.ValidateCreditsRequest) (*models.CreditValidationResult, error) {
	return s.ValidateCredits(ctx, req)
}

func (s *Service) InternalReserveCredits(ctx context.Context, req *models.ReserveCreditsRequest) (*models.CreditReservation, error) {
	return s.ReserveCredits(ctx, req)
}

func (s *Service) InternalConsumeCredits(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditBalanceResponse, error) {
	return s.ConsumeCredits(ctx, req)
}

func (s *Service) InternalRefundCredits(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditBalanceResponse, error) {
	return s.RefundCredits(ctx, req)
}

func (s *Service) InternalGetCreditBalance(ctx context.Context, userID string) (*models.CreditBalanceResponse, error) {
	return s.GetCreditBalance(ctx, userID)
}

// AI Credit Validation methods

func (s *Service) ValidateCreditsForAI(ctx context.Context, req *models.ValidateAICreditsRequest) (*models.ValidateAICreditsResponse, error) {
	s.logger.Infof("Validating credits for AI operation: %s", req.Operation)

	// Get user's credit balance
	balance, err := s.GetCreditBalance(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credit balance: %w", err)
	}

	// Calculate actual cost based on operation and parameters
	actualCost := s.calculateAICost(req.Operation, req.EstimatedCost, req.Parameters)

	// Check if user has sufficient credits
	valid := balance.CurrentCredits >= actualCost && balance.MonthlyRemaining >= actualCost

	response := &models.ValidateAICreditsResponse{
		Valid:               valid,
		UserID:              req.UserID,
		Operation:           req.Operation,
		EstimatedCost:       req.EstimatedCost,
		ActualCost:          actualCost,
		CurrentCredits:      balance.CurrentCredits,
		RequiredCredits:     actualCost,
		ValidationTimestamp: time.Now().Unix(),
	}

	if !valid {
		if balance.CurrentCredits < actualCost {
			response.Shortage = actualCost - balance.CurrentCredits
		} else if balance.MonthlyRemaining < actualCost {
			response.Shortage = actualCost - balance.MonthlyRemaining
		}
	}

	// Add cost breakdown
	response.CostBreakdown = map[string]int{
		"base_cost": actualCost,
	}

	return response, nil
}

func (s *Service) ReserveCreditsForAI(ctx context.Context, req *models.ReserveAICreditsRequest) (*models.ReserveAICreditsResponse, error) {
	s.logger.Infof("Reserving credits for AI operation: %s", req.Operation)

	// Validate credits first
	validateReq := &models.ValidateAICreditsRequest{
		UserID:        req.UserID,
		Operation:     req.Operation,
		EstimatedCost: req.Credits,
		Parameters:    req.Parameters,
	}

	validation, err := s.ValidateCreditsForAI(ctx, validateReq)
	if err != nil {
		return nil, fmt.Errorf("failed to validate credits: %w", err)
	}

	if !validation.Valid {
		return &models.ReserveAICreditsResponse{
			Success:         false,
			ReservedCredits: 0,
			CurrentCredits:  validation.CurrentCredits,
			RequiredCredits: validation.RequiredCredits,
			ErrorMessage:    "Insufficient credits",
		}, nil
	}

	// Create reservation
	reservation, err := s.ReserveCredits(ctx, &models.ReserveCreditsRequest{
		UserID:        req.UserID,
		Amount:        validation.ActualCost,
		Purpose:       fmt.Sprintf("AI operation: %s", req.Operation),
		ReferenceID:   req.ReferenceID,
		ReferenceType: "ai_operation",
		ExpiresIn:     300, // 5 minutes
	})
	if err != nil {
		return nil, fmt.Errorf("failed to reserve credits: %w", err)
	}

	return &models.ReserveAICreditsResponse{
		Success:         true,
		ReservedCredits: validation.ActualCost,
		CurrentCredits:  validation.CurrentCredits,
		RequiredCredits: validation.RequiredCredits,
		ReservationID:   reservation.ID,
	}, nil
}

func (s *Service) ConfirmAIOperation(ctx context.Context, req *models.ConfirmAIOperationRequest) (*models.ConfirmAIOperationResponse, error) {
	s.logger.Infof("Confirming AI operation for user: %s", req.UserID)

	if req.Success {
		// Consume the reserved credits
		_, err := s.ConsumeCredits(ctx, &models.ConsumeCreditsRequest{
			UserID:        req.UserID,
			ReservationID: req.ReservationID,
			Amount:        req.ActualCost,
			Purpose:       fmt.Sprintf("AI operation completed: %s", req.Operation),
			ReferenceID:   req.ReservationID,
			ReferenceType: "ai_operation",
		})
		if err != nil {
			return nil, fmt.Errorf("failed to consume credits: %w", err)
		}

		return &models.ConfirmAIOperationResponse{
			Success:     true,
			ActualCost:  req.ActualCost,
			Adjustment:  req.ActualCost - req.ReservedCredits,
			OperationID: req.ReservationID,
		}, nil
	} else {
		// Refund the reserved credits
		_, err := s.RefundCredits(ctx, &models.RefundCreditsRequest{
			UserID:        req.UserID,
			Amount:        req.ReservedCredits,
			Reason:        fmt.Sprintf("AI operation failed: %s", req.ErrorMessage),
			ReferenceID:   req.ReservationID,
			ReferenceType: "ai_operation",
		})
		if err != nil {
			return nil, fmt.Errorf("failed to refund credits: %w", err)
		}

		return &models.ConfirmAIOperationResponse{
			Success:     false,
			ActualCost:  0,
			Adjustment:  -req.ReservedCredits,
			OperationID: req.ReservationID,
		}, nil
	}
}

func (s *Service) GetAIOperationQuota(ctx context.Context, req *models.GetAIOperationQuotaRequest) (*models.GetAIOperationQuotaResponse, error) {
	s.logger.Infof("Getting AI operation quota for user: %s", req.UserID)

	// Get user's credit balance
	balance, err := s.GetCreditBalance(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credit balance: %w", err)
	}

	// Create quota response (simplified implementation)
	quota := models.AIOperationQuota{
		UserID:           req.UserID,
		Operation:        req.Operation,
		DailyQuota:       balance.MonthlyLimit / 30, // Simplified daily quota
		MonthlyQuota:     balance.MonthlyLimit,
		DailyUsed:        0, // TODO: Calculate actual daily usage
		MonthlyUsed:      balance.MonthlyUsed,
		DailyRemaining:   balance.MonthlyLimit/30 - 0, // TODO: Calculate actual daily remaining
		MonthlyRemaining: balance.MonthlyRemaining,
		QuotaResetDate:   time.Now().AddDate(0, 1, 0).Format("2006-01-02"),
	}

	limits := models.AIOperationLimits{
		UserID:       req.UserID,
		PlanID:       *balance.PlanID,
		DailyLimit:   balance.MonthlyLimit / 30,
		MonthlyLimit: balance.MonthlyLimit,
		ResetDate:    time.Now().AddDate(0, 1, 0).Format("2006-01-02"),
	}

	return &models.GetAIOperationQuotaResponse{
		UserID: req.UserID,
		Quotas: []models.AIOperationQuota{quota},
		Limits: limits,
	}, nil
}

func (s *Service) UpdateAIOperationQuota(ctx context.Context, req *models.UpdateAIOperationQuotaRequest) error {
	s.logger.Infof("Updating AI operation quota for user: %s", req.UserID)

	// This would typically update quota usage tracking
	// For now, we'll just consume credits
	_, err := s.ConsumeCredits(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.CreditsUsed,
		Purpose:       fmt.Sprintf("AI operation quota update: %s", req.Operation),
		ReferenceID:   req.ReferenceID,
		ReferenceType: "ai_quota_update",
	})

	return err
}

// Helper method to calculate AI operation cost
func (s *Service) calculateAICost(operation string, estimatedCost int, parameters map[string]interface{}) int {
	switch operation {
	case "text_generation":
		// Calculate based on tokens
		if tokens, ok := parameters["max_tokens"].(float64); ok {
			// 1 credit per 100 tokens (minimum 1 credit)
			cost := int(tokens / 100)
			if cost < 1 {
				cost = 1
			}
			return cost
		}
		return estimatedCost

	case "image_generation":
		// Calculate based on size and quality
		baseCost := 5 // Base cost for image generation
		if quality, ok := parameters["quality"].(string); ok && quality == "hd" {
			baseCost *= 2 // Double cost for HD
		}
		return baseCost

	case "image_analysis":
		return 2 // Fixed cost for image analysis

	default:
		return estimatedCost
	}
}

// Helper method to get current time (useful for testing)
func (s *Service) getCurrentTime() time.Time {
	return time.Now()
}

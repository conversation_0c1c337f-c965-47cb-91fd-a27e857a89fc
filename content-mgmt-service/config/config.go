package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
	sharedconfig "github.com/social-content-ai/pkg-shared/config"
)

// Config extends the shared config with content management service specific configuration
type Config struct {
	*sharedconfig.Config
	
	// Override database config with extended version
	Database DatabaseConfig `mapstructure:"database"`
	
	// Content management service specific configurations
	Services    ServicesConfig   `mapstructure:"services"`
	Storage     StorageConfig    `mapstructure:"storage"`
	Processing  ProcessingConfig `mapstructure:"processing"`
	Features    FeaturesConfig   `mapstructure:"features"`
	RateLimit   RateLimitConfig  `mapstructure:"rate_limit"`
	CORS        CORSConfig       `mapstructure:"cors"`
	Monitoring  MonitoringConfig `mapstructure:"monitoring"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig
	
	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`
	
	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// ServicesConfig holds external service configurations
type ServicesConfig struct {
	UserService   ServiceEndpoint `mapstructure:"user_service"`
	AssetService  ServiceEndpoint `mapstructure:"asset_service"`
	AIService     ServiceEndpoint `mapstructure:"ai_service"`
	NotificationService ServiceEndpoint `mapstructure:"notification_service"`
}

// ServiceEndpoint represents a service endpoint configuration
type ServiceEndpoint struct {
	Host    string        `mapstructure:"host"`
	Port    int           `mapstructure:"port"`
	Timeout time.Duration `mapstructure:"timeout"`
	Retries int           `mapstructure:"retries"`
}

// StorageConfig holds storage configuration
type StorageConfig struct {
	Provider    string `mapstructure:"provider"`    // "s3" or "local"
	S3Config    S3Config `mapstructure:"s3"`
	LocalConfig LocalConfig `mapstructure:"local"`
}

// S3Config holds S3 storage configuration
type S3Config struct {
	Bucket    string `mapstructure:"bucket"`
	Region    string `mapstructure:"region"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	Endpoint  string `mapstructure:"endpoint"`
}

// LocalConfig holds local storage configuration
type LocalConfig struct {
	BasePath string `mapstructure:"base_path"`
}

// ProcessingConfig holds content processing configuration
type ProcessingConfig struct {
	MaxFileSize      string        `mapstructure:"max_file_size"`
	AllowedFileTypes []string      `mapstructure:"allowed_file_types"`
	ProcessingTimeout time.Duration `mapstructure:"processing_timeout"`
	WorkerCount      int           `mapstructure:"worker_count"`
	QueueSize        int           `mapstructure:"queue_size"`
}

// FeaturesConfig holds feature flags
type FeaturesConfig struct {
	EnableTemplates     bool `mapstructure:"enable_templates"`
	EnableScheduling    bool `mapstructure:"enable_scheduling"`
	EnableCollaboration bool `mapstructure:"enable_collaboration"`
	EnableVersioning    bool `mapstructure:"enable_versioning"`
	EnableAnalytics     bool `mapstructure:"enable_analytics"`
	EnableAIGeneration  bool `mapstructure:"enable_ai_generation"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled            bool `mapstructure:"enabled"`
	RequestsPerMinute  int  `mapstructure:"requests_per_minute"`
	Burst              int  `mapstructure:"burst"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
	ExposedHeaders   []string `mapstructure:"exposed_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	MetricsPort     int    `mapstructure:"metrics_port"`
	HealthCheckPath string `mapstructure:"health_check_path"`
	MetricsPath     string `mapstructure:"metrics_path"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Load environment-specific config
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	// Try to read environment-specific config
	viper.SetConfigName(env)
	if err := viper.MergeInConfig(); err != nil {
		// It's okay if environment-specific config doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read environment config: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Load shared config
	sharedCfg, err := sharedconfig.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}
	config.Config = sharedCfg

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.sqlite_path", "./data/content_mgmt_service.db")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.name", "content_mgmt_db")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// Services defaults
	viper.SetDefault("services.user_service.host", "localhost")
	viper.SetDefault("services.user_service.port", 50051)
	viper.SetDefault("services.user_service.timeout", "30s")
	viper.SetDefault("services.user_service.retries", 3)
	
	viper.SetDefault("services.asset_service.host", "localhost")
	viper.SetDefault("services.asset_service.port", 50053)
	viper.SetDefault("services.asset_service.timeout", "30s")
	viper.SetDefault("services.asset_service.retries", 3)
	
	viper.SetDefault("services.ai_service.host", "localhost")
	viper.SetDefault("services.ai_service.port", 50054)
	viper.SetDefault("services.ai_service.timeout", "60s")
	viper.SetDefault("services.ai_service.retries", 2)

	// Storage defaults
	viper.SetDefault("storage.provider", "local")
	viper.SetDefault("storage.local.base_path", "./uploads")
	viper.SetDefault("storage.s3.bucket", "content-mgmt-bucket")
	viper.SetDefault("storage.s3.region", "us-east-1")

	// Processing defaults
	viper.SetDefault("processing.max_file_size", "100MB")
	viper.SetDefault("processing.allowed_file_types", []string{"image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain"})
	viper.SetDefault("processing.processing_timeout", "5m")
	viper.SetDefault("processing.worker_count", 4)
	viper.SetDefault("processing.queue_size", 100)

	// Features defaults
	viper.SetDefault("features.enable_templates", true)
	viper.SetDefault("features.enable_scheduling", true)
	viper.SetDefault("features.enable_collaboration", true)
	viper.SetDefault("features.enable_versioning", true)
	viper.SetDefault("features.enable_analytics", true)
	viper.SetDefault("features.enable_ai_generation", true)

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.requests_per_minute", 100)
	viper.SetDefault("rate_limit.burst", 20)

	// CORS defaults
	viper.SetDefault("cors.allowed_origins", []string{"http://localhost:3000"})
	viper.SetDefault("cors.allowed_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("cors.allowed_headers", []string{"Content-Type", "Authorization", "X-Requested-With"})
	viper.SetDefault("cors.allow_credentials", true)
	viper.SetDefault("cors.max_age", 86400)

	// Monitoring defaults
	viper.SetDefault("monitoring.enabled", true)
	viper.SetDefault("monitoring.metrics_port", 9092)
	viper.SetDefault("monitoring.health_check_path", "/health")
	viper.SetDefault("monitoring.metrics_path", "/metrics")
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// GetDriverName returns the database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres"
	}
}

// GetServiceURL returns the full URL for a service
func (s *ServiceEndpoint) GetServiceURL() string {
	return fmt.Sprintf("%s:%d", s.Host, s.Port)
}

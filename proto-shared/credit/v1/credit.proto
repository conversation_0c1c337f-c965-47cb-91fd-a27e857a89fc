syntax = "proto3";

package credit.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/credit/v1;creditv1";

// UserCredit represents user's credit information
message UserCredit {
  string user_id = 1;
  int32 current_credits = 2;
  int32 total_credits = 3;
  string plan_id = 4;
  string renewal_date = 5;   // Format: YYYY-MM-DD
}

// CreditTransaction represents a credit transaction
message CreditTransaction {
  string id = 1;
  string user_id = 2;
  string type = 3;           // purchase, usage, bonus, refund
  int32 amount = 4;          // Positive for credit, negative for debit
  string description = 5;
  string reference_id = 6;   // Reference to related entity (post, generation, etc.)
  google.protobuf.Timestamp created_at = 7;
}

// GetUserCreditRequest for retrieving user credits
message GetUserCreditRequest {
  string user_id = 1;
}

// UpdateUserCreditRequest for updating user credits
message UpdateUserCreditRequest {
  string user_id = 1;
  int32 amount = 2;          // Amount to add/subtract
  string type = 3;           // transaction type
  string description = 4;
  string reference_id = 5;
}

// ValidateCreditRequest for checking if user has enough credits
message ValidateCreditRequest {
  string user_id = 1;
  int32 required_credits = 2;
}

// ValidateCreditResponse with validation result
message ValidateCreditResponse {
  bool has_sufficient_credits = 1;
  int32 current_credits = 2;
  int32 required_credits = 3;
  int32 shortage = 4;        // How many credits short (if insufficient)
}

// ListTransactionsRequest for listing credit transactions
message ListTransactionsRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string type = 3;           // Filter by transaction type
  string date_from = 4;      // Format: YYYY-MM-DD
  string date_to = 5;        // Format: YYYY-MM-DD
}

// ListTransactionsResponse with paginated transactions
message ListTransactionsResponse {
  repeated CreditTransaction transactions = 1;
  common.v1.PaginationResponse pagination = 2;
  int32 total_credits_earned = 3;
  int32 total_credits_spent = 4;
}

// AddCreditsRequest for adding credits to user account
message AddCreditsRequest {
  string user_id = 1;
  int32 amount = 2;
  string source = 3;         // purchase, bonus, referral, etc.
  string description = 4;
  string payment_id = 5;     // Optional payment reference
}

// DeductCreditsRequest for deducting credits from user account
message DeductCreditsRequest {
  string user_id = 1;
  int32 amount = 2;
  string purpose = 3;        // ai_generation, ai_improvement, template_usage
  string description = 4;
  string reference_id = 5;   // Reference to the operation
}

// Subscription represents a user subscription plan
message Subscription {
  string id = 1;
  string user_id = 2;
  string plan_id = 3;            // Plan identifier
  string plan_name = 4;          // Plan display name
  string status = 5;             // active, cancelled, expired, pending
  int32 credits_per_month = 6;   // Monthly credit allocation
  int32 price_per_month = 7;     // Monthly price in cents
  string currency = 8;           // Currency code (USD, VND, etc.)
  google.protobuf.Timestamp current_period_start = 9;
  google.protobuf.Timestamp current_period_end = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp cancelled_at = 12;
  string payment_method = 13;    // card, bank_transfer, paypal
  map<string, string> metadata = 14; // Additional subscription data
}

// PaymentMethod represents a payment method
message PaymentMethod {
  string id = 1;
  string user_id = 2;
  string type = 3;               // card, bank_transfer, paypal
  string status = 4;             // active, expired, failed
  map<string, string> details = 5; // Payment method specific details
  bool is_default = 6;           // Whether this is the default payment method
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp expires_at = 8; // For cards
}

// BankTransferPayment represents a bank transfer payment
message BankTransferPayment {
  string id = 1;
  string user_id = 2;
  string subscription_id = 3;    // Optional subscription association
  int32 amount = 4;              // Amount in cents
  string currency = 5;           // Currency code
  string status = 6;             // pending, confirmed, failed, expired
  string bank_account = 7;       // Destination bank account
  string reference_code = 8;     // Unique reference code for transfer
  string transfer_instructions = 9; // Instructions for user
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp confirmed_at = 11;
  google.protobuf.Timestamp expires_at = 12; // Payment deadline
  int32 credits_to_add = 13;     // Credits to add when confirmed
}

// CreateSubscriptionRequest for creating a subscription
message CreateSubscriptionRequest {
  string user_id = 1;
  string plan_id = 2;
  string payment_method_id = 3;  // Optional payment method
  string payment_type = 4;       // card, bank_transfer, paypal
  map<string, string> metadata = 5;
}

// CreateSubscriptionResponse with subscription info
message CreateSubscriptionResponse {
  Subscription subscription = 1;
  BankTransferPayment bank_transfer = 2; // If payment_type is bank_transfer
  string payment_url = 3;        // For card/paypal payments
  bool requires_confirmation = 4; // For bank transfers
}

// ConfirmBankTransferRequest for confirming bank transfer
message ConfirmBankTransferRequest {
  string payment_id = 1;
  string user_id = 2;            // Optional admin override
  string confirmation_code = 3;  // Bank confirmation code
  string transaction_id = 4;     // Bank transaction ID
  google.protobuf.Timestamp transfer_date = 5;
}

// ConfirmBankTransferResponse with confirmation result
message ConfirmBankTransferResponse {
  bool success = 1;
  string subscription_id = 2;    // If subscription was activated
  int32 credits_added = 3;       // Credits added to account
  string error_message = 4;      // If confirmation failed
}

// GetSubscriptionRequest for retrieving subscription
message GetSubscriptionRequest {
  string user_id = 1;
  string subscription_id = 2;    // Optional specific subscription
}

// ListSubscriptionsRequest for listing user subscriptions
message ListSubscriptionsRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string status = 3;             // Filter by status
}

// ListSubscriptionsResponse with paginated subscriptions
message ListSubscriptionsResponse {
  repeated Subscription subscriptions = 1;
  common.v1.PaginationResponse pagination = 2;
}

// CancelSubscriptionRequest for cancelling subscription
message CancelSubscriptionRequest {
  string subscription_id = 1;
  string user_id = 2;
  bool immediate = 3;            // Cancel immediately or at period end
  string reason = 4;             // Cancellation reason
}

// UpdateSubscriptionRequest for updating subscription
message UpdateSubscriptionRequest {
  string subscription_id = 1;
  string user_id = 2;
  string new_plan_id = 3;        // Optional plan change
  string payment_method_id = 4;  // Optional payment method change
}

// GetPaymentMethodsRequest for listing payment methods
message GetPaymentMethodsRequest {
  string user_id = 1;
  string type = 2;               // Optional filter by type
}

// GetPaymentMethodsResponse with payment methods
message GetPaymentMethodsResponse {
  repeated PaymentMethod payment_methods = 1;
}

// AddPaymentMethodRequest for adding payment method
message AddPaymentMethodRequest {
  string user_id = 1;
  string type = 2;               // card, bank_transfer, paypal
  map<string, string> details = 3; // Payment method details
  bool set_as_default = 4;       // Set as default payment method
}

// RemovePaymentMethodRequest for removing payment method
message RemovePaymentMethodRequest {
  string payment_method_id = 1;
  string user_id = 2;
}

// ProcessRecurringPaymentRequest for processing recurring payments
message ProcessRecurringPaymentRequest {
  string subscription_id = 1;
  bool force_process = 2;        // Force process even if not due
}

// ProcessRecurringPaymentResponse with processing result
message ProcessRecurringPaymentResponse {
  bool success = 1;
  string payment_id = 2;         // Payment transaction ID
  int32 credits_added = 3;       // Credits added to account
  google.protobuf.Timestamp next_payment_date = 4;
  string error_message = 5;      // If processing failed
}

// Empty response
message Empty {}

// CreditService provides credit management operations
service CreditService {
  // Basic credit operations
  rpc GetUserCredit(GetUserCreditRequest) returns (UserCredit);
  rpc ValidateCredit(ValidateCreditRequest) returns (ValidateCreditResponse);
  rpc AddCredits(AddCreditsRequest) returns (UserCredit);
  rpc DeductCredits(DeductCreditsRequest) returns (UserCredit);
  rpc ListTransactions(ListTransactionsRequest) returns (ListTransactionsResponse);

  // Subscription management
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  rpc GetSubscription(GetSubscriptionRequest) returns (Subscription);
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (Subscription);
  rpc CancelSubscription(CancelSubscriptionRequest) returns (Empty);

  // Payment method management
  rpc GetPaymentMethods(GetPaymentMethodsRequest) returns (GetPaymentMethodsResponse);
  rpc AddPaymentMethod(AddPaymentMethodRequest) returns (PaymentMethod);
  rpc RemovePaymentMethod(RemovePaymentMethodRequest) returns (Empty);

  // Bank transfer operations
  rpc ConfirmBankTransfer(ConfirmBankTransferRequest) returns (ConfirmBankTransferResponse);

  // Recurring payment processing
  rpc ProcessRecurringPayment(ProcessRecurringPaymentRequest) returns (ProcessRecurringPaymentResponse);
}

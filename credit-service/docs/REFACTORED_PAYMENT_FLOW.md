# Refactored Payment Flow Documentation

This document describes the new refactored payment flow that centralizes payment operations through the Factory pattern.

## New Flow Architecture

### **Create Flow:**
```
Subscription → Factory.CreatePayment → PaypalProcess.InitiatePayment → on Subscription → create subscribe
```

### **Webhook Flow:**
```
HTTP → Factory.WebHook → PaypalProcess.WebHook → data → on Factory.WebHook → push event update subscribe
```

## Implementation Details

### 1. Factory.CreatePayment Method

**Purpose**: Centralized payment creation through factory pattern
**Flow**: `Subscription → Factory.CreatePayment → PaypalProcess.InitiatePayment`

```go
// CreatePayment creates a payment through the appropriate processor
func (f *Factory) CreatePayment(ctx context.Context, processorType ProcessorType, req *PaymentRequest) (*PaymentResponse, error) {
    f.logger.WithFields(map[string]interface{}{
        "processor_type": processorType,
        "user_id":        req.UserID,
        "amount":         req.Amount,
        "currency":       req.Currency,
    }).Info("Creating payment through factory")

    // Create processor
    processor, err := f.CreateProcessor(processorType)
    if err != nil {
        return nil, fmt.Errorf("failed to create processor: %w", err)
    }

    // Process payment
    response, err := processor.ProcessPayment(ctx, req)
    if err != nil {
        return nil, fmt.Errorf("failed to process payment: %w", err)
    }

    return response, nil
}
```

### 2. Factory.WebHook Method

**Purpose**: Centralized webhook processing with event publishing
**Flow**: `HTTP → Factory.WebHook → PaypalProcess.WebHook → data → push event`

```go
// WebHook processes webhook through the appropriate processor
func (f *Factory) WebHook(ctx context.Context, processorType ProcessorType, payload []byte, signature string) (*WebhookProcessResult, error) {
    // Create processor
    processor, err := f.CreateProcessor(processorType)
    if err != nil {
        return nil, fmt.Errorf("failed to create processor: %w", err)
    }

    // Validate webhook signature
    valid, err := processor.ValidateWebhook(ctx, payload, signature)
    if err != nil || !valid {
        return nil, fmt.Errorf("invalid webhook signature")
    }

    // Process webhook
    webhookEvent, err := processor.ProcessWebhook(ctx, payload)
    if err != nil {
        return nil, fmt.Errorf("failed to process webhook: %w", err)
    }

    // Create webhook result with event data
    result := &WebhookProcessResult{
        Success:     true,
        EventID:     webhookEvent.ID,
        EventType:   webhookEvent.Type,
        EventData:   webhookEvent.Data,
        ProcessedAt: webhookEvent.CreatedAt,
    }

    // Push event for subscription update
    err = f.pushSubscriptionUpdateEvent(ctx, result)
    if err != nil {
        f.logger.WithError(err).Error("Failed to push subscription update event")
    }

    return result, nil
}
```

### 3. Enhanced PayPal Processor

**Updated ProcessPayment**: Optimized for factory flow
```go
// ProcessPayment processes a payment through PayPal
// Flow: Factory.CreatePayment -> PaypalProcess.InitiatePayment
func (p *payPalProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
    p.logger.Info("Processing PayPal payment through processor")

    // Use PayPal usecase to initiate payment
    if p.paypalUC != nil {
        return p.processPaymentWithUseCase(ctx, req)
    }

    // Fallback to mock implementation
    return p.processPaymentMock(ctx, req)
}
```

**Updated ProcessWebhook**: Returns data to factory for event processing
```go
// ProcessWebhook processes a PayPal webhook for payment flow completion
// Flow: Factory.WebHook -> PaypalProcess.WebHook -> data
func (p *payPalProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
    p.logger.Info("Processing PayPal webhook through processor")
    
    // Parse webhook payload and create event
    webhookEvent := &WebhookEvent{
        ID:        uuid.New().String(),
        Type:      eventType,
        Data:      webhookData,
        CreatedAt: time.Now().Format(time.RFC3339),
    }
    
    // Process through PayPal usecase if available
    if p.paypalUC != nil {
        _, err := p.paypalUC.HandleWebhook(ctx, paypalWebhookReq)
        // Handle error but continue
    }
    
    p.logger.Info("PayPal webhook processed, returning data to factory")
    return webhookEvent, nil
}
```

## Usage Examples

### 1. Create Payment Flow

```go
// Step 1: Subscription service creates subscription
subscription, err := subscriptionService.CreateSubscription(ctx, &CreateSubscriptionRequest{
    UserID: userID,
    PlanID: planID,
})

// Step 2: Create payment through factory
paymentReq := &PaymentRequest{
    UserID:        userID,
    Amount:        subscription.Amount,
    Currency:      "USD",
    PaymentMethod: "paypal",
    OrderID:       subscription.ID,
    Metadata: map[string]interface{}{
        "subscription_id": subscription.ID,
    },
}

// Factory.CreatePayment -> PaypalProcess.InitiatePayment
paymentResp, err := paymentFactory.CreatePayment(ctx, ProcessorTypePayPal, paymentReq)
if err != nil {
    return fmt.Errorf("failed to create payment: %w", err)
}

// Step 3: Redirect user to PayPal
redirectURL := paymentResp.PaymentURL
```

### 2. Webhook Processing Flow

```go
// HTTP webhook handler
func HandlePayPalWebhook(w http.ResponseWriter, r *http.Request) {
    // Read webhook payload
    payload, err := ioutil.ReadAll(r.Body)
    if err != nil {
        http.Error(w, "Failed to read payload", http.StatusBadRequest)
        return
    }
    
    // Get PayPal signature
    signature := r.Header.Get("PAYPAL-TRANSMISSION-SIG")
    
    // Process webhook through factory
    // HTTP -> Factory.WebHook -> PaypalProcess.WebHook -> data -> push event
    result, err := paymentFactory.WebHook(ctx, ProcessorTypePayPal, payload, signature)
    if err != nil {
        http.Error(w, "Failed to process webhook", http.StatusInternalServerError)
        return
    }
    
    // Webhook processed successfully
    // Factory automatically pushes subscription update events
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(result)
}
```

## Event Publishing System

### Subscription Update Events

The factory automatically handles subscription update events based on webhook types:

```go
func (f *Factory) pushSubscriptionUpdateEvent(ctx context.Context, result *WebhookProcessResult) error {
    switch result.EventType {
    case "PAYMENT.CAPTURE.COMPLETED":
        return f.handlePaymentCompletedEvent(ctx, result)
    case "PAYMENT.CAPTURE.DENIED":
        return f.handlePaymentFailedEvent(ctx, result)
    case "BILLING.SUBSCRIPTION.ACTIVATED":
        return f.handleSubscriptionActivatedEvent(ctx, result)
    case "BILLING.SUBSCRIPTION.CANCELLED":
        return f.handleSubscriptionCancelledEvent(ctx, result)
    }
    return nil
}
```

### Event Handlers

```go
// Payment completed -> Activate subscription
func (f *Factory) handlePaymentCompletedEvent(ctx context.Context, result *WebhookProcessResult) error {
    // TODO: Extract subscription ID from event data
    // TODO: Call subscription service to activate subscription
    // TODO: Update payment method status
    return nil
}

// Payment failed -> Suspend subscription
func (f *Factory) handlePaymentFailedEvent(ctx context.Context, result *WebhookProcessResult) error {
    // TODO: Call subscription service to handle payment failure
    return nil
}
```

## Benefits of Refactored Flow

### 1. **Centralized Control**
- All payment operations go through factory
- Consistent logging and error handling
- Single point of configuration

### 2. **Event-Driven Architecture**
- Automatic event publishing after webhook processing
- Decoupled subscription updates
- Scalable event handling

### 3. **Simplified Integration**
- Single factory interface for all payment operations
- Consistent API across different payment processors
- Easy to add new payment methods

### 4. **Better Error Handling**
- Centralized error handling in factory
- Graceful fallback for event publishing failures
- Comprehensive logging throughout the flow

### 5. **Testability**
- Easy to mock factory for testing
- Isolated testing of payment processors
- Event publishing can be tested separately

## Migration from Old Flow

### Before (Direct Processor Usage):
```go
// Old way - direct processor usage
processor, err := factory.CreateProcessor(ProcessorTypePayPal)
payment, err := processor.ProcessPayment(ctx, req)
```

### After (Factory Methods):
```go
// New way - factory methods
payment, err := factory.CreatePayment(ctx, ProcessorTypePayPal, req)
```

### Webhook Migration:
```go
// Old way - manual webhook handling
processor, _ := factory.CreateProcessor(ProcessorTypePayPal)
valid, _ := processor.ValidateWebhook(ctx, payload, signature)
event, _ := processor.ProcessWebhook(ctx, payload)
// Manual event publishing...

// New way - factory webhook handling
result, err := factory.WebHook(ctx, ProcessorTypePayPal, payload, signature)
// Automatic event publishing included
```

This refactored flow provides a more robust, scalable, and maintainable payment processing system with centralized control and automatic event handling.

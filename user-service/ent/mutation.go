// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/predicate"
	"github.com/social-content-ai/user-service/ent/session"
	"github.com/social-content-ai/user-service/ent/user"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeSession = "Session"
	TypeUser    = "User"
)

// SessionMutation represents an operation that mutates the Session nodes in the graph.
type SessionMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	device_info   *string
	status        *session.Status
	ip_address    *string
	login_time    *time.Time
	logout_time   *time.Time
	expires_at    *time.Time
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	user          *uuid.UUID
	cleareduser   bool
	done          bool
	oldValue      func(context.Context) (*Session, error)
	predicates    []predicate.Session
}

var _ ent.Mutation = (*SessionMutation)(nil)

// sessionOption allows management of the mutation configuration using functional options.
type sessionOption func(*SessionMutation)

// newSessionMutation creates new mutation for the Session entity.
func newSessionMutation(c config, op Op, opts ...sessionOption) *SessionMutation {
	m := &SessionMutation{
		config:        c,
		op:            op,
		typ:           TypeSession,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSessionID sets the ID field of the mutation.
func withSessionID(id uuid.UUID) sessionOption {
	return func(m *SessionMutation) {
		var (
			err   error
			once  sync.Once
			value *Session
		)
		m.oldValue = func(ctx context.Context) (*Session, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Session.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSession sets the old Session of the mutation.
func withSession(node *Session) sessionOption {
	return func(m *SessionMutation) {
		m.oldValue = func(context.Context) (*Session, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SessionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SessionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Session entities.
func (m *SessionMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SessionMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SessionMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Session.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *SessionMutation) SetUserID(u uuid.UUID) {
	m.user = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *SessionMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *SessionMutation) ResetUserID() {
	m.user = nil
}

// SetDeviceInfo sets the "device_info" field.
func (m *SessionMutation) SetDeviceInfo(s string) {
	m.device_info = &s
}

// DeviceInfo returns the value of the "device_info" field in the mutation.
func (m *SessionMutation) DeviceInfo() (r string, exists bool) {
	v := m.device_info
	if v == nil {
		return
	}
	return *v, true
}

// OldDeviceInfo returns the old "device_info" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldDeviceInfo(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeviceInfo is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeviceInfo requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeviceInfo: %w", err)
	}
	return oldValue.DeviceInfo, nil
}

// ClearDeviceInfo clears the value of the "device_info" field.
func (m *SessionMutation) ClearDeviceInfo() {
	m.device_info = nil
	m.clearedFields[session.FieldDeviceInfo] = struct{}{}
}

// DeviceInfoCleared returns if the "device_info" field was cleared in this mutation.
func (m *SessionMutation) DeviceInfoCleared() bool {
	_, ok := m.clearedFields[session.FieldDeviceInfo]
	return ok
}

// ResetDeviceInfo resets all changes to the "device_info" field.
func (m *SessionMutation) ResetDeviceInfo() {
	m.device_info = nil
	delete(m.clearedFields, session.FieldDeviceInfo)
}

// SetStatus sets the "status" field.
func (m *SessionMutation) SetStatus(s session.Status) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *SessionMutation) Status() (r session.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldStatus(ctx context.Context) (v session.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *SessionMutation) ResetStatus() {
	m.status = nil
}

// SetIPAddress sets the "ip_address" field.
func (m *SessionMutation) SetIPAddress(s string) {
	m.ip_address = &s
}

// IPAddress returns the value of the "ip_address" field in the mutation.
func (m *SessionMutation) IPAddress() (r string, exists bool) {
	v := m.ip_address
	if v == nil {
		return
	}
	return *v, true
}

// OldIPAddress returns the old "ip_address" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldIPAddress(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIPAddress is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIPAddress requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIPAddress: %w", err)
	}
	return oldValue.IPAddress, nil
}

// ClearIPAddress clears the value of the "ip_address" field.
func (m *SessionMutation) ClearIPAddress() {
	m.ip_address = nil
	m.clearedFields[session.FieldIPAddress] = struct{}{}
}

// IPAddressCleared returns if the "ip_address" field was cleared in this mutation.
func (m *SessionMutation) IPAddressCleared() bool {
	_, ok := m.clearedFields[session.FieldIPAddress]
	return ok
}

// ResetIPAddress resets all changes to the "ip_address" field.
func (m *SessionMutation) ResetIPAddress() {
	m.ip_address = nil
	delete(m.clearedFields, session.FieldIPAddress)
}

// SetLoginTime sets the "login_time" field.
func (m *SessionMutation) SetLoginTime(t time.Time) {
	m.login_time = &t
}

// LoginTime returns the value of the "login_time" field in the mutation.
func (m *SessionMutation) LoginTime() (r time.Time, exists bool) {
	v := m.login_time
	if v == nil {
		return
	}
	return *v, true
}

// OldLoginTime returns the old "login_time" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldLoginTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLoginTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLoginTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLoginTime: %w", err)
	}
	return oldValue.LoginTime, nil
}

// ResetLoginTime resets all changes to the "login_time" field.
func (m *SessionMutation) ResetLoginTime() {
	m.login_time = nil
}

// SetLogoutTime sets the "logout_time" field.
func (m *SessionMutation) SetLogoutTime(t time.Time) {
	m.logout_time = &t
}

// LogoutTime returns the value of the "logout_time" field in the mutation.
func (m *SessionMutation) LogoutTime() (r time.Time, exists bool) {
	v := m.logout_time
	if v == nil {
		return
	}
	return *v, true
}

// OldLogoutTime returns the old "logout_time" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldLogoutTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLogoutTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLogoutTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLogoutTime: %w", err)
	}
	return oldValue.LogoutTime, nil
}

// ClearLogoutTime clears the value of the "logout_time" field.
func (m *SessionMutation) ClearLogoutTime() {
	m.logout_time = nil
	m.clearedFields[session.FieldLogoutTime] = struct{}{}
}

// LogoutTimeCleared returns if the "logout_time" field was cleared in this mutation.
func (m *SessionMutation) LogoutTimeCleared() bool {
	_, ok := m.clearedFields[session.FieldLogoutTime]
	return ok
}

// ResetLogoutTime resets all changes to the "logout_time" field.
func (m *SessionMutation) ResetLogoutTime() {
	m.logout_time = nil
	delete(m.clearedFields, session.FieldLogoutTime)
}

// SetExpiresAt sets the "expires_at" field.
func (m *SessionMutation) SetExpiresAt(t time.Time) {
	m.expires_at = &t
}

// ExpiresAt returns the value of the "expires_at" field in the mutation.
func (m *SessionMutation) ExpiresAt() (r time.Time, exists bool) {
	v := m.expires_at
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiresAt returns the old "expires_at" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldExpiresAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiresAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiresAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiresAt: %w", err)
	}
	return oldValue.ExpiresAt, nil
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (m *SessionMutation) ClearExpiresAt() {
	m.expires_at = nil
	m.clearedFields[session.FieldExpiresAt] = struct{}{}
}

// ExpiresAtCleared returns if the "expires_at" field was cleared in this mutation.
func (m *SessionMutation) ExpiresAtCleared() bool {
	_, ok := m.clearedFields[session.FieldExpiresAt]
	return ok
}

// ResetExpiresAt resets all changes to the "expires_at" field.
func (m *SessionMutation) ResetExpiresAt() {
	m.expires_at = nil
	delete(m.clearedFields, session.FieldExpiresAt)
}

// SetCreatedAt sets the "created_at" field.
func (m *SessionMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *SessionMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *SessionMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *SessionMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *SessionMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *SessionMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearUser clears the "user" edge to the User entity.
func (m *SessionMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[session.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *SessionMutation) UserCleared() bool {
	return m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *SessionMutation) UserIDs() (ids []uuid.UUID) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *SessionMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// Where appends a list predicates to the SessionMutation builder.
func (m *SessionMutation) Where(ps ...predicate.Session) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SessionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SessionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Session, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SessionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SessionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Session).
func (m *SessionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SessionMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.user != nil {
		fields = append(fields, session.FieldUserID)
	}
	if m.device_info != nil {
		fields = append(fields, session.FieldDeviceInfo)
	}
	if m.status != nil {
		fields = append(fields, session.FieldStatus)
	}
	if m.ip_address != nil {
		fields = append(fields, session.FieldIPAddress)
	}
	if m.login_time != nil {
		fields = append(fields, session.FieldLoginTime)
	}
	if m.logout_time != nil {
		fields = append(fields, session.FieldLogoutTime)
	}
	if m.expires_at != nil {
		fields = append(fields, session.FieldExpiresAt)
	}
	if m.created_at != nil {
		fields = append(fields, session.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, session.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SessionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case session.FieldUserID:
		return m.UserID()
	case session.FieldDeviceInfo:
		return m.DeviceInfo()
	case session.FieldStatus:
		return m.Status()
	case session.FieldIPAddress:
		return m.IPAddress()
	case session.FieldLoginTime:
		return m.LoginTime()
	case session.FieldLogoutTime:
		return m.LogoutTime()
	case session.FieldExpiresAt:
		return m.ExpiresAt()
	case session.FieldCreatedAt:
		return m.CreatedAt()
	case session.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SessionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case session.FieldUserID:
		return m.OldUserID(ctx)
	case session.FieldDeviceInfo:
		return m.OldDeviceInfo(ctx)
	case session.FieldStatus:
		return m.OldStatus(ctx)
	case session.FieldIPAddress:
		return m.OldIPAddress(ctx)
	case session.FieldLoginTime:
		return m.OldLoginTime(ctx)
	case session.FieldLogoutTime:
		return m.OldLogoutTime(ctx)
	case session.FieldExpiresAt:
		return m.OldExpiresAt(ctx)
	case session.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case session.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Session field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SessionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case session.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case session.FieldDeviceInfo:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeviceInfo(v)
		return nil
	case session.FieldStatus:
		v, ok := value.(session.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case session.FieldIPAddress:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIPAddress(v)
		return nil
	case session.FieldLoginTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLoginTime(v)
		return nil
	case session.FieldLogoutTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLogoutTime(v)
		return nil
	case session.FieldExpiresAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiresAt(v)
		return nil
	case session.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case session.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Session field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SessionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SessionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SessionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Session numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SessionMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(session.FieldDeviceInfo) {
		fields = append(fields, session.FieldDeviceInfo)
	}
	if m.FieldCleared(session.FieldIPAddress) {
		fields = append(fields, session.FieldIPAddress)
	}
	if m.FieldCleared(session.FieldLogoutTime) {
		fields = append(fields, session.FieldLogoutTime)
	}
	if m.FieldCleared(session.FieldExpiresAt) {
		fields = append(fields, session.FieldExpiresAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SessionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SessionMutation) ClearField(name string) error {
	switch name {
	case session.FieldDeviceInfo:
		m.ClearDeviceInfo()
		return nil
	case session.FieldIPAddress:
		m.ClearIPAddress()
		return nil
	case session.FieldLogoutTime:
		m.ClearLogoutTime()
		return nil
	case session.FieldExpiresAt:
		m.ClearExpiresAt()
		return nil
	}
	return fmt.Errorf("unknown Session nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SessionMutation) ResetField(name string) error {
	switch name {
	case session.FieldUserID:
		m.ResetUserID()
		return nil
	case session.FieldDeviceInfo:
		m.ResetDeviceInfo()
		return nil
	case session.FieldStatus:
		m.ResetStatus()
		return nil
	case session.FieldIPAddress:
		m.ResetIPAddress()
		return nil
	case session.FieldLoginTime:
		m.ResetLoginTime()
		return nil
	case session.FieldLogoutTime:
		m.ResetLogoutTime()
		return nil
	case session.FieldExpiresAt:
		m.ResetExpiresAt()
		return nil
	case session.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case session.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Session field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SessionMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.user != nil {
		edges = append(edges, session.EdgeUser)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SessionMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case session.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SessionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SessionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SessionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareduser {
		edges = append(edges, session.EdgeUser)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SessionMutation) EdgeCleared(name string) bool {
	switch name {
	case session.EdgeUser:
		return m.cleareduser
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SessionMutation) ClearEdge(name string) error {
	switch name {
	case session.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown Session unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SessionMutation) ResetEdge(name string) error {
	switch name {
	case session.EdgeUser:
		m.ResetUser()
		return nil
	}
	return fmt.Errorf("unknown Session edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op                            Op
	typ                           string
	id                            *uuid.UUID
	full_name                     *string
	email                         *string
	phone                         *string
	avatar_url                    *string
	bio                           *string
	company                       *string
	industry                      *string
	role                          *user.Role
	password_hash                 *string
	is_verified                   *bool
	two_factor_enabled            *bool
	two_factor_secret             *string
	two_factor_backup_codes       *[]string
	appendtwo_factor_backup_codes []string
	two_factor_enabled_at         *time.Time
	notification_settings         *map[string]interface{}
	invite_code                   *string
	aff_code                      *string
	aff_joined_at                 *time.Time
	created_at                    *time.Time
	updated_at                    *time.Time
	deleted_at                    *time.Time
	clearedFields                 map[string]struct{}
	sessions                      map[uuid.UUID]struct{}
	removedsessions               map[uuid.UUID]struct{}
	clearedsessions               bool
	done                          bool
	oldValue                      func(context.Context) (*User, error)
	predicates                    []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id uuid.UUID) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of User entities.
func (m *UserMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetFullName sets the "full_name" field.
func (m *UserMutation) SetFullName(s string) {
	m.full_name = &s
}

// FullName returns the value of the "full_name" field in the mutation.
func (m *UserMutation) FullName() (r string, exists bool) {
	v := m.full_name
	if v == nil {
		return
	}
	return *v, true
}

// OldFullName returns the old "full_name" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldFullName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFullName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFullName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFullName: %w", err)
	}
	return oldValue.FullName, nil
}

// ClearFullName clears the value of the "full_name" field.
func (m *UserMutation) ClearFullName() {
	m.full_name = nil
	m.clearedFields[user.FieldFullName] = struct{}{}
}

// FullNameCleared returns if the "full_name" field was cleared in this mutation.
func (m *UserMutation) FullNameCleared() bool {
	_, ok := m.clearedFields[user.FieldFullName]
	return ok
}

// ResetFullName resets all changes to the "full_name" field.
func (m *UserMutation) ResetFullName() {
	m.full_name = nil
	delete(m.clearedFields, user.FieldFullName)
}

// SetEmail sets the "email" field.
func (m *UserMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *UserMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ResetEmail resets all changes to the "email" field.
func (m *UserMutation) ResetEmail() {
	m.email = nil
}

// SetPhone sets the "phone" field.
func (m *UserMutation) SetPhone(s string) {
	m.phone = &s
}

// Phone returns the value of the "phone" field in the mutation.
func (m *UserMutation) Phone() (r string, exists bool) {
	v := m.phone
	if v == nil {
		return
	}
	return *v, true
}

// OldPhone returns the old "phone" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPhone(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPhone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPhone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPhone: %w", err)
	}
	return oldValue.Phone, nil
}

// ClearPhone clears the value of the "phone" field.
func (m *UserMutation) ClearPhone() {
	m.phone = nil
	m.clearedFields[user.FieldPhone] = struct{}{}
}

// PhoneCleared returns if the "phone" field was cleared in this mutation.
func (m *UserMutation) PhoneCleared() bool {
	_, ok := m.clearedFields[user.FieldPhone]
	return ok
}

// ResetPhone resets all changes to the "phone" field.
func (m *UserMutation) ResetPhone() {
	m.phone = nil
	delete(m.clearedFields, user.FieldPhone)
}

// SetAvatarURL sets the "avatar_url" field.
func (m *UserMutation) SetAvatarURL(s string) {
	m.avatar_url = &s
}

// AvatarURL returns the value of the "avatar_url" field in the mutation.
func (m *UserMutation) AvatarURL() (r string, exists bool) {
	v := m.avatar_url
	if v == nil {
		return
	}
	return *v, true
}

// OldAvatarURL returns the old "avatar_url" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldAvatarURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAvatarURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAvatarURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAvatarURL: %w", err)
	}
	return oldValue.AvatarURL, nil
}

// ClearAvatarURL clears the value of the "avatar_url" field.
func (m *UserMutation) ClearAvatarURL() {
	m.avatar_url = nil
	m.clearedFields[user.FieldAvatarURL] = struct{}{}
}

// AvatarURLCleared returns if the "avatar_url" field was cleared in this mutation.
func (m *UserMutation) AvatarURLCleared() bool {
	_, ok := m.clearedFields[user.FieldAvatarURL]
	return ok
}

// ResetAvatarURL resets all changes to the "avatar_url" field.
func (m *UserMutation) ResetAvatarURL() {
	m.avatar_url = nil
	delete(m.clearedFields, user.FieldAvatarURL)
}

// SetBio sets the "bio" field.
func (m *UserMutation) SetBio(s string) {
	m.bio = &s
}

// Bio returns the value of the "bio" field in the mutation.
func (m *UserMutation) Bio() (r string, exists bool) {
	v := m.bio
	if v == nil {
		return
	}
	return *v, true
}

// OldBio returns the old "bio" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldBio(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBio is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBio requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBio: %w", err)
	}
	return oldValue.Bio, nil
}

// ClearBio clears the value of the "bio" field.
func (m *UserMutation) ClearBio() {
	m.bio = nil
	m.clearedFields[user.FieldBio] = struct{}{}
}

// BioCleared returns if the "bio" field was cleared in this mutation.
func (m *UserMutation) BioCleared() bool {
	_, ok := m.clearedFields[user.FieldBio]
	return ok
}

// ResetBio resets all changes to the "bio" field.
func (m *UserMutation) ResetBio() {
	m.bio = nil
	delete(m.clearedFields, user.FieldBio)
}

// SetCompany sets the "company" field.
func (m *UserMutation) SetCompany(s string) {
	m.company = &s
}

// Company returns the value of the "company" field in the mutation.
func (m *UserMutation) Company() (r string, exists bool) {
	v := m.company
	if v == nil {
		return
	}
	return *v, true
}

// OldCompany returns the old "company" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCompany(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCompany is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCompany requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCompany: %w", err)
	}
	return oldValue.Company, nil
}

// ClearCompany clears the value of the "company" field.
func (m *UserMutation) ClearCompany() {
	m.company = nil
	m.clearedFields[user.FieldCompany] = struct{}{}
}

// CompanyCleared returns if the "company" field was cleared in this mutation.
func (m *UserMutation) CompanyCleared() bool {
	_, ok := m.clearedFields[user.FieldCompany]
	return ok
}

// ResetCompany resets all changes to the "company" field.
func (m *UserMutation) ResetCompany() {
	m.company = nil
	delete(m.clearedFields, user.FieldCompany)
}

// SetIndustry sets the "industry" field.
func (m *UserMutation) SetIndustry(s string) {
	m.industry = &s
}

// Industry returns the value of the "industry" field in the mutation.
func (m *UserMutation) Industry() (r string, exists bool) {
	v := m.industry
	if v == nil {
		return
	}
	return *v, true
}

// OldIndustry returns the old "industry" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldIndustry(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIndustry is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIndustry requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIndustry: %w", err)
	}
	return oldValue.Industry, nil
}

// ClearIndustry clears the value of the "industry" field.
func (m *UserMutation) ClearIndustry() {
	m.industry = nil
	m.clearedFields[user.FieldIndustry] = struct{}{}
}

// IndustryCleared returns if the "industry" field was cleared in this mutation.
func (m *UserMutation) IndustryCleared() bool {
	_, ok := m.clearedFields[user.FieldIndustry]
	return ok
}

// ResetIndustry resets all changes to the "industry" field.
func (m *UserMutation) ResetIndustry() {
	m.industry = nil
	delete(m.clearedFields, user.FieldIndustry)
}

// SetRole sets the "role" field.
func (m *UserMutation) SetRole(u user.Role) {
	m.role = &u
}

// Role returns the value of the "role" field in the mutation.
func (m *UserMutation) Role() (r user.Role, exists bool) {
	v := m.role
	if v == nil {
		return
	}
	return *v, true
}

// OldRole returns the old "role" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldRole(ctx context.Context) (v user.Role, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRole is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRole requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRole: %w", err)
	}
	return oldValue.Role, nil
}

// ResetRole resets all changes to the "role" field.
func (m *UserMutation) ResetRole() {
	m.role = nil
}

// SetPasswordHash sets the "password_hash" field.
func (m *UserMutation) SetPasswordHash(s string) {
	m.password_hash = &s
}

// PasswordHash returns the value of the "password_hash" field in the mutation.
func (m *UserMutation) PasswordHash() (r string, exists bool) {
	v := m.password_hash
	if v == nil {
		return
	}
	return *v, true
}

// OldPasswordHash returns the old "password_hash" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPasswordHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPasswordHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPasswordHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPasswordHash: %w", err)
	}
	return oldValue.PasswordHash, nil
}

// ResetPasswordHash resets all changes to the "password_hash" field.
func (m *UserMutation) ResetPasswordHash() {
	m.password_hash = nil
}

// SetIsVerified sets the "is_verified" field.
func (m *UserMutation) SetIsVerified(b bool) {
	m.is_verified = &b
}

// IsVerified returns the value of the "is_verified" field in the mutation.
func (m *UserMutation) IsVerified() (r bool, exists bool) {
	v := m.is_verified
	if v == nil {
		return
	}
	return *v, true
}

// OldIsVerified returns the old "is_verified" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldIsVerified(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsVerified is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsVerified requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsVerified: %w", err)
	}
	return oldValue.IsVerified, nil
}

// ResetIsVerified resets all changes to the "is_verified" field.
func (m *UserMutation) ResetIsVerified() {
	m.is_verified = nil
}

// SetTwoFactorEnabled sets the "two_factor_enabled" field.
func (m *UserMutation) SetTwoFactorEnabled(b bool) {
	m.two_factor_enabled = &b
}

// TwoFactorEnabled returns the value of the "two_factor_enabled" field in the mutation.
func (m *UserMutation) TwoFactorEnabled() (r bool, exists bool) {
	v := m.two_factor_enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldTwoFactorEnabled returns the old "two_factor_enabled" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldTwoFactorEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTwoFactorEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTwoFactorEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTwoFactorEnabled: %w", err)
	}
	return oldValue.TwoFactorEnabled, nil
}

// ResetTwoFactorEnabled resets all changes to the "two_factor_enabled" field.
func (m *UserMutation) ResetTwoFactorEnabled() {
	m.two_factor_enabled = nil
}

// SetTwoFactorSecret sets the "two_factor_secret" field.
func (m *UserMutation) SetTwoFactorSecret(s string) {
	m.two_factor_secret = &s
}

// TwoFactorSecret returns the value of the "two_factor_secret" field in the mutation.
func (m *UserMutation) TwoFactorSecret() (r string, exists bool) {
	v := m.two_factor_secret
	if v == nil {
		return
	}
	return *v, true
}

// OldTwoFactorSecret returns the old "two_factor_secret" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldTwoFactorSecret(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTwoFactorSecret is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTwoFactorSecret requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTwoFactorSecret: %w", err)
	}
	return oldValue.TwoFactorSecret, nil
}

// ClearTwoFactorSecret clears the value of the "two_factor_secret" field.
func (m *UserMutation) ClearTwoFactorSecret() {
	m.two_factor_secret = nil
	m.clearedFields[user.FieldTwoFactorSecret] = struct{}{}
}

// TwoFactorSecretCleared returns if the "two_factor_secret" field was cleared in this mutation.
func (m *UserMutation) TwoFactorSecretCleared() bool {
	_, ok := m.clearedFields[user.FieldTwoFactorSecret]
	return ok
}

// ResetTwoFactorSecret resets all changes to the "two_factor_secret" field.
func (m *UserMutation) ResetTwoFactorSecret() {
	m.two_factor_secret = nil
	delete(m.clearedFields, user.FieldTwoFactorSecret)
}

// SetTwoFactorBackupCodes sets the "two_factor_backup_codes" field.
func (m *UserMutation) SetTwoFactorBackupCodes(s []string) {
	m.two_factor_backup_codes = &s
	m.appendtwo_factor_backup_codes = nil
}

// TwoFactorBackupCodes returns the value of the "two_factor_backup_codes" field in the mutation.
func (m *UserMutation) TwoFactorBackupCodes() (r []string, exists bool) {
	v := m.two_factor_backup_codes
	if v == nil {
		return
	}
	return *v, true
}

// OldTwoFactorBackupCodes returns the old "two_factor_backup_codes" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldTwoFactorBackupCodes(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTwoFactorBackupCodes is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTwoFactorBackupCodes requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTwoFactorBackupCodes: %w", err)
	}
	return oldValue.TwoFactorBackupCodes, nil
}

// AppendTwoFactorBackupCodes adds s to the "two_factor_backup_codes" field.
func (m *UserMutation) AppendTwoFactorBackupCodes(s []string) {
	m.appendtwo_factor_backup_codes = append(m.appendtwo_factor_backup_codes, s...)
}

// AppendedTwoFactorBackupCodes returns the list of values that were appended to the "two_factor_backup_codes" field in this mutation.
func (m *UserMutation) AppendedTwoFactorBackupCodes() ([]string, bool) {
	if len(m.appendtwo_factor_backup_codes) == 0 {
		return nil, false
	}
	return m.appendtwo_factor_backup_codes, true
}

// ClearTwoFactorBackupCodes clears the value of the "two_factor_backup_codes" field.
func (m *UserMutation) ClearTwoFactorBackupCodes() {
	m.two_factor_backup_codes = nil
	m.appendtwo_factor_backup_codes = nil
	m.clearedFields[user.FieldTwoFactorBackupCodes] = struct{}{}
}

// TwoFactorBackupCodesCleared returns if the "two_factor_backup_codes" field was cleared in this mutation.
func (m *UserMutation) TwoFactorBackupCodesCleared() bool {
	_, ok := m.clearedFields[user.FieldTwoFactorBackupCodes]
	return ok
}

// ResetTwoFactorBackupCodes resets all changes to the "two_factor_backup_codes" field.
func (m *UserMutation) ResetTwoFactorBackupCodes() {
	m.two_factor_backup_codes = nil
	m.appendtwo_factor_backup_codes = nil
	delete(m.clearedFields, user.FieldTwoFactorBackupCodes)
}

// SetTwoFactorEnabledAt sets the "two_factor_enabled_at" field.
func (m *UserMutation) SetTwoFactorEnabledAt(t time.Time) {
	m.two_factor_enabled_at = &t
}

// TwoFactorEnabledAt returns the value of the "two_factor_enabled_at" field in the mutation.
func (m *UserMutation) TwoFactorEnabledAt() (r time.Time, exists bool) {
	v := m.two_factor_enabled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldTwoFactorEnabledAt returns the old "two_factor_enabled_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldTwoFactorEnabledAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTwoFactorEnabledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTwoFactorEnabledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTwoFactorEnabledAt: %w", err)
	}
	return oldValue.TwoFactorEnabledAt, nil
}

// ClearTwoFactorEnabledAt clears the value of the "two_factor_enabled_at" field.
func (m *UserMutation) ClearTwoFactorEnabledAt() {
	m.two_factor_enabled_at = nil
	m.clearedFields[user.FieldTwoFactorEnabledAt] = struct{}{}
}

// TwoFactorEnabledAtCleared returns if the "two_factor_enabled_at" field was cleared in this mutation.
func (m *UserMutation) TwoFactorEnabledAtCleared() bool {
	_, ok := m.clearedFields[user.FieldTwoFactorEnabledAt]
	return ok
}

// ResetTwoFactorEnabledAt resets all changes to the "two_factor_enabled_at" field.
func (m *UserMutation) ResetTwoFactorEnabledAt() {
	m.two_factor_enabled_at = nil
	delete(m.clearedFields, user.FieldTwoFactorEnabledAt)
}

// SetNotificationSettings sets the "notification_settings" field.
func (m *UserMutation) SetNotificationSettings(value map[string]interface{}) {
	m.notification_settings = &value
}

// NotificationSettings returns the value of the "notification_settings" field in the mutation.
func (m *UserMutation) NotificationSettings() (r map[string]interface{}, exists bool) {
	v := m.notification_settings
	if v == nil {
		return
	}
	return *v, true
}

// OldNotificationSettings returns the old "notification_settings" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldNotificationSettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNotificationSettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNotificationSettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNotificationSettings: %w", err)
	}
	return oldValue.NotificationSettings, nil
}

// ClearNotificationSettings clears the value of the "notification_settings" field.
func (m *UserMutation) ClearNotificationSettings() {
	m.notification_settings = nil
	m.clearedFields[user.FieldNotificationSettings] = struct{}{}
}

// NotificationSettingsCleared returns if the "notification_settings" field was cleared in this mutation.
func (m *UserMutation) NotificationSettingsCleared() bool {
	_, ok := m.clearedFields[user.FieldNotificationSettings]
	return ok
}

// ResetNotificationSettings resets all changes to the "notification_settings" field.
func (m *UserMutation) ResetNotificationSettings() {
	m.notification_settings = nil
	delete(m.clearedFields, user.FieldNotificationSettings)
}

// SetInviteCode sets the "invite_code" field.
func (m *UserMutation) SetInviteCode(s string) {
	m.invite_code = &s
}

// InviteCode returns the value of the "invite_code" field in the mutation.
func (m *UserMutation) InviteCode() (r string, exists bool) {
	v := m.invite_code
	if v == nil {
		return
	}
	return *v, true
}

// OldInviteCode returns the old "invite_code" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldInviteCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInviteCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInviteCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInviteCode: %w", err)
	}
	return oldValue.InviteCode, nil
}

// ClearInviteCode clears the value of the "invite_code" field.
func (m *UserMutation) ClearInviteCode() {
	m.invite_code = nil
	m.clearedFields[user.FieldInviteCode] = struct{}{}
}

// InviteCodeCleared returns if the "invite_code" field was cleared in this mutation.
func (m *UserMutation) InviteCodeCleared() bool {
	_, ok := m.clearedFields[user.FieldInviteCode]
	return ok
}

// ResetInviteCode resets all changes to the "invite_code" field.
func (m *UserMutation) ResetInviteCode() {
	m.invite_code = nil
	delete(m.clearedFields, user.FieldInviteCode)
}

// SetAffCode sets the "aff_code" field.
func (m *UserMutation) SetAffCode(s string) {
	m.aff_code = &s
}

// AffCode returns the value of the "aff_code" field in the mutation.
func (m *UserMutation) AffCode() (r string, exists bool) {
	v := m.aff_code
	if v == nil {
		return
	}
	return *v, true
}

// OldAffCode returns the old "aff_code" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldAffCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAffCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAffCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAffCode: %w", err)
	}
	return oldValue.AffCode, nil
}

// ClearAffCode clears the value of the "aff_code" field.
func (m *UserMutation) ClearAffCode() {
	m.aff_code = nil
	m.clearedFields[user.FieldAffCode] = struct{}{}
}

// AffCodeCleared returns if the "aff_code" field was cleared in this mutation.
func (m *UserMutation) AffCodeCleared() bool {
	_, ok := m.clearedFields[user.FieldAffCode]
	return ok
}

// ResetAffCode resets all changes to the "aff_code" field.
func (m *UserMutation) ResetAffCode() {
	m.aff_code = nil
	delete(m.clearedFields, user.FieldAffCode)
}

// SetAffJoinedAt sets the "aff_joined_at" field.
func (m *UserMutation) SetAffJoinedAt(t time.Time) {
	m.aff_joined_at = &t
}

// AffJoinedAt returns the value of the "aff_joined_at" field in the mutation.
func (m *UserMutation) AffJoinedAt() (r time.Time, exists bool) {
	v := m.aff_joined_at
	if v == nil {
		return
	}
	return *v, true
}

// OldAffJoinedAt returns the old "aff_joined_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldAffJoinedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAffJoinedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAffJoinedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAffJoinedAt: %w", err)
	}
	return oldValue.AffJoinedAt, nil
}

// ClearAffJoinedAt clears the value of the "aff_joined_at" field.
func (m *UserMutation) ClearAffJoinedAt() {
	m.aff_joined_at = nil
	m.clearedFields[user.FieldAffJoinedAt] = struct{}{}
}

// AffJoinedAtCleared returns if the "aff_joined_at" field was cleared in this mutation.
func (m *UserMutation) AffJoinedAtCleared() bool {
	_, ok := m.clearedFields[user.FieldAffJoinedAt]
	return ok
}

// ResetAffJoinedAt resets all changes to the "aff_joined_at" field.
func (m *UserMutation) ResetAffJoinedAt() {
	m.aff_joined_at = nil
	delete(m.clearedFields, user.FieldAffJoinedAt)
}

// SetCreatedAt sets the "created_at" field.
func (m *UserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *UserMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *UserMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *UserMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[user.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *UserMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[user.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *UserMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, user.FieldDeletedAt)
}

// AddSessionIDs adds the "sessions" edge to the Session entity by ids.
func (m *UserMutation) AddSessionIDs(ids ...uuid.UUID) {
	if m.sessions == nil {
		m.sessions = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.sessions[ids[i]] = struct{}{}
	}
}

// ClearSessions clears the "sessions" edge to the Session entity.
func (m *UserMutation) ClearSessions() {
	m.clearedsessions = true
}

// SessionsCleared reports if the "sessions" edge to the Session entity was cleared.
func (m *UserMutation) SessionsCleared() bool {
	return m.clearedsessions
}

// RemoveSessionIDs removes the "sessions" edge to the Session entity by IDs.
func (m *UserMutation) RemoveSessionIDs(ids ...uuid.UUID) {
	if m.removedsessions == nil {
		m.removedsessions = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.sessions, ids[i])
		m.removedsessions[ids[i]] = struct{}{}
	}
}

// RemovedSessions returns the removed IDs of the "sessions" edge to the Session entity.
func (m *UserMutation) RemovedSessionsIDs() (ids []uuid.UUID) {
	for id := range m.removedsessions {
		ids = append(ids, id)
	}
	return
}

// SessionsIDs returns the "sessions" edge IDs in the mutation.
func (m *UserMutation) SessionsIDs() (ids []uuid.UUID) {
	for id := range m.sessions {
		ids = append(ids, id)
	}
	return
}

// ResetSessions resets all changes to the "sessions" edge.
func (m *UserMutation) ResetSessions() {
	m.sessions = nil
	m.clearedsessions = false
	m.removedsessions = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 21)
	if m.full_name != nil {
		fields = append(fields, user.FieldFullName)
	}
	if m.email != nil {
		fields = append(fields, user.FieldEmail)
	}
	if m.phone != nil {
		fields = append(fields, user.FieldPhone)
	}
	if m.avatar_url != nil {
		fields = append(fields, user.FieldAvatarURL)
	}
	if m.bio != nil {
		fields = append(fields, user.FieldBio)
	}
	if m.company != nil {
		fields = append(fields, user.FieldCompany)
	}
	if m.industry != nil {
		fields = append(fields, user.FieldIndustry)
	}
	if m.role != nil {
		fields = append(fields, user.FieldRole)
	}
	if m.password_hash != nil {
		fields = append(fields, user.FieldPasswordHash)
	}
	if m.is_verified != nil {
		fields = append(fields, user.FieldIsVerified)
	}
	if m.two_factor_enabled != nil {
		fields = append(fields, user.FieldTwoFactorEnabled)
	}
	if m.two_factor_secret != nil {
		fields = append(fields, user.FieldTwoFactorSecret)
	}
	if m.two_factor_backup_codes != nil {
		fields = append(fields, user.FieldTwoFactorBackupCodes)
	}
	if m.two_factor_enabled_at != nil {
		fields = append(fields, user.FieldTwoFactorEnabledAt)
	}
	if m.notification_settings != nil {
		fields = append(fields, user.FieldNotificationSettings)
	}
	if m.invite_code != nil {
		fields = append(fields, user.FieldInviteCode)
	}
	if m.aff_code != nil {
		fields = append(fields, user.FieldAffCode)
	}
	if m.aff_joined_at != nil {
		fields = append(fields, user.FieldAffJoinedAt)
	}
	if m.created_at != nil {
		fields = append(fields, user.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, user.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, user.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldFullName:
		return m.FullName()
	case user.FieldEmail:
		return m.Email()
	case user.FieldPhone:
		return m.Phone()
	case user.FieldAvatarURL:
		return m.AvatarURL()
	case user.FieldBio:
		return m.Bio()
	case user.FieldCompany:
		return m.Company()
	case user.FieldIndustry:
		return m.Industry()
	case user.FieldRole:
		return m.Role()
	case user.FieldPasswordHash:
		return m.PasswordHash()
	case user.FieldIsVerified:
		return m.IsVerified()
	case user.FieldTwoFactorEnabled:
		return m.TwoFactorEnabled()
	case user.FieldTwoFactorSecret:
		return m.TwoFactorSecret()
	case user.FieldTwoFactorBackupCodes:
		return m.TwoFactorBackupCodes()
	case user.FieldTwoFactorEnabledAt:
		return m.TwoFactorEnabledAt()
	case user.FieldNotificationSettings:
		return m.NotificationSettings()
	case user.FieldInviteCode:
		return m.InviteCode()
	case user.FieldAffCode:
		return m.AffCode()
	case user.FieldAffJoinedAt:
		return m.AffJoinedAt()
	case user.FieldCreatedAt:
		return m.CreatedAt()
	case user.FieldUpdatedAt:
		return m.UpdatedAt()
	case user.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldFullName:
		return m.OldFullName(ctx)
	case user.FieldEmail:
		return m.OldEmail(ctx)
	case user.FieldPhone:
		return m.OldPhone(ctx)
	case user.FieldAvatarURL:
		return m.OldAvatarURL(ctx)
	case user.FieldBio:
		return m.OldBio(ctx)
	case user.FieldCompany:
		return m.OldCompany(ctx)
	case user.FieldIndustry:
		return m.OldIndustry(ctx)
	case user.FieldRole:
		return m.OldRole(ctx)
	case user.FieldPasswordHash:
		return m.OldPasswordHash(ctx)
	case user.FieldIsVerified:
		return m.OldIsVerified(ctx)
	case user.FieldTwoFactorEnabled:
		return m.OldTwoFactorEnabled(ctx)
	case user.FieldTwoFactorSecret:
		return m.OldTwoFactorSecret(ctx)
	case user.FieldTwoFactorBackupCodes:
		return m.OldTwoFactorBackupCodes(ctx)
	case user.FieldTwoFactorEnabledAt:
		return m.OldTwoFactorEnabledAt(ctx)
	case user.FieldNotificationSettings:
		return m.OldNotificationSettings(ctx)
	case user.FieldInviteCode:
		return m.OldInviteCode(ctx)
	case user.FieldAffCode:
		return m.OldAffCode(ctx)
	case user.FieldAffJoinedAt:
		return m.OldAffJoinedAt(ctx)
	case user.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case user.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case user.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldFullName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFullName(v)
		return nil
	case user.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	case user.FieldPhone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPhone(v)
		return nil
	case user.FieldAvatarURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAvatarURL(v)
		return nil
	case user.FieldBio:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBio(v)
		return nil
	case user.FieldCompany:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCompany(v)
		return nil
	case user.FieldIndustry:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIndustry(v)
		return nil
	case user.FieldRole:
		v, ok := value.(user.Role)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRole(v)
		return nil
	case user.FieldPasswordHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPasswordHash(v)
		return nil
	case user.FieldIsVerified:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsVerified(v)
		return nil
	case user.FieldTwoFactorEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTwoFactorEnabled(v)
		return nil
	case user.FieldTwoFactorSecret:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTwoFactorSecret(v)
		return nil
	case user.FieldTwoFactorBackupCodes:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTwoFactorBackupCodes(v)
		return nil
	case user.FieldTwoFactorEnabledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTwoFactorEnabledAt(v)
		return nil
	case user.FieldNotificationSettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNotificationSettings(v)
		return nil
	case user.FieldInviteCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInviteCode(v)
		return nil
	case user.FieldAffCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAffCode(v)
		return nil
	case user.FieldAffJoinedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAffJoinedAt(v)
		return nil
	case user.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case user.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case user.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(user.FieldFullName) {
		fields = append(fields, user.FieldFullName)
	}
	if m.FieldCleared(user.FieldPhone) {
		fields = append(fields, user.FieldPhone)
	}
	if m.FieldCleared(user.FieldAvatarURL) {
		fields = append(fields, user.FieldAvatarURL)
	}
	if m.FieldCleared(user.FieldBio) {
		fields = append(fields, user.FieldBio)
	}
	if m.FieldCleared(user.FieldCompany) {
		fields = append(fields, user.FieldCompany)
	}
	if m.FieldCleared(user.FieldIndustry) {
		fields = append(fields, user.FieldIndustry)
	}
	if m.FieldCleared(user.FieldTwoFactorSecret) {
		fields = append(fields, user.FieldTwoFactorSecret)
	}
	if m.FieldCleared(user.FieldTwoFactorBackupCodes) {
		fields = append(fields, user.FieldTwoFactorBackupCodes)
	}
	if m.FieldCleared(user.FieldTwoFactorEnabledAt) {
		fields = append(fields, user.FieldTwoFactorEnabledAt)
	}
	if m.FieldCleared(user.FieldNotificationSettings) {
		fields = append(fields, user.FieldNotificationSettings)
	}
	if m.FieldCleared(user.FieldInviteCode) {
		fields = append(fields, user.FieldInviteCode)
	}
	if m.FieldCleared(user.FieldAffCode) {
		fields = append(fields, user.FieldAffCode)
	}
	if m.FieldCleared(user.FieldAffJoinedAt) {
		fields = append(fields, user.FieldAffJoinedAt)
	}
	if m.FieldCleared(user.FieldDeletedAt) {
		fields = append(fields, user.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	switch name {
	case user.FieldFullName:
		m.ClearFullName()
		return nil
	case user.FieldPhone:
		m.ClearPhone()
		return nil
	case user.FieldAvatarURL:
		m.ClearAvatarURL()
		return nil
	case user.FieldBio:
		m.ClearBio()
		return nil
	case user.FieldCompany:
		m.ClearCompany()
		return nil
	case user.FieldIndustry:
		m.ClearIndustry()
		return nil
	case user.FieldTwoFactorSecret:
		m.ClearTwoFactorSecret()
		return nil
	case user.FieldTwoFactorBackupCodes:
		m.ClearTwoFactorBackupCodes()
		return nil
	case user.FieldTwoFactorEnabledAt:
		m.ClearTwoFactorEnabledAt()
		return nil
	case user.FieldNotificationSettings:
		m.ClearNotificationSettings()
		return nil
	case user.FieldInviteCode:
		m.ClearInviteCode()
		return nil
	case user.FieldAffCode:
		m.ClearAffCode()
		return nil
	case user.FieldAffJoinedAt:
		m.ClearAffJoinedAt()
		return nil
	case user.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldFullName:
		m.ResetFullName()
		return nil
	case user.FieldEmail:
		m.ResetEmail()
		return nil
	case user.FieldPhone:
		m.ResetPhone()
		return nil
	case user.FieldAvatarURL:
		m.ResetAvatarURL()
		return nil
	case user.FieldBio:
		m.ResetBio()
		return nil
	case user.FieldCompany:
		m.ResetCompany()
		return nil
	case user.FieldIndustry:
		m.ResetIndustry()
		return nil
	case user.FieldRole:
		m.ResetRole()
		return nil
	case user.FieldPasswordHash:
		m.ResetPasswordHash()
		return nil
	case user.FieldIsVerified:
		m.ResetIsVerified()
		return nil
	case user.FieldTwoFactorEnabled:
		m.ResetTwoFactorEnabled()
		return nil
	case user.FieldTwoFactorSecret:
		m.ResetTwoFactorSecret()
		return nil
	case user.FieldTwoFactorBackupCodes:
		m.ResetTwoFactorBackupCodes()
		return nil
	case user.FieldTwoFactorEnabledAt:
		m.ResetTwoFactorEnabledAt()
		return nil
	case user.FieldNotificationSettings:
		m.ResetNotificationSettings()
		return nil
	case user.FieldInviteCode:
		m.ResetInviteCode()
		return nil
	case user.FieldAffCode:
		m.ResetAffCode()
		return nil
	case user.FieldAffJoinedAt:
		m.ResetAffJoinedAt()
		return nil
	case user.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case user.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case user.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.sessions != nil {
		edges = append(edges, user.EdgeSessions)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeSessions:
		ids := make([]ent.Value, 0, len(m.sessions))
		for id := range m.sessions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedsessions != nil {
		edges = append(edges, user.EdgeSessions)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeSessions:
		ids := make([]ent.Value, 0, len(m.removedsessions))
		for id := range m.removedsessions {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedsessions {
		edges = append(edges, user.EdgeSessions)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeSessions:
		return m.clearedsessions
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeSessions:
		m.ResetSessions()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}

package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/asset-service/usecase/folder"
	"github.com/social-content-ai/pkg-shared/logging"
)

// <PERSON>older<PERSON>andler handles folder-related HTTP requests
type FolderHandler struct {
	folderUseCase folder.UseCase
	logger        logging.Logger
}

// NewFolderHandler creates a new folder handler
func NewFolderHandler(folderUseCase folder.UseCase, logger logging.Logger) *FolderHandler {
	return &FolderHandler{
		folderUseCase: folderUseCase,
		logger:        logger,
	}
}

// CreateFolder handles POST /api/v1/folders
func (h *FolderHandler) CreateFolder(c *gin.Context) {
	var req models.CreateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.UserID = userID.(string)

	response, err := h.folderUseCase.CreateFolder(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetFolder handles GET /api/v1/folders/:id
func (h *FolderHandler) GetFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.folderUseCase.GetFolder(c.Request.Context(), folderID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get folder")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListFolders handles GET /api/v1/folders
func (h *FolderHandler) ListFolders(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	parentID := c.Query("parent_id")

	req := &models.ListFoldersRequest{
		UserID:   userID.(string),
		Page:     page,
		Limit:    limit,
		ParentID: parentID,
	}

	response, err := h.folderUseCase.ListFolders(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list folders")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateFolder handles PUT /api/v1/folders/:id
func (h *FolderHandler) UpdateFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	var req models.UpdateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.FolderID = folderID
	req.UserID = userID.(string)

	response, err := h.folderUseCase.UpdateFolder(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteFolder handles DELETE /api/v1/folders/:id
func (h *FolderHandler) DeleteFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.folderUseCase.DeleteFolder(c.Request.Context(), folderID, userID.(string), false)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Folder deleted successfully"})
}

// MoveFolder handles POST /api/v1/folders/:id/move
func (h *FolderHandler) MoveFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	var req models.MoveFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.FolderID = folderID
	req.UserID = userID.(string)

	response, err := h.folderUseCase.MoveFolder(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to move folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CopyFolder handles POST /api/v1/folders/:id/copy
func (h *FolderHandler) CopyFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	var req models.CopyFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.FolderID = folderID
	req.UserID = userID.(string)

	response, err := h.folderUseCase.CopyFolder(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to copy folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetFolderAssets handles GET /api/v1/folders/:id/assets
func (h *FolderHandler) GetFolderAssets(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	response, err := h.folderUseCase.GetFolderAssets(c.Request.Context(), folderID, userID.(string), page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get folder assets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ShareFolder handles POST /api/v1/folders/:id/share
func (h *FolderHandler) ShareFolder(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	var req models.ShareFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.FolderID = folderID
	req.UserID = userID.(string)

	response, err := h.folderUseCase.ShareFolder(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to share folder")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetFolderTree handles GET /api/v1/folders/tree
func (h *FolderHandler) GetFolderTree(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	rootFolderID := c.Query("root_folder_id")

	response, err := h.folderUseCase.GetFolderTree(c.Request.Context(), userID.(string), rootFolderID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get folder tree")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetFolderBreadcrumb handles GET /api/v1/folders/:id/breadcrumb
func (h *FolderHandler) GetFolderBreadcrumb(c *gin.Context) {
	folderID := c.Param("id")
	if folderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Folder ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.folderUseCase.GetFolderBreadcrumb(c.Request.Context(), folderID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get folder breadcrumb")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the Service interface
type service struct {
	config    *Config
	providers map[string]Provider
	builder   PromptBuilder
	logger    logging.Logger
}

// NewService creates a new AI service
func NewService(config *Config, logger logging.Logger) Service {
	s := &service{
		config:    config,
		providers: make(map[string]Provider),
		builder:   NewPromptBuilder(),
		logger:    logger,
	}

	// Initialize providers based on configuration
	if config.OpenAI.Enabled {
		s.providers["openai"] = NewOpenAIProvider(&config.OpenAI, logger)
	}
	if config.Anthropic.Enabled {
		s.providers["anthropic"] = NewAnthropicProvider(&config.Anthropic, logger)
	}
	if config.Google.Enabled {
		s.providers["google"] = NewGoogleProvider(&config.Google, logger)
	}

	return s
}

// GenerateContent generates content using AI
func (s *service) GenerateContent(ctx context.Context, req *GenerationRequest) (*GenerationResult, error) {
	// Validate request
	if req.Topic == "" {
		return nil, &AIError{
			Code:    ErrCodeInvalidRequest,
			Message: "topic is required",
		}
	}

	// Get provider for model
	provider, err := s.getProviderForModel(req.Model)
	if err != nil {
		return nil, err
	}

	// Build prompt
	prompt, err := s.builder.BuildGenerationPrompt(req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to build generation prompt")
		return nil, &AIError{
			Code:    ErrCodeInvalidRequest,
			Message: "failed to build prompt",
			Details: err.Error(),
		}
	}

	// Set defaults
	maxTokens := req.MaxTokens
	if maxTokens == 0 {
		maxTokens = s.getDefaultMaxTokens(req.Length)
	}

	temperature := req.Temperature
	if temperature == 0 {
		temperature = s.config.DefaultTemperature
	}

	// Create provider request
	providerReq := &TextGenerationRequest{
		Model:       req.Model,
		Prompt:      prompt,
		MaxTokens:   maxTokens,
		Temperature: temperature,
		Metadata: map[string]string{
			"content_type": req.ContentType,
			"tone":         req.Tone,
			"length":       req.Length,
		},
	}

	// Generate content
	start := time.Now()
	response, err := provider.GenerateText(ctx, providerReq)
	if err != nil {
		s.logger.WithError(err).WithField("model", req.Model).Error("Failed to generate content")
		return nil, &AIError{
			Code:    ErrCodeProviderError,
			Message: "failed to generate content",
			Details: err.Error(),
		}
	}

	s.logger.WithFields(map[string]interface{}{
		"model":           req.Model,
		"tokens_used":     response.TokensUsed,
		"generation_time": time.Since(start).Seconds(),
	}).Info("Content generated successfully")

	// Post-process the content
	result := &GenerationResult{
		Content:      strings.TrimSpace(response.Text),
		TokensUsed:   response.TokensUsed,
		Model:        response.Model,
		FinishReason: response.FinishReason,
	}

	// Extract hashtags and suggestions (simplified implementation)
	result.Hashtags = s.extractHashtags(result.Content, req.Platforms)
	result.SuggestedImages = s.generateImageSuggestions(req.Topic, req.ContentType)

	return result, nil
}

// ImproveContent improves existing content
func (s *service) ImproveContent(ctx context.Context, req *ImprovementRequest) (*ImprovementResult, error) {
	// Validate request
	if req.OriginalText == "" {
		return nil, &AIError{
			Code:    ErrCodeInvalidRequest,
			Message: "original_text is required",
		}
	}

	// Get provider for model
	provider, err := s.getProviderForModel(req.Model)
	if err != nil {
		return nil, err
	}

	// Build improvement prompt
	prompt, err := s.builder.BuildImprovementPrompt(req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to build improvement prompt")
		return nil, &AIError{
			Code:    ErrCodeInvalidRequest,
			Message: "failed to build prompt",
			Details: err.Error(),
		}
	}

	// Set defaults
	maxTokens := req.MaxTokens
	if maxTokens == 0 {
		maxTokens = len(req.OriginalText) * 2 // Allow for expansion
		if maxTokens > 4000 {
			maxTokens = 4000
		}
	}

	temperature := req.Temperature
	if temperature == 0 {
		temperature = 0.3 // Lower temperature for improvements
	}

	// Create provider request
	providerReq := &TextGenerationRequest{
		Model:       req.Model,
		Prompt:      prompt,
		MaxTokens:   maxTokens,
		Temperature: temperature,
		Metadata: map[string]string{
			"improvement_type": req.ImprovementType,
			"target_tone":      req.TargetTone,
			"platform":         req.Platform,
		},
	}

	// Improve content
	start := time.Now()
	response, err := provider.GenerateText(ctx, providerReq)
	if err != nil {
		s.logger.WithError(err).WithField("model", req.Model).Error("Failed to improve content")
		return nil, &AIError{
			Code:    ErrCodeProviderError,
			Message: "failed to improve content",
			Details: err.Error(),
		}
	}

	// Analyze the improved text
	analysisReq := &TextAnalysisRequest{
		Text:          response.Text,
		AnalysisTypes: []string{"sentiment", "readability", "engagement"},
		Model:         req.Model,
	}

	analysis, err := provider.AnalyzeText(ctx, analysisReq)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to analyze improved text")
		// Continue without analysis
		analysis = &TextAnalysisResponse{
			SentimentScore:   0.0,
			ReadabilityScore: 5.0,
			EngagementScore:  5.0,
		}
	}

	s.logger.WithFields(map[string]interface{}{
		"model":            req.Model,
		"improvement_type": req.ImprovementType,
		"tokens_used":      response.TokensUsed,
		"processing_time":  time.Since(start).Seconds(),
	}).Info("Content improved successfully")

	result := &ImprovementResult{
		ImprovedText:        strings.TrimSpace(response.Text),
		ReadabilityScore:    analysis.ReadabilityScore,
		EngagementPotential: analysis.EngagementScore,
		SentimentScore:      analysis.SentimentScore,
		AnalysisDetails:     analysis.AnalysisDetails,
		Suggestions:         analysis.Suggestions,
		TokensUsed:          response.TokensUsed + analysis.TokensUsed,
		Model:               response.Model,
	}

	return result, nil
}

// GetAvailableModels returns available AI models
func (s *service) GetAvailableModels(ctx context.Context) ([]*ModelInfo, error) {
	var models []*ModelInfo

	// Add models from each provider
	for providerName, provider := range s.providers {
		if !provider.IsHealthy(ctx) {
			continue
		}

		supportedModels := provider.GetSupportedModels()
		for _, modelID := range supportedModels {
			model := s.getModelInfo(providerName, modelID)
			if model != nil {
				models = append(models, model)
			}
		}
	}

	return models, nil
}

// GetModelInfo returns information about a specific model
func (s *service) GetModelInfo(ctx context.Context, modelID string) (*ModelInfo, error) {
	provider, err := s.getProviderForModel(modelID)
	if err != nil {
		return nil, err
	}

	providerName := provider.GetProviderName()
	model := s.getModelInfo(providerName, modelID)
	if model == nil {
		return nil, &AIError{
			Code:    ErrCodeInvalidModel,
			Message: fmt.Sprintf("model %s not found", modelID),
		}
	}

	model.IsAvailable = provider.IsHealthy(ctx)
	return model, nil
}

// HealthCheck checks the health of AI service
func (s *service) HealthCheck(ctx context.Context) error {
	healthyProviders := 0
	totalProviders := len(s.providers)

	for name, provider := range s.providers {
		if provider.IsHealthy(ctx) {
			healthyProviders++
		} else {
			s.logger.WithField("provider", name).Warn("Provider is unhealthy")
		}
	}

	if healthyProviders == 0 {
		return &AIError{
			Code:    ErrCodeProviderError,
			Message: "no healthy providers available",
		}
	}

	s.logger.WithFields(map[string]interface{}{
		"healthy_providers": healthyProviders,
		"total_providers":   totalProviders,
	}).Info("AI service health check completed")

	return nil
}

// Helper methods

// getProviderForModel returns the provider for a given model
func (s *service) getProviderForModel(model string) (Provider, error) {
	// Simple model to provider mapping
	var providerName string
	switch {
	case strings.HasPrefix(model, "gpt-"):
		providerName = "openai"
	case strings.HasPrefix(model, "claude-"):
		providerName = "anthropic"
	case strings.HasPrefix(model, "gemini-"):
		providerName = "google"
	default:
		// Try to find any available provider
		for name := range s.providers {
			providerName = name
			break
		}
	}

	provider, exists := s.providers[providerName]
	if !exists {
		return nil, &AIError{
			Code:    ErrCodeInvalidModel,
			Message: fmt.Sprintf("no provider available for model %s", model),
		}
	}

	return provider, nil
}

// getModelInfo returns model information
func (s *service) getModelInfo(providerName, modelID string) *ModelInfo {
	// Simplified model information
	modelMap := map[string]*ModelInfo{
		"gpt-4": {
			ID:           "gpt-4",
			Name:         "GPT-4",
			Provider:     "openai",
			Type:         "text",
			MaxTokens:    8192,
			CostPerToken: 0.00003,
			Capabilities: []string{"text_generation", "text_analysis"},
			Description:  "Most capable GPT model for complex tasks",
		},
		"gpt-3.5-turbo": {
			ID:           "gpt-3.5-turbo",
			Name:         "GPT-3.5 Turbo",
			Provider:     "openai",
			Type:         "text",
			MaxTokens:    4096,
			CostPerToken: 0.000002,
			Capabilities: []string{"text_generation", "text_analysis"},
			Description:  "Fast and efficient model for most tasks",
		},
		"claude-3": {
			ID:           "claude-3",
			Name:         "Claude 3",
			Provider:     "anthropic",
			Type:         "text",
			MaxTokens:    100000,
			CostPerToken: 0.000015,
			Capabilities: []string{"text_generation", "text_analysis"},
			Description:  "Advanced reasoning and analysis capabilities",
		},
		"gemini-pro": {
			ID:           "gemini-pro",
			Name:         "Gemini Pro",
			Provider:     "google",
			Type:         "text",
			MaxTokens:    32768,
			CostPerToken: 0.000001,
			Capabilities: []string{"text_generation", "text_analysis"},
			Description:  "Google's most capable model",
		},
	}

	return modelMap[modelID]
}

// getDefaultMaxTokens returns default max tokens based on length
func (s *service) getDefaultMaxTokens(length string) int {
	switch length {
	case "short":
		return 500
	case "medium":
		return 1000
	case "long":
		return 2000
	default:
		return s.config.DefaultMaxTokens
	}
}

// extractHashtags extracts relevant hashtags from content
func (s *service) extractHashtags(content string, platforms []string) []string {
	// Simplified hashtag extraction
	// In a real implementation, you might use NLP or AI to generate relevant hashtags
	words := strings.Fields(strings.ToLower(content))
	var hashtags []string

	// Extract potential hashtag words (simplified)
	for _, word := range words {
		if len(word) > 3 && len(word) < 20 {
			// Remove punctuation and check if it's a good hashtag candidate
			cleaned := strings.Trim(word, ".,!?;:")
			if len(cleaned) > 3 {
				hashtags = append(hashtags, "#"+cleaned)
			}
		}
		if len(hashtags) >= 5 {
			break
		}
	}

	return hashtags
}

// generateImageSuggestions generates image suggestions based on topic
func (s *service) generateImageSuggestions(topic, contentType string) []string {
	// Simplified image suggestion generation
	suggestions := []string{
		fmt.Sprintf("stock photo related to %s", topic),
		fmt.Sprintf("infographic about %s", topic),
	}

	switch contentType {
	case "educational":
		suggestions = append(suggestions, "educational diagram", "tutorial screenshot")
	case "promotional":
		suggestions = append(suggestions, "product image", "promotional banner")
	case "entertainment":
		suggestions = append(suggestions, "fun illustration", "meme template")
	}

	return suggestions
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// UserCredit holds the schema definition for the UserCredit entity.
type UserCredit struct {
	ent.Schema
}

// Fields of the UserCredit.
func (UserCredit) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Unique(),
		field.Int("current_credits").
			Default(0).
			Min(0),
		field.Int("total_credits").
			Default(0).
			Min(0),
		field.String("plan_id").
			MaxLen(36).
			Optional(),
		field.String("plan_name").
			MaxLen(100).
			Optional(),
		field.Time("plan_expires_at").
			Optional(),
		field.Enum("status").
			Values("active", "suspended", "cancelled").
			Default("active"),
		field.Int("monthly_limit").
			Default(0).
			Min(0),
		field.Int("monthly_used").
			Default(0).
			Min(0),
		field.Time("monthly_reset_at").
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the UserCredit.
func (UserCredit) Edges() []ent.Edge {
	return nil
}

// Indexes of the UserCredit.
func (UserCredit) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id").Unique(),
		index.Fields("plan_id"),
		index.Fields("status"),
		index.Fields("plan_expires_at"),
		index.Fields("monthly_reset_at"),
	}
}

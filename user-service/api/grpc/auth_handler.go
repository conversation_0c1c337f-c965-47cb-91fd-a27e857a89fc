package grpc

import (
	"context"
	"strconv"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/user-service/usecase/auth"
	"github.com/social-content-ai/user-service/usecase/twofa"
)

// AuthHandler implements the AuthService gRPC server
type AuthHandler struct {
	userv1.UnimplementedAuthServiceServer
	authUC  auth.UseCase
	twofaUC twofa.UseCase
	logger  logging.Logger
}

// NewAuthHandler creates a new auth gRPC handler
func <PERSON>AuthHandler(authUC auth.UseCase, twofaUC twofa.UseCase, logger logging.Logger) *AuthHandler {
	return &AuthHandler{
		authUC:  authUC,
		twofaUC: twofaUC,
		logger:  logger,
	}
}

// Login authenticates user and returns tokens
func (h *Auth<PERSON>and<PERSON>) Login(ctx context.Context, req *userv1.LoginRequest) (*userv1.LoginResponse, error) {
	// Validate request
	if req.Email == "" {
		return nil, status.Error(codes.InvalidArgument, "email is required")
	}
	if req.Password == "" {
		return nil, status.Error(codes.InvalidArgument, "password is required")
	}

	// Convert to usecase request
	loginReq := &auth.LoginRequest{
		Email:      req.Email,
		Password:   req.Password,
		DeviceInfo: req.DeviceInfo,
		IPAddress:  req.IpAddress,
	}

	// Authenticate user
	loginResp, err := h.authUC.Login(ctx, loginReq)
	if err != nil {
		h.logger.WithError(err).WithField("email", req.Email).Error("Failed to login user")
		return nil, status.Error(codes.Unauthenticated, "authentication failed")
	}

	// Convert to proto response
	protoResp := &userv1.LoginResponse{
		AccessToken:  loginResp.AccessToken,
		RefreshToken: loginResp.RefreshToken,
		Requires_2Fa: loginResp.Requires2FA,
		SessionId:    loginResp.SessionID,
	}

	// Parse and convert ExpiresAt
	if loginResp.ExpiresAt != "" {
		if expiresAt, err := time.Parse(time.RFC3339, loginResp.ExpiresAt); err == nil {
			protoResp.ExpiresAt = timestamppb.New(expiresAt)
		}
	}

	// Convert user info if available
	if loginResp.User != nil {
		protoResp.User = h.convertUserInfoToProtoUser(loginResp.User)
	}

	return protoResp, nil
}

// RefreshToken generates new access token using refresh token
func (h *AuthHandler) RefreshToken(ctx context.Context, req *userv1.RefreshTokenRequest) (*userv1.LoginResponse, error) {
	if req.RefreshToken == "" {
		return nil, status.Error(codes.InvalidArgument, "refresh_token is required")
	}

	// Refresh token
	loginResp, err := h.authUC.RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).Error("Failed to refresh token")
		return nil, status.Error(codes.Unauthenticated, "invalid refresh token")
	}

	// Convert to proto response
	protoResp := &userv1.LoginResponse{
		AccessToken:  loginResp.AccessToken,
		RefreshToken: loginResp.RefreshToken,
		Requires_2Fa: loginResp.Requires2FA,
		SessionId:    loginResp.SessionID,
	}

	// Parse and convert ExpiresAt
	if loginResp.ExpiresAt != "" {
		if expiresAt, err := time.Parse(time.RFC3339, loginResp.ExpiresAt); err == nil {
			protoResp.ExpiresAt = timestamppb.New(expiresAt)
		}
	}

	// Convert user info if available
	if loginResp.User != nil {
		protoResp.User = h.convertUserInfoToProtoUser(loginResp.User)
	}

	return protoResp, nil
}

// Logout invalidates user session
func (h *AuthHandler) Logout(ctx context.Context, req *userv1.LogoutRequest) (*userv1.Empty, error) {
	if req.SessionId == "" {
		return nil, status.Error(codes.InvalidArgument, "session_id is required")
	}

	err := h.authUC.Logout(ctx, req.SessionId)
	if err != nil {
		h.logger.WithError(err).WithField("session_id", req.SessionId).Error("Failed to logout user")
		return nil, status.Error(codes.Internal, "logout failed")
	}

	return &userv1.Empty{}, nil
}

// ChangePassword changes user password
func (h *AuthHandler) ChangePassword(ctx context.Context, req *userv1.ChangePasswordRequest) (*userv1.Empty, error) {
	// Validate request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}
	if req.CurrentPassword == "" {
		return nil, status.Error(codes.InvalidArgument, "current_password is required")
	}
	if req.NewPassword == "" {
		return nil, status.Error(codes.InvalidArgument, "new_password is required")
	}

	// Convert to usecase request
	changeReq := &auth.ChangePasswordRequest{
		UserID:          req.UserId,
		CurrentPassword: req.CurrentPassword,
		NewPassword:     req.NewPassword,
	}

	err := h.authUC.ChangePassword(ctx, changeReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.UserId).Error("Failed to change password")
		return nil, status.Error(codes.Internal, "password change failed")
	}

	return &userv1.Empty{}, nil
}

// ResetPassword initiates password reset process
func (h *AuthHandler) ResetPassword(ctx context.Context, req *userv1.ResetPasswordRequest) (*userv1.Empty, error) {
	if req.Email == "" {
		return nil, status.Error(codes.InvalidArgument, "email is required")
	}

	err := h.authUC.ResetPassword(ctx, req.Email)
	if err != nil {
		h.logger.WithError(err).WithField("email", req.Email).Error("Failed to initiate password reset")
		return nil, status.Error(codes.Internal, "password reset failed")
	}

	return &userv1.Empty{}, nil
}

// ConfirmPasswordReset confirms password reset with token
func (h *AuthHandler) ConfirmPasswordReset(ctx context.Context, req *userv1.ConfirmPasswordResetRequest) (*userv1.Empty, error) {
	// Validate request
	if req.Token == "" {
		return nil, status.Error(codes.InvalidArgument, "token is required")
	}
	if req.NewPassword == "" {
		return nil, status.Error(codes.InvalidArgument, "new_password is required")
	}

	// Convert to usecase request
	confirmReq := &auth.ConfirmPasswordResetRequest{
		Token:       req.Token,
		NewPassword: req.NewPassword,
	}

	err := h.authUC.ConfirmPasswordReset(ctx, confirmReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm password reset")
		return nil, status.Error(codes.Internal, "password reset confirmation failed")
	}

	return &userv1.Empty{}, nil
}

// ValidateToken validates JWT token and returns user info
func (h *AuthHandler) ValidateToken(ctx context.Context, req *userv1.ValidateTokenRequest) (*userv1.ValidateTokenResponse, error) {
	if req.Token == "" {
		return nil, status.Error(codes.InvalidArgument, "token is required")
	}

	// Validate token
	result, err := h.authUC.ValidateToken(ctx, req.Token)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate token")
		return nil, status.Error(codes.Internal, "token validation failed")
	}

	// Convert to proto response
	protoResp := &userv1.ValidateTokenResponse{
		Valid:  result.Valid,
		UserId: result.UserID,
		Claims: result.Claims,
	}

	if result.ExpiresAt != "" {
		if expiresAt, err := time.Parse(time.RFC3339, result.ExpiresAt); err == nil {
			protoResp.ExpiresAt = timestamppb.New(expiresAt)
		}
	}

	return protoResp, nil
}

// convertUserInfoToProtoUser converts auth.UserInfo to userv1.User
func (h *AuthHandler) convertUserInfoToProtoUser(userInfo *auth.UserInfo) *userv1.User {
	if userInfo == nil {
		return nil
	}

	// Convert notification settings
	notificationSettings := make(map[string]string)
	if userInfo.NotificationSettings != nil {
		for k, v := range userInfo.NotificationSettings {
			if str, ok := v.(string); ok {
				notificationSettings[k] = str
			}
		}
	}

	return &userv1.User{
		Id:                   userInfo.ID,
		FullName:             userInfo.FullName,
		Email:                userInfo.Email,
		Role:                 userInfo.Role,
		IsVerified:           userInfo.IsVerified,
		TwoFactorEnabled:     userInfo.TwoFactorEnabled,
		NotificationSettings: notificationSettings,
	}
}

// SetupTwoFA sets up 2FA for a user
func (h *AuthHandler) SetupTwoFA(ctx context.Context, req *userv1.SetupTwoFARequest) (*userv1.SetupTwoFAResponse, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	setupReq := &twofa.SetupTwoFARequest{
		UserID: userID,
	}

	resp, err := h.twofaUC.SetupTwoFA(ctx, setupReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to setup 2FA")
		return nil, status.Errorf(codes.Internal, "failed to setup 2FA: %v", err)
	}

	return &userv1.SetupTwoFAResponse{
		Secret:      resp.Secret,
		QrCodeUrl:   resp.QRCodeURL,
		QrCode:      resp.QRCode,
		BackupCodes: resp.BackupCodes,
	}, nil
}

// EnableTwoFA enables 2FA for a user
func (h *AuthHandler) EnableTwoFA(ctx context.Context, req *userv1.EnableTwoFARequest) (*userv1.EnableTwoFAResponse, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	enableReq := &twofa.EnableTwoFARequest{
		UserID: userID,
		Token:  req.Token,
	}

	resp, err := h.twofaUC.EnableTwoFA(ctx, enableReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to enable 2FA")
		return nil, status.Errorf(codes.Internal, "failed to enable 2FA: %v", err)
	}

	var enabledAt *timestamppb.Timestamp
	if resp.EnabledAt != "" {
		if t, err := time.Parse(time.RFC3339, resp.EnabledAt); err == nil {
			enabledAt = timestamppb.New(t)
		}
	}

	return &userv1.EnableTwoFAResponse{
		Enabled:     resp.Enabled,
		BackupCodes: resp.BackupCodes,
		EnabledAt:   enabledAt,
	}, nil
}

// DisableTwoFA disables 2FA for a user
func (h *AuthHandler) DisableTwoFA(ctx context.Context, req *userv1.DisableTwoFARequest) (*userv1.Empty, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	disableReq := &twofa.DisableTwoFARequest{
		UserID:   userID,
		Password: req.Password,
		Token:    req.Token,
	}

	err = h.twofaUC.DisableTwoFA(ctx, disableReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to disable 2FA")
		return nil, status.Errorf(codes.Internal, "failed to disable 2FA: %v", err)
	}

	return &userv1.Empty{}, nil
}

// VerifyTwoFA verifies 2FA token during login
func (h *AuthHandler) VerifyTwoFA(ctx context.Context, req *userv1.VerifyTwoFARequest) (*userv1.VerifyTwoFAResponse, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	verifyReq := &twofa.VerifyTwoFARequest{
		UserID: userID,
		Token:  req.Token,
	}

	resp, err := h.twofaUC.VerifyTwoFA(ctx, verifyReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to verify 2FA")
		return nil, status.Errorf(codes.Internal, "failed to verify 2FA: %v", err)
	}

	return &userv1.VerifyTwoFAResponse{
		Valid:          resp.Valid,
		BackupCodeUsed: resp.BackupCodeUsed,
		RemainingCodes: int32(resp.RemainingCodes),
		Message:        resp.Message,
	}, nil
}

// GenerateBackupCodes generates new backup codes for a user
func (h *AuthHandler) GenerateBackupCodes(ctx context.Context, req *userv1.GenerateBackupCodesRequest) (*userv1.GenerateBackupCodesResponse, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	generateReq := &twofa.GenerateBackupCodesRequest{
		UserID:   userID,
		Password: req.Password,
	}

	resp, err := h.twofaUC.GenerateBackupCodes(ctx, generateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to generate backup codes")
		return nil, status.Errorf(codes.Internal, "failed to generate backup codes: %v", err)
	}

	var generatedAt *timestamppb.Timestamp
	if resp.GeneratedAt != "" {
		if t, err := time.Parse(time.RFC3339, resp.GeneratedAt); err == nil {
			generatedAt = timestamppb.New(t)
		}
	}

	return &userv1.GenerateBackupCodesResponse{
		BackupCodes: resp.BackupCodes,
		GeneratedAt: generatedAt,
	}, nil
}

// GetTwoFAStatus gets 2FA status for a user
func (h *AuthHandler) GetTwoFAStatus(ctx context.Context, req *userv1.GetTwoFAStatusRequest) (*userv1.GetTwoFAStatusResponse, error) {
	userID, err := strconv.ParseUint(req.UserId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: %v", err)
	}

	resp, err := h.twofaUC.GetTwoFAStatus(ctx, userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get 2FA status")
		return nil, status.Errorf(codes.Internal, "failed to get 2FA status: %v", err)
	}

	var enabledAt *timestamppb.Timestamp
	if resp.EnabledAt != "" {
		if t, err := time.Parse(time.RFC3339, resp.EnabledAt); err == nil {
			enabledAt = timestamppb.New(t)
		}
	}

	var lastVerificationAt *timestamppb.Timestamp
	if resp.LastVerificationAt != "" {
		if t, err := time.Parse(time.RFC3339, resp.LastVerificationAt); err == nil {
			lastVerificationAt = timestamppb.New(t)
		}
	}

	return &userv1.GetTwoFAStatusResponse{
		UserId:             req.UserId,
		TwoFaEnabled:       resp.TwoFAEnabled,
		EnabledAt:          enabledAt,
		BackupCodesCount:   int32(resp.BackupCodesCount),
		HasBackupCodes:     resp.HasBackupCodes,
		LastVerificationAt: lastVerificationAt,
	}, nil
}

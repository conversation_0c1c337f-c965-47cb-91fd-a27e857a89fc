// Code generated by ent, DO NOT EDIT.

package paymentconfirmation

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the paymentconfirmation type in the database.
	Label = "payment_confirmation"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldBankTransferID holds the string denoting the bank_transfer_id field in the database.
	FieldBankTransferID = "bank_transfer_id"
	// FieldOrderID holds the string denoting the order_id field in the database.
	FieldOrderID = "order_id"
	// FieldSubscriptionID holds the string denoting the subscription_id field in the database.
	FieldSubscriptionID = "subscription_id"
	// FieldPaymentMethod holds the string denoting the payment_method field in the database.
	FieldPaymentMethod = "payment_method"
	// FieldExternalPaymentID holds the string denoting the external_payment_id field in the database.
	FieldExternalPaymentID = "external_payment_id"
	// FieldBankTransactionID holds the string denoting the bank_transaction_id field in the database.
	FieldBankTransactionID = "bank_transaction_id"
	// FieldAmountPaid holds the string denoting the amount_paid field in the database.
	FieldAmountPaid = "amount_paid"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldConfirmedBy holds the string denoting the confirmed_by field in the database.
	FieldConfirmedBy = "confirmed_by"
	// FieldConfirmationMethod holds the string denoting the confirmation_method field in the database.
	FieldConfirmationMethod = "confirmation_method"
	// FieldConfirmationNotes holds the string denoting the confirmation_notes field in the database.
	FieldConfirmationNotes = "confirmation_notes"
	// FieldPaymentDetails holds the string denoting the payment_details field in the database.
	FieldPaymentDetails = "payment_details"
	// FieldWebhookData holds the string denoting the webhook_data field in the database.
	FieldWebhookData = "webhook_data"
	// FieldPaymentDate holds the string denoting the payment_date field in the database.
	FieldPaymentDate = "payment_date"
	// FieldConfirmedAt holds the string denoting the confirmed_at field in the database.
	FieldConfirmedAt = "confirmed_at"
	// FieldFailureReason holds the string denoting the failure_reason field in the database.
	FieldFailureReason = "failure_reason"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the paymentconfirmation in the database.
	Table = "payment_confirmations"
)

// Columns holds all SQL columns for paymentconfirmation fields.
var Columns = []string{
	FieldID,
	FieldBankTransferID,
	FieldOrderID,
	FieldSubscriptionID,
	FieldPaymentMethod,
	FieldExternalPaymentID,
	FieldBankTransactionID,
	FieldAmountPaid,
	FieldCurrency,
	FieldStatus,
	FieldConfirmedBy,
	FieldConfirmationMethod,
	FieldConfirmationNotes,
	FieldPaymentDetails,
	FieldWebhookData,
	FieldPaymentDate,
	FieldConfirmedAt,
	FieldFailureReason,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	PaymentMethodValidator func(string) error
	// ExternalPaymentIDValidator is a validator for the "external_payment_id" field. It is called by the builders before save.
	ExternalPaymentIDValidator func(string) error
	// BankTransactionIDValidator is a validator for the "bank_transaction_id" field. It is called by the builders before save.
	BankTransactionIDValidator func(string) error
	// AmountPaidValidator is a validator for the "amount_paid" field. It is called by the builders before save.
	AmountPaidValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// DefaultConfirmationMethod holds the default value on creation for the "confirmation_method" field.
	DefaultConfirmationMethod string
	// ConfirmationMethodValidator is a validator for the "confirmation_method" field. It is called by the builders before save.
	ConfirmationMethodValidator func(string) error
	// FailureReasonValidator is a validator for the "failure_reason" field. It is called by the builders before save.
	FailureReasonValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending   Status = "pending"
	StatusConfirmed Status = "confirmed"
	StatusFailed    Status = "failed"
	StatusDisputed  Status = "disputed"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusConfirmed, StatusFailed, StatusDisputed:
		return nil
	default:
		return fmt.Errorf("paymentconfirmation: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the PaymentConfirmation queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByBankTransferID orders the results by the bank_transfer_id field.
func ByBankTransferID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankTransferID, opts...).ToFunc()
}

// ByOrderID orders the results by the order_id field.
func ByOrderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderID, opts...).ToFunc()
}

// BySubscriptionID orders the results by the subscription_id field.
func BySubscriptionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSubscriptionID, opts...).ToFunc()
}

// ByPaymentMethod orders the results by the payment_method field.
func ByPaymentMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentMethod, opts...).ToFunc()
}

// ByExternalPaymentID orders the results by the external_payment_id field.
func ByExternalPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExternalPaymentID, opts...).ToFunc()
}

// ByBankTransactionID orders the results by the bank_transaction_id field.
func ByBankTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBankTransactionID, opts...).ToFunc()
}

// ByAmountPaid orders the results by the amount_paid field.
func ByAmountPaid(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmountPaid, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByConfirmedBy orders the results by the confirmed_by field.
func ByConfirmedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmedBy, opts...).ToFunc()
}

// ByConfirmationMethod orders the results by the confirmation_method field.
func ByConfirmationMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmationMethod, opts...).ToFunc()
}

// ByConfirmationNotes orders the results by the confirmation_notes field.
func ByConfirmationNotes(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmationNotes, opts...).ToFunc()
}

// ByPaymentDate orders the results by the payment_date field.
func ByPaymentDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentDate, opts...).ToFunc()
}

// ByConfirmedAt orders the results by the confirmed_at field.
func ByConfirmedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmedAt, opts...).ToFunc()
}

// ByFailureReason orders the results by the failure_reason field.
func ByFailureReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailureReason, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// Code generated by ent, DO NOT EDIT.

package subscription

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldUserID, v))
}

// PlanID applies equality check predicate on the "plan_id" field. It's identical to PlanIDEQ.
func PlanID(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldPlanID, v))
}

// CurrentPeriodStart applies equality check predicate on the "current_period_start" field. It's identical to CurrentPeriodStartEQ.
func CurrentPeriodStart(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCurrentPeriodStart, v))
}

// CurrentPeriodEnd applies equality check predicate on the "current_period_end" field. It's identical to CurrentPeriodEndEQ.
func CurrentPeriodEnd(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCurrentPeriodEnd, v))
}

// TrialStart applies equality check predicate on the "trial_start" field. It's identical to TrialStartEQ.
func TrialStart(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldTrialStart, v))
}

// TrialEnd applies equality check predicate on the "trial_end" field. It's identical to TrialEndEQ.
func TrialEnd(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldTrialEnd, v))
}

// CancelledAt applies equality check predicate on the "cancelled_at" field. It's identical to CancelledAtEQ.
func CancelledAt(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCancelledAt, v))
}

// CancelAtPeriodEnd applies equality check predicate on the "cancel_at_period_end" field. It's identical to CancelAtPeriodEndEQ.
func CancelAtPeriodEnd(v bool) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCancelAtPeriodEnd, v))
}

// PaymentMethod applies equality check predicate on the "payment_method" field. It's identical to PaymentMethodEQ.
func PaymentMethod(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldPaymentMethod, v))
}

// LastBilledAt applies equality check predicate on the "last_billed_at" field. It's identical to LastBilledAtEQ.
func LastBilledAt(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldLastBilledAt, v))
}

// NextBillingAt applies equality check predicate on the "next_billing_at" field. It's identical to NextBillingAtEQ.
func NextBillingAt(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldNextBillingAt, v))
}

// ExternalSubscriptionID applies equality check predicate on the "external_subscription_id" field. It's identical to ExternalSubscriptionIDEQ.
func ExternalSubscriptionID(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldExternalSubscriptionID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldUserID, v))
}

// PlanIDEQ applies the EQ predicate on the "plan_id" field.
func PlanIDEQ(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldPlanID, v))
}

// PlanIDNEQ applies the NEQ predicate on the "plan_id" field.
func PlanIDNEQ(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldPlanID, v))
}

// PlanIDIn applies the In predicate on the "plan_id" field.
func PlanIDIn(vs ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldPlanID, vs...))
}

// PlanIDNotIn applies the NotIn predicate on the "plan_id" field.
func PlanIDNotIn(vs ...uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldPlanID, vs...))
}

// PlanIDGT applies the GT predicate on the "plan_id" field.
func PlanIDGT(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldPlanID, v))
}

// PlanIDGTE applies the GTE predicate on the "plan_id" field.
func PlanIDGTE(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldPlanID, v))
}

// PlanIDLT applies the LT predicate on the "plan_id" field.
func PlanIDLT(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldPlanID, v))
}

// PlanIDLTE applies the LTE predicate on the "plan_id" field.
func PlanIDLTE(v uuid.UUID) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldPlanID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldStatus, vs...))
}

// CurrentPeriodStartEQ applies the EQ predicate on the "current_period_start" field.
func CurrentPeriodStartEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCurrentPeriodStart, v))
}

// CurrentPeriodStartNEQ applies the NEQ predicate on the "current_period_start" field.
func CurrentPeriodStartNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldCurrentPeriodStart, v))
}

// CurrentPeriodStartIn applies the In predicate on the "current_period_start" field.
func CurrentPeriodStartIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldCurrentPeriodStart, vs...))
}

// CurrentPeriodStartNotIn applies the NotIn predicate on the "current_period_start" field.
func CurrentPeriodStartNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldCurrentPeriodStart, vs...))
}

// CurrentPeriodStartGT applies the GT predicate on the "current_period_start" field.
func CurrentPeriodStartGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldCurrentPeriodStart, v))
}

// CurrentPeriodStartGTE applies the GTE predicate on the "current_period_start" field.
func CurrentPeriodStartGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldCurrentPeriodStart, v))
}

// CurrentPeriodStartLT applies the LT predicate on the "current_period_start" field.
func CurrentPeriodStartLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldCurrentPeriodStart, v))
}

// CurrentPeriodStartLTE applies the LTE predicate on the "current_period_start" field.
func CurrentPeriodStartLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldCurrentPeriodStart, v))
}

// CurrentPeriodEndEQ applies the EQ predicate on the "current_period_end" field.
func CurrentPeriodEndEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCurrentPeriodEnd, v))
}

// CurrentPeriodEndNEQ applies the NEQ predicate on the "current_period_end" field.
func CurrentPeriodEndNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldCurrentPeriodEnd, v))
}

// CurrentPeriodEndIn applies the In predicate on the "current_period_end" field.
func CurrentPeriodEndIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldCurrentPeriodEnd, vs...))
}

// CurrentPeriodEndNotIn applies the NotIn predicate on the "current_period_end" field.
func CurrentPeriodEndNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldCurrentPeriodEnd, vs...))
}

// CurrentPeriodEndGT applies the GT predicate on the "current_period_end" field.
func CurrentPeriodEndGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldCurrentPeriodEnd, v))
}

// CurrentPeriodEndGTE applies the GTE predicate on the "current_period_end" field.
func CurrentPeriodEndGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldCurrentPeriodEnd, v))
}

// CurrentPeriodEndLT applies the LT predicate on the "current_period_end" field.
func CurrentPeriodEndLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldCurrentPeriodEnd, v))
}

// CurrentPeriodEndLTE applies the LTE predicate on the "current_period_end" field.
func CurrentPeriodEndLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldCurrentPeriodEnd, v))
}

// TrialStartEQ applies the EQ predicate on the "trial_start" field.
func TrialStartEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldTrialStart, v))
}

// TrialStartNEQ applies the NEQ predicate on the "trial_start" field.
func TrialStartNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldTrialStart, v))
}

// TrialStartIn applies the In predicate on the "trial_start" field.
func TrialStartIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldTrialStart, vs...))
}

// TrialStartNotIn applies the NotIn predicate on the "trial_start" field.
func TrialStartNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldTrialStart, vs...))
}

// TrialStartGT applies the GT predicate on the "trial_start" field.
func TrialStartGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldTrialStart, v))
}

// TrialStartGTE applies the GTE predicate on the "trial_start" field.
func TrialStartGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldTrialStart, v))
}

// TrialStartLT applies the LT predicate on the "trial_start" field.
func TrialStartLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldTrialStart, v))
}

// TrialStartLTE applies the LTE predicate on the "trial_start" field.
func TrialStartLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldTrialStart, v))
}

// TrialStartIsNil applies the IsNil predicate on the "trial_start" field.
func TrialStartIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldTrialStart))
}

// TrialStartNotNil applies the NotNil predicate on the "trial_start" field.
func TrialStartNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldTrialStart))
}

// TrialEndEQ applies the EQ predicate on the "trial_end" field.
func TrialEndEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldTrialEnd, v))
}

// TrialEndNEQ applies the NEQ predicate on the "trial_end" field.
func TrialEndNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldTrialEnd, v))
}

// TrialEndIn applies the In predicate on the "trial_end" field.
func TrialEndIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldTrialEnd, vs...))
}

// TrialEndNotIn applies the NotIn predicate on the "trial_end" field.
func TrialEndNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldTrialEnd, vs...))
}

// TrialEndGT applies the GT predicate on the "trial_end" field.
func TrialEndGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldTrialEnd, v))
}

// TrialEndGTE applies the GTE predicate on the "trial_end" field.
func TrialEndGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldTrialEnd, v))
}

// TrialEndLT applies the LT predicate on the "trial_end" field.
func TrialEndLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldTrialEnd, v))
}

// TrialEndLTE applies the LTE predicate on the "trial_end" field.
func TrialEndLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldTrialEnd, v))
}

// TrialEndIsNil applies the IsNil predicate on the "trial_end" field.
func TrialEndIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldTrialEnd))
}

// TrialEndNotNil applies the NotNil predicate on the "trial_end" field.
func TrialEndNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldTrialEnd))
}

// CancelledAtEQ applies the EQ predicate on the "cancelled_at" field.
func CancelledAtEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCancelledAt, v))
}

// CancelledAtNEQ applies the NEQ predicate on the "cancelled_at" field.
func CancelledAtNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldCancelledAt, v))
}

// CancelledAtIn applies the In predicate on the "cancelled_at" field.
func CancelledAtIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldCancelledAt, vs...))
}

// CancelledAtNotIn applies the NotIn predicate on the "cancelled_at" field.
func CancelledAtNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldCancelledAt, vs...))
}

// CancelledAtGT applies the GT predicate on the "cancelled_at" field.
func CancelledAtGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldCancelledAt, v))
}

// CancelledAtGTE applies the GTE predicate on the "cancelled_at" field.
func CancelledAtGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldCancelledAt, v))
}

// CancelledAtLT applies the LT predicate on the "cancelled_at" field.
func CancelledAtLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldCancelledAt, v))
}

// CancelledAtLTE applies the LTE predicate on the "cancelled_at" field.
func CancelledAtLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldCancelledAt, v))
}

// CancelledAtIsNil applies the IsNil predicate on the "cancelled_at" field.
func CancelledAtIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldCancelledAt))
}

// CancelledAtNotNil applies the NotNil predicate on the "cancelled_at" field.
func CancelledAtNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldCancelledAt))
}

// CancelAtPeriodEndEQ applies the EQ predicate on the "cancel_at_period_end" field.
func CancelAtPeriodEndEQ(v bool) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCancelAtPeriodEnd, v))
}

// CancelAtPeriodEndNEQ applies the NEQ predicate on the "cancel_at_period_end" field.
func CancelAtPeriodEndNEQ(v bool) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldCancelAtPeriodEnd, v))
}

// PaymentMethodEQ applies the EQ predicate on the "payment_method" field.
func PaymentMethodEQ(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentMethodNEQ applies the NEQ predicate on the "payment_method" field.
func PaymentMethodNEQ(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldPaymentMethod, v))
}

// PaymentMethodIn applies the In predicate on the "payment_method" field.
func PaymentMethodIn(vs ...string) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldPaymentMethod, vs...))
}

// PaymentMethodNotIn applies the NotIn predicate on the "payment_method" field.
func PaymentMethodNotIn(vs ...string) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldPaymentMethod, vs...))
}

// PaymentMethodGT applies the GT predicate on the "payment_method" field.
func PaymentMethodGT(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldPaymentMethod, v))
}

// PaymentMethodGTE applies the GTE predicate on the "payment_method" field.
func PaymentMethodGTE(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldPaymentMethod, v))
}

// PaymentMethodLT applies the LT predicate on the "payment_method" field.
func PaymentMethodLT(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldPaymentMethod, v))
}

// PaymentMethodLTE applies the LTE predicate on the "payment_method" field.
func PaymentMethodLTE(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldPaymentMethod, v))
}

// PaymentMethodContains applies the Contains predicate on the "payment_method" field.
func PaymentMethodContains(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldContains(FieldPaymentMethod, v))
}

// PaymentMethodHasPrefix applies the HasPrefix predicate on the "payment_method" field.
func PaymentMethodHasPrefix(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldHasPrefix(FieldPaymentMethod, v))
}

// PaymentMethodHasSuffix applies the HasSuffix predicate on the "payment_method" field.
func PaymentMethodHasSuffix(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldHasSuffix(FieldPaymentMethod, v))
}

// PaymentMethodEqualFold applies the EqualFold predicate on the "payment_method" field.
func PaymentMethodEqualFold(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEqualFold(FieldPaymentMethod, v))
}

// PaymentMethodContainsFold applies the ContainsFold predicate on the "payment_method" field.
func PaymentMethodContainsFold(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldContainsFold(FieldPaymentMethod, v))
}

// BillingAddressIsNil applies the IsNil predicate on the "billing_address" field.
func BillingAddressIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldBillingAddress))
}

// BillingAddressNotNil applies the NotNil predicate on the "billing_address" field.
func BillingAddressNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldBillingAddress))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldMetadata))
}

// LastBilledAtEQ applies the EQ predicate on the "last_billed_at" field.
func LastBilledAtEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldLastBilledAt, v))
}

// LastBilledAtNEQ applies the NEQ predicate on the "last_billed_at" field.
func LastBilledAtNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldLastBilledAt, v))
}

// LastBilledAtIn applies the In predicate on the "last_billed_at" field.
func LastBilledAtIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldLastBilledAt, vs...))
}

// LastBilledAtNotIn applies the NotIn predicate on the "last_billed_at" field.
func LastBilledAtNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldLastBilledAt, vs...))
}

// LastBilledAtGT applies the GT predicate on the "last_billed_at" field.
func LastBilledAtGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldLastBilledAt, v))
}

// LastBilledAtGTE applies the GTE predicate on the "last_billed_at" field.
func LastBilledAtGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldLastBilledAt, v))
}

// LastBilledAtLT applies the LT predicate on the "last_billed_at" field.
func LastBilledAtLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldLastBilledAt, v))
}

// LastBilledAtLTE applies the LTE predicate on the "last_billed_at" field.
func LastBilledAtLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldLastBilledAt, v))
}

// LastBilledAtIsNil applies the IsNil predicate on the "last_billed_at" field.
func LastBilledAtIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldLastBilledAt))
}

// LastBilledAtNotNil applies the NotNil predicate on the "last_billed_at" field.
func LastBilledAtNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldLastBilledAt))
}

// NextBillingAtEQ applies the EQ predicate on the "next_billing_at" field.
func NextBillingAtEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldNextBillingAt, v))
}

// NextBillingAtNEQ applies the NEQ predicate on the "next_billing_at" field.
func NextBillingAtNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldNextBillingAt, v))
}

// NextBillingAtIn applies the In predicate on the "next_billing_at" field.
func NextBillingAtIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldNextBillingAt, vs...))
}

// NextBillingAtNotIn applies the NotIn predicate on the "next_billing_at" field.
func NextBillingAtNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldNextBillingAt, vs...))
}

// NextBillingAtGT applies the GT predicate on the "next_billing_at" field.
func NextBillingAtGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldNextBillingAt, v))
}

// NextBillingAtGTE applies the GTE predicate on the "next_billing_at" field.
func NextBillingAtGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldNextBillingAt, v))
}

// NextBillingAtLT applies the LT predicate on the "next_billing_at" field.
func NextBillingAtLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldNextBillingAt, v))
}

// NextBillingAtLTE applies the LTE predicate on the "next_billing_at" field.
func NextBillingAtLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldNextBillingAt, v))
}

// NextBillingAtIsNil applies the IsNil predicate on the "next_billing_at" field.
func NextBillingAtIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldNextBillingAt))
}

// NextBillingAtNotNil applies the NotNil predicate on the "next_billing_at" field.
func NextBillingAtNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldNextBillingAt))
}

// ExternalSubscriptionIDEQ applies the EQ predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDEQ(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDNEQ applies the NEQ predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDNEQ(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDIn applies the In predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDIn(vs ...string) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldExternalSubscriptionID, vs...))
}

// ExternalSubscriptionIDNotIn applies the NotIn predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDNotIn(vs ...string) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldExternalSubscriptionID, vs...))
}

// ExternalSubscriptionIDGT applies the GT predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDGT(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDGTE applies the GTE predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDGTE(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDLT applies the LT predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDLT(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDLTE applies the LTE predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDLTE(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDContains applies the Contains predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDContains(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldContains(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDHasPrefix applies the HasPrefix predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDHasPrefix(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldHasPrefix(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDHasSuffix applies the HasSuffix predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDHasSuffix(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldHasSuffix(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDIsNil applies the IsNil predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDIsNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldIsNull(FieldExternalSubscriptionID))
}

// ExternalSubscriptionIDNotNil applies the NotNil predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDNotNil() predicate.Subscription {
	return predicate.Subscription(sql.FieldNotNull(FieldExternalSubscriptionID))
}

// ExternalSubscriptionIDEqualFold applies the EqualFold predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDEqualFold(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldEqualFold(FieldExternalSubscriptionID, v))
}

// ExternalSubscriptionIDContainsFold applies the ContainsFold predicate on the "external_subscription_id" field.
func ExternalSubscriptionIDContainsFold(v string) predicate.Subscription {
	return predicate.Subscription(sql.FieldContainsFold(FieldExternalSubscriptionID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Subscription {
	return predicate.Subscription(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Subscription) predicate.Subscription {
	return predicate.Subscription(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Subscription) predicate.Subscription {
	return predicate.Subscription(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Subscription) predicate.Subscription {
	return predicate.Subscription(sql.NotPredicates(p))
}

package database

import (
	"context"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/pkg-shared/logging"

	// Import database drivers
	_ "github.com/lib/pq"           // PostgreSQL driver
	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

// Config holds database configuration
type Config struct {
	Driver   string `yaml:"driver" env:"DB_DRIVER" env-default:"sqlite"`
	Host     string `yaml:"host" env:"DB_HOST" env-default:"localhost"`
	Port     int    `yaml:"port" env:"DB_PORT" env-default:"5432"`
	Database string `yaml:"database" env:"DB_NAME" env-default:"integration_service"`
	Username string `yaml:"username" env:"DB_USER" env-default:"postgres"`
	Password string `yaml:"password" env:"DB_PASSWORD" env-default:""`
	SSLMode  string `yaml:"ssl_mode" env:"DB_SSL_MODE" env-default:"disable"`
	FilePath string `yaml:"file_path" env:"DB_FILE_PATH" env-default:"./integration_service.db"`
	
	// Connection pool settings
	MaxOpenConns    int           `yaml:"max_open_conns" env:"DB_MAX_OPEN_CONNS" env-default:"25"`
	MaxIdleConns    int           `yaml:"max_idle_conns" env:"DB_MAX_IDLE_CONNS" env-default:"5"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" env:"DB_CONN_MAX_LIFETIME" env-default:"5m"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time" env:"DB_CONN_MAX_IDLE_TIME" env-default:"5m"`
}

// NewClient creates a new database client based on configuration
func NewClient(cfg Config, logger logging.Logger) (*ent.Client, error) {
	var drv *sql.Driver
	var err error

	switch cfg.Driver {
	case "postgres", "postgresql":
		drv, err = newPostgreSQLDriver(cfg, logger)
	case "sqlite", "sqlite3":
		drv, err = newSQLiteDriver(cfg, logger)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Driver)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create database driver: %w", err)
	}

	client := ent.NewClient(ent.Driver(drv))
	
	logger.WithFields(map[string]interface{}{
		"driver":   cfg.Driver,
		"database": cfg.Database,
	}).Info("Database client created successfully")

	return client, nil
}

// newPostgreSQLDriver creates a PostgreSQL driver
func newPostgreSQLDriver(cfg Config, logger logging.Logger) (*sql.Driver, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)

	db, err := sql.Open(dialect.Postgres, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open PostgreSQL connection: %w", err)
	}

	// Configure connection pool
	sqlDB := db.DB()
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(cfg.ConnMaxIdleTime)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping PostgreSQL database: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"host":     cfg.Host,
		"port":     cfg.Port,
		"database": cfg.Database,
		"ssl_mode": cfg.SSLMode,
	}).Info("PostgreSQL connection established")

	return sql.OpenDB(dialect.Postgres, sqlDB), nil
}

// newSQLiteDriver creates a SQLite driver
func newSQLiteDriver(cfg Config, logger logging.Logger) (*sql.Driver, error) {
	dsn := cfg.FilePath
	if dsn == "" {
		dsn = "./integration_service.db"
	}

	// Add SQLite specific parameters
	dsn += "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"

	db, err := sql.Open(dialect.SQLite, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open SQLite connection: %w", err)
	}

	// Configure connection pool (SQLite specific settings)
	sqlDB := db.DB()
	sqlDB.SetMaxOpenConns(1) // SQLite works best with single connection
	sqlDB.SetMaxIdleConns(1)
	sqlDB.SetConnMaxLifetime(0) // No limit for SQLite

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping SQLite database: %w", err)
	}

	logger.WithField("file_path", dsn).Info("SQLite connection established")

	return sql.OpenDB(dialect.SQLite, sqlDB), nil
}

// MigrateSchema runs database migrations
func MigrateSchema(ctx context.Context, client *ent.Client, logger logging.Logger) error {
	logger.Info("Running database migrations")

	if err := client.Schema.Create(ctx); err != nil {
		return fmt.Errorf("failed to create schema: %w", err)
	}

	logger.Info("Database migrations completed successfully")
	return nil
}

// CloseClient closes the database client
func CloseClient(client *ent.Client, logger logging.Logger) error {
	if client == nil {
		return nil
	}

	if err := client.Close(); err != nil {
		logger.WithError(err).Error("Failed to close database client")
		return err
	}

	logger.Info("Database client closed successfully")
	return nil
}

// HealthCheck performs a database health check
func HealthCheck(ctx context.Context, client *ent.Client) error {
	if client == nil {
		return fmt.Errorf("database client is nil")
	}

	// Simple query to test connection
	_, err := client.PlatformIntegration.Query().Count(ctx)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	return nil
}

// GetConnectionInfo returns database connection information
func GetConnectionInfo(cfg Config) map[string]interface{} {
	info := map[string]interface{}{
		"driver": cfg.Driver,
	}

	switch cfg.Driver {
	case "postgres", "postgresql":
		info["host"] = cfg.Host
		info["port"] = cfg.Port
		info["database"] = cfg.Database
		info["ssl_mode"] = cfg.SSLMode
	case "sqlite", "sqlite3":
		info["file_path"] = cfg.FilePath
	}

	return info
}

package handlers

import (
	"google.golang.org/grpc"

	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Handlers contains all gRPC handlers
type Handlers struct {
	creditService       *CreditServiceHandler
	subscriptionService *SubscriptionServiceHandler
	paymentService      *PaymentServiceHandler
	logger              logging.Logger
}

// NewHandlers creates a new handlers instance
func NewHandlers(
	creditUseCase credit.UseCase,
	planUseCase plan.UseCase,
	subscriptionUseCase subscription.UseCase,
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *Handlers {
	// Create individual service handlers
	creditService := NewCreditServiceHandler(
		creditUseCase,
		subscriptionUseCase,
		transactionUseCase,
		logger,
	)

	subscriptionService := NewSubscriptionServiceHandler(
		subscriptionUseCase,
		planUseCase,
		logger,
	)

	paymentService := NewPaymentServiceHandler(
		subscriptionUseCase,
		transactionUseCase,
		logger,
	)

	return &Handlers{
		creditService:       creditService,
		subscriptionService: subscriptionService,
		paymentService:      paymentService,
		logger:              logger,
	}
}

// RegisterServices registers all gRPC services
func (h *Handlers) RegisterServices(server *grpc.Server) {
	// Register CreditService with combined handlers
	creditv1.RegisterCreditServiceServer(server, &CreditServiceServer{
		creditHandler:       h.creditService,
		subscriptionHandler: h.subscriptionService,
		paymentHandler:      h.paymentService,
	})
	
	h.logger.Info("gRPC services registered successfully")
}

// CreditServiceServer combines all handlers to implement the full CreditService interface
type CreditServiceServer struct {
	creditv1.UnimplementedCreditServiceServer
	creditHandler       *CreditServiceHandler
	subscriptionHandler *SubscriptionServiceHandler
	paymentHandler      *PaymentServiceHandler
}

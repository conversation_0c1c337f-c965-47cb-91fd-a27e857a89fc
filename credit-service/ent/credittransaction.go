// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
)

// CreditTransaction is the model entity for the CreditTransaction schema.
type CreditTransaction struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Type holds the value of the "type" field.
	Type credittransaction.Type `json:"type,omitempty"`
	// Positive for credit, negative for debit
	Amount int `json:"amount,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Reference to related entity (post, generation, payment, etc.)
	ReferenceID string `json:"reference_id,omitempty"`
	// Type of reference (generation, improvement, payment, etc.)
	ReferenceType string `json:"reference_type,omitempty"`
	// Payment processor transaction ID
	PaymentID string `json:"payment_id,omitempty"`
	// Invoice ID for billing
	InvoiceID string `json:"invoice_id,omitempty"`
	// Status holds the value of the "status" field.
	Status credittransaction.Status `json:"status,omitempty"`
	// Additional transaction metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// When the transaction was processed
	ProcessedAt time.Time `json:"processed_at,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CreditTransaction) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case credittransaction.FieldMetadata:
			values[i] = new([]byte)
		case credittransaction.FieldAmount:
			values[i] = new(sql.NullInt64)
		case credittransaction.FieldType, credittransaction.FieldDescription, credittransaction.FieldReferenceID, credittransaction.FieldReferenceType, credittransaction.FieldPaymentID, credittransaction.FieldInvoiceID, credittransaction.FieldStatus:
			values[i] = new(sql.NullString)
		case credittransaction.FieldProcessedAt, credittransaction.FieldCreatedAt, credittransaction.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case credittransaction.FieldID, credittransaction.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CreditTransaction fields.
func (ct *CreditTransaction) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case credittransaction.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				ct.ID = *value
			}
		case credittransaction.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				ct.UserID = *value
			}
		case credittransaction.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				ct.Type = credittransaction.Type(value.String)
			}
		case credittransaction.FieldAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				ct.Amount = int(value.Int64)
			}
		case credittransaction.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				ct.Description = value.String
			}
		case credittransaction.FieldReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_id", values[i])
			} else if value.Valid {
				ct.ReferenceID = value.String
			}
		case credittransaction.FieldReferenceType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_type", values[i])
			} else if value.Valid {
				ct.ReferenceType = value.String
			}
		case credittransaction.FieldPaymentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_id", values[i])
			} else if value.Valid {
				ct.PaymentID = value.String
			}
		case credittransaction.FieldInvoiceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field invoice_id", values[i])
			} else if value.Valid {
				ct.InvoiceID = value.String
			}
		case credittransaction.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				ct.Status = credittransaction.Status(value.String)
			}
		case credittransaction.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ct.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case credittransaction.FieldProcessedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field processed_at", values[i])
			} else if value.Valid {
				ct.ProcessedAt = value.Time
			}
		case credittransaction.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ct.CreatedAt = value.Time
			}
		case credittransaction.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ct.UpdatedAt = value.Time
			}
		default:
			ct.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CreditTransaction.
// This includes values selected through modifiers, order, etc.
func (ct *CreditTransaction) Value(name string) (ent.Value, error) {
	return ct.selectValues.Get(name)
}

// Update returns a builder for updating this CreditTransaction.
// Note that you need to call CreditTransaction.Unwrap() before calling this method if this CreditTransaction
// was returned from a transaction, and the transaction was committed or rolled back.
func (ct *CreditTransaction) Update() *CreditTransactionUpdateOne {
	return NewCreditTransactionClient(ct.config).UpdateOne(ct)
}

// Unwrap unwraps the CreditTransaction entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ct *CreditTransaction) Unwrap() *CreditTransaction {
	_tx, ok := ct.config.driver.(*txDriver)
	if !ok {
		panic("ent: CreditTransaction is not a transactional entity")
	}
	ct.config.driver = _tx.drv
	return ct
}

// String implements the fmt.Stringer.
func (ct *CreditTransaction) String() string {
	var builder strings.Builder
	builder.WriteString("CreditTransaction(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ct.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", ct.UserID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", ct.Type))
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", ct.Amount))
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(ct.Description)
	builder.WriteString(", ")
	builder.WriteString("reference_id=")
	builder.WriteString(ct.ReferenceID)
	builder.WriteString(", ")
	builder.WriteString("reference_type=")
	builder.WriteString(ct.ReferenceType)
	builder.WriteString(", ")
	builder.WriteString("payment_id=")
	builder.WriteString(ct.PaymentID)
	builder.WriteString(", ")
	builder.WriteString("invoice_id=")
	builder.WriteString(ct.InvoiceID)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", ct.Status))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", ct.Metadata))
	builder.WriteString(", ")
	builder.WriteString("processed_at=")
	builder.WriteString(ct.ProcessedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ct.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ct.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CreditTransactions is a parsable slice of CreditTransaction.
type CreditTransactions []*CreditTransaction

package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"

	"github.com/social-content-ai/schedule-processor/internal/processor"
	"github.com/social-content-ai/pkg-shared/config"
	"github.com/social-content-ai/pkg-shared/logging"
)

const (
	serviceName = "schedule-processor"
	version     = "1.0.0"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithFields(logrus.Fields{
		"service": serviceName,
		"version": version,
	}).Info("Starting Schedule Processor")

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize processor
	proc, err := processor.NewProcessor(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize processor")
	}

	// Create cron scheduler
	c := cron.New(cron.WithSeconds())

	// Schedule jobs
	scheduleJobs(c, proc, logger)

	// Start cron scheduler
	c.Start()
	logger.Info("Cron scheduler started")

	// Start background workers
	go proc.StartWorkers(ctx)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	<-sigChan

	logger.Info("Shutting down Schedule Processor")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Stop cron scheduler
	cronCtx := c.Stop()
	select {
	case <-cronCtx.Done():
		logger.Info("Cron scheduler stopped gracefully")
	case <-shutdownCtx.Done():
		logger.Warn("Cron scheduler shutdown timeout")
	}

	// Stop processor
	cancel()
	proc.Stop()

	logger.Info("Schedule Processor stopped")
}

// scheduleJobs configures all scheduled jobs
func scheduleJobs(c *cron.Cron, proc *processor.Processor, logger logging.Logger) {
	// Process scheduled posts every minute
	_, err := c.AddFunc("0 * * * * *", func() {
		if err := proc.ProcessScheduledPosts(); err != nil {
			logger.WithError(err).Error("Failed to process scheduled posts")
		}
	})
	if err != nil {
		logger.WithError(err).Fatal("Failed to schedule post processing job")
	}

	// Retry failed posts every 5 minutes
	_, err = c.AddFunc("0 */5 * * * *", func() {
		if err := proc.RetryFailedPosts(); err != nil {
			logger.WithError(err).Error("Failed to retry failed posts")
		}
	})
	if err != nil {
		logger.WithError(err).Fatal("Failed to schedule retry job")
	}

	// Clean up old processed posts every hour
	_, err = c.AddFunc("0 0 * * * *", func() {
		if err := proc.CleanupOldPosts(); err != nil {
			logger.WithError(err).Error("Failed to cleanup old posts")
		}
	})
	if err != nil {
		logger.WithError(err).Fatal("Failed to schedule cleanup job")
	}

	// Sync analytics data every 30 minutes
	_, err = c.AddFunc("0 */30 * * * *", func() {
		if err := proc.SyncAnalytics(); err != nil {
			logger.WithError(err).Error("Failed to sync analytics")
		}
	})
	if err != nil {
		logger.WithError(err).Fatal("Failed to schedule analytics sync job")
	}

	// Health check every 10 minutes
	_, err = c.AddFunc("0 */10 * * * *", func() {
		if err := proc.HealthCheck(); err != nil {
			logger.WithError(err).Error("Health check failed")
		}
	})
	if err != nil {
		logger.WithError(err).Fatal("Failed to schedule health check job")
	}

	logger.Info("All scheduled jobs configured successfully")
}

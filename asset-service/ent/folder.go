// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/folder"
)

// Folder is the model entity for the Folder schema.
type Folder struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// Owner of the folder
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Parent folder ID for nested structure
	ParentID *uuid.UUID `json:"parent_id,omitempty"`
	// Folder name
	Name string `json:"name,omitempty"`
	// Folder description
	Description string `json:"description,omitempty"`
	// Full path of the folder
	Path string `json:"path,omitempty"`
	// Folder depth level
	Level int `json:"level,omitempty"`
	// Whether folder is shared with others
	IsShared bool `json:"is_shared,omitempty"`
	// List of user IDs folder is shared with
	SharedWith []string `json:"shared_with,omitempty"`
	// Folder permissions
	Permissions []string `json:"permissions,omitempty"`
	// Number of assets in folder
	AssetCount int64 `json:"asset_count,omitempty"`
	// Total size of assets in folder
	TotalSize int64 `json:"total_size,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the FolderQuery when eager-loading is set.
	Edges        FolderEdges `json:"edges"`
	selectValues sql.SelectValues
}

// FolderEdges holds the relations/edges for other nodes in the graph.
type FolderEdges struct {
	// Parent holds the value of the parent edge.
	Parent *Folder `json:"parent,omitempty"`
	// Children holds the value of the children edge.
	Children []*Folder `json:"children,omitempty"`
	// Assets holds the value of the assets edge.
	Assets []*Asset `json:"assets,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// ParentOrErr returns the Parent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FolderEdges) ParentOrErr() (*Folder, error) {
	if e.Parent != nil {
		return e.Parent, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: folder.Label}
	}
	return nil, &NotLoadedError{edge: "parent"}
}

// ChildrenOrErr returns the Children value or an error if the edge
// was not loaded in eager-loading.
func (e FolderEdges) ChildrenOrErr() ([]*Folder, error) {
	if e.loadedTypes[1] {
		return e.Children, nil
	}
	return nil, &NotLoadedError{edge: "children"}
}

// AssetsOrErr returns the Assets value or an error if the edge
// was not loaded in eager-loading.
func (e FolderEdges) AssetsOrErr() ([]*Asset, error) {
	if e.loadedTypes[2] {
		return e.Assets, nil
	}
	return nil, &NotLoadedError{edge: "assets"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Folder) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case folder.FieldParentID:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case folder.FieldSharedWith, folder.FieldPermissions:
			values[i] = new([]byte)
		case folder.FieldIsShared:
			values[i] = new(sql.NullBool)
		case folder.FieldLevel, folder.FieldAssetCount, folder.FieldTotalSize:
			values[i] = new(sql.NullInt64)
		case folder.FieldName, folder.FieldDescription, folder.FieldPath:
			values[i] = new(sql.NullString)
		case folder.FieldCreatedAt, folder.FieldUpdatedAt, folder.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case folder.FieldID, folder.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Folder fields.
func (f *Folder) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case folder.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				f.ID = *value
			}
		case folder.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				f.UserID = *value
			}
		case folder.FieldParentID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field parent_id", values[i])
			} else if value.Valid {
				f.ParentID = new(uuid.UUID)
				*f.ParentID = *value.S.(*uuid.UUID)
			}
		case folder.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				f.Name = value.String
			}
		case folder.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				f.Description = value.String
			}
		case folder.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				f.Path = value.String
			}
		case folder.FieldLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field level", values[i])
			} else if value.Valid {
				f.Level = int(value.Int64)
			}
		case folder.FieldIsShared:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_shared", values[i])
			} else if value.Valid {
				f.IsShared = value.Bool
			}
		case folder.FieldSharedWith:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field shared_with", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &f.SharedWith); err != nil {
					return fmt.Errorf("unmarshal field shared_with: %w", err)
				}
			}
		case folder.FieldPermissions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field permissions", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &f.Permissions); err != nil {
					return fmt.Errorf("unmarshal field permissions: %w", err)
				}
			}
		case folder.FieldAssetCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field asset_count", values[i])
			} else if value.Valid {
				f.AssetCount = value.Int64
			}
		case folder.FieldTotalSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field total_size", values[i])
			} else if value.Valid {
				f.TotalSize = value.Int64
			}
		case folder.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				f.CreatedAt = value.Time
			}
		case folder.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				f.UpdatedAt = value.Time
			}
		case folder.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				f.DeletedAt = new(time.Time)
				*f.DeletedAt = value.Time
			}
		default:
			f.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Folder.
// This includes values selected through modifiers, order, etc.
func (f *Folder) Value(name string) (ent.Value, error) {
	return f.selectValues.Get(name)
}

// QueryParent queries the "parent" edge of the Folder entity.
func (f *Folder) QueryParent() *FolderQuery {
	return NewFolderClient(f.config).QueryParent(f)
}

// QueryChildren queries the "children" edge of the Folder entity.
func (f *Folder) QueryChildren() *FolderQuery {
	return NewFolderClient(f.config).QueryChildren(f)
}

// QueryAssets queries the "assets" edge of the Folder entity.
func (f *Folder) QueryAssets() *AssetQuery {
	return NewFolderClient(f.config).QueryAssets(f)
}

// Update returns a builder for updating this Folder.
// Note that you need to call Folder.Unwrap() before calling this method if this Folder
// was returned from a transaction, and the transaction was committed or rolled back.
func (f *Folder) Update() *FolderUpdateOne {
	return NewFolderClient(f.config).UpdateOne(f)
}

// Unwrap unwraps the Folder entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (f *Folder) Unwrap() *Folder {
	_tx, ok := f.config.driver.(*txDriver)
	if !ok {
		panic("ent: Folder is not a transactional entity")
	}
	f.config.driver = _tx.drv
	return f
}

// String implements the fmt.Stringer.
func (f *Folder) String() string {
	var builder strings.Builder
	builder.WriteString("Folder(")
	builder.WriteString(fmt.Sprintf("id=%v, ", f.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", f.UserID))
	builder.WriteString(", ")
	if v := f.ParentID; v != nil {
		builder.WriteString("parent_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(f.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(f.Description)
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(f.Path)
	builder.WriteString(", ")
	builder.WriteString("level=")
	builder.WriteString(fmt.Sprintf("%v", f.Level))
	builder.WriteString(", ")
	builder.WriteString("is_shared=")
	builder.WriteString(fmt.Sprintf("%v", f.IsShared))
	builder.WriteString(", ")
	builder.WriteString("shared_with=")
	builder.WriteString(fmt.Sprintf("%v", f.SharedWith))
	builder.WriteString(", ")
	builder.WriteString("permissions=")
	builder.WriteString(fmt.Sprintf("%v", f.Permissions))
	builder.WriteString(", ")
	builder.WriteString("asset_count=")
	builder.WriteString(fmt.Sprintf("%v", f.AssetCount))
	builder.WriteString(", ")
	builder.WriteString("total_size=")
	builder.WriteString(fmt.Sprintf("%v", f.TotalSize))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(f.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(f.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := f.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Folders is a parsable slice of Folder.
type Folders []*Folder

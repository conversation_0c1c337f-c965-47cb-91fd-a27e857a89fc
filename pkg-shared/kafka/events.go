package kafka

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// EventType represents the type of event
type EventType string

// Common Event Types across all services
const (
	// User Events
	EventTypeUserRegistered      EventType = "user.registered"
	EventTypeUserUpdated         EventType = "user.updated"
	EventTypeUserDeleted         EventType = "user.deleted"
	EventTypeUserWelcome         EventType = "user.welcome"
	EventTypeUserUpgraded        EventType = "user.upgraded"
	EventTypeUserProfileUpdated  EventType = "user.profile_updated"
	EventTypeUserPasswordReset   EventType = "user.password_reset"
	EventTypeUserEmailVerified   EventType = "user.email_verified"
	EventTypeUserSecurityAlert   EventType = "user.security_alert"

	// Content Events
	EventTypeContentGenerated    EventType = "content.generated"
	EventTypeContentImproved     EventType = "content.improved"
	EventTypePostPublished       EventType = "post.published"
	EventTypePostScheduled       EventType = "post.scheduled"
	EventTypePostFailed          EventType = "post.failed"
	EventTypeTemplateCreated     EventType = "template.created"
	EventTypeTemplateUpdated     EventType = "template.updated"
	EventTypeTemplateDeleted     EventType = "template.deleted"

	// Billing Events
	EventTypeCreditLow           EventType = "credit.low"
	EventTypeCreditPurchased     EventType = "credit.purchased"
	EventTypeCreditConsumed      EventType = "credit.consumed"
	EventTypeSubscriptionRenewed EventType = "subscription.renewed"
	EventTypeSubscriptionExpired EventType = "subscription.expired"
	EventTypePaymentFailed       EventType = "payment.failed"
	EventTypePaymentSucceeded    EventType = "payment.succeeded"

	// Integration Events
	EventTypeIntegrationConnected    EventType = "integration.connected"
	EventTypeIntegrationFailed       EventType = "integration.failed"
	EventTypeIntegrationDisconnected EventType = "integration.disconnected"

	// Analytics Events
	EventTypeAnalyticsReport     EventType = "analytics.report"
	EventTypeAnalyticsInsight    EventType = "analytics.insight"

	// System Events
	EventTypeSystemMaintenance   EventType = "system.maintenance"
	EventTypeSystemUpdate        EventType = "system.update"
	EventTypeSystemAlert         EventType = "system.alert"

	// Asset Events
	EventTypeAssetUploaded       EventType = "asset.uploaded"
	EventTypeAssetDeleted        EventType = "asset.deleted"
	EventTypeAssetValidated      EventType = "asset.validated"

	// RAG Events
	EventTypeRAGProcessingStarted   EventType = "rag.processing_started"
	EventTypeRAGProcessingCompleted EventType = "rag.processing_completed"
	EventTypeRAGProcessingFailed    EventType = "rag.processing_failed"
	EventTypeRAGRetrainingStarted   EventType = "rag.retraining_started"
	EventTypeRAGRetrainingCompleted EventType = "rag.retraining_completed"

	// Notification Events
	EventTypeNotificationSent      EventType = "notification.sent"
	EventTypeNotificationDelivered EventType = "notification.delivered"
	EventTypeNotificationFailed    EventType = "notification.failed"
)

// Priority represents the priority level of an event
type Priority string

const (
	PriorityLow      Priority = "low"
	PriorityNormal   Priority = "normal"
	PriorityHigh     Priority = "high"
	PriorityCritical Priority = "critical"
)

// BaseEvent represents the base structure for all events
type BaseEvent struct {
	ID          string                 `json:"id"`
	Type        EventType              `json:"type"`
	Source      string                 `json:"source"`      // Service that generated the event
	Version     string                 `json:"version"`
	Timestamp   time.Time              `json:"timestamp"`
	UserID      string                 `json:"user_id,omitempty"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Priority    Priority               `json:"priority"`
	Data        map[string]interface{} `json:"data"`
	Metadata    EventMetadata          `json:"metadata"`
}

// EventMetadata contains metadata about the event
type EventMetadata struct {
	CorrelationID string            `json:"correlation_id,omitempty"`
	TraceID       string            `json:"trace_id,omitempty"`
	SpanID        string            `json:"span_id,omitempty"`
	RetryCount    int               `json:"retry_count,omitempty"`
	Tags          map[string]string `json:"tags,omitempty"`
	Environment   string            `json:"environment,omitempty"`
}

// NewBaseEvent creates a new base event with default values
func NewBaseEvent(eventType EventType, source string) *BaseEvent {
	return &BaseEvent{
		ID:        uuid.New().String(),
		Type:      eventType,
		Source:    source,
		Version:   "1.0",
		Timestamp: time.Now(),
		Priority:  PriorityNormal,
		Data:      make(map[string]interface{}),
		Metadata: EventMetadata{
			CorrelationID: uuid.New().String(),
			Tags:          make(map[string]string),
		},
	}
}

// WithUserID sets the user ID for the event
func (e *BaseEvent) WithUserID(userID string) *BaseEvent {
	e.UserID = userID
	return e
}

// WithWorkspaceID sets the workspace ID for the event
func (e *BaseEvent) WithWorkspaceID(workspaceID string) *BaseEvent {
	e.WorkspaceID = workspaceID
	return e
}

// WithPriority sets the priority for the event
func (e *BaseEvent) WithPriority(priority Priority) *BaseEvent {
	e.Priority = priority
	return e
}

// WithData sets data for the event
func (e *BaseEvent) WithData(key string, value interface{}) *BaseEvent {
	e.Data[key] = value
	return e
}

// WithCorrelationID sets the correlation ID for the event
func (e *BaseEvent) WithCorrelationID(correlationID string) *BaseEvent {
	e.Metadata.CorrelationID = correlationID
	return e
}

// WithTraceID sets the trace ID for the event
func (e *BaseEvent) WithTraceID(traceID string) *BaseEvent {
	e.Metadata.TraceID = traceID
	return e
}

// WithTag adds a tag to the event metadata
func (e *BaseEvent) WithTag(key, value string) *BaseEvent {
	e.Metadata.Tags[key] = value
	return e
}

// ToJSON converts the event to JSON bytes
func (e *BaseEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// FromJSON creates an event from JSON bytes
func FromJSON(data []byte) (*BaseEvent, error) {
	var event BaseEvent
	err := json.Unmarshal(data, &event)
	return &event, err
}

// UserEvent represents user-related events
type UserEvent struct {
	*BaseEvent
	Email     string `json:"email,omitempty"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Role      string `json:"role,omitempty"`
}

// NewUserEvent creates a new user event
func NewUserEvent(eventType EventType, userID string) *UserEvent {
	base := NewBaseEvent(eventType, "user-service")
	base.WithUserID(userID)
	
	return &UserEvent{
		BaseEvent: base,
	}
}

// ContentEvent represents content-related events
type ContentEvent struct {
	*BaseEvent
	ContentID    string `json:"content_id,omitempty"`
	ContentType  string `json:"content_type,omitempty"`
	ContentTitle string `json:"content_title,omitempty"`
	Platform     string `json:"platform,omitempty"`
	PostID       string `json:"post_id,omitempty"`
	TemplateID   string `json:"template_id,omitempty"`
}

// NewContentEvent creates a new content event
func NewContentEvent(eventType EventType, userID string) *ContentEvent {
	base := NewBaseEvent(eventType, "content-service")
	base.WithUserID(userID)
	
	return &ContentEvent{
		BaseEvent: base,
	}
}

// BillingEvent represents billing-related events
type BillingEvent struct {
	*BaseEvent
	SubscriptionID string  `json:"subscription_id,omitempty"`
	PaymentID      string  `json:"payment_id,omitempty"`
	Amount         float64 `json:"amount,omitempty"`
	Currency       string  `json:"currency,omitempty"`
	Credits        int     `json:"credits,omitempty"`
	PlanName       string  `json:"plan_name,omitempty"`
}

// NewBillingEvent creates a new billing event
func NewBillingEvent(eventType EventType, userID string) *BillingEvent {
	base := NewBaseEvent(eventType, "billing-service")
	base.WithUserID(userID)
	
	return &BillingEvent{
		BaseEvent: base,
	}
}

// IntegrationEvent represents integration-related events
type IntegrationEvent struct {
	*BaseEvent
	IntegrationID   string `json:"integration_id,omitempty"`
	IntegrationType string `json:"integration_type,omitempty"`
	PlatformName    string `json:"platform_name,omitempty"`
	AccountName     string `json:"account_name,omitempty"`
	Error           string `json:"error,omitempty"`
}

// NewIntegrationEvent creates a new integration event
func NewIntegrationEvent(eventType EventType, userID string) *IntegrationEvent {
	base := NewBaseEvent(eventType, "integration-service")
	base.WithUserID(userID)
	
	return &IntegrationEvent{
		BaseEvent: base,
	}
}

// AssetEvent represents asset-related events
type AssetEvent struct {
	*BaseEvent
	AssetID      string `json:"asset_id,omitempty"`
	AssetType    string `json:"asset_type,omitempty"`
	AssetURL     string `json:"asset_url,omitempty"`
	FileName     string `json:"file_name,omitempty"`
	FileSize     int64  `json:"file_size,omitempty"`
	ContentType  string `json:"content_type,omitempty"`
	ValidationStatus string `json:"validation_status,omitempty"`
}

// NewAssetEvent creates a new asset event
func NewAssetEvent(eventType EventType, userID string) *AssetEvent {
	base := NewBaseEvent(eventType, "asset-service")
	base.WithUserID(userID)
	
	return &AssetEvent{
		BaseEvent: base,
	}
}

// RAGEvent represents RAG processing events
type RAGEvent struct {
	*BaseEvent
	ProcessingID string `json:"processing_id,omitempty"`
	DocumentID   string `json:"document_id,omitempty"`
	TemplateID   string `json:"template_id,omitempty"`
	Status       string `json:"status,omitempty"`
	Progress     int    `json:"progress,omitempty"`
	Error        string `json:"error,omitempty"`
}

// NewRAGEvent creates a new RAG event
func NewRAGEvent(eventType EventType, userID string) *RAGEvent {
	base := NewBaseEvent(eventType, "rag-processing-service")
	base.WithUserID(userID)
	
	return &RAGEvent{
		BaseEvent: base,
	}
}

// SystemEvent represents system-wide events
type SystemEvent struct {
	*BaseEvent
	Component        string    `json:"component,omitempty"`
	MaintenanceID    string    `json:"maintenance_id,omitempty"`
	Title            string    `json:"title,omitempty"`
	Description      string    `json:"description,omitempty"`
	StartTime        time.Time `json:"start_time,omitempty"`
	EndTime          time.Time `json:"end_time,omitempty"`
	AffectedServices []string  `json:"affected_services,omitempty"`
}

// NewSystemEvent creates a new system event
func NewSystemEvent(eventType EventType) *SystemEvent {
	base := NewBaseEvent(eventType, "system")
	
	return &SystemEvent{
		BaseEvent: base,
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
)

// NotificationTemplateCreate is the builder for creating a NotificationTemplate entity.
type NotificationTemplateCreate struct {
	config
	mutation *NotificationTemplateMutation
	hooks    []Hook
}

// SetEventType sets the "event_type" field.
func (ntc *NotificationTemplateCreate) SetEventType(s string) *NotificationTemplateCreate {
	ntc.mutation.SetEventType(s)
	return ntc
}

// SetName sets the "name" field.
func (ntc *NotificationTemplateCreate) SetName(s string) *NotificationTemplateCreate {
	ntc.mutation.SetName(s)
	return ntc
}

// SetDescription sets the "description" field.
func (ntc *NotificationTemplateCreate) SetDescription(s string) *NotificationTemplateCreate {
	ntc.mutation.SetDescription(s)
	return ntc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableDescription(s *string) *NotificationTemplateCreate {
	if s != nil {
		ntc.SetDescription(*s)
	}
	return ntc
}

// SetTemplates sets the "templates" field.
func (ntc *NotificationTemplateCreate) SetTemplates(m map[string]interface{}) *NotificationTemplateCreate {
	ntc.mutation.SetTemplates(m)
	return ntc
}

// SetIsActive sets the "is_active" field.
func (ntc *NotificationTemplateCreate) SetIsActive(b bool) *NotificationTemplateCreate {
	ntc.mutation.SetIsActive(b)
	return ntc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableIsActive(b *bool) *NotificationTemplateCreate {
	if b != nil {
		ntc.SetIsActive(*b)
	}
	return ntc
}

// SetVariables sets the "variables" field.
func (ntc *NotificationTemplateCreate) SetVariables(s []string) *NotificationTemplateCreate {
	ntc.mutation.SetVariables(s)
	return ntc
}

// SetDefaultChannels sets the "default_channels" field.
func (ntc *NotificationTemplateCreate) SetDefaultChannels(s []string) *NotificationTemplateCreate {
	ntc.mutation.SetDefaultChannels(s)
	return ntc
}

// SetDefaultPriority sets the "default_priority" field.
func (ntc *NotificationTemplateCreate) SetDefaultPriority(np notificationtemplate.DefaultPriority) *NotificationTemplateCreate {
	ntc.mutation.SetDefaultPriority(np)
	return ntc
}

// SetNillableDefaultPriority sets the "default_priority" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableDefaultPriority(np *notificationtemplate.DefaultPriority) *NotificationTemplateCreate {
	if np != nil {
		ntc.SetDefaultPriority(*np)
	}
	return ntc
}

// SetLanguage sets the "language" field.
func (ntc *NotificationTemplateCreate) SetLanguage(s string) *NotificationTemplateCreate {
	ntc.mutation.SetLanguage(s)
	return ntc
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableLanguage(s *string) *NotificationTemplateCreate {
	if s != nil {
		ntc.SetLanguage(*s)
	}
	return ntc
}

// SetVersion sets the "version" field.
func (ntc *NotificationTemplateCreate) SetVersion(s string) *NotificationTemplateCreate {
	ntc.mutation.SetVersion(s)
	return ntc
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableVersion(s *string) *NotificationTemplateCreate {
	if s != nil {
		ntc.SetVersion(*s)
	}
	return ntc
}

// SetMetadata sets the "metadata" field.
func (ntc *NotificationTemplateCreate) SetMetadata(m map[string]interface{}) *NotificationTemplateCreate {
	ntc.mutation.SetMetadata(m)
	return ntc
}

// SetCreatedBy sets the "created_by" field.
func (ntc *NotificationTemplateCreate) SetCreatedBy(s string) *NotificationTemplateCreate {
	ntc.mutation.SetCreatedBy(s)
	return ntc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableCreatedBy(s *string) *NotificationTemplateCreate {
	if s != nil {
		ntc.SetCreatedBy(*s)
	}
	return ntc
}

// SetCreatedAt sets the "created_at" field.
func (ntc *NotificationTemplateCreate) SetCreatedAt(t time.Time) *NotificationTemplateCreate {
	ntc.mutation.SetCreatedAt(t)
	return ntc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableCreatedAt(t *time.Time) *NotificationTemplateCreate {
	if t != nil {
		ntc.SetCreatedAt(*t)
	}
	return ntc
}

// SetUpdatedAt sets the "updated_at" field.
func (ntc *NotificationTemplateCreate) SetUpdatedAt(t time.Time) *NotificationTemplateCreate {
	ntc.mutation.SetUpdatedAt(t)
	return ntc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableUpdatedAt(t *time.Time) *NotificationTemplateCreate {
	if t != nil {
		ntc.SetUpdatedAt(*t)
	}
	return ntc
}

// SetDeletedAt sets the "deleted_at" field.
func (ntc *NotificationTemplateCreate) SetDeletedAt(t time.Time) *NotificationTemplateCreate {
	ntc.mutation.SetDeletedAt(t)
	return ntc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableDeletedAt(t *time.Time) *NotificationTemplateCreate {
	if t != nil {
		ntc.SetDeletedAt(*t)
	}
	return ntc
}

// SetID sets the "id" field.
func (ntc *NotificationTemplateCreate) SetID(u uuid.UUID) *NotificationTemplateCreate {
	ntc.mutation.SetID(u)
	return ntc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ntc *NotificationTemplateCreate) SetNillableID(u *uuid.UUID) *NotificationTemplateCreate {
	if u != nil {
		ntc.SetID(*u)
	}
	return ntc
}

// Mutation returns the NotificationTemplateMutation object of the builder.
func (ntc *NotificationTemplateCreate) Mutation() *NotificationTemplateMutation {
	return ntc.mutation
}

// Save creates the NotificationTemplate in the database.
func (ntc *NotificationTemplateCreate) Save(ctx context.Context) (*NotificationTemplate, error) {
	ntc.defaults()
	return withHooks(ctx, ntc.sqlSave, ntc.mutation, ntc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ntc *NotificationTemplateCreate) SaveX(ctx context.Context) *NotificationTemplate {
	v, err := ntc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ntc *NotificationTemplateCreate) Exec(ctx context.Context) error {
	_, err := ntc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ntc *NotificationTemplateCreate) ExecX(ctx context.Context) {
	if err := ntc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ntc *NotificationTemplateCreate) defaults() {
	if _, ok := ntc.mutation.IsActive(); !ok {
		v := notificationtemplate.DefaultIsActive
		ntc.mutation.SetIsActive(v)
	}
	if _, ok := ntc.mutation.DefaultPriority(); !ok {
		v := notificationtemplate.DefaultDefaultPriority
		ntc.mutation.SetDefaultPriority(v)
	}
	if _, ok := ntc.mutation.Language(); !ok {
		v := notificationtemplate.DefaultLanguage
		ntc.mutation.SetLanguage(v)
	}
	if _, ok := ntc.mutation.Version(); !ok {
		v := notificationtemplate.DefaultVersion
		ntc.mutation.SetVersion(v)
	}
	if _, ok := ntc.mutation.CreatedAt(); !ok {
		v := notificationtemplate.DefaultCreatedAt()
		ntc.mutation.SetCreatedAt(v)
	}
	if _, ok := ntc.mutation.UpdatedAt(); !ok {
		v := notificationtemplate.DefaultUpdatedAt()
		ntc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ntc.mutation.ID(); !ok {
		v := notificationtemplate.DefaultID()
		ntc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ntc *NotificationTemplateCreate) check() error {
	if _, ok := ntc.mutation.EventType(); !ok {
		return &ValidationError{Name: "event_type", err: errors.New(`ent: missing required field "NotificationTemplate.event_type"`)}
	}
	if v, ok := ntc.mutation.EventType(); ok {
		if err := notificationtemplate.EventTypeValidator(v); err != nil {
			return &ValidationError{Name: "event_type", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.event_type": %w`, err)}
		}
	}
	if _, ok := ntc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "NotificationTemplate.name"`)}
	}
	if v, ok := ntc.mutation.Name(); ok {
		if err := notificationtemplate.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.name": %w`, err)}
		}
	}
	if _, ok := ntc.mutation.Templates(); !ok {
		return &ValidationError{Name: "templates", err: errors.New(`ent: missing required field "NotificationTemplate.templates"`)}
	}
	if _, ok := ntc.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "NotificationTemplate.is_active"`)}
	}
	if _, ok := ntc.mutation.DefaultPriority(); !ok {
		return &ValidationError{Name: "default_priority", err: errors.New(`ent: missing required field "NotificationTemplate.default_priority"`)}
	}
	if v, ok := ntc.mutation.DefaultPriority(); ok {
		if err := notificationtemplate.DefaultPriorityValidator(v); err != nil {
			return &ValidationError{Name: "default_priority", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.default_priority": %w`, err)}
		}
	}
	if _, ok := ntc.mutation.Language(); !ok {
		return &ValidationError{Name: "language", err: errors.New(`ent: missing required field "NotificationTemplate.language"`)}
	}
	if v, ok := ntc.mutation.Language(); ok {
		if err := notificationtemplate.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.language": %w`, err)}
		}
	}
	if _, ok := ntc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "NotificationTemplate.version"`)}
	}
	if v, ok := ntc.mutation.Version(); ok {
		if err := notificationtemplate.VersionValidator(v); err != nil {
			return &ValidationError{Name: "version", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.version": %w`, err)}
		}
	}
	if v, ok := ntc.mutation.CreatedBy(); ok {
		if err := notificationtemplate.CreatedByValidator(v); err != nil {
			return &ValidationError{Name: "created_by", err: fmt.Errorf(`ent: validator failed for field "NotificationTemplate.created_by": %w`, err)}
		}
	}
	if _, ok := ntc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "NotificationTemplate.created_at"`)}
	}
	if _, ok := ntc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "NotificationTemplate.updated_at"`)}
	}
	return nil
}

func (ntc *NotificationTemplateCreate) sqlSave(ctx context.Context) (*NotificationTemplate, error) {
	if err := ntc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ntc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ntc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ntc.mutation.id = &_node.ID
	ntc.mutation.done = true
	return _node, nil
}

func (ntc *NotificationTemplateCreate) createSpec() (*NotificationTemplate, *sqlgraph.CreateSpec) {
	var (
		_node = &NotificationTemplate{config: ntc.config}
		_spec = sqlgraph.NewCreateSpec(notificationtemplate.Table, sqlgraph.NewFieldSpec(notificationtemplate.FieldID, field.TypeUUID))
	)
	if id, ok := ntc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ntc.mutation.EventType(); ok {
		_spec.SetField(notificationtemplate.FieldEventType, field.TypeString, value)
		_node.EventType = value
	}
	if value, ok := ntc.mutation.Name(); ok {
		_spec.SetField(notificationtemplate.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := ntc.mutation.Description(); ok {
		_spec.SetField(notificationtemplate.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := ntc.mutation.Templates(); ok {
		_spec.SetField(notificationtemplate.FieldTemplates, field.TypeJSON, value)
		_node.Templates = value
	}
	if value, ok := ntc.mutation.IsActive(); ok {
		_spec.SetField(notificationtemplate.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := ntc.mutation.Variables(); ok {
		_spec.SetField(notificationtemplate.FieldVariables, field.TypeJSON, value)
		_node.Variables = value
	}
	if value, ok := ntc.mutation.DefaultChannels(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultChannels, field.TypeJSON, value)
		_node.DefaultChannels = value
	}
	if value, ok := ntc.mutation.DefaultPriority(); ok {
		_spec.SetField(notificationtemplate.FieldDefaultPriority, field.TypeEnum, value)
		_node.DefaultPriority = value
	}
	if value, ok := ntc.mutation.Language(); ok {
		_spec.SetField(notificationtemplate.FieldLanguage, field.TypeString, value)
		_node.Language = value
	}
	if value, ok := ntc.mutation.Version(); ok {
		_spec.SetField(notificationtemplate.FieldVersion, field.TypeString, value)
		_node.Version = value
	}
	if value, ok := ntc.mutation.Metadata(); ok {
		_spec.SetField(notificationtemplate.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ntc.mutation.CreatedBy(); ok {
		_spec.SetField(notificationtemplate.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := ntc.mutation.CreatedAt(); ok {
		_spec.SetField(notificationtemplate.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ntc.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationtemplate.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ntc.mutation.DeletedAt(); ok {
		_spec.SetField(notificationtemplate.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// NotificationTemplateCreateBulk is the builder for creating many NotificationTemplate entities in bulk.
type NotificationTemplateCreateBulk struct {
	config
	err      error
	builders []*NotificationTemplateCreate
}

// Save creates the NotificationTemplate entities in the database.
func (ntcb *NotificationTemplateCreateBulk) Save(ctx context.Context) ([]*NotificationTemplate, error) {
	if ntcb.err != nil {
		return nil, ntcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ntcb.builders))
	nodes := make([]*NotificationTemplate, len(ntcb.builders))
	mutators := make([]Mutator, len(ntcb.builders))
	for i := range ntcb.builders {
		func(i int, root context.Context) {
			builder := ntcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationTemplateMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ntcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ntcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ntcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ntcb *NotificationTemplateCreateBulk) SaveX(ctx context.Context) []*NotificationTemplate {
	v, err := ntcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ntcb *NotificationTemplateCreateBulk) Exec(ctx context.Context) error {
	_, err := ntcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ntcb *NotificationTemplateCreateBulk) ExecX(ctx context.Context) {
	if err := ntcb.Exec(ctx); err != nil {
		panic(err)
	}
}

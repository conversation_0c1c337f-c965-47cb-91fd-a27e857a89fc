# User Cleanup Service

## Overview

The User Cleanup Service automatically removes unverified users after a configurable time period (default: 24 hours) to maintain database hygiene and comply with data retention policies.

## Features

- **Automatic Cleanup**: Removes unverified users after TTL expires
- **Warning Notifications**: Sends warning emails before deletion
- **Batch Processing**: Processes users in configurable batches
- **Event Publishing**: Publishes Kafka events for deletion tracking
- **Admin API**: RESTful API for monitoring and control
- **Configurable**: Fully configurable via YAML config

## Configuration

### Basic Settings

```yaml
cleanup:
  enabled: true                  # Enable/disable cleanup service
  auto_start: true              # Auto-start service on application startup
  unverified_user_ttl: "24h"    # Time to live for unverified users
  cleanup_interval: "1h"        # How often to run cleanup
  batch_size: 100               # Number of users to process per batch
  enable_notifications: true    # Send warning notifications
  warning_time: "2h"            # Time before deletion to send warning
```

### Environment-Specific Configurations

#### Development
- TTL: 1 hour (for testing)
- Auto-start: disabled
- Notifications: disabled
- Cleanup interval: 10 minutes

#### Production
- TTL: 24 hours (standard)
- Auto-start: enabled
- Notifications: enabled
- Cleanup interval: 1 hour

## How It Works

### 1. User Registration
- User registers but doesn't verify email
- User is created with `is_verified = false`
- Verification email is sent

### 2. Warning Phase
- 2 hours before deletion (configurable)
- Warning email is sent via Notification Service
- Kafka event `user.deletion_warning` is published
- User is marked as warned in `notification_settings`

### 3. Deletion Phase
- After TTL expires (24 hours by default)
- User is soft-deleted (`deleted_at` timestamp set)
- Kafka event `user.deleted_unverified` is published
- Cleanup stats are updated

### 4. Event Flow

```mermaid
graph TD
    A[User Registers] --> B[Email Verification Sent]
    B --> C{User Verifies?}
    C -->|Yes| D[User Active]
    C -->|No| E[Warning Timer Starts]
    E --> F[Warning Email Sent]
    F --> G{User Verifies?}
    G -->|Yes| D
    G -->|No| H[User Deleted After TTL]
    H --> I[Deletion Event Published]
```

## API Endpoints

### Admin Cleanup Management

All endpoints require admin authentication.

#### Get Cleanup Statistics
```http
GET /api/v1/admin/cleanup/stats
```

Response:
```json
{
  "total_unverified": 150,
  "expired_count": 25,
  "warning_count": 10,
  "next_cleanup_in": "45m30s",
  "unverified_user_ttl": "24h0m0s",
  "service_running": true
}
```

#### Get Service Status
```http
GET /api/v1/admin/cleanup/status
```

Response:
```json
{
  "service_running": true,
  "service_name": "user-cleanup-service",
  "status": "running",
  "message": "Cleanup service is running normally"
}
```

#### Trigger Manual Cleanup
```http
POST /api/v1/admin/cleanup/trigger
```

#### Start/Stop Service
```http
POST /api/v1/admin/cleanup/start
POST /api/v1/admin/cleanup/stop
```

## Kafka Events

### User Deletion Warning
```json
{
  "event_type": "user.deletion_warning",
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2023-12-01T10:00:00Z",
  "data": {
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "deletion_time": "2023-12-02T12:00:00Z",
    "verification_url": "https://app.social-content-ai.com/auth/resend-verification?email=<EMAIL>"
  }
}
```

### User Deleted (Unverified)
```json
{
  "event_type": "user.deleted_unverified",
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2023-12-02T10:00:00Z",
  "data": {
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "created_at": "2023-12-01T10:00:00Z",
    "reason": "email_not_verified",
    "ttl_hours": "24",
    "age_hours": 24.5
  }
}
```

## Monitoring

### Metrics
- `cleanup_users_deleted_total`: Total users deleted
- `cleanup_warnings_sent_total`: Total warnings sent
- `cleanup_batch_duration_seconds`: Time taken per cleanup batch
- `cleanup_errors_total`: Total cleanup errors

### Logs
- All cleanup operations are logged with structured logging
- Log levels: INFO for normal operations, ERROR for failures
- Includes user IDs, emails, and timestamps

### Health Checks
- Service status available via `/api/v1/admin/cleanup/status`
- Integrated with application health checks

## Security Considerations

### Data Protection
- Soft deletion preserves audit trail
- User data can be recovered if needed
- Complies with GDPR "right to be forgotten"

### Access Control
- Admin-only API endpoints
- JWT authentication required
- Role-based access control

### Rate Limiting
- Batch processing prevents database overload
- Configurable batch sizes
- Graceful error handling

## Troubleshooting

### Common Issues

#### Service Not Starting
```bash
# Check configuration
kubectl logs user-service-pod | grep cleanup

# Verify database connectivity
kubectl exec user-service-pod -- pg_isready

# Check Kafka connectivity
kubectl exec user-service-pod -- kafka-topics --list
```

#### High Memory Usage
- Reduce `batch_size` in configuration
- Increase `cleanup_interval` to reduce frequency
- Monitor database query performance

#### Missing Notifications
- Verify Notification Service connectivity
- Check Kafka broker availability
- Review notification service logs

### Debug Mode
Enable debug logging for detailed cleanup operations:

```yaml
logging:
  level: "debug"
```

## Best Practices

### Configuration
- Set appropriate TTL based on business requirements
- Configure warning time to give users adequate notice
- Use smaller batch sizes in high-traffic environments

### Monitoring
- Set up alerts for cleanup failures
- Monitor deletion rates and trends
- Track user verification rates

### Testing
- Test cleanup in development environment
- Verify notification delivery
- Validate Kafka event publishing

## Migration Guide

### Enabling Cleanup on Existing System

1. **Update Configuration**
   ```yaml
   cleanup:
     enabled: true
     auto_start: false  # Start manually first time
   ```

2. **Run Initial Cleanup**
   ```http
   POST /api/v1/admin/cleanup/trigger
   ```

3. **Monitor Results**
   ```http
   GET /api/v1/admin/cleanup/stats
   ```

4. **Enable Auto-Start**
   ```yaml
   cleanup:
     auto_start: true
   ```

### Rollback Plan
- Set `enabled: false` to disable cleanup
- Restore soft-deleted users if needed:
  ```sql
  UPDATE users SET deleted_at = NULL WHERE deleted_at IS NOT NULL AND is_verified = false;
  ```

## Performance Impact

### Database
- Minimal impact due to batch processing
- Uses efficient queries with proper indexing
- Soft deletion preserves referential integrity

### Memory
- Low memory footprint
- Configurable batch sizes
- Garbage collection friendly

### Network
- Async Kafka publishing
- Non-blocking notification sending
- Graceful degradation if external services unavailable

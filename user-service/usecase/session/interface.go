package session

import (
	"context"

	"github.com/social-content-ai/user-service/ent"
)

// UseCase defines the session business logic interface
type UseCase interface {
	// Session management
	CreateSession(ctx context.Context, req *CreateSessionRequest) (*ent.Session, error)
	GetSession(ctx context.Context, id string) (*ent.Session, error)
	ListSessions(ctx context.Context, req *ListSessionsRequest) ([]*ent.Session, int, error)
	RevokeSession(ctx context.Context, id string, reason string) error
	RevokeAllSessions(ctx context.Context, userID string, exceptSessionID string) error

	// Session validation
	IsSessionActive(ctx context.Context, id string) (bool, error)
	RefreshSession(ctx context.Context, id string) (*ent.Session, error)

	// Cleanup
	CleanupExpiredSessions(ctx context.Context) (int, error)
}

// CreateSessionRequest represents session creation request
type CreateSessionRequest struct {
	UserID          string `json:"user_id" validate:"required,uuid"`
	DeviceInfo      string `json:"device_info,omitempty"`
	IPAddress       string `json:"ip_address,omitempty"`
	DurationSeconds int64  `json:"duration_seconds" validate:"min=300,max=604800"` // 5 minutes to 7 days
}

// ListSessionsRequest represents session listing request
type ListSessionsRequest struct {
	UserID    string `json:"user_id" validate:"required,uuid"`
	Page      int    `json:"page" validate:"min=1"`
	Limit     int    `json:"limit" validate:"min=1,max=100"`
	Status    string `json:"status,omitempty" validate:"omitempty,oneof=active expired revoked"`
	SortBy    string `json:"sort_by,omitempty" validate:"omitempty,oneof=created_at login_time expires_at"`
	SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc"`
}

// SessionResponse represents session response
type SessionResponse struct {
	ID         string `json:"id"`
	UserID     string `json:"user_id"`
	DeviceInfo string `json:"device_info,omitempty"`
	Status     string `json:"status"`
	IPAddress  string `json:"ip_address,omitempty"`
	LoginTime  string `json:"login_time"`
	LogoutTime string `json:"logout_time,omitempty"`
	ExpiresAt  string `json:"expires_at,omitempty"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}

// ToSessionResponse converts ent.Session to SessionResponse
func ToSessionResponse(session *ent.Session) *SessionResponse {
	if session == nil {
		return nil
	}

	response := &SessionResponse{
		ID:         session.ID.String(),
		UserID:     session.UserID.String(),
		DeviceInfo: session.DeviceInfo,
		Status:     string(session.Status),
		IPAddress:  session.IPAddress,
		LoginTime:  session.LoginTime.Format("2006-01-02T15:04:05Z07:00"),
		CreatedAt:  session.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:  session.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	if !session.LogoutTime.IsZero() {
		logoutTime := session.LogoutTime.Format("2006-01-02T15:04:05Z07:00")
		response.LogoutTime = logoutTime
	}

	if !session.ExpiresAt.IsZero() {
		expiresAt := session.ExpiresAt.Format("2006-01-02T15:04:05Z07:00")
		response.ExpiresAt = expiresAt
	}

	return response
}

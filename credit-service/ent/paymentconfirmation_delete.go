// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// PaymentConfirmationDelete is the builder for deleting a PaymentConfirmation entity.
type PaymentConfirmationDelete struct {
	config
	hooks    []Hook
	mutation *PaymentConfirmationMutation
}

// Where appends a list predicates to the PaymentConfirmationDelete builder.
func (pcd *PaymentConfirmationDelete) Where(ps ...predicate.PaymentConfirmation) *PaymentConfirmationDelete {
	pcd.mutation.Where(ps...)
	return pcd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (pcd *PaymentConfirmationDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, pcd.sqlExec, pcd.mutation, pcd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (pcd *PaymentConfirmationDelete) ExecX(ctx context.Context) int {
	n, err := pcd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (pcd *PaymentConfirmationDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(paymentconfirmation.Table, sqlgraph.NewFieldSpec(paymentconfirmation.FieldID, field.TypeUUID))
	if ps := pcd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, pcd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	pcd.mutation.done = true
	return affected, err
}

// PaymentConfirmationDeleteOne is the builder for deleting a single PaymentConfirmation entity.
type PaymentConfirmationDeleteOne struct {
	pcd *PaymentConfirmationDelete
}

// Where appends a list predicates to the PaymentConfirmationDelete builder.
func (pcdo *PaymentConfirmationDeleteOne) Where(ps ...predicate.PaymentConfirmation) *PaymentConfirmationDeleteOne {
	pcdo.pcd.mutation.Where(ps...)
	return pcdo
}

// Exec executes the deletion query.
func (pcdo *PaymentConfirmationDeleteOne) Exec(ctx context.Context) error {
	n, err := pcdo.pcd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{paymentconfirmation.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (pcdo *PaymentConfirmationDeleteOne) ExecX(ctx context.Context) {
	if err := pcdo.Exec(ctx); err != nil {
		panic(err)
	}
}

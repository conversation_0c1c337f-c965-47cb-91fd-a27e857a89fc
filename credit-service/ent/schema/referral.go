package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Referral holds the schema definition for the Referral entity.
type Referral struct {
	ent.Schema
}

// Fields of the Referral.
func (Referral) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("referrer_id", uuid.UUID{}), // User who made the referral
		field.UUID("referred_id", uuid.UUID{}).
			Optional(), // User who was referred (null until signup)
		field.String("referral_code").
			MaxLen(20).
			Unique(), // Unique referral code
		field.String("referred_email").
			MaxLen(255).
			Optional(), // Email of referred user
		field.Enum("status").
			Values("pending", "completed", "expired", "cancelled").
			Default("pending"),
		field.Int("referrer_credits_earned").
			Default(0).
			Min(0), // Credits earned by referrer
		field.Int("referred_credits_earned").
			Default(0).
			Min(0), // Credits earned by referred user
		field.String("referral_type").
			MaxLen(50).
			Default("signup"), // 'signup', 'purchase', 'subscription'
		field.Time("completed_at").
			Optional(), // When referral was completed
		field.Time("expires_at").
			Optional(), // When referral expires
		field.JSON("referral_conditions", map[string]interface{}{}).
			Optional(), // Conditions for referral completion
		field.JSON("tracking_data", map[string]interface{}{}).
			Optional(), // UTM parameters, source, etc.
		field.String("campaign_id").
			MaxLen(50).
			Optional(), // Marketing campaign ID
		field.String("source").
			MaxLen(100).
			Optional(), // Referral source (social, email, etc.)
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Referral.
func (Referral) Edges() []ent.Edge {
	return nil
}

// Indexes of the Referral.
func (Referral) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("referrer_id"),
		index.Fields("referred_id"),
		index.Fields("referral_code"),
		index.Fields("referred_email"),
		index.Fields("status"),
		index.Fields("referral_type"),
		index.Fields("completed_at"),
		index.Fields("expires_at"),
		index.Fields("campaign_id"),
		index.Fields("source"),
		index.Fields("created_at"),
	}
}

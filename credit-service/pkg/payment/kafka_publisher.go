package payment

import (
	"context"
	"encoding/json"
	"fmt"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaEventPublisher implements EventPublisher using Kafka
type KafkaEventPublisher struct {
	producer sharedKafka.Producer
	logger   logging.Logger
}

// NewKafkaEventPublisher creates a new Kafka event publisher
func NewKafkaEventPublisher(producer sharedKafka.Producer, logger logging.Logger) *KafkaEventPublisher {
	return &KafkaEventPublisher{
		producer: producer,
		logger:   logger,
	}
}

// PublishEvent publishes an event to Kafka topic
func (k *KafkaEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	k.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Publishing event to Kafka")

	// Serialize event to JSON
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	// Create Kafka message
	message := &sharedKafka.Message{
		Topic: topic,
		Key:   k.generateMessageKey(event),
		Value: eventData,
		Headers: map[string]string{
			"event_type": fmt.Sprintf("%T", event),
			"source":     "credit-service",
		},
	}

	// Publish to Kafka
	err = k.producer.Publish(ctx, message)
	if err != nil {
		k.logger.WithError(err).WithField("topic", topic).Error("Failed to publish event to Kafka")
		return fmt.Errorf("failed to publish event to Kafka: %w", err)
	}

	k.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"key":   message.Key,
	}).Info("Successfully published event to Kafka")

	return nil
}

// generateMessageKey generates a message key for partitioning
func (k *KafkaEventPublisher) generateMessageKey(event interface{}) string {
	switch e := event.(type) {
	case *SubscriptionUpdateEvent:
		if e.SubscriptionID != "" {
			return e.SubscriptionID
		}
		if e.UserID != "" {
			return e.UserID
		}
		return e.EventID
	default:
		return "default"
	}
}

// MockEventPublisher implements EventPublisher for testing
type MockEventPublisher struct {
	logger logging.Logger
	events []PublishedEvent
}

// PublishedEvent represents a published event for testing
type PublishedEvent struct {
	Topic string
	Event interface{}
}

// NewMockEventPublisher creates a new mock event publisher
func NewMockEventPublisher(logger logging.Logger) *MockEventPublisher {
	return &MockEventPublisher{
		logger: logger,
		events: make([]PublishedEvent, 0),
	}
}

// PublishEvent mocks event publishing
func (m *MockEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Mock publishing event")

	// Store event for testing
	m.events = append(m.events, PublishedEvent{
		Topic: topic,
		Event: event,
	})

	return nil
}

// GetPublishedEvents returns all published events (for testing)
func (m *MockEventPublisher) GetPublishedEvents() []PublishedEvent {
	return m.events
}

// ClearEvents clears all published events (for testing)
func (m *MockEventPublisher) ClearEvents() {
	m.events = make([]PublishedEvent, 0)
}

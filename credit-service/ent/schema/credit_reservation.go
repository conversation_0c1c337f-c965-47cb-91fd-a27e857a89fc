package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// CreditReservation holds the schema definition for the CreditReservation entity.
type CreditReservation struct {
	ent.Schema
}

// Fields of the CreditReservation.
func (CreditReservation) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.Int("amount").
			Min(1),
		field.String("purpose").
			MaxLen(100),
		field.String("reference_id").
			MaxLen(100).
			Optional(),
		field.String("reference_type").
			MaxLen(50).
			Optional(),
		field.Enum("status").
			Values("reserved", "consumed", "expired", "cancelled").
			Default("reserved"),
		field.Time("expires_at"),
		field.Time("consumed_at").
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the CreditReservation.
func (CreditReservation) Edges() []ent.Edge {
	return nil
}

// Indexes of the CreditReservation.
func (CreditReservation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("status"),
		index.Fields("expires_at"),
		index.Fields("reference_id"),
		index.Fields("reference_type"),
		index.Fields("created_at"),
	}
}

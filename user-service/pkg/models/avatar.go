package models

import (
	"fmt"
	"time"
)

// RequestAvatarUploadRequest represents a request to upload avatar
type RequestAvatarUploadRequest struct {
	UserID   uint64 `json:"user_id,omitempty"`
	FileName string `json:"file_name" binding:"required" example:"avatar.jpg"`
	FileType string `json:"file_type" binding:"required" example:"image/jpeg"`
	FileSize int64  `json:"file_size" binding:"required" example:"1048576"`
}

// RequestAvatarUploadResponse represents the response with upload URL
type RequestAvatarUploadResponse struct {
	UploadID     string              `json:"upload_id" example:"upload_123456"`
	PresignedURL string              `json:"presigned_url" example:"https://s3.amazonaws.com/bucket/key?signature=..."`
	S3Key        string              `json:"s3_key" example:"avatars/user_123/avatar_456.jpg"`
	S3Bucket     string              `json:"s3_bucket" example:"social-content-ai-assets"`
	ExpiresAt    time.Time           `json:"expires_at" example:"2023-12-01T11:00:00Z"`
	UploadFields map[string]string   `json:"upload_fields,omitempty"`
	Instructions *UploadInstructions `json:"instructions"`
}

// UploadInstructions provides guidance for frontend upload
type UploadInstructions struct {
	Method       string            `json:"method" example:"PUT"`
	URL          string            `json:"url" example:"https://s3.amazonaws.com/bucket/key?signature=..."`
	Headers      map[string]string `json:"headers,omitempty"`
	FormFields   map[string]string `json:"form_fields,omitempty"`
	MaxFileSize  int64             `json:"max_file_size" example:"5242880"`
	AllowedTypes []string          `json:"allowed_types" example:"image/jpeg,image/png,image/gif"`
}

// ConfirmAvatarUploadRequest represents avatar upload confirmation
type ConfirmAvatarUploadRequest struct {
	UserID   uint64 `json:"user_id,omitempty"`
	UploadID string `json:"upload_id" binding:"required" example:"upload_123456"`
	ETag     string `json:"etag" binding:"required" example:"d41d8cd98f00b204e9800998ecf8427e"`
}

// ConfirmAvatarUploadResponse represents avatar upload confirmation response
type ConfirmAvatarUploadResponse struct {
	AssetID      string `json:"asset_id" example:"asset_123456"`
	Status       string `json:"status" example:"validated"`
	AvatarURL    string `json:"avatar_url,omitempty" example:"https://cdn.social-content-ai.com/avatars/user_123/avatar_456.jpg"`
	ThumbnailURL string `json:"thumbnail_url,omitempty" example:"https://cdn.social-content-ai.com/avatars/user_123/thumb_456.jpg"`
	Message      string `json:"message" example:"Avatar uploaded and validated successfully"`
}

// GetAvatarResponse represents avatar information response
type GetAvatarResponse struct {
	AssetID      string    `json:"asset_id" example:"asset_123456"`
	AvatarURL    string    `json:"avatar_url" example:"https://cdn.social-content-ai.com/avatars/user_123/avatar_456.jpg"`
	ThumbnailURL string    `json:"thumbnail_url,omitempty" example:"https://cdn.social-content-ai.com/avatars/user_123/thumb_456.jpg"`
	FileSize     int64     `json:"file_size" example:"1048576"`
	FileType     string    `json:"file_type" example:"image/jpeg"`
	Status       string    `json:"status" example:"ready"`
	IsValid      bool      `json:"is_valid" example:"true"`
	UploadedAt   time.Time `json:"uploaded_at" example:"2023-12-01T10:00:00Z"`
}

// AvatarInfo represents detailed avatar information
type AvatarInfo struct {
	AssetID      string                  `json:"asset_id"`
	UserID       string                  `json:"user_id"`
	FileName     string                  `json:"file_name"`
	OriginalName string                  `json:"original_name"`
	FileType     string                  `json:"file_type"`
	FileSize     int64                   `json:"file_size"`
	S3Key        string                  `json:"s3_key"`
	S3Bucket     string                  `json:"s3_bucket"`
	Status       string                  `json:"status"`
	IsPublic     bool                    `json:"is_public"`
	AvatarURL    string                  `json:"avatar_url,omitempty"`
	ThumbnailURL string                  `json:"thumbnail_url,omitempty"`
	Metadata     map[string]string       `json:"metadata,omitempty"`
	CreatedAt    time.Time               `json:"created_at"`
	UpdatedAt    time.Time               `json:"updated_at"`
	Validation   *AvatarValidationResult `json:"validation,omitempty"`
}

// AvatarValidationResult contains avatar validation information
type AvatarValidationResult struct {
	IsValid     bool              `json:"is_valid"`
	Errors      []string          `json:"errors,omitempty"`
	Warnings    []string          `json:"warnings,omitempty"`
	IsSafe      bool              `json:"is_safe"`
	IsCorrupted bool              `json:"is_corrupted"`
	MimeType    string            `json:"mime_type"`
	Properties  map[string]string `json:"properties,omitempty"`
	ValidatedAt time.Time         `json:"validated_at"`
}

// Avatar status constants
const (
	AvatarStatusPending           = "pending"
	AvatarStatusValidated         = "validated"
	AvatarStatusProcessing        = "processing"
	AvatarStatusReady             = "ready"
	AvatarStatusFailed            = "failed"
	AvatarStatusPendingValidation = "pending_validation"
)

// Avatar file type constants
const (
	AvatarTypeJPEG = "image/jpeg"
	AvatarTypePNG  = "image/png"
	AvatarTypeGIF  = "image/gif"
	AvatarTypeWEBP = "image/webp"
)

// Avatar size limits
const (
	MaxAvatarFileSize = 5 * 1024 * 1024 // 5MB
	MinAvatarFileSize = 1024            // 1KB
)

// Helper functions

// IsValidAvatarType checks if the file type is valid for avatars
func IsValidAvatarType(fileType string) bool {
	switch fileType {
	case AvatarTypeJPEG, AvatarTypePNG, AvatarTypeGIF, AvatarTypeWEBP:
		return true
	default:
		return false
	}
}

// IsValidAvatarSize checks if the file size is within limits
func IsValidAvatarSize(fileSize int64) bool {
	return fileSize >= MinAvatarFileSize && fileSize <= MaxAvatarFileSize
}

// GetAllowedAvatarTypes returns all allowed avatar file types
func GetAllowedAvatarTypes() []string {
	return []string{AvatarTypeJPEG, AvatarTypePNG, AvatarTypeGIF, AvatarTypeWEBP}
}

// ValidateAvatarRequest validates avatar upload request
func (r *RequestAvatarUploadRequest) Validate() error {
	if r.FileName == "" {
		return fmt.Errorf("file name is required")
	}

	if !IsValidAvatarType(r.FileType) {
		return fmt.Errorf("invalid file type: %s", r.FileType)
	}

	if !IsValidAvatarSize(r.FileSize) {
		return fmt.Errorf("file size must be between %d and %d bytes", MinAvatarFileSize, MaxAvatarFileSize)
	}

	return nil
}

// GetFileExtension returns the file extension from file type
func GetFileExtensionFromType(fileType string) string {
	switch fileType {
	case AvatarTypeJPEG:
		return "jpg"
	case AvatarTypePNG:
		return "png"
	case AvatarTypeGIF:
		return "gif"
	case AvatarTypeWEBP:
		return "webp"
	default:
		return ""
	}
}

// BuildAvatarS3Key builds S3 key for avatar
func BuildAvatarS3Key(userID uint64, fileName string) string {
	return fmt.Sprintf("avatars/user_%d/%s", userID, fileName)
}

// IsAvatarReady checks if avatar is ready for use
func (a *AvatarInfo) IsReady() bool {
	return a.Status == AvatarStatusReady
}

// IsAvatarValid checks if avatar passed validation
func (a *AvatarInfo) IsValid() bool {
	return a.Validation != nil && a.Validation.IsValid
}

// GetSizeInMB returns avatar file size in megabytes
func (a *AvatarInfo) GetSizeInMB() float64 {
	return float64(a.FileSize) / (1024 * 1024)
}

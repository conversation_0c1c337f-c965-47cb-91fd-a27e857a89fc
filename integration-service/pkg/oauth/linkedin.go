package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// LinkedInOAuth handles LinkedIn OAuth flow
type LinkedInOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewLinkedInOAuth creates a new LinkedIn OAuth handler
func NewLinkedInOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) Provider {
	return &LinkedInOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// LinkedInTokenResponse represents LinkedIn token response
type LinkedInTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// LinkedInUserProfile represents LinkedIn user profile
type LinkedInUserProfile struct {
	ID        string `json:"id"`
	FirstName struct {
		Localized map[string]string `json:"localized"`
	} `json:"firstName"`
	LastName struct {
		Localized map[string]string `json:"localized"`
	} `json:"lastName"`
	ProfilePicture struct {
		DisplayImage struct {
			Elements []struct {
				Identifiers []struct {
					Identifier string `json:"identifier"`
				} `json:"identifiers"`
			} `json:"elements"`
		} `json:"displayImage"`
	} `json:"profilePicture"`
}

// GetAuthURL generates LinkedIn OAuth authorization URL
func (l *LinkedInOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://www.linkedin.com/oauth/v2/authorization"

	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", l.clientID)
	params.Set("redirect_uri", l.redirectURL)
	params.Set("state", state)

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, " "))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "r_liteprofile r_emailaddress w_member_social")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (l *LinkedInOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://www.linkedin.com/oauth/v2/accessToken"

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", l.redirectURL)
	data.Set("client_id", l.clientID)
	data.Set("client_secret", l.clientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := l.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp LinkedInTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	l.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.ExpiresIn,
		"scope":      tokenResp.Scope,
	}).Info("LinkedIn token exchanged successfully")

	// Convert LinkedInTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RefreshToken refreshes LinkedIn access token
func (l *LinkedInOAuth) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	tokenURL := "https://www.linkedin.com/oauth/v2/accessToken"

	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)
	data.Set("client_id", l.clientID)
	data.Set("client_secret", l.clientSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := l.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp LinkedInTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert LinkedInTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.TokenType,
		Scope:        tokenResp.Scope,
	}, nil
}

// RevokeToken revokes LinkedIn access token
func (l *LinkedInOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	// LinkedIn doesn't have a specific revoke endpoint
	// The token will expire naturally
	l.logger.Info("LinkedIn token marked for expiration")
	return nil
}

// GetUserProfile implements Provider interface
func (l *LinkedInOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	userURL := "https://api.linkedin.com/v2/people/~"

	params := url.Values{}
	params.Set("projection", "(id,firstName,lastName,profilePicture(displayImage~:playableStreams))")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", userURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := l.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var profile LinkedInUserProfile
	if err := json.NewDecoder(resp.Body).Decode(&profile); err != nil {
		return nil, fmt.Errorf("failed to decode user profile: %w", err)
	}

	// Extract name from localized fields
	var firstName, lastName string
	for _, name := range profile.FirstName.Localized {
		firstName = name
		break
	}
	for _, name := range profile.LastName.Localized {
		lastName = name
		break
	}

	displayName := strings.TrimSpace(firstName + " " + lastName)

	// Extract profile picture
	var avatarURL string
	if len(profile.ProfilePicture.DisplayImage.Elements) > 0 &&
		len(profile.ProfilePicture.DisplayImage.Elements[0].Identifiers) > 0 {
		avatarURL = profile.ProfilePicture.DisplayImage.Elements[0].Identifiers[0].Identifier
	}

	return &UserProfile{
		ID:          profile.ID,
		Username:    profile.ID, // LinkedIn doesn't have username, use ID
		DisplayName: displayName,
		AvatarURL:   avatarURL,
	}, nil
}

// GetPermissions implements Provider interface
func (l *LinkedInOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	// LinkedIn doesn't have a specific permissions endpoint
	// Return default permissions based on successful authentication
	return []string{"r_liteprofile", "r_emailaddress", "w_member_social"}, nil
}

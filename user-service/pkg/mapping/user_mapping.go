package mapping

import (
	"strconv"
	"strings"

	"google.golang.org/protobuf/types/known/timestamppb"

	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/pkg/models"
)

// ToProtoUser converts ent.User to proto User
func ToProtoUser(userEntity *ent.User) *userv1.User {
	if userEntity == nil {
		return nil
	}

	// Convert notification settings
	notificationSettings := make(map[string]string)
	if userEntity.NotificationSettings != nil {
		for k, v := range userEntity.NotificationSettings {
			if str, ok := v.(string); ok {
				notificationSettings[k] = str
			}
		}
	}

	protoUser := &userv1.User{
		Id:                   userEntity.ID.String(),
		FullName:             userEntity.FullName,
		Email:                userEntity.Email,
		Phone:                userEntity.Phone,
		AvatarUrl:            userEntity.AvatarURL,
		Bio:                  userEntity.Bio,
		Company:              userEntity.Company,
		Industry:             userEntity.Industry,
		Role:                 string(userEntity.Role),
		IsVerified:           userEntity.IsVerified,
		TwoFactorEnabled:     userEntity.TwoFactorEnabled,
		NotificationSettings: notificationSettings,
		InviteCode:           userEntity.InviteCode,
	}

	// Add timestamps
	protoUser.Timestamps = &commonv1.TimestampInfo{
		CreatedAt: timestamppb.New(userEntity.CreatedAt),
		UpdatedAt: timestamppb.New(userEntity.UpdatedAt),
	}

	if !userEntity.DeletedAt.IsZero() {
		protoUser.Timestamps.DeletedAt = timestamppb.New(userEntity.DeletedAt)
	}

	if !userEntity.AffJoinedAt.IsZero() {
		protoUser.AffJoinedAt = timestamppb.New(userEntity.AffJoinedAt)
	}

	return protoUser
}

// ToProtoUserFromResponse converts models.UserResponse to proto User
func ToProtoUserFromResponse(userResp *models.UserResponse) *userv1.User {
	if userResp == nil {
		return nil
	}

	// Combine first and last name to full name
	fullName := strings.TrimSpace(userResp.FirstName + " " + userResp.LastName)

	// Convert notification settings
	notificationSettings := make(map[string]string)
	if userResp.Preferences != nil {
		for k, v := range userResp.Preferences {
			if str, ok := v.(string); ok {
				notificationSettings[k] = str
			}
		}
	}

	protoUser := &userv1.User{
		Id:                   strconv.FormatUint(userResp.ID, 10),
		FullName:             fullName,
		Email:                userResp.Email,
		Role:                 userResp.Role,
		IsVerified:           userResp.EmailVerified,
		AvatarUrl:            userResp.AvatarURL,
		NotificationSettings: notificationSettings,
	}

	// Add profile information if available
	if userResp.Profile != nil {
		protoUser.Phone = userResp.Profile.Phone
		protoUser.Company = userResp.Profile.Company
		protoUser.Bio = userResp.Profile.Bio
		protoUser.Industry = userResp.Profile.JobTitle // Map job title to industry for now
	}

	// Add timestamps
	protoUser.Timestamps = &commonv1.TimestampInfo{
		CreatedAt: timestamppb.New(userResp.CreatedAt),
		UpdatedAt: timestamppb.New(userResp.UpdatedAt),
	}

	if userResp.DeletedAt != nil {
		protoUser.Timestamps.DeletedAt = timestamppb.New(*userResp.DeletedAt)
	}

	return protoUser
}

// ToCreateUserRequest converts proto CreateUserRequest to models.CreateUserRequest
func ToCreateUserRequest(req *userv1.CreateUserRequest) *models.CreateUserRequest {
	if req == nil {
		return nil
	}

	// Split full name into first and last name (simple split)
	firstName := req.FullName
	lastName := ""
	if parts := strings.Fields(req.FullName); len(parts) > 1 {
		firstName = parts[0]
		lastName = strings.Join(parts[1:], " ")
	}

	profile := &models.UserProfile{}
	if req.Phone != "" {
		profile.Phone = req.Phone
	}
	if req.Company != "" {
		profile.Company = req.Company
	}
	if req.Industry != "" {
		profile.JobTitle = req.Industry // Map industry to job title for now
	}

	return &models.CreateUserRequest{
		Email:     req.Email,
		Password:  req.Password,
		FirstName: firstName,
		LastName:  lastName,
		Role:      "user", // Default role
		Profile:   profile,
	}
}

// ToUpdateUserRequest converts proto UpdateUserRequest to models.UpdateUserRequest
func ToUpdateUserRequest(req *userv1.UpdateUserRequest) *models.UpdateUserRequest {
	if req == nil {
		return nil
	}

	// Parse ID from string to uint64
	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil
	}

	updateReq := &models.UpdateUserRequest{
		ID: id,
	}

	// Handle full name update
	if req.FullName != nil {
		firstName := *req.FullName
		lastName := ""
		if parts := strings.Fields(*req.FullName); len(parts) > 1 {
			firstName = parts[0]
			lastName = strings.Join(parts[1:], " ")
		}
		updateReq.FirstName = firstName
		updateReq.LastName = lastName
	}

	// Handle profile updates
	profile := &models.UserProfile{}
	profileUpdated := false

	if req.Phone != nil {
		profile.Phone = *req.Phone
		profileUpdated = true
	}
	if req.Company != nil {
		profile.Company = *req.Company
		profileUpdated = true
	}
	if req.Bio != nil {
		profile.Bio = *req.Bio
		profileUpdated = true
	}
	if req.Industry != nil {
		profile.JobTitle = *req.Industry
		profileUpdated = true
	}

	if profileUpdated {
		updateReq.Profile = profile
	}

	return updateReq
}

// ToListUsersRequest converts proto ListUsersRequest to models.ListUsersRequest
func ToListUsersRequest(req *userv1.ListUsersRequest) *models.ListUsersRequest {
	if req == nil {
		return nil
	}

	page := int(req.Pagination.Page)
	if page < 1 {
		page = 1
	}
	limit := int(req.Pagination.Limit)
	if limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return &models.ListUsersRequest{
		Page:   page,
		Limit:  limit,
		Role:   req.Role,
		SortBy: req.Pagination.SortBy,
		Order:  req.Pagination.SortOrder,
	}
}

// StringToUint64 converts string ID to uint64
func StringToUint64(s string) (uint64, error) {
	return strconv.ParseUint(s, 10, 64)
}

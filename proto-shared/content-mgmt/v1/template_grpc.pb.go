// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: content-mgmt/v1/template.proto

package contentmgmtv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TemplateServiceClient is the client API for TemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TemplateServiceClient interface {
	// Basic template operations
	CreateTemplate(ctx context.Context, in *CreateTemplateRequest, opts ...grpc.CallOption) (*Template, error)
	GetTemplate(ctx context.Context, in *GetTemplateRequest, opts ...grpc.CallOption) (*Template, error)
	UpdateTemplate(ctx context.Context, in *UpdateTemplateRequest, opts ...grpc.CallOption) (*Template, error)
	ListTemplates(ctx context.Context, in *ListTemplatesRequest, opts ...grpc.CallOption) (*ListTemplatesResponse, error)
	UseTemplate(ctx context.Context, in *UseTemplateRequest, opts ...grpc.CallOption) (*UseTemplateResponse, error)
	DeleteTemplate(ctx context.Context, in *DeleteTemplateRequest, opts ...grpc.CallOption) (*Empty, error)
	// Rating and review operations
	RateTemplate(ctx context.Context, in *RateTemplateRequest, opts ...grpc.CallOption) (*Empty, error)
	GetTemplateRatings(ctx context.Context, in *GetTemplateRatingsRequest, opts ...grpc.CallOption) (*GetTemplateRatingsResponse, error)
	// Marketplace operations
	PurchaseTemplate(ctx context.Context, in *PurchaseTemplateRequest, opts ...grpc.CallOption) (*PurchaseTemplateResponse, error)
	GetPurchasedTemplates(ctx context.Context, in *GetPurchasedTemplatesRequest, opts ...grpc.CallOption) (*GetPurchasedTemplatesResponse, error)
	// Revenue tracking operations
	GetTemplateRevenue(ctx context.Context, in *GetTemplateRevenueRequest, opts ...grpc.CallOption) (*GetTemplateRevenueResponse, error)
}

type templateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTemplateServiceClient(cc grpc.ClientConnInterface) TemplateServiceClient {
	return &templateServiceClient{cc}
}

func (c *templateServiceClient) CreateTemplate(ctx context.Context, in *CreateTemplateRequest, opts ...grpc.CallOption) (*Template, error) {
	out := new(Template)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/CreateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) GetTemplate(ctx context.Context, in *GetTemplateRequest, opts ...grpc.CallOption) (*Template, error) {
	out := new(Template)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/GetTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) UpdateTemplate(ctx context.Context, in *UpdateTemplateRequest, opts ...grpc.CallOption) (*Template, error) {
	out := new(Template)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/UpdateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) ListTemplates(ctx context.Context, in *ListTemplatesRequest, opts ...grpc.CallOption) (*ListTemplatesResponse, error) {
	out := new(ListTemplatesResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/ListTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) UseTemplate(ctx context.Context, in *UseTemplateRequest, opts ...grpc.CallOption) (*UseTemplateResponse, error) {
	out := new(UseTemplateResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/UseTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) DeleteTemplate(ctx context.Context, in *DeleteTemplateRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/DeleteTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) RateTemplate(ctx context.Context, in *RateTemplateRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/RateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) GetTemplateRatings(ctx context.Context, in *GetTemplateRatingsRequest, opts ...grpc.CallOption) (*GetTemplateRatingsResponse, error) {
	out := new(GetTemplateRatingsResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/GetTemplateRatings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) PurchaseTemplate(ctx context.Context, in *PurchaseTemplateRequest, opts ...grpc.CallOption) (*PurchaseTemplateResponse, error) {
	out := new(PurchaseTemplateResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/PurchaseTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) GetPurchasedTemplates(ctx context.Context, in *GetPurchasedTemplatesRequest, opts ...grpc.CallOption) (*GetPurchasedTemplatesResponse, error) {
	out := new(GetPurchasedTemplatesResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/GetPurchasedTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) GetTemplateRevenue(ctx context.Context, in *GetTemplateRevenueRequest, opts ...grpc.CallOption) (*GetTemplateRevenueResponse, error) {
	out := new(GetTemplateRevenueResponse)
	err := c.cc.Invoke(ctx, "/content_mgmt.v1.TemplateService/GetTemplateRevenue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TemplateServiceServer is the server API for TemplateService service.
// All implementations must embed UnimplementedTemplateServiceServer
// for forward compatibility
type TemplateServiceServer interface {
	// Basic template operations
	CreateTemplate(context.Context, *CreateTemplateRequest) (*Template, error)
	GetTemplate(context.Context, *GetTemplateRequest) (*Template, error)
	UpdateTemplate(context.Context, *UpdateTemplateRequest) (*Template, error)
	ListTemplates(context.Context, *ListTemplatesRequest) (*ListTemplatesResponse, error)
	UseTemplate(context.Context, *UseTemplateRequest) (*UseTemplateResponse, error)
	DeleteTemplate(context.Context, *DeleteTemplateRequest) (*Empty, error)
	// Rating and review operations
	RateTemplate(context.Context, *RateTemplateRequest) (*Empty, error)
	GetTemplateRatings(context.Context, *GetTemplateRatingsRequest) (*GetTemplateRatingsResponse, error)
	// Marketplace operations
	PurchaseTemplate(context.Context, *PurchaseTemplateRequest) (*PurchaseTemplateResponse, error)
	GetPurchasedTemplates(context.Context, *GetPurchasedTemplatesRequest) (*GetPurchasedTemplatesResponse, error)
	// Revenue tracking operations
	GetTemplateRevenue(context.Context, *GetTemplateRevenueRequest) (*GetTemplateRevenueResponse, error)
	mustEmbedUnimplementedTemplateServiceServer()
}

// UnimplementedTemplateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTemplateServiceServer struct {
}

func (UnimplementedTemplateServiceServer) CreateTemplate(context.Context, *CreateTemplateRequest) (*Template, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) GetTemplate(context.Context, *GetTemplateRequest) (*Template, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) UpdateTemplate(context.Context, *UpdateTemplateRequest) (*Template, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) ListTemplates(context.Context, *ListTemplatesRequest) (*ListTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplates not implemented")
}
func (UnimplementedTemplateServiceServer) UseTemplate(context.Context, *UseTemplateRequest) (*UseTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UseTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) DeleteTemplate(context.Context, *DeleteTemplateRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) RateTemplate(context.Context, *RateTemplateRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RateTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) GetTemplateRatings(context.Context, *GetTemplateRatingsRequest) (*GetTemplateRatingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplateRatings not implemented")
}
func (UnimplementedTemplateServiceServer) PurchaseTemplate(context.Context, *PurchaseTemplateRequest) (*PurchaseTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurchaseTemplate not implemented")
}
func (UnimplementedTemplateServiceServer) GetPurchasedTemplates(context.Context, *GetPurchasedTemplatesRequest) (*GetPurchasedTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPurchasedTemplates not implemented")
}
func (UnimplementedTemplateServiceServer) GetTemplateRevenue(context.Context, *GetTemplateRevenueRequest) (*GetTemplateRevenueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplateRevenue not implemented")
}
func (UnimplementedTemplateServiceServer) mustEmbedUnimplementedTemplateServiceServer() {}

// UnsafeTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TemplateServiceServer will
// result in compilation errors.
type UnsafeTemplateServiceServer interface {
	mustEmbedUnimplementedTemplateServiceServer()
}

func RegisterTemplateServiceServer(s grpc.ServiceRegistrar, srv TemplateServiceServer) {
	s.RegisterService(&TemplateService_ServiceDesc, srv)
}

func _TemplateService_CreateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).CreateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/CreateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).CreateTemplate(ctx, req.(*CreateTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_GetTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).GetTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/GetTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).GetTemplate(ctx, req.(*GetTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_UpdateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).UpdateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/UpdateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).UpdateTemplate(ctx, req.(*UpdateTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_ListTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).ListTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/ListTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).ListTemplates(ctx, req.(*ListTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_UseTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).UseTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/UseTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).UseTemplate(ctx, req.(*UseTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_DeleteTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).DeleteTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/DeleteTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).DeleteTemplate(ctx, req.(*DeleteTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_RateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RateTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).RateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/RateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).RateTemplate(ctx, req.(*RateTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_GetTemplateRatings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateRatingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).GetTemplateRatings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/GetTemplateRatings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).GetTemplateRatings(ctx, req.(*GetTemplateRatingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_PurchaseTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).PurchaseTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/PurchaseTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).PurchaseTemplate(ctx, req.(*PurchaseTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_GetPurchasedTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchasedTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).GetPurchasedTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/GetPurchasedTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).GetPurchasedTemplates(ctx, req.(*GetPurchasedTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_GetTemplateRevenue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateRevenueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).GetTemplateRevenue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/content_mgmt.v1.TemplateService/GetTemplateRevenue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).GetTemplateRevenue(ctx, req.(*GetTemplateRevenueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TemplateService_ServiceDesc is the grpc.ServiceDesc for TemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "content_mgmt.v1.TemplateService",
	HandlerType: (*TemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTemplate",
			Handler:    _TemplateService_CreateTemplate_Handler,
		},
		{
			MethodName: "GetTemplate",
			Handler:    _TemplateService_GetTemplate_Handler,
		},
		{
			MethodName: "UpdateTemplate",
			Handler:    _TemplateService_UpdateTemplate_Handler,
		},
		{
			MethodName: "ListTemplates",
			Handler:    _TemplateService_ListTemplates_Handler,
		},
		{
			MethodName: "UseTemplate",
			Handler:    _TemplateService_UseTemplate_Handler,
		},
		{
			MethodName: "DeleteTemplate",
			Handler:    _TemplateService_DeleteTemplate_Handler,
		},
		{
			MethodName: "RateTemplate",
			Handler:    _TemplateService_RateTemplate_Handler,
		},
		{
			MethodName: "GetTemplateRatings",
			Handler:    _TemplateService_GetTemplateRatings_Handler,
		},
		{
			MethodName: "PurchaseTemplate",
			Handler:    _TemplateService_PurchaseTemplate_Handler,
		},
		{
			MethodName: "GetPurchasedTemplates",
			Handler:    _TemplateService_GetPurchasedTemplates_Handler,
		},
		{
			MethodName: "GetTemplateRevenue",
			Handler:    _TemplateService_GetTemplateRevenue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "content-mgmt/v1/template.proto",
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// SubscriptionPlan holds the schema definition for the SubscriptionPlan entity.
type SubscriptionPlan struct {
	ent.Schema
}

// Fields of the SubscriptionPlan.
func (SubscriptionPlan) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.String("name").
			MaxLen(100).
			Unique(),
		field.String("display_name").
			MaxLen(100),
		field.Text("description").
			Optional(),
		field.Int64("price"). // in cents
					Min(0),
		field.String("currency").
			MaxLen(3).
			Default("USD"),
		field.Enum("billing_interval").
			Values("monthly", "yearly").
			<PERSON><PERSON>ult("monthly"),
		field.Int("credits_included").
			Default(0).
			<PERSON>(0),
		field.Int("monthly_credits").
			Default(0).
			Min(0).
			Comment("Credits renewed each month"),
		field.Bool("is_unlimited").
			Default(false).
			Comment("Whether this plan has unlimited credits"),
		field.JSON("features", []string{}).
			Optional().
			Comment("List of features included in this plan"),
		field.JSON("limits", map[string]interface{}{}).
			Optional().
			Comment("Usage limits for this plan"),
		field.Int("max_users").
			Default(1).
			Min(1).
			Comment("Maximum number of users for this plan"),
		field.Int("max_workspaces").
			Default(1).
			Min(1).
			Comment("Maximum number of workspaces"),
		field.Bool("is_active").
			Default(true),
		field.Bool("is_featured").
			Default(false),
		field.Int("sort_order").
			Default(0),
		field.String("stripe_price_id").
			MaxLen(100).
			Optional().
			Comment("Stripe price ID for billing"),
		field.String("stripe_product_id").
			MaxLen(100).
			Optional().
			Comment("Stripe product ID"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the SubscriptionPlan.
func (SubscriptionPlan) Edges() []ent.Edge {
	return nil
}

// Indexes of the SubscriptionPlan.
func (SubscriptionPlan) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("name").Unique(),
		index.Fields("is_active"),
		index.Fields("billing_interval"),
		index.Fields("sort_order"),
		index.Fields("stripe_price_id"),
		index.Fields("created_at"),
	}
}

package scheduler

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MonthlyCreditRenewalService handles monthly credit renewal for active subscriptions
type MonthlyCreditRenewalService struct {
	readDB   *ent.Client
	writeDB  *ent.Client
	creditUC credit.UseCase
	config   MonthlyCreditRenewalConfig
	logger   logging.Logger
	stats    *JobStats
}

// NewMonthlyCreditRenewalService creates a new monthly credit renewal service
func NewMonthlyCreditRenewalService(
	readDB *ent.Client,
	writeDB *ent.Client,
	creditUC credit.UseCase,
	config MonthlyCreditRenewalConfig,
	logger logging.Logger,
) *MonthlyCreditRenewalService {
	return &MonthlyCreditRenewalService{
		readDB:   readDB,
		writeDB:  writeDB,
		creditUC: creditUC,
		config:   config,
		logger:   logger,
		stats: &JobStats{
			JobName:       "monthly_credit_renewal",
			LastRunStatus: "never_run",
		},
	}
}

// Run executes the monthly credit renewal job
func (s *MonthlyCreditRenewalService) Run(ctx context.Context) error {
	if !s.config.Enabled {
		s.logger.Debug("Monthly credit renewal is disabled")
		return nil
	}

	startTime := time.Now()
	s.stats.LastRunAt = &startTime
	s.stats.LastRunStatus = "running"
	s.stats.TotalRuns++

	s.logger.WithFields(map[string]interface{}{
		"batch_size": s.config.BatchSize,
		"dry_run":    s.config.DryRun,
	}).Info("Starting monthly credit renewal job")

	defer func() {
		duration := time.Since(startTime)
		s.stats.LastRunDuration = duration
		s.logger.WithFields(map[string]interface{}{
			"duration": duration,
			"status":   s.stats.LastRunStatus,
		}).Info("Monthly credit renewal job completed")
	}()

	// Get current month start and end
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)

	// Process active subscriptions in batches
	offset := 0
	totalProcessed := 0
	totalRenewed := 0

	for {
		subscriptions, err := s.getActiveSubscriptions(ctx, offset, s.config.BatchSize)
		if err != nil {
			s.stats.LastRunStatus = "failed"
			s.stats.LastError = err.Error()
			s.stats.FailedRuns++
			return fmt.Errorf("failed to get active subscriptions: %w", err)
		}

		if len(subscriptions) == 0 {
			break // No more subscriptions to process
		}

		for _, sub := range subscriptions {
			if err := s.processSubscriptionRenewal(ctx, sub, monthStart); err != nil {
				s.logger.WithError(err).WithFields(map[string]interface{}{
					"subscription_id": sub.ID,
					"user_id":         sub.UserID,
					"plan_id":         sub.PlanID,
				}).Error("Failed to process subscription renewal")

				// Continue processing other subscriptions even if one fails
				continue
			}
			totalRenewed++
		}

		totalProcessed += len(subscriptions)
		offset += s.config.BatchSize

		s.logger.WithFields(map[string]interface{}{
			"processed": totalProcessed,
			"renewed":   totalRenewed,
		}).Debug("Processed subscription batch")

		// Check if context is cancelled
		if ctx.Err() != nil {
			s.stats.LastRunStatus = "cancelled"
			s.stats.LastError = ctx.Err().Error()
			return ctx.Err()
		}
	}

	s.stats.LastRunStatus = "success"
	s.stats.LastError = ""
	s.stats.SuccessfulRuns++

	s.logger.WithFields(map[string]interface{}{
		"total_processed": totalProcessed,
		"total_renewed":   totalRenewed,
	}).Info("Monthly credit renewal job completed successfully")

	return nil
}

// getActiveSubscriptions retrieves active subscriptions that need renewal
func (s *MonthlyCreditRenewalService) getActiveSubscriptions(ctx context.Context, offset, limit int) ([]*ent.Subscription, error) {
	return s.readDB.Subscription.Query().
		Where(
			subscription.StatusEQ(subscription.StatusActive),
		).
		Offset(offset).
		Limit(limit).
		All(ctx)
}

// processSubscriptionRenewal processes renewal for a single subscription
func (s *MonthlyCreditRenewalService) processSubscriptionRenewal(ctx context.Context, sub *ent.Subscription, monthStart time.Time) error {
	// Check if subscription should be renewed this month
	if !s.shouldRenewSubscription(sub, monthStart) {
		s.logger.WithFields(map[string]interface{}{
			"subscription_id": sub.ID,
			"user_id":         sub.UserID,
		}).Debug("Subscription does not need renewal this month")
		return nil
	}

	// Get plan information
	plan, err := s.readDB.SubscriptionPlan.Get(ctx, sub.PlanID)
	if err != nil {
		return fmt.Errorf("failed to get plan %s: %w", sub.PlanID, err)
	}

	// Check if credits were already added this month
	alreadyAdded, err := s.checkCreditsAlreadyAdded(ctx, sub.UserID.String(), sub.ID.String(), monthStart)
	if err != nil {
		return fmt.Errorf("failed to check if credits already added: %w", err)
	}

	if alreadyAdded {
		s.logger.WithFields(map[string]interface{}{
			"subscription_id": sub.ID,
			"user_id":         sub.UserID,
			"month":           monthStart.Format("2006-01"),
		}).Debug("Credits already added for this subscription this month")
		return nil
	}

	// Add monthly credits
	if plan.MonthlyCredits > 0 {
		if s.config.DryRun {
			s.logger.WithFields(map[string]interface{}{
				"subscription_id": sub.ID,
				"user_id":         sub.UserID,
				"plan_name":       plan.Name,
				"credits":         plan.MonthlyCredits,
			}).Info("DRY RUN: Would add monthly credits")
			return nil
		}

		req := &models.AddCreditsRequest{
			UserID:        sub.UserID.String(),
			Amount:        plan.MonthlyCredits,
			Source:        "subscription",
			Description:   fmt.Sprintf("Monthly credits for %s plan", plan.Name),
			ReferenceID:   sub.ID.String(),
			ReferenceType: "monthly_renewal",
			Metadata: map[string]interface{}{
				"plan_id":         plan.ID.String(),
				"plan_name":       plan.Name,
				"renewal_month":   monthStart.Format("2006-01"),
				"subscription_id": sub.ID.String(),
			},
		}

		_, err := s.creditUC.AddCredits(ctx, req)
		if err != nil {
			return fmt.Errorf("failed to add monthly credits: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"subscription_id": sub.ID,
			"user_id":         sub.UserID,
			"plan_name":       plan.Name,
			"credits_added":   plan.MonthlyCredits,
			"month":           monthStart.Format("2006-01"),
		}).Info("Successfully added monthly credits")
	}

	return nil
}

// shouldRenewSubscription determines if a subscription should be renewed this month
func (s *MonthlyCreditRenewalService) shouldRenewSubscription(sub *ent.Subscription, monthStart time.Time) bool {
	// Check if subscription is active
	if sub.Status != subscription.StatusActive {
		return false
	}

	// Check if subscription started before this month (using current_period_start)
	if sub.CurrentPeriodStart.After(monthStart) {
		return false
	}

	// Check if subscription has ended (using current_period_end)
	if sub.CurrentPeriodEnd.Before(monthStart) {
		return false
	}

	// Check if it's time for monthly renewal
	// If current period end is in this month or before, it needs renewal
	monthEnd := monthStart.AddDate(0, 1, 0)
	return sub.CurrentPeriodEnd.Before(monthEnd)
}

// checkCreditsAlreadyAdded checks if credits were already added for this subscription this month
func (s *MonthlyCreditRenewalService) checkCreditsAlreadyAdded(ctx context.Context, userID, subscriptionID string, monthStart time.Time) (bool, error) {
	monthEnd := monthStart.AddDate(0, 1, 0)

	// Check if there's already a credit transaction for this subscription this month
	// Look for transactions with type "subscription" and reference_type "monthly_renewal"
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}

	count, err := s.readDB.CreditTransaction.Query().
		Where(
			credittransaction.UserIDEQ(userUUID),
			credittransaction.TypeEQ(credittransaction.TypeSubscription),
			credittransaction.ReferenceTypeEQ("monthly_renewal"),
			credittransaction.ReferenceIDEQ(subscriptionID),
			credittransaction.CreatedAtGTE(monthStart),
			credittransaction.CreatedAtLT(monthEnd),
		).
		Count(ctx)

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetStats returns job statistics
func (s *MonthlyCreditRenewalService) GetStats() *JobStats {
	return s.stats
}

// UpdateConfig updates the service configuration
func (s *MonthlyCreditRenewalService) UpdateConfig(config MonthlyCreditRenewalConfig) {
	s.config = config
	s.logger.WithFields(map[string]interface{}{
		"enabled":     config.Enabled,
		"cron_spec":   config.CronSpec,
		"batch_size":  config.BatchSize,
		"max_retries": config.MaxRetries,
		"dry_run":     config.DryRun,
	}).Info("Updated monthly credit renewal configuration")
}

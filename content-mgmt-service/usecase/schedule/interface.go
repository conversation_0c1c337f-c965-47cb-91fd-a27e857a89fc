package schedule

import (
	"context"

	"github.com/social-content-ai/content-mgmt-service/pkg/models"
)

// UseCase defines the schedule business logic interface
type UseCase interface {
	// Schedule management
	CreateSchedule(ctx context.Context, req *models.CreateScheduleRequest) (*models.ScheduleResponse, error)
	GetSchedule(ctx context.Context, scheduleID, userID string) (*models.ScheduleResponse, error)
	UpdateSchedule(ctx context.Context, req *models.UpdateScheduleRequest) (*models.ScheduleResponse, error)
	DeleteSchedule(ctx context.Context, scheduleID, userID string) error
	ListSchedules(ctx context.Context, req *models.ListSchedulesRequest) (*models.ListSchedulesResponse, error)

	// Schedule execution
	ExecuteSchedule(ctx context.Context, scheduleID string) (*models.ScheduleExecutionResponse, error)
	GetScheduleExecution(ctx context.Context, executionID string) (*models.ScheduleExecutionResponse, error)
	ListScheduleExecutions(ctx context.Context, scheduleID string, page, limit int) (*models.ListScheduleExecutionsResponse, error)

	// Schedule status
	EnableSchedule(ctx context.Context, scheduleID, userID string) (*models.ScheduleResponse, error)
	DisableSchedule(ctx context.Context, scheduleID, userID string) (*models.ScheduleResponse, error)
	GetScheduleStatus(ctx context.Context, scheduleID, userID string) (*models.ScheduleStatusResponse, error)

	// Bulk operations
	BulkEnableSchedules(ctx context.Context, scheduleIDs []string, userID string) (*models.BulkOperationResult, error)
	BulkDisableSchedules(ctx context.Context, scheduleIDs []string, userID string) (*models.BulkOperationResult, error)
	BulkDeleteSchedules(ctx context.Context, scheduleIDs []string, userID string) (*models.BulkOperationResult, error)

	// Calendar and timeline
	GetScheduleCalendar(ctx context.Context, req *models.ScheduleCalendarRequest) (*models.ScheduleCalendarResponse, error)
	GetUpcomingSchedules(ctx context.Context, userID string, limit int) (*models.ListSchedulesResponse, error)
	GetScheduleTimeline(ctx context.Context, req *models.ScheduleTimelineRequest) (*models.ScheduleTimelineResponse, error)

	// Analytics
	GetScheduleAnalytics(ctx context.Context, req *models.ScheduleAnalyticsRequest) (*models.ScheduleAnalyticsResponse, error)
	GetSchedulesAnalytics(ctx context.Context, userID string, period string) (*models.ScheduleAnalyticsResponse, error)
	GetSchedulePerformance(ctx context.Context, scheduleID, userID string) (*models.SchedulePerformanceResponse, error)

	// Admin operations
	AdminListSchedules(ctx context.Context, req *models.ListSchedulesRequest) (*models.ListSchedulesResponse, error)
	AdminGetSchedule(ctx context.Context, scheduleID string) (*models.ScheduleResponse, error)
	AdminDeleteSchedule(ctx context.Context, scheduleID string) error
	GetAdminScheduleAnalytics(ctx context.Context, period string) (*models.ScheduleAnalyticsResponse, error)
}

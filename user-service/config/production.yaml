# Production Environment Configuration

# Production server settings
server:
  env: "production"
  grpc_port: 50051
  http_port: 8080
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Production database settings (use environment variables)
database:
  # Always use PostgreSQL in production
  type: "postgres"
  host: "${DB_HOST}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  name: "${DB_NAME}"
  ssl_mode: "require"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Production JWT settings (use environment variables)
jwt:
  secret_key: "${JWT_SECRET_KEY}"
  token_duration: "1h"  # Shorter for security
  refresh_duration: "24h"  # Shorter refresh window
  issuer: "user-service"
  audience: "social-content-ai"

# Production logging
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Production Redis settings
redis:
  host: "${REDIS_HOST}"
  port: "${REDIS_PORT:6379}"
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 20
  min_idle_conns: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  pool_timeout: "4s"
  idle_timeout: "5m"

# Production email settings
email:
  smtp_host: "${SMTP_HOST}"
  smtp_port: "${SMTP_PORT:587}"
  username: "${SMTP_USERNAME}"
  password: "${SMTP_PASSWORD}"
  from_email: "${FROM_EMAIL}"
  from_name: "Social Content AI"

# Production rate limiting
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# Production security settings
security:
  bcrypt_cost: 14  # Higher cost for production
  password_min_length: 8
  session_duration: "1h"  # Shorter sessions
  max_sessions_per_user: 3
  require_email_verification: true
  enable_2fa: true

# Production monitoring
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_path: "/health"
  metrics_path: "/metrics"

# Production feature flags
features:
  user_registration: true
  password_reset: true
  email_verification: true
  social_login: false  # Disable until implemented
  admin_panel: true
  user_analytics: true

# Production external services
external_services:
  notification_service:
    host: "${NOTIFICATION_SERVICE_HOST}"
    port: "${NOTIFICATION_SERVICE_PORT:50052}"
    timeout: "10s"
  
  content_service:
    host: "${CONTENT_SERVICE_HOST}"
    port: "${CONTENT_SERVICE_PORT:50053}"
    timeout: "10s"
  
  billing_service:
    host: "${BILLING_SERVICE_HOST}"
    port: "${BILLING_SERVICE_PORT:50054}"
    timeout: "10s"

# Production Kafka settings
kafka:
  brokers: "${KAFKA_BROKERS:localhost:9092}"
  consumer:
    group_id: "user-service-prod-consumer"
    auto_offset_reset: "latest"
  producer:
    acks: "all"
    retries: 5
    batch_size: 16384
    linger_ms: 5
    buffer_memory: 33554432

# Production file upload settings
file_upload:
  max_size: "5MB"  # Smaller limit for production
  allowed_types:
    - "image/jpeg"
    - "image/png"
  upload_path: "/app/uploads"

# Production CORS settings
cors:
  allowed_origins:
    - "https://app.social-content-ai.com"
    - "https://admin.social-content-ai.com"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  expose_headers:
    - "X-Total-Count"
  allow_credentials: true
  max_age: 86400

# Production validation (stricter)
validation:
  email_regex: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  phone_regex: "^\\+?[1-9]\\d{1,14}$"
  password_regex: "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"

# Production cleanup settings
cleanup:
  expired_sessions_interval: "1h"
  old_sessions_retention: "90d"
  deleted_users_retention: "365d"  # Longer retention for compliance

  # Production unverified user cleanup settings
  enabled: true
  auto_start: true               # Auto-start in production
  unverified_user_ttl: "24h"     # Standard 24-hour TTL
  cleanup_interval: "1h"         # Run cleanup every hour
  batch_size: 100                # Process 100 users per batch
  enable_notifications: true     # Send warning emails in production
  warning_time: "2h"             # Send warning 2 hours before deletion

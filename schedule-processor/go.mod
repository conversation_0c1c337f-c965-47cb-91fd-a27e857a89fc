module github.com/social-content-ai/schedule-processor

go 1.21

require (
	github.com/social-content-ai/proto-shared v0.0.0
	github.com/social-content-ai/pkg-shared v0.0.0
	google.golang.org/grpc v1.59.0
	github.com/robfig/cron/v3 v3.0.1
	github.com/sirupsen/logrus v1.9.3
	github.com/segmentio/kafka-go v0.4.47
	github.com/redis/go-redis/v9 v9.3.0
	github.com/google/uuid v1.4.0
)

require (
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/pierrec/lz4/v4 v4.1.19 // indirect
	golang.org/x/net v0.19.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20231212172506-995d672761c0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231212172506-995d672761c0 // indirect
	google.golang.org/protobuf v1.31.0 // indirect
)

replace github.com/social-content-ai/proto-shared => ../proto-shared
replace github.com/social-content-ai/pkg-shared => ../pkg-shared

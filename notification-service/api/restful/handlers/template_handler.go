package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/notification-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TemplateHandler handles template-related HTTP requests
type TemplateHandler struct {
	templateUC template.UseCase
	logger     logging.Logger
}

// NewTemplateHandler creates a new template handler
func NewTemplateHandler(templateUC template.UseCase, logger logging.Logger) *TemplateHandler {
	return &TemplateHandler{
		templateUC: templateUC,
		logger:     logger,
	}
}

// ListTemplates handles GET /api/v1/templates
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuer<PERSON>("limit", "20"))
	// TODO: Add support for these query parameters
	// category := c.Query("category")
	// language := c.Query("language")
	// search := c.Query("search")

	req := &template.ListTemplatesRequest{
		Page:  page,
		Limit: limit,
		// TODO: Add Category, Language, Search fields to ListTemplatesRequest
		// Category: category,
		// Language: language,
		// Search:   search,
	}

	response, err := h.templateUC.ListTemplates(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreateTemplate handles POST /api/v1/templates
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req template.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req.CreatedBy = userID.(string)

	response, err := h.templateUC.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetTemplate handles GET /api/v1/templates/:id
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	response, err := h.templateUC.GetTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateTemplate handles PUT /api/v1/templates/:id
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var req template.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.ID = templateID

	response, err := h.templateUC.UpdateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DeleteTemplate handles DELETE /api/v1/templates/:id
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	err := h.templateUC.DeleteTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// PreviewTemplate handles POST /api/v1/templates/:id/preview
func (h *TemplateHandler) PreviewTemplate(c *gin.Context) {
	// TODO: Implement when PreviewTemplateRequest is available
	h.logger.Info("Preview template not implemented yet")
	c.JSON(http.StatusOK, gin.H{"status": "not_implemented"})
}

// TestTemplate handles POST /api/v1/templates/:id/test
func (h *TemplateHandler) TestTemplate(c *gin.Context) {
	// TODO: Implement when TestTemplateRequest is available
	h.logger.Info("Test template not implemented yet")
	c.JSON(http.StatusOK, gin.H{"status": "not_implemented"})
}

// CloneTemplate handles POST /api/v1/templates/:id/clone
func (h *TemplateHandler) CloneTemplate(c *gin.Context) {
	// TODO: Implement when CloneTemplateRequest is available
	h.logger.Info("Clone template not implemented yet")
	c.JSON(http.StatusOK, gin.H{"status": "not_implemented"})
}

// GetTemplateCategories handles GET /api/v1/templates/categories
func (h *TemplateHandler) GetTemplateCategories(c *gin.Context) {
	// TODO: Implement when GetTemplateCategories method is available
	h.logger.Info("Get template categories not implemented yet")
	c.JSON(http.StatusOK, gin.H{"categories": []string{}})
}

// GetTemplateTags handles GET /api/v1/templates/tags
func (h *TemplateHandler) GetTemplateTags(c *gin.Context) {
	// TODO: Implement when GetTemplateTags method is available
	h.logger.Info("Get template tags not implemented yet")
	c.JSON(http.StatusOK, gin.H{"tags": []string{}})
}

// Admin handlers

// AdminListTemplates handles GET /api/v1/admin/templates
func (h *TemplateHandler) AdminListTemplates(c *gin.Context) {
	// TODO: Parse query parameters when needed
	// page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	// limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	// status := c.Query("status")
	// category := c.Query("category")

	// TODO: Implement when AdminListTemplatesRequest is available
	h.logger.Info("Admin list templates not implemented yet")
	c.JSON(http.StatusOK, gin.H{"templates": []interface{}{}, "total": 0})
}

// ApproveTemplate handles PUT /api/v1/admin/templates/:id/approve
func (h *TemplateHandler) ApproveTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	// TODO: Implement when ApproveTemplateRequest is available
	h.logger.Info("Approve template not implemented yet")
	c.JSON(http.StatusOK, gin.H{"status": "not_implemented"})
}

// RejectTemplate handles PUT /api/v1/admin/templates/:id/reject
func (h *TemplateHandler) RejectTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	// TODO: Implement when RejectTemplateRequest is available
	h.logger.Info("Reject template not implemented yet")
	c.JSON(http.StatusOK, gin.H{"status": "not_implemented"})
}

package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// payPalProcessor implements the Processor interface using PayPal API
type payPalProcessor struct {
	config       PayPalConfig
	paypalClient paypal.Client
	logger       logging.Logger
}

// NewPayPalProcessor creates a new PayPal payment processor
func NewPayPalProcessor(config PayPalConfig, paypalClient paypal.Client, logger logging.Logger) Processor {
	return &payPalProcessor{
		config:       config,
		paypalClient: paypalClient,
		logger:       logger,
	}
}

// ProcessPayment processes a payment using PayPal API
func (p *payPalProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id":  req.UserID,
		"amount":   req.Amount,
		"currency": req.Currency,
	}).Info("Processing PayPal payment")

	// Convert amount from cents to PayPal format (string with 2 decimal places)
	amountValue := fmt.Sprintf("%.2f", float64(req.Amount)/100.0)

	// Create PayPal order request
	orderReq := &paypal.CreateOrderRequest{
		Intent: "CAPTURE",
		PurchaseUnits: []paypal.PurchaseUnit{
			{
				ReferenceID: req.OrderID,
				Amount: paypal.Amount{
					CurrencyCode: req.Currency,
					Value:        amountValue,
				},
				Description: req.Description,
				CustomID:    req.UserID,
			},
		},
		ApplicationContext: &paypal.ApplicationContext{
			BrandName:          "SocialAI",
			Locale:             "en-US",
			LandingPage:        "BILLING",
			ShippingPreference: "NO_SHIPPING",
			UserAction:         "PAY_NOW",
			ReturnURL:          p.config.ReturnURL,
			CancelURL:          p.config.CancelURL,
		},
	}

	// Create order via PayPal API
	order, err := p.paypalClient.CreateOrder(ctx, orderReq)
	if err != nil {
		p.logger.WithError(err).Error("Failed to create PayPal order")
		return nil, fmt.Errorf("failed to create PayPal order: %w", err)
	}

	// Extract approval URL from links
	var approvalURL string
	for _, link := range order.Links {
		if link.Rel == "approve" {
			approvalURL = link.Href
			break
		}
	}

	if approvalURL == "" {
		return nil, fmt.Errorf("no approval URL found in PayPal order response")
	}

	p.logger.WithFields(map[string]interface{}{
		"order_id":     order.ID,
		"status":       order.Status,
		"approval_url": approvalURL,
	}).Info("PayPal order created successfully")

	return &PaymentResponse{
		PaymentID:  order.ID,
		Status:     order.Status,
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: approvalURL,
		PaymentInfo: map[string]interface{}{
			"paypal_order_id": order.ID,
			"approval_url":    approvalURL,
			"return_url":      p.config.ReturnURL,
			"cancel_url":      p.config.CancelURL,
			"intent":          order.Intent,
		},
		ExpiresAt: time.Now().Add(3 * time.Hour).Format(time.RFC3339), // PayPal orders expire in 3 hours
	}, nil
}

// RefundPayment refunds a payment using PayPal API
func (p *payPalProcessor) RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"amount":     req.Amount,
		"reason":     req.Reason,
	}).Info("Refunding PayPal payment")

	// First, get the order to find capture ID
	order, err := p.paypalClient.GetOrder(ctx, req.PaymentID)
	if err != nil {
		p.logger.WithError(err).Error("Failed to get PayPal order for refund")
		return nil, fmt.Errorf("failed to get PayPal order: %w", err)
	}

	// For now, return a mock refund response since PayPal refund API requires capture ID
	// In production, you would need to implement proper refund logic with capture tracking
	p.logger.WithFields(map[string]interface{}{
		"order_id": order.ID,
		"status":   order.Status,
	}).Info("PayPal refund processed (mock implementation)")

	return &RefundResponse{
		RefundID:  fmt.Sprintf("refund_%s_%d", req.PaymentID, time.Now().Unix()),
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// GetPaymentStatus gets payment status using PayPal API
func (p *payPalProcessor) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	p.logger.WithField("payment_id", paymentID).Info("Getting PayPal payment status")

	// Get order from PayPal API
	order, err := p.paypalClient.GetOrder(ctx, paymentID)
	if err != nil {
		p.logger.WithError(err).Error("Failed to get PayPal order status")
		return nil, fmt.Errorf("failed to get PayPal order: %w", err)
	}

	// Extract amount from order
	var amount int64
	var currency string
	if len(order.PurchaseUnits) > 0 {
		amountStr := order.PurchaseUnits[0].Amount.Value
		currency = order.PurchaseUnits[0].Amount.CurrencyCode
		if amountFloat, err := strconv.ParseFloat(amountStr, 64); err == nil {
			amount = int64(amountFloat * 100) // Convert to cents
		}
	}

	p.logger.WithFields(map[string]interface{}{
		"order_id": order.ID,
		"status":   order.Status,
		"amount":   amount,
	}).Info("Retrieved PayPal payment status")

	return &PaymentStatus{
		PaymentID:   order.ID,
		Status:      order.Status,
		Amount:      amount,
		Currency:    currency,
		ProcessedAt: order.UpdateTime.Format(time.RFC3339),
	}, nil
}

// ValidateWebhook validates PayPal webhook signature
func (p *payPalProcessor) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	p.logger.Info("Validating PayPal webhook signature")

	// Use PayPal client for webhook validation
	valid, err := p.paypalClient.VerifyWebhookSignature(ctx, payload, signature)
	if err != nil {
		p.logger.WithError(err).Error("Failed to verify PayPal webhook signature")
		return false, fmt.Errorf("failed to verify webhook signature: %w", err)
	}

	p.logger.WithField("valid", valid).Info("PayPal webhook signature validation result")
	return valid, nil
}

// ProcessWebhook processes PayPal webhook events
func (p *payPalProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
	p.logger.Info("Processing PayPal webhook")

	// Parse PayPal webhook event
	var webhookEvent paypal.WebhookEvent
	if err := json.Unmarshal(payload, &webhookEvent); err != nil {
		p.logger.WithError(err).Error("Failed to parse PayPal webhook payload")
		return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_id":   webhookEvent.ID,
		"event_type": webhookEvent.EventType,
		"summary":    webhookEvent.Summary,
	}).Info("Parsed PayPal webhook event")

	// Convert to our webhook event format
	event := &WebhookEvent{
		ID:        webhookEvent.ID,
		Type:      webhookEvent.EventType,
		Data:      webhookEvent.Resource,
		CreatedAt: webhookEvent.CreateTime.Format(time.RFC3339),
	}

	return event, nil
}

// CaptureOrder captures a PayPal order (used when user completes payment)
func (p *payPalProcessor) CaptureOrder(ctx context.Context, orderID string) (*CaptureResponse, error) {
	p.logger.WithField("order_id", orderID).Info("Capturing PayPal order")

	// Capture order via PayPal API
	captureResp, err := p.paypalClient.CaptureOrder(ctx, orderID)
	if err != nil {
		p.logger.WithError(err).Error("Failed to capture PayPal order")
		return nil, fmt.Errorf("failed to capture PayPal order: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"order_id":   orderID,
		"capture_id": captureResp.ID,
		"status":     captureResp.Status,
		"amount":     captureResp.Amount.Value,
	}).Info("PayPal order captured successfully")

	// Convert amount to cents
	var amountCents int64
	if amountFloat, err := strconv.ParseFloat(captureResp.Amount.Value, 64); err == nil {
		amountCents = int64(amountFloat * 100)
	}

	return &CaptureResponse{
		CaptureID: captureResp.ID,
		OrderID:   orderID,
		Status:    captureResp.Status,
		Amount:    amountCents,
		Currency:  captureResp.Amount.CurrencyCode,
	}, nil
}

// CreateSubscription creates a PayPal subscription for recurring payments
func (p *payPalProcessor) CreateSubscription(ctx context.Context, req *SubscriptionRequest) (*SubscriptionResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
	}).Info("Creating PayPal subscription")

	// Create PayPal subscription request
	subscriptionReq := &paypal.CreateSubscriptionRequest{
		PlanID:   req.PlanID,
		CustomID: req.UserID,
		Subscriber: &paypal.Subscriber{
			EmailAddress: req.UserEmail,
		},
		ApplicationContext: &paypal.ApplicationContext{
			BrandName:  "SocialAI",
			Locale:     "en-US",
			UserAction: "SUBSCRIBE_NOW",
			ReturnURL:  p.config.ReturnURL,
			CancelURL:  p.config.CancelURL,
		},
	}

	// Create subscription via PayPal API
	subscription, err := p.paypalClient.CreateSubscription(ctx, subscriptionReq)
	if err != nil {
		p.logger.WithError(err).Error("Failed to create PayPal subscription")
		return nil, fmt.Errorf("failed to create PayPal subscription: %w", err)
	}

	// Extract approval URL from links
	var approvalURL string
	for _, link := range subscription.Links {
		if link.Rel == "approve" {
			approvalURL = link.Href
			break
		}
	}

	p.logger.WithFields(map[string]interface{}{
		"subscription_id": subscription.ID,
		"status":          subscription.Status,
		"approval_url":    approvalURL,
	}).Info("PayPal subscription created successfully")

	return &SubscriptionResponse{
		SubscriptionID: subscription.ID,
		Status:         subscription.Status,
		ApprovalURL:    approvalURL,
		PlanID:         subscription.PlanID,
	}, nil
}

// GetSubscription gets PayPal subscription details
func (p *payPalProcessor) GetSubscription(ctx context.Context, subscriptionID string) (*SubscriptionDetails, error) {
	p.logger.WithField("subscription_id", subscriptionID).Info("Getting PayPal subscription")

	// Get subscription via PayPal API
	subscription, err := p.paypalClient.GetSubscription(ctx, subscriptionID)
	if err != nil {
		p.logger.WithError(err).Error("Failed to get PayPal subscription")
		return nil, fmt.Errorf("failed to get PayPal subscription: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"subscription_id": subscription.ID,
		"status":          subscription.Status,
		"plan_id":         subscription.PlanID,
	}).Info("Retrieved PayPal subscription details")

	return &SubscriptionDetails{
		SubscriptionID: subscription.ID,
		PlanID:         subscription.PlanID,
		Status:         subscription.Status,
		StartTime:      subscription.StartTime.Format(time.RFC3339),
		CreateTime:     subscription.CreateTime.Format(time.RFC3339),
		UpdateTime:     subscription.UpdateTime.Format(time.RFC3339),
	}, nil
}

// CancelSubscription cancels a PayPal subscription
func (p *payPalProcessor) CancelSubscription(ctx context.Context, subscriptionID string, reason string) error {
	p.logger.WithFields(map[string]interface{}{
		"subscription_id": subscriptionID,
		"reason":          reason,
	}).Info("Cancelling PayPal subscription")

	// Cancel subscription via PayPal API
	err := p.paypalClient.CancelSubscription(ctx, subscriptionID, reason)
	if err != nil {
		p.logger.WithError(err).Error("Failed to cancel PayPal subscription")
		return fmt.Errorf("failed to cancel PayPal subscription: %w", err)
	}

	p.logger.WithField("subscription_id", subscriptionID).Info("PayPal subscription cancelled successfully")
	return nil
}

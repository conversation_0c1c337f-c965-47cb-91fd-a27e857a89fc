package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/social-content-ai/credit-service/pkg/scheduler"
	sharedconfig "github.com/social-content-ai/pkg-shared/config"
	"github.com/spf13/viper"
)

// Config represents the complete application configuration
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Database  DatabaseConfig  `mapstructure:"database"`
	Payment   PaymentConfig   `mapstructure:"payment"`
	Services  ServicesConfig  `mapstructure:"services"`
	Metrics   MetricsConfig   `mapstructure:"metrics"`
	Logging   LoggingConfig   `mapstructure:"logging"`
	JWT       JWTConfig       `mapstructure:"jwt"`
	Kafka     KafkaConfig     `mapstructure:"kafka"`
	Scheduler SchedulerConfig `mapstructure:"scheduler"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	Env             string        `mapstructure:"env"`
	GRPCPort        int           `mapstructure:"grpc_port"`
	HTTPPort        int           `mapstructure:"http_port"`
	Host            string        `mapstructure:"host"`
	ReadTimeout     time.Duration `mapstructure:"read_timeout"`
	WriteTimeout    time.Duration `mapstructure:"write_timeout"`
	IdleTimeout     time.Duration `mapstructure:"idle_timeout"`
	ShutdownTimeout time.Duration `mapstructure:"shutdown_timeout"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig

	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// PaymentConfig contains payment processor configurations
type PaymentConfig struct {
	Stripe StripeConfig `mapstructure:"stripe"`
}

// StripeConfig contains Stripe payment configuration
type StripeConfig struct {
	SecretKey      string `mapstructure:"secret_key"`
	PublishableKey string `mapstructure:"publishable_key"`
	WebhookSecret  string `mapstructure:"webhook_secret"`
}

// ServicesConfig contains external service configurations
type ServicesConfig struct {
	UserService string `mapstructure:"user_service"`
}

// MetricsConfig contains metrics server configuration
type MetricsConfig struct {
	Port int `mapstructure:"port"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// JWTConfig contains JWT configuration
type JWTConfig struct {
	SecretKey     string        `mapstructure:"secret_key"`
	TokenDuration time.Duration `mapstructure:"token_duration"`
	Issuer        string        `mapstructure:"issuer"`
}

// KafkaConfig contains Kafka configuration
type KafkaConfig struct {
	Brokers []string `mapstructure:"brokers"`
	GroupID string   `mapstructure:"group_id"`
	Topics  struct {
		CreditEvents       string `mapstructure:"credit_events"`
		SubscriptionEvents string `mapstructure:"subscription_events"`
		PaymentEvents      string `mapstructure:"payment_events"`
		NotificationEvents string `mapstructure:"notification_events"`
	} `mapstructure:"topics"`
	Producer struct {
		BatchSize    int           `mapstructure:"batch_size"`
		BatchTimeout time.Duration `mapstructure:"batch_timeout"`
		Compression  string        `mapstructure:"compression"`
	} `mapstructure:"producer"`
	Consumer struct {
		MinBytes       int           `mapstructure:"min_bytes"`
		MaxBytes       int           `mapstructure:"max_bytes"`
		CommitInterval time.Duration `mapstructure:"commit_interval"`
	} `mapstructure:"consumer"`
}

// SchedulerConfig contains scheduler configuration
type SchedulerConfig struct {
	MonthlyCreditRenewal struct {
		Enabled    bool   `mapstructure:"enabled"`
		CronSpec   string `mapstructure:"cron_spec"`
		BatchSize  int    `mapstructure:"batch_size"`
		MaxRetries int    `mapstructure:"max_retries"`
		RetryDelay string `mapstructure:"retry_delay"`
		DryRun     bool   `mapstructure:"dry_run"`
		Timezone   string `mapstructure:"timezone"`
	} `mapstructure:"monthly_credit_renewal"`

	CreditExpirationCleanup struct {
		Enabled     bool   `mapstructure:"enabled"`
		CronSpec    string `mapstructure:"cron_spec"`
		BatchSize   int    `mapstructure:"batch_size"`
		GracePeriod string `mapstructure:"grace_period"`
		DryRun      bool   `mapstructure:"dry_run"`
	} `mapstructure:"credit_expiration_cleanup"`

	LowCreditWarning struct {
		Enabled           bool   `mapstructure:"enabled"`
		CronSpec          string `mapstructure:"cron_spec"`
		BatchSize         int    `mapstructure:"batch_size"`
		WarningThresholds []int  `mapstructure:"warning_thresholds"`
		CooldownPeriod    string `mapstructure:"cooldown_period"`
		DryRun            bool   `mapstructure:"dry_run"`
	} `mapstructure:"low_credit_warning"`
}

// GetDriverName returns the appropriate database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres" // Default to postgres
	}
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// CreateDataDirectory creates the data directory for SQLite if needed
func (c *DatabaseConfig) CreateDataDirectory() error {
	if c.Type == "sqlite" {
		dir := filepath.Dir(c.SQLitePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create SQLite data directory %s: %w", dir, err)
		}
	}
	return nil
}

// Load loads configuration from files and environment variables
func Load(configPath string) (*Config, error) {
	// Set default values
	viper.SetDefault("server.env", "development")
	viper.SetDefault("server.grpc_port", 50053)
	viper.SetDefault("server.http_port", 8083)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")
	viper.SetDefault("server.shutdown_timeout", "30s")

	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.name", "credit_service_db")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.sqlite_path", "./data/credit_service.db")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// Services defaults
	viper.SetDefault("services.user_service", "localhost:50051")

	// Metrics defaults
	viper.SetDefault("metrics.port", 9093)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// JWT defaults
	viper.SetDefault("jwt.secret_key", "your-secret-key")
	viper.SetDefault("jwt.token_duration", "24h")
	viper.SetDefault("jwt.issuer", "credit-service")

	// Kafka defaults
	viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
	viper.SetDefault("kafka.group_id", "credit-service")
	viper.SetDefault("kafka.topics.credit_events", "credit.events")
	viper.SetDefault("kafka.topics.subscription_events", "subscription.events")
	viper.SetDefault("kafka.topics.payment_events", "payment.events")
	viper.SetDefault("kafka.topics.notification_events", "notification.events")
	viper.SetDefault("kafka.producer.batch_size", 100)
	viper.SetDefault("kafka.producer.batch_timeout", "10ms")
	viper.SetDefault("kafka.producer.compression", "snappy")
	viper.SetDefault("kafka.consumer.min_bytes", 10240)    // 10KB
	viper.SetDefault("kafka.consumer.max_bytes", 10485760) // 10MB
	viper.SetDefault("kafka.consumer.commit_interval", "1s")

	// Scheduler defaults
	viper.SetDefault("scheduler.monthly_credit_renewal.enabled", true)
	viper.SetDefault("scheduler.monthly_credit_renewal.cron_spec", "0 0 1 * *") // 1st day of month at midnight
	viper.SetDefault("scheduler.monthly_credit_renewal.batch_size", 100)
	viper.SetDefault("scheduler.monthly_credit_renewal.max_retries", 3)
	viper.SetDefault("scheduler.monthly_credit_renewal.retry_delay", "5m")
	viper.SetDefault("scheduler.monthly_credit_renewal.dry_run", false)
	viper.SetDefault("scheduler.monthly_credit_renewal.timezone", "UTC")

	viper.SetDefault("scheduler.credit_expiration_cleanup.enabled", true)
	viper.SetDefault("scheduler.credit_expiration_cleanup.cron_spec", "0 2 * * *") // Daily at 2 AM
	viper.SetDefault("scheduler.credit_expiration_cleanup.batch_size", 500)
	viper.SetDefault("scheduler.credit_expiration_cleanup.grace_period", "168h") // 7 days
	viper.SetDefault("scheduler.credit_expiration_cleanup.dry_run", false)

	viper.SetDefault("scheduler.low_credit_warning.enabled", true)
	viper.SetDefault("scheduler.low_credit_warning.cron_spec", "0 9 * * *") // Daily at 9 AM
	viper.SetDefault("scheduler.low_credit_warning.batch_size", 200)
	viper.SetDefault("scheduler.low_credit_warning.warning_thresholds", []int{10, 5, 1})
	viper.SetDefault("scheduler.low_credit_warning.cooldown_period", "24h")
	viper.SetDefault("scheduler.low_credit_warning.dry_run", false)

	// Set config file path
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath("./config")
	viper.AddConfigPath(".")

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvPrefix("CREDIT_SERVICE")

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found, continue with defaults and env vars
	}

	// Unmarshal config
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// ToSchedulerConfig converts SchedulerConfig to scheduler.Config
func (c *SchedulerConfig) ToSchedulerConfig() (*scheduler.Config, error) {
	// Parse durations
	retryDelay, err := time.ParseDuration(c.MonthlyCreditRenewal.RetryDelay)
	if err != nil {
		return nil, fmt.Errorf("invalid retry delay: %w", err)
	}

	gracePeriod, err := time.ParseDuration(c.CreditExpirationCleanup.GracePeriod)
	if err != nil {
		return nil, fmt.Errorf("invalid grace period: %w", err)
	}

	cooldownPeriod, err := time.ParseDuration(c.LowCreditWarning.CooldownPeriod)
	if err != nil {
		return nil, fmt.Errorf("invalid cooldown period: %w", err)
	}

	return &scheduler.Config{
		MonthlyCreditRenewal: scheduler.MonthlyCreditRenewalConfig{
			Enabled:    c.MonthlyCreditRenewal.Enabled,
			CronSpec:   c.MonthlyCreditRenewal.CronSpec,
			BatchSize:  c.MonthlyCreditRenewal.BatchSize,
			MaxRetries: c.MonthlyCreditRenewal.MaxRetries,
			RetryDelay: retryDelay,
			DryRun:     c.MonthlyCreditRenewal.DryRun,
			Timezone:   c.MonthlyCreditRenewal.Timezone,
		},
		CreditExpirationCleanup: scheduler.CreditExpirationCleanupConfig{
			Enabled:     c.CreditExpirationCleanup.Enabled,
			CronSpec:    c.CreditExpirationCleanup.CronSpec,
			BatchSize:   c.CreditExpirationCleanup.BatchSize,
			GracePeriod: gracePeriod,
			DryRun:      c.CreditExpirationCleanup.DryRun,
		},
		LowCreditWarning: scheduler.LowCreditWarningConfig{
			Enabled:           c.LowCreditWarning.Enabled,
			CronSpec:          c.LowCreditWarning.CronSpec,
			BatchSize:         c.LowCreditWarning.BatchSize,
			WarningThresholds: c.LowCreditWarning.WarningThresholds,
			CooldownPeriod:    cooldownPeriod,
			DryRun:            c.LowCreditWarning.DryRun,
		},
	}, nil
}

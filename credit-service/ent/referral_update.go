// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/referral"
)

// ReferralUpdate is the builder for updating Referral entities.
type ReferralUpdate struct {
	config
	hooks    []Hook
	mutation *ReferralMutation
}

// Where appends a list predicates to the ReferralUpdate builder.
func (ru *ReferralUpdate) Where(ps ...predicate.Referral) *ReferralUpdate {
	ru.mutation.Where(ps...)
	return ru
}

// SetReferrerID sets the "referrer_id" field.
func (ru *ReferralUpdate) SetReferrerID(u uuid.UUID) *ReferralUpdate {
	ru.mutation.SetReferrerID(u)
	return ru
}

// SetNillableReferrerID sets the "referrer_id" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferrerID(u *uuid.UUID) *ReferralUpdate {
	if u != nil {
		ru.SetReferrerID(*u)
	}
	return ru
}

// SetReferredID sets the "referred_id" field.
func (ru *ReferralUpdate) SetReferredID(u uuid.UUID) *ReferralUpdate {
	ru.mutation.SetReferredID(u)
	return ru
}

// SetNillableReferredID sets the "referred_id" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferredID(u *uuid.UUID) *ReferralUpdate {
	if u != nil {
		ru.SetReferredID(*u)
	}
	return ru
}

// ClearReferredID clears the value of the "referred_id" field.
func (ru *ReferralUpdate) ClearReferredID() *ReferralUpdate {
	ru.mutation.ClearReferredID()
	return ru
}

// SetReferralCode sets the "referral_code" field.
func (ru *ReferralUpdate) SetReferralCode(s string) *ReferralUpdate {
	ru.mutation.SetReferralCode(s)
	return ru
}

// SetNillableReferralCode sets the "referral_code" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferralCode(s *string) *ReferralUpdate {
	if s != nil {
		ru.SetReferralCode(*s)
	}
	return ru
}

// SetReferredEmail sets the "referred_email" field.
func (ru *ReferralUpdate) SetReferredEmail(s string) *ReferralUpdate {
	ru.mutation.SetReferredEmail(s)
	return ru
}

// SetNillableReferredEmail sets the "referred_email" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferredEmail(s *string) *ReferralUpdate {
	if s != nil {
		ru.SetReferredEmail(*s)
	}
	return ru
}

// ClearReferredEmail clears the value of the "referred_email" field.
func (ru *ReferralUpdate) ClearReferredEmail() *ReferralUpdate {
	ru.mutation.ClearReferredEmail()
	return ru
}

// SetStatus sets the "status" field.
func (ru *ReferralUpdate) SetStatus(r referral.Status) *ReferralUpdate {
	ru.mutation.SetStatus(r)
	return ru
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableStatus(r *referral.Status) *ReferralUpdate {
	if r != nil {
		ru.SetStatus(*r)
	}
	return ru
}

// SetReferrerCreditsEarned sets the "referrer_credits_earned" field.
func (ru *ReferralUpdate) SetReferrerCreditsEarned(i int) *ReferralUpdate {
	ru.mutation.ResetReferrerCreditsEarned()
	ru.mutation.SetReferrerCreditsEarned(i)
	return ru
}

// SetNillableReferrerCreditsEarned sets the "referrer_credits_earned" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferrerCreditsEarned(i *int) *ReferralUpdate {
	if i != nil {
		ru.SetReferrerCreditsEarned(*i)
	}
	return ru
}

// AddReferrerCreditsEarned adds i to the "referrer_credits_earned" field.
func (ru *ReferralUpdate) AddReferrerCreditsEarned(i int) *ReferralUpdate {
	ru.mutation.AddReferrerCreditsEarned(i)
	return ru
}

// SetReferredCreditsEarned sets the "referred_credits_earned" field.
func (ru *ReferralUpdate) SetReferredCreditsEarned(i int) *ReferralUpdate {
	ru.mutation.ResetReferredCreditsEarned()
	ru.mutation.SetReferredCreditsEarned(i)
	return ru
}

// SetNillableReferredCreditsEarned sets the "referred_credits_earned" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferredCreditsEarned(i *int) *ReferralUpdate {
	if i != nil {
		ru.SetReferredCreditsEarned(*i)
	}
	return ru
}

// AddReferredCreditsEarned adds i to the "referred_credits_earned" field.
func (ru *ReferralUpdate) AddReferredCreditsEarned(i int) *ReferralUpdate {
	ru.mutation.AddReferredCreditsEarned(i)
	return ru
}

// SetReferralType sets the "referral_type" field.
func (ru *ReferralUpdate) SetReferralType(s string) *ReferralUpdate {
	ru.mutation.SetReferralType(s)
	return ru
}

// SetNillableReferralType sets the "referral_type" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableReferralType(s *string) *ReferralUpdate {
	if s != nil {
		ru.SetReferralType(*s)
	}
	return ru
}

// SetCompletedAt sets the "completed_at" field.
func (ru *ReferralUpdate) SetCompletedAt(t time.Time) *ReferralUpdate {
	ru.mutation.SetCompletedAt(t)
	return ru
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableCompletedAt(t *time.Time) *ReferralUpdate {
	if t != nil {
		ru.SetCompletedAt(*t)
	}
	return ru
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (ru *ReferralUpdate) ClearCompletedAt() *ReferralUpdate {
	ru.mutation.ClearCompletedAt()
	return ru
}

// SetExpiresAt sets the "expires_at" field.
func (ru *ReferralUpdate) SetExpiresAt(t time.Time) *ReferralUpdate {
	ru.mutation.SetExpiresAt(t)
	return ru
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableExpiresAt(t *time.Time) *ReferralUpdate {
	if t != nil {
		ru.SetExpiresAt(*t)
	}
	return ru
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (ru *ReferralUpdate) ClearExpiresAt() *ReferralUpdate {
	ru.mutation.ClearExpiresAt()
	return ru
}

// SetReferralConditions sets the "referral_conditions" field.
func (ru *ReferralUpdate) SetReferralConditions(m map[string]interface{}) *ReferralUpdate {
	ru.mutation.SetReferralConditions(m)
	return ru
}

// ClearReferralConditions clears the value of the "referral_conditions" field.
func (ru *ReferralUpdate) ClearReferralConditions() *ReferralUpdate {
	ru.mutation.ClearReferralConditions()
	return ru
}

// SetTrackingData sets the "tracking_data" field.
func (ru *ReferralUpdate) SetTrackingData(m map[string]interface{}) *ReferralUpdate {
	ru.mutation.SetTrackingData(m)
	return ru
}

// ClearTrackingData clears the value of the "tracking_data" field.
func (ru *ReferralUpdate) ClearTrackingData() *ReferralUpdate {
	ru.mutation.ClearTrackingData()
	return ru
}

// SetCampaignID sets the "campaign_id" field.
func (ru *ReferralUpdate) SetCampaignID(s string) *ReferralUpdate {
	ru.mutation.SetCampaignID(s)
	return ru
}

// SetNillableCampaignID sets the "campaign_id" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableCampaignID(s *string) *ReferralUpdate {
	if s != nil {
		ru.SetCampaignID(*s)
	}
	return ru
}

// ClearCampaignID clears the value of the "campaign_id" field.
func (ru *ReferralUpdate) ClearCampaignID() *ReferralUpdate {
	ru.mutation.ClearCampaignID()
	return ru
}

// SetSource sets the "source" field.
func (ru *ReferralUpdate) SetSource(s string) *ReferralUpdate {
	ru.mutation.SetSource(s)
	return ru
}

// SetNillableSource sets the "source" field if the given value is not nil.
func (ru *ReferralUpdate) SetNillableSource(s *string) *ReferralUpdate {
	if s != nil {
		ru.SetSource(*s)
	}
	return ru
}

// ClearSource clears the value of the "source" field.
func (ru *ReferralUpdate) ClearSource() *ReferralUpdate {
	ru.mutation.ClearSource()
	return ru
}

// SetMetadata sets the "metadata" field.
func (ru *ReferralUpdate) SetMetadata(m map[string]interface{}) *ReferralUpdate {
	ru.mutation.SetMetadata(m)
	return ru
}

// ClearMetadata clears the value of the "metadata" field.
func (ru *ReferralUpdate) ClearMetadata() *ReferralUpdate {
	ru.mutation.ClearMetadata()
	return ru
}

// SetUpdatedAt sets the "updated_at" field.
func (ru *ReferralUpdate) SetUpdatedAt(t time.Time) *ReferralUpdate {
	ru.mutation.SetUpdatedAt(t)
	return ru
}

// Mutation returns the ReferralMutation object of the builder.
func (ru *ReferralUpdate) Mutation() *ReferralMutation {
	return ru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ru *ReferralUpdate) Save(ctx context.Context) (int, error) {
	ru.defaults()
	return withHooks(ctx, ru.sqlSave, ru.mutation, ru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ru *ReferralUpdate) SaveX(ctx context.Context) int {
	affected, err := ru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ru *ReferralUpdate) Exec(ctx context.Context) error {
	_, err := ru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ru *ReferralUpdate) ExecX(ctx context.Context) {
	if err := ru.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ru *ReferralUpdate) defaults() {
	if _, ok := ru.mutation.UpdatedAt(); !ok {
		v := referral.UpdateDefaultUpdatedAt()
		ru.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ru *ReferralUpdate) check() error {
	if v, ok := ru.mutation.ReferralCode(); ok {
		if err := referral.ReferralCodeValidator(v); err != nil {
			return &ValidationError{Name: "referral_code", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_code": %w`, err)}
		}
	}
	if v, ok := ru.mutation.ReferredEmail(); ok {
		if err := referral.ReferredEmailValidator(v); err != nil {
			return &ValidationError{Name: "referred_email", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_email": %w`, err)}
		}
	}
	if v, ok := ru.mutation.Status(); ok {
		if err := referral.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Referral.status": %w`, err)}
		}
	}
	if v, ok := ru.mutation.ReferrerCreditsEarned(); ok {
		if err := referral.ReferrerCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referrer_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referrer_credits_earned": %w`, err)}
		}
	}
	if v, ok := ru.mutation.ReferredCreditsEarned(); ok {
		if err := referral.ReferredCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referred_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_credits_earned": %w`, err)}
		}
	}
	if v, ok := ru.mutation.ReferralType(); ok {
		if err := referral.ReferralTypeValidator(v); err != nil {
			return &ValidationError{Name: "referral_type", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_type": %w`, err)}
		}
	}
	if v, ok := ru.mutation.CampaignID(); ok {
		if err := referral.CampaignIDValidator(v); err != nil {
			return &ValidationError{Name: "campaign_id", err: fmt.Errorf(`ent: validator failed for field "Referral.campaign_id": %w`, err)}
		}
	}
	if v, ok := ru.mutation.Source(); ok {
		if err := referral.SourceValidator(v); err != nil {
			return &ValidationError{Name: "source", err: fmt.Errorf(`ent: validator failed for field "Referral.source": %w`, err)}
		}
	}
	return nil
}

func (ru *ReferralUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(referral.Table, referral.Columns, sqlgraph.NewFieldSpec(referral.FieldID, field.TypeUUID))
	if ps := ru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ru.mutation.ReferrerID(); ok {
		_spec.SetField(referral.FieldReferrerID, field.TypeUUID, value)
	}
	if value, ok := ru.mutation.ReferredID(); ok {
		_spec.SetField(referral.FieldReferredID, field.TypeUUID, value)
	}
	if ru.mutation.ReferredIDCleared() {
		_spec.ClearField(referral.FieldReferredID, field.TypeUUID)
	}
	if value, ok := ru.mutation.ReferralCode(); ok {
		_spec.SetField(referral.FieldReferralCode, field.TypeString, value)
	}
	if value, ok := ru.mutation.ReferredEmail(); ok {
		_spec.SetField(referral.FieldReferredEmail, field.TypeString, value)
	}
	if ru.mutation.ReferredEmailCleared() {
		_spec.ClearField(referral.FieldReferredEmail, field.TypeString)
	}
	if value, ok := ru.mutation.Status(); ok {
		_spec.SetField(referral.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ru.mutation.ReferrerCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferrerCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ru.mutation.AddedReferrerCreditsEarned(); ok {
		_spec.AddField(referral.FieldReferrerCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ru.mutation.ReferredCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferredCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ru.mutation.AddedReferredCreditsEarned(); ok {
		_spec.AddField(referral.FieldReferredCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ru.mutation.ReferralType(); ok {
		_spec.SetField(referral.FieldReferralType, field.TypeString, value)
	}
	if value, ok := ru.mutation.CompletedAt(); ok {
		_spec.SetField(referral.FieldCompletedAt, field.TypeTime, value)
	}
	if ru.mutation.CompletedAtCleared() {
		_spec.ClearField(referral.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := ru.mutation.ExpiresAt(); ok {
		_spec.SetField(referral.FieldExpiresAt, field.TypeTime, value)
	}
	if ru.mutation.ExpiresAtCleared() {
		_spec.ClearField(referral.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := ru.mutation.ReferralConditions(); ok {
		_spec.SetField(referral.FieldReferralConditions, field.TypeJSON, value)
	}
	if ru.mutation.ReferralConditionsCleared() {
		_spec.ClearField(referral.FieldReferralConditions, field.TypeJSON)
	}
	if value, ok := ru.mutation.TrackingData(); ok {
		_spec.SetField(referral.FieldTrackingData, field.TypeJSON, value)
	}
	if ru.mutation.TrackingDataCleared() {
		_spec.ClearField(referral.FieldTrackingData, field.TypeJSON)
	}
	if value, ok := ru.mutation.CampaignID(); ok {
		_spec.SetField(referral.FieldCampaignID, field.TypeString, value)
	}
	if ru.mutation.CampaignIDCleared() {
		_spec.ClearField(referral.FieldCampaignID, field.TypeString)
	}
	if value, ok := ru.mutation.Source(); ok {
		_spec.SetField(referral.FieldSource, field.TypeString, value)
	}
	if ru.mutation.SourceCleared() {
		_spec.ClearField(referral.FieldSource, field.TypeString)
	}
	if value, ok := ru.mutation.Metadata(); ok {
		_spec.SetField(referral.FieldMetadata, field.TypeJSON, value)
	}
	if ru.mutation.MetadataCleared() {
		_spec.ClearField(referral.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ru.mutation.UpdatedAt(); ok {
		_spec.SetField(referral.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{referral.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ru.mutation.done = true
	return n, nil
}

// ReferralUpdateOne is the builder for updating a single Referral entity.
type ReferralUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ReferralMutation
}

// SetReferrerID sets the "referrer_id" field.
func (ruo *ReferralUpdateOne) SetReferrerID(u uuid.UUID) *ReferralUpdateOne {
	ruo.mutation.SetReferrerID(u)
	return ruo
}

// SetNillableReferrerID sets the "referrer_id" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferrerID(u *uuid.UUID) *ReferralUpdateOne {
	if u != nil {
		ruo.SetReferrerID(*u)
	}
	return ruo
}

// SetReferredID sets the "referred_id" field.
func (ruo *ReferralUpdateOne) SetReferredID(u uuid.UUID) *ReferralUpdateOne {
	ruo.mutation.SetReferredID(u)
	return ruo
}

// SetNillableReferredID sets the "referred_id" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferredID(u *uuid.UUID) *ReferralUpdateOne {
	if u != nil {
		ruo.SetReferredID(*u)
	}
	return ruo
}

// ClearReferredID clears the value of the "referred_id" field.
func (ruo *ReferralUpdateOne) ClearReferredID() *ReferralUpdateOne {
	ruo.mutation.ClearReferredID()
	return ruo
}

// SetReferralCode sets the "referral_code" field.
func (ruo *ReferralUpdateOne) SetReferralCode(s string) *ReferralUpdateOne {
	ruo.mutation.SetReferralCode(s)
	return ruo
}

// SetNillableReferralCode sets the "referral_code" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferralCode(s *string) *ReferralUpdateOne {
	if s != nil {
		ruo.SetReferralCode(*s)
	}
	return ruo
}

// SetReferredEmail sets the "referred_email" field.
func (ruo *ReferralUpdateOne) SetReferredEmail(s string) *ReferralUpdateOne {
	ruo.mutation.SetReferredEmail(s)
	return ruo
}

// SetNillableReferredEmail sets the "referred_email" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferredEmail(s *string) *ReferralUpdateOne {
	if s != nil {
		ruo.SetReferredEmail(*s)
	}
	return ruo
}

// ClearReferredEmail clears the value of the "referred_email" field.
func (ruo *ReferralUpdateOne) ClearReferredEmail() *ReferralUpdateOne {
	ruo.mutation.ClearReferredEmail()
	return ruo
}

// SetStatus sets the "status" field.
func (ruo *ReferralUpdateOne) SetStatus(r referral.Status) *ReferralUpdateOne {
	ruo.mutation.SetStatus(r)
	return ruo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableStatus(r *referral.Status) *ReferralUpdateOne {
	if r != nil {
		ruo.SetStatus(*r)
	}
	return ruo
}

// SetReferrerCreditsEarned sets the "referrer_credits_earned" field.
func (ruo *ReferralUpdateOne) SetReferrerCreditsEarned(i int) *ReferralUpdateOne {
	ruo.mutation.ResetReferrerCreditsEarned()
	ruo.mutation.SetReferrerCreditsEarned(i)
	return ruo
}

// SetNillableReferrerCreditsEarned sets the "referrer_credits_earned" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferrerCreditsEarned(i *int) *ReferralUpdateOne {
	if i != nil {
		ruo.SetReferrerCreditsEarned(*i)
	}
	return ruo
}

// AddReferrerCreditsEarned adds i to the "referrer_credits_earned" field.
func (ruo *ReferralUpdateOne) AddReferrerCreditsEarned(i int) *ReferralUpdateOne {
	ruo.mutation.AddReferrerCreditsEarned(i)
	return ruo
}

// SetReferredCreditsEarned sets the "referred_credits_earned" field.
func (ruo *ReferralUpdateOne) SetReferredCreditsEarned(i int) *ReferralUpdateOne {
	ruo.mutation.ResetReferredCreditsEarned()
	ruo.mutation.SetReferredCreditsEarned(i)
	return ruo
}

// SetNillableReferredCreditsEarned sets the "referred_credits_earned" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferredCreditsEarned(i *int) *ReferralUpdateOne {
	if i != nil {
		ruo.SetReferredCreditsEarned(*i)
	}
	return ruo
}

// AddReferredCreditsEarned adds i to the "referred_credits_earned" field.
func (ruo *ReferralUpdateOne) AddReferredCreditsEarned(i int) *ReferralUpdateOne {
	ruo.mutation.AddReferredCreditsEarned(i)
	return ruo
}

// SetReferralType sets the "referral_type" field.
func (ruo *ReferralUpdateOne) SetReferralType(s string) *ReferralUpdateOne {
	ruo.mutation.SetReferralType(s)
	return ruo
}

// SetNillableReferralType sets the "referral_type" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableReferralType(s *string) *ReferralUpdateOne {
	if s != nil {
		ruo.SetReferralType(*s)
	}
	return ruo
}

// SetCompletedAt sets the "completed_at" field.
func (ruo *ReferralUpdateOne) SetCompletedAt(t time.Time) *ReferralUpdateOne {
	ruo.mutation.SetCompletedAt(t)
	return ruo
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableCompletedAt(t *time.Time) *ReferralUpdateOne {
	if t != nil {
		ruo.SetCompletedAt(*t)
	}
	return ruo
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (ruo *ReferralUpdateOne) ClearCompletedAt() *ReferralUpdateOne {
	ruo.mutation.ClearCompletedAt()
	return ruo
}

// SetExpiresAt sets the "expires_at" field.
func (ruo *ReferralUpdateOne) SetExpiresAt(t time.Time) *ReferralUpdateOne {
	ruo.mutation.SetExpiresAt(t)
	return ruo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableExpiresAt(t *time.Time) *ReferralUpdateOne {
	if t != nil {
		ruo.SetExpiresAt(*t)
	}
	return ruo
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (ruo *ReferralUpdateOne) ClearExpiresAt() *ReferralUpdateOne {
	ruo.mutation.ClearExpiresAt()
	return ruo
}

// SetReferralConditions sets the "referral_conditions" field.
func (ruo *ReferralUpdateOne) SetReferralConditions(m map[string]interface{}) *ReferralUpdateOne {
	ruo.mutation.SetReferralConditions(m)
	return ruo
}

// ClearReferralConditions clears the value of the "referral_conditions" field.
func (ruo *ReferralUpdateOne) ClearReferralConditions() *ReferralUpdateOne {
	ruo.mutation.ClearReferralConditions()
	return ruo
}

// SetTrackingData sets the "tracking_data" field.
func (ruo *ReferralUpdateOne) SetTrackingData(m map[string]interface{}) *ReferralUpdateOne {
	ruo.mutation.SetTrackingData(m)
	return ruo
}

// ClearTrackingData clears the value of the "tracking_data" field.
func (ruo *ReferralUpdateOne) ClearTrackingData() *ReferralUpdateOne {
	ruo.mutation.ClearTrackingData()
	return ruo
}

// SetCampaignID sets the "campaign_id" field.
func (ruo *ReferralUpdateOne) SetCampaignID(s string) *ReferralUpdateOne {
	ruo.mutation.SetCampaignID(s)
	return ruo
}

// SetNillableCampaignID sets the "campaign_id" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableCampaignID(s *string) *ReferralUpdateOne {
	if s != nil {
		ruo.SetCampaignID(*s)
	}
	return ruo
}

// ClearCampaignID clears the value of the "campaign_id" field.
func (ruo *ReferralUpdateOne) ClearCampaignID() *ReferralUpdateOne {
	ruo.mutation.ClearCampaignID()
	return ruo
}

// SetSource sets the "source" field.
func (ruo *ReferralUpdateOne) SetSource(s string) *ReferralUpdateOne {
	ruo.mutation.SetSource(s)
	return ruo
}

// SetNillableSource sets the "source" field if the given value is not nil.
func (ruo *ReferralUpdateOne) SetNillableSource(s *string) *ReferralUpdateOne {
	if s != nil {
		ruo.SetSource(*s)
	}
	return ruo
}

// ClearSource clears the value of the "source" field.
func (ruo *ReferralUpdateOne) ClearSource() *ReferralUpdateOne {
	ruo.mutation.ClearSource()
	return ruo
}

// SetMetadata sets the "metadata" field.
func (ruo *ReferralUpdateOne) SetMetadata(m map[string]interface{}) *ReferralUpdateOne {
	ruo.mutation.SetMetadata(m)
	return ruo
}

// ClearMetadata clears the value of the "metadata" field.
func (ruo *ReferralUpdateOne) ClearMetadata() *ReferralUpdateOne {
	ruo.mutation.ClearMetadata()
	return ruo
}

// SetUpdatedAt sets the "updated_at" field.
func (ruo *ReferralUpdateOne) SetUpdatedAt(t time.Time) *ReferralUpdateOne {
	ruo.mutation.SetUpdatedAt(t)
	return ruo
}

// Mutation returns the ReferralMutation object of the builder.
func (ruo *ReferralUpdateOne) Mutation() *ReferralMutation {
	return ruo.mutation
}

// Where appends a list predicates to the ReferralUpdate builder.
func (ruo *ReferralUpdateOne) Where(ps ...predicate.Referral) *ReferralUpdateOne {
	ruo.mutation.Where(ps...)
	return ruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ruo *ReferralUpdateOne) Select(field string, fields ...string) *ReferralUpdateOne {
	ruo.fields = append([]string{field}, fields...)
	return ruo
}

// Save executes the query and returns the updated Referral entity.
func (ruo *ReferralUpdateOne) Save(ctx context.Context) (*Referral, error) {
	ruo.defaults()
	return withHooks(ctx, ruo.sqlSave, ruo.mutation, ruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ruo *ReferralUpdateOne) SaveX(ctx context.Context) *Referral {
	node, err := ruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ruo *ReferralUpdateOne) Exec(ctx context.Context) error {
	_, err := ruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ruo *ReferralUpdateOne) ExecX(ctx context.Context) {
	if err := ruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ruo *ReferralUpdateOne) defaults() {
	if _, ok := ruo.mutation.UpdatedAt(); !ok {
		v := referral.UpdateDefaultUpdatedAt()
		ruo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ruo *ReferralUpdateOne) check() error {
	if v, ok := ruo.mutation.ReferralCode(); ok {
		if err := referral.ReferralCodeValidator(v); err != nil {
			return &ValidationError{Name: "referral_code", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_code": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.ReferredEmail(); ok {
		if err := referral.ReferredEmailValidator(v); err != nil {
			return &ValidationError{Name: "referred_email", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_email": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.Status(); ok {
		if err := referral.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Referral.status": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.ReferrerCreditsEarned(); ok {
		if err := referral.ReferrerCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referrer_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referrer_credits_earned": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.ReferredCreditsEarned(); ok {
		if err := referral.ReferredCreditsEarnedValidator(v); err != nil {
			return &ValidationError{Name: "referred_credits_earned", err: fmt.Errorf(`ent: validator failed for field "Referral.referred_credits_earned": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.ReferralType(); ok {
		if err := referral.ReferralTypeValidator(v); err != nil {
			return &ValidationError{Name: "referral_type", err: fmt.Errorf(`ent: validator failed for field "Referral.referral_type": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.CampaignID(); ok {
		if err := referral.CampaignIDValidator(v); err != nil {
			return &ValidationError{Name: "campaign_id", err: fmt.Errorf(`ent: validator failed for field "Referral.campaign_id": %w`, err)}
		}
	}
	if v, ok := ruo.mutation.Source(); ok {
		if err := referral.SourceValidator(v); err != nil {
			return &ValidationError{Name: "source", err: fmt.Errorf(`ent: validator failed for field "Referral.source": %w`, err)}
		}
	}
	return nil
}

func (ruo *ReferralUpdateOne) sqlSave(ctx context.Context) (_node *Referral, err error) {
	if err := ruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(referral.Table, referral.Columns, sqlgraph.NewFieldSpec(referral.FieldID, field.TypeUUID))
	id, ok := ruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Referral.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, referral.FieldID)
		for _, f := range fields {
			if !referral.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != referral.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ruo.mutation.ReferrerID(); ok {
		_spec.SetField(referral.FieldReferrerID, field.TypeUUID, value)
	}
	if value, ok := ruo.mutation.ReferredID(); ok {
		_spec.SetField(referral.FieldReferredID, field.TypeUUID, value)
	}
	if ruo.mutation.ReferredIDCleared() {
		_spec.ClearField(referral.FieldReferredID, field.TypeUUID)
	}
	if value, ok := ruo.mutation.ReferralCode(); ok {
		_spec.SetField(referral.FieldReferralCode, field.TypeString, value)
	}
	if value, ok := ruo.mutation.ReferredEmail(); ok {
		_spec.SetField(referral.FieldReferredEmail, field.TypeString, value)
	}
	if ruo.mutation.ReferredEmailCleared() {
		_spec.ClearField(referral.FieldReferredEmail, field.TypeString)
	}
	if value, ok := ruo.mutation.Status(); ok {
		_spec.SetField(referral.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ruo.mutation.ReferrerCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferrerCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ruo.mutation.AddedReferrerCreditsEarned(); ok {
		_spec.AddField(referral.FieldReferrerCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ruo.mutation.ReferredCreditsEarned(); ok {
		_spec.SetField(referral.FieldReferredCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ruo.mutation.AddedReferredCreditsEarned(); ok {
		_spec.AddField(referral.FieldReferredCreditsEarned, field.TypeInt, value)
	}
	if value, ok := ruo.mutation.ReferralType(); ok {
		_spec.SetField(referral.FieldReferralType, field.TypeString, value)
	}
	if value, ok := ruo.mutation.CompletedAt(); ok {
		_spec.SetField(referral.FieldCompletedAt, field.TypeTime, value)
	}
	if ruo.mutation.CompletedAtCleared() {
		_spec.ClearField(referral.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := ruo.mutation.ExpiresAt(); ok {
		_spec.SetField(referral.FieldExpiresAt, field.TypeTime, value)
	}
	if ruo.mutation.ExpiresAtCleared() {
		_spec.ClearField(referral.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := ruo.mutation.ReferralConditions(); ok {
		_spec.SetField(referral.FieldReferralConditions, field.TypeJSON, value)
	}
	if ruo.mutation.ReferralConditionsCleared() {
		_spec.ClearField(referral.FieldReferralConditions, field.TypeJSON)
	}
	if value, ok := ruo.mutation.TrackingData(); ok {
		_spec.SetField(referral.FieldTrackingData, field.TypeJSON, value)
	}
	if ruo.mutation.TrackingDataCleared() {
		_spec.ClearField(referral.FieldTrackingData, field.TypeJSON)
	}
	if value, ok := ruo.mutation.CampaignID(); ok {
		_spec.SetField(referral.FieldCampaignID, field.TypeString, value)
	}
	if ruo.mutation.CampaignIDCleared() {
		_spec.ClearField(referral.FieldCampaignID, field.TypeString)
	}
	if value, ok := ruo.mutation.Source(); ok {
		_spec.SetField(referral.FieldSource, field.TypeString, value)
	}
	if ruo.mutation.SourceCleared() {
		_spec.ClearField(referral.FieldSource, field.TypeString)
	}
	if value, ok := ruo.mutation.Metadata(); ok {
		_spec.SetField(referral.FieldMetadata, field.TypeJSON, value)
	}
	if ruo.mutation.MetadataCleared() {
		_spec.ClearField(referral.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ruo.mutation.UpdatedAt(); ok {
		_spec.SetField(referral.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &Referral{config: ruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{referral.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ruo.mutation.done = true
	return _node, nil
}

package notification

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	notificationv1 "github.com/social-content-ai/proto-shared/notification/v1"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Client wraps the notification service gRPC client
type Client struct {
	client notificationv1.NotificationServiceClient
	conn   *grpc.ClientConn
	logger logging.Logger
}

// NewClient creates a new notification service client
func NewClient(address string, logger logging.Logger) (*Client, error) {
	conn, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to notification service: %w", err)
	}

	client := notificationv1.NewNotificationServiceClient(conn)

	return &Client{
		client: client,
		conn:   conn,
		logger: logger,
	}, nil
}

// Close closes the gRPC connection
func (c *Client) Close() error {
	return c.conn.Close()
}

// SendEmailVerification sends email verification notification
func (c *Client) SendEmailVerification(ctx context.Context, userID, email, verificationToken string) error {
	req := &notificationv1.SendNotificationRequest{
		UserId:   userID,
		Type:     "email_verification",
		Title:    "Verify Your Email Address",
		Message:  fmt.Sprintf("Please click the link to verify your email: %s", c.buildVerificationURL(verificationToken)),
		Channels: []notificationv1.NotificationChannel{notificationv1.NotificationChannel_EMAIL},
		Priority: notificationv1.NotificationPriority_HIGH,
		Data: map[string]string{
			"email":              email,
			"verification_token": verificationToken,
			"verification_url":   c.buildVerificationURL(verificationToken),
			"template_type":      "email_verification",
		},
		Immediate: true,
	}

	resp, err := c.client.SendNotification(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"email":   email,
		}).Error("Failed to send email verification notification")
		return fmt.Errorf("failed to send email verification: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"email":           email,
		"notification_id": resp.NotificationId,
	}).Info("Email verification notification sent successfully")

	return nil
}

// SendWelcomeEmail sends welcome email notification
func (c *Client) SendWelcomeEmail(ctx context.Context, userID, email, firstName string) error {
	req := &notificationv1.SendNotificationRequest{
		UserId:   userID,
		Type:     "user_welcome",
		Title:    "Welcome to Social Content AI!",
		Message:  fmt.Sprintf("Welcome %s! Your account has been created successfully.", firstName),
		Channels: []notificationv1.NotificationChannel{notificationv1.NotificationChannel_EMAIL},
		Priority: notificationv1.NotificationPriority_NORMAL,
		Data: map[string]string{
			"email":         email,
			"first_name":    firstName,
			"template_type": "user_welcome",
		},
		Immediate: true,
	}

	resp, err := c.client.SendNotification(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"email":   email,
		}).Error("Failed to send welcome email notification")
		return fmt.Errorf("failed to send welcome email: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"email":           email,
		"notification_id": resp.NotificationId,
	}).Info("Welcome email notification sent successfully")

	return nil
}

// SendPasswordResetEmail sends password reset notification
func (c *Client) SendPasswordResetEmail(ctx context.Context, userID, email, resetToken string) error {
	req := &notificationv1.SendNotificationRequest{
		UserId:   userID,
		Type:     "password_reset",
		Title:    "Reset Your Password",
		Message:  fmt.Sprintf("Click the link to reset your password: %s", c.buildPasswordResetURL(resetToken)),
		Channels: []notificationv1.NotificationChannel{notificationv1.NotificationChannel_EMAIL},
		Priority: notificationv1.NotificationPriority_HIGH,
		Data: map[string]string{
			"email":             email,
			"reset_token":       resetToken,
			"reset_url":         c.buildPasswordResetURL(resetToken),
			"template_type":     "password_reset",
			"expiry_time":       time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		},
		Immediate: true,
	}

	resp, err := c.client.SendNotification(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"email":   email,
		}).Error("Failed to send password reset notification")
		return fmt.Errorf("failed to send password reset email: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"email":           email,
		"notification_id": resp.NotificationId,
	}).Info("Password reset email notification sent successfully")

	return nil
}

// SendSecurityAlert sends security alert notification
func (c *Client) SendSecurityAlert(ctx context.Context, userID, email, alertType, message string) error {
	req := &notificationv1.SendNotificationRequest{
		UserId:   userID,
		Type:     "security_alert",
		Title:    "Security Alert",
		Message:  message,
		Channels: []notificationv1.NotificationChannel{notificationv1.NotificationChannel_EMAIL},
		Priority: notificationv1.NotificationPriority_URGENT,
		Data: map[string]string{
			"email":        email,
			"alert_type":   alertType,
			"template_type": "security_alert",
			"timestamp":    time.Now().Format(time.RFC3339),
		},
		Immediate: true,
	}

	resp, err := c.client.SendNotification(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":    userID,
			"email":      email,
			"alert_type": alertType,
		}).Error("Failed to send security alert notification")
		return fmt.Errorf("failed to send security alert: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":         userID,
		"email":           email,
		"alert_type":      alertType,
		"notification_id": resp.NotificationId,
	}).Info("Security alert notification sent successfully")

	return nil
}

// buildVerificationURL builds the email verification URL
func (c *Client) buildVerificationURL(token string) string {
	// This should be configurable based on environment
	baseURL := "https://app.social-content-ai.com" // TODO: Make configurable
	return fmt.Sprintf("%s/auth/verify-email?token=%s", baseURL, token)
}

// buildPasswordResetURL builds the password reset URL
func (c *Client) buildPasswordResetURL(token string) string {
	// This should be configurable based on environment
	baseURL := "https://app.social-content-ai.com" // TODO: Make configurable
	return fmt.Sprintf("%s/auth/reset-password?token=%s", baseURL, token)
}

// SendTemplatedNotification sends a templated notification
func (c *Client) SendTemplatedNotification(ctx context.Context, req *SendTemplatedNotificationRequest) error {
	grpcReq := &notificationv1.SendTemplatedNotificationRequest{
		UserId:     req.UserID,
		TemplateId: req.TemplateID,
		Variables:  req.Variables,
		Channels:   req.Channels,
		Priority:   req.Priority,
		Data:       req.Data,
		Immediate:  req.Immediate,
	}

	resp, err := c.client.SendTemplatedNotification(ctx, grpcReq)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":     req.UserID,
			"template_id": req.TemplateID,
		}).Error("Failed to send templated notification")
		return fmt.Errorf("failed to send templated notification: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":         req.UserID,
		"template_id":     req.TemplateID,
		"notification_id": resp.NotificationId,
	}).Info("Templated notification sent successfully")

	return nil
}

// SendTemplatedNotificationRequest represents a templated notification request
type SendTemplatedNotificationRequest struct {
	UserID     string
	TemplateID string
	Variables  map[string]string
	Channels   []notificationv1.NotificationChannel
	Priority   notificationv1.NotificationPriority
	Data       map[string]string
	Immediate  bool
}

openapi: 3.0.3
info:
  title: SocialAI API
  description: |
    API cho nền tảng quản lý nội dung mạng xã hội thông minh với AI

    ## Tính năng chính:
    - 🤖 Tạo nội dung bằng AI với hệ thống credits
    - 📱 Quản lý đa nền tảng (Facebook, Instagram, LinkedIn, Twitter, YouTube, Zalo)
    - 📅 Lên lịch đăng bài tự động
    - 📊 Thống kê và phân tích hiệu suất
    - 🎨 Quản lý template và marketplace với monetization
    - 🖼️ Quản lý hình ảnh và tạo ảnh AI
    - 🔗 Tích hợp với các nền tảng bên ngoài
    - 👤 Quản lý tài khoản và bảo mật
    - 💳 Hệ thống credits và gói dịch vụ
    - 🧠 RAG Knowledge Base cho template chuyên môn

    ## Phiên bản 1.2.0 - Cập nhật:
    - Team Collaboration với workspace management
    - AI Optimal Scheduling với engagement analysis
    - Template Revenue Dashboard với withdrawal system
    - Advanced member role management
    - Credit pooling cho team workspace
  version: 1.2.0
  contact:
    name: SocialAI Support
    email: <EMAIL>
    url: https://socialai.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.socialai.com/v1
    description: Production server
  - url: https://staging-api.socialai.com/v1
    description: Staging server
  - url: http://localhost:3000/api/v1
    description: Development server

security:
  - bearerAuth: []
  - apiKey: []

tags:
  - name: Authentication
    description: Đăng nhập, đăng ký và quản lý phiên
  - name: Users
    description: Quản lý thông tin người dùng
  - name: Posts
    description: Tạo, quản lý bài viết và lên lịch đăng bài
  - name: AI Content
    description: Tạo nội dung bằng AI
  - name: Templates
    description: Quản lý template và marketplace
  - name: Assets
    description: Quản lý tài nguyên (hình ảnh, file, documents)
  - name: Analytics
    description: Thống kê và phân tích
  - name: Integrations
    description: Tích hợp với các nền tảng
  - name: Credits
    description: Quản lý credits và gói dịch vụ
  - name: Plans
    description: Thông tin gói dịch vụ và credit hằng tháng
  - name: CreditConfig
    description: Cấu hình phí credit theo AI model
  - name: Documentation
    description: Tài liệu API và hướng dẫn sử dụng
  - name: Security
    description: Bảo mật tài khoản và quản lý dữ liệu
  - name: Workspaces
    description: Quản lý workspace & cộng tác nhóm
  - name: WorkspaceMembers
    description: Quản lý thành viên và quyền trong workspace
  - name: ScheduleAI
    description: AI gợi ý thời gian đăng tối ưu
  - name: TemplateRevenue
    description: Báo cáo & rút tiền doanh thu template trả phí
  - name: Notifications
    description: Quản lý thông báo và cài đặt thông báo

paths:
  # Credit Configuration & Plans
  /credits/config:
    get:
      tags: [CreditConfig]
      summary: Lấy cấu hình phí credit theo model
      description: |
        Trả về bảng phí credit áp dụng cho từng model AI (text & image).
        Giá trị này được backend dùng để trừ credit khi gọi `/ai/*`.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Cấu hình phí
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ModelCreditConfig'

  /plans:
    get:
      tags: [Plans]
      summary: Lấy danh sách gói dịch vụ
      description: Danh sách gói, allowance credit hàng tháng, giá & quyền lợi.
      responses:
        '200':
          description: Danh sách gói
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Plan'

  /users/credits/allowance:
    post:
      tags: [Credits]
      summary: Nhận credit hàng tháng
      description: |
        Cấp phát credit định kỳ dựa trên gói hiện tại.
        Endpoint **idempotent** – chỉ cấp phát 1 lần trong chu kỳ. Backend cũng có job tự động;         endpoint này hỗ trợ manual/cron hoặc khi user login.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Đã cấp phát credit
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          addedCredits:
                            type: integer
                            example: 100
                          currentCredits:
                            type: integer
                            example: 850

  # Workspace Management
  /workspaces:
    get:
      tags: [Workspaces]
      summary: Danh sách workspace của người dùng
      description: Lấy danh sách tất cả workspace mà người dùng tham gia
      security:
        - bearerAuth: []
      parameters:
        - name: role
          in: query
          schema:
            type: string
            enum: [owner, admin, editor, viewer]
          description: "Lọc theo vai trò trong workspace"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Danh sách workspace
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách workspace thành công"
                      data:
                        type: object
                        properties:
                          workspaces:
                            type: array
                            items:
                              $ref: '#/components/schemas/Workspace'
                          pagination:
                            $ref: '#/components/schemas/Pagination'

    post:
      tags: [Workspaces]
      summary: Tạo workspace mới
      description: Tạo workspace mới với người dùng hiện tại làm owner
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name]
              properties:
                name:
                  type: string
                  minLength: 3
                  maxLength: 100
                  example: "Marketing Team"
                  description: "Tên workspace"
                description:
                  type: string
                  maxLength: 500
                  example: "Workspace cho team marketing"
                  description: "Mô tả workspace"
                logo:
                  type: string
                  format: uri
                  example: "https://example.com/logo.png"
                  description: "URL logo workspace"
                initialCreditPool:
                  type: integer
                  minimum: 0
                  default: 0
                  example: 1000
                  description: "Số credits ban đầu cho workspace"
      responses:
        '201':
          description: Tạo workspace thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tạo workspace thành công!"
                      data:
                        $ref: '#/components/schemas/Workspace'

  /workspaces/{workspaceId}:
    get:
      tags: [Workspaces]
      summary: Chi tiết workspace
      description: Lấy thông tin chi tiết của workspace
      security:
        - bearerAuth: []
      parameters:
        - name: workspaceId
          in: path
          required: true
          schema:
            type: string
          example: "ws_123"
      responses:
        '200':
          description: Thông tin workspace
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy thông tin workspace thành công"
                      data:
                        $ref: '#/components/schemas/WorkspaceDetail'

    patch:
      tags: [Workspaces]
      summary: Cập nhật workspace
      description: Cập nhật thông tin workspace (chỉ owner/admin)
      security:
        - bearerAuth: []
      parameters:
        - name: workspaceId
          in: path
          required: true
          schema:
            type: string
          example: "ws_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  minLength: 3
                  maxLength: 100
                  example: "Marketing Team Updated"
                description:
                  type: string
                  maxLength: 500
                  example: "Workspace cho team marketing - updated"
                logo:
                  type: string
                  format: uri
                  example: "https://example.com/new-logo.png"
                creditPool:
                  type: integer
                  minimum: 0
                  example: 2000
                  description: "Cập nhật credit pool (chỉ owner)"
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật workspace thành công!"
                      data:
                        $ref: '#/components/schemas/Workspace'

    delete:
      tags: [Workspaces]
      summary: Xóa workspace
      description: Xóa workspace (chỉ owner)
      security:
        - bearerAuth: []
      parameters:
        - name: workspaceId
          in: path
          required: true
          schema:
            type: string
          example: "ws_123"
      responses:
        '204':
          description: Đã xóa workspace
        '403':
          description: Không có quyền xóa workspace
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "Chỉ owner mới có thể xóa workspace"
                      code:
                        example: "INSUFFICIENT_PERMISSION"

  /workspaces/{workspaceId}/members:
    get:
      tags: [WorkspaceMembers]
      summary: Danh sách thành viên workspace
      description: Lấy danh sách tất cả thành viên trong workspace
      security:
        - bearerAuth: []
      parameters:
        - name: workspaceId
          in: path
          required: true
          schema:
            type: string
          example: "ws_123"
        - name: role
          in: query
          schema:
            type: string
            enum: [owner, admin, editor, viewer]
          description: "Lọc theo vai trò"
        - name: status
          in: query
          schema:
            type: string
            enum: [active, pending, inactive]
            default: active
          description: "Lọc theo trạng thái"
      responses:
        '200':
          description: Danh sách thành viên
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách thành viên thành công"
                      data:
                        type: object
                        properties:
                          members:
                            type: array
                            items:
                              $ref: '#/components/schemas/WorkspaceMember'
                          summary:
                            type: object
                            properties:
                              totalMembers:
                                type: integer
                                example: 8
                              byRole:
                                type: object
                                properties:
                                  owner:
                                    type: integer
                                    example: 1
                                  admin:
                                    type: integer
                                    example: 2
                                  editor:
                                    type: integer
                                    example: 4
                                  viewer:
                                    type: integer
                                    example: 1

    post:
      tags: [WorkspaceMembers]
      summary: Mời thành viên vào workspace
      description: Gửi lời mời tham gia workspace (admin/owner only)
      security:
        - bearerAuth: []
      parameters:
        - name: workspaceId
          in: path
          required: true
          schema:
            type: string
          example: "ws_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, role]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                  description: "Email người được mời"
                role:
                  type: string
                  enum: [admin, editor, viewer]
                  example: "editor"
                  description: "Vai trò trong workspace"
                message:
                  type: string
                  maxLength: 500
                  example: "Chào mừng bạn tham gia team!"
                  description: "Tin nhắn kèm lời mời"
      responses:
        '201':
          description: Gửi lời mời thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Gửi lời mời thành công!"
                      data:
                        type: object
                        properties:
                          inviteId:
                            type: string
                            example: "invite_456"
                          email:
                            type: string
                            example: "<EMAIL>"
                          role:
                            type: string
                            example: "editor"
                          status:
                            type: string
                            example: "pending"
                          expiresAt:
                            type: string
                            format: date-time
                            example: "2024-02-22T10:30:00Z"

  # Authentication Endpoints
  /auth/register:
    post:
      tags: [Authentication]
      summary: Đăng ký tài khoản mới
      description: Tạo tài khoản người dùng mới với thông tin cơ bản
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [fullName, email, phone, password]
              properties:
                fullName:
                  type: string
                  minLength: 2
                  example: "Nguyễn Văn A"
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                phone:
                  type: string
                  pattern: '^[0-9]{10,11}$'
                  example: "**********"
                password:
                  type: string
                  minLength: 6
                  example: "password123"
                agreement:
                  type: boolean
                  example: true
                newsletter:
                  type: boolean
                  example: false
      responses:
        '201':
          description: Đăng ký thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản."
                      data:
                        type: object
                        properties:
                          userId:
                            type: string
                            example: "user_123456"
                          email:
                            type: string
                            example: "<EMAIL>"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_register_123"
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: Email đã tồn tại
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "Email đã được sử dụng"
                      code:
                        example: "EMAIL_EXISTS"

  /auth/login:
    post:
      tags: [Authentication]
      summary: Đăng nhập
      description: Xác thực người dùng và tạo access token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  example: "password123"
                remember:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Đăng nhập thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đăng nhập thành công!"
                      data:
                        type: object
                        properties:
                          accessToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                          refreshToken:
                            type: string
                            example: "refresh_token_here"
                          user:
                            $ref: '#/components/schemas/User'
                          expiresIn:
                            type: integer
                            example: 3600
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_login_456"
                          loginMethod:
                            type: string
                            example: "email"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/logout:
    post:
      tags: [Authentication]
      summary: Đăng xuất
      description: Hủy access token và đăng xuất người dùng
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Đăng xuất thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Đăng xuất thành công!"

  /auth/forgot-password:
    post:
      tags: [Authentication]
      summary: Quên mật khẩu
      description: Gửi email khôi phục mật khẩu
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
      responses:
        '200':
          description: Email khôi phục đã được gửi
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Email khôi phục mật khẩu đã được gửi!"

  /auth/social/{provider}:
    post:
      tags: [Authentication]
      summary: Đăng nhập bằng mạng xã hội
      description: Xác thực qua Google, Facebook, etc.
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [google, facebook]
          example: "google"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [token]
              properties:
                token:
                  type: string
                  description: OAuth token từ provider
                  example: "oauth_token_from_provider"
      responses:
        '200':
          description: Đăng nhập thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      accessToken:
                        type: string
                      user:
                        $ref: '#/components/schemas/User'

  # Authentication History & Sessions
  /auth/sessions:
    get:
      tags: [Authentication]
      summary: Lấy danh sách phiên đăng nhập
      description: Lấy danh sách tất cả phiên đăng nhập đang hoạt động và lịch sử đăng nhập
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, expired, all]
            default: all
          example: "active"
          description: "Trạng thái phiên đăng nhập"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Danh sách phiên đăng nhập
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách phiên đăng nhập thành công"
                      data:
                        type: object
                        properties:
                          sessions:
                            type: array
                            items:
                              $ref: '#/components/schemas/LoginSession'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
                          summary:
                            type: object
                            properties:
                              totalSessions:
                                type: integer
                                example: 15
                              activeSessions:
                                type: integer
                                example: 3
                              expiredSessions:
                                type: integer
                                example: 12
                              currentSession:
                                type: string
                                example: "session_current_123"
                              lastLogin:
                                type: string
                                format: date-time
                                example: "2024-02-15T10:30:00Z"

  /auth/sessions/{sessionId}:
    delete:
      tags: [Authentication]
      summary: Xóa phiên đăng nhập
      description: Đăng xuất và xóa một phiên đăng nhập cụ thể (thiết bị)
      security:
        - bearerAuth: []
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "session_123"
      responses:
        '200':
          description: Xóa phiên đăng nhập thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đăng xuất thiết bị thành công!"
                      data:
                        type: object
                        properties:
                          sessionId:
                            type: string
                            example: "session_123"
                          deviceInfo:
                            type: string
                            example: "Chrome on Windows"
                          loggedOutAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
        '404':
          $ref: '#/components/responses/NotFound'

  /auth/sessions/revoke-all:
    post:
      tags: [Authentication]
      summary: Đăng xuất tất cả thiết bị
      description: Đăng xuất khỏi tất cả thiết bị (trừ thiết bị hiện tại)
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                excludeCurrent:
                  type: boolean
                  default: true
                  example: true
                  description: "Giữ phiên đăng nhập hiện tại"
                reason:
                  type: string
                  example: "Bảo mật tài khoản"
                  description: "Lý do đăng xuất"
      responses:
        '200':
          description: Đăng xuất tất cả thiết bị thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đăng xuất tất cả thiết bị thành công!"
                      data:
                        type: object
                        properties:
                          revokedSessions:
                            type: integer
                            example: 4
                          currentSessionKept:
                            type: boolean
                            example: true
                          revokedAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"

  # 2FA Authentication
  /auth/2fa/setup:
    post:
      tags: [Authentication]
      summary: Thiết lập 2FA
      description: Tạo QR code và secret key để thiết lập xác thực 2 bước
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                method:
                  type: string
                  enum: [totp, sms]
                  default: totp
                  example: "totp"
                  description: "Phương thức 2FA"
                phoneNumber:
                  type: string
                  example: "+84123456789"
                  description: "Số điện thoại (cho SMS 2FA)"
      responses:
        '200':
          description: Thiết lập 2FA thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Thiết lập 2FA thành công!"
                      data:
                        type: object
                        properties:
                          method:
                            type: string
                            example: "totp"
                          secretKey:
                            type: string
                            example: "JBSWY3DPEHPK3PXP"
                            description: "Secret key cho TOTP app"
                          qrCodeUrl:
                            type: string
                            format: uri
                            example: "https://api.socialai.com/v1/auth/2fa/qr/temp_123"
                            description: "URL để lấy QR code"
                          qrCodeData:
                            type: string
                            example: "otpauth://totp/SocialAI:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=SocialAI"
                            description: "Data cho QR code"
                          backupCodes:
                            type: array
                            items:
                              type: string
                            example: ["12345678", "87654321", "11223344"]
                            description: "Mã backup khẩn cấp"
                          setupToken:
                            type: string
                            example: "setup_token_456"
                            description: "Token để verify setup"

  /auth/2fa/qr/{token}:
    get:
      tags: [Authentication]
      summary: Lấy QR code
      description: Lấy hình ảnh QR code để scan bằng authenticator app
      security:
        - bearerAuth: []
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
          example: "temp_123"
      responses:
        '200':
          description: QR code image
          content:
            image/png:
              schema:
                type: string
                format: binary
        '404':
          $ref: '#/components/responses/NotFound'

  /auth/2fa/verify-setup:
    post:
      tags: [Authentication]
      summary: Xác thực thiết lập 2FA
      description: Xác thực mã từ authenticator app để hoàn tất thiết lập 2FA
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [setupToken, code]
              properties:
                setupToken:
                  type: string
                  example: "setup_token_456"
                  description: "Token từ bước setup"
                code:
                  type: string
                  example: "123456"
                  description: "Mã 6 số từ authenticator app"
      responses:
        '200':
          description: Xác thực thành công, 2FA đã được kích hoạt
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Kích hoạt 2FA thành công!"
                      data:
                        type: object
                        properties:
                          enabled:
                            type: boolean
                            example: true
                          method:
                            type: string
                            example: "totp"
                          enabledAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          backupCodesRemaining:
                            type: integer
                            example: 3
        '400':
          description: Mã xác thực không đúng
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "Mã xác thực không đúng"
                      code:
                        example: "INVALID_2FA_CODE"

  /auth/2fa/verify:
    post:
      tags: [Authentication]
      summary: Xác thực 2FA khi đăng nhập
      description: Xác thực mã 2FA trong quá trình đăng nhập
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [loginToken, code]
              properties:
                loginToken:
                  type: string
                  example: "login_token_789"
                  description: "Token từ bước đăng nhập"
                code:
                  type: string
                  example: "123456"
                  description: "Mã 6 số từ authenticator app hoặc SMS"
                isBackupCode:
                  type: boolean
                  default: false
                  example: false
                  description: "Có phải mã backup không"
                rememberDevice:
                  type: boolean
                  default: false
                  example: false
                  description: "Nhớ thiết bị này trong 30 ngày"
      responses:
        '200':
          description: Xác thực 2FA thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đăng nhập thành công!"
                      data:
                        type: object
                        properties:
                          accessToken:
                            type: string
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                          refreshToken:
                            type: string
                            example: "refresh_token_here"
                          user:
                            $ref: '#/components/schemas/User'
                          expiresIn:
                            type: integer
                            example: 3600
                          deviceTrusted:
                            type: boolean
                            example: false
                          backupCodeUsed:
                            type: boolean
                            example: false
                          backupCodesRemaining:
                            type: integer
                            example: 2

  # User Management
  /users/profile:
    get:
      tags: [Users]
      summary: Lấy thông tin profile
      description: Lấy thông tin chi tiết của người dùng hiện tại
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Thông tin profile
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy thông tin profile thành công"
                      data:
                        $ref: '#/components/schemas/UserProfile'
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_profile_123"
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags: [Users]
      summary: Cập nhật profile
      description: Cập nhật thông tin cá nhân của người dùng
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                fullName:
                  type: string
                  example: "Nguyễn Văn A"
                phone:
                  type: string
                  example: "**********"
                avatar:
                  type: string
                  format: uri
                  example: "https://example.com/avatar.jpg"
                bio:
                  type: string
                  example: "Digital marketer"
                company:
                  type: string
                  example: "ABC Company"
                industry:
                  type: string
                  example: "Công nghệ thông tin"
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Cập nhật profile thành công!"
                  data:
                    $ref: '#/components/schemas/UserProfile'

  /users/change-password:
    post:
      tags: [Users]
      summary: Đổi mật khẩu
      description: Thay đổi mật khẩu của người dùng
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [currentPassword, newPassword]
              properties:
                currentPassword:
                  type: string
                  example: "oldpassword123"
                newPassword:
                  type: string
                  minLength: 6
                  example: "newpassword123"
      responses:
        '200':
          description: Đổi mật khẩu thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Đổi mật khẩu thành công!"

  /users/credits:
    get:
      tags: [Credits]
      summary: Lấy thông tin credits
      description: Lấy thông tin credits hiện tại của người dùng
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Thông tin credits
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy thông tin credits thành công"
                      data:
                        type: object
                        properties:
                          currentCredits:
                            type: integer
                            example: 850
                          totalCredits:
                            type: integer
                            example: 1000
                          usageThisMonth:
                            type: integer
                            example: 150
                          plan:
                            type: string
                            example: "Pro Plan"
                          renewalDate:
                            type: string
                            format: date
                            example: "2024-02-15"
                          usageHistory:
                            type: array
                            items:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: date
                                  example: "2024-02-14"
                                used:
                                  type: integer
                                  example: 25
                                action:
                                  type: string
                                  example: "content-generation"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_credits_789"

  # Notifications Management
  /notifications:
    get:
      tags: [Notifications]
      summary: Lấy danh sách thông báo
      description: Lấy danh sách thông báo của người dùng với phân trang và lọc
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
          example: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [read, unread, all]
            default: all
          example: "unread"
          description: "Lọc theo trạng thái đã đọc"
        - name: type
          in: query
          schema:
            type: string
            enum: [system, post_published, post_failed, credit_low, template_sold, workspace_invite, payment_success, payment_failed]
          example: "post_published"
          description: "Lọc theo loại thông báo"
        - name: priority
          in: query
          schema:
            type: string
            enum: [low, medium, high, urgent]
          example: "high"
          description: "Lọc theo mức độ ưu tiên"
      responses:
        '200':
          description: Danh sách thông báo
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách thông báo thành công"
                      data:
                        type: object
                        properties:
                          notifications:
                            type: array
                            items:
                              $ref: '#/components/schemas/Notification'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
                          summary:
                            type: object
                            properties:
                              totalNotifications:
                                type: integer
                                example: 25
                              unreadCount:
                                type: integer
                                example: 8
                              readCount:
                                type: integer
                                example: 17
                              byType:
                                type: object
                                properties:
                                  system:
                                    type: integer
                                    example: 3
                                  post_published:
                                    type: integer
                                    example: 12
                                  post_failed:
                                    type: integer
                                    example: 2
                                  credit_low:
                                    type: integer
                                    example: 1
                                  template_sold:
                                    type: integer
                                    example: 5
                                  workspace_invite:
                                    type: integer
                                    example: 2

  /notifications/{notificationId}:
    get:
      tags: [Notifications]
      summary: Lấy chi tiết thông báo
      description: Lấy thông tin chi tiết của một thông báo cụ thể
      security:
        - bearerAuth: []
      parameters:
        - name: notificationId
          in: path
          required: true
          schema:
            type: string
          example: "notif_123456"
      responses:
        '200':
          description: Chi tiết thông báo
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy chi tiết thông báo thành công"
                      data:
                        $ref: '#/components/schemas/NotificationDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    patch:
      tags: [Notifications]
      summary: Cập nhật trạng thái thông báo
      description: Đánh dấu thông báo là đã đọc/chưa đọc hoặc cập nhật trạng thái khác
      security:
        - bearerAuth: []
      parameters:
        - name: notificationId
          in: path
          required: true
          schema:
            type: string
          example: "notif_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                isRead:
                  type: boolean
                  example: true
                  description: "Đánh dấu đã đọc/chưa đọc"
                isArchived:
                  type: boolean
                  example: false
                  description: "Lưu trữ thông báo"
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật thông báo thành công!"
                      data:
                        $ref: '#/components/schemas/Notification'

    delete:
      tags: [Notifications]
      summary: Xóa thông báo
      description: Xóa thông báo khỏi danh sách của người dùng
      security:
        - bearerAuth: []
      parameters:
        - name: notificationId
          in: path
          required: true
          schema:
            type: string
          example: "notif_123456"
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Xóa thông báo thành công!"

  /notifications/mark-all-read:
    post:
      tags: [Notifications]
      summary: Đánh dấu tất cả đã đọc
      description: Đánh dấu tất cả thông báo của người dùng là đã đọc
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  enum: [system, post_published, post_failed, credit_low, template_sold, workspace_invite, payment_success, payment_failed]
                  example: "post_published"
                  description: "Chỉ đánh dấu đã đọc cho loại thông báo cụ thể (tùy chọn)"
                olderThan:
                  type: string
                  format: date-time
                  example: "2024-02-15T10:30:00Z"
                  description: "Chỉ đánh dấu các thông báo cũ hơn thời gian này (tùy chọn)"
      responses:
        '200':
          description: Đánh dấu thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đánh dấu tất cả thông báo đã đọc thành công!"
                      data:
                        type: object
                        properties:
                          markedCount:
                            type: integer
                            example: 15
                            description: "Số lượng thông báo được đánh dấu"
                          remainingUnread:
                            type: integer
                            example: 3
                            description: "Số thông báo chưa đọc còn lại"

  /notifications/bulk-actions:
    post:
      tags: [Notifications]
      summary: Thao tác hàng loạt
      description: Thực hiện các thao tác hàng loạt trên nhiều thông báo
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [notificationIds, action]
              properties:
                notificationIds:
                  type: array
                  items:
                    type: string
                  example: ["notif_123", "notif_456", "notif_789"]
                  description: "Danh sách ID thông báo"
                action:
                  type: string
                  enum: [mark_read, mark_unread, archive, delete]
                  example: "mark_read"
                  description: "Hành động thực hiện"
      responses:
        '200':
          description: Thao tác thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Thao tác hàng loạt thành công!"
                      data:
                        type: object
                        properties:
                          processedCount:
                            type: integer
                            example: 3
                            description: "Số thông báo được xử lý"
                          failedCount:
                            type: integer
                            example: 0
                            description: "Số thông báo xử lý thất bại"
                          results:
                            type: array
                            items:
                              type: object
                              properties:
                                notificationId:
                                  type: string
                                  example: "notif_123"
                                success:
                                  type: boolean
                                  example: true
                                error:
                                  type: string
                                  example: null

  /notifications/settings:
    get:
      tags: [Notifications]
      summary: Lấy cài đặt thông báo
      description: Lấy cài đặt thông báo hiện tại của người dùng
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Cài đặt thông báo
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy cài đặt thông báo thành công"
                      data:
                        $ref: '#/components/schemas/NotificationSettings'

    put:
      tags: [Notifications]
      summary: Cập nhật cài đặt thông báo
      description: Cập nhật cài đặt thông báo của người dùng
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                emailNotifications:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                      example: true
                      description: "Bật/tắt thông báo email"
                    postPublished:
                      type: boolean
                      example: true
                      description: "Thông báo khi bài viết được đăng"
                    postFailed:
                      type: boolean
                      example: true
                      description: "Thông báo khi đăng bài thất bại"
                    creditLow:
                      type: boolean
                      example: true
                      description: "Thông báo khi credit thấp"
                    templateSold:
                      type: boolean
                      example: true
                      description: "Thông báo khi template được bán"
                    workspaceInvite:
                      type: boolean
                      example: true
                      description: "Thông báo lời mời workspace"
                    paymentSuccess:
                      type: boolean
                      example: true
                      description: "Thông báo thanh toán thành công"
                    paymentFailed:
                      type: boolean
                      example: true
                      description: "Thông báo thanh toán thất bại"
                    systemUpdates:
                      type: boolean
                      example: false
                      description: "Thông báo cập nhật hệ thống"
                pushNotifications:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                      example: true
                      description: "Bật/tắt push notification"
                    postPublished:
                      type: boolean
                      example: true
                    postFailed:
                      type: boolean
                      example: true
                    creditLow:
                      type: boolean
                      example: true
                    templateSold:
                      type: boolean
                      example: false
                    workspaceInvite:
                      type: boolean
                      example: true
                    paymentSuccess:
                      type: boolean
                      example: false
                    paymentFailed:
                      type: boolean
                      example: true
                    systemUpdates:
                      type: boolean
                      example: false
                inAppNotifications:
                  type: object
                  properties:
                    enabled:
                      type: boolean
                      example: true
                      description: "Bật/tắt thông báo trong app"
                    postPublished:
                      type: boolean
                      example: true
                    postFailed:
                      type: boolean
                      example: true
                    creditLow:
                      type: boolean
                      example: true
                    templateSold:
                      type: boolean
                      example: true
                    workspaceInvite:
                      type: boolean
                      example: true
                    paymentSuccess:
                      type: boolean
                      example: true
                    paymentFailed:
                      type: boolean
                      example: true
                    systemUpdates:
                      type: boolean
                      example: true
                frequency:
                  type: object
                  properties:
                    digest:
                      type: string
                      enum: [never, daily, weekly, monthly]
                      example: "weekly"
                      description: "Tần suất gửi email tổng hợp"
                    quietHours:
                      type: object
                      properties:
                        enabled:
                          type: boolean
                          example: true
                        startTime:
                          type: string
                          example: "22:00"
                          description: "Giờ bắt đầu không gửi thông báo (HH:mm)"
                        endTime:
                          type: string
                          example: "08:00"
                          description: "Giờ kết thúc không gửi thông báo (HH:mm)"
                        timezone:
                          type: string
                          example: "Asia/Ho_Chi_Minh"
                          description: "Múi giờ"
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật cài đặt thông báo thành công!"
                      data:
                        $ref: '#/components/schemas/NotificationSettings'

  # Posts Management
  /posts:
    get:
      tags: [Posts]
      summary: Lấy danh sách bài viết
      description: Lấy danh sách bài viết của người dùng với phân trang và lọc
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          example: 10
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, scheduled, published, failed]
          example: "published"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: "facebook"
        - name: search
          in: query
          schema:
            type: string
          example: "marketing tips"
      responses:
        '200':
          description: Danh sách bài viết
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      posts:
                        type: array
                        items:
                          $ref: '#/components/schemas/Post'
                      pagination:
                        $ref: '#/components/schemas/Pagination'

    post:
      tags: [Posts]
      summary: Tạo bài viết mới
      description: Tạo bài viết mới (manual hoặc từ AI)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [title, content, platforms]
              properties:
                title:
                  type: string
                  example: "10 Tips Marketing Hiệu Quả"
                content:
                  type: string
                  example: "Nội dung bài viết chi tiết..."
                platforms:
                  type: array
                  items:
                    type: string
                    enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                  example: ["facebook", "instagram"]
                images:
                  type: array
                  items:
                    type: string
                    format: uri
                  example: ["https://example.com/image1.jpg"]
                hashtags:
                  type: array
                  items:
                    type: string
                  example: ["#marketing", "#tips"]
                scheduledAt:
                  type: string
                  format: date-time
                  example: "2024-02-15T10:30:00Z"
                type:
                  type: string
                  enum: [manual, ai-generated]
                  default: manual
                  example: "manual"
      responses:
        '201':
          description: Bài viết được tạo thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Bài viết được tạo thành công!"
                  data:
                    $ref: '#/components/schemas/Post'

  /posts/{postId}:
    get:
      tags: [Posts]
      summary: Lấy chi tiết bài viết
      description: Lấy thông tin chi tiết của một bài viết
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123456"
      responses:
        '200':
          description: Chi tiết bài viết
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/PostDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags: [Posts]
      summary: Cập nhật bài viết
      description: Cập nhật thông tin bài viết
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123456"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: "Updated Title"
                content:
                  type: string
                  example: "Updated content..."
                images:
                  type: array
                  items:
                    type: string
                    format: uri
                hashtags:
                  type: array
                  items:
                    type: string
                scheduledAt:
                  type: string
                  format: date-time
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Cập nhật bài viết thành công!"
                  data:
                    $ref: '#/components/schemas/Post'

    delete:
      tags: [Posts]
      summary: Xóa bài viết
      description: Xóa bài viết khỏi hệ thống
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123456"
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Xóa bài viết thành công!"

  /posts/{postId}/publish:
    post:
      tags: [Posts]
      summary: Đăng bài ngay
      description: Đăng bài viết lên các nền tảng đã chọn
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123456"
      responses:
        '200':
          description: Đăng bài thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Đăng bài thành công!"
                  data:
                    type: object
                    properties:
                      publishResults:
                        type: array
                        items:
                          type: object
                          properties:
                            platform:
                              type: string
                              example: "facebook"
                            status:
                              type: string
                              enum: [success, failed]
                              example: "success"
                            platformPostId:
                              type: string
                              example: "fb_post_789"
                            error:
                              type: string
                              example: null

  # AI Content Generation
  /ai/generate-content:
    post:
      tags: [AI Content]
      summary: Tạo nội dung bằng AI
      description: |
        Sử dụng AI để tạo nội dung bài viết dựa trên chủ đề và tham số.

        **Credit Usage**: Tiêu thụ credits theo model được chọn:
        - GPT-4o: 5 credits
        - Claude 3.5 Sonnet: 3 credits
        - Gemini Pro: 2 credits
        - GPT-3.5 Turbo: 1 credit

        Credits sẽ được trừ tự động khi API call thành công.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [topic, platforms]
              properties:
                topic:
                  type: string
                  example: "10 cách tiết kiệm tiền hiệu quả cho gia đình trẻ"
                platforms:
                  type: array
                  items:
                    type: string
                    enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                  example: ["facebook", "instagram"]
                contentType:
                  type: string
                  enum: [educational, promotional, entertaining, news, story]
                  default: educational
                  example: "educational"
                tone:
                  type: string
                  enum: [friendly, professional, casual, formal, humorous]
                  default: friendly
                  example: "friendly"
                length:
                  type: string
                  enum: [short, medium, long]
                  default: medium
                  example: "medium"
                language:
                  type: string
                  enum: [vi, en]
                  default: vi
                  example: "vi"
                includeEmojis:
                  type: boolean
                  default: true
                  example: true
                includeHashtags:
                  type: boolean
                  default: true
                  example: true
                templateId:
                  type: string
                  example: "template_123"
                model:
                  type: string
                  enum: [gpt-4o, claude-3.5-sonnet, gemini-pro, gpt-3.5-turbo]
                  default: gpt-3.5-turbo
                  example: "gpt-4o"
                  description: "Model AI sử dụng (ảnh hưởng đến credit cost)"
      responses:
        '200':
          description: Nội dung được tạo thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tạo nội dung thành công!"
                      data:
                        type: object
                        properties:
                          content:
                            type: string
                            example: "🏠 10 Cách Tiết Kiệm Tiền Hiệu Quả Cho Gia Đình Trẻ..."
                          hashtags:
                            type: array
                            items:
                              type: string
                            example: ["#tiếtkiệm", "#giađình", "#tàichính"]
                          suggestedImages:
                            type: array
                            items:
                              type: string
                            example: ["family savings", "money tips"]
                          creditsUsed:
                            type: integer
                            example: 5
                          generationTime:
                            type: number
                            format: float
                            example: 8.5
                            description: "Thời gian tạo nội dung (giây)"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_generate_789"
                          aiModel:
                            type: string
                            example: "gpt-4-turbo"
        '402':
          $ref: '#/components/responses/InsufficientCredits'

  /ai/generate-image:
    post:
      tags: [AI Content]
      summary: Tạo hình ảnh bằng AI
      description: |
        Sử dụng AI để tạo hình ảnh dựa trên mô tả.

        **Credit Usage**: Tiêu thụ credits theo chất lượng và kích thước:
        - Standard (1024x1024): 3 credits
        - High Quality (1024x1024): 5 credits
        - Standard (1792x1024): 5 credits
        - High Quality (1792x1024): 8 credits
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [prompt]
              properties:
                prompt:
                  type: string
                  example: "Một gia đình hạnh phúc đang tiết kiệm tiền"
                style:
                  type: string
                  enum: [realistic, cartoon, illustration, minimalist]
                  default: realistic
                  example: "realistic"
                size:
                  type: string
                  enum: [square, landscape, portrait]
                  default: square
                  example: "square"
                quality:
                  type: string
                  enum: [standard, high]
                  default: standard
                  example: "standard"
      responses:
        '200':
          description: Hình ảnh được tạo thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      imageUrl:
                        type: string
                        format: uri
                        example: "https://cdn.socialai.com/ai-images/img_123.jpg"
                      thumbnailUrl:
                        type: string
                        format: uri
                        example: "https://cdn.socialai.com/ai-images/thumb_123.jpg"
                      prompt:
                        type: string
                        example: "Một gia đình hạnh phúc đang tiết kiệm tiền"
                      creditsUsed:
                        type: integer
                        example: 10

  /ai/improve-text:
    post:
      tags: [AI Content]
      summary: Cải thiện đoạn văn
      description: |
        Sử dụng AI để cải thiện, chỉnh sửa và tối ưu hóa đoạn văn bản đã có.

        **Credit Usage**: 1-3 credits tùy theo độ dài văn bản:
        - Văn bản ngắn (<200 ký tự): 1 credit
        - Văn bản trung bình (200-500 ký tự): 2 credits
        - Văn bản dài (>500 ký tự): 3 credits
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [text, improvementType]
              properties:
                text:
                  type: string
                  example: "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền. Đây là một chủ đề rất quan trọng."
                  description: "Đoạn văn bản cần cải thiện"
                improvementType:
                  type: string
                  enum: [grammar, style, engagement, clarity, tone, length]
                  example: "engagement"
                  description: "Loại cải thiện cần thực hiện"
                targetTone:
                  type: string
                  enum: [friendly, professional, casual, formal, humorous, persuasive]
                  default: friendly
                  example: "friendly"
                  description: "Tone mong muốn cho văn bản"
                targetLength:
                  type: string
                  enum: [shorter, longer, same]
                  default: same
                  example: "longer"
                  description: "Độ dài mong muốn"
                platform:
                  type: string
                  enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                  example: "facebook"
                  description: "Nền tảng đích để tối ưu hóa"
                context:
                  type: string
                  example: "Bài viết về tài chính cá nhân cho gia đình trẻ"
                  description: "Ngữ cảnh hoặc chủ đề của bài viết"
                preserveFormatting:
                  type: boolean
                  default: true
                  example: true
                  description: "Giữ nguyên định dạng gốc (emoji, hashtag, line breaks)"
      responses:
        '200':
          description: Cải thiện văn bản thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cải thiện văn bản thành công!"
                      data:
                        type: object
                        properties:
                          originalText:
                            type: string
                            example: "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền. Đây là một chủ đề rất quan trọng."
                          improvedText:
                            type: string
                            example: "🏠 Hôm nay mình muốn chia sẻ với các bạn những bí quyết tiết kiệm tiền siêu hiệu quả! 💰 Đây không chỉ là chủ đề quan trọng mà còn là kỹ năng sống thiết yếu mà ai cũng nên biết. Cùng mình khám phá nhé! ✨"
                          improvements:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  example: "engagement"
                                description:
                                  type: string
                                  example: "Thêm emoji và call-to-action để tăng tương tác"
                                before:
                                  type: string
                                  example: "Hôm nay tôi muốn chia sẻ"
                                after:
                                  type: string
                                  example: "🏠 Hôm nay mình muốn chia sẻ với các bạn"
                                impact:
                                  type: string
                                  enum: [low, medium, high]
                                  example: "high"
                          suggestions:
                            type: array
                            items:
                              type: string
                            example: [
                              "Thêm hashtag #tiếtkiệm #tàichính để tăng reach",
                              "Kết thúc bằng câu hỏi để khuyến khích comment",
                              "Chia nhỏ thành đoạn ngắn để dễ đọc hơn"
                            ]
                          metrics:
                            type: object
                            properties:
                              readabilityScore:
                                type: number
                                format: float
                                example: 8.5
                              engagementPotential:
                                type: number
                                format: float
                                example: 7.8
                              sentimentScore:
                                type: number
                                format: float
                                example: 0.85
                          creditsUsed:
                            type: integer
                            example: 3
                          confidence:
                            type: number
                            format: float
                            example: 0.92
                            description: "Độ tin cậy của việc cải thiện (0-1)"
                          processingTime:
                            type: number
                            format: float
                            example: 3.2
                            description: "Thời gian xử lý (giây)"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_improve_456"
                          aiModel:
                            type: string
                            example: "gpt-4-turbo"
        '400':
          description: Dữ liệu đầu vào không hợp lệ
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "Văn bản quá ngắn để cải thiện"
                      code:
                        example: "TEXT_TOO_SHORT"
                      errors:
                        type: array
                        items:
                          type: object
                          properties:
                            field:
                              type: string
                              example: "text"
                            code:
                              type: string
                              example: "MIN_LENGTH"
                            message:
                              type: string
                              example: "Văn bản phải có ít nhất 10 ký tự"
        '402':
          $ref: '#/components/responses/InsufficientCredits'

  /ai/stream:
    get:
      tags: [AI Content]
      summary: Kết nối WebSocket cho AI streaming
      description: |
        Kết nối WebSocket để nhận real-time responses từ AI agent

        ## WebSocket Events:

        ### Client → Server:
        - `generate-content`: Tạo nội dung
        - `improve-text`: Cải thiện văn bản
        - `generate-image`: Tạo hình ảnh
        - `subscribe-agent`: Subscribe agent messages

        ### Server → Client:
        - `agent-message`: Tin nhắn từ AI agent
        - `content-chunk`: Chunk nội dung đang được tạo
        - `generation-complete`: Hoàn thành tạo nội dung
        - `error`: Lỗi xảy ra
        - `credits-updated`: Cập nhật credits

        ## Connection URL:
        ```
        wss://api.socialai.com/v1/ai/stream?token={jwt_token}
        ```
      security:
        - bearerAuth: []
      parameters:
        - name: token
          in: query
          required: true
          schema:
            type: string
          description: JWT token để xác thực WebSocket connection
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        '101':
          description: WebSocket connection established
          headers:
            Upgrade:
              schema:
                type: string
                example: websocket
            Connection:
              schema:
                type: string
                example: Upgrade
            Sec-WebSocket-Accept:
              schema:
                type: string
        '401':
          description: Token không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '426':
          description: Upgrade Required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                success: false
                message: "WebSocket upgrade required"
                code: "UPGRADE_REQUIRED"

  # Templates Management
  /templates:
    get:
      tags: [Templates]
      summary: Lấy danh sách template
      description: Lấy danh sách template của người dùng và public templates
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [my, public, marketplace]
            default: my
          example: "my"
        - name: category
          in: query
          schema:
            type: string
          example: "marketing"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: "facebook"
      responses:
        '200':
          description: Danh sách template
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Template'

    post:
      tags: [Templates]
      summary: Tạo template mới
      description: Tạo template nội dung mới
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name, content, category]
              properties:
                name:
                  type: string
                  example: "Template Marketing Tips"
                description:
                  type: string
                  example: "Template cho các bài viết marketing tips"
                content:
                  type: string
                  example: "🎯 {topic}\n\n{content}\n\n#marketing #tips"
                category:
                  type: string
                  example: "marketing"
                platforms:
                  type: array
                  items:
                    type: string
                    enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                  example: ["facebook", "instagram"]
                isPublic:
                  type: boolean
                  default: false
                  example: false
                prompt:
                  type: string
                  example: "Tạo nội dung marketing tips hữu ích"
                trainingSamples:
                  type: array
                  items:
                    type: object
                    properties:
                      description:
                        type: string
                        example: "Tips tiết kiệm"
                      content:
                        type: string
                        example: "10 cách tiết kiệm hiệu quả..."
                ragFiles:
                  type: array
                  items:
                    type: string
                  example: ["rag_file_123", "rag_file_456"]
                  description: "Danh sách ID file RAG để AI tham khảo"
                creditCost:
                  type: integer
                  minimum: 0
                  maximum: 50
                  default: 0
                  example: 5
                  description: "Giá credit để sử dụng template (0 = miễn phí)"
                enableMonetization:
                  type: boolean
                  default: false
                  example: false
                  description: "Bật kiếm tiền từ template (cần kiểm duyệt)"
      responses:
        '201':
          description: Template được tạo thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Template được tạo thành công!"
                  data:
                    $ref: '#/components/schemas/Template'

  /templates/{templateId}:
    get:
      tags: [Templates]
      summary: Lấy chi tiết template
      security:
        - bearerAuth: []
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: string
          example: "template_123"
      responses:
        '200':
          description: Chi tiết template
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/TemplateDetail'

    put:
      tags: [Templates]
      summary: Cập nhật template
      security:
        - bearerAuth: []
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: string
          example: "template_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                content:
                  type: string
                category:
                  type: string
                platforms:
                  type: array
                  items:
                    type: string
                isPublic:
                  type: boolean
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Cập nhật template thành công!"

    delete:
      tags: [Templates]
      summary: Xóa template
      security:
        - bearerAuth: []
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: string
          example: "template_123"
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Xóa template thành công!"

  /templates/marketplace:
    get:
      tags: [Templates]
      summary: Lấy template marketplace
      description: Lấy danh sách template từ marketplace
      security:
        - bearerAuth: []
      parameters:
        - name: category
          in: query
          schema:
            type: string
          example: "marketing"
        - name: sort
          in: query
          schema:
            type: string
            enum: [popular, newest, rating]
            default: popular
          example: "popular"
        - name: free
          in: query
          schema:
            type: boolean
          example: true
      responses:
        '200':
          description: Danh sách template marketplace
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MarketplaceTemplate'

  /templates/{templateId}/save:
    post:
      tags: [Templates]
      summary: Lưu template từ marketplace
      description: Lưu template từ marketplace vào thư viện cá nhân
      security:
        - bearerAuth: []
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: string
          example: "template_123"
      responses:
        '200':
          description: Lưu template thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Đã lưu template vào thư viện!"

  # Template Revenue Management
  /templates/{templateId}/revenue:
    get:
      tags: [TemplateRevenue]
      summary: Doanh thu chi tiết của template
      description: Lấy thông tin doanh thu chi tiết của một template cụ thể
      security:
        - bearerAuth: []
      parameters:
        - name: templateId
          in: path
          required: true
          schema:
            type: string
          example: "template_123"
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y, all]
            default: 30d
          example: "30d"
          description: "Khoảng thời gian thống kê"
        - name: groupBy
          in: query
          schema:
            type: string
            enum: [day, week, month]
            default: day
          example: "day"
          description: "Nhóm dữ liệu theo"
      responses:
        '200':
          description: Thông tin doanh thu template
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy doanh thu template thành công"
                      data:
                        $ref: '#/components/schemas/TemplateRevenueDetail'

  /users/me/templates/revenue:
    get:
      tags: [TemplateRevenue]
      summary: Tổng quan doanh thu của tác giả
      description: Lấy tổng quan doanh thu từ tất cả template của tác giả
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y, all]
            default: 30d
          example: "30d"
          description: "Khoảng thời gian thống kê"
        - name: status
          in: query
          schema:
            type: string
            enum: [all, earning, not_earning]
            default: all
          example: "earning"
          description: "Lọc template theo trạng thái kiếm tiền"
      responses:
        '200':
          description: Tổng quan doanh thu tác giả
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy tổng quan doanh thu thành công"
                      data:
                        $ref: '#/components/schemas/AuthorRevenueSummary'

  /users/me/templates/revenue/withdraw:
    post:
      tags: [TemplateRevenue]
      summary: Yêu cầu rút tiền
      description: Tạo yêu cầu rút tiền từ doanh thu template
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [amount, method]
              properties:
                amount:
                  type: integer
                  minimum: 50000
                  example: 500000
                  description: "Số tiền rút (VND, tối thiểu 50,000)"
                method:
                  type: string
                  enum: [bank_transfer, momo, zalopay]
                  example: "bank_transfer"
                  description: "Phương thức rút tiền"
                bankInfo:
                  type: object
                  properties:
                    bankName:
                      type: string
                      example: "Vietcombank"
                    accountNumber:
                      type: string
                      example: "**********"
                    accountName:
                      type: string
                      example: "Nguyen Van A"
                    branch:
                      type: string
                      example: "Chi nhánh Hà Nội"
                  description: "Thông tin ngân hàng (bắt buộc nếu method = bank_transfer)"
                walletInfo:
                  type: object
                  properties:
                    phoneNumber:
                      type: string
                      example: "**********"
                    fullName:
                      type: string
                      example: "Nguyen Van A"
                  description: "Thông tin ví điện tử (bắt buộc nếu method = momo/zalopay)"
                note:
                  type: string
                  maxLength: 500
                  example: "Rút tiền doanh thu tháng 2"
                  description: "Ghi chú cho yêu cầu rút tiền"
      responses:
        '201':
          description: Tạo yêu cầu rút tiền thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tạo yêu cầu rút tiền thành công!"
                      data:
                        type: object
                        properties:
                          withdrawalId:
                            type: string
                            example: "withdrawal_789"
                          amount:
                            type: integer
                            example: 500000
                          method:
                            type: string
                            example: "bank_transfer"
                          status:
                            type: string
                            example: "pending"
                          estimatedProcessTime:
                            type: string
                            example: "1-3 ngày làm việc"
                          createdAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"

  /users/me/templates/revenue/withdrawals:
    get:
      tags: [TemplateRevenue]
      summary: Lịch sử rút tiền
      description: Lấy lịch sử các yêu cầu rút tiền
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [all, pending, processing, completed, failed, cancelled]
            default: all
          example: "completed"
          description: "Lọc theo trạng thái"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Lịch sử rút tiền
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy lịch sử rút tiền thành công"
                      data:
                        type: object
                        properties:
                          withdrawals:
                            type: array
                            items:
                              $ref: '#/components/schemas/WithdrawalRecord'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
                          summary:
                            type: object
                            properties:
                              totalWithdrawn:
                                type: integer
                                example: 2500000
                                description: "Tổng số tiền đã rút"
                              pendingAmount:
                                type: integer
                                example: 500000
                                description: "Số tiền đang chờ xử lý"
                              availableBalance:
                                type: integer
                                example: 1200000
                                description: "Số dư có thể rút"

  # Assets Management
  /assets/images:
    get:
      tags: [Assets]
      summary: Lấy danh sách hình ảnh
      description: Lấy danh sách hình ảnh của người dùng với filtering và search
      security:
        - bearerAuth: []
      parameters:
        - name: folderId
          in: query
          schema:
            type: string
          example: "folder_123"
          description: "ID folder chứa ảnh"
        - name: type
          in: query
          schema:
            type: string
            enum: [uploaded, ai-generated, stock]
          example: "uploaded"
          description: "Loại hình ảnh"
        - name: search
          in: query
          schema:
            type: string
          example: "marketing banner"
          description: "Tìm kiếm theo tên hoặc tag"
        - name: tags
          in: query
          schema:
            type: array
            items:
              type: string
          example: ["marketing", "banner"]
          description: "Lọc theo tags"
        - name: format
          in: query
          schema:
            type: string
            enum: [jpg, png, gif, webp, svg]
          example: "png"
          description: "Lọc theo định dạng file"
        - name: size
          in: query
          schema:
            type: string
            enum: [small, medium, large]
          example: "medium"
          description: "Lọc theo kích thước"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
        - name: sortBy
          in: query
          schema:
            type: string
            enum: [name, created_at, size, usage_count]
            default: created_at
          example: "created_at"
        - name: sortOrder
          in: query
          schema:
            type: string
            enum: [asc, desc]
            default: desc
          example: "desc"
      responses:
        '200':
          description: Danh sách hình ảnh
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách hình ảnh thành công"
                      data:
                        type: object
                        properties:
                          images:
                            type: array
                            items:
                              $ref: '#/components/schemas/Image'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
                          filters:
                            type: object
                            properties:
                              totalByType:
                                type: object
                                properties:
                                  uploaded:
                                    type: integer
                                    example: 45
                                  ai-generated:
                                    type: integer
                                    example: 23
                                  stock:
                                    type: integer
                                    example: 12
                              totalByFormat:
                                type: object
                                properties:
                                  jpg:
                                    type: integer
                                    example: 30
                                  png:
                                    type: integer
                                    example: 25
                                  gif:
                                    type: integer
                                    example: 5
                              popularTags:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    tag:
                                      type: string
                                      example: "marketing"
                                    count:
                                      type: integer
                                      example: 15
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_images_123"

    post:
      tags: [Assets]
      summary: Upload hình ảnh
      description: Upload một hoặc nhiều hình ảnh mới
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required: [files]
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: "File hình ảnh (JPG, PNG, GIF, WebP, SVG) - tối đa 10 files"
                folderId:
                  type: string
                  example: "folder_123"
                  description: "ID folder để lưu ảnh"
                tags:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "banner", "social-media"]
                  description: "Tags cho ảnh"
                autoOptimize:
                  type: boolean
                  default: true
                  example: true
                  description: "Tự động tối ưu hóa ảnh"
                generateThumbnails:
                  type: boolean
                  default: true
                  example: true
                  description: "Tự động tạo thumbnails"
                extractMetadata:
                  type: boolean
                  default: true
                  example: true
                  description: "Trích xuất metadata"
      responses:
        '201':
          description: Upload thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Upload 3 hình ảnh thành công!"
                      data:
                        type: object
                        properties:
                          uploadedImages:
                            type: array
                            items:
                              $ref: '#/components/schemas/Image'
                          failedUploads:
                            type: array
                            items:
                              type: object
                              properties:
                                filename:
                                  type: string
                                  example: "invalid-image.txt"
                                error:
                                  type: string
                                  example: "Định dạng file không được hỗ trợ"
                          summary:
                            type: object
                            properties:
                              totalFiles:
                                type: integer
                                example: 3
                              successCount:
                                type: integer
                                example: 3
                              failedCount:
                                type: integer
                                example: 0
                              totalSize:
                                type: integer
                                example: 2048000
                                description: "Tổng kích thước (bytes)"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_upload_456"
                          processingTime:
                            type: number
                            format: float
                            example: 3.2

  /assets/images/folders:
    get:
      tags: [Assets]
      summary: Lấy danh sách folder hình ảnh
      description: Lấy cấu trúc thư mục hình ảnh của người dùng
      security:
        - bearerAuth: []
      parameters:
        - name: parentId
          in: query
          schema:
            type: string
          example: "folder_parent_123"
          description: "ID folder cha (để lấy subfolder)"
        - name: includeStats
          in: query
          schema:
            type: boolean
            default: true
          example: true
          description: "Bao gồm thống kê số lượng ảnh"
      responses:
        '200':
          description: Danh sách folder
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách folder thành công"
                      data:
                        type: object
                        properties:
                          folders:
                            type: array
                            items:
                              $ref: '#/components/schemas/AssetFolder'
                          breadcrumb:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: string
                                  example: "folder_123"
                                name:
                                  type: string
                                  example: "Marketing"
                                path:
                                  type: string
                                  example: "/marketing"

    post:
      tags: [Assets]
      summary: Tạo folder hình ảnh
      description: Tạo folder mới để tổ chức hình ảnh
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name]
              properties:
                name:
                  type: string
                  example: "Marketing Images"
                  description: "Tên folder"
                description:
                  type: string
                  example: "Folder chứa ảnh marketing campaigns"
                  description: "Mô tả folder"
                parentId:
                  type: string
                  example: "folder_parent_123"
                  description: "ID folder cha (để tạo subfolder)"
                color:
                  type: string
                  example: "#3b82f6"
                  description: "Màu sắc folder"
                isPrivate:
                  type: boolean
                  default: false
                  example: false
                  description: "Folder riêng tư"
      responses:
        '201':
          description: Tạo folder thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tạo folder thành công!"
                      data:
                        $ref: '#/components/schemas/AssetFolder'

  /assets/images/{imageId}:
    get:
      tags: [Assets]
      summary: Lấy chi tiết hình ảnh
      description: Lấy thông tin chi tiết của một hình ảnh
      security:
        - bearerAuth: []
      parameters:
        - name: imageId
          in: path
          required: true
          schema:
            type: string
          example: "image_123"
      responses:
        '200':
          description: Chi tiết hình ảnh
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy chi tiết hình ảnh thành công"
                      data:
                        $ref: '#/components/schemas/ImageDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags: [Assets]
      summary: Cập nhật thông tin hình ảnh
      description: Cập nhật metadata, tags, folder của hình ảnh
      security:
        - bearerAuth: []
      parameters:
        - name: imageId
          in: path
          required: true
          schema:
            type: string
          example: "image_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: "Updated Marketing Banner"
                description:
                  type: string
                  example: "Banner cho campaign Q1 2024"
                tags:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "banner", "q1-2024"]
                folderId:
                  type: string
                  example: "folder_456"
                altText:
                  type: string
                  example: "Marketing banner với logo công ty"
                isPublic:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật hình ảnh thành công!"
                      data:
                        $ref: '#/components/schemas/Image'

    delete:
      tags: [Assets]
      summary: Xóa hình ảnh
      description: Xóa hình ảnh khỏi hệ thống
      security:
        - bearerAuth: []
      parameters:
        - name: imageId
          in: path
          required: true
          schema:
            type: string
          example: "image_123"
        - name: force
          in: query
          schema:
            type: boolean
            default: false
          example: false
          description: "Xóa vĩnh viễn (không thể khôi phục)"
        - name: reason
          in: query
          schema:
            type: string
          example: "Không còn sử dụng"
          description: "Lý do xóa"
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Xóa hình ảnh thành công!"
                      data:
                        type: object
                        properties:
                          imageId:
                            type: string
                            example: "image_123"
                          deletedAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          isRecoverable:
                            type: boolean
                            example: true
                            description: "Có thể khôi phục trong 30 ngày"

  # File Management for RAG Documents
  /assets/sources:
    get:
      tags: [Assets]
      summary: Lấy danh sách file RAG
      description: Lấy danh sách documents được sử dụng cho RAG (Retrieval-Augmented Generation)
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [document, knowledge-base, training-data, reference]
            default: document
          example: "document"
          description: "Loại file RAG"
        - name: format
          in: query
          schema:
            type: string
            enum: [pdf, docx, txt, md, csv, json]
          example: "pdf"
          description: "Định dạng file"
        - name: status
          in: query
          schema:
            type: string
            enum: [processing, indexed, failed, active, inactive]
            default: active
          example: "active"
          description: "Trạng thái xử lý"
        - name: search
          in: query
          schema:
            type: string
          example: "marketing strategy"
          description: "Tìm kiếm trong nội dung file"
        - name: tags
          in: query
          schema:
            type: array
            items:
              type: string
          example: ["marketing", "strategy", "2024"]
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Danh sách file RAG
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy danh sách file RAG thành công"
                      data:
                        type: object
                        properties:
                          files:
                            type: array
                            items:
                              $ref: '#/components/schemas/RAGDocument'
                          pagination:
                            $ref: '#/components/schemas/Pagination'
                          summary:
                            type: object
                            properties:
                              totalFiles:
                                type: integer
                                example: 25
                              totalSize:
                                type: integer
                                example: 52428800
                                description: "Tổng kích thước (bytes)"
                              byStatus:
                                type: object
                                properties:
                                  active:
                                    type: integer
                                    example: 20
                                  processing:
                                    type: integer
                                    example: 3
                                  failed:
                                    type: integer
                                    example: 2
                              byFormat:
                                type: object
                                properties:
                                  pdf:
                                    type: integer
                                    example: 15
                                  docx:
                                    type: integer
                                    example: 8
                                  txt:
                                    type: integer
                                    example: 2

    post:
      tags: [Assets]
      summary: Upload file RAG
      description: Upload documents để sử dụng cho RAG trong template creation và AI content generation
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required: [files]
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: "File documents (PDF, DOCX, TXT, MD, CSV, JSON) - tối đa 5 files"
                type:
                  type: string
                  enum: [document, knowledge-base, training-data, reference]
                  default: document
                  example: "document"
                  description: "Loại file RAG"
                title:
                  type: string
                  example: "Marketing Strategy 2024"
                  description: "Tiêu đề cho file"
                description:
                  type: string
                  example: "Tài liệu chiến lược marketing năm 2024"
                  description: "Mô tả nội dung file"
                tags:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "strategy", "2024", "planning"]
                  description: "Tags để phân loại"
                category:
                  type: string
                  example: "marketing"
                  description: "Danh mục file"
                language:
                  type: string
                  enum: [vi, en, auto]
                  default: auto
                  example: "vi"
                  description: "Ngôn ngữ nội dung"
                isPublic:
                  type: boolean
                  default: false
                  example: false
                  description: "Chia sẻ với team"
                autoIndex:
                  type: boolean
                  default: true
                  example: true
                  description: "Tự động index cho RAG"
                extractMetadata:
                  type: boolean
                  default: true
                  example: true
                  description: "Trích xuất metadata"
                chunkSize:
                  type: integer
                  minimum: 100
                  maximum: 2000
                  default: 500
                  example: 500
                  description: "Kích thước chunk cho RAG (characters)"
                overlap:
                  type: integer
                  minimum: 0
                  maximum: 200
                  default: 50
                  example: 50
                  description: "Overlap giữa các chunks (characters)"
      responses:
        '201':
          description: Upload file RAG thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Upload 3 file RAG thành công!"
                      data:
                        type: object
                        properties:
                          uploadedFiles:
                            type: array
                            items:
                              $ref: '#/components/schemas/RAGDocument'
                          failedUploads:
                            type: array
                            items:
                              type: object
                              properties:
                                filename:
                                  type: string
                                  example: "invalid-doc.exe"
                                error:
                                  type: string
                                  example: "Định dạng file không được hỗ trợ"
                          processing:
                            type: object
                            properties:
                              totalFiles:
                                type: integer
                                example: 3
                              estimatedTime:
                                type: integer
                                example: 120
                                description: "Thời gian ước tính xử lý (giây)"
                              jobIds:
                                type: array
                                items:
                                  type: string
                                example: ["job_123", "job_456", "job_789"]
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_rag_upload_789"

  /assets/sources/{fileId}:
    get:
      tags: [Assets]
      summary: Lấy chi tiết file RAG
      description: Lấy thông tin chi tiết và trạng thái xử lý của file RAG
      security:
        - bearerAuth: []
      parameters:
        - name: fileId
          in: path
          required: true
          schema:
            type: string
          example: "rag_file_123"
      responses:
        '200':
          description: Chi tiết file RAG
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy chi tiết file RAG thành công"
                      data:
                        $ref: '#/components/schemas/RAGDocumentDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags: [Assets]
      summary: Cập nhật file RAG
      description: Cập nhật metadata và cài đặt của file RAG
      security:
        - bearerAuth: []
      parameters:
        - name: fileId
          in: path
          required: true
          schema:
            type: string
          example: "rag_file_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: "Updated Marketing Strategy 2024"
                description:
                  type: string
                  example: "Tài liệu chiến lược marketing cập nhật"
                tags:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "strategy", "2024", "updated"]
                category:
                  type: string
                  example: "marketing"
                isPublic:
                  type: boolean
                  example: false
                status:
                  type: string
                  enum: [active, inactive]
                  example: "active"
                chunkSize:
                  type: integer
                  minimum: 100
                  maximum: 2000
                  example: 600
                overlap:
                  type: integer
                  minimum: 0
                  maximum: 200
                  example: 75
                reindex:
                  type: boolean
                  default: false
                  example: false
                  description: "Reindex lại file với cài đặt mới"
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật file RAG thành công!"
                      data:
                        $ref: '#/components/schemas/RAGDocument'

    delete:
      tags: [Assets]
      summary: Xóa file RAG
      description: Xóa file RAG và tất cả dữ liệu index liên quan
      security:
        - bearerAuth: []
      parameters:
        - name: fileId
          in: path
          required: true
          schema:
            type: string
          example: "rag_file_123"
        - name: deleteIndex
          in: query
          schema:
            type: boolean
            default: true
          example: true
          description: "Xóa cả dữ liệu index"
        - name: reason
          in: query
          schema:
            type: string
          example: "File không còn cần thiết"
          description: "Lý do xóa"
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Xóa file RAG thành công!"
                      data:
                        type: object
                        properties:
                          fileId:
                            type: string
                            example: "rag_file_123"
                          deletedAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          indexDeleted:
                            type: boolean
                            example: true
                          chunksDeleted:
                            type: integer
                            example: 45
                            description: "Số chunks đã xóa"

  /assets/sources/{fileId}/reindex:
    post:
      tags: [Assets]
      summary: Reindex file RAG
      description: Reindex lại file RAG với cài đặt mới
      security:
        - bearerAuth: []
      parameters:
        - name: fileId
          in: path
          required: true
          schema:
            type: string
          example: "rag_file_123"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                chunkSize:
                  type: integer
                  minimum: 100
                  maximum: 2000
                  example: 600
                overlap:
                  type: integer
                  minimum: 0
                  maximum: 200
                  example: 75
                force:
                  type: boolean
                  default: false
                  example: false
                  description: "Force reindex ngay cả khi đang xử lý"
      responses:
        '200':
          description: Bắt đầu reindex
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Bắt đầu reindex file RAG!"
                      data:
                        type: object
                        properties:
                          fileId:
                            type: string
                            example: "rag_file_123"
                          jobId:
                            type: string
                            example: "reindex_job_456"
                          estimatedTime:
                            type: integer
                            example: 180
                            description: "Thời gian ước tính (giây)"
                          status:
                            type: string
                            example: "processing"

  /assets/sources/search:
    post:
      tags: [Assets]
      summary: Tìm kiếm trong file RAG
      description: Tìm kiếm semantic trong nội dung các file RAG
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [query]
              properties:
                query:
                  type: string
                  example: "chiến lược marketing digital"
                  description: "Câu truy vấn tìm kiếm"
                fileIds:
                  type: array
                  items:
                    type: string
                  example: ["rag_file_123", "rag_file_456"]
                  description: "Giới hạn tìm kiếm trong các file cụ thể"
                categories:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "strategy"]
                  description: "Lọc theo danh mục"
                limit:
                  type: integer
                  minimum: 1
                  maximum: 50
                  default: 10
                  example: 10
                  description: "Số kết quả trả về"
                threshold:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 1
                  default: 0.7
                  example: 0.8
                  description: "Ngưỡng similarity (0-1)"
                includeMetadata:
                  type: boolean
                  default: true
                  example: true
                  description: "Bao gồm metadata trong kết quả"
      responses:
        '200':
          description: Kết quả tìm kiếm
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tìm kiếm thành công"
                      data:
                        type: object
                        properties:
                          query:
                            type: string
                            example: "chiến lược marketing digital"
                          results:
                            type: array
                            items:
                              type: object
                              properties:
                                fileId:
                                  type: string
                                  example: "rag_file_123"
                                fileName:
                                  type: string
                                  example: "Marketing Strategy 2024.pdf"
                                chunkId:
                                  type: string
                                  example: "chunk_789"
                                content:
                                  type: string
                                  example: "Chiến lược marketing digital tập trung vào..."
                                similarity:
                                  type: number
                                  format: float
                                  example: 0.85
                                metadata:
                                  type: object
                                  properties:
                                    page:
                                      type: integer
                                      example: 5
                                    section:
                                      type: string
                                      example: "Digital Marketing Strategy"
                                    tags:
                                      type: array
                                      items:
                                        type: string
                                      example: ["marketing", "digital"]
                          summary:
                            type: object
                            properties:
                              totalResults:
                                type: integer
                                example: 15
                              searchTime:
                                type: number
                                format: float
                                example: 0.25
                                description: "Thời gian tìm kiếm (giây)"
                              filesSearched:
                                type: integer
                                example: 8
                              chunksSearched:
                                type: integer
                                example: 245

  /posts/schedule:
    get:
      tags: [Posts]
      summary: Lấy lịch đăng bài
      description: Lấy danh sách bài viết đã lên lịch theo calendar view
      security:
        - bearerAuth: []
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-02-01"
          description: "Ngày bắt đầu lấy lịch"
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-02-29"
          description: "Ngày kết thúc lấy lịch"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          description: "Lọc theo nền tảng"
        - name: status
          in: query
          schema:
            type: string
            enum: [scheduled, published, failed]
            default: scheduled
          example: "scheduled"
          description: "Trạng thái bài viết"
        - name: view
          in: query
          schema:
            type: string
            enum: [calendar, list]
            default: calendar
          example: "calendar"
          description: "Kiểu hiển thị"
      responses:
        '200':
          description: Lịch đăng bài
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy lịch đăng bài thành công"
                      data:
                        type: object
                        properties:
                          view:
                            type: string
                            example: "calendar"
                          dateRange:
                            type: object
                            properties:
                              startDate:
                                type: string
                                format: date
                                example: "2024-02-01"
                              endDate:
                                type: string
                                format: date
                                example: "2024-02-29"
                          schedule:
                            type: array
                            items:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: date
                                  example: "2024-02-15"
                                posts:
                                  type: array
                                  items:
                                    allOf:
                                      - $ref: '#/components/schemas/Post'
                                      - type: object
                                        properties:
                                          scheduledTime:
                                            type: string
                                            format: time
                                            example: "09:00"
                                          timeSlot:
                                            type: string
                                            example: "morning"
                          summary:
                            type: object
                            properties:
                              totalScheduled:
                                type: integer
                                example: 25
                              byPlatform:
                                type: object
                                properties:
                                  facebook:
                                    type: integer
                                    example: 10
                                  instagram:
                                    type: integer
                                    example: 8
                                  linkedin:
                                    type: integer
                                    example: 7
                              byStatus:
                                type: object
                                properties:
                                  scheduled:
                                    type: integer
                                    example: 20
                                  published:
                                    type: integer
                                    example: 4
                                  failed:
                                    type: integer
                                    example: 1
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_schedule_123"

  /posts/schedule/bulk:
    post:
      tags: [Posts]
      summary: Lên lịch hàng loạt
      description: Lên lịch đăng nhiều bài viết cùng lúc với các tùy chọn thời gian linh hoạt
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [posts, schedule]
              properties:
                posts:
                  type: array
                  items:
                    type: string
                  example: ["post_123", "post_456", "post_789"]
                  description: "Danh sách ID bài viết cần lên lịch"
                schedule:
                  type: object
                  required: [startDate, times]
                  properties:
                    startDate:
                      type: string
                      format: date
                      example: "2024-02-15"
                      description: "Ngày bắt đầu lên lịch"
                    endDate:
                      type: string
                      format: date
                      example: "2024-02-20"
                      description: "Ngày kết thúc (optional, nếu không có sẽ lên lịch liên tiếp)"
                    times:
                      type: array
                      items:
                        type: string
                        format: time
                      example: ["09:00", "15:00", "20:00"]
                      description: "Các khung giờ đăng trong ngày"
                    platforms:
                      type: array
                      items:
                        type: string
                        enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                      example: ["facebook", "instagram"]
                      description: "Nền tảng đăng bài"
                    distribution:
                      type: string
                      enum: [sequential, random, optimal]
                      default: sequential
                      example: "sequential"
                      description: "Cách phân bổ bài viết"
                    timezone:
                      type: string
                      example: "Asia/Ho_Chi_Minh"
                      description: "Múi giờ"
                    skipWeekends:
                      type: boolean
                      default: false
                      example: false
                      description: "Bỏ qua cuối tuần"
                    skipHolidays:
                      type: boolean
                      default: false
                      example: false
                      description: "Bỏ qua ngày lễ"
      responses:
        '200':
          description: Lên lịch thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Đã lên lịch 6 bài viết thành công!"
                      data:
                        type: object
                        properties:
                          scheduledCount:
                            type: integer
                            example: 6
                          failedCount:
                            type: integer
                            example: 0
                          schedule:
                            type: array
                            items:
                              type: object
                              properties:
                                postId:
                                  type: string
                                  example: "post_123"
                                title:
                                  type: string
                                  example: "10 Tips Marketing Hiệu Quả"
                                scheduledAt:
                                  type: string
                                  format: date-time
                                  example: "2024-02-15T09:00:00Z"
                                platforms:
                                  type: array
                                  items:
                                    type: string
                                  example: ["facebook", "instagram"]
                                status:
                                  type: string
                                  example: "scheduled"
                          summary:
                            type: object
                            properties:
                              dateRange:
                                type: object
                                properties:
                                  startDate:
                                    type: string
                                    format: date
                                    example: "2024-02-15"
                                  endDate:
                                    type: string
                                    format: date
                                    example: "2024-02-17"
                              totalSlots:
                                type: integer
                                example: 9
                              usedSlots:
                                type: integer
                                example: 6
                              nextAvailableSlot:
                                type: string
                                format: date-time
                                example: "2024-02-18T09:00:00Z"
                      meta:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          requestId:
                            type: string
                            example: "req_bulk_schedule_456"
                          processingTime:
                            type: number
                            format: float
                            example: 2.3

  /posts/{postId}/schedule:
    put:
      tags: [Posts]
      summary: Cập nhật lịch đăng bài
      description: Thay đổi thời gian lên lịch của một bài viết cụ thể
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [scheduledAt]
              properties:
                scheduledAt:
                  type: string
                  format: date-time
                  example: "2024-02-16T14:30:00Z"
                  description: "Thời gian mới để đăng bài"
                platforms:
                  type: array
                  items:
                    type: string
                    enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
                  example: ["facebook", "instagram"]
                  description: "Cập nhật nền tảng đăng bài"
                timezone:
                  type: string
                  example: "Asia/Ho_Chi_Minh"
                  description: "Múi giờ"
                reason:
                  type: string
                  example: "Thay đổi theo yêu cầu khách hàng"
                  description: "Lý do thay đổi lịch"
      responses:
        '200':
          description: Cập nhật lịch thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Cập nhật lịch đăng bài thành công!"
                      data:
                        type: object
                        properties:
                          postId:
                            type: string
                            example: "post_123"
                          oldScheduledAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T09:00:00Z"
                          newScheduledAt:
                            type: string
                            format: date-time
                            example: "2024-02-16T14:30:00Z"
                          platforms:
                            type: array
                            items:
                              type: string
                            example: ["facebook", "instagram"]
                          status:
                            type: string
                            example: "rescheduled"
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [Posts]
      summary: Hủy lịch đăng bài
      description: Hủy lịch đăng của một bài viết và chuyển về trạng thái draft
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123"
        - name: reason
          in: query
          schema:
            type: string
          example: "Thay đổi kế hoạch marketing"
          description: "Lý do hủy lịch"
        - name: keepDraft
          in: query
          schema:
            type: boolean
            default: true
          example: true
          description: "Giữ bài viết ở trạng thái draft"
      responses:
        '200':
          description: Hủy lịch thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Hủy lịch đăng bài thành công!"
                      data:
                        type: object
                        properties:
                          postId:
                            type: string
                            example: "post_123"
                          previousStatus:
                            type: string
                            example: "scheduled"
                          newStatus:
                            type: string
                            example: "draft"
                          cancelledAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"

  /posts/schedule/conflicts:
    get:
      tags: [Posts]
      summary: Kiểm tra xung đột lịch
      description: Kiểm tra các xung đột thời gian khi lên lịch bài viết
      security:
        - bearerAuth: []
      parameters:
        - name: startDate
          in: query
          required: true
          schema:
            type: string
            format: date
          example: "2024-02-15"
        - name: endDate
          in: query
          required: true
          schema:
            type: string
            format: date
          example: "2024-02-20"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
        - name: maxPostsPerDay
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 10
            default: 3
          example: 3
        - name: minInterval
          in: query
          schema:
            type: integer
            minimum: 30
            default: 120
          example: 120
          description: "Khoảng cách tối thiểu giữa các bài (phút)"
      responses:
        '200':
          description: Thông tin xung đột lịch
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Kiểm tra xung đột lịch thành công"
                      data:
                        type: object
                        properties:
                          hasConflicts:
                            type: boolean
                            example: true
                          conflicts:
                            type: array
                            items:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: date
                                  example: "2024-02-16"
                                platform:
                                  type: string
                                  example: "facebook"
                                issue:
                                  type: string
                                  enum: [too_many_posts, interval_too_short, platform_limit]
                                  example: "too_many_posts"
                                description:
                                  type: string
                                  example: "Đã có 4 bài viết trong ngày, vượt quá giới hạn 3 bài"
                                existingPosts:
                                  type: array
                                  items:
                                    type: object
                                    properties:
                                      postId:
                                        type: string
                                        example: "post_456"
                                      scheduledAt:
                                        type: string
                                        format: date-time
                                        example: "2024-02-16T09:00:00Z"
                          suggestions:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  enum: [reschedule, change_platform, increase_interval]
                                  example: "reschedule"
                                description:
                                  type: string
                                  example: "Chuyển bài viết sang ngày 2024-02-17"
                                suggestedTime:
                                  type: string
                                  format: date-time
                                  example: "2024-02-17T09:00:00Z"
                          availableSlots:
                            type: array
                            items:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: date
                                  example: "2024-02-17"
                                slots:
                                  type: array
                                  items:
                                    type: string
                                    format: time
                                  example: ["09:00", "15:00", "20:00"]

  /posts/schedule/recommendations:
    get:
      tags: [ScheduleAI]
      summary: Gợi ý khung giờ đăng tối ưu
      description: |
        Phân tích lịch sử engagement để đề xuất 5 khung giờ tốt nhất
        trong khoảng ngày cho nền tảng chỉ định.

        AI sẽ phân tích:
        - Lịch sử engagement của user
        - Thời gian hoạt động của audience
        - Xu hướng engagement theo ngày/giờ
        - Tần suất đăng bài tối ưu
      security:
        - bearerAuth: []
      parameters:
        - name: platform
          in: query
          required: true
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: "facebook"
          description: "Nền tảng cần gợi ý"
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-02-20"
          description: "Ngày bắt đầu (mặc định: hôm nay)"
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-02-27"
          description: "Ngày kết thúc (mặc định: +7 ngày)"
        - name: timezone
          in: query
          schema:
            type: string
          example: "Asia/Ho_Chi_Minh"
          description: "Múi giờ (mặc định: user timezone)"
        - name: contentType
          in: query
          schema:
            type: string
            enum: [educational, promotional, entertainment, news, story]
          example: "educational"
          description: "Loại nội dung để tối ưu hóa"
        - name: maxRecommendations
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 10
            default: 5
          example: 5
          description: "Số lượng gợi ý tối đa"
      responses:
        '200':
          description: Danh sách khuyến nghị thời gian đăng
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Tạo gợi ý thời gian đăng thành công"
                      data:
                        type: object
                        properties:
                          recommendations:
                            type: array
                            items:
                              $ref: '#/components/schemas/ScheduleRecommendation'
                          analysis:
                            type: object
                            properties:
                              totalPostsAnalyzed:
                                type: integer
                                example: 45
                                description: "Số bài viết đã phân tích"
                              avgEngagementRate:
                                type: number
                                format: float
                                example: 7.2
                                description: "Tỷ lệ engagement trung bình"
                              bestDayOfWeek:
                                type: string
                                example: "Tuesday"
                                description: "Ngày trong tuần tốt nhất"
                              bestTimeOfDay:
                                type: string
                                example: "09:00-11:00"
                                description: "Khung giờ tốt nhất trong ngày"
                              audienceActiveHours:
                                type: array
                                items:
                                  type: string
                                example: ["09:00-11:00", "15:00-17:00", "20:00-22:00"]
                                description: "Khung giờ audience hoạt động"
                          confidence:
                            type: number
                            format: float
                            example: 0.85
                            description: "Độ tin cậy của gợi ý (0-1)"
                          lastUpdated:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                            description: "Lần cập nhật phân tích cuối"

  # Analytics
  /analytics/overview:
    get:
      tags: [Analytics]
      summary: Tổng quan analytics
      description: Lấy thống kê tổng quan về hiệu suất bài viết
      security:
        - bearerAuth: []
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y]
            default: 30d
          example: "30d"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
      responses:
        '200':
          description: Thống kê tổng quan
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalPosts:
                        type: integer
                        example: 45
                      totalViews:
                        type: integer
                        example: 12500
                      totalLikes:
                        type: integer
                        example: 890
                      totalComments:
                        type: integer
                        example: 234
                      totalShares:
                        type: integer
                        example: 156
                      avgEngagementRate:
                        type: number
                        format: float
                        example: 7.2
                      topPlatforms:
                        type: array
                        items:
                          type: object
                          properties:
                            platform:
                              type: string
                              example: "facebook"
                            posts:
                              type: integer
                              example: 20
                            engagement:
                              type: number
                              format: float
                              example: 8.5
                      growthRate:
                        type: object
                        properties:
                          views:
                            type: number
                            format: float
                            example: 15.2
                          engagement:
                            type: number
                            format: float
                            example: 8.7

  /analytics/posts/{postId}:
    get:
      tags: [Analytics]
      summary: Analytics chi tiết bài viết
      description: Lấy thống kê chi tiết của một bài viết
      security:
        - bearerAuth: []
      parameters:
        - name: postId
          in: path
          required: true
          schema:
            type: string
          example: "post_123"
      responses:
        '200':
          description: Analytics chi tiết
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      post:
                        $ref: '#/components/schemas/Post'
                      analytics:
                        type: object
                        properties:
                          totalViews:
                            type: integer
                            example: 1250
                          totalLikes:
                            type: integer
                            example: 89
                          totalComments:
                            type: integer
                            example: 23
                          totalShares:
                            type: integer
                            example: 12
                          engagementRate:
                            type: number
                            format: float
                            example: 9.8
                          platformBreakdown:
                            type: array
                            items:
                              type: object
                              properties:
                                platform:
                                  type: string
                                  example: "facebook"
                                views:
                                  type: integer
                                  example: 800
                                likes:
                                  type: integer
                                  example: 65
                                comments:
                                  type: integer
                                  example: 18
                                shares:
                                  type: integer
                                  example: 9
                          timeSeriesData:
                            type: array
                            items:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: date
                                  example: "2024-02-15"
                                views:
                                  type: integer
                                  example: 150
                                engagement:
                                  type: integer
                                  example: 12

  # Integrations - Platform Account Management
  /integrations:
    get:
      tags: [Integrations]
      summary: Lấy danh sách tài khoản platform đã kết nối
      description: |
        Lấy danh sách tất cả tài khoản mạng xã hội đã kết nối của người dùng.
        Hỗ trợ lọc theo platform, workspace, trạng thái và phân trang.
      security:
        - bearerAuth: []
      parameters:
        - name: user_id
          in: query
          required: true
          schema:
            type: string
          example: "user_123"
          description: "ID người dùng"
        - name: workspace_id
          in: query
          schema:
            type: string
          example: "workspace_456"
          description: "ID workspace (optional)"
        - name: platform
          in: query
          schema:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, tiktok]
          example: "facebook"
          description: "Lọc theo platform"
        - name: status
          in: query
          schema:
            type: string
            enum: [active, expired, error, disconnected]
          example: "active"
          description: "Lọc theo trạng thái"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
      responses:
        '200':
          description: Danh sách tài khoản platform
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      accounts:
                        type: array
                        items:
                          $ref: '#/components/schemas/PlatformAccount'
                      total_count:
                        type: integer
                        example: 25
                      page:
                        type: integer
                        example: 1
                      limit:
                        type: integer
                        example: 10

  /integrations/oauth/url:
    post:
      tags: [Integrations]
      summary: Tạo OAuth URL để kết nối platform
      description: |
        Tạo OAuth authorization URL để người dùng có thể kết nối tài khoản platform.
        State parameter sẽ được encode để chứa thông tin user và workspace.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [platform]
              properties:
                platform:
                  type: string
                  enum: [facebook, instagram, linkedin, twitter, youtube, tiktok]
                  example: "facebook"
                  description: "Platform cần kết nối"
                redirect_uri:
                  type: string
                  format: uri
                  example: "http://localhost:8085/oauth/facebook/callback"
                  description: "Callback URL (optional, sẽ dùng default nếu không có)"
                requested_permissions:
                  type: array
                  items:
                    type: string
                  example: ["pages_manage_posts", "pages_read_engagement"]
                  description: "Quyền cần yêu cầu"
                state:
                  type: string
                  example: "eyJ1c2VyX2lkIjoidXNlcl8xMjMiLCJ3b3Jrc3BhY2VfaWQiOiJ3b3Jrc3BhY2VfNDU2In0="
                  description: "State parameter (base64 encoded JSON)"
      responses:
        '200':
          description: OAuth URL được tạo thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      auth_url:
                        type: string
                        format: uri
                        example: "https://www.facebook.com/v23.0/dialog/oauth?client_id=123&redirect_uri=..."
                      state:
                        type: string
                        example: "eyJ1c2VyX2lkIjoidXNlcl8xMjMifQ=="
                      permissions:
                        type: array
                        items:
                          type: string
                        example: ["pages_manage_posts", "pages_read_engagement"]

  /integrations/{id}:
    get:
      tags: [Integrations]
      summary: Lấy thông tin chi tiết tài khoản platform
      description: Lấy thông tin chi tiết và trạng thái của một tài khoản platform cụ thể
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          example: "integration_123"
          description: "ID tài khoản platform"
        - name: user_id
          in: query
          required: true
          schema:
            type: string
          example: "user_123"
          description: "ID người dùng"
      responses:
        '200':
          description: Thông tin tài khoản platform
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/PlatformAccountDetail'

    delete:
      tags: [Integrations]
      summary: Ngắt kết nối tài khoản platform
      description: |
        Ngắt kết nối và xóa tài khoản platform.
        Có thể chọn revoke token trên platform hoặc chỉ xóa local.
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          example: "integration_123"
          description: "ID tài khoản platform"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                revoke_token:
                  type: boolean
                  default: true
                  example: true
                  description: "Có revoke token trên platform không"
      responses:
        '200':
          description: Ngắt kết nối thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Ngắt kết nối tài khoản thành công"

  /integrations/{id}/refresh:
    post:
      tags: [Integrations]
      summary: Làm mới access token
      description: Làm mới access token cho tài khoản platform khi token hết hạn
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          example: "integration_123"
          description: "ID tài khoản platform"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                force_refresh:
                  type: boolean
                  default: false
                  example: false
                  description: "Bắt buộc làm mới ngay cả khi token chưa hết hạn"
      responses:
        '200':
          description: Làm mới token thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      new_expires_at:
                        type: integer
                        example: **********
                        description: "Thời gian hết hạn mới (Unix timestamp)"
                      error_message:
                        type: string
                        example: null
                        description: "Thông báo lỗi nếu có"

  # Publishing - Content Publishing to Platforms
  /publishing/posts:
    post:
      tags: [Integrations]
      summary: Đăng bài lên platform
      description: |
        Đăng nội dung lên platform đã kết nối.
        Hỗ trợ text, hình ảnh, hashtags và các tùy chọn platform-specific.
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [account_id, content]
              properties:
                account_id:
                  type: string
                  example: "integration_123"
                  description: "ID tài khoản platform"
                post_id:
                  type: string
                  example: "post_456"
                  description: "ID bài viết trong hệ thống (optional)"
                content:
                  type: string
                  example: "Nội dung bài viết mới! #marketing #ai"
                  description: "Nội dung bài viết"
                image_urls:
                  type: array
                  items:
                    type: string
                    format: uri
                  example: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
                  description: "Danh sách URL hình ảnh"
                hashtags:
                  type: array
                  items:
                    type: string
                  example: ["marketing", "ai", "socialai"]
                  description: "Danh sách hashtags"
                platform_options:
                  type: object
                  additionalProperties:
                    type: string
                  example:
                    facebook_page_id: "page_123"
                    twitter_thread: "false"
                    linkedin_visibility: "public"
                  description: "Tùy chọn riêng cho từng platform"
                scheduled_at:
                  type: integer
                  example: **********
                  description: "Thời gian đăng bài (Unix timestamp, optional)"
      responses:
        '200':
          description: Đăng bài thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      platform_post_id:
                        type: string
                        example: "fb_post_789"
                        description: "ID bài viết trên platform"
                      platform_url:
                        type: string
                        format: uri
                        example: "https://facebook.com/posts/123456789"
                        description: "URL bài viết trên platform"
                      published_at:
                        type: integer
                        example: **********
                        description: "Thời gian đăng thực tế"
                      error_message:
                        type: string
                        example: null
                        description: "Thông báo lỗi nếu có"

  /publishing/posts/analytics:
    post:
      tags: [Integrations]
      summary: Lấy analytics của bài viết
      description: Lấy số liệu thống kê của bài viết đã đăng trên platform
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [account_id, platform_post_id]
              properties:
                account_id:
                  type: string
                  example: "integration_123"
                  description: "ID tài khoản platform"
                platform_post_id:
                  type: string
                  example: "fb_post_789"
                  description: "ID bài viết trên platform"
                metrics:
                  type: array
                  items:
                    type: string
                  example: ["likes", "comments", "shares", "reach", "impressions"]
                  description: "Các metrics cần lấy (optional, lấy tất cả nếu không có)"
      responses:
        '200':
          description: Analytics bài viết
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      platform_post_id:
                        type: string
                        example: "fb_post_789"
                      platform:
                        type: string
                        example: "facebook"
                      metrics:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          likes: 125
                          comments: 23
                          shares: 8
                          reach: 1250
                          impressions: 2100
                      collected_at:
                        type: integer
                        example: **********
                        description: "Thời gian thu thập dữ liệu"
                      post_published_at:
                        type: integer
                        example: **********
                        description: "Thời gian đăng bài"

  /publishing/accounts/analytics:
    post:
      tags: [Integrations]
      summary: Lấy analytics của tài khoản platform
      description: Lấy số liệu thống kê tổng quan của tài khoản platform trong khoảng thời gian
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [account_id]
              properties:
                account_id:
                  type: string
                  example: "integration_123"
                  description: "ID tài khoản platform"
                date_from:
                  type: string
                  format: date
                  example: "2024-02-01"
                  description: "Ngày bắt đầu (YYYY-MM-DD)"
                date_to:
                  type: string
                  format: date
                  example: "2024-02-15"
                  description: "Ngày kết thúc (YYYY-MM-DD)"
                metrics:
                  type: array
                  items:
                    type: string
                  example: ["followers", "posts", "engagement", "reach"]
                  description: "Các metrics cần lấy"
      responses:
        '200':
          description: Analytics tài khoản
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      account_id:
                        type: string
                        example: "integration_123"
                      platform:
                        type: string
                        example: "facebook"
                      metrics:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          followers: 1250
                          posts: 45
                          engagement: 890
                          reach: 15000
                      time_series:
                        type: object
                        additionalProperties:
                          type: array
                          items:
                            type: integer
                        example:
                          daily_reach: [1200, 1350, 1100, 1450, 1600]
                          daily_engagement: [85, 92, 78, 105, 120]
                        description: "Dữ liệu theo thời gian (optional)"
                      collected_at:
                        type: integer
                        example: **********

  # Platform Information & Limits
  /platform/info:
    get:
      tags: [Integrations]
      summary: Lấy thông tin các platform được hỗ trợ
      description: |
        Lấy danh sách các platform được hỗ trợ, tính năng,
        quyền cần thiết và giới hạn của từng platform.
      responses:
        '200':
          description: Thông tin platforms
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      platforms:
                        type: array
                        items:
                          $ref: '#/components/schemas/PlatformInfo'
                      total:
                        type: integer
                        example: 6

  /platform/limits:
    post:
      tags: [Integrations]
      summary: Lấy giới hạn và usage của platform
      description: Lấy thông tin rate limits và usage hiện tại của platform/tài khoản
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [platform]
              properties:
                platform:
                  type: string
                  enum: [facebook, instagram, linkedin, twitter, youtube, tiktok]
                  example: "facebook"
                  description: "Platform cần kiểm tra"
                account_id:
                  type: string
                  example: "integration_123"
                  description: "ID tài khoản cụ thể (optional)"
      responses:
        '200':
          description: Thông tin giới hạn platform
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      platform:
                        type: string
                        example: "facebook"
                      rate_limits:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          posts_per_hour: 25
                          api_calls_per_hour: 200
                        description: "Giới hạn rate limit"
                      current_usage:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          posts_per_hour: 5
                          api_calls_per_hour: 45
                        description: "Usage hiện tại"
                      reset_times:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          posts_per_hour: 1708016400
                          api_calls_per_hour: 1708016400
                        description: "Thời gian reset (Unix timestamp)"

  # Credits & Billing
  /credits/purchase:
    post:
      tags: [Credits]
      summary: Mua credits
      description: Mua thêm credits hoặc nâng cấp gói
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [packageId]
              properties:
                packageId:
                  type: string
                  example: "package_pro_monthly"
                paymentMethod:
                  type: string
                  enum: [card, bank_transfer, momo, zalopay]
                  example: "card"
      responses:
        '200':
          description: Tạo đơn hàng thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      orderId:
                        type: string
                        example: "order_123456"
                      paymentUrl:
                        type: string
                        format: uri
                        example: "https://payment.socialai.com/pay/order_123456"
                      amount:
                        type: integer
                        example: 299000
                      currency:
                        type: string
                        example: "VND"

  /credits/history:
    get:
      tags: [Credits]
      summary: Lịch sử sử dụng credits
      description: Lấy lịch sử sử dụng và nạp credits
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [all, usage, purchase]
            default: all
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Lịch sử credits
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      transactions:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              example: "txn_123456"
                            type:
                              type: string
                              enum: [usage, purchase, bonus]
                              example: "usage"
                            amount:
                              type: integer
                              example: -5
                            description:
                              type: string
                              example: "Tạo nội dung AI"
                            createdAt:
                              type: string
                              format: date-time
                              example: "2024-02-15T10:30:00Z"
                      pagination:
                        $ref: '#/components/schemas/Pagination'

  # Security Management
  /security/account/delete:
    post:
      tags: [Security]
      summary: Yêu cầu xóa tài khoản
      description: Gửi yêu cầu xóa tài khoản vĩnh viễn (có thời gian chờ 30 ngày)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [password, reason]
              properties:
                password:
                  type: string
                  example: "current_password"
                  description: "Mật khẩu hiện tại để xác thực"
                reason:
                  type: string
                  enum: [
                    "no_longer_needed",
                    "privacy_concerns",
                    "switching_service",
                    "technical_issues",
                    "other"
                  ]
                  example: "no_longer_needed"
                  description: "Lý do xóa tài khoản"
                reasonDetail:
                  type: string
                  example: "Không còn nhu cầu sử dụng dịch vụ"
                  description: "Chi tiết lý do (optional)"
                confirmText:
                  type: string
                  example: "DELETE MY ACCOUNT"
                  description: "Text xác nhận (phải nhập chính xác)"
                code2FA:
                  type: string
                  example: "123456"
                  description: "Mã 2FA (nếu đã bật 2FA)"
                deleteData:
                  type: boolean
                  default: true
                  example: true
                  description: "Xóa tất cả dữ liệu liên quan"
                downloadBackup:
                  type: boolean
                  default: false
                  example: true
                  description: "Tải backup trước khi xóa"
      responses:
        '200':
          description: Yêu cầu xóa tài khoản thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Yêu cầu xóa tài khoản đã được gửi!"
                      data:
                        type: object
                        properties:
                          deletionId:
                            type: string
                            example: "deletion_123456"
                          scheduledAt:
                            type: string
                            format: date-time
                            example: "2024-03-17T10:30:00Z"
                            description: "Thời gian xóa thực tế (30 ngày sau)"
                          gracePeriod:
                            type: integer
                            example: 30
                            description: "Số ngày có thể hủy yêu cầu"
                          backupUrl:
                            type: string
                            format: uri
                            example: "https://api.socialai.com/v1/security/backup/download/backup_789"
                            description: "URL tải backup (nếu được yêu cầu)"
                          cancelUrl:
                            type: string
                            format: uri
                            example: "https://socialai.com/account/cancel-deletion/deletion_123456"
                            description: "URL để hủy yêu cầu xóa"
                          warningMessage:
                            type: string
                            example: "Tài khoản sẽ bị xóa vĩnh viễn sau 30 ngày. Bạn có thể hủy yêu cầu này trước thời hạn."

  /security/account/cancel-deletion:
    post:
      tags: [Security]
      summary: Hủy yêu cầu xóa tài khoản
      description: Hủy yêu cầu xóa tài khoản trong thời gian grace period
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [deletionId, password]
              properties:
                deletionId:
                  type: string
                  example: "deletion_123456"
                  description: "ID yêu cầu xóa tài khoản"
                password:
                  type: string
                  example: "current_password"
                  description: "Mật khẩu hiện tại"
                reason:
                  type: string
                  example: "Đã thay đổi ý định"
                  description: "Lý do hủy yêu cầu"
      responses:
        '200':
          description: Hủy yêu cầu xóa tài khoản thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Hủy yêu cầu xóa tài khoản thành công!"
                      data:
                        type: object
                        properties:
                          deletionId:
                            type: string
                            example: "deletion_123456"
                          cancelledAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"
                          accountStatus:
                            type: string
                            example: "active"

  /security/backup/request:
    post:
      tags: [Security]
      summary: Yêu cầu backup dữ liệu
      description: Tạo yêu cầu backup toàn bộ dữ liệu người dùng
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                includeData:
                  type: array
                  items:
                    type: string
                    enum: [
                      "profile",
                      "posts",
                      "templates",
                      "images",
                      "rag_documents",
                      "analytics",
                      "settings",
                      "credits_history"
                    ]
                  default: ["profile", "posts", "templates", "images"]
                  example: ["profile", "posts", "templates", "images"]
                  description: "Loại dữ liệu cần backup"
                format:
                  type: string
                  enum: [json, csv, zip]
                  default: zip
                  example: "zip"
                  description: "Định dạng file backup"
                password:
                  type: string
                  example: "current_password"
                  description: "Mật khẩu hiện tại để xác thực"
                encryptBackup:
                  type: boolean
                  default: true
                  example: true
                  description: "Mã hóa file backup"
      responses:
        '200':
          description: Yêu cầu backup thành công
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Yêu cầu backup dữ liệu thành công!"
                      data:
                        type: object
                        properties:
                          backupId:
                            type: string
                            example: "backup_789012"
                          status:
                            type: string
                            example: "processing"
                          estimatedTime:
                            type: integer
                            example: 300
                            description: "Thời gian ước tính (giây)"
                          includeData:
                            type: array
                            items:
                              type: string
                            example: ["profile", "posts", "templates", "images"]
                          format:
                            type: string
                            example: "zip"
                          encrypted:
                            type: boolean
                            example: true
                          createdAt:
                            type: string
                            format: date-time
                            example: "2024-02-15T10:30:00Z"

  /security/backup/status/{backupId}:
    get:
      tags: [Security]
      summary: Kiểm tra trạng thái backup
      description: Lấy thông tin trạng thái và tiến độ backup
      security:
        - bearerAuth: []
      parameters:
        - name: backupId
          in: path
          required: true
          schema:
            type: string
          example: "backup_789012"
      responses:
        '200':
          description: Thông tin trạng thái backup
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy trạng thái backup thành công"
                      data:
                        type: object
                        properties:
                          backupId:
                            type: string
                            example: "backup_789012"
                          status:
                            type: string
                            enum: [processing, completed, failed, expired]
                            example: "completed"
                          progress:
                            type: integer
                            example: 100
                            description: "Tiến độ (0-100%)"
                          currentStep:
                            type: string
                            example: "Compressing files"
                          totalSteps:
                            type: integer
                            example: 5
                          fileSize:
                            type: integer
                            example: 52428800
                            description: "Kích thước file backup (bytes)"
                          downloadUrl:
                            type: string
                            format: uri
                            example: "https://api.socialai.com/v1/security/backup/download/backup_789012"
                            description: "URL tải file (chỉ có khi completed)"
                          expiresAt:
                            type: string
                            format: date-time
                            example: "2024-02-22T10:30:00Z"
                            description: "Thời gian hết hạn link tải"
                          error:
                            type: string
                            example: null
                            description: "Thông báo lỗi (nếu có)"

  /security/backup/download/{backupId}:
    get:
      tags: [Security]
      summary: Tải file backup
      description: Tải file backup dữ liệu người dùng
      security:
        - bearerAuth: []
      parameters:
        - name: backupId
          in: path
          required: true
          schema:
            type: string
          example: "backup_789012"
      responses:
        '200':
          description: File backup
          content:
            application/zip:
              schema:
                type: string
                format: binary
            application/json:
              schema:
                type: string
                format: binary
        '404':
          $ref: '#/components/responses/NotFound'
        '410':
          description: File backup đã hết hạn
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "File backup đã hết hạn"
                      code:
                        example: "BACKUP_EXPIRED"

  /security/backup/history:
    get:
      tags: [Security]
      summary: Lịch sử backup
      description: Lấy danh sách các yêu cầu backup đã tạo
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Lịch sử backup
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      message:
                        example: "Lấy lịch sử backup thành công"
                      data:
                        type: object
                        properties:
                          backups:
                            type: array
                            items:
                              type: object
                              properties:
                                backupId:
                                  type: string
                                  example: "backup_789012"
                                status:
                                  type: string
                                  example: "completed"
                                format:
                                  type: string
                                  example: "zip"
                                fileSize:
                                  type: integer
                                  example: 52428800
                                includeData:
                                  type: array
                                  items:
                                    type: string
                                  example: ["profile", "posts"]
                                createdAt:
                                  type: string
                                  format: date-time
                                  example: "2024-02-15T10:30:00Z"
                                expiresAt:
                                  type: string
                                  format: date-time
                                  example: "2024-02-22T10:30:00Z"
                                downloadUrl:
                                  type: string
                                  format: uri
                                  example: "https://api.socialai.com/v1/security/backup/download/backup_789012"
                          pagination:
                            $ref: '#/components/schemas/Pagination'

  # API Response Format Documentation
  /docs/response-format:
    get:
      tags: [Documentation]
      summary: API Response Format Documentation
      description: |
        ## 📋 Chuẩn Response Format

        Tất cả API responses đều tuân theo format chuẩn sau:

        ### ✅ Success Response Format:
        ```json
        {
          "success": true,
          "message": "Thao tác thành công",
          "data": {
            // Dữ liệu trả về
          },
          "meta": {
            "timestamp": "2024-02-15T10:30:00Z",
            "requestId": "req_123456789",
            "version": "1.0.0"
          }
        }
        ```

        ### ❌ Error Response Format:
        ```json
        {
          "success": false,
          "message": "Có lỗi xảy ra",
          "code": "ERROR_CODE",
          "errors": [
            {
              "field": "email",
              "code": "INVALID_FORMAT",
              "message": "Email không đúng định dạng"
            }
          ],
          "meta": {
            "timestamp": "2024-02-15T10:30:00Z",
            "requestId": "req_123456789",
            "version": "1.0.0"
          }
        }
        ```

        ### 🔑 Các trường bắt buộc:
        - **success** (boolean): Trạng thái thành công/thất bại
        - **message** (string): Thông báo mô tả kết quả

        ### 📦 Các trường tùy chọn:
        - **data** (object): Dữ liệu trả về (chỉ có khi success = true)
        - **code** (string): Mã lỗi cụ thể (chỉ có khi success = false)
        - **errors** (array): Chi tiết lỗi validation (optional)
        - **meta** (object): Metadata bổ sung (optional)

        ### 📊 Ví dụ thực tế:

        #### Đăng nhập thành công:
        ```json
        {
          "success": true,
          "message": "Đăng nhập thành công!",
          "data": {
            "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "user": {
              "id": "user_123",
              "email": "<EMAIL>",
              "fullName": "Nguyễn Văn A"
            },
            "expiresIn": 3600
          },
          "meta": {
            "timestamp": "2024-02-15T10:30:00Z",
            "requestId": "req_login_456",
            "loginMethod": "email"
          }
        }
        ```

        #### Validation Error:
        ```json
        {
          "success": false,
          "message": "Dữ liệu đầu vào không hợp lệ",
          "code": "VALIDATION_ERROR",
          "errors": [
            {
              "field": "email",
              "code": "INVALID_FORMAT",
              "message": "Email không đúng định dạng"
            },
            {
              "field": "password",
              "code": "MIN_LENGTH",
              "message": "Mật khẩu phải có ít nhất 6 ký tự"
            }
          ],
          "meta": {
            "timestamp": "2024-02-15T10:30:00Z",
            "requestId": "req_error_123"
          }
        }
        ```

        #### Insufficient Credits:
        ```json
        {
          "success": false,
          "message": "Không đủ credits để thực hiện",
          "code": "INSUFFICIENT_CREDITS",
          "data": {
            "required": 5,
            "available": 2,
            "upgradeUrl": "https://socialai.com/pricing"
          },
          "meta": {
            "timestamp": "2024-02-15T10:30:00Z",
            "requestId": "req_credits_123"
          }
        }
        ```

        ### 🎯 HTTP Status Codes:
        - **200**: Success - Thành công
        - **201**: Created - Tạo mới thành công
        - **400**: Bad Request - Dữ liệu không hợp lệ
        - **401**: Unauthorized - Chưa xác thực
        - **403**: Forbidden - Không có quyền
        - **404**: Not Found - Không tìm thấy
        - **409**: Conflict - Xung đột dữ liệu
        - **422**: Unprocessable Entity - Lỗi validation
        - **429**: Too Many Requests - Quá giới hạn
        - **500**: Internal Server Error - Lỗi server
      responses:
        '200':
          description: Documentation only - no actual endpoint

  # WebSocket Events Documentation
  /ai/websocket-events:
    get:
      tags: [AI Content]
      summary: WebSocket Events Documentation
      description: |
        ## WebSocket Message Formats

        ### Client Events (Client → Server):

        #### 1. Generate Content
        ```json
        {
          "event": "generate-content",
          "data": {
            "topic": "10 cách tiết kiệm tiền hiệu quả",
            "platforms": ["facebook", "instagram"],
            "contentType": "educational",
            "tone": "friendly",
            "length": "medium",
            "includeEmojis": true,
            "includeHashtags": true,
            "templateId": "template_123"
          },
          "requestId": "req_123456"
        }
        ```

        #### 2. Improve Text
        ```json
        {
          "event": "improve-text",
          "data": {
            "text": "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền",
            "improvementType": "engagement",
            "targetTone": "friendly",
            "platform": "facebook"
          },
          "requestId": "req_123457"
        }
        ```

        #### 3. Generate Image
        ```json
        {
          "event": "generate-image",
          "data": {
            "prompt": "Một gia đình hạnh phúc đang tiết kiệm tiền",
            "style": "realistic",
            "size": "square"
          },
          "requestId": "req_123458"
        }
        ```

        #### 4. Subscribe Agent Messages
        ```json
        {
          "event": "subscribe-agent",
          "data": {
            "agentId": "content-generator",
            "topics": ["content-generation", "text-improvement"]
          }
        }
        ```

        ### Server Events (Server → Client):

        #### 1. Agent Message
        ```json
        {
          "event": "agent-message",
          "data": {
            "agentId": "content-generator",
            "message": "Đang phân tích chủ đề và tạo outline...",
            "type": "info",
            "timestamp": "2024-02-15T10:30:00Z",
            "progress": 25
          }
        }
        ```

        #### 2. Content Chunk (Streaming)
        ```json
        {
          "event": "content-chunk",
          "data": {
            "requestId": "req_123456",
            "chunk": "🏠 10 Cách Tiết Kiệm Tiền Hiệu Quả",
            "isComplete": false,
            "progress": 15
          }
        }
        ```

        #### 3. Generation Complete
        ```json
        {
          "event": "generation-complete",
          "data": {
            "requestId": "req_123456",
            "result": {
              "content": "🏠 10 Cách Tiết Kiệm Tiền Hiệu Quả...",
              "hashtags": ["#tiếtkiệm", "#tàichính"],
              "creditsUsed": 5
            },
            "timestamp": "2024-02-15T10:32:00Z"
          }
        }
        ```

        #### 4. Error
        ```json
        {
          "event": "error",
          "data": {
            "requestId": "req_123456",
            "error": {
              "code": "INSUFFICIENT_CREDITS",
              "message": "Không đủ credits để thực hiện",
              "details": {
                "required": 5,
                "available": 2
              }
            }
          }
        }
        ```

        #### 5. Credits Updated
        ```json
        {
          "event": "credits-updated",
          "data": {
            "currentCredits": 845,
            "usedCredits": 5,
            "action": "content-generation",
            "timestamp": "2024-02-15T10:32:00Z"
          }
        }
        ```
      responses:
        '200':
          description: Documentation only - no actual endpoint

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    # Credit Configuration & Plans Schemas
    ModelCreditConfig:
      type: object
      additionalProperties:
        type: object
        properties:
          type:
            type: string
            enum: [text, image]
            example: text
          baseCost:
            type: integer
            example: 5
          per1000Tokens:
            type: integer
            example: 1
          perImage:
            type: integer
            example: 10
          description:
            type: string
            example: "Chi phí cho GPT‑4o text generation"
      example:
        gpt-4o:
          type: text
          baseCost: 5
          per1000Tokens: 2
          description: "OpenAI GPT‑4o 2025‑06"
        dalle-3:
          type: image
          perImage: 10
          description: "DALL·E 3 (1024×1024)"

    Plan:
      type: object
      properties:
        id:
          type: string
          example: "pro_monthly"
        name:
          type: string
          example: "Pro – Monthly"
        price:
          type: integer
          example: 299000
        currency:
          type: string
          example: "VND"
        monthlyCredits:
          type: integer
          example: 1000
        maxUsers:
          type: integer
          example: 5
        revenueShareTemplate:
          type: integer
          example: 50
          description: "Phần trăm credit chia lại khi template trả phí được sử dụng"
        features:
          type: array
          items:
            type: string
          example: ["ai-content", "ai-image", "schedule", "analytics-advanced"]

    # Base Response Schema - Tất cả responses phải follow format này
    BaseResponse:
      type: object
      required: [success, message]
      properties:
        success:
          type: boolean
          description: "Trạng thái thành công của request"
          example: true
        message:
          type: string
          description: "Thông báo mô tả kết quả"
          example: "Thao tác thành công"
        data:
          type: object
          description: "Dữ liệu trả về (optional)"
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: "email"
              code:
                type: string
                example: "INVALID_FORMAT"
              message:
                type: string
                example: "Email không đúng định dạng"
          description: "Chi tiết lỗi validation (optional)"
        meta:
          type: object
          properties:
            timestamp:
              type: string
              format: date-time
              example: "2024-02-15T10:30:00Z"
            requestId:
              type: string
              example: "req_123456789"
            version:
              type: string
              example: "1.0.0"
          description: "Metadata bổ sung (optional)"

    # Success Response với data
    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: true
            message:
              type: string
              example: "Thao tác thành công"

    # Error Response
    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: false
            message:
              type: string
              example: "Có lỗi xảy ra"
            code:
              type: string
              example: "VALIDATION_ERROR"
              description: "Mã lỗi cụ thể"

    User:
      type: object
      properties:
        id:
          type: string
          example: "user_123456"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        fullName:
          type: string
          example: "Nguyễn Văn A"
        avatar:
          type: string
          format: uri
          example: "https://example.com/avatar.jpg"
        plan:
          type: string
          example: "Pro Plan"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    UserProfile:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            phone:
              type: string
              example: "**********"
            bio:
              type: string
              example: "Digital marketer"
            company:
              type: string
              example: "ABC Company"
            industry:
              type: string
              example: "Công nghệ thông tin"
            settings:
              type: object
              properties:
                notifications:
                  type: boolean
                  example: true
                newsletter:
                  type: boolean
                  example: false

    # Notification Schemas
    Notification:
      type: object
      properties:
        id:
          type: string
          example: "notif_123456"
        type:
          type: string
          enum: [system, post_published, post_failed, credit_low, template_sold, workspace_invite, payment_success, payment_failed]
          example: "post_published"
          description: "Loại thông báo"
        title:
          type: string
          example: "Bài viết đã được đăng thành công"
          description: "Tiêu đề thông báo"
        message:
          type: string
          example: "Bài viết '10 Tips Marketing Hiệu Quả' đã được đăng lên Facebook và Instagram"
          description: "Nội dung thông báo"
        priority:
          type: string
          enum: [low, medium, high, urgent]
          example: "medium"
          description: "Mức độ ưu tiên"
        isRead:
          type: boolean
          example: false
          description: "Đã đọc hay chưa"
        isArchived:
          type: boolean
          example: false
          description: "Đã lưu trữ hay chưa"
        data:
          type: object
          description: "Dữ liệu bổ sung tùy theo loại thông báo"
          example:
            postId: "post_123456"
            platforms: ["facebook", "instagram"]
            publishedAt: "2024-02-15T10:30:00Z"
        actionUrl:
          type: string
          format: uri
          example: "/posts/post_123456"
          description: "URL để thực hiện hành động liên quan"
        actionText:
          type: string
          example: "Xem bài viết"
          description: "Text cho nút hành động"
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        readAt:
          type: string
          format: date-time
          example: "2024-02-15T11:00:00Z"
          description: "Thời gian đọc thông báo"
        expiresAt:
          type: string
          format: date-time
          example: "2024-03-17T10:30:00Z"
          description: "Thời gian hết hạn thông báo"
        userId:
          type: string
          example: "user_123456"

    NotificationDetail:
      allOf:
        - $ref: '#/components/schemas/Notification'
        - type: object
          properties:
            metadata:
              type: object
              properties:
                source:
                  type: string
                  example: "post_scheduler"
                  description: "Nguồn tạo thông báo"
                deviceInfo:
                  type: object
                  properties:
                    platform:
                      type: string
                      example: "web"
                    userAgent:
                      type: string
                      example: "Mozilla/5.0..."
                relatedNotifications:
                  type: array
                  items:
                    type: string
                  example: ["notif_123457", "notif_123458"]
                  description: "Các thông báo liên quan"
            history:
              type: array
              items:
                type: object
                properties:
                  action:
                    type: string
                    enum: [created, read, archived, deleted]
                    example: "read"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2024-02-15T11:00:00Z"
                  deviceInfo:
                    type: string
                    example: "Chrome on Windows"

    NotificationSettings:
      type: object
      properties:
        userId:
          type: string
          example: "user_123456"
        emailNotifications:
          type: object
          properties:
            enabled:
              type: boolean
              example: true
              description: "Bật/tắt thông báo email"
            postPublished:
              type: boolean
              example: true
              description: "Thông báo khi bài viết được đăng"
            postFailed:
              type: boolean
              example: true
              description: "Thông báo khi đăng bài thất bại"
            creditLow:
              type: boolean
              example: true
              description: "Thông báo khi credit thấp"
            templateSold:
              type: boolean
              example: true
              description: "Thông báo khi template được bán"
            workspaceInvite:
              type: boolean
              example: true
              description: "Thông báo lời mời workspace"
            paymentSuccess:
              type: boolean
              example: true
              description: "Thông báo thanh toán thành công"
            paymentFailed:
              type: boolean
              example: true
              description: "Thông báo thanh toán thất bại"
            systemUpdates:
              type: boolean
              example: false
              description: "Thông báo cập nhật hệ thống"
        pushNotifications:
          type: object
          properties:
            enabled:
              type: boolean
              example: true
              description: "Bật/tắt push notification"
            postPublished:
              type: boolean
              example: true
            postFailed:
              type: boolean
              example: true
            creditLow:
              type: boolean
              example: true
            templateSold:
              type: boolean
              example: false
            workspaceInvite:
              type: boolean
              example: true
            paymentSuccess:
              type: boolean
              example: false
            paymentFailed:
              type: boolean
              example: true
            systemUpdates:
              type: boolean
              example: false
        inAppNotifications:
          type: object
          properties:
            enabled:
              type: boolean
              example: true
              description: "Bật/tắt thông báo trong app"
            postPublished:
              type: boolean
              example: true
            postFailed:
              type: boolean
              example: true
            creditLow:
              type: boolean
              example: true
            templateSold:
              type: boolean
              example: true
            workspaceInvite:
              type: boolean
              example: true
            paymentSuccess:
              type: boolean
              example: true
            paymentFailed:
              type: boolean
              example: true
            systemUpdates:
              type: boolean
              example: true
        frequency:
          type: object
          properties:
            digest:
              type: string
              enum: [never, daily, weekly, monthly]
              example: "weekly"
              description: "Tần suất gửi email tổng hợp"
            quietHours:
              type: object
              properties:
                enabled:
                  type: boolean
                  example: true
                startTime:
                  type: string
                  example: "22:00"
                  description: "Giờ bắt đầu không gửi thông báo (HH:mm)"
                endTime:
                  type: string
                  example: "08:00"
                  description: "Giờ kết thúc không gửi thông báo (HH:mm)"
                timezone:
                  type: string
                  example: "Asia/Ho_Chi_Minh"
                  description: "Múi giờ"
        updatedAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"

    # Platform Integration Schemas
    PlatformAccount:
      type: object
      properties:
        id:
          type: string
          example: "integration_123"
          description: "ID tài khoản platform trong hệ thống"
        user_id:
          type: string
          example: "user_123"
          description: "ID người dùng sở hữu"
        workspace_id:
          type: string
          example: "workspace_456"
          description: "ID workspace (optional)"
        platform:
          type: string
          enum: [facebook, instagram, linkedin, twitter, youtube, tiktok]
          example: "facebook"
          description: "Tên platform"
        account_id:
          type: string
          example: "fb_page_789"
          description: "ID tài khoản trên platform"
        username:
          type: string
          example: "my_business_page"
          description: "Username/handle trên platform"
        display_name:
          type: string
          example: "My Business Page"
          description: "Tên hiển thị"
        avatar_url:
          type: string
          format: uri
          example: "https://platform.com/avatar.jpg"
          description: "URL avatar"
        status:
          type: string
          enum: [active, expired, error, disconnected]
          example: "active"
          description: "Trạng thái kết nối"
        permissions:
          type: array
          items:
            type: string
          example: ["pages_manage_posts", "pages_read_engagement"]
          description: "Quyền đã được cấp"
        metadata:
          type: object
          additionalProperties:
            type: string
          example:
            page_category: "Business"
            follower_count: "1250"
            verification_status: "verified"
          description: "Metadata bổ sung từ platform"
        connected_at:
          type: integer
          example: **********
          description: "Thời gian kết nối (Unix timestamp)"
        last_used:
          type: integer
          example: **********
          description: "Lần sử dụng cuối (Unix timestamp)"

    PlatformAccountDetail:
      allOf:
        - $ref: '#/components/schemas/PlatformAccount'
        - type: object
          properties:
            token_expires_at:
              type: integer
              example: **********
              description: "Thời gian hết hạn token (Unix timestamp)"
            last_sync:
              type: integer
              example: **********
              description: "Lần đồng bộ cuối (Unix timestamp)"
            sync_status:
              type: string
              enum: [success, failed, pending]
              example: "success"
              description: "Trạng thái đồng bộ cuối"
            error_message:
              type: string
              example: null
              description: "Thông báo lỗi nếu có"
            usage_stats:
              type: object
              properties:
                posts_published:
                  type: integer
                  example: 45
                  description: "Số bài đã đăng"
                api_calls_today:
                  type: integer
                  example: 23
                  description: "Số lần gọi API hôm nay"
                last_post_at:
                  type: integer
                  example: 1708095600
                  description: "Thời gian đăng bài cuối"

    PlatformInfo:
      type: object
      properties:
        name:
          type: string
          example: "facebook"
          description: "Tên platform"
        display_name:
          type: string
          example: "Facebook"
          description: "Tên hiển thị"
        features:
          type: object
          properties:
            oauth:
              type: boolean
              example: true
              description: "Hỗ trợ OAuth"
            publishing:
              type: boolean
              example: true
              description: "Hỗ trợ đăng bài text"
            analytics:
              type: boolean
              example: true
              description: "Hỗ trợ analytics"
            webhooks:
              type: boolean
              example: true
              description: "Hỗ trợ webhooks"
            scheduling:
              type: boolean
              example: true
              description: "Hỗ trợ lên lịch"
            image_upload:
              type: boolean
              example: true
              description: "Hỗ trợ upload hình ảnh"
            video_upload:
              type: boolean
              example: false
              description: "Hỗ trợ upload video"
        permissions:
          type: array
          items:
            type: string
          example: ["pages_manage_posts", "pages_read_engagement", "pages_show_list"]
          description: "Danh sách quyền cần thiết"
        rate_limits:
          type: object
          additionalProperties:
            type: integer
          example:
            posts_per_hour: 25
            api_calls_per_hour: 200
          description: "Giới hạn rate limit"

    # Authentication & Security Schemas
    LoginSession:
      type: object
      properties:
        id:
          type: string
          example: "session_123456"
        deviceInfo:
          type: object
          properties:
            browser:
              type: string
              example: "Chrome"
            browserVersion:
              type: string
              example: "120.0.0.0"
            os:
              type: string
              example: "Windows"
            osVersion:
              type: string
              example: "10"
            device:
              type: string
              example: "Desktop"
            userAgent:
              type: string
              example: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        location:
          type: object
          properties:
            ip:
              type: string
              example: "*************"
            country:
              type: string
              example: "Vietnam"
            city:
              type: string
              example: "Ho Chi Minh City"
            timezone:
              type: string
              example: "Asia/Ho_Chi_Minh"
        status:
          type: string
          enum: [active, expired]
          example: "active"
        isCurrent:
          type: boolean
          example: true
          description: "Có phải phiên hiện tại không"
        isTrusted:
          type: boolean
          example: false
          description: "Thiết bị đã được tin cậy (2FA)"
        loginMethod:
          type: string
          enum: [email, google, facebook]
          example: "email"
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        lastActivity:
          type: string
          format: date-time
          example: "2024-02-15T15:45:00Z"
        expiresAt:
          type: string
          format: date-time
          example: "2024-03-17T10:30:00Z"

    TwoFactorAuth:
      type: object
      properties:
        enabled:
          type: boolean
          example: true
        method:
          type: string
          enum: [totp, sms]
          example: "totp"
        enabledAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        backupCodesRemaining:
          type: integer
          example: 6
        trustedDevices:
          type: integer
          example: 2
          description: "Số thiết bị đã được tin cậy"

    SecurityEvent:
      type: object
      properties:
        id:
          type: string
          example: "event_123456"
        type:
          type: string
          enum: [
            login_success, login_failed, password_changed,
            2fa_enabled, 2fa_disabled, session_revoked,
            account_deletion_requested, backup_created
          ]
          example: "login_success"
        description:
          type: string
          example: "Đăng nhập thành công từ Chrome on Windows"
        severity:
          type: string
          enum: [low, medium, high, critical]
          example: "low"
        deviceInfo:
          type: object
          properties:
            browser:
              type: string
              example: "Chrome"
            os:
              type: string
              example: "Windows"
            ip:
              type: string
              example: "*************"
            location:
              type: string
              example: "Ho Chi Minh City, Vietnam"
        metadata:
          type: object
          properties:
            sessionId:
              type: string
              example: "session_123456"
            userAgent:
              type: string
              example: "Mozilla/5.0..."
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"

    AccountDeletion:
      type: object
      properties:
        id:
          type: string
          example: "deletion_123456"
        status:
          type: string
          enum: [pending, cancelled, completed]
          example: "pending"
        reason:
          type: string
          example: "no_longer_needed"
        reasonDetail:
          type: string
          example: "Không còn nhu cầu sử dụng dịch vụ"
        scheduledAt:
          type: string
          format: date-time
          example: "2024-03-17T10:30:00Z"
        gracePeriodEnds:
          type: string
          format: date-time
          example: "2024-03-17T10:30:00Z"
        canCancel:
          type: boolean
          example: true
        backupCreated:
          type: boolean
          example: true
        backupUrl:
          type: string
          format: uri
          example: "https://api.socialai.com/v1/security/backup/download/backup_789"
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"

    DataBackup:
      type: object
      properties:
        id:
          type: string
          example: "backup_789012"
        status:
          type: string
          enum: [processing, completed, failed, expired]
          example: "completed"
        format:
          type: string
          enum: [json, csv, zip]
          example: "zip"
        includeData:
          type: array
          items:
            type: string
          example: ["profile", "posts", "templates", "images"]
        fileSize:
          type: integer
          example: 52428800
          description: "Kích thước file (bytes)"
        encrypted:
          type: boolean
          example: true
        downloadUrl:
          type: string
          format: uri
          example: "https://api.socialai.com/v1/security/backup/download/backup_789012"
        downloadCount:
          type: integer
          example: 2
          description: "Số lần đã tải"
        progress:
          type: integer
          example: 100
          description: "Tiến độ tạo backup (0-100%)"
        processingSteps:
          type: array
          items:
            type: object
            properties:
              step:
                type: string
                example: "Exporting posts"
              status:
                type: string
                enum: [pending, processing, completed, failed]
                example: "completed"
              progress:
                type: integer
                example: 100
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        completedAt:
          type: string
          format: date-time
          example: "2024-02-15T10:35:00Z"
        expiresAt:
          type: string
          format: date-time
          example: "2024-02-22T10:30:00Z"
        error:
          type: string
          example: null
          description: "Thông báo lỗi (nếu có)"

    Post:
      type: object
      properties:
        id:
          type: string
          example: "post_123456"
        title:
          type: string
          example: "10 Tips Marketing Hiệu Quả"
        content:
          type: string
          example: "Nội dung bài viết chi tiết..."
        platforms:
          type: array
          items:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: ["facebook", "instagram"]
        status:
          type: string
          enum: [draft, scheduled, published, failed]
          example: "published"
        type:
          type: string
          enum: [manual, ai-generated]
          example: "ai-generated"
        images:
          type: array
          items:
            type: string
            format: uri
          example: ["https://example.com/image1.jpg"]
        hashtags:
          type: array
          items:
            type: string
          example: ["#marketing", "#tips"]
        scheduledAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        publishedAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2024-02-15T09:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-02-15T09:30:00Z"
        userId:
          type: string
          example: "user_123456"

    PostDetail:
      allOf:
        - $ref: '#/components/schemas/Post'
        - type: object
          properties:
            analytics:
              type: object
              properties:
                views:
                  type: integer
                  example: 1250
                likes:
                  type: integer
                  example: 89
                comments:
                  type: integer
                  example: 23
                shares:
                  type: integer
                  example: 12
                engagementRate:
                  type: number
                  format: float
                  example: 9.8
            platformResults:
              type: array
              items:
                type: object
                properties:
                  platform:
                    type: string
                    example: "facebook"
                  status:
                    type: string
                    enum: [success, failed, pending]
                    example: "success"
                  platformPostId:
                    type: string
                    example: "fb_post_789"
                  publishedAt:
                    type: string
                    format: date-time
                    example: "2024-02-15T10:30:00Z"
                  error:
                    type: string
                    example: null

    Template:
      type: object
      properties:
        id:
          type: string
          example: "template_123"
        name:
          type: string
          example: "Template Marketing Tips"
        description:
          type: string
          example: "Template cho các bài viết marketing tips"
        content:
          type: string
          example: "🎯 {topic}\n\n{content}\n\n#marketing #tips"
        category:
          type: string
          example: "marketing"
        platforms:
          type: array
          items:
            type: string
            enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: ["facebook", "instagram"]
        isPublic:
          type: boolean
          example: false
        usageCount:
          type: integer
          example: 25
        creditCost:
          type: integer
          minimum: 0
          maximum: 50
          example: 5
          description: "Giá credit để sử dụng template (0 = miễn phí)"
        enableMonetization:
          type: boolean
          example: false
          description: "Template có kiếm tiền không"
        isVerified:
          type: boolean
          example: false
          description: "Template đã được kiểm duyệt"
        ragFiles:
          type: array
          items:
            type: string
          example: ["rag_file_123", "rag_file_456"]
          description: "Danh sách ID file RAG"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        userId:
          type: string
          example: "user_123456"

    TemplateDetail:
      allOf:
        - $ref: '#/components/schemas/Template'
        - type: object
          properties:
            prompt:
              type: string
              example: "Tạo nội dung marketing tips hữu ích"
            trainingSamples:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    example: "sample_123"
                  description:
                    type: string
                    example: "Tips tiết kiệm"
                  content:
                    type: string
                    example: "10 cách tiết kiệm hiệu quả..."
            variables:
              type: array
              items:
                type: string
              example: ["topic", "content"]
            ragDocuments:
              type: array
              items:
                $ref: '#/components/schemas/RAGDocument'
              description: "Chi tiết file RAG được sử dụng"
            revenue:
              type: object
              properties:
                totalEarned:
                  type: integer
                  example: 150
                  description: "Tổng credits đã kiếm được"
                usageCount:
                  type: integer
                  example: 30
                  description: "Số lần template được sử dụng có phí"
                lastUsed:
                  type: string
                  format: date-time
                  example: "2024-02-14T15:30:00Z"

    MarketplaceTemplate:
      allOf:
        - $ref: '#/components/schemas/Template'
        - type: object
          properties:
            author:
              type: object
              properties:
                id:
                  type: string
                  example: "user_456"
                name:
                  type: string
                  example: "Marketing Expert"
                avatar:
                  type: string
                  format: uri
                  example: "https://example.com/avatar.jpg"
            price:
              type: integer
              example: 50
              description: "Giá bằng credits, 0 = miễn phí"
            rating:
              type: number
              format: float
              example: 4.8
            downloadCount:
              type: integer
              example: 1250
            tags:
              type: array
              items:
                type: string
              example: ["marketing", "social-media", "tips"]

    Image:
      type: object
      properties:
        id:
          type: string
          example: "image_123"
        title:
          type: string
          example: "Marketing Banner"
        url:
          type: string
          format: uri
          example: "https://cdn.socialai.com/images/img_123.jpg"
        thumbnailUrl:
          type: string
          format: uri
          example: "https://cdn.socialai.com/images/thumb_123.jpg"
        type:
          type: string
          enum: [uploaded, ai-generated, stock]
          example: "uploaded"
        size:
          type: integer
          example: 1024000
          description: "Kích thước file tính bằng bytes"
        dimensions:
          type: object
          properties:
            width:
              type: integer
              example: 1920
            height:
              type: integer
              example: 1080
        folderId:
          type: string
          example: "folder_123"
        tags:
          type: array
          items:
            type: string
          example: ["marketing", "banner"]
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        userId:
          type: string
          example: "user_123456"

    ImageDetail:
      allOf:
        - $ref: '#/components/schemas/Image'
        - type: object
          properties:
            metadata:
              type: object
              properties:
                exif:
                  type: object
                  properties:
                    camera:
                      type: string
                      example: "Canon EOS R5"
                    lens:
                      type: string
                      example: "RF 24-70mm f/2.8L IS USM"
                    iso:
                      type: integer
                      example: 400
                    aperture:
                      type: string
                      example: "f/2.8"
                    shutterSpeed:
                      type: string
                      example: "1/125"
                colorProfile:
                  type: string
                  example: "sRGB"
                compression:
                  type: string
                  example: "JPEG"
                hasTransparency:
                  type: boolean
                  example: false
            usage:
              type: object
              properties:
                usedInPosts:
                  type: integer
                  example: 5
                usedInTemplates:
                  type: integer
                  example: 2
                lastUsed:
                  type: string
                  format: date-time
                  example: "2024-02-10T15:30:00Z"
                popularityScore:
                  type: number
                  format: float
                  example: 8.5
            versions:
              type: array
              items:
                type: object
                properties:
                  size:
                    type: string
                    enum: [thumbnail, small, medium, large, original]
                    example: "medium"
                  url:
                    type: string
                    format: uri
                    example: "https://cdn.socialai.com/images/medium_img_123.jpg"
                  dimensions:
                    type: object
                    properties:
                      width:
                        type: integer
                        example: 800
                      height:
                        type: integer
                        example: 600
                  fileSize:
                    type: integer
                    example: 245760

    AssetFolder:
      type: object
      properties:
        id:
          type: string
          example: "folder_123"
        name:
          type: string
          example: "Marketing Images"
        description:
          type: string
          example: "Folder chứa ảnh marketing campaigns"
        parentId:
          type: string
          example: "folder_parent_123"
        path:
          type: string
          example: "/marketing/campaigns"
        color:
          type: string
          example: "#3b82f6"
        isPrivate:
          type: boolean
          example: false
        stats:
          type: object
          properties:
            imageCount:
              type: integer
              example: 25
            totalSize:
              type: integer
              example: 52428800
            lastModified:
              type: string
              format: date-time
              example: "2024-02-14T16:45:00Z"
        permissions:
          type: object
          properties:
            canEdit:
              type: boolean
              example: true
            canDelete:
              type: boolean
              example: true
            canShare:
              type: boolean
              example: true
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-02-14T16:45:00Z"
        userId:
          type: string
          example: "user_123456"

    # RAG Document Schemas
    RAGDocument:
      type: object
      properties:
        id:
          type: string
          example: "rag_file_123"
        title:
          type: string
          example: "Marketing Strategy 2024"
        filename:
          type: string
          example: "marketing_strategy_2024.pdf"
        originalName:
          type: string
          example: "Marketing Strategy 2024.pdf"
        description:
          type: string
          example: "Tài liệu chiến lược marketing năm 2024"
        type:
          type: string
          enum: [document, knowledge-base, training-data, reference]
          example: "document"
        format:
          type: string
          enum: [pdf, docx, txt, md, csv, json]
          example: "pdf"
        size:
          type: integer
          example: 2048000
          description: "Kích thước file (bytes)"
        language:
          type: string
          example: "vi"
        category:
          type: string
          example: "marketing"
        tags:
          type: array
          items:
            type: string
          example: ["marketing", "strategy", "2024", "planning"]
        status:
          type: string
          enum: [processing, indexed, failed, active, inactive]
          example: "active"
        isPublic:
          type: boolean
          example: false
        url:
          type: string
          format: uri
          example: "https://cdn.socialai.com/rag/rag_file_123.pdf"
        downloadUrl:
          type: string
          format: uri
          example: "https://api.socialai.com/v1/assets/sources/rag_file_123/download"
        indexStats:
          type: object
          properties:
            totalChunks:
              type: integer
              example: 45
            chunkSize:
              type: integer
              example: 500
            overlap:
              type: integer
              example: 50
            indexedAt:
              type: string
              format: date-time
              example: "2024-02-15T11:00:00Z"
            processingTime:
              type: number
              format: float
              example: 125.5
              description: "Thời gian xử lý (giây)"
        usage:
          type: object
          properties:
            usedInTemplates:
              type: integer
              example: 3
            searchCount:
              type: integer
              example: 15
            lastUsed:
              type: string
              format: date-time
              example: "2024-02-14T09:30:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-02-15T11:00:00Z"
        userId:
          type: string
          example: "user_123456"

    RAGDocumentDetail:
      allOf:
        - $ref: '#/components/schemas/RAGDocument'
        - type: object
          properties:
            content:
              type: object
              properties:
                preview:
                  type: string
                  example: "Chiến lược marketing năm 2024 tập trung vào..."
                  description: "Preview 500 ký tự đầu"
                wordCount:
                  type: integer
                  example: 5420
                pageCount:
                  type: integer
                  example: 25
                extractedText:
                  type: string
                  example: "Full extracted text content..."
                  description: "Toàn bộ text đã extract (chỉ hiển thị khi cần)"
            chunks:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    example: "chunk_789"
                  content:
                    type: string
                    example: "Chiến lược marketing digital tập trung vào..."
                  startIndex:
                    type: integer
                    example: 1250
                  endIndex:
                    type: integer
                    example: 1750
                  metadata:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 5
                      section:
                        type: string
                        example: "Digital Marketing Strategy"
                      heading:
                        type: string
                        example: "Chiến lược Marketing Digital"
            processing:
              type: object
              properties:
                jobId:
                  type: string
                  example: "job_456"
                progress:
                  type: integer
                  example: 100
                  description: "Tiến độ xử lý (0-100%)"
                logs:
                  type: array
                  items:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:35:00Z"
                      level:
                        type: string
                        enum: [info, warning, error]
                        example: "info"
                      message:
                        type: string
                        example: "Text extraction completed"
                errors:
                  type: array
                  items:
                    type: object
                    properties:
                      code:
                        type: string
                        example: "EXTRACTION_WARNING"
                      message:
                        type: string
                        example: "Some images could not be processed"
                      details:
                        type: string
                        example: "Pages 10-12 contain complex layouts"

    Pagination:
      type: object
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        total:
          type: integer
          example: 150
        totalPages:
          type: integer
          example: 15
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    # WebSocket Message Schemas
    WebSocketMessage:
      type: object
      required: [event, data]
      properties:
        event:
          type: string
          enum: [
            generate-content, improve-text, generate-image, subscribe-agent,
            agent-message, content-chunk, generation-complete, error, credits-updated
          ]
          example: "agent-message"
        data:
          type: object
          description: "Event-specific data payload"
        requestId:
          type: string
          example: "req_123456"
          description: "Unique request identifier for tracking"
        timestamp:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"

    AgentMessage:
      type: object
      properties:
        agentId:
          type: string
          enum: [content-generator, text-improver, image-generator]
          example: "content-generator"
        message:
          type: string
          example: "Đang phân tích chủ đề và tạo outline..."
        type:
          type: string
          enum: [info, warning, error, success]
          example: "info"
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 25
          description: "Tiến độ thực hiện (0-100%)"
        metadata:
          type: object
          properties:
            step:
              type: string
              example: "analyzing_topic"
            totalSteps:
              type: integer
              example: 4
            estimatedTime:
              type: integer
              example: 30
              description: "Thời gian ước tính còn lại (giây)"

    ContentChunk:
      type: object
      properties:
        requestId:
          type: string
          example: "req_123456"
        chunk:
          type: string
          example: "🏠 10 Cách Tiết Kiệm Tiền"
        isComplete:
          type: boolean
          example: false
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 15
        chunkIndex:
          type: integer
          example: 1
          description: "Thứ tự của chunk trong stream"
        totalChunks:
          type: integer
          example: 10
          description: "Tổng số chunks dự kiến"

    GenerationResult:
      type: object
      properties:
        requestId:
          type: string
          example: "req_123456"
        result:
          oneOf:
            - type: object
              properties:
                content:
                  type: string
                  example: "🏠 10 Cách Tiết Kiệm Tiền Hiệu Quả..."
                hashtags:
                  type: array
                  items:
                    type: string
                  example: ["#tiếtkiệm", "#tàichính"]
                suggestedImages:
                  type: array
                  items:
                    type: string
                  example: ["family savings", "money tips"]
            - type: object
              properties:
                originalText:
                  type: string
                  example: "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền"
                improvedText:
                  type: string
                  example: "🏠 Hôm nay mình muốn chia sẻ với các bạn những bí quyết tiết kiệm tiền siêu hiệu quả!"
                improvements:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        example: "engagement"
                      description:
                        type: string
                        example: "Thêm emoji và call-to-action"
            - type: object
              properties:
                imageUrl:
                  type: string
                  format: uri
                  example: "https://cdn.socialai.com/ai-images/img_123.jpg"
                thumbnailUrl:
                  type: string
                  format: uri
                  example: "https://cdn.socialai.com/ai-images/thumb_123.jpg"
                prompt:
                  type: string
                  example: "Một gia đình hạnh phúc đang tiết kiệm tiền"
        creditsUsed:
          type: integer
          example: 5
        processingTime:
          type: number
          format: float
          example: 12.5
          description: "Thời gian xử lý (giây)"

    WebSocketError:
      type: object
      properties:
        requestId:
          type: string
          example: "req_123456"
        error:
          type: object
          properties:
            code:
              type: string
              enum: [
                INSUFFICIENT_CREDITS, INVALID_REQUEST, RATE_LIMIT_EXCEEDED,
                GENERATION_FAILED, TIMEOUT, INTERNAL_ERROR
              ]
              example: "INSUFFICIENT_CREDITS"
            message:
              type: string
              example: "Không đủ credits để thực hiện"
            details:
              type: object
              example:
                required: 5
                available: 2

    CreditsUpdate:
      type: object
      properties:
        currentCredits:
          type: integer
          example: 845
        usedCredits:
          type: integer
          example: 5
        action:
          type: string
          enum: [content-generation, text-improvement, image-generation]
          example: "content-generation"
        description:
          type: string
          example: "Tạo nội dung AI cho chủ đề: 10 cách tiết kiệm tiền"

    # Text Improvement Schemas
    TextImprovementRequest:
      type: object
      required: [text, improvementType]
      properties:
        text:
          type: string
          minLength: 10
          maxLength: 5000
          example: "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền. Đây là một chủ đề rất quan trọng."
        improvementType:
          type: string
          enum: [grammar, style, engagement, clarity, tone, length]
          example: "engagement"
        targetTone:
          type: string
          enum: [friendly, professional, casual, formal, humorous, persuasive]
          default: friendly
          example: "friendly"
        targetLength:
          type: string
          enum: [shorter, longer, same]
          default: same
          example: "longer"
        platform:
          type: string
          enum: [facebook, instagram, linkedin, twitter, youtube, zalo]
          example: "facebook"
        context:
          type: string
          maxLength: 500
          example: "Bài viết về tài chính cá nhân cho gia đình trẻ"
        preserveFormatting:
          type: boolean
          default: true
          example: true

    TextImprovementResponse:
      type: object
      properties:
        originalText:
          type: string
          example: "Hôm nay tôi muốn chia sẻ về cách tiết kiệm tiền. Đây là một chủ đề rất quan trọng."
        improvedText:
          type: string
          example: "🏠 Hôm nay mình muốn chia sẻ với các bạn những bí quyết tiết kiệm tiền siêu hiệu quả! 💰"
        improvements:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: "engagement"
              description:
                type: string
                example: "Thêm emoji và call-to-action để tăng tương tác"
              before:
                type: string
                example: "Hôm nay tôi muốn chia sẻ"
              after:
                type: string
                example: "🏠 Hôm nay mình muốn chia sẻ với các bạn"
              impact:
                type: string
                enum: [low, medium, high]
                example: "high"
        suggestions:
          type: array
          items:
            type: string
          example: [
            "Thêm hashtag #tiếtkiệm #tàichính để tăng reach",
            "Kết thúc bằng câu hỏi để khuyến khích comment"
          ]
        metrics:
          type: object
          properties:
            readabilityScore:
              type: number
              format: float
              example: 8.5
              description: "Điểm dễ đọc (1-10)"
            engagementPotential:
              type: number
              format: float
              example: 7.8
              description: "Tiềm năng tương tác (1-10)"
            sentimentScore:
              type: number
              format: float
              example: 0.85
              description: "Điểm tích cực (-1 đến 1)"
        creditsUsed:
          type: integer
          example: 3
        confidence:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.92

    # Workspace Schemas
    Workspace:
      type: object
      properties:
        id:
          type: string
          example: "ws_123"
        name:
          type: string
          example: "Marketing Team"
        description:
          type: string
          example: "Workspace cho team marketing"
        slug:
          type: string
          example: "marketing-team"
        logo:
          type: string
          format: uri
          example: "https://example.com/logo.png"
        creditPool:
          type: integer
          example: 1500
          description: "Số credits chung của workspace"
        role:
          type: string
          enum: [owner, admin, editor, viewer]
          example: "editor"
          description: "Vai trò của user hiện tại trong workspace"
        memberCount:
          type: integer
          example: 8
          description: "Số lượng thành viên"
        isActive:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00Z"
        ownerId:
          type: string
          example: "user_456"

    WorkspaceDetail:
      allOf:
        - $ref: '#/components/schemas/Workspace'
        - type: object
          properties:
            settings:
              type: object
              properties:
                allowMemberInvite:
                  type: boolean
                  example: true
                  description: "Cho phép thành viên mời người khác"
                requireApproval:
                  type: boolean
                  example: false
                  description: "Yêu cầu phê duyệt khi tham gia"
                defaultRole:
                  type: string
                  enum: [editor, viewer]
                  example: "editor"
                  description: "Vai trò mặc định cho thành viên mới"
                creditSharing:
                  type: boolean
                  example: true
                  description: "Chia sẻ credit pool"
            stats:
              type: object
              properties:
                totalPosts:
                  type: integer
                  example: 156
                totalCreditsUsed:
                  type: integer
                  example: 2340
                activeMembers:
                  type: integer
                  example: 6
                  description: "Số thành viên hoạt động trong 30 ngày"
            recentActivity:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    example: "activity_789"
                  type:
                    type: string
                    enum: [post_created, member_joined, template_used, credit_used]
                    example: "post_created"
                  description:
                    type: string
                    example: "Nguyen Van A đã tạo bài viết mới"
                  userId:
                    type: string
                    example: "user_123"
                  userName:
                    type: string
                    example: "Nguyen Van A"
                  createdAt:
                    type: string
                    format: date-time
                    example: "2024-02-15T09:30:00Z"

    WorkspaceMember:
      type: object
      properties:
        id:
          type: string
          example: "member_456"
        userId:
          type: string
          example: "user_123"
        fullName:
          type: string
          example: "Nguyen Van A"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        avatar:
          type: string
          format: uri
          example: "https://example.com/avatar.jpg"
        role:
          type: string
          enum: [owner, admin, editor, viewer]
          example: "editor"
        status:
          type: string
          enum: [active, pending, inactive]
          example: "active"
        joinedAt:
          type: string
          format: date-time
          example: "2024-01-20T10:30:00Z"
        lastActiveAt:
          type: string
          format: date-time
          example: "2024-02-15T09:30:00Z"
        permissions:
          type: object
          properties:
            canCreatePosts:
              type: boolean
              example: true
            canManageTemplates:
              type: boolean
              example: true
            canInviteMembers:
              type: boolean
              example: false
            canManageSettings:
              type: boolean
              example: false
        stats:
          type: object
          properties:
            postsCreated:
              type: integer
              example: 23
            creditsUsed:
              type: integer
              example: 145
            templatesCreated:
              type: integer
              example: 5

    # Schedule AI Schemas
    ScheduleRecommendation:
      type: object
      properties:
        dateTime:
          type: string
          format: date-time
          example: "2024-07-01T09:00:00Z"
          description: "Thời gian được gợi ý"
        score:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.92
          description: "Điểm số tối ưu (0-1)"
        reason:
          type: string
          example: "Peak audience activity on Facebook"
          description: "Lý do gợi ý thời gian này"
        expectedEngagement:
          type: object
          properties:
            views:
              type: integer
              example: 1250
            likes:
              type: integer
              example: 89
            comments:
              type: integer
              example: 23
            shares:
              type: integer
              example: 12
        audienceActivity:
          type: object
          properties:
            activeUsers:
              type: integer
              example: 2340
              description: "Số user hoạt động dự kiến"
            activityLevel:
              type: string
              enum: [low, medium, high, peak]
              example: "peak"
            demographics:
              type: object
              properties:
                primaryAgeGroup:
                  type: string
                  example: "25-34"
                primaryLocation:
                  type: string
                  example: "Ho Chi Minh City"
        competitorAnalysis:
          type: object
          properties:
            competitorPostCount:
              type: integer
              example: 5
              description: "Số bài của competitor trong khung giờ này"
            competitionLevel:
              type: string
              enum: [low, medium, high]
              example: "medium"
        confidence:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.85
          description: "Độ tin cậy của gợi ý"

    # Template Revenue Schemas
    TemplateRevenueDetail:
      type: object
      properties:
        templateId:
          type: string
          example: "template_123"
        templateName:
          type: string
          example: "Marketing Tips Template"
        totalEarned:
          type: integer
          example: 2500
          description: "Tổng credits đã kiếm được"
        totalPaidOut:
          type: integer
          example: 1250
          description: "Tổng credits đã được trả (50%)"
        pendingPayout:
          type: integer
          example: 1250
          description: "Credits chờ thanh toán"
        usageStats:
          type: object
          properties:
            totalUsages:
              type: integer
              example: 500
            paidUsages:
              type: integer
              example: 50
              description: "Số lần sử dụng có phí"
            freeUsages:
              type: integer
              example: 450
              description: "Số lần sử dụng miễn phí"
            averageRating:
              type: number
              format: float
              example: 4.7
        revenueByPeriod:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
                example: "2024-02-15"
              usageCount:
                type: integer
                example: 8
              creditsEarned:
                type: integer
                example: 40
              uniqueUsers:
                type: integer
                example: 6
        topUsers:
          type: array
          items:
            type: object
            properties:
              userId:
                type: string
                example: "user_456"
              userName:
                type: string
                example: "Nguyen Van B"
              usageCount:
                type: integer
                example: 12
              creditsSpent:
                type: integer
                example: 60
        performance:
          type: object
          properties:
            conversionRate:
              type: number
              format: float
              example: 0.12
              description: "Tỷ lệ chuyển đổi từ view sang sử dụng"
            retentionRate:
              type: number
              format: float
              example: 0.68
              description: "Tỷ lệ user quay lại sử dụng"
            satisfactionScore:
              type: number
              format: float
              example: 4.5
              description: "Điểm hài lòng trung bình"

    AuthorRevenueSummary:
      type: object
      properties:
        totalEarned:
          type: integer
          example: 15000
          description: "Tổng credits đã kiếm được từ tất cả template"
        totalPaidOut:
          type: integer
          example: 7500
          description: "Tổng credits đã được trả"
        withdrawable:
          type: integer
          example: 7500
          description: "Số credits có thể rút"
        pendingWithdrawal:
          type: integer
          example: 2000
          description: "Số credits đang chờ rút"
        templateCount:
          type: integer
          example: 12
          description: "Tổng số template đã tạo"
        earningTemplateCount:
          type: integer
          example: 8
          description: "Số template đang kiếm tiền"
        topEarningTemplates:
          type: array
          items:
            type: object
            properties:
              templateId:
                type: string
                example: "template_123"
              name:
                type: string
                example: "Marketing Tips Template"
              earned:
                type: integer
                example: 2500
              usageCount:
                type: integer
                example: 500
              rating:
                type: number
                format: float
                example: 4.7
        revenueByMonth:
          type: array
          items:
            type: object
            properties:
              month:
                type: string
                example: "2024-02"
              earned:
                type: integer
                example: 3200
              paidOut:
                type: integer
                example: 1600
              templateUsages:
                type: integer
                example: 64
        stats:
          type: object
          properties:
            averageEarningPerTemplate:
              type: number
              format: float
              example: 1250.0
            averageRating:
              type: number
              format: float
              example: 4.6
            totalDownloads:
              type: integer
              example: 5420
            conversionRate:
              type: number
              format: float
              example: 0.15
              description: "Tỷ lệ chuyển đổi từ download sang sử dụng có phí"

    WithdrawalRecord:
      type: object
      properties:
        id:
          type: string
          example: "withdrawal_789"
        amount:
          type: integer
          example: 500000
          description: "Số tiền rút (VND)"
        creditsAmount:
          type: integer
          example: 5000
          description: "Số credits tương ứng"
        method:
          type: string
          enum: [bank_transfer, momo, zalopay]
          example: "bank_transfer"
        status:
          type: string
          enum: [pending, processing, completed, failed, cancelled]
          example: "completed"
        bankInfo:
          type: object
          properties:
            bankName:
              type: string
              example: "Vietcombank"
            accountNumber:
              type: string
              example: "****7890"
            accountName:
              type: string
              example: "Nguyen Van A"
        processingTime:
          type: object
          properties:
            requestedAt:
              type: string
              format: date-time
              example: "2024-02-15T10:30:00Z"
            processedAt:
              type: string
              format: date-time
              example: "2024-02-16T14:20:00Z"
            completedAt:
              type: string
              format: date-time
              example: "2024-02-17T09:15:00Z"
        fees:
          type: object
          properties:
            processingFee:
              type: integer
              example: 15000
              description: "Phí xử lý (VND)"
            bankFee:
              type: integer
              example: 5000
              description: "Phí ngân hàng (VND)"
            totalFees:
              type: integer
              example: 20000
        actualAmount:
          type: integer
          example: 480000
          description: "Số tiền thực nhận sau phí"
        note:
          type: string
          example: "Rút tiền doanh thu tháng 2"
        transactionId:
          type: string
          example: "txn_456789"
          description: "Mã giao dịch từ ngân hàng/ví"

    Error:
      type: object
      required: [success, message]
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Có lỗi xảy ra"
        code:
          type: string
          example: "VALIDATION_ERROR"
        details:
          type: object

  responses:
    BadRequest:
      description: Dữ liệu đầu vào không hợp lệ
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Dữ liệu đầu vào không hợp lệ"
                  code:
                    example: "VALIDATION_ERROR"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                          example: "email"
                        code:
                          type: string
                          example: "INVALID_FORMAT"
                        message:
                          type: string
                          example: "Email không đúng định dạng"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_error_123"

    Unauthorized:
      description: Không có quyền truy cập
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Token không hợp lệ hoặc đã hết hạn"
                  code:
                    example: "UNAUTHORIZED"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_unauth_123"

    Forbidden:
      description: Không đủ quyền thực hiện
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Không đủ quyền thực hiện hành động này"
                  code:
                    example: "FORBIDDEN"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_forbidden_123"

    NotFound:
      description: Không tìm thấy tài nguyên
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Không tìm thấy tài nguyên"
                  code:
                    example: "NOT_FOUND"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_notfound_123"

    InternalServerError:
      description: Lỗi server nội bộ
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Lỗi server nội bộ"
                  code:
                    example: "INTERNAL_ERROR"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_internal_123"

    InsufficientCredits:
      description: Không đủ credits
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  message:
                    example: "Không đủ credits để thực hiện"
                  code:
                    example: "INSUFFICIENT_CREDITS"
                  data:
                    type: object
                    properties:
                      required:
                        type: integer
                        example: 5
                      available:
                        type: integer
                        example: 2
                      upgradeUrl:
                        type: string
                        format: uri
                        example: "https://socialai.com/pricing"
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        example: "2024-02-15T10:30:00Z"
                      requestId:
                        type: string
                        example: "req_credits_123"

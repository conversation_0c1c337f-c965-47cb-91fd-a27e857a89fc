// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/predicate"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// PublishedPostUpdate is the builder for updating PublishedPost entities.
type PublishedPostUpdate struct {
	config
	hooks    []Hook
	mutation *PublishedPostMutation
}

// Where appends a list predicates to the PublishedPostUpdate builder.
func (ppu *PublishedPostUpdate) Where(ps ...predicate.PublishedPost) *PublishedPostUpdate {
	ppu.mutation.Where(ps...)
	return ppu
}

// SetUserID sets the "user_id" field.
func (ppu *PublishedPostUpdate) SetUserID(u uuid.UUID) *PublishedPostUpdate {
	ppu.mutation.SetUserID(u)
	return ppu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableUserID(u *uuid.UUID) *PublishedPostUpdate {
	if u != nil {
		ppu.SetUserID(*u)
	}
	return ppu
}

// SetPostID sets the "post_id" field.
func (ppu *PublishedPostUpdate) SetPostID(s string) *PublishedPostUpdate {
	ppu.mutation.SetPostID(s)
	return ppu
}

// SetNillablePostID sets the "post_id" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillablePostID(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetPostID(*s)
	}
	return ppu
}

// SetIntegrationID sets the "integration_id" field.
func (ppu *PublishedPostUpdate) SetIntegrationID(s string) *PublishedPostUpdate {
	ppu.mutation.SetIntegrationID(s)
	return ppu
}

// SetNillableIntegrationID sets the "integration_id" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableIntegrationID(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetIntegrationID(*s)
	}
	return ppu
}

// SetPlatform sets the "platform" field.
func (ppu *PublishedPostUpdate) SetPlatform(pu publishedpost.Platform) *PublishedPostUpdate {
	ppu.mutation.SetPlatform(pu)
	return ppu
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillablePlatform(pu *publishedpost.Platform) *PublishedPostUpdate {
	if pu != nil {
		ppu.SetPlatform(*pu)
	}
	return ppu
}

// SetPlatformPostID sets the "platform_post_id" field.
func (ppu *PublishedPostUpdate) SetPlatformPostID(s string) *PublishedPostUpdate {
	ppu.mutation.SetPlatformPostID(s)
	return ppu
}

// SetNillablePlatformPostID sets the "platform_post_id" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillablePlatformPostID(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetPlatformPostID(*s)
	}
	return ppu
}

// SetPlatformPostURL sets the "platform_post_url" field.
func (ppu *PublishedPostUpdate) SetPlatformPostURL(s string) *PublishedPostUpdate {
	ppu.mutation.SetPlatformPostURL(s)
	return ppu
}

// SetNillablePlatformPostURL sets the "platform_post_url" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillablePlatformPostURL(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetPlatformPostURL(*s)
	}
	return ppu
}

// ClearPlatformPostURL clears the value of the "platform_post_url" field.
func (ppu *PublishedPostUpdate) ClearPlatformPostURL() *PublishedPostUpdate {
	ppu.mutation.ClearPlatformPostURL()
	return ppu
}

// SetContent sets the "content" field.
func (ppu *PublishedPostUpdate) SetContent(s string) *PublishedPostUpdate {
	ppu.mutation.SetContent(s)
	return ppu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableContent(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetContent(*s)
	}
	return ppu
}

// SetMediaUrls sets the "media_urls" field.
func (ppu *PublishedPostUpdate) SetMediaUrls(s []string) *PublishedPostUpdate {
	ppu.mutation.SetMediaUrls(s)
	return ppu
}

// AppendMediaUrls appends s to the "media_urls" field.
func (ppu *PublishedPostUpdate) AppendMediaUrls(s []string) *PublishedPostUpdate {
	ppu.mutation.AppendMediaUrls(s)
	return ppu
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (ppu *PublishedPostUpdate) ClearMediaUrls() *PublishedPostUpdate {
	ppu.mutation.ClearMediaUrls()
	return ppu
}

// SetHashtags sets the "hashtags" field.
func (ppu *PublishedPostUpdate) SetHashtags(s []string) *PublishedPostUpdate {
	ppu.mutation.SetHashtags(s)
	return ppu
}

// AppendHashtags appends s to the "hashtags" field.
func (ppu *PublishedPostUpdate) AppendHashtags(s []string) *PublishedPostUpdate {
	ppu.mutation.AppendHashtags(s)
	return ppu
}

// ClearHashtags clears the value of the "hashtags" field.
func (ppu *PublishedPostUpdate) ClearHashtags() *PublishedPostUpdate {
	ppu.mutation.ClearHashtags()
	return ppu
}

// SetStatus sets the "status" field.
func (ppu *PublishedPostUpdate) SetStatus(pu publishedpost.Status) *PublishedPostUpdate {
	ppu.mutation.SetStatus(pu)
	return ppu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableStatus(pu *publishedpost.Status) *PublishedPostUpdate {
	if pu != nil {
		ppu.SetStatus(*pu)
	}
	return ppu
}

// SetErrorMessage sets the "error_message" field.
func (ppu *PublishedPostUpdate) SetErrorMessage(s string) *PublishedPostUpdate {
	ppu.mutation.SetErrorMessage(s)
	return ppu
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableErrorMessage(s *string) *PublishedPostUpdate {
	if s != nil {
		ppu.SetErrorMessage(*s)
	}
	return ppu
}

// ClearErrorMessage clears the value of the "error_message" field.
func (ppu *PublishedPostUpdate) ClearErrorMessage() *PublishedPostUpdate {
	ppu.mutation.ClearErrorMessage()
	return ppu
}

// SetScheduledAt sets the "scheduled_at" field.
func (ppu *PublishedPostUpdate) SetScheduledAt(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetScheduledAt(t)
	return ppu
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableScheduledAt(t *time.Time) *PublishedPostUpdate {
	if t != nil {
		ppu.SetScheduledAt(*t)
	}
	return ppu
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (ppu *PublishedPostUpdate) ClearScheduledAt() *PublishedPostUpdate {
	ppu.mutation.ClearScheduledAt()
	return ppu
}

// SetPublishedAt sets the "published_at" field.
func (ppu *PublishedPostUpdate) SetPublishedAt(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetPublishedAt(t)
	return ppu
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillablePublishedAt(t *time.Time) *PublishedPostUpdate {
	if t != nil {
		ppu.SetPublishedAt(*t)
	}
	return ppu
}

// ClearPublishedAt clears the value of the "published_at" field.
func (ppu *PublishedPostUpdate) ClearPublishedAt() *PublishedPostUpdate {
	ppu.mutation.ClearPublishedAt()
	return ppu
}

// SetPlatformResponse sets the "platform_response" field.
func (ppu *PublishedPostUpdate) SetPlatformResponse(m map[string]interface{}) *PublishedPostUpdate {
	ppu.mutation.SetPlatformResponse(m)
	return ppu
}

// ClearPlatformResponse clears the value of the "platform_response" field.
func (ppu *PublishedPostUpdate) ClearPlatformResponse() *PublishedPostUpdate {
	ppu.mutation.ClearPlatformResponse()
	return ppu
}

// SetAnalytics sets the "analytics" field.
func (ppu *PublishedPostUpdate) SetAnalytics(m map[string]interface{}) *PublishedPostUpdate {
	ppu.mutation.SetAnalytics(m)
	return ppu
}

// ClearAnalytics clears the value of the "analytics" field.
func (ppu *PublishedPostUpdate) ClearAnalytics() *PublishedPostUpdate {
	ppu.mutation.ClearAnalytics()
	return ppu
}

// SetLastAnalyticsSync sets the "last_analytics_sync" field.
func (ppu *PublishedPostUpdate) SetLastAnalyticsSync(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetLastAnalyticsSync(t)
	return ppu
}

// SetNillableLastAnalyticsSync sets the "last_analytics_sync" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableLastAnalyticsSync(t *time.Time) *PublishedPostUpdate {
	if t != nil {
		ppu.SetLastAnalyticsSync(*t)
	}
	return ppu
}

// ClearLastAnalyticsSync clears the value of the "last_analytics_sync" field.
func (ppu *PublishedPostUpdate) ClearLastAnalyticsSync() *PublishedPostUpdate {
	ppu.mutation.ClearLastAnalyticsSync()
	return ppu
}

// SetRetryCount sets the "retry_count" field.
func (ppu *PublishedPostUpdate) SetRetryCount(i int) *PublishedPostUpdate {
	ppu.mutation.ResetRetryCount()
	ppu.mutation.SetRetryCount(i)
	return ppu
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableRetryCount(i *int) *PublishedPostUpdate {
	if i != nil {
		ppu.SetRetryCount(*i)
	}
	return ppu
}

// AddRetryCount adds i to the "retry_count" field.
func (ppu *PublishedPostUpdate) AddRetryCount(i int) *PublishedPostUpdate {
	ppu.mutation.AddRetryCount(i)
	return ppu
}

// SetNextRetryAt sets the "next_retry_at" field.
func (ppu *PublishedPostUpdate) SetNextRetryAt(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetNextRetryAt(t)
	return ppu
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableNextRetryAt(t *time.Time) *PublishedPostUpdate {
	if t != nil {
		ppu.SetNextRetryAt(*t)
	}
	return ppu
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (ppu *PublishedPostUpdate) ClearNextRetryAt() *PublishedPostUpdate {
	ppu.mutation.ClearNextRetryAt()
	return ppu
}

// SetPublishOptions sets the "publish_options" field.
func (ppu *PublishedPostUpdate) SetPublishOptions(m map[string]interface{}) *PublishedPostUpdate {
	ppu.mutation.SetPublishOptions(m)
	return ppu
}

// ClearPublishOptions clears the value of the "publish_options" field.
func (ppu *PublishedPostUpdate) ClearPublishOptions() *PublishedPostUpdate {
	ppu.mutation.ClearPublishOptions()
	return ppu
}

// SetMetadata sets the "metadata" field.
func (ppu *PublishedPostUpdate) SetMetadata(m map[string]interface{}) *PublishedPostUpdate {
	ppu.mutation.SetMetadata(m)
	return ppu
}

// ClearMetadata clears the value of the "metadata" field.
func (ppu *PublishedPostUpdate) ClearMetadata() *PublishedPostUpdate {
	ppu.mutation.ClearMetadata()
	return ppu
}

// SetUpdatedAt sets the "updated_at" field.
func (ppu *PublishedPostUpdate) SetUpdatedAt(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetUpdatedAt(t)
	return ppu
}

// SetDeletedAt sets the "deleted_at" field.
func (ppu *PublishedPostUpdate) SetDeletedAt(t time.Time) *PublishedPostUpdate {
	ppu.mutation.SetDeletedAt(t)
	return ppu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ppu *PublishedPostUpdate) SetNillableDeletedAt(t *time.Time) *PublishedPostUpdate {
	if t != nil {
		ppu.SetDeletedAt(*t)
	}
	return ppu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ppu *PublishedPostUpdate) ClearDeletedAt() *PublishedPostUpdate {
	ppu.mutation.ClearDeletedAt()
	return ppu
}

// Mutation returns the PublishedPostMutation object of the builder.
func (ppu *PublishedPostUpdate) Mutation() *PublishedPostMutation {
	return ppu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ppu *PublishedPostUpdate) Save(ctx context.Context) (int, error) {
	ppu.defaults()
	return withHooks(ctx, ppu.sqlSave, ppu.mutation, ppu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ppu *PublishedPostUpdate) SaveX(ctx context.Context) int {
	affected, err := ppu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ppu *PublishedPostUpdate) Exec(ctx context.Context) error {
	_, err := ppu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ppu *PublishedPostUpdate) ExecX(ctx context.Context) {
	if err := ppu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ppu *PublishedPostUpdate) defaults() {
	if _, ok := ppu.mutation.UpdatedAt(); !ok {
		v := publishedpost.UpdateDefaultUpdatedAt()
		ppu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ppu *PublishedPostUpdate) check() error {
	if v, ok := ppu.mutation.PostID(); ok {
		if err := publishedpost.PostIDValidator(v); err != nil {
			return &ValidationError{Name: "post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.post_id": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.IntegrationID(); ok {
		if err := publishedpost.IntegrationIDValidator(v); err != nil {
			return &ValidationError{Name: "integration_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.integration_id": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.Platform(); ok {
		if err := publishedpost.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.PlatformPostID(); ok {
		if err := publishedpost.PlatformPostIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_id": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.PlatformPostURL(); ok {
		if err := publishedpost.PlatformPostURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_url", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_url": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.Status(); ok {
		if err := publishedpost.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.status": %w`, err)}
		}
	}
	if v, ok := ppu.mutation.ErrorMessage(); ok {
		if err := publishedpost.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.error_message": %w`, err)}
		}
	}
	return nil
}

func (ppu *PublishedPostUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ppu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(publishedpost.Table, publishedpost.Columns, sqlgraph.NewFieldSpec(publishedpost.FieldID, field.TypeUUID))
	if ps := ppu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ppu.mutation.UserID(); ok {
		_spec.SetField(publishedpost.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ppu.mutation.PostID(); ok {
		_spec.SetField(publishedpost.FieldPostID, field.TypeString, value)
	}
	if value, ok := ppu.mutation.IntegrationID(); ok {
		_spec.SetField(publishedpost.FieldIntegrationID, field.TypeString, value)
	}
	if value, ok := ppu.mutation.Platform(); ok {
		_spec.SetField(publishedpost.FieldPlatform, field.TypeEnum, value)
	}
	if value, ok := ppu.mutation.PlatformPostID(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostID, field.TypeString, value)
	}
	if value, ok := ppu.mutation.PlatformPostURL(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostURL, field.TypeString, value)
	}
	if ppu.mutation.PlatformPostURLCleared() {
		_spec.ClearField(publishedpost.FieldPlatformPostURL, field.TypeString)
	}
	if value, ok := ppu.mutation.Content(); ok {
		_spec.SetField(publishedpost.FieldContent, field.TypeString, value)
	}
	if value, ok := ppu.mutation.MediaUrls(); ok {
		_spec.SetField(publishedpost.FieldMediaUrls, field.TypeJSON, value)
	}
	if value, ok := ppu.mutation.AppendedMediaUrls(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, publishedpost.FieldMediaUrls, value)
		})
	}
	if ppu.mutation.MediaUrlsCleared() {
		_spec.ClearField(publishedpost.FieldMediaUrls, field.TypeJSON)
	}
	if value, ok := ppu.mutation.Hashtags(); ok {
		_spec.SetField(publishedpost.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := ppu.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, publishedpost.FieldHashtags, value)
		})
	}
	if ppu.mutation.HashtagsCleared() {
		_spec.ClearField(publishedpost.FieldHashtags, field.TypeJSON)
	}
	if value, ok := ppu.mutation.Status(); ok {
		_spec.SetField(publishedpost.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ppu.mutation.ErrorMessage(); ok {
		_spec.SetField(publishedpost.FieldErrorMessage, field.TypeString, value)
	}
	if ppu.mutation.ErrorMessageCleared() {
		_spec.ClearField(publishedpost.FieldErrorMessage, field.TypeString)
	}
	if value, ok := ppu.mutation.ScheduledAt(); ok {
		_spec.SetField(publishedpost.FieldScheduledAt, field.TypeTime, value)
	}
	if ppu.mutation.ScheduledAtCleared() {
		_spec.ClearField(publishedpost.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := ppu.mutation.PublishedAt(); ok {
		_spec.SetField(publishedpost.FieldPublishedAt, field.TypeTime, value)
	}
	if ppu.mutation.PublishedAtCleared() {
		_spec.ClearField(publishedpost.FieldPublishedAt, field.TypeTime)
	}
	if value, ok := ppu.mutation.PlatformResponse(); ok {
		_spec.SetField(publishedpost.FieldPlatformResponse, field.TypeJSON, value)
	}
	if ppu.mutation.PlatformResponseCleared() {
		_spec.ClearField(publishedpost.FieldPlatformResponse, field.TypeJSON)
	}
	if value, ok := ppu.mutation.Analytics(); ok {
		_spec.SetField(publishedpost.FieldAnalytics, field.TypeJSON, value)
	}
	if ppu.mutation.AnalyticsCleared() {
		_spec.ClearField(publishedpost.FieldAnalytics, field.TypeJSON)
	}
	if value, ok := ppu.mutation.LastAnalyticsSync(); ok {
		_spec.SetField(publishedpost.FieldLastAnalyticsSync, field.TypeTime, value)
	}
	if ppu.mutation.LastAnalyticsSyncCleared() {
		_spec.ClearField(publishedpost.FieldLastAnalyticsSync, field.TypeTime)
	}
	if value, ok := ppu.mutation.RetryCount(); ok {
		_spec.SetField(publishedpost.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := ppu.mutation.AddedRetryCount(); ok {
		_spec.AddField(publishedpost.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := ppu.mutation.NextRetryAt(); ok {
		_spec.SetField(publishedpost.FieldNextRetryAt, field.TypeTime, value)
	}
	if ppu.mutation.NextRetryAtCleared() {
		_spec.ClearField(publishedpost.FieldNextRetryAt, field.TypeTime)
	}
	if value, ok := ppu.mutation.PublishOptions(); ok {
		_spec.SetField(publishedpost.FieldPublishOptions, field.TypeJSON, value)
	}
	if ppu.mutation.PublishOptionsCleared() {
		_spec.ClearField(publishedpost.FieldPublishOptions, field.TypeJSON)
	}
	if value, ok := ppu.mutation.Metadata(); ok {
		_spec.SetField(publishedpost.FieldMetadata, field.TypeJSON, value)
	}
	if ppu.mutation.MetadataCleared() {
		_spec.ClearField(publishedpost.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ppu.mutation.UpdatedAt(); ok {
		_spec.SetField(publishedpost.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ppu.mutation.DeletedAt(); ok {
		_spec.SetField(publishedpost.FieldDeletedAt, field.TypeTime, value)
	}
	if ppu.mutation.DeletedAtCleared() {
		_spec.ClearField(publishedpost.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ppu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{publishedpost.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ppu.mutation.done = true
	return n, nil
}

// PublishedPostUpdateOne is the builder for updating a single PublishedPost entity.
type PublishedPostUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PublishedPostMutation
}

// SetUserID sets the "user_id" field.
func (ppuo *PublishedPostUpdateOne) SetUserID(u uuid.UUID) *PublishedPostUpdateOne {
	ppuo.mutation.SetUserID(u)
	return ppuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableUserID(u *uuid.UUID) *PublishedPostUpdateOne {
	if u != nil {
		ppuo.SetUserID(*u)
	}
	return ppuo
}

// SetPostID sets the "post_id" field.
func (ppuo *PublishedPostUpdateOne) SetPostID(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetPostID(s)
	return ppuo
}

// SetNillablePostID sets the "post_id" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillablePostID(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetPostID(*s)
	}
	return ppuo
}

// SetIntegrationID sets the "integration_id" field.
func (ppuo *PublishedPostUpdateOne) SetIntegrationID(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetIntegrationID(s)
	return ppuo
}

// SetNillableIntegrationID sets the "integration_id" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableIntegrationID(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetIntegrationID(*s)
	}
	return ppuo
}

// SetPlatform sets the "platform" field.
func (ppuo *PublishedPostUpdateOne) SetPlatform(pu publishedpost.Platform) *PublishedPostUpdateOne {
	ppuo.mutation.SetPlatform(pu)
	return ppuo
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillablePlatform(pu *publishedpost.Platform) *PublishedPostUpdateOne {
	if pu != nil {
		ppuo.SetPlatform(*pu)
	}
	return ppuo
}

// SetPlatformPostID sets the "platform_post_id" field.
func (ppuo *PublishedPostUpdateOne) SetPlatformPostID(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetPlatformPostID(s)
	return ppuo
}

// SetNillablePlatformPostID sets the "platform_post_id" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillablePlatformPostID(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetPlatformPostID(*s)
	}
	return ppuo
}

// SetPlatformPostURL sets the "platform_post_url" field.
func (ppuo *PublishedPostUpdateOne) SetPlatformPostURL(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetPlatformPostURL(s)
	return ppuo
}

// SetNillablePlatformPostURL sets the "platform_post_url" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillablePlatformPostURL(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetPlatformPostURL(*s)
	}
	return ppuo
}

// ClearPlatformPostURL clears the value of the "platform_post_url" field.
func (ppuo *PublishedPostUpdateOne) ClearPlatformPostURL() *PublishedPostUpdateOne {
	ppuo.mutation.ClearPlatformPostURL()
	return ppuo
}

// SetContent sets the "content" field.
func (ppuo *PublishedPostUpdateOne) SetContent(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetContent(s)
	return ppuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableContent(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetContent(*s)
	}
	return ppuo
}

// SetMediaUrls sets the "media_urls" field.
func (ppuo *PublishedPostUpdateOne) SetMediaUrls(s []string) *PublishedPostUpdateOne {
	ppuo.mutation.SetMediaUrls(s)
	return ppuo
}

// AppendMediaUrls appends s to the "media_urls" field.
func (ppuo *PublishedPostUpdateOne) AppendMediaUrls(s []string) *PublishedPostUpdateOne {
	ppuo.mutation.AppendMediaUrls(s)
	return ppuo
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (ppuo *PublishedPostUpdateOne) ClearMediaUrls() *PublishedPostUpdateOne {
	ppuo.mutation.ClearMediaUrls()
	return ppuo
}

// SetHashtags sets the "hashtags" field.
func (ppuo *PublishedPostUpdateOne) SetHashtags(s []string) *PublishedPostUpdateOne {
	ppuo.mutation.SetHashtags(s)
	return ppuo
}

// AppendHashtags appends s to the "hashtags" field.
func (ppuo *PublishedPostUpdateOne) AppendHashtags(s []string) *PublishedPostUpdateOne {
	ppuo.mutation.AppendHashtags(s)
	return ppuo
}

// ClearHashtags clears the value of the "hashtags" field.
func (ppuo *PublishedPostUpdateOne) ClearHashtags() *PublishedPostUpdateOne {
	ppuo.mutation.ClearHashtags()
	return ppuo
}

// SetStatus sets the "status" field.
func (ppuo *PublishedPostUpdateOne) SetStatus(pu publishedpost.Status) *PublishedPostUpdateOne {
	ppuo.mutation.SetStatus(pu)
	return ppuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableStatus(pu *publishedpost.Status) *PublishedPostUpdateOne {
	if pu != nil {
		ppuo.SetStatus(*pu)
	}
	return ppuo
}

// SetErrorMessage sets the "error_message" field.
func (ppuo *PublishedPostUpdateOne) SetErrorMessage(s string) *PublishedPostUpdateOne {
	ppuo.mutation.SetErrorMessage(s)
	return ppuo
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableErrorMessage(s *string) *PublishedPostUpdateOne {
	if s != nil {
		ppuo.SetErrorMessage(*s)
	}
	return ppuo
}

// ClearErrorMessage clears the value of the "error_message" field.
func (ppuo *PublishedPostUpdateOne) ClearErrorMessage() *PublishedPostUpdateOne {
	ppuo.mutation.ClearErrorMessage()
	return ppuo
}

// SetScheduledAt sets the "scheduled_at" field.
func (ppuo *PublishedPostUpdateOne) SetScheduledAt(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetScheduledAt(t)
	return ppuo
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableScheduledAt(t *time.Time) *PublishedPostUpdateOne {
	if t != nil {
		ppuo.SetScheduledAt(*t)
	}
	return ppuo
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (ppuo *PublishedPostUpdateOne) ClearScheduledAt() *PublishedPostUpdateOne {
	ppuo.mutation.ClearScheduledAt()
	return ppuo
}

// SetPublishedAt sets the "published_at" field.
func (ppuo *PublishedPostUpdateOne) SetPublishedAt(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetPublishedAt(t)
	return ppuo
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillablePublishedAt(t *time.Time) *PublishedPostUpdateOne {
	if t != nil {
		ppuo.SetPublishedAt(*t)
	}
	return ppuo
}

// ClearPublishedAt clears the value of the "published_at" field.
func (ppuo *PublishedPostUpdateOne) ClearPublishedAt() *PublishedPostUpdateOne {
	ppuo.mutation.ClearPublishedAt()
	return ppuo
}

// SetPlatformResponse sets the "platform_response" field.
func (ppuo *PublishedPostUpdateOne) SetPlatformResponse(m map[string]interface{}) *PublishedPostUpdateOne {
	ppuo.mutation.SetPlatformResponse(m)
	return ppuo
}

// ClearPlatformResponse clears the value of the "platform_response" field.
func (ppuo *PublishedPostUpdateOne) ClearPlatformResponse() *PublishedPostUpdateOne {
	ppuo.mutation.ClearPlatformResponse()
	return ppuo
}

// SetAnalytics sets the "analytics" field.
func (ppuo *PublishedPostUpdateOne) SetAnalytics(m map[string]interface{}) *PublishedPostUpdateOne {
	ppuo.mutation.SetAnalytics(m)
	return ppuo
}

// ClearAnalytics clears the value of the "analytics" field.
func (ppuo *PublishedPostUpdateOne) ClearAnalytics() *PublishedPostUpdateOne {
	ppuo.mutation.ClearAnalytics()
	return ppuo
}

// SetLastAnalyticsSync sets the "last_analytics_sync" field.
func (ppuo *PublishedPostUpdateOne) SetLastAnalyticsSync(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetLastAnalyticsSync(t)
	return ppuo
}

// SetNillableLastAnalyticsSync sets the "last_analytics_sync" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableLastAnalyticsSync(t *time.Time) *PublishedPostUpdateOne {
	if t != nil {
		ppuo.SetLastAnalyticsSync(*t)
	}
	return ppuo
}

// ClearLastAnalyticsSync clears the value of the "last_analytics_sync" field.
func (ppuo *PublishedPostUpdateOne) ClearLastAnalyticsSync() *PublishedPostUpdateOne {
	ppuo.mutation.ClearLastAnalyticsSync()
	return ppuo
}

// SetRetryCount sets the "retry_count" field.
func (ppuo *PublishedPostUpdateOne) SetRetryCount(i int) *PublishedPostUpdateOne {
	ppuo.mutation.ResetRetryCount()
	ppuo.mutation.SetRetryCount(i)
	return ppuo
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableRetryCount(i *int) *PublishedPostUpdateOne {
	if i != nil {
		ppuo.SetRetryCount(*i)
	}
	return ppuo
}

// AddRetryCount adds i to the "retry_count" field.
func (ppuo *PublishedPostUpdateOne) AddRetryCount(i int) *PublishedPostUpdateOne {
	ppuo.mutation.AddRetryCount(i)
	return ppuo
}

// SetNextRetryAt sets the "next_retry_at" field.
func (ppuo *PublishedPostUpdateOne) SetNextRetryAt(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetNextRetryAt(t)
	return ppuo
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableNextRetryAt(t *time.Time) *PublishedPostUpdateOne {
	if t != nil {
		ppuo.SetNextRetryAt(*t)
	}
	return ppuo
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (ppuo *PublishedPostUpdateOne) ClearNextRetryAt() *PublishedPostUpdateOne {
	ppuo.mutation.ClearNextRetryAt()
	return ppuo
}

// SetPublishOptions sets the "publish_options" field.
func (ppuo *PublishedPostUpdateOne) SetPublishOptions(m map[string]interface{}) *PublishedPostUpdateOne {
	ppuo.mutation.SetPublishOptions(m)
	return ppuo
}

// ClearPublishOptions clears the value of the "publish_options" field.
func (ppuo *PublishedPostUpdateOne) ClearPublishOptions() *PublishedPostUpdateOne {
	ppuo.mutation.ClearPublishOptions()
	return ppuo
}

// SetMetadata sets the "metadata" field.
func (ppuo *PublishedPostUpdateOne) SetMetadata(m map[string]interface{}) *PublishedPostUpdateOne {
	ppuo.mutation.SetMetadata(m)
	return ppuo
}

// ClearMetadata clears the value of the "metadata" field.
func (ppuo *PublishedPostUpdateOne) ClearMetadata() *PublishedPostUpdateOne {
	ppuo.mutation.ClearMetadata()
	return ppuo
}

// SetUpdatedAt sets the "updated_at" field.
func (ppuo *PublishedPostUpdateOne) SetUpdatedAt(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetUpdatedAt(t)
	return ppuo
}

// SetDeletedAt sets the "deleted_at" field.
func (ppuo *PublishedPostUpdateOne) SetDeletedAt(t time.Time) *PublishedPostUpdateOne {
	ppuo.mutation.SetDeletedAt(t)
	return ppuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ppuo *PublishedPostUpdateOne) SetNillableDeletedAt(t *time.Time) *PublishedPostUpdateOne {
	if t != nil {
		ppuo.SetDeletedAt(*t)
	}
	return ppuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ppuo *PublishedPostUpdateOne) ClearDeletedAt() *PublishedPostUpdateOne {
	ppuo.mutation.ClearDeletedAt()
	return ppuo
}

// Mutation returns the PublishedPostMutation object of the builder.
func (ppuo *PublishedPostUpdateOne) Mutation() *PublishedPostMutation {
	return ppuo.mutation
}

// Where appends a list predicates to the PublishedPostUpdate builder.
func (ppuo *PublishedPostUpdateOne) Where(ps ...predicate.PublishedPost) *PublishedPostUpdateOne {
	ppuo.mutation.Where(ps...)
	return ppuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ppuo *PublishedPostUpdateOne) Select(field string, fields ...string) *PublishedPostUpdateOne {
	ppuo.fields = append([]string{field}, fields...)
	return ppuo
}

// Save executes the query and returns the updated PublishedPost entity.
func (ppuo *PublishedPostUpdateOne) Save(ctx context.Context) (*PublishedPost, error) {
	ppuo.defaults()
	return withHooks(ctx, ppuo.sqlSave, ppuo.mutation, ppuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ppuo *PublishedPostUpdateOne) SaveX(ctx context.Context) *PublishedPost {
	node, err := ppuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ppuo *PublishedPostUpdateOne) Exec(ctx context.Context) error {
	_, err := ppuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ppuo *PublishedPostUpdateOne) ExecX(ctx context.Context) {
	if err := ppuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ppuo *PublishedPostUpdateOne) defaults() {
	if _, ok := ppuo.mutation.UpdatedAt(); !ok {
		v := publishedpost.UpdateDefaultUpdatedAt()
		ppuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ppuo *PublishedPostUpdateOne) check() error {
	if v, ok := ppuo.mutation.PostID(); ok {
		if err := publishedpost.PostIDValidator(v); err != nil {
			return &ValidationError{Name: "post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.post_id": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.IntegrationID(); ok {
		if err := publishedpost.IntegrationIDValidator(v); err != nil {
			return &ValidationError{Name: "integration_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.integration_id": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.Platform(); ok {
		if err := publishedpost.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.PlatformPostID(); ok {
		if err := publishedpost.PlatformPostIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_id": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.PlatformPostURL(); ok {
		if err := publishedpost.PlatformPostURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_url", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_url": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.Status(); ok {
		if err := publishedpost.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.status": %w`, err)}
		}
	}
	if v, ok := ppuo.mutation.ErrorMessage(); ok {
		if err := publishedpost.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.error_message": %w`, err)}
		}
	}
	return nil
}

func (ppuo *PublishedPostUpdateOne) sqlSave(ctx context.Context) (_node *PublishedPost, err error) {
	if err := ppuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(publishedpost.Table, publishedpost.Columns, sqlgraph.NewFieldSpec(publishedpost.FieldID, field.TypeUUID))
	id, ok := ppuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "PublishedPost.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ppuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, publishedpost.FieldID)
		for _, f := range fields {
			if !publishedpost.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != publishedpost.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ppuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ppuo.mutation.UserID(); ok {
		_spec.SetField(publishedpost.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ppuo.mutation.PostID(); ok {
		_spec.SetField(publishedpost.FieldPostID, field.TypeString, value)
	}
	if value, ok := ppuo.mutation.IntegrationID(); ok {
		_spec.SetField(publishedpost.FieldIntegrationID, field.TypeString, value)
	}
	if value, ok := ppuo.mutation.Platform(); ok {
		_spec.SetField(publishedpost.FieldPlatform, field.TypeEnum, value)
	}
	if value, ok := ppuo.mutation.PlatformPostID(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostID, field.TypeString, value)
	}
	if value, ok := ppuo.mutation.PlatformPostURL(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostURL, field.TypeString, value)
	}
	if ppuo.mutation.PlatformPostURLCleared() {
		_spec.ClearField(publishedpost.FieldPlatformPostURL, field.TypeString)
	}
	if value, ok := ppuo.mutation.Content(); ok {
		_spec.SetField(publishedpost.FieldContent, field.TypeString, value)
	}
	if value, ok := ppuo.mutation.MediaUrls(); ok {
		_spec.SetField(publishedpost.FieldMediaUrls, field.TypeJSON, value)
	}
	if value, ok := ppuo.mutation.AppendedMediaUrls(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, publishedpost.FieldMediaUrls, value)
		})
	}
	if ppuo.mutation.MediaUrlsCleared() {
		_spec.ClearField(publishedpost.FieldMediaUrls, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.Hashtags(); ok {
		_spec.SetField(publishedpost.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := ppuo.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, publishedpost.FieldHashtags, value)
		})
	}
	if ppuo.mutation.HashtagsCleared() {
		_spec.ClearField(publishedpost.FieldHashtags, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.Status(); ok {
		_spec.SetField(publishedpost.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ppuo.mutation.ErrorMessage(); ok {
		_spec.SetField(publishedpost.FieldErrorMessage, field.TypeString, value)
	}
	if ppuo.mutation.ErrorMessageCleared() {
		_spec.ClearField(publishedpost.FieldErrorMessage, field.TypeString)
	}
	if value, ok := ppuo.mutation.ScheduledAt(); ok {
		_spec.SetField(publishedpost.FieldScheduledAt, field.TypeTime, value)
	}
	if ppuo.mutation.ScheduledAtCleared() {
		_spec.ClearField(publishedpost.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := ppuo.mutation.PublishedAt(); ok {
		_spec.SetField(publishedpost.FieldPublishedAt, field.TypeTime, value)
	}
	if ppuo.mutation.PublishedAtCleared() {
		_spec.ClearField(publishedpost.FieldPublishedAt, field.TypeTime)
	}
	if value, ok := ppuo.mutation.PlatformResponse(); ok {
		_spec.SetField(publishedpost.FieldPlatformResponse, field.TypeJSON, value)
	}
	if ppuo.mutation.PlatformResponseCleared() {
		_spec.ClearField(publishedpost.FieldPlatformResponse, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.Analytics(); ok {
		_spec.SetField(publishedpost.FieldAnalytics, field.TypeJSON, value)
	}
	if ppuo.mutation.AnalyticsCleared() {
		_spec.ClearField(publishedpost.FieldAnalytics, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.LastAnalyticsSync(); ok {
		_spec.SetField(publishedpost.FieldLastAnalyticsSync, field.TypeTime, value)
	}
	if ppuo.mutation.LastAnalyticsSyncCleared() {
		_spec.ClearField(publishedpost.FieldLastAnalyticsSync, field.TypeTime)
	}
	if value, ok := ppuo.mutation.RetryCount(); ok {
		_spec.SetField(publishedpost.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := ppuo.mutation.AddedRetryCount(); ok {
		_spec.AddField(publishedpost.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := ppuo.mutation.NextRetryAt(); ok {
		_spec.SetField(publishedpost.FieldNextRetryAt, field.TypeTime, value)
	}
	if ppuo.mutation.NextRetryAtCleared() {
		_spec.ClearField(publishedpost.FieldNextRetryAt, field.TypeTime)
	}
	if value, ok := ppuo.mutation.PublishOptions(); ok {
		_spec.SetField(publishedpost.FieldPublishOptions, field.TypeJSON, value)
	}
	if ppuo.mutation.PublishOptionsCleared() {
		_spec.ClearField(publishedpost.FieldPublishOptions, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.Metadata(); ok {
		_spec.SetField(publishedpost.FieldMetadata, field.TypeJSON, value)
	}
	if ppuo.mutation.MetadataCleared() {
		_spec.ClearField(publishedpost.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ppuo.mutation.UpdatedAt(); ok {
		_spec.SetField(publishedpost.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ppuo.mutation.DeletedAt(); ok {
		_spec.SetField(publishedpost.FieldDeletedAt, field.TypeTime, value)
	}
	if ppuo.mutation.DeletedAtCleared() {
		_spec.ClearField(publishedpost.FieldDeletedAt, field.TypeTime)
	}
	_node = &PublishedPost{config: ppuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ppuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{publishedpost.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ppuo.mutation.done = true
	return _node, nil
}

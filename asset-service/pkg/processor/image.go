package processor

import (
	"bytes"
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"path/filepath"
	"strings"

	"github.com/disintegration/imaging"
	"golang.org/x/image/webp"
)

// ImageProcessor handles image processing operations
type ImageProcessor struct {
	maxWidth  int
	maxHeight int
	quality   int
}

// NewImageProcessor creates a new image processor
func NewImageProcessor(maxWidth, maxHeight, quality int) *ImageProcessor {
	return &ImageProcessor{
		maxWidth:  maxWidth,
		maxHeight: maxHeight,
		quality:   quality,
	}
}

// ProcessedImage represents a processed image
type ProcessedImage struct {
	Data        []byte
	ContentType string
	Width       int
	Height      int
	Size        int64
}

// ThumbnailSizes defines standard thumbnail sizes
var ThumbnailSizes = map[string]struct {
	Width  int
	Height int
}{
	"small":  {Width: 150, Height: 150},
	"medium": {Width: 300, Height: 300},
	"large":  {Width: 600, Height: 600},
}

// ProcessImage processes an image with resizing and optimization
func (p *ImageProcessor) ProcessImage(data []byte, contentType string) (*ProcessedImage, error) {
	// Decode image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Get original dimensions
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// Resize if needed
	if originalWidth > p.maxWidth || originalHeight > p.maxHeight {
		img = imaging.Fit(img, p.maxWidth, p.maxHeight, imaging.Lanczos)
	}

	// Encode processed image
	processedData, processedContentType, err := p.encodeImage(img, format)
	if err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	// Get new dimensions
	newBounds := img.Bounds()

	return &ProcessedImage{
		Data:        processedData,
		ContentType: processedContentType,
		Width:       newBounds.Dx(),
		Height:      newBounds.Dy(),
		Size:        int64(len(processedData)),
	}, nil
}

// GenerateThumbnails generates multiple thumbnail sizes
func (p *ImageProcessor) GenerateThumbnails(data []byte, contentType string) (map[string]*ProcessedImage, error) {
	// Decode original image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	thumbnails := make(map[string]*ProcessedImage)

	// Generate each thumbnail size
	for sizeName, size := range ThumbnailSizes {
		// Resize image
		thumbnail := imaging.Fit(img, size.Width, size.Height, imaging.Lanczos)

		// Encode thumbnail
		thumbnailData, thumbnailContentType, err := p.encodeImage(thumbnail, format)
		if err != nil {
			return nil, fmt.Errorf("failed to encode thumbnail %s: %w", sizeName, err)
		}

		// Get thumbnail dimensions
		bounds := thumbnail.Bounds()

		thumbnails[sizeName] = &ProcessedImage{
			Data:        thumbnailData,
			ContentType: thumbnailContentType,
			Width:       bounds.Dx(),
			Height:      bounds.Dy(),
			Size:        int64(len(thumbnailData)),
		}
	}

	return thumbnails, nil
}

// OptimizeImage optimizes an image for web usage
func (p *ImageProcessor) OptimizeImage(data []byte, contentType string, targetSize int64) (*ProcessedImage, error) {
	// Decode image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Start with high quality
	quality := p.quality
	var processedData []byte
	var processedContentType string

	// Reduce quality until target size is reached or minimum quality is hit
	for quality >= 20 {
		processedData, processedContentType, err = p.encodeImageWithQuality(img, format, quality)
		if err != nil {
			return nil, fmt.Errorf("failed to encode image: %w", err)
		}

		if int64(len(processedData)) <= targetSize {
			break
		}

		quality -= 10
	}

	// Get dimensions
	bounds := img.Bounds()

	return &ProcessedImage{
		Data:        processedData,
		ContentType: processedContentType,
		Width:       bounds.Dx(),
		Height:      bounds.Dy(),
		Size:        int64(len(processedData)),
	}, nil
}

// ConvertFormat converts image to a different format
func (p *ImageProcessor) ConvertFormat(data []byte, contentType, targetFormat string) (*ProcessedImage, error) {
	// Decode image
	img, _, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Encode in target format
	processedData, processedContentType, err := p.encodeImage(img, targetFormat)
	if err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	// Get dimensions
	bounds := img.Bounds()

	return &ProcessedImage{
		Data:        processedData,
		ContentType: processedContentType,
		Width:       bounds.Dx(),
		Height:      bounds.Dy(),
		Size:        int64(len(processedData)),
	}, nil
}

// GetImageInfo extracts image information without processing
func (p *ImageProcessor) GetImageInfo(data []byte, contentType string) (*ImageInfo, error) {
	// Decode image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	bounds := img.Bounds()

	return &ImageInfo{
		Width:       bounds.Dx(),
		Height:      bounds.Dy(),
		Format:      format,
		ContentType: contentType,
		Size:        int64(len(data)),
	}, nil
}

// ImageInfo represents image metadata
type ImageInfo struct {
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Format      string `json:"format"`
	ContentType string `json:"content_type"`
	Size        int64  `json:"size"`
}

// decodeImage decodes image from bytes
func (p *ImageProcessor) decodeImage(data []byte, contentType string) (image.Image, string, error) {
	reader := bytes.NewReader(data)

	switch contentType {
	case "image/jpeg", "image/jpg":
		img, err := jpeg.Decode(reader)
		return img, "jpeg", err
	case "image/png":
		img, err := png.Decode(reader)
		return img, "png", err
	case "image/gif":
		img, err := gif.Decode(reader)
		return img, "gif", err
	case "image/webp":
		img, err := webp.Decode(reader)
		return img, "webp", err
	default:
		// Try to auto-detect format
		reader.Seek(0, 0)
		img, format, err := image.Decode(reader)
		return img, format, err
	}
}

// encodeImage encodes image to bytes
func (p *ImageProcessor) encodeImage(img image.Image, format string) ([]byte, string, error) {
	return p.encodeImageWithQuality(img, format, p.quality)
}

// encodeImageWithQuality encodes image with specific quality
func (p *ImageProcessor) encodeImageWithQuality(img image.Image, format string, quality int) ([]byte, string, error) {
	var buf bytes.Buffer

	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		return buf.Bytes(), "image/jpeg", err
	case "png":
		err := png.Encode(&buf, img)
		return buf.Bytes(), "image/png", err
	case "gif":
		err := gif.Encode(&buf, img, nil)
		return buf.Bytes(), "image/gif", err
	default:
		// Default to JPEG
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		return buf.Bytes(), "image/jpeg", err
	}
}

// IsImageFile checks if file is a supported image format
func IsImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	supportedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	for _, supportedExt := range supportedExts {
		if ext == supportedExt {
			return true
		}
	}
	return false
}

// GetImageContentType returns content type based on file extension
func GetImageContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// ValidateImageSize validates image dimensions and file size
func (p *ImageProcessor) ValidateImageSize(data []byte, maxFileSize int64) error {
	if int64(len(data)) > maxFileSize {
		return fmt.Errorf("file size %d exceeds maximum allowed size %d", len(data), maxFileSize)
	}

	// Try to decode to validate it's a valid image
	_, _, err := image.DecodeConfig(bytes.NewReader(data))
	if err != nil {
		return fmt.Errorf("invalid image format: %w", err)
	}

	return nil
}

// CropImage crops image to specified dimensions
func (p *ImageProcessor) CropImage(data []byte, contentType string, x, y, width, height int) (*ProcessedImage, error) {
	// Decode image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Crop image
	croppedImg := imaging.Crop(img, image.Rect(x, y, x+width, y+height))

	// Encode cropped image
	processedData, processedContentType, err := p.encodeImage(croppedImg, format)
	if err != nil {
		return nil, fmt.Errorf("failed to encode cropped image: %w", err)
	}

	return &ProcessedImage{
		Data:        processedData,
		ContentType: processedContentType,
		Width:       width,
		Height:      height,
		Size:        int64(len(processedData)),
	}, nil
}

// RotateImage rotates image by specified angle
func (p *ImageProcessor) RotateImage(data []byte, contentType string, angle float64) (*ProcessedImage, error) {
	// Decode image
	img, format, err := p.decodeImage(data, contentType)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Rotate image
	rotatedImg := imaging.Rotate(img, angle, image.Transparent)

	// Encode rotated image
	processedData, processedContentType, err := p.encodeImage(rotatedImg, format)
	if err != nil {
		return nil, fmt.Errorf("failed to encode rotated image: %w", err)
	}

	// Get new dimensions
	bounds := rotatedImg.Bounds()

	return &ProcessedImage{
		Data:        processedData,
		ContentType: processedContentType,
		Width:       bounds.Dx(),
		Height:      bounds.Dy(),
		Size:        int64(len(processedData)),
	}, nil
}

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # User Service
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:50051']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # AI Content Service
  - job_name: 'ai-content-service'
    static_configs:
      - targets: ['ai-content-service:50052']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Content Management Service
  - job_name: 'content-mgmt-service'
    static_configs:
      - targets: ['content-mgmt-service:50053']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Credit Service
  - job_name: 'credit-service'
    static_configs:
      - targets: ['credit-service:50054']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Asset Service
  - job_name: 'asset-service'
    static_configs:
      - targets: ['asset-service:50055']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Integration Service
  - job_name: 'integration-service'
    static_configs:
      - targets: ['integration-service:50056']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Notification Service
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:50057']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Analytics Service
  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:50058']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Kafka Exporter
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-exporter:9308']
    scrape_interval: 30s

  # MinIO Exporter
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 30s

  # Node Exporter (for system metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor (for container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

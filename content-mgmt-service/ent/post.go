// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
)

// Post is the model entity for the Post schema.
type Post struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// WorkspaceID holds the value of the "workspace_id" field.
	WorkspaceID string `json:"workspace_id,omitempty"`
	// TemplateID holds the value of the "template_id" field.
	TemplateID string `json:"template_id,omitempty"`
	// GenerationID holds the value of the "generation_id" field.
	GenerationID string `json:"generation_id,omitempty"`
	// Title holds the value of the "title" field.
	Title string `json:"title,omitempty"`
	// Content holds the value of the "content" field.
	Content string `json:"content,omitempty"`
	// Content before any improvements
	OriginalContent string `json:"original_content,omitempty"`
	// Target platforms for this post
	Platforms []string `json:"platforms,omitempty"`
	// Status holds the value of the "status" field.
	Status post.Status `json:"status,omitempty"`
	// When the post is scheduled to be published
	ScheduledAt time.Time `json:"scheduled_at,omitempty"`
	// When the post was actually published
	PublishedAt time.Time `json:"published_at,omitempty"`
	// Platform-specific post IDs and metadata
	PlatformPosts map[string]interface{} `json:"platform_posts,omitempty"`
	// Hashtags holds the value of the "hashtags" field.
	Hashtags []string `json:"hashtags,omitempty"`
	// Mentions holds the value of the "mentions" field.
	Mentions []string `json:"mentions,omitempty"`
	// URLs of attached media files
	MediaUrls []string `json:"media_urls,omitempty"`
	// Template variables used
	Variables map[string]string `json:"variables,omitempty"`
	// Visibility holds the value of the "visibility" field.
	Visibility post.Visibility `json:"visibility,omitempty"`
	// Content language code
	Language string `json:"language,omitempty"`
	// Post performance analytics
	Analytics map[string]interface{} `json:"analytics,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Post) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case post.FieldPlatforms, post.FieldPlatformPosts, post.FieldHashtags, post.FieldMentions, post.FieldMediaUrls, post.FieldVariables, post.FieldAnalytics, post.FieldMetadata:
			values[i] = new([]byte)
		case post.FieldWorkspaceID, post.FieldTemplateID, post.FieldGenerationID, post.FieldTitle, post.FieldContent, post.FieldOriginalContent, post.FieldStatus, post.FieldVisibility, post.FieldLanguage:
			values[i] = new(sql.NullString)
		case post.FieldScheduledAt, post.FieldPublishedAt, post.FieldCreatedAt, post.FieldUpdatedAt, post.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case post.FieldID, post.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Post fields.
func (po *Post) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case post.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				po.ID = *value
			}
		case post.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				po.UserID = *value
			}
		case post.FieldWorkspaceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field workspace_id", values[i])
			} else if value.Valid {
				po.WorkspaceID = value.String
			}
		case post.FieldTemplateID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field template_id", values[i])
			} else if value.Valid {
				po.TemplateID = value.String
			}
		case post.FieldGenerationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field generation_id", values[i])
			} else if value.Valid {
				po.GenerationID = value.String
			}
		case post.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				po.Title = value.String
			}
		case post.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				po.Content = value.String
			}
		case post.FieldOriginalContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field original_content", values[i])
			} else if value.Valid {
				po.OriginalContent = value.String
			}
		case post.FieldPlatforms:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field platforms", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Platforms); err != nil {
					return fmt.Errorf("unmarshal field platforms: %w", err)
				}
			}
		case post.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				po.Status = post.Status(value.String)
			}
		case post.FieldScheduledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field scheduled_at", values[i])
			} else if value.Valid {
				po.ScheduledAt = value.Time
			}
		case post.FieldPublishedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field published_at", values[i])
			} else if value.Valid {
				po.PublishedAt = value.Time
			}
		case post.FieldPlatformPosts:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field platform_posts", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.PlatformPosts); err != nil {
					return fmt.Errorf("unmarshal field platform_posts: %w", err)
				}
			}
		case post.FieldHashtags:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field hashtags", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Hashtags); err != nil {
					return fmt.Errorf("unmarshal field hashtags: %w", err)
				}
			}
		case post.FieldMentions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field mentions", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Mentions); err != nil {
					return fmt.Errorf("unmarshal field mentions: %w", err)
				}
			}
		case post.FieldMediaUrls:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field media_urls", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.MediaUrls); err != nil {
					return fmt.Errorf("unmarshal field media_urls: %w", err)
				}
			}
		case post.FieldVariables:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field variables", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Variables); err != nil {
					return fmt.Errorf("unmarshal field variables: %w", err)
				}
			}
		case post.FieldVisibility:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field visibility", values[i])
			} else if value.Valid {
				po.Visibility = post.Visibility(value.String)
			}
		case post.FieldLanguage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field language", values[i])
			} else if value.Valid {
				po.Language = value.String
			}
		case post.FieldAnalytics:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field analytics", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Analytics); err != nil {
					return fmt.Errorf("unmarshal field analytics: %w", err)
				}
			}
		case post.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &po.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case post.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				po.CreatedAt = value.Time
			}
		case post.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				po.UpdatedAt = value.Time
			}
		case post.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				po.DeletedAt = value.Time
			}
		default:
			po.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Post.
// This includes values selected through modifiers, order, etc.
func (po *Post) Value(name string) (ent.Value, error) {
	return po.selectValues.Get(name)
}

// Update returns a builder for updating this Post.
// Note that you need to call Post.Unwrap() before calling this method if this Post
// was returned from a transaction, and the transaction was committed or rolled back.
func (po *Post) Update() *PostUpdateOne {
	return NewPostClient(po.config).UpdateOne(po)
}

// Unwrap unwraps the Post entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (po *Post) Unwrap() *Post {
	_tx, ok := po.config.driver.(*txDriver)
	if !ok {
		panic("ent: Post is not a transactional entity")
	}
	po.config.driver = _tx.drv
	return po
}

// String implements the fmt.Stringer.
func (po *Post) String() string {
	var builder strings.Builder
	builder.WriteString("Post(")
	builder.WriteString(fmt.Sprintf("id=%v, ", po.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", po.UserID))
	builder.WriteString(", ")
	builder.WriteString("workspace_id=")
	builder.WriteString(po.WorkspaceID)
	builder.WriteString(", ")
	builder.WriteString("template_id=")
	builder.WriteString(po.TemplateID)
	builder.WriteString(", ")
	builder.WriteString("generation_id=")
	builder.WriteString(po.GenerationID)
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(po.Title)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(po.Content)
	builder.WriteString(", ")
	builder.WriteString("original_content=")
	builder.WriteString(po.OriginalContent)
	builder.WriteString(", ")
	builder.WriteString("platforms=")
	builder.WriteString(fmt.Sprintf("%v", po.Platforms))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", po.Status))
	builder.WriteString(", ")
	builder.WriteString("scheduled_at=")
	builder.WriteString(po.ScheduledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("published_at=")
	builder.WriteString(po.PublishedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("platform_posts=")
	builder.WriteString(fmt.Sprintf("%v", po.PlatformPosts))
	builder.WriteString(", ")
	builder.WriteString("hashtags=")
	builder.WriteString(fmt.Sprintf("%v", po.Hashtags))
	builder.WriteString(", ")
	builder.WriteString("mentions=")
	builder.WriteString(fmt.Sprintf("%v", po.Mentions))
	builder.WriteString(", ")
	builder.WriteString("media_urls=")
	builder.WriteString(fmt.Sprintf("%v", po.MediaUrls))
	builder.WriteString(", ")
	builder.WriteString("variables=")
	builder.WriteString(fmt.Sprintf("%v", po.Variables))
	builder.WriteString(", ")
	builder.WriteString("visibility=")
	builder.WriteString(fmt.Sprintf("%v", po.Visibility))
	builder.WriteString(", ")
	builder.WriteString("language=")
	builder.WriteString(po.Language)
	builder.WriteString(", ")
	builder.WriteString("analytics=")
	builder.WriteString(fmt.Sprintf("%v", po.Analytics))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", po.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(po.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(po.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(po.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Posts is a parsable slice of Post.
type Posts []*Post

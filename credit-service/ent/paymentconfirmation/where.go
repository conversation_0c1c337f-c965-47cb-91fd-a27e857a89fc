// Code generated by ent, DO NOT EDIT.

package paymentconfirmation

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldID, id))
}

// BankTransferID applies equality check predicate on the "bank_transfer_id" field. It's identical to BankTransferIDEQ.
func BankTransferID(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldBankTransferID, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldOrderID, v))
}

// SubscriptionID applies equality check predicate on the "subscription_id" field. It's identical to SubscriptionIDEQ.
func SubscriptionID(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldSubscriptionID, v))
}

// PaymentMethod applies equality check predicate on the "payment_method" field. It's identical to PaymentMethodEQ.
func PaymentMethod(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldPaymentMethod, v))
}

// ExternalPaymentID applies equality check predicate on the "external_payment_id" field. It's identical to ExternalPaymentIDEQ.
func ExternalPaymentID(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldExternalPaymentID, v))
}

// BankTransactionID applies equality check predicate on the "bank_transaction_id" field. It's identical to BankTransactionIDEQ.
func BankTransactionID(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldBankTransactionID, v))
}

// AmountPaid applies equality check predicate on the "amount_paid" field. It's identical to AmountPaidEQ.
func AmountPaid(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldAmountPaid, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldCurrency, v))
}

// ConfirmedBy applies equality check predicate on the "confirmed_by" field. It's identical to ConfirmedByEQ.
func ConfirmedBy(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmedBy, v))
}

// ConfirmationMethod applies equality check predicate on the "confirmation_method" field. It's identical to ConfirmationMethodEQ.
func ConfirmationMethod(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmationMethod, v))
}

// ConfirmationNotes applies equality check predicate on the "confirmation_notes" field. It's identical to ConfirmationNotesEQ.
func ConfirmationNotes(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmationNotes, v))
}

// PaymentDate applies equality check predicate on the "payment_date" field. It's identical to PaymentDateEQ.
func PaymentDate(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldPaymentDate, v))
}

// ConfirmedAt applies equality check predicate on the "confirmed_at" field. It's identical to ConfirmedAtEQ.
func ConfirmedAt(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmedAt, v))
}

// FailureReason applies equality check predicate on the "failure_reason" field. It's identical to FailureReasonEQ.
func FailureReason(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldFailureReason, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldUpdatedAt, v))
}

// BankTransferIDEQ applies the EQ predicate on the "bank_transfer_id" field.
func BankTransferIDEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldBankTransferID, v))
}

// BankTransferIDNEQ applies the NEQ predicate on the "bank_transfer_id" field.
func BankTransferIDNEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldBankTransferID, v))
}

// BankTransferIDIn applies the In predicate on the "bank_transfer_id" field.
func BankTransferIDIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldBankTransferID, vs...))
}

// BankTransferIDNotIn applies the NotIn predicate on the "bank_transfer_id" field.
func BankTransferIDNotIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldBankTransferID, vs...))
}

// BankTransferIDGT applies the GT predicate on the "bank_transfer_id" field.
func BankTransferIDGT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldBankTransferID, v))
}

// BankTransferIDGTE applies the GTE predicate on the "bank_transfer_id" field.
func BankTransferIDGTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldBankTransferID, v))
}

// BankTransferIDLT applies the LT predicate on the "bank_transfer_id" field.
func BankTransferIDLT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldBankTransferID, v))
}

// BankTransferIDLTE applies the LTE predicate on the "bank_transfer_id" field.
func BankTransferIDLTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldBankTransferID, v))
}

// BankTransferIDIsNil applies the IsNil predicate on the "bank_transfer_id" field.
func BankTransferIDIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldBankTransferID))
}

// BankTransferIDNotNil applies the NotNil predicate on the "bank_transfer_id" field.
func BankTransferIDNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldBankTransferID))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldOrderID))
}

// SubscriptionIDEQ applies the EQ predicate on the "subscription_id" field.
func SubscriptionIDEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldSubscriptionID, v))
}

// SubscriptionIDNEQ applies the NEQ predicate on the "subscription_id" field.
func SubscriptionIDNEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldSubscriptionID, v))
}

// SubscriptionIDIn applies the In predicate on the "subscription_id" field.
func SubscriptionIDIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldSubscriptionID, vs...))
}

// SubscriptionIDNotIn applies the NotIn predicate on the "subscription_id" field.
func SubscriptionIDNotIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldSubscriptionID, vs...))
}

// SubscriptionIDGT applies the GT predicate on the "subscription_id" field.
func SubscriptionIDGT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldSubscriptionID, v))
}

// SubscriptionIDGTE applies the GTE predicate on the "subscription_id" field.
func SubscriptionIDGTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldSubscriptionID, v))
}

// SubscriptionIDLT applies the LT predicate on the "subscription_id" field.
func SubscriptionIDLT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldSubscriptionID, v))
}

// SubscriptionIDLTE applies the LTE predicate on the "subscription_id" field.
func SubscriptionIDLTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldSubscriptionID, v))
}

// SubscriptionIDIsNil applies the IsNil predicate on the "subscription_id" field.
func SubscriptionIDIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldSubscriptionID))
}

// SubscriptionIDNotNil applies the NotNil predicate on the "subscription_id" field.
func SubscriptionIDNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldSubscriptionID))
}

// PaymentMethodEQ applies the EQ predicate on the "payment_method" field.
func PaymentMethodEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentMethodNEQ applies the NEQ predicate on the "payment_method" field.
func PaymentMethodNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldPaymentMethod, v))
}

// PaymentMethodIn applies the In predicate on the "payment_method" field.
func PaymentMethodIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldPaymentMethod, vs...))
}

// PaymentMethodNotIn applies the NotIn predicate on the "payment_method" field.
func PaymentMethodNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldPaymentMethod, vs...))
}

// PaymentMethodGT applies the GT predicate on the "payment_method" field.
func PaymentMethodGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldPaymentMethod, v))
}

// PaymentMethodGTE applies the GTE predicate on the "payment_method" field.
func PaymentMethodGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldPaymentMethod, v))
}

// PaymentMethodLT applies the LT predicate on the "payment_method" field.
func PaymentMethodLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldPaymentMethod, v))
}

// PaymentMethodLTE applies the LTE predicate on the "payment_method" field.
func PaymentMethodLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldPaymentMethod, v))
}

// PaymentMethodContains applies the Contains predicate on the "payment_method" field.
func PaymentMethodContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldPaymentMethod, v))
}

// PaymentMethodHasPrefix applies the HasPrefix predicate on the "payment_method" field.
func PaymentMethodHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldPaymentMethod, v))
}

// PaymentMethodHasSuffix applies the HasSuffix predicate on the "payment_method" field.
func PaymentMethodHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldPaymentMethod, v))
}

// PaymentMethodEqualFold applies the EqualFold predicate on the "payment_method" field.
func PaymentMethodEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldPaymentMethod, v))
}

// PaymentMethodContainsFold applies the ContainsFold predicate on the "payment_method" field.
func PaymentMethodContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldPaymentMethod, v))
}

// ExternalPaymentIDEQ applies the EQ predicate on the "external_payment_id" field.
func ExternalPaymentIDEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldExternalPaymentID, v))
}

// ExternalPaymentIDNEQ applies the NEQ predicate on the "external_payment_id" field.
func ExternalPaymentIDNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldExternalPaymentID, v))
}

// ExternalPaymentIDIn applies the In predicate on the "external_payment_id" field.
func ExternalPaymentIDIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldExternalPaymentID, vs...))
}

// ExternalPaymentIDNotIn applies the NotIn predicate on the "external_payment_id" field.
func ExternalPaymentIDNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldExternalPaymentID, vs...))
}

// ExternalPaymentIDGT applies the GT predicate on the "external_payment_id" field.
func ExternalPaymentIDGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldExternalPaymentID, v))
}

// ExternalPaymentIDGTE applies the GTE predicate on the "external_payment_id" field.
func ExternalPaymentIDGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldExternalPaymentID, v))
}

// ExternalPaymentIDLT applies the LT predicate on the "external_payment_id" field.
func ExternalPaymentIDLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldExternalPaymentID, v))
}

// ExternalPaymentIDLTE applies the LTE predicate on the "external_payment_id" field.
func ExternalPaymentIDLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldExternalPaymentID, v))
}

// ExternalPaymentIDContains applies the Contains predicate on the "external_payment_id" field.
func ExternalPaymentIDContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldExternalPaymentID, v))
}

// ExternalPaymentIDHasPrefix applies the HasPrefix predicate on the "external_payment_id" field.
func ExternalPaymentIDHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldExternalPaymentID, v))
}

// ExternalPaymentIDHasSuffix applies the HasSuffix predicate on the "external_payment_id" field.
func ExternalPaymentIDHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldExternalPaymentID, v))
}

// ExternalPaymentIDIsNil applies the IsNil predicate on the "external_payment_id" field.
func ExternalPaymentIDIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldExternalPaymentID))
}

// ExternalPaymentIDNotNil applies the NotNil predicate on the "external_payment_id" field.
func ExternalPaymentIDNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldExternalPaymentID))
}

// ExternalPaymentIDEqualFold applies the EqualFold predicate on the "external_payment_id" field.
func ExternalPaymentIDEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldExternalPaymentID, v))
}

// ExternalPaymentIDContainsFold applies the ContainsFold predicate on the "external_payment_id" field.
func ExternalPaymentIDContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldExternalPaymentID, v))
}

// BankTransactionIDEQ applies the EQ predicate on the "bank_transaction_id" field.
func BankTransactionIDEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldBankTransactionID, v))
}

// BankTransactionIDNEQ applies the NEQ predicate on the "bank_transaction_id" field.
func BankTransactionIDNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldBankTransactionID, v))
}

// BankTransactionIDIn applies the In predicate on the "bank_transaction_id" field.
func BankTransactionIDIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldBankTransactionID, vs...))
}

// BankTransactionIDNotIn applies the NotIn predicate on the "bank_transaction_id" field.
func BankTransactionIDNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldBankTransactionID, vs...))
}

// BankTransactionIDGT applies the GT predicate on the "bank_transaction_id" field.
func BankTransactionIDGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldBankTransactionID, v))
}

// BankTransactionIDGTE applies the GTE predicate on the "bank_transaction_id" field.
func BankTransactionIDGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldBankTransactionID, v))
}

// BankTransactionIDLT applies the LT predicate on the "bank_transaction_id" field.
func BankTransactionIDLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldBankTransactionID, v))
}

// BankTransactionIDLTE applies the LTE predicate on the "bank_transaction_id" field.
func BankTransactionIDLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldBankTransactionID, v))
}

// BankTransactionIDContains applies the Contains predicate on the "bank_transaction_id" field.
func BankTransactionIDContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldBankTransactionID, v))
}

// BankTransactionIDHasPrefix applies the HasPrefix predicate on the "bank_transaction_id" field.
func BankTransactionIDHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldBankTransactionID, v))
}

// BankTransactionIDHasSuffix applies the HasSuffix predicate on the "bank_transaction_id" field.
func BankTransactionIDHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldBankTransactionID, v))
}

// BankTransactionIDIsNil applies the IsNil predicate on the "bank_transaction_id" field.
func BankTransactionIDIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldBankTransactionID))
}

// BankTransactionIDNotNil applies the NotNil predicate on the "bank_transaction_id" field.
func BankTransactionIDNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldBankTransactionID))
}

// BankTransactionIDEqualFold applies the EqualFold predicate on the "bank_transaction_id" field.
func BankTransactionIDEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldBankTransactionID, v))
}

// BankTransactionIDContainsFold applies the ContainsFold predicate on the "bank_transaction_id" field.
func BankTransactionIDContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldBankTransactionID, v))
}

// AmountPaidEQ applies the EQ predicate on the "amount_paid" field.
func AmountPaidEQ(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldAmountPaid, v))
}

// AmountPaidNEQ applies the NEQ predicate on the "amount_paid" field.
func AmountPaidNEQ(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldAmountPaid, v))
}

// AmountPaidIn applies the In predicate on the "amount_paid" field.
func AmountPaidIn(vs ...int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldAmountPaid, vs...))
}

// AmountPaidNotIn applies the NotIn predicate on the "amount_paid" field.
func AmountPaidNotIn(vs ...int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldAmountPaid, vs...))
}

// AmountPaidGT applies the GT predicate on the "amount_paid" field.
func AmountPaidGT(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldAmountPaid, v))
}

// AmountPaidGTE applies the GTE predicate on the "amount_paid" field.
func AmountPaidGTE(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldAmountPaid, v))
}

// AmountPaidLT applies the LT predicate on the "amount_paid" field.
func AmountPaidLT(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldAmountPaid, v))
}

// AmountPaidLTE applies the LTE predicate on the "amount_paid" field.
func AmountPaidLTE(v int64) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldAmountPaid, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldCurrency, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldStatus, vs...))
}

// ConfirmedByEQ applies the EQ predicate on the "confirmed_by" field.
func ConfirmedByEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmedBy, v))
}

// ConfirmedByNEQ applies the NEQ predicate on the "confirmed_by" field.
func ConfirmedByNEQ(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldConfirmedBy, v))
}

// ConfirmedByIn applies the In predicate on the "confirmed_by" field.
func ConfirmedByIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldConfirmedBy, vs...))
}

// ConfirmedByNotIn applies the NotIn predicate on the "confirmed_by" field.
func ConfirmedByNotIn(vs ...uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldConfirmedBy, vs...))
}

// ConfirmedByGT applies the GT predicate on the "confirmed_by" field.
func ConfirmedByGT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldConfirmedBy, v))
}

// ConfirmedByGTE applies the GTE predicate on the "confirmed_by" field.
func ConfirmedByGTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldConfirmedBy, v))
}

// ConfirmedByLT applies the LT predicate on the "confirmed_by" field.
func ConfirmedByLT(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldConfirmedBy, v))
}

// ConfirmedByLTE applies the LTE predicate on the "confirmed_by" field.
func ConfirmedByLTE(v uuid.UUID) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldConfirmedBy, v))
}

// ConfirmedByIsNil applies the IsNil predicate on the "confirmed_by" field.
func ConfirmedByIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldConfirmedBy))
}

// ConfirmedByNotNil applies the NotNil predicate on the "confirmed_by" field.
func ConfirmedByNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldConfirmedBy))
}

// ConfirmationMethodEQ applies the EQ predicate on the "confirmation_method" field.
func ConfirmationMethodEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmationMethod, v))
}

// ConfirmationMethodNEQ applies the NEQ predicate on the "confirmation_method" field.
func ConfirmationMethodNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldConfirmationMethod, v))
}

// ConfirmationMethodIn applies the In predicate on the "confirmation_method" field.
func ConfirmationMethodIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldConfirmationMethod, vs...))
}

// ConfirmationMethodNotIn applies the NotIn predicate on the "confirmation_method" field.
func ConfirmationMethodNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldConfirmationMethod, vs...))
}

// ConfirmationMethodGT applies the GT predicate on the "confirmation_method" field.
func ConfirmationMethodGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldConfirmationMethod, v))
}

// ConfirmationMethodGTE applies the GTE predicate on the "confirmation_method" field.
func ConfirmationMethodGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldConfirmationMethod, v))
}

// ConfirmationMethodLT applies the LT predicate on the "confirmation_method" field.
func ConfirmationMethodLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldConfirmationMethod, v))
}

// ConfirmationMethodLTE applies the LTE predicate on the "confirmation_method" field.
func ConfirmationMethodLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldConfirmationMethod, v))
}

// ConfirmationMethodContains applies the Contains predicate on the "confirmation_method" field.
func ConfirmationMethodContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldConfirmationMethod, v))
}

// ConfirmationMethodHasPrefix applies the HasPrefix predicate on the "confirmation_method" field.
func ConfirmationMethodHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldConfirmationMethod, v))
}

// ConfirmationMethodHasSuffix applies the HasSuffix predicate on the "confirmation_method" field.
func ConfirmationMethodHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldConfirmationMethod, v))
}

// ConfirmationMethodEqualFold applies the EqualFold predicate on the "confirmation_method" field.
func ConfirmationMethodEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldConfirmationMethod, v))
}

// ConfirmationMethodContainsFold applies the ContainsFold predicate on the "confirmation_method" field.
func ConfirmationMethodContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldConfirmationMethod, v))
}

// ConfirmationNotesEQ applies the EQ predicate on the "confirmation_notes" field.
func ConfirmationNotesEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmationNotes, v))
}

// ConfirmationNotesNEQ applies the NEQ predicate on the "confirmation_notes" field.
func ConfirmationNotesNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldConfirmationNotes, v))
}

// ConfirmationNotesIn applies the In predicate on the "confirmation_notes" field.
func ConfirmationNotesIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldConfirmationNotes, vs...))
}

// ConfirmationNotesNotIn applies the NotIn predicate on the "confirmation_notes" field.
func ConfirmationNotesNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldConfirmationNotes, vs...))
}

// ConfirmationNotesGT applies the GT predicate on the "confirmation_notes" field.
func ConfirmationNotesGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldConfirmationNotes, v))
}

// ConfirmationNotesGTE applies the GTE predicate on the "confirmation_notes" field.
func ConfirmationNotesGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldConfirmationNotes, v))
}

// ConfirmationNotesLT applies the LT predicate on the "confirmation_notes" field.
func ConfirmationNotesLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldConfirmationNotes, v))
}

// ConfirmationNotesLTE applies the LTE predicate on the "confirmation_notes" field.
func ConfirmationNotesLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldConfirmationNotes, v))
}

// ConfirmationNotesContains applies the Contains predicate on the "confirmation_notes" field.
func ConfirmationNotesContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldConfirmationNotes, v))
}

// ConfirmationNotesHasPrefix applies the HasPrefix predicate on the "confirmation_notes" field.
func ConfirmationNotesHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldConfirmationNotes, v))
}

// ConfirmationNotesHasSuffix applies the HasSuffix predicate on the "confirmation_notes" field.
func ConfirmationNotesHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldConfirmationNotes, v))
}

// ConfirmationNotesIsNil applies the IsNil predicate on the "confirmation_notes" field.
func ConfirmationNotesIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldConfirmationNotes))
}

// ConfirmationNotesNotNil applies the NotNil predicate on the "confirmation_notes" field.
func ConfirmationNotesNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldConfirmationNotes))
}

// ConfirmationNotesEqualFold applies the EqualFold predicate on the "confirmation_notes" field.
func ConfirmationNotesEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldConfirmationNotes, v))
}

// ConfirmationNotesContainsFold applies the ContainsFold predicate on the "confirmation_notes" field.
func ConfirmationNotesContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldConfirmationNotes, v))
}

// PaymentDetailsIsNil applies the IsNil predicate on the "payment_details" field.
func PaymentDetailsIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldPaymentDetails))
}

// PaymentDetailsNotNil applies the NotNil predicate on the "payment_details" field.
func PaymentDetailsNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldPaymentDetails))
}

// WebhookDataIsNil applies the IsNil predicate on the "webhook_data" field.
func WebhookDataIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldWebhookData))
}

// WebhookDataNotNil applies the NotNil predicate on the "webhook_data" field.
func WebhookDataNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldWebhookData))
}

// PaymentDateEQ applies the EQ predicate on the "payment_date" field.
func PaymentDateEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldPaymentDate, v))
}

// PaymentDateNEQ applies the NEQ predicate on the "payment_date" field.
func PaymentDateNEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldPaymentDate, v))
}

// PaymentDateIn applies the In predicate on the "payment_date" field.
func PaymentDateIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldPaymentDate, vs...))
}

// PaymentDateNotIn applies the NotIn predicate on the "payment_date" field.
func PaymentDateNotIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldPaymentDate, vs...))
}

// PaymentDateGT applies the GT predicate on the "payment_date" field.
func PaymentDateGT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldPaymentDate, v))
}

// PaymentDateGTE applies the GTE predicate on the "payment_date" field.
func PaymentDateGTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldPaymentDate, v))
}

// PaymentDateLT applies the LT predicate on the "payment_date" field.
func PaymentDateLT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldPaymentDate, v))
}

// PaymentDateLTE applies the LTE predicate on the "payment_date" field.
func PaymentDateLTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldPaymentDate, v))
}

// PaymentDateIsNil applies the IsNil predicate on the "payment_date" field.
func PaymentDateIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldPaymentDate))
}

// PaymentDateNotNil applies the NotNil predicate on the "payment_date" field.
func PaymentDateNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldPaymentDate))
}

// ConfirmedAtEQ applies the EQ predicate on the "confirmed_at" field.
func ConfirmedAtEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldConfirmedAt, v))
}

// ConfirmedAtNEQ applies the NEQ predicate on the "confirmed_at" field.
func ConfirmedAtNEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldConfirmedAt, v))
}

// ConfirmedAtIn applies the In predicate on the "confirmed_at" field.
func ConfirmedAtIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldConfirmedAt, vs...))
}

// ConfirmedAtNotIn applies the NotIn predicate on the "confirmed_at" field.
func ConfirmedAtNotIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldConfirmedAt, vs...))
}

// ConfirmedAtGT applies the GT predicate on the "confirmed_at" field.
func ConfirmedAtGT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldConfirmedAt, v))
}

// ConfirmedAtGTE applies the GTE predicate on the "confirmed_at" field.
func ConfirmedAtGTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldConfirmedAt, v))
}

// ConfirmedAtLT applies the LT predicate on the "confirmed_at" field.
func ConfirmedAtLT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldConfirmedAt, v))
}

// ConfirmedAtLTE applies the LTE predicate on the "confirmed_at" field.
func ConfirmedAtLTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldConfirmedAt, v))
}

// ConfirmedAtIsNil applies the IsNil predicate on the "confirmed_at" field.
func ConfirmedAtIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldConfirmedAt))
}

// ConfirmedAtNotNil applies the NotNil predicate on the "confirmed_at" field.
func ConfirmedAtNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldConfirmedAt))
}

// FailureReasonEQ applies the EQ predicate on the "failure_reason" field.
func FailureReasonEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldFailureReason, v))
}

// FailureReasonNEQ applies the NEQ predicate on the "failure_reason" field.
func FailureReasonNEQ(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldFailureReason, v))
}

// FailureReasonIn applies the In predicate on the "failure_reason" field.
func FailureReasonIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldFailureReason, vs...))
}

// FailureReasonNotIn applies the NotIn predicate on the "failure_reason" field.
func FailureReasonNotIn(vs ...string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldFailureReason, vs...))
}

// FailureReasonGT applies the GT predicate on the "failure_reason" field.
func FailureReasonGT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldFailureReason, v))
}

// FailureReasonGTE applies the GTE predicate on the "failure_reason" field.
func FailureReasonGTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldFailureReason, v))
}

// FailureReasonLT applies the LT predicate on the "failure_reason" field.
func FailureReasonLT(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldFailureReason, v))
}

// FailureReasonLTE applies the LTE predicate on the "failure_reason" field.
func FailureReasonLTE(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldFailureReason, v))
}

// FailureReasonContains applies the Contains predicate on the "failure_reason" field.
func FailureReasonContains(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContains(FieldFailureReason, v))
}

// FailureReasonHasPrefix applies the HasPrefix predicate on the "failure_reason" field.
func FailureReasonHasPrefix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasPrefix(FieldFailureReason, v))
}

// FailureReasonHasSuffix applies the HasSuffix predicate on the "failure_reason" field.
func FailureReasonHasSuffix(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldHasSuffix(FieldFailureReason, v))
}

// FailureReasonIsNil applies the IsNil predicate on the "failure_reason" field.
func FailureReasonIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldFailureReason))
}

// FailureReasonNotNil applies the NotNil predicate on the "failure_reason" field.
func FailureReasonNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldFailureReason))
}

// FailureReasonEqualFold applies the EqualFold predicate on the "failure_reason" field.
func FailureReasonEqualFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEqualFold(FieldFailureReason, v))
}

// FailureReasonContainsFold applies the ContainsFold predicate on the "failure_reason" field.
func FailureReasonContainsFold(v string) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldContainsFold(FieldFailureReason, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PaymentConfirmation) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PaymentConfirmation) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PaymentConfirmation) predicate.PaymentConfirmation {
	return predicate.PaymentConfirmation(sql.NotPredicates(p))
}

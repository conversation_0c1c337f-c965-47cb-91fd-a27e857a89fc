// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/subscription"
)

// SubscriptionUpdate is the builder for updating Subscription entities.
type SubscriptionUpdate struct {
	config
	hooks    []Hook
	mutation *SubscriptionMutation
}

// Where appends a list predicates to the SubscriptionUpdate builder.
func (su *SubscriptionUpdate) Where(ps ...predicate.Subscription) *SubscriptionUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetUserID sets the "user_id" field.
func (su *SubscriptionUpdate) SetUserID(u uuid.UUID) *SubscriptionUpdate {
	su.mutation.SetUserID(u)
	return su
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableUserID(u *uuid.UUID) *SubscriptionUpdate {
	if u != nil {
		su.SetUserID(*u)
	}
	return su
}

// SetPlanID sets the "plan_id" field.
func (su *SubscriptionUpdate) SetPlanID(u uuid.UUID) *SubscriptionUpdate {
	su.mutation.SetPlanID(u)
	return su
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillablePlanID(u *uuid.UUID) *SubscriptionUpdate {
	if u != nil {
		su.SetPlanID(*u)
	}
	return su
}

// SetStatus sets the "status" field.
func (su *SubscriptionUpdate) SetStatus(s subscription.Status) *SubscriptionUpdate {
	su.mutation.SetStatus(s)
	return su
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableStatus(s *subscription.Status) *SubscriptionUpdate {
	if s != nil {
		su.SetStatus(*s)
	}
	return su
}

// SetCurrentPeriodStart sets the "current_period_start" field.
func (su *SubscriptionUpdate) SetCurrentPeriodStart(t time.Time) *SubscriptionUpdate {
	su.mutation.SetCurrentPeriodStart(t)
	return su
}

// SetNillableCurrentPeriodStart sets the "current_period_start" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableCurrentPeriodStart(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetCurrentPeriodStart(*t)
	}
	return su
}

// SetCurrentPeriodEnd sets the "current_period_end" field.
func (su *SubscriptionUpdate) SetCurrentPeriodEnd(t time.Time) *SubscriptionUpdate {
	su.mutation.SetCurrentPeriodEnd(t)
	return su
}

// SetNillableCurrentPeriodEnd sets the "current_period_end" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableCurrentPeriodEnd(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetCurrentPeriodEnd(*t)
	}
	return su
}

// SetTrialStart sets the "trial_start" field.
func (su *SubscriptionUpdate) SetTrialStart(t time.Time) *SubscriptionUpdate {
	su.mutation.SetTrialStart(t)
	return su
}

// SetNillableTrialStart sets the "trial_start" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableTrialStart(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetTrialStart(*t)
	}
	return su
}

// ClearTrialStart clears the value of the "trial_start" field.
func (su *SubscriptionUpdate) ClearTrialStart() *SubscriptionUpdate {
	su.mutation.ClearTrialStart()
	return su
}

// SetTrialEnd sets the "trial_end" field.
func (su *SubscriptionUpdate) SetTrialEnd(t time.Time) *SubscriptionUpdate {
	su.mutation.SetTrialEnd(t)
	return su
}

// SetNillableTrialEnd sets the "trial_end" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableTrialEnd(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetTrialEnd(*t)
	}
	return su
}

// ClearTrialEnd clears the value of the "trial_end" field.
func (su *SubscriptionUpdate) ClearTrialEnd() *SubscriptionUpdate {
	su.mutation.ClearTrialEnd()
	return su
}

// SetCancelledAt sets the "cancelled_at" field.
func (su *SubscriptionUpdate) SetCancelledAt(t time.Time) *SubscriptionUpdate {
	su.mutation.SetCancelledAt(t)
	return su
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableCancelledAt(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetCancelledAt(*t)
	}
	return su
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (su *SubscriptionUpdate) ClearCancelledAt() *SubscriptionUpdate {
	su.mutation.ClearCancelledAt()
	return su
}

// SetCancelAtPeriodEnd sets the "cancel_at_period_end" field.
func (su *SubscriptionUpdate) SetCancelAtPeriodEnd(b bool) *SubscriptionUpdate {
	su.mutation.SetCancelAtPeriodEnd(b)
	return su
}

// SetNillableCancelAtPeriodEnd sets the "cancel_at_period_end" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableCancelAtPeriodEnd(b *bool) *SubscriptionUpdate {
	if b != nil {
		su.SetCancelAtPeriodEnd(*b)
	}
	return su
}

// SetPaymentMethod sets the "payment_method" field.
func (su *SubscriptionUpdate) SetPaymentMethod(s string) *SubscriptionUpdate {
	su.mutation.SetPaymentMethod(s)
	return su
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillablePaymentMethod(s *string) *SubscriptionUpdate {
	if s != nil {
		su.SetPaymentMethod(*s)
	}
	return su
}

// SetBillingAddress sets the "billing_address" field.
func (su *SubscriptionUpdate) SetBillingAddress(m map[string]interface{}) *SubscriptionUpdate {
	su.mutation.SetBillingAddress(m)
	return su
}

// ClearBillingAddress clears the value of the "billing_address" field.
func (su *SubscriptionUpdate) ClearBillingAddress() *SubscriptionUpdate {
	su.mutation.ClearBillingAddress()
	return su
}

// SetMetadata sets the "metadata" field.
func (su *SubscriptionUpdate) SetMetadata(m map[string]interface{}) *SubscriptionUpdate {
	su.mutation.SetMetadata(m)
	return su
}

// ClearMetadata clears the value of the "metadata" field.
func (su *SubscriptionUpdate) ClearMetadata() *SubscriptionUpdate {
	su.mutation.ClearMetadata()
	return su
}

// SetLastBilledAt sets the "last_billed_at" field.
func (su *SubscriptionUpdate) SetLastBilledAt(t time.Time) *SubscriptionUpdate {
	su.mutation.SetLastBilledAt(t)
	return su
}

// SetNillableLastBilledAt sets the "last_billed_at" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableLastBilledAt(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetLastBilledAt(*t)
	}
	return su
}

// ClearLastBilledAt clears the value of the "last_billed_at" field.
func (su *SubscriptionUpdate) ClearLastBilledAt() *SubscriptionUpdate {
	su.mutation.ClearLastBilledAt()
	return su
}

// SetNextBillingAt sets the "next_billing_at" field.
func (su *SubscriptionUpdate) SetNextBillingAt(t time.Time) *SubscriptionUpdate {
	su.mutation.SetNextBillingAt(t)
	return su
}

// SetNillableNextBillingAt sets the "next_billing_at" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableNextBillingAt(t *time.Time) *SubscriptionUpdate {
	if t != nil {
		su.SetNextBillingAt(*t)
	}
	return su
}

// ClearNextBillingAt clears the value of the "next_billing_at" field.
func (su *SubscriptionUpdate) ClearNextBillingAt() *SubscriptionUpdate {
	su.mutation.ClearNextBillingAt()
	return su
}

// SetExternalSubscriptionID sets the "external_subscription_id" field.
func (su *SubscriptionUpdate) SetExternalSubscriptionID(s string) *SubscriptionUpdate {
	su.mutation.SetExternalSubscriptionID(s)
	return su
}

// SetNillableExternalSubscriptionID sets the "external_subscription_id" field if the given value is not nil.
func (su *SubscriptionUpdate) SetNillableExternalSubscriptionID(s *string) *SubscriptionUpdate {
	if s != nil {
		su.SetExternalSubscriptionID(*s)
	}
	return su
}

// ClearExternalSubscriptionID clears the value of the "external_subscription_id" field.
func (su *SubscriptionUpdate) ClearExternalSubscriptionID() *SubscriptionUpdate {
	su.mutation.ClearExternalSubscriptionID()
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *SubscriptionUpdate) SetUpdatedAt(t time.Time) *SubscriptionUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// Mutation returns the SubscriptionMutation object of the builder.
func (su *SubscriptionUpdate) Mutation() *SubscriptionMutation {
	return su.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *SubscriptionUpdate) Save(ctx context.Context) (int, error) {
	su.defaults()
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *SubscriptionUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *SubscriptionUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *SubscriptionUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *SubscriptionUpdate) defaults() {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		v := subscription.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (su *SubscriptionUpdate) check() error {
	if v, ok := su.mutation.Status(); ok {
		if err := subscription.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Subscription.status": %w`, err)}
		}
	}
	if v, ok := su.mutation.PaymentMethod(); ok {
		if err := subscription.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Subscription.payment_method": %w`, err)}
		}
	}
	if v, ok := su.mutation.ExternalSubscriptionID(); ok {
		if err := subscription.ExternalSubscriptionIDValidator(v); err != nil {
			return &ValidationError{Name: "external_subscription_id", err: fmt.Errorf(`ent: validator failed for field "Subscription.external_subscription_id": %w`, err)}
		}
	}
	return nil
}

func (su *SubscriptionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := su.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(subscription.Table, subscription.Columns, sqlgraph.NewFieldSpec(subscription.FieldID, field.TypeUUID))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.UserID(); ok {
		_spec.SetField(subscription.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := su.mutation.PlanID(); ok {
		_spec.SetField(subscription.FieldPlanID, field.TypeUUID, value)
	}
	if value, ok := su.mutation.Status(); ok {
		_spec.SetField(subscription.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := su.mutation.CurrentPeriodStart(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodStart, field.TypeTime, value)
	}
	if value, ok := su.mutation.CurrentPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodEnd, field.TypeTime, value)
	}
	if value, ok := su.mutation.TrialStart(); ok {
		_spec.SetField(subscription.FieldTrialStart, field.TypeTime, value)
	}
	if su.mutation.TrialStartCleared() {
		_spec.ClearField(subscription.FieldTrialStart, field.TypeTime)
	}
	if value, ok := su.mutation.TrialEnd(); ok {
		_spec.SetField(subscription.FieldTrialEnd, field.TypeTime, value)
	}
	if su.mutation.TrialEndCleared() {
		_spec.ClearField(subscription.FieldTrialEnd, field.TypeTime)
	}
	if value, ok := su.mutation.CancelledAt(); ok {
		_spec.SetField(subscription.FieldCancelledAt, field.TypeTime, value)
	}
	if su.mutation.CancelledAtCleared() {
		_spec.ClearField(subscription.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := su.mutation.CancelAtPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCancelAtPeriodEnd, field.TypeBool, value)
	}
	if value, ok := su.mutation.PaymentMethod(); ok {
		_spec.SetField(subscription.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := su.mutation.BillingAddress(); ok {
		_spec.SetField(subscription.FieldBillingAddress, field.TypeJSON, value)
	}
	if su.mutation.BillingAddressCleared() {
		_spec.ClearField(subscription.FieldBillingAddress, field.TypeJSON)
	}
	if value, ok := su.mutation.Metadata(); ok {
		_spec.SetField(subscription.FieldMetadata, field.TypeJSON, value)
	}
	if su.mutation.MetadataCleared() {
		_spec.ClearField(subscription.FieldMetadata, field.TypeJSON)
	}
	if value, ok := su.mutation.LastBilledAt(); ok {
		_spec.SetField(subscription.FieldLastBilledAt, field.TypeTime, value)
	}
	if su.mutation.LastBilledAtCleared() {
		_spec.ClearField(subscription.FieldLastBilledAt, field.TypeTime)
	}
	if value, ok := su.mutation.NextBillingAt(); ok {
		_spec.SetField(subscription.FieldNextBillingAt, field.TypeTime, value)
	}
	if su.mutation.NextBillingAtCleared() {
		_spec.ClearField(subscription.FieldNextBillingAt, field.TypeTime)
	}
	if value, ok := su.mutation.ExternalSubscriptionID(); ok {
		_spec.SetField(subscription.FieldExternalSubscriptionID, field.TypeString, value)
	}
	if su.mutation.ExternalSubscriptionIDCleared() {
		_spec.ClearField(subscription.FieldExternalSubscriptionID, field.TypeString)
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(subscription.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{subscription.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// SubscriptionUpdateOne is the builder for updating a single Subscription entity.
type SubscriptionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SubscriptionMutation
}

// SetUserID sets the "user_id" field.
func (suo *SubscriptionUpdateOne) SetUserID(u uuid.UUID) *SubscriptionUpdateOne {
	suo.mutation.SetUserID(u)
	return suo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableUserID(u *uuid.UUID) *SubscriptionUpdateOne {
	if u != nil {
		suo.SetUserID(*u)
	}
	return suo
}

// SetPlanID sets the "plan_id" field.
func (suo *SubscriptionUpdateOne) SetPlanID(u uuid.UUID) *SubscriptionUpdateOne {
	suo.mutation.SetPlanID(u)
	return suo
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillablePlanID(u *uuid.UUID) *SubscriptionUpdateOne {
	if u != nil {
		suo.SetPlanID(*u)
	}
	return suo
}

// SetStatus sets the "status" field.
func (suo *SubscriptionUpdateOne) SetStatus(s subscription.Status) *SubscriptionUpdateOne {
	suo.mutation.SetStatus(s)
	return suo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableStatus(s *subscription.Status) *SubscriptionUpdateOne {
	if s != nil {
		suo.SetStatus(*s)
	}
	return suo
}

// SetCurrentPeriodStart sets the "current_period_start" field.
func (suo *SubscriptionUpdateOne) SetCurrentPeriodStart(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetCurrentPeriodStart(t)
	return suo
}

// SetNillableCurrentPeriodStart sets the "current_period_start" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableCurrentPeriodStart(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetCurrentPeriodStart(*t)
	}
	return suo
}

// SetCurrentPeriodEnd sets the "current_period_end" field.
func (suo *SubscriptionUpdateOne) SetCurrentPeriodEnd(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetCurrentPeriodEnd(t)
	return suo
}

// SetNillableCurrentPeriodEnd sets the "current_period_end" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableCurrentPeriodEnd(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetCurrentPeriodEnd(*t)
	}
	return suo
}

// SetTrialStart sets the "trial_start" field.
func (suo *SubscriptionUpdateOne) SetTrialStart(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetTrialStart(t)
	return suo
}

// SetNillableTrialStart sets the "trial_start" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableTrialStart(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetTrialStart(*t)
	}
	return suo
}

// ClearTrialStart clears the value of the "trial_start" field.
func (suo *SubscriptionUpdateOne) ClearTrialStart() *SubscriptionUpdateOne {
	suo.mutation.ClearTrialStart()
	return suo
}

// SetTrialEnd sets the "trial_end" field.
func (suo *SubscriptionUpdateOne) SetTrialEnd(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetTrialEnd(t)
	return suo
}

// SetNillableTrialEnd sets the "trial_end" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableTrialEnd(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetTrialEnd(*t)
	}
	return suo
}

// ClearTrialEnd clears the value of the "trial_end" field.
func (suo *SubscriptionUpdateOne) ClearTrialEnd() *SubscriptionUpdateOne {
	suo.mutation.ClearTrialEnd()
	return suo
}

// SetCancelledAt sets the "cancelled_at" field.
func (suo *SubscriptionUpdateOne) SetCancelledAt(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetCancelledAt(t)
	return suo
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableCancelledAt(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetCancelledAt(*t)
	}
	return suo
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (suo *SubscriptionUpdateOne) ClearCancelledAt() *SubscriptionUpdateOne {
	suo.mutation.ClearCancelledAt()
	return suo
}

// SetCancelAtPeriodEnd sets the "cancel_at_period_end" field.
func (suo *SubscriptionUpdateOne) SetCancelAtPeriodEnd(b bool) *SubscriptionUpdateOne {
	suo.mutation.SetCancelAtPeriodEnd(b)
	return suo
}

// SetNillableCancelAtPeriodEnd sets the "cancel_at_period_end" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableCancelAtPeriodEnd(b *bool) *SubscriptionUpdateOne {
	if b != nil {
		suo.SetCancelAtPeriodEnd(*b)
	}
	return suo
}

// SetPaymentMethod sets the "payment_method" field.
func (suo *SubscriptionUpdateOne) SetPaymentMethod(s string) *SubscriptionUpdateOne {
	suo.mutation.SetPaymentMethod(s)
	return suo
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillablePaymentMethod(s *string) *SubscriptionUpdateOne {
	if s != nil {
		suo.SetPaymentMethod(*s)
	}
	return suo
}

// SetBillingAddress sets the "billing_address" field.
func (suo *SubscriptionUpdateOne) SetBillingAddress(m map[string]interface{}) *SubscriptionUpdateOne {
	suo.mutation.SetBillingAddress(m)
	return suo
}

// ClearBillingAddress clears the value of the "billing_address" field.
func (suo *SubscriptionUpdateOne) ClearBillingAddress() *SubscriptionUpdateOne {
	suo.mutation.ClearBillingAddress()
	return suo
}

// SetMetadata sets the "metadata" field.
func (suo *SubscriptionUpdateOne) SetMetadata(m map[string]interface{}) *SubscriptionUpdateOne {
	suo.mutation.SetMetadata(m)
	return suo
}

// ClearMetadata clears the value of the "metadata" field.
func (suo *SubscriptionUpdateOne) ClearMetadata() *SubscriptionUpdateOne {
	suo.mutation.ClearMetadata()
	return suo
}

// SetLastBilledAt sets the "last_billed_at" field.
func (suo *SubscriptionUpdateOne) SetLastBilledAt(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetLastBilledAt(t)
	return suo
}

// SetNillableLastBilledAt sets the "last_billed_at" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableLastBilledAt(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetLastBilledAt(*t)
	}
	return suo
}

// ClearLastBilledAt clears the value of the "last_billed_at" field.
func (suo *SubscriptionUpdateOne) ClearLastBilledAt() *SubscriptionUpdateOne {
	suo.mutation.ClearLastBilledAt()
	return suo
}

// SetNextBillingAt sets the "next_billing_at" field.
func (suo *SubscriptionUpdateOne) SetNextBillingAt(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetNextBillingAt(t)
	return suo
}

// SetNillableNextBillingAt sets the "next_billing_at" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableNextBillingAt(t *time.Time) *SubscriptionUpdateOne {
	if t != nil {
		suo.SetNextBillingAt(*t)
	}
	return suo
}

// ClearNextBillingAt clears the value of the "next_billing_at" field.
func (suo *SubscriptionUpdateOne) ClearNextBillingAt() *SubscriptionUpdateOne {
	suo.mutation.ClearNextBillingAt()
	return suo
}

// SetExternalSubscriptionID sets the "external_subscription_id" field.
func (suo *SubscriptionUpdateOne) SetExternalSubscriptionID(s string) *SubscriptionUpdateOne {
	suo.mutation.SetExternalSubscriptionID(s)
	return suo
}

// SetNillableExternalSubscriptionID sets the "external_subscription_id" field if the given value is not nil.
func (suo *SubscriptionUpdateOne) SetNillableExternalSubscriptionID(s *string) *SubscriptionUpdateOne {
	if s != nil {
		suo.SetExternalSubscriptionID(*s)
	}
	return suo
}

// ClearExternalSubscriptionID clears the value of the "external_subscription_id" field.
func (suo *SubscriptionUpdateOne) ClearExternalSubscriptionID() *SubscriptionUpdateOne {
	suo.mutation.ClearExternalSubscriptionID()
	return suo
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *SubscriptionUpdateOne) SetUpdatedAt(t time.Time) *SubscriptionUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// Mutation returns the SubscriptionMutation object of the builder.
func (suo *SubscriptionUpdateOne) Mutation() *SubscriptionMutation {
	return suo.mutation
}

// Where appends a list predicates to the SubscriptionUpdate builder.
func (suo *SubscriptionUpdateOne) Where(ps ...predicate.Subscription) *SubscriptionUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *SubscriptionUpdateOne) Select(field string, fields ...string) *SubscriptionUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Subscription entity.
func (suo *SubscriptionUpdateOne) Save(ctx context.Context) (*Subscription, error) {
	suo.defaults()
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *SubscriptionUpdateOne) SaveX(ctx context.Context) *Subscription {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *SubscriptionUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *SubscriptionUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *SubscriptionUpdateOne) defaults() {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		v := subscription.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (suo *SubscriptionUpdateOne) check() error {
	if v, ok := suo.mutation.Status(); ok {
		if err := subscription.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Subscription.status": %w`, err)}
		}
	}
	if v, ok := suo.mutation.PaymentMethod(); ok {
		if err := subscription.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Subscription.payment_method": %w`, err)}
		}
	}
	if v, ok := suo.mutation.ExternalSubscriptionID(); ok {
		if err := subscription.ExternalSubscriptionIDValidator(v); err != nil {
			return &ValidationError{Name: "external_subscription_id", err: fmt.Errorf(`ent: validator failed for field "Subscription.external_subscription_id": %w`, err)}
		}
	}
	return nil
}

func (suo *SubscriptionUpdateOne) sqlSave(ctx context.Context) (_node *Subscription, err error) {
	if err := suo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(subscription.Table, subscription.Columns, sqlgraph.NewFieldSpec(subscription.FieldID, field.TypeUUID))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Subscription.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, subscription.FieldID)
		for _, f := range fields {
			if !subscription.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != subscription.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.UserID(); ok {
		_spec.SetField(subscription.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := suo.mutation.PlanID(); ok {
		_spec.SetField(subscription.FieldPlanID, field.TypeUUID, value)
	}
	if value, ok := suo.mutation.Status(); ok {
		_spec.SetField(subscription.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := suo.mutation.CurrentPeriodStart(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodStart, field.TypeTime, value)
	}
	if value, ok := suo.mutation.CurrentPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCurrentPeriodEnd, field.TypeTime, value)
	}
	if value, ok := suo.mutation.TrialStart(); ok {
		_spec.SetField(subscription.FieldTrialStart, field.TypeTime, value)
	}
	if suo.mutation.TrialStartCleared() {
		_spec.ClearField(subscription.FieldTrialStart, field.TypeTime)
	}
	if value, ok := suo.mutation.TrialEnd(); ok {
		_spec.SetField(subscription.FieldTrialEnd, field.TypeTime, value)
	}
	if suo.mutation.TrialEndCleared() {
		_spec.ClearField(subscription.FieldTrialEnd, field.TypeTime)
	}
	if value, ok := suo.mutation.CancelledAt(); ok {
		_spec.SetField(subscription.FieldCancelledAt, field.TypeTime, value)
	}
	if suo.mutation.CancelledAtCleared() {
		_spec.ClearField(subscription.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := suo.mutation.CancelAtPeriodEnd(); ok {
		_spec.SetField(subscription.FieldCancelAtPeriodEnd, field.TypeBool, value)
	}
	if value, ok := suo.mutation.PaymentMethod(); ok {
		_spec.SetField(subscription.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := suo.mutation.BillingAddress(); ok {
		_spec.SetField(subscription.FieldBillingAddress, field.TypeJSON, value)
	}
	if suo.mutation.BillingAddressCleared() {
		_spec.ClearField(subscription.FieldBillingAddress, field.TypeJSON)
	}
	if value, ok := suo.mutation.Metadata(); ok {
		_spec.SetField(subscription.FieldMetadata, field.TypeJSON, value)
	}
	if suo.mutation.MetadataCleared() {
		_spec.ClearField(subscription.FieldMetadata, field.TypeJSON)
	}
	if value, ok := suo.mutation.LastBilledAt(); ok {
		_spec.SetField(subscription.FieldLastBilledAt, field.TypeTime, value)
	}
	if suo.mutation.LastBilledAtCleared() {
		_spec.ClearField(subscription.FieldLastBilledAt, field.TypeTime)
	}
	if value, ok := suo.mutation.NextBillingAt(); ok {
		_spec.SetField(subscription.FieldNextBillingAt, field.TypeTime, value)
	}
	if suo.mutation.NextBillingAtCleared() {
		_spec.ClearField(subscription.FieldNextBillingAt, field.TypeTime)
	}
	if value, ok := suo.mutation.ExternalSubscriptionID(); ok {
		_spec.SetField(subscription.FieldExternalSubscriptionID, field.TypeString, value)
	}
	if suo.mutation.ExternalSubscriptionIDCleared() {
		_spec.ClearField(subscription.FieldExternalSubscriptionID, field.TypeString)
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(subscription.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &Subscription{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{subscription.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}

package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/asset-service/api/grpc/handlers"
	"github.com/social-content-ai/asset-service/api/restful"
	"github.com/social-content-ai/asset-service/config"
	"github.com/social-content-ai/asset-service/ent"
	"github.com/social-content-ai/asset-service/pkg/cleanup"
	"github.com/social-content-ai/asset-service/pkg/processor"
	"github.com/social-content-ai/asset-service/pkg/storage"
	"github.com/social-content-ai/asset-service/usecase/asset"
	"github.com/social-content-ai/asset-service/usecase/folder"
	"github.com/social-content-ai/asset-service/usecase/rag"

	"github.com/social-content-ai/pkg-shared/logging"

	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

func main() {
	// Load configuration
	fmt.Println("Loading configuration...")
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	if cfg == nil {
		fmt.Println("Config is nil")
		os.Exit(1)
	}

	fmt.Printf("Config loaded: %+v\n", cfg.Server)

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		fmt.Printf("Invalid configuration: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Config loaded successfully: env=%s, db_type=%s\n", cfg.Server.Env, cfg.Database.Type)

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
	})

	logger.WithField("env", cfg.Server.Env).Info("Starting Asset Service")

	// Initialize databases
	readDB, writeDB, err := initDatabases(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize databases")
	}
	defer readDB.Close()
	defer writeDB.Close()

	// Run database migrations
	if err := writeDB.Schema.Create(context.Background()); err != nil {
		logger.WithError(err).Fatal("Failed to create schema")
	}

	// Initialize storage
	storageConfig := &storage.Config{
		Endpoint:  cfg.Storage.Endpoint,
		AccessKey: cfg.Storage.AccessKey,
		SecretKey: cfg.Storage.SecretKey,
		Bucket:    cfg.Storage.Bucket,
		UseSSL:    cfg.Storage.UseSSL,
		PublicURL: cfg.Storage.PublicURL,
		Region:    cfg.Storage.Region,
	}

	var storageClient storage.Storage
	if cfg.Storage.Provider == "minio" || cfg.Storage.Provider == "s3" {
		storageClient, err = storage.NewMinIOStorage(storageConfig)
		if err != nil {
			logger.WithError(err).Warn("Failed to initialize storage, continuing without storage")
			storageClient = nil
		} else {
			logger.WithField("provider", cfg.Storage.Provider).Info("Storage initialized successfully")
		}
	} else {
		logger.WithField("provider", cfg.Storage.Provider).Warn("Unknown storage provider, continuing without storage")
		storageClient = nil
	}

	// Initialize processors
	imageProcessor := processor.NewImageProcessor(2048, 2048, 85)
	documentProcessor := processor.NewDocumentProcessor()

	// TODO: Initialize external service clients properly
	// For now, create a mock user client
	var userClient userv1.UserServiceClient

	// Initialize use cases
	assetUseCase := asset.NewService(
		readDB,
		writeDB,
		storageClient,
		imageProcessor,
		documentProcessor,
		logger,
	)

	folderUseCase := folder.NewService(readDB, writeDB, logger)
	ragUseCase := rag.NewService(readDB, writeDB, logger)

	// Initialize cleanup service
	cleanupConfig := &cleanup.Config{
		PendingUploadTTL:     cfg.Cleanup.PendingUploadTTL,
		OrphanedFileTTL:      cfg.Cleanup.OrphanedFileTTL,
		DeletedAssetTTL:      cfg.Cleanup.DeletedAssetTTL,
		CleanupInterval:      cfg.Cleanup.CleanupInterval,
		BatchSize:            cfg.Cleanup.BatchSize,
		EnableStorageCleanup: cfg.Cleanup.EnableStorageCleanup,
		DryRun:               cfg.Cleanup.DryRun,
	}

	var cleanupScheduler *cleanup.Scheduler
	if cfg.Cleanup.Enabled {
		cleanupService := cleanup.NewService(cleanupConfig, readDB, writeDB, storageClient, logger)
		cleanupScheduler = cleanup.NewScheduler(cleanupService, logger)

		// Start cleanup scheduler
		if err := cleanupScheduler.Start(); err != nil {
			logger.WithError(err).Error("Failed to start cleanup scheduler")
		} else {
			logger.Info("Cleanup scheduler started successfully")
		}
	} else {
		logger.Info("Cleanup service disabled by configuration")
	}

	// Initialize gRPC server
	grpcServer := grpc.NewServer()
	assetv1.RegisterAssetServiceServer(grpcServer, handlers.NewAssetHandler(
		assetUseCase,
		logger,
	))

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())

	restful.SetupRoutes(httpRouter, assetUseCase, folderUseCase, ragUseCase, userClient, logger)

	// Start gRPC server
	grpcAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.GRPCPort)
	grpcListener, err := net.Listen("tcp", grpcAddr)
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("address", grpcAddr).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start HTTP server
	httpAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.HTTPPort)
	httpServer := &http.Server{
		Addr:    httpAddr,
		Handler: httpRouter,
	}

	go func() {
		logger.WithField("address", httpAddr).Info("Starting HTTP server")
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	logger.WithFields(map[string]interface{}{
		"grpc_address": grpcAddr,
		"http_address": httpAddr,
		"health_check": fmt.Sprintf("http://%s/health", httpAddr),
		"environment":  cfg.Server.Env,
	}).Info("Asset Service started successfully")

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down servers...")

	// Shutdown cleanup scheduler
	if cleanupScheduler != nil {
		if err := cleanupScheduler.Stop(); err != nil {
			logger.WithError(err).Error("Failed to stop cleanup scheduler")
		} else {
			logger.Info("Cleanup scheduler stopped")
		}
	}

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Servers stopped")
}

// initDatabases initializes read and write database connections
func initDatabases(cfg *config.Config, logger logging.Logger) (*ent.Client, *ent.Client, error) {
	// Get database driver and DSN based on configuration
	driverName := cfg.Database.GetDriverName()
	dsn := cfg.Database.GetDSN()

	logger.WithFields(map[string]interface{}{
		"driver": driverName,
		"type":   cfg.Database.Type,
	}).Info("Initializing database connection")

	// Create data directory for SQLite if needed
	if err := cfg.Database.CreateDataDirectory(); err != nil {
		return nil, nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// Create Ent client
	client, err := ent.Open(driverName, dsn)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"driver": driverName,
			"type":   cfg.Database.Type,
		}).Error("Failed opening database connection")
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool (if available)
	// Note: Ent client doesn't expose DB() method directly
	// Connection pool settings are applied through the driver

	// Test connection by checking if we can query the database
	// This will be verified when we run migrations

	logger.WithFields(map[string]interface{}{
		"driver":         driverName,
		"type":           cfg.Database.Type,
		"max_open_conns": cfg.Database.MaxOpenConns,
		"max_idle_conns": cfg.Database.MaxIdleConns,
	}).Info("Database connection established")

	// For now, use the same client for both read and write
	// In production, you might want separate read/write connections
	return client, client, nil
}

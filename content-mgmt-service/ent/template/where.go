// Code generated by ent, DO NOT EDIT.

package template

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUserID, v))
}

// WorkspaceID applies equality check predicate on the "workspace_id" field. It's identical to WorkspaceIDEQ.
func WorkspaceID(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldWorkspaceID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldDescription, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldContent, v))
}

// IsPublic applies equality check predicate on the "is_public" field. It's identical to IsPublicEQ.
func IsPublic(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldIsPublic, v))
}

// IsFeatured applies equality check predicate on the "is_featured" field. It's identical to IsFeaturedEQ.
func IsFeatured(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldIsFeatured, v))
}

// UsageCount applies equality check predicate on the "usage_count" field. It's identical to UsageCountEQ.
func UsageCount(v int) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUsageCount, v))
}

// Rating applies equality check predicate on the "rating" field. It's identical to RatingEQ.
func Rating(v float64) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRating, v))
}

// RatingCount applies equality check predicate on the "rating_count" field. It's identical to RatingCountEQ.
func RatingCount(v int) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRatingCount, v))
}

// Language applies equality check predicate on the "language" field. It's identical to LanguageEQ.
func Language(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldLanguage, v))
}

// RagTrained applies equality check predicate on the "rag_trained" field. It's identical to RagTrainedEQ.
func RagTrained(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRagTrained, v))
}

// RagTrainedAt applies equality check predicate on the "rag_trained_at" field. It's identical to RagTrainedAtEQ.
func RagTrainedAt(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRagTrainedAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldUserID, v))
}

// WorkspaceIDEQ applies the EQ predicate on the "workspace_id" field.
func WorkspaceIDEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldWorkspaceID, v))
}

// WorkspaceIDNEQ applies the NEQ predicate on the "workspace_id" field.
func WorkspaceIDNEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldWorkspaceID, v))
}

// WorkspaceIDIn applies the In predicate on the "workspace_id" field.
func WorkspaceIDIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDNotIn applies the NotIn predicate on the "workspace_id" field.
func WorkspaceIDNotIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDGT applies the GT predicate on the "workspace_id" field.
func WorkspaceIDGT(v string) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldWorkspaceID, v))
}

// WorkspaceIDGTE applies the GTE predicate on the "workspace_id" field.
func WorkspaceIDGTE(v string) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldWorkspaceID, v))
}

// WorkspaceIDLT applies the LT predicate on the "workspace_id" field.
func WorkspaceIDLT(v string) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldWorkspaceID, v))
}

// WorkspaceIDLTE applies the LTE predicate on the "workspace_id" field.
func WorkspaceIDLTE(v string) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldWorkspaceID, v))
}

// WorkspaceIDContains applies the Contains predicate on the "workspace_id" field.
func WorkspaceIDContains(v string) predicate.Template {
	return predicate.Template(sql.FieldContains(FieldWorkspaceID, v))
}

// WorkspaceIDHasPrefix applies the HasPrefix predicate on the "workspace_id" field.
func WorkspaceIDHasPrefix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasPrefix(FieldWorkspaceID, v))
}

// WorkspaceIDHasSuffix applies the HasSuffix predicate on the "workspace_id" field.
func WorkspaceIDHasSuffix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasSuffix(FieldWorkspaceID, v))
}

// WorkspaceIDIsNil applies the IsNil predicate on the "workspace_id" field.
func WorkspaceIDIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldWorkspaceID))
}

// WorkspaceIDNotNil applies the NotNil predicate on the "workspace_id" field.
func WorkspaceIDNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldWorkspaceID))
}

// WorkspaceIDEqualFold applies the EqualFold predicate on the "workspace_id" field.
func WorkspaceIDEqualFold(v string) predicate.Template {
	return predicate.Template(sql.FieldEqualFold(FieldWorkspaceID, v))
}

// WorkspaceIDContainsFold applies the ContainsFold predicate on the "workspace_id" field.
func WorkspaceIDContainsFold(v string) predicate.Template {
	return predicate.Template(sql.FieldContainsFold(FieldWorkspaceID, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Template {
	return predicate.Template(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Template {
	return predicate.Template(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Template {
	return predicate.Template(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Template {
	return predicate.Template(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Template {
	return predicate.Template(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Template {
	return predicate.Template(sql.FieldContainsFold(FieldDescription, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.Template {
	return predicate.Template(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.Template {
	return predicate.Template(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.Template {
	return predicate.Template(sql.FieldContainsFold(FieldContent, v))
}

// VariablesIsNil applies the IsNil predicate on the "variables" field.
func VariablesIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldVariables))
}

// VariablesNotNil applies the NotNil predicate on the "variables" field.
func VariablesNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldVariables))
}

// VariableDescriptionsIsNil applies the IsNil predicate on the "variable_descriptions" field.
func VariableDescriptionsIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldVariableDescriptions))
}

// VariableDescriptionsNotNil applies the NotNil predicate on the "variable_descriptions" field.
func VariableDescriptionsNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldVariableDescriptions))
}

// CategoryEQ applies the EQ predicate on the "category" field.
func CategoryEQ(v Category) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldCategory, v))
}

// CategoryNEQ applies the NEQ predicate on the "category" field.
func CategoryNEQ(v Category) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldCategory, v))
}

// CategoryIn applies the In predicate on the "category" field.
func CategoryIn(vs ...Category) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldCategory, vs...))
}

// CategoryNotIn applies the NotIn predicate on the "category" field.
func CategoryNotIn(vs ...Category) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldCategory, vs...))
}

// PlatformsIsNil applies the IsNil predicate on the "platforms" field.
func PlatformsIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldPlatforms))
}

// PlatformsNotNil applies the NotNil predicate on the "platforms" field.
func PlatformsNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldPlatforms))
}

// ToneEQ applies the EQ predicate on the "tone" field.
func ToneEQ(v Tone) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldTone, v))
}

// ToneNEQ applies the NEQ predicate on the "tone" field.
func ToneNEQ(v Tone) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldTone, v))
}

// ToneIn applies the In predicate on the "tone" field.
func ToneIn(vs ...Tone) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldTone, vs...))
}

// ToneNotIn applies the NotIn predicate on the "tone" field.
func ToneNotIn(vs ...Tone) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldTone, vs...))
}

// ContentTypeEQ applies the EQ predicate on the "content_type" field.
func ContentTypeEQ(v ContentType) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldContentType, v))
}

// ContentTypeNEQ applies the NEQ predicate on the "content_type" field.
func ContentTypeNEQ(v ContentType) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldContentType, v))
}

// ContentTypeIn applies the In predicate on the "content_type" field.
func ContentTypeIn(vs ...ContentType) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldContentType, vs...))
}

// ContentTypeNotIn applies the NotIn predicate on the "content_type" field.
func ContentTypeNotIn(vs ...ContentType) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldContentType, vs...))
}

// HashtagsIsNil applies the IsNil predicate on the "hashtags" field.
func HashtagsIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldHashtags))
}

// HashtagsNotNil applies the NotNil predicate on the "hashtags" field.
func HashtagsNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldHashtags))
}

// IsPublicEQ applies the EQ predicate on the "is_public" field.
func IsPublicEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldIsPublic, v))
}

// IsPublicNEQ applies the NEQ predicate on the "is_public" field.
func IsPublicNEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldIsPublic, v))
}

// IsFeaturedEQ applies the EQ predicate on the "is_featured" field.
func IsFeaturedEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldIsFeatured, v))
}

// IsFeaturedNEQ applies the NEQ predicate on the "is_featured" field.
func IsFeaturedNEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldIsFeatured, v))
}

// UsageCountEQ applies the EQ predicate on the "usage_count" field.
func UsageCountEQ(v int) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUsageCount, v))
}

// UsageCountNEQ applies the NEQ predicate on the "usage_count" field.
func UsageCountNEQ(v int) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldUsageCount, v))
}

// UsageCountIn applies the In predicate on the "usage_count" field.
func UsageCountIn(vs ...int) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldUsageCount, vs...))
}

// UsageCountNotIn applies the NotIn predicate on the "usage_count" field.
func UsageCountNotIn(vs ...int) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldUsageCount, vs...))
}

// UsageCountGT applies the GT predicate on the "usage_count" field.
func UsageCountGT(v int) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldUsageCount, v))
}

// UsageCountGTE applies the GTE predicate on the "usage_count" field.
func UsageCountGTE(v int) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldUsageCount, v))
}

// UsageCountLT applies the LT predicate on the "usage_count" field.
func UsageCountLT(v int) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldUsageCount, v))
}

// UsageCountLTE applies the LTE predicate on the "usage_count" field.
func UsageCountLTE(v int) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldUsageCount, v))
}

// RatingEQ applies the EQ predicate on the "rating" field.
func RatingEQ(v float64) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRating, v))
}

// RatingNEQ applies the NEQ predicate on the "rating" field.
func RatingNEQ(v float64) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldRating, v))
}

// RatingIn applies the In predicate on the "rating" field.
func RatingIn(vs ...float64) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldRating, vs...))
}

// RatingNotIn applies the NotIn predicate on the "rating" field.
func RatingNotIn(vs ...float64) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldRating, vs...))
}

// RatingGT applies the GT predicate on the "rating" field.
func RatingGT(v float64) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldRating, v))
}

// RatingGTE applies the GTE predicate on the "rating" field.
func RatingGTE(v float64) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldRating, v))
}

// RatingLT applies the LT predicate on the "rating" field.
func RatingLT(v float64) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldRating, v))
}

// RatingLTE applies the LTE predicate on the "rating" field.
func RatingLTE(v float64) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldRating, v))
}

// RatingCountEQ applies the EQ predicate on the "rating_count" field.
func RatingCountEQ(v int) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRatingCount, v))
}

// RatingCountNEQ applies the NEQ predicate on the "rating_count" field.
func RatingCountNEQ(v int) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldRatingCount, v))
}

// RatingCountIn applies the In predicate on the "rating_count" field.
func RatingCountIn(vs ...int) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldRatingCount, vs...))
}

// RatingCountNotIn applies the NotIn predicate on the "rating_count" field.
func RatingCountNotIn(vs ...int) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldRatingCount, vs...))
}

// RatingCountGT applies the GT predicate on the "rating_count" field.
func RatingCountGT(v int) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldRatingCount, v))
}

// RatingCountGTE applies the GTE predicate on the "rating_count" field.
func RatingCountGTE(v int) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldRatingCount, v))
}

// RatingCountLT applies the LT predicate on the "rating_count" field.
func RatingCountLT(v int) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldRatingCount, v))
}

// RatingCountLTE applies the LTE predicate on the "rating_count" field.
func RatingCountLTE(v int) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldRatingCount, v))
}

// TagsIsNil applies the IsNil predicate on the "tags" field.
func TagsIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldTags))
}

// TagsNotNil applies the NotNil predicate on the "tags" field.
func TagsNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldTags))
}

// LanguageEQ applies the EQ predicate on the "language" field.
func LanguageEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldLanguage, v))
}

// LanguageNEQ applies the NEQ predicate on the "language" field.
func LanguageNEQ(v string) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldLanguage, v))
}

// LanguageIn applies the In predicate on the "language" field.
func LanguageIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldLanguage, vs...))
}

// LanguageNotIn applies the NotIn predicate on the "language" field.
func LanguageNotIn(vs ...string) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldLanguage, vs...))
}

// LanguageGT applies the GT predicate on the "language" field.
func LanguageGT(v string) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldLanguage, v))
}

// LanguageGTE applies the GTE predicate on the "language" field.
func LanguageGTE(v string) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldLanguage, v))
}

// LanguageLT applies the LT predicate on the "language" field.
func LanguageLT(v string) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldLanguage, v))
}

// LanguageLTE applies the LTE predicate on the "language" field.
func LanguageLTE(v string) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldLanguage, v))
}

// LanguageContains applies the Contains predicate on the "language" field.
func LanguageContains(v string) predicate.Template {
	return predicate.Template(sql.FieldContains(FieldLanguage, v))
}

// LanguageHasPrefix applies the HasPrefix predicate on the "language" field.
func LanguageHasPrefix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasPrefix(FieldLanguage, v))
}

// LanguageHasSuffix applies the HasSuffix predicate on the "language" field.
func LanguageHasSuffix(v string) predicate.Template {
	return predicate.Template(sql.FieldHasSuffix(FieldLanguage, v))
}

// LanguageEqualFold applies the EqualFold predicate on the "language" field.
func LanguageEqualFold(v string) predicate.Template {
	return predicate.Template(sql.FieldEqualFold(FieldLanguage, v))
}

// LanguageContainsFold applies the ContainsFold predicate on the "language" field.
func LanguageContainsFold(v string) predicate.Template {
	return predicate.Template(sql.FieldContainsFold(FieldLanguage, v))
}

// TrainingFilesIsNil applies the IsNil predicate on the "training_files" field.
func TrainingFilesIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldTrainingFiles))
}

// TrainingFilesNotNil applies the NotNil predicate on the "training_files" field.
func TrainingFilesNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldTrainingFiles))
}

// RagTrainedEQ applies the EQ predicate on the "rag_trained" field.
func RagTrainedEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRagTrained, v))
}

// RagTrainedNEQ applies the NEQ predicate on the "rag_trained" field.
func RagTrainedNEQ(v bool) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldRagTrained, v))
}

// RagTrainedAtEQ applies the EQ predicate on the "rag_trained_at" field.
func RagTrainedAtEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldRagTrainedAt, v))
}

// RagTrainedAtNEQ applies the NEQ predicate on the "rag_trained_at" field.
func RagTrainedAtNEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldRagTrainedAt, v))
}

// RagTrainedAtIn applies the In predicate on the "rag_trained_at" field.
func RagTrainedAtIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldRagTrainedAt, vs...))
}

// RagTrainedAtNotIn applies the NotIn predicate on the "rag_trained_at" field.
func RagTrainedAtNotIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldRagTrainedAt, vs...))
}

// RagTrainedAtGT applies the GT predicate on the "rag_trained_at" field.
func RagTrainedAtGT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldRagTrainedAt, v))
}

// RagTrainedAtGTE applies the GTE predicate on the "rag_trained_at" field.
func RagTrainedAtGTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldRagTrainedAt, v))
}

// RagTrainedAtLT applies the LT predicate on the "rag_trained_at" field.
func RagTrainedAtLT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldRagTrainedAt, v))
}

// RagTrainedAtLTE applies the LTE predicate on the "rag_trained_at" field.
func RagTrainedAtLTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldRagTrainedAt, v))
}

// RagTrainedAtIsNil applies the IsNil predicate on the "rag_trained_at" field.
func RagTrainedAtIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldRagTrainedAt))
}

// RagTrainedAtNotNil applies the NotNil predicate on the "rag_trained_at" field.
func RagTrainedAtNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldRagTrainedAt))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Template {
	return predicate.Template(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Template {
	return predicate.Template(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Template {
	return predicate.Template(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Template {
	return predicate.Template(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Template) predicate.Template {
	return predicate.Template(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Template) predicate.Template {
	return predicate.Template(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Template) predicate.Template {
	return predicate.Template(sql.NotPredicates(p))
}

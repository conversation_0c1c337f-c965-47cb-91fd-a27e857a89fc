package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/payment"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/pkg-shared/logging"
)

// BillingHandler handles billing-related HTTP requests
type BillingHandler struct {
	creditUseCase    credit.UseCase
	planUseCase      plan.UseCase
	paymentProcessor payment.Processor
	logger           logging.Logger
}

// NewBillingHandler creates a new billing handler
func NewBillingHandler(
	creditUseCase credit.UseCase,
	planUseCase plan.UseCase,
	paymentProcessor payment.Processor,
	logger logging.Logger,
) *BillingHandler {
	return &BillingHandler{
		creditUseCase:    creditUseCase,
		planUseCase:      planUseCase,
		paymentProcessor: paymentProcessor,
		logger:           logger,
	}
}

// PurchaseCredits godoc
// @Summary Purchase credits
// @Description Purchase credit package
// @Tags billing
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.PurchaseCreditsRequest true "Purchase credits request"
// @Success 200 {object} models.PurchaseCreditsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/purchase [post]
func (h *BillingHandler) PurchaseCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.PurchaseCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid purchase credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	// TODO: Implement purchase credits logic
	// This would involve:
	// 1. Validate package ID
	// 2. Create order
	// 3. Process payment
	// 4. Add credits on successful payment

	c.JSON(http.StatusOK, models.PurchaseCreditsResponse{
		OrderID:    "order_123",
		PaymentURL: "https://payment.example.com/pay/123",
		Amount:     1000, // in cents
		Currency:   "USD",
		Status:     "pending",
	})
}

// GetCreditPackages godoc
// @Summary Get credit packages
// @Description Get available credit packages for purchase
// @Tags billing
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CreditPackagesResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/packages [get]
func (h *BillingHandler) GetCreditPackages(c *gin.Context) {
	// TODO: Implement get credit packages
	packages := []models.CreditPackage{
		{
			Name:        "Starter Pack",
			Description: "Perfect for getting started",
			Credits:     100,
			Price:       999, // $9.99
			Currency:    "USD",
			Popular:     false,
			Active:      true,
			Features:    []string{"100 AI generations", "Basic support"},
		},
		{
			Name:        "Pro Pack",
			Description: "For power users",
			Credits:     500,
			Price:       3999, // $39.99
			Currency:    "USD",
			Popular:     true,
			Active:      true,
			Features:    []string{"500 AI generations", "Priority support", "Advanced features"},
		},
	}

	c.JSON(http.StatusOK, models.CreditPackagesResponse{
		Packages: packages,
		Currency: "USD",
	})
}

// GetOrders godoc
// @Summary Get user orders
// @Description Get user's order history
// @Tags billing
// @Produce json
// @Security BearerAuth
// @Param status query string false "Filter by status"
// @Param type query string false "Filter by type"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.ListOrdersResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/orders [get]
func (h *BillingHandler) GetOrders(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	req := &models.ListOrdersRequest{
		UserID:    userID.(string),
		Status:    c.Query("status"),
		Type:      c.Query("type"),
		Page:      page,
		Limit:     limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "desc"),
	}

	// TODO: Implement get orders
	_ = req

	c.JSON(http.StatusOK, models.ListOrdersResponse{
		Orders: []models.Order{},
		Pagination: models.PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      0,
			TotalPages: 0,
		},
		Summary: models.OrderSummary{
			TotalOrders:  0,
			TotalAmount:  0,
			TotalCredits: 0,
			ByStatus:     make(map[string]int),
			ByType:       make(map[string]int),
		},
	})
}

// GetOrder godoc
// @Summary Get order by ID
// @Description Get order details by ID
// @Tags billing
// @Produce json
// @Security BearerAuth
// @Param id path string true "Order ID"
// @Success 200 {object} models.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/credits/orders/{id} [get]
func (h *BillingHandler) GetOrder(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	orderID := c.Param("id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_ORDER_ID",
			Message: "Order ID is required",
		})
		return
	}

	// TODO: Implement get order
	_ = userID

	c.JSON(http.StatusNotFound, models.ErrorResponse{
		Error:   "ORDER_NOT_FOUND",
		Message: "Order not found",
	})
}

// CancelOrder godoc
// @Summary Cancel order
// @Description Cancel a pending order
// @Tags billing
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Order ID"
// @Param request body models.CancelOrderRequest true "Cancel order request"
// @Success 200 {object} models.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/credits/orders/{id}/cancel [post]
func (h *BillingHandler) CancelOrder(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	orderID := c.Param("id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_ORDER_ID",
			Message: "Order ID is required",
		})
		return
	}

	var req models.CancelOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid cancel order request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.OrderID = orderID
	req.UserID = userID.(string)

	// TODO: Implement cancel order
	_ = req

	c.JSON(http.StatusNotFound, models.ErrorResponse{
		Error:   "ORDER_NOT_FOUND",
		Message: "Order not found",
	})
}

// GetRevenueAnalytics godoc
// @Summary Get revenue analytics
// @Description Get revenue analytics for user
// @Tags billing
// @Produce json
// @Security BearerAuth
// @Param period query string false "Period (day, week, month, year)" default("month")
// @Success 200 {object} models.RevenueAnalyticsResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/analytics/revenue [get]
func (h *BillingHandler) GetRevenueAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	period := c.DefaultQuery("period", "month")

	// TODO: Implement get revenue analytics
	_ = userID
	_ = period

	c.JSON(http.StatusOK, models.RevenueAnalyticsResponse{
		Period:            period,
		TotalRevenue:      0,
		TotalOrders:       0,
		AverageOrderValue: 0,
		Timeline:          []models.RevenueTimelinePoint{},
		RevenueByMethod:   make(map[string]int64),
		RevenueByPackage:  make(map[string]int64),
		TopPackages:       []models.PackageRevenue{},
		ConversionRate:    0,
		RefundRate:        0,
	})
}

// GetPublicCreditPackages godoc
// @Summary Get public credit packages
// @Description Get publicly available credit packages
// @Tags public
// @Produce json
// @Success 200 {object} models.CreditPackagesResponse
// @Router /public/credit-packages [get]
func (h *BillingHandler) GetPublicCreditPackages(c *gin.Context) {
	// TODO: Implement get public credit packages
	packages := []models.CreditPackage{
		{
			Name:        "Starter Pack",
			Description: "Perfect for getting started",
			Credits:     100,
			Price:       999, // $9.99
			Currency:    "USD",
			Popular:     false,
			Active:      true,
			Features:    []string{"100 AI generations", "Basic support"},
		},
		{
			Name:        "Pro Pack",
			Description: "For power users",
			Credits:     500,
			Price:       3999, // $39.99
			Currency:    "USD",
			Popular:     true,
			Active:      true,
			Features:    []string{"500 AI generations", "Priority support", "Advanced features"},
		},
	}

	c.JSON(http.StatusOK, models.CreditPackagesResponse{
		Packages: packages,
		Currency: "USD",
	})
}

// Webhook handlers
// StripeWebhook handles Stripe webhook events
func (h *BillingHandler) StripeWebhook(c *gin.Context) {
	// TODO: Implement Stripe webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// MomoWebhook handles MoMo webhook events
func (h *BillingHandler) MomoWebhook(c *gin.Context) {
	// TODO: Implement MoMo webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// ZaloPayWebhook handles ZaloPay webhook events
func (h *BillingHandler) ZaloPayWebhook(c *gin.Context) {
	// TODO: Implement ZaloPay webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// BankTransferWebhook handles bank transfer webhook events
func (h *BillingHandler) BankTransferWebhook(c *gin.Context) {
	// TODO: Implement bank transfer webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// PaymentSuccessWebhook handles payment success webhook events
func (h *BillingHandler) PaymentSuccessWebhook(c *gin.Context) {
	// TODO: Implement payment success webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// PaymentFailedWebhook handles payment failed webhook events
func (h *BillingHandler) PaymentFailedWebhook(c *gin.Context) {
	// TODO: Implement payment failed webhook handler
	c.JSON(http.StatusOK, gin.H{"received": true})
}

// Admin handlers
// AdminListOrders lists all orders (admin)
func (h *BillingHandler) AdminListOrders(c *gin.Context) {
	// TODO: Implement admin list orders
	c.JSON(http.StatusOK, models.ListOrdersResponse{
		Orders:     []models.Order{},
		Pagination: models.PaginationMeta{},
		Summary:    models.OrderSummary{},
	})
}

// AdminGetOrder gets order (admin)
func (h *BillingHandler) AdminGetOrder(c *gin.Context) {
	// TODO: Implement admin get order
	c.JSON(http.StatusNotFound, models.ErrorResponse{
		Error:   "ORDER_NOT_FOUND",
		Message: "Order not found",
	})
}

// AdminRefundOrder refunds order (admin)
func (h *BillingHandler) AdminRefundOrder(c *gin.Context) {
	// TODO: Implement admin refund order
	c.JSON(http.StatusNotFound, models.ErrorResponse{
		Error:   "ORDER_NOT_FOUND",
		Message: "Order not found",
	})
}

// AdminGetRevenue gets admin revenue
func (h *BillingHandler) AdminGetRevenue(c *gin.Context) {
	// TODO: Implement admin get revenue
	c.JSON(http.StatusOK, models.RevenueAnalyticsResponse{})
}

// GetAdminRevenueAnalytics gets admin revenue analytics
func (h *BillingHandler) GetAdminRevenueAnalytics(c *gin.Context) {
	// TODO: Implement get admin revenue analytics
	c.JSON(http.StatusOK, models.RevenueAnalyticsResponse{})
}

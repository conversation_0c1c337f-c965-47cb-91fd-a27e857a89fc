package auth

import (
	"context"

	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/pkg/models"
)

// UseCase defines the authentication business logic interface
type UseCase interface {
	// Authentication
	Register(ctx context.Context, req *models.RegisterRequest) (*models.RegisterResponse, error)
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error)
	Logout(ctx context.Context, sessionID string) error
	ValidateToken(ctx context.Context, token string) (*TokenValidationResult, error)
	VerifyEmail(ctx context.Context, request *models.VerifyEmailRequest) error
	ResendVerification(ctx context.Context, request *models.ResendVerificationRequest) error

	// Password management
	ChangePassword(ctx context.Context, req *ChangePasswordRequest) error
	ResetPassword(ctx context.Context, email string) error
	ConfirmPasswordReset(ctx context.Context, req *ConfirmPasswordResetRequest) error
}

// LoginRequest represents login request
type LoginRequest struct {
	Email      string `json:"email" validate:"required,email"`
	Password   string `json:"password" validate:"required"`
	DeviceInfo string `json:"device_info,omitempty"`
	IPAddress  string `json:"ip_address,omitempty"`
	Remember   bool   `json:"remember,omitempty"`
}

// LoginResponse represents login response
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    string    `json:"expires_at"`
	User         *UserInfo `json:"user"`
	Requires2FA  bool      `json:"requires_2fa"`
	SessionID    string    `json:"session_id"`
}

// UserInfo represents user information in auth response
type UserInfo struct {
	ID                   string                 `json:"id"`
	FullName             string                 `json:"full_name"`
	Email                string                 `json:"email"`
	Role                 string                 `json:"role"`
	IsVerified           bool                   `json:"is_verified"`
	TwoFactorEnabled     bool                   `json:"two_factor_enabled"`
	NotificationSettings map[string]interface{} `json:"notification_settings,omitempty"`
}

// TokenValidationResult represents token validation result
type TokenValidationResult struct {
	Valid     bool              `json:"valid"`
	UserID    string            `json:"user_id,omitempty"`
	Email     string            `json:"email,omitempty"`
	Role      string            `json:"role,omitempty"`
	SessionID string            `json:"session_id,omitempty"`
	ExpiresAt string            `json:"expires_at,omitempty"`
	Claims    map[string]string `json:"claims,omitempty"`
}

// ChangePasswordRequest represents password change request
type ChangePasswordRequest struct {
	UserID          string `json:"user_id" validate:"required,uuid"`
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

// ConfirmPasswordResetRequest represents password reset confirmation
type ConfirmPasswordResetRequest struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// TwoFactorSetupResponse represents 2FA setup response
type TwoFactorSetupResponse struct {
	SecretKey   string   `json:"secret_key"`
	QRCodeURL   string   `json:"qr_code_url"`
	BackupCodes []string `json:"backup_codes"`
}

// ToUserInfo converts ent.User to UserInfo
func ToUserInfo(user *ent.User) *UserInfo {
	if user == nil {
		return nil
	}

	return &UserInfo{
		ID:                   user.ID.String(),
		FullName:             user.FullName,
		Email:                user.Email,
		Role:                 string(user.Role),
		IsVerified:           user.IsVerified,
		TwoFactorEnabled:     user.TwoFactorEnabled,
		NotificationSettings: user.NotificationSettings,
	}
}

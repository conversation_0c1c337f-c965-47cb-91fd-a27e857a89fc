// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/predicate"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// PublishedPostQuery is the builder for querying PublishedPost entities.
type PublishedPostQuery struct {
	config
	ctx        *QueryContext
	order      []publishedpost.OrderOption
	inters     []Interceptor
	predicates []predicate.PublishedPost
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the PublishedPostQuery builder.
func (ppq *PublishedPostQuery) Where(ps ...predicate.PublishedPost) *PublishedPostQuery {
	ppq.predicates = append(ppq.predicates, ps...)
	return ppq
}

// Limit the number of records to be returned by this query.
func (ppq *PublishedPostQuery) Limit(limit int) *PublishedPostQuery {
	ppq.ctx.Limit = &limit
	return ppq
}

// Offset to start from.
func (ppq *PublishedPostQuery) Offset(offset int) *PublishedPostQuery {
	ppq.ctx.Offset = &offset
	return ppq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ppq *PublishedPostQuery) Unique(unique bool) *PublishedPostQuery {
	ppq.ctx.Unique = &unique
	return ppq
}

// Order specifies how the records should be ordered.
func (ppq *PublishedPostQuery) Order(o ...publishedpost.OrderOption) *PublishedPostQuery {
	ppq.order = append(ppq.order, o...)
	return ppq
}

// First returns the first PublishedPost entity from the query.
// Returns a *NotFoundError when no PublishedPost was found.
func (ppq *PublishedPostQuery) First(ctx context.Context) (*PublishedPost, error) {
	nodes, err := ppq.Limit(1).All(setContextOp(ctx, ppq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{publishedpost.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ppq *PublishedPostQuery) FirstX(ctx context.Context) *PublishedPost {
	node, err := ppq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first PublishedPost ID from the query.
// Returns a *NotFoundError when no PublishedPost ID was found.
func (ppq *PublishedPostQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ppq.Limit(1).IDs(setContextOp(ctx, ppq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{publishedpost.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ppq *PublishedPostQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ppq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single PublishedPost entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one PublishedPost entity is found.
// Returns a *NotFoundError when no PublishedPost entities are found.
func (ppq *PublishedPostQuery) Only(ctx context.Context) (*PublishedPost, error) {
	nodes, err := ppq.Limit(2).All(setContextOp(ctx, ppq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{publishedpost.Label}
	default:
		return nil, &NotSingularError{publishedpost.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ppq *PublishedPostQuery) OnlyX(ctx context.Context) *PublishedPost {
	node, err := ppq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only PublishedPost ID in the query.
// Returns a *NotSingularError when more than one PublishedPost ID is found.
// Returns a *NotFoundError when no entities are found.
func (ppq *PublishedPostQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ppq.Limit(2).IDs(setContextOp(ctx, ppq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{publishedpost.Label}
	default:
		err = &NotSingularError{publishedpost.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ppq *PublishedPostQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ppq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of PublishedPosts.
func (ppq *PublishedPostQuery) All(ctx context.Context) ([]*PublishedPost, error) {
	ctx = setContextOp(ctx, ppq.ctx, ent.OpQueryAll)
	if err := ppq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*PublishedPost, *PublishedPostQuery]()
	return withInterceptors[[]*PublishedPost](ctx, ppq, qr, ppq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ppq *PublishedPostQuery) AllX(ctx context.Context) []*PublishedPost {
	nodes, err := ppq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of PublishedPost IDs.
func (ppq *PublishedPostQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ppq.ctx.Unique == nil && ppq.path != nil {
		ppq.Unique(true)
	}
	ctx = setContextOp(ctx, ppq.ctx, ent.OpQueryIDs)
	if err = ppq.Select(publishedpost.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ppq *PublishedPostQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ppq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ppq *PublishedPostQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ppq.ctx, ent.OpQueryCount)
	if err := ppq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ppq, querierCount[*PublishedPostQuery](), ppq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ppq *PublishedPostQuery) CountX(ctx context.Context) int {
	count, err := ppq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ppq *PublishedPostQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ppq.ctx, ent.OpQueryExist)
	switch _, err := ppq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ppq *PublishedPostQuery) ExistX(ctx context.Context) bool {
	exist, err := ppq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the PublishedPostQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ppq *PublishedPostQuery) Clone() *PublishedPostQuery {
	if ppq == nil {
		return nil
	}
	return &PublishedPostQuery{
		config:     ppq.config,
		ctx:        ppq.ctx.Clone(),
		order:      append([]publishedpost.OrderOption{}, ppq.order...),
		inters:     append([]Interceptor{}, ppq.inters...),
		predicates: append([]predicate.PublishedPost{}, ppq.predicates...),
		// clone intermediate query.
		sql:  ppq.sql.Clone(),
		path: ppq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.PublishedPost.Query().
//		GroupBy(publishedpost.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ppq *PublishedPostQuery) GroupBy(field string, fields ...string) *PublishedPostGroupBy {
	ppq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &PublishedPostGroupBy{build: ppq}
	grbuild.flds = &ppq.ctx.Fields
	grbuild.label = publishedpost.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.PublishedPost.Query().
//		Select(publishedpost.FieldUserID).
//		Scan(ctx, &v)
func (ppq *PublishedPostQuery) Select(fields ...string) *PublishedPostSelect {
	ppq.ctx.Fields = append(ppq.ctx.Fields, fields...)
	sbuild := &PublishedPostSelect{PublishedPostQuery: ppq}
	sbuild.label = publishedpost.Label
	sbuild.flds, sbuild.scan = &ppq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a PublishedPostSelect configured with the given aggregations.
func (ppq *PublishedPostQuery) Aggregate(fns ...AggregateFunc) *PublishedPostSelect {
	return ppq.Select().Aggregate(fns...)
}

func (ppq *PublishedPostQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ppq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ppq); err != nil {
				return err
			}
		}
	}
	for _, f := range ppq.ctx.Fields {
		if !publishedpost.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ppq.path != nil {
		prev, err := ppq.path(ctx)
		if err != nil {
			return err
		}
		ppq.sql = prev
	}
	return nil
}

func (ppq *PublishedPostQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*PublishedPost, error) {
	var (
		nodes = []*PublishedPost{}
		_spec = ppq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*PublishedPost).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &PublishedPost{config: ppq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ppq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ppq *PublishedPostQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ppq.querySpec()
	_spec.Node.Columns = ppq.ctx.Fields
	if len(ppq.ctx.Fields) > 0 {
		_spec.Unique = ppq.ctx.Unique != nil && *ppq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ppq.driver, _spec)
}

func (ppq *PublishedPostQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(publishedpost.Table, publishedpost.Columns, sqlgraph.NewFieldSpec(publishedpost.FieldID, field.TypeUUID))
	_spec.From = ppq.sql
	if unique := ppq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ppq.path != nil {
		_spec.Unique = true
	}
	if fields := ppq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, publishedpost.FieldID)
		for i := range fields {
			if fields[i] != publishedpost.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ppq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ppq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ppq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ppq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ppq *PublishedPostQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ppq.driver.Dialect())
	t1 := builder.Table(publishedpost.Table)
	columns := ppq.ctx.Fields
	if len(columns) == 0 {
		columns = publishedpost.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ppq.sql != nil {
		selector = ppq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ppq.ctx.Unique != nil && *ppq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ppq.predicates {
		p(selector)
	}
	for _, p := range ppq.order {
		p(selector)
	}
	if offset := ppq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ppq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// PublishedPostGroupBy is the group-by builder for PublishedPost entities.
type PublishedPostGroupBy struct {
	selector
	build *PublishedPostQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ppgb *PublishedPostGroupBy) Aggregate(fns ...AggregateFunc) *PublishedPostGroupBy {
	ppgb.fns = append(ppgb.fns, fns...)
	return ppgb
}

// Scan applies the selector query and scans the result into the given value.
func (ppgb *PublishedPostGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ppgb.build.ctx, ent.OpQueryGroupBy)
	if err := ppgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PublishedPostQuery, *PublishedPostGroupBy](ctx, ppgb.build, ppgb, ppgb.build.inters, v)
}

func (ppgb *PublishedPostGroupBy) sqlScan(ctx context.Context, root *PublishedPostQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ppgb.fns))
	for _, fn := range ppgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ppgb.flds)+len(ppgb.fns))
		for _, f := range *ppgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ppgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ppgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// PublishedPostSelect is the builder for selecting fields of PublishedPost entities.
type PublishedPostSelect struct {
	*PublishedPostQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (pps *PublishedPostSelect) Aggregate(fns ...AggregateFunc) *PublishedPostSelect {
	pps.fns = append(pps.fns, fns...)
	return pps
}

// Scan applies the selector query and scans the result into the given value.
func (pps *PublishedPostSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pps.ctx, ent.OpQuerySelect)
	if err := pps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PublishedPostQuery, *PublishedPostSelect](ctx, pps.PublishedPostQuery, pps, pps.inters, v)
}

func (pps *PublishedPostSelect) sqlScan(ctx context.Context, root *PublishedPostQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(pps.fns))
	for _, fn := range pps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*pps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

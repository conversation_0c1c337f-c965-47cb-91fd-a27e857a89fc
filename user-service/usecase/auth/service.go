package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"

	"github.com/social-content-ai/pkg-shared/auth"
	"github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/ent/user"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/pkg/notification"
	"github.com/social-content-ai/user-service/pkg/verification"
	"github.com/social-content-ai/user-service/usecase/session"
)

// service implements the UseCase interface
type service struct {
	readDB             *ent.Client
	writeDB            *ent.Client
	jwtManager         *auth.JWTManager
	sessionUC          session.UseCase
	logger             logging.Logger
	kafkaProducer      *kafka.Producer
	notificationClient *notification.Client
	verificationSvc    *verification.TokenService
}

// NewService creates a new auth service
func NewService(
	readDB, writeDB *ent.Client,
	jwtManager *auth.JWTManager,
	sessionUC session.UseCase,
	logger logging.Logger,
	kafkaProducer *kafka.Producer,
	notificationClient *notification.Client,
	verificationSvc *verification.TokenService,
) UseCase {
	return &service{
		readDB:             readDB,
		writeDB:            writeDB,
		jwtManager:         jwtManager,
		sessionUC:          sessionUC,
		logger:             logger,
		kafkaProducer:      kafkaProducer,
		notificationClient: notificationClient,
		verificationSvc:    verificationSvc,
	}
}

// Register creates a new user account
func (s *service) Register(ctx context.Context, req *models.RegisterRequest) (*models.RegisterResponse, error) {
	// Validate email format
	email := strings.ToLower(strings.TrimSpace(req.Email))
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	// Check if user already exists
	exists, err := s.readDB.User.Query().
		Where(user.Email(email)).
		Where(user.DeletedAtIsNil()).
		Exist(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to check if user exists")
		return nil, fmt.Errorf("failed to check user existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("EMAIL_ALREADY_EXISTS")
	}

	// Validate password strength
	if len(req.Password) < 8 {
		return nil, fmt.Errorf("WEAK_PASSWORD")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash password")
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Combine first and last name
	fullName := strings.TrimSpace(req.FirstName + " " + req.LastName)

	// Create user
	newUser, err := s.writeDB.User.Create().
		SetEmail(email).
		SetPasswordHash(string(hashedPassword)).
		SetFullName(fullName).
		SetRole(user.RoleUser). // Default role
		SetIsVerified(false).   // Require email verification
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("email", email).Error("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id": newUser.ID,
		"email":   email,
	}).Info("User registered successfully")

	// Send email verification
	verificationSent := false
	if s.verificationSvc != nil && s.notificationClient != nil {
		// Generate verification token
		verificationToken, err := s.verificationSvc.GenerateEmailVerificationToken(newUser.ID.String(), email)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", newUser.ID).Error("Failed to generate verification token")
		} else {
			// Send verification email via notification service
			err = s.notificationClient.SendEmailVerification(ctx, newUser.ID.String(), email, verificationToken)
			if err != nil {
				s.logger.WithError(err).WithField("user_id", newUser.ID).Error("Failed to send verification email")
			} else {
				verificationSent = true
				s.logger.WithField("user_id", newUser.ID).Info("Verification email sent successfully")
			}
		}
	}

	// Send Kafka event for user registration
	if s.kafkaProducer != nil {
		userEvent := kafka.NewUserEvent(kafka.EventTypeUserRegistered, newUser.ID.String())
		userEvent.BaseEvent.WithData("email", email).
			WithData("first_name", req.FirstName).
			WithData("last_name", req.LastName).
			WithData("verification_sent", strconv.FormatBool(verificationSent))

		userEvent.Email = email
		userEvent.FirstName = req.FirstName
		userEvent.LastName = req.LastName
		userEvent.Role = string(user.RoleUser)

		err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
		if err != nil {
			s.logger.WithError(err).Error("Failed to publish user registration event")
		} else {
			s.logger.WithField("user_id", newUser.ID).Info("User registration event published")
		}
	}

	return &models.RegisterResponse{
		UserID:           uint64(newUser.ID.ID()),
		Email:            newUser.Email,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		EmailVerified:    newUser.IsVerified,
		VerificationSent: verificationSent,
		Message:          "Registration successful. Please check your email for verification.",
	}, nil
}

// Login authenticates user and returns tokens
func (s *service) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	// Get user by email
	userEntity, err := s.readDB.User.
		Query().
		Where(user.Email(strings.ToLower(req.Email))).
		Where(user.DeletedAtIsNil()).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			s.logger.WithField("email", req.Email).Warn("Login attempt with non-existent email")
			return nil, fmt.Errorf("invalid credentials")
		}
		s.logger.WithError(err).WithField("email", req.Email).Error("Failed to get user for login")
		return nil, fmt.Errorf("failed to authenticate user: %w", err)
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(userEntity.PasswordHash), []byte(req.Password)); err != nil {
		s.logger.WithField("user_id", userEntity.ID).Warn("Login attempt with invalid password")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is verified
	if !userEntity.IsVerified {
		return nil, fmt.Errorf("account not verified")
	}

	// Check if 2FA is enabled
	if userEntity.TwoFactorEnabled {
		// For 2FA users, return partial response
		return &LoginResponse{
			User:        ToUserInfo(userEntity),
			Requires2FA: true,
		}, nil
	}

	// Create session
	sessionDuration := 24 * time.Hour
	if req.Remember {
		sessionDuration = 7 * 24 * time.Hour // 7 days
	}

	sessionReq := &session.CreateSessionRequest{
		UserID:          userEntity.ID.String(),
		DeviceInfo:      req.DeviceInfo,
		IPAddress:       req.IPAddress,
		DurationSeconds: int64(sessionDuration.Seconds()),
	}

	sessionEntity, err := s.sessionUC.CreateSession(ctx, sessionReq)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to create session")
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Generate tokens
	permissions := []string{"read", "write"}
	if userEntity.Role == user.RoleAdmin {
		permissions = append(permissions, "admin")
	}

	accessToken, err := s.jwtManager.GenerateToken(
		userEntity.ID.String(),
		userEntity.Email,
		string(userEntity.Role),
		sessionEntity.ID.String(),
		permissions,
	)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to generate access token")
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(
		userEntity.ID.String(),
		sessionEntity.ID.String(),
	)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to generate refresh token")
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":    userEntity.ID,
		"session_id": sessionEntity.ID,
		"ip_address": req.IPAddress,
	}).Info("User logged in successfully")

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    sessionEntity.ExpiresAt.Format(time.RFC3339),
		User:         ToUserInfo(userEntity),
		Requires2FA:  false,
		SessionID:    sessionEntity.ID.String(),
	}, nil
}

// RefreshToken generates new access token using refresh token
func (s *service) RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error) {
	// Verify refresh token
	claims, err := s.jwtManager.VerifyToken(refreshToken)
	if err != nil {
		s.logger.WithError(err).Warn("Invalid refresh token")
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Get session
	sessionEntity, err := s.sessionUC.GetSession(ctx, claims.SessionID)
	if err != nil {
		s.logger.WithError(err).WithField("session_id", claims.SessionID).Error("Failed to get session")
		return nil, fmt.Errorf("invalid session: %w", err)
	}

	// Check if session is active
	if sessionEntity.Status != "active" {
		return nil, fmt.Errorf("session is not active")
	}

	// Get user
	userEntity, err := s.readDB.User.Get(ctx, uuid.MustParse(claims.UserID))
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("user not found")
		}
		s.logger.WithError(err).WithField("user_id", claims.UserID).Error("Failed to get user for refresh")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Generate new access token
	permissions := []string{"read", "write"}
	if userEntity.Role == user.RoleAdmin {
		permissions = append(permissions, "admin")
	}

	accessToken, err := s.jwtManager.GenerateToken(
		userEntity.ID.String(),
		userEntity.Email,
		string(userEntity.Role),
		sessionEntity.ID.String(),
		permissions,
	)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to generate new access token")
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":    userEntity.ID,
		"session_id": sessionEntity.ID,
	}).Info("Token refreshed successfully")

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken, // Keep the same refresh token
		ExpiresAt:    sessionEntity.ExpiresAt.Format(time.RFC3339),
		User:         ToUserInfo(userEntity),
		Requires2FA:  false,
		SessionID:    sessionEntity.ID.String(),
	}, nil
}

// Logout invalidates user session
func (s *service) Logout(ctx context.Context, sessionID string) error {
	err := s.sessionUC.RevokeSession(ctx, sessionID, "user_logout")
	if err != nil {
		s.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to revoke session")
		return fmt.Errorf("failed to logout: %w", err)
	}

	s.logger.WithField("session_id", sessionID).Info("User logged out successfully")
	return nil
}

// ValidateToken validates JWT token and returns user info
func (s *service) ValidateToken(ctx context.Context, token string) (*TokenValidationResult, error) {
	claims, err := s.jwtManager.VerifyToken(token)
	if err != nil {
		return &TokenValidationResult{Valid: false}, nil
	}

	// Check if session is still active
	sessionEntity, err := s.sessionUC.GetSession(ctx, claims.SessionID)
	if err != nil {
		return &TokenValidationResult{Valid: false}, nil
	}

	if sessionEntity.Status != "active" {
		return &TokenValidationResult{Valid: false}, nil
	}

	claimsMap := map[string]string{
		"user_id":    claims.UserID,
		"email":      claims.Email,
		"role":       claims.Role,
		"session_id": claims.SessionID,
	}

	return &TokenValidationResult{
		Valid:     true,
		UserID:    claims.UserID,
		Email:     claims.Email,
		Role:      claims.Role,
		SessionID: claims.SessionID,
		ExpiresAt: claims.ExpiresAt.Format(time.RFC3339),
		Claims:    claimsMap,
	}, nil
}

// ChangePassword changes user password
func (s *service) ChangePassword(ctx context.Context, req *ChangePasswordRequest) error {
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Get user
	userEntity, err := s.readDB.User.Get(ctx, userID)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("user not found")
		}
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to get user for password change")
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(userEntity.PasswordHash), []byte(req.CurrentPassword)); err != nil {
		s.logger.WithField("user_id", req.UserID).Warn("Password change attempt with invalid current password")
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash new password")
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password
	err = s.writeDB.User.UpdateOneID(userID).
		SetPasswordHash(string(hashedPassword)).
		Exec(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to update password")
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Revoke all sessions except current one (if any)
	// This forces re-login on all devices
	err = s.sessionUC.RevokeAllSessions(ctx, req.UserID, "")
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Warn("Failed to revoke sessions after password change")
	}

	s.logger.WithField("user_id", req.UserID).Info("Password changed successfully")
	return nil
}

// ResetPassword initiates password reset process
func (s *service) ResetPassword(ctx context.Context, email string) error {
	email = strings.ToLower(strings.TrimSpace(email))

	// Get user by email
	userEntity, err := s.readDB.User.
		Query().
		Where(user.Email(email)).
		Where(user.DeletedAtIsNil()).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Don't reveal if email exists or not for security
			s.logger.WithField("email", email).Info("Password reset requested for non-existent email")
			return nil
		}
		s.logger.WithError(err).WithField("email", email).Error("Failed to get user for password reset")
		return fmt.Errorf("failed to process password reset: %w", err)
	}

	// Generate password reset token using verification service
	if s.verificationSvc != nil && s.notificationClient != nil {
		// Generate verification token
		resetToken, err := s.verificationSvc.GeneratePasswordResetToken(userEntity.ID.String(), email)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to generate password reset token")
			return fmt.Errorf("failed to generate reset token: %w", err)
		}

		// Send password reset email via notification service
		err = s.notificationClient.SendPasswordResetEmail(ctx, userEntity.ID.String(), email, resetToken)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to send password reset email")
			return fmt.Errorf("failed to send password reset email: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id": userEntity.ID,
			"email":   email,
		}).Info("Password reset email sent successfully")

		// Send Kafka event for password reset request
		if s.kafkaProducer != nil {
			userEvent := kafka.NewUserEvent("user.password_reset_requested", userEntity.ID.String())
			userEvent.BaseEvent.WithData("email", email).
				WithData("requested_at", time.Now().Format(time.RFC3339))

			userEvent.Email = email

			err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
			if err != nil {
				s.logger.WithError(err).Error("Failed to publish password reset request event")
			} else {
				s.logger.WithField("user_id", userEntity.ID).Info("Password reset request event published")
			}
		}

		return nil
	}

	// Fallback to legacy method if verification service not available
	return s.resetPasswordLegacy(ctx, userEntity, email)
}

// resetPasswordLegacy handles password reset using legacy notification_settings method
func (s *service) resetPasswordLegacy(ctx context.Context, userEntity *ent.User, email string) error {
	// Generate secure reset token
	resetToken, err := s.generateResetToken()
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate reset token")
		return fmt.Errorf("failed to generate reset token: %w", err)
	}

	// Store reset token in database (using notification_settings field as temporary storage)
	// In production, you'd want a dedicated table for reset tokens
	tokenData := map[string]interface{}{
		"reset_token":   resetToken,
		"reset_expires": time.Now().Add(1 * time.Hour).Unix(), // 1 hour expiry
		"reset_user_id": userEntity.ID.String(),
	}

	// Update user with reset token data
	err = s.writeDB.User.UpdateOneID(userEntity.ID).
		SetNotificationSettings(tokenData).
		Exec(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to store reset token")
		return fmt.Errorf("failed to store reset token: %w", err)
	}

	// TODO: Send reset email with token
	// For now, just log the token (in production, this should be sent via email)
	s.logger.WithFields(map[string]interface{}{
		"user_id":     userEntity.ID,
		"email":       email,
		"reset_token": resetToken,
	}).Info("Password reset token generated (legacy method)")

	return nil
}

// generateResetToken generates a secure random token for password reset
func (s *service) generateResetToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// ConfirmPasswordReset confirms password reset with token
func (s *service) ConfirmPasswordReset(ctx context.Context, req *ConfirmPasswordResetRequest) error {
	if req.Token == "" {
		return fmt.Errorf("reset token is required")
	}

	if len(req.NewPassword) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	// Validate password reset token using verification service
	if s.verificationSvc != nil {
		claims, err := s.verificationSvc.ValidatePasswordResetToken(req.Token)
		if err != nil {
			s.logger.WithError(err).WithField("token", req.Token).Warn("Invalid password reset token")
			return fmt.Errorf("invalid or expired reset token: %w", err)
		}

		// Parse user ID from claims
		userID, err := uuid.Parse(claims.UserID)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", claims.UserID).Error("Invalid user ID in token")
			return fmt.Errorf("invalid user ID in token: %w", err)
		}

		// Get user from database
		targetUser, err := s.readDB.User.Get(ctx, userID)
		if err != nil {
			if ent.IsNotFound(err) {
				s.logger.WithField("user_id", claims.UserID).Warn("User not found for password reset")
				return fmt.Errorf("user not found")
			}
			s.logger.WithError(err).WithField("user_id", claims.UserID).Error("Failed to get user for password reset")
			return fmt.Errorf("failed to get user: %w", err)
		}

		// Check if email matches
		if targetUser.Email != claims.Email {
			s.logger.WithFields(map[string]interface{}{
				"user_id":     claims.UserID,
				"token_email": claims.Email,
				"user_email":  targetUser.Email,
			}).Warn("Email mismatch in password reset token")
			return fmt.Errorf("email mismatch in password reset token")
		}

		// Hash new password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
		if err != nil {
			s.logger.WithError(err).Error("Failed to hash new password")
			return fmt.Errorf("failed to hash password: %w", err)
		}

		// Update password
		err = s.writeDB.User.UpdateOneID(targetUser.ID).
			SetPasswordHash(string(hashedPassword)).
			Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", targetUser.ID).Error("Failed to update password")
			return fmt.Errorf("failed to update password: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id": targetUser.ID,
			"email":   targetUser.Email,
		}).Info("Password reset completed successfully")

		// Send password changed notification
		if s.notificationClient != nil {
			err = s.notificationClient.SendSecurityAlert(ctx, targetUser.ID.String(), targetUser.Email, "password_changed", "Your password has been successfully changed.")
			if err != nil {
				s.logger.WithError(err).WithField("user_id", targetUser.ID).Error("Failed to send password changed notification")
				// Don't return error, password reset was successful
			}
		}

		// Send Kafka event for password reset
		if s.kafkaProducer != nil {
			userEvent := kafka.NewUserEvent("user.password_reset", targetUser.ID.String())
			userEvent.BaseEvent.WithData("email", targetUser.Email).
				WithData("reset_at", time.Now().Format(time.RFC3339))

			userEvent.Email = targetUser.Email

			err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
			if err != nil {
				s.logger.WithError(err).Error("Failed to publish password reset event")
			} else {
				s.logger.WithField("user_id", targetUser.ID).Info("Password reset event published")
			}
		}

		return nil
	}

	// Fallback to legacy method if verification service not available
	return s.confirmPasswordResetLegacy(ctx, req)
}

// confirmPasswordResetLegacy handles password reset using legacy notification_settings method
func (s *service) confirmPasswordResetLegacy(ctx context.Context, req *ConfirmPasswordResetRequest) error {
	// Find user with matching reset token
	users, err := s.readDB.User.
		Query().
		Where(user.DeletedAtIsNil()).
		All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to query users for password reset")
		return fmt.Errorf("failed to process password reset: %w", err)
	}

	var targetUser *ent.User
	for _, userEntity := range users {
		if userEntity.NotificationSettings != nil {
			if resetToken, exists := userEntity.NotificationSettings["reset_token"]; exists {
				if resetToken == req.Token {
					targetUser = userEntity
					break
				}
			}
		}
	}

	if targetUser == nil {
		return fmt.Errorf("invalid or expired reset token")
	}

	// Check if token is expired
	if targetUser.NotificationSettings != nil {
		if expiresInterface, exists := targetUser.NotificationSettings["reset_expires"]; exists {
			if expires, ok := expiresInterface.(float64); ok {
				if time.Now().Unix() > int64(expires) {
					return fmt.Errorf("reset token has expired")
				}
			}
		}
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash new password")
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password and clear reset token
	err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetPasswordHash(string(hashedPassword)).
		ClearNotificationSettings(). // Clear reset token data
		Exec(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", targetUser.ID).Error("Failed to update password")
		return fmt.Errorf("failed to update password: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id": targetUser.ID,
		"email":   targetUser.Email,
	}).Info("Password reset completed successfully (legacy method)")

	return nil
}

// VerifyEmail verifies a user's email using a verification token
func (s *service) VerifyEmail(ctx context.Context, request *models.VerifyEmailRequest) error {
	if request.Token == "" {
		return fmt.Errorf("verification token is required")
	}

	// Validate verification token using verification service
	if s.verificationSvc == nil {
		return fmt.Errorf("verification service not available")
	}

	claims, err := s.verificationSvc.ValidateEmailVerificationToken(request.Token)
	if err != nil {
		s.logger.WithError(err).WithField("token", request.Token).Warn("Invalid verification token")
		return fmt.Errorf("invalid or expired verification token: %w", err)
	}

	// Parse user ID from claims
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", claims.UserID).Error("Invalid user ID in token")
		return fmt.Errorf("invalid user ID in token: %w", err)
	}

	// Get user from database
	targetUser, err := s.readDB.User.Get(ctx, userID)
	if err != nil {
		if ent.IsNotFound(err) {
			s.logger.WithField("user_id", claims.UserID).Warn("User not found for email verification")
			return fmt.Errorf("user not found")
		}
		s.logger.WithError(err).WithField("user_id", claims.UserID).Error("Failed to get user for email verification")
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if email matches
	if targetUser.Email != claims.Email {
		s.logger.WithFields(map[string]interface{}{
			"user_id":     claims.UserID,
			"token_email": claims.Email,
			"user_email":  targetUser.Email,
		}).Warn("Email mismatch in verification token")
		return fmt.Errorf("email mismatch in verification token")
	}

	// Check if user is already verified
	if targetUser.IsVerified {
		s.logger.WithField("user_id", claims.UserID).Info("User email already verified")
		return nil // Not an error, just already verified
	}

	// Mark user as verified
	err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetIsVerified(true).
		Exec(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", targetUser.ID).Error("Failed to verify email")
		return fmt.Errorf("failed to verify email: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id": targetUser.ID,
		"email":   targetUser.Email,
	}).Info("Email verified successfully")

	// Send welcome email after verification
	if s.notificationClient != nil {
		err = s.notificationClient.SendWelcomeEmail(ctx, targetUser.ID.String(), targetUser.Email, targetUser.FullName)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", targetUser.ID).Error("Failed to send welcome email")
			// Don't return error, verification was successful
		}
	}

	// Send Kafka event for email verification
	if s.kafkaProducer != nil {
		userEvent := kafka.NewUserEvent(kafka.EventTypeUserEmailVerified, targetUser.ID.String())
		userEvent.BaseEvent.WithData("email", targetUser.Email).
			WithData("verified_at", time.Now().Format(time.RFC3339))

		userEvent.Email = targetUser.Email

		err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
		if err != nil {
			s.logger.WithError(err).Error("Failed to publish email verification event")
		} else {
			s.logger.WithField("user_id", targetUser.ID).Info("Email verification event published")
		}
	}

	return nil
}

// ResendVerification resends email verification to user
func (s *service) ResendVerification(ctx context.Context, request *models.ResendVerificationRequest) error {
	if request.Email == "" {
		return fmt.Errorf("email is required")
	}

	email := strings.ToLower(strings.TrimSpace(request.Email))

	// Get user by email
	userEntity, err := s.readDB.User.
		Query().
		Where(user.Email(email)).
		Where(user.DeletedAtIsNil()).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// Don't reveal if email exists or not for security
			s.logger.WithField("email", email).Info("Resend verification requested for non-existent email")
			return nil
		}
		s.logger.WithError(err).WithField("email", email).Error("Failed to get user for resend verification")
		return fmt.Errorf("failed to process resend verification: %w", err)
	}

	// Check if user is already verified
	if userEntity.IsVerified {
		s.logger.WithField("user_id", userEntity.ID).Info("Resend verification requested for already verified user")
		return fmt.Errorf("email is already verified")
	}

	// Send verification email
	if s.verificationSvc != nil && s.notificationClient != nil {
		// Generate new verification token
		verificationToken, err := s.verificationSvc.GenerateEmailVerificationToken(userEntity.ID.String(), email)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to generate verification token for resend")
			return fmt.Errorf("failed to generate verification token: %w", err)
		}

		// Send verification email via notification service
		err = s.notificationClient.SendEmailVerification(ctx, userEntity.ID.String(), email, verificationToken)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to resend verification email")
			return fmt.Errorf("failed to send verification email: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id": userEntity.ID,
			"email":   email,
		}).Info("Verification email resent successfully")
	} else {
		s.logger.Error("Verification service or notification client not available for resend")
		return fmt.Errorf("verification service not available")
	}

	return nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/order"
)

// Order is the model entity for the Order schema.
type Order struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// OrderNumber holds the value of the "order_number" field.
	OrderNumber string `json:"order_number,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// PackageID holds the value of the "package_id" field.
	PackageID uuid.UUID `json:"package_id,omitempty"`
	// PlanID holds the value of the "plan_id" field.
	PlanID uuid.UUID `json:"plan_id,omitempty"`
	// Type holds the value of the "type" field.
	Type order.Type `json:"type,omitempty"`
	// Amount holds the value of the "amount" field.
	Amount int64 `json:"amount,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// Status holds the value of the "status" field.
	Status order.Status `json:"status,omitempty"`
	// PaymentMethod holds the value of the "payment_method" field.
	PaymentMethod string `json:"payment_method,omitempty"`
	// PaymentID holds the value of the "payment_id" field.
	PaymentID string `json:"payment_id,omitempty"`
	// InvoiceID holds the value of the "invoice_id" field.
	InvoiceID string `json:"invoice_id,omitempty"`
	// Credits holds the value of the "credits" field.
	Credits int `json:"credits,omitempty"`
	// BillingAddress holds the value of the "billing_address" field.
	BillingAddress map[string]interface{} `json:"billing_address,omitempty"`
	// Items holds the value of the "items" field.
	Items []map[string]interface{} `json:"items,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// ProcessedAt holds the value of the "processed_at" field.
	ProcessedAt time.Time `json:"processed_at,omitempty"`
	// ExpiresAt holds the value of the "expires_at" field.
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	// FailureReason holds the value of the "failure_reason" field.
	FailureReason string `json:"failure_reason,omitempty"`
	// ExternalOrderID holds the value of the "external_order_id" field.
	ExternalOrderID string `json:"external_order_id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Order) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case order.FieldBillingAddress, order.FieldItems, order.FieldMetadata:
			values[i] = new([]byte)
		case order.FieldAmount, order.FieldCredits:
			values[i] = new(sql.NullInt64)
		case order.FieldOrderNumber, order.FieldType, order.FieldCurrency, order.FieldStatus, order.FieldPaymentMethod, order.FieldPaymentID, order.FieldInvoiceID, order.FieldFailureReason, order.FieldExternalOrderID:
			values[i] = new(sql.NullString)
		case order.FieldProcessedAt, order.FieldExpiresAt, order.FieldCreatedAt, order.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case order.FieldID, order.FieldUserID, order.FieldPackageID, order.FieldPlanID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Order fields.
func (o *Order) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case order.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				o.ID = *value
			}
		case order.FieldOrderNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_number", values[i])
			} else if value.Valid {
				o.OrderNumber = value.String
			}
		case order.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				o.UserID = *value
			}
		case order.FieldPackageID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field package_id", values[i])
			} else if value != nil {
				o.PackageID = *value
			}
		case order.FieldPlanID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field plan_id", values[i])
			} else if value != nil {
				o.PlanID = *value
			}
		case order.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				o.Type = order.Type(value.String)
			}
		case order.FieldAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				o.Amount = value.Int64
			}
		case order.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				o.Currency = value.String
			}
		case order.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				o.Status = order.Status(value.String)
			}
		case order.FieldPaymentMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_method", values[i])
			} else if value.Valid {
				o.PaymentMethod = value.String
			}
		case order.FieldPaymentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_id", values[i])
			} else if value.Valid {
				o.PaymentID = value.String
			}
		case order.FieldInvoiceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field invoice_id", values[i])
			} else if value.Valid {
				o.InvoiceID = value.String
			}
		case order.FieldCredits:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field credits", values[i])
			} else if value.Valid {
				o.Credits = int(value.Int64)
			}
		case order.FieldBillingAddress:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field billing_address", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &o.BillingAddress); err != nil {
					return fmt.Errorf("unmarshal field billing_address: %w", err)
				}
			}
		case order.FieldItems:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field items", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &o.Items); err != nil {
					return fmt.Errorf("unmarshal field items: %w", err)
				}
			}
		case order.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &o.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case order.FieldProcessedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field processed_at", values[i])
			} else if value.Valid {
				o.ProcessedAt = value.Time
			}
		case order.FieldExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_at", values[i])
			} else if value.Valid {
				o.ExpiresAt = value.Time
			}
		case order.FieldFailureReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field failure_reason", values[i])
			} else if value.Valid {
				o.FailureReason = value.String
			}
		case order.FieldExternalOrderID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field external_order_id", values[i])
			} else if value.Valid {
				o.ExternalOrderID = value.String
			}
		case order.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				o.CreatedAt = value.Time
			}
		case order.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				o.UpdatedAt = value.Time
			}
		default:
			o.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Order.
// This includes values selected through modifiers, order, etc.
func (o *Order) Value(name string) (ent.Value, error) {
	return o.selectValues.Get(name)
}

// Update returns a builder for updating this Order.
// Note that you need to call Order.Unwrap() before calling this method if this Order
// was returned from a transaction, and the transaction was committed or rolled back.
func (o *Order) Update() *OrderUpdateOne {
	return NewOrderClient(o.config).UpdateOne(o)
}

// Unwrap unwraps the Order entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (o *Order) Unwrap() *Order {
	_tx, ok := o.config.driver.(*txDriver)
	if !ok {
		panic("ent: Order is not a transactional entity")
	}
	o.config.driver = _tx.drv
	return o
}

// String implements the fmt.Stringer.
func (o *Order) String() string {
	var builder strings.Builder
	builder.WriteString("Order(")
	builder.WriteString(fmt.Sprintf("id=%v, ", o.ID))
	builder.WriteString("order_number=")
	builder.WriteString(o.OrderNumber)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", o.UserID))
	builder.WriteString(", ")
	builder.WriteString("package_id=")
	builder.WriteString(fmt.Sprintf("%v", o.PackageID))
	builder.WriteString(", ")
	builder.WriteString("plan_id=")
	builder.WriteString(fmt.Sprintf("%v", o.PlanID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", o.Type))
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", o.Amount))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(o.Currency)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", o.Status))
	builder.WriteString(", ")
	builder.WriteString("payment_method=")
	builder.WriteString(o.PaymentMethod)
	builder.WriteString(", ")
	builder.WriteString("payment_id=")
	builder.WriteString(o.PaymentID)
	builder.WriteString(", ")
	builder.WriteString("invoice_id=")
	builder.WriteString(o.InvoiceID)
	builder.WriteString(", ")
	builder.WriteString("credits=")
	builder.WriteString(fmt.Sprintf("%v", o.Credits))
	builder.WriteString(", ")
	builder.WriteString("billing_address=")
	builder.WriteString(fmt.Sprintf("%v", o.BillingAddress))
	builder.WriteString(", ")
	builder.WriteString("items=")
	builder.WriteString(fmt.Sprintf("%v", o.Items))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", o.Metadata))
	builder.WriteString(", ")
	builder.WriteString("processed_at=")
	builder.WriteString(o.ProcessedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("expires_at=")
	builder.WriteString(o.ExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("failure_reason=")
	builder.WriteString(o.FailureReason)
	builder.WriteString(", ")
	builder.WriteString("external_order_id=")
	builder.WriteString(o.ExternalOrderID)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(o.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(o.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Orders is a parsable slice of Order.
type Orders []*Order

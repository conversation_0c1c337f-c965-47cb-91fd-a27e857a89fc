# Production Environment Configuration

# Production server settings
server:
  env: "production"
  grpc_port: 50055
  http_port: 8085
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Production database settings
database:
  # Use PostgreSQL for production
  type: "postgres"
  host: "${DB_HOST}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  name: "${DB_NAME}"
  ssl_mode: "require"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Production OAuth settings (from environment)
oauth:
  facebook:
    client_id: "${FACEBOOK_CLIENT_ID}"
    client_secret: "${FACEBOOK_CLIENT_SECRET}"
    redirect_url: "${FACEBOOK_REDIRECT_URL}"
  
  twitter:
    client_id: "${TWITTER_CLIENT_ID}"
    client_secret: "${TWITTER_CLIENT_SECRET}"
    redirect_url: "${TWITTER_REDIRECT_URL}"
  
  instagram:
    client_id: "${INSTAGRAM_CLIENT_ID}"
    client_secret: "${INSTAGRAM_CLIENT_SECRET}"
    redirect_url: "${INSTAGRAM_REDIRECT_URL}"
  
  linkedin:
    client_id: "${LINKEDIN_CLIENT_ID}"
    client_secret: "${LINKEDIN_CLIENT_SECRET}"
    redirect_url: "${LINKEDIN_REDIRECT_URL}"
  
  youtube:
    client_id: "${YOUTUBE_CLIENT_ID}"
    client_secret: "${YOUTUBE_CLIENT_SECRET}"
    redirect_url: "${YOUTUBE_REDIRECT_URL}"
  
  tiktok:
    client_id: "${TIKTOK_CLIENT_ID}"
    client_secret: "${TIKTOK_CLIENT_SECRET}"
    redirect_url: "${TIKTOK_REDIRECT_URL}"

# Production JWT settings
jwt:
  secret_key: "${JWT_SECRET_KEY}"
  token_duration: "1h"
  refresh_duration: "24h"

# Production logging
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Production features
features:
  scheduled_posts: true
  analytics: true
  webhooks: true
  bulk_operations: true

# Production rate limiting
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# Production cleanup
cleanup:
  enabled: true
  interval: "24h"
  retain_failed_posts_days: 30
  retain_success_posts_days: 90

# Production monitoring
monitoring:
  enabled: true
  metrics_port: "9095"
  health_endpoint: "/health"

# Production services
services:
  user_service: "${USER_SERVICE_URL:user-service:50051}"
  asset_service: "${ASSET_SERVICE_URL:asset-service:50052}"
  ai_content_service: "${AI_CONTENT_SERVICE_URL:ai-content-service:50053}"
  credit_billing_service: "${CREDIT_BILLING_SERVICE_URL:credit-billing-service:50054}"
  notification_service: "${NOTIFICATION_SERVICE_URL:notification-service:50056}"

# Production Kafka
kafka:
  brokers: "${KAFKA_BROKERS:kafka:9092}"
  consumer_group: "integration-service"
  topics:
    notification_events: "notification.events"
  producer:
    retry_max: 5
    retry_backoff: "100ms"
    flush_timeout: "10s"
  consumer:
    session_timeout: "10s"
    heartbeat_interval: "3s"

# Production tracing
tracing:
  enabled: true
  service_name: "integration-service"
  jaeger_endpoint: "${JAEGER_ENDPOINT:http://jaeger:14268/api/traces}"
  sample_rate: 0.1

# Production CORS (more restrictive)
cors:
  allowed_origins: "${ALLOWED_ORIGINS:https://app.social-content-ai.com}"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true
  max_age: 86400

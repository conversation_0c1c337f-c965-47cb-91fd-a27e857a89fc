// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditReservationQuery is the builder for querying CreditReservation entities.
type CreditReservationQuery struct {
	config
	ctx        *QueryContext
	order      []creditreservation.OrderOption
	inters     []Interceptor
	predicates []predicate.CreditReservation
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CreditReservationQuery builder.
func (crq *CreditReservationQuery) Where(ps ...predicate.CreditReservation) *CreditReservationQuery {
	crq.predicates = append(crq.predicates, ps...)
	return crq
}

// Limit the number of records to be returned by this query.
func (crq *CreditReservationQuery) Limit(limit int) *CreditReservationQuery {
	crq.ctx.Limit = &limit
	return crq
}

// Offset to start from.
func (crq *CreditReservationQuery) Offset(offset int) *CreditReservationQuery {
	crq.ctx.Offset = &offset
	return crq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (crq *CreditReservationQuery) Unique(unique bool) *CreditReservationQuery {
	crq.ctx.Unique = &unique
	return crq
}

// Order specifies how the records should be ordered.
func (crq *CreditReservationQuery) Order(o ...creditreservation.OrderOption) *CreditReservationQuery {
	crq.order = append(crq.order, o...)
	return crq
}

// First returns the first CreditReservation entity from the query.
// Returns a *NotFoundError when no CreditReservation was found.
func (crq *CreditReservationQuery) First(ctx context.Context) (*CreditReservation, error) {
	nodes, err := crq.Limit(1).All(setContextOp(ctx, crq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{creditreservation.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (crq *CreditReservationQuery) FirstX(ctx context.Context) *CreditReservation {
	node, err := crq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CreditReservation ID from the query.
// Returns a *NotFoundError when no CreditReservation ID was found.
func (crq *CreditReservationQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = crq.Limit(1).IDs(setContextOp(ctx, crq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{creditreservation.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (crq *CreditReservationQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := crq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CreditReservation entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CreditReservation entity is found.
// Returns a *NotFoundError when no CreditReservation entities are found.
func (crq *CreditReservationQuery) Only(ctx context.Context) (*CreditReservation, error) {
	nodes, err := crq.Limit(2).All(setContextOp(ctx, crq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{creditreservation.Label}
	default:
		return nil, &NotSingularError{creditreservation.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (crq *CreditReservationQuery) OnlyX(ctx context.Context) *CreditReservation {
	node, err := crq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CreditReservation ID in the query.
// Returns a *NotSingularError when more than one CreditReservation ID is found.
// Returns a *NotFoundError when no entities are found.
func (crq *CreditReservationQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = crq.Limit(2).IDs(setContextOp(ctx, crq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{creditreservation.Label}
	default:
		err = &NotSingularError{creditreservation.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (crq *CreditReservationQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := crq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CreditReservations.
func (crq *CreditReservationQuery) All(ctx context.Context) ([]*CreditReservation, error) {
	ctx = setContextOp(ctx, crq.ctx, ent.OpQueryAll)
	if err := crq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CreditReservation, *CreditReservationQuery]()
	return withInterceptors[[]*CreditReservation](ctx, crq, qr, crq.inters)
}

// AllX is like All, but panics if an error occurs.
func (crq *CreditReservationQuery) AllX(ctx context.Context) []*CreditReservation {
	nodes, err := crq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CreditReservation IDs.
func (crq *CreditReservationQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if crq.ctx.Unique == nil && crq.path != nil {
		crq.Unique(true)
	}
	ctx = setContextOp(ctx, crq.ctx, ent.OpQueryIDs)
	if err = crq.Select(creditreservation.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (crq *CreditReservationQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := crq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (crq *CreditReservationQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, crq.ctx, ent.OpQueryCount)
	if err := crq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, crq, querierCount[*CreditReservationQuery](), crq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (crq *CreditReservationQuery) CountX(ctx context.Context) int {
	count, err := crq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (crq *CreditReservationQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, crq.ctx, ent.OpQueryExist)
	switch _, err := crq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (crq *CreditReservationQuery) ExistX(ctx context.Context) bool {
	exist, err := crq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CreditReservationQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (crq *CreditReservationQuery) Clone() *CreditReservationQuery {
	if crq == nil {
		return nil
	}
	return &CreditReservationQuery{
		config:     crq.config,
		ctx:        crq.ctx.Clone(),
		order:      append([]creditreservation.OrderOption{}, crq.order...),
		inters:     append([]Interceptor{}, crq.inters...),
		predicates: append([]predicate.CreditReservation{}, crq.predicates...),
		// clone intermediate query.
		sql:  crq.sql.Clone(),
		path: crq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CreditReservation.Query().
//		GroupBy(creditreservation.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (crq *CreditReservationQuery) GroupBy(field string, fields ...string) *CreditReservationGroupBy {
	crq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CreditReservationGroupBy{build: crq}
	grbuild.flds = &crq.ctx.Fields
	grbuild.label = creditreservation.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.CreditReservation.Query().
//		Select(creditreservation.FieldUserID).
//		Scan(ctx, &v)
func (crq *CreditReservationQuery) Select(fields ...string) *CreditReservationSelect {
	crq.ctx.Fields = append(crq.ctx.Fields, fields...)
	sbuild := &CreditReservationSelect{CreditReservationQuery: crq}
	sbuild.label = creditreservation.Label
	sbuild.flds, sbuild.scan = &crq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CreditReservationSelect configured with the given aggregations.
func (crq *CreditReservationQuery) Aggregate(fns ...AggregateFunc) *CreditReservationSelect {
	return crq.Select().Aggregate(fns...)
}

func (crq *CreditReservationQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range crq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, crq); err != nil {
				return err
			}
		}
	}
	for _, f := range crq.ctx.Fields {
		if !creditreservation.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if crq.path != nil {
		prev, err := crq.path(ctx)
		if err != nil {
			return err
		}
		crq.sql = prev
	}
	return nil
}

func (crq *CreditReservationQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CreditReservation, error) {
	var (
		nodes = []*CreditReservation{}
		_spec = crq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CreditReservation).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CreditReservation{config: crq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, crq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (crq *CreditReservationQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := crq.querySpec()
	_spec.Node.Columns = crq.ctx.Fields
	if len(crq.ctx.Fields) > 0 {
		_spec.Unique = crq.ctx.Unique != nil && *crq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, crq.driver, _spec)
}

func (crq *CreditReservationQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(creditreservation.Table, creditreservation.Columns, sqlgraph.NewFieldSpec(creditreservation.FieldID, field.TypeUUID))
	_spec.From = crq.sql
	if unique := crq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if crq.path != nil {
		_spec.Unique = true
	}
	if fields := crq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, creditreservation.FieldID)
		for i := range fields {
			if fields[i] != creditreservation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := crq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := crq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := crq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := crq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (crq *CreditReservationQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(crq.driver.Dialect())
	t1 := builder.Table(creditreservation.Table)
	columns := crq.ctx.Fields
	if len(columns) == 0 {
		columns = creditreservation.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if crq.sql != nil {
		selector = crq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if crq.ctx.Unique != nil && *crq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range crq.predicates {
		p(selector)
	}
	for _, p := range crq.order {
		p(selector)
	}
	if offset := crq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := crq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CreditReservationGroupBy is the group-by builder for CreditReservation entities.
type CreditReservationGroupBy struct {
	selector
	build *CreditReservationQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (crgb *CreditReservationGroupBy) Aggregate(fns ...AggregateFunc) *CreditReservationGroupBy {
	crgb.fns = append(crgb.fns, fns...)
	return crgb
}

// Scan applies the selector query and scans the result into the given value.
func (crgb *CreditReservationGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, crgb.build.ctx, ent.OpQueryGroupBy)
	if err := crgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditReservationQuery, *CreditReservationGroupBy](ctx, crgb.build, crgb, crgb.build.inters, v)
}

func (crgb *CreditReservationGroupBy) sqlScan(ctx context.Context, root *CreditReservationQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(crgb.fns))
	for _, fn := range crgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*crgb.flds)+len(crgb.fns))
		for _, f := range *crgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*crgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := crgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CreditReservationSelect is the builder for selecting fields of CreditReservation entities.
type CreditReservationSelect struct {
	*CreditReservationQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (crs *CreditReservationSelect) Aggregate(fns ...AggregateFunc) *CreditReservationSelect {
	crs.fns = append(crs.fns, fns...)
	return crs
}

// Scan applies the selector query and scans the result into the given value.
func (crs *CreditReservationSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, crs.ctx, ent.OpQuerySelect)
	if err := crs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditReservationQuery, *CreditReservationSelect](ctx, crs.CreditReservationQuery, crs, crs.inters, v)
}

func (crs *CreditReservationSelect) sqlScan(ctx context.Context, root *CreditReservationQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(crs.fns))
	for _, fn := range crs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*crs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := crs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

#!/bin/bash

# Script to generate all protobuf files for Social Content AI microservices
# This script ensures all proto files are generated with proper dependencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if protoc is installed
check_protoc() {
    if ! command -v protoc &> /dev/null; then
        print_error "protoc is not installed. Please install Protocol Buffers compiler."
        print_error "Visit: https://grpc.io/docs/protoc-installation/"
        exit 1
    fi
    
    print_status "Found protoc version: $(protoc --version)"
}

# Check if Go protobuf plugins are installed
check_go_plugins() {
    if ! command -v protoc-gen-go &> /dev/null; then
        print_error "protoc-gen-go is not installed."
        print_error "Run: go install google.golang.org/protobuf/cmd/protoc-gen-go@latest"
        exit 1
    fi
    
    if ! command -v protoc-gen-go-grpc &> /dev/null; then
        print_error "protoc-gen-go-grpc is not installed."
        print_error "Run: go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest"
        exit 1
    fi
    
    print_status "Go protobuf plugins are installed"
}

# Clean previous generated files
clean_generated() {
    print_status "Cleaning previous generated files..."
    find . -name "*.pb.go" -delete 2>/dev/null || true
    find . -name "*_grpc.pb.go" -delete 2>/dev/null || true
    print_success "Cleaned generated files"
}

# Generate protobuf files for a service
generate_service() {
    local service=$1
    local service_path=$2
    
    if [ ! -d "$service_path" ]; then
        print_warning "Service directory $service_path does not exist, skipping..."
        return 0
    fi
    
    if [ ! -f "$service_path"/*.proto ]; then
        print_warning "No .proto files found in $service_path, skipping..."
        return 0
    fi
    
    print_status "Generating $service service proto files..."
    
    protoc --go_out=. --go_opt=paths=source_relative \
           --go-grpc_out=. --go-grpc_opt=paths=source_relative \
           -I . \
           "$service_path"/*.proto
    
    if [ $? -eq 0 ]; then
        print_success "Generated $service service proto files"
    else
        print_error "Failed to generate $service service proto files"
        exit 1
    fi
}

# Validate generated files
validate_generated() {
    print_status "Validating generated files..."
    
    local pb_files=$(find . -name "*.pb.go" | wc -l)
    local grpc_files=$(find . -name "*_grpc.pb.go" | wc -l)
    
    print_status "Generated $pb_files .pb.go files"
    print_status "Generated $grpc_files _grpc.pb.go files"
    
    if [ $pb_files -eq 0 ]; then
        print_error "No .pb.go files were generated"
        exit 1
    fi
    
    print_success "Validation completed"
}

# Main execution
main() {
    print_status "Starting protobuf generation for Social Content AI microservices"
    print_status "Working directory: $(pwd)"
    
    # Pre-checks
    check_protoc
    check_go_plugins
    
    # Clean previous files
    if [ "$1" = "--clean" ] || [ "$1" = "-c" ]; then
        clean_generated
    fi
    
    # Generate in dependency order (common first)
    print_status "Generating protobuf files in dependency order..."
    
    # 1. Common types (no dependencies)
    generate_service "common" "common/v1"
    
    # 2. Core services
    generate_service "user" "user/v1"
    generate_service "credit" "credit/v1"
    generate_service "asset" "asset/v1"
    generate_service "notification" "notification/v1"
    generate_service "analytics" "analytics/v1"
    
    # 3. Business logic services (depend on common and core)
    generate_service "ai-content" "ai-content/v1"
    generate_service "content-mgmt" "content-mgmt/v1"
    generate_service "integration" "integration/v1"
    generate_service "rag-processing" "rag-processing/v1"
    generate_service "data-mgmt" "data-mgmt/v1"
    
    # Validate results
    validate_generated
    
    print_success "All protobuf files generated successfully!"
    print_status "You can now use these generated files in your Go microservices"
    print_status "Import example: github.com/social-content-ai/proto-shared/user/v1"
}

# Show usage if help requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Generate protobuf files for Social Content AI microservices"
    echo ""
    echo "Options:"
    echo "  -c, --clean    Clean previous generated files before generating"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Generate all proto files"
    echo "  $0 --clean      # Clean and generate all proto files"
    echo ""
    exit 0
fi

# Run main function
main "$@"

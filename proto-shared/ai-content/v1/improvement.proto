syntax = "proto3";

package ai_content.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/ai-content/v1;aicontentv1";

// ContentImprovementRequest for improving existing content
message ContentImprovementRequest {
  string user_id = 1;
  string original_text = 2;
  string improvement_type = 3; // grammar, style, engagement, etc.
  string target_tone = 4;      // Target tone for improvement
  string target_length = 5;    // shorter, longer, same
  string platform = 6;         // Target platform for optimization
  string context = 7;          // Additional context
  string model = 8;            // AI model to use
  string workspace_id = 9;     // Workspace context
}

// ContentImprovementResponse with improved content
message ContentImprovementResponse {
  string id = 1;
  string original_text = 2;
  string improved_text = 3;
  string improvement_type = 4;
  string target_tone = 5;
  string target_length = 6;
  string platform = 7;
  float readability_score = 8;    // 1-10 scale
  float engagement_potential = 9;  // 1-10 scale
  float sentiment_score = 10;      // -1 to 1 scale
  int32 credits_used = 11;
  google.protobuf.Timestamp created_at = 12;
}

// GetImprovementRequest for retrieving an improvement
message GetImprovementRequest {
  string id = 1;
}

// ListImprovementsRequest for listing user improvements
message ListImprovementsRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string improvement_type = 3; // Filter by improvement type
  string platform = 4;         // Filter by platform
}

// ListImprovementsResponse with paginated improvements
message ListImprovementsResponse {
  repeated ContentImprovementResponse improvements = 1;
  common.v1.PaginationResponse pagination = 2;
}

// ContentImprovementService provides AI content improvement
service ContentImprovementService {
  rpc ImproveContent(ContentImprovementRequest) returns (ContentImprovementResponse);
  rpc GetImprovement(GetImprovementRequest) returns (ContentImprovementResponse);
  rpc ListImprovements(ListImprovementsRequest) returns (ListImprovementsResponse);
}

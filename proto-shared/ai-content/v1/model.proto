syntax = "proto3";

package ai_content.v1;

option go_package = "github.com/social-content-ai/proto-shared/ai-content/v1;aicontentv1";

// AIModel represents an available AI model
message AIModel {
  string id = 1;
  string name = 2;           // Display name (e.g., "GPT-4 Turbo")
  string provider = 3;       // openai, anthropic, google
  string type = 4;           // text, image, multimodal
  int32 credit_cost = 5;     // Credits required per request
  bool is_available = 6;     // Whether model is currently available
  string description = 7;    // Model description
  repeated string capabilities = 8; // List of capabilities
  int32 max_tokens = 9;      // Maximum tokens supported
  float response_time_avg = 10; // Average response time in seconds
}

// GetModelsRequest for listing available models
message GetModelsRequest {
  string type = 1;           // Filter by type (text, image, multimodal)
  bool available_only = 2;   // Only return available models
}

// GetModelsResponse with list of models
message GetModelsResponse {
  repeated AIModel models = 1;
}

// GetModelRequest for getting specific model info
message GetModelRequest {
  string id = 1;
}

// ModelUsageStats for model usage statistics
message ModelUsageStats {
  string model_id = 1;
  string model_name = 2;
  int32 total_requests = 3;
  int32 total_credits_used = 4;
  float avg_response_time = 5;
  float success_rate = 6;    // Percentage of successful requests
}

// GetModelStatsRequest for model statistics
message GetModelStatsRequest {
  string user_id = 1;        // Optional: stats for specific user
  string date_from = 2;      // Format: YYYY-MM-DD
  string date_to = 3;        // Format: YYYY-MM-DD
}

// GetModelStatsResponse with model statistics
message GetModelStatsResponse {
  repeated ModelUsageStats stats = 1;
  int32 total_requests = 2;
  int32 total_credits_used = 3;
}

// AIModelService provides AI model management
service AIModelService {
  rpc GetModels(GetModelsRequest) returns (GetModelsResponse);
  rpc GetModel(GetModelRequest) returns (AIModel);
  rpc GetModelStats(GetModelStatsRequest) returns (GetModelStatsResponse);
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: ai-content/v1/improvement.proto

package aicontentv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ContentImprovementServiceClient is the client API for ContentImprovementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ContentImprovementServiceClient interface {
	ImproveContent(ctx context.Context, in *ContentImprovementRequest, opts ...grpc.CallOption) (*ContentImprovementResponse, error)
	GetImprovement(ctx context.Context, in *GetImprovementRequest, opts ...grpc.CallOption) (*ContentImprovementResponse, error)
	ListImprovements(ctx context.Context, in *ListImprovementsRequest, opts ...grpc.CallOption) (*ListImprovementsResponse, error)
}

type contentImprovementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContentImprovementServiceClient(cc grpc.ClientConnInterface) ContentImprovementServiceClient {
	return &contentImprovementServiceClient{cc}
}

func (c *contentImprovementServiceClient) ImproveContent(ctx context.Context, in *ContentImprovementRequest, opts ...grpc.CallOption) (*ContentImprovementResponse, error) {
	out := new(ContentImprovementResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentImprovementService/ImproveContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentImprovementServiceClient) GetImprovement(ctx context.Context, in *GetImprovementRequest, opts ...grpc.CallOption) (*ContentImprovementResponse, error) {
	out := new(ContentImprovementResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentImprovementService/GetImprovement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentImprovementServiceClient) ListImprovements(ctx context.Context, in *ListImprovementsRequest, opts ...grpc.CallOption) (*ListImprovementsResponse, error) {
	out := new(ListImprovementsResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentImprovementService/ListImprovements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContentImprovementServiceServer is the server API for ContentImprovementService service.
// All implementations must embed UnimplementedContentImprovementServiceServer
// for forward compatibility
type ContentImprovementServiceServer interface {
	ImproveContent(context.Context, *ContentImprovementRequest) (*ContentImprovementResponse, error)
	GetImprovement(context.Context, *GetImprovementRequest) (*ContentImprovementResponse, error)
	ListImprovements(context.Context, *ListImprovementsRequest) (*ListImprovementsResponse, error)
	mustEmbedUnimplementedContentImprovementServiceServer()
}

// UnimplementedContentImprovementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedContentImprovementServiceServer struct {
}

func (UnimplementedContentImprovementServiceServer) ImproveContent(context.Context, *ContentImprovementRequest) (*ContentImprovementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImproveContent not implemented")
}
func (UnimplementedContentImprovementServiceServer) GetImprovement(context.Context, *GetImprovementRequest) (*ContentImprovementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetImprovement not implemented")
}
func (UnimplementedContentImprovementServiceServer) ListImprovements(context.Context, *ListImprovementsRequest) (*ListImprovementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImprovements not implemented")
}
func (UnimplementedContentImprovementServiceServer) mustEmbedUnimplementedContentImprovementServiceServer() {
}

// UnsafeContentImprovementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContentImprovementServiceServer will
// result in compilation errors.
type UnsafeContentImprovementServiceServer interface {
	mustEmbedUnimplementedContentImprovementServiceServer()
}

func RegisterContentImprovementServiceServer(s grpc.ServiceRegistrar, srv ContentImprovementServiceServer) {
	s.RegisterService(&ContentImprovementService_ServiceDesc, srv)
}

func _ContentImprovementService_ImproveContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContentImprovementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentImprovementServiceServer).ImproveContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentImprovementService/ImproveContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentImprovementServiceServer).ImproveContent(ctx, req.(*ContentImprovementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentImprovementService_GetImprovement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetImprovementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentImprovementServiceServer).GetImprovement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentImprovementService/GetImprovement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentImprovementServiceServer).GetImprovement(ctx, req.(*GetImprovementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentImprovementService_ListImprovements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImprovementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentImprovementServiceServer).ListImprovements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentImprovementService/ListImprovements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentImprovementServiceServer).ListImprovements(ctx, req.(*ListImprovementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ContentImprovementService_ServiceDesc is the grpc.ServiceDesc for ContentImprovementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ContentImprovementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai_content.v1.ContentImprovementService",
	HandlerType: (*ContentImprovementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImproveContent",
			Handler:    _ContentImprovementService_ImproveContent_Handler,
		},
		{
			MethodName: "GetImprovement",
			Handler:    _ContentImprovementService_GetImprovement_Handler,
		},
		{
			MethodName: "ListImprovements",
			Handler:    _ContentImprovementService_ListImprovements_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai-content/v1/improvement.proto",
}

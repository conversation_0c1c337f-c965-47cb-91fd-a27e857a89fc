-- Social Content AI Platform - Database Initialization Script
-- This script creates all required databases for the microservices

-- Create databases for each service
CREATE DATABASE user_db;
CREATE DATABASE ai_content_db;
CREATE DATABASE content_mgmt_db;
CREATE DATABASE credit_db;
CREATE DATABASE asset_db;
CREATE DATABASE integration_db;
CREATE DATABASE notification_db;
CREATE DATABASE analytics_db;

-- Grant permissions to postgres user (for development)
GRANT ALL PRIVILEGES ON DATABASE user_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE ai_content_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE content_mgmt_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE credit_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE asset_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE integration_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE notification_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE analytics_db TO postgres;

-- Create extensions that might be needed
\c user_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c ai_content_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c content_mgmt_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c credit_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c asset_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c integration_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c notification_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c analytics_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

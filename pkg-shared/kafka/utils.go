package kafka

import (
	"context"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TopicManager manages Kafka topics
type TopicManager struct {
	config *Config
	logger logging.Logger
}

// NewTopicManager creates a new topic manager
func NewTopicManager(config *Config, logger logging.Logger) *TopicManager {
	return &TopicManager{
		config: config,
		logger: logger,
	}
}

// CreateTopics creates all required topics if they don't exist
func (tm *TopicManager) CreateTopics(ctx context.Context) error {
	conn, err := kafka.Dial("tcp", tm.config.Brokers[0])
	if err != nil {
		return fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()
	
	// Get default topic configurations
	topicConfigs := GetDefaultTopicConfigs()
	
	// Convert to kafka-go format
	var topics []kafka.TopicConfig
	for _, config := range topicConfigs {
		topics = append(topics, kafka.TopicConfig{
			Topic:             config.Topic,
			NumPartitions:     config.NumPartitions,
			ReplicationFactor: config.ReplicationFactor,
		})
	}
	
	// Create topics
	err = conn.CreateTopics(topics...)
	if err != nil {
		tm.logger.WithError(err).Error("Failed to create topics")
		return fmt.Errorf("failed to create topics: %w", err)
	}
	
	tm.logger.WithField("topic_count", len(topics)).Info("Successfully created Kafka topics")
	return nil
}

// ListTopics lists all existing topics
func (tm *TopicManager) ListTopics(ctx context.Context) ([]string, error) {
	conn, err := kafka.DialContext(ctx, "tcp", tm.config.Brokers[0])
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()
	
	partitions, err := conn.ReadPartitions()
	if err != nil {
		return nil, fmt.Errorf("failed to read partitions: %w", err)
	}
	
	// Extract unique topic names
	topicSet := make(map[string]bool)
	for _, partition := range partitions {
		topicSet[partition.Topic] = true
	}
	
	var topics []string
	for topic := range topicSet {
		topics = append(topics, topic)
	}
	
	return topics, nil
}

// DeleteTopic deletes a topic
func (tm *TopicManager) DeleteTopic(ctx context.Context, topic string) error {
	conn, err := kafka.DialContext(ctx, "tcp", tm.config.Brokers[0])
	if err != nil {
		return fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()
	
	// Note: kafka-go doesn't have built-in topic deletion
	// This would require admin API calls or external tools
	tm.logger.WithField("topic", topic).Warn("Topic deletion not implemented - use Kafka admin tools")
	return fmt.Errorf("topic deletion not implemented")
}

// HealthCheck checks if Kafka is healthy
func (tm *TopicManager) HealthCheck(ctx context.Context) error {
	conn, err := kafka.DialContext(ctx, "tcp", tm.config.Brokers[0])
	if err != nil {
		return fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()
	
	// Try to get metadata
	_, err = conn.ReadPartitions()
	if err != nil {
		return fmt.Errorf("failed to read partitions: %w", err)
	}
	
	return nil
}

// GetTopicInfo gets information about a specific topic
func (tm *TopicManager) GetTopicInfo(ctx context.Context, topic string) (*TopicInfo, error) {
	conn, err := kafka.DialContext(ctx, "tcp", tm.config.Brokers[0])
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()
	
	partitions, err := conn.ReadPartitions(topic)
	if err != nil {
		return nil, fmt.Errorf("failed to read partitions for topic %s: %w", topic, err)
	}
	
	if len(partitions) == 0 {
		return nil, fmt.Errorf("topic %s not found", topic)
	}
	
	// Calculate replication factor (assuming all partitions have same replication)
	replicationFactor := len(partitions[0].Replicas)
	
	return &TopicInfo{
		Topic:             topic,
		NumPartitions:     len(partitions),
		ReplicationFactor: replicationFactor,
		Partitions:        partitions,
	}, nil
}

// TopicInfo represents information about a Kafka topic
type TopicInfo struct {
	Topic             string              `json:"topic"`
	NumPartitions     int                 `json:"num_partitions"`
	ReplicationFactor int                 `json:"replication_factor"`
	Partitions        []kafka.Partition   `json:"partitions"`
}

// RetryManager manages retry logic for failed messages
type RetryManager struct {
	config *Config
	logger logging.Logger
	policy RetryPolicy
}

// NewRetryManager creates a new retry manager
func NewRetryManager(config *Config, logger logging.Logger) *RetryManager {
	return &RetryManager{
		config: config,
		logger: logger,
		policy: GetDefaultRetryPolicy(),
	}
}

// ShouldRetry determines if a message should be retried
func (rm *RetryManager) ShouldRetry(retryCount int, err error) bool {
	if retryCount >= rm.policy.MaxRetries {
		return false
	}
	
	// Add logic to determine if error is retryable
	// For now, retry all errors except validation errors
	if isValidationError(err) {
		return false
	}
	
	return true
}

// GetRetryDelay calculates the delay before next retry
func (rm *RetryManager) GetRetryDelay(retryCount int) time.Duration {
	if retryCount <= 0 {
		return rm.policy.InitialInterval
	}
	
	// Exponential backoff with jitter
	delay := time.Duration(float64(rm.policy.InitialInterval) * 
		(rm.policy.Multiplier * float64(retryCount)))
	
	if delay > rm.policy.MaxInterval {
		delay = rm.policy.MaxInterval
	}
	
	return delay
}

// isValidationError checks if an error is a validation error
func isValidationError(err error) bool {
	// Simple check - in production, you might want more sophisticated error classification
	return err != nil && (
		fmt.Sprintf("%v", err) == "validation failed" ||
		fmt.Sprintf("%v", err) == "event data validation failed")
}

// EventPublisher provides a high-level interface for publishing events
type EventPublisher struct {
	producer *Producer
	logger   logging.Logger
}

// NewEventPublisher creates a new event publisher
func NewEventPublisher(config *Config, logger logging.Logger) *EventPublisher {
	producer := NewProducer(config, logger)
	return &EventPublisher{
		producer: producer,
		logger:   logger,
	}
}

// PublishUserRegistered publishes a user registered event
func (ep *EventPublisher) PublishUserRegistered(ctx context.Context, userID, email, firstName, lastName string) error {
	event := NewUserEvent(EventTypeUserRegistered, userID)
	event.Email = email
	event.FirstName = firstName
	event.LastName = lastName
	event.WithPriority(PriorityHigh)
	event.WithData("email", email)
	event.WithData("first_name", firstName)
	event.WithData("last_name", lastName)
	
	return ep.producer.PublishUserEvent(ctx, event)
}

// PublishContentGenerated publishes a content generated event
func (ep *EventPublisher) PublishContentGenerated(ctx context.Context, userID, contentID, contentType, title string, creditsUsed int) error {
	event := NewContentEvent(EventTypeContentGenerated, userID)
	event.ContentID = contentID
	event.ContentType = contentType
	event.ContentTitle = title
	event.WithData("content_id", contentID)
	event.WithData("content_type", contentType)
	event.WithData("content_title", title)
	event.WithData("credits_used", creditsUsed)
	
	return ep.producer.PublishContentEvent(ctx, event)
}

// PublishPostPublished publishes a post published event
func (ep *EventPublisher) PublishPostPublished(ctx context.Context, userID, postID, title, platform string) error {
	event := NewContentEvent(EventTypePostPublished, userID)
	event.PostID = postID
	event.ContentTitle = title
	event.Platform = platform
	event.WithData("post_id", postID)
	event.WithData("post_title", title)
	event.WithData("platform", platform)
	
	return ep.producer.PublishContentEvent(ctx, event)
}

// PublishCreditLow publishes a credit low event
func (ep *EventPublisher) PublishCreditLow(ctx context.Context, userID string, currentCredits, threshold int) error {
	event := NewBillingEvent(EventTypeCreditLow, userID)
	event.Credits = currentCredits
	event.WithPriority(PriorityHigh)
	event.WithData("current_credits", currentCredits)
	event.WithData("threshold_credits", threshold)
	
	return ep.producer.PublishBillingEvent(ctx, event)
}

// PublishSubscriptionExpired publishes a subscription expired event
func (ep *EventPublisher) PublishSubscriptionExpired(ctx context.Context, userID, subscriptionID, planName string) error {
	event := NewBillingEvent(EventTypeSubscriptionExpired, userID)
	event.SubscriptionID = subscriptionID
	event.PlanName = planName
	event.WithPriority(PriorityCritical)
	event.WithData("subscription_id", subscriptionID)
	event.WithData("plan_name", planName)
	event.WithData("expiration_date", time.Now().Format(time.RFC3339))
	
	return ep.producer.PublishBillingEvent(ctx, event)
}

// PublishIntegrationConnected publishes an integration connected event
func (ep *EventPublisher) PublishIntegrationConnected(ctx context.Context, userID, integrationID, integrationType, platformName string) error {
	event := NewIntegrationEvent(EventTypeIntegrationConnected, userID)
	event.IntegrationID = integrationID
	event.IntegrationType = integrationType
	event.PlatformName = platformName
	event.WithData("integration_id", integrationID)
	event.WithData("integration_type", integrationType)
	event.WithData("platform_name", platformName)
	
	return ep.producer.PublishIntegrationEvent(ctx, event)
}

// PublishAssetUploaded publishes an asset uploaded event
func (ep *EventPublisher) PublishAssetUploaded(ctx context.Context, userID, assetID, assetType, fileName string, fileSize int64) error {
	event := NewAssetEvent(EventTypeAssetUploaded, userID)
	event.AssetID = assetID
	event.AssetType = assetType
	event.FileName = fileName
	event.FileSize = fileSize
	event.WithData("asset_id", assetID)
	event.WithData("asset_type", assetType)
	event.WithData("file_name", fileName)
	event.WithData("file_size", fileSize)
	
	return ep.producer.PublishAssetEvent(ctx, event)
}

// PublishRAGProcessingStarted publishes a RAG processing started event
func (ep *EventPublisher) PublishRAGProcessingStarted(ctx context.Context, userID, processingID, documentID string) error {
	event := NewRAGEvent(EventTypeRAGProcessingStarted, userID)
	event.ProcessingID = processingID
	event.DocumentID = documentID
	event.Status = "started"
	event.Progress = 0
	event.WithData("processing_id", processingID)
	event.WithData("document_id", documentID)
	event.WithData("status", "started")
	event.WithData("progress", 0)
	
	return ep.producer.PublishRAGEvent(ctx, event)
}

// PublishSystemMaintenance publishes a system maintenance event
func (ep *EventPublisher) PublishSystemMaintenance(ctx context.Context, title, description string, startTime, endTime time.Time, affectedServices []string) error {
	event := NewSystemEvent(EventTypeSystemMaintenance)
	event.Title = title
	event.Description = description
	event.StartTime = startTime
	event.EndTime = endTime
	event.AffectedServices = affectedServices
	event.WithPriority(PriorityHigh)
	event.WithData("title", title)
	event.WithData("description", description)
	event.WithData("start_time", startTime.Format(time.RFC3339))
	event.WithData("end_time", endTime.Format(time.RFC3339))
	event.WithData("affected_services", affectedServices)
	
	return ep.producer.PublishSystemEvent(ctx, event)
}

// Close closes the event publisher
func (ep *EventPublisher) Close() error {
	return ep.producer.Close()
}

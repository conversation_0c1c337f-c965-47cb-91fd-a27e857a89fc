// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: content-mgmt/v1/workspace.proto

package contentmgmtv1

import (
	v1 "github.com/social-content-ai/proto-shared/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Workspace represents a collaborative workspace
type Workspace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description  string             `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	OwnerId      string             `protobuf:"bytes,4,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`                 // Workspace owner
	Plan         string             `protobuf:"bytes,5,opt,name=plan,proto3" json:"plan,omitempty"`                                      // free, pro, enterprise
	MemberLimit  int32              `protobuf:"varint,6,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`    // Maximum members allowed
	StorageLimit int32              `protobuf:"varint,7,opt,name=storage_limit,json=storageLimit,proto3" json:"storage_limit,omitempty"` // Storage limit in MB
	IsActive     bool               `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	Settings     *WorkspaceSettings `protobuf:"bytes,9,opt,name=settings,proto3" json:"settings,omitempty"`
	Timestamps   *v1.TimestampInfo  `protobuf:"bytes,10,opt,name=timestamps,proto3" json:"timestamps,omitempty"`
}

func (x *Workspace) Reset() {
	*x = Workspace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workspace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workspace) ProtoMessage() {}

func (x *Workspace) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workspace.ProtoReflect.Descriptor instead.
func (*Workspace) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{0}
}

func (x *Workspace) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Workspace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Workspace) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Workspace) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *Workspace) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

func (x *Workspace) GetMemberLimit() int32 {
	if x != nil {
		return x.MemberLimit
	}
	return 0
}

func (x *Workspace) GetStorageLimit() int32 {
	if x != nil {
		return x.StorageLimit
	}
	return 0
}

func (x *Workspace) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Workspace) GetSettings() *WorkspaceSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *Workspace) GetTimestamps() *v1.TimestampInfo {
	if x != nil {
		return x.Timestamps
	}
	return nil
}

// WorkspaceSettings contains workspace configuration
type WorkspaceSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllowPublicTemplates bool              `protobuf:"varint,1,opt,name=allow_public_templates,json=allowPublicTemplates,proto3" json:"allow_public_templates,omitempty"`                                  // Allow members to create public templates
	RequireApproval      bool              `protobuf:"varint,2,opt,name=require_approval,json=requireApproval,proto3" json:"require_approval,omitempty"`                                                   // Require approval for posts
	AllowAiGeneration    bool              `protobuf:"varint,3,opt,name=allow_ai_generation,json=allowAiGeneration,proto3" json:"allow_ai_generation,omitempty"`                                           // Allow AI content generation
	AllowedPlatforms     []string          `protobuf:"bytes,4,rep,name=allowed_platforms,json=allowedPlatforms,proto3" json:"allowed_platforms,omitempty"`                                                 // Allowed social platforms
	Branding             map[string]string `protobuf:"bytes,5,rep,name=branding,proto3" json:"branding,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Custom branding settings
	EnableAnalytics      bool              `protobuf:"varint,6,opt,name=enable_analytics,json=enableAnalytics,proto3" json:"enable_analytics,omitempty"`                                                   // Enable workspace analytics
}

func (x *WorkspaceSettings) Reset() {
	*x = WorkspaceSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceSettings) ProtoMessage() {}

func (x *WorkspaceSettings) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceSettings.ProtoReflect.Descriptor instead.
func (*WorkspaceSettings) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{1}
}

func (x *WorkspaceSettings) GetAllowPublicTemplates() bool {
	if x != nil {
		return x.AllowPublicTemplates
	}
	return false
}

func (x *WorkspaceSettings) GetRequireApproval() bool {
	if x != nil {
		return x.RequireApproval
	}
	return false
}

func (x *WorkspaceSettings) GetAllowAiGeneration() bool {
	if x != nil {
		return x.AllowAiGeneration
	}
	return false
}

func (x *WorkspaceSettings) GetAllowedPlatforms() []string {
	if x != nil {
		return x.AllowedPlatforms
	}
	return nil
}

func (x *WorkspaceSettings) GetBranding() map[string]string {
	if x != nil {
		return x.Branding
	}
	return nil
}

func (x *WorkspaceSettings) GetEnableAnalytics() bool {
	if x != nil {
		return x.EnableAnalytics
	}
	return false
}

// WorkspaceMember represents a workspace member
type WorkspaceMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	WorkspaceId string                 `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	UserId      string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Email       string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`             // Member email
	Name        string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`               // Member display name
	Role        string                 `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`               // owner, admin, editor, viewer
	Status      string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`           // active, pending, suspended
	Permissions []string               `protobuf:"bytes,8,rep,name=permissions,proto3" json:"permissions,omitempty"` // Specific permissions
	JoinedAt    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	LastActive  *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_active,json=lastActive,proto3" json:"last_active,omitempty"`
}

func (x *WorkspaceMember) Reset() {
	*x = WorkspaceMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceMember) ProtoMessage() {}

func (x *WorkspaceMember) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceMember.ProtoReflect.Descriptor instead.
func (*WorkspaceMember) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{2}
}

func (x *WorkspaceMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceMember) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *WorkspaceMember) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *WorkspaceMember) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *WorkspaceMember) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspaceMember) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *WorkspaceMember) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WorkspaceMember) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *WorkspaceMember) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

func (x *WorkspaceMember) GetLastActive() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActive
	}
	return nil
}

// WorkspaceInvitation represents a workspace invitation
type WorkspaceInvitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	WorkspaceId     string                 `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	InviterId       string                 `protobuf:"bytes,3,opt,name=inviter_id,json=inviterId,proto3" json:"inviter_id,omitempty"`                   // User who sent invitation
	Email           string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`                                            // Invited email
	Role            string                 `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`                                              // Proposed role
	Status          string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`                                          // pending, accepted, declined, expired
	InvitationToken string                 `protobuf:"bytes,7,opt,name=invitation_token,json=invitationToken,proto3" json:"invitation_token,omitempty"` // Unique invitation token
	ExpiresAt       *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	RespondedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=responded_at,json=respondedAt,proto3" json:"responded_at,omitempty"`
}

func (x *WorkspaceInvitation) Reset() {
	*x = WorkspaceInvitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceInvitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceInvitation) ProtoMessage() {}

func (x *WorkspaceInvitation) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceInvitation.ProtoReflect.Descriptor instead.
func (*WorkspaceInvitation) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{3}
}

func (x *WorkspaceInvitation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkspaceInvitation) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *WorkspaceInvitation) GetInviterId() string {
	if x != nil {
		return x.InviterId
	}
	return ""
}

func (x *WorkspaceInvitation) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *WorkspaceInvitation) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *WorkspaceInvitation) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WorkspaceInvitation) GetInvitationToken() string {
	if x != nil {
		return x.InvitationToken
	}
	return ""
}

func (x *WorkspaceInvitation) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *WorkspaceInvitation) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WorkspaceInvitation) GetRespondedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RespondedAt
	}
	return nil
}

// CreateWorkspaceRequest for creating a new workspace
type CreateWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string             `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Owner ID
	Name        string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string             `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Plan        string             `protobuf:"bytes,4,opt,name=plan,proto3" json:"plan,omitempty"` // free, pro, enterprise
	Settings    *WorkspaceSettings `protobuf:"bytes,5,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *CreateWorkspaceRequest) Reset() {
	*x = CreateWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkspaceRequest) ProtoMessage() {}

func (x *CreateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{4}
}

func (x *CreateWorkspaceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

func (x *CreateWorkspaceRequest) GetSettings() *WorkspaceSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// UpdateWorkspaceRequest for updating workspace
type UpdateWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId      string             `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Must be owner or admin
	Name        *string            `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Description *string            `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	Plan        *string            `protobuf:"bytes,5,opt,name=plan,proto3,oneof" json:"plan,omitempty"`
	Settings    *WorkspaceSettings `protobuf:"bytes,6,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *UpdateWorkspaceRequest) Reset() {
	*x = UpdateWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkspaceRequest) ProtoMessage() {}

func (x *UpdateWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWorkspaceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetPlan() string {
	if x != nil && x.Plan != nil {
		return *x.Plan
	}
	return ""
}

func (x *UpdateWorkspaceRequest) GetSettings() *WorkspaceSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// GetWorkspaceRequest for retrieving workspace
type GetWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // For access control
}

func (x *GetWorkspaceRequest) Reset() {
	*x = GetWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceRequest) ProtoMessage() {}

func (x *GetWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{6}
}

func (x *GetWorkspaceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetWorkspaceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// ListWorkspacesRequest for listing user workspaces
type ListWorkspacesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pagination *v1.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Role       string                `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`     // Filter by user role
	Status     string                `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"` // Filter by workspace status
}

func (x *ListWorkspacesRequest) Reset() {
	*x = ListWorkspacesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspacesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspacesRequest) ProtoMessage() {}

func (x *ListWorkspacesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspacesRequest.ProtoReflect.Descriptor instead.
func (*ListWorkspacesRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{7}
}

func (x *ListWorkspacesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListWorkspacesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWorkspacesRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *ListWorkspacesRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// ListWorkspacesResponse with paginated workspaces
type ListWorkspacesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspaces  []*Workspace           `protobuf:"bytes,1,rep,name=workspaces,proto3" json:"workspaces,omitempty"`
	Pagination  *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Memberships []*WorkspaceMember     `protobuf:"bytes,3,rep,name=memberships,proto3" json:"memberships,omitempty"` // User's membership info
}

func (x *ListWorkspacesResponse) Reset() {
	*x = ListWorkspacesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkspacesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkspacesResponse) ProtoMessage() {}

func (x *ListWorkspacesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkspacesResponse.ProtoReflect.Descriptor instead.
func (*ListWorkspacesResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{8}
}

func (x *ListWorkspacesResponse) GetWorkspaces() []*Workspace {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

func (x *ListWorkspacesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListWorkspacesResponse) GetMemberships() []*WorkspaceMember {
	if x != nil {
		return x.Memberships
	}
	return nil
}

// InviteMemberRequest for inviting a member
type InviteMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	InviterId   string `protobuf:"bytes,2,opt,name=inviter_id,json=inviterId,proto3" json:"inviter_id,omitempty"` // User sending invitation
	Email       string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`                          // Email to invite
	Role        string `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`                            // Proposed role
	Message     string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`                      // Optional invitation message
}

func (x *InviteMemberRequest) Reset() {
	*x = InviteMemberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteMemberRequest) ProtoMessage() {}

func (x *InviteMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteMemberRequest.ProtoReflect.Descriptor instead.
func (*InviteMemberRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{9}
}

func (x *InviteMemberRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *InviteMemberRequest) GetInviterId() string {
	if x != nil {
		return x.InviterId
	}
	return ""
}

func (x *InviteMemberRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InviteMemberRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *InviteMemberRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// InviteMemberResponse with invitation info
type InviteMemberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitationId    string                 `protobuf:"bytes,1,opt,name=invitation_id,json=invitationId,proto3" json:"invitation_id,omitempty"`
	InvitationToken string                 `protobuf:"bytes,2,opt,name=invitation_token,json=invitationToken,proto3" json:"invitation_token,omitempty"`
	InvitationUrl   string                 `protobuf:"bytes,3,opt,name=invitation_url,json=invitationUrl,proto3" json:"invitation_url,omitempty"` // Full invitation URL
	ExpiresAt       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *InviteMemberResponse) Reset() {
	*x = InviteMemberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteMemberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteMemberResponse) ProtoMessage() {}

func (x *InviteMemberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteMemberResponse.ProtoReflect.Descriptor instead.
func (*InviteMemberResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{10}
}

func (x *InviteMemberResponse) GetInvitationId() string {
	if x != nil {
		return x.InvitationId
	}
	return ""
}

func (x *InviteMemberResponse) GetInvitationToken() string {
	if x != nil {
		return x.InvitationToken
	}
	return ""
}

func (x *InviteMemberResponse) GetInvitationUrl() string {
	if x != nil {
		return x.InvitationUrl
	}
	return ""
}

func (x *InviteMemberResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

// AcceptInvitationRequest for accepting invitation
type AcceptInvitationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitationToken string `protobuf:"bytes,1,opt,name=invitation_token,json=invitationToken,proto3" json:"invitation_token,omitempty"`
	UserId          string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // User accepting invitation
}

func (x *AcceptInvitationRequest) Reset() {
	*x = AcceptInvitationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptInvitationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptInvitationRequest) ProtoMessage() {}

func (x *AcceptInvitationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptInvitationRequest.ProtoReflect.Descriptor instead.
func (*AcceptInvitationRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{11}
}

func (x *AcceptInvitationRequest) GetInvitationToken() string {
	if x != nil {
		return x.InvitationToken
	}
	return ""
}

func (x *AcceptInvitationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// AcceptInvitationResponse with membership info
type AcceptInvitationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string           `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	Membership  *WorkspaceMember `protobuf:"bytes,2,opt,name=membership,proto3" json:"membership,omitempty"`
	Workspace   *Workspace       `protobuf:"bytes,3,opt,name=workspace,proto3" json:"workspace,omitempty"`
}

func (x *AcceptInvitationResponse) Reset() {
	*x = AcceptInvitationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptInvitationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptInvitationResponse) ProtoMessage() {}

func (x *AcceptInvitationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptInvitationResponse.ProtoReflect.Descriptor instead.
func (*AcceptInvitationResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{12}
}

func (x *AcceptInvitationResponse) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *AcceptInvitationResponse) GetMembership() *WorkspaceMember {
	if x != nil {
		return x.Membership
	}
	return nil
}

func (x *AcceptInvitationResponse) GetWorkspace() *Workspace {
	if x != nil {
		return x.Workspace
	}
	return nil
}

// UpdateMemberRoleRequest for updating member role
type UpdateMemberRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string   `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	AdminId     string   `protobuf:"bytes,2,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`    // User making the change
	MemberId    string   `protobuf:"bytes,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"` // Member to update
	NewRole     string   `protobuf:"bytes,4,opt,name=new_role,json=newRole,proto3" json:"new_role,omitempty"`
	Permissions []string `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"` // Specific permissions
}

func (x *UpdateMemberRoleRequest) Reset() {
	*x = UpdateMemberRoleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMemberRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemberRoleRequest) ProtoMessage() {}

func (x *UpdateMemberRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemberRoleRequest.ProtoReflect.Descriptor instead.
func (*UpdateMemberRoleRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateMemberRoleRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *UpdateMemberRoleRequest) GetAdminId() string {
	if x != nil {
		return x.AdminId
	}
	return ""
}

func (x *UpdateMemberRoleRequest) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

func (x *UpdateMemberRoleRequest) GetNewRole() string {
	if x != nil {
		return x.NewRole
	}
	return ""
}

func (x *UpdateMemberRoleRequest) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

// RemoveMemberRequest for removing a member
type RemoveMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	AdminId     string `protobuf:"bytes,2,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`    // User making the change
	MemberId    string `protobuf:"bytes,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"` // Member to remove
}

func (x *RemoveMemberRequest) Reset() {
	*x = RemoveMemberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveMemberRequest) ProtoMessage() {}

func (x *RemoveMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveMemberRequest.ProtoReflect.Descriptor instead.
func (*RemoveMemberRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{14}
}

func (x *RemoveMemberRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *RemoveMemberRequest) GetAdminId() string {
	if x != nil {
		return x.AdminId
	}
	return ""
}

func (x *RemoveMemberRequest) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

// ListMembersRequest for listing workspace members
type ListMembersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string                `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	UserId      string                `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // For access control
	Pagination  *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Role        string                `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`     // Filter by role
	Status      string                `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"` // Filter by status
}

func (x *ListMembersRequest) Reset() {
	*x = ListMembersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersRequest) ProtoMessage() {}

func (x *ListMembersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersRequest.ProtoReflect.Descriptor instead.
func (*ListMembersRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{15}
}

func (x *ListMembersRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListMembersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListMembersRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListMembersRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *ListMembersRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// ListMembersResponse with paginated members
type ListMembersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Members    []*WorkspaceMember     `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	Pagination *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListMembersResponse) Reset() {
	*x = ListMembersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMembersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMembersResponse) ProtoMessage() {}

func (x *ListMembersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMembersResponse.ProtoReflect.Descriptor instead.
func (*ListMembersResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{16}
}

func (x *ListMembersResponse) GetMembers() []*WorkspaceMember {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *ListMembersResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListInvitationsRequest for listing workspace invitations
type ListInvitationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string                `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	UserId      string                `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // For access control
	Pagination  *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Status      string                `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"` // Filter by status
}

func (x *ListInvitationsRequest) Reset() {
	*x = ListInvitationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInvitationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInvitationsRequest) ProtoMessage() {}

func (x *ListInvitationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInvitationsRequest.ProtoReflect.Descriptor instead.
func (*ListInvitationsRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{17}
}

func (x *ListInvitationsRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListInvitationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListInvitationsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListInvitationsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// ListInvitationsResponse with paginated invitations
type ListInvitationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Invitations []*WorkspaceInvitation `protobuf:"bytes,1,rep,name=invitations,proto3" json:"invitations,omitempty"`
	Pagination  *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListInvitationsResponse) Reset() {
	*x = ListInvitationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInvitationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInvitationsResponse) ProtoMessage() {}

func (x *ListInvitationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInvitationsResponse.ProtoReflect.Descriptor instead.
func (*ListInvitationsResponse) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{18}
}

func (x *ListInvitationsResponse) GetInvitations() []*WorkspaceInvitation {
	if x != nil {
		return x.Invitations
	}
	return nil
}

func (x *ListInvitationsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetWorkspaceStatsRequest for workspace statistics
type GetWorkspaceStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceId string `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // For access control
	DateFrom    string `protobuf:"bytes,3,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"` // Format: YYYY-MM-DD
	DateTo      string `protobuf:"bytes,4,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`       // Format: YYYY-MM-DD
}

func (x *GetWorkspaceStatsRequest) Reset() {
	*x = GetWorkspaceStatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkspaceStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkspaceStatsRequest) ProtoMessage() {}

func (x *GetWorkspaceStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkspaceStatsRequest.ProtoReflect.Descriptor instead.
func (*GetWorkspaceStatsRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{19}
}

func (x *GetWorkspaceStatsRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *GetWorkspaceStatsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetWorkspaceStatsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *GetWorkspaceStatsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

// WorkspaceStats contains workspace statistics
type WorkspaceStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalMembers        int32            `protobuf:"varint,1,opt,name=total_members,json=totalMembers,proto3" json:"total_members,omitempty"`
	ActiveMembers       int32            `protobuf:"varint,2,opt,name=active_members,json=activeMembers,proto3" json:"active_members,omitempty"` // Members active in last 30 days
	TotalPosts          int32            `protobuf:"varint,3,opt,name=total_posts,json=totalPosts,proto3" json:"total_posts,omitempty"`
	PublishedPosts      int32            `protobuf:"varint,4,opt,name=published_posts,json=publishedPosts,proto3" json:"published_posts,omitempty"`
	ScheduledPosts      int32            `protobuf:"varint,5,opt,name=scheduled_posts,json=scheduledPosts,proto3" json:"scheduled_posts,omitempty"`
	TotalTemplates      int32            `protobuf:"varint,6,opt,name=total_templates,json=totalTemplates,proto3" json:"total_templates,omitempty"`
	PublicTemplates     int32            `protobuf:"varint,7,opt,name=public_templates,json=publicTemplates,proto3" json:"public_templates,omitempty"`
	StorageUsed         int64            `protobuf:"varint,8,opt,name=storage_used,json=storageUsed,proto3" json:"storage_used,omitempty"`    // Storage used in bytes
	StorageLimit        int64            `protobuf:"varint,9,opt,name=storage_limit,json=storageLimit,proto3" json:"storage_limit,omitempty"` // Storage limit in bytes
	StorageUsagePercent float32          `protobuf:"fixed32,10,opt,name=storage_usage_percent,json=storageUsagePercent,proto3" json:"storage_usage_percent,omitempty"`
	ActivityByMember    map[string]int32 `protobuf:"bytes,11,rep,name=activity_by_member,json=activityByMember,proto3" json:"activity_by_member,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // member_id -> activity_count
}

func (x *WorkspaceStats) Reset() {
	*x = WorkspaceStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceStats) ProtoMessage() {}

func (x *WorkspaceStats) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceStats.ProtoReflect.Descriptor instead.
func (*WorkspaceStats) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{20}
}

func (x *WorkspaceStats) GetTotalMembers() int32 {
	if x != nil {
		return x.TotalMembers
	}
	return 0
}

func (x *WorkspaceStats) GetActiveMembers() int32 {
	if x != nil {
		return x.ActiveMembers
	}
	return 0
}

func (x *WorkspaceStats) GetTotalPosts() int32 {
	if x != nil {
		return x.TotalPosts
	}
	return 0
}

func (x *WorkspaceStats) GetPublishedPosts() int32 {
	if x != nil {
		return x.PublishedPosts
	}
	return 0
}

func (x *WorkspaceStats) GetScheduledPosts() int32 {
	if x != nil {
		return x.ScheduledPosts
	}
	return 0
}

func (x *WorkspaceStats) GetTotalTemplates() int32 {
	if x != nil {
		return x.TotalTemplates
	}
	return 0
}

func (x *WorkspaceStats) GetPublicTemplates() int32 {
	if x != nil {
		return x.PublicTemplates
	}
	return 0
}

func (x *WorkspaceStats) GetStorageUsed() int64 {
	if x != nil {
		return x.StorageUsed
	}
	return 0
}

func (x *WorkspaceStats) GetStorageLimit() int64 {
	if x != nil {
		return x.StorageLimit
	}
	return 0
}

func (x *WorkspaceStats) GetStorageUsagePercent() float32 {
	if x != nil {
		return x.StorageUsagePercent
	}
	return 0
}

func (x *WorkspaceStats) GetActivityByMember() map[string]int32 {
	if x != nil {
		return x.ActivityByMember
	}
	return nil
}

// DeleteWorkspaceRequest for deleting workspace
type DeleteWorkspaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                 // Must be owner
	ForceDelete bool   `protobuf:"varint,3,opt,name=force_delete,json=forceDelete,proto3" json:"force_delete,omitempty"` // Force delete even with members
}

func (x *DeleteWorkspaceRequest) Reset() {
	*x = DeleteWorkspaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_content_mgmt_v1_workspace_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkspaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkspaceRequest) ProtoMessage() {}

func (x *DeleteWorkspaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_mgmt_v1_workspace_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkspaceRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkspaceRequest) Descriptor() ([]byte, []int) {
	return file_content_mgmt_v1_workspace_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteWorkspaceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteWorkspaceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteWorkspaceRequest) GetForceDelete() bool {
	if x != nil {
		return x.ForceDelete
	}
	return false
}

var File_content_mgmt_v1_workspace_proto protoreflect.FileDescriptor

var file_content_mgmt_v1_workspace_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6f, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdf, 0x02, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3e,
	0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x38,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x22, 0x87, 0x03, 0x0a, 0x11, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12,
	0x2e, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x69, 0x5f, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x41, 0x69, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2b, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x4c, 0x0a, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x3b, 0x0a, 0x0d, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xcb, 0x02, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x6a, 0x6f,
	0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x22, 0x89, 0x03, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a,
	0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x22, 0xbb, 0x01, 0x0a,
	0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xfc, 0x01, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x17,
	0x0a, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x04,
	0x70, 0x6c, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x22, 0x3e, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd7, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73,
	0x22, 0x9b, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc8,
	0x01, 0x0a, 0x14, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x39,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x22, 0x5d, 0x0a, 0x17, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x18, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0a,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x38, 0x0a, 0x09, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6e,
	0x65, 0x77, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e,
	0x65, 0x77, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x70, 0x0a, 0x13, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x22, 0xba, 0x01, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3a, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x16, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x22, 0xc9, 0x04, 0x0a, 0x0e, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x73,
	0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x73,
	0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x12, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x79, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x42, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x42, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x1a, 0x43, 0x0a, 0x15, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x42, 0x79, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x32, 0xe0, 0x08, 0x0a, 0x10,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x56, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x27, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5b, 0x0a, 0x0c, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5e, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x4c, 0x0a, 0x0c, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x58, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x23, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x42, 0x49,
	0x5a, 0x47, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x6c, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x61, 0x69, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x6d, 0x67, 0x6d, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_content_mgmt_v1_workspace_proto_rawDescOnce sync.Once
	file_content_mgmt_v1_workspace_proto_rawDescData = file_content_mgmt_v1_workspace_proto_rawDesc
)

func file_content_mgmt_v1_workspace_proto_rawDescGZIP() []byte {
	file_content_mgmt_v1_workspace_proto_rawDescOnce.Do(func() {
		file_content_mgmt_v1_workspace_proto_rawDescData = protoimpl.X.CompressGZIP(file_content_mgmt_v1_workspace_proto_rawDescData)
	})
	return file_content_mgmt_v1_workspace_proto_rawDescData
}

var file_content_mgmt_v1_workspace_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_content_mgmt_v1_workspace_proto_goTypes = []interface{}{
	(*Workspace)(nil),                // 0: content_mgmt.v1.Workspace
	(*WorkspaceSettings)(nil),        // 1: content_mgmt.v1.WorkspaceSettings
	(*WorkspaceMember)(nil),          // 2: content_mgmt.v1.WorkspaceMember
	(*WorkspaceInvitation)(nil),      // 3: content_mgmt.v1.WorkspaceInvitation
	(*CreateWorkspaceRequest)(nil),   // 4: content_mgmt.v1.CreateWorkspaceRequest
	(*UpdateWorkspaceRequest)(nil),   // 5: content_mgmt.v1.UpdateWorkspaceRequest
	(*GetWorkspaceRequest)(nil),      // 6: content_mgmt.v1.GetWorkspaceRequest
	(*ListWorkspacesRequest)(nil),    // 7: content_mgmt.v1.ListWorkspacesRequest
	(*ListWorkspacesResponse)(nil),   // 8: content_mgmt.v1.ListWorkspacesResponse
	(*InviteMemberRequest)(nil),      // 9: content_mgmt.v1.InviteMemberRequest
	(*InviteMemberResponse)(nil),     // 10: content_mgmt.v1.InviteMemberResponse
	(*AcceptInvitationRequest)(nil),  // 11: content_mgmt.v1.AcceptInvitationRequest
	(*AcceptInvitationResponse)(nil), // 12: content_mgmt.v1.AcceptInvitationResponse
	(*UpdateMemberRoleRequest)(nil),  // 13: content_mgmt.v1.UpdateMemberRoleRequest
	(*RemoveMemberRequest)(nil),      // 14: content_mgmt.v1.RemoveMemberRequest
	(*ListMembersRequest)(nil),       // 15: content_mgmt.v1.ListMembersRequest
	(*ListMembersResponse)(nil),      // 16: content_mgmt.v1.ListMembersResponse
	(*ListInvitationsRequest)(nil),   // 17: content_mgmt.v1.ListInvitationsRequest
	(*ListInvitationsResponse)(nil),  // 18: content_mgmt.v1.ListInvitationsResponse
	(*GetWorkspaceStatsRequest)(nil), // 19: content_mgmt.v1.GetWorkspaceStatsRequest
	(*WorkspaceStats)(nil),           // 20: content_mgmt.v1.WorkspaceStats
	(*DeleteWorkspaceRequest)(nil),   // 21: content_mgmt.v1.DeleteWorkspaceRequest
	nil,                              // 22: content_mgmt.v1.WorkspaceSettings.BrandingEntry
	nil,                              // 23: content_mgmt.v1.WorkspaceStats.ActivityByMemberEntry
	(*v1.TimestampInfo)(nil),         // 24: common.v1.TimestampInfo
	(*timestamppb.Timestamp)(nil),    // 25: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),     // 26: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),    // 27: common.v1.PaginationResponse
	(*Empty)(nil),                    // 28: content_mgmt.v1.Empty
}
var file_content_mgmt_v1_workspace_proto_depIdxs = []int32{
	1,  // 0: content_mgmt.v1.Workspace.settings:type_name -> content_mgmt.v1.WorkspaceSettings
	24, // 1: content_mgmt.v1.Workspace.timestamps:type_name -> common.v1.TimestampInfo
	22, // 2: content_mgmt.v1.WorkspaceSettings.branding:type_name -> content_mgmt.v1.WorkspaceSettings.BrandingEntry
	25, // 3: content_mgmt.v1.WorkspaceMember.joined_at:type_name -> google.protobuf.Timestamp
	25, // 4: content_mgmt.v1.WorkspaceMember.last_active:type_name -> google.protobuf.Timestamp
	25, // 5: content_mgmt.v1.WorkspaceInvitation.expires_at:type_name -> google.protobuf.Timestamp
	25, // 6: content_mgmt.v1.WorkspaceInvitation.created_at:type_name -> google.protobuf.Timestamp
	25, // 7: content_mgmt.v1.WorkspaceInvitation.responded_at:type_name -> google.protobuf.Timestamp
	1,  // 8: content_mgmt.v1.CreateWorkspaceRequest.settings:type_name -> content_mgmt.v1.WorkspaceSettings
	1,  // 9: content_mgmt.v1.UpdateWorkspaceRequest.settings:type_name -> content_mgmt.v1.WorkspaceSettings
	26, // 10: content_mgmt.v1.ListWorkspacesRequest.pagination:type_name -> common.v1.PaginationRequest
	0,  // 11: content_mgmt.v1.ListWorkspacesResponse.workspaces:type_name -> content_mgmt.v1.Workspace
	27, // 12: content_mgmt.v1.ListWorkspacesResponse.pagination:type_name -> common.v1.PaginationResponse
	2,  // 13: content_mgmt.v1.ListWorkspacesResponse.memberships:type_name -> content_mgmt.v1.WorkspaceMember
	25, // 14: content_mgmt.v1.InviteMemberResponse.expires_at:type_name -> google.protobuf.Timestamp
	2,  // 15: content_mgmt.v1.AcceptInvitationResponse.membership:type_name -> content_mgmt.v1.WorkspaceMember
	0,  // 16: content_mgmt.v1.AcceptInvitationResponse.workspace:type_name -> content_mgmt.v1.Workspace
	26, // 17: content_mgmt.v1.ListMembersRequest.pagination:type_name -> common.v1.PaginationRequest
	2,  // 18: content_mgmt.v1.ListMembersResponse.members:type_name -> content_mgmt.v1.WorkspaceMember
	27, // 19: content_mgmt.v1.ListMembersResponse.pagination:type_name -> common.v1.PaginationResponse
	26, // 20: content_mgmt.v1.ListInvitationsRequest.pagination:type_name -> common.v1.PaginationRequest
	3,  // 21: content_mgmt.v1.ListInvitationsResponse.invitations:type_name -> content_mgmt.v1.WorkspaceInvitation
	27, // 22: content_mgmt.v1.ListInvitationsResponse.pagination:type_name -> common.v1.PaginationResponse
	23, // 23: content_mgmt.v1.WorkspaceStats.activity_by_member:type_name -> content_mgmt.v1.WorkspaceStats.ActivityByMemberEntry
	4,  // 24: content_mgmt.v1.WorkspaceService.CreateWorkspace:input_type -> content_mgmt.v1.CreateWorkspaceRequest
	6,  // 25: content_mgmt.v1.WorkspaceService.GetWorkspace:input_type -> content_mgmt.v1.GetWorkspaceRequest
	5,  // 26: content_mgmt.v1.WorkspaceService.UpdateWorkspace:input_type -> content_mgmt.v1.UpdateWorkspaceRequest
	7,  // 27: content_mgmt.v1.WorkspaceService.ListWorkspaces:input_type -> content_mgmt.v1.ListWorkspacesRequest
	21, // 28: content_mgmt.v1.WorkspaceService.DeleteWorkspace:input_type -> content_mgmt.v1.DeleteWorkspaceRequest
	9,  // 29: content_mgmt.v1.WorkspaceService.InviteMember:input_type -> content_mgmt.v1.InviteMemberRequest
	11, // 30: content_mgmt.v1.WorkspaceService.AcceptInvitation:input_type -> content_mgmt.v1.AcceptInvitationRequest
	13, // 31: content_mgmt.v1.WorkspaceService.UpdateMemberRole:input_type -> content_mgmt.v1.UpdateMemberRoleRequest
	14, // 32: content_mgmt.v1.WorkspaceService.RemoveMember:input_type -> content_mgmt.v1.RemoveMemberRequest
	15, // 33: content_mgmt.v1.WorkspaceService.ListMembers:input_type -> content_mgmt.v1.ListMembersRequest
	17, // 34: content_mgmt.v1.WorkspaceService.ListInvitations:input_type -> content_mgmt.v1.ListInvitationsRequest
	19, // 35: content_mgmt.v1.WorkspaceService.GetWorkspaceStats:input_type -> content_mgmt.v1.GetWorkspaceStatsRequest
	0,  // 36: content_mgmt.v1.WorkspaceService.CreateWorkspace:output_type -> content_mgmt.v1.Workspace
	0,  // 37: content_mgmt.v1.WorkspaceService.GetWorkspace:output_type -> content_mgmt.v1.Workspace
	0,  // 38: content_mgmt.v1.WorkspaceService.UpdateWorkspace:output_type -> content_mgmt.v1.Workspace
	8,  // 39: content_mgmt.v1.WorkspaceService.ListWorkspaces:output_type -> content_mgmt.v1.ListWorkspacesResponse
	28, // 40: content_mgmt.v1.WorkspaceService.DeleteWorkspace:output_type -> content_mgmt.v1.Empty
	10, // 41: content_mgmt.v1.WorkspaceService.InviteMember:output_type -> content_mgmt.v1.InviteMemberResponse
	12, // 42: content_mgmt.v1.WorkspaceService.AcceptInvitation:output_type -> content_mgmt.v1.AcceptInvitationResponse
	2,  // 43: content_mgmt.v1.WorkspaceService.UpdateMemberRole:output_type -> content_mgmt.v1.WorkspaceMember
	28, // 44: content_mgmt.v1.WorkspaceService.RemoveMember:output_type -> content_mgmt.v1.Empty
	16, // 45: content_mgmt.v1.WorkspaceService.ListMembers:output_type -> content_mgmt.v1.ListMembersResponse
	18, // 46: content_mgmt.v1.WorkspaceService.ListInvitations:output_type -> content_mgmt.v1.ListInvitationsResponse
	20, // 47: content_mgmt.v1.WorkspaceService.GetWorkspaceStats:output_type -> content_mgmt.v1.WorkspaceStats
	36, // [36:48] is the sub-list for method output_type
	24, // [24:36] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_content_mgmt_v1_workspace_proto_init() }
func file_content_mgmt_v1_workspace_proto_init() {
	if File_content_mgmt_v1_workspace_proto != nil {
		return
	}
	file_content_mgmt_v1_post_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_content_mgmt_v1_workspace_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workspace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceInvitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspacesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkspacesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InviteMemberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InviteMemberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptInvitationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptInvitationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMemberRoleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveMemberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMembersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInvitationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInvitationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkspaceStatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_content_mgmt_v1_workspace_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkspaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_content_mgmt_v1_workspace_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_content_mgmt_v1_workspace_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_mgmt_v1_workspace_proto_goTypes,
		DependencyIndexes: file_content_mgmt_v1_workspace_proto_depIdxs,
		MessageInfos:      file_content_mgmt_v1_workspace_proto_msgTypes,
	}.Build()
	File_content_mgmt_v1_workspace_proto = out.File
	file_content_mgmt_v1_workspace_proto_rawDesc = nil
	file_content_mgmt_v1_workspace_proto_goTypes = nil
	file_content_mgmt_v1_workspace_proto_depIdxs = nil
}

syntax = "proto3";

package content_mgmt.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";
import "common/v1/timestamp.proto";
import "content-mgmt/v1/post.proto";

option go_package = "github.com/social-content-ai/proto-shared/content-mgmt/v1;contentmgmtv1";

// Workspace represents a collaborative workspace
message Workspace {
  string id = 1;
  string name = 2;
  string description = 3;
  string owner_id = 4;           // Workspace owner
  string plan = 5;               // free, pro, enterprise
  int32 member_limit = 6;        // Maximum members allowed
  int32 storage_limit = 7;       // Storage limit in MB
  bool is_active = 8;
  WorkspaceSettings settings = 9;
  common.v1.TimestampInfo timestamps = 10;
}

// WorkspaceSettings contains workspace configuration
message WorkspaceSettings {
  bool allow_public_templates = 1;    // Allow members to create public templates
  bool require_approval = 2;          // Require approval for posts
  bool allow_ai_generation = 3;       // Allow AI content generation
  repeated string allowed_platforms = 4; // Allowed social platforms
  map<string, string> branding = 5;   // Custom branding settings
  bool enable_analytics = 6;          // Enable workspace analytics
}

// WorkspaceMember represents a workspace member
message WorkspaceMember {
  string id = 1;
  string workspace_id = 2;
  string user_id = 3;
  string email = 4;              // Member email
  string name = 5;               // Member display name
  string role = 6;               // owner, admin, editor, viewer
  string status = 7;             // active, pending, suspended
  repeated string permissions = 8; // Specific permissions
  google.protobuf.Timestamp joined_at = 9;
  google.protobuf.Timestamp last_active = 10;
}

// WorkspaceInvitation represents a workspace invitation
message WorkspaceInvitation {
  string id = 1;
  string workspace_id = 2;
  string inviter_id = 3;         // User who sent invitation
  string email = 4;              // Invited email
  string role = 5;               // Proposed role
  string status = 6;             // pending, accepted, declined, expired
  string invitation_token = 7;   // Unique invitation token
  google.protobuf.Timestamp expires_at = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp responded_at = 10;
}

// CreateWorkspaceRequest for creating a new workspace
message CreateWorkspaceRequest {
  string user_id = 1;            // Owner ID
  string name = 2;
  string description = 3;
  string plan = 4;               // free, pro, enterprise
  WorkspaceSettings settings = 5;
}

// UpdateWorkspaceRequest for updating workspace
message UpdateWorkspaceRequest {
  string id = 1;
  string user_id = 2;            // Must be owner or admin
  optional string name = 3;
  optional string description = 4;
  optional string plan = 5;
  WorkspaceSettings settings = 6;
}

// GetWorkspaceRequest for retrieving workspace
message GetWorkspaceRequest {
  string id = 1;
  string user_id = 2;            // For access control
}

// ListWorkspacesRequest for listing user workspaces
message ListWorkspacesRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string role = 3;               // Filter by user role
  string status = 4;             // Filter by workspace status
}

// ListWorkspacesResponse with paginated workspaces
message ListWorkspacesResponse {
  repeated Workspace workspaces = 1;
  common.v1.PaginationResponse pagination = 2;
  repeated WorkspaceMember memberships = 3; // User's membership info
}

// InviteMemberRequest for inviting a member
message InviteMemberRequest {
  string workspace_id = 1;
  string inviter_id = 2;         // User sending invitation
  string email = 3;              // Email to invite
  string role = 4;               // Proposed role
  string message = 5;            // Optional invitation message
}

// InviteMemberResponse with invitation info
message InviteMemberResponse {
  string invitation_id = 1;
  string invitation_token = 2;
  string invitation_url = 3;     // Full invitation URL
  google.protobuf.Timestamp expires_at = 4;
}

// AcceptInvitationRequest for accepting invitation
message AcceptInvitationRequest {
  string invitation_token = 1;
  string user_id = 2;            // User accepting invitation
}

// AcceptInvitationResponse with membership info
message AcceptInvitationResponse {
  string workspace_id = 1;
  WorkspaceMember membership = 2;
  Workspace workspace = 3;
}

// UpdateMemberRoleRequest for updating member role
message UpdateMemberRoleRequest {
  string workspace_id = 1;
  string admin_id = 2;           // User making the change
  string member_id = 3;          // Member to update
  string new_role = 4;
  repeated string permissions = 5; // Specific permissions
}

// RemoveMemberRequest for removing a member
message RemoveMemberRequest {
  string workspace_id = 1;
  string admin_id = 2;           // User making the change
  string member_id = 3;          // Member to remove
}

// ListMembersRequest for listing workspace members
message ListMembersRequest {
  string workspace_id = 1;
  string user_id = 2;            // For access control
  common.v1.PaginationRequest pagination = 3;
  string role = 4;               // Filter by role
  string status = 5;             // Filter by status
}

// ListMembersResponse with paginated members
message ListMembersResponse {
  repeated WorkspaceMember members = 1;
  common.v1.PaginationResponse pagination = 2;
}

// ListInvitationsRequest for listing workspace invitations
message ListInvitationsRequest {
  string workspace_id = 1;
  string user_id = 2;            // For access control
  common.v1.PaginationRequest pagination = 3;
  string status = 4;             // Filter by status
}

// ListInvitationsResponse with paginated invitations
message ListInvitationsResponse {
  repeated WorkspaceInvitation invitations = 1;
  common.v1.PaginationResponse pagination = 2;
}

// GetWorkspaceStatsRequest for workspace statistics
message GetWorkspaceStatsRequest {
  string workspace_id = 1;
  string user_id = 2;            // For access control
  string date_from = 3;          // Format: YYYY-MM-DD
  string date_to = 4;            // Format: YYYY-MM-DD
}

// WorkspaceStats contains workspace statistics
message WorkspaceStats {
  int32 total_members = 1;
  int32 active_members = 2;      // Members active in last 30 days
  int32 total_posts = 3;
  int32 published_posts = 4;
  int32 scheduled_posts = 5;
  int32 total_templates = 6;
  int32 public_templates = 7;
  int64 storage_used = 8;        // Storage used in bytes
  int64 storage_limit = 9;       // Storage limit in bytes
  float storage_usage_percent = 10;
  map<string, int32> activity_by_member = 11; // member_id -> activity_count
}

// DeleteWorkspaceRequest for deleting workspace
message DeleteWorkspaceRequest {
  string id = 1;
  string user_id = 2;            // Must be owner
  bool force_delete = 3;         // Force delete even with members
}

// WorkspaceService provides workspace collaboration operations
service WorkspaceService {
  // Workspace management
  rpc CreateWorkspace(CreateWorkspaceRequest) returns (Workspace);
  rpc GetWorkspace(GetWorkspaceRequest) returns (Workspace);
  rpc UpdateWorkspace(UpdateWorkspaceRequest) returns (Workspace);
  rpc ListWorkspaces(ListWorkspacesRequest) returns (ListWorkspacesResponse);
  rpc DeleteWorkspace(DeleteWorkspaceRequest) returns (Empty);
  
  // Member management
  rpc InviteMember(InviteMemberRequest) returns (InviteMemberResponse);
  rpc AcceptInvitation(AcceptInvitationRequest) returns (AcceptInvitationResponse);
  rpc UpdateMemberRole(UpdateMemberRoleRequest) returns (WorkspaceMember);
  rpc RemoveMember(RemoveMemberRequest) returns (Empty);
  rpc ListMembers(ListMembersRequest) returns (ListMembersResponse);
  
  // Invitation management
  rpc ListInvitations(ListInvitationsRequest) returns (ListInvitationsResponse);
  
  // Analytics
  rpc GetWorkspaceStats(GetWorkspaceStatsRequest) returns (WorkspaceStats);
}

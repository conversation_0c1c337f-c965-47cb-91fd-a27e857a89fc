package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PayPalConfig holds PayPal configuration
type PayPalConfig struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Environment  string `json:"environment"` // sandbox or live
	WebhookID    string `json:"webhook_id"`
	BaseURL      string `json:"base_url"`
	ReturnURL    string `json:"return_url"`
	CancelURL    string `json:"cancel_url"`
}

// PayPalUseCase defines the interface for PayPal business logic
type PayPalUseCase interface {
	InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error)
	ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error)
	CancelPayment(ctx context.Context, req *models.CancelPayPalPaymentRequest) (*models.CancelPayPalPaymentResponse, error)
	GetPaymentStatus(ctx context.Context, req *models.GetPayPalPaymentStatusRequest) (*models.GetPayPalPaymentStatusResponse, error)
	RefundPayment(ctx context.Context, req *models.RefundPayPalPaymentRequest) (*models.RefundPayPalPaymentResponse, error)
	CreateSubscription(ctx context.Context, req *models.CreatePayPalSubscriptionRequest) (*models.CreatePayPalSubscriptionResponse, error)
	CancelSubscription(ctx context.Context, req *models.CancelPayPalSubscriptionRequest) (*models.CancelPayPalSubscriptionResponse, error)
	GetSubscription(ctx context.Context, req *models.GetPayPalSubscriptionRequest) (*models.GetPayPalSubscriptionResponse, error)
}

// payPalProcessor implements the Processor interface for PayPal payments
type payPalProcessor struct {
	config       PayPalConfig
	paypalUC     PayPalUseCase
	paypalClient paypal.Client
	logger       logging.Logger
}

// NewPayPalProcessor creates a new PayPal payment processor
func NewPayPalProcessor(config PayPalConfig, paypalUC PayPalUseCase, paypalClient paypal.Client, logger logging.Logger) Processor {
	return &payPalProcessor{
		config:       config,
		paypalUC:     paypalUC,
		paypalClient: paypalClient,
		logger:       logger,
	}
}

// ProcessPayment processes a payment through PayPal
func (p *payPalProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id":        req.UserID,
		"amount":         req.Amount,
		"currency":       req.Currency,
		"payment_method": req.PaymentMethod,
		"order_id":       req.OrderID,
	}).Info("Processing PayPal payment")

	// Use PayPal usecase to initiate payment
	if p.paypalUC != nil {
		return p.processPaymentWithUseCase(ctx, req)
	}

	// Fallback to mock implementation
	return p.processPaymentMock(ctx, req)
}

// processPaymentWithUseCase processes payment using PayPal usecase
func (p *payPalProcessor) processPaymentWithUseCase(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// Convert PaymentRequest to InitiatePayPalPaymentRequest
	paypalReq := &models.InitiatePayPalPaymentRequest{
		UserID:      req.UserID,
		Amount:      req.Amount,
		Currency:    req.Currency,
		Purpose:     "credit_topup", // Default purpose
		Description: fmt.Sprintf("Payment for order %s", req.OrderID),
		ReturnURL:   p.config.ReturnURL,
		CancelURL:   p.config.CancelURL,
	}

	// If this is a subscription payment, set purpose and plan ID
	if planID, ok := req.Metadata["plan_id"].(string); ok {
		paypalReq.Purpose = "subscription"
		paypalReq.PlanID = planID
	}

	// Initiate PayPal payment
	paypalResp, err := p.paypalUC.InitiatePayment(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to initiate PayPal payment: %w", err)
	}

	if !paypalResp.Success {
		return nil, fmt.Errorf("PayPal payment initiation failed")
	}

	return &PaymentResponse{
		PaymentID:  paypalResp.PaymentID,
		Status:     "created",
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: paypalResp.ApprovalURL,
		PaymentInfo: map[string]interface{}{
			"payment_id":      paypalResp.PaymentID,
			"paypal_order_id": paypalResp.PayPalOrderID,
			"approval_url":    paypalResp.ApprovalURL,
			"cancel_url":      p.config.CancelURL,
			"return_url":      p.config.ReturnURL,
		},
		ExpiresAt: time.Now().Add(3 * time.Hour).Format(time.RFC3339),
	}, nil
}

// processPaymentMock processes payment using mock implementation
func (p *payPalProcessor) processPaymentMock(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// Create PayPal payment
	paypalPayment, err := p.createPayPalPayment(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal payment: %w", err)
	}

	return &PaymentResponse{
		PaymentID:  paypalPayment.PaymentID,
		Status:     paypalPayment.Status,
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: paypalPayment.ApprovalURL,
		PaymentInfo: map[string]interface{}{
			"payment_id":   paypalPayment.PaymentID,
			"approval_url": paypalPayment.ApprovalURL,
			"cancel_url":   paypalPayment.CancelURL,
			"return_url":   paypalPayment.ReturnURL,
		},
		ExpiresAt: time.Now().Add(3 * time.Hour).Format(time.RFC3339), // PayPal payments expire in 3 hours
	}, nil
}

// createPayPalPayment creates a PayPal payment
func (p *payPalProcessor) createPayPalPayment(ctx context.Context, req *PaymentRequest) (*models.PayPalPaymentInfo, error) {
	// For now, this is a mock implementation
	// In production, this would call PayPal API

	paymentID := uuid.New().String()

	// Mock PayPal payment creation
	paymentInfo := &models.PayPalPaymentInfo{
		PaymentID:   paymentID,
		ApprovalURL: fmt.Sprintf("%s/checkout?payment_id=%s", p.config.BaseURL, paymentID),
		CancelURL:   p.config.CancelURL,
		ReturnURL:   p.config.ReturnURL,
		Status:      "created",
	}

	p.logger.WithFields(map[string]interface{}{
		"payment_id":   paymentID,
		"approval_url": paymentInfo.ApprovalURL,
		"amount":       req.Amount,
		"currency":     req.Currency,
	}).Info("Created PayPal payment")

	return paymentInfo, nil
}

// RefundPayment refunds a PayPal payment
func (p *payPalProcessor) RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"amount":     req.Amount,
		"reason":     req.Reason,
	}).Info("Processing PayPal refund")

	// Use PayPal usecase if available
	if p.paypalUC != nil {
		return p.refundPaymentWithUseCase(ctx, req)
	}

	// Fallback to mock implementation
	return p.refundPaymentMock(ctx, req)
}

// refundPaymentWithUseCase refunds payment using PayPal usecase
func (p *payPalProcessor) refundPaymentWithUseCase(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	// First get payment status to get user ID
	statusReq := &models.GetPayPalPaymentStatusRequest{
		PaymentID: req.PaymentID,
		UserID:    "system", // We'll get the actual user ID from the payment record
	}

	statusResp, err := p.paypalUC.GetPaymentStatus(ctx, statusReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment status: %w", err)
	}

	// Convert RefundRequest to RefundPayPalPaymentRequest
	// Note: In a real implementation, you would extract user ID from the payment record
	// For now, we'll use a system user ID since RefundRequest doesn't include user ID
	paypalReq := &models.RefundPayPalPaymentRequest{
		PaymentID: req.PaymentID,
		UserID:    "system", // Use system user for refunds
		Amount:    req.Amount,
		Reason:    req.Reason,
	}

	// Log the payment status for debugging
	p.logger.WithFields(map[string]interface{}{
		"payment_id":     statusResp.PaymentID,
		"payment_status": statusResp.Status,
		"payment_amount": statusResp.Amount,
	}).Info("Retrieved payment status for refund")

	// Call PayPal usecase
	paypalResp, err := p.paypalUC.RefundPayment(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to refund PayPal payment: %w", err)
	}

	if !paypalResp.Success {
		return nil, fmt.Errorf("PayPal refund failed: %s", paypalResp.ErrorMessage)
	}

	return &RefundResponse{
		RefundID:  paypalResp.RefundID,
		PaymentID: req.PaymentID,
		Amount:    paypalResp.Amount,
		Status:    paypalResp.Status,
		Reason:    req.Reason,
	}, nil
}

// refundPaymentMock refunds payment using mock implementation
func (p *payPalProcessor) refundPaymentMock(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	// Mock refund implementation
	refundID := uuid.New().String()

	return &RefundResponse{
		RefundID:  refundID,
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// CancelPayment cancels a PayPal payment
func (p *payPalProcessor) CancelPayment(ctx context.Context, paymentID string) error {
	p.logger.WithField("payment_id", paymentID).Info("Cancelling PayPal payment")

	// Mock cancellation
	return nil
}

// GetPaymentStatus gets the status of a PayPal payment
func (p *payPalProcessor) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	p.logger.WithField("payment_id", paymentID).Info("Getting PayPal payment status")

	// Use PayPal usecase if available
	if p.paypalUC != nil {
		return p.getPaymentStatusWithUseCase(ctx, paymentID)
	}

	// Fallback to mock implementation
	return p.getPaymentStatusMock(ctx, paymentID)
}

// getPaymentStatusWithUseCase gets payment status using PayPal usecase
func (p *payPalProcessor) getPaymentStatusWithUseCase(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	// Convert to PayPal request
	paypalReq := &models.GetPayPalPaymentStatusRequest{
		PaymentID: paymentID,
		UserID:    "system", // Use system user for status checks
	}

	// Call PayPal usecase
	paypalResp, err := p.paypalUC.GetPaymentStatus(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get PayPal payment status: %w", err)
	}

	return &PaymentStatus{
		PaymentID:   paypalResp.PaymentID,
		Status:      paypalResp.Status,
		Amount:      paypalResp.Amount,
		Currency:    paypalResp.Currency,
		ProcessedAt: paypalResp.UpdatedAt.Format(time.RFC3339),
	}, nil
}

// getPaymentStatusMock gets payment status using mock implementation
func (p *payPalProcessor) getPaymentStatusMock(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	// Mock status check
	return &PaymentStatus{
		PaymentID:   paymentID,
		Status:      "completed",
		Amount:      1000, // Mock amount
		Currency:    "USD",
		ProcessedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// CreatePaymentMethod creates a PayPal payment method
func (p *payPalProcessor) CreatePaymentMethod(ctx context.Context, req *CreatePaymentMethodRequest) (*PaymentMethodResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"type":    req.Type,
	}).Info("Creating PayPal payment method")

	return &PaymentMethodResponse{
		PaymentMethodID: uuid.New().String(),
		UserID:          req.UserID,
		Type:            req.Type,
		Details:         req.Details,
		IsDefault:       req.IsDefault,
		CreatedAt:       time.Now().Format(time.RFC3339),
		UpdatedAt:       time.Now().Format(time.RFC3339),
	}, nil
}

// UpdatePaymentMethod updates a PayPal payment method
func (p *payPalProcessor) UpdatePaymentMethod(ctx context.Context, req *UpdatePaymentMethodRequest) (*PaymentMethodResponse, error) {
	p.logger.WithField("payment_method_id", req.PaymentMethodID).Info("Updating PayPal payment method")

	return &PaymentMethodResponse{
		PaymentMethodID: req.PaymentMethodID,
		UserID:          "mock-user",
		Type:            "paypal",
		Details:         req.Details,
		IsDefault:       *req.IsDefault,
		CreatedAt:       time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		UpdatedAt:       time.Now().Format(time.RFC3339),
	}, nil
}

// DeletePaymentMethod deletes a PayPal payment method
func (p *payPalProcessor) DeletePaymentMethod(ctx context.Context, paymentMethodID string) error {
	p.logger.WithField("payment_method_id", paymentMethodID).Info("Deleting PayPal payment method")
	return nil
}

// ListPaymentMethods lists PayPal payment methods
func (p *payPalProcessor) ListPaymentMethods(ctx context.Context, userID string) ([]*PaymentMethodResponse, error) {
	p.logger.WithField("user_id", userID).Info("Listing PayPal payment methods")

	return []*PaymentMethodResponse{
		{
			PaymentMethodID: uuid.New().String(),
			UserID:          userID,
			Type:            "paypal",
			Details: map[string]interface{}{
				"email": "<EMAIL>",
			},
			IsDefault: true,
			CreatedAt: time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
			UpdatedAt: time.Now().Format(time.RFC3339),
		},
	}, nil
}

// CreateSubscription creates a PayPal subscription
func (p *payPalProcessor) CreateSubscription(ctx context.Context, req *CreateSubscriptionRequest) (*SubscriptionResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
	}).Info("Creating PayPal subscription")

	// Use PayPal usecase if available
	if p.paypalUC != nil {
		return p.createSubscriptionWithUseCase(ctx, req)
	}

	// Fallback to mock implementation
	return p.createSubscriptionMock(ctx, req)
}

// createSubscriptionWithUseCase creates subscription using PayPal usecase
func (p *payPalProcessor) createSubscriptionWithUseCase(ctx context.Context, req *CreateSubscriptionRequest) (*SubscriptionResponse, error) {
	// Convert to PayPal request
	paypalReq := &models.CreatePayPalSubscriptionRequest{
		UserID:    req.UserID,
		PlanID:    req.PlanID,
		ReturnURL: p.config.ReturnURL,
		CancelURL: p.config.CancelURL,
	}

	// Call PayPal usecase
	paypalResp, err := p.paypalUC.CreateSubscription(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal subscription: %w", err)
	}

	if !paypalResp.Success {
		return nil, fmt.Errorf("PayPal subscription creation failed: %s", paypalResp.ErrorMessage)
	}

	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     paypalResp.SubscriptionID,
		UserID:             req.UserID,
		PlanID:             req.PlanID,
		Status:             "pending_approval", // PayPal subscriptions start as pending
		CurrentPeriodStart: now.Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, 0).Format(time.RFC3339),
		Metadata:           req.Metadata,
	}, nil
}

// createSubscriptionMock creates subscription using mock implementation
func (p *payPalProcessor) createSubscriptionMock(ctx context.Context, req *CreateSubscriptionRequest) (*SubscriptionResponse, error) {
	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     uuid.New().String(),
		UserID:             req.UserID,
		PlanID:             req.PlanID,
		Status:             "active",
		CurrentPeriodStart: now.Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, 0).Format(time.RFC3339),
		Metadata:           req.Metadata,
	}, nil
}

// UpdateSubscription updates a PayPal subscription
func (p *payPalProcessor) UpdateSubscription(ctx context.Context, req *UpdateSubscriptionRequest) (*SubscriptionResponse, error) {
	p.logger.WithField("subscription_id", req.SubscriptionID).Info("Updating PayPal subscription")

	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     req.SubscriptionID,
		UserID:             "mock-user",
		PlanID:             req.PlanID,
		Status:             "active",
		CurrentPeriodStart: now.Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, 0).Format(time.RFC3339),
		Metadata:           req.Metadata,
	}, nil
}

// CancelSubscription cancels a PayPal subscription
func (p *payPalProcessor) CancelSubscription(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	p.logger.WithField("subscription_id", subscriptionID).Info("Cancelling PayPal subscription")

	// Use PayPal usecase if available
	if p.paypalUC != nil {
		return p.cancelSubscriptionWithUseCase(ctx, subscriptionID)
	}

	// Fallback to mock implementation
	return p.cancelSubscriptionMock(ctx, subscriptionID)
}

// cancelSubscriptionWithUseCase cancels subscription using PayPal usecase
func (p *payPalProcessor) cancelSubscriptionWithUseCase(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	// Convert to PayPal request
	paypalReq := &models.CancelPayPalSubscriptionRequest{
		SubscriptionID: subscriptionID,
		UserID:         "system", // Use system user for cancellations
		Reason:         "User requested cancellation",
	}

	// Call PayPal usecase
	paypalResp, err := p.paypalUC.CancelSubscription(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to cancel PayPal subscription: %w", err)
	}

	if !paypalResp.Success {
		return nil, fmt.Errorf("PayPal subscription cancellation failed: %s", paypalResp.Message)
	}

	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     paypalResp.SubscriptionID,
		UserID:             "system",
		PlanID:             "unknown",
		Status:             paypalResp.Status,
		CurrentPeriodStart: now.Add(-24 * time.Hour).Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, -1).Format(time.RFC3339),
		CancelledAt:        now.Format(time.RFC3339),
	}, nil
}

// cancelSubscriptionMock cancels subscription using mock implementation
func (p *payPalProcessor) cancelSubscriptionMock(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     subscriptionID,
		UserID:             "mock-user",
		PlanID:             "mock-plan",
		Status:             "cancelled",
		CurrentPeriodStart: now.Add(-24 * time.Hour).Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, -1).Format(time.RFC3339),
		CancelledAt:        now.Format(time.RFC3339),
	}, nil
}

// GetSubscription gets a PayPal subscription
func (p *payPalProcessor) GetSubscription(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	p.logger.WithField("subscription_id", subscriptionID).Info("Getting PayPal subscription")

	// Use PayPal usecase if available
	if p.paypalUC != nil {
		return p.getSubscriptionWithUseCase(ctx, subscriptionID)
	}

	// Fallback to mock implementation
	return p.getSubscriptionMock(ctx, subscriptionID)
}

// getSubscriptionWithUseCase gets subscription using PayPal usecase
func (p *payPalProcessor) getSubscriptionWithUseCase(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	// Convert to PayPal request
	paypalReq := &models.GetPayPalSubscriptionRequest{
		SubscriptionID: subscriptionID,
		UserID:         "system", // Use system user for status checks
	}

	// Call PayPal usecase
	paypalResp, err := p.paypalUC.GetSubscription(ctx, paypalReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get PayPal subscription: %w", err)
	}

	return &SubscriptionResponse{
		SubscriptionID:     paypalResp.SubscriptionID,
		UserID:             "system",
		PlanID:             paypalResp.PlanID,
		Status:             paypalResp.Status,
		CurrentPeriodStart: paypalResp.StartTime.Format(time.RFC3339),
		CurrentPeriodEnd:   paypalResp.NextBillingTime.Format(time.RFC3339),
	}, nil
}

// getSubscriptionMock gets subscription using mock implementation
func (p *payPalProcessor) getSubscriptionMock(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error) {
	now := time.Now()
	return &SubscriptionResponse{
		SubscriptionID:     subscriptionID,
		UserID:             "mock-user",
		PlanID:             "mock-plan",
		Status:             "active",
		CurrentPeriodStart: now.Format(time.RFC3339),
		CurrentPeriodEnd:   now.AddDate(0, 1, 0).Format(time.RFC3339),
	}, nil
}

// ValidateWebhook validates a PayPal webhook
func (p *payPalProcessor) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	p.logger.Info("Validating PayPal webhook")

	// Mock validation - in production, this would verify PayPal signature
	return true, nil
}

// ProcessWebhook processes a PayPal webhook
func (p *payPalProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
	p.logger.Info("Processing PayPal webhook")

	// Parse PayPal webhook payload
	var webhookData map[string]interface{}
	if err := json.Unmarshal(payload, &webhookData); err != nil {
		return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
	}

	eventType, ok := webhookData["event_type"].(string)
	if !ok {
		return nil, fmt.Errorf("missing event_type in webhook payload")
	}

	return &WebhookEvent{
		ID:        uuid.New().String(),
		Type:      eventType,
		Data:      webhookData,
		CreatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

package integration

import (
	"context"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/pkg/oauth"
)

// UseCase defines the integration use case interface
type UseCase interface {
	// OAuth provider management
	RegisterOAuthProvider(platform string, provider oauth.Provider)

	// OAuth operations
	GetOAuthURL(ctx context.Context, req *GetOAuthURLRequest) (*GetOAuthURLResponse, error)
	ConnectAccount(ctx context.Context, req *ConnectAccountRequest) (*ConnectAccountResponse, error)
	RefreshToken(ctx context.Context, req *RefreshTokenRequest) (*RefreshTokenResponse, error)
	DisconnectAccount(ctx context.Context, req *DisconnectAccountRequest) error

	// Account management
	ListAccounts(ctx context.Context, req *ListAccountsRequest) (*ListAccountsResponse, error)
	ValidateAccount(ctx context.Context, req *ValidateAccountRequest) (*ValidateAccountResponse, error)
}

// Repository defines the data access interface for integration operations
type Repository interface {
	Reader
	Writer
}

// Reader defines read operations interface
type Reader interface {
	GetPlatformIntegration(ctx context.Context, id string) (*ent.PlatformIntegration, error)
	GetPlatformIntegrationByUserAndPlatform(ctx context.Context, userID, platform string) (*ent.PlatformIntegration, error)
	ListPlatformIntegrations(ctx context.Context, userID, workspaceID, platform, status string, limit, offset int32) ([]*ent.PlatformIntegration, int32, error)
}

// Writer defines write operations interface
type Writer interface {
	CreatePlatformIntegration(ctx context.Context, integration *ent.PlatformIntegration) (*ent.PlatformIntegration, error)
	UpdatePlatformIntegration(ctx context.Context, id string, updates map[string]interface{}) (*ent.PlatformIntegration, error)
	DeletePlatformIntegration(ctx context.Context, id string) error
}

package handlers

// Common response types
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

type SuccessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// OAuth URL request/response
type GetOAuthURLRequest struct {
	Platform             string   `json:"platform" binding:"required"`
	RedirectURI          string   `json:"redirect_uri,omitempty"`
	RequestedPermissions []string `json:"requested_permissions,omitempty"`
	State                string   `json:"state,omitempty"`
}

type GetOAuthURLResponse struct {
	AuthURL     string   `json:"auth_url"`
	State       string   `json:"state"`
	Permissions []string `json:"permissions"`
}

// Platform account structure
type PlatformAccount struct {
	ID          string            `json:"id"`
	UserID      string            `json:"user_id"`
	WorkspaceID string            `json:"workspace_id,omitempty"`
	Platform    string            `json:"platform"`
	AccountID   string            `json:"account_id"`
	Username    string            `json:"username"`
	DisplayName string            `json:"display_name"`
	AvatarURL   string            `json:"avatar_url,omitempty"`
	Status      string            `json:"status"`
	Permissions []string          `json:"permissions"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	ConnectedAt int64             `json:"connected_at"`
	LastUsed    int64             `json:"last_used,omitempty"`
}

// List integrations response
type ListIntegrationsResponse struct {
	Accounts   []PlatformAccount `json:"accounts"`
	TotalCount int32             `json:"total_count"`
	Page       int32             `json:"page"`
	Limit      int32             `json:"limit"`
}

// Disconnect integration request
type DisconnectIntegrationRequest struct {
	RevokeToken bool `json:"revoke_token,omitempty"`
}

// Refresh integration request/response
type RefreshIntegrationRequest struct {
	ForceRefresh bool `json:"force_refresh,omitempty"`
}

type RefreshIntegrationResponse struct {
	Success      bool   `json:"success"`
	NewExpiresAt int64  `json:"new_expires_at,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// Publishing request/response
type PublishPostRequest struct {
	AccountID       string            `json:"account_id" binding:"required"`
	PostID          string            `json:"post_id,omitempty"`
	Content         string            `json:"content" binding:"required"`
	ImageURLs       []string          `json:"image_urls,omitempty"`
	Hashtags        []string          `json:"hashtags,omitempty"`
	PlatformOptions map[string]string `json:"platform_options,omitempty"`
	ScheduledAt     int64             `json:"scheduled_at,omitempty"`
}

type PublishPostResponse struct {
	Success        bool   `json:"success"`
	PlatformPostID string `json:"platform_post_id,omitempty"`
	PlatformURL    string `json:"platform_url,omitempty"`
	ErrorMessage   string `json:"error_message,omitempty"`
	PublishedAt    int64  `json:"published_at,omitempty"`
}

// Analytics request/response
type GetPostAnalyticsRequest struct {
	AccountID      string   `json:"account_id" binding:"required"`
	PlatformPostID string   `json:"platform_post_id" binding:"required"`
	Metrics        []string `json:"metrics,omitempty"`
}

type GetPostAnalyticsResponse struct {
	PlatformPostID  string           `json:"platform_post_id"`
	Platform        string           `json:"platform"`
	Metrics         map[string]int64 `json:"metrics"`
	CollectedAt     int64            `json:"collected_at"`
	PostPublishedAt int64            `json:"post_published_at"`
}

type GetAccountAnalyticsRequest struct {
	AccountID string   `json:"account_id" binding:"required"`
	DateFrom  string   `json:"date_from,omitempty"`
	DateTo    string   `json:"date_to,omitempty"`
	Metrics   []string `json:"metrics,omitempty"`
}

type GetAccountAnalyticsResponse struct {
	AccountID   string             `json:"account_id"`
	Platform    string             `json:"platform"`
	Metrics     map[string]int64   `json:"metrics"`
	TimeSeries  map[string][]int64 `json:"time_series,omitempty"`
	CollectedAt int64              `json:"collected_at"`
}

// Platform limits request/response
type GetPlatformLimitsRequest struct {
	Platform  string `json:"platform" binding:"required"`
	AccountID string `json:"account_id,omitempty"`
}

type GetPlatformLimitsResponse struct {
	Platform     string           `json:"platform"`
	RateLimits   map[string]int32 `json:"rate_limits"`
	CurrentUsage map[string]int32 `json:"current_usage"`
	ResetTimes   map[string]int64 `json:"reset_times"`
}

// Validation response
type ValidateIntegrationResponse struct {
	IsValid            bool     `json:"is_valid"`
	TokenExpired       bool     `json:"token_expired"`
	MissingPermissions []string `json:"missing_permissions,omitempty"`
	ErrorMessage       string   `json:"error_message,omitempty"`
	LastValidated      int64    `json:"last_validated"`
}

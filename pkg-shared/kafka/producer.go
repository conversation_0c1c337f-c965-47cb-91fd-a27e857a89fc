package kafka

import (
	"context"
	"fmt"

	"github.com/segmentio/kafka-go"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Producer represents a Kafka producer
type Producer struct {
	config  *Config
	logger  logging.Logger
	writers map[string]*kafka.Writer
	builder *MessageBuilder
}

// NewProducer creates a new Kafka producer
func NewProducer(config *Config, logger logging.Logger) *Producer {
	return &Producer{
		config:  config,
		logger:  logger,
		writers: make(map[string]*kafka.Writer),
		builder: NewMessageBuilder(),
	}
}

// getWriter gets or creates a writer for a topic
func (p *Producer) getWriter(topic string) *kafka.Writer {
	if writer, exists := p.writers[topic]; exists {
		return writer
	}

	writer := kafka.NewWriter(p.getWriterConfig(topic))
	p.writers[topic] = writer
	return writer
}

// getWriterConfig returns kafka-go writer config for a topic
func (p *Producer) getWriterConfig(topic string) kafka.WriterConfig {
	config := kafka.WriterConfig{
		Brokers:      p.config.Brokers,
		Topic:        topic,
		MaxAttempts:  p.config.Producer.RetryMax,
		BatchSize:    p.config.Producer.BatchSize,
		BatchTimeout: p.config.Producer.BatchTimeout,
		ReadTimeout:  p.config.Producer.ReadTimeout,
		WriteTimeout: p.config.Producer.WriteTimeout,
		RequiredAcks: p.config.Producer.RequiredAcks,
		Async:        p.config.Producer.Async,
	}

	// Set compression
	switch p.config.Producer.Compression {
	case "gzip":
		config.CompressionCodec = kafka.Gzip.Codec()
	case "snappy":
		config.CompressionCodec = kafka.Snappy.Codec()
	case "lz4":
		config.CompressionCodec = kafka.Lz4.Codec()
	case "zstd":
		config.CompressionCodec = kafka.Zstd.Codec()
	default:
		config.CompressionCodec = kafka.Snappy.Codec()
	}

	// Add security configuration if enabled
	if p.config.Security.Enabled {
		// TODO: Add SASL/TLS configuration
	}

	return config
}

// PublishEvent publishes an event to the appropriate topic
func (p *Producer) PublishEvent(ctx context.Context, event *BaseEvent) error {
	// Build event message
	eventMsg, err := p.builder.BuildEventMessage(event)
	if err != nil {
		return fmt.Errorf("failed to build event message: %w", err)
	}

	// Determine topic
	topic := GetTopicForEventType(event.Type)

	// Build Kafka message
	kafkaMsg, err := p.builder.BuildKafkaMessage(topic, eventMsg)
	if err != nil {
		return fmt.Errorf("failed to build kafka message: %w", err)
	}

	// Send message
	return p.writeMessage(ctx, kafkaMsg)
}

// PublishUserEvent publishes a user event
func (p *Producer) PublishUserEvent(ctx context.Context, event *UserEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishContentEvent publishes a content event
func (p *Producer) PublishContentEvent(ctx context.Context, event *ContentEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishBillingEvent publishes a billing event
func (p *Producer) PublishBillingEvent(ctx context.Context, event *BillingEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishIntegrationEvent publishes an integration event
func (p *Producer) PublishIntegrationEvent(ctx context.Context, event *IntegrationEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishAssetEvent publishes an asset event
func (p *Producer) PublishAssetEvent(ctx context.Context, event *AssetEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishRAGEvent publishes a RAG event
func (p *Producer) PublishRAGEvent(ctx context.Context, event *RAGEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishSystemEvent publishes a system event
func (p *Producer) PublishSystemEvent(ctx context.Context, event *SystemEvent) error {
	return p.PublishEvent(ctx, event.BaseEvent)
}

// PublishToTopic publishes a message to a specific topic
func (p *Producer) PublishToTopic(ctx context.Context, topic string, event *BaseEvent) error {
	// Build event message
	eventMsg, err := p.builder.BuildEventMessage(event)
	if err != nil {
		return fmt.Errorf("failed to build event message: %w", err)
	}

	// Build Kafka message
	kafkaMsg, err := p.builder.BuildKafkaMessage(topic, eventMsg)
	if err != nil {
		return fmt.Errorf("failed to build kafka message: %w", err)
	}

	// Send message
	return p.writeMessage(ctx, kafkaMsg)
}

// writeMessage writes a message to Kafka
func (p *Producer) writeMessage(ctx context.Context, msg *Message) error {
	writer := p.getWriter(msg.Topic)

	kafkaMessage := kafka.Message{
		Key:   []byte(msg.Key),
		Value: msg.Value,
		Time:  msg.Timestamp,
	}

	// Add headers
	for key, value := range msg.Headers {
		kafkaMessage.Headers = append(kafkaMessage.Headers, kafka.Header{
			Key:   key,
			Value: []byte(value),
		})
	}

	if err := writer.WriteMessages(ctx, kafkaMessage); err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"topic": msg.Topic,
			"key":   msg.Key,
		}).Error("Failed to write message")
		return err
	}

	p.logger.WithFields(map[string]interface{}{
		"topic":      msg.Topic,
		"key":        msg.Key,
		"event_type": msg.Headers["event_type"],
		"source":     msg.Headers["source"],
	}).Debug("Message published successfully")

	return nil
}

// PublishBatch publishes multiple events in a batch
func (p *Producer) PublishBatch(ctx context.Context, events []*BaseEvent) error {
	if len(events) == 0 {
		return nil
	}

	// Group events by topic
	topicEvents := make(map[string][]*BaseEvent)
	for _, event := range events {
		topic := GetTopicForEventType(event.Type)
		topicEvents[topic] = append(topicEvents[topic], event)
	}

	// Send to each topic
	for topic, topicEventList := range topicEvents {
		if err := p.publishBatchToTopic(ctx, topic, topicEventList); err != nil {
			return fmt.Errorf("failed to publish batch to topic %s: %w", topic, err)
		}
	}

	return nil
}

// publishBatchToTopic publishes a batch of events to a specific topic
func (p *Producer) publishBatchToTopic(ctx context.Context, topic string, events []*BaseEvent) error {
	writer := p.getWriter(topic)

	var messages []kafka.Message
	for _, event := range events {
		// Build event message
		eventMsg, err := p.builder.BuildEventMessage(event)
		if err != nil {
			p.logger.WithError(err).WithField("event_id", event.ID).Error("Failed to build event message")
			continue
		}

		// Build Kafka message
		kafkaMsg, err := p.builder.BuildKafkaMessage(topic, eventMsg)
		if err != nil {
			p.logger.WithError(err).WithField("event_id", event.ID).Error("Failed to build kafka message")
			continue
		}

		kafkaMessage := kafka.Message{
			Key:   []byte(kafkaMsg.Key),
			Value: kafkaMsg.Value,
			Time:  kafkaMsg.Timestamp,
		}

		// Add headers
		for key, value := range kafkaMsg.Headers {
			kafkaMessage.Headers = append(kafkaMessage.Headers, kafka.Header{
				Key:   key,
				Value: []byte(value),
			})
		}

		messages = append(messages, kafkaMessage)
	}

	if len(messages) == 0 {
		return fmt.Errorf("no valid messages to send")
	}

	if err := writer.WriteMessages(ctx, messages...); err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"topic":         topic,
			"message_count": len(messages),
		}).Error("Failed to write batch messages")
		return err
	}

	p.logger.WithFields(map[string]interface{}{
		"topic":         topic,
		"message_count": len(messages),
	}).Info("Batch messages published successfully")

	return nil
}

// Close closes all writers
func (p *Producer) Close() error {
	for topic, writer := range p.writers {
		if err := writer.Close(); err != nil {
			p.logger.WithError(err).WithField("topic", topic).Error("Failed to close writer")
		}
	}
	return nil
}

// GetStats returns producer statistics
func (p *Producer) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	for topic, writer := range p.writers {
		writerStats := writer.Stats()
		stats[topic] = map[string]interface{}{
			"messages": writerStats.Messages,
			"bytes":    writerStats.Bytes,
			"errors":   writerStats.Errors,
			"retries":  writerStats.Retries,
		}
	}

	return stats
}

// HealthCheck checks if the producer is healthy
func (p *Producer) HealthCheck(ctx context.Context) error {
	// Try to connect to Kafka
	conn, err := kafka.DialContext(ctx, "tcp", p.config.Brokers[0])
	if err != nil {
		return fmt.Errorf("failed to connect to Kafka: %w", err)
	}
	defer conn.Close()

	// Try to get metadata
	_, err = conn.ReadPartitions()
	if err != nil {
		return fmt.Errorf("failed to read partitions: %w", err)
	}

	return nil
}

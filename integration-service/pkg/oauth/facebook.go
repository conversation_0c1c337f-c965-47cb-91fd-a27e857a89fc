package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// FacebookOAuth handles Facebook OAuth flow
type FacebookOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewFacebookOAuth creates a new Facebook OAuth handler
func NewFacebookOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) Provider {
	return &FacebookOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// FacebookTokenResponse represents Facebook token response
type FacebookTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

// FacebookUserProfile represents Facebook user profile
type FacebookUserProfile struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Email   string `json:"email"`
	Picture struct {
		Data struct {
			URL string `json:"url"`
		} `json:"data"`
	} `json:"picture"`
}

// FacebookUserInfo represents Facebook user information
type FacebookUserInfo struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// FacebookPageInfo represents Facebook page information
type FacebookPageInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	AccessToken string `json:"access_token"`
	Category    string `json:"category"`
}

// GetAuthURL generates Facebook OAuth authorization URL
func (f *FacebookOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://www.facebook.com/v23.0/dialog/oauth"

	params := url.Values{}
	params.Set("client_id", f.clientID)
	params.Set("redirect_uri", f.redirectURL)
	params.Set("state", state)
	params.Set("response_type", "code")

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, ","))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "pages_manage_posts,pages_read_engagement,pages_show_list,publish_to_groups")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (f *FacebookOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://graph.facebook.com/v23.0/oauth/access_token"

	params := url.Values{}
	params.Set("client_id", f.clientID)
	params.Set("client_secret", f.clientSecret)
	params.Set("redirect_uri", f.redirectURL)
	params.Set("code", code)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp FacebookTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.ExpiresIn,
	}).Info("Facebook token exchanged successfully")

	// Convert FacebookTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken: tokenResp.AccessToken,
		TokenType:   tokenResp.TokenType,
		ExpiresAt:   expiresAt,
	}, nil
}

// GetUserInfo retrieves user information using access token
func (f *FacebookOAuth) GetUserInfo(ctx context.Context, accessToken string) (*FacebookUserInfo, error) {
	userURL := "https://graph.facebook.com/v23.0/me"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("fields", "id,name,email")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", userURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user info failed with status: %d", resp.StatusCode)
	}

	var userInfo FacebookUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &userInfo, nil
}

// GetUserPages retrieves user's Facebook pages
func (f *FacebookOAuth) GetUserPages(ctx context.Context, accessToken string) ([]FacebookPageInfo, error) {
	pagesURL := "https://graph.facebook.com/v23.0/me/accounts"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("fields", "id,name,access_token,category")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", pagesURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get pages: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get pages failed with status: %d", resp.StatusCode)
	}

	var pagesResp struct {
		Data []FacebookPageInfo `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&pagesResp); err != nil {
		return nil, fmt.Errorf("failed to decode pages response: %w", err)
	}

	return pagesResp.Data, nil
}

// RefreshToken refreshes Facebook access token
func (f *FacebookOAuth) RefreshToken(ctx context.Context, accessToken string) (*TokenResponse, error) {
	refreshURL := "https://graph.facebook.com/v23.0/oauth/access_token"

	params := url.Values{}
	params.Set("grant_type", "fb_exchange_token")
	params.Set("client_id", f.clientID)
	params.Set("client_secret", f.clientSecret)
	params.Set("fb_exchange_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", refreshURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp FacebookTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert FacebookTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken: tokenResp.AccessToken,
		TokenType:   tokenResp.TokenType,
		ExpiresAt:   expiresAt,
	}, nil
}

// ValidateToken validates Facebook access token
func (f *FacebookOAuth) ValidateToken(ctx context.Context, accessToken string) (bool, error) {
	validateURL := "https://graph.facebook.com/v23.0/me"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("fields", "id")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", validateURL, params.Encode()), nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to validate token: %w", err)
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK, nil
}

// RevokeToken revokes Facebook access token
func (f *FacebookOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	revokeURL := "https://graph.facebook.com/v23.0/me/permissions"

	params := url.Values{}
	params.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "DELETE", fmt.Sprintf("%s?%s", revokeURL, params.Encode()), nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("token revocation failed with status: %d", resp.StatusCode)
	}

	f.logger.Info("Facebook token revoked successfully")
	return nil
}

// GetTokenInfo gets information about the access token
func (f *FacebookOAuth) GetTokenInfo(ctx context.Context, accessToken string) (map[string]interface{}, error) {
	tokenInfoURL := "https://graph.facebook.com/v23.0/oauth/access_token_info"

	params := url.Values{}
	params.Set("client_id", f.clientID)
	params.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", tokenInfoURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get token info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get token info failed with status: %d", resp.StatusCode)
	}

	var tokenInfo map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&tokenInfo); err != nil {
		return nil, fmt.Errorf("failed to decode token info: %w", err)
	}

	return tokenInfo, nil
}

// Interface implementation methods

// GetUserProfile implements Provider interface
func (f *FacebookOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	profileURL := "https://graph.facebook.com/v23.0/me"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("fields", "id,name,email,picture")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", profileURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create profile request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get profile failed with status: %d", resp.StatusCode)
	}

	var fbProfile FacebookUserProfile
	if err := json.NewDecoder(resp.Body).Decode(&fbProfile); err != nil {
		return nil, fmt.Errorf("failed to decode profile response: %w", err)
	}

	return &UserProfile{
		ID:          fbProfile.ID,
		Username:    fbProfile.Name, // Facebook doesn't have username, use name
		DisplayName: fbProfile.Name,
		Email:       fbProfile.Email,
		AvatarURL:   fbProfile.Picture.Data.URL,
	}, nil
}

// GetPermissions implements Provider interface
func (f *FacebookOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	permissionsURL := "https://graph.facebook.com/v23.0/me/permissions"

	params := url.Values{}
	params.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", permissionsURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create permissions request: %w", err)
	}

	resp, err := f.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get permissions failed with status: %d", resp.StatusCode)
	}

	var permissionsResp struct {
		Data []struct {
			Permission string `json:"permission"`
			Status     string `json:"status"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&permissionsResp); err != nil {
		return nil, fmt.Errorf("failed to decode permissions response: %w", err)
	}

	var permissions []string
	for _, perm := range permissionsResp.Data {
		if perm.Status == "granted" {
			permissions = append(permissions, perm.Permission)
		}
	}

	return permissions, nil
}

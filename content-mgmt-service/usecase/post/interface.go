package post

import (
	"context"

	"github.com/social-content-ai/content-mgmt-service/pkg/models"
)

// UseCase defines the post business logic interface
type UseCase interface {
	// Basic CRUD operations
	CreatePost(ctx context.Context, req *models.CreatePostRequest) (*models.PostResponse, error)
	GetPost(ctx context.Context, postID, userID string) (*models.PostResponse, error)
	UpdatePost(ctx context.Context, req *models.UpdatePostRequest) (*models.PostResponse, error)
	DeletePost(ctx context.Context, postID, userID string) error
	ListPosts(ctx context.Context, req *models.ListPostsRequest) (*models.ListPostsResponse, error)

	// Scheduling and publishing
	SchedulePost(ctx context.Context, req *models.SchedulePostRequest) (*models.PostResponse, error)
	PublishPost(ctx context.Context, req *models.PublishPostRequest) (*models.PostResponse, error)
	CancelScheduledPost(ctx context.Context, postID, userID string) (*models.PostResponse, error)
	GetPostStatus(ctx context.Context, postID, userID string) (*models.PostStatusResponse, error)

	// Bulk operations
	BulkSchedulePosts(ctx context.Context, req *models.BulkScheduleRequest) (*models.BulkScheduleResponse, error)
	BulkUpdateSchedule(ctx context.Context, req *models.BulkScheduleRequest) (*models.BulkScheduleResponse, error)
	BulkCancelSchedule(ctx context.Context, postIDs []string, userID string) (*models.BulkOperationResult, error)

	// Schedule management
	ListScheduledPosts(ctx context.Context, userID string, page, limit int) (*models.ListPostsResponse, error)
	GetScheduleCalendar(ctx context.Context, req *models.ScheduleCalendarRequest) (*models.ScheduleCalendarResponse, error)

	// Analytics
	GetPostAnalytics(ctx context.Context, req *models.PostAnalyticsRequest) (*models.PostAnalyticsResponse, error)
	GetPostEngagement(ctx context.Context, req *models.PostEngagementRequest) (*models.PostEngagementResponse, error)
	GetPostsAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error)
	GetPerformanceAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error)
	GetEngagementAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error)

	// Admin operations
	AdminListPosts(ctx context.Context, req *models.ListPostsRequest) (*models.ListPostsResponse, error)
	AdminGetPost(ctx context.Context, postID string) (*models.PostResponse, error)
	AdminDeletePost(ctx context.Context, postID string) error
	GetAdminAnalytics(ctx context.Context, period string) (*models.PostAnalyticsResponse, error)
}

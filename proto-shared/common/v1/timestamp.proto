syntax = "proto3";

package common.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/social-content-ai/proto-shared/common/v1;commonv1";

// TimeRange represents a time range with start and end
message TimeRange {
  google.protobuf.Timestamp start = 1;
  google.protobuf.Timestamp end = 2;
}

// DateRange represents a date range (date only, no time)
message DateRange {
  string start_date = 1;     // Format: YYYY-MM-DD
  string end_date = 2;       // Format: YYYY-MM-DD
}

// TimestampInfo with creation and update times
message TimestampInfo {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  google.protobuf.Timestamp deleted_at = 3;  // For soft delete
}

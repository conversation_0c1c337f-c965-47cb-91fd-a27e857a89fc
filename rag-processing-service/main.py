"""
RAG Processing Service for Social Content AI Platform

This service handles:
- Document processing and embedding
- Vector database operations with Qdrant
- Graph database operations with Neo4j
- Knowledge base management
- RAG training and inference
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import get_settings
from app.core.logging import setup_logging
from app.api.routes import router
from app.services.vector_db import VectorDBService
from app.services.graph_db import GraphDBService
from app.services.document_processor import DocumentProcessor
from app.services.rag_trainer import RAGTrainer
from app.services.kafka_consumer import KafkaConsumer


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    settings = get_settings()
    
    # Setup logging
    setup_logging(settings.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting RAG Processing Service")
    
    # Initialize services
    try:
        # Vector database
        vector_db = VectorDBService(settings.qdrant_url)
        await vector_db.initialize()
        app.state.vector_db = vector_db
        
        # Graph database
        graph_db = GraphDBService(
            settings.neo4j_uri,
            settings.neo4j_user,
            settings.neo4j_password
        )
        await graph_db.initialize()
        app.state.graph_db = graph_db
        
        # Document processor
        doc_processor = DocumentProcessor(vector_db, graph_db)
        app.state.doc_processor = doc_processor
        
        # RAG trainer
        rag_trainer = RAGTrainer(vector_db, graph_db, settings)
        app.state.rag_trainer = rag_trainer
        
        # Kafka consumer
        kafka_consumer = KafkaConsumer(
            settings.kafka_bootstrap_servers,
            settings.kafka_group_id,
            doc_processor,
            rag_trainer
        )
        
        # Start Kafka consumer in background
        consumer_task = asyncio.create_task(kafka_consumer.start())
        app.state.consumer_task = consumer_task
        
        logger.info("RAG Processing Service started successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise
    
    yield
    
    # Cleanup
    logger.info("Shutting down RAG Processing Service")
    
    # Stop Kafka consumer
    if hasattr(app.state, 'consumer_task'):
        app.state.consumer_task.cancel()
        try:
            await app.state.consumer_task
        except asyncio.CancelledError:
            pass
    
    # Close database connections
    if hasattr(app.state, 'vector_db'):
        await app.state.vector_db.close()
    
    if hasattr(app.state, 'graph_db'):
        await app.state.graph_db.close()
    
    logger.info("RAG Processing Service stopped")


def create_app() -> FastAPI:
    """Create FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="RAG Processing Service",
        description="Document processing and RAG training service for Social Content AI",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(router, prefix="/api/v1")
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "service": "rag-processing-service",
            "version": "1.0.0"
        }
    
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "RAG Processing Service",
            "version": "1.0.0",
            "docs": "/docs"
        }
    
    return app


app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )

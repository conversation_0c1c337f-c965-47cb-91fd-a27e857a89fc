// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// SessionsColumns holds the columns for the "sessions" table.
	SessionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "device_info", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"active", "expired", "revoked"}, Default: "active"},
		{Name: "ip_address", Type: field.TypeString, Nullable: true, Size: 45},
		{Name: "login_time", Type: field.TypeTime},
		{Name: "logout_time", Type: field.TypeTime, Nullable: true},
		{Name: "expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeUUID},
	}
	// SessionsTable holds the schema information for the "sessions" table.
	SessionsTable = &schema.Table{
		Name:       "sessions",
		Columns:    SessionsColumns,
		PrimaryKey: []*schema.Column{SessionsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "sessions_users_sessions",
				Columns:    []*schema.Column{SessionsColumns[9]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "session_user_id",
				Unique:  false,
				Columns: []*schema.Column{SessionsColumns[9]},
			},
			{
				Name:    "session_status",
				Unique:  false,
				Columns: []*schema.Column{SessionsColumns[2]},
			},
			{
				Name:    "session_expires_at",
				Unique:  false,
				Columns: []*schema.Column{SessionsColumns[6]},
			},
			{
				Name:    "session_created_at",
				Unique:  false,
				Columns: []*schema.Column{SessionsColumns[7]},
			},
		},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "full_name", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "email", Type: field.TypeString, Unique: true, Size: 255},
		{Name: "phone", Type: field.TypeString, Nullable: true, Size: 15},
		{Name: "avatar_url", Type: field.TypeString, Nullable: true},
		{Name: "bio", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "company", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "industry", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "role", Type: field.TypeEnum, Enums: []string{"user", "admin"}, Default: "user"},
		{Name: "password_hash", Type: field.TypeString},
		{Name: "is_verified", Type: field.TypeBool, Default: false},
		{Name: "two_factor_enabled", Type: field.TypeBool, Default: false},
		{Name: "two_factor_secret", Type: field.TypeString, Nullable: true},
		{Name: "two_factor_backup_codes", Type: field.TypeJSON, Nullable: true},
		{Name: "two_factor_enabled_at", Type: field.TypeTime, Nullable: true},
		{Name: "notification_settings", Type: field.TypeJSON, Nullable: true},
		{Name: "invite_code", Type: field.TypeString, Unique: true, Nullable: true, Size: 20},
		{Name: "aff_code", Type: field.TypeString, Nullable: true, Size: 20},
		{Name: "aff_joined_at", Type: field.TypeTime, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "user_email",
				Unique:  true,
				Columns: []*schema.Column{UsersColumns[2]},
			},
			{
				Name:    "user_invite_code",
				Unique:  true,
				Columns: []*schema.Column{UsersColumns[16]},
			},
			{
				Name:    "user_role",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[8]},
			},
			{
				Name:    "user_is_verified",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[10]},
			},
			{
				Name:    "user_created_at",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[19]},
			},
			{
				Name:    "user_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{UsersColumns[21]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		SessionsTable,
		UsersTable,
	}
)

func init() {
	SessionsTable.ForeignKeys[0].RefTable = UsersTable
}

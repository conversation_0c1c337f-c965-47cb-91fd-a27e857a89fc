package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Generation holds the schema definition for the Generation entity.
type Generation struct {
	ent.Schema
}

// Fields of the Generation.
func (Generation) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.String("topic").
			MaxLen(500),
		field.Enum("content_type").
			Values("educational", "promotional", "entertainment", "informational", "news", "tutorial").
			Default("informational"),
		field.Enum("tone").
			Values("professional", "friendly", "humorous", "formal", "casual", "enthusiastic", "authoritative").
			De<PERSON>ult("professional"),
		field.Enum("length").
			Values("short", "medium", "long").
			Default("medium"),
		field.JSON("platforms", []string{}).
			Optional(),
		field.String("model").
			MaxLen(50).
			Default("gpt-4"),
		field.String("template_id").
			MaxLen(36).
			Optional(),
		field.JSON("variables", map[string]interface{}{}).
			Optional(),
		field.String("workspace_id").
			MaxLen(36).
			Optional(),
		field.Text("content"),
		field.JSON("hashtags", []string{}).
			Optional(),
		field.JSON("suggested_images", []string{}).
			Optional(),
		field.Int("credits_used").
			Default(1),
		field.Float("generation_time").
			Default(0.0),
		field.String("model_used").
			MaxLen(50),
		field.Enum("status").
			Values("pending", "completed", "failed").
			Default("pending"),
		field.Text("error_message").
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Generation.
func (Generation) Edges() []ent.Edge {
	return nil
}

// Indexes of the Generation.
func (Generation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("content_type"),
		index.Fields("model"),
		index.Fields("status"),
		index.Fields("workspace_id"),
		index.Fields("created_at"),
		index.Fields("user_id", "created_at"),
	}
}

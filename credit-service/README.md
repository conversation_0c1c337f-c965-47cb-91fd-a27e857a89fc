# Credit Service

Credit Service is a microservice responsible for managing user credits, subscriptions, and billing in the Social Content AI platform. It provides both gRPC and REST APIs for credit operations, subscription management, and payment processing.

## Features

- **Credit Management**: Add, deduct, transfer, and validate user credits
- **AI Credit Validation**: Specialized credit validation for AI operations with cost calculation
- **Subscription Management**: Handle subscription plans, activations, renewals, and cancellations
- **Payment Processing**: Integration with Stripe and PayPal for payment processing
- **Multi-Database Support**: Supports both PostgreSQL and SQLite databases
- **Event-Driven Architecture**: Kafka integration for publishing credit and subscription events
- **Monitoring**: Prometheus metrics and health checks
- **Security**: JWT-based authentication and authorization

## Architecture

The service follows Clean Architecture principles with the following layers:

```
├── api/                    # API layer (gRPC and REST handlers)
│   ├── grpc/              # gRPC handlers
│   └── restful/           # REST API handlers
├── config/                # Configuration management
├── ent/                   # Database entities and schema
├── pkg/                   # Shared packages
│   ├── events/            # Event definitions
│   ├── kafka/             # Kafka producers and consumers
│   └── models/            # Data models
└── usecase/               # Business logic layer
    ├── credit/            # Credit management
    ├── paypal/            # PayPal integration
    ├── plan/              # Subscription plans
    ├── subscription/      # Subscription management
    └── transaction/       # Transaction management
```

## Getting Started

### Prerequisites

- Go 1.21 or later
- Docker and Docker Compose
- PostgreSQL (optional, SQLite is default)
- Apache Kafka (for event-driven features)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd credit-service
```

2. Copy environment configuration:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Install dependencies:
```bash
make deps
```

4. Set up development environment:
```bash
make setup
```

This will:
- Start PostgreSQL, Kafka, and other infrastructure services
- Run database migrations
- Create required Kafka topics
- Seed initial data

### Running the Service

#### Development Mode
```bash
make run
```

#### Using Docker
```bash
make docker-up
```

#### Production Mode
```bash
make build
./bin/credit-service
```

## Configuration

The service supports configuration through:
- YAML configuration files (`config/config.yaml`)
- Environment variables (prefixed with `CREDIT_SERVICE_`)
- Command-line flags

### Key Configuration Sections

#### Database
```yaml
database:
  type: "postgres"  # or "sqlite"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "credit_service_db"
```

#### Kafka
```yaml
kafka:
  brokers: ["localhost:9092"]
  group_id: "credit-service"
  topics:
    credit_events: "credit.events"
    subscription_events: "subscription.events"
    payment_events: "payment.events"
    notification_events: "notification.events"
```

#### Payment Gateways
```yaml
payment:
  stripe:
    secret_key: "${STRIPE_SECRET_KEY}"
    publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
    webhook_secret: "${STRIPE_WEBHOOK_SECRET}"

paypal:
  client_id: "${PAYPAL_CLIENT_ID}"
  client_secret: "${PAYPAL_CLIENT_SECRET}"
  mode: "sandbox"
```

## API Documentation

### gRPC Services

The service provides the following gRPC services:

- **CreditService**: Credit management operations
- **AICreditService**: AI-specific credit validation
- **SubscriptionService**: Subscription management
- **PlanService**: Subscription plan management
- **TransactionService**: Transaction history

### REST API

REST endpoints are available at:
- Base URL: `http://localhost:8083/api/v1`
- Health check: `GET /health`
- Metrics: `GET /metrics` (port 9093)

#### Key Endpoints

```
POST   /api/v1/credits/add              # Add credits
POST   /api/v1/credits/deduct           # Deduct credits
GET    /api/v1/credits/balance          # Get credit balance
POST   /api/v1/credits/transfer         # Transfer credits

GET    /api/v1/plans                    # List subscription plans
POST   /api/v1/subscriptions           # Create subscription
GET    /api/v1/subscriptions           # Get user subscription

POST   /api/v1/paypal/initiate         # Initiate PayPal payment
POST   /api/v1/paypal/execute          # Execute PayPal payment
```

## Database Schema

### Core Tables

- **credit_balances**: User credit balances
- **credit_transactions**: Credit transaction history
- **subscription_plans**: Available subscription plans
- **subscriptions**: User subscriptions
- **payments**: Payment records

### Migrations

Run migrations:
```bash
make migrate
```

Reset database:
```bash
make db-reset
```

## Event-Driven Architecture

The service publishes events to Kafka topics:

### Credit Events (`credit.events`)
- `credit.added`: When credits are added to user account
- `credit.deducted`: When credits are deducted
- `credit.transferred`: When credits are transferred between users

### Subscription Events (`subscription.events`)
- `subscription.activated`: When subscription is activated
- `subscription.cancelled`: When subscription is cancelled
- `subscription.renewed`: When subscription is renewed

### Payment Events (`payment.events`)
- `payment.succeeded`: When payment is successful
- `payment.failed`: When payment fails

## Testing

### Unit Tests
```bash
make test
```

### Integration Tests
```bash
make test-integration
```

### Test Coverage
```bash
make test-coverage
```

### Benchmarks
```bash
make benchmark
```

## Monitoring

### Health Checks
- HTTP: `GET /health`
- gRPC: Health check service

### Metrics
Prometheus metrics available at `:9093/metrics`:
- Request counts and latencies
- Database connection metrics
- Kafka producer/consumer metrics
- Credit operation metrics

### Logging
Structured JSON logging with configurable levels:
- `debug`, `info`, `warn`, `error`

## Development

### Code Generation
```bash
make generate        # Generate all code
make ent-generate   # Generate Ent code
make proto          # Generate protobuf code
```

### Code Quality
```bash
make lint           # Run linter
make format         # Format code
make vet           # Run go vet
```

### Database Operations
```bash
make db-connect     # Connect to database
make db-backup      # Backup database
make kafka-topics   # List Kafka topics
```

## Deployment

### Docker
```bash
make docker-build
docker run -p 8083:8083 -p 50053:50053 credit-service:latest
```

### Environment Variables
See `.env.example` for all available environment variables.

### Production Considerations
- Use PostgreSQL for production
- Configure proper Kafka cluster
- Set up monitoring and alerting
- Use HTTPS for REST API
- Configure proper JWT secrets
- Set up database backups

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `make lint` and `make test`
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API specifications in `proto-shared`

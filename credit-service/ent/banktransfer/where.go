// Code generated by ent, DO NOT EDIT.

package banktransfer

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldUserID, v))
}

// PlanID applies equality check predicate on the "plan_id" field. It's identical to PlanIDEQ.
func PlanID(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldPlanID, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldAmount, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCurrency, v))
}

// BankAccount applies equality check predicate on the "bank_account" field. It's identical to BankAccountEQ.
func BankAccount(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldBankAccount, v))
}

// ReferenceCode applies equality check predicate on the "reference_code" field. It's identical to ReferenceCodeEQ.
func ReferenceCode(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldReferenceCode, v))
}

// TransferInstructions applies equality check predicate on the "transfer_instructions" field. It's identical to TransferInstructionsEQ.
func TransferInstructions(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldTransferInstructions, v))
}

// ConfirmedAt applies equality check predicate on the "confirmed_at" field. It's identical to ConfirmedAtEQ.
func ConfirmedAt(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmedAt, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldExpiresAt, v))
}

// CreditsToAdd applies equality check predicate on the "credits_to_add" field. It's identical to CreditsToAddEQ.
func CreditsToAdd(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCreditsToAdd, v))
}

// BankTransactionID applies equality check predicate on the "bank_transaction_id" field. It's identical to BankTransactionIDEQ.
func BankTransactionID(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldBankTransactionID, v))
}

// ActualAmountReceived applies equality check predicate on the "actual_amount_received" field. It's identical to ActualAmountReceivedEQ.
func ActualAmountReceived(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldActualAmountReceived, v))
}

// ConfirmationMethod applies equality check predicate on the "confirmation_method" field. It's identical to ConfirmationMethodEQ.
func ConfirmationMethod(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmationMethod, v))
}

// ConfirmedBy applies equality check predicate on the "confirmed_by" field. It's identical to ConfirmedByEQ.
func ConfirmedBy(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmedBy, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldUserID, v))
}

// PlanIDEQ applies the EQ predicate on the "plan_id" field.
func PlanIDEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldPlanID, v))
}

// PlanIDNEQ applies the NEQ predicate on the "plan_id" field.
func PlanIDNEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldPlanID, v))
}

// PlanIDIn applies the In predicate on the "plan_id" field.
func PlanIDIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldPlanID, vs...))
}

// PlanIDNotIn applies the NotIn predicate on the "plan_id" field.
func PlanIDNotIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldPlanID, vs...))
}

// PlanIDGT applies the GT predicate on the "plan_id" field.
func PlanIDGT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldPlanID, v))
}

// PlanIDGTE applies the GTE predicate on the "plan_id" field.
func PlanIDGTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldPlanID, v))
}

// PlanIDLT applies the LT predicate on the "plan_id" field.
func PlanIDLT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldPlanID, v))
}

// PlanIDLTE applies the LTE predicate on the "plan_id" field.
func PlanIDLTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldPlanID, v))
}

// PlanIDIsNil applies the IsNil predicate on the "plan_id" field.
func PlanIDIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldPlanID))
}

// PlanIDNotNil applies the NotNil predicate on the "plan_id" field.
func PlanIDNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldPlanID))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldAmount, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldCurrency, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldStatus, vs...))
}

// BankAccountEQ applies the EQ predicate on the "bank_account" field.
func BankAccountEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldBankAccount, v))
}

// BankAccountNEQ applies the NEQ predicate on the "bank_account" field.
func BankAccountNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldBankAccount, v))
}

// BankAccountIn applies the In predicate on the "bank_account" field.
func BankAccountIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldBankAccount, vs...))
}

// BankAccountNotIn applies the NotIn predicate on the "bank_account" field.
func BankAccountNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldBankAccount, vs...))
}

// BankAccountGT applies the GT predicate on the "bank_account" field.
func BankAccountGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldBankAccount, v))
}

// BankAccountGTE applies the GTE predicate on the "bank_account" field.
func BankAccountGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldBankAccount, v))
}

// BankAccountLT applies the LT predicate on the "bank_account" field.
func BankAccountLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldBankAccount, v))
}

// BankAccountLTE applies the LTE predicate on the "bank_account" field.
func BankAccountLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldBankAccount, v))
}

// BankAccountContains applies the Contains predicate on the "bank_account" field.
func BankAccountContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldBankAccount, v))
}

// BankAccountHasPrefix applies the HasPrefix predicate on the "bank_account" field.
func BankAccountHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldBankAccount, v))
}

// BankAccountHasSuffix applies the HasSuffix predicate on the "bank_account" field.
func BankAccountHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldBankAccount, v))
}

// BankAccountEqualFold applies the EqualFold predicate on the "bank_account" field.
func BankAccountEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldBankAccount, v))
}

// BankAccountContainsFold applies the ContainsFold predicate on the "bank_account" field.
func BankAccountContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldBankAccount, v))
}

// ReferenceCodeEQ applies the EQ predicate on the "reference_code" field.
func ReferenceCodeEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldReferenceCode, v))
}

// ReferenceCodeNEQ applies the NEQ predicate on the "reference_code" field.
func ReferenceCodeNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldReferenceCode, v))
}

// ReferenceCodeIn applies the In predicate on the "reference_code" field.
func ReferenceCodeIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldReferenceCode, vs...))
}

// ReferenceCodeNotIn applies the NotIn predicate on the "reference_code" field.
func ReferenceCodeNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldReferenceCode, vs...))
}

// ReferenceCodeGT applies the GT predicate on the "reference_code" field.
func ReferenceCodeGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldReferenceCode, v))
}

// ReferenceCodeGTE applies the GTE predicate on the "reference_code" field.
func ReferenceCodeGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldReferenceCode, v))
}

// ReferenceCodeLT applies the LT predicate on the "reference_code" field.
func ReferenceCodeLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldReferenceCode, v))
}

// ReferenceCodeLTE applies the LTE predicate on the "reference_code" field.
func ReferenceCodeLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldReferenceCode, v))
}

// ReferenceCodeContains applies the Contains predicate on the "reference_code" field.
func ReferenceCodeContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldReferenceCode, v))
}

// ReferenceCodeHasPrefix applies the HasPrefix predicate on the "reference_code" field.
func ReferenceCodeHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldReferenceCode, v))
}

// ReferenceCodeHasSuffix applies the HasSuffix predicate on the "reference_code" field.
func ReferenceCodeHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldReferenceCode, v))
}

// ReferenceCodeEqualFold applies the EqualFold predicate on the "reference_code" field.
func ReferenceCodeEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldReferenceCode, v))
}

// ReferenceCodeContainsFold applies the ContainsFold predicate on the "reference_code" field.
func ReferenceCodeContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldReferenceCode, v))
}

// TransferInstructionsEQ applies the EQ predicate on the "transfer_instructions" field.
func TransferInstructionsEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldTransferInstructions, v))
}

// TransferInstructionsNEQ applies the NEQ predicate on the "transfer_instructions" field.
func TransferInstructionsNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldTransferInstructions, v))
}

// TransferInstructionsIn applies the In predicate on the "transfer_instructions" field.
func TransferInstructionsIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldTransferInstructions, vs...))
}

// TransferInstructionsNotIn applies the NotIn predicate on the "transfer_instructions" field.
func TransferInstructionsNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldTransferInstructions, vs...))
}

// TransferInstructionsGT applies the GT predicate on the "transfer_instructions" field.
func TransferInstructionsGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldTransferInstructions, v))
}

// TransferInstructionsGTE applies the GTE predicate on the "transfer_instructions" field.
func TransferInstructionsGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldTransferInstructions, v))
}

// TransferInstructionsLT applies the LT predicate on the "transfer_instructions" field.
func TransferInstructionsLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldTransferInstructions, v))
}

// TransferInstructionsLTE applies the LTE predicate on the "transfer_instructions" field.
func TransferInstructionsLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldTransferInstructions, v))
}

// TransferInstructionsContains applies the Contains predicate on the "transfer_instructions" field.
func TransferInstructionsContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldTransferInstructions, v))
}

// TransferInstructionsHasPrefix applies the HasPrefix predicate on the "transfer_instructions" field.
func TransferInstructionsHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldTransferInstructions, v))
}

// TransferInstructionsHasSuffix applies the HasSuffix predicate on the "transfer_instructions" field.
func TransferInstructionsHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldTransferInstructions, v))
}

// TransferInstructionsEqualFold applies the EqualFold predicate on the "transfer_instructions" field.
func TransferInstructionsEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldTransferInstructions, v))
}

// TransferInstructionsContainsFold applies the ContainsFold predicate on the "transfer_instructions" field.
func TransferInstructionsContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldTransferInstructions, v))
}

// ConfirmedAtEQ applies the EQ predicate on the "confirmed_at" field.
func ConfirmedAtEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmedAt, v))
}

// ConfirmedAtNEQ applies the NEQ predicate on the "confirmed_at" field.
func ConfirmedAtNEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldConfirmedAt, v))
}

// ConfirmedAtIn applies the In predicate on the "confirmed_at" field.
func ConfirmedAtIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldConfirmedAt, vs...))
}

// ConfirmedAtNotIn applies the NotIn predicate on the "confirmed_at" field.
func ConfirmedAtNotIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldConfirmedAt, vs...))
}

// ConfirmedAtGT applies the GT predicate on the "confirmed_at" field.
func ConfirmedAtGT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldConfirmedAt, v))
}

// ConfirmedAtGTE applies the GTE predicate on the "confirmed_at" field.
func ConfirmedAtGTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldConfirmedAt, v))
}

// ConfirmedAtLT applies the LT predicate on the "confirmed_at" field.
func ConfirmedAtLT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldConfirmedAt, v))
}

// ConfirmedAtLTE applies the LTE predicate on the "confirmed_at" field.
func ConfirmedAtLTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldConfirmedAt, v))
}

// ConfirmedAtIsNil applies the IsNil predicate on the "confirmed_at" field.
func ConfirmedAtIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldConfirmedAt))
}

// ConfirmedAtNotNil applies the NotNil predicate on the "confirmed_at" field.
func ConfirmedAtNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldConfirmedAt))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldExpiresAt, v))
}

// CreditsToAddEQ applies the EQ predicate on the "credits_to_add" field.
func CreditsToAddEQ(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCreditsToAdd, v))
}

// CreditsToAddNEQ applies the NEQ predicate on the "credits_to_add" field.
func CreditsToAddNEQ(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldCreditsToAdd, v))
}

// CreditsToAddIn applies the In predicate on the "credits_to_add" field.
func CreditsToAddIn(vs ...int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldCreditsToAdd, vs...))
}

// CreditsToAddNotIn applies the NotIn predicate on the "credits_to_add" field.
func CreditsToAddNotIn(vs ...int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldCreditsToAdd, vs...))
}

// CreditsToAddGT applies the GT predicate on the "credits_to_add" field.
func CreditsToAddGT(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldCreditsToAdd, v))
}

// CreditsToAddGTE applies the GTE predicate on the "credits_to_add" field.
func CreditsToAddGTE(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldCreditsToAdd, v))
}

// CreditsToAddLT applies the LT predicate on the "credits_to_add" field.
func CreditsToAddLT(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldCreditsToAdd, v))
}

// CreditsToAddLTE applies the LTE predicate on the "credits_to_add" field.
func CreditsToAddLTE(v int) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldCreditsToAdd, v))
}

// BankTransactionIDEQ applies the EQ predicate on the "bank_transaction_id" field.
func BankTransactionIDEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldBankTransactionID, v))
}

// BankTransactionIDNEQ applies the NEQ predicate on the "bank_transaction_id" field.
func BankTransactionIDNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldBankTransactionID, v))
}

// BankTransactionIDIn applies the In predicate on the "bank_transaction_id" field.
func BankTransactionIDIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldBankTransactionID, vs...))
}

// BankTransactionIDNotIn applies the NotIn predicate on the "bank_transaction_id" field.
func BankTransactionIDNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldBankTransactionID, vs...))
}

// BankTransactionIDGT applies the GT predicate on the "bank_transaction_id" field.
func BankTransactionIDGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldBankTransactionID, v))
}

// BankTransactionIDGTE applies the GTE predicate on the "bank_transaction_id" field.
func BankTransactionIDGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldBankTransactionID, v))
}

// BankTransactionIDLT applies the LT predicate on the "bank_transaction_id" field.
func BankTransactionIDLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldBankTransactionID, v))
}

// BankTransactionIDLTE applies the LTE predicate on the "bank_transaction_id" field.
func BankTransactionIDLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldBankTransactionID, v))
}

// BankTransactionIDContains applies the Contains predicate on the "bank_transaction_id" field.
func BankTransactionIDContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldBankTransactionID, v))
}

// BankTransactionIDHasPrefix applies the HasPrefix predicate on the "bank_transaction_id" field.
func BankTransactionIDHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldBankTransactionID, v))
}

// BankTransactionIDHasSuffix applies the HasSuffix predicate on the "bank_transaction_id" field.
func BankTransactionIDHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldBankTransactionID, v))
}

// BankTransactionIDIsNil applies the IsNil predicate on the "bank_transaction_id" field.
func BankTransactionIDIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldBankTransactionID))
}

// BankTransactionIDNotNil applies the NotNil predicate on the "bank_transaction_id" field.
func BankTransactionIDNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldBankTransactionID))
}

// BankTransactionIDEqualFold applies the EqualFold predicate on the "bank_transaction_id" field.
func BankTransactionIDEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldBankTransactionID, v))
}

// BankTransactionIDContainsFold applies the ContainsFold predicate on the "bank_transaction_id" field.
func BankTransactionIDContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldBankTransactionID, v))
}

// ActualAmountReceivedEQ applies the EQ predicate on the "actual_amount_received" field.
func ActualAmountReceivedEQ(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldActualAmountReceived, v))
}

// ActualAmountReceivedNEQ applies the NEQ predicate on the "actual_amount_received" field.
func ActualAmountReceivedNEQ(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldActualAmountReceived, v))
}

// ActualAmountReceivedIn applies the In predicate on the "actual_amount_received" field.
func ActualAmountReceivedIn(vs ...int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldActualAmountReceived, vs...))
}

// ActualAmountReceivedNotIn applies the NotIn predicate on the "actual_amount_received" field.
func ActualAmountReceivedNotIn(vs ...int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldActualAmountReceived, vs...))
}

// ActualAmountReceivedGT applies the GT predicate on the "actual_amount_received" field.
func ActualAmountReceivedGT(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldActualAmountReceived, v))
}

// ActualAmountReceivedGTE applies the GTE predicate on the "actual_amount_received" field.
func ActualAmountReceivedGTE(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldActualAmountReceived, v))
}

// ActualAmountReceivedLT applies the LT predicate on the "actual_amount_received" field.
func ActualAmountReceivedLT(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldActualAmountReceived, v))
}

// ActualAmountReceivedLTE applies the LTE predicate on the "actual_amount_received" field.
func ActualAmountReceivedLTE(v int64) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldActualAmountReceived, v))
}

// ActualAmountReceivedIsNil applies the IsNil predicate on the "actual_amount_received" field.
func ActualAmountReceivedIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldActualAmountReceived))
}

// ActualAmountReceivedNotNil applies the NotNil predicate on the "actual_amount_received" field.
func ActualAmountReceivedNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldActualAmountReceived))
}

// ConfirmationMethodEQ applies the EQ predicate on the "confirmation_method" field.
func ConfirmationMethodEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmationMethod, v))
}

// ConfirmationMethodNEQ applies the NEQ predicate on the "confirmation_method" field.
func ConfirmationMethodNEQ(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldConfirmationMethod, v))
}

// ConfirmationMethodIn applies the In predicate on the "confirmation_method" field.
func ConfirmationMethodIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldConfirmationMethod, vs...))
}

// ConfirmationMethodNotIn applies the NotIn predicate on the "confirmation_method" field.
func ConfirmationMethodNotIn(vs ...string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldConfirmationMethod, vs...))
}

// ConfirmationMethodGT applies the GT predicate on the "confirmation_method" field.
func ConfirmationMethodGT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldConfirmationMethod, v))
}

// ConfirmationMethodGTE applies the GTE predicate on the "confirmation_method" field.
func ConfirmationMethodGTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldConfirmationMethod, v))
}

// ConfirmationMethodLT applies the LT predicate on the "confirmation_method" field.
func ConfirmationMethodLT(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldConfirmationMethod, v))
}

// ConfirmationMethodLTE applies the LTE predicate on the "confirmation_method" field.
func ConfirmationMethodLTE(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldConfirmationMethod, v))
}

// ConfirmationMethodContains applies the Contains predicate on the "confirmation_method" field.
func ConfirmationMethodContains(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContains(FieldConfirmationMethod, v))
}

// ConfirmationMethodHasPrefix applies the HasPrefix predicate on the "confirmation_method" field.
func ConfirmationMethodHasPrefix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasPrefix(FieldConfirmationMethod, v))
}

// ConfirmationMethodHasSuffix applies the HasSuffix predicate on the "confirmation_method" field.
func ConfirmationMethodHasSuffix(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldHasSuffix(FieldConfirmationMethod, v))
}

// ConfirmationMethodEqualFold applies the EqualFold predicate on the "confirmation_method" field.
func ConfirmationMethodEqualFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEqualFold(FieldConfirmationMethod, v))
}

// ConfirmationMethodContainsFold applies the ContainsFold predicate on the "confirmation_method" field.
func ConfirmationMethodContainsFold(v string) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldContainsFold(FieldConfirmationMethod, v))
}

// ConfirmedByEQ applies the EQ predicate on the "confirmed_by" field.
func ConfirmedByEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldConfirmedBy, v))
}

// ConfirmedByNEQ applies the NEQ predicate on the "confirmed_by" field.
func ConfirmedByNEQ(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldConfirmedBy, v))
}

// ConfirmedByIn applies the In predicate on the "confirmed_by" field.
func ConfirmedByIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldConfirmedBy, vs...))
}

// ConfirmedByNotIn applies the NotIn predicate on the "confirmed_by" field.
func ConfirmedByNotIn(vs ...uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldConfirmedBy, vs...))
}

// ConfirmedByGT applies the GT predicate on the "confirmed_by" field.
func ConfirmedByGT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldConfirmedBy, v))
}

// ConfirmedByGTE applies the GTE predicate on the "confirmed_by" field.
func ConfirmedByGTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldConfirmedBy, v))
}

// ConfirmedByLT applies the LT predicate on the "confirmed_by" field.
func ConfirmedByLT(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldConfirmedBy, v))
}

// ConfirmedByLTE applies the LTE predicate on the "confirmed_by" field.
func ConfirmedByLTE(v uuid.UUID) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldConfirmedBy, v))
}

// ConfirmedByIsNil applies the IsNil predicate on the "confirmed_by" field.
func ConfirmedByIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldConfirmedBy))
}

// ConfirmedByNotNil applies the NotNil predicate on the "confirmed_by" field.
func ConfirmedByNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldConfirmedBy))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.BankTransfer {
	return predicate.BankTransfer(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.BankTransfer) predicate.BankTransfer {
	return predicate.BankTransfer(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.BankTransfer) predicate.BankTransfer {
	return predicate.BankTransfer(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.BankTransfer) predicate.BankTransfer {
	return predicate.BankTransfer(sql.NotPredicates(p))
}

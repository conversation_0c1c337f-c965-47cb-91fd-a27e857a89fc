package restful

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/notification-service/api/restful/handlers"
	"github.com/social-content-ai/notification-service/api/restful/middleware"
	"github.com/social-content-ai/notification-service/pkg/websocket"
	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/notification-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// SetupRoutes sets up all HTTP routes for notification service
func SetupRoutes(
	router *gin.Engine,
	notificationUC notification.UseCase,
	preferenceUC preference.UseCase,
	templateUC template.UseCase,
	wsHub *websocket.Hub,
	userClient userv1.AuthServiceClient,
	logger logging.Logger,
) {
	// Initialize handlers
	notificationHandler := handlers.NewNotificationHandler(notificationUC, logger)
	preferenceHandler := handlers.NewPreferenceHandler(preferenceUC, logger)
	templateHandler := handlers.NewTemplateHandler(templateUC, logger)
	websocketHandler := handlers.NewWebSocketHandler(wsHub, logger)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(userClient, logger)

	// Global middleware
	router.Use(middleware.CORS())
	router.Use(middleware.RequestLogger(logger))
	router.Use(middleware.ErrorHandler(logger))

	// Health check endpoint (no auth required)
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "notification-service",
			"status":  "ok",
		})
	})

	// WebSocket endpoint (auth required)
	router.GET("/ws", authMiddleware.RequireAuth(), websocketHandler.HandleWebSocket)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Notification routes (require authentication)
		notifications := v1.Group("/notifications")
		notifications.Use(authMiddleware.RequireAuth())
		{
			// Notification CRUD
			notifications.POST("", notificationHandler.SendNotification)
			notifications.POST("/templated", notificationHandler.SendTemplatedNotification)
			notifications.POST("/bulk", notificationHandler.SendBulkNotification)
			notifications.GET("", notificationHandler.ListNotifications)
			notifications.GET("/:id", notificationHandler.GetNotification)
			notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
			notifications.PUT("/read-all", notificationHandler.MarkAllAsRead)
			notifications.DELETE("/:id", notificationHandler.DeleteNotification)

			// Delivery status
			notifications.GET("/:id/delivery-status", notificationHandler.GetDeliveryStatus)
			notifications.GET("/delivery-stats", notificationHandler.GetDeliveryStats)

			// Notification analytics
			notifications.GET("/analytics", notificationHandler.GetNotificationAnalytics)
		}

		// Preference routes (require authentication)
		preferences := v1.Group("/preferences")
		preferences.Use(authMiddleware.RequireAuth())
		{
			preferences.GET("", preferenceHandler.GetPreferences)
			preferences.PUT("", preferenceHandler.UpdatePreferences)
			preferences.POST("/reset", preferenceHandler.ResetPreferences)
			preferences.GET("/defaults", preferenceHandler.GetDefaultPreferences)
		}

		// Template routes (require authentication)
		templates := v1.Group("/templates")
		templates.Use(authMiddleware.RequireAuth())
		{
			// Template CRUD
			templates.GET("", templateHandler.ListTemplates)
			templates.POST("", templateHandler.CreateTemplate)
			templates.GET("/:id", templateHandler.GetTemplate)
			templates.PUT("/:id", templateHandler.UpdateTemplate)
			templates.DELETE("/:id", templateHandler.DeleteTemplate)

			// Template operations
			templates.POST("/:id/preview", templateHandler.PreviewTemplate)
			templates.POST("/:id/test", templateHandler.TestTemplate)
			templates.POST("/:id/clone", templateHandler.CloneTemplate)

			// Template categories and tags
			templates.GET("/categories", templateHandler.GetTemplateCategories)
			templates.GET("/tags", templateHandler.GetTemplateTags)
		}

		// Admin routes (require admin authentication)
		admin := v1.Group("/admin")
		admin.Use(authMiddleware.AdminAuth())
		{
			// System notifications
			admin.POST("/notifications/system", notificationHandler.SendSystemNotification)
			admin.POST("/notifications/broadcast", notificationHandler.BroadcastNotification)

			// Template management
			admin.GET("/templates", templateHandler.AdminListTemplates)
			admin.PUT("/templates/:id/approve", templateHandler.ApproveTemplate)
			admin.PUT("/templates/:id/reject", templateHandler.RejectTemplate)

			// System analytics
			admin.GET("/analytics/overview", notificationHandler.GetSystemAnalytics)
			admin.GET("/analytics/delivery", notificationHandler.GetDeliveryAnalytics)
			admin.GET("/analytics/channels", notificationHandler.GetChannelAnalytics)

			// System configuration
			admin.GET("/config", notificationHandler.GetSystemConfig)
			admin.PUT("/config", notificationHandler.UpdateSystemConfig)

			// Queue management
			admin.GET("/queues/status", notificationHandler.GetQueueStatus)
			admin.POST("/queues/retry", notificationHandler.RetryFailedNotifications)
			admin.POST("/queues/clear", notificationHandler.ClearQueues)
		}

		// Webhook routes (no authentication required)
		webhooks := v1.Group("/webhooks")
		{
			// Email delivery webhooks
			webhooks.POST("/email/delivered", notificationHandler.EmailDeliveredWebhook)
			webhooks.POST("/email/bounced", notificationHandler.EmailBouncedWebhook)
			webhooks.POST("/email/complained", notificationHandler.EmailComplainedWebhook)

			// SMS delivery webhooks
			webhooks.POST("/sms/delivered", notificationHandler.SMSDeliveredWebhook)
			webhooks.POST("/sms/failed", notificationHandler.SMSFailedWebhook)

			// Push notification webhooks
			webhooks.POST("/push/delivered", notificationHandler.PushDeliveredWebhook)
			webhooks.POST("/push/failed", notificationHandler.PushFailedWebhook)
		}
	}

	logger.Info("REST API routes setup completed")
	logger.WithFields(map[string]interface{}{
		"notification_endpoints": "✓ Send, CRUD, Analytics, Delivery tracking",
		"preference_endpoints":   "✓ Get, Update, Reset, Defaults",
		"template_endpoints":     "✓ CRUD, Preview, Test, Clone, Categories",
		"admin_endpoints":        "✓ System notifications, Analytics, Queue management",
		"webhook_endpoints":      "✓ Email, SMS, Push delivery status",
		"websocket_endpoint":     "✓ Real-time notifications",
		"auth_required":          "✓ All endpoints except /health and webhooks",
	}).Info("Available REST API endpoints")
}

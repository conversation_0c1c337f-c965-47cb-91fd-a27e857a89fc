package handlers

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/integration-service/usecase/integration"
)

// IntegrationHandler handles integration REST API endpoints
type IntegrationHandler struct {
	*BaseHandler
}

// NewIntegrationHandler creates a new integration handler
func NewIntegrationHandler(base *BaseHandler) *IntegrationHandler {
	return &IntegrationHandler{
		BaseHandler: base,
	}
}

// GetOAuthURL generates OAuth URL for platform connection
// @Summary Generate OAuth URL
// @Description Generate OAuth authorization URL for connecting a platform account
// @Tags integrations
// @Accept json
// @Produce json
// @Param request body GetOAuthURLRequest true "OAuth URL request"
// @Success 200 {object} GetOAuthURLResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/integrations/oauth/url [post]
func (h *IntegrationHandler) GetOAuthURL(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}

	var req GetOAuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if req.Platform == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_required_fields",
			Message: "platform is required",
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &integration.GetOAuthURLRequest{
		UserID:               userID.(string),
		Platform:             req.Platform,
		RedirectURI:          req.RedirectURI,
		RequestedPermissions: req.RequestedPermissions,
		State:                req.State,
	}

	response, err := h.integrationUseCase.GetOAuthURL(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate OAuth URL")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "oauth_url_generation_failed",
			Message: "Failed to generate OAuth URL",
		})
		return
	}

	c.JSON(http.StatusOK, GetOAuthURLResponse{
		AuthURL:     response.AuthURL,
		State:       response.State,
		Permissions: response.Permissions,
	})
}

// ListIntegrations lists all platform integrations for a user
// @Summary List platform integrations
// @Description Get list of connected platform accounts for a user
// @Tags integrations
// @Accept json
// @Produce json
// @Param workspace_id query string false "Workspace ID"
// @Param platform query string false "Platform filter"
// @Param status query string false "Status filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} ListIntegrationsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/integrations [get]
func (h *IntegrationHandler) ListIntegrations(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	// Parse pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Convert to usecase request
	req := &integration.ListAccountsRequest{
		UserID:      userID,
		WorkspaceID: c.Query("workspace_id"),
		Platform:    c.Query("platform"),
		Status:      c.Query("status"),
		Limit:       int32(limit),
		Offset:      int32((page - 1) * limit),
	}

	response, err := h.integrationUseCase.ListAccounts(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list integrations")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "list_integrations_failed",
			Message: "Failed to list integrations",
		})
		return
	}

	// Convert accounts to response format
	accounts := make([]PlatformAccount, len(response.Accounts))
	for i, account := range response.Accounts {
		accounts[i] = PlatformAccount{
			ID:          account.ID,
			UserID:      account.UserID,
			WorkspaceID: account.WorkspaceID,
			Platform:    account.Platform,
			AccountID:   account.AccountID,
			Username:    account.Username,
			DisplayName: account.DisplayName,
			AvatarURL:   account.AvatarURL,
			Status:      account.Status,
			Permissions: account.Permissions,
			Metadata:    account.Metadata,
			ConnectedAt: account.ConnectedAt,
			LastUsed:    account.LastUsed,
		}
	}

	c.JSON(http.StatusOK, ListIntegrationsResponse{
		Accounts:   accounts,
		TotalCount: response.TotalCount,
		Page:       int32(page),
		Limit:      int32(limit),
	})
}

// GetIntegration gets a specific platform integration
// @Summary Get platform integration
// @Description Get details of a specific platform integration
// @Tags integrations
// @Accept json
// @Produce json
// @Param id path string true "Integration ID"
// @Success 200 {object} PlatformAccount
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/integrations/{id} [get]
func (h *IntegrationHandler) GetIntegration(c *gin.Context) {
	integrationID := c.Param("id")
	if integrationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_parameters",
			Message: "integration ID is required",
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	// Validate account request
	req := &integration.ValidateAccountRequest{
		AccountID:        integrationID,
		UserID:           userID,
		CheckPermissions: true,
	}

	response, err := h.integrationUseCase.ValidateAccount(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get integration")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "get_integration_failed",
			Message: "Failed to get integration",
		})
		return
	}

	if !response.IsValid {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "integration_not_found",
			Message: "Integration not found or access denied",
		})
		return
	}

	// For now, return basic validation info
	// TODO: Implement proper GetAccount method in usecase
	c.JSON(http.StatusOK, gin.H{
		"id":                  integrationID,
		"user_id":             userID,
		"is_valid":            response.IsValid,
		"token_expired":       response.TokenExpired,
		"missing_permissions": response.MissingPermissions,
		"last_validated":      response.LastValidated,
	})
}

// DeleteIntegration disconnects a platform integration
// @Summary Disconnect platform integration
// @Description Disconnect and remove a platform integration
// @Tags integrations
// @Accept json
// @Produce json
// @Param id path string true "Integration ID"
// @Param request body DisconnectIntegrationRequest true "Disconnect request"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/integrations/{id} [delete]
func (h *IntegrationHandler) DeleteIntegration(c *gin.Context) {
	integrationID := c.Param("id")
	if integrationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_parameters",
			Message: "integration ID is required",
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	var req DisconnectIntegrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &integration.DisconnectAccountRequest{
		AccountID:   integrationID,
		UserID:      userID,
		RevokeToken: req.RevokeToken,
	}

	err := h.integrationUseCase.DisconnectAccount(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to disconnect integration")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "disconnect_failed",
			Message: "Failed to disconnect integration",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Integration disconnected successfully",
	})
}

// RefreshIntegration refreshes the access token for a platform integration
// @Summary Refresh integration token
// @Description Refresh the access token for a platform integration
// @Tags integrations
// @Accept json
// @Produce json
// @Param id path string true "Integration ID"
// @Param request body RefreshIntegrationRequest true "Refresh request"
// @Success 200 {object} RefreshIntegrationResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/integrations/{id}/refresh [post]
func (h *IntegrationHandler) RefreshIntegration(c *gin.Context) {
	integrationID := c.Param("id")
	if integrationID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_parameters",
			Message: "integration ID is required",
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not authenticated",
		})
		return
	}
	userID := userIDInterface.(string)

	var req RefreshIntegrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &integration.RefreshTokenRequest{
		AccountID:    integrationID,
		UserID:       userID,
		ForceRefresh: req.ForceRefresh,
	}

	response, err := h.integrationUseCase.RefreshToken(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to refresh integration token")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "refresh_failed",
			Message: "Failed to refresh integration token",
		})
		return
	}

	c.JSON(http.StatusOK, RefreshIntegrationResponse{
		Success:      response.Success,
		NewExpiresAt: response.NewExpiresAt,
		ErrorMessage: response.ErrorMessage,
	})
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Workspace holds the schema definition for the Workspace entity.
type Workspace struct {
	ent.Schema
}

// Fields of the Workspace.
func (Workspace) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("owner_id", uuid.UUID{}).
			Comment("User who owns this workspace"),
		field.String("name").
			MaxLen(100),
		field.Text("description").
			Optional(),
		field.String("color").
			MaxLen(7).
			Default("#3B82F6").
			Comment("Hex color code for workspace theme"),
		field.String("icon").
			MaxLen(50).
			Optional().
			Comment("Icon identifier for workspace"),
		field.JSON("settings", map[string]interface{}{}).
			Optional().
			Comment("Workspace-specific settings"),
		field.JSON("default_platforms", []string{}).
			Optional().
			Comment("Default platforms for new posts"),
		field.JSON("brand_guidelines", map[string]interface{}{}).
			Optional().
			Comment("Brand voice, tone, and style guidelines"),
		field.Bool("is_personal").
			Default(true).
			Comment("Whether this is a personal workspace"),
		field.Bool("is_active").
			Default(true),
		field.Int("post_count").
			Default(0).
			Comment("Total number of posts in this workspace"),
		field.Int("template_count").
			Default(0).
			Comment("Total number of templates in this workspace"),
		field.Time("last_activity_at").
			Optional().
			Comment("Last time any activity happened in this workspace"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the Workspace.
func (Workspace) Edges() []ent.Edge {
	return nil
}

// Indexes of the Workspace.
func (Workspace) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("owner_id"),
		index.Fields("is_active"),
		index.Fields("is_personal"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("last_activity_at"),
		index.Fields("owner_id", "is_active"),
	}
}

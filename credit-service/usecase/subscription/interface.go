package subscription

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the subscription management business logic interface
type UseCase interface {
	// Subscription management
	CreateSubscription(ctx context.Context, req *models.CreateSubscriptionRequest) (*models.SubscriptionResponse, error)
	GetSubscription(ctx context.Context, userID string) (*models.SubscriptionResponse, error)
	GetSubscriptionByID(ctx context.Context, subscriptionID string) (*models.SubscriptionResponse, error)
	UpdateSubscription(ctx context.Context, req *models.UpdateSubscriptionRequest) (*models.SubscriptionResponse, error)
	CancelSubscription(ctx context.Context, req *models.CancelSubscriptionRequest) (*models.SubscriptionResponse, error)
	ReactivateSubscription(ctx context.Context, req *models.ReactivateSubscriptionRequest) (*models.SubscriptionResponse, error)

	// Subscription plans
	GetSubscriptionPlans(ctx context.Context) ([]*models.SubscriptionPlanResponse, error)
	GetSubscriptionPlan(ctx context.Context, planID string) (*models.SubscriptionPlanResponse, error)

	// Subscription billing
	GetSubscriptionInvoices(ctx context.Context, req *models.GetInvoicesRequest) (*models.InvoicesResponse, error)
	GetUpcomingInvoice(ctx context.Context, userID string) (*models.InvoiceResponse, error)
	ProcessSubscriptionPayment(ctx context.Context, req *models.ProcessPaymentRequest) (*models.PaymentResponse, error)

	// Subscription usage
	GetSubscriptionUsage(ctx context.Context, userID string) (*models.SubscriptionUsageResponse, error)
	UpdateSubscriptionUsage(ctx context.Context, req *models.UpdateUsageRequest) (*models.SubscriptionUsageResponse, error)
	ResetSubscriptionUsage(ctx context.Context, userID string) (*models.SubscriptionUsageResponse, error)

	// Subscription lifecycle
	RenewSubscription(ctx context.Context, userID string) (*models.SubscriptionResponse, error)
	UpgradeSubscription(ctx context.Context, req *models.UpgradeSubscriptionRequest) (*models.SubscriptionResponse, error)
	DowngradeSubscription(ctx context.Context, req *models.DowngradeSubscriptionRequest) (*models.SubscriptionResponse, error)

	// Subscription analytics
	GetSubscriptionAnalytics(ctx context.Context, userID string, period string) (*models.SubscriptionAnalyticsResponse, error)
	GetSubscriptionHistory(ctx context.Context, req *models.SubscriptionHistoryRequest) (*models.SubscriptionHistoryResponse, error)

	// Admin operations
	AdminListSubscriptions(ctx context.Context, req *models.AdminListSubscriptionsRequest) (*models.AdminSubscriptionsResponse, error)
	AdminGetSubscription(ctx context.Context, subscriptionID string) (*models.SubscriptionResponse, error)
	AdminUpdateSubscription(ctx context.Context, req *models.AdminUpdateSubscriptionRequest) (*models.SubscriptionResponse, error)
	AdminCancelSubscription(ctx context.Context, subscriptionID string, reason string) (*models.SubscriptionResponse, error)
	AdminCreateSubscriptionPlan(ctx context.Context, req *models.CreateSubscriptionPlanRequest) (*models.SubscriptionPlanResponse, error)
	AdminUpdateSubscriptionPlan(ctx context.Context, req *models.UpdateSubscriptionPlanRequest) (*models.SubscriptionPlanResponse, error)
	AdminDeleteSubscriptionPlan(ctx context.Context, planID string) error
	AdminGetSubscriptionAnalytics(ctx context.Context, period string) (*models.AdminSubscriptionAnalyticsResponse, error)

	// Webhook handlers
	HandleStripeWebhook(ctx context.Context, event *models.StripeWebhookEvent) error
	HandlePaymentWebhook(ctx context.Context, event *models.PaymentWebhookEvent) error

	// Internal operations (for service-to-service communication)
	InternalGetSubscription(ctx context.Context, userID string) (*models.SubscriptionResponse, error)
	InternalUpdateSubscriptionUsage(ctx context.Context, req *models.UpdateUsageRequest) (*models.SubscriptionUsageResponse, error)
	InternalCheckSubscriptionLimits(ctx context.Context, userID string, usage int) (*models.SubscriptionLimitCheckResponse, error)
}

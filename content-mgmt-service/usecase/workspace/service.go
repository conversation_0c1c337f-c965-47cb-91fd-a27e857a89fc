package workspace

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new workspace service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreateWorkspace creates a new workspace
func (s *service) CreateWorkspace(ctx context.Context, req *models.CreateWorkspaceRequest) (*models.WorkspaceResponse, error) {
	s.logger.WithField("owner_id", req.OwnerID).Info("Creating workspace")

	// Parse owner ID to UUID
	ownerUUID, err := uuid.Parse(req.OwnerID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid owner ID format")
		return nil, fmt.Errorf("invalid owner ID format: %w", err)
	}

	// Determine if it's a personal workspace
	isPersonal := req.Type == "personal"

	// Create workspace in database
	workspace, err := s.writeDB.Workspace.
		Create().
		SetName(req.Name).
		SetDescription(req.Description).
		SetOwnerID(ownerUUID).
		SetIsPersonal(isPersonal).
		SetIsActive(true).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to create workspace")
		return nil, fmt.Errorf("failed to create workspace: %w", err)
	}

	// Convert to response model
	response := &models.WorkspaceResponse{
		BaseModel: models.BaseModel{
			ID:        workspace.ID.String(),
			CreatedAt: workspace.CreatedAt,
			UpdatedAt: workspace.UpdatedAt,
		},
		Name:        workspace.Name,
		Description: workspace.Description,
		OwnerID:     workspace.OwnerID.String(),
		Type:        req.Type,
		Status:      "active",
		Settings:    models.WorkspaceSettings{}, // Default settings
	}

	s.logger.WithField("workspace_id", workspace.ID).Info("Workspace created successfully")
	return response, nil
}

// GetWorkspace gets a workspace by ID
func (s *service) GetWorkspace(ctx context.Context, workspaceID, userID string) (*models.WorkspaceResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"workspace_id": workspaceID,
		"user_id":      userID,
	}).Info("Getting workspace")

	// Parse workspace ID to UUID
	workspaceUUID, err := uuid.Parse(workspaceID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid workspace ID format")
		return nil, fmt.Errorf("invalid workspace ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get workspace from database
	workspace, err := s.readDB.Workspace.
		Query().
		Where(
			workspace.ID(workspaceUUID),
			workspace.IsActive(true),
			workspace.Or(
				workspace.OwnerID(userUUID),
				// TODO: Add member check when member relationship is implemented
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("workspace not found")
		}
		s.logger.WithError(err).Error("Failed to get workspace")
		return nil, fmt.Errorf("failed to get workspace: %w", err)
	}

	// Convert to response model
	response := &models.WorkspaceResponse{
		BaseModel: models.BaseModel{
			ID:        workspace.ID.String(),
			CreatedAt: workspace.CreatedAt,
			UpdatedAt: workspace.UpdatedAt,
		},
		Name:        workspace.Name,
		Description: workspace.Description,
		OwnerID:     workspace.OwnerID.String(),
		Type:        getWorkspaceType(workspace.IsPersonal),
		Status:      getWorkspaceStatus(workspace.IsActive),
		Settings:    models.WorkspaceSettings{}, // TODO: Parse from workspace.Settings
	}

	return response, nil
}

// Helper function to get workspace type
func getWorkspaceType(isPersonal bool) string {
	if isPersonal {
		return "personal"
	}
	return "team"
}

// Helper function to get workspace status
func getWorkspaceStatus(isActive bool) string {
	if isActive {
		return "active"
	}
	return "suspended"
}

// UpdateWorkspace updates a workspace
func (s *service) UpdateWorkspace(ctx context.Context, req *models.UpdateWorkspaceRequest) (*models.WorkspaceResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"workspace_id": req.ID,
		"user_id":      req.UserID,
	}).Info("Updating workspace")

	// Parse workspace ID to UUID
	workspaceUUID, err := uuid.Parse(req.ID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid workspace ID format")
		return nil, fmt.Errorf("invalid workspace ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build update query
	updateQuery := s.writeDB.Workspace.
		Update().
		Where(
			workspace.ID(workspaceUUID),
			workspace.OwnerID(userUUID),
			workspace.IsActive(true),
		)

	// Apply updates only for non-empty fields
	if req.Name != "" {
		updateQuery = updateQuery.SetName(req.Name)
	}

	if req.Description != "" {
		updateQuery = updateQuery.SetDescription(req.Description)
	}

	// Execute update
	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update workspace")
		return nil, fmt.Errorf("failed to update workspace: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("workspace not found or no changes made")
	}

	// Get updated workspace
	updatedWorkspace, err := s.readDB.Workspace.
		Query().
		Where(
			workspace.ID(workspaceUUID),
			workspace.OwnerID(userUUID),
			workspace.IsActive(true),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated workspace")
		return nil, fmt.Errorf("failed to get updated workspace: %w", err)
	}

	// Convert to response model
	response := &models.WorkspaceResponse{
		BaseModel: models.BaseModel{
			ID:        updatedWorkspace.ID.String(),
			CreatedAt: updatedWorkspace.CreatedAt,
			UpdatedAt: updatedWorkspace.UpdatedAt,
		},
		Name:        updatedWorkspace.Name,
		Description: updatedWorkspace.Description,
		OwnerID:     updatedWorkspace.OwnerID.String(),
		Type:        getWorkspaceType(updatedWorkspace.IsPersonal),
		Status:      getWorkspaceStatus(updatedWorkspace.IsActive),
		Settings:    models.WorkspaceSettings{}, // TODO: Parse from workspace.Settings
	}

	s.logger.WithField("workspace_id", req.ID).Info("Workspace updated successfully")
	return response, nil
}

// DeleteWorkspace deletes a workspace
func (s *service) DeleteWorkspace(ctx context.Context, workspaceID, userID string) error {
	s.logger.WithFields(map[string]interface{}{
		"workspace_id": workspaceID,
		"user_id":      userID,
	}).Info("Deleting workspace")

	// Parse workspace ID to UUID
	workspaceUUID, err := uuid.Parse(workspaceID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid workspace ID format")
		return fmt.Errorf("invalid workspace ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if workspace exists and user is owner
	existingWorkspace, err := s.readDB.Workspace.
		Query().
		Where(
			workspace.ID(workspaceUUID),
			workspace.OwnerID(userUUID),
			workspace.IsActive(true),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("workspace not found")
		}
		s.logger.WithError(err).Error("Failed to get workspace")
		return fmt.Errorf("failed to get workspace: %w", err)
	}

	// Prevent deletion of personal workspace
	if existingWorkspace.IsPersonal {
		return fmt.Errorf("cannot delete personal workspace")
	}

	// Soft delete the workspace by setting is_active to false
	affected, err := s.writeDB.Workspace.
		Update().
		Where(
			workspace.ID(workspaceUUID),
			workspace.OwnerID(userUUID),
			workspace.IsActive(true),
		).
		SetIsActive(false).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete workspace")
		return fmt.Errorf("failed to delete workspace: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("workspace not found or already deleted")
	}

	s.logger.WithField("workspace_id", workspaceID).Info("Workspace deleted successfully")
	return nil
}

// ListWorkspaces lists workspaces with pagination
func (s *service) ListWorkspaces(ctx context.Context, req *models.ListWorkspacesRequest) (*models.ListWorkspacesResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing workspaces")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query
	query := s.readDB.Workspace.
		Query().
		Where(
			workspace.IsActive(true),
			workspace.OwnerID(userUUID),
		)

	// Apply filters
	if req.Type != "" {
		isPersonal := req.Type == "personal"
		query = query.Where(workspace.IsPersonal(isPersonal))
	}

	if req.Search != "" {
		query = query.Where(
			workspace.Or(
				workspace.NameContains(req.Search),
				workspace.DescriptionContains(req.Search),
			),
		)
	}

	// Apply sorting
	switch req.SortBy {
	case "name":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(workspace.FieldName))
		} else {
			query = query.Order(ent.Asc(workspace.FieldName))
		}
	case "created_at":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(workspace.FieldCreatedAt))
		} else {
			query = query.Order(ent.Asc(workspace.FieldCreatedAt))
		}
	default:
		query = query.Order(ent.Desc(workspace.FieldUpdatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count workspaces")
		return nil, fmt.Errorf("failed to count workspaces: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	workspaces, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list workspaces")
		return nil, fmt.Errorf("failed to list workspaces: %w", err)
	}

	// Convert to response models
	workspaceResponses := make([]models.WorkspaceResponse, len(workspaces))
	for i, ws := range workspaces {
		workspaceResponses[i] = models.WorkspaceResponse{
			BaseModel: models.BaseModel{
				ID:        ws.ID.String(),
				CreatedAt: ws.CreatedAt,
				UpdatedAt: ws.UpdatedAt,
			},
			Name:        ws.Name,
			Description: ws.Description,
			OwnerID:     ws.OwnerID.String(),
			Type:        getWorkspaceType(ws.IsPersonal),
			Status:      getWorkspaceStatus(ws.IsActive),
			Settings:    models.WorkspaceSettings{}, // TODO: Parse from ws.Settings
		}
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	response := &models.ListWorkspacesResponse{
		Workspaces: workspaceResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	return response, nil
}

// AddMember adds a member to workspace
func (s *service) AddMember(ctx context.Context, req *models.AddMemberRequest) (*models.WorkspaceMember, error) {
	// TODO: Implement add member
	return nil, fmt.Errorf("not implemented")
}

// ListMembers lists workspace members
func (s *service) ListMembers(ctx context.Context, req *models.ListMembersRequest) (*models.ListMembersResponse, error) {
	// TODO: Implement list members
	return nil, fmt.Errorf("not implemented")
}

// UpdateMemberRole updates member role
func (s *service) UpdateMemberRole(ctx context.Context, req *models.UpdateMemberRoleRequest) (*models.WorkspaceMember, error) {
	// TODO: Implement update member role
	return nil, fmt.Errorf("not implemented")
}

// RemoveMember removes a member from workspace
func (s *service) RemoveMember(ctx context.Context, workspaceID, userID, memberID string) error {
	// TODO: Implement remove member
	return fmt.Errorf("not implemented")
}

// GetWorkspacePosts gets workspace posts
func (s *service) GetWorkspacePosts(ctx context.Context, workspaceID, userID string, page, limit int) (*models.ListPostsResponse, error) {
	// TODO: Implement get workspace posts
	return nil, fmt.Errorf("not implemented")
}

// GetWorkspaceTemplates gets workspace templates
func (s *service) GetWorkspaceTemplates(ctx context.Context, workspaceID, userID string, page, limit int) (*models.ListTemplatesResponse, error) {
	// TODO: Implement get workspace templates
	return nil, fmt.Errorf("not implemented")
}

// UpdateWorkspaceSettings updates workspace settings
func (s *service) UpdateWorkspaceSettings(ctx context.Context, workspaceID, userID string, settings models.WorkspaceSettings) (*models.WorkspaceResponse, error) {
	// TODO: Implement update workspace settings
	return nil, fmt.Errorf("not implemented")
}

// GetWorkspaceSettings gets workspace settings
func (s *service) GetWorkspaceSettings(ctx context.Context, workspaceID, userID string) (*models.WorkspaceSettings, error) {
	// TODO: Implement get workspace settings
	return nil, fmt.Errorf("not implemented")
}

// GetWorkspaceAnalytics gets workspace analytics
func (s *service) GetWorkspaceAnalytics(ctx context.Context, req *models.WorkspaceAnalyticsRequest) (*models.WorkspaceAnalyticsResponse, error) {
	// TODO: Implement get workspace analytics
	return nil, fmt.Errorf("not implemented")
}

// GetWorkspacesAnalytics gets workspaces analytics
func (s *service) GetWorkspacesAnalytics(ctx context.Context, userID string, period string) (*models.WorkspaceAnalyticsResponse, error) {
	// TODO: Implement get workspaces analytics
	return nil, fmt.Errorf("not implemented")
}

// AdminListWorkspaces lists workspaces (admin)
func (s *service) AdminListWorkspaces(ctx context.Context, req *models.ListWorkspacesRequest) (*models.ListWorkspacesResponse, error) {
	// TODO: Implement admin list workspaces
	return nil, fmt.Errorf("not implemented")
}

// AdminGetWorkspace gets workspace (admin)
func (s *service) AdminGetWorkspace(ctx context.Context, workspaceID string) (*models.WorkspaceResponse, error) {
	// TODO: Implement admin get workspace
	return nil, fmt.Errorf("not implemented")
}

// AdminDeleteWorkspace deletes workspace (admin)
func (s *service) AdminDeleteWorkspace(ctx context.Context, workspaceID string) error {
	// TODO: Implement admin delete workspace
	return fmt.Errorf("not implemented")
}

// GetAdminWorkspaceAnalytics gets admin workspace analytics
func (s *service) GetAdminWorkspaceAnalytics(ctx context.Context, period string) (*models.WorkspaceAnalyticsResponse, error) {
	// TODO: Implement get admin workspace analytics
	return nil, fmt.Errorf("not implemented")
}

package payment

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// MockProcessor is a mock implementation of the Processor interface for testing
type MockProcessor struct {
	shouldFail bool
}

// NewMockProcessor creates a new mock payment processor
func NewMockProcessor(shouldFail bool) Processor {
	return &MockProcessor{
		shouldFail: shouldFail,
	}
}

// ProcessPayment processes a payment (mock implementation)
func (m *MockProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock payment failed")
	}

	paymentID := uuid.New().String()

	return &PaymentResponse{
		PaymentID:  paymentID,
		Status:     "created",
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: fmt.Sprintf("https://mock-paypal.com/checkout?payment_id=%s", paymentID),
		PaymentInfo: map[string]interface{}{
			"approval_url": fmt.Sprintf("https://mock-paypal.com/checkout?payment_id=%s", paymentID),
			"execute_url":  fmt.Sprintf("https://mock-paypal.com/execute?payment_id=%s", paymentID),
		},
		ExpiresAt: time.Now().Add(1 * time.Hour).Format(time.RFC3339),
	}, nil
}

// RefundPayment refunds a payment (mock implementation)
func (m *MockProcessor) RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock refund failed")
	}

	return &RefundResponse{
		RefundID:  uuid.New().String(),
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// CancelPayment cancels a payment (mock implementation)
func (m *MockProcessor) CancelPayment(ctx context.Context, paymentID string) error {
	if m.shouldFail {
		return fmt.Errorf("mock cancel failed")
	}
	return nil
}

// GetPaymentStatus gets payment status (mock implementation)
func (m *MockProcessor) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock get status failed")
	}

	return &PaymentStatus{
		PaymentID:   paymentID,
		Status:      "completed",
		Amount:      1000, // $10.00
		Currency:    "USD",
		ProcessedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// ValidateWebhook validates a webhook (mock implementation)
func (m *MockProcessor) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	if m.shouldFail {
		return false, fmt.Errorf("mock webhook validation failed")
	}
	return true, nil
}

// ProcessWebhook processes a webhook (mock implementation)
func (m *MockProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock webhook processing failed")
	}

	return &WebhookEvent{
		ID:   uuid.New().String(),
		Type: "payment.completed",
		Data: map[string]interface{}{
			"payment_id": "mock-payment-id",
			"status":     "completed",
		},
		CreatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

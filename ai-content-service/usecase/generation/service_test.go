package generation

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/social-content-ai/ai-content-service/ent"
	"github.com/social-content-ai/ai-content-service/ent/generation"
	"github.com/social-content-ai/ai-content-service/pkg/ai"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockAIService is a mock implementation of ai.Service
type MockAIService struct {
	mock.Mock
}

func (m *MockAIService) GenerateContent(ctx context.Context, req *ai.GenerationRequest) (*ai.GenerationResult, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*ai.GenerationResult), args.Error(1)
}

func (m *MockAIService) ImproveContent(ctx context.Context, req *ai.ImprovementRequest) (*ai.ImprovementResult, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*ai.ImprovementResult), args.Error(1)
}

func (m *MockAIService) GetAvailableModels(ctx context.Context) ([]*ai.ModelInfo, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*ai.ModelInfo), args.Error(1)
}

func (m *MockAIService) GetModelInfo(ctx context.Context, modelID string) (*ai.ModelInfo, error) {
	args := m.Called(ctx, modelID)
	return args.Get(0).(*ai.ModelInfo), args.Error(1)
}

func (m *MockAIService) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// GenerationServiceTestSuite defines the test suite for generation service
type GenerationServiceTestSuite struct {
	suite.Suite
	service   UseCase
	aiService *MockAIService
	logger    logging.Logger
	ctx       context.Context
}

// SetupTest sets up the test suite
func (suite *GenerationServiceTestSuite) SetupTest() {
	suite.aiService = &MockAIService{}
	suite.logger = logging.NewLogger(logging.DefaultConfig())
	suite.ctx = context.Background()
	
	// Note: In a real implementation, you would need to properly mock the ent.Client
	// For now, we'll create a simplified version
	suite.service = &service{
		readDB:    nil, // Would be mocked ent.Client
		writeDB:   nil, // Would be mocked ent.Client
		aiService: suite.aiService,
		logger:    suite.logger,
	}
}

// TestGenerateContentRequest_Validation tests generation request validation
func (suite *GenerationServiceTestSuite) TestGenerateContentRequest_Validation() {
	testCases := []struct {
		name        string
		request     *GenerateContentRequest
		expectError bool
	}{
		{
			name: "Valid request",
			request: &GenerateContentRequest{
				UserID:      uuid.New().String(),
				Topic:       "How to use social media for business",
				ContentType: "educational",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			expectError: false,
		},
		{
			name: "Empty topic",
			request: &GenerateContentRequest{
				UserID:      uuid.New().String(),
				Topic:       "",
				ContentType: "educational",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			expectError: true,
		},
		{
			name: "Invalid user ID",
			request: &GenerateContentRequest{
				UserID:      "invalid-uuid",
				Topic:       "Test topic",
				ContentType: "educational",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			expectError: true,
		},
		{
			name: "Invalid content type",
			request: &GenerateContentRequest{
				UserID:      uuid.New().String(),
				Topic:       "Test topic",
				ContentType: "invalid",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Basic validation checks
			if tc.request.Topic == "" {
				suite.True(tc.expectError, "Should expect error for empty topic")
			}
			if tc.request.UserID != "" {
				_, err := uuid.Parse(tc.request.UserID)
				if err != nil {
					suite.True(tc.expectError, "Should expect error for invalid UUID")
				}
			}
			
			validContentTypes := []string{"educational", "promotional", "entertainment", "informational", "news", "tutorial"}
			if tc.request.ContentType != "" && !suite.contains(validContentTypes, tc.request.ContentType) {
				suite.True(tc.expectError, "Should expect error for invalid content type")
			}
		})
	}
}

// TestCalculateCreditsUsed tests credit calculation
func (suite *GenerationServiceTestSuite) TestCalculateCreditsUsed() {
	s := &service{}

	testCases := []struct {
		model         string
		contentLength int
		expectedMin   int
		expectedMax   int
	}{
		{"gpt-4", 100, 3, 4},
		{"gpt-3.5-turbo", 100, 1, 2},
		{"claude-3", 100, 3, 4},
		{"gemini-pro", 100, 2, 3},
		{"gpt-4", 1500, 4, 5}, // Long content should cost more
	}

	for _, tc := range testCases {
		suite.Run(tc.model, func() {
			credits := s.calculateCreditsUsed(tc.model, tc.contentLength)
			suite.GreaterOrEqual(credits, tc.expectedMin)
			suite.LessOrEqual(credits, tc.expectedMax)
		})
	}
}

// TestExtractKeywords tests keyword extraction
func (suite *GenerationServiceTestSuite) TestExtractKeywords() {
	s := &service{}

	testCases := []struct {
		topic    string
		expected []string
	}{
		{
			topic:    "How to use social media for business marketing",
			expected: []string{"social", "media", "business", "marketing"},
		},
		{
			topic:    "The best practices for content creation",
			expected: []string{"best", "practices", "content", "creation"},
		},
		{
			topic:    "AI and machine learning in healthcare",
			expected: []string{"machine", "learning", "healthcare"},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.topic, func() {
			keywords := s.extractKeywords(tc.topic)
			suite.NotEmpty(keywords)
			
			// Check that some expected keywords are present
			for _, expected := range tc.expected {
				found := false
				for _, keyword := range keywords {
					if keyword == expected {
						found = true
						break
					}
				}
				if len(tc.expected) > 0 {
					// At least some keywords should be found
					suite.True(len(keywords) > 0, "Should extract some keywords")
				}
			}
		})
	}
}

// TestToGenerationResponse tests response conversion
func (suite *GenerationServiceTestSuite) TestToGenerationResponse() {
	userID := uuid.New()
	now := time.Now()
	
	generation := &ent.Generation{
		ID:              uuid.New(),
		UserID:          userID,
		Topic:           "Test topic",
		ContentType:     generation.ContentTypeEducational,
		Tone:            generation.ToneProfessional,
		Length:          generation.LengthMedium,
		Model:           "gpt-4",
		Content:         "Generated content",
		CreditsUsed:     3,
		GenerationTime:  1.5,
		ModelUsed:       "gpt-4",
		Status:          generation.StatusCompleted,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	response := ToGenerationResponse(generation)

	suite.NotNil(response)
	suite.Equal(generation.ID.String(), response.ID)
	suite.Equal(userID.String(), response.UserID)
	suite.Equal("Test topic", response.Topic)
	suite.Equal("educational", response.ContentType)
	suite.Equal("professional", response.Tone)
	suite.Equal("medium", response.Length)
	suite.Equal("gpt-4", response.Model)
	suite.Equal("Generated content", response.Content)
	suite.Equal(3, response.CreditsUsed)
	suite.Equal(1.5, response.GenerationTime)
	suite.Equal("gpt-4", response.ModelUsed)
	suite.Equal("completed", response.Status)
}

// TestToGenerationResponse_Nil tests nil handling
func (suite *GenerationServiceTestSuite) TestToGenerationResponse_Nil() {
	response := ToGenerationResponse(nil)
	suite.Nil(response)
}

// Helper methods
func (suite *GenerationServiceTestSuite) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// TestSuite runner
func TestGenerationServiceSuite(t *testing.T) {
	suite.Run(t, new(GenerationServiceTestSuite))
}

// Individual test functions for go test compatibility

func TestGenerateContentRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		req     GenerateContentRequest
		wantErr bool
	}{
		{
			name: "valid request",
			req: GenerateContentRequest{
				UserID:      uuid.New().String(),
				Topic:       "Test topic",
				ContentType: "educational",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			wantErr: false,
		},
		{
			name: "empty topic",
			req: GenerateContentRequest{
				UserID:      uuid.New().String(),
				Topic:       "",
				ContentType: "educational",
				Tone:        "professional",
				Length:      "medium",
				Model:       "gpt-4",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation
			if tt.req.Topic == "" {
				assert.True(t, tt.wantErr)
			}
			if tt.req.UserID != "" {
				_, err := uuid.Parse(tt.req.UserID)
				if err != nil {
					assert.True(t, tt.wantErr)
				}
			}
		})
	}
}

func TestCalculateCreditsUsed(t *testing.T) {
	s := &service{}

	tests := []struct {
		model         string
		contentLength int
		expectedMin   int
	}{
		{"gpt-4", 100, 3},
		{"gpt-3.5-turbo", 100, 1},
		{"claude-3", 100, 3},
		{"unknown", 100, 1},
	}

	for _, tt := range tests {
		t.Run(tt.model, func(t *testing.T) {
			credits := s.calculateCreditsUsed(tt.model, tt.contentLength)
			assert.GreaterOrEqual(t, credits, tt.expectedMin)
		})
	}
}

func TestExtractKeywords(t *testing.T) {
	s := &service{}

	tests := []struct {
		topic    string
		minWords int
	}{
		{"How to use social media for business", 2},
		{"AI and machine learning", 1},
		{"The best practices", 1},
	}

	for _, tt := range tests {
		t.Run(tt.topic, func(t *testing.T) {
			keywords := s.extractKeywords(tt.topic)
			assert.GreaterOrEqual(t, len(keywords), tt.minWords)
		})
	}
}

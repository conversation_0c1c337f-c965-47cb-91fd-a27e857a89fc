// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
)

// Asset is the model entity for the Asset schema.
type Asset struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who uploaded this asset
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Workspace this asset belongs to
	WorkspaceID string `json:"workspace_id,omitempty"`
	// Folder this asset belongs to
	FolderID *uuid.UUID `json:"folder_id,omitempty"`
	// Current filename
	FileName string `json:"file_name,omitempty"`
	// Original filename
	OriginalName string `json:"original_name,omitempty"`
	// MIME type of the file
	FileType string `json:"file_type,omitempty"`
	// File extension
	FileExtension string `json:"file_extension,omitempty"`
	// S3 storage key/path
	S3Key string `json:"s3_key,omitempty"`
	// S3 bucket name
	S3Bucket string `json:"s3_bucket,omitempty"`
	// Content hash for deduplication
	ContentHash string `json:"content_hash,omitempty"`
	// Purpose of the asset
	Purpose string `json:"purpose,omitempty"`
	// File size in bytes
	FileSize int64 `json:"file_size,omitempty"`
	// Thumbnail URL for images/videos
	ThumbnailURL string `json:"thumbnail_url,omitempty"`
	// File metadata (dimensions, duration, etc.)
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// Asset status
	Status string `json:"status,omitempty"`
	// Whether the asset is publicly accessible
	IsPublic bool `json:"is_public,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AssetQuery when eager-loading is set.
	Edges        AssetEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AssetEdges holds the relations/edges for other nodes in the graph.
type AssetEdges struct {
	// Folder holds the value of the folder edge.
	Folder *Folder `json:"folder,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// FolderOrErr returns the Folder value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e AssetEdges) FolderOrErr() (*Folder, error) {
	if e.Folder != nil {
		return e.Folder, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: folder.Label}
	}
	return nil, &NotLoadedError{edge: "folder"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Asset) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case asset.FieldFolderID:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case asset.FieldMetadata:
			values[i] = new([]byte)
		case asset.FieldIsPublic:
			values[i] = new(sql.NullBool)
		case asset.FieldFileSize:
			values[i] = new(sql.NullInt64)
		case asset.FieldWorkspaceID, asset.FieldFileName, asset.FieldOriginalName, asset.FieldFileType, asset.FieldFileExtension, asset.FieldS3Key, asset.FieldS3Bucket, asset.FieldContentHash, asset.FieldPurpose, asset.FieldThumbnailURL, asset.FieldStatus:
			values[i] = new(sql.NullString)
		case asset.FieldCreatedAt, asset.FieldUpdatedAt, asset.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case asset.FieldID, asset.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Asset fields.
func (a *Asset) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case asset.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				a.ID = *value
			}
		case asset.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				a.UserID = *value
			}
		case asset.FieldWorkspaceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field workspace_id", values[i])
			} else if value.Valid {
				a.WorkspaceID = value.String
			}
		case asset.FieldFolderID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field folder_id", values[i])
			} else if value.Valid {
				a.FolderID = new(uuid.UUID)
				*a.FolderID = *value.S.(*uuid.UUID)
			}
		case asset.FieldFileName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_name", values[i])
			} else if value.Valid {
				a.FileName = value.String
			}
		case asset.FieldOriginalName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field original_name", values[i])
			} else if value.Valid {
				a.OriginalName = value.String
			}
		case asset.FieldFileType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_type", values[i])
			} else if value.Valid {
				a.FileType = value.String
			}
		case asset.FieldFileExtension:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_extension", values[i])
			} else if value.Valid {
				a.FileExtension = value.String
			}
		case asset.FieldS3Key:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field s3_key", values[i])
			} else if value.Valid {
				a.S3Key = value.String
			}
		case asset.FieldS3Bucket:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field s3_bucket", values[i])
			} else if value.Valid {
				a.S3Bucket = value.String
			}
		case asset.FieldContentHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content_hash", values[i])
			} else if value.Valid {
				a.ContentHash = value.String
			}
		case asset.FieldPurpose:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field purpose", values[i])
			} else if value.Valid {
				a.Purpose = value.String
			}
		case asset.FieldFileSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_size", values[i])
			} else if value.Valid {
				a.FileSize = value.Int64
			}
		case asset.FieldThumbnailURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field thumbnail_url", values[i])
			} else if value.Valid {
				a.ThumbnailURL = value.String
			}
		case asset.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &a.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case asset.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				a.Status = value.String
			}
		case asset.FieldIsPublic:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_public", values[i])
			} else if value.Valid {
				a.IsPublic = value.Bool
			}
		case asset.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				a.CreatedAt = value.Time
			}
		case asset.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				a.UpdatedAt = value.Time
			}
		case asset.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				a.DeletedAt = new(time.Time)
				*a.DeletedAt = value.Time
			}
		default:
			a.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Asset.
// This includes values selected through modifiers, order, etc.
func (a *Asset) Value(name string) (ent.Value, error) {
	return a.selectValues.Get(name)
}

// QueryFolder queries the "folder" edge of the Asset entity.
func (a *Asset) QueryFolder() *FolderQuery {
	return NewAssetClient(a.config).QueryFolder(a)
}

// Update returns a builder for updating this Asset.
// Note that you need to call Asset.Unwrap() before calling this method if this Asset
// was returned from a transaction, and the transaction was committed or rolled back.
func (a *Asset) Update() *AssetUpdateOne {
	return NewAssetClient(a.config).UpdateOne(a)
}

// Unwrap unwraps the Asset entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (a *Asset) Unwrap() *Asset {
	_tx, ok := a.config.driver.(*txDriver)
	if !ok {
		panic("ent: Asset is not a transactional entity")
	}
	a.config.driver = _tx.drv
	return a
}

// String implements the fmt.Stringer.
func (a *Asset) String() string {
	var builder strings.Builder
	builder.WriteString("Asset(")
	builder.WriteString(fmt.Sprintf("id=%v, ", a.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", a.UserID))
	builder.WriteString(", ")
	builder.WriteString("workspace_id=")
	builder.WriteString(a.WorkspaceID)
	builder.WriteString(", ")
	if v := a.FolderID; v != nil {
		builder.WriteString("folder_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("file_name=")
	builder.WriteString(a.FileName)
	builder.WriteString(", ")
	builder.WriteString("original_name=")
	builder.WriteString(a.OriginalName)
	builder.WriteString(", ")
	builder.WriteString("file_type=")
	builder.WriteString(a.FileType)
	builder.WriteString(", ")
	builder.WriteString("file_extension=")
	builder.WriteString(a.FileExtension)
	builder.WriteString(", ")
	builder.WriteString("s3_key=")
	builder.WriteString(a.S3Key)
	builder.WriteString(", ")
	builder.WriteString("s3_bucket=")
	builder.WriteString(a.S3Bucket)
	builder.WriteString(", ")
	builder.WriteString("content_hash=")
	builder.WriteString(a.ContentHash)
	builder.WriteString(", ")
	builder.WriteString("purpose=")
	builder.WriteString(a.Purpose)
	builder.WriteString(", ")
	builder.WriteString("file_size=")
	builder.WriteString(fmt.Sprintf("%v", a.FileSize))
	builder.WriteString(", ")
	builder.WriteString("thumbnail_url=")
	builder.WriteString(a.ThumbnailURL)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", a.Metadata))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(a.Status)
	builder.WriteString(", ")
	builder.WriteString("is_public=")
	builder.WriteString(fmt.Sprintf("%v", a.IsPublic))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(a.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(a.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := a.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Assets is a parsable slice of Asset.
type Assets []*Asset

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/predicate"
)

// PlatformIntegrationUpdate is the builder for updating PlatformIntegration entities.
type PlatformIntegrationUpdate struct {
	config
	hooks    []Hook
	mutation *PlatformIntegrationMutation
}

// Where appends a list predicates to the PlatformIntegrationUpdate builder.
func (piu *PlatformIntegrationUpdate) Where(ps ...predicate.PlatformIntegration) *PlatformIntegrationUpdate {
	piu.mutation.Where(ps...)
	return piu
}

// SetUserID sets the "user_id" field.
func (piu *PlatformIntegrationUpdate) SetUserID(u uuid.UUID) *PlatformIntegrationUpdate {
	piu.mutation.SetUserID(u)
	return piu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableUserID(u *uuid.UUID) *PlatformIntegrationUpdate {
	if u != nil {
		piu.SetUserID(*u)
	}
	return piu
}

// SetWorkspaceID sets the "workspace_id" field.
func (piu *PlatformIntegrationUpdate) SetWorkspaceID(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetWorkspaceID(s)
	return piu
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableWorkspaceID(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetWorkspaceID(*s)
	}
	return piu
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (piu *PlatformIntegrationUpdate) ClearWorkspaceID() *PlatformIntegrationUpdate {
	piu.mutation.ClearWorkspaceID()
	return piu
}

// SetPlatform sets the "platform" field.
func (piu *PlatformIntegrationUpdate) SetPlatform(pl platformintegration.Platform) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatform(pl)
	return piu
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillablePlatform(pl *platformintegration.Platform) *PlatformIntegrationUpdate {
	if pl != nil {
		piu.SetPlatform(*pl)
	}
	return piu
}

// SetPlatformUserID sets the "platform_user_id" field.
func (piu *PlatformIntegrationUpdate) SetPlatformUserID(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatformUserID(s)
	return piu
}

// SetNillablePlatformUserID sets the "platform_user_id" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillablePlatformUserID(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetPlatformUserID(*s)
	}
	return piu
}

// SetPlatformUsername sets the "platform_username" field.
func (piu *PlatformIntegrationUpdate) SetPlatformUsername(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatformUsername(s)
	return piu
}

// SetNillablePlatformUsername sets the "platform_username" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillablePlatformUsername(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetPlatformUsername(*s)
	}
	return piu
}

// ClearPlatformUsername clears the value of the "platform_username" field.
func (piu *PlatformIntegrationUpdate) ClearPlatformUsername() *PlatformIntegrationUpdate {
	piu.mutation.ClearPlatformUsername()
	return piu
}

// SetPlatformDisplayName sets the "platform_display_name" field.
func (piu *PlatformIntegrationUpdate) SetPlatformDisplayName(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatformDisplayName(s)
	return piu
}

// SetNillablePlatformDisplayName sets the "platform_display_name" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillablePlatformDisplayName(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetPlatformDisplayName(*s)
	}
	return piu
}

// ClearPlatformDisplayName clears the value of the "platform_display_name" field.
func (piu *PlatformIntegrationUpdate) ClearPlatformDisplayName() *PlatformIntegrationUpdate {
	piu.mutation.ClearPlatformDisplayName()
	return piu
}

// SetPlatformAvatarURL sets the "platform_avatar_url" field.
func (piu *PlatformIntegrationUpdate) SetPlatformAvatarURL(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatformAvatarURL(s)
	return piu
}

// SetNillablePlatformAvatarURL sets the "platform_avatar_url" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillablePlatformAvatarURL(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetPlatformAvatarURL(*s)
	}
	return piu
}

// ClearPlatformAvatarURL clears the value of the "platform_avatar_url" field.
func (piu *PlatformIntegrationUpdate) ClearPlatformAvatarURL() *PlatformIntegrationUpdate {
	piu.mutation.ClearPlatformAvatarURL()
	return piu
}

// SetAccessToken sets the "access_token" field.
func (piu *PlatformIntegrationUpdate) SetAccessToken(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetAccessToken(s)
	return piu
}

// SetNillableAccessToken sets the "access_token" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableAccessToken(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetAccessToken(*s)
	}
	return piu
}

// SetRefreshToken sets the "refresh_token" field.
func (piu *PlatformIntegrationUpdate) SetRefreshToken(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetRefreshToken(s)
	return piu
}

// SetNillableRefreshToken sets the "refresh_token" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableRefreshToken(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetRefreshToken(*s)
	}
	return piu
}

// ClearRefreshToken clears the value of the "refresh_token" field.
func (piu *PlatformIntegrationUpdate) ClearRefreshToken() *PlatformIntegrationUpdate {
	piu.mutation.ClearRefreshToken()
	return piu
}

// SetTokenExpiresAt sets the "token_expires_at" field.
func (piu *PlatformIntegrationUpdate) SetTokenExpiresAt(t time.Time) *PlatformIntegrationUpdate {
	piu.mutation.SetTokenExpiresAt(t)
	return piu
}

// SetNillableTokenExpiresAt sets the "token_expires_at" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableTokenExpiresAt(t *time.Time) *PlatformIntegrationUpdate {
	if t != nil {
		piu.SetTokenExpiresAt(*t)
	}
	return piu
}

// ClearTokenExpiresAt clears the value of the "token_expires_at" field.
func (piu *PlatformIntegrationUpdate) ClearTokenExpiresAt() *PlatformIntegrationUpdate {
	piu.mutation.ClearTokenExpiresAt()
	return piu
}

// SetScopes sets the "scopes" field.
func (piu *PlatformIntegrationUpdate) SetScopes(s []string) *PlatformIntegrationUpdate {
	piu.mutation.SetScopes(s)
	return piu
}

// AppendScopes appends s to the "scopes" field.
func (piu *PlatformIntegrationUpdate) AppendScopes(s []string) *PlatformIntegrationUpdate {
	piu.mutation.AppendScopes(s)
	return piu
}

// ClearScopes clears the value of the "scopes" field.
func (piu *PlatformIntegrationUpdate) ClearScopes() *PlatformIntegrationUpdate {
	piu.mutation.ClearScopes()
	return piu
}

// SetPlatformData sets the "platform_data" field.
func (piu *PlatformIntegrationUpdate) SetPlatformData(m map[string]interface{}) *PlatformIntegrationUpdate {
	piu.mutation.SetPlatformData(m)
	return piu
}

// ClearPlatformData clears the value of the "platform_data" field.
func (piu *PlatformIntegrationUpdate) ClearPlatformData() *PlatformIntegrationUpdate {
	piu.mutation.ClearPlatformData()
	return piu
}

// SetStatus sets the "status" field.
func (piu *PlatformIntegrationUpdate) SetStatus(pl platformintegration.Status) *PlatformIntegrationUpdate {
	piu.mutation.SetStatus(pl)
	return piu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableStatus(pl *platformintegration.Status) *PlatformIntegrationUpdate {
	if pl != nil {
		piu.SetStatus(*pl)
	}
	return piu
}

// SetErrorMessage sets the "error_message" field.
func (piu *PlatformIntegrationUpdate) SetErrorMessage(s string) *PlatformIntegrationUpdate {
	piu.mutation.SetErrorMessage(s)
	return piu
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableErrorMessage(s *string) *PlatformIntegrationUpdate {
	if s != nil {
		piu.SetErrorMessage(*s)
	}
	return piu
}

// ClearErrorMessage clears the value of the "error_message" field.
func (piu *PlatformIntegrationUpdate) ClearErrorMessage() *PlatformIntegrationUpdate {
	piu.mutation.ClearErrorMessage()
	return piu
}

// SetLastSyncAt sets the "last_sync_at" field.
func (piu *PlatformIntegrationUpdate) SetLastSyncAt(t time.Time) *PlatformIntegrationUpdate {
	piu.mutation.SetLastSyncAt(t)
	return piu
}

// SetNillableLastSyncAt sets the "last_sync_at" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableLastSyncAt(t *time.Time) *PlatformIntegrationUpdate {
	if t != nil {
		piu.SetLastSyncAt(*t)
	}
	return piu
}

// ClearLastSyncAt clears the value of the "last_sync_at" field.
func (piu *PlatformIntegrationUpdate) ClearLastSyncAt() *PlatformIntegrationUpdate {
	piu.mutation.ClearLastSyncAt()
	return piu
}

// SetLastPostAt sets the "last_post_at" field.
func (piu *PlatformIntegrationUpdate) SetLastPostAt(t time.Time) *PlatformIntegrationUpdate {
	piu.mutation.SetLastPostAt(t)
	return piu
}

// SetNillableLastPostAt sets the "last_post_at" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableLastPostAt(t *time.Time) *PlatformIntegrationUpdate {
	if t != nil {
		piu.SetLastPostAt(*t)
	}
	return piu
}

// ClearLastPostAt clears the value of the "last_post_at" field.
func (piu *PlatformIntegrationUpdate) ClearLastPostAt() *PlatformIntegrationUpdate {
	piu.mutation.ClearLastPostAt()
	return piu
}

// SetCapabilities sets the "capabilities" field.
func (piu *PlatformIntegrationUpdate) SetCapabilities(s []string) *PlatformIntegrationUpdate {
	piu.mutation.SetCapabilities(s)
	return piu
}

// AppendCapabilities appends s to the "capabilities" field.
func (piu *PlatformIntegrationUpdate) AppendCapabilities(s []string) *PlatformIntegrationUpdate {
	piu.mutation.AppendCapabilities(s)
	return piu
}

// ClearCapabilities clears the value of the "capabilities" field.
func (piu *PlatformIntegrationUpdate) ClearCapabilities() *PlatformIntegrationUpdate {
	piu.mutation.ClearCapabilities()
	return piu
}

// SetSettings sets the "settings" field.
func (piu *PlatformIntegrationUpdate) SetSettings(m map[string]interface{}) *PlatformIntegrationUpdate {
	piu.mutation.SetSettings(m)
	return piu
}

// ClearSettings clears the value of the "settings" field.
func (piu *PlatformIntegrationUpdate) ClearSettings() *PlatformIntegrationUpdate {
	piu.mutation.ClearSettings()
	return piu
}

// SetAutoPublish sets the "auto_publish" field.
func (piu *PlatformIntegrationUpdate) SetAutoPublish(b bool) *PlatformIntegrationUpdate {
	piu.mutation.SetAutoPublish(b)
	return piu
}

// SetNillableAutoPublish sets the "auto_publish" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableAutoPublish(b *bool) *PlatformIntegrationUpdate {
	if b != nil {
		piu.SetAutoPublish(*b)
	}
	return piu
}

// SetIsPrimary sets the "is_primary" field.
func (piu *PlatformIntegrationUpdate) SetIsPrimary(b bool) *PlatformIntegrationUpdate {
	piu.mutation.SetIsPrimary(b)
	return piu
}

// SetNillableIsPrimary sets the "is_primary" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableIsPrimary(b *bool) *PlatformIntegrationUpdate {
	if b != nil {
		piu.SetIsPrimary(*b)
	}
	return piu
}

// SetMetadata sets the "metadata" field.
func (piu *PlatformIntegrationUpdate) SetMetadata(m map[string]interface{}) *PlatformIntegrationUpdate {
	piu.mutation.SetMetadata(m)
	return piu
}

// ClearMetadata clears the value of the "metadata" field.
func (piu *PlatformIntegrationUpdate) ClearMetadata() *PlatformIntegrationUpdate {
	piu.mutation.ClearMetadata()
	return piu
}

// SetUpdatedAt sets the "updated_at" field.
func (piu *PlatformIntegrationUpdate) SetUpdatedAt(t time.Time) *PlatformIntegrationUpdate {
	piu.mutation.SetUpdatedAt(t)
	return piu
}

// SetDeletedAt sets the "deleted_at" field.
func (piu *PlatformIntegrationUpdate) SetDeletedAt(t time.Time) *PlatformIntegrationUpdate {
	piu.mutation.SetDeletedAt(t)
	return piu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (piu *PlatformIntegrationUpdate) SetNillableDeletedAt(t *time.Time) *PlatformIntegrationUpdate {
	if t != nil {
		piu.SetDeletedAt(*t)
	}
	return piu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (piu *PlatformIntegrationUpdate) ClearDeletedAt() *PlatformIntegrationUpdate {
	piu.mutation.ClearDeletedAt()
	return piu
}

// Mutation returns the PlatformIntegrationMutation object of the builder.
func (piu *PlatformIntegrationUpdate) Mutation() *PlatformIntegrationMutation {
	return piu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (piu *PlatformIntegrationUpdate) Save(ctx context.Context) (int, error) {
	piu.defaults()
	return withHooks(ctx, piu.sqlSave, piu.mutation, piu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (piu *PlatformIntegrationUpdate) SaveX(ctx context.Context) int {
	affected, err := piu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (piu *PlatformIntegrationUpdate) Exec(ctx context.Context) error {
	_, err := piu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (piu *PlatformIntegrationUpdate) ExecX(ctx context.Context) {
	if err := piu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (piu *PlatformIntegrationUpdate) defaults() {
	if _, ok := piu.mutation.UpdatedAt(); !ok {
		v := platformintegration.UpdateDefaultUpdatedAt()
		piu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (piu *PlatformIntegrationUpdate) check() error {
	if v, ok := piu.mutation.WorkspaceID(); ok {
		if err := platformintegration.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.workspace_id": %w`, err)}
		}
	}
	if v, ok := piu.mutation.Platform(); ok {
		if err := platformintegration.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform": %w`, err)}
		}
	}
	if v, ok := piu.mutation.PlatformUserID(); ok {
		if err := platformintegration.PlatformUserIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_user_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_user_id": %w`, err)}
		}
	}
	if v, ok := piu.mutation.PlatformUsername(); ok {
		if err := platformintegration.PlatformUsernameValidator(v); err != nil {
			return &ValidationError{Name: "platform_username", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_username": %w`, err)}
		}
	}
	if v, ok := piu.mutation.PlatformDisplayName(); ok {
		if err := platformintegration.PlatformDisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "platform_display_name", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_display_name": %w`, err)}
		}
	}
	if v, ok := piu.mutation.PlatformAvatarURL(); ok {
		if err := platformintegration.PlatformAvatarURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_avatar_url", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_avatar_url": %w`, err)}
		}
	}
	if v, ok := piu.mutation.Status(); ok {
		if err := platformintegration.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.status": %w`, err)}
		}
	}
	if v, ok := piu.mutation.ErrorMessage(); ok {
		if err := platformintegration.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.error_message": %w`, err)}
		}
	}
	return nil
}

func (piu *PlatformIntegrationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := piu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(platformintegration.Table, platformintegration.Columns, sqlgraph.NewFieldSpec(platformintegration.FieldID, field.TypeUUID))
	if ps := piu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := piu.mutation.UserID(); ok {
		_spec.SetField(platformintegration.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := piu.mutation.WorkspaceID(); ok {
		_spec.SetField(platformintegration.FieldWorkspaceID, field.TypeString, value)
	}
	if piu.mutation.WorkspaceIDCleared() {
		_spec.ClearField(platformintegration.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := piu.mutation.Platform(); ok {
		_spec.SetField(platformintegration.FieldPlatform, field.TypeEnum, value)
	}
	if value, ok := piu.mutation.PlatformUserID(); ok {
		_spec.SetField(platformintegration.FieldPlatformUserID, field.TypeString, value)
	}
	if value, ok := piu.mutation.PlatformUsername(); ok {
		_spec.SetField(platformintegration.FieldPlatformUsername, field.TypeString, value)
	}
	if piu.mutation.PlatformUsernameCleared() {
		_spec.ClearField(platformintegration.FieldPlatformUsername, field.TypeString)
	}
	if value, ok := piu.mutation.PlatformDisplayName(); ok {
		_spec.SetField(platformintegration.FieldPlatformDisplayName, field.TypeString, value)
	}
	if piu.mutation.PlatformDisplayNameCleared() {
		_spec.ClearField(platformintegration.FieldPlatformDisplayName, field.TypeString)
	}
	if value, ok := piu.mutation.PlatformAvatarURL(); ok {
		_spec.SetField(platformintegration.FieldPlatformAvatarURL, field.TypeString, value)
	}
	if piu.mutation.PlatformAvatarURLCleared() {
		_spec.ClearField(platformintegration.FieldPlatformAvatarURL, field.TypeString)
	}
	if value, ok := piu.mutation.AccessToken(); ok {
		_spec.SetField(platformintegration.FieldAccessToken, field.TypeString, value)
	}
	if value, ok := piu.mutation.RefreshToken(); ok {
		_spec.SetField(platformintegration.FieldRefreshToken, field.TypeString, value)
	}
	if piu.mutation.RefreshTokenCleared() {
		_spec.ClearField(platformintegration.FieldRefreshToken, field.TypeString)
	}
	if value, ok := piu.mutation.TokenExpiresAt(); ok {
		_spec.SetField(platformintegration.FieldTokenExpiresAt, field.TypeTime, value)
	}
	if piu.mutation.TokenExpiresAtCleared() {
		_spec.ClearField(platformintegration.FieldTokenExpiresAt, field.TypeTime)
	}
	if value, ok := piu.mutation.Scopes(); ok {
		_spec.SetField(platformintegration.FieldScopes, field.TypeJSON, value)
	}
	if value, ok := piu.mutation.AppendedScopes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, platformintegration.FieldScopes, value)
		})
	}
	if piu.mutation.ScopesCleared() {
		_spec.ClearField(platformintegration.FieldScopes, field.TypeJSON)
	}
	if value, ok := piu.mutation.PlatformData(); ok {
		_spec.SetField(platformintegration.FieldPlatformData, field.TypeJSON, value)
	}
	if piu.mutation.PlatformDataCleared() {
		_spec.ClearField(platformintegration.FieldPlatformData, field.TypeJSON)
	}
	if value, ok := piu.mutation.Status(); ok {
		_spec.SetField(platformintegration.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := piu.mutation.ErrorMessage(); ok {
		_spec.SetField(platformintegration.FieldErrorMessage, field.TypeString, value)
	}
	if piu.mutation.ErrorMessageCleared() {
		_spec.ClearField(platformintegration.FieldErrorMessage, field.TypeString)
	}
	if value, ok := piu.mutation.LastSyncAt(); ok {
		_spec.SetField(platformintegration.FieldLastSyncAt, field.TypeTime, value)
	}
	if piu.mutation.LastSyncAtCleared() {
		_spec.ClearField(platformintegration.FieldLastSyncAt, field.TypeTime)
	}
	if value, ok := piu.mutation.LastPostAt(); ok {
		_spec.SetField(platformintegration.FieldLastPostAt, field.TypeTime, value)
	}
	if piu.mutation.LastPostAtCleared() {
		_spec.ClearField(platformintegration.FieldLastPostAt, field.TypeTime)
	}
	if value, ok := piu.mutation.Capabilities(); ok {
		_spec.SetField(platformintegration.FieldCapabilities, field.TypeJSON, value)
	}
	if value, ok := piu.mutation.AppendedCapabilities(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, platformintegration.FieldCapabilities, value)
		})
	}
	if piu.mutation.CapabilitiesCleared() {
		_spec.ClearField(platformintegration.FieldCapabilities, field.TypeJSON)
	}
	if value, ok := piu.mutation.Settings(); ok {
		_spec.SetField(platformintegration.FieldSettings, field.TypeJSON, value)
	}
	if piu.mutation.SettingsCleared() {
		_spec.ClearField(platformintegration.FieldSettings, field.TypeJSON)
	}
	if value, ok := piu.mutation.AutoPublish(); ok {
		_spec.SetField(platformintegration.FieldAutoPublish, field.TypeBool, value)
	}
	if value, ok := piu.mutation.IsPrimary(); ok {
		_spec.SetField(platformintegration.FieldIsPrimary, field.TypeBool, value)
	}
	if value, ok := piu.mutation.Metadata(); ok {
		_spec.SetField(platformintegration.FieldMetadata, field.TypeJSON, value)
	}
	if piu.mutation.MetadataCleared() {
		_spec.ClearField(platformintegration.FieldMetadata, field.TypeJSON)
	}
	if value, ok := piu.mutation.UpdatedAt(); ok {
		_spec.SetField(platformintegration.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := piu.mutation.DeletedAt(); ok {
		_spec.SetField(platformintegration.FieldDeletedAt, field.TypeTime, value)
	}
	if piu.mutation.DeletedAtCleared() {
		_spec.ClearField(platformintegration.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, piu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{platformintegration.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	piu.mutation.done = true
	return n, nil
}

// PlatformIntegrationUpdateOne is the builder for updating a single PlatformIntegration entity.
type PlatformIntegrationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PlatformIntegrationMutation
}

// SetUserID sets the "user_id" field.
func (piuo *PlatformIntegrationUpdateOne) SetUserID(u uuid.UUID) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetUserID(u)
	return piuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableUserID(u *uuid.UUID) *PlatformIntegrationUpdateOne {
	if u != nil {
		piuo.SetUserID(*u)
	}
	return piuo
}

// SetWorkspaceID sets the "workspace_id" field.
func (piuo *PlatformIntegrationUpdateOne) SetWorkspaceID(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetWorkspaceID(s)
	return piuo
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableWorkspaceID(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetWorkspaceID(*s)
	}
	return piuo
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (piuo *PlatformIntegrationUpdateOne) ClearWorkspaceID() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearWorkspaceID()
	return piuo
}

// SetPlatform sets the "platform" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatform(pl platformintegration.Platform) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatform(pl)
	return piuo
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillablePlatform(pl *platformintegration.Platform) *PlatformIntegrationUpdateOne {
	if pl != nil {
		piuo.SetPlatform(*pl)
	}
	return piuo
}

// SetPlatformUserID sets the "platform_user_id" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatformUserID(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatformUserID(s)
	return piuo
}

// SetNillablePlatformUserID sets the "platform_user_id" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillablePlatformUserID(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetPlatformUserID(*s)
	}
	return piuo
}

// SetPlatformUsername sets the "platform_username" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatformUsername(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatformUsername(s)
	return piuo
}

// SetNillablePlatformUsername sets the "platform_username" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillablePlatformUsername(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetPlatformUsername(*s)
	}
	return piuo
}

// ClearPlatformUsername clears the value of the "platform_username" field.
func (piuo *PlatformIntegrationUpdateOne) ClearPlatformUsername() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearPlatformUsername()
	return piuo
}

// SetPlatformDisplayName sets the "platform_display_name" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatformDisplayName(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatformDisplayName(s)
	return piuo
}

// SetNillablePlatformDisplayName sets the "platform_display_name" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillablePlatformDisplayName(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetPlatformDisplayName(*s)
	}
	return piuo
}

// ClearPlatformDisplayName clears the value of the "platform_display_name" field.
func (piuo *PlatformIntegrationUpdateOne) ClearPlatformDisplayName() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearPlatformDisplayName()
	return piuo
}

// SetPlatformAvatarURL sets the "platform_avatar_url" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatformAvatarURL(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatformAvatarURL(s)
	return piuo
}

// SetNillablePlatformAvatarURL sets the "platform_avatar_url" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillablePlatformAvatarURL(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetPlatformAvatarURL(*s)
	}
	return piuo
}

// ClearPlatformAvatarURL clears the value of the "platform_avatar_url" field.
func (piuo *PlatformIntegrationUpdateOne) ClearPlatformAvatarURL() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearPlatformAvatarURL()
	return piuo
}

// SetAccessToken sets the "access_token" field.
func (piuo *PlatformIntegrationUpdateOne) SetAccessToken(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetAccessToken(s)
	return piuo
}

// SetNillableAccessToken sets the "access_token" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableAccessToken(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetAccessToken(*s)
	}
	return piuo
}

// SetRefreshToken sets the "refresh_token" field.
func (piuo *PlatformIntegrationUpdateOne) SetRefreshToken(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetRefreshToken(s)
	return piuo
}

// SetNillableRefreshToken sets the "refresh_token" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableRefreshToken(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetRefreshToken(*s)
	}
	return piuo
}

// ClearRefreshToken clears the value of the "refresh_token" field.
func (piuo *PlatformIntegrationUpdateOne) ClearRefreshToken() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearRefreshToken()
	return piuo
}

// SetTokenExpiresAt sets the "token_expires_at" field.
func (piuo *PlatformIntegrationUpdateOne) SetTokenExpiresAt(t time.Time) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetTokenExpiresAt(t)
	return piuo
}

// SetNillableTokenExpiresAt sets the "token_expires_at" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableTokenExpiresAt(t *time.Time) *PlatformIntegrationUpdateOne {
	if t != nil {
		piuo.SetTokenExpiresAt(*t)
	}
	return piuo
}

// ClearTokenExpiresAt clears the value of the "token_expires_at" field.
func (piuo *PlatformIntegrationUpdateOne) ClearTokenExpiresAt() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearTokenExpiresAt()
	return piuo
}

// SetScopes sets the "scopes" field.
func (piuo *PlatformIntegrationUpdateOne) SetScopes(s []string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetScopes(s)
	return piuo
}

// AppendScopes appends s to the "scopes" field.
func (piuo *PlatformIntegrationUpdateOne) AppendScopes(s []string) *PlatformIntegrationUpdateOne {
	piuo.mutation.AppendScopes(s)
	return piuo
}

// ClearScopes clears the value of the "scopes" field.
func (piuo *PlatformIntegrationUpdateOne) ClearScopes() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearScopes()
	return piuo
}

// SetPlatformData sets the "platform_data" field.
func (piuo *PlatformIntegrationUpdateOne) SetPlatformData(m map[string]interface{}) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetPlatformData(m)
	return piuo
}

// ClearPlatformData clears the value of the "platform_data" field.
func (piuo *PlatformIntegrationUpdateOne) ClearPlatformData() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearPlatformData()
	return piuo
}

// SetStatus sets the "status" field.
func (piuo *PlatformIntegrationUpdateOne) SetStatus(pl platformintegration.Status) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetStatus(pl)
	return piuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableStatus(pl *platformintegration.Status) *PlatformIntegrationUpdateOne {
	if pl != nil {
		piuo.SetStatus(*pl)
	}
	return piuo
}

// SetErrorMessage sets the "error_message" field.
func (piuo *PlatformIntegrationUpdateOne) SetErrorMessage(s string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetErrorMessage(s)
	return piuo
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableErrorMessage(s *string) *PlatformIntegrationUpdateOne {
	if s != nil {
		piuo.SetErrorMessage(*s)
	}
	return piuo
}

// ClearErrorMessage clears the value of the "error_message" field.
func (piuo *PlatformIntegrationUpdateOne) ClearErrorMessage() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearErrorMessage()
	return piuo
}

// SetLastSyncAt sets the "last_sync_at" field.
func (piuo *PlatformIntegrationUpdateOne) SetLastSyncAt(t time.Time) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetLastSyncAt(t)
	return piuo
}

// SetNillableLastSyncAt sets the "last_sync_at" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableLastSyncAt(t *time.Time) *PlatformIntegrationUpdateOne {
	if t != nil {
		piuo.SetLastSyncAt(*t)
	}
	return piuo
}

// ClearLastSyncAt clears the value of the "last_sync_at" field.
func (piuo *PlatformIntegrationUpdateOne) ClearLastSyncAt() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearLastSyncAt()
	return piuo
}

// SetLastPostAt sets the "last_post_at" field.
func (piuo *PlatformIntegrationUpdateOne) SetLastPostAt(t time.Time) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetLastPostAt(t)
	return piuo
}

// SetNillableLastPostAt sets the "last_post_at" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableLastPostAt(t *time.Time) *PlatformIntegrationUpdateOne {
	if t != nil {
		piuo.SetLastPostAt(*t)
	}
	return piuo
}

// ClearLastPostAt clears the value of the "last_post_at" field.
func (piuo *PlatformIntegrationUpdateOne) ClearLastPostAt() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearLastPostAt()
	return piuo
}

// SetCapabilities sets the "capabilities" field.
func (piuo *PlatformIntegrationUpdateOne) SetCapabilities(s []string) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetCapabilities(s)
	return piuo
}

// AppendCapabilities appends s to the "capabilities" field.
func (piuo *PlatformIntegrationUpdateOne) AppendCapabilities(s []string) *PlatformIntegrationUpdateOne {
	piuo.mutation.AppendCapabilities(s)
	return piuo
}

// ClearCapabilities clears the value of the "capabilities" field.
func (piuo *PlatformIntegrationUpdateOne) ClearCapabilities() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearCapabilities()
	return piuo
}

// SetSettings sets the "settings" field.
func (piuo *PlatformIntegrationUpdateOne) SetSettings(m map[string]interface{}) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetSettings(m)
	return piuo
}

// ClearSettings clears the value of the "settings" field.
func (piuo *PlatformIntegrationUpdateOne) ClearSettings() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearSettings()
	return piuo
}

// SetAutoPublish sets the "auto_publish" field.
func (piuo *PlatformIntegrationUpdateOne) SetAutoPublish(b bool) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetAutoPublish(b)
	return piuo
}

// SetNillableAutoPublish sets the "auto_publish" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableAutoPublish(b *bool) *PlatformIntegrationUpdateOne {
	if b != nil {
		piuo.SetAutoPublish(*b)
	}
	return piuo
}

// SetIsPrimary sets the "is_primary" field.
func (piuo *PlatformIntegrationUpdateOne) SetIsPrimary(b bool) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetIsPrimary(b)
	return piuo
}

// SetNillableIsPrimary sets the "is_primary" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableIsPrimary(b *bool) *PlatformIntegrationUpdateOne {
	if b != nil {
		piuo.SetIsPrimary(*b)
	}
	return piuo
}

// SetMetadata sets the "metadata" field.
func (piuo *PlatformIntegrationUpdateOne) SetMetadata(m map[string]interface{}) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetMetadata(m)
	return piuo
}

// ClearMetadata clears the value of the "metadata" field.
func (piuo *PlatformIntegrationUpdateOne) ClearMetadata() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearMetadata()
	return piuo
}

// SetUpdatedAt sets the "updated_at" field.
func (piuo *PlatformIntegrationUpdateOne) SetUpdatedAt(t time.Time) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetUpdatedAt(t)
	return piuo
}

// SetDeletedAt sets the "deleted_at" field.
func (piuo *PlatformIntegrationUpdateOne) SetDeletedAt(t time.Time) *PlatformIntegrationUpdateOne {
	piuo.mutation.SetDeletedAt(t)
	return piuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (piuo *PlatformIntegrationUpdateOne) SetNillableDeletedAt(t *time.Time) *PlatformIntegrationUpdateOne {
	if t != nil {
		piuo.SetDeletedAt(*t)
	}
	return piuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (piuo *PlatformIntegrationUpdateOne) ClearDeletedAt() *PlatformIntegrationUpdateOne {
	piuo.mutation.ClearDeletedAt()
	return piuo
}

// Mutation returns the PlatformIntegrationMutation object of the builder.
func (piuo *PlatformIntegrationUpdateOne) Mutation() *PlatformIntegrationMutation {
	return piuo.mutation
}

// Where appends a list predicates to the PlatformIntegrationUpdate builder.
func (piuo *PlatformIntegrationUpdateOne) Where(ps ...predicate.PlatformIntegration) *PlatformIntegrationUpdateOne {
	piuo.mutation.Where(ps...)
	return piuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (piuo *PlatformIntegrationUpdateOne) Select(field string, fields ...string) *PlatformIntegrationUpdateOne {
	piuo.fields = append([]string{field}, fields...)
	return piuo
}

// Save executes the query and returns the updated PlatformIntegration entity.
func (piuo *PlatformIntegrationUpdateOne) Save(ctx context.Context) (*PlatformIntegration, error) {
	piuo.defaults()
	return withHooks(ctx, piuo.sqlSave, piuo.mutation, piuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (piuo *PlatformIntegrationUpdateOne) SaveX(ctx context.Context) *PlatformIntegration {
	node, err := piuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (piuo *PlatformIntegrationUpdateOne) Exec(ctx context.Context) error {
	_, err := piuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (piuo *PlatformIntegrationUpdateOne) ExecX(ctx context.Context) {
	if err := piuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (piuo *PlatformIntegrationUpdateOne) defaults() {
	if _, ok := piuo.mutation.UpdatedAt(); !ok {
		v := platformintegration.UpdateDefaultUpdatedAt()
		piuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (piuo *PlatformIntegrationUpdateOne) check() error {
	if v, ok := piuo.mutation.WorkspaceID(); ok {
		if err := platformintegration.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.workspace_id": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.Platform(); ok {
		if err := platformintegration.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.PlatformUserID(); ok {
		if err := platformintegration.PlatformUserIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_user_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_user_id": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.PlatformUsername(); ok {
		if err := platformintegration.PlatformUsernameValidator(v); err != nil {
			return &ValidationError{Name: "platform_username", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_username": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.PlatformDisplayName(); ok {
		if err := platformintegration.PlatformDisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "platform_display_name", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_display_name": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.PlatformAvatarURL(); ok {
		if err := platformintegration.PlatformAvatarURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_avatar_url", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_avatar_url": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.Status(); ok {
		if err := platformintegration.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.status": %w`, err)}
		}
	}
	if v, ok := piuo.mutation.ErrorMessage(); ok {
		if err := platformintegration.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.error_message": %w`, err)}
		}
	}
	return nil
}

func (piuo *PlatformIntegrationUpdateOne) sqlSave(ctx context.Context) (_node *PlatformIntegration, err error) {
	if err := piuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(platformintegration.Table, platformintegration.Columns, sqlgraph.NewFieldSpec(platformintegration.FieldID, field.TypeUUID))
	id, ok := piuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "PlatformIntegration.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := piuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, platformintegration.FieldID)
		for _, f := range fields {
			if !platformintegration.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != platformintegration.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := piuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := piuo.mutation.UserID(); ok {
		_spec.SetField(platformintegration.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := piuo.mutation.WorkspaceID(); ok {
		_spec.SetField(platformintegration.FieldWorkspaceID, field.TypeString, value)
	}
	if piuo.mutation.WorkspaceIDCleared() {
		_spec.ClearField(platformintegration.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := piuo.mutation.Platform(); ok {
		_spec.SetField(platformintegration.FieldPlatform, field.TypeEnum, value)
	}
	if value, ok := piuo.mutation.PlatformUserID(); ok {
		_spec.SetField(platformintegration.FieldPlatformUserID, field.TypeString, value)
	}
	if value, ok := piuo.mutation.PlatformUsername(); ok {
		_spec.SetField(platformintegration.FieldPlatformUsername, field.TypeString, value)
	}
	if piuo.mutation.PlatformUsernameCleared() {
		_spec.ClearField(platformintegration.FieldPlatformUsername, field.TypeString)
	}
	if value, ok := piuo.mutation.PlatformDisplayName(); ok {
		_spec.SetField(platformintegration.FieldPlatformDisplayName, field.TypeString, value)
	}
	if piuo.mutation.PlatformDisplayNameCleared() {
		_spec.ClearField(platformintegration.FieldPlatformDisplayName, field.TypeString)
	}
	if value, ok := piuo.mutation.PlatformAvatarURL(); ok {
		_spec.SetField(platformintegration.FieldPlatformAvatarURL, field.TypeString, value)
	}
	if piuo.mutation.PlatformAvatarURLCleared() {
		_spec.ClearField(platformintegration.FieldPlatformAvatarURL, field.TypeString)
	}
	if value, ok := piuo.mutation.AccessToken(); ok {
		_spec.SetField(platformintegration.FieldAccessToken, field.TypeString, value)
	}
	if value, ok := piuo.mutation.RefreshToken(); ok {
		_spec.SetField(platformintegration.FieldRefreshToken, field.TypeString, value)
	}
	if piuo.mutation.RefreshTokenCleared() {
		_spec.ClearField(platformintegration.FieldRefreshToken, field.TypeString)
	}
	if value, ok := piuo.mutation.TokenExpiresAt(); ok {
		_spec.SetField(platformintegration.FieldTokenExpiresAt, field.TypeTime, value)
	}
	if piuo.mutation.TokenExpiresAtCleared() {
		_spec.ClearField(platformintegration.FieldTokenExpiresAt, field.TypeTime)
	}
	if value, ok := piuo.mutation.Scopes(); ok {
		_spec.SetField(platformintegration.FieldScopes, field.TypeJSON, value)
	}
	if value, ok := piuo.mutation.AppendedScopes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, platformintegration.FieldScopes, value)
		})
	}
	if piuo.mutation.ScopesCleared() {
		_spec.ClearField(platformintegration.FieldScopes, field.TypeJSON)
	}
	if value, ok := piuo.mutation.PlatformData(); ok {
		_spec.SetField(platformintegration.FieldPlatformData, field.TypeJSON, value)
	}
	if piuo.mutation.PlatformDataCleared() {
		_spec.ClearField(platformintegration.FieldPlatformData, field.TypeJSON)
	}
	if value, ok := piuo.mutation.Status(); ok {
		_spec.SetField(platformintegration.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := piuo.mutation.ErrorMessage(); ok {
		_spec.SetField(platformintegration.FieldErrorMessage, field.TypeString, value)
	}
	if piuo.mutation.ErrorMessageCleared() {
		_spec.ClearField(platformintegration.FieldErrorMessage, field.TypeString)
	}
	if value, ok := piuo.mutation.LastSyncAt(); ok {
		_spec.SetField(platformintegration.FieldLastSyncAt, field.TypeTime, value)
	}
	if piuo.mutation.LastSyncAtCleared() {
		_spec.ClearField(platformintegration.FieldLastSyncAt, field.TypeTime)
	}
	if value, ok := piuo.mutation.LastPostAt(); ok {
		_spec.SetField(platformintegration.FieldLastPostAt, field.TypeTime, value)
	}
	if piuo.mutation.LastPostAtCleared() {
		_spec.ClearField(platformintegration.FieldLastPostAt, field.TypeTime)
	}
	if value, ok := piuo.mutation.Capabilities(); ok {
		_spec.SetField(platformintegration.FieldCapabilities, field.TypeJSON, value)
	}
	if value, ok := piuo.mutation.AppendedCapabilities(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, platformintegration.FieldCapabilities, value)
		})
	}
	if piuo.mutation.CapabilitiesCleared() {
		_spec.ClearField(platformintegration.FieldCapabilities, field.TypeJSON)
	}
	if value, ok := piuo.mutation.Settings(); ok {
		_spec.SetField(platformintegration.FieldSettings, field.TypeJSON, value)
	}
	if piuo.mutation.SettingsCleared() {
		_spec.ClearField(platformintegration.FieldSettings, field.TypeJSON)
	}
	if value, ok := piuo.mutation.AutoPublish(); ok {
		_spec.SetField(platformintegration.FieldAutoPublish, field.TypeBool, value)
	}
	if value, ok := piuo.mutation.IsPrimary(); ok {
		_spec.SetField(platformintegration.FieldIsPrimary, field.TypeBool, value)
	}
	if value, ok := piuo.mutation.Metadata(); ok {
		_spec.SetField(platformintegration.FieldMetadata, field.TypeJSON, value)
	}
	if piuo.mutation.MetadataCleared() {
		_spec.ClearField(platformintegration.FieldMetadata, field.TypeJSON)
	}
	if value, ok := piuo.mutation.UpdatedAt(); ok {
		_spec.SetField(platformintegration.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := piuo.mutation.DeletedAt(); ok {
		_spec.SetField(platformintegration.FieldDeletedAt, field.TypeTime, value)
	}
	if piuo.mutation.DeletedAtCleared() {
		_spec.ClearField(platformintegration.FieldDeletedAt, field.TypeTime)
	}
	_node = &PlatformIntegration{config: piuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, piuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{platformintegration.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	piuo.mutation.done = true
	return _node, nil
}

-- Initialize Content Management Service Database
-- This script is run when PostgreSQL container starts for the first time

-- Create additional databases if needed
-- CREATE DATABASE content_mgmt_test;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For text search
CREATE EXTENSION IF NOT EXISTS "unaccent"; -- For text normalization

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE content_mgmt_dev TO postgres;

-- Set timezone
SET timezone = 'UTC';

-- Create schema if needed (Ent will handle table creation)
-- Tables will be created automatically by Ent migrations

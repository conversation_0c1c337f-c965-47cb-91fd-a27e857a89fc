// Code generated by ent, DO NOT EDIT.

package payment

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldUserID, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldOrderID, v))
}

// SubscriptionID applies equality check predicate on the "subscription_id" field. It's identical to SubscriptionIDEQ.
func SubscriptionID(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldSubscriptionID, v))
}

// PaymentMethod applies equality check predicate on the "payment_method" field. It's identical to PaymentMethodEQ.
func PaymentMethod(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentProvider applies equality check predicate on the "payment_provider" field. It's identical to PaymentProviderEQ.
func PaymentProvider(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentProvider, v))
}

// ExternalPaymentID applies equality check predicate on the "external_payment_id" field. It's identical to ExternalPaymentIDEQ.
func ExternalPaymentID(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldExternalPaymentID, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldAmount, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldCurrency, v))
}

// PaymentIntentID applies equality check predicate on the "payment_intent_id" field. It's identical to PaymentIntentIDEQ.
func PaymentIntentID(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentIntentID, v))
}

// ChargeID applies equality check predicate on the "charge_id" field. It's identical to ChargeIDEQ.
func ChargeID(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldChargeID, v))
}

// PaidAt applies equality check predicate on the "paid_at" field. It's identical to PaidAtEQ.
func PaidAt(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaidAt, v))
}

// FailedAt applies equality check predicate on the "failed_at" field. It's identical to FailedAtEQ.
func FailedAt(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailedAt, v))
}

// FailureCode applies equality check predicate on the "failure_code" field. It's identical to FailureCodeEQ.
func FailureCode(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailureCode, v))
}

// FailureMessage applies equality check predicate on the "failure_message" field. It's identical to FailureMessageEQ.
func FailureMessage(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailureMessage, v))
}

// RefundedAmount applies equality check predicate on the "refunded_amount" field. It's identical to RefundedAmountEQ.
func RefundedAmount(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundedAmount, v))
}

// RefundedAt applies equality check predicate on the "refunded_at" field. It's identical to RefundedAtEQ.
func RefundedAt(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundedAt, v))
}

// RefundReason applies equality check predicate on the "refund_reason" field. It's identical to RefundReasonEQ.
func RefundReason(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundReason, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldUserID, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDIsNil applies the IsNil predicate on the "order_id" field.
func OrderIDIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldOrderID))
}

// OrderIDNotNil applies the NotNil predicate on the "order_id" field.
func OrderIDNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldOrderID))
}

// SubscriptionIDEQ applies the EQ predicate on the "subscription_id" field.
func SubscriptionIDEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldSubscriptionID, v))
}

// SubscriptionIDNEQ applies the NEQ predicate on the "subscription_id" field.
func SubscriptionIDNEQ(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldSubscriptionID, v))
}

// SubscriptionIDIn applies the In predicate on the "subscription_id" field.
func SubscriptionIDIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldSubscriptionID, vs...))
}

// SubscriptionIDNotIn applies the NotIn predicate on the "subscription_id" field.
func SubscriptionIDNotIn(vs ...uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldSubscriptionID, vs...))
}

// SubscriptionIDGT applies the GT predicate on the "subscription_id" field.
func SubscriptionIDGT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldSubscriptionID, v))
}

// SubscriptionIDGTE applies the GTE predicate on the "subscription_id" field.
func SubscriptionIDGTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldSubscriptionID, v))
}

// SubscriptionIDLT applies the LT predicate on the "subscription_id" field.
func SubscriptionIDLT(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldSubscriptionID, v))
}

// SubscriptionIDLTE applies the LTE predicate on the "subscription_id" field.
func SubscriptionIDLTE(v uuid.UUID) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldSubscriptionID, v))
}

// SubscriptionIDIsNil applies the IsNil predicate on the "subscription_id" field.
func SubscriptionIDIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldSubscriptionID))
}

// SubscriptionIDNotNil applies the NotNil predicate on the "subscription_id" field.
func SubscriptionIDNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldSubscriptionID))
}

// PaymentMethodEQ applies the EQ predicate on the "payment_method" field.
func PaymentMethodEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentMethodNEQ applies the NEQ predicate on the "payment_method" field.
func PaymentMethodNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldPaymentMethod, v))
}

// PaymentMethodIn applies the In predicate on the "payment_method" field.
func PaymentMethodIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldPaymentMethod, vs...))
}

// PaymentMethodNotIn applies the NotIn predicate on the "payment_method" field.
func PaymentMethodNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldPaymentMethod, vs...))
}

// PaymentMethodGT applies the GT predicate on the "payment_method" field.
func PaymentMethodGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldPaymentMethod, v))
}

// PaymentMethodGTE applies the GTE predicate on the "payment_method" field.
func PaymentMethodGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldPaymentMethod, v))
}

// PaymentMethodLT applies the LT predicate on the "payment_method" field.
func PaymentMethodLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldPaymentMethod, v))
}

// PaymentMethodLTE applies the LTE predicate on the "payment_method" field.
func PaymentMethodLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldPaymentMethod, v))
}

// PaymentMethodContains applies the Contains predicate on the "payment_method" field.
func PaymentMethodContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldPaymentMethod, v))
}

// PaymentMethodHasPrefix applies the HasPrefix predicate on the "payment_method" field.
func PaymentMethodHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldPaymentMethod, v))
}

// PaymentMethodHasSuffix applies the HasSuffix predicate on the "payment_method" field.
func PaymentMethodHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldPaymentMethod, v))
}

// PaymentMethodEqualFold applies the EqualFold predicate on the "payment_method" field.
func PaymentMethodEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldPaymentMethod, v))
}

// PaymentMethodContainsFold applies the ContainsFold predicate on the "payment_method" field.
func PaymentMethodContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldPaymentMethod, v))
}

// PaymentProviderEQ applies the EQ predicate on the "payment_provider" field.
func PaymentProviderEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentProvider, v))
}

// PaymentProviderNEQ applies the NEQ predicate on the "payment_provider" field.
func PaymentProviderNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldPaymentProvider, v))
}

// PaymentProviderIn applies the In predicate on the "payment_provider" field.
func PaymentProviderIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldPaymentProvider, vs...))
}

// PaymentProviderNotIn applies the NotIn predicate on the "payment_provider" field.
func PaymentProviderNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldPaymentProvider, vs...))
}

// PaymentProviderGT applies the GT predicate on the "payment_provider" field.
func PaymentProviderGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldPaymentProvider, v))
}

// PaymentProviderGTE applies the GTE predicate on the "payment_provider" field.
func PaymentProviderGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldPaymentProvider, v))
}

// PaymentProviderLT applies the LT predicate on the "payment_provider" field.
func PaymentProviderLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldPaymentProvider, v))
}

// PaymentProviderLTE applies the LTE predicate on the "payment_provider" field.
func PaymentProviderLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldPaymentProvider, v))
}

// PaymentProviderContains applies the Contains predicate on the "payment_provider" field.
func PaymentProviderContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldPaymentProvider, v))
}

// PaymentProviderHasPrefix applies the HasPrefix predicate on the "payment_provider" field.
func PaymentProviderHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldPaymentProvider, v))
}

// PaymentProviderHasSuffix applies the HasSuffix predicate on the "payment_provider" field.
func PaymentProviderHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldPaymentProvider, v))
}

// PaymentProviderEqualFold applies the EqualFold predicate on the "payment_provider" field.
func PaymentProviderEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldPaymentProvider, v))
}

// PaymentProviderContainsFold applies the ContainsFold predicate on the "payment_provider" field.
func PaymentProviderContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldPaymentProvider, v))
}

// ExternalPaymentIDEQ applies the EQ predicate on the "external_payment_id" field.
func ExternalPaymentIDEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldExternalPaymentID, v))
}

// ExternalPaymentIDNEQ applies the NEQ predicate on the "external_payment_id" field.
func ExternalPaymentIDNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldExternalPaymentID, v))
}

// ExternalPaymentIDIn applies the In predicate on the "external_payment_id" field.
func ExternalPaymentIDIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldExternalPaymentID, vs...))
}

// ExternalPaymentIDNotIn applies the NotIn predicate on the "external_payment_id" field.
func ExternalPaymentIDNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldExternalPaymentID, vs...))
}

// ExternalPaymentIDGT applies the GT predicate on the "external_payment_id" field.
func ExternalPaymentIDGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldExternalPaymentID, v))
}

// ExternalPaymentIDGTE applies the GTE predicate on the "external_payment_id" field.
func ExternalPaymentIDGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldExternalPaymentID, v))
}

// ExternalPaymentIDLT applies the LT predicate on the "external_payment_id" field.
func ExternalPaymentIDLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldExternalPaymentID, v))
}

// ExternalPaymentIDLTE applies the LTE predicate on the "external_payment_id" field.
func ExternalPaymentIDLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldExternalPaymentID, v))
}

// ExternalPaymentIDContains applies the Contains predicate on the "external_payment_id" field.
func ExternalPaymentIDContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldExternalPaymentID, v))
}

// ExternalPaymentIDHasPrefix applies the HasPrefix predicate on the "external_payment_id" field.
func ExternalPaymentIDHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldExternalPaymentID, v))
}

// ExternalPaymentIDHasSuffix applies the HasSuffix predicate on the "external_payment_id" field.
func ExternalPaymentIDHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldExternalPaymentID, v))
}

// ExternalPaymentIDIsNil applies the IsNil predicate on the "external_payment_id" field.
func ExternalPaymentIDIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldExternalPaymentID))
}

// ExternalPaymentIDNotNil applies the NotNil predicate on the "external_payment_id" field.
func ExternalPaymentIDNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldExternalPaymentID))
}

// ExternalPaymentIDEqualFold applies the EqualFold predicate on the "external_payment_id" field.
func ExternalPaymentIDEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldExternalPaymentID, v))
}

// ExternalPaymentIDContainsFold applies the ContainsFold predicate on the "external_payment_id" field.
func ExternalPaymentIDContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldExternalPaymentID, v))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...int64) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...int64) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldAmount, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldCurrency, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldStatus, vs...))
}

// PaymentIntentIDEQ applies the EQ predicate on the "payment_intent_id" field.
func PaymentIntentIDEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaymentIntentID, v))
}

// PaymentIntentIDNEQ applies the NEQ predicate on the "payment_intent_id" field.
func PaymentIntentIDNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldPaymentIntentID, v))
}

// PaymentIntentIDIn applies the In predicate on the "payment_intent_id" field.
func PaymentIntentIDIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldPaymentIntentID, vs...))
}

// PaymentIntentIDNotIn applies the NotIn predicate on the "payment_intent_id" field.
func PaymentIntentIDNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldPaymentIntentID, vs...))
}

// PaymentIntentIDGT applies the GT predicate on the "payment_intent_id" field.
func PaymentIntentIDGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldPaymentIntentID, v))
}

// PaymentIntentIDGTE applies the GTE predicate on the "payment_intent_id" field.
func PaymentIntentIDGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldPaymentIntentID, v))
}

// PaymentIntentIDLT applies the LT predicate on the "payment_intent_id" field.
func PaymentIntentIDLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldPaymentIntentID, v))
}

// PaymentIntentIDLTE applies the LTE predicate on the "payment_intent_id" field.
func PaymentIntentIDLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldPaymentIntentID, v))
}

// PaymentIntentIDContains applies the Contains predicate on the "payment_intent_id" field.
func PaymentIntentIDContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldPaymentIntentID, v))
}

// PaymentIntentIDHasPrefix applies the HasPrefix predicate on the "payment_intent_id" field.
func PaymentIntentIDHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldPaymentIntentID, v))
}

// PaymentIntentIDHasSuffix applies the HasSuffix predicate on the "payment_intent_id" field.
func PaymentIntentIDHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldPaymentIntentID, v))
}

// PaymentIntentIDIsNil applies the IsNil predicate on the "payment_intent_id" field.
func PaymentIntentIDIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldPaymentIntentID))
}

// PaymentIntentIDNotNil applies the NotNil predicate on the "payment_intent_id" field.
func PaymentIntentIDNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldPaymentIntentID))
}

// PaymentIntentIDEqualFold applies the EqualFold predicate on the "payment_intent_id" field.
func PaymentIntentIDEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldPaymentIntentID, v))
}

// PaymentIntentIDContainsFold applies the ContainsFold predicate on the "payment_intent_id" field.
func PaymentIntentIDContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldPaymentIntentID, v))
}

// ChargeIDEQ applies the EQ predicate on the "charge_id" field.
func ChargeIDEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldChargeID, v))
}

// ChargeIDNEQ applies the NEQ predicate on the "charge_id" field.
func ChargeIDNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldChargeID, v))
}

// ChargeIDIn applies the In predicate on the "charge_id" field.
func ChargeIDIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldChargeID, vs...))
}

// ChargeIDNotIn applies the NotIn predicate on the "charge_id" field.
func ChargeIDNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldChargeID, vs...))
}

// ChargeIDGT applies the GT predicate on the "charge_id" field.
func ChargeIDGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldChargeID, v))
}

// ChargeIDGTE applies the GTE predicate on the "charge_id" field.
func ChargeIDGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldChargeID, v))
}

// ChargeIDLT applies the LT predicate on the "charge_id" field.
func ChargeIDLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldChargeID, v))
}

// ChargeIDLTE applies the LTE predicate on the "charge_id" field.
func ChargeIDLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldChargeID, v))
}

// ChargeIDContains applies the Contains predicate on the "charge_id" field.
func ChargeIDContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldChargeID, v))
}

// ChargeIDHasPrefix applies the HasPrefix predicate on the "charge_id" field.
func ChargeIDHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldChargeID, v))
}

// ChargeIDHasSuffix applies the HasSuffix predicate on the "charge_id" field.
func ChargeIDHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldChargeID, v))
}

// ChargeIDIsNil applies the IsNil predicate on the "charge_id" field.
func ChargeIDIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldChargeID))
}

// ChargeIDNotNil applies the NotNil predicate on the "charge_id" field.
func ChargeIDNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldChargeID))
}

// ChargeIDEqualFold applies the EqualFold predicate on the "charge_id" field.
func ChargeIDEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldChargeID, v))
}

// ChargeIDContainsFold applies the ContainsFold predicate on the "charge_id" field.
func ChargeIDContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldChargeID, v))
}

// BillingDetailsIsNil applies the IsNil predicate on the "billing_details" field.
func BillingDetailsIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldBillingDetails))
}

// BillingDetailsNotNil applies the NotNil predicate on the "billing_details" field.
func BillingDetailsNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldBillingDetails))
}

// PaymentMethodDetailsIsNil applies the IsNil predicate on the "payment_method_details" field.
func PaymentMethodDetailsIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldPaymentMethodDetails))
}

// PaymentMethodDetailsNotNil applies the NotNil predicate on the "payment_method_details" field.
func PaymentMethodDetailsNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldPaymentMethodDetails))
}

// PaidAtEQ applies the EQ predicate on the "paid_at" field.
func PaidAtEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldPaidAt, v))
}

// PaidAtNEQ applies the NEQ predicate on the "paid_at" field.
func PaidAtNEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldPaidAt, v))
}

// PaidAtIn applies the In predicate on the "paid_at" field.
func PaidAtIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldPaidAt, vs...))
}

// PaidAtNotIn applies the NotIn predicate on the "paid_at" field.
func PaidAtNotIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldPaidAt, vs...))
}

// PaidAtGT applies the GT predicate on the "paid_at" field.
func PaidAtGT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldPaidAt, v))
}

// PaidAtGTE applies the GTE predicate on the "paid_at" field.
func PaidAtGTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldPaidAt, v))
}

// PaidAtLT applies the LT predicate on the "paid_at" field.
func PaidAtLT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldPaidAt, v))
}

// PaidAtLTE applies the LTE predicate on the "paid_at" field.
func PaidAtLTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldPaidAt, v))
}

// PaidAtIsNil applies the IsNil predicate on the "paid_at" field.
func PaidAtIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldPaidAt))
}

// PaidAtNotNil applies the NotNil predicate on the "paid_at" field.
func PaidAtNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldPaidAt))
}

// FailedAtEQ applies the EQ predicate on the "failed_at" field.
func FailedAtEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailedAt, v))
}

// FailedAtNEQ applies the NEQ predicate on the "failed_at" field.
func FailedAtNEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldFailedAt, v))
}

// FailedAtIn applies the In predicate on the "failed_at" field.
func FailedAtIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldFailedAt, vs...))
}

// FailedAtNotIn applies the NotIn predicate on the "failed_at" field.
func FailedAtNotIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldFailedAt, vs...))
}

// FailedAtGT applies the GT predicate on the "failed_at" field.
func FailedAtGT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldFailedAt, v))
}

// FailedAtGTE applies the GTE predicate on the "failed_at" field.
func FailedAtGTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldFailedAt, v))
}

// FailedAtLT applies the LT predicate on the "failed_at" field.
func FailedAtLT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldFailedAt, v))
}

// FailedAtLTE applies the LTE predicate on the "failed_at" field.
func FailedAtLTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldFailedAt, v))
}

// FailedAtIsNil applies the IsNil predicate on the "failed_at" field.
func FailedAtIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldFailedAt))
}

// FailedAtNotNil applies the NotNil predicate on the "failed_at" field.
func FailedAtNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldFailedAt))
}

// FailureCodeEQ applies the EQ predicate on the "failure_code" field.
func FailureCodeEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailureCode, v))
}

// FailureCodeNEQ applies the NEQ predicate on the "failure_code" field.
func FailureCodeNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldFailureCode, v))
}

// FailureCodeIn applies the In predicate on the "failure_code" field.
func FailureCodeIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldFailureCode, vs...))
}

// FailureCodeNotIn applies the NotIn predicate on the "failure_code" field.
func FailureCodeNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldFailureCode, vs...))
}

// FailureCodeGT applies the GT predicate on the "failure_code" field.
func FailureCodeGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldFailureCode, v))
}

// FailureCodeGTE applies the GTE predicate on the "failure_code" field.
func FailureCodeGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldFailureCode, v))
}

// FailureCodeLT applies the LT predicate on the "failure_code" field.
func FailureCodeLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldFailureCode, v))
}

// FailureCodeLTE applies the LTE predicate on the "failure_code" field.
func FailureCodeLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldFailureCode, v))
}

// FailureCodeContains applies the Contains predicate on the "failure_code" field.
func FailureCodeContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldFailureCode, v))
}

// FailureCodeHasPrefix applies the HasPrefix predicate on the "failure_code" field.
func FailureCodeHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldFailureCode, v))
}

// FailureCodeHasSuffix applies the HasSuffix predicate on the "failure_code" field.
func FailureCodeHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldFailureCode, v))
}

// FailureCodeIsNil applies the IsNil predicate on the "failure_code" field.
func FailureCodeIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldFailureCode))
}

// FailureCodeNotNil applies the NotNil predicate on the "failure_code" field.
func FailureCodeNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldFailureCode))
}

// FailureCodeEqualFold applies the EqualFold predicate on the "failure_code" field.
func FailureCodeEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldFailureCode, v))
}

// FailureCodeContainsFold applies the ContainsFold predicate on the "failure_code" field.
func FailureCodeContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldFailureCode, v))
}

// FailureMessageEQ applies the EQ predicate on the "failure_message" field.
func FailureMessageEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldFailureMessage, v))
}

// FailureMessageNEQ applies the NEQ predicate on the "failure_message" field.
func FailureMessageNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldFailureMessage, v))
}

// FailureMessageIn applies the In predicate on the "failure_message" field.
func FailureMessageIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldFailureMessage, vs...))
}

// FailureMessageNotIn applies the NotIn predicate on the "failure_message" field.
func FailureMessageNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldFailureMessage, vs...))
}

// FailureMessageGT applies the GT predicate on the "failure_message" field.
func FailureMessageGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldFailureMessage, v))
}

// FailureMessageGTE applies the GTE predicate on the "failure_message" field.
func FailureMessageGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldFailureMessage, v))
}

// FailureMessageLT applies the LT predicate on the "failure_message" field.
func FailureMessageLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldFailureMessage, v))
}

// FailureMessageLTE applies the LTE predicate on the "failure_message" field.
func FailureMessageLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldFailureMessage, v))
}

// FailureMessageContains applies the Contains predicate on the "failure_message" field.
func FailureMessageContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldFailureMessage, v))
}

// FailureMessageHasPrefix applies the HasPrefix predicate on the "failure_message" field.
func FailureMessageHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldFailureMessage, v))
}

// FailureMessageHasSuffix applies the HasSuffix predicate on the "failure_message" field.
func FailureMessageHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldFailureMessage, v))
}

// FailureMessageIsNil applies the IsNil predicate on the "failure_message" field.
func FailureMessageIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldFailureMessage))
}

// FailureMessageNotNil applies the NotNil predicate on the "failure_message" field.
func FailureMessageNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldFailureMessage))
}

// FailureMessageEqualFold applies the EqualFold predicate on the "failure_message" field.
func FailureMessageEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldFailureMessage, v))
}

// FailureMessageContainsFold applies the ContainsFold predicate on the "failure_message" field.
func FailureMessageContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldFailureMessage, v))
}

// RefundedAmountEQ applies the EQ predicate on the "refunded_amount" field.
func RefundedAmountEQ(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundedAmount, v))
}

// RefundedAmountNEQ applies the NEQ predicate on the "refunded_amount" field.
func RefundedAmountNEQ(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldRefundedAmount, v))
}

// RefundedAmountIn applies the In predicate on the "refunded_amount" field.
func RefundedAmountIn(vs ...int64) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldRefundedAmount, vs...))
}

// RefundedAmountNotIn applies the NotIn predicate on the "refunded_amount" field.
func RefundedAmountNotIn(vs ...int64) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldRefundedAmount, vs...))
}

// RefundedAmountGT applies the GT predicate on the "refunded_amount" field.
func RefundedAmountGT(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldRefundedAmount, v))
}

// RefundedAmountGTE applies the GTE predicate on the "refunded_amount" field.
func RefundedAmountGTE(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldRefundedAmount, v))
}

// RefundedAmountLT applies the LT predicate on the "refunded_amount" field.
func RefundedAmountLT(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldRefundedAmount, v))
}

// RefundedAmountLTE applies the LTE predicate on the "refunded_amount" field.
func RefundedAmountLTE(v int64) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldRefundedAmount, v))
}

// RefundedAtEQ applies the EQ predicate on the "refunded_at" field.
func RefundedAtEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundedAt, v))
}

// RefundedAtNEQ applies the NEQ predicate on the "refunded_at" field.
func RefundedAtNEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldRefundedAt, v))
}

// RefundedAtIn applies the In predicate on the "refunded_at" field.
func RefundedAtIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldRefundedAt, vs...))
}

// RefundedAtNotIn applies the NotIn predicate on the "refunded_at" field.
func RefundedAtNotIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldRefundedAt, vs...))
}

// RefundedAtGT applies the GT predicate on the "refunded_at" field.
func RefundedAtGT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldRefundedAt, v))
}

// RefundedAtGTE applies the GTE predicate on the "refunded_at" field.
func RefundedAtGTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldRefundedAt, v))
}

// RefundedAtLT applies the LT predicate on the "refunded_at" field.
func RefundedAtLT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldRefundedAt, v))
}

// RefundedAtLTE applies the LTE predicate on the "refunded_at" field.
func RefundedAtLTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldRefundedAt, v))
}

// RefundedAtIsNil applies the IsNil predicate on the "refunded_at" field.
func RefundedAtIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldRefundedAt))
}

// RefundedAtNotNil applies the NotNil predicate on the "refunded_at" field.
func RefundedAtNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldRefundedAt))
}

// RefundReasonEQ applies the EQ predicate on the "refund_reason" field.
func RefundReasonEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldRefundReason, v))
}

// RefundReasonNEQ applies the NEQ predicate on the "refund_reason" field.
func RefundReasonNEQ(v string) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldRefundReason, v))
}

// RefundReasonIn applies the In predicate on the "refund_reason" field.
func RefundReasonIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldRefundReason, vs...))
}

// RefundReasonNotIn applies the NotIn predicate on the "refund_reason" field.
func RefundReasonNotIn(vs ...string) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldRefundReason, vs...))
}

// RefundReasonGT applies the GT predicate on the "refund_reason" field.
func RefundReasonGT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldRefundReason, v))
}

// RefundReasonGTE applies the GTE predicate on the "refund_reason" field.
func RefundReasonGTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldRefundReason, v))
}

// RefundReasonLT applies the LT predicate on the "refund_reason" field.
func RefundReasonLT(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldRefundReason, v))
}

// RefundReasonLTE applies the LTE predicate on the "refund_reason" field.
func RefundReasonLTE(v string) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldRefundReason, v))
}

// RefundReasonContains applies the Contains predicate on the "refund_reason" field.
func RefundReasonContains(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContains(FieldRefundReason, v))
}

// RefundReasonHasPrefix applies the HasPrefix predicate on the "refund_reason" field.
func RefundReasonHasPrefix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasPrefix(FieldRefundReason, v))
}

// RefundReasonHasSuffix applies the HasSuffix predicate on the "refund_reason" field.
func RefundReasonHasSuffix(v string) predicate.Payment {
	return predicate.Payment(sql.FieldHasSuffix(FieldRefundReason, v))
}

// RefundReasonIsNil applies the IsNil predicate on the "refund_reason" field.
func RefundReasonIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldRefundReason))
}

// RefundReasonNotNil applies the NotNil predicate on the "refund_reason" field.
func RefundReasonNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldRefundReason))
}

// RefundReasonEqualFold applies the EqualFold predicate on the "refund_reason" field.
func RefundReasonEqualFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldEqualFold(FieldRefundReason, v))
}

// RefundReasonContainsFold applies the ContainsFold predicate on the "refund_reason" field.
func RefundReasonContainsFold(v string) predicate.Payment {
	return predicate.Payment(sql.FieldContainsFold(FieldRefundReason, v))
}

// WebhookDataIsNil applies the IsNil predicate on the "webhook_data" field.
func WebhookDataIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldWebhookData))
}

// WebhookDataNotNil applies the NotNil predicate on the "webhook_data" field.
func WebhookDataNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldWebhookData))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Payment {
	return predicate.Payment(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Payment {
	return predicate.Payment(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Payment {
	return predicate.Payment(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Payment) predicate.Payment {
	return predicate.Payment(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Payment) predicate.Payment {
	return predicate.Payment(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Payment) predicate.Payment {
	return predicate.Payment(sql.NotPredicates(p))
}

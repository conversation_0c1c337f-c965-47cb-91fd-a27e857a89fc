package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PublishedPostRepository handles published post data operations
type PublishedPostRepository struct {
	client *ent.Client
	logger logging.Logger
}

// NewPublishedPostRepository creates a new published post repository
func NewPublishedPostRepository(client *ent.Client, logger logging.Logger) *PublishedPostRepository {
	return &PublishedPostRepository{
		client: client,
		logger: logger,
	}
}

// GetPublishedPost retrieves a published post by ID
func (r *PublishedPostRepository) GetPublishedPost(ctx context.Context, id string) (*ent.PublishedPost, error) {
	postID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid post ID: %w", err)
	}

	post, err := r.client.PublishedPost.
		Query().
		Where(publishedpost.ID(postID)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("published post not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get published post: %w", err)
	}

	return post, nil
}

// ListPublishedPosts retrieves published posts with filters
func (r *PublishedPostRepository) ListPublishedPosts(ctx context.Context, userID, platform string, limit, offset int32) ([]*ent.PublishedPost, int32, error) {
	query := r.client.PublishedPost.Query()

	// Apply filters
	if userID != "" {
		userUUID, err := uuid.Parse(userID)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid user ID: %w", err)
		}
		query = query.Where(publishedpost.UserID(userUUID))
	}

	if platform != "" {
		query = query.Where(publishedpost.PlatformEQ(publishedpost.Platform(platform)))
	}

	// Get total count
	totalCount, err := query.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count published posts: %w", err)
	}

	// Apply pagination
	if limit > 0 {
		query = query.Limit(int(limit))
	}
	if offset > 0 {
		query = query.Offset(int(offset))
	}

	// Order by creation date (newest first)
	query = query.Order(ent.Desc(publishedpost.FieldCreatedAt))

	posts, err := query.All(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list published posts: %w", err)
	}

	return posts, int32(totalCount), nil
}

// CreatePublishedPost creates a new published post
func (r *PublishedPostRepository) CreatePublishedPost(ctx context.Context, post *ent.PublishedPost) (*ent.PublishedPost, error) {
	create := r.client.PublishedPost.Create().
		SetID(post.ID).
		SetUserID(post.UserID).
		SetPostID(post.PostID).
		SetIntegrationID(post.IntegrationID).
		SetPlatform(post.Platform).
		SetContent(post.Content).
		SetStatus(post.Status).
		SetCreatedAt(post.CreatedAt).
		SetUpdatedAt(post.UpdatedAt)

	// Set optional fields
	if len(post.MediaUrls) > 0 {
		create = create.SetMediaUrls(post.MediaUrls)
	}
	if len(post.Hashtags) > 0 {
		create = create.SetHashtags(post.Hashtags)
	}
	if post.PlatformPostID != "" {
		create = create.SetPlatformPostID(post.PlatformPostID)
	}
	if post.PlatformPostURL != "" {
		create = create.SetPlatformPostURL(post.PlatformPostURL)
	}
	if !post.ScheduledAt.IsZero() {
		create = create.SetScheduledAt(post.ScheduledAt)
	}
	if !post.PublishedAt.IsZero() {
		create = create.SetPublishedAt(post.PublishedAt)
	}
	if post.ErrorMessage != "" {
		create = create.SetErrorMessage(post.ErrorMessage)
	}
	if post.PublishOptions != nil {
		create = create.SetPublishOptions(post.PublishOptions)
	}
	if post.Analytics != nil {
		create = create.SetAnalytics(post.Analytics)
	}

	created, err := create.Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create published post: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"post_id":     created.ID,
		"user_id":     created.UserID,
		"platform":    created.Platform,
		"original_id": created.PostID,
	}).Info("Published post created")

	return created, nil
}

// UpdatePublishedPost updates a published post
func (r *PublishedPostRepository) UpdatePublishedPost(ctx context.Context, id string, updates map[string]interface{}) (*ent.PublishedPost, error) {
	postID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid post ID: %w", err)
	}

	update := r.client.PublishedPost.UpdateOneID(postID)

	// Apply updates based on the map
	for key, value := range updates {
		switch key {
		case "platform_post_id":
			if v, ok := value.(string); ok {
				update = update.SetPlatformPostID(v)
			}
		case "platform_post_url":
			if v, ok := value.(string); ok {
				update = update.SetPlatformPostURL(v)
			}
		case "status":
			if v, ok := value.(string); ok {
				update = update.SetStatus(publishedpost.Status(v))
			}
		case "published_at":
			if v, ok := value.(time.Time); ok {
				update = update.SetPublishedAt(v)
			}
		case "error_message":
			if v, ok := value.(string); ok {
				update = update.SetErrorMessage(v)
			}
		case "updated_at":
			if v, ok := value.(time.Time); ok {
				update = update.SetUpdatedAt(v)
			}
		case "analytics":
			if v, ok := value.(map[string]interface{}); ok {
				update = update.SetAnalytics(v)
			}
		case "publish_options":
			if v, ok := value.(map[string]interface{}); ok {
				update = update.SetPublishOptions(v)
			}
		}
	}

	updated, err := update.Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("published post not found: %s", id)
		}
		return nil, fmt.Errorf("failed to update published post: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"post_id": updated.ID,
		"updates": len(updates),
	}).Info("Published post updated")

	return updated, nil
}

// DeletePublishedPost deletes a published post
func (r *PublishedPostRepository) DeletePublishedPost(ctx context.Context, id string) error {
	postID, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid post ID: %w", err)
	}

	err = r.client.PublishedPost.DeleteOneID(postID).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("published post not found: %s", id)
		}
		return fmt.Errorf("failed to delete published post: %w", err)
	}

	r.logger.WithField("post_id", postID).Info("Published post deleted")
	return nil
}

// GetPublishedPostsByIntegration retrieves published posts for a specific integration
func (r *PublishedPostRepository) GetPublishedPostsByIntegration(ctx context.Context, integrationID string, limit, offset int32) ([]*ent.PublishedPost, error) {
	query := r.client.PublishedPost.
		Query().
		Where(publishedpost.IntegrationID(integrationID))

	// Apply pagination
	if limit > 0 {
		query = query.Limit(int(limit))
	}
	if offset > 0 {
		query = query.Offset(int(offset))
	}

	// Order by creation date (newest first)
	query = query.Order(ent.Desc(publishedpost.FieldCreatedAt))

	posts, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get published posts by integration: %w", err)
	}

	return posts, nil
}

// GetScheduledPosts retrieves posts that are scheduled for publishing
func (r *PublishedPostRepository) GetScheduledPosts(ctx context.Context, before time.Time) ([]*ent.PublishedPost, error) {
	posts, err := r.client.PublishedPost.
		Query().
		Where(
			publishedpost.StatusEQ(publishedpost.StatusScheduled),
			publishedpost.ScheduledAtLTE(before),
		).
		Order(ent.Asc(publishedpost.FieldScheduledAt)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get scheduled posts: %w", err)
	}

	return posts, nil
}

// GetPostAnalytics retrieves analytics for posts
func (r *PublishedPostRepository) GetPostAnalytics(ctx context.Context, userID string, platform string, dateFrom, dateTo time.Time) ([]*ent.PublishedPost, error) {
	query := r.client.PublishedPost.Query()

	if userID != "" {
		userUUID, err := uuid.Parse(userID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		query = query.Where(publishedpost.UserID(userUUID))
	}

	if platform != "" {
		query = query.Where(publishedpost.PlatformEQ(publishedpost.Platform(platform)))
	}

	query = query.Where(
		publishedpost.StatusEQ(publishedpost.StatusPublished),
		publishedpost.PublishedAtGTE(dateFrom),
		publishedpost.PublishedAtLTE(dateTo),
	)

	posts, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get post analytics: %w", err)
	}

	return posts, nil
}

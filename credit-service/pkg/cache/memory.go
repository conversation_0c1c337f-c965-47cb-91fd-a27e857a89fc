package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// MemoryCache is an in-memory cache implementation for testing
type MemoryCache struct {
	mu    sync.RWMutex
	data  map[string]cacheItem
	
	// Default TTL
	defaultTTL       time.Duration
	creditBalanceTTL time.Duration
}

type cacheItem struct {
	value     []byte
	expiresAt time.Time
}

// NewMemoryCache creates a new in-memory cache
func NewMemoryCache(defaultTTL, creditBalanceTTL time.Duration) *MemoryCache {
	cache := &MemoryCache{
		data:             make(map[string]cacheItem),
		defaultTTL:       defaultTTL,
		creditBalanceTTL: creditBalanceTTL,
	}
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// cleanup removes expired items periodically
func (m *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		m.mu.Lock()
		now := time.Now()
		for key, item := range m.data {
			if now.After(item.expiresAt) {
				delete(m.data, key)
			}
		}
		m.mu.Unlock()
	}
}

// SetCreditBalance caches user credit balance
func (m *MemoryCache) SetCreditBalance(ctx context.Context, userID string, balance interface{}) error {
	key := fmt.Sprintf("credit_balance:%s", userID)
	return m.Set(ctx, key, balance, m.creditBalanceTTL)
}

// GetCreditBalance retrieves cached user credit balance
func (m *MemoryCache) GetCreditBalance(ctx context.Context, userID string, result interface{}) error {
	key := fmt.Sprintf("credit_balance:%s", userID)
	return m.Get(ctx, key, result)
}

// DeleteCreditBalance removes cached user credit balance
func (m *MemoryCache) DeleteCreditBalance(ctx context.Context, userID string) error {
	key := fmt.Sprintf("credit_balance:%s", userID)
	return m.Delete(ctx, key)
}

// Set caches data with TTL
func (m *MemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}
	
	if ttl == 0 {
		ttl = m.defaultTTL
	}
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.data[key] = cacheItem{
		value:     data,
		expiresAt: time.Now().Add(ttl),
	}
	
	return nil
}

// Get retrieves cached data
func (m *MemoryCache) Get(ctx context.Context, key string, result interface{}) error {
	m.mu.RLock()
	item, exists := m.data[key]
	m.mu.RUnlock()
	
	if !exists {
		return ErrCacheMiss
	}
	
	if time.Now().After(item.expiresAt) {
		m.mu.Lock()
		delete(m.data, key)
		m.mu.Unlock()
		return ErrCacheMiss
	}
	
	err := json.Unmarshal(item.value, result)
	if err != nil {
		return fmt.Errorf("failed to unmarshal cached data: %w", err)
	}
	
	return nil
}

// Delete removes cached data
func (m *MemoryCache) Delete(ctx context.Context, key string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	delete(m.data, key)
	return nil
}

// Exists checks if key exists in cache
func (m *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	m.mu.RLock()
	item, exists := m.data[key]
	m.mu.RUnlock()
	
	if !exists {
		return false, nil
	}
	
	if time.Now().After(item.expiresAt) {
		m.mu.Lock()
		delete(m.data, key)
		m.mu.Unlock()
		return false, nil
	}
	
	return true, nil
}

// TTL returns the remaining time to live of a key
func (m *MemoryCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	m.mu.RLock()
	item, exists := m.data[key]
	m.mu.RUnlock()
	
	if !exists {
		return -2 * time.Second, nil // Key does not exist
	}
	
	remaining := time.Until(item.expiresAt)
	if remaining <= 0 {
		m.mu.Lock()
		delete(m.data, key)
		m.mu.Unlock()
		return -2 * time.Second, nil
	}
	
	return remaining, nil
}

// FlushAll clears all cache
func (m *MemoryCache) FlushAll(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.data = make(map[string]cacheItem)
	return nil
}

// Close closes the cache (no-op for memory cache)
func (m *MemoryCache) Close() error {
	return nil
}

// Size returns the number of items in cache
func (m *MemoryCache) Size() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return len(m.data)
}

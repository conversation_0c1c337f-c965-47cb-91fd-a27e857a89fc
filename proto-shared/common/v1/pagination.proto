syntax = "proto3";

package common.v1;

option go_package = "github.com/social-content-ai/proto-shared/common/v1;commonv1";

// PaginationRequest for paginated requests
message PaginationRequest {
  int32 page = 1;            // Page number (1-based)
  int32 limit = 2;           // Items per page
  string sort_by = 3;        // Field to sort by
  string sort_order = 4;     // Sort order: "asc" or "desc"
}

// PaginationResponse for paginated responses
message PaginationResponse {
  int32 page = 1;            // Current page number
  int32 limit = 2;           // Items per page
  int32 total_items = 3;     // Total number of items
  int32 total_pages = 4;     // Total number of pages
  bool has_next = 5;         // Whether there's a next page
  bool has_prev = 6;         // Whether there's a previous page
}

// ListRequest with pagination and filtering
message ListRequest {
  PaginationRequest pagination = 1;
  map<string, string> filters = 2;  // Key-value filters
  string search = 3;                // Search query
}

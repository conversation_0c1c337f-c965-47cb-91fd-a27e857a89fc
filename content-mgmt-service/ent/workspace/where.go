// Code generated by ent, DO NOT EDIT.

package workspace

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldID, id))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldOwnerID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldDescription, v))
}

// Color applies equality check predicate on the "color" field. It's identical to ColorEQ.
func Color(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldColor, v))
}

// Icon applies equality check predicate on the "icon" field. It's identical to IconEQ.
func Icon(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIcon, v))
}

// IsPersonal applies equality check predicate on the "is_personal" field. It's identical to IsPersonalEQ.
func IsPersonal(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIsPersonal, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIsActive, v))
}

// PostCount applies equality check predicate on the "post_count" field. It's identical to PostCountEQ.
func PostCount(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldPostCount, v))
}

// TemplateCount applies equality check predicate on the "template_count" field. It's identical to TemplateCountEQ.
func TemplateCount(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldTemplateCount, v))
}

// LastActivityAt applies equality check predicate on the "last_activity_at" field. It's identical to LastActivityAtEQ.
func LastActivityAt(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldLastActivityAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldDeletedAt, v))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v uuid.UUID) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldOwnerID, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContainsFold(FieldDescription, v))
}

// ColorEQ applies the EQ predicate on the "color" field.
func ColorEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldColor, v))
}

// ColorNEQ applies the NEQ predicate on the "color" field.
func ColorNEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldColor, v))
}

// ColorIn applies the In predicate on the "color" field.
func ColorIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldColor, vs...))
}

// ColorNotIn applies the NotIn predicate on the "color" field.
func ColorNotIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldColor, vs...))
}

// ColorGT applies the GT predicate on the "color" field.
func ColorGT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldColor, v))
}

// ColorGTE applies the GTE predicate on the "color" field.
func ColorGTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldColor, v))
}

// ColorLT applies the LT predicate on the "color" field.
func ColorLT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldColor, v))
}

// ColorLTE applies the LTE predicate on the "color" field.
func ColorLTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldColor, v))
}

// ColorContains applies the Contains predicate on the "color" field.
func ColorContains(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContains(FieldColor, v))
}

// ColorHasPrefix applies the HasPrefix predicate on the "color" field.
func ColorHasPrefix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasPrefix(FieldColor, v))
}

// ColorHasSuffix applies the HasSuffix predicate on the "color" field.
func ColorHasSuffix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasSuffix(FieldColor, v))
}

// ColorEqualFold applies the EqualFold predicate on the "color" field.
func ColorEqualFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEqualFold(FieldColor, v))
}

// ColorContainsFold applies the ContainsFold predicate on the "color" field.
func ColorContainsFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContainsFold(FieldColor, v))
}

// IconEQ applies the EQ predicate on the "icon" field.
func IconEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIcon, v))
}

// IconNEQ applies the NEQ predicate on the "icon" field.
func IconNEQ(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldIcon, v))
}

// IconIn applies the In predicate on the "icon" field.
func IconIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldIcon, vs...))
}

// IconNotIn applies the NotIn predicate on the "icon" field.
func IconNotIn(vs ...string) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldIcon, vs...))
}

// IconGT applies the GT predicate on the "icon" field.
func IconGT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldIcon, v))
}

// IconGTE applies the GTE predicate on the "icon" field.
func IconGTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldIcon, v))
}

// IconLT applies the LT predicate on the "icon" field.
func IconLT(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldIcon, v))
}

// IconLTE applies the LTE predicate on the "icon" field.
func IconLTE(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldIcon, v))
}

// IconContains applies the Contains predicate on the "icon" field.
func IconContains(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContains(FieldIcon, v))
}

// IconHasPrefix applies the HasPrefix predicate on the "icon" field.
func IconHasPrefix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasPrefix(FieldIcon, v))
}

// IconHasSuffix applies the HasSuffix predicate on the "icon" field.
func IconHasSuffix(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldHasSuffix(FieldIcon, v))
}

// IconIsNil applies the IsNil predicate on the "icon" field.
func IconIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldIcon))
}

// IconNotNil applies the NotNil predicate on the "icon" field.
func IconNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldIcon))
}

// IconEqualFold applies the EqualFold predicate on the "icon" field.
func IconEqualFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldEqualFold(FieldIcon, v))
}

// IconContainsFold applies the ContainsFold predicate on the "icon" field.
func IconContainsFold(v string) predicate.Workspace {
	return predicate.Workspace(sql.FieldContainsFold(FieldIcon, v))
}

// SettingsIsNil applies the IsNil predicate on the "settings" field.
func SettingsIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldSettings))
}

// SettingsNotNil applies the NotNil predicate on the "settings" field.
func SettingsNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldSettings))
}

// DefaultPlatformsIsNil applies the IsNil predicate on the "default_platforms" field.
func DefaultPlatformsIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldDefaultPlatforms))
}

// DefaultPlatformsNotNil applies the NotNil predicate on the "default_platforms" field.
func DefaultPlatformsNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldDefaultPlatforms))
}

// BrandGuidelinesIsNil applies the IsNil predicate on the "brand_guidelines" field.
func BrandGuidelinesIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldBrandGuidelines))
}

// BrandGuidelinesNotNil applies the NotNil predicate on the "brand_guidelines" field.
func BrandGuidelinesNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldBrandGuidelines))
}

// IsPersonalEQ applies the EQ predicate on the "is_personal" field.
func IsPersonalEQ(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIsPersonal, v))
}

// IsPersonalNEQ applies the NEQ predicate on the "is_personal" field.
func IsPersonalNEQ(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldIsPersonal, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldIsActive, v))
}

// PostCountEQ applies the EQ predicate on the "post_count" field.
func PostCountEQ(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldPostCount, v))
}

// PostCountNEQ applies the NEQ predicate on the "post_count" field.
func PostCountNEQ(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldPostCount, v))
}

// PostCountIn applies the In predicate on the "post_count" field.
func PostCountIn(vs ...int) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldPostCount, vs...))
}

// PostCountNotIn applies the NotIn predicate on the "post_count" field.
func PostCountNotIn(vs ...int) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldPostCount, vs...))
}

// PostCountGT applies the GT predicate on the "post_count" field.
func PostCountGT(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldPostCount, v))
}

// PostCountGTE applies the GTE predicate on the "post_count" field.
func PostCountGTE(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldPostCount, v))
}

// PostCountLT applies the LT predicate on the "post_count" field.
func PostCountLT(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldPostCount, v))
}

// PostCountLTE applies the LTE predicate on the "post_count" field.
func PostCountLTE(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldPostCount, v))
}

// TemplateCountEQ applies the EQ predicate on the "template_count" field.
func TemplateCountEQ(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldTemplateCount, v))
}

// TemplateCountNEQ applies the NEQ predicate on the "template_count" field.
func TemplateCountNEQ(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldTemplateCount, v))
}

// TemplateCountIn applies the In predicate on the "template_count" field.
func TemplateCountIn(vs ...int) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldTemplateCount, vs...))
}

// TemplateCountNotIn applies the NotIn predicate on the "template_count" field.
func TemplateCountNotIn(vs ...int) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldTemplateCount, vs...))
}

// TemplateCountGT applies the GT predicate on the "template_count" field.
func TemplateCountGT(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldTemplateCount, v))
}

// TemplateCountGTE applies the GTE predicate on the "template_count" field.
func TemplateCountGTE(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldTemplateCount, v))
}

// TemplateCountLT applies the LT predicate on the "template_count" field.
func TemplateCountLT(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldTemplateCount, v))
}

// TemplateCountLTE applies the LTE predicate on the "template_count" field.
func TemplateCountLTE(v int) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldTemplateCount, v))
}

// LastActivityAtEQ applies the EQ predicate on the "last_activity_at" field.
func LastActivityAtEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldLastActivityAt, v))
}

// LastActivityAtNEQ applies the NEQ predicate on the "last_activity_at" field.
func LastActivityAtNEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldLastActivityAt, v))
}

// LastActivityAtIn applies the In predicate on the "last_activity_at" field.
func LastActivityAtIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldLastActivityAt, vs...))
}

// LastActivityAtNotIn applies the NotIn predicate on the "last_activity_at" field.
func LastActivityAtNotIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldLastActivityAt, vs...))
}

// LastActivityAtGT applies the GT predicate on the "last_activity_at" field.
func LastActivityAtGT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldLastActivityAt, v))
}

// LastActivityAtGTE applies the GTE predicate on the "last_activity_at" field.
func LastActivityAtGTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldLastActivityAt, v))
}

// LastActivityAtLT applies the LT predicate on the "last_activity_at" field.
func LastActivityAtLT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldLastActivityAt, v))
}

// LastActivityAtLTE applies the LTE predicate on the "last_activity_at" field.
func LastActivityAtLTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldLastActivityAt, v))
}

// LastActivityAtIsNil applies the IsNil predicate on the "last_activity_at" field.
func LastActivityAtIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldLastActivityAt))
}

// LastActivityAtNotNil applies the NotNil predicate on the "last_activity_at" field.
func LastActivityAtNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldLastActivityAt))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Workspace {
	return predicate.Workspace(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Workspace {
	return predicate.Workspace(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Workspace) predicate.Workspace {
	return predicate.Workspace(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Workspace) predicate.Workspace {
	return predicate.Workspace(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Workspace) predicate.Workspace {
	return predicate.Workspace(sql.NotPredicates(p))
}

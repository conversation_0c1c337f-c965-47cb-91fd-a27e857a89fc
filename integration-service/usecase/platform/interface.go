package platform

import "context"

// UseCase defines the platform use case interface
type UseCase interface {
	// Platform operations
	GetPlatformLimits(ctx context.Context, req *GetPlatformLimitsRequest) (*GetPlatformLimitsResponse, error)
	ProcessWebhook(ctx context.Context, req *ProcessWebhookRequest) (*ProcessWebhookResponse, error)
}

// Repository defines the data access interface for platform operations
type Repository interface {
	Reader
	Writer
}

// Reader defines read operations interface
type Reader interface {
	// Add platform-specific read operations here
}

// Writer defines write operations interface
type Writer interface {
	// Add platform-specific write operations here
}

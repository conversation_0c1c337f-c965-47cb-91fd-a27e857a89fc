package twofa

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/twofa"
)

func TestTwoFAService_GenerateSecret(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	req := &twofa.GenerateSecretRequest{
		UserEmail: "<EMAIL>",
		UserID:    "123",
	}

	resp, err := twofaService.GenerateSecret(req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotEmpty(t, resp.Secret)
	assert.NotEmpty(t, resp.QRCodeURL)
	assert.Contains(t, resp.QRCodeURL, "otpauth://totp/")
	assert.Contains(t, resp.QRCodeURL, "<EMAIL>")
}

func TestTwoFAService_VerifyToken(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Generate a secret first
	req := &twofa.GenerateSecretRequest{
		UserEmail: "<EMAIL>",
		UserID:    "123",
	}

	secretResp, err := twofaService.GenerateSecret(req)
	assert.NoError(t, err)
	assert.NotEmpty(t, secretResp.Secret)

	// Get current token for the secret
	currentToken, err := twofaService.GetCurrentToken(secretResp.Secret)
	assert.NoError(t, err)
	assert.Len(t, currentToken, 6)

	// Verify the token
	verifyReq := &twofa.VerifyTokenRequest{
		Secret: secretResp.Secret,
		Token:  currentToken,
	}

	valid, err := twofaService.VerifyToken(verifyReq)
	assert.NoError(t, err)
	assert.True(t, valid)
}

func TestTwoFAService_VerifyToken_Invalid(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Generate a secret first
	req := &twofa.GenerateSecretRequest{
		UserEmail: "<EMAIL>",
		UserID:    "123",
	}

	secretResp, err := twofaService.GenerateSecret(req)
	assert.NoError(t, err)

	// Verify with invalid token
	verifyReq := &twofa.VerifyTokenRequest{
		Secret: secretResp.Secret,
		Token:  "000000", // Invalid token
	}

	valid, err := twofaService.VerifyToken(verifyReq)
	assert.NoError(t, err)
	assert.False(t, valid)
}

func TestTwoFAService_GenerateBackupCodes(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	resp, err := twofaService.GenerateBackupCodes()

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Codes, 10)

	// Check format of backup codes
	for _, code := range resp.Codes {
		assert.NotEmpty(t, code)
		assert.Contains(t, code, "-") // Should have format XXXX-XXXX
	}
}

func TestTwoFAService_VerifyBackupCode(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Generate backup codes
	resp, err := twofaService.GenerateBackupCodes()
	assert.NoError(t, err)
	assert.NotEmpty(t, resp.Codes)

	// Test valid backup code
	validCode := resp.Codes[0]
	valid, index := twofaService.VerifyBackupCode(validCode, resp.Codes)
	assert.True(t, valid)
	assert.Equal(t, 0, index)

	// Test invalid backup code
	valid, index = twofaService.VerifyBackupCode("INVALID-CODE", resp.Codes)
	assert.False(t, valid)
	assert.Equal(t, -1, index)
}

func TestTwoFAService_RemoveUsedBackupCode(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Generate backup codes
	resp, err := twofaService.GenerateBackupCodes()
	assert.NoError(t, err)
	assert.Len(t, resp.Codes, 10)

	// Remove first code
	newCodes := twofaService.RemoveUsedBackupCode(resp.Codes, 0)
	assert.Len(t, newCodes, 9)
	assert.NotContains(t, newCodes, resp.Codes[0])

	// Test invalid index
	sameCodes := twofaService.RemoveUsedBackupCode(resp.Codes, -1)
	assert.Equal(t, resp.Codes, sameCodes)

	sameCodes = twofaService.RemoveUsedBackupCode(resp.Codes, 100)
	assert.Equal(t, resp.Codes, sameCodes)
}

func TestTwoFAService_ValidateSecret(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Test valid secret
	validSecret := "JBSWY3DPEHPK3PXP"
	err := twofaService.ValidateSecret(validSecret)
	assert.NoError(t, err)

	// Test empty secret
	err = twofaService.ValidateSecret("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "secret cannot be empty")

	// Test invalid secret format
	err = twofaService.ValidateSecret("invalid-secret!")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid secret format")
}

func TestTwoFAService_GenerateQRCode(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	secret := "JBSWY3DPEHPK3PXP"
	email := "<EMAIL>"

	qrCode, err := twofaService.GenerateQRCode(secret, email)

	assert.NoError(t, err)
	assert.NotNil(t, qrCode)
	assert.Greater(t, len(qrCode), 0)
}

func TestTwoFAService_VerifyTokenWithWindow(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	// Generate a secret first
	req := &twofa.GenerateSecretRequest{
		UserEmail: "<EMAIL>",
		UserID:    "123",
	}

	secretResp, err := twofaService.GenerateSecret(req)
	assert.NoError(t, err)

	// Get current token
	currentToken, err := twofaService.GetCurrentToken(secretResp.Secret)
	assert.NoError(t, err)

	// Verify with window
	valid, err := twofaService.VerifyTokenWithWindow(secretResp.Secret, currentToken, 1)
	assert.NoError(t, err)
	assert.True(t, valid)

	// Test invalid token
	valid, err = twofaService.VerifyTokenWithWindow(secretResp.Secret, "000000", 1)
	assert.NoError(t, err)
	assert.False(t, valid)
}

func TestTwoFAConfig(t *testing.T) {
	config := twofa.DefaultConfig()

	assert.NotNil(t, config)
	assert.Equal(t, "Social Content AI", config.Issuer)
	assert.Equal(t, 1, config.WindowSize)
	assert.Equal(t, 10, config.BackupCodes)
	assert.Equal(t, 32, config.SecretLength)
}

func TestTwoFAStats(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	twofaService := twofa.NewService(logger, "Test App")

	stats := twofaService.GetStats()

	assert.NotNil(t, stats)
	assert.Equal(t, 0, stats.TotalUsers)
	assert.Equal(t, 0, stats.UsersWithTwoFA)
	assert.Equal(t, 0.0, stats.TwoFAEnabledPercent)
}

func TestNewService(t *testing.T) {
	logger := logging.NewLogger(&logging.Config{Level: "debug"})
	issuer := "Test App"

	service := twofa.NewService(logger, issuer)

	assert.NotNil(t, service)
}

// Test request/response types
func TestTwoFATypes(t *testing.T) {
	// Test GenerateSecretRequest
	req := &twofa.GenerateSecretRequest{
		UserEmail: "<EMAIL>",
		UserID:    "123",
	}
	assert.Equal(t, "<EMAIL>", req.UserEmail)
	assert.Equal(t, "123", req.UserID)

	// Test VerifyTokenRequest
	verifyReq := &twofa.VerifyTokenRequest{
		Secret: "secret",
		Token:  "123456",
	}
	assert.Equal(t, "secret", verifyReq.Secret)
	assert.Equal(t, "123456", verifyReq.Token)

	// Test BackupCodesResponse
	backupResp := &twofa.BackupCodesResponse{
		Codes: []string{"CODE1", "CODE2"},
	}
	assert.Len(t, backupResp.Codes, 2)
}

// Test interface types
func TestTwoFAInterfaceTypes(t *testing.T) {
	// Test SetupTwoFARequest
	setupReq := &SetupTwoFARequest{
		UserID: 123,
	}
	assert.Equal(t, uint64(123), setupReq.UserID)

	// Test EnableTwoFARequest
	enableReq := &EnableTwoFARequest{
		UserID: 123,
		Token:  "123456",
	}
	assert.Equal(t, uint64(123), enableReq.UserID)
	assert.Equal(t, "123456", enableReq.Token)

	// Test VerifyTwoFARequest
	verifyReq := &VerifyTwoFARequest{
		UserID: 123,
		Token:  "123456",
	}
	assert.Equal(t, uint64(123), verifyReq.UserID)
	assert.Equal(t, "123456", verifyReq.Token)
}

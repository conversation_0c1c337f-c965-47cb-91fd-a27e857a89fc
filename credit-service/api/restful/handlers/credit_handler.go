package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/pkg-shared/logging"
)

// CreditHandler handles credit-related HTTP requests
type CreditHandler struct {
	creditUseCase credit.UseCase
	logger        logging.Logger
}

// NewCreditHandler creates a new credit handler
func NewCreditHandler(creditUseCase credit.UseCase, logger logging.Logger) *CreditHandler {
	return &CreditHandler{
		creditUseCase: creditUseCase,
		logger:        logger,
	}
}

// GetCreditBalance godoc
// @Summary Get user credit balance
// @Description Get current credit balance and plan information
// @Tags credits
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CreditBalanceResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/balance [get]
func (h *CreditHandler) GetCreditBalance(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	balance, err := h.creditUseCase.GetCreditBalance(c.Request.Context(), userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit balance")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_BALANCE_FAILED",
			Message: "Failed to get credit balance",
		})
		return
	}

	c.JSON(http.StatusOK, balance)
}

// GetCreditHistory godoc
// @Summary Get credit transaction history
// @Description Get paginated credit transaction history with filters
// @Tags credits
// @Produce json
// @Security BearerAuth
// @Param type query string false "Transaction type filter"
// @Param reference_type query string false "Reference type filter"
// @Param date_from query string false "Start date (YYYY-MM-DD)"
// @Param date_to query string false "End date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.CreditHistoryResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/history [get]
func (h *CreditHandler) GetCreditHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	req := &models.CreditHistoryRequest{
		UserID:        userID.(string),
		Type:          c.Query("type"),
		ReferenceType: c.Query("reference_type"),
		Page:          page,
		Limit:         limit,
		SortBy:        c.DefaultQuery("sort_by", "created_at"),
		SortOrder:     c.DefaultQuery("sort_order", "desc"),
	}

	// Parse date filters if provided
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		// TODO: Parse date_from
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		// TODO: Parse date_to
	}

	history, err := h.creditUseCase.GetCreditHistory(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit history")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_HISTORY_FAILED",
			Message: "Failed to get credit history",
		})
		return
	}

	c.JSON(http.StatusOK, history)
}

// ValidateCredits godoc
// @Summary Validate credit availability
// @Description Check if user has sufficient credits for an operation
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ValidateCreditsRequest true "Validate credits request"
// @Success 200 {object} models.CreditValidationResult
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/validate [post]
func (h *CreditHandler) ValidateCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.ValidateCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid validate credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	result, err := h.creditUseCase.ValidateCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate credits")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "VALIDATE_CREDITS_FAILED",
			Message: "Failed to validate credits",
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ReserveCredits godoc
// @Summary Reserve credits for an operation
// @Description Reserve credits temporarily for an operation
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ReserveCreditsRequest true "Reserve credits request"
// @Success 200 {object} models.CreditReservation
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/reserve [post]
func (h *CreditHandler) ReserveCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.ReserveCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid reserve credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	reservation, err := h.creditUseCase.ReserveCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to reserve credits")

		switch err.Error() {
		case "INSUFFICIENT_CREDITS":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INSUFFICIENT_CREDITS",
				Message: "Insufficient credits available",
			})
		case "MONTHLY_LIMIT_EXCEEDED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "MONTHLY_LIMIT_EXCEEDED",
				Message: "Monthly credit limit exceeded",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "RESERVE_CREDITS_FAILED",
				Message: "Failed to reserve credits",
			})
		}
		return
	}

	c.JSON(http.StatusOK, reservation)
}

// ConsumeCredits godoc
// @Summary Consume reserved or direct credits
// @Description Consume credits for a completed operation
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ConsumeCreditsRequest true "Consume credits request"
// @Success 200 {object} models.CreditBalanceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/consume [post]
func (h *CreditHandler) ConsumeCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.ConsumeCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid consume credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	balance, err := h.creditUseCase.ConsumeCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to consume credits")

		switch err.Error() {
		case "INSUFFICIENT_CREDITS":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INSUFFICIENT_CREDITS",
				Message: "Insufficient credits available",
			})
		case "RESERVATION_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "RESERVATION_NOT_FOUND",
				Message: "Credit reservation not found or expired",
			})
		case "RESERVATION_EXPIRED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "RESERVATION_EXPIRED",
				Message: "Credit reservation has expired",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "CONSUME_CREDITS_FAILED",
				Message: "Failed to consume credits",
			})
		}
		return
	}

	c.JSON(http.StatusOK, balance)
}

// RefundCredits godoc
// @Summary Refund credits
// @Description Refund credits for a cancelled or failed operation
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.RefundCreditsRequest true "Refund credits request"
// @Success 200 {object} models.CreditBalanceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/refund [post]
func (h *CreditHandler) RefundCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.RefundCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid refund credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	balance, err := h.creditUseCase.RefundCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to refund credits")

		switch err.Error() {
		case "TRANSACTION_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_FOUND",
				Message: "Original transaction not found",
			})
		case "ALREADY_REFUNDED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "ALREADY_REFUNDED",
				Message: "Transaction has already been refunded",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REFUND_CREDITS_FAILED",
				Message: "Failed to refund credits",
			})
		}
		return
	}

	c.JSON(http.StatusOK, balance)
}

// TransferCredits godoc
// @Summary Transfer credits
// @Description Transfer credits between users
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.TransferCreditsRequest true "Transfer credits request"
// @Success 200 {object} models.CreditBalanceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/transfer [post]
func (h *CreditHandler) TransferCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.TransferCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid transfer credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.FromUserID = userID.(string)

	balance, err := h.creditUseCase.TransferCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to transfer credits")

		switch err.Error() {
		case "INSUFFICIENT_CREDITS":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INSUFFICIENT_CREDITS",
				Message: "Insufficient credits for transfer",
			})
		case "USER_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "Target user not found",
			})
		case "SELF_TRANSFER":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "SELF_TRANSFER",
				Message: "Cannot transfer credits to yourself",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "TRANSFER_CREDITS_FAILED",
				Message: "Failed to transfer credits",
			})
		}
		return
	}

	c.JSON(http.StatusOK, balance)
}

// AdjustCredits godoc
// @Summary Adjust credits
// @Description Adjust user credits (admin only)
// @Tags credits
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.AdjustCreditsRequest true "Adjust credits request"
// @Success 200 {object} models.CreditBalanceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/adjust [post]
func (h *CreditHandler) AdjustCredits(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.AdjustCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid adjust credits request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	balance, err := h.creditUseCase.AdjustCredits(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to adjust credits")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "ADJUST_CREDITS_FAILED",
			Message: "Failed to adjust credits",
		})
		return
	}

	c.JSON(http.StatusOK, balance)
}

// GetCreditStats godoc
// @Summary Get credit statistics
// @Description Get credit usage statistics
// @Tags credits
// @Produce json
// @Security BearerAuth
// @Param period query string false "Period (day, week, month, year)" default("month")
// @Param date_from query string false "Start date (YYYY-MM-DD)"
// @Param date_to query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} models.CreditStats
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/credits/stats [get]
func (h *CreditHandler) GetCreditStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	req := &models.CreditStatsRequest{
		UserID: userID.(string),
		Period: c.DefaultQuery("period", "month"),
	}

	// Parse date filters if provided
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		// TODO: Parse date_from
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		// TODO: Parse date_to
	}

	stats, err := h.creditUseCase.GetCreditStats(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit stats")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_STATS_FAILED",
			Message: "Failed to get credit statistics",
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

/**
 * OAuth Utilities for Frontend Integration
 * 
 * This file provides utility functions for integrating with the Integration Service OAuth flow.
 * Include this in your frontend application to easily generate OAuth URLs and handle callbacks.
 */

/**
 * Generates a base64 encoded state parameter for OAuth flow
 * @param {string} userId - User UUID
 * @param {string} workspaceId - Workspace UUID (optional)
 * @param {string} redirectUrl - Custom redirect URL after OAuth completion (optional)
 * @returns {string} Base64 encoded state parameter
 */
function generateOAuthState(userId, workspaceId = null, redirectUrl = null) {
  const state = {
    user_id: userId
  };
  
  if (workspaceId) {
    state.workspace_id = workspaceId;
  }
  
  if (redirectUrl) {
    state.redirect_url = redirectUrl;
  }
  
  return btoa(JSON.stringify(state));
}

/**
 * Parses OAuth state parameter
 * @param {string} encodedState - Base64 encoded state parameter
 * @returns {Object} Parsed state object
 */
function parseOAuthState(encodedState) {
  try {
    const decoded = atob(encodedState);
    return JSON.parse(decoded);
  } catch (error) {
    throw new Error('Invalid state parameter: ' + error.message);
  }
}

/**
 * Generates OAuth URL for a specific platform
 * @param {Object} config - Configuration object
 * @param {string} config.platform - Platform name (facebook, twitter, instagram, linkedin, youtube, tiktok)
 * @param {string} config.userId - User UUID
 * @param {string} config.workspaceId - Workspace UUID (optional)
 * @param {string} config.redirectUrl - Custom redirect URL (optional)
 * @param {Array<string>} config.permissions - Requested permissions (optional)
 * @param {string} config.integrationServiceUrl - Integration service base URL
 * @returns {Promise<string>} OAuth URL
 */
async function generateOAuthURL(config) {
  const {
    platform,
    userId,
    workspaceId,
    redirectUrl,
    permissions = [],
    integrationServiceUrl = 'http://localhost:8085'
  } = config;
  
  if (!platform || !userId) {
    throw new Error('Platform and userId are required');
  }
  
  const state = generateOAuthState(userId, workspaceId, redirectUrl);
  const callbackUrl = `${integrationServiceUrl}/oauth/${platform}/callback`;
  
  // For gRPC-Web or REST API call
  const requestBody = {
    user_id: userId,
    platform: platform,
    redirect_uri: callbackUrl,
    requested_permissions: permissions,
    state: state
  };
  
  try {
    // Example using fetch (adjust based on your gRPC-Web setup)
    const response = await fetch(`${integrationServiceUrl}/api/v1/oauth/url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}` // Implement getAuthToken()
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data.auth_url;
  } catch (error) {
    throw new Error('Failed to generate OAuth URL: ' + error.message);
  }
}

/**
 * Initiates OAuth flow by redirecting to platform
 * @param {Object} config - Same as generateOAuthURL config
 */
async function initiateOAuth(config) {
  try {
    const oauthUrl = await generateOAuthURL(config);
    window.location.href = oauthUrl;
  } catch (error) {
    console.error('Failed to initiate OAuth:', error);
    throw error;
  }
}

/**
 * Parses OAuth callback URL parameters
 * @param {string} url - Current URL or search params (optional, defaults to window.location.search)
 * @returns {Object} Parsed callback parameters
 */
function parseOAuthCallback(url = window.location.search) {
  const params = new URLSearchParams(url);
  
  return {
    success: params.get('status') === 'success',
    error: params.get('error'),
    errorMessage: params.get('message'),
    platform: params.get('platform'),
    accountId: params.get('account_id'),
    code: params.get('code'),
    state: params.get('state')
  };
}

/**
 * Platform-specific OAuth configurations
 */
const PLATFORM_CONFIGS = {
  facebook: {
    name: 'Facebook',
    defaultPermissions: ['pages_manage_posts', 'pages_read_engagement'],
    color: '#1877F2'
  },
  twitter: {
    name: 'Twitter/X',
    defaultPermissions: ['tweet.read', 'tweet.write', 'users.read'],
    color: '#1DA1F2'
  },
  instagram: {
    name: 'Instagram',
    defaultPermissions: ['instagram_basic', 'instagram_content_publish'],
    color: '#E4405F'
  },
  linkedin: {
    name: 'LinkedIn',
    defaultPermissions: ['w_member_social', 'r_liteprofile'],
    color: '#0A66C2'
  },
  youtube: {
    name: 'YouTube',
    defaultPermissions: ['https://www.googleapis.com/auth/youtube.upload'],
    color: '#FF0000'
  },
  tiktok: {
    name: 'TikTok',
    defaultPermissions: ['user.info.basic', 'video.publish'],
    color: '#000000'
  }
};

/**
 * Gets platform configuration
 * @param {string} platform - Platform name
 * @returns {Object} Platform configuration
 */
function getPlatformConfig(platform) {
  return PLATFORM_CONFIGS[platform] || null;
}

/**
 * React Hook for OAuth integration
 * @param {Object} config - Configuration object
 * @returns {Object} Hook return object
 */
function useOAuth(config = {}) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  
  const initiateOAuthFlow = React.useCallback(async (platform, options = {}) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await initiateOAuth({
        platform,
        ...config,
        ...options
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [config]);
  
  const parseCallback = React.useCallback(() => {
    return parseOAuthCallback();
  }, []);
  
  return {
    initiateOAuth: initiateOAuthFlow,
    parseCallback,
    isLoading,
    error,
    platforms: PLATFORM_CONFIGS
  };
}

/**
 * Vue.js Composable for OAuth integration
 * @param {Object} config - Configuration object
 * @returns {Object} Composable return object
 */
function useOAuthVue(config = {}) {
  const isLoading = Vue.ref(false);
  const error = Vue.ref(null);
  
  const initiateOAuthFlow = async (platform, options = {}) => {
    isLoading.value = true;
    error.value = null;
    
    try {
      await initiateOAuth({
        platform,
        ...config,
        ...options
      });
    } catch (err) {
      error.value = err.message;
    } finally {
      isLoading.value = false;
    }
  };
  
  const parseCallback = () => {
    return parseOAuthCallback();
  };
  
  return {
    initiateOAuth: initiateOAuthFlow,
    parseCallback,
    isLoading: Vue.readonly(isLoading),
    error: Vue.readonly(error),
    platforms: PLATFORM_CONFIGS
  };
}

/**
 * Example usage in vanilla JavaScript
 */
function exampleUsage() {
  // Generate OAuth URL
  const config = {
    platform: 'facebook',
    userId: '123e4567-e89b-12d3-a456-************',
    workspaceId: '456e7890-e89b-12d3-a456-************',
    redirectUrl: 'https://myapp.com/oauth/success',
    permissions: ['pages_manage_posts', 'pages_read_engagement'],
    integrationServiceUrl: 'http://localhost:8085'
  };
  
  // Initiate OAuth flow
  initiateOAuth(config).catch(console.error);
  
  // Parse callback (on callback page)
  const callbackResult = parseOAuthCallback();
  if (callbackResult.success) {
    console.log('Account connected:', callbackResult.platform, callbackResult.accountId);
  } else if (callbackResult.error) {
    console.error('OAuth error:', callbackResult.error, callbackResult.errorMessage);
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateOAuthState,
    parseOAuthState,
    generateOAuthURL,
    initiateOAuth,
    parseOAuthCallback,
    getPlatformConfig,
    PLATFORM_CONFIGS,
    useOAuth,
    useOAuthVue
  };
}

// Global exports for browser
if (typeof window !== 'undefined') {
  window.OAuthUtils = {
    generateOAuthState,
    parseOAuthState,
    generateOAuthURL,
    initiateOAuth,
    parseOAuthCallback,
    getPlatformConfig,
    PLATFORM_CONFIGS,
    useOAuth,
    useOAuthVue
  };
}

/**
 * Placeholder for getting auth token
 * Implement this based on your authentication system
 */
function getAuthToken() {
  // Return JWT token from localStorage, cookies, or state management
  return localStorage.getItem('auth_token') || '';
}

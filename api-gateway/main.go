package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/social-content-ai/api-gateway/internal/handlers"
	"github.com/social-content-ai/api-gateway/internal/middleware"
	"github.com/social-content-ai/api-gateway/internal/services"
	"github.com/social-content-ai/pkg-shared/config"
	"github.com/social-content-ai/pkg-shared/logging"
)

const (
	serviceName = "api-gateway"
	version     = "1.0.0"
)

// @title Social Content AI API Gateway
// @version 1.0
// @description API Gateway for Social Content AI platform
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.socialcontentai.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithField("service", serviceName).Info("Starting API Gateway")

	// Initialize services
	serviceClients, err := services.NewServiceClients(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize service clients")
	}
	defer serviceClients.Close()

	// Initialize handlers
	handlers := handlers.NewHandlers(serviceClients, logger)

	// Setup Gin router
	if cfg.Server.Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Global middleware
	router.Use(gin.Recovery())
	router.Use(middleware.RequestLogger(logger))
	router.Use(middleware.CORS())
	router.Use(middleware.RateLimit())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": serviceName,
			"version": version,
			"time":    time.Now().UTC(),
		})
	})

	// Metrics endpoint
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes (no auth required)
		auth := v1.Group("/auth")
		{
			auth.POST("/login", handlers.Login)
			auth.POST("/register", handlers.Register)
			auth.POST("/refresh", handlers.RefreshToken)
			auth.POST("/logout", handlers.Logout)
			auth.POST("/forgot-password", handlers.ForgotPassword)
			auth.POST("/reset-password", handlers.ResetPassword)
		}

		// Protected routes (require authentication)
		protected := v1.Group("/")
		protected.Use(middleware.AuthRequired(serviceClients.UserService, logger))
		{
			// User management
			users := protected.Group("/users")
			{
				users.GET("/me", handlers.GetCurrentUser)
				users.PUT("/me", handlers.UpdateCurrentUser)
				users.POST("/me/avatar", handlers.UploadAvatar)
				users.GET("/me/credits", handlers.GetUserCredits)
				users.GET("/me/transactions", handlers.GetUserTransactions)
			}

			// Content generation
			content := protected.Group("/content")
			{
				content.POST("/generate", handlers.GenerateContent)
				content.POST("/improve", handlers.ImproveContent)
				content.GET("/generations", handlers.ListGenerations)
				content.GET("/generations/:id", handlers.GetGeneration)
				content.DELETE("/generations/:id", handlers.DeleteGeneration)
			}

			// Post management
			posts := protected.Group("/posts")
			{
				posts.POST("/", handlers.CreatePost)
				posts.GET("/", handlers.ListPosts)
				posts.GET("/:id", handlers.GetPost)
				posts.PUT("/:id", handlers.UpdatePost)
				posts.DELETE("/:id", handlers.DeletePost)
				posts.POST("/:id/schedule", handlers.SchedulePost)
				posts.POST("/:id/publish", handlers.PublishPost)
			}

			// Template management
			templates := protected.Group("/templates")
			{
				templates.POST("/", handlers.CreateTemplate)
				templates.GET("/", handlers.ListTemplates)
				templates.GET("/:id", handlers.GetTemplate)
				templates.PUT("/:id", handlers.UpdateTemplate)
				templates.DELETE("/:id", handlers.DeleteTemplate)
				templates.POST("/:id/use", handlers.UseTemplate)
			}

			// Workspace management
			workspaces := protected.Group("/workspaces")
			{
				workspaces.POST("/", handlers.CreateWorkspace)
				workspaces.GET("/", handlers.ListWorkspaces)
				workspaces.GET("/:id", handlers.GetWorkspace)
				workspaces.PUT("/:id", handlers.UpdateWorkspace)
				workspaces.DELETE("/:id", handlers.DeleteWorkspace)
			}

			// Credit management
			credits := protected.Group("/credits")
			{
				credits.GET("/", handlers.GetUserCredits)
				credits.GET("/transactions", handlers.GetUserTransactions)
				credits.GET("/stats", handlers.GetCreditStats)
			}

			// Integration management
			integrations := protected.Group("/integrations")
			{
				integrations.GET("/", handlers.ListIntegrations)
				integrations.POST("/:platform/connect", handlers.ConnectPlatform)
				integrations.DELETE("/:platform/disconnect", handlers.DisconnectPlatform)
				integrations.GET("/:platform/status", handlers.GetPlatformStatus)
			}

			// Analytics
			analytics := protected.Group("/analytics")
			{
				analytics.GET("/dashboard", handlers.GetDashboard)
				analytics.GET("/posts/:id/analytics", handlers.GetPostAnalytics)
				analytics.GET("/performance", handlers.GetPerformanceReport)
			}
		}

		// Admin routes (require admin role)
		admin := v1.Group("/admin")
		admin.Use(middleware.AuthRequired(serviceClients.UserService, logger))
		admin.Use(middleware.AdminRequired())
		{
			admin.GET("/users", handlers.ListAllUsers)
			admin.GET("/users/:id", handlers.GetUserByID)
			admin.PUT("/users/:id", handlers.UpdateUserByID)
			admin.DELETE("/users/:id", handlers.DeleteUserByID)
			admin.GET("/analytics/system", handlers.GetSystemAnalytics)
			admin.GET("/credits/report", handlers.GetCreditReport)
		}
	}

	// Swagger documentation
	if cfg.Server.Env != "production" {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// Start server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in goroutine
	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start HTTP server")
		}
	}()

	// Wait for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	logger.Info("Shutting down API Gateway")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown server gracefully")
	}

	logger.Info("API Gateway stopped")
}

package twofa

import (
	"crypto/rand"
	"encoding/base32"
	"fmt"
	"image/png"
	"strings"
	"time"

	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service provides Two-Factor Authentication functionality
type Service struct {
	logger logging.Logger
	issuer string
}

// NewService creates a new 2FA service
func NewService(logger logging.Logger, issuer string) *Service {
	return &Service{
		logger: logger,
		issuer: issuer,
	}
}

// GenerateSecretRequest represents a request to generate 2FA secret
type GenerateSecretRequest struct {
	UserEmail string `json:"user_email" validate:"required,email"`
	UserID    string `json:"user_id" validate:"required"`
}

// GenerateSecretResponse represents the response with 2FA secret
type GenerateSecretResponse struct {
	Secret    string `json:"secret"`
	QRCodeURL string `json:"qr_code_url"`
	QRCode    []byte `json:"qr_code,omitempty"`
}

// VerifyTokenRequest represents a request to verify 2FA token
type VerifyTokenRequest struct {
	Secret string `json:"secret" validate:"required"`
	Token  string `json:"token" validate:"required,len=6"`
}

// BackupCodesResponse represents backup codes
type BackupCodesResponse struct {
	Codes []string `json:"codes"`
}

// GenerateSecret generates a new TOTP secret for a user
func (s *Service) GenerateSecret(req *GenerateSecretRequest) (*GenerateSecretResponse, error) {
	// Generate TOTP key
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      s.issuer,
		AccountName: req.UserEmail,
		SecretSize:  32,
	})
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate TOTP key")
		return nil, fmt.Errorf("failed to generate TOTP key: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"user_email": req.UserEmail,
	}).Info("Generated 2FA secret")

	return &GenerateSecretResponse{
		Secret:    key.Secret(),
		QRCodeURL: key.URL(),
	}, nil
}

// GenerateQRCode generates QR code image for the secret
func (s *Service) GenerateQRCode(secret, userEmail string) ([]byte, error) {
	// Create TOTP key from secret
	key, err := otp.NewKeyFromURL(fmt.Sprintf("otpauth://totp/%s:%s?secret=%s&issuer=%s",
		s.issuer, userEmail, secret, s.issuer))
	if err != nil {
		return nil, fmt.Errorf("failed to create key from secret: %w", err)
	}

	// Generate QR code
	img, err := key.Image(256, 256)
	if err != nil {
		return nil, fmt.Errorf("failed to generate QR code image: %w", err)
	}

	// Convert to PNG bytes
	var buf []byte
	writer := &bytesWriter{data: &buf}
	if err := png.Encode(writer, img); err != nil {
		return nil, fmt.Errorf("failed to encode QR code as PNG: %w", err)
	}

	return buf, nil
}

// VerifyToken verifies a TOTP token against a secret
func (s *Service) VerifyToken(req *VerifyTokenRequest) (bool, error) {
	// Validate token format
	if len(req.Token) != 6 {
		return false, fmt.Errorf("token must be 6 digits")
	}

	// Verify TOTP token
	valid := totp.Validate(req.Token, req.Secret)

	s.logger.WithFields(map[string]interface{}{
		"token_valid": valid,
		"token":       req.Token[:2] + "****", // Log only first 2 digits for security
	}).Debug("Verified 2FA token")

	return valid, nil
}

// VerifyTokenWithWindow verifies a TOTP token with time window tolerance
func (s *Service) VerifyTokenWithWindow(secret, token string, window int) (bool, error) {
	if len(token) != 6 {
		return false, fmt.Errorf("token must be 6 digits")
	}

	// Verify with time window (allows for clock skew)
	valid, err := totp.ValidateCustom(token, secret, time.Now(), totp.ValidateOpts{
		Period:    30,
		Skew:      uint(window),
		Digits:    otp.DigitsSix,
		Algorithm: otp.AlgorithmSHA1,
	})
	if err != nil {
		return false, fmt.Errorf("failed to validate token: %w", err)
	}

	return valid, nil
}

// GenerateBackupCodes generates backup codes for 2FA recovery
func (s *Service) GenerateBackupCodes() (*BackupCodesResponse, error) {
	codes := make([]string, 10) // Generate 10 backup codes

	for i := 0; i < 10; i++ {
		code, err := s.generateBackupCode()
		if err != nil {
			return nil, fmt.Errorf("failed to generate backup code: %w", err)
		}
		codes[i] = code
	}

	s.logger.Info("Generated backup codes for 2FA")

	return &BackupCodesResponse{
		Codes: codes,
	}, nil
}

// VerifyBackupCode verifies a backup code
func (s *Service) VerifyBackupCode(code string, backupCodes []string) (bool, int) {
	code = strings.TrimSpace(strings.ToUpper(code))

	for i, backupCode := range backupCodes {
		if backupCode == code {
			return true, i
		}
	}

	return false, -1
}

// RemoveUsedBackupCode removes a used backup code from the list
func (s *Service) RemoveUsedBackupCode(backupCodes []string, index int) []string {
	if index < 0 || index >= len(backupCodes) {
		return backupCodes
	}

	// Remove the used code
	newCodes := make([]string, 0, len(backupCodes)-1)
	for i, code := range backupCodes {
		if i != index {
			newCodes = append(newCodes, code)
		}
	}

	return newCodes
}

// ValidateSecret validates if a secret is properly formatted
func (s *Service) ValidateSecret(secret string) error {
	if secret == "" {
		return fmt.Errorf("secret cannot be empty")
	}

	// Decode base32 secret to validate format
	_, err := base32.StdEncoding.DecodeString(secret)
	if err != nil {
		return fmt.Errorf("invalid secret format: %w", err)
	}

	return nil
}

// generateBackupCode generates a single backup code
func (s *Service) generateBackupCode() (string, error) {
	// Generate 8 random bytes
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	// Convert to base32 and format
	code := base32.StdEncoding.EncodeToString(bytes)
	code = strings.TrimRight(code, "=") // Remove padding

	// Format as XXXX-XXXX
	if len(code) >= 8 {
		code = code[:4] + "-" + code[4:8]
	}

	return code, nil
}

// GetCurrentToken generates current TOTP token for testing purposes
func (s *Service) GetCurrentToken(secret string) (string, error) {
	token, err := totp.GenerateCode(secret, time.Now())
	if err != nil {
		return "", fmt.Errorf("failed to generate current token: %w", err)
	}
	return token, nil
}

// bytesWriter implements io.Writer for []byte
type bytesWriter struct {
	data *[]byte
}

func (w *bytesWriter) Write(p []byte) (int, error) {
	*w.data = append(*w.data, p...)
	return len(p), nil
}

// TwoFAConfig represents 2FA configuration
type TwoFAConfig struct {
	Issuer       string `json:"issuer"`
	WindowSize   int    `json:"window_size"`   // Time window for token validation (in 30-second periods)
	BackupCodes  int    `json:"backup_codes"`  // Number of backup codes to generate
	SecretLength int    `json:"secret_length"` // Length of the secret in bytes
}

// DefaultConfig returns default 2FA configuration
func DefaultConfig() *TwoFAConfig {
	return &TwoFAConfig{
		Issuer:       "Social Content AI",
		WindowSize:   1, // Allow 1 period before/after current time
		BackupCodes:  10,
		SecretLength: 32,
	}
}

// Stats represents 2FA usage statistics
type Stats struct {
	TotalUsers          int     `json:"total_users"`
	UsersWithTwoFA      int     `json:"users_with_2fa"`
	TwoFAEnabledPercent float64 `json:"2fa_enabled_percent"`
	RecentVerifications int     `json:"recent_verifications"`
	FailedVerifications int     `json:"failed_verifications"`
}

// GetStats returns 2FA usage statistics (placeholder implementation)
func (s *Service) GetStats() *Stats {
	// This would typically query the database for actual statistics
	return &Stats{
		TotalUsers:          0,
		UsersWithTwoFA:      0,
		TwoFAEnabledPercent: 0.0,
		RecentVerifications: 0,
		FailedVerifications: 0,
	}
}

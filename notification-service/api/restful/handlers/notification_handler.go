package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/pkg-shared/logging"
)

// NotificationHandler handles notification-related HTTP requests
type NotificationHandler struct {
	notificationUC notification.UseCase
	logger         logging.Logger
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationUC notification.UseCase, logger logging.Logger) *NotificationHandler {
	return &NotificationHandler{
		notificationUC: notificationUC,
		logger:         logger,
	}
}

// SendNotification handles POST /api/v1/notifications
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	var req notification.SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Override user ID from token
	req.UserID = userID.(string)

	response, err := h.notificationUC.SendNotification(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// SendTemplatedNotification handles POST /api/v1/notifications/templated
func (h *NotificationHandler) SendTemplatedNotification(c *gin.Context) {
	var req notification.SendTemplatedNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Override user ID from token
	req.UserID = userID.(string)

	response, err := h.notificationUC.SendTemplatedNotification(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send templated notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// SendBulkNotification handles POST /api/v1/notifications/bulk
func (h *NotificationHandler) SendBulkNotification(c *gin.Context) {
	h.logger.Info("Processing bulk notification request")

	var req notification.BulkNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid bulk notification request")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate request
	if len(req.UserIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_ids cannot be empty"})
		return
	}
	if req.Type == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "type is required"})
		return
	}
	if req.Title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title is required"})
		return
	}
	if req.Message == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "message is required"})
		return
	}
	if len(req.Channels) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "channels cannot be empty"})
		return
	}
	if req.Priority == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "priority is required"})
		return
	}

	response, err := h.notificationUC.SendBulkNotification(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send bulk notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// ListNotifications handles GET /api/v1/notifications
func (h *NotificationHandler) ListNotifications(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	notificationType := c.Query("type")
	sortBy := c.DefaultQuery("sort_by", "created_at")

	req := &notification.ListNotificationsRequest{
		UserID: userID.(string),
		Status: status,
		Type:   notificationType,
		Page:   page,
		Limit:  limit,
		SortBy: sortBy,
	}

	response, err := h.notificationUC.ListNotifications(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list notifications")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetNotification handles GET /api/v1/notifications/:id
func (h *NotificationHandler) GetNotification(c *gin.Context) {
	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	response, err := h.notificationUC.GetNotification(c.Request.Context(), notificationID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// MarkAsRead handles PUT /api/v1/notifications/:id/read
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	req := &notification.MarkAsReadRequest{
		UserID:          userID.(string),
		NotificationIDs: []string{notificationID},
	}

	err := h.notificationUC.MarkAsRead(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to mark notification as read")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification marked as read"})
}

// MarkAllAsRead handles PUT /api/v1/notifications/read-all
func (h *NotificationHandler) MarkAllAsRead(c *gin.Context) {
	h.logger.Info("Marking all notifications as read")

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.notificationUC.MarkAllAsRead(c.Request.Context(), userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to mark all notifications as read")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "All notifications marked as read"})
}

// DeleteNotification handles DELETE /api/v1/notifications/:id
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	h.logger.Info("Deleting notification")

	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "notification ID is required"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.notificationUC.DeleteNotification(c.Request.Context(), notificationID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete notification")
		if err.Error() == "notification not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted successfully"})
}

// GetDeliveryStatus handles GET /api/v1/notifications/:id/delivery-status
func (h *NotificationHandler) GetDeliveryStatus(c *gin.Context) {
	notificationID := c.Param("id")
	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	req := &notification.GetDeliveryStatusRequest{
		NotificationID: notificationID,
	}

	response, err := h.notificationUC.GetDeliveryStatus(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get delivery status")
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetDeliveryStats handles GET /api/v1/notifications/delivery-stats
func (h *NotificationHandler) GetDeliveryStats(c *gin.Context) {
	// TODO: Implement GetDeliveryStats in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetNotificationAnalytics handles GET /api/v1/notifications/analytics
func (h *NotificationHandler) GetNotificationAnalytics(c *gin.Context) {
	// TODO: Implement GetNotificationAnalytics in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// Admin handlers

// SendSystemNotification handles POST /api/v1/admin/notifications/system
func (h *NotificationHandler) SendSystemNotification(c *gin.Context) {
	// TODO: Implement SendSystemNotification in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// BroadcastNotification handles POST /api/v1/admin/notifications/broadcast
func (h *NotificationHandler) BroadcastNotification(c *gin.Context) {
	// TODO: Implement BroadcastNotification in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetSystemAnalytics handles GET /api/v1/admin/analytics/overview
func (h *NotificationHandler) GetSystemAnalytics(c *gin.Context) {
	// TODO: Implement GetSystemAnalytics in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetDeliveryAnalytics handles GET /api/v1/admin/analytics/delivery
func (h *NotificationHandler) GetDeliveryAnalytics(c *gin.Context) {
	// TODO: Implement GetDeliveryAnalytics in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetChannelAnalytics handles GET /api/v1/admin/analytics/channels
func (h *NotificationHandler) GetChannelAnalytics(c *gin.Context) {
	// TODO: Implement GetChannelAnalytics in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetSystemConfig handles GET /api/v1/admin/config
func (h *NotificationHandler) GetSystemConfig(c *gin.Context) {
	// TODO: Implement GetSystemConfig in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// UpdateSystemConfig handles PUT /api/v1/admin/config
func (h *NotificationHandler) UpdateSystemConfig(c *gin.Context) {
	// TODO: Implement UpdateSystemConfig in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// GetQueueStatus handles GET /api/v1/admin/queues/status
func (h *NotificationHandler) GetQueueStatus(c *gin.Context) {
	// TODO: Implement GetQueueStatus in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// RetryFailedNotifications handles POST /api/v1/admin/queues/retry
func (h *NotificationHandler) RetryFailedNotifications(c *gin.Context) {
	// TODO: Implement RetryFailedNotifications in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// ClearQueues handles POST /api/v1/admin/queues/clear
func (h *NotificationHandler) ClearQueues(c *gin.Context) {
	// TODO: Implement ClearQueues in usecase
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Method not implemented yet"})
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/order"
)

// OrderCreate is the builder for creating a Order entity.
type OrderCreate struct {
	config
	mutation *OrderMutation
	hooks    []Hook
}

// SetOrderNumber sets the "order_number" field.
func (oc *OrderCreate) SetOrderNumber(s string) *OrderCreate {
	oc.mutation.SetOrderNumber(s)
	return oc
}

// SetUserID sets the "user_id" field.
func (oc *OrderCreate) SetUserID(u uuid.UUID) *OrderCreate {
	oc.mutation.SetUserID(u)
	return oc
}

// SetPackageID sets the "package_id" field.
func (oc *OrderCreate) SetPackageID(u uuid.UUID) *OrderCreate {
	oc.mutation.SetPackageID(u)
	return oc
}

// SetNillablePackageID sets the "package_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillablePackageID(u *uuid.UUID) *OrderCreate {
	if u != nil {
		oc.SetPackageID(*u)
	}
	return oc
}

// SetPlanID sets the "plan_id" field.
func (oc *OrderCreate) SetPlanID(u uuid.UUID) *OrderCreate {
	oc.mutation.SetPlanID(u)
	return oc
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillablePlanID(u *uuid.UUID) *OrderCreate {
	if u != nil {
		oc.SetPlanID(*u)
	}
	return oc
}

// SetType sets the "type" field.
func (oc *OrderCreate) SetType(o order.Type) *OrderCreate {
	oc.mutation.SetType(o)
	return oc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (oc *OrderCreate) SetNillableType(o *order.Type) *OrderCreate {
	if o != nil {
		oc.SetType(*o)
	}
	return oc
}

// SetAmount sets the "amount" field.
func (oc *OrderCreate) SetAmount(i int64) *OrderCreate {
	oc.mutation.SetAmount(i)
	return oc
}

// SetCurrency sets the "currency" field.
func (oc *OrderCreate) SetCurrency(s string) *OrderCreate {
	oc.mutation.SetCurrency(s)
	return oc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCurrency(s *string) *OrderCreate {
	if s != nil {
		oc.SetCurrency(*s)
	}
	return oc
}

// SetStatus sets the "status" field.
func (oc *OrderCreate) SetStatus(o order.Status) *OrderCreate {
	oc.mutation.SetStatus(o)
	return oc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (oc *OrderCreate) SetNillableStatus(o *order.Status) *OrderCreate {
	if o != nil {
		oc.SetStatus(*o)
	}
	return oc
}

// SetPaymentMethod sets the "payment_method" field.
func (oc *OrderCreate) SetPaymentMethod(s string) *OrderCreate {
	oc.mutation.SetPaymentMethod(s)
	return oc
}

// SetPaymentID sets the "payment_id" field.
func (oc *OrderCreate) SetPaymentID(s string) *OrderCreate {
	oc.mutation.SetPaymentID(s)
	return oc
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillablePaymentID(s *string) *OrderCreate {
	if s != nil {
		oc.SetPaymentID(*s)
	}
	return oc
}

// SetInvoiceID sets the "invoice_id" field.
func (oc *OrderCreate) SetInvoiceID(s string) *OrderCreate {
	oc.mutation.SetInvoiceID(s)
	return oc
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillableInvoiceID(s *string) *OrderCreate {
	if s != nil {
		oc.SetInvoiceID(*s)
	}
	return oc
}

// SetCredits sets the "credits" field.
func (oc *OrderCreate) SetCredits(i int) *OrderCreate {
	oc.mutation.SetCredits(i)
	return oc
}

// SetNillableCredits sets the "credits" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCredits(i *int) *OrderCreate {
	if i != nil {
		oc.SetCredits(*i)
	}
	return oc
}

// SetBillingAddress sets the "billing_address" field.
func (oc *OrderCreate) SetBillingAddress(m map[string]interface{}) *OrderCreate {
	oc.mutation.SetBillingAddress(m)
	return oc
}

// SetItems sets the "items" field.
func (oc *OrderCreate) SetItems(m []map[string]interface{}) *OrderCreate {
	oc.mutation.SetItems(m)
	return oc
}

// SetMetadata sets the "metadata" field.
func (oc *OrderCreate) SetMetadata(m map[string]interface{}) *OrderCreate {
	oc.mutation.SetMetadata(m)
	return oc
}

// SetProcessedAt sets the "processed_at" field.
func (oc *OrderCreate) SetProcessedAt(t time.Time) *OrderCreate {
	oc.mutation.SetProcessedAt(t)
	return oc
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableProcessedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetProcessedAt(*t)
	}
	return oc
}

// SetExpiresAt sets the "expires_at" field.
func (oc *OrderCreate) SetExpiresAt(t time.Time) *OrderCreate {
	oc.mutation.SetExpiresAt(t)
	return oc
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableExpiresAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetExpiresAt(*t)
	}
	return oc
}

// SetFailureReason sets the "failure_reason" field.
func (oc *OrderCreate) SetFailureReason(s string) *OrderCreate {
	oc.mutation.SetFailureReason(s)
	return oc
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (oc *OrderCreate) SetNillableFailureReason(s *string) *OrderCreate {
	if s != nil {
		oc.SetFailureReason(*s)
	}
	return oc
}

// SetExternalOrderID sets the "external_order_id" field.
func (oc *OrderCreate) SetExternalOrderID(s string) *OrderCreate {
	oc.mutation.SetExternalOrderID(s)
	return oc
}

// SetNillableExternalOrderID sets the "external_order_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillableExternalOrderID(s *string) *OrderCreate {
	if s != nil {
		oc.SetExternalOrderID(*s)
	}
	return oc
}

// SetCreatedAt sets the "created_at" field.
func (oc *OrderCreate) SetCreatedAt(t time.Time) *OrderCreate {
	oc.mutation.SetCreatedAt(t)
	return oc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCreatedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetCreatedAt(*t)
	}
	return oc
}

// SetUpdatedAt sets the "updated_at" field.
func (oc *OrderCreate) SetUpdatedAt(t time.Time) *OrderCreate {
	oc.mutation.SetUpdatedAt(t)
	return oc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableUpdatedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetUpdatedAt(*t)
	}
	return oc
}

// SetID sets the "id" field.
func (oc *OrderCreate) SetID(u uuid.UUID) *OrderCreate {
	oc.mutation.SetID(u)
	return oc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (oc *OrderCreate) SetNillableID(u *uuid.UUID) *OrderCreate {
	if u != nil {
		oc.SetID(*u)
	}
	return oc
}

// Mutation returns the OrderMutation object of the builder.
func (oc *OrderCreate) Mutation() *OrderMutation {
	return oc.mutation
}

// Save creates the Order in the database.
func (oc *OrderCreate) Save(ctx context.Context) (*Order, error) {
	oc.defaults()
	return withHooks(ctx, oc.sqlSave, oc.mutation, oc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (oc *OrderCreate) SaveX(ctx context.Context) *Order {
	v, err := oc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (oc *OrderCreate) Exec(ctx context.Context) error {
	_, err := oc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (oc *OrderCreate) ExecX(ctx context.Context) {
	if err := oc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (oc *OrderCreate) defaults() {
	if _, ok := oc.mutation.GetType(); !ok {
		v := order.DefaultType
		oc.mutation.SetType(v)
	}
	if _, ok := oc.mutation.Currency(); !ok {
		v := order.DefaultCurrency
		oc.mutation.SetCurrency(v)
	}
	if _, ok := oc.mutation.Status(); !ok {
		v := order.DefaultStatus
		oc.mutation.SetStatus(v)
	}
	if _, ok := oc.mutation.Credits(); !ok {
		v := order.DefaultCredits
		oc.mutation.SetCredits(v)
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		v := order.DefaultCreatedAt()
		oc.mutation.SetCreatedAt(v)
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		v := order.DefaultUpdatedAt()
		oc.mutation.SetUpdatedAt(v)
	}
	if _, ok := oc.mutation.ID(); !ok {
		v := order.DefaultID()
		oc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (oc *OrderCreate) check() error {
	if _, ok := oc.mutation.OrderNumber(); !ok {
		return &ValidationError{Name: "order_number", err: errors.New(`ent: missing required field "Order.order_number"`)}
	}
	if v, ok := oc.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`ent: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if _, ok := oc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Order.user_id"`)}
	}
	if _, ok := oc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Order.type"`)}
	}
	if v, ok := oc.mutation.GetType(); ok {
		if err := order.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Order.type": %w`, err)}
		}
	}
	if _, ok := oc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "Order.amount"`)}
	}
	if v, ok := oc.mutation.Amount(); ok {
		if err := order.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Order.amount": %w`, err)}
		}
	}
	if _, ok := oc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "Order.currency"`)}
	}
	if v, ok := oc.mutation.Currency(); ok {
		if err := order.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Order.currency": %w`, err)}
		}
	}
	if _, ok := oc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Order.status"`)}
	}
	if v, ok := oc.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Order.status": %w`, err)}
		}
	}
	if _, ok := oc.mutation.PaymentMethod(); !ok {
		return &ValidationError{Name: "payment_method", err: errors.New(`ent: missing required field "Order.payment_method"`)}
	}
	if v, ok := oc.mutation.PaymentMethod(); ok {
		if err := order.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Order.payment_method": %w`, err)}
		}
	}
	if v, ok := oc.mutation.PaymentID(); ok {
		if err := order.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "Order.payment_id": %w`, err)}
		}
	}
	if v, ok := oc.mutation.InvoiceID(); ok {
		if err := order.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "Order.invoice_id": %w`, err)}
		}
	}
	if _, ok := oc.mutation.Credits(); !ok {
		return &ValidationError{Name: "credits", err: errors.New(`ent: missing required field "Order.credits"`)}
	}
	if v, ok := oc.mutation.Credits(); ok {
		if err := order.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "Order.credits": %w`, err)}
		}
	}
	if v, ok := oc.mutation.FailureReason(); ok {
		if err := order.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "Order.failure_reason": %w`, err)}
		}
	}
	if v, ok := oc.mutation.ExternalOrderID(); ok {
		if err := order.ExternalOrderIDValidator(v); err != nil {
			return &ValidationError{Name: "external_order_id", err: fmt.Errorf(`ent: validator failed for field "Order.external_order_id": %w`, err)}
		}
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Order.created_at"`)}
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Order.updated_at"`)}
	}
	return nil
}

func (oc *OrderCreate) sqlSave(ctx context.Context) (*Order, error) {
	if err := oc.check(); err != nil {
		return nil, err
	}
	_node, _spec := oc.createSpec()
	if err := sqlgraph.CreateNode(ctx, oc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	oc.mutation.id = &_node.ID
	oc.mutation.done = true
	return _node, nil
}

func (oc *OrderCreate) createSpec() (*Order, *sqlgraph.CreateSpec) {
	var (
		_node = &Order{config: oc.config}
		_spec = sqlgraph.NewCreateSpec(order.Table, sqlgraph.NewFieldSpec(order.FieldID, field.TypeUUID))
	)
	if id, ok := oc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := oc.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
		_node.OrderNumber = value
	}
	if value, ok := oc.mutation.UserID(); ok {
		_spec.SetField(order.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := oc.mutation.PackageID(); ok {
		_spec.SetField(order.FieldPackageID, field.TypeUUID, value)
		_node.PackageID = value
	}
	if value, ok := oc.mutation.PlanID(); ok {
		_spec.SetField(order.FieldPlanID, field.TypeUUID, value)
		_node.PlanID = value
	}
	if value, ok := oc.mutation.GetType(); ok {
		_spec.SetField(order.FieldType, field.TypeEnum, value)
		_node.Type = value
	}
	if value, ok := oc.mutation.Amount(); ok {
		_spec.SetField(order.FieldAmount, field.TypeInt64, value)
		_node.Amount = value
	}
	if value, ok := oc.mutation.Currency(); ok {
		_spec.SetField(order.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := oc.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := oc.mutation.PaymentMethod(); ok {
		_spec.SetField(order.FieldPaymentMethod, field.TypeString, value)
		_node.PaymentMethod = value
	}
	if value, ok := oc.mutation.PaymentID(); ok {
		_spec.SetField(order.FieldPaymentID, field.TypeString, value)
		_node.PaymentID = value
	}
	if value, ok := oc.mutation.InvoiceID(); ok {
		_spec.SetField(order.FieldInvoiceID, field.TypeString, value)
		_node.InvoiceID = value
	}
	if value, ok := oc.mutation.Credits(); ok {
		_spec.SetField(order.FieldCredits, field.TypeInt, value)
		_node.Credits = value
	}
	if value, ok := oc.mutation.BillingAddress(); ok {
		_spec.SetField(order.FieldBillingAddress, field.TypeJSON, value)
		_node.BillingAddress = value
	}
	if value, ok := oc.mutation.Items(); ok {
		_spec.SetField(order.FieldItems, field.TypeJSON, value)
		_node.Items = value
	}
	if value, ok := oc.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := oc.mutation.ProcessedAt(); ok {
		_spec.SetField(order.FieldProcessedAt, field.TypeTime, value)
		_node.ProcessedAt = value
	}
	if value, ok := oc.mutation.ExpiresAt(); ok {
		_spec.SetField(order.FieldExpiresAt, field.TypeTime, value)
		_node.ExpiresAt = value
	}
	if value, ok := oc.mutation.FailureReason(); ok {
		_spec.SetField(order.FieldFailureReason, field.TypeString, value)
		_node.FailureReason = value
	}
	if value, ok := oc.mutation.ExternalOrderID(); ok {
		_spec.SetField(order.FieldExternalOrderID, field.TypeString, value)
		_node.ExternalOrderID = value
	}
	if value, ok := oc.mutation.CreatedAt(); ok {
		_spec.SetField(order.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := oc.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// OrderCreateBulk is the builder for creating many Order entities in bulk.
type OrderCreateBulk struct {
	config
	err      error
	builders []*OrderCreate
}

// Save creates the Order entities in the database.
func (ocb *OrderCreateBulk) Save(ctx context.Context) ([]*Order, error) {
	if ocb.err != nil {
		return nil, ocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ocb.builders))
	nodes := make([]*Order, len(ocb.builders))
	mutators := make([]Mutator, len(ocb.builders))
	for i := range ocb.builders {
		func(i int, root context.Context) {
			builder := ocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*OrderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ocb *OrderCreateBulk) SaveX(ctx context.Context) []*Order {
	v, err := ocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ocb *OrderCreateBulk) Exec(ctx context.Context) error {
	_, err := ocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ocb *OrderCreateBulk) ExecX(ctx context.Context) {
	if err := ocb.Exec(ctx); err != nil {
		panic(err)
	}
}

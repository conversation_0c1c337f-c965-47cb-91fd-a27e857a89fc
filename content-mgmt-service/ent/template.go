// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
)

// Template is the model entity for the Template schema.
type Template struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who created this template
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Workspace this template belongs to
	WorkspaceID string `json:"workspace_id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Template content with variables
	Content string `json:"content,omitempty"`
	// List of variable names used in template
	Variables []string `json:"variables,omitempty"`
	// Descriptions for each variable
	VariableDescriptions map[string]string `json:"variable_descriptions,omitempty"`
	// Category holds the value of the "category" field.
	Category template.Category `json:"category,omitempty"`
	// Recommended platforms for this template
	Platforms []string `json:"platforms,omitempty"`
	// Tone holds the value of the "tone" field.
	Tone template.Tone `json:"tone,omitempty"`
	// ContentType holds the value of the "content_type" field.
	ContentType template.ContentType `json:"content_type,omitempty"`
	// Default hashtags for this template
	Hashtags []string `json:"hashtags,omitempty"`
	// Whether this template is publicly available
	IsPublic bool `json:"is_public,omitempty"`
	// Whether this template is featured
	IsFeatured bool `json:"is_featured,omitempty"`
	// Number of times this template has been used
	UsageCount int `json:"usage_count,omitempty"`
	// Average user rating
	Rating float64 `json:"rating,omitempty"`
	// Number of ratings
	RatingCount int `json:"rating_count,omitempty"`
	// Tags for categorization and search
	Tags []string `json:"tags,omitempty"`
	// Template language code
	Language string `json:"language,omitempty"`
	// File URLs used for RAG training
	TrainingFiles []string `json:"training_files,omitempty"`
	// Whether RAG training has been completed
	RagTrained bool `json:"rag_trained,omitempty"`
	// When RAG training was last completed
	RagTrainedAt time.Time `json:"rag_trained_at,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Template) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case template.FieldVariables, template.FieldVariableDescriptions, template.FieldPlatforms, template.FieldHashtags, template.FieldTags, template.FieldTrainingFiles, template.FieldMetadata:
			values[i] = new([]byte)
		case template.FieldIsPublic, template.FieldIsFeatured, template.FieldRagTrained:
			values[i] = new(sql.NullBool)
		case template.FieldRating:
			values[i] = new(sql.NullFloat64)
		case template.FieldUsageCount, template.FieldRatingCount:
			values[i] = new(sql.NullInt64)
		case template.FieldWorkspaceID, template.FieldName, template.FieldDescription, template.FieldContent, template.FieldCategory, template.FieldTone, template.FieldContentType, template.FieldLanguage:
			values[i] = new(sql.NullString)
		case template.FieldRagTrainedAt, template.FieldCreatedAt, template.FieldUpdatedAt, template.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case template.FieldID, template.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Template fields.
func (t *Template) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case template.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				t.ID = *value
			}
		case template.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				t.UserID = *value
			}
		case template.FieldWorkspaceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field workspace_id", values[i])
			} else if value.Valid {
				t.WorkspaceID = value.String
			}
		case template.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				t.Name = value.String
			}
		case template.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				t.Description = value.String
			}
		case template.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				t.Content = value.String
			}
		case template.FieldVariables:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field variables", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Variables); err != nil {
					return fmt.Errorf("unmarshal field variables: %w", err)
				}
			}
		case template.FieldVariableDescriptions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field variable_descriptions", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.VariableDescriptions); err != nil {
					return fmt.Errorf("unmarshal field variable_descriptions: %w", err)
				}
			}
		case template.FieldCategory:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field category", values[i])
			} else if value.Valid {
				t.Category = template.Category(value.String)
			}
		case template.FieldPlatforms:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field platforms", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Platforms); err != nil {
					return fmt.Errorf("unmarshal field platforms: %w", err)
				}
			}
		case template.FieldTone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tone", values[i])
			} else if value.Valid {
				t.Tone = template.Tone(value.String)
			}
		case template.FieldContentType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content_type", values[i])
			} else if value.Valid {
				t.ContentType = template.ContentType(value.String)
			}
		case template.FieldHashtags:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field hashtags", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Hashtags); err != nil {
					return fmt.Errorf("unmarshal field hashtags: %w", err)
				}
			}
		case template.FieldIsPublic:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_public", values[i])
			} else if value.Valid {
				t.IsPublic = value.Bool
			}
		case template.FieldIsFeatured:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_featured", values[i])
			} else if value.Valid {
				t.IsFeatured = value.Bool
			}
		case template.FieldUsageCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field usage_count", values[i])
			} else if value.Valid {
				t.UsageCount = int(value.Int64)
			}
		case template.FieldRating:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field rating", values[i])
			} else if value.Valid {
				t.Rating = value.Float64
			}
		case template.FieldRatingCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field rating_count", values[i])
			} else if value.Valid {
				t.RatingCount = int(value.Int64)
			}
		case template.FieldTags:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field tags", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Tags); err != nil {
					return fmt.Errorf("unmarshal field tags: %w", err)
				}
			}
		case template.FieldLanguage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field language", values[i])
			} else if value.Valid {
				t.Language = value.String
			}
		case template.FieldTrainingFiles:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field training_files", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.TrainingFiles); err != nil {
					return fmt.Errorf("unmarshal field training_files: %w", err)
				}
			}
		case template.FieldRagTrained:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field rag_trained", values[i])
			} else if value.Valid {
				t.RagTrained = value.Bool
			}
		case template.FieldRagTrainedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field rag_trained_at", values[i])
			} else if value.Valid {
				t.RagTrainedAt = value.Time
			}
		case template.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case template.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				t.CreatedAt = value.Time
			}
		case template.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				t.UpdatedAt = value.Time
			}
		case template.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				t.DeletedAt = value.Time
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Template.
// This includes values selected through modifiers, order, etc.
func (t *Template) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Template.
// Note that you need to call Template.Unwrap() before calling this method if this Template
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Template) Update() *TemplateUpdateOne {
	return NewTemplateClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Template entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Template) Unwrap() *Template {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Template is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Template) String() string {
	var builder strings.Builder
	builder.WriteString("Template(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", t.UserID))
	builder.WriteString(", ")
	builder.WriteString("workspace_id=")
	builder.WriteString(t.WorkspaceID)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(t.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(t.Description)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(t.Content)
	builder.WriteString(", ")
	builder.WriteString("variables=")
	builder.WriteString(fmt.Sprintf("%v", t.Variables))
	builder.WriteString(", ")
	builder.WriteString("variable_descriptions=")
	builder.WriteString(fmt.Sprintf("%v", t.VariableDescriptions))
	builder.WriteString(", ")
	builder.WriteString("category=")
	builder.WriteString(fmt.Sprintf("%v", t.Category))
	builder.WriteString(", ")
	builder.WriteString("platforms=")
	builder.WriteString(fmt.Sprintf("%v", t.Platforms))
	builder.WriteString(", ")
	builder.WriteString("tone=")
	builder.WriteString(fmt.Sprintf("%v", t.Tone))
	builder.WriteString(", ")
	builder.WriteString("content_type=")
	builder.WriteString(fmt.Sprintf("%v", t.ContentType))
	builder.WriteString(", ")
	builder.WriteString("hashtags=")
	builder.WriteString(fmt.Sprintf("%v", t.Hashtags))
	builder.WriteString(", ")
	builder.WriteString("is_public=")
	builder.WriteString(fmt.Sprintf("%v", t.IsPublic))
	builder.WriteString(", ")
	builder.WriteString("is_featured=")
	builder.WriteString(fmt.Sprintf("%v", t.IsFeatured))
	builder.WriteString(", ")
	builder.WriteString("usage_count=")
	builder.WriteString(fmt.Sprintf("%v", t.UsageCount))
	builder.WriteString(", ")
	builder.WriteString("rating=")
	builder.WriteString(fmt.Sprintf("%v", t.Rating))
	builder.WriteString(", ")
	builder.WriteString("rating_count=")
	builder.WriteString(fmt.Sprintf("%v", t.RatingCount))
	builder.WriteString(", ")
	builder.WriteString("tags=")
	builder.WriteString(fmt.Sprintf("%v", t.Tags))
	builder.WriteString(", ")
	builder.WriteString("language=")
	builder.WriteString(t.Language)
	builder.WriteString(", ")
	builder.WriteString("training_files=")
	builder.WriteString(fmt.Sprintf("%v", t.TrainingFiles))
	builder.WriteString(", ")
	builder.WriteString("rag_trained=")
	builder.WriteString(fmt.Sprintf("%v", t.RagTrained))
	builder.WriteString(", ")
	builder.WriteString("rag_trained_at=")
	builder.WriteString(t.RagTrainedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", t.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(t.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(t.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(t.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Templates is a parsable slice of Template.
type Templates []*Template

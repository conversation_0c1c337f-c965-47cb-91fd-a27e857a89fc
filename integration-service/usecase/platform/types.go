package platform

// Request/Response types

// GetPlatformLimitsRequest represents a request to get platform rate limits
type GetPlatformLimitsRequest struct {
	Platform  string `json:"platform" validate:"required"`
	AccountID string `json:"account_id" validate:"required"`
}

// GetPlatformLimitsResponse represents the response with platform rate limits
type GetPlatformLimitsResponse struct {
	Platform     string            `json:"platform"`
	RateLimits   map[string]int32  `json:"rate_limits"`
	CurrentUsage map[string]int32  `json:"current_usage"`
	ResetTimes   map[string]int64  `json:"reset_times"`
}

// ProcessWebhookRequest represents a request to process webhook events
type ProcessWebhookRequest struct {
	Platform  string            `json:"platform" validate:"required"`
	Signature string            `json:"signature"`
	Payload   []byte            `json:"payload" validate:"required"`
	Headers   map[string]string `json:"headers"`
}

// ProcessWebhookResponse represents the response after processing webhook
type ProcessWebhookResponse struct {
	Success      bool   `json:"success"`
	EventID      string `json:"event_id,omitempty"`
	EventType    string `json:"event_type,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// Code generated by ent, DO NOT EDIT.

package credittransaction

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the credittransaction type in the database.
	Label = "credit_transaction"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldReferenceID holds the string denoting the reference_id field in the database.
	FieldReferenceID = "reference_id"
	// FieldReferenceType holds the string denoting the reference_type field in the database.
	FieldReferenceType = "reference_type"
	// FieldPaymentID holds the string denoting the payment_id field in the database.
	FieldPaymentID = "payment_id"
	// FieldInvoiceID holds the string denoting the invoice_id field in the database.
	FieldInvoiceID = "invoice_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldProcessedAt holds the string denoting the processed_at field in the database.
	FieldProcessedAt = "processed_at"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the credittransaction in the database.
	Table = "credit_transactions"
)

// Columns holds all SQL columns for credittransaction fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldType,
	FieldAmount,
	FieldDescription,
	FieldReferenceID,
	FieldReferenceType,
	FieldPaymentID,
	FieldInvoiceID,
	FieldStatus,
	FieldMetadata,
	FieldProcessedAt,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	DescriptionValidator func(string) error
	// ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	ReferenceIDValidator func(string) error
	// ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	ReferenceTypeValidator func(string) error
	// PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	PaymentIDValidator func(string) error
	// InvoiceIDValidator is a validator for the "invoice_id" field. It is called by the builders before save.
	InvoiceIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Type defines the type for the "type" enum field.
type Type string

// TypeUsage is the default value of the Type enum.
const DefaultType = TypeUsage

// Type values.
const (
	TypePurchase     Type = "purchase"
	TypeUsage        Type = "usage"
	TypeBonus        Type = "bonus"
	TypeRefund       Type = "refund"
	TypeAdjustment   Type = "adjustment"
	TypeSubscription Type = "subscription"
	TypeReferral     Type = "referral"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case TypePurchase, TypeUsage, TypeBonus, TypeRefund, TypeAdjustment, TypeSubscription, TypeReferral:
		return nil
	default:
		return fmt.Errorf("credittransaction: invalid enum value for type field: %q", _type)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusCompleted is the default value of the Status enum.
const DefaultStatus = StatusCompleted

// Status values.
const (
	StatusPending   Status = "pending"
	StatusCompleted Status = "completed"
	StatusFailed    Status = "failed"
	StatusCancelled Status = "cancelled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusCompleted, StatusFailed, StatusCancelled:
		return nil
	default:
		return fmt.Errorf("credittransaction: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the CreditTransaction queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByReferenceID orders the results by the reference_id field.
func ByReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceID, opts...).ToFunc()
}

// ByReferenceType orders the results by the reference_type field.
func ByReferenceType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceType, opts...).ToFunc()
}

// ByPaymentID orders the results by the payment_id field.
func ByPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentID, opts...).ToFunc()
}

// ByInvoiceID orders the results by the invoice_id field.
func ByInvoiceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInvoiceID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByProcessedAt orders the results by the processed_at field.
func ByProcessedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProcessedAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

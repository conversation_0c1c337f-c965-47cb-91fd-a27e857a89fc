// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationTemplateDelete is the builder for deleting a NotificationTemplate entity.
type NotificationTemplateDelete struct {
	config
	hooks    []Hook
	mutation *NotificationTemplateMutation
}

// Where appends a list predicates to the NotificationTemplateDelete builder.
func (ntd *NotificationTemplateDelete) Where(ps ...predicate.NotificationTemplate) *NotificationTemplateDelete {
	ntd.mutation.Where(ps...)
	return ntd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ntd *NotificationTemplateDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ntd.sqlExec, ntd.mutation, ntd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ntd *NotificationTemplateDelete) ExecX(ctx context.Context) int {
	n, err := ntd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ntd *NotificationTemplateDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(notificationtemplate.Table, sqlgraph.NewFieldSpec(notificationtemplate.FieldID, field.TypeUUID))
	if ps := ntd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ntd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ntd.mutation.done = true
	return affected, err
}

// NotificationTemplateDeleteOne is the builder for deleting a single NotificationTemplate entity.
type NotificationTemplateDeleteOne struct {
	ntd *NotificationTemplateDelete
}

// Where appends a list predicates to the NotificationTemplateDelete builder.
func (ntdo *NotificationTemplateDeleteOne) Where(ps ...predicate.NotificationTemplate) *NotificationTemplateDeleteOne {
	ntdo.ntd.mutation.Where(ps...)
	return ntdo
}

// Exec executes the deletion query.
func (ntdo *NotificationTemplateDeleteOne) Exec(ctx context.Context) error {
	n, err := ntdo.ntd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{notificationtemplate.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ntdo *NotificationTemplateDeleteOne) ExecX(ctx context.Context) {
	if err := ntdo.Exec(ctx); err != nil {
		panic(err)
	}
}

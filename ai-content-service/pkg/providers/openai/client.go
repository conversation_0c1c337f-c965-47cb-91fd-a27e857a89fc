package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// Client represents OpenAI API client
type Client struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     logging.Logger
}

// NewClient creates a new OpenAI client
func NewClient(apiKey, baseURL string, logger logging.Logger) *Client {
	if baseURL == "" {
		baseURL = "https://api.openai.com/v1"
	}

	return &Client{
		apiKey:  apiKey,
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// GenerateContentRequest represents content generation request
type GenerateContentRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Temperature float64   `json:"temperature,omitempty"`
	TopP        float64   `json:"top_p,omitempty"`
	Stop        []string  `json:"stop,omitempty"`
	User        string    `json:"user,omitempty"`
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// GenerateContentResponse represents content generation response
type GenerateContentResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice represents a generation choice
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// Usage represents token usage
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ErrorResponse represents API error response
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// GenerateContent generates content using OpenAI API
func (c *Client) GenerateContent(ctx context.Context, req *GenerateContentRequest) (*GenerateContentResponse, error) {
	// Set default model if not specified
	if req.Model == "" {
		req.Model = "gpt-3.5-turbo"
	}

	// Set default temperature if not specified
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	// Log request
	c.logger.WithFields(map[string]interface{}{
		"model":       req.Model,
		"messages":    len(req.Messages),
		"max_tokens":  req.MaxTokens,
		"temperature": req.Temperature,
	}).Debug("Making OpenAI API request")

	// Make request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(respBody))
		}
		return nil, fmt.Errorf("OpenAI API error: %s", errResp.Error.Message)
	}

	// Parse successful response
	var result GenerateContentResponse
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Log response
	c.logger.WithFields(map[string]interface{}{
		"id":                result.ID,
		"model":             result.Model,
		"choices":           len(result.Choices),
		"prompt_tokens":     result.Usage.PromptTokens,
		"completion_tokens": result.Usage.CompletionTokens,
		"total_tokens":      result.Usage.TotalTokens,
	}).Debug("Received OpenAI API response")

	return &result, nil
}

// ImproveContent improves existing content
func (c *Client) ImproveContent(ctx context.Context, content, instructions string) (*GenerateContentResponse, error) {
	messages := []Message{
		{
			Role: "system",
			Content: `You are an expert content writer and editor. Your task is to improve the given content based on the provided instructions. 
Focus on:
- Clarity and readability
- Engagement and appeal
- Grammar and style
- SEO optimization when relevant
- Platform-specific best practices

Maintain the original tone and intent while making improvements.`,
		},
		{
			Role:    "user",
			Content: fmt.Sprintf("Please improve this content:\n\n%s\n\nInstructions: %s", content, instructions),
		},
	}

	req := &GenerateContentRequest{
		Model:       "gpt-3.5-turbo",
		Messages:    messages,
		MaxTokens:   1000,
		Temperature: 0.7,
	}

	return c.GenerateContent(ctx, req)
}

// GenerateHashtags generates hashtags for content
func (c *Client) GenerateHashtags(ctx context.Context, content string, platform string, count int) ([]string, error) {
	if count <= 0 {
		count = 10
	}

	messages := []Message{
		{
			Role: "system",
			Content: `You are a social media expert. Generate relevant hashtags for the given content and platform.
Rules:
- Generate hashtags that are relevant and trending
- Consider platform-specific best practices
- Mix popular and niche hashtags
- Avoid banned or problematic hashtags
- Return only the hashtags, one per line, without the # symbol`,
		},
		{
			Role:    "user",
			Content: fmt.Sprintf("Generate %d hashtags for this %s content:\n\n%s", count, platform, content),
		},
	}

	req := &GenerateContentRequest{
		Model:       "gpt-3.5-turbo",
		Messages:    messages,
		MaxTokens:   200,
		Temperature: 0.8,
	}

	resp, err := c.GenerateContent(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no hashtags generated")
	}

	// Parse hashtags from response
	hashtagsText := resp.Choices[0].Message.Content
	hashtags := parseHashtags(hashtagsText)

	return hashtags, nil
}

// parseHashtags parses hashtags from text response
func parseHashtags(text string) []string {
	lines := strings.Split(strings.TrimSpace(text), "\n")
	var hashtags []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Remove # if present
			line = strings.TrimPrefix(line, "#")
			// Remove numbers and dots from beginning
			line = strings.TrimLeft(line, "**********. ")
			if line != "" {
				hashtags = append(hashtags, line)
			}
		}
	}

	return hashtags
}

// HealthCheck checks if the OpenAI API is accessible
func (c *Client) HealthCheck(ctx context.Context) error {
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/models", nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status: %d", resp.StatusCode)
	}

	return nil
}

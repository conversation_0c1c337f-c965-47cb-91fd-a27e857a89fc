package credit

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/creditbalance"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// ValidateCreditsForAI validates if user has sufficient credits for AI operations
func (s *service) ValidateCreditsForAI(ctx context.Context, req *models.ValidateAICreditsRequest) (*models.ValidateAICreditsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"operation":  req.Operation,
		"cost":       req.EstimatedCost,
		"service":    req.ServiceName,
	}).Info("Validating credits for AI operation")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get user's credit balance
	balance, err := s.readDB.CreditBalance.Query().
		Where(creditbalance.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			// User has no credit balance, create one with 0 credits
			balance = &ent.CreditBalance{
				UserID:         userUUID,
				CurrentCredits: 0,
				TotalCredits:   0,
			}
		} else {
			return nil, fmt.Errorf("failed to get credit balance: %w", err)
		}
	}

	// Calculate actual cost based on operation type
	actualCost := s.calculateAICost(req.Operation, req.EstimatedCost, req.Parameters)

	// Check if user has sufficient credits
	hasSufficientCredits := balance.CurrentCredits >= actualCost
	shortage := 0
	if !hasSufficientCredits {
		shortage = actualCost - balance.CurrentCredits
	}

	// Get cost breakdown
	costBreakdown := s.getAICostBreakdown(req.Operation, req.Parameters)

	response := &models.ValidateAICreditsResponse{
		Valid:                hasSufficientCredits,
		UserID:               req.UserID,
		Operation:            req.Operation,
		EstimatedCost:        req.EstimatedCost,
		ActualCost:           actualCost,
		CurrentCredits:       balance.CurrentCredits,
		RequiredCredits:      actualCost,
		Shortage:             shortage,
		CostBreakdown:        costBreakdown,
		RecommendedPlan:      "",
		ValidationTimestamp:  s.getCurrentTimestamp(),
	}

	// If user doesn't have enough credits, suggest a plan
	if !hasSufficientCredits {
		recommendedPlan := s.getRecommendedPlan(shortage)
		response.RecommendedPlan = recommendedPlan
	}

	return response, nil
}

// calculateAICost calculates the actual cost for AI operations
func (s *service) calculateAICost(operation string, estimatedCost int, parameters map[string]interface{}) int {
	// Base cost from estimation
	actualCost := estimatedCost

	switch operation {
	case "text_generation":
		// Cost based on token count or word count
		if tokens, ok := parameters["max_tokens"].(float64); ok {
			// 1 credit per 100 tokens (adjust as needed)
			actualCost = int(tokens / 100)
			if actualCost < 1 {
				actualCost = 1
			}
		}
	case "image_generation":
		// Cost based on image size and quality
		quality := "standard"
		if q, ok := parameters["quality"].(string); ok {
			quality = q
		}
		
		size := "1024x1024"
		if s, ok := parameters["size"].(string); ok {
			size = s
		}

		actualCost = s.calculateImageGenerationCost(size, quality)
		
	case "image_analysis":
		// Fixed cost for image analysis
		actualCost = 2
		
	case "text_to_speech":
		// Cost based on character count
		if chars, ok := parameters["character_count"].(float64); ok {
			// 1 credit per 1000 characters
			actualCost = int(chars / 1000)
			if actualCost < 1 {
				actualCost = 1
			}
		}
		
	case "speech_to_text":
		// Cost based on audio duration
		if duration, ok := parameters["duration_seconds"].(float64); ok {
			// 1 credit per 60 seconds
			actualCost = int(duration / 60)
			if actualCost < 1 {
				actualCost = 1
			}
		}
		
	case "content_moderation":
		// Fixed cost for content moderation
		actualCost = 1
		
	default:
		// Use estimated cost for unknown operations
		if actualCost < 1 {
			actualCost = 1
		}
	}

	return actualCost
}

// calculateImageGenerationCost calculates cost for image generation
func (s *service) calculateImageGenerationCost(size, quality string) int {
	baseCost := 5 // Base cost for standard image

	// Adjust cost based on size
	switch size {
	case "256x256":
		baseCost = 2
	case "512x512":
		baseCost = 3
	case "1024x1024":
		baseCost = 5
	case "1792x1024", "1024x1792":
		baseCost = 8
	default:
		baseCost = 5
	}

	// Adjust cost based on quality
	if quality == "hd" {
		baseCost = baseCost * 2
	}

	return baseCost
}

// getAICostBreakdown provides detailed cost breakdown
func (s *service) getAICostBreakdown(operation string, parameters map[string]interface{}) map[string]int {
	breakdown := make(map[string]int)

	switch operation {
	case "text_generation":
		if tokens, ok := parameters["max_tokens"].(float64); ok {
			breakdown["base_cost"] = 1
			breakdown["token_cost"] = int(tokens / 100)
		}
	case "image_generation":
		quality := "standard"
		if q, ok := parameters["quality"].(string); ok {
			quality = q
		}
		
		size := "1024x1024"
		if s, ok := parameters["size"].(string); ok {
			size = s
		}

		breakdown["base_cost"] = 2
		breakdown["size_cost"] = s.getSizeCost(size)
		breakdown["quality_cost"] = s.getQualityCost(quality)
		
	default:
		breakdown["base_cost"] = 1
	}

	return breakdown
}

// getSizeCost returns cost adjustment for image size
func (s *service) getSizeCost(size string) int {
	switch size {
	case "256x256":
		return 0
	case "512x512":
		return 1
	case "1024x1024":
		return 3
	case "1792x1024", "1024x1792":
		return 6
	default:
		return 3
	}
}

// getQualityCost returns cost adjustment for image quality
func (s *service) getQualityCost(quality string) int {
	if quality == "hd" {
		return 5
	}
	return 0
}

// getRecommendedPlan suggests a subscription plan based on credit shortage
func (s *service) getRecommendedPlan(shortage int) string {
	if shortage <= 100 {
		return "basic"
	} else if shortage <= 500 {
		return "pro"
	} else {
		return "enterprise"
	}
}

// getCurrentTimestamp returns current timestamp in Unix format
func (s *service) getCurrentTimestamp() int64 {
	return s.getCurrentTime().Unix()
}

// ReserveCreditsForAI reserves credits for AI operations (pre-deduction)
func (s *service) ReserveCreditsForAI(ctx context.Context, req *models.ReserveAICreditsRequest) (*models.ReserveAICreditsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserID,
		"operation":    req.Operation,
		"credits":      req.Credits,
		"reference_id": req.ReferenceID,
	}).Info("Reserving credits for AI operation")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get and lock credit balance
	balance, err := tx.CreditBalance.Query().
		Where(creditbalance.UserID(userUUID)).
		ForUpdate().
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &models.ReserveAICreditsResponse{
				Success:      false,
				ErrorMessage: "Insufficient credits",
				ReservedCredits: 0,
			}, nil
		}
		return nil, fmt.Errorf("failed to get credit balance: %w", err)
	}

	// Check if user has sufficient credits
	if balance.CurrentCredits < req.Credits {
		return &models.ReserveAICreditsResponse{
			Success:         false,
			ErrorMessage:    "Insufficient credits",
			ReservedCredits: 0,
			CurrentCredits:  balance.CurrentCredits,
			RequiredCredits: req.Credits,
		}, nil
	}

	// Reserve credits (deduct from current balance)
	newBalance := balance.CurrentCredits - req.Credits
	_, err = tx.CreditBalance.UpdateOne(balance).
		SetCurrentCredits(newBalance).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update credit balance: %w", err)
	}

	// Create credit transaction record
	_, err = tx.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType("deduction").
		SetAmount(-req.Credits).
		SetDescription(fmt.Sprintf("Reserved for %s operation", req.Operation)).
		SetReferenceID(req.ReferenceID).
		SetReferenceType("ai_operation_reserve").
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create credit transaction: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &models.ReserveAICreditsResponse{
		Success:         true,
		ReservedCredits: req.Credits,
		CurrentCredits:  newBalance,
		ReservationID:   req.ReferenceID,
	}, nil
}

// ConfirmAIOperation confirms AI operation and finalizes credit deduction
func (s *service) ConfirmAIOperation(ctx context.Context, req *models.ConfirmAIOperationRequest) (*models.ConfirmAIOperationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":        req.UserID,
		"operation":      req.Operation,
		"reservation_id": req.ReservationID,
		"actual_cost":    req.ActualCost,
	}).Info("Confirming AI operation")

	// If actual cost is different from reserved amount, adjust the balance
	if req.ActualCost != req.ReservedCredits {
		adjustment := req.ReservedCredits - req.ActualCost
		if adjustment != 0 {
			adjustReq := &models.AddCreditsRequest{
				UserID:      req.UserID,
				Amount:      adjustment,
				Source:      "ai_operation_adjustment",
				Description: fmt.Sprintf("Credit adjustment for %s operation", req.Operation),
			}

			_, err := s.AddCredits(ctx, adjustReq)
			if err != nil {
				s.logger.WithError(err).Error("Failed to adjust credits after AI operation")
				// Don't fail the confirmation for adjustment errors
			}
		}
	}

	return &models.ConfirmAIOperationResponse{
		Success:       true,
		ActualCost:    req.ActualCost,
		Adjustment:    req.ReservedCredits - req.ActualCost,
		OperationID:   req.ReservationID,
	}, nil
}

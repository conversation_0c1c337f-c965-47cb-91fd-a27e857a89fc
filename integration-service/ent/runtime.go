// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
	"github.com/social-content-ai/integration-service/ent/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	platformintegrationFields := schema.PlatformIntegration{}.Fields()
	_ = platformintegrationFields
	// platformintegrationDescWorkspaceID is the schema descriptor for workspace_id field.
	platformintegrationDescWorkspaceID := platformintegrationFields[2].Descriptor()
	// platformintegration.WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	platformintegration.WorkspaceIDValidator = platformintegrationDescWorkspaceID.Validators[0].(func(string) error)
	// platformintegrationDescPlatformUserID is the schema descriptor for platform_user_id field.
	platformintegrationDescPlatformUserID := platformintegrationFields[4].Descriptor()
	// platformintegration.PlatformUserIDValidator is a validator for the "platform_user_id" field. It is called by the builders before save.
	platformintegration.PlatformUserIDValidator = platformintegrationDescPlatformUserID.Validators[0].(func(string) error)
	// platformintegrationDescPlatformUsername is the schema descriptor for platform_username field.
	platformintegrationDescPlatformUsername := platformintegrationFields[5].Descriptor()
	// platformintegration.PlatformUsernameValidator is a validator for the "platform_username" field. It is called by the builders before save.
	platformintegration.PlatformUsernameValidator = platformintegrationDescPlatformUsername.Validators[0].(func(string) error)
	// platformintegrationDescPlatformDisplayName is the schema descriptor for platform_display_name field.
	platformintegrationDescPlatformDisplayName := platformintegrationFields[6].Descriptor()
	// platformintegration.PlatformDisplayNameValidator is a validator for the "platform_display_name" field. It is called by the builders before save.
	platformintegration.PlatformDisplayNameValidator = platformintegrationDescPlatformDisplayName.Validators[0].(func(string) error)
	// platformintegrationDescPlatformAvatarURL is the schema descriptor for platform_avatar_url field.
	platformintegrationDescPlatformAvatarURL := platformintegrationFields[7].Descriptor()
	// platformintegration.PlatformAvatarURLValidator is a validator for the "platform_avatar_url" field. It is called by the builders before save.
	platformintegration.PlatformAvatarURLValidator = platformintegrationDescPlatformAvatarURL.Validators[0].(func(string) error)
	// platformintegrationDescErrorMessage is the schema descriptor for error_message field.
	platformintegrationDescErrorMessage := platformintegrationFields[14].Descriptor()
	// platformintegration.ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	platformintegration.ErrorMessageValidator = platformintegrationDescErrorMessage.Validators[0].(func(string) error)
	// platformintegrationDescAutoPublish is the schema descriptor for auto_publish field.
	platformintegrationDescAutoPublish := platformintegrationFields[19].Descriptor()
	// platformintegration.DefaultAutoPublish holds the default value on creation for the auto_publish field.
	platformintegration.DefaultAutoPublish = platformintegrationDescAutoPublish.Default.(bool)
	// platformintegrationDescIsPrimary is the schema descriptor for is_primary field.
	platformintegrationDescIsPrimary := platformintegrationFields[20].Descriptor()
	// platformintegration.DefaultIsPrimary holds the default value on creation for the is_primary field.
	platformintegration.DefaultIsPrimary = platformintegrationDescIsPrimary.Default.(bool)
	// platformintegrationDescCreatedAt is the schema descriptor for created_at field.
	platformintegrationDescCreatedAt := platformintegrationFields[22].Descriptor()
	// platformintegration.DefaultCreatedAt holds the default value on creation for the created_at field.
	platformintegration.DefaultCreatedAt = platformintegrationDescCreatedAt.Default.(func() time.Time)
	// platformintegrationDescUpdatedAt is the schema descriptor for updated_at field.
	platformintegrationDescUpdatedAt := platformintegrationFields[23].Descriptor()
	// platformintegration.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	platformintegration.DefaultUpdatedAt = platformintegrationDescUpdatedAt.Default.(func() time.Time)
	// platformintegration.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	platformintegration.UpdateDefaultUpdatedAt = platformintegrationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// platformintegrationDescID is the schema descriptor for id field.
	platformintegrationDescID := platformintegrationFields[0].Descriptor()
	// platformintegration.DefaultID holds the default value on creation for the id field.
	platformintegration.DefaultID = platformintegrationDescID.Default.(func() uuid.UUID)
	publishedpostFields := schema.PublishedPost{}.Fields()
	_ = publishedpostFields
	// publishedpostDescPostID is the schema descriptor for post_id field.
	publishedpostDescPostID := publishedpostFields[2].Descriptor()
	// publishedpost.PostIDValidator is a validator for the "post_id" field. It is called by the builders before save.
	publishedpost.PostIDValidator = publishedpostDescPostID.Validators[0].(func(string) error)
	// publishedpostDescIntegrationID is the schema descriptor for integration_id field.
	publishedpostDescIntegrationID := publishedpostFields[3].Descriptor()
	// publishedpost.IntegrationIDValidator is a validator for the "integration_id" field. It is called by the builders before save.
	publishedpost.IntegrationIDValidator = publishedpostDescIntegrationID.Validators[0].(func(string) error)
	// publishedpostDescPlatformPostID is the schema descriptor for platform_post_id field.
	publishedpostDescPlatformPostID := publishedpostFields[5].Descriptor()
	// publishedpost.PlatformPostIDValidator is a validator for the "platform_post_id" field. It is called by the builders before save.
	publishedpost.PlatformPostIDValidator = publishedpostDescPlatformPostID.Validators[0].(func(string) error)
	// publishedpostDescPlatformPostURL is the schema descriptor for platform_post_url field.
	publishedpostDescPlatformPostURL := publishedpostFields[6].Descriptor()
	// publishedpost.PlatformPostURLValidator is a validator for the "platform_post_url" field. It is called by the builders before save.
	publishedpost.PlatformPostURLValidator = publishedpostDescPlatformPostURL.Validators[0].(func(string) error)
	// publishedpostDescErrorMessage is the schema descriptor for error_message field.
	publishedpostDescErrorMessage := publishedpostFields[11].Descriptor()
	// publishedpost.ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	publishedpost.ErrorMessageValidator = publishedpostDescErrorMessage.Validators[0].(func(string) error)
	// publishedpostDescRetryCount is the schema descriptor for retry_count field.
	publishedpostDescRetryCount := publishedpostFields[17].Descriptor()
	// publishedpost.DefaultRetryCount holds the default value on creation for the retry_count field.
	publishedpost.DefaultRetryCount = publishedpostDescRetryCount.Default.(int)
	// publishedpostDescCreatedAt is the schema descriptor for created_at field.
	publishedpostDescCreatedAt := publishedpostFields[21].Descriptor()
	// publishedpost.DefaultCreatedAt holds the default value on creation for the created_at field.
	publishedpost.DefaultCreatedAt = publishedpostDescCreatedAt.Default.(func() time.Time)
	// publishedpostDescUpdatedAt is the schema descriptor for updated_at field.
	publishedpostDescUpdatedAt := publishedpostFields[22].Descriptor()
	// publishedpost.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	publishedpost.DefaultUpdatedAt = publishedpostDescUpdatedAt.Default.(func() time.Time)
	// publishedpost.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	publishedpost.UpdateDefaultUpdatedAt = publishedpostDescUpdatedAt.UpdateDefault.(func() time.Time)
	// publishedpostDescID is the schema descriptor for id field.
	publishedpostDescID := publishedpostFields[0].Descriptor()
	// publishedpost.DefaultID holds the default value on creation for the id field.
	publishedpost.DefaultID = publishedpostDescID.Default.(func() uuid.UUID)
}

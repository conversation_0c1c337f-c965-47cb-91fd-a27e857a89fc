package handlers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/usecase/session"
)

// SessionHandler handles session-related HTTP requests
type SessionHandler struct {
	sessionUseCase session.UseCase
	logger         logging.Logger
}

// NewSessionHandler creates a new session handler
func NewSessionHandler(sessionUseCase session.UseCase, logger logging.Logger) *SessionHandler {
	return &SessionHandler{
		sessionUseCase: sessionUseCase,
		logger:         logger,
	}
}

// ListSessions godoc
// @Summary List user sessions
// @Description Get a paginated list of all active sessions for the authenticated user
// @Tags sessions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(50) minimum(1) maximum(100)
// @Param status query string false "Session status filter" Enums(active,expired,revoked)
// @Success 200 {object} models.ListSessionsResponse "Successfully retrieved sessions"
// @Failure 400 {object} models.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/sessions [get]
func (h *SessionHandler) ListSessions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}
	// Parse pagination query parameters
	page := 1
	limit := 50

	if p := c.Query("page"); p != "" {
		if _, err := fmt.Sscanf(p, "%d", &page); err != nil || page < 1 {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_PAGE",
				Message: "Page must be a positive integer",
			})
			return
		}
	}
	if l := c.Query("limit"); l != "" {
		if _, err := fmt.Sscanf(l, "%d", &limit); err != nil || limit < 1 || limit > 100 {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_LIMIT",
				Message: "Limit must be between 1 and 100",
			})
			return
		}
	}

	// Optional status filter
	status := c.Query("status")
	if status != "" && status != "active" && status != "expired" && status != "revoked" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_STATUS",
			Message: "Status must be one of: active, expired, revoked",
		})
		return
	}

	// Create list sessions request
	req := &session.ListSessionsRequest{
		UserID: fmt.Sprintf("%d", userID.(uint64)),
		Page:   page,
		Limit:  limit,
		Status: status,
	}

	sessions, total, err := h.sessionUseCase.ListSessions(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list sessions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_SESSIONS_FAILED",
			Message: "Failed to list sessions",
		})
		return
	}

	// Convert to response format
	sessionResponses := make([]*session.SessionResponse, len(sessions))
	for i, s := range sessions {
		sessionResponses[i] = session.ToSessionResponse(s)
	}

	c.JSON(http.StatusOK, models.ListSessionsResponse{
		Sessions: sessionResponses,
		Total:    total,
	})
}

// RevokeSession godoc
// @Summary Revoke a session
// @Description Revoke a specific session by its unique identifier. The session will be immediately invalidated.
// @Tags sessions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Session ID" format(uuid) example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} models.SuccessResponse "Session successfully revoked"
// @Failure 400 {object} models.ErrorResponse "Invalid session ID format"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Not authorized to revoke this session"
// @Failure 404 {object} models.ErrorResponse "Session not found"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/sessions/{id} [delete]
func (h *SessionHandler) RevokeSession(c *gin.Context) {
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	sessionID := c.Param("id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_SESSION_ID",
			Message: "Session ID is required",
		})
		return
	}

	err := h.sessionUseCase.RevokeSession(c.Request.Context(), sessionID, "User requested")
	if err != nil {
		h.logger.WithError(err).Error("Failed to revoke session")

		switch err.Error() {
		case "SESSION_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "SESSION_NOT_FOUND",
				Message: "Session not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REVOKE_SESSION_FAILED",
				Message: "Failed to revoke session",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Session revoked successfully",
	})
}

// RevokeAllSessions godoc
// @Summary Revoke all sessions
// @Description Revoke all sessions for the authenticated user except the current session. This is useful for security purposes when user suspects account compromise.
// @Tags sessions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "All sessions successfully revoked"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/sessions [delete]
func (h *SessionHandler) RevokeAllSessions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	currentSessionID, _ := c.Get("session_id")

	userIDStr := fmt.Sprintf("%d", userID.(uint64))
	var exceptSessionID string
	if currentSessionID != nil {
		exceptSessionID = currentSessionID.(string)
	}

	err := h.sessionUseCase.RevokeAllSessions(c.Request.Context(), userIDStr, exceptSessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to revoke all sessions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "REVOKE_ALL_SESSIONS_FAILED",
			Message: "Failed to revoke all sessions",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "All sessions revoked successfully",
	})
}

// GetSessionAnalytics godoc
// @Summary Get session analytics
// @Description Get detailed session analytics including active sessions, login patterns, and security metrics. Admin access required.
// @Tags admin,analytics,sessions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param period query string false "Time period for analytics" Enums(day,week,month,year) default(week)
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Success 200 {object} models.SessionAnalyticsResponse "Session analytics data"
// @Failure 400 {object} models.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /admin/v1/analytics/sessions [get]
func (h *SessionHandler) GetSessionAnalytics(c *gin.Context) {
	// TODO: Implement GetSessionAnalytics in session usecase
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Session analytics not implemented yet",
	})
}

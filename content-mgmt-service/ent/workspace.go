// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

// Workspace is the model entity for the Workspace schema.
type Workspace struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who owns this workspace
	OwnerID uuid.UUID `json:"owner_id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Hex color code for workspace theme
	Color string `json:"color,omitempty"`
	// Icon identifier for workspace
	Icon string `json:"icon,omitempty"`
	// Workspace-specific settings
	Settings map[string]interface{} `json:"settings,omitempty"`
	// Default platforms for new posts
	DefaultPlatforms []string `json:"default_platforms,omitempty"`
	// Brand voice, tone, and style guidelines
	BrandGuidelines map[string]interface{} `json:"brand_guidelines,omitempty"`
	// Whether this is a personal workspace
	IsPersonal bool `json:"is_personal,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// Total number of posts in this workspace
	PostCount int `json:"post_count,omitempty"`
	// Total number of templates in this workspace
	TemplateCount int `json:"template_count,omitempty"`
	// Last time any activity happened in this workspace
	LastActivityAt time.Time `json:"last_activity_at,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Workspace) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case workspace.FieldSettings, workspace.FieldDefaultPlatforms, workspace.FieldBrandGuidelines, workspace.FieldMetadata:
			values[i] = new([]byte)
		case workspace.FieldIsPersonal, workspace.FieldIsActive:
			values[i] = new(sql.NullBool)
		case workspace.FieldPostCount, workspace.FieldTemplateCount:
			values[i] = new(sql.NullInt64)
		case workspace.FieldName, workspace.FieldDescription, workspace.FieldColor, workspace.FieldIcon:
			values[i] = new(sql.NullString)
		case workspace.FieldLastActivityAt, workspace.FieldCreatedAt, workspace.FieldUpdatedAt, workspace.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case workspace.FieldID, workspace.FieldOwnerID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Workspace fields.
func (w *Workspace) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case workspace.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				w.ID = *value
			}
		case workspace.FieldOwnerID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field owner_id", values[i])
			} else if value != nil {
				w.OwnerID = *value
			}
		case workspace.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				w.Name = value.String
			}
		case workspace.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				w.Description = value.String
			}
		case workspace.FieldColor:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field color", values[i])
			} else if value.Valid {
				w.Color = value.String
			}
		case workspace.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				w.Icon = value.String
			}
		case workspace.FieldSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &w.Settings); err != nil {
					return fmt.Errorf("unmarshal field settings: %w", err)
				}
			}
		case workspace.FieldDefaultPlatforms:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field default_platforms", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &w.DefaultPlatforms); err != nil {
					return fmt.Errorf("unmarshal field default_platforms: %w", err)
				}
			}
		case workspace.FieldBrandGuidelines:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field brand_guidelines", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &w.BrandGuidelines); err != nil {
					return fmt.Errorf("unmarshal field brand_guidelines: %w", err)
				}
			}
		case workspace.FieldIsPersonal:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_personal", values[i])
			} else if value.Valid {
				w.IsPersonal = value.Bool
			}
		case workspace.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				w.IsActive = value.Bool
			}
		case workspace.FieldPostCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field post_count", values[i])
			} else if value.Valid {
				w.PostCount = int(value.Int64)
			}
		case workspace.FieldTemplateCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field template_count", values[i])
			} else if value.Valid {
				w.TemplateCount = int(value.Int64)
			}
		case workspace.FieldLastActivityAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_activity_at", values[i])
			} else if value.Valid {
				w.LastActivityAt = value.Time
			}
		case workspace.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &w.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case workspace.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				w.CreatedAt = value.Time
			}
		case workspace.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				w.UpdatedAt = value.Time
			}
		case workspace.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				w.DeletedAt = value.Time
			}
		default:
			w.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Workspace.
// This includes values selected through modifiers, order, etc.
func (w *Workspace) Value(name string) (ent.Value, error) {
	return w.selectValues.Get(name)
}

// Update returns a builder for updating this Workspace.
// Note that you need to call Workspace.Unwrap() before calling this method if this Workspace
// was returned from a transaction, and the transaction was committed or rolled back.
func (w *Workspace) Update() *WorkspaceUpdateOne {
	return NewWorkspaceClient(w.config).UpdateOne(w)
}

// Unwrap unwraps the Workspace entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (w *Workspace) Unwrap() *Workspace {
	_tx, ok := w.config.driver.(*txDriver)
	if !ok {
		panic("ent: Workspace is not a transactional entity")
	}
	w.config.driver = _tx.drv
	return w
}

// String implements the fmt.Stringer.
func (w *Workspace) String() string {
	var builder strings.Builder
	builder.WriteString("Workspace(")
	builder.WriteString(fmt.Sprintf("id=%v, ", w.ID))
	builder.WriteString("owner_id=")
	builder.WriteString(fmt.Sprintf("%v", w.OwnerID))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(w.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(w.Description)
	builder.WriteString(", ")
	builder.WriteString("color=")
	builder.WriteString(w.Color)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(w.Icon)
	builder.WriteString(", ")
	builder.WriteString("settings=")
	builder.WriteString(fmt.Sprintf("%v", w.Settings))
	builder.WriteString(", ")
	builder.WriteString("default_platforms=")
	builder.WriteString(fmt.Sprintf("%v", w.DefaultPlatforms))
	builder.WriteString(", ")
	builder.WriteString("brand_guidelines=")
	builder.WriteString(fmt.Sprintf("%v", w.BrandGuidelines))
	builder.WriteString(", ")
	builder.WriteString("is_personal=")
	builder.WriteString(fmt.Sprintf("%v", w.IsPersonal))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", w.IsActive))
	builder.WriteString(", ")
	builder.WriteString("post_count=")
	builder.WriteString(fmt.Sprintf("%v", w.PostCount))
	builder.WriteString(", ")
	builder.WriteString("template_count=")
	builder.WriteString(fmt.Sprintf("%v", w.TemplateCount))
	builder.WriteString(", ")
	builder.WriteString("last_activity_at=")
	builder.WriteString(w.LastActivityAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", w.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(w.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(w.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(w.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Workspaces is a parsable slice of Workspace.
type Workspaces []*Workspace

package cleanup

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Scheduler manages cleanup jobs and schedules
type Scheduler struct {
	cleanupService *Service
	logger         logging.Logger
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	running        bool
	mu             sync.RWMutex
}

// NewScheduler creates a new cleanup scheduler
func NewScheduler(cleanupService *Service, logger logging.Logger) *Scheduler {
	return &Scheduler{
		cleanupService: cleanupService,
		logger:         logger,
	}
}

// Start starts the cleanup scheduler
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return nil // Already running
	}

	s.ctx, s.cancel = context.WithCancel(context.Background())
	s.running = true

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.cleanupService.Start(s.ctx); err != nil && err != context.Canceled {
			s.logger.WithError(err).Error("Asset cleanup service error")
		}
	}()

	s.logger.Info("Asset cleanup scheduler started")
	return nil
}

// Stop stops the cleanup scheduler
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil // Already stopped
	}

	s.cancel()
	s.running = false

	// Wait for cleanup service to stop with timeout
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("Asset cleanup scheduler stopped")
		return nil
	case <-time.After(30 * time.Second):
		s.logger.Warn("Asset cleanup scheduler stop timeout")
		return fmt.Errorf("cleanup scheduler stop timeout")
	}
}

// IsRunning returns whether the scheduler is running
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// TriggerCleanup manually triggers a cleanup cycle
func (s *Scheduler) TriggerCleanup() error {
	if !s.IsRunning() {
		return fmt.Errorf("scheduler is not running")
	}

	// Run cleanup in background
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cancel()

		if err := s.cleanupService.runCleanup(ctx); err != nil {
			s.logger.WithError(err).Error("Manual asset cleanup failed")
		} else {
			s.logger.Info("Manual asset cleanup completed")
		}
	}()

	return nil
}

// GetConfig returns the cleanup configuration
func (s *Scheduler) GetConfig() *Config {
	if s.cleanupService == nil {
		return nil
	}
	return s.cleanupService.config
}

// Stats represents cleanup statistics
type Stats struct {
	// Configuration
	PendingUploadTTL time.Duration `json:"pending_upload_ttl"`
	OrphanedFileTTL  time.Duration `json:"orphaned_file_ttl"`
	DeletedAssetTTL  time.Duration `json:"deleted_asset_ttl"`
	CleanupInterval  time.Duration `json:"cleanup_interval"`
	BatchSize        int           `json:"batch_size"`
	DryRun           bool          `json:"dry_run"`

	// Current counts
	PendingUploads    int `json:"pending_uploads"`
	ExpiredUploads    int `json:"expired_uploads"`
	DeletedAssets     int `json:"deleted_assets"`
	QuarantinedAssets int `json:"quarantined_assets"`

	// Cleanup history
	LastCleanupAt      *time.Time `json:"last_cleanup_at,omitempty"`
	TotalCleanupsRun   int        `json:"total_cleanups_run"`
	TotalAssetsCleaned int        `json:"total_assets_cleaned"`
}

// GetStats returns cleanup statistics
func (s *Scheduler) GetStats(ctx context.Context) (*Stats, error) {
	if s.cleanupService == nil {
		return nil, fmt.Errorf("cleanup service not available")
	}

	config := s.cleanupService.config
	readDB := s.cleanupService.readDB

	stats := &Stats{
		PendingUploadTTL: config.PendingUploadTTL,
		OrphanedFileTTL:  config.OrphanedFileTTL,
		DeletedAssetTTL:  config.DeletedAssetTTL,
		CleanupInterval:  config.CleanupInterval,
		BatchSize:        config.BatchSize,
		DryRun:           config.DryRun,
	}

	// Count pending uploads
	pendingCount, err := readDB.Asset.Query().
		Where(
			asset.Or(
				asset.StatusEQ("pending"),
				asset.StatusEQ("validating"),
				asset.StatusEQ("uploaded"),
			),
			asset.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count pending uploads")
	} else {
		stats.PendingUploads = pendingCount
	}

	// Count expired uploads
	expiredCount, err := readDB.Asset.Query().
		Where(
			asset.StatusEQ("expired"),
			asset.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count expired uploads")
	} else {
		stats.ExpiredUploads = expiredCount
	}

	// Count deleted assets
	deletedCount, err := readDB.Asset.Query().
		Where(asset.DeletedAtNotNil()).
		Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count deleted assets")
	} else {
		stats.DeletedAssets = deletedCount
	}

	// Count quarantined assets
	quarantinedCount, err := readDB.Asset.Query().
		Where(
			asset.StatusEQ("quarantined"),
			asset.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count quarantined assets")
	} else {
		stats.QuarantinedAssets = quarantinedCount
	}

	return stats, nil
}

// SetDryRun enables or disables dry-run mode
func (s *Scheduler) SetDryRun(dryRun bool) {
	if s.cleanupService != nil && s.cleanupService.config != nil {
		s.cleanupService.config.DryRun = dryRun
		s.logger.WithField("dry_run", dryRun).Info("Updated cleanup dry-run mode")
	}
}

// UpdateConfig updates cleanup configuration
func (s *Scheduler) UpdateConfig(config *Config) error {
	if s.cleanupService == nil {
		return fmt.Errorf("cleanup service not available")
	}

	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// Validate configuration
	if config.PendingUploadTTL <= 0 {
		return fmt.Errorf("pending upload TTL must be positive")
	}
	if config.OrphanedFileTTL <= 0 {
		return fmt.Errorf("orphaned file TTL must be positive")
	}
	if config.DeletedAssetTTL <= 0 {
		return fmt.Errorf("deleted asset TTL must be positive")
	}
	if config.CleanupInterval <= 0 {
		return fmt.Errorf("cleanup interval must be positive")
	}
	if config.BatchSize <= 0 {
		return fmt.Errorf("batch size must be positive")
	}

	// Update configuration
	s.cleanupService.config = config

	s.logger.WithFields(map[string]interface{}{
		"pending_upload_ttl": config.PendingUploadTTL,
		"orphaned_file_ttl":  config.OrphanedFileTTL,
		"deleted_asset_ttl":  config.DeletedAssetTTL,
		"cleanup_interval":   config.CleanupInterval,
		"batch_size":         config.BatchSize,
		"dry_run":            config.DryRun,
	}).Info("Updated cleanup configuration")

	return nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: notification/v1/notification.proto

package notificationv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NotificationServiceClient is the client API for NotificationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotificationServiceClient interface {
	// Notification sending operations
	SendNotification(ctx context.Context, in *SendNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error)
	SendTemplatedNotification(ctx context.Context, in *SendTemplatedNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error)
	SendBulkNotification(ctx context.Context, in *BulkNotificationRequest, opts ...grpc.CallOption) (*BulkNotificationResponse, error)
	// Notification management operations
	GetNotification(ctx context.Context, in *GetNotificationRequest, opts ...grpc.CallOption) (*Notification, error)
	ListNotifications(ctx context.Context, in *ListNotificationsRequest, opts ...grpc.CallOption) (*ListNotificationsResponse, error)
	MarkAsRead(ctx context.Context, in *MarkAsReadRequest, opts ...grpc.CallOption) (*Empty, error)
	// Delivery status operations
	GetDeliveryStatus(ctx context.Context, in *GetDeliveryStatusRequest, opts ...grpc.CallOption) (*DeliveryStatus, error)
	// Preference management operations
	GetPreferences(ctx context.Context, in *GetPreferencesRequest, opts ...grpc.CallOption) (*NotificationPreferences, error)
	UpdatePreferences(ctx context.Context, in *UpdatePreferencesRequest, opts ...grpc.CallOption) (*NotificationPreferences, error)
	// Event processing operations
	ProcessKafkaEvent(ctx context.Context, in *KafkaEventRequest, opts ...grpc.CallOption) (*KafkaEventResponse, error)
}

type notificationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationServiceClient(cc grpc.ClientConnInterface) NotificationServiceClient {
	return &notificationServiceClient{cc}
}

func (c *notificationServiceClient) SendNotification(ctx context.Context, in *SendNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error) {
	out := new(SendNotificationResponse)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/SendNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) SendTemplatedNotification(ctx context.Context, in *SendTemplatedNotificationRequest, opts ...grpc.CallOption) (*SendNotificationResponse, error) {
	out := new(SendNotificationResponse)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/SendTemplatedNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) SendBulkNotification(ctx context.Context, in *BulkNotificationRequest, opts ...grpc.CallOption) (*BulkNotificationResponse, error) {
	out := new(BulkNotificationResponse)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/SendBulkNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetNotification(ctx context.Context, in *GetNotificationRequest, opts ...grpc.CallOption) (*Notification, error) {
	out := new(Notification)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/GetNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) ListNotifications(ctx context.Context, in *ListNotificationsRequest, opts ...grpc.CallOption) (*ListNotificationsResponse, error) {
	out := new(ListNotificationsResponse)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/ListNotifications", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) MarkAsRead(ctx context.Context, in *MarkAsReadRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/MarkAsRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetDeliveryStatus(ctx context.Context, in *GetDeliveryStatusRequest, opts ...grpc.CallOption) (*DeliveryStatus, error) {
	out := new(DeliveryStatus)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/GetDeliveryStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetPreferences(ctx context.Context, in *GetPreferencesRequest, opts ...grpc.CallOption) (*NotificationPreferences, error) {
	out := new(NotificationPreferences)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/GetPreferences", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) UpdatePreferences(ctx context.Context, in *UpdatePreferencesRequest, opts ...grpc.CallOption) (*NotificationPreferences, error) {
	out := new(NotificationPreferences)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/UpdatePreferences", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) ProcessKafkaEvent(ctx context.Context, in *KafkaEventRequest, opts ...grpc.CallOption) (*KafkaEventResponse, error) {
	out := new(KafkaEventResponse)
	err := c.cc.Invoke(ctx, "/notification.v1.NotificationService/ProcessKafkaEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationServiceServer is the server API for NotificationService service.
// All implementations must embed UnimplementedNotificationServiceServer
// for forward compatibility
type NotificationServiceServer interface {
	// Notification sending operations
	SendNotification(context.Context, *SendNotificationRequest) (*SendNotificationResponse, error)
	SendTemplatedNotification(context.Context, *SendTemplatedNotificationRequest) (*SendNotificationResponse, error)
	SendBulkNotification(context.Context, *BulkNotificationRequest) (*BulkNotificationResponse, error)
	// Notification management operations
	GetNotification(context.Context, *GetNotificationRequest) (*Notification, error)
	ListNotifications(context.Context, *ListNotificationsRequest) (*ListNotificationsResponse, error)
	MarkAsRead(context.Context, *MarkAsReadRequest) (*Empty, error)
	// Delivery status operations
	GetDeliveryStatus(context.Context, *GetDeliveryStatusRequest) (*DeliveryStatus, error)
	// Preference management operations
	GetPreferences(context.Context, *GetPreferencesRequest) (*NotificationPreferences, error)
	UpdatePreferences(context.Context, *UpdatePreferencesRequest) (*NotificationPreferences, error)
	// Event processing operations
	ProcessKafkaEvent(context.Context, *KafkaEventRequest) (*KafkaEventResponse, error)
	mustEmbedUnimplementedNotificationServiceServer()
}

// UnimplementedNotificationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNotificationServiceServer struct {
}

func (UnimplementedNotificationServiceServer) SendNotification(context.Context, *SendNotificationRequest) (*SendNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendNotification not implemented")
}
func (UnimplementedNotificationServiceServer) SendTemplatedNotification(context.Context, *SendTemplatedNotificationRequest) (*SendNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTemplatedNotification not implemented")
}
func (UnimplementedNotificationServiceServer) SendBulkNotification(context.Context, *BulkNotificationRequest) (*BulkNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBulkNotification not implemented")
}
func (UnimplementedNotificationServiceServer) GetNotification(context.Context, *GetNotificationRequest) (*Notification, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotification not implemented")
}
func (UnimplementedNotificationServiceServer) ListNotifications(context.Context, *ListNotificationsRequest) (*ListNotificationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNotifications not implemented")
}
func (UnimplementedNotificationServiceServer) MarkAsRead(context.Context, *MarkAsReadRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkAsRead not implemented")
}
func (UnimplementedNotificationServiceServer) GetDeliveryStatus(context.Context, *GetDeliveryStatusRequest) (*DeliveryStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeliveryStatus not implemented")
}
func (UnimplementedNotificationServiceServer) GetPreferences(context.Context, *GetPreferencesRequest) (*NotificationPreferences, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreferences not implemented")
}
func (UnimplementedNotificationServiceServer) UpdatePreferences(context.Context, *UpdatePreferencesRequest) (*NotificationPreferences, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePreferences not implemented")
}
func (UnimplementedNotificationServiceServer) ProcessKafkaEvent(context.Context, *KafkaEventRequest) (*KafkaEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessKafkaEvent not implemented")
}
func (UnimplementedNotificationServiceServer) mustEmbedUnimplementedNotificationServiceServer() {}

// UnsafeNotificationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationServiceServer will
// result in compilation errors.
type UnsafeNotificationServiceServer interface {
	mustEmbedUnimplementedNotificationServiceServer()
}

func RegisterNotificationServiceServer(s grpc.ServiceRegistrar, srv NotificationServiceServer) {
	s.RegisterService(&NotificationService_ServiceDesc, srv)
}

func _NotificationService_SendNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).SendNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/SendNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).SendNotification(ctx, req.(*SendNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_SendTemplatedNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTemplatedNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).SendTemplatedNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/SendTemplatedNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).SendTemplatedNotification(ctx, req.(*SendTemplatedNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_SendBulkNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).SendBulkNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/SendBulkNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).SendBulkNotification(ctx, req.(*BulkNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/GetNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetNotification(ctx, req.(*GetNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_ListNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNotificationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).ListNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/ListNotifications",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).ListNotifications(ctx, req.(*ListNotificationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_MarkAsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).MarkAsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/MarkAsRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).MarkAsRead(ctx, req.(*MarkAsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetDeliveryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeliveryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetDeliveryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/GetDeliveryStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetDeliveryStatus(ctx, req.(*GetDeliveryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetPreferences_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreferencesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetPreferences(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/GetPreferences",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetPreferences(ctx, req.(*GetPreferencesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_UpdatePreferences_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePreferencesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).UpdatePreferences(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/UpdatePreferences",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).UpdatePreferences(ctx, req.(*UpdatePreferencesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_ProcessKafkaEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KafkaEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).ProcessKafkaEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notification.v1.NotificationService/ProcessKafkaEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).ProcessKafkaEvent(ctx, req.(*KafkaEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationService_ServiceDesc is the grpc.ServiceDesc for NotificationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "notification.v1.NotificationService",
	HandlerType: (*NotificationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendNotification",
			Handler:    _NotificationService_SendNotification_Handler,
		},
		{
			MethodName: "SendTemplatedNotification",
			Handler:    _NotificationService_SendTemplatedNotification_Handler,
		},
		{
			MethodName: "SendBulkNotification",
			Handler:    _NotificationService_SendBulkNotification_Handler,
		},
		{
			MethodName: "GetNotification",
			Handler:    _NotificationService_GetNotification_Handler,
		},
		{
			MethodName: "ListNotifications",
			Handler:    _NotificationService_ListNotifications_Handler,
		},
		{
			MethodName: "MarkAsRead",
			Handler:    _NotificationService_MarkAsRead_Handler,
		},
		{
			MethodName: "GetDeliveryStatus",
			Handler:    _NotificationService_GetDeliveryStatus_Handler,
		},
		{
			MethodName: "GetPreferences",
			Handler:    _NotificationService_GetPreferences_Handler,
		},
		{
			MethodName: "UpdatePreferences",
			Handler:    _NotificationService_UpdatePreferences_Handler,
		},
		{
			MethodName: "ProcessKafkaEvent",
			Handler:    _NotificationService_ProcessKafkaEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "notification/v1/notification.proto",
}

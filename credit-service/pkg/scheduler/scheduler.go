package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Scheduler manages all scheduled jobs for credit service
type Scheduler struct {
	cron                    *cron.Cron
	config                  *Config
	logger                  logging.Logger
	readDB                  *ent.Client
	writeDB                 *ent.Client
	creditUC                credit.UseCase
	
	// Services
	monthlyCreditRenewal    *MonthlyCreditRenewalService
	
	// State management
	running                 bool
	startedAt               *time.Time
	mu                      sync.RWMutex
	
	// Job tracking
	jobEntries              map[string]cron.EntryID
}

// NewScheduler creates a new scheduler instance
func NewScheduler(
	config *Config,
	readDB *ent.Client,
	writeDB *ent.Client,
	creditUC credit.UseCase,
	logger logging.Logger,
) (*Scheduler, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid scheduler config: %w", err)
	}

	// Create cron scheduler with timezone support
	location, err := time.LoadLocation(config.MonthlyCreditRenewal.Timezone)
	if err != nil {
		logger.WithError(err).Warn("Failed to load timezone, using UTC")
		location = time.UTC
	}

	cronScheduler := cron.New(
		cron.WithLocation(location),
		cron.WithSeconds(), // Enable seconds precision
		cron.WithChain(
			cron.SkipIfStillRunning(cron.DefaultLogger),
			cron.Recover(cron.DefaultLogger),
		),
	)

	scheduler := &Scheduler{
		cron:       cronScheduler,
		config:     config,
		logger:     logger,
		readDB:     readDB,
		writeDB:    writeDB,
		creditUC:   creditUC,
		jobEntries: make(map[string]cron.EntryID),
	}

	// Initialize services
	scheduler.monthlyCreditRenewal = NewMonthlyCreditRenewalService(
		readDB,
		writeDB,
		creditUC,
		config.MonthlyCreditRenewal,
		logger.WithField("service", "monthly_credit_renewal"),
	)

	return scheduler, nil
}

// Start starts the scheduler and all configured jobs
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	s.logger.Info("Starting credit service scheduler")

	// Schedule monthly credit renewal job
	if s.config.MonthlyCreditRenewal.Enabled {
		entryID, err := s.cron.AddFunc(s.config.MonthlyCreditRenewal.CronSpec, func() {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
			defer cancel()

			if err := s.monthlyCreditRenewal.Run(ctx); err != nil {
				s.logger.WithError(err).Error("Monthly credit renewal job failed")
			}
		})
		if err != nil {
			return fmt.Errorf("failed to schedule monthly credit renewal job: %w", err)
		}
		s.jobEntries["monthly_credit_renewal"] = entryID
		s.logger.WithField("cron_spec", s.config.MonthlyCreditRenewal.CronSpec).Info("Scheduled monthly credit renewal job")
	}

	// Schedule credit expiration cleanup job
	if s.config.CreditExpirationCleanup.Enabled {
		entryID, err := s.cron.AddFunc(s.config.CreditExpirationCleanup.CronSpec, func() {
			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
			defer cancel()

			if err := s.runCreditExpirationCleanup(ctx); err != nil {
				s.logger.WithError(err).Error("Credit expiration cleanup job failed")
			}
		})
		if err != nil {
			return fmt.Errorf("failed to schedule credit expiration cleanup job: %w", err)
		}
		s.jobEntries["credit_expiration_cleanup"] = entryID
		s.logger.WithField("cron_spec", s.config.CreditExpirationCleanup.CronSpec).Info("Scheduled credit expiration cleanup job")
	}

	// Schedule low credit warning job
	if s.config.LowCreditWarning.Enabled {
		entryID, err := s.cron.AddFunc(s.config.LowCreditWarning.CronSpec, func() {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
			defer cancel()

			if err := s.runLowCreditWarning(ctx); err != nil {
				s.logger.WithError(err).Error("Low credit warning job failed")
			}
		})
		if err != nil {
			return fmt.Errorf("failed to schedule low credit warning job: %w", err)
		}
		s.jobEntries["low_credit_warning"] = entryID
		s.logger.WithField("cron_spec", s.config.LowCreditWarning.CronSpec).Info("Scheduled low credit warning job")
	}

	// Start the cron scheduler
	s.cron.Start()
	now := time.Now()
	s.startedAt = &now
	s.running = true

	s.logger.WithFields(map[string]interface{}{
		"jobs_scheduled": len(s.jobEntries),
		"started_at":     s.startedAt,
	}).Info("Credit service scheduler started successfully")

	return nil
}

// Stop stops the scheduler and all running jobs
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil // Already stopped
	}

	s.logger.Info("Stopping credit service scheduler")

	// Stop the cron scheduler
	ctx := s.cron.Stop()
	
	// Wait for all jobs to complete with timeout
	select {
	case <-ctx.Done():
		s.logger.Info("All scheduled jobs stopped gracefully")
	case <-time.After(30 * time.Second):
		s.logger.Warn("Scheduler stop timeout - some jobs may still be running")
	}

	s.running = false
	s.startedAt = nil
	s.jobEntries = make(map[string]cron.EntryID)

	s.logger.Info("Credit service scheduler stopped")
	return nil
}

// IsRunning returns whether the scheduler is currently running
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// GetStats returns scheduler statistics
func (s *Scheduler) GetStats() *SchedulerStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	stats := &SchedulerStats{
		IsRunning: s.running,
		StartedAt: s.startedAt,
		Jobs:      make([]JobStats, 0),
	}

	// Add job statistics
	if s.monthlyCreditRenewal != nil {
		jobStats := *s.monthlyCreditRenewal.GetStats()
		
		// Add next run time if job is scheduled
		if entryID, exists := s.jobEntries["monthly_credit_renewal"]; exists {
			entry := s.cron.Entry(entryID)
			if !entry.Next.IsZero() {
				jobStats.NextRunAt = &entry.Next
			}
		}
		
		stats.Jobs = append(stats.Jobs, jobStats)
	}

	return stats
}

// TriggerMonthlyCreditRenewal manually triggers the monthly credit renewal job
func (s *Scheduler) TriggerMonthlyCreditRenewal(ctx context.Context) error {
	if !s.IsRunning() {
		return fmt.Errorf("scheduler is not running")
	}

	s.logger.Info("Manually triggering monthly credit renewal job")
	
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()

		if err := s.monthlyCreditRenewal.Run(ctx); err != nil {
			s.logger.WithError(err).Error("Manual monthly credit renewal job failed")
		} else {
			s.logger.Info("Manual monthly credit renewal job completed successfully")
		}
	}()

	return nil
}

// UpdateConfig updates the scheduler configuration
func (s *Scheduler) UpdateConfig(config *Config) error {
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid scheduler config: %w", err)
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.config = config

	// Update service configurations
	if s.monthlyCreditRenewal != nil {
		s.monthlyCreditRenewal.UpdateConfig(config.MonthlyCreditRenewal)
	}

	s.logger.Info("Scheduler configuration updated")
	return nil
}

// runCreditExpirationCleanup runs the credit expiration cleanup job
func (s *Scheduler) runCreditExpirationCleanup(ctx context.Context) error {
	// TODO: Implement credit expiration cleanup logic
	s.logger.Info("Running credit expiration cleanup job (placeholder)")
	return nil
}

// runLowCreditWarning runs the low credit warning job
func (s *Scheduler) runLowCreditWarning(ctx context.Context) error {
	// TODO: Implement low credit warning logic
	s.logger.Info("Running low credit warning job (placeholder)")
	return nil
}

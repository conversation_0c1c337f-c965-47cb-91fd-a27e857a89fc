package events

// Event type constants
const (
	// Credit events
	EventTypeCreditAdded          = "credit.added"
	EventTypeCreditDeducted       = "credit.deducted"
	EventTypeCreditTransferred    = "credit.transferred"
	EventTypeCreditBalanceUpdated = "credit.balance_updated"

	// Subscription events
	EventTypeSubscriptionActivated  = "subscription.activated"
	EventTypeSubscriptionCancelled  = "subscription.cancelled"
	EventTypeSubscriptionRenewed    = "subscription.renewed"
	EventTypeSubscriptionExpired    = "subscription.expired"
	EventTypeSubscriptionUpgraded   = "subscription.upgraded"
	EventTypeSubscriptionDowngraded = "subscription.downgraded"

	// Subscription Payment events (for Kafka topics)
	EventTypeSubscriptionPaymentCompleted = "subscription.payment.completed"
	EventTypeSubscriptionPaymentFailed    = "subscription.payment.failed"
	EventTypeSubscriptionActivatedKafka   = "subscription.activated"
	EventTypeSubscriptionCancelledKafka   = "subscription.cancelled"
	EventTypeSubscriptionGeneral          = "subscription.events"

	// Payment events
	EventTypePaymentSucceeded = "payment.succeeded"
	EventTypePaymentFailed    = "payment.failed"
	EventTypePaymentRefunded  = "payment.refunded"

	// Notification events
	EventTypeNotificationSend = "notification.send"
)

// Event source constants
const (
	SourceCreditService       = "credit-service"
	SourcePaymentService      = "payment-service"
	SourceSubscriptionService = "subscription-service"
)

// Event version
const (
	EventVersion = "1.0"
)

// Kafka Topic Constants
const (
	TopicSubscriptionEvents           = "subscription.events"
	TopicSubscriptionPaymentCompleted = "subscription.payment.completed"
	TopicSubscriptionPaymentFailed    = "subscription.payment.failed"
	TopicSubscriptionActivated        = "subscription.activated"
	TopicSubscriptionCancelled        = "subscription.cancelled"
)

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/predicate"
)

// PlatformIntegrationQuery is the builder for querying PlatformIntegration entities.
type PlatformIntegrationQuery struct {
	config
	ctx        *QueryContext
	order      []platformintegration.OrderOption
	inters     []Interceptor
	predicates []predicate.PlatformIntegration
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the PlatformIntegrationQuery builder.
func (piq *PlatformIntegrationQuery) Where(ps ...predicate.PlatformIntegration) *PlatformIntegrationQuery {
	piq.predicates = append(piq.predicates, ps...)
	return piq
}

// Limit the number of records to be returned by this query.
func (piq *PlatformIntegrationQuery) Limit(limit int) *PlatformIntegrationQuery {
	piq.ctx.Limit = &limit
	return piq
}

// Offset to start from.
func (piq *PlatformIntegrationQuery) Offset(offset int) *PlatformIntegrationQuery {
	piq.ctx.Offset = &offset
	return piq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (piq *PlatformIntegrationQuery) Unique(unique bool) *PlatformIntegrationQuery {
	piq.ctx.Unique = &unique
	return piq
}

// Order specifies how the records should be ordered.
func (piq *PlatformIntegrationQuery) Order(o ...platformintegration.OrderOption) *PlatformIntegrationQuery {
	piq.order = append(piq.order, o...)
	return piq
}

// First returns the first PlatformIntegration entity from the query.
// Returns a *NotFoundError when no PlatformIntegration was found.
func (piq *PlatformIntegrationQuery) First(ctx context.Context) (*PlatformIntegration, error) {
	nodes, err := piq.Limit(1).All(setContextOp(ctx, piq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{platformintegration.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) FirstX(ctx context.Context) *PlatformIntegration {
	node, err := piq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first PlatformIntegration ID from the query.
// Returns a *NotFoundError when no PlatformIntegration ID was found.
func (piq *PlatformIntegrationQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = piq.Limit(1).IDs(setContextOp(ctx, piq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{platformintegration.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := piq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single PlatformIntegration entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one PlatformIntegration entity is found.
// Returns a *NotFoundError when no PlatformIntegration entities are found.
func (piq *PlatformIntegrationQuery) Only(ctx context.Context) (*PlatformIntegration, error) {
	nodes, err := piq.Limit(2).All(setContextOp(ctx, piq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{platformintegration.Label}
	default:
		return nil, &NotSingularError{platformintegration.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) OnlyX(ctx context.Context) *PlatformIntegration {
	node, err := piq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only PlatformIntegration ID in the query.
// Returns a *NotSingularError when more than one PlatformIntegration ID is found.
// Returns a *NotFoundError when no entities are found.
func (piq *PlatformIntegrationQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = piq.Limit(2).IDs(setContextOp(ctx, piq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{platformintegration.Label}
	default:
		err = &NotSingularError{platformintegration.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := piq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of PlatformIntegrations.
func (piq *PlatformIntegrationQuery) All(ctx context.Context) ([]*PlatformIntegration, error) {
	ctx = setContextOp(ctx, piq.ctx, ent.OpQueryAll)
	if err := piq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*PlatformIntegration, *PlatformIntegrationQuery]()
	return withInterceptors[[]*PlatformIntegration](ctx, piq, qr, piq.inters)
}

// AllX is like All, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) AllX(ctx context.Context) []*PlatformIntegration {
	nodes, err := piq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of PlatformIntegration IDs.
func (piq *PlatformIntegrationQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if piq.ctx.Unique == nil && piq.path != nil {
		piq.Unique(true)
	}
	ctx = setContextOp(ctx, piq.ctx, ent.OpQueryIDs)
	if err = piq.Select(platformintegration.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := piq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (piq *PlatformIntegrationQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, piq.ctx, ent.OpQueryCount)
	if err := piq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, piq, querierCount[*PlatformIntegrationQuery](), piq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) CountX(ctx context.Context) int {
	count, err := piq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (piq *PlatformIntegrationQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, piq.ctx, ent.OpQueryExist)
	switch _, err := piq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (piq *PlatformIntegrationQuery) ExistX(ctx context.Context) bool {
	exist, err := piq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the PlatformIntegrationQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (piq *PlatformIntegrationQuery) Clone() *PlatformIntegrationQuery {
	if piq == nil {
		return nil
	}
	return &PlatformIntegrationQuery{
		config:     piq.config,
		ctx:        piq.ctx.Clone(),
		order:      append([]platformintegration.OrderOption{}, piq.order...),
		inters:     append([]Interceptor{}, piq.inters...),
		predicates: append([]predicate.PlatformIntegration{}, piq.predicates...),
		// clone intermediate query.
		sql:  piq.sql.Clone(),
		path: piq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.PlatformIntegration.Query().
//		GroupBy(platformintegration.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (piq *PlatformIntegrationQuery) GroupBy(field string, fields ...string) *PlatformIntegrationGroupBy {
	piq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &PlatformIntegrationGroupBy{build: piq}
	grbuild.flds = &piq.ctx.Fields
	grbuild.label = platformintegration.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.PlatformIntegration.Query().
//		Select(platformintegration.FieldUserID).
//		Scan(ctx, &v)
func (piq *PlatformIntegrationQuery) Select(fields ...string) *PlatformIntegrationSelect {
	piq.ctx.Fields = append(piq.ctx.Fields, fields...)
	sbuild := &PlatformIntegrationSelect{PlatformIntegrationQuery: piq}
	sbuild.label = platformintegration.Label
	sbuild.flds, sbuild.scan = &piq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a PlatformIntegrationSelect configured with the given aggregations.
func (piq *PlatformIntegrationQuery) Aggregate(fns ...AggregateFunc) *PlatformIntegrationSelect {
	return piq.Select().Aggregate(fns...)
}

func (piq *PlatformIntegrationQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range piq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, piq); err != nil {
				return err
			}
		}
	}
	for _, f := range piq.ctx.Fields {
		if !platformintegration.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if piq.path != nil {
		prev, err := piq.path(ctx)
		if err != nil {
			return err
		}
		piq.sql = prev
	}
	return nil
}

func (piq *PlatformIntegrationQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*PlatformIntegration, error) {
	var (
		nodes = []*PlatformIntegration{}
		_spec = piq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*PlatformIntegration).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &PlatformIntegration{config: piq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, piq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (piq *PlatformIntegrationQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := piq.querySpec()
	_spec.Node.Columns = piq.ctx.Fields
	if len(piq.ctx.Fields) > 0 {
		_spec.Unique = piq.ctx.Unique != nil && *piq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, piq.driver, _spec)
}

func (piq *PlatformIntegrationQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(platformintegration.Table, platformintegration.Columns, sqlgraph.NewFieldSpec(platformintegration.FieldID, field.TypeUUID))
	_spec.From = piq.sql
	if unique := piq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if piq.path != nil {
		_spec.Unique = true
	}
	if fields := piq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, platformintegration.FieldID)
		for i := range fields {
			if fields[i] != platformintegration.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := piq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := piq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := piq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := piq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (piq *PlatformIntegrationQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(piq.driver.Dialect())
	t1 := builder.Table(platformintegration.Table)
	columns := piq.ctx.Fields
	if len(columns) == 0 {
		columns = platformintegration.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if piq.sql != nil {
		selector = piq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if piq.ctx.Unique != nil && *piq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range piq.predicates {
		p(selector)
	}
	for _, p := range piq.order {
		p(selector)
	}
	if offset := piq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := piq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// PlatformIntegrationGroupBy is the group-by builder for PlatformIntegration entities.
type PlatformIntegrationGroupBy struct {
	selector
	build *PlatformIntegrationQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (pigb *PlatformIntegrationGroupBy) Aggregate(fns ...AggregateFunc) *PlatformIntegrationGroupBy {
	pigb.fns = append(pigb.fns, fns...)
	return pigb
}

// Scan applies the selector query and scans the result into the given value.
func (pigb *PlatformIntegrationGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pigb.build.ctx, ent.OpQueryGroupBy)
	if err := pigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PlatformIntegrationQuery, *PlatformIntegrationGroupBy](ctx, pigb.build, pigb, pigb.build.inters, v)
}

func (pigb *PlatformIntegrationGroupBy) sqlScan(ctx context.Context, root *PlatformIntegrationQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(pigb.fns))
	for _, fn := range pigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*pigb.flds)+len(pigb.fns))
		for _, f := range *pigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*pigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// PlatformIntegrationSelect is the builder for selecting fields of PlatformIntegration entities.
type PlatformIntegrationSelect struct {
	*PlatformIntegrationQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (pis *PlatformIntegrationSelect) Aggregate(fns ...AggregateFunc) *PlatformIntegrationSelect {
	pis.fns = append(pis.fns, fns...)
	return pis
}

// Scan applies the selector query and scans the result into the given value.
func (pis *PlatformIntegrationSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pis.ctx, ent.OpQuerySelect)
	if err := pis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PlatformIntegrationQuery, *PlatformIntegrationSelect](ctx, pis.PlatformIntegrationQuery, pis, pis.inters, v)
}

func (pis *PlatformIntegrationSelect) sqlScan(ctx context.Context, root *PlatformIntegrationQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(pis.fns))
	for _, fn := range pis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*pis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

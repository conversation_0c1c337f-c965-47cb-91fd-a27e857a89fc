# Asset Service Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50054
  http_port: 8084
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"

  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "asset_service_db"
  ssl_mode: "disable"

  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/asset_service.db"

  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Storage configuration
storage:
  provider: "minio"  # "minio" or "s3"
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  bucket: "assets"
  use_ssl: false
  public_url: "http://localhost:9000"
  region: "us-east-1"

# Cleanup configuration
cleanup:
  enabled: true
  pending_upload_ttl: "1h"      # Delete pending uploads after 1 hour
  orphaned_file_ttl: "24h"      # Delete orphaned files after 24 hours
  deleted_asset_ttl: "720h"     # Delete soft-deleted assets after 30 days
  cleanup_interval: "30m"       # Run cleanup every 30 minutes
  batch_size: 100               # Process 100 assets per batch
  enable_storage_cleanup: true  # Whether to cleanup storage files
  dry_run: false                # Whether to run in dry-run mode

# Security configuration
security:
  max_file_size: 104857600      # 100MB max file size
  allowed_mime_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"
    - "application/json"
    - "text/markdown"
    - "text/csv"
  enable_virus_scanning: true
  quarantine_ttl: "720h"        # Keep quarantined files for 30 days

# Feature flags
features:
  enable_image_processing: true
  enable_rag: true
  enable_folders: true
  enable_versioning: false

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# JWT configuration (for token validation)
jwt:
  secret_key: "your-secret-key-here"
  token_duration: "24h"
  refresh_duration: "168h"

# Kafka configuration (for event publishing)
kafka:
  enabled: false
  brokers:
    - "localhost:9092"
  topics:
    asset_events: "asset.events"
    notification_events: "notification.events"
  producer:
    timeout: "10s"
    retries: 3
    batch_size: 100
    flush_frequency: "1s"

# External services configuration
services:
  user_service:
    host: "localhost"
    port: 50051
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "localhost"
    port: 50055
    timeout: "60s"
    retries: 2

# Monitoring configuration
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: "30s"
  
# Rate limiting configuration
rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 20
  upload_requests_per_minute: 10
  upload_burst: 5

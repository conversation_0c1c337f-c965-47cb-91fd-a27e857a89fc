package scheduler

import (
	"fmt"
	"time"
)

// Config holds scheduler configuration
type Config struct {
	// Monthly credit renewal settings
	MonthlyCreditRenewal MonthlyCreditRenewalConfig `json:"monthly_credit_renewal"`

	// Credit expiration cleanup settings
	CreditExpirationCleanup CreditExpirationCleanupConfig `json:"credit_expiration_cleanup"`

	// Low credit warning settings
	LowCreditWarning LowCreditWarningConfig `json:"low_credit_warning"`
}

// MonthlyCreditRenewalConfig configures monthly credit renewal job
type MonthlyCreditRenewalConfig struct {
	Enabled    bool          `json:"enabled"`     // Whether to run monthly renewal
	CronSpec   string        `json:"cron_spec"`   // Cron expression (default: "0 0 1 * *" - 1st day of month at midnight)
	BatchSize  int           `json:"batch_size"`  // Number of subscriptions to process per batch
	MaxRetries int           `json:"max_retries"` // Maximum retry attempts for failed renewals
	RetryDelay time.Duration `json:"retry_delay"` // Delay between retries
	DryRun     bool          `json:"dry_run"`     // If true, only log what would be done
	Timezone   string        `json:"timezone"`    // Timezone for scheduling (default: "UTC")
}

// CreditExpirationCleanupConfig configures credit expiration cleanup job
type CreditExpirationCleanupConfig struct {
	Enabled     bool          `json:"enabled"`      // Whether to run expiration cleanup
	CronSpec    string        `json:"cron_spec"`    // Cron expression (default: "0 2 * * *" - daily at 2 AM)
	BatchSize   int           `json:"batch_size"`   // Number of expired credits to process per batch
	GracePeriod time.Duration `json:"grace_period"` // Grace period before actually expiring credits
	DryRun      bool          `json:"dry_run"`      // If true, only log what would be done
}

// LowCreditWarningConfig configures low credit warning job
type LowCreditWarningConfig struct {
	Enabled           bool          `json:"enabled"`            // Whether to send low credit warnings
	CronSpec          string        `json:"cron_spec"`          // Cron expression (default: "0 9 * * *" - daily at 9 AM)
	BatchSize         int           `json:"batch_size"`         // Number of users to check per batch
	WarningThresholds []int         `json:"warning_thresholds"` // Credit thresholds to warn at (e.g., [10, 5, 1])
	CooldownPeriod    time.Duration `json:"cooldown_period"`    // Minimum time between warnings for same user
	DryRun            bool          `json:"dry_run"`            // If true, only log what would be done
}

// DefaultConfig returns default scheduler configuration
func DefaultConfig() *Config {
	return &Config{
		MonthlyCreditRenewal: MonthlyCreditRenewalConfig{
			Enabled:    true,
			CronSpec:   "0 0 1 * *", // 1st day of month at midnight UTC
			BatchSize:  100,
			MaxRetries: 3,
			RetryDelay: 5 * time.Minute,
			DryRun:     false,
			Timezone:   "UTC",
		},
		CreditExpirationCleanup: CreditExpirationCleanupConfig{
			Enabled:     true,
			CronSpec:    "0 2 * * *", // Daily at 2 AM UTC
			BatchSize:   500,
			GracePeriod: 7 * 24 * time.Hour, // 7 days grace period
			DryRun:      false,
		},
		LowCreditWarning: LowCreditWarningConfig{
			Enabled:           true,
			CronSpec:          "0 9 * * *", // Daily at 9 AM UTC
			BatchSize:         200,
			WarningThresholds: []int{10, 5, 1}, // Warn at 10, 5, and 1 credits
			CooldownPeriod:    24 * time.Hour,  // Don't spam warnings
			DryRun:            false,
		},
	}
}

// Validate validates the scheduler configuration
func (c *Config) Validate() error {
	// Validate monthly credit renewal config
	if c.MonthlyCreditRenewal.Enabled {
		if c.MonthlyCreditRenewal.CronSpec == "" {
			return fmt.Errorf("monthly credit renewal cron spec cannot be empty")
		}
		if c.MonthlyCreditRenewal.BatchSize <= 0 {
			return fmt.Errorf("monthly credit renewal batch size must be positive")
		}
		if c.MonthlyCreditRenewal.MaxRetries < 0 {
			return fmt.Errorf("monthly credit renewal max retries cannot be negative")
		}
	}

	// Validate credit expiration cleanup config
	if c.CreditExpirationCleanup.Enabled {
		if c.CreditExpirationCleanup.CronSpec == "" {
			return fmt.Errorf("credit expiration cleanup cron spec cannot be empty")
		}
		if c.CreditExpirationCleanup.BatchSize <= 0 {
			return fmt.Errorf("credit expiration cleanup batch size must be positive")
		}
	}

	// Validate low credit warning config
	if c.LowCreditWarning.Enabled {
		if c.LowCreditWarning.CronSpec == "" {
			return fmt.Errorf("low credit warning cron spec cannot be empty")
		}
		if c.LowCreditWarning.BatchSize <= 0 {
			return fmt.Errorf("low credit warning batch size must be positive")
		}
		if len(c.LowCreditWarning.WarningThresholds) == 0 {
			return fmt.Errorf("low credit warning thresholds cannot be empty")
		}
	}

	return nil
}

// JobStats represents statistics for a scheduled job
type JobStats struct {
	JobName         string        `json:"job_name"`
	LastRunAt       *time.Time    `json:"last_run_at,omitempty"`
	LastRunStatus   string        `json:"last_run_status"` // "success", "failed", "running"
	LastRunDuration time.Duration `json:"last_run_duration"`
	LastError       string        `json:"last_error,omitempty"`
	TotalRuns       int64         `json:"total_runs"`
	SuccessfulRuns  int64         `json:"successful_runs"`
	FailedRuns      int64         `json:"failed_runs"`
	NextRunAt       *time.Time    `json:"next_run_at,omitempty"`
}

// SchedulerStats represents overall scheduler statistics
type SchedulerStats struct {
	IsRunning bool       `json:"is_running"`
	StartedAt *time.Time `json:"started_at,omitempty"`
	Jobs      []JobStats `json:"jobs"`
}

package handlers

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
)

// CreditHandler implements the CreditService gRPC server
type CreditHandler struct {
	creditv1.UnimplementedCreditServiceServer
	creditUseCase       credit.UseCase
	subscriptionUseCase subscription.UseCase
	transactionUseCase  transaction.UseCase
	logger              logging.Logger
}

// NewCreditHandler creates a new credit handler
func NewCreditHandler(
	creditUseCase credit.UseCase,
	subscriptionUseCase subscription.UseCase,
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *CreditHandler {
	return &CreditHandler{
		creditUseCase:       creditUseCase,
		subscriptionUseCase: subscriptionUseCase,
		transactionUseCase:  transactionUseCase,
		logger:              logger,
	}
}

// GetUserCredit retrieves user's credit information
func (h *CreditHandler) GetUserCredit(ctx context.Context, req *creditv1.GetUserCreditRequest) (*creditv1.UserCredit, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting user credit")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit balance")
		return nil, status.Error(codes.Internal, "failed to get credit balance")
	}

	planID := ""
	if balance.PlanID != nil {
		planID = *balance.PlanID
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
		PlanId:         planID,
		RenewalDate:    "", // TODO: Calculate renewal date
	}, nil
}

// ValidateCredit checks if user has sufficient credits
func (h *CreditHandler) ValidateCredit(ctx context.Context, req *creditv1.ValidateCreditRequest) (*creditv1.ValidateCreditResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":          req.UserId,
		"required_credits": req.RequiredCredits,
	}).Info("Validating credit")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.RequiredCredits <= 0 {
		return nil, status.Error(codes.InvalidArgument, "required_credits must be positive")
	}

	validateReq := &models.ValidateCreditsRequest{
		UserID:          req.UserId,
		RequiredCredits: int(req.RequiredCredits),
	}

	result, err := h.creditUseCase.ValidateCredits(ctx, validateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate credits")
		return nil, status.Error(codes.Internal, "failed to validate credits")
	}

	return &creditv1.ValidateCreditResponse{
		HasSufficientCredits: result.HasSufficientCredits,
		CurrentCredits:       int32(result.CurrentCredits),
		RequiredCredits:      int32(result.RequiredCredits),
		Shortage:             int32(result.Shortage),
	}, nil
}

// AddCredits adds credits to user account
func (h *CreditHandler) AddCredits(ctx context.Context, req *creditv1.AddCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"amount":  req.Amount,
		"source":  req.Source,
	}).Info("Adding credits")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.Amount <= 0 {
		return nil, status.Error(codes.InvalidArgument, "amount must be positive")
	}

	addReq := &models.AddCreditsRequest{
		UserID:      req.UserId,
		Amount:      int(req.Amount),
		Source:      req.Source,
		Description: req.Description,
		PaymentID:   req.PaymentId,
	}

	balance, err := h.creditUseCase.AddCredits(ctx, addReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add credits")
		return nil, status.Error(codes.Internal, "failed to add credits")
	}

	planID := ""
	if balance.PlanID != nil {
		planID = *balance.PlanID
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
		PlanId:         planID,
		RenewalDate:    "", // TODO: Calculate renewal date
	}, nil
}

// DeductCredits deducts credits from user account
func (h *CreditHandler) DeductCredits(ctx context.Context, req *creditv1.DeductCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserId,
		"amount":       req.Amount,
		"purpose":      req.Purpose,
		"reference_id": req.ReferenceId,
	}).Info("Deducting credits")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.Amount <= 0 {
		return nil, status.Error(codes.InvalidArgument, "amount must be positive")
	}

	deductReq := &models.DeductCreditsRequest{
		UserID:        req.UserId,
		Amount:        int(req.Amount),
		Purpose:       req.Purpose,
		Description:   req.Description,
		ReferenceID:   req.ReferenceId,
		ReferenceType: "grpc_request",
	}

	balance, err := h.creditUseCase.DeductCredits(ctx, deductReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to deduct credits")
		return nil, status.Error(codes.Internal, "failed to deduct credits")
	}

	planID := ""
	if balance.PlanID != nil {
		planID = *balance.PlanID
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
		PlanId:         planID,
		RenewalDate:    "", // TODO: Calculate renewal date
	}, nil
}

// ListTransactions lists user's credit transactions
func (h *CreditHandler) ListTransactions(ctx context.Context, req *creditv1.ListTransactionsRequest) (*creditv1.ListTransactionsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Listing transactions")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Parse pagination
	page := 1
	limit := 20
	if req.Pagination != nil {
		if req.Pagination.Page > 0 {
			page = int(req.Pagination.Page)
		}
		if req.Pagination.Limit > 0 {
			limit = int(req.Pagination.Limit)
		}
	}

	// Parse date filters
	var dateFrom, dateTo *time.Time
	if req.DateFrom != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			dateFrom = &parsed
		}
	}
	if req.DateTo != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			dateTo = &parsed
		}
	}

	historyReq := &models.CreditHistoryRequest{
		UserID:   req.UserId,
		Type:     req.Type,
		DateFrom: dateFrom,
		DateTo:   dateTo,
		Page:     page,
		Limit:    limit,
	}

	history, err := h.creditUseCase.GetCreditHistory(ctx, historyReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit history")
		return nil, status.Error(codes.Internal, "failed to get credit history")
	}

	// Convert transactions
	var transactions []*creditv1.CreditTransaction
	totalEarned := int32(0)
	totalSpent := int32(0)

	for _, tx := range history.Transactions {
		transactions = append(transactions, &creditv1.CreditTransaction{
			Id:          tx.ID,
			UserId:      tx.UserID,
			Type:        tx.Type,
			Amount:      int32(tx.Amount),
			Description: tx.Description,
			ReferenceId: tx.ReferenceID,
			CreatedAt:   timestamppb.New(tx.CreatedAt),
		})

		if tx.Amount > 0 {
			totalEarned += int32(tx.Amount)
		} else {
			totalSpent += int32(-tx.Amount)
		}
	}

	return &creditv1.ListTransactionsResponse{
		Transactions:       transactions,
		TotalCreditsEarned: totalEarned,
		TotalCreditsSpent:  totalSpent,
		// TODO: Add pagination response
	}, nil
}

// CreateSubscription creates a new subscription
func (h *CreditHandler) CreateSubscription(ctx context.Context, req *creditv1.CreateSubscriptionRequest) (*creditv1.CreateSubscriptionResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":           req.UserId,
		"plan_id":           req.PlanId,
		"payment_type":      req.PaymentType,
		"payment_method_id": req.PaymentMethodId,
	}).Info("Creating subscription")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.PlanId == "" {
		return nil, status.Error(codes.InvalidArgument, "plan_id is required")
	}

	// TODO: Implement subscription creation
	return nil, status.Error(codes.Unimplemented, "CreateSubscription not implemented yet")
}

// GetSubscription retrieves user's subscription
func (h *CreditHandler) GetSubscription(ctx context.Context, req *creditv1.GetSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":         req.UserId,
		"subscription_id": req.SubscriptionId,
	}).Info("Getting subscription")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement get subscription
	return nil, status.Error(codes.Unimplemented, "GetSubscription not implemented yet")
}

// ListSubscriptions lists user's subscriptions
func (h *CreditHandler) ListSubscriptions(ctx context.Context, req *creditv1.ListSubscriptionsRequest) (*creditv1.ListSubscriptionsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Listing subscriptions")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement list subscriptions
	return nil, status.Error(codes.Unimplemented, "ListSubscriptions not implemented yet")
}

// UpdateSubscription updates a subscription
func (h *CreditHandler) UpdateSubscription(ctx context.Context, req *creditv1.UpdateSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"user_id":         req.UserId,
		"new_plan_id":     req.NewPlanId,
	}).Info("Updating subscription")

	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "subscription_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement update subscription
	return nil, status.Error(codes.Unimplemented, "UpdateSubscription not implemented yet")
}

// CancelSubscription cancels a subscription
func (h *CreditHandler) CancelSubscription(ctx context.Context, req *creditv1.CancelSubscriptionRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"user_id":         req.UserId,
		"immediate":       req.Immediate,
		"reason":          req.Reason,
	}).Info("Cancelling subscription")

	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "subscription_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement cancel subscription
	return nil, status.Error(codes.Unimplemented, "CancelSubscription not implemented yet")
}

// GetPaymentMethods retrieves user's payment methods
func (h *CreditHandler) GetPaymentMethods(ctx context.Context, req *creditv1.GetPaymentMethodsRequest) (*creditv1.GetPaymentMethodsResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
	}).Info("Getting payment methods")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement get payment methods
	return nil, status.Error(codes.Unimplemented, "GetPaymentMethods not implemented yet")
}

// AddPaymentMethod adds a new payment method
func (h *CreditHandler) AddPaymentMethod(ctx context.Context, req *creditv1.AddPaymentMethodRequest) (*creditv1.PaymentMethod, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":        req.UserId,
		"type":           req.Type,
		"set_as_default": req.SetAsDefault,
	}).Info("Adding payment method")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.Type == "" {
		return nil, status.Error(codes.InvalidArgument, "type is required")
	}

	// TODO: Implement add payment method
	return nil, status.Error(codes.Unimplemented, "AddPaymentMethod not implemented yet")
}

// RemovePaymentMethod removes a payment method
func (h *CreditHandler) RemovePaymentMethod(ctx context.Context, req *creditv1.RemovePaymentMethodRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_method_id": req.PaymentMethodId,
		"user_id":           req.UserId,
	}).Info("Removing payment method")

	if req.PaymentMethodId == "" {
		return nil, status.Error(codes.InvalidArgument, "payment_method_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// TODO: Implement remove payment method
	return nil, status.Error(codes.Unimplemented, "RemovePaymentMethod not implemented yet")
}

// ConfirmBankTransfer confirms a bank transfer payment
func (h *CreditHandler) ConfirmBankTransfer(ctx context.Context, req *creditv1.ConfirmBankTransferRequest) (*creditv1.ConfirmBankTransferResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_id":        req.PaymentId,
		"user_id":           req.UserId,
		"confirmation_code": req.ConfirmationCode,
		"transaction_id":    req.TransactionId,
	}).Info("Confirming bank transfer")

	if req.PaymentId == "" {
		return nil, status.Error(codes.InvalidArgument, "payment_id is required")
	}

	// TODO: Implement confirm bank transfer
	return nil, status.Error(codes.Unimplemented, "ConfirmBankTransfer not implemented yet")
}

// ProcessRecurringPayment processes recurring subscription payments
func (h *CreditHandler) ProcessRecurringPayment(ctx context.Context, req *creditv1.ProcessRecurringPaymentRequest) (*creditv1.ProcessRecurringPaymentResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"force_process":   req.ForceProcess,
	}).Info("Processing recurring payment")

	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "subscription_id is required")
	}

	// TODO: Implement process recurring payment
	return nil, status.Error(codes.Unimplemented, "ProcessRecurringPayment not implemented yet")
}

// Code generated by ent, DO NOT EDIT.

package payment

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the payment type in the database.
	Label = "payment"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldOrderID holds the string denoting the order_id field in the database.
	FieldOrderID = "order_id"
	// FieldSubscriptionID holds the string denoting the subscription_id field in the database.
	FieldSubscriptionID = "subscription_id"
	// FieldPaymentMethod holds the string denoting the payment_method field in the database.
	FieldPaymentMethod = "payment_method"
	// FieldPaymentProvider holds the string denoting the payment_provider field in the database.
	FieldPaymentProvider = "payment_provider"
	// FieldExternalPaymentID holds the string denoting the external_payment_id field in the database.
	FieldExternalPaymentID = "external_payment_id"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldPaymentIntentID holds the string denoting the payment_intent_id field in the database.
	FieldPaymentIntentID = "payment_intent_id"
	// FieldChargeID holds the string denoting the charge_id field in the database.
	FieldChargeID = "charge_id"
	// FieldBillingDetails holds the string denoting the billing_details field in the database.
	FieldBillingDetails = "billing_details"
	// FieldPaymentMethodDetails holds the string denoting the payment_method_details field in the database.
	FieldPaymentMethodDetails = "payment_method_details"
	// FieldPaidAt holds the string denoting the paid_at field in the database.
	FieldPaidAt = "paid_at"
	// FieldFailedAt holds the string denoting the failed_at field in the database.
	FieldFailedAt = "failed_at"
	// FieldFailureCode holds the string denoting the failure_code field in the database.
	FieldFailureCode = "failure_code"
	// FieldFailureMessage holds the string denoting the failure_message field in the database.
	FieldFailureMessage = "failure_message"
	// FieldRefundedAmount holds the string denoting the refunded_amount field in the database.
	FieldRefundedAmount = "refunded_amount"
	// FieldRefundedAt holds the string denoting the refunded_at field in the database.
	FieldRefundedAt = "refunded_at"
	// FieldRefundReason holds the string denoting the refund_reason field in the database.
	FieldRefundReason = "refund_reason"
	// FieldWebhookData holds the string denoting the webhook_data field in the database.
	FieldWebhookData = "webhook_data"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the payment in the database.
	Table = "payments"
)

// Columns holds all SQL columns for payment fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldOrderID,
	FieldSubscriptionID,
	FieldPaymentMethod,
	FieldPaymentProvider,
	FieldExternalPaymentID,
	FieldAmount,
	FieldCurrency,
	FieldStatus,
	FieldPaymentIntentID,
	FieldChargeID,
	FieldBillingDetails,
	FieldPaymentMethodDetails,
	FieldPaidAt,
	FieldFailedAt,
	FieldFailureCode,
	FieldFailureMessage,
	FieldRefundedAmount,
	FieldRefundedAt,
	FieldRefundReason,
	FieldWebhookData,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	PaymentMethodValidator func(string) error
	// PaymentProviderValidator is a validator for the "payment_provider" field. It is called by the builders before save.
	PaymentProviderValidator func(string) error
	// ExternalPaymentIDValidator is a validator for the "external_payment_id" field. It is called by the builders before save.
	ExternalPaymentIDValidator func(string) error
	// AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	AmountValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// PaymentIntentIDValidator is a validator for the "payment_intent_id" field. It is called by the builders before save.
	PaymentIntentIDValidator func(string) error
	// ChargeIDValidator is a validator for the "charge_id" field. It is called by the builders before save.
	ChargeIDValidator func(string) error
	// FailureCodeValidator is a validator for the "failure_code" field. It is called by the builders before save.
	FailureCodeValidator func(string) error
	// FailureMessageValidator is a validator for the "failure_message" field. It is called by the builders before save.
	FailureMessageValidator func(string) error
	// DefaultRefundedAmount holds the default value on creation for the "refunded_amount" field.
	DefaultRefundedAmount int64
	// RefundedAmountValidator is a validator for the "refunded_amount" field. It is called by the builders before save.
	RefundedAmountValidator func(int64) error
	// RefundReasonValidator is a validator for the "refund_reason" field. It is called by the builders before save.
	RefundReasonValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending    Status = "pending"
	StatusProcessing Status = "processing"
	StatusCompleted  Status = "completed"
	StatusFailed     Status = "failed"
	StatusCancelled  Status = "cancelled"
	StatusRefunded   Status = "refunded"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusProcessing, StatusCompleted, StatusFailed, StatusCancelled, StatusRefunded:
		return nil
	default:
		return fmt.Errorf("payment: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Payment queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByOrderID orders the results by the order_id field.
func ByOrderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderID, opts...).ToFunc()
}

// BySubscriptionID orders the results by the subscription_id field.
func BySubscriptionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSubscriptionID, opts...).ToFunc()
}

// ByPaymentMethod orders the results by the payment_method field.
func ByPaymentMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentMethod, opts...).ToFunc()
}

// ByPaymentProvider orders the results by the payment_provider field.
func ByPaymentProvider(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentProvider, opts...).ToFunc()
}

// ByExternalPaymentID orders the results by the external_payment_id field.
func ByExternalPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExternalPaymentID, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByPaymentIntentID orders the results by the payment_intent_id field.
func ByPaymentIntentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentIntentID, opts...).ToFunc()
}

// ByChargeID orders the results by the charge_id field.
func ByChargeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldChargeID, opts...).ToFunc()
}

// ByPaidAt orders the results by the paid_at field.
func ByPaidAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaidAt, opts...).ToFunc()
}

// ByFailedAt orders the results by the failed_at field.
func ByFailedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailedAt, opts...).ToFunc()
}

// ByFailureCode orders the results by the failure_code field.
func ByFailureCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailureCode, opts...).ToFunc()
}

// ByFailureMessage orders the results by the failure_message field.
func ByFailureMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailureMessage, opts...).ToFunc()
}

// ByRefundedAmount orders the results by the refunded_amount field.
func ByRefundedAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRefundedAmount, opts...).ToFunc()
}

// ByRefundedAt orders the results by the refunded_at field.
func ByRefundedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRefundedAt, opts...).ToFunc()
}

// ByRefundReason orders the results by the refund_reason field.
func ByRefundReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRefundReason, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

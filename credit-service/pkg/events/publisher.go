package events

import (
	"context"
	"fmt"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Publisher handles publishing events using shared kafka
type Publisher struct {
	producer *sharedKafka.Producer
	adapter  *EventAdapter
	logger   logging.Logger
}

// NewPublisher creates a new event publisher
func NewPublisher(kafkaConfig *sharedKafka.Config, logger logging.Logger) *Publisher {
	producer := sharedKafka.NewProducer(kafkaConfig, logger)
	return &Publisher{
		producer: producer,
		adapter:  NewEventAdapter(),
		logger:   logger,
	}
}

// PublishCreditAdded publishes a credit added event
func (p *Publisher) PublishCreditAdded(ctx context.Context, event CreditAddedEvent) error {
	sharedEvent := p.adapter.CreditAddedToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type": "credit.added",
			"user_id":    event.UserID,
			"amount":     event.Amount,
		}).Error("Failed to publish credit added event")
		return fmt.Errorf("failed to publish credit added event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type": "credit.added",
		"user_id":    event.UserID,
		"amount":     event.Amount,
		"event_id":   sharedEvent.ID,
	}).Info("Credit added event published successfully")

	return nil
}

// PublishCreditDeducted publishes a credit deducted event
func (p *Publisher) PublishCreditDeducted(ctx context.Context, event CreditDeductedEvent) error {
	sharedEvent := p.adapter.CreditDeductedToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type": "credit.deducted",
			"user_id":    event.UserID,
			"amount":     event.Amount,
		}).Error("Failed to publish credit deducted event")
		return fmt.Errorf("failed to publish credit deducted event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type": "credit.deducted",
		"user_id":    event.UserID,
		"amount":     event.Amount,
		"event_id":   sharedEvent.ID,
	}).Info("Credit deducted event published successfully")

	return nil
}

// PublishCreditTransferred publishes a credit transferred event
func (p *Publisher) PublishCreditTransferred(ctx context.Context, event CreditTransferredEvent) error {
	sharedEvent := p.adapter.CreditTransferredToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type":   "credit.transferred",
			"from_user_id": event.FromUserID,
			"to_user_id":   event.ToUserID,
			"amount":       event.Amount,
		}).Error("Failed to publish credit transferred event")
		return fmt.Errorf("failed to publish credit transferred event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type":   "credit.transferred",
		"from_user_id": event.FromUserID,
		"to_user_id":   event.ToUserID,
		"amount":       event.Amount,
		"event_id":     sharedEvent.ID,
	}).Info("Credit transferred event published successfully")

	return nil
}

// PublishSubscriptionActivated publishes a subscription activated event
func (p *Publisher) PublishSubscriptionActivated(ctx context.Context, event SubscriptionActivatedEvent) error {
	sharedEvent := p.adapter.SubscriptionActivatedToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type":      "subscription.activated",
			"user_id":         event.UserID,
			"subscription_id": event.SubscriptionID,
			"plan_id":         event.PlanID,
		}).Error("Failed to publish subscription activated event")
		return fmt.Errorf("failed to publish subscription activated event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type":      "subscription.activated",
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"event_id":        sharedEvent.ID,
	}).Info("Subscription activated event published successfully")

	return nil
}

// PublishSubscriptionCancelled publishes a subscription cancelled event
func (p *Publisher) PublishSubscriptionCancelled(ctx context.Context, event SubscriptionCancelledEvent) error {
	sharedEvent := p.adapter.SubscriptionCancelledToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type":      "subscription.cancelled",
			"user_id":         event.UserID,
			"subscription_id": event.SubscriptionID,
			"plan_id":         event.PlanID,
		}).Error("Failed to publish subscription cancelled event")
		return fmt.Errorf("failed to publish subscription cancelled event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type":      "subscription.cancelled",
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"event_id":        sharedEvent.ID,
	}).Info("Subscription cancelled event published successfully")

	return nil
}

// PublishSubscriptionRenewed publishes a subscription renewed event
func (p *Publisher) PublishSubscriptionRenewed(ctx context.Context, event SubscriptionRenewedEvent) error {
	sharedEvent := p.adapter.SubscriptionRenewedToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type":      "subscription.renewed",
			"user_id":         event.UserID,
			"subscription_id": event.SubscriptionID,
			"plan_id":         event.PlanID,
		}).Error("Failed to publish subscription renewed event")
		return fmt.Errorf("failed to publish subscription renewed event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type":      "subscription.renewed",
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"event_id":        sharedEvent.ID,
	}).Info("Subscription renewed event published successfully")

	return nil
}

// PublishPaymentSucceeded publishes a payment succeeded event
func (p *Publisher) PublishPaymentSucceeded(ctx context.Context, event PaymentSucceededEvent) error {
	sharedEvent := p.adapter.PaymentSucceededToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type": "payment.succeeded",
			"user_id":    event.UserID,
			"payment_id": event.PaymentID,
			"amount":     event.Amount,
		}).Error("Failed to publish payment succeeded event")
		return fmt.Errorf("failed to publish payment succeeded event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type": "payment.succeeded",
		"user_id":    event.UserID,
		"payment_id": event.PaymentID,
		"amount":     event.Amount,
		"event_id":   sharedEvent.ID,
	}).Info("Payment succeeded event published successfully")

	return nil
}

// PublishPaymentFailed publishes a payment failed event
func (p *Publisher) PublishPaymentFailed(ctx context.Context, event PaymentFailedEvent) error {
	sharedEvent := p.adapter.PaymentFailedToSharedEvent(event)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type": "payment.failed",
			"user_id":    event.UserID,
			"payment_id": event.PaymentID,
			"amount":     event.Amount,
		}).Error("Failed to publish payment failed event")
		return fmt.Errorf("failed to publish payment failed event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type": "payment.failed",
		"user_id":    event.UserID,
		"payment_id": event.PaymentID,
		"amount":     event.Amount,
		"event_id":   sharedEvent.ID,
	}).Info("Payment failed event published successfully")

	return nil
}

// PublishNotification publishes a notification event
func (p *Publisher) PublishNotification(ctx context.Context, event NotificationEvent) error {
	sharedEvent := p.adapter.NotificationToSharedEvent(event)

	err := p.producer.PublishEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type": "notification.send",
			"user_id":    event.UserID,
			"type":       event.Type,
			"template":   event.Template,
		}).Error("Failed to publish notification event")
		return fmt.Errorf("failed to publish notification event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type": "notification.send",
		"user_id":    event.UserID,
		"type":       event.Type,
		"template":   event.Template,
		"event_id":   sharedEvent.ID,
	}).Info("Notification event published successfully")

	return nil
}

// PublishCreditLowWarning publishes a credit low warning event
func (p *Publisher) PublishCreditLowWarning(ctx context.Context, userID string, currentCredits, threshold int) error {
	sharedEvent := p.adapter.CreditLowToSharedEvent(userID, currentCredits, threshold)

	err := p.producer.PublishBillingEvent(ctx, sharedEvent)
	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"event_type":      "credit.low",
			"user_id":         userID,
			"current_credits": currentCredits,
			"threshold":       threshold,
		}).Error("Failed to publish credit low warning event")
		return fmt.Errorf("failed to publish credit low warning event: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"event_type":      "credit.low",
		"user_id":         userID,
		"current_credits": currentCredits,
		"threshold":       threshold,
		"event_id":        sharedEvent.ID,
	}).Info("Credit low warning event published successfully")

	return nil
}

package asset

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
)

// Asset represents a file asset
type Asset struct {
	ID            string            `json:"id"`
	UserID        string            `json:"user_id"`
	WorkspaceID   string            `json:"workspace_id,omitempty"`
	TemplateID    string            `json:"template_id,omitempty"`
	FileName      string            `json:"file_name"`
	OriginalName  string            `json:"original_name"`
	FileType      string            `json:"file_type"`
	FileExtension string            `json:"file_extension"`
	FileSize      int64             `json:"file_size"`
	S3Key         string            `json:"s3_key"`
	S3Bucket      string            `json:"s3_bucket"`
	ContentHash   string            `json:"content_hash"`
	Purpose       string            `json:"purpose"`
	Status        string            `json:"status"`
	IsPublic      bool              `json:"is_public"`
	ThumbnailURL  string            `json:"thumbnail_url,omitempty"`
	Metadata      map[string]string `json:"metadata,omitempty"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
	Validation    *ValidationResult `json:"validation,omitempty"`
}

// ValidationResult contains file validation information
type ValidationResult struct {
	IsValid     bool              `json:"is_valid"`
	Errors      []string          `json:"errors,omitempty"`
	Warnings    []string          `json:"warnings,omitempty"`
	IsSafe      bool              `json:"is_safe"`
	IsCorrupted bool              `json:"is_corrupted"`
	MimeType    string            `json:"mime_type"`
	Properties  map[string]string `json:"properties,omitempty"`
	ValidatedAt time.Time         `json:"validated_at"`
}

// UploadResponse contains presigned URL information
type UploadResponse struct {
	UploadID     string            `json:"upload_id"`
	PresignedURL string            `json:"presigned_url"`
	S3Key        string            `json:"s3_key"`
	S3Bucket     string            `json:"s3_bucket"`
	ExpiresAt    time.Time         `json:"expires_at"`
	UploadFields map[string]string `json:"upload_fields,omitempty"`
}

// ConfirmUploadResponse contains upload confirmation information
type ConfirmUploadResponse struct {
	AssetID             string `json:"asset_id"`
	Status              string `json:"status"`
	ValidationTriggered bool   `json:"validation_triggered"`
	ProcessingTriggered bool   `json:"processing_triggered"`
	Asset               *Asset `json:"asset,omitempty"`
}

// DownloadURLResponse contains download URL information
type DownloadURLResponse struct {
	DownloadURL   string    `json:"download_url"`
	ExpiresAt     time.Time `json:"expires_at"`
	ContentType   string    `json:"content_type"`
	ContentLength int64     `json:"content_length"`
}

// AvatarUploadRequest represents avatar upload request
type AvatarUploadRequest struct {
	UserID   string `json:"user_id"`
	FileName string `json:"file_name"`
	FileType string `json:"file_type"`
	FileSize int64  `json:"file_size"`
}

// AvatarUploadResponse represents avatar upload response
type AvatarUploadResponse struct {
	UploadID     string              `json:"upload_id"`
	PresignedURL string              `json:"presigned_url"`
	S3Key        string              `json:"s3_key"`
	S3Bucket     string              `json:"s3_bucket"`
	ExpiresAt    time.Time           `json:"expires_at"`
	UploadFields map[string]string   `json:"upload_fields,omitempty"`
	Instructions *UploadInstructions `json:"instructions"`
}

// UploadInstructions provides guidance for frontend upload
type UploadInstructions struct {
	Method       string            `json:"method"`                // "PUT" or "POST"
	URL          string            `json:"url"`                   // Upload URL
	Headers      map[string]string `json:"headers,omitempty"`     // Required headers
	FormFields   map[string]string `json:"form_fields,omitempty"` // Form fields for POST
	MaxFileSize  int64             `json:"max_file_size"`         // Maximum file size
	AllowedTypes []string          `json:"allowed_types"`         // Allowed MIME types
}

// AvatarConfirmRequest represents avatar upload confirmation
type AvatarConfirmRequest struct {
	UploadID string `json:"upload_id"`
	UserID   string `json:"user_id"`
	ETag     string `json:"etag"`
}

// AvatarConfirmResponse represents avatar upload confirmation response
type AvatarConfirmResponse struct {
	AssetID      string `json:"asset_id"`
	Status       string `json:"status"`
	AvatarURL    string `json:"avatar_url,omitempty"`
	ThumbnailURL string `json:"thumbnail_url,omitempty"`
	Message      string `json:"message"`
}

// AvatarInfo represents avatar information
type AvatarInfo struct {
	AssetID      string    `json:"asset_id"`
	AvatarURL    string    `json:"avatar_url"`
	ThumbnailURL string    `json:"thumbnail_url,omitempty"`
	FileSize     int64     `json:"file_size"`
	FileType     string    `json:"file_type"`
	Status       string    `json:"status"`
	IsValid      bool      `json:"is_valid"`
	UploadedAt   time.Time `json:"uploaded_at"`
}

// AssetStatus constants
const (
	StatusPending           = "pending"
	StatusValidated         = "validated"
	StatusProcessing        = "processing"
	StatusReady             = "ready"
	StatusFailed            = "failed"
	StatusPendingValidation = "pending_validation"
)

// AssetPurpose constants
const (
	PurposeProfileImage      = "profile_image"
	PurposePostImage         = "post_image"
	PurposeRAGTraining       = "rag_training"
	PurposeTemplateThumbnail = "template_thumbnail"
)

// Validation types
const (
	ValidationTypeImage      = "image"
	ValidationTypeSafety     = "safety"
	ValidationTypeCorruption = "corruption"
	ValidationTypeContent    = "content"
)

// Helper methods

// IsReady returns true if the asset is ready for use
func (a *Asset) IsReady() bool {
	return a.Status == StatusReady
}

// IsValid returns true if the asset passed validation
func (a *Asset) IsValid() bool {
	return a.Validation != nil && a.Validation.IsValid
}

// IsSafe returns true if the asset is safe (no malware)
func (a *Asset) IsSafe() bool {
	return a.Validation != nil && a.Validation.IsSafe
}

// GetPublicURL returns the public URL for the asset if it's public
func (a *Asset) GetPublicURL() string {
	if !a.IsPublic || a.S3Key == "" {
		return ""
	}
	// This would typically be constructed based on CDN/S3 configuration
	// For now, return empty - should be handled by GetDownloadURL
	return ""
}

// GetFileExtension returns the file extension from the filename
func (a *Asset) GetFileExtension() string {
	if a.FileExtension != "" {
		return a.FileExtension
	}
	// Extract from filename if not set
	if len(a.FileName) > 0 {
		for i := len(a.FileName) - 1; i >= 0; i-- {
			if a.FileName[i] == '.' {
				return a.FileName[i+1:]
			}
		}
	}
	return ""
}

// IsImage returns true if the asset is an image
func (a *Asset) IsImage() bool {
	switch a.FileType {
	case "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp":
		return true
	default:
		return false
	}
}

// GetSizeInMB returns the file size in megabytes
func (a *Asset) GetSizeInMB() float64 {
	return float64(a.FileSize) / (1024 * 1024)
}

// HasValidation returns true if the asset has validation results
func (a *Asset) HasValidation() bool {
	return a.Validation != nil
}

// GetValidationErrors returns all validation errors
func (a *Asset) GetValidationErrors() []string {
	if a.Validation == nil {
		return nil
	}
	return a.Validation.Errors
}

// GetValidationWarnings returns all validation warnings
func (a *Asset) GetValidationWarnings() []string {
	if a.Validation == nil {
		return nil
	}
	return a.Validation.Warnings
}

// ToProto converts Asset to protobuf Asset
func (a *Asset) ToProto() *assetv1.Asset {
	if a == nil {
		return nil
	}

	pbAsset := &assetv1.Asset{
		Id:            a.ID,
		UserId:        a.UserID,
		WorkspaceId:   a.WorkspaceID,
		TemplateId:    a.TemplateID,
		FileName:      a.FileName,
		OriginalName:  a.OriginalName,
		FileType:      a.FileType,
		FileExtension: a.FileExtension,
		FileSize:      a.FileSize,
		S3Key:         a.S3Key,
		S3Bucket:      a.S3Bucket,
		ContentHash:   a.ContentHash,
		Purpose:       a.Purpose,
		Status:        a.Status,
		IsPublic:      a.IsPublic,
		ThumbnailUrl:  a.ThumbnailURL,
		Metadata:      a.Metadata,
	}

	if !a.CreatedAt.IsZero() {
		pbAsset.CreatedAt = timestamppb.New(a.CreatedAt)
	}

	if !a.UpdatedAt.IsZero() {
		pbAsset.UpdatedAt = timestamppb.New(a.UpdatedAt)
	}

	if a.Validation != nil {
		pbAsset.Validation = a.Validation.ToProto()
	}

	return pbAsset
}

// ToProto converts ValidationResult to protobuf ValidationResult
func (v *ValidationResult) ToProto() *assetv1.ValidationResult {
	if v == nil {
		return nil
	}

	pbResult := &assetv1.ValidationResult{
		IsValid:     v.IsValid,
		Errors:      v.Errors,
		Warnings:    v.Warnings,
		IsSafe:      v.IsSafe,
		IsCorrupted: v.IsCorrupted,
		MimeType:    v.MimeType,
		Properties:  v.Properties,
	}

	if !v.ValidatedAt.IsZero() {
		pbResult.ValidatedAt = timestamppb.New(v.ValidatedAt)
	}

	return pbResult
}

package asset

import (
	"context"

	"github.com/social-content-ai/asset-service/pkg/models"
)

// UseCase defines the asset management business logic interface
type UseCase interface {
	// Upload operations
	RequestUpload(ctx context.Context, req *models.RequestUploadRequest) (*models.UploadURLResponse, error)
	ConfirmUpload(ctx context.Context, req *models.ConfirmUploadRequest) (*models.AssetResponse, error)

	// Asset management
	GetAsset(ctx context.Context, assetID, userID string) (*models.AssetResponse, error)
	ListAssets(ctx context.Context, req *models.ListAssetsRequest) (*models.ListAssetsResponse, error)
	UpdateAsset(ctx context.Context, req *models.UpdateAssetRequest) (*models.AssetResponse, error)
	DeleteAsset(ctx context.Context, assetID, userID string) error

	// Asset operations
	ProcessAsset(ctx context.Context, assetID, userID string) (*models.AssetResponse, error)
	GenerateThumbnail(ctx context.Context, assetID, userID string) (*models.AssetResponse, error)
	DownloadAsset(ctx context.Context, assetID, userID string) (string, error)
	GetPublicURL(ctx context.Context, assetID, userID string) (string, error)

	// Bulk operations
	BulkUpload(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error)
	BulkDelete(ctx context.Context, req *models.BulkDeleteRequest) (*models.BulkOperationResult, error)
	BulkMove(ctx context.Context, req *models.BulkMoveRequest) (*models.BulkOperationResult, error)
	BulkTag(ctx context.Context, req *models.BulkTagRequest) (*models.BulkOperationResult, error)

	// Image operations
	ListImages(ctx context.Context, userID string, page, limit int) (*models.ListAssetsResponse, error)
	UploadImages(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error)
	ResizeImage(ctx context.Context, req *models.ResizeImageRequest) (*models.AssetResponse, error)
	OptimizeImage(ctx context.Context, req *models.OptimizeImageRequest) (*models.AssetResponse, error)
	ConvertImageFormat(ctx context.Context, req *models.ConvertImageFormatRequest) (*models.AssetResponse, error)
	GetImageVariants(ctx context.Context, assetID, userID string) ([]models.AssetVariant, error)

	// Document operations
	ListDocuments(ctx context.Context, userID string, page, limit int) (*models.ListAssetsResponse, error)
	UploadDocuments(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error)
	GetDocumentPreview(ctx context.Context, assetID, userID string) (string, error)
	ExtractDocumentText(ctx context.Context, assetID, userID string) (string, error)
	GetDocumentMetadata(ctx context.Context, assetID, userID string) (*models.DocumentInfo, error)

	// Search operations
	SearchAssets(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error)
	SearchImages(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error)
	SearchDocuments(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error)
	VisualSearch(ctx context.Context, req *models.VisualSearchRequest) (*models.VisualSearchResponse, error)

	// Analytics
	GetStorageAnalytics(ctx context.Context, userID string) (*models.StorageAnalyticsResponse, error)
	GetUsageAnalytics(ctx context.Context, userID, period string) (*models.UsageAnalyticsResponse, error)
	GetPerformanceAnalytics(ctx context.Context, userID, period string) (*models.PerformanceAnalyticsResponse, error)

	// Admin operations
	AdminListAssets(ctx context.Context, req *models.ListAssetsRequest) (*models.ListAssetsResponse, error)
	AdminGetAsset(ctx context.Context, assetID string) (*models.AssetResponse, error)
	AdminDeleteAsset(ctx context.Context, assetID string) error
	QuarantineAsset(ctx context.Context, assetID string) error
	RestoreAsset(ctx context.Context, assetID string) error
	GetStorageStats(ctx context.Context) (*models.StorageAnalyticsResponse, error)
	CleanupStorage(ctx context.Context) (*models.BulkOperationResult, error)
	OptimizeStorage(ctx context.Context) (*models.BulkOperationResult, error)
	GetAdminAnalytics(ctx context.Context, period string) (*models.StorageAnalyticsResponse, error)
	GetAdminStorageAnalytics(ctx context.Context, period string) (*models.StorageAnalyticsResponse, error)
	GetAdminUsageAnalytics(ctx context.Context, period string) (*models.UsageAnalyticsResponse, error)

	// Public operations
	GetPublicAsset(ctx context.Context, assetID string) (*models.AssetResponse, error)
	GetPublicImage(ctx context.Context, assetID string) (*models.AssetResponse, error)
	GetPublicThumbnail(ctx context.Context, assetID string) (*models.AssetResponse, error)

	// Webhook operations
	ProcessingCompleteWebhook(ctx context.Context, assetID string, status string) error
	VirusScanCompleteWebhook(ctx context.Context, assetID string, safe bool, threats []string) error
}

package grpc

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/pkg-shared/logging"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/usecase/session"
)

// SessionHandler implements the SessionService gRPC server
type SessionHandler struct {
	userv1.UnimplementedSessionServiceServer
	sessionUC session.UseCase
	logger    logging.Logger
}

// NewSessionHandler creates a new session gRPC handler
func NewSessionHandler(sessionUC session.UseCase, logger logging.Logger) *SessionHandler {
	return &SessionHandler{
		sessionUC: sessionUC,
		logger:    logger,
	}
}

// CreateSession creates a new user session
func (h *SessionHandler) CreateSession(ctx context.Context, req *userv1.CreateSessionRequest) (*userv1.Session, error) {
	// Validate request
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}
	if req.DurationSeconds <= 0 {
		return nil, status.Error(codes.InvalidArgument, "duration_seconds must be positive")
	}

	// Convert to usecase request
	createReq := &session.CreateSessionRequest{
		UserID:          req.UserId,
		DeviceInfo:      req.DeviceInfo,
		IPAddress:       req.IpAddress,
		DurationSeconds: req.DurationSeconds,
	}

	// Create session
	sessionEntity, err := h.sessionUC.CreateSession(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.UserId).Error("Failed to create session")
		return nil, status.Error(codes.Internal, "failed to create session")
	}

	// Convert to proto response
	return h.toProtoSession(sessionEntity), nil
}

// GetSession retrieves a session by ID
func (h *SessionHandler) GetSession(ctx context.Context, req *userv1.GetSessionRequest) (*userv1.Session, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	sessionEntity, err := h.sessionUC.GetSession(ctx, req.Id)
	if err != nil {
		h.logger.WithError(err).WithField("session_id", req.Id).Error("Failed to get session")
		return nil, status.Error(codes.NotFound, "session not found")
	}

	return h.toProtoSession(sessionEntity), nil
}

// ListSessions lists user sessions with pagination and filters
func (h *SessionHandler) ListSessions(ctx context.Context, req *userv1.ListSessionsRequest) (*userv1.ListSessionsResponse, error) {
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Set defaults
	page := int(req.Pagination.Page)
	if page < 1 {
		page = 1
	}
	limit := int(req.Pagination.Limit)
	if limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	// Convert to usecase request
	listReq := &session.ListSessionsRequest{
		UserID:    req.UserId,
		Page:      page,
		Limit:     limit,
		Status:    req.Status,
		SortBy:    req.Pagination.SortBy,
		SortOrder: req.Pagination.SortOrder,
	}

	// List sessions
	sessions, total, err := h.sessionUC.ListSessions(ctx, listReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.UserId).Error("Failed to list sessions")
		return nil, status.Error(codes.Internal, "failed to list sessions")
	}

	// Convert to proto response
	protoSessions := make([]*userv1.Session, len(sessions))
	for i, s := range sessions {
		protoSessions[i] = h.toProtoSession(s)
	}

	totalPages := (total + limit - 1) / limit
	hasNext := page < totalPages
	hasPrev := page > 1

	return &userv1.ListSessionsResponse{
		Sessions: protoSessions,
		Pagination: &commonv1.PaginationResponse{
			Page:       int32(page),
			Limit:      int32(limit),
			TotalItems: int32(total),
			TotalPages: int32(totalPages),
			HasNext:    hasNext,
			HasPrev:    hasPrev,
		},
	}, nil
}

// RevokeSession revokes a specific session
func (h *SessionHandler) RevokeSession(ctx context.Context, req *userv1.RevokeSessionRequest) (*userv1.Empty, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	err := h.sessionUC.RevokeSession(ctx, req.Id, req.Reason)
	if err != nil {
		h.logger.WithError(err).WithField("session_id", req.Id).Error("Failed to revoke session")
		return nil, status.Error(codes.Internal, "failed to revoke session")
	}

	return &userv1.Empty{}, nil
}

// RevokeAllSessions revokes all user sessions except optionally one
func (h *SessionHandler) RevokeAllSessions(ctx context.Context, req *userv1.RevokeAllSessionsRequest) (*userv1.Empty, error) {
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	err := h.sessionUC.RevokeAllSessions(ctx, req.UserId, req.ExceptSessionId)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.UserId).Error("Failed to revoke all sessions")
		return nil, status.Error(codes.Internal, "failed to revoke sessions")
	}

	return &userv1.Empty{}, nil
}

// toProtoSession converts ent.Session to proto Session
func (h *SessionHandler) toProtoSession(sessionEntity *ent.Session) *userv1.Session {
	if sessionEntity == nil {
		return nil
	}

	protoSession := &userv1.Session{
		Id:         sessionEntity.ID.String(),
		UserId:     sessionEntity.UserID.String(),
		DeviceInfo: sessionEntity.DeviceInfo,
		Status:     string(sessionEntity.Status),
		IpAddress:  sessionEntity.IPAddress,
		LoginTime:  timestamppb.New(sessionEntity.LoginTime),
	}

	// Add optional timestamps
	if !sessionEntity.LogoutTime.IsZero() {
		protoSession.LogoutTime = timestamppb.New(sessionEntity.LogoutTime)
	}

	if !sessionEntity.ExpiresAt.IsZero() {
		protoSession.ExpiresAt = timestamppb.New(sessionEntity.ExpiresAt)
	}

	return protoSession
}

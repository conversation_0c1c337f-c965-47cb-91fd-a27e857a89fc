// Code generated by ent, DO NOT EDIT.

package folder

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldUserID, v))
}

// ParentID applies equality check predicate on the "parent_id" field. It's identical to ParentIDEQ.
func ParentID(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldParentID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldDescription, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldPath, v))
}

// Level applies equality check predicate on the "level" field. It's identical to LevelEQ.
func Level(v int) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldLevel, v))
}

// IsShared applies equality check predicate on the "is_shared" field. It's identical to IsSharedEQ.
func IsShared(v bool) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldIsShared, v))
}

// AssetCount applies equality check predicate on the "asset_count" field. It's identical to AssetCountEQ.
func AssetCount(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldAssetCount, v))
}

// TotalSize applies equality check predicate on the "total_size" field. It's identical to TotalSizeEQ.
func TotalSize(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldTotalSize, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldUserID, v))
}

// ParentIDEQ applies the EQ predicate on the "parent_id" field.
func ParentIDEQ(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldParentID, v))
}

// ParentIDNEQ applies the NEQ predicate on the "parent_id" field.
func ParentIDNEQ(v uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldParentID, v))
}

// ParentIDIn applies the In predicate on the "parent_id" field.
func ParentIDIn(vs ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldParentID, vs...))
}

// ParentIDNotIn applies the NotIn predicate on the "parent_id" field.
func ParentIDNotIn(vs ...uuid.UUID) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldParentID, vs...))
}

// ParentIDIsNil applies the IsNil predicate on the "parent_id" field.
func ParentIDIsNil() predicate.Folder {
	return predicate.Folder(sql.FieldIsNull(FieldParentID))
}

// ParentIDNotNil applies the NotNil predicate on the "parent_id" field.
func ParentIDNotNil() predicate.Folder {
	return predicate.Folder(sql.FieldNotNull(FieldParentID))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.Folder {
	return predicate.Folder(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.Folder {
	return predicate.Folder(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContainsFold(FieldDescription, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.Folder {
	return predicate.Folder(sql.FieldHasSuffix(FieldPath, v))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.Folder {
	return predicate.Folder(sql.FieldContainsFold(FieldPath, v))
}

// LevelEQ applies the EQ predicate on the "level" field.
func LevelEQ(v int) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldLevel, v))
}

// LevelNEQ applies the NEQ predicate on the "level" field.
func LevelNEQ(v int) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldLevel, v))
}

// LevelIn applies the In predicate on the "level" field.
func LevelIn(vs ...int) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldLevel, vs...))
}

// LevelNotIn applies the NotIn predicate on the "level" field.
func LevelNotIn(vs ...int) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldLevel, vs...))
}

// LevelGT applies the GT predicate on the "level" field.
func LevelGT(v int) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldLevel, v))
}

// LevelGTE applies the GTE predicate on the "level" field.
func LevelGTE(v int) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldLevel, v))
}

// LevelLT applies the LT predicate on the "level" field.
func LevelLT(v int) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldLevel, v))
}

// LevelLTE applies the LTE predicate on the "level" field.
func LevelLTE(v int) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldLevel, v))
}

// IsSharedEQ applies the EQ predicate on the "is_shared" field.
func IsSharedEQ(v bool) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldIsShared, v))
}

// IsSharedNEQ applies the NEQ predicate on the "is_shared" field.
func IsSharedNEQ(v bool) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldIsShared, v))
}

// SharedWithIsNil applies the IsNil predicate on the "shared_with" field.
func SharedWithIsNil() predicate.Folder {
	return predicate.Folder(sql.FieldIsNull(FieldSharedWith))
}

// SharedWithNotNil applies the NotNil predicate on the "shared_with" field.
func SharedWithNotNil() predicate.Folder {
	return predicate.Folder(sql.FieldNotNull(FieldSharedWith))
}

// PermissionsIsNil applies the IsNil predicate on the "permissions" field.
func PermissionsIsNil() predicate.Folder {
	return predicate.Folder(sql.FieldIsNull(FieldPermissions))
}

// PermissionsNotNil applies the NotNil predicate on the "permissions" field.
func PermissionsNotNil() predicate.Folder {
	return predicate.Folder(sql.FieldNotNull(FieldPermissions))
}

// AssetCountEQ applies the EQ predicate on the "asset_count" field.
func AssetCountEQ(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldAssetCount, v))
}

// AssetCountNEQ applies the NEQ predicate on the "asset_count" field.
func AssetCountNEQ(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldAssetCount, v))
}

// AssetCountIn applies the In predicate on the "asset_count" field.
func AssetCountIn(vs ...int64) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldAssetCount, vs...))
}

// AssetCountNotIn applies the NotIn predicate on the "asset_count" field.
func AssetCountNotIn(vs ...int64) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldAssetCount, vs...))
}

// AssetCountGT applies the GT predicate on the "asset_count" field.
func AssetCountGT(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldAssetCount, v))
}

// AssetCountGTE applies the GTE predicate on the "asset_count" field.
func AssetCountGTE(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldAssetCount, v))
}

// AssetCountLT applies the LT predicate on the "asset_count" field.
func AssetCountLT(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldAssetCount, v))
}

// AssetCountLTE applies the LTE predicate on the "asset_count" field.
func AssetCountLTE(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldAssetCount, v))
}

// TotalSizeEQ applies the EQ predicate on the "total_size" field.
func TotalSizeEQ(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldTotalSize, v))
}

// TotalSizeNEQ applies the NEQ predicate on the "total_size" field.
func TotalSizeNEQ(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldTotalSize, v))
}

// TotalSizeIn applies the In predicate on the "total_size" field.
func TotalSizeIn(vs ...int64) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldTotalSize, vs...))
}

// TotalSizeNotIn applies the NotIn predicate on the "total_size" field.
func TotalSizeNotIn(vs ...int64) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldTotalSize, vs...))
}

// TotalSizeGT applies the GT predicate on the "total_size" field.
func TotalSizeGT(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldTotalSize, v))
}

// TotalSizeGTE applies the GTE predicate on the "total_size" field.
func TotalSizeGTE(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldTotalSize, v))
}

// TotalSizeLT applies the LT predicate on the "total_size" field.
func TotalSizeLT(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldTotalSize, v))
}

// TotalSizeLTE applies the LTE predicate on the "total_size" field.
func TotalSizeLTE(v int64) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldTotalSize, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Folder {
	return predicate.Folder(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Folder {
	return predicate.Folder(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Folder {
	return predicate.Folder(sql.FieldNotNull(FieldDeletedAt))
}

// HasParent applies the HasEdge predicate on the "parent" edge.
func HasParent() predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasParentWith applies the HasEdge predicate on the "parent" edge with a given conditions (other predicates).
func HasParentWith(preds ...predicate.Folder) predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := newParentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasChildren applies the HasEdge predicate on the "children" edge.
func HasChildren() predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasChildrenWith applies the HasEdge predicate on the "children" edge with a given conditions (other predicates).
func HasChildrenWith(preds ...predicate.Folder) predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := newChildrenStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAssets applies the HasEdge predicate on the "assets" edge.
func HasAssets() predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, AssetsTable, AssetsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAssetsWith applies the HasEdge predicate on the "assets" edge with a given conditions (other predicates).
func HasAssetsWith(preds ...predicate.Asset) predicate.Folder {
	return predicate.Folder(func(s *sql.Selector) {
		step := newAssetsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Folder) predicate.Folder {
	return predicate.Folder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Folder) predicate.Folder {
	return predicate.Folder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Folder) predicate.Folder {
	return predicate.Folder(sql.NotPredicates(p))
}

package payment

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockProcessor implements the Processor interface for testing
type MockProcessor struct {
	shouldFail bool
	logger     logging.Logger
}

// NewMockProcessor creates a new mock payment processor
func NewMockProcessor(shouldFail bool, logger logging.Logger) Processor {
	return &MockProcessor{
		shouldFail: shouldFail,
		logger:     logger,
	}
}

// ProcessPayment processes a payment (mock implementation)
func (m *MockProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock payment processing failed")
	}

	paymentID := uuid.New().String()
	return &PaymentResponse{
		PaymentID:  paymentID,
		Status:     "completed",
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: fmt.Sprintf("https://mock-payment.com/pay/%s", paymentID),
		PaymentInfo: map[string]interface{}{
			"payment_id": paymentID,
			"mock":       true,
			"processor":  "mock",
		},
		ExpiresAt: time.Now().Add(1 * time.Hour).Format(time.RFC3339),
	}, nil
}

// RefundPayment refunds a payment (mock implementation)
func (m *MockProcessor) RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock refund failed")
	}

	return &RefundResponse{
		RefundID:  uuid.New().String(),
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// GetPaymentStatus gets payment status (mock implementation)
func (m *MockProcessor) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock get payment status failed")
	}

	return &PaymentStatus{
		PaymentID:   paymentID,
		Status:      "completed",
		Amount:      1000,
		Currency:    "USD",
		ProcessedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// ValidateWebhook validates webhook (mock implementation)
func (m *MockProcessor) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	if m.shouldFail {
		return false, fmt.Errorf("mock webhook validation failed")
	}
	return true, nil
}

// ProcessWebhook processes webhook (mock implementation)
func (m *MockProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
	if m.shouldFail {
		return nil, fmt.Errorf("mock webhook processing failed")
	}

	return &WebhookEvent{
		ID:        uuid.New().String(),
		Type:      "mock.event",
		Data:      map[string]interface{}{"mock": true},
		CreatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// PlatformIntegration holds the schema definition for the PlatformIntegration entity.
type PlatformIntegration struct {
	ent.Schema
}

// Fields of the PlatformIntegration.
func (PlatformIntegration) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who owns this integration"),
		field.String("workspace_id").
			MaxLen(36).
			Optional().
			Comment("Workspace this integration belongs to"),
		field.Enum("platform").
			Values("facebook", "instagram", "twitter", "linkedin", "tiktok", "youtube", "pinterest", "snapchat").
			Comment("Social media platform"),
		field.String("platform_user_id").
			MaxLen(100).
			Comment("User ID on the platform"),
		field.String("platform_username").
			MaxLen(100).
			Optional().
			Comment("Username on the platform"),
		field.String("platform_display_name").
			MaxLen(200).
			Optional().
			Comment("Display name on the platform"),
		field.String("platform_avatar_url").
			MaxLen(500).
			Optional().
			Comment("Avatar URL on the platform"),
		field.Text("access_token").
			Sensitive().
			Comment("OAuth access token"),
		field.Text("refresh_token").
			Optional().
			Sensitive().
			Comment("OAuth refresh token"),
		field.Time("token_expires_at").
			Optional().
			Comment("When the access token expires"),
		field.JSON("scopes", []string{}).
			Optional().
			Comment("OAuth scopes granted"),
		field.JSON("platform_data", map[string]interface{}{}).
			Optional().
			Comment("Additional platform-specific data"),
		field.Enum("status").
			Values("active", "expired", "revoked", "error", "pending").
			Default("pending").
			Comment("Integration status"),
		field.String("error_message").
			MaxLen(500).
			Optional().
			Comment("Last error message if status is error"),
		field.Time("last_sync_at").
			Optional().
			Comment("Last time data was synced from platform"),
		field.Time("last_post_at").
			Optional().
			Comment("Last time a post was published to this platform"),
		field.JSON("capabilities", []string{}).
			Optional().
			Comment("Platform capabilities (post_text, post_image, post_video, etc.)"),
		field.JSON("settings", map[string]interface{}{}).
			Optional().
			Comment("User-defined integration settings"),
		field.Bool("auto_publish").
			Default(false).
			Comment("Whether to auto-publish posts to this platform"),
		field.Bool("is_primary").
			Default(false).
			Comment("Whether this is the primary account for this platform"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the PlatformIntegration.
func (PlatformIntegration) Edges() []ent.Edge {
	return nil
}

// Indexes of the PlatformIntegration.
func (PlatformIntegration) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("workspace_id"),
		index.Fields("platform"),
		index.Fields("platform_user_id"),
		index.Fields("status"),
		index.Fields("token_expires_at"),
		index.Fields("is_primary"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "platform"),
		index.Fields("user_id", "platform", "platform_user_id").Unique(),
	}
}

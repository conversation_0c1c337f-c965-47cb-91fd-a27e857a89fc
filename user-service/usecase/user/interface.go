package user

import (
	"context"

	"github.com/social-content-ai/user-service/pkg/models"
)

// UseCase defines the user business logic interface
type UseCase interface {
	// User management
	CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error)
	GetUserByID(ctx context.Context, id uint64) (*models.UserResponse, error)
	GetUserByEmail(ctx context.Context, email string) (*models.UserResponse, error)
	UpdateUser(ctx context.Context, req *models.UpdateUserRequest) (*models.UserResponse, error)
	DeleteUser(ctx context.Context, id uint64) error
	ListUsers(ctx context.Context, req *models.ListUsersRequest) (*models.ListUsersResponse, error)

	// User status management
	LockUser(ctx context.Context, req *models.LockUserRequest) error
	UnlockUser(ctx context.Context, id uint64) error

	// Password management
	ChangePassword(ctx context.Context, req *models.ChangePasswordRequest) error
	AdminResetPassword(ctx context.Context, userID uint64, req *models.ResetPasswordRequest) error

	// Avatar management
	RequestAvatarUpload(ctx context.Context, req *models.RequestAvatarUploadRequest) (*models.RequestAvatarUploadResponse, error)
	ConfirmAvatarUpload(ctx context.Context, req *models.ConfirmAvatarUploadRequest) (*models.ConfirmAvatarUploadResponse, error)
	GetCurrentAvatar(ctx context.Context, userID uint64) (*models.GetAvatarResponse, error)
	DeleteCurrentAvatar(ctx context.Context, userID uint64) error

	// Bulk operations (admin)
	AdminListUsers(ctx context.Context, req *models.ListUsersRequest) (*models.ListUsersResponse, error)
	AdminGetUser(ctx context.Context, id uint64) (*models.UserResponse, error)
	AdminCreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error)
	AdminUpdateUser(ctx context.Context, req *models.UpdateUserRequest) (*models.UserResponse, error)
	AdminDeleteUser(ctx context.Context, id uint64) error
	BulkCreateUsers(ctx context.Context, req *models.BulkCreateUsersRequest) (*models.BulkCreateUsersResponse, error)
	BulkUpdateUsers(ctx context.Context, req *models.BulkUpdateUsersRequest) (*models.BulkUpdateUsersResponse, error)
	BulkDeleteUsers(ctx context.Context, req *models.BulkDeleteUsersRequest) (*models.BulkDeleteUsersResponse, error)

	// Analytics
	GetUserAnalytics(ctx context.Context, req *models.UserAnalyticsRequest) (*models.UserAnalyticsResponse, error)

	// Profile and preferences
	UpdatePreferences(ctx context.Context, userID uint64, req *models.UpdatePreferencesRequest) (*models.UserResponse, error)
	GetActivity(ctx context.Context, userID uint64, req *models.GetActivityRequest) (*models.GetActivityResponse, error)
}

package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// InstagramOAuth handles Instagram OAuth flow
type InstagramOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewInstagramOAuth creates a new Instagram OAuth handler
func NewInstagramOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) Provider {
	return &InstagramOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// InstagramTokenResponse represents Instagram token response
type InstagramTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

// InstagramUserProfile represents Instagram user profile
type InstagramUserProfile struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Picture  string `json:"profile_picture_url"`
}

// GetAuthURL generates Instagram OAuth authorization URL
func (i *InstagramOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://api.instagram.com/oauth/authorize"

	params := url.Values{}
	params.Set("client_id", i.clientID)
	params.Set("redirect_uri", i.redirectURL)
	params.Set("state", state)
	params.Set("response_type", "code")

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, ","))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "user_profile,user_media")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (i *InstagramOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://api.instagram.com/oauth/access_token"

	data := url.Values{}
	data.Set("client_id", i.clientID)
	data.Set("client_secret", i.clientSecret)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", i.redirectURL)
	data.Set("code", code)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp InstagramTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	i.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.ExpiresIn,
	}).Info("Instagram token exchanged successfully")

	// Convert InstagramTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken: tokenResp.AccessToken,
		TokenType:   tokenResp.TokenType,
		ExpiresAt:   expiresAt,
	}, nil
}

// RefreshToken refreshes Instagram access token
func (i *InstagramOAuth) RefreshToken(ctx context.Context, accessToken string) (*TokenResponse, error) {
	refreshURL := "https://graph.instagram.com/refresh_access_token"

	params := url.Values{}
	params.Set("grant_type", "ig_refresh_token")
	params.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", refreshURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp InstagramTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert InstagramTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken: tokenResp.AccessToken,
		TokenType:   tokenResp.TokenType,
		ExpiresAt:   expiresAt,
	}, nil
}

// RevokeToken revokes Instagram access token
func (i *InstagramOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	// Instagram doesn't have a specific revoke endpoint
	// The token will expire naturally
	i.logger.Info("Instagram token marked for expiration")
	return nil
}

// GetUserProfile implements Provider interface
func (i *InstagramOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	userURL := "https://graph.instagram.com/me"

	params := url.Values{}
	params.Set("fields", "id,username,account_type")
	params.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", userURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var profile InstagramUserProfile
	if err := json.NewDecoder(resp.Body).Decode(&profile); err != nil {
		return nil, fmt.Errorf("failed to decode user profile: %w", err)
	}

	return &UserProfile{
		ID:          profile.ID,
		Username:    profile.Username,
		DisplayName: profile.Name,
		AvatarURL:   profile.Picture,
	}, nil
}

// GetPermissions implements Provider interface
func (i *InstagramOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	// Instagram doesn't have a specific permissions endpoint
	// Return default permissions based on successful authentication
	return []string{"user_profile", "user_media"}, nil
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// CreditTransaction holds the schema definition for the CreditTransaction entity.
type CreditTransaction struct {
	ent.Schema
}

// Fields of the CreditTransaction.
func (CreditTransaction) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.Enum("type").
			Values("purchase", "usage", "bonus", "refund", "adjustment", "subscription", "referral").
			Default("usage"),
		field.Int("amount").
			Comment("Positive for credit, negative for debit"),
		field.String("description").
			MaxLen(500),
		field.String("reference_id").
			MaxLen(100).
			Optional().
			Comment("Reference to related entity (post, generation, payment, etc.)"),
		field.String("reference_type").
			MaxLen(50).
			Optional().
			Comment("Type of reference (generation, improvement, payment, etc.)"),
		field.String("payment_id").
			MaxLen(100).
			Optional().
			Comment("Payment processor transaction ID"),
		field.String("invoice_id").
			MaxLen(100).
			Optional().
			Comment("Invoice ID for billing"),
		field.Enum("status").
			Values("pending", "completed", "failed", "cancelled").
			Default("completed"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("Additional transaction metadata"),
		field.Time("processed_at").
			Optional().
			Comment("When the transaction was processed"),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the CreditTransaction.
func (CreditTransaction) Edges() []ent.Edge {
	return nil
}

// Indexes of the CreditTransaction.
func (CreditTransaction) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("type"),
		index.Fields("status"),
		index.Fields("reference_id"),
		index.Fields("reference_type"),
		index.Fields("payment_id"),
		index.Fields("created_at"),
		index.Fields("user_id", "created_at"),
		index.Fields("user_id", "type"),
	}
}

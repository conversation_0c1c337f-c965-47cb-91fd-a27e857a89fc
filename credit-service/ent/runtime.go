// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/ent/creditplan"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/order"
	"github.com/social-content-ai/credit-service/ent/payment"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
	"github.com/social-content-ai/credit-service/ent/referral"
	"github.com/social-content-ai/credit-service/ent/schema"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	banktransferFields := schema.BankTransfer{}.Fields()
	_ = banktransferFields
	// banktransferDescAmount is the schema descriptor for amount field.
	banktransferDescAmount := banktransferFields[3].Descriptor()
	// banktransfer.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	banktransfer.AmountValidator = banktransferDescAmount.Validators[0].(func(int64) error)
	// banktransferDescCurrency is the schema descriptor for currency field.
	banktransferDescCurrency := banktransferFields[4].Descriptor()
	// banktransfer.DefaultCurrency holds the default value on creation for the currency field.
	banktransfer.DefaultCurrency = banktransferDescCurrency.Default.(string)
	// banktransfer.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	banktransfer.CurrencyValidator = banktransferDescCurrency.Validators[0].(func(string) error)
	// banktransferDescBankAccount is the schema descriptor for bank_account field.
	banktransferDescBankAccount := banktransferFields[6].Descriptor()
	// banktransfer.BankAccountValidator is a validator for the "bank_account" field. It is called by the builders before save.
	banktransfer.BankAccountValidator = banktransferDescBankAccount.Validators[0].(func(string) error)
	// banktransferDescReferenceCode is the schema descriptor for reference_code field.
	banktransferDescReferenceCode := banktransferFields[7].Descriptor()
	// banktransfer.ReferenceCodeValidator is a validator for the "reference_code" field. It is called by the builders before save.
	banktransfer.ReferenceCodeValidator = banktransferDescReferenceCode.Validators[0].(func(string) error)
	// banktransferDescCreditsToAdd is the schema descriptor for credits_to_add field.
	banktransferDescCreditsToAdd := banktransferFields[11].Descriptor()
	// banktransfer.DefaultCreditsToAdd holds the default value on creation for the credits_to_add field.
	banktransfer.DefaultCreditsToAdd = banktransferDescCreditsToAdd.Default.(int)
	// banktransfer.CreditsToAddValidator is a validator for the "credits_to_add" field. It is called by the builders before save.
	banktransfer.CreditsToAddValidator = banktransferDescCreditsToAdd.Validators[0].(func(int) error)
	// banktransferDescBankTransactionID is the schema descriptor for bank_transaction_id field.
	banktransferDescBankTransactionID := banktransferFields[12].Descriptor()
	// banktransfer.BankTransactionIDValidator is a validator for the "bank_transaction_id" field. It is called by the builders before save.
	banktransfer.BankTransactionIDValidator = banktransferDescBankTransactionID.Validators[0].(func(string) error)
	// banktransferDescActualAmountReceived is the schema descriptor for actual_amount_received field.
	banktransferDescActualAmountReceived := banktransferFields[13].Descriptor()
	// banktransfer.ActualAmountReceivedValidator is a validator for the "actual_amount_received" field. It is called by the builders before save.
	banktransfer.ActualAmountReceivedValidator = banktransferDescActualAmountReceived.Validators[0].(func(int64) error)
	// banktransferDescConfirmationMethod is the schema descriptor for confirmation_method field.
	banktransferDescConfirmationMethod := banktransferFields[14].Descriptor()
	// banktransfer.DefaultConfirmationMethod holds the default value on creation for the confirmation_method field.
	banktransfer.DefaultConfirmationMethod = banktransferDescConfirmationMethod.Default.(string)
	// banktransfer.ConfirmationMethodValidator is a validator for the "confirmation_method" field. It is called by the builders before save.
	banktransfer.ConfirmationMethodValidator = banktransferDescConfirmationMethod.Validators[0].(func(string) error)
	// banktransferDescCreatedAt is the schema descriptor for created_at field.
	banktransferDescCreatedAt := banktransferFields[17].Descriptor()
	// banktransfer.DefaultCreatedAt holds the default value on creation for the created_at field.
	banktransfer.DefaultCreatedAt = banktransferDescCreatedAt.Default.(func() time.Time)
	// banktransferDescUpdatedAt is the schema descriptor for updated_at field.
	banktransferDescUpdatedAt := banktransferFields[18].Descriptor()
	// banktransfer.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	banktransfer.DefaultUpdatedAt = banktransferDescUpdatedAt.Default.(func() time.Time)
	// banktransfer.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	banktransfer.UpdateDefaultUpdatedAt = banktransferDescUpdatedAt.UpdateDefault.(func() time.Time)
	// banktransferDescID is the schema descriptor for id field.
	banktransferDescID := banktransferFields[0].Descriptor()
	// banktransfer.DefaultID holds the default value on creation for the id field.
	banktransfer.DefaultID = banktransferDescID.Default.(func() uuid.UUID)
	creditplanFields := schema.CreditPlan{}.Fields()
	_ = creditplanFields
	// creditplanDescName is the schema descriptor for name field.
	creditplanDescName := creditplanFields[1].Descriptor()
	// creditplan.NameValidator is a validator for the "name" field. It is called by the builders before save.
	creditplan.NameValidator = creditplanDescName.Validators[0].(func(string) error)
	// creditplanDescDisplayName is the schema descriptor for display_name field.
	creditplanDescDisplayName := creditplanFields[2].Descriptor()
	// creditplan.DisplayNameValidator is a validator for the "display_name" field. It is called by the builders before save.
	creditplan.DisplayNameValidator = creditplanDescDisplayName.Validators[0].(func(string) error)
	// creditplanDescCredits is the schema descriptor for credits field.
	creditplanDescCredits := creditplanFields[4].Descriptor()
	// creditplan.CreditsValidator is a validator for the "credits" field. It is called by the builders before save.
	creditplan.CreditsValidator = creditplanDescCredits.Validators[0].(func(int) error)
	// creditplanDescPrice is the schema descriptor for price field.
	creditplanDescPrice := creditplanFields[5].Descriptor()
	// creditplan.PriceValidator is a validator for the "price" field. It is called by the builders before save.
	creditplan.PriceValidator = creditplanDescPrice.Validators[0].(func(int64) error)
	// creditplanDescCurrency is the schema descriptor for currency field.
	creditplanDescCurrency := creditplanFields[6].Descriptor()
	// creditplan.DefaultCurrency holds the default value on creation for the currency field.
	creditplan.DefaultCurrency = creditplanDescCurrency.Default.(string)
	// creditplan.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	creditplan.CurrencyValidator = creditplanDescCurrency.Validators[0].(func(string) error)
	// creditplanDescDiscountPercentage is the schema descriptor for discount_percentage field.
	creditplanDescDiscountPercentage := creditplanFields[7].Descriptor()
	// creditplan.DefaultDiscountPercentage holds the default value on creation for the discount_percentage field.
	creditplan.DefaultDiscountPercentage = creditplanDescDiscountPercentage.Default.(int)
	// creditplan.DiscountPercentageValidator is a validator for the "discount_percentage" field. It is called by the builders before save.
	creditplan.DiscountPercentageValidator = func() func(int) error {
		validators := creditplanDescDiscountPercentage.Validators
		fns := [...]func(int) error{
			validators[0].(func(int) error),
			validators[1].(func(int) error),
		}
		return func(discount_percentage int) error {
			for _, fn := range fns {
				if err := fn(discount_percentage); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// creditplanDescIsPopular is the schema descriptor for is_popular field.
	creditplanDescIsPopular := creditplanFields[8].Descriptor()
	// creditplan.DefaultIsPopular holds the default value on creation for the is_popular field.
	creditplan.DefaultIsPopular = creditplanDescIsPopular.Default.(bool)
	// creditplanDescIsActive is the schema descriptor for is_active field.
	creditplanDescIsActive := creditplanFields[9].Descriptor()
	// creditplan.DefaultIsActive holds the default value on creation for the is_active field.
	creditplan.DefaultIsActive = creditplanDescIsActive.Default.(bool)
	// creditplanDescSortOrder is the schema descriptor for sort_order field.
	creditplanDescSortOrder := creditplanFields[12].Descriptor()
	// creditplan.DefaultSortOrder holds the default value on creation for the sort_order field.
	creditplan.DefaultSortOrder = creditplanDescSortOrder.Default.(int)
	// creditplanDescStripePriceID is the schema descriptor for stripe_price_id field.
	creditplanDescStripePriceID := creditplanFields[13].Descriptor()
	// creditplan.StripePriceIDValidator is a validator for the "stripe_price_id" field. It is called by the builders before save.
	creditplan.StripePriceIDValidator = creditplanDescStripePriceID.Validators[0].(func(string) error)
	// creditplanDescStripeProductID is the schema descriptor for stripe_product_id field.
	creditplanDescStripeProductID := creditplanFields[14].Descriptor()
	// creditplan.StripeProductIDValidator is a validator for the "stripe_product_id" field. It is called by the builders before save.
	creditplan.StripeProductIDValidator = creditplanDescStripeProductID.Validators[0].(func(string) error)
	// creditplanDescCreatedAt is the schema descriptor for created_at field.
	creditplanDescCreatedAt := creditplanFields[16].Descriptor()
	// creditplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	creditplan.DefaultCreatedAt = creditplanDescCreatedAt.Default.(func() time.Time)
	// creditplanDescUpdatedAt is the schema descriptor for updated_at field.
	creditplanDescUpdatedAt := creditplanFields[17].Descriptor()
	// creditplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	creditplan.DefaultUpdatedAt = creditplanDescUpdatedAt.Default.(func() time.Time)
	// creditplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	creditplan.UpdateDefaultUpdatedAt = creditplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// creditplanDescID is the schema descriptor for id field.
	creditplanDescID := creditplanFields[0].Descriptor()
	// creditplan.DefaultID holds the default value on creation for the id field.
	creditplan.DefaultID = creditplanDescID.Default.(func() uuid.UUID)
	creditreservationFields := schema.CreditReservation{}.Fields()
	_ = creditreservationFields
	// creditreservationDescAmount is the schema descriptor for amount field.
	creditreservationDescAmount := creditreservationFields[2].Descriptor()
	// creditreservation.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	creditreservation.AmountValidator = creditreservationDescAmount.Validators[0].(func(int) error)
	// creditreservationDescPurpose is the schema descriptor for purpose field.
	creditreservationDescPurpose := creditreservationFields[3].Descriptor()
	// creditreservation.PurposeValidator is a validator for the "purpose" field. It is called by the builders before save.
	creditreservation.PurposeValidator = creditreservationDescPurpose.Validators[0].(func(string) error)
	// creditreservationDescReferenceID is the schema descriptor for reference_id field.
	creditreservationDescReferenceID := creditreservationFields[4].Descriptor()
	// creditreservation.ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	creditreservation.ReferenceIDValidator = creditreservationDescReferenceID.Validators[0].(func(string) error)
	// creditreservationDescReferenceType is the schema descriptor for reference_type field.
	creditreservationDescReferenceType := creditreservationFields[5].Descriptor()
	// creditreservation.ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	creditreservation.ReferenceTypeValidator = creditreservationDescReferenceType.Validators[0].(func(string) error)
	// creditreservationDescCreatedAt is the schema descriptor for created_at field.
	creditreservationDescCreatedAt := creditreservationFields[10].Descriptor()
	// creditreservation.DefaultCreatedAt holds the default value on creation for the created_at field.
	creditreservation.DefaultCreatedAt = creditreservationDescCreatedAt.Default.(func() time.Time)
	// creditreservationDescUpdatedAt is the schema descriptor for updated_at field.
	creditreservationDescUpdatedAt := creditreservationFields[11].Descriptor()
	// creditreservation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	creditreservation.DefaultUpdatedAt = creditreservationDescUpdatedAt.Default.(func() time.Time)
	// creditreservation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	creditreservation.UpdateDefaultUpdatedAt = creditreservationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// creditreservationDescID is the schema descriptor for id field.
	creditreservationDescID := creditreservationFields[0].Descriptor()
	// creditreservation.DefaultID holds the default value on creation for the id field.
	creditreservation.DefaultID = creditreservationDescID.Default.(func() uuid.UUID)
	credittransactionFields := schema.CreditTransaction{}.Fields()
	_ = credittransactionFields
	// credittransactionDescDescription is the schema descriptor for description field.
	credittransactionDescDescription := credittransactionFields[4].Descriptor()
	// credittransaction.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	credittransaction.DescriptionValidator = credittransactionDescDescription.Validators[0].(func(string) error)
	// credittransactionDescReferenceID is the schema descriptor for reference_id field.
	credittransactionDescReferenceID := credittransactionFields[5].Descriptor()
	// credittransaction.ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	credittransaction.ReferenceIDValidator = credittransactionDescReferenceID.Validators[0].(func(string) error)
	// credittransactionDescReferenceType is the schema descriptor for reference_type field.
	credittransactionDescReferenceType := credittransactionFields[6].Descriptor()
	// credittransaction.ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	credittransaction.ReferenceTypeValidator = credittransactionDescReferenceType.Validators[0].(func(string) error)
	// credittransactionDescPaymentID is the schema descriptor for payment_id field.
	credittransactionDescPaymentID := credittransactionFields[7].Descriptor()
	// credittransaction.PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	credittransaction.PaymentIDValidator = credittransactionDescPaymentID.Validators[0].(func(string) error)
	// credittransactionDescInvoiceID is the schema descriptor for invoice_id field.
	credittransactionDescInvoiceID := credittransactionFields[8].Descriptor()
	// credittransaction.InvoiceIDValidator is a validator for the "invoice_id" field. It is called by the builders before save.
	credittransaction.InvoiceIDValidator = credittransactionDescInvoiceID.Validators[0].(func(string) error)
	// credittransactionDescCreatedAt is the schema descriptor for created_at field.
	credittransactionDescCreatedAt := credittransactionFields[12].Descriptor()
	// credittransaction.DefaultCreatedAt holds the default value on creation for the created_at field.
	credittransaction.DefaultCreatedAt = credittransactionDescCreatedAt.Default.(func() time.Time)
	// credittransactionDescUpdatedAt is the schema descriptor for updated_at field.
	credittransactionDescUpdatedAt := credittransactionFields[13].Descriptor()
	// credittransaction.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	credittransaction.DefaultUpdatedAt = credittransactionDescUpdatedAt.Default.(func() time.Time)
	// credittransaction.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	credittransaction.UpdateDefaultUpdatedAt = credittransactionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// credittransactionDescID is the schema descriptor for id field.
	credittransactionDescID := credittransactionFields[0].Descriptor()
	// credittransaction.DefaultID holds the default value on creation for the id field.
	credittransaction.DefaultID = credittransactionDescID.Default.(func() uuid.UUID)
	orderFields := schema.Order{}.Fields()
	_ = orderFields
	// orderDescOrderNumber is the schema descriptor for order_number field.
	orderDescOrderNumber := orderFields[1].Descriptor()
	// order.OrderNumberValidator is a validator for the "order_number" field. It is called by the builders before save.
	order.OrderNumberValidator = orderDescOrderNumber.Validators[0].(func(string) error)
	// orderDescAmount is the schema descriptor for amount field.
	orderDescAmount := orderFields[6].Descriptor()
	// order.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	order.AmountValidator = orderDescAmount.Validators[0].(func(int64) error)
	// orderDescCurrency is the schema descriptor for currency field.
	orderDescCurrency := orderFields[7].Descriptor()
	// order.DefaultCurrency holds the default value on creation for the currency field.
	order.DefaultCurrency = orderDescCurrency.Default.(string)
	// order.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	order.CurrencyValidator = orderDescCurrency.Validators[0].(func(string) error)
	// orderDescPaymentMethod is the schema descriptor for payment_method field.
	orderDescPaymentMethod := orderFields[9].Descriptor()
	// order.PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	order.PaymentMethodValidator = orderDescPaymentMethod.Validators[0].(func(string) error)
	// orderDescPaymentID is the schema descriptor for payment_id field.
	orderDescPaymentID := orderFields[10].Descriptor()
	// order.PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	order.PaymentIDValidator = orderDescPaymentID.Validators[0].(func(string) error)
	// orderDescInvoiceID is the schema descriptor for invoice_id field.
	orderDescInvoiceID := orderFields[11].Descriptor()
	// order.InvoiceIDValidator is a validator for the "invoice_id" field. It is called by the builders before save.
	order.InvoiceIDValidator = orderDescInvoiceID.Validators[0].(func(string) error)
	// orderDescCredits is the schema descriptor for credits field.
	orderDescCredits := orderFields[12].Descriptor()
	// order.DefaultCredits holds the default value on creation for the credits field.
	order.DefaultCredits = orderDescCredits.Default.(int)
	// order.CreditsValidator is a validator for the "credits" field. It is called by the builders before save.
	order.CreditsValidator = orderDescCredits.Validators[0].(func(int) error)
	// orderDescFailureReason is the schema descriptor for failure_reason field.
	orderDescFailureReason := orderFields[18].Descriptor()
	// order.FailureReasonValidator is a validator for the "failure_reason" field. It is called by the builders before save.
	order.FailureReasonValidator = orderDescFailureReason.Validators[0].(func(string) error)
	// orderDescExternalOrderID is the schema descriptor for external_order_id field.
	orderDescExternalOrderID := orderFields[19].Descriptor()
	// order.ExternalOrderIDValidator is a validator for the "external_order_id" field. It is called by the builders before save.
	order.ExternalOrderIDValidator = orderDescExternalOrderID.Validators[0].(func(string) error)
	// orderDescCreatedAt is the schema descriptor for created_at field.
	orderDescCreatedAt := orderFields[20].Descriptor()
	// order.DefaultCreatedAt holds the default value on creation for the created_at field.
	order.DefaultCreatedAt = orderDescCreatedAt.Default.(func() time.Time)
	// orderDescUpdatedAt is the schema descriptor for updated_at field.
	orderDescUpdatedAt := orderFields[21].Descriptor()
	// order.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	order.DefaultUpdatedAt = orderDescUpdatedAt.Default.(func() time.Time)
	// order.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	order.UpdateDefaultUpdatedAt = orderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// orderDescID is the schema descriptor for id field.
	orderDescID := orderFields[0].Descriptor()
	// order.DefaultID holds the default value on creation for the id field.
	order.DefaultID = orderDescID.Default.(func() uuid.UUID)
	paymentFields := schema.Payment{}.Fields()
	_ = paymentFields
	// paymentDescPaymentMethod is the schema descriptor for payment_method field.
	paymentDescPaymentMethod := paymentFields[4].Descriptor()
	// payment.PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	payment.PaymentMethodValidator = paymentDescPaymentMethod.Validators[0].(func(string) error)
	// paymentDescPaymentProvider is the schema descriptor for payment_provider field.
	paymentDescPaymentProvider := paymentFields[5].Descriptor()
	// payment.PaymentProviderValidator is a validator for the "payment_provider" field. It is called by the builders before save.
	payment.PaymentProviderValidator = paymentDescPaymentProvider.Validators[0].(func(string) error)
	// paymentDescExternalPaymentID is the schema descriptor for external_payment_id field.
	paymentDescExternalPaymentID := paymentFields[6].Descriptor()
	// payment.ExternalPaymentIDValidator is a validator for the "external_payment_id" field. It is called by the builders before save.
	payment.ExternalPaymentIDValidator = paymentDescExternalPaymentID.Validators[0].(func(string) error)
	// paymentDescAmount is the schema descriptor for amount field.
	paymentDescAmount := paymentFields[7].Descriptor()
	// payment.AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	payment.AmountValidator = paymentDescAmount.Validators[0].(func(int64) error)
	// paymentDescCurrency is the schema descriptor for currency field.
	paymentDescCurrency := paymentFields[8].Descriptor()
	// payment.DefaultCurrency holds the default value on creation for the currency field.
	payment.DefaultCurrency = paymentDescCurrency.Default.(string)
	// payment.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	payment.CurrencyValidator = paymentDescCurrency.Validators[0].(func(string) error)
	// paymentDescPaymentIntentID is the schema descriptor for payment_intent_id field.
	paymentDescPaymentIntentID := paymentFields[10].Descriptor()
	// payment.PaymentIntentIDValidator is a validator for the "payment_intent_id" field. It is called by the builders before save.
	payment.PaymentIntentIDValidator = paymentDescPaymentIntentID.Validators[0].(func(string) error)
	// paymentDescChargeID is the schema descriptor for charge_id field.
	paymentDescChargeID := paymentFields[11].Descriptor()
	// payment.ChargeIDValidator is a validator for the "charge_id" field. It is called by the builders before save.
	payment.ChargeIDValidator = paymentDescChargeID.Validators[0].(func(string) error)
	// paymentDescFailureCode is the schema descriptor for failure_code field.
	paymentDescFailureCode := paymentFields[16].Descriptor()
	// payment.FailureCodeValidator is a validator for the "failure_code" field. It is called by the builders before save.
	payment.FailureCodeValidator = paymentDescFailureCode.Validators[0].(func(string) error)
	// paymentDescFailureMessage is the schema descriptor for failure_message field.
	paymentDescFailureMessage := paymentFields[17].Descriptor()
	// payment.FailureMessageValidator is a validator for the "failure_message" field. It is called by the builders before save.
	payment.FailureMessageValidator = paymentDescFailureMessage.Validators[0].(func(string) error)
	// paymentDescRefundedAmount is the schema descriptor for refunded_amount field.
	paymentDescRefundedAmount := paymentFields[18].Descriptor()
	// payment.DefaultRefundedAmount holds the default value on creation for the refunded_amount field.
	payment.DefaultRefundedAmount = paymentDescRefundedAmount.Default.(int64)
	// payment.RefundedAmountValidator is a validator for the "refunded_amount" field. It is called by the builders before save.
	payment.RefundedAmountValidator = paymentDescRefundedAmount.Validators[0].(func(int64) error)
	// paymentDescRefundReason is the schema descriptor for refund_reason field.
	paymentDescRefundReason := paymentFields[20].Descriptor()
	// payment.RefundReasonValidator is a validator for the "refund_reason" field. It is called by the builders before save.
	payment.RefundReasonValidator = paymentDescRefundReason.Validators[0].(func(string) error)
	// paymentDescCreatedAt is the schema descriptor for created_at field.
	paymentDescCreatedAt := paymentFields[23].Descriptor()
	// payment.DefaultCreatedAt holds the default value on creation for the created_at field.
	payment.DefaultCreatedAt = paymentDescCreatedAt.Default.(func() time.Time)
	// paymentDescUpdatedAt is the schema descriptor for updated_at field.
	paymentDescUpdatedAt := paymentFields[24].Descriptor()
	// payment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	payment.DefaultUpdatedAt = paymentDescUpdatedAt.Default.(func() time.Time)
	// payment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	payment.UpdateDefaultUpdatedAt = paymentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// paymentDescID is the schema descriptor for id field.
	paymentDescID := paymentFields[0].Descriptor()
	// payment.DefaultID holds the default value on creation for the id field.
	payment.DefaultID = paymentDescID.Default.(func() uuid.UUID)
	paymentconfirmationFields := schema.PaymentConfirmation{}.Fields()
	_ = paymentconfirmationFields
	// paymentconfirmationDescPaymentMethod is the schema descriptor for payment_method field.
	paymentconfirmationDescPaymentMethod := paymentconfirmationFields[4].Descriptor()
	// paymentconfirmation.PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	paymentconfirmation.PaymentMethodValidator = paymentconfirmationDescPaymentMethod.Validators[0].(func(string) error)
	// paymentconfirmationDescExternalPaymentID is the schema descriptor for external_payment_id field.
	paymentconfirmationDescExternalPaymentID := paymentconfirmationFields[5].Descriptor()
	// paymentconfirmation.ExternalPaymentIDValidator is a validator for the "external_payment_id" field. It is called by the builders before save.
	paymentconfirmation.ExternalPaymentIDValidator = paymentconfirmationDescExternalPaymentID.Validators[0].(func(string) error)
	// paymentconfirmationDescBankTransactionID is the schema descriptor for bank_transaction_id field.
	paymentconfirmationDescBankTransactionID := paymentconfirmationFields[6].Descriptor()
	// paymentconfirmation.BankTransactionIDValidator is a validator for the "bank_transaction_id" field. It is called by the builders before save.
	paymentconfirmation.BankTransactionIDValidator = paymentconfirmationDescBankTransactionID.Validators[0].(func(string) error)
	// paymentconfirmationDescAmountPaid is the schema descriptor for amount_paid field.
	paymentconfirmationDescAmountPaid := paymentconfirmationFields[7].Descriptor()
	// paymentconfirmation.AmountPaidValidator is a validator for the "amount_paid" field. It is called by the builders before save.
	paymentconfirmation.AmountPaidValidator = paymentconfirmationDescAmountPaid.Validators[0].(func(int64) error)
	// paymentconfirmationDescCurrency is the schema descriptor for currency field.
	paymentconfirmationDescCurrency := paymentconfirmationFields[8].Descriptor()
	// paymentconfirmation.DefaultCurrency holds the default value on creation for the currency field.
	paymentconfirmation.DefaultCurrency = paymentconfirmationDescCurrency.Default.(string)
	// paymentconfirmation.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	paymentconfirmation.CurrencyValidator = paymentconfirmationDescCurrency.Validators[0].(func(string) error)
	// paymentconfirmationDescConfirmationMethod is the schema descriptor for confirmation_method field.
	paymentconfirmationDescConfirmationMethod := paymentconfirmationFields[11].Descriptor()
	// paymentconfirmation.DefaultConfirmationMethod holds the default value on creation for the confirmation_method field.
	paymentconfirmation.DefaultConfirmationMethod = paymentconfirmationDescConfirmationMethod.Default.(string)
	// paymentconfirmation.ConfirmationMethodValidator is a validator for the "confirmation_method" field. It is called by the builders before save.
	paymentconfirmation.ConfirmationMethodValidator = paymentconfirmationDescConfirmationMethod.Validators[0].(func(string) error)
	// paymentconfirmationDescFailureReason is the schema descriptor for failure_reason field.
	paymentconfirmationDescFailureReason := paymentconfirmationFields[17].Descriptor()
	// paymentconfirmation.FailureReasonValidator is a validator for the "failure_reason" field. It is called by the builders before save.
	paymentconfirmation.FailureReasonValidator = paymentconfirmationDescFailureReason.Validators[0].(func(string) error)
	// paymentconfirmationDescCreatedAt is the schema descriptor for created_at field.
	paymentconfirmationDescCreatedAt := paymentconfirmationFields[19].Descriptor()
	// paymentconfirmation.DefaultCreatedAt holds the default value on creation for the created_at field.
	paymentconfirmation.DefaultCreatedAt = paymentconfirmationDescCreatedAt.Default.(func() time.Time)
	// paymentconfirmationDescUpdatedAt is the schema descriptor for updated_at field.
	paymentconfirmationDescUpdatedAt := paymentconfirmationFields[20].Descriptor()
	// paymentconfirmation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	paymentconfirmation.DefaultUpdatedAt = paymentconfirmationDescUpdatedAt.Default.(func() time.Time)
	// paymentconfirmation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	paymentconfirmation.UpdateDefaultUpdatedAt = paymentconfirmationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// paymentconfirmationDescID is the schema descriptor for id field.
	paymentconfirmationDescID := paymentconfirmationFields[0].Descriptor()
	// paymentconfirmation.DefaultID holds the default value on creation for the id field.
	paymentconfirmation.DefaultID = paymentconfirmationDescID.Default.(func() uuid.UUID)
	referralFields := schema.Referral{}.Fields()
	_ = referralFields
	// referralDescReferralCode is the schema descriptor for referral_code field.
	referralDescReferralCode := referralFields[3].Descriptor()
	// referral.ReferralCodeValidator is a validator for the "referral_code" field. It is called by the builders before save.
	referral.ReferralCodeValidator = referralDescReferralCode.Validators[0].(func(string) error)
	// referralDescReferredEmail is the schema descriptor for referred_email field.
	referralDescReferredEmail := referralFields[4].Descriptor()
	// referral.ReferredEmailValidator is a validator for the "referred_email" field. It is called by the builders before save.
	referral.ReferredEmailValidator = referralDescReferredEmail.Validators[0].(func(string) error)
	// referralDescReferrerCreditsEarned is the schema descriptor for referrer_credits_earned field.
	referralDescReferrerCreditsEarned := referralFields[6].Descriptor()
	// referral.DefaultReferrerCreditsEarned holds the default value on creation for the referrer_credits_earned field.
	referral.DefaultReferrerCreditsEarned = referralDescReferrerCreditsEarned.Default.(int)
	// referral.ReferrerCreditsEarnedValidator is a validator for the "referrer_credits_earned" field. It is called by the builders before save.
	referral.ReferrerCreditsEarnedValidator = referralDescReferrerCreditsEarned.Validators[0].(func(int) error)
	// referralDescReferredCreditsEarned is the schema descriptor for referred_credits_earned field.
	referralDescReferredCreditsEarned := referralFields[7].Descriptor()
	// referral.DefaultReferredCreditsEarned holds the default value on creation for the referred_credits_earned field.
	referral.DefaultReferredCreditsEarned = referralDescReferredCreditsEarned.Default.(int)
	// referral.ReferredCreditsEarnedValidator is a validator for the "referred_credits_earned" field. It is called by the builders before save.
	referral.ReferredCreditsEarnedValidator = referralDescReferredCreditsEarned.Validators[0].(func(int) error)
	// referralDescReferralType is the schema descriptor for referral_type field.
	referralDescReferralType := referralFields[8].Descriptor()
	// referral.DefaultReferralType holds the default value on creation for the referral_type field.
	referral.DefaultReferralType = referralDescReferralType.Default.(string)
	// referral.ReferralTypeValidator is a validator for the "referral_type" field. It is called by the builders before save.
	referral.ReferralTypeValidator = referralDescReferralType.Validators[0].(func(string) error)
	// referralDescCampaignID is the schema descriptor for campaign_id field.
	referralDescCampaignID := referralFields[13].Descriptor()
	// referral.CampaignIDValidator is a validator for the "campaign_id" field. It is called by the builders before save.
	referral.CampaignIDValidator = referralDescCampaignID.Validators[0].(func(string) error)
	// referralDescSource is the schema descriptor for source field.
	referralDescSource := referralFields[14].Descriptor()
	// referral.SourceValidator is a validator for the "source" field. It is called by the builders before save.
	referral.SourceValidator = referralDescSource.Validators[0].(func(string) error)
	// referralDescCreatedAt is the schema descriptor for created_at field.
	referralDescCreatedAt := referralFields[16].Descriptor()
	// referral.DefaultCreatedAt holds the default value on creation for the created_at field.
	referral.DefaultCreatedAt = referralDescCreatedAt.Default.(func() time.Time)
	// referralDescUpdatedAt is the schema descriptor for updated_at field.
	referralDescUpdatedAt := referralFields[17].Descriptor()
	// referral.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	referral.DefaultUpdatedAt = referralDescUpdatedAt.Default.(func() time.Time)
	// referral.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	referral.UpdateDefaultUpdatedAt = referralDescUpdatedAt.UpdateDefault.(func() time.Time)
	// referralDescID is the schema descriptor for id field.
	referralDescID := referralFields[0].Descriptor()
	// referral.DefaultID holds the default value on creation for the id field.
	referral.DefaultID = referralDescID.Default.(func() uuid.UUID)
	subscriptionFields := schema.Subscription{}.Fields()
	_ = subscriptionFields
	// subscriptionDescCancelAtPeriodEnd is the schema descriptor for cancel_at_period_end field.
	subscriptionDescCancelAtPeriodEnd := subscriptionFields[9].Descriptor()
	// subscription.DefaultCancelAtPeriodEnd holds the default value on creation for the cancel_at_period_end field.
	subscription.DefaultCancelAtPeriodEnd = subscriptionDescCancelAtPeriodEnd.Default.(bool)
	// subscriptionDescPaymentMethod is the schema descriptor for payment_method field.
	subscriptionDescPaymentMethod := subscriptionFields[10].Descriptor()
	// subscription.PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	subscription.PaymentMethodValidator = subscriptionDescPaymentMethod.Validators[0].(func(string) error)
	// subscriptionDescExternalSubscriptionID is the schema descriptor for external_subscription_id field.
	subscriptionDescExternalSubscriptionID := subscriptionFields[15].Descriptor()
	// subscription.ExternalSubscriptionIDValidator is a validator for the "external_subscription_id" field. It is called by the builders before save.
	subscription.ExternalSubscriptionIDValidator = subscriptionDescExternalSubscriptionID.Validators[0].(func(string) error)
	// subscriptionDescPaymentID is the schema descriptor for payment_id field.
	subscriptionDescPaymentID := subscriptionFields[16].Descriptor()
	// subscription.PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	subscription.PaymentIDValidator = subscriptionDescPaymentID.Validators[0].(func(string) error)
	// subscriptionDescCreatedAt is the schema descriptor for created_at field.
	subscriptionDescCreatedAt := subscriptionFields[17].Descriptor()
	// subscription.DefaultCreatedAt holds the default value on creation for the created_at field.
	subscription.DefaultCreatedAt = subscriptionDescCreatedAt.Default.(func() time.Time)
	// subscriptionDescUpdatedAt is the schema descriptor for updated_at field.
	subscriptionDescUpdatedAt := subscriptionFields[18].Descriptor()
	// subscription.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	subscription.DefaultUpdatedAt = subscriptionDescUpdatedAt.Default.(func() time.Time)
	// subscription.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	subscription.UpdateDefaultUpdatedAt = subscriptionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// subscriptionDescID is the schema descriptor for id field.
	subscriptionDescID := subscriptionFields[0].Descriptor()
	// subscription.DefaultID holds the default value on creation for the id field.
	subscription.DefaultID = subscriptionDescID.Default.(func() uuid.UUID)
	subscriptionplanFields := schema.SubscriptionPlan{}.Fields()
	_ = subscriptionplanFields
	// subscriptionplanDescName is the schema descriptor for name field.
	subscriptionplanDescName := subscriptionplanFields[1].Descriptor()
	// subscriptionplan.NameValidator is a validator for the "name" field. It is called by the builders before save.
	subscriptionplan.NameValidator = subscriptionplanDescName.Validators[0].(func(string) error)
	// subscriptionplanDescDisplayName is the schema descriptor for display_name field.
	subscriptionplanDescDisplayName := subscriptionplanFields[2].Descriptor()
	// subscriptionplan.DisplayNameValidator is a validator for the "display_name" field. It is called by the builders before save.
	subscriptionplan.DisplayNameValidator = subscriptionplanDescDisplayName.Validators[0].(func(string) error)
	// subscriptionplanDescPrice is the schema descriptor for price field.
	subscriptionplanDescPrice := subscriptionplanFields[4].Descriptor()
	// subscriptionplan.PriceValidator is a validator for the "price" field. It is called by the builders before save.
	subscriptionplan.PriceValidator = subscriptionplanDescPrice.Validators[0].(func(int64) error)
	// subscriptionplanDescCurrency is the schema descriptor for currency field.
	subscriptionplanDescCurrency := subscriptionplanFields[5].Descriptor()
	// subscriptionplan.DefaultCurrency holds the default value on creation for the currency field.
	subscriptionplan.DefaultCurrency = subscriptionplanDescCurrency.Default.(string)
	// subscriptionplan.CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	subscriptionplan.CurrencyValidator = subscriptionplanDescCurrency.Validators[0].(func(string) error)
	// subscriptionplanDescCreditsIncluded is the schema descriptor for credits_included field.
	subscriptionplanDescCreditsIncluded := subscriptionplanFields[7].Descriptor()
	// subscriptionplan.DefaultCreditsIncluded holds the default value on creation for the credits_included field.
	subscriptionplan.DefaultCreditsIncluded = subscriptionplanDescCreditsIncluded.Default.(int)
	// subscriptionplan.CreditsIncludedValidator is a validator for the "credits_included" field. It is called by the builders before save.
	subscriptionplan.CreditsIncludedValidator = subscriptionplanDescCreditsIncluded.Validators[0].(func(int) error)
	// subscriptionplanDescMonthlyCredits is the schema descriptor for monthly_credits field.
	subscriptionplanDescMonthlyCredits := subscriptionplanFields[8].Descriptor()
	// subscriptionplan.DefaultMonthlyCredits holds the default value on creation for the monthly_credits field.
	subscriptionplan.DefaultMonthlyCredits = subscriptionplanDescMonthlyCredits.Default.(int)
	// subscriptionplan.MonthlyCreditsValidator is a validator for the "monthly_credits" field. It is called by the builders before save.
	subscriptionplan.MonthlyCreditsValidator = subscriptionplanDescMonthlyCredits.Validators[0].(func(int) error)
	// subscriptionplanDescIsUnlimited is the schema descriptor for is_unlimited field.
	subscriptionplanDescIsUnlimited := subscriptionplanFields[9].Descriptor()
	// subscriptionplan.DefaultIsUnlimited holds the default value on creation for the is_unlimited field.
	subscriptionplan.DefaultIsUnlimited = subscriptionplanDescIsUnlimited.Default.(bool)
	// subscriptionplanDescMaxUsers is the schema descriptor for max_users field.
	subscriptionplanDescMaxUsers := subscriptionplanFields[12].Descriptor()
	// subscriptionplan.DefaultMaxUsers holds the default value on creation for the max_users field.
	subscriptionplan.DefaultMaxUsers = subscriptionplanDescMaxUsers.Default.(int)
	// subscriptionplan.MaxUsersValidator is a validator for the "max_users" field. It is called by the builders before save.
	subscriptionplan.MaxUsersValidator = subscriptionplanDescMaxUsers.Validators[0].(func(int) error)
	// subscriptionplanDescMaxWorkspaces is the schema descriptor for max_workspaces field.
	subscriptionplanDescMaxWorkspaces := subscriptionplanFields[13].Descriptor()
	// subscriptionplan.DefaultMaxWorkspaces holds the default value on creation for the max_workspaces field.
	subscriptionplan.DefaultMaxWorkspaces = subscriptionplanDescMaxWorkspaces.Default.(int)
	// subscriptionplan.MaxWorkspacesValidator is a validator for the "max_workspaces" field. It is called by the builders before save.
	subscriptionplan.MaxWorkspacesValidator = subscriptionplanDescMaxWorkspaces.Validators[0].(func(int) error)
	// subscriptionplanDescIsActive is the schema descriptor for is_active field.
	subscriptionplanDescIsActive := subscriptionplanFields[14].Descriptor()
	// subscriptionplan.DefaultIsActive holds the default value on creation for the is_active field.
	subscriptionplan.DefaultIsActive = subscriptionplanDescIsActive.Default.(bool)
	// subscriptionplanDescIsFeatured is the schema descriptor for is_featured field.
	subscriptionplanDescIsFeatured := subscriptionplanFields[15].Descriptor()
	// subscriptionplan.DefaultIsFeatured holds the default value on creation for the is_featured field.
	subscriptionplan.DefaultIsFeatured = subscriptionplanDescIsFeatured.Default.(bool)
	// subscriptionplanDescSortOrder is the schema descriptor for sort_order field.
	subscriptionplanDescSortOrder := subscriptionplanFields[16].Descriptor()
	// subscriptionplan.DefaultSortOrder holds the default value on creation for the sort_order field.
	subscriptionplan.DefaultSortOrder = subscriptionplanDescSortOrder.Default.(int)
	// subscriptionplanDescStripePriceID is the schema descriptor for stripe_price_id field.
	subscriptionplanDescStripePriceID := subscriptionplanFields[17].Descriptor()
	// subscriptionplan.StripePriceIDValidator is a validator for the "stripe_price_id" field. It is called by the builders before save.
	subscriptionplan.StripePriceIDValidator = subscriptionplanDescStripePriceID.Validators[0].(func(string) error)
	// subscriptionplanDescStripeProductID is the schema descriptor for stripe_product_id field.
	subscriptionplanDescStripeProductID := subscriptionplanFields[18].Descriptor()
	// subscriptionplan.StripeProductIDValidator is a validator for the "stripe_product_id" field. It is called by the builders before save.
	subscriptionplan.StripeProductIDValidator = subscriptionplanDescStripeProductID.Validators[0].(func(string) error)
	// subscriptionplanDescCreatedAt is the schema descriptor for created_at field.
	subscriptionplanDescCreatedAt := subscriptionplanFields[20].Descriptor()
	// subscriptionplan.DefaultCreatedAt holds the default value on creation for the created_at field.
	subscriptionplan.DefaultCreatedAt = subscriptionplanDescCreatedAt.Default.(func() time.Time)
	// subscriptionplanDescUpdatedAt is the schema descriptor for updated_at field.
	subscriptionplanDescUpdatedAt := subscriptionplanFields[21].Descriptor()
	// subscriptionplan.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	subscriptionplan.DefaultUpdatedAt = subscriptionplanDescUpdatedAt.Default.(func() time.Time)
	// subscriptionplan.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	subscriptionplan.UpdateDefaultUpdatedAt = subscriptionplanDescUpdatedAt.UpdateDefault.(func() time.Time)
	// subscriptionplanDescID is the schema descriptor for id field.
	subscriptionplanDescID := subscriptionplanFields[0].Descriptor()
	// subscriptionplan.DefaultID holds the default value on creation for the id field.
	subscriptionplan.DefaultID = subscriptionplanDescID.Default.(func() uuid.UUID)
	usercreditFields := schema.UserCredit{}.Fields()
	_ = usercreditFields
	// usercreditDescCurrentCredits is the schema descriptor for current_credits field.
	usercreditDescCurrentCredits := usercreditFields[2].Descriptor()
	// usercredit.DefaultCurrentCredits holds the default value on creation for the current_credits field.
	usercredit.DefaultCurrentCredits = usercreditDescCurrentCredits.Default.(int)
	// usercredit.CurrentCreditsValidator is a validator for the "current_credits" field. It is called by the builders before save.
	usercredit.CurrentCreditsValidator = usercreditDescCurrentCredits.Validators[0].(func(int) error)
	// usercreditDescTotalCredits is the schema descriptor for total_credits field.
	usercreditDescTotalCredits := usercreditFields[3].Descriptor()
	// usercredit.DefaultTotalCredits holds the default value on creation for the total_credits field.
	usercredit.DefaultTotalCredits = usercreditDescTotalCredits.Default.(int)
	// usercredit.TotalCreditsValidator is a validator for the "total_credits" field. It is called by the builders before save.
	usercredit.TotalCreditsValidator = usercreditDescTotalCredits.Validators[0].(func(int) error)
	// usercreditDescPlanID is the schema descriptor for plan_id field.
	usercreditDescPlanID := usercreditFields[4].Descriptor()
	// usercredit.PlanIDValidator is a validator for the "plan_id" field. It is called by the builders before save.
	usercredit.PlanIDValidator = usercreditDescPlanID.Validators[0].(func(string) error)
	// usercreditDescPlanName is the schema descriptor for plan_name field.
	usercreditDescPlanName := usercreditFields[5].Descriptor()
	// usercredit.PlanNameValidator is a validator for the "plan_name" field. It is called by the builders before save.
	usercredit.PlanNameValidator = usercreditDescPlanName.Validators[0].(func(string) error)
	// usercreditDescMonthlyLimit is the schema descriptor for monthly_limit field.
	usercreditDescMonthlyLimit := usercreditFields[8].Descriptor()
	// usercredit.DefaultMonthlyLimit holds the default value on creation for the monthly_limit field.
	usercredit.DefaultMonthlyLimit = usercreditDescMonthlyLimit.Default.(int)
	// usercredit.MonthlyLimitValidator is a validator for the "monthly_limit" field. It is called by the builders before save.
	usercredit.MonthlyLimitValidator = usercreditDescMonthlyLimit.Validators[0].(func(int) error)
	// usercreditDescMonthlyUsed is the schema descriptor for monthly_used field.
	usercreditDescMonthlyUsed := usercreditFields[9].Descriptor()
	// usercredit.DefaultMonthlyUsed holds the default value on creation for the monthly_used field.
	usercredit.DefaultMonthlyUsed = usercreditDescMonthlyUsed.Default.(int)
	// usercredit.MonthlyUsedValidator is a validator for the "monthly_used" field. It is called by the builders before save.
	usercredit.MonthlyUsedValidator = usercreditDescMonthlyUsed.Validators[0].(func(int) error)
	// usercreditDescCreatedAt is the schema descriptor for created_at field.
	usercreditDescCreatedAt := usercreditFields[12].Descriptor()
	// usercredit.DefaultCreatedAt holds the default value on creation for the created_at field.
	usercredit.DefaultCreatedAt = usercreditDescCreatedAt.Default.(func() time.Time)
	// usercreditDescUpdatedAt is the schema descriptor for updated_at field.
	usercreditDescUpdatedAt := usercreditFields[13].Descriptor()
	// usercredit.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	usercredit.DefaultUpdatedAt = usercreditDescUpdatedAt.Default.(func() time.Time)
	// usercredit.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	usercredit.UpdateDefaultUpdatedAt = usercreditDescUpdatedAt.UpdateDefault.(func() time.Time)
	// usercreditDescID is the schema descriptor for id field.
	usercreditDescID := usercreditFields[0].Descriptor()
	// usercredit.DefaultID holds the default value on creation for the id field.
	usercredit.DefaultID = usercreditDescID.Default.(func() uuid.UUID)
}

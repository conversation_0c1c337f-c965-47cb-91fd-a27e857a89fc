package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// AuthMiddleware handles authentication middleware
type AuthMiddleware struct {
	userClient userv1.AuthServiceClient
	logger     logging.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(userClient userv1.AuthServiceClient, logger logging.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		userClient: userClient,
		logger:     logger,
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "MISSING_TOKEN",
				"message": "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "INVALID_TOKEN_FORMAT",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil {
			m.logger.WithError(err).Error("Token validation failed")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "INVALID_TOKEN",
				"message": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		if !resp.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "INVALID_TOKEN",
				"message": "Token is not valid",
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		c.Set("expires_at", resp.ExpiresAt)

		// Extract additional info from claims if available
		if resp.Claims != nil {
			if email, exists := resp.Claims["email"]; exists {
				c.Set("email", email)
			}
			if role, exists := resp.Claims["role"]; exists {
				c.Set("role", role)
			}
			if sessionID, exists := resp.Claims["session_id"]; exists {
				c.Set("session_id", sessionID)
			}
		}

		c.Next()
	}
}

// OptionalAuth middleware that optionally authenticates user
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		token := tokenParts[1]

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil || !resp.Valid {
			// Don't abort, just continue without authentication
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		c.Set("expires_at", resp.ExpiresAt)

		// Extract additional info from claims if available
		if resp.Claims != nil {
			if email, exists := resp.Claims["email"]; exists {
				c.Set("email", email)
			}
			if role, exists := resp.Claims["role"]; exists {
				c.Set("role", role)
			}
			if sessionID, exists := resp.Claims["session_id"]; exists {
				c.Set("session_id", sessionID)
			}
		}

		c.Next()
	}
}

// RequireRole middleware that requires specific role
func (m *AuthMiddleware) RequireRole(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "UNAUTHORIZED",
				"message": "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		if userRole != requiredRole && userRole != "admin" { // admin can access everything
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "INSUFFICIENT_PERMISSIONS",
				"message": "Insufficient permissions to access this resource",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserID extracts user ID from context
func GetUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	return userID.(string), true
}

// GetUserEmail extracts user email from context
func GetUserEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("email")
	if !exists {
		return "", false
	}
	return email.(string), true
}

// GetUserRole extracts user role from context
func GetUserRole(c *gin.Context) (string, bool) {
	role, exists := c.Get("role")
	if !exists {
		return "", false
	}
	return role.(string), true
}

// GetSessionID extracts session ID from context
func GetSessionID(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}
	return sessionID.(string), true
}

// RequireUserID middleware ensures user ID is present in context
func RequireUserID() gin.HandlerFunc {
	return func(c *gin.Context) {
		if _, exists := GetUserID(c); !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "UNAUTHORIZED",
				"message": "User authentication required",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

package config

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
)

// Config holds the complete configuration for asset service
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Storage  StorageConfig  `mapstructure:"storage"`
	Cleanup  CleanupConfig  `mapstructure:"cleanup"`
	Security SecurityConfig `mapstructure:"security"`
	Features FeaturesConfig `mapstructure:"features"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Env      string `mapstructure:"env"`
	Host     string `mapstructure:"host"`
	GRPCPort int    `mapstructure:"grpc_port"`
	HTTPPort int    `mapstructure:"http_port"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// PostgreSQL configuration
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Name     string `mapstructure:"name"`
	SSLMode  string `mapstructure:"ssl_mode"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`

	// Connection pool settings
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
}

// StorageConfig holds storage-related configuration
type StorageConfig struct {
	Provider  string `mapstructure:"provider"` // "minio" or "s3"
	Endpoint  string `mapstructure:"endpoint"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	Bucket    string `mapstructure:"bucket"`
	UseSSL    bool   `mapstructure:"use_ssl"`
	PublicURL string `mapstructure:"public_url"`
	Region    string `mapstructure:"region"`
}

// CleanupConfig holds cleanup service configuration
type CleanupConfig struct {
	Enabled              bool          `mapstructure:"enabled"`
	PendingUploadTTL     time.Duration `mapstructure:"pending_upload_ttl"`
	OrphanedFileTTL      time.Duration `mapstructure:"orphaned_file_ttl"`
	DeletedAssetTTL      time.Duration `mapstructure:"deleted_asset_ttl"`
	CleanupInterval      time.Duration `mapstructure:"cleanup_interval"`
	BatchSize            int           `mapstructure:"batch_size"`
	EnableStorageCleanup bool          `mapstructure:"enable_storage_cleanup"`
	DryRun               bool          `mapstructure:"dry_run"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	MaxFileSize         int64         `mapstructure:"max_file_size"`
	AllowedMimeTypes    []string      `mapstructure:"allowed_mime_types"`
	EnableVirusScanning bool          `mapstructure:"enable_virus_scanning"`
	QuarantineTTL       time.Duration `mapstructure:"quarantine_ttl"`
}

// FeaturesConfig holds feature flags
type FeaturesConfig struct {
	EnableImageProcessing bool `mapstructure:"enable_image_processing"`
	EnableRAG             bool `mapstructure:"enable_rag"`
	EnableFolders         bool `mapstructure:"enable_folders"`
	EnableVersioning      bool `mapstructure:"enable_versioning"`
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig() (*Config, error) {
	// Set config file name and paths
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")
	viper.AddConfigPath(".")

	// Set environment variable prefix
	viper.SetEnvPrefix("ASSET_SERVICE")
	viper.AutomaticEnv()

	// Set defaults
	setDefaults()

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found, use defaults and environment variables
	}

	// Check for environment-specific config
	env := viper.GetString("server.env")
	if env != "" {
		viper.SetConfigName(env)
		if err := viper.MergeInConfig(); err != nil {
			// Environment config not found, continue with base config
		}
	}

	// Unmarshal config
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.env", "development")
	viper.SetDefault("server.grpc_port", 50054)
	viper.SetDefault("server.http_port", 8084)
	viper.SetDefault("server.host", "0.0.0.0")

	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.sqlite_path", "./data/asset_service.db")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.name", "asset_service_db")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// Storage defaults
	viper.SetDefault("storage.provider", "minio")
	viper.SetDefault("storage.endpoint", "localhost:9000")
	viper.SetDefault("storage.access_key", "minioadmin")
	viper.SetDefault("storage.secret_key", "minioadmin")
	viper.SetDefault("storage.bucket", "assets")
	viper.SetDefault("storage.use_ssl", false)
	viper.SetDefault("storage.public_url", "http://localhost:9000")
	viper.SetDefault("storage.region", "us-east-1")

	// Cleanup defaults
	viper.SetDefault("cleanup.enabled", true)
	viper.SetDefault("cleanup.pending_upload_ttl", "1h")
	viper.SetDefault("cleanup.orphaned_file_ttl", "24h")
	viper.SetDefault("cleanup.deleted_asset_ttl", "720h") // 30 days
	viper.SetDefault("cleanup.cleanup_interval", "30m")
	viper.SetDefault("cleanup.batch_size", 100)
	viper.SetDefault("cleanup.enable_storage_cleanup", true)
	viper.SetDefault("cleanup.dry_run", false)

	// Security defaults
	viper.SetDefault("security.max_file_size", 104857600) // 100MB
	viper.SetDefault("security.allowed_mime_types", []string{
		"image/jpeg", "image/png", "image/gif", "image/webp",
		"application/pdf", "text/plain", "application/json",
		"text/markdown", "text/csv",
	})
	viper.SetDefault("security.enable_virus_scanning", true)
	viper.SetDefault("security.quarantine_ttl", "720h") // 30 days

	// Features defaults
	viper.SetDefault("features.enable_image_processing", true)
	viper.SetDefault("features.enable_rag", true)
	viper.SetDefault("features.enable_folders", true)
	viper.SetDefault("features.enable_versioning", false)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// GetDriverName returns the database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres"
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate database config
	if c.Database.Type != "postgres" && c.Database.Type != "sqlite" {
		return fmt.Errorf("unsupported database type: %s", c.Database.Type)
	}

	if c.Database.Type == "sqlite" && c.Database.SQLitePath == "" {
		return fmt.Errorf("sqlite_path is required when using SQLite")
	}

	if c.Database.Type == "postgres" {
		if c.Database.Host == "" {
			return fmt.Errorf("database host is required for PostgreSQL")
		}
		if c.Database.Name == "" {
			return fmt.Errorf("database name is required for PostgreSQL")
		}
		if c.Database.User == "" {
			return fmt.Errorf("database user is required for PostgreSQL")
		}
	}

	// Validate storage config
	if c.Storage.Provider != "minio" && c.Storage.Provider != "s3" {
		return fmt.Errorf("unsupported storage provider: %s", c.Storage.Provider)
	}

	if c.Storage.Bucket == "" {
		return fmt.Errorf("storage bucket is required")
	}

	return nil
}

// CreateDataDirectory creates the data directory for SQLite if needed
func (c *DatabaseConfig) CreateDataDirectory() error {
	if c.Type == "sqlite" {
		dir := "./data"
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create data directory: %w", err)
		}
	}
	return nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/predicate"
	"github.com/social-content-ai/user-service/ent/session"
	"github.com/social-content-ai/user-service/ent/user"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks    []Hook
	mutation *UserMutation
}

// Where appends a list predicates to the UserUpdate builder.
func (uu *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetFullName sets the "full_name" field.
func (uu *UserUpdate) SetFullName(s string) *UserUpdate {
	uu.mutation.SetFullName(s)
	return uu
}

// SetNillableFullName sets the "full_name" field if the given value is not nil.
func (uu *UserUpdate) SetNillableFullName(s *string) *UserUpdate {
	if s != nil {
		uu.SetFullName(*s)
	}
	return uu
}

// ClearFullName clears the value of the "full_name" field.
func (uu *UserUpdate) ClearFullName() *UserUpdate {
	uu.mutation.ClearFullName()
	return uu
}

// SetEmail sets the "email" field.
func (uu *UserUpdate) SetEmail(s string) *UserUpdate {
	uu.mutation.SetEmail(s)
	return uu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmail(s *string) *UserUpdate {
	if s != nil {
		uu.SetEmail(*s)
	}
	return uu
}

// SetPhone sets the "phone" field.
func (uu *UserUpdate) SetPhone(s string) *UserUpdate {
	uu.mutation.SetPhone(s)
	return uu
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePhone(s *string) *UserUpdate {
	if s != nil {
		uu.SetPhone(*s)
	}
	return uu
}

// ClearPhone clears the value of the "phone" field.
func (uu *UserUpdate) ClearPhone() *UserUpdate {
	uu.mutation.ClearPhone()
	return uu
}

// SetAvatarURL sets the "avatar_url" field.
func (uu *UserUpdate) SetAvatarURL(s string) *UserUpdate {
	uu.mutation.SetAvatarURL(s)
	return uu
}

// SetNillableAvatarURL sets the "avatar_url" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAvatarURL(s *string) *UserUpdate {
	if s != nil {
		uu.SetAvatarURL(*s)
	}
	return uu
}

// ClearAvatarURL clears the value of the "avatar_url" field.
func (uu *UserUpdate) ClearAvatarURL() *UserUpdate {
	uu.mutation.ClearAvatarURL()
	return uu
}

// SetBio sets the "bio" field.
func (uu *UserUpdate) SetBio(s string) *UserUpdate {
	uu.mutation.SetBio(s)
	return uu
}

// SetNillableBio sets the "bio" field if the given value is not nil.
func (uu *UserUpdate) SetNillableBio(s *string) *UserUpdate {
	if s != nil {
		uu.SetBio(*s)
	}
	return uu
}

// ClearBio clears the value of the "bio" field.
func (uu *UserUpdate) ClearBio() *UserUpdate {
	uu.mutation.ClearBio()
	return uu
}

// SetCompany sets the "company" field.
func (uu *UserUpdate) SetCompany(s string) *UserUpdate {
	uu.mutation.SetCompany(s)
	return uu
}

// SetNillableCompany sets the "company" field if the given value is not nil.
func (uu *UserUpdate) SetNillableCompany(s *string) *UserUpdate {
	if s != nil {
		uu.SetCompany(*s)
	}
	return uu
}

// ClearCompany clears the value of the "company" field.
func (uu *UserUpdate) ClearCompany() *UserUpdate {
	uu.mutation.ClearCompany()
	return uu
}

// SetIndustry sets the "industry" field.
func (uu *UserUpdate) SetIndustry(s string) *UserUpdate {
	uu.mutation.SetIndustry(s)
	return uu
}

// SetNillableIndustry sets the "industry" field if the given value is not nil.
func (uu *UserUpdate) SetNillableIndustry(s *string) *UserUpdate {
	if s != nil {
		uu.SetIndustry(*s)
	}
	return uu
}

// ClearIndustry clears the value of the "industry" field.
func (uu *UserUpdate) ClearIndustry() *UserUpdate {
	uu.mutation.ClearIndustry()
	return uu
}

// SetRole sets the "role" field.
func (uu *UserUpdate) SetRole(u user.Role) *UserUpdate {
	uu.mutation.SetRole(u)
	return uu
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (uu *UserUpdate) SetNillableRole(u *user.Role) *UserUpdate {
	if u != nil {
		uu.SetRole(*u)
	}
	return uu
}

// SetPasswordHash sets the "password_hash" field.
func (uu *UserUpdate) SetPasswordHash(s string) *UserUpdate {
	uu.mutation.SetPasswordHash(s)
	return uu
}

// SetNillablePasswordHash sets the "password_hash" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePasswordHash(s *string) *UserUpdate {
	if s != nil {
		uu.SetPasswordHash(*s)
	}
	return uu
}

// SetIsVerified sets the "is_verified" field.
func (uu *UserUpdate) SetIsVerified(b bool) *UserUpdate {
	uu.mutation.SetIsVerified(b)
	return uu
}

// SetNillableIsVerified sets the "is_verified" field if the given value is not nil.
func (uu *UserUpdate) SetNillableIsVerified(b *bool) *UserUpdate {
	if b != nil {
		uu.SetIsVerified(*b)
	}
	return uu
}

// SetTwoFactorEnabled sets the "two_factor_enabled" field.
func (uu *UserUpdate) SetTwoFactorEnabled(b bool) *UserUpdate {
	uu.mutation.SetTwoFactorEnabled(b)
	return uu
}

// SetNillableTwoFactorEnabled sets the "two_factor_enabled" field if the given value is not nil.
func (uu *UserUpdate) SetNillableTwoFactorEnabled(b *bool) *UserUpdate {
	if b != nil {
		uu.SetTwoFactorEnabled(*b)
	}
	return uu
}

// SetTwoFactorSecret sets the "two_factor_secret" field.
func (uu *UserUpdate) SetTwoFactorSecret(s string) *UserUpdate {
	uu.mutation.SetTwoFactorSecret(s)
	return uu
}

// SetNillableTwoFactorSecret sets the "two_factor_secret" field if the given value is not nil.
func (uu *UserUpdate) SetNillableTwoFactorSecret(s *string) *UserUpdate {
	if s != nil {
		uu.SetTwoFactorSecret(*s)
	}
	return uu
}

// ClearTwoFactorSecret clears the value of the "two_factor_secret" field.
func (uu *UserUpdate) ClearTwoFactorSecret() *UserUpdate {
	uu.mutation.ClearTwoFactorSecret()
	return uu
}

// SetTwoFactorBackupCodes sets the "two_factor_backup_codes" field.
func (uu *UserUpdate) SetTwoFactorBackupCodes(s []string) *UserUpdate {
	uu.mutation.SetTwoFactorBackupCodes(s)
	return uu
}

// AppendTwoFactorBackupCodes appends s to the "two_factor_backup_codes" field.
func (uu *UserUpdate) AppendTwoFactorBackupCodes(s []string) *UserUpdate {
	uu.mutation.AppendTwoFactorBackupCodes(s)
	return uu
}

// ClearTwoFactorBackupCodes clears the value of the "two_factor_backup_codes" field.
func (uu *UserUpdate) ClearTwoFactorBackupCodes() *UserUpdate {
	uu.mutation.ClearTwoFactorBackupCodes()
	return uu
}

// SetTwoFactorEnabledAt sets the "two_factor_enabled_at" field.
func (uu *UserUpdate) SetTwoFactorEnabledAt(t time.Time) *UserUpdate {
	uu.mutation.SetTwoFactorEnabledAt(t)
	return uu
}

// SetNillableTwoFactorEnabledAt sets the "two_factor_enabled_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableTwoFactorEnabledAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetTwoFactorEnabledAt(*t)
	}
	return uu
}

// ClearTwoFactorEnabledAt clears the value of the "two_factor_enabled_at" field.
func (uu *UserUpdate) ClearTwoFactorEnabledAt() *UserUpdate {
	uu.mutation.ClearTwoFactorEnabledAt()
	return uu
}

// SetNotificationSettings sets the "notification_settings" field.
func (uu *UserUpdate) SetNotificationSettings(m map[string]interface{}) *UserUpdate {
	uu.mutation.SetNotificationSettings(m)
	return uu
}

// ClearNotificationSettings clears the value of the "notification_settings" field.
func (uu *UserUpdate) ClearNotificationSettings() *UserUpdate {
	uu.mutation.ClearNotificationSettings()
	return uu
}

// SetInviteCode sets the "invite_code" field.
func (uu *UserUpdate) SetInviteCode(s string) *UserUpdate {
	uu.mutation.SetInviteCode(s)
	return uu
}

// SetNillableInviteCode sets the "invite_code" field if the given value is not nil.
func (uu *UserUpdate) SetNillableInviteCode(s *string) *UserUpdate {
	if s != nil {
		uu.SetInviteCode(*s)
	}
	return uu
}

// ClearInviteCode clears the value of the "invite_code" field.
func (uu *UserUpdate) ClearInviteCode() *UserUpdate {
	uu.mutation.ClearInviteCode()
	return uu
}

// SetAffCode sets the "aff_code" field.
func (uu *UserUpdate) SetAffCode(s string) *UserUpdate {
	uu.mutation.SetAffCode(s)
	return uu
}

// SetNillableAffCode sets the "aff_code" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAffCode(s *string) *UserUpdate {
	if s != nil {
		uu.SetAffCode(*s)
	}
	return uu
}

// ClearAffCode clears the value of the "aff_code" field.
func (uu *UserUpdate) ClearAffCode() *UserUpdate {
	uu.mutation.ClearAffCode()
	return uu
}

// SetAffJoinedAt sets the "aff_joined_at" field.
func (uu *UserUpdate) SetAffJoinedAt(t time.Time) *UserUpdate {
	uu.mutation.SetAffJoinedAt(t)
	return uu
}

// SetNillableAffJoinedAt sets the "aff_joined_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAffJoinedAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetAffJoinedAt(*t)
	}
	return uu
}

// ClearAffJoinedAt clears the value of the "aff_joined_at" field.
func (uu *UserUpdate) ClearAffJoinedAt() *UserUpdate {
	uu.mutation.ClearAffJoinedAt()
	return uu
}

// SetUpdatedAt sets the "updated_at" field.
func (uu *UserUpdate) SetUpdatedAt(t time.Time) *UserUpdate {
	uu.mutation.SetUpdatedAt(t)
	return uu
}

// SetDeletedAt sets the "deleted_at" field.
func (uu *UserUpdate) SetDeletedAt(t time.Time) *UserUpdate {
	uu.mutation.SetDeletedAt(t)
	return uu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDeletedAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetDeletedAt(*t)
	}
	return uu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uu *UserUpdate) ClearDeletedAt() *UserUpdate {
	uu.mutation.ClearDeletedAt()
	return uu
}

// AddSessionIDs adds the "sessions" edge to the Session entity by IDs.
func (uu *UserUpdate) AddSessionIDs(ids ...uuid.UUID) *UserUpdate {
	uu.mutation.AddSessionIDs(ids...)
	return uu
}

// AddSessions adds the "sessions" edges to the Session entity.
func (uu *UserUpdate) AddSessions(s ...*Session) *UserUpdate {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uu.AddSessionIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uu *UserUpdate) Mutation() *UserMutation {
	return uu.mutation
}

// ClearSessions clears all "sessions" edges to the Session entity.
func (uu *UserUpdate) ClearSessions() *UserUpdate {
	uu.mutation.ClearSessions()
	return uu
}

// RemoveSessionIDs removes the "sessions" edge to Session entities by IDs.
func (uu *UserUpdate) RemoveSessionIDs(ids ...uuid.UUID) *UserUpdate {
	uu.mutation.RemoveSessionIDs(ids...)
	return uu
}

// RemoveSessions removes "sessions" edges to Session entities.
func (uu *UserUpdate) RemoveSessions(s ...*Session) *UserUpdate {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uu.RemoveSessionIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UserUpdate) Save(ctx context.Context) (int, error) {
	uu.defaults()
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UserUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UserUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UserUpdate) defaults() {
	if _, ok := uu.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		uu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uu *UserUpdate) check() error {
	if v, ok := uu.mutation.FullName(); ok {
		if err := user.FullNameValidator(v); err != nil {
			return &ValidationError{Name: "full_name", err: fmt.Errorf(`ent: validator failed for field "User.full_name": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Phone(); ok {
		if err := user.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "User.phone": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Company(); ok {
		if err := user.CompanyValidator(v); err != nil {
			return &ValidationError{Name: "company", err: fmt.Errorf(`ent: validator failed for field "User.company": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Industry(); ok {
		if err := user.IndustryValidator(v); err != nil {
			return &ValidationError{Name: "industry", err: fmt.Errorf(`ent: validator failed for field "User.industry": %w`, err)}
		}
	}
	if v, ok := uu.mutation.Role(); ok {
		if err := user.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "User.role": %w`, err)}
		}
	}
	if v, ok := uu.mutation.InviteCode(); ok {
		if err := user.InviteCodeValidator(v); err != nil {
			return &ValidationError{Name: "invite_code", err: fmt.Errorf(`ent: validator failed for field "User.invite_code": %w`, err)}
		}
	}
	if v, ok := uu.mutation.AffCode(); ok {
		if err := user.AffCodeValidator(v); err != nil {
			return &ValidationError{Name: "aff_code", err: fmt.Errorf(`ent: validator failed for field "User.aff_code": %w`, err)}
		}
	}
	return nil
}

func (uu *UserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.FullName(); ok {
		_spec.SetField(user.FieldFullName, field.TypeString, value)
	}
	if uu.mutation.FullNameCleared() {
		_spec.ClearField(user.FieldFullName, field.TypeString)
	}
	if value, ok := uu.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := uu.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
	}
	if uu.mutation.PhoneCleared() {
		_spec.ClearField(user.FieldPhone, field.TypeString)
	}
	if value, ok := uu.mutation.AvatarURL(); ok {
		_spec.SetField(user.FieldAvatarURL, field.TypeString, value)
	}
	if uu.mutation.AvatarURLCleared() {
		_spec.ClearField(user.FieldAvatarURL, field.TypeString)
	}
	if value, ok := uu.mutation.Bio(); ok {
		_spec.SetField(user.FieldBio, field.TypeString, value)
	}
	if uu.mutation.BioCleared() {
		_spec.ClearField(user.FieldBio, field.TypeString)
	}
	if value, ok := uu.mutation.Company(); ok {
		_spec.SetField(user.FieldCompany, field.TypeString, value)
	}
	if uu.mutation.CompanyCleared() {
		_spec.ClearField(user.FieldCompany, field.TypeString)
	}
	if value, ok := uu.mutation.Industry(); ok {
		_spec.SetField(user.FieldIndustry, field.TypeString, value)
	}
	if uu.mutation.IndustryCleared() {
		_spec.ClearField(user.FieldIndustry, field.TypeString)
	}
	if value, ok := uu.mutation.Role(); ok {
		_spec.SetField(user.FieldRole, field.TypeEnum, value)
	}
	if value, ok := uu.mutation.PasswordHash(); ok {
		_spec.SetField(user.FieldPasswordHash, field.TypeString, value)
	}
	if value, ok := uu.mutation.IsVerified(); ok {
		_spec.SetField(user.FieldIsVerified, field.TypeBool, value)
	}
	if value, ok := uu.mutation.TwoFactorEnabled(); ok {
		_spec.SetField(user.FieldTwoFactorEnabled, field.TypeBool, value)
	}
	if value, ok := uu.mutation.TwoFactorSecret(); ok {
		_spec.SetField(user.FieldTwoFactorSecret, field.TypeString, value)
	}
	if uu.mutation.TwoFactorSecretCleared() {
		_spec.ClearField(user.FieldTwoFactorSecret, field.TypeString)
	}
	if value, ok := uu.mutation.TwoFactorBackupCodes(); ok {
		_spec.SetField(user.FieldTwoFactorBackupCodes, field.TypeJSON, value)
	}
	if value, ok := uu.mutation.AppendedTwoFactorBackupCodes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, user.FieldTwoFactorBackupCodes, value)
		})
	}
	if uu.mutation.TwoFactorBackupCodesCleared() {
		_spec.ClearField(user.FieldTwoFactorBackupCodes, field.TypeJSON)
	}
	if value, ok := uu.mutation.TwoFactorEnabledAt(); ok {
		_spec.SetField(user.FieldTwoFactorEnabledAt, field.TypeTime, value)
	}
	if uu.mutation.TwoFactorEnabledAtCleared() {
		_spec.ClearField(user.FieldTwoFactorEnabledAt, field.TypeTime)
	}
	if value, ok := uu.mutation.NotificationSettings(); ok {
		_spec.SetField(user.FieldNotificationSettings, field.TypeJSON, value)
	}
	if uu.mutation.NotificationSettingsCleared() {
		_spec.ClearField(user.FieldNotificationSettings, field.TypeJSON)
	}
	if value, ok := uu.mutation.InviteCode(); ok {
		_spec.SetField(user.FieldInviteCode, field.TypeString, value)
	}
	if uu.mutation.InviteCodeCleared() {
		_spec.ClearField(user.FieldInviteCode, field.TypeString)
	}
	if value, ok := uu.mutation.AffCode(); ok {
		_spec.SetField(user.FieldAffCode, field.TypeString, value)
	}
	if uu.mutation.AffCodeCleared() {
		_spec.ClearField(user.FieldAffCode, field.TypeString)
	}
	if value, ok := uu.mutation.AffJoinedAt(); ok {
		_spec.SetField(user.FieldAffJoinedAt, field.TypeTime, value)
	}
	if uu.mutation.AffJoinedAtCleared() {
		_spec.ClearField(user.FieldAffJoinedAt, field.TypeTime)
	}
	if value, ok := uu.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uu.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uu.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if uu.mutation.SessionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedSessionsIDs(); len(nodes) > 0 && !uu.mutation.SessionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.SessionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserMutation
}

// SetFullName sets the "full_name" field.
func (uuo *UserUpdateOne) SetFullName(s string) *UserUpdateOne {
	uuo.mutation.SetFullName(s)
	return uuo
}

// SetNillableFullName sets the "full_name" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableFullName(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetFullName(*s)
	}
	return uuo
}

// ClearFullName clears the value of the "full_name" field.
func (uuo *UserUpdateOne) ClearFullName() *UserUpdateOne {
	uuo.mutation.ClearFullName()
	return uuo
}

// SetEmail sets the "email" field.
func (uuo *UserUpdateOne) SetEmail(s string) *UserUpdateOne {
	uuo.mutation.SetEmail(s)
	return uuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmail(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetEmail(*s)
	}
	return uuo
}

// SetPhone sets the "phone" field.
func (uuo *UserUpdateOne) SetPhone(s string) *UserUpdateOne {
	uuo.mutation.SetPhone(s)
	return uuo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePhone(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPhone(*s)
	}
	return uuo
}

// ClearPhone clears the value of the "phone" field.
func (uuo *UserUpdateOne) ClearPhone() *UserUpdateOne {
	uuo.mutation.ClearPhone()
	return uuo
}

// SetAvatarURL sets the "avatar_url" field.
func (uuo *UserUpdateOne) SetAvatarURL(s string) *UserUpdateOne {
	uuo.mutation.SetAvatarURL(s)
	return uuo
}

// SetNillableAvatarURL sets the "avatar_url" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAvatarURL(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetAvatarURL(*s)
	}
	return uuo
}

// ClearAvatarURL clears the value of the "avatar_url" field.
func (uuo *UserUpdateOne) ClearAvatarURL() *UserUpdateOne {
	uuo.mutation.ClearAvatarURL()
	return uuo
}

// SetBio sets the "bio" field.
func (uuo *UserUpdateOne) SetBio(s string) *UserUpdateOne {
	uuo.mutation.SetBio(s)
	return uuo
}

// SetNillableBio sets the "bio" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableBio(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetBio(*s)
	}
	return uuo
}

// ClearBio clears the value of the "bio" field.
func (uuo *UserUpdateOne) ClearBio() *UserUpdateOne {
	uuo.mutation.ClearBio()
	return uuo
}

// SetCompany sets the "company" field.
func (uuo *UserUpdateOne) SetCompany(s string) *UserUpdateOne {
	uuo.mutation.SetCompany(s)
	return uuo
}

// SetNillableCompany sets the "company" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableCompany(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetCompany(*s)
	}
	return uuo
}

// ClearCompany clears the value of the "company" field.
func (uuo *UserUpdateOne) ClearCompany() *UserUpdateOne {
	uuo.mutation.ClearCompany()
	return uuo
}

// SetIndustry sets the "industry" field.
func (uuo *UserUpdateOne) SetIndustry(s string) *UserUpdateOne {
	uuo.mutation.SetIndustry(s)
	return uuo
}

// SetNillableIndustry sets the "industry" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableIndustry(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetIndustry(*s)
	}
	return uuo
}

// ClearIndustry clears the value of the "industry" field.
func (uuo *UserUpdateOne) ClearIndustry() *UserUpdateOne {
	uuo.mutation.ClearIndustry()
	return uuo
}

// SetRole sets the "role" field.
func (uuo *UserUpdateOne) SetRole(u user.Role) *UserUpdateOne {
	uuo.mutation.SetRole(u)
	return uuo
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableRole(u *user.Role) *UserUpdateOne {
	if u != nil {
		uuo.SetRole(*u)
	}
	return uuo
}

// SetPasswordHash sets the "password_hash" field.
func (uuo *UserUpdateOne) SetPasswordHash(s string) *UserUpdateOne {
	uuo.mutation.SetPasswordHash(s)
	return uuo
}

// SetNillablePasswordHash sets the "password_hash" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePasswordHash(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPasswordHash(*s)
	}
	return uuo
}

// SetIsVerified sets the "is_verified" field.
func (uuo *UserUpdateOne) SetIsVerified(b bool) *UserUpdateOne {
	uuo.mutation.SetIsVerified(b)
	return uuo
}

// SetNillableIsVerified sets the "is_verified" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableIsVerified(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetIsVerified(*b)
	}
	return uuo
}

// SetTwoFactorEnabled sets the "two_factor_enabled" field.
func (uuo *UserUpdateOne) SetTwoFactorEnabled(b bool) *UserUpdateOne {
	uuo.mutation.SetTwoFactorEnabled(b)
	return uuo
}

// SetNillableTwoFactorEnabled sets the "two_factor_enabled" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableTwoFactorEnabled(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetTwoFactorEnabled(*b)
	}
	return uuo
}

// SetTwoFactorSecret sets the "two_factor_secret" field.
func (uuo *UserUpdateOne) SetTwoFactorSecret(s string) *UserUpdateOne {
	uuo.mutation.SetTwoFactorSecret(s)
	return uuo
}

// SetNillableTwoFactorSecret sets the "two_factor_secret" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableTwoFactorSecret(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetTwoFactorSecret(*s)
	}
	return uuo
}

// ClearTwoFactorSecret clears the value of the "two_factor_secret" field.
func (uuo *UserUpdateOne) ClearTwoFactorSecret() *UserUpdateOne {
	uuo.mutation.ClearTwoFactorSecret()
	return uuo
}

// SetTwoFactorBackupCodes sets the "two_factor_backup_codes" field.
func (uuo *UserUpdateOne) SetTwoFactorBackupCodes(s []string) *UserUpdateOne {
	uuo.mutation.SetTwoFactorBackupCodes(s)
	return uuo
}

// AppendTwoFactorBackupCodes appends s to the "two_factor_backup_codes" field.
func (uuo *UserUpdateOne) AppendTwoFactorBackupCodes(s []string) *UserUpdateOne {
	uuo.mutation.AppendTwoFactorBackupCodes(s)
	return uuo
}

// ClearTwoFactorBackupCodes clears the value of the "two_factor_backup_codes" field.
func (uuo *UserUpdateOne) ClearTwoFactorBackupCodes() *UserUpdateOne {
	uuo.mutation.ClearTwoFactorBackupCodes()
	return uuo
}

// SetTwoFactorEnabledAt sets the "two_factor_enabled_at" field.
func (uuo *UserUpdateOne) SetTwoFactorEnabledAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetTwoFactorEnabledAt(t)
	return uuo
}

// SetNillableTwoFactorEnabledAt sets the "two_factor_enabled_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableTwoFactorEnabledAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetTwoFactorEnabledAt(*t)
	}
	return uuo
}

// ClearTwoFactorEnabledAt clears the value of the "two_factor_enabled_at" field.
func (uuo *UserUpdateOne) ClearTwoFactorEnabledAt() *UserUpdateOne {
	uuo.mutation.ClearTwoFactorEnabledAt()
	return uuo
}

// SetNotificationSettings sets the "notification_settings" field.
func (uuo *UserUpdateOne) SetNotificationSettings(m map[string]interface{}) *UserUpdateOne {
	uuo.mutation.SetNotificationSettings(m)
	return uuo
}

// ClearNotificationSettings clears the value of the "notification_settings" field.
func (uuo *UserUpdateOne) ClearNotificationSettings() *UserUpdateOne {
	uuo.mutation.ClearNotificationSettings()
	return uuo
}

// SetInviteCode sets the "invite_code" field.
func (uuo *UserUpdateOne) SetInviteCode(s string) *UserUpdateOne {
	uuo.mutation.SetInviteCode(s)
	return uuo
}

// SetNillableInviteCode sets the "invite_code" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableInviteCode(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetInviteCode(*s)
	}
	return uuo
}

// ClearInviteCode clears the value of the "invite_code" field.
func (uuo *UserUpdateOne) ClearInviteCode() *UserUpdateOne {
	uuo.mutation.ClearInviteCode()
	return uuo
}

// SetAffCode sets the "aff_code" field.
func (uuo *UserUpdateOne) SetAffCode(s string) *UserUpdateOne {
	uuo.mutation.SetAffCode(s)
	return uuo
}

// SetNillableAffCode sets the "aff_code" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAffCode(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetAffCode(*s)
	}
	return uuo
}

// ClearAffCode clears the value of the "aff_code" field.
func (uuo *UserUpdateOne) ClearAffCode() *UserUpdateOne {
	uuo.mutation.ClearAffCode()
	return uuo
}

// SetAffJoinedAt sets the "aff_joined_at" field.
func (uuo *UserUpdateOne) SetAffJoinedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetAffJoinedAt(t)
	return uuo
}

// SetNillableAffJoinedAt sets the "aff_joined_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAffJoinedAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetAffJoinedAt(*t)
	}
	return uuo
}

// ClearAffJoinedAt clears the value of the "aff_joined_at" field.
func (uuo *UserUpdateOne) ClearAffJoinedAt() *UserUpdateOne {
	uuo.mutation.ClearAffJoinedAt()
	return uuo
}

// SetUpdatedAt sets the "updated_at" field.
func (uuo *UserUpdateOne) SetUpdatedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetUpdatedAt(t)
	return uuo
}

// SetDeletedAt sets the "deleted_at" field.
func (uuo *UserUpdateOne) SetDeletedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetDeletedAt(t)
	return uuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDeletedAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetDeletedAt(*t)
	}
	return uuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uuo *UserUpdateOne) ClearDeletedAt() *UserUpdateOne {
	uuo.mutation.ClearDeletedAt()
	return uuo
}

// AddSessionIDs adds the "sessions" edge to the Session entity by IDs.
func (uuo *UserUpdateOne) AddSessionIDs(ids ...uuid.UUID) *UserUpdateOne {
	uuo.mutation.AddSessionIDs(ids...)
	return uuo
}

// AddSessions adds the "sessions" edges to the Session entity.
func (uuo *UserUpdateOne) AddSessions(s ...*Session) *UserUpdateOne {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uuo.AddSessionIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uuo *UserUpdateOne) Mutation() *UserMutation {
	return uuo.mutation
}

// ClearSessions clears all "sessions" edges to the Session entity.
func (uuo *UserUpdateOne) ClearSessions() *UserUpdateOne {
	uuo.mutation.ClearSessions()
	return uuo
}

// RemoveSessionIDs removes the "sessions" edge to Session entities by IDs.
func (uuo *UserUpdateOne) RemoveSessionIDs(ids ...uuid.UUID) *UserUpdateOne {
	uuo.mutation.RemoveSessionIDs(ids...)
	return uuo
}

// RemoveSessions removes "sessions" edges to Session entities.
func (uuo *UserUpdateOne) RemoveSessions(s ...*Session) *UserUpdateOne {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uuo.RemoveSessionIDs(ids...)
}

// Where appends a list predicates to the UserUpdate builder.
func (uuo *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated User entity.
func (uuo *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	uuo.defaults()
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UserUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UserUpdateOne) defaults() {
	if _, ok := uuo.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		uuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UserUpdateOne) check() error {
	if v, ok := uuo.mutation.FullName(); ok {
		if err := user.FullNameValidator(v); err != nil {
			return &ValidationError{Name: "full_name", err: fmt.Errorf(`ent: validator failed for field "User.full_name": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Phone(); ok {
		if err := user.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "User.phone": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Company(); ok {
		if err := user.CompanyValidator(v); err != nil {
			return &ValidationError{Name: "company", err: fmt.Errorf(`ent: validator failed for field "User.company": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Industry(); ok {
		if err := user.IndustryValidator(v); err != nil {
			return &ValidationError{Name: "industry", err: fmt.Errorf(`ent: validator failed for field "User.industry": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.Role(); ok {
		if err := user.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "User.role": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.InviteCode(); ok {
		if err := user.InviteCodeValidator(v); err != nil {
			return &ValidationError{Name: "invite_code", err: fmt.Errorf(`ent: validator failed for field "User.invite_code": %w`, err)}
		}
	}
	if v, ok := uuo.mutation.AffCode(); ok {
		if err := user.AffCodeValidator(v); err != nil {
			return &ValidationError{Name: "aff_code", err: fmt.Errorf(`ent: validator failed for field "User.aff_code": %w`, err)}
		}
	}
	return nil
}

func (uuo *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.FullName(); ok {
		_spec.SetField(user.FieldFullName, field.TypeString, value)
	}
	if uuo.mutation.FullNameCleared() {
		_spec.ClearField(user.FieldFullName, field.TypeString)
	}
	if value, ok := uuo.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
	}
	if uuo.mutation.PhoneCleared() {
		_spec.ClearField(user.FieldPhone, field.TypeString)
	}
	if value, ok := uuo.mutation.AvatarURL(); ok {
		_spec.SetField(user.FieldAvatarURL, field.TypeString, value)
	}
	if uuo.mutation.AvatarURLCleared() {
		_spec.ClearField(user.FieldAvatarURL, field.TypeString)
	}
	if value, ok := uuo.mutation.Bio(); ok {
		_spec.SetField(user.FieldBio, field.TypeString, value)
	}
	if uuo.mutation.BioCleared() {
		_spec.ClearField(user.FieldBio, field.TypeString)
	}
	if value, ok := uuo.mutation.Company(); ok {
		_spec.SetField(user.FieldCompany, field.TypeString, value)
	}
	if uuo.mutation.CompanyCleared() {
		_spec.ClearField(user.FieldCompany, field.TypeString)
	}
	if value, ok := uuo.mutation.Industry(); ok {
		_spec.SetField(user.FieldIndustry, field.TypeString, value)
	}
	if uuo.mutation.IndustryCleared() {
		_spec.ClearField(user.FieldIndustry, field.TypeString)
	}
	if value, ok := uuo.mutation.Role(); ok {
		_spec.SetField(user.FieldRole, field.TypeEnum, value)
	}
	if value, ok := uuo.mutation.PasswordHash(); ok {
		_spec.SetField(user.FieldPasswordHash, field.TypeString, value)
	}
	if value, ok := uuo.mutation.IsVerified(); ok {
		_spec.SetField(user.FieldIsVerified, field.TypeBool, value)
	}
	if value, ok := uuo.mutation.TwoFactorEnabled(); ok {
		_spec.SetField(user.FieldTwoFactorEnabled, field.TypeBool, value)
	}
	if value, ok := uuo.mutation.TwoFactorSecret(); ok {
		_spec.SetField(user.FieldTwoFactorSecret, field.TypeString, value)
	}
	if uuo.mutation.TwoFactorSecretCleared() {
		_spec.ClearField(user.FieldTwoFactorSecret, field.TypeString)
	}
	if value, ok := uuo.mutation.TwoFactorBackupCodes(); ok {
		_spec.SetField(user.FieldTwoFactorBackupCodes, field.TypeJSON, value)
	}
	if value, ok := uuo.mutation.AppendedTwoFactorBackupCodes(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, user.FieldTwoFactorBackupCodes, value)
		})
	}
	if uuo.mutation.TwoFactorBackupCodesCleared() {
		_spec.ClearField(user.FieldTwoFactorBackupCodes, field.TypeJSON)
	}
	if value, ok := uuo.mutation.TwoFactorEnabledAt(); ok {
		_spec.SetField(user.FieldTwoFactorEnabledAt, field.TypeTime, value)
	}
	if uuo.mutation.TwoFactorEnabledAtCleared() {
		_spec.ClearField(user.FieldTwoFactorEnabledAt, field.TypeTime)
	}
	if value, ok := uuo.mutation.NotificationSettings(); ok {
		_spec.SetField(user.FieldNotificationSettings, field.TypeJSON, value)
	}
	if uuo.mutation.NotificationSettingsCleared() {
		_spec.ClearField(user.FieldNotificationSettings, field.TypeJSON)
	}
	if value, ok := uuo.mutation.InviteCode(); ok {
		_spec.SetField(user.FieldInviteCode, field.TypeString, value)
	}
	if uuo.mutation.InviteCodeCleared() {
		_spec.ClearField(user.FieldInviteCode, field.TypeString)
	}
	if value, ok := uuo.mutation.AffCode(); ok {
		_spec.SetField(user.FieldAffCode, field.TypeString, value)
	}
	if uuo.mutation.AffCodeCleared() {
		_spec.ClearField(user.FieldAffCode, field.TypeString)
	}
	if value, ok := uuo.mutation.AffJoinedAt(); ok {
		_spec.SetField(user.FieldAffJoinedAt, field.TypeTime, value)
	}
	if uuo.mutation.AffJoinedAtCleared() {
		_spec.ClearField(user.FieldAffJoinedAt, field.TypeTime)
	}
	if value, ok := uuo.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uuo.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uuo.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if uuo.mutation.SessionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedSessionsIDs(); len(nodes) > 0 && !uuo.mutation.SessionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.SessionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &User{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}

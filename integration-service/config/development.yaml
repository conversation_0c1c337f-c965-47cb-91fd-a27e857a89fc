# Development Environment Configuration

# Override server settings for development
server:
  env: "development"
  grpc_port: 50055
  http_port: 8085

# Development database settings
database:
  # Use SQLite for development for easier setup
  type: "sqlite"
  sqlite_path: "./data/integration_service_dev.db"

  # PostgreSQL fallback configuration
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "integration_service_dev"
  ssl_mode: "disable"

# Development OAuth settings (use test credentials)
oauth:
  facebook:
    client_id: "dev-facebook-client-id"
    client_secret: "dev-facebook-client-secret"
    redirect_url: "http://localhost:8085/oauth/facebook/callback"
  
  twitter:
    client_id: "dev-twitter-client-id"
    client_secret: "dev-twitter-client-secret"
    redirect_url: "http://localhost:8085/oauth/twitter/callback"
  
  instagram:
    client_id: "dev-instagram-client-id"
    client_secret: "dev-instagram-client-secret"
    redirect_url: "http://localhost:8085/oauth/instagram/callback"
  
  linkedin:
    client_id: "dev-linkedin-client-id"
    client_secret: "dev-linkedin-client-secret"
    redirect_url: "http://localhost:8085/oauth/linkedin/callback"
  
  youtube:
    client_id: "dev-youtube-client-id"
    client_secret: "dev-youtube-client-secret"
    redirect_url: "http://localhost:8085/oauth/youtube/callback"
  
  tiktok:
    client_id: "dev-tiktok-client-id"
    client_secret: "dev-tiktok-client-secret"
    redirect_url: "http://localhost:8085/oauth/tiktok/callback"

# Development JWT settings (less secure for easier testing)
jwt:
  secret_key: "dev-integration-secret-key-not-for-production"
  token_duration: "24h"
  refresh_duration: "168h"

# Development logging (more verbose)
logging:
  level: "debug"
  format: "text"
  output: "stdout"

# Development features (enable all for testing)
features:
  scheduled_posts: true
  analytics: true
  webhooks: true
  bulk_operations: true

# Development rate limiting (more permissive)
rate_limit:
  enabled: false
  requests_per_minute: 1000
  burst: 100

# Development cleanup (less aggressive)
cleanup:
  enabled: false
  interval: "24h"
  retain_failed_posts_days: 7
  retain_success_posts_days: 30

# Development monitoring
monitoring:
  enabled: true
  metrics_port: "9095"
  health_endpoint: "/health"

# Development services (local)
services:
  user_service: "localhost:50051"
  asset_service: "localhost:50052"
  ai_content_service: "localhost:50053"
  credit_billing_service: "localhost:50054"
  notification_service: "localhost:50056"

# Development Kafka (local)
kafka:
  brokers:
    - "localhost:9092"
  consumer_group: "integration-service-dev"
  topics:
    notification_events: "notification.events.dev"

# Development tracing (higher sample rate)
tracing:
  enabled: true
  service_name: "integration-service-dev"
  jaeger_endpoint: "http://localhost:14268/api/traces"
  sample_rate: 1.0

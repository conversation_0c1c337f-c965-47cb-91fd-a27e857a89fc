version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    container_name: socialai-postgres
    environment:
      POSTGRES_DB: socialai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: socialai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: socialai-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: socialai-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - socialai-network

  minio:
    image: minio/minio:latest
    container_name: socialai-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: socialai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - socialai-network

  grafana:
    image: grafana/grafana:latest
    container_name: socialai-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - socialai-network

  # Application Services
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: socialai-api-gateway
    ports:
      - "8080:8080"
    environment:
      - SERVER_HTTP_PORT=8080
      - SERVER_ENV=development
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=socialai
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - KAFKA_BROKERS=kafka:9092
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=50051
      - AI_CONTENT_SERVICE_HOST=ai-content-service
      - AI_CONTENT_SERVICE_PORT=50052
      - CONTENT_MGMT_SERVICE_HOST=content-mgmt-service
      - CONTENT_MGMT_SERVICE_PORT=50053
      - CREDIT_SERVICE_HOST=credit-service
      - CREDIT_SERVICE_PORT=50054
    depends_on:
      - postgres
      - redis
      - kafka
      - user-service
      - ai-content-service
      - content-mgmt-service
      - credit-service
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    container_name: socialai-user-service
    ports:
      - "50051:50051"
    environment:
      - SERVER_GRPC_PORT=50051
      - SERVER_ENV=development
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=socialai
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET_KEY=your-secret-key
    depends_on:
      - postgres
      - redis
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50051"]
      interval: 30s
      timeout: 10s
      retries: 3

  ai-content-service:
    build:
      context: ./ai-content-service
      dockerfile: Dockerfile
    container_name: socialai-ai-content-service
    ports:
      - "50052:50052"
    environment:
      - SERVER_GRPC_PORT=50052
      - SERVER_ENV=development
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=socialai
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      - postgres
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50052"]
      interval: 30s
      timeout: 10s
      retries: 3

  content-mgmt-service:
    build:
      context: ./content-mgmt-service
      dockerfile: Dockerfile
    container_name: socialai-content-mgmt-service
    ports:
      - "50053:50053"
    environment:
      - SERVER_GRPC_PORT=50053
      - SERVER_ENV=development
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=socialai
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - postgres
      - kafka
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50053"]
      interval: 30s
      timeout: 10s
      retries: 3

  credit-service:
    build:
      context: ./credit-service
      dockerfile: Dockerfile
    container_name: socialai-credit-service
    ports:
      - "50054:50054"
    environment:
      - SERVER_GRPC_PORT=50054
      - SERVER_ENV=development
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_NAME=socialai
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    depends_on:
      - postgres
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50054"]
      interval: 30s
      timeout: 10s
      retries: 3

  asset-service:
    build:
      context: ./asset-service
      dockerfile: Dockerfile
    container_name: socialai-asset-service
    ports:
      - "50054:50054"
      - "8084:8084"
    environment:
      - GRPC_PORT=50054
      - HTTP_PORT=8084
      - DATABASE_URL=******************************************/asset_db?sslmode=disable
      - STORAGE_ENDPOINT=minio:9000
      - STORAGE_ACCESS_KEY=minioadmin
      - STORAGE_SECRET_KEY=minioadmin
      - STORAGE_BUCKET=social-content-ai-assets
      - USER_SERVICE_URL=user-service:50050
    depends_on:
      - postgres
      - minio
      - user-service
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  integration-service:
    build:
      context: ./integration-service
      dockerfile: Dockerfile
    container_name: socialai-integration-service
    ports:
      - "50055:50055"
      - "8085:8085"
    environment:
      - GRPC_PORT=50055
      - HTTP_PORT=8085
      - DATABASE_URL=******************************************/integration_db?sslmode=disable
      - USER_SERVICE_URL=user-service:50050
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
    depends_on:
      - postgres
      - user-service
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: socialai-notification-service
    ports:
      - "50056:50056"
      - "8086:8086"
    environment:
      - GRPC_PORT=50056
      - HTTP_PORT=8086
      - DATABASE_URL=******************************************/notification_db?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - USER_SERVICE_URL=user-service:50050
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    depends_on:
      - postgres
      - redis
      - user-service
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  analytics-service:
    build:
      context: ./analytics-service
      dockerfile: Dockerfile
    container_name: socialai-analytics-service
    ports:
      - "50057:50057"
      - "8087:8087"
    environment:
      - GRPC_PORT=50057
      - HTTP_PORT=8087
      - DATABASE_URL=******************************************/analytics_db?sslmode=disable
      - USER_SERVICE_URL=user-service:50050
    depends_on:
      - postgres
      - user-service
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RAG Processing Service (Python)
  rag-processing-service:
    build:
      context: ./rag-processing-service
      dockerfile: Dockerfile
    container_name: socialai-rag-processing
    ports:
      - "8088:8088"
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
    depends_on:
      - neo4j
      - qdrant
    networks:
      - socialai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Neo4j Graph Database (for RAG)
  neo4j:
    image: neo4j:5
    container_name: socialai-neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
    volumes:
      - neo4j_data:/data
    networks:
      - socialai-network

  # Qdrant Vector Database (for RAG)
  qdrant:
    image: qdrant/qdrant:latest
    container_name: socialai-qdrant
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - socialai-network





volumes:
  postgres_data:
  redis_data:
  minio_data:
  prometheus_data:
  grafana_data:
  neo4j_data:
  qdrant_data:

networks:
  socialai-network:
    driver: bridge

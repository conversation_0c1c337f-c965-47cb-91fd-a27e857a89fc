// Code generated by ent, DO NOT EDIT.

package post

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the post type in the database.
	Label = "post"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldWorkspaceID holds the string denoting the workspace_id field in the database.
	FieldWorkspaceID = "workspace_id"
	// FieldTemplateID holds the string denoting the template_id field in the database.
	FieldTemplateID = "template_id"
	// FieldGenerationID holds the string denoting the generation_id field in the database.
	FieldGenerationID = "generation_id"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldOriginalContent holds the string denoting the original_content field in the database.
	FieldOriginalContent = "original_content"
	// FieldPlatforms holds the string denoting the platforms field in the database.
	FieldPlatforms = "platforms"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldScheduledAt holds the string denoting the scheduled_at field in the database.
	FieldScheduledAt = "scheduled_at"
	// FieldPublishedAt holds the string denoting the published_at field in the database.
	FieldPublishedAt = "published_at"
	// FieldPlatformPosts holds the string denoting the platform_posts field in the database.
	FieldPlatformPosts = "platform_posts"
	// FieldHashtags holds the string denoting the hashtags field in the database.
	FieldHashtags = "hashtags"
	// FieldMentions holds the string denoting the mentions field in the database.
	FieldMentions = "mentions"
	// FieldMediaUrls holds the string denoting the media_urls field in the database.
	FieldMediaUrls = "media_urls"
	// FieldVariables holds the string denoting the variables field in the database.
	FieldVariables = "variables"
	// FieldVisibility holds the string denoting the visibility field in the database.
	FieldVisibility = "visibility"
	// FieldLanguage holds the string denoting the language field in the database.
	FieldLanguage = "language"
	// FieldAnalytics holds the string denoting the analytics field in the database.
	FieldAnalytics = "analytics"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the post in the database.
	Table = "posts"
)

// Columns holds all SQL columns for post fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldWorkspaceID,
	FieldTemplateID,
	FieldGenerationID,
	FieldTitle,
	FieldContent,
	FieldOriginalContent,
	FieldPlatforms,
	FieldStatus,
	FieldScheduledAt,
	FieldPublishedAt,
	FieldPlatformPosts,
	FieldHashtags,
	FieldMentions,
	FieldMediaUrls,
	FieldVariables,
	FieldVisibility,
	FieldLanguage,
	FieldAnalytics,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	WorkspaceIDValidator func(string) error
	// TemplateIDValidator is a validator for the "template_id" field. It is called by the builders before save.
	TemplateIDValidator func(string) error
	// GenerationIDValidator is a validator for the "generation_id" field. It is called by the builders before save.
	GenerationIDValidator func(string) error
	// TitleValidator is a validator for the "title" field. It is called by the builders before save.
	TitleValidator func(string) error
	// DefaultLanguage holds the default value on creation for the "language" field.
	DefaultLanguage string
	// LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	LanguageValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusDraft is the default value of the Status enum.
const DefaultStatus = StatusDraft

// Status values.
const (
	StatusDraft     Status = "draft"
	StatusScheduled Status = "scheduled"
	StatusPublished Status = "published"
	StatusFailed    Status = "failed"
	StatusArchived  Status = "archived"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusDraft, StatusScheduled, StatusPublished, StatusFailed, StatusArchived:
		return nil
	default:
		return fmt.Errorf("post: invalid enum value for status field: %q", s)
	}
}

// Visibility defines the type for the "visibility" enum field.
type Visibility string

// VisibilityPublic is the default value of the Visibility enum.
const DefaultVisibility = VisibilityPublic

// Visibility values.
const (
	VisibilityPublic   Visibility = "public"
	VisibilityPrivate  Visibility = "private"
	VisibilityUnlisted Visibility = "unlisted"
)

func (v Visibility) String() string {
	return string(v)
}

// VisibilityValidator is a validator for the "visibility" field enum values. It is called by the builders before save.
func VisibilityValidator(v Visibility) error {
	switch v {
	case VisibilityPublic, VisibilityPrivate, VisibilityUnlisted:
		return nil
	default:
		return fmt.Errorf("post: invalid enum value for visibility field: %q", v)
	}
}

// OrderOption defines the ordering options for the Post queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByWorkspaceID orders the results by the workspace_id field.
func ByWorkspaceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkspaceID, opts...).ToFunc()
}

// ByTemplateID orders the results by the template_id field.
func ByTemplateID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTemplateID, opts...).ToFunc()
}

// ByGenerationID orders the results by the generation_id field.
func ByGenerationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGenerationID, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByOriginalContent orders the results by the original_content field.
func ByOriginalContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOriginalContent, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByScheduledAt orders the results by the scheduled_at field.
func ByScheduledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldScheduledAt, opts...).ToFunc()
}

// ByPublishedAt orders the results by the published_at field.
func ByPublishedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPublishedAt, opts...).ToFunc()
}

// ByVisibility orders the results by the visibility field.
func ByVisibility(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVisibility, opts...).ToFunc()
}

// ByLanguage orders the results by the language field.
func ByLanguage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLanguage, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

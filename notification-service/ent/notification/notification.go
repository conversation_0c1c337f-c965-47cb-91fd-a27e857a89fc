// Code generated by ent, DO NOT EDIT.

package notification

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the notification type in the database.
	Label = "notification"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldMessage holds the string denoting the message field in the database.
	FieldMessage = "message"
	// FieldData holds the string denoting the data field in the database.
	FieldData = "data"
	// FieldPriority holds the string denoting the priority field in the database.
	FieldPriority = "priority"
	// FieldChannels holds the string denoting the channels field in the database.
	FieldChannels = "channels"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldReferenceID holds the string denoting the reference_id field in the database.
	FieldReferenceID = "reference_id"
	// FieldReferenceType holds the string denoting the reference_type field in the database.
	FieldReferenceType = "reference_type"
	// FieldDeliveryStatus holds the string denoting the delivery_status field in the database.
	FieldDeliveryStatus = "delivery_status"
	// FieldScheduledAt holds the string denoting the scheduled_at field in the database.
	FieldScheduledAt = "scheduled_at"
	// FieldSentAt holds the string denoting the sent_at field in the database.
	FieldSentAt = "sent_at"
	// FieldDeliveredAt holds the string denoting the delivered_at field in the database.
	FieldDeliveredAt = "delivered_at"
	// FieldReadAt holds the string denoting the read_at field in the database.
	FieldReadAt = "read_at"
	// FieldExpiresAt holds the string denoting the expires_at field in the database.
	FieldExpiresAt = "expires_at"
	// FieldActionURL holds the string denoting the action_url field in the database.
	FieldActionURL = "action_url"
	// FieldActionData holds the string denoting the action_data field in the database.
	FieldActionData = "action_data"
	// FieldRetryCount holds the string denoting the retry_count field in the database.
	FieldRetryCount = "retry_count"
	// FieldNextRetryAt holds the string denoting the next_retry_at field in the database.
	FieldNextRetryAt = "next_retry_at"
	// FieldErrorMessage holds the string denoting the error_message field in the database.
	FieldErrorMessage = "error_message"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the notification in the database.
	Table = "notifications"
)

// Columns holds all SQL columns for notification fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldType,
	FieldTitle,
	FieldMessage,
	FieldData,
	FieldPriority,
	FieldChannels,
	FieldStatus,
	FieldReferenceID,
	FieldReferenceType,
	FieldDeliveryStatus,
	FieldScheduledAt,
	FieldSentAt,
	FieldDeliveredAt,
	FieldReadAt,
	FieldExpiresAt,
	FieldActionURL,
	FieldActionData,
	FieldRetryCount,
	FieldNextRetryAt,
	FieldErrorMessage,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// TitleValidator is a validator for the "title" field. It is called by the builders before save.
	TitleValidator func(string) error
	// ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	ReferenceIDValidator func(string) error
	// ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	ReferenceTypeValidator func(string) error
	// ActionURLValidator is a validator for the "action_url" field. It is called by the builders before save.
	ActionURLValidator func(string) error
	// DefaultRetryCount holds the default value on creation for the "retry_count" field.
	DefaultRetryCount int
	// ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	ErrorMessageValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Type defines the type for the "type" enum field.
type Type string

// Type values.
const (
	TypeContentGenerated     Type = "content_generated"
	TypeContentImproved      Type = "content_improved"
	TypePostPublished        Type = "post_published"
	TypePostScheduled        Type = "post_scheduled"
	TypePostFailed           Type = "post_failed"
	TypeCreditLow            Type = "credit_low"
	TypeCreditPurchased      Type = "credit_purchased"
	TypeSubscriptionRenewed  Type = "subscription_renewed"
	TypeSubscriptionExpired  Type = "subscription_expired"
	TypeIntegrationConnected Type = "integration_connected"
	TypeIntegrationFailed    Type = "integration_failed"
	TypeAnalyticsReport      Type = "analytics_report"
	TypeSystemMaintenance    Type = "system_maintenance"
	TypeWelcome              Type = "welcome"
	TypePasswordReset        Type = "password_reset"
	TypeEmailVerification    Type = "email_verification"
	TypeSecurityAlert        Type = "security_alert"
)

func (_type Type) String() string {
	return string(_type)
}

// TypeValidator is a validator for the "type" field enum values. It is called by the builders before save.
func TypeValidator(_type Type) error {
	switch _type {
	case TypeContentGenerated, TypeContentImproved, TypePostPublished, TypePostScheduled, TypePostFailed, TypeCreditLow, TypeCreditPurchased, TypeSubscriptionRenewed, TypeSubscriptionExpired, TypeIntegrationConnected, TypeIntegrationFailed, TypeAnalyticsReport, TypeSystemMaintenance, TypeWelcome, TypePasswordReset, TypeEmailVerification, TypeSecurityAlert:
		return nil
	default:
		return fmt.Errorf("notification: invalid enum value for type field: %q", _type)
	}
}

// Priority defines the type for the "priority" enum field.
type Priority string

// PriorityNormal is the default value of the Priority enum.
const DefaultPriority = PriorityNormal

// Priority values.
const (
	PriorityLow    Priority = "low"
	PriorityNormal Priority = "normal"
	PriorityHigh   Priority = "high"
	PriorityUrgent Priority = "urgent"
)

func (pr Priority) String() string {
	return string(pr)
}

// PriorityValidator is a validator for the "priority" field enum values. It is called by the builders before save.
func PriorityValidator(pr Priority) error {
	switch pr {
	case PriorityLow, PriorityNormal, PriorityHigh, PriorityUrgent:
		return nil
	default:
		return fmt.Errorf("notification: invalid enum value for priority field: %q", pr)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending   Status = "pending"
	StatusSent      Status = "sent"
	StatusDelivered Status = "delivered"
	StatusFailed    Status = "failed"
	StatusRead      Status = "read"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusSent, StatusDelivered, StatusFailed, StatusRead:
		return nil
	default:
		return fmt.Errorf("notification: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Notification queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// ByMessage orders the results by the message field.
func ByMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMessage, opts...).ToFunc()
}

// ByPriority orders the results by the priority field.
func ByPriority(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPriority, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByReferenceID orders the results by the reference_id field.
func ByReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceID, opts...).ToFunc()
}

// ByReferenceType orders the results by the reference_type field.
func ByReferenceType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceType, opts...).ToFunc()
}

// ByScheduledAt orders the results by the scheduled_at field.
func ByScheduledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldScheduledAt, opts...).ToFunc()
}

// BySentAt orders the results by the sent_at field.
func BySentAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSentAt, opts...).ToFunc()
}

// ByDeliveredAt orders the results by the delivered_at field.
func ByDeliveredAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeliveredAt, opts...).ToFunc()
}

// ByReadAt orders the results by the read_at field.
func ByReadAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReadAt, opts...).ToFunc()
}

// ByExpiresAt orders the results by the expires_at field.
func ByExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresAt, opts...).ToFunc()
}

// ByActionURL orders the results by the action_url field.
func ByActionURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActionURL, opts...).ToFunc()
}

// ByRetryCount orders the results by the retry_count field.
func ByRetryCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRetryCount, opts...).ToFunc()
}

// ByNextRetryAt orders the results by the next_retry_at field.
func ByNextRetryAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNextRetryAt, opts...).ToFunc()
}

// ByErrorMessage orders the results by the error_message field.
func ByErrorMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorMessage, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

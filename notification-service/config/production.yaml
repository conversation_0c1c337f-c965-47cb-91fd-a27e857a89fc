# Notification Service Production Configuration

server:
  grpc_port: 50056
  http_port: 8086
  env: production

database:
  type: postgres
  host: ${DATABASE_HOST}
  port: ${DATABASE_PORT}
  name: ${DATABASE_NAME}
  user: ${DATABASE_USER}
  password: ${DATABASE_PASSWORD}
  ssl_mode: require

email:
  smtp_host: ${SMTP_HOST}
  smtp_port: ${SMTP_PORT}
  username: ${SMTP_USERNAME}
  password: ${SMTP_PASSWORD}
  from_email: ${SMTP_FROM_EMAIL}
  from_name: ${SMTP_FROM_NAME}
  use_tls: true
  rate_limit: 1000
  retry_attempts: 5

sms:
  provider: ${SMS_PROVIDER}
  api_key: ${SMS_API_KEY}
  api_secret: ${SMS_API_SECRET}
  from_number: ${SMS_FROM_NUMBER}
  rate_limit: 500
  retry_attempts: 5

push:
  provider: ${PUSH_PROVIDER}
  api_key: ${PUSH_API_KEY}
  rate_limit: 2000
  retry_attempts: 5

websocket:
  enabled: true
  max_connections: 10000
  ping_interval: 30s
  write_timeout: 10s
  read_timeout: 60s

kafka:
  brokers:
    - ${KAFKA_BROKER_1}
    - ${KAFKA_BROKER_2}
    - ${KAFKA_BROKER_3}
  group_id: notification-service-prod
  topics:
    notification_events: notification.events
    user_events: user.events
    content_events: content.events
    billing_events: billing.events
    data_events: data.events
    platform_events: platform.events
    asset_events: asset.events
    dead_letter_queue: notification.dlq

templates:
  default_language: en
  supported_languages:
    - en
    - vi
    - zh
    - ja
    - ko
  templates_path: /app/templates
  cache_enabled: true
  cache_ttl: 24h

rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 20

features:
  email_notifications: true
  sms_notifications: true
  push_notifications: true
  websocket_notifications: true
  template_system: true
  user_preferences: true
  analytics: true
  admin_panel: true

monitoring:
  enabled: true
  metrics_port: 9096
  health_check_path: /health
  metrics_path: /metrics

logging:
  level: info
  format: json
  output: stdout

jwt:
  secret_key: ${JWT_SECRET_KEY}
  token_duration: 24h
  issuer: social-content-ai-notification-prod

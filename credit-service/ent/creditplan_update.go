// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/creditplan"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditPlanUpdate is the builder for updating CreditPlan entities.
type CreditPlanUpdate struct {
	config
	hooks    []Hook
	mutation *CreditPlanMutation
}

// Where appends a list predicates to the CreditPlanUpdate builder.
func (cpu *CreditPlanUpdate) Where(ps ...predicate.CreditPlan) *CreditPlanUpdate {
	cpu.mutation.Where(ps...)
	return cpu
}

// SetName sets the "name" field.
func (cpu *CreditPlanUpdate) SetName(s string) *CreditPlanUpdate {
	cpu.mutation.SetName(s)
	return cpu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableName(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetName(*s)
	}
	return cpu
}

// SetDisplayName sets the "display_name" field.
func (cpu *CreditPlanUpdate) SetDisplayName(s string) *CreditPlanUpdate {
	cpu.mutation.SetDisplayName(s)
	return cpu
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableDisplayName(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetDisplayName(*s)
	}
	return cpu
}

// SetDescription sets the "description" field.
func (cpu *CreditPlanUpdate) SetDescription(s string) *CreditPlanUpdate {
	cpu.mutation.SetDescription(s)
	return cpu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableDescription(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetDescription(*s)
	}
	return cpu
}

// ClearDescription clears the value of the "description" field.
func (cpu *CreditPlanUpdate) ClearDescription() *CreditPlanUpdate {
	cpu.mutation.ClearDescription()
	return cpu
}

// SetCredits sets the "credits" field.
func (cpu *CreditPlanUpdate) SetCredits(i int) *CreditPlanUpdate {
	cpu.mutation.ResetCredits()
	cpu.mutation.SetCredits(i)
	return cpu
}

// SetNillableCredits sets the "credits" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableCredits(i *int) *CreditPlanUpdate {
	if i != nil {
		cpu.SetCredits(*i)
	}
	return cpu
}

// AddCredits adds i to the "credits" field.
func (cpu *CreditPlanUpdate) AddCredits(i int) *CreditPlanUpdate {
	cpu.mutation.AddCredits(i)
	return cpu
}

// SetPrice sets the "price" field.
func (cpu *CreditPlanUpdate) SetPrice(i int64) *CreditPlanUpdate {
	cpu.mutation.ResetPrice()
	cpu.mutation.SetPrice(i)
	return cpu
}

// SetNillablePrice sets the "price" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillablePrice(i *int64) *CreditPlanUpdate {
	if i != nil {
		cpu.SetPrice(*i)
	}
	return cpu
}

// AddPrice adds i to the "price" field.
func (cpu *CreditPlanUpdate) AddPrice(i int64) *CreditPlanUpdate {
	cpu.mutation.AddPrice(i)
	return cpu
}

// SetCurrency sets the "currency" field.
func (cpu *CreditPlanUpdate) SetCurrency(s string) *CreditPlanUpdate {
	cpu.mutation.SetCurrency(s)
	return cpu
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableCurrency(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetCurrency(*s)
	}
	return cpu
}

// SetDiscountPercentage sets the "discount_percentage" field.
func (cpu *CreditPlanUpdate) SetDiscountPercentage(i int) *CreditPlanUpdate {
	cpu.mutation.ResetDiscountPercentage()
	cpu.mutation.SetDiscountPercentage(i)
	return cpu
}

// SetNillableDiscountPercentage sets the "discount_percentage" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableDiscountPercentage(i *int) *CreditPlanUpdate {
	if i != nil {
		cpu.SetDiscountPercentage(*i)
	}
	return cpu
}

// AddDiscountPercentage adds i to the "discount_percentage" field.
func (cpu *CreditPlanUpdate) AddDiscountPercentage(i int) *CreditPlanUpdate {
	cpu.mutation.AddDiscountPercentage(i)
	return cpu
}

// SetIsPopular sets the "is_popular" field.
func (cpu *CreditPlanUpdate) SetIsPopular(b bool) *CreditPlanUpdate {
	cpu.mutation.SetIsPopular(b)
	return cpu
}

// SetNillableIsPopular sets the "is_popular" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableIsPopular(b *bool) *CreditPlanUpdate {
	if b != nil {
		cpu.SetIsPopular(*b)
	}
	return cpu
}

// SetIsActive sets the "is_active" field.
func (cpu *CreditPlanUpdate) SetIsActive(b bool) *CreditPlanUpdate {
	cpu.mutation.SetIsActive(b)
	return cpu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableIsActive(b *bool) *CreditPlanUpdate {
	if b != nil {
		cpu.SetIsActive(*b)
	}
	return cpu
}

// SetFeatures sets the "features" field.
func (cpu *CreditPlanUpdate) SetFeatures(s []string) *CreditPlanUpdate {
	cpu.mutation.SetFeatures(s)
	return cpu
}

// AppendFeatures appends s to the "features" field.
func (cpu *CreditPlanUpdate) AppendFeatures(s []string) *CreditPlanUpdate {
	cpu.mutation.AppendFeatures(s)
	return cpu
}

// ClearFeatures clears the value of the "features" field.
func (cpu *CreditPlanUpdate) ClearFeatures() *CreditPlanUpdate {
	cpu.mutation.ClearFeatures()
	return cpu
}

// SetValidUntil sets the "valid_until" field.
func (cpu *CreditPlanUpdate) SetValidUntil(t time.Time) *CreditPlanUpdate {
	cpu.mutation.SetValidUntil(t)
	return cpu
}

// SetNillableValidUntil sets the "valid_until" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableValidUntil(t *time.Time) *CreditPlanUpdate {
	if t != nil {
		cpu.SetValidUntil(*t)
	}
	return cpu
}

// ClearValidUntil clears the value of the "valid_until" field.
func (cpu *CreditPlanUpdate) ClearValidUntil() *CreditPlanUpdate {
	cpu.mutation.ClearValidUntil()
	return cpu
}

// SetSortOrder sets the "sort_order" field.
func (cpu *CreditPlanUpdate) SetSortOrder(i int) *CreditPlanUpdate {
	cpu.mutation.ResetSortOrder()
	cpu.mutation.SetSortOrder(i)
	return cpu
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableSortOrder(i *int) *CreditPlanUpdate {
	if i != nil {
		cpu.SetSortOrder(*i)
	}
	return cpu
}

// AddSortOrder adds i to the "sort_order" field.
func (cpu *CreditPlanUpdate) AddSortOrder(i int) *CreditPlanUpdate {
	cpu.mutation.AddSortOrder(i)
	return cpu
}

// SetStripePriceID sets the "stripe_price_id" field.
func (cpu *CreditPlanUpdate) SetStripePriceID(s string) *CreditPlanUpdate {
	cpu.mutation.SetStripePriceID(s)
	return cpu
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableStripePriceID(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetStripePriceID(*s)
	}
	return cpu
}

// ClearStripePriceID clears the value of the "stripe_price_id" field.
func (cpu *CreditPlanUpdate) ClearStripePriceID() *CreditPlanUpdate {
	cpu.mutation.ClearStripePriceID()
	return cpu
}

// SetStripeProductID sets the "stripe_product_id" field.
func (cpu *CreditPlanUpdate) SetStripeProductID(s string) *CreditPlanUpdate {
	cpu.mutation.SetStripeProductID(s)
	return cpu
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (cpu *CreditPlanUpdate) SetNillableStripeProductID(s *string) *CreditPlanUpdate {
	if s != nil {
		cpu.SetStripeProductID(*s)
	}
	return cpu
}

// ClearStripeProductID clears the value of the "stripe_product_id" field.
func (cpu *CreditPlanUpdate) ClearStripeProductID() *CreditPlanUpdate {
	cpu.mutation.ClearStripeProductID()
	return cpu
}

// SetMetadata sets the "metadata" field.
func (cpu *CreditPlanUpdate) SetMetadata(m map[string]interface{}) *CreditPlanUpdate {
	cpu.mutation.SetMetadata(m)
	return cpu
}

// ClearMetadata clears the value of the "metadata" field.
func (cpu *CreditPlanUpdate) ClearMetadata() *CreditPlanUpdate {
	cpu.mutation.ClearMetadata()
	return cpu
}

// SetUpdatedAt sets the "updated_at" field.
func (cpu *CreditPlanUpdate) SetUpdatedAt(t time.Time) *CreditPlanUpdate {
	cpu.mutation.SetUpdatedAt(t)
	return cpu
}

// Mutation returns the CreditPlanMutation object of the builder.
func (cpu *CreditPlanUpdate) Mutation() *CreditPlanMutation {
	return cpu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cpu *CreditPlanUpdate) Save(ctx context.Context) (int, error) {
	cpu.defaults()
	return withHooks(ctx, cpu.sqlSave, cpu.mutation, cpu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cpu *CreditPlanUpdate) SaveX(ctx context.Context) int {
	affected, err := cpu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cpu *CreditPlanUpdate) Exec(ctx context.Context) error {
	_, err := cpu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cpu *CreditPlanUpdate) ExecX(ctx context.Context) {
	if err := cpu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cpu *CreditPlanUpdate) defaults() {
	if _, ok := cpu.mutation.UpdatedAt(); !ok {
		v := creditplan.UpdateDefaultUpdatedAt()
		cpu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cpu *CreditPlanUpdate) check() error {
	if v, ok := cpu.mutation.Name(); ok {
		if err := creditplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.name": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.DisplayName(); ok {
		if err := creditplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.display_name": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.Credits(); ok {
		if err := creditplan.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.credits": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.Price(); ok {
		if err := creditplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.price": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.Currency(); ok {
		if err := creditplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.currency": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.DiscountPercentage(); ok {
		if err := creditplan.DiscountPercentageValidator(v); err != nil {
			return &ValidationError{Name: "discount_percentage", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.discount_percentage": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.StripePriceID(); ok {
		if err := creditplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := cpu.mutation.StripeProductID(); ok {
		if err := creditplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_product_id": %w`, err)}
		}
	}
	return nil
}

func (cpu *CreditPlanUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cpu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(creditplan.Table, creditplan.Columns, sqlgraph.NewFieldSpec(creditplan.FieldID, field.TypeUUID))
	if ps := cpu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cpu.mutation.Name(); ok {
		_spec.SetField(creditplan.FieldName, field.TypeString, value)
	}
	if value, ok := cpu.mutation.DisplayName(); ok {
		_spec.SetField(creditplan.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := cpu.mutation.Description(); ok {
		_spec.SetField(creditplan.FieldDescription, field.TypeString, value)
	}
	if cpu.mutation.DescriptionCleared() {
		_spec.ClearField(creditplan.FieldDescription, field.TypeString)
	}
	if value, ok := cpu.mutation.Credits(); ok {
		_spec.SetField(creditplan.FieldCredits, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.AddedCredits(); ok {
		_spec.AddField(creditplan.FieldCredits, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.Price(); ok {
		_spec.SetField(creditplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := cpu.mutation.AddedPrice(); ok {
		_spec.AddField(creditplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := cpu.mutation.Currency(); ok {
		_spec.SetField(creditplan.FieldCurrency, field.TypeString, value)
	}
	if value, ok := cpu.mutation.DiscountPercentage(); ok {
		_spec.SetField(creditplan.FieldDiscountPercentage, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.AddedDiscountPercentage(); ok {
		_spec.AddField(creditplan.FieldDiscountPercentage, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.IsPopular(); ok {
		_spec.SetField(creditplan.FieldIsPopular, field.TypeBool, value)
	}
	if value, ok := cpu.mutation.IsActive(); ok {
		_spec.SetField(creditplan.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := cpu.mutation.Features(); ok {
		_spec.SetField(creditplan.FieldFeatures, field.TypeJSON, value)
	}
	if value, ok := cpu.mutation.AppendedFeatures(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, creditplan.FieldFeatures, value)
		})
	}
	if cpu.mutation.FeaturesCleared() {
		_spec.ClearField(creditplan.FieldFeatures, field.TypeJSON)
	}
	if value, ok := cpu.mutation.ValidUntil(); ok {
		_spec.SetField(creditplan.FieldValidUntil, field.TypeTime, value)
	}
	if cpu.mutation.ValidUntilCleared() {
		_spec.ClearField(creditplan.FieldValidUntil, field.TypeTime)
	}
	if value, ok := cpu.mutation.SortOrder(); ok {
		_spec.SetField(creditplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.AddedSortOrder(); ok {
		_spec.AddField(creditplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := cpu.mutation.StripePriceID(); ok {
		_spec.SetField(creditplan.FieldStripePriceID, field.TypeString, value)
	}
	if cpu.mutation.StripePriceIDCleared() {
		_spec.ClearField(creditplan.FieldStripePriceID, field.TypeString)
	}
	if value, ok := cpu.mutation.StripeProductID(); ok {
		_spec.SetField(creditplan.FieldStripeProductID, field.TypeString, value)
	}
	if cpu.mutation.StripeProductIDCleared() {
		_spec.ClearField(creditplan.FieldStripeProductID, field.TypeString)
	}
	if value, ok := cpu.mutation.Metadata(); ok {
		_spec.SetField(creditplan.FieldMetadata, field.TypeJSON, value)
	}
	if cpu.mutation.MetadataCleared() {
		_spec.ClearField(creditplan.FieldMetadata, field.TypeJSON)
	}
	if value, ok := cpu.mutation.UpdatedAt(); ok {
		_spec.SetField(creditplan.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cpu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{creditplan.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cpu.mutation.done = true
	return n, nil
}

// CreditPlanUpdateOne is the builder for updating a single CreditPlan entity.
type CreditPlanUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CreditPlanMutation
}

// SetName sets the "name" field.
func (cpuo *CreditPlanUpdateOne) SetName(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetName(s)
	return cpuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableName(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetName(*s)
	}
	return cpuo
}

// SetDisplayName sets the "display_name" field.
func (cpuo *CreditPlanUpdateOne) SetDisplayName(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetDisplayName(s)
	return cpuo
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableDisplayName(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetDisplayName(*s)
	}
	return cpuo
}

// SetDescription sets the "description" field.
func (cpuo *CreditPlanUpdateOne) SetDescription(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetDescription(s)
	return cpuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableDescription(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetDescription(*s)
	}
	return cpuo
}

// ClearDescription clears the value of the "description" field.
func (cpuo *CreditPlanUpdateOne) ClearDescription() *CreditPlanUpdateOne {
	cpuo.mutation.ClearDescription()
	return cpuo
}

// SetCredits sets the "credits" field.
func (cpuo *CreditPlanUpdateOne) SetCredits(i int) *CreditPlanUpdateOne {
	cpuo.mutation.ResetCredits()
	cpuo.mutation.SetCredits(i)
	return cpuo
}

// SetNillableCredits sets the "credits" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableCredits(i *int) *CreditPlanUpdateOne {
	if i != nil {
		cpuo.SetCredits(*i)
	}
	return cpuo
}

// AddCredits adds i to the "credits" field.
func (cpuo *CreditPlanUpdateOne) AddCredits(i int) *CreditPlanUpdateOne {
	cpuo.mutation.AddCredits(i)
	return cpuo
}

// SetPrice sets the "price" field.
func (cpuo *CreditPlanUpdateOne) SetPrice(i int64) *CreditPlanUpdateOne {
	cpuo.mutation.ResetPrice()
	cpuo.mutation.SetPrice(i)
	return cpuo
}

// SetNillablePrice sets the "price" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillablePrice(i *int64) *CreditPlanUpdateOne {
	if i != nil {
		cpuo.SetPrice(*i)
	}
	return cpuo
}

// AddPrice adds i to the "price" field.
func (cpuo *CreditPlanUpdateOne) AddPrice(i int64) *CreditPlanUpdateOne {
	cpuo.mutation.AddPrice(i)
	return cpuo
}

// SetCurrency sets the "currency" field.
func (cpuo *CreditPlanUpdateOne) SetCurrency(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetCurrency(s)
	return cpuo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableCurrency(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetCurrency(*s)
	}
	return cpuo
}

// SetDiscountPercentage sets the "discount_percentage" field.
func (cpuo *CreditPlanUpdateOne) SetDiscountPercentage(i int) *CreditPlanUpdateOne {
	cpuo.mutation.ResetDiscountPercentage()
	cpuo.mutation.SetDiscountPercentage(i)
	return cpuo
}

// SetNillableDiscountPercentage sets the "discount_percentage" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableDiscountPercentage(i *int) *CreditPlanUpdateOne {
	if i != nil {
		cpuo.SetDiscountPercentage(*i)
	}
	return cpuo
}

// AddDiscountPercentage adds i to the "discount_percentage" field.
func (cpuo *CreditPlanUpdateOne) AddDiscountPercentage(i int) *CreditPlanUpdateOne {
	cpuo.mutation.AddDiscountPercentage(i)
	return cpuo
}

// SetIsPopular sets the "is_popular" field.
func (cpuo *CreditPlanUpdateOne) SetIsPopular(b bool) *CreditPlanUpdateOne {
	cpuo.mutation.SetIsPopular(b)
	return cpuo
}

// SetNillableIsPopular sets the "is_popular" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableIsPopular(b *bool) *CreditPlanUpdateOne {
	if b != nil {
		cpuo.SetIsPopular(*b)
	}
	return cpuo
}

// SetIsActive sets the "is_active" field.
func (cpuo *CreditPlanUpdateOne) SetIsActive(b bool) *CreditPlanUpdateOne {
	cpuo.mutation.SetIsActive(b)
	return cpuo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableIsActive(b *bool) *CreditPlanUpdateOne {
	if b != nil {
		cpuo.SetIsActive(*b)
	}
	return cpuo
}

// SetFeatures sets the "features" field.
func (cpuo *CreditPlanUpdateOne) SetFeatures(s []string) *CreditPlanUpdateOne {
	cpuo.mutation.SetFeatures(s)
	return cpuo
}

// AppendFeatures appends s to the "features" field.
func (cpuo *CreditPlanUpdateOne) AppendFeatures(s []string) *CreditPlanUpdateOne {
	cpuo.mutation.AppendFeatures(s)
	return cpuo
}

// ClearFeatures clears the value of the "features" field.
func (cpuo *CreditPlanUpdateOne) ClearFeatures() *CreditPlanUpdateOne {
	cpuo.mutation.ClearFeatures()
	return cpuo
}

// SetValidUntil sets the "valid_until" field.
func (cpuo *CreditPlanUpdateOne) SetValidUntil(t time.Time) *CreditPlanUpdateOne {
	cpuo.mutation.SetValidUntil(t)
	return cpuo
}

// SetNillableValidUntil sets the "valid_until" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableValidUntil(t *time.Time) *CreditPlanUpdateOne {
	if t != nil {
		cpuo.SetValidUntil(*t)
	}
	return cpuo
}

// ClearValidUntil clears the value of the "valid_until" field.
func (cpuo *CreditPlanUpdateOne) ClearValidUntil() *CreditPlanUpdateOne {
	cpuo.mutation.ClearValidUntil()
	return cpuo
}

// SetSortOrder sets the "sort_order" field.
func (cpuo *CreditPlanUpdateOne) SetSortOrder(i int) *CreditPlanUpdateOne {
	cpuo.mutation.ResetSortOrder()
	cpuo.mutation.SetSortOrder(i)
	return cpuo
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableSortOrder(i *int) *CreditPlanUpdateOne {
	if i != nil {
		cpuo.SetSortOrder(*i)
	}
	return cpuo
}

// AddSortOrder adds i to the "sort_order" field.
func (cpuo *CreditPlanUpdateOne) AddSortOrder(i int) *CreditPlanUpdateOne {
	cpuo.mutation.AddSortOrder(i)
	return cpuo
}

// SetStripePriceID sets the "stripe_price_id" field.
func (cpuo *CreditPlanUpdateOne) SetStripePriceID(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetStripePriceID(s)
	return cpuo
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableStripePriceID(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetStripePriceID(*s)
	}
	return cpuo
}

// ClearStripePriceID clears the value of the "stripe_price_id" field.
func (cpuo *CreditPlanUpdateOne) ClearStripePriceID() *CreditPlanUpdateOne {
	cpuo.mutation.ClearStripePriceID()
	return cpuo
}

// SetStripeProductID sets the "stripe_product_id" field.
func (cpuo *CreditPlanUpdateOne) SetStripeProductID(s string) *CreditPlanUpdateOne {
	cpuo.mutation.SetStripeProductID(s)
	return cpuo
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (cpuo *CreditPlanUpdateOne) SetNillableStripeProductID(s *string) *CreditPlanUpdateOne {
	if s != nil {
		cpuo.SetStripeProductID(*s)
	}
	return cpuo
}

// ClearStripeProductID clears the value of the "stripe_product_id" field.
func (cpuo *CreditPlanUpdateOne) ClearStripeProductID() *CreditPlanUpdateOne {
	cpuo.mutation.ClearStripeProductID()
	return cpuo
}

// SetMetadata sets the "metadata" field.
func (cpuo *CreditPlanUpdateOne) SetMetadata(m map[string]interface{}) *CreditPlanUpdateOne {
	cpuo.mutation.SetMetadata(m)
	return cpuo
}

// ClearMetadata clears the value of the "metadata" field.
func (cpuo *CreditPlanUpdateOne) ClearMetadata() *CreditPlanUpdateOne {
	cpuo.mutation.ClearMetadata()
	return cpuo
}

// SetUpdatedAt sets the "updated_at" field.
func (cpuo *CreditPlanUpdateOne) SetUpdatedAt(t time.Time) *CreditPlanUpdateOne {
	cpuo.mutation.SetUpdatedAt(t)
	return cpuo
}

// Mutation returns the CreditPlanMutation object of the builder.
func (cpuo *CreditPlanUpdateOne) Mutation() *CreditPlanMutation {
	return cpuo.mutation
}

// Where appends a list predicates to the CreditPlanUpdate builder.
func (cpuo *CreditPlanUpdateOne) Where(ps ...predicate.CreditPlan) *CreditPlanUpdateOne {
	cpuo.mutation.Where(ps...)
	return cpuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cpuo *CreditPlanUpdateOne) Select(field string, fields ...string) *CreditPlanUpdateOne {
	cpuo.fields = append([]string{field}, fields...)
	return cpuo
}

// Save executes the query and returns the updated CreditPlan entity.
func (cpuo *CreditPlanUpdateOne) Save(ctx context.Context) (*CreditPlan, error) {
	cpuo.defaults()
	return withHooks(ctx, cpuo.sqlSave, cpuo.mutation, cpuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cpuo *CreditPlanUpdateOne) SaveX(ctx context.Context) *CreditPlan {
	node, err := cpuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cpuo *CreditPlanUpdateOne) Exec(ctx context.Context) error {
	_, err := cpuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cpuo *CreditPlanUpdateOne) ExecX(ctx context.Context) {
	if err := cpuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cpuo *CreditPlanUpdateOne) defaults() {
	if _, ok := cpuo.mutation.UpdatedAt(); !ok {
		v := creditplan.UpdateDefaultUpdatedAt()
		cpuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cpuo *CreditPlanUpdateOne) check() error {
	if v, ok := cpuo.mutation.Name(); ok {
		if err := creditplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.name": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.DisplayName(); ok {
		if err := creditplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.display_name": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.Credits(); ok {
		if err := creditplan.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.credits": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.Price(); ok {
		if err := creditplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.price": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.Currency(); ok {
		if err := creditplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.currency": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.DiscountPercentage(); ok {
		if err := creditplan.DiscountPercentageValidator(v); err != nil {
			return &ValidationError{Name: "discount_percentage", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.discount_percentage": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.StripePriceID(); ok {
		if err := creditplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := cpuo.mutation.StripeProductID(); ok {
		if err := creditplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_product_id": %w`, err)}
		}
	}
	return nil
}

func (cpuo *CreditPlanUpdateOne) sqlSave(ctx context.Context) (_node *CreditPlan, err error) {
	if err := cpuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(creditplan.Table, creditplan.Columns, sqlgraph.NewFieldSpec(creditplan.FieldID, field.TypeUUID))
	id, ok := cpuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CreditPlan.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cpuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, creditplan.FieldID)
		for _, f := range fields {
			if !creditplan.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != creditplan.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cpuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cpuo.mutation.Name(); ok {
		_spec.SetField(creditplan.FieldName, field.TypeString, value)
	}
	if value, ok := cpuo.mutation.DisplayName(); ok {
		_spec.SetField(creditplan.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := cpuo.mutation.Description(); ok {
		_spec.SetField(creditplan.FieldDescription, field.TypeString, value)
	}
	if cpuo.mutation.DescriptionCleared() {
		_spec.ClearField(creditplan.FieldDescription, field.TypeString)
	}
	if value, ok := cpuo.mutation.Credits(); ok {
		_spec.SetField(creditplan.FieldCredits, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.AddedCredits(); ok {
		_spec.AddField(creditplan.FieldCredits, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.Price(); ok {
		_spec.SetField(creditplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := cpuo.mutation.AddedPrice(); ok {
		_spec.AddField(creditplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := cpuo.mutation.Currency(); ok {
		_spec.SetField(creditplan.FieldCurrency, field.TypeString, value)
	}
	if value, ok := cpuo.mutation.DiscountPercentage(); ok {
		_spec.SetField(creditplan.FieldDiscountPercentage, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.AddedDiscountPercentage(); ok {
		_spec.AddField(creditplan.FieldDiscountPercentage, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.IsPopular(); ok {
		_spec.SetField(creditplan.FieldIsPopular, field.TypeBool, value)
	}
	if value, ok := cpuo.mutation.IsActive(); ok {
		_spec.SetField(creditplan.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := cpuo.mutation.Features(); ok {
		_spec.SetField(creditplan.FieldFeatures, field.TypeJSON, value)
	}
	if value, ok := cpuo.mutation.AppendedFeatures(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, creditplan.FieldFeatures, value)
		})
	}
	if cpuo.mutation.FeaturesCleared() {
		_spec.ClearField(creditplan.FieldFeatures, field.TypeJSON)
	}
	if value, ok := cpuo.mutation.ValidUntil(); ok {
		_spec.SetField(creditplan.FieldValidUntil, field.TypeTime, value)
	}
	if cpuo.mutation.ValidUntilCleared() {
		_spec.ClearField(creditplan.FieldValidUntil, field.TypeTime)
	}
	if value, ok := cpuo.mutation.SortOrder(); ok {
		_spec.SetField(creditplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.AddedSortOrder(); ok {
		_spec.AddField(creditplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := cpuo.mutation.StripePriceID(); ok {
		_spec.SetField(creditplan.FieldStripePriceID, field.TypeString, value)
	}
	if cpuo.mutation.StripePriceIDCleared() {
		_spec.ClearField(creditplan.FieldStripePriceID, field.TypeString)
	}
	if value, ok := cpuo.mutation.StripeProductID(); ok {
		_spec.SetField(creditplan.FieldStripeProductID, field.TypeString, value)
	}
	if cpuo.mutation.StripeProductIDCleared() {
		_spec.ClearField(creditplan.FieldStripeProductID, field.TypeString)
	}
	if value, ok := cpuo.mutation.Metadata(); ok {
		_spec.SetField(creditplan.FieldMetadata, field.TypeJSON, value)
	}
	if cpuo.mutation.MetadataCleared() {
		_spec.ClearField(creditplan.FieldMetadata, field.TypeJSON)
	}
	if value, ok := cpuo.mutation.UpdatedAt(); ok {
		_spec.SetField(creditplan.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &CreditPlan{config: cpuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cpuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{creditplan.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cpuo.mutation.done = true
	return _node, nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/payment"
)

// Payment is the model entity for the Payment schema.
type Payment struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// OrderID holds the value of the "order_id" field.
	OrderID uuid.UUID `json:"order_id,omitempty"`
	// SubscriptionID holds the value of the "subscription_id" field.
	SubscriptionID uuid.UUID `json:"subscription_id,omitempty"`
	// PaymentMethod holds the value of the "payment_method" field.
	PaymentMethod string `json:"payment_method,omitempty"`
	// PaymentProvider holds the value of the "payment_provider" field.
	PaymentProvider string `json:"payment_provider,omitempty"`
	// ExternalPaymentID holds the value of the "external_payment_id" field.
	ExternalPaymentID string `json:"external_payment_id,omitempty"`
	// Amount holds the value of the "amount" field.
	Amount int64 `json:"amount,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// Status holds the value of the "status" field.
	Status payment.Status `json:"status,omitempty"`
	// PaymentIntentID holds the value of the "payment_intent_id" field.
	PaymentIntentID string `json:"payment_intent_id,omitempty"`
	// ChargeID holds the value of the "charge_id" field.
	ChargeID string `json:"charge_id,omitempty"`
	// BillingDetails holds the value of the "billing_details" field.
	BillingDetails map[string]interface{} `json:"billing_details,omitempty"`
	// PaymentMethodDetails holds the value of the "payment_method_details" field.
	PaymentMethodDetails map[string]interface{} `json:"payment_method_details,omitempty"`
	// PaidAt holds the value of the "paid_at" field.
	PaidAt time.Time `json:"paid_at,omitempty"`
	// FailedAt holds the value of the "failed_at" field.
	FailedAt time.Time `json:"failed_at,omitempty"`
	// FailureCode holds the value of the "failure_code" field.
	FailureCode string `json:"failure_code,omitempty"`
	// FailureMessage holds the value of the "failure_message" field.
	FailureMessage string `json:"failure_message,omitempty"`
	// RefundedAmount holds the value of the "refunded_amount" field.
	RefundedAmount int64 `json:"refunded_amount,omitempty"`
	// RefundedAt holds the value of the "refunded_at" field.
	RefundedAt time.Time `json:"refunded_at,omitempty"`
	// RefundReason holds the value of the "refund_reason" field.
	RefundReason string `json:"refund_reason,omitempty"`
	// WebhookData holds the value of the "webhook_data" field.
	WebhookData map[string]interface{} `json:"webhook_data,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Payment) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case payment.FieldBillingDetails, payment.FieldPaymentMethodDetails, payment.FieldWebhookData, payment.FieldMetadata:
			values[i] = new([]byte)
		case payment.FieldAmount, payment.FieldRefundedAmount:
			values[i] = new(sql.NullInt64)
		case payment.FieldPaymentMethod, payment.FieldPaymentProvider, payment.FieldExternalPaymentID, payment.FieldCurrency, payment.FieldStatus, payment.FieldPaymentIntentID, payment.FieldChargeID, payment.FieldFailureCode, payment.FieldFailureMessage, payment.FieldRefundReason:
			values[i] = new(sql.NullString)
		case payment.FieldPaidAt, payment.FieldFailedAt, payment.FieldRefundedAt, payment.FieldCreatedAt, payment.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case payment.FieldID, payment.FieldUserID, payment.FieldOrderID, payment.FieldSubscriptionID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Payment fields.
func (pa *Payment) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case payment.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				pa.ID = *value
			}
		case payment.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				pa.UserID = *value
			}
		case payment.FieldOrderID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field order_id", values[i])
			} else if value != nil {
				pa.OrderID = *value
			}
		case payment.FieldSubscriptionID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field subscription_id", values[i])
			} else if value != nil {
				pa.SubscriptionID = *value
			}
		case payment.FieldPaymentMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_method", values[i])
			} else if value.Valid {
				pa.PaymentMethod = value.String
			}
		case payment.FieldPaymentProvider:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_provider", values[i])
			} else if value.Valid {
				pa.PaymentProvider = value.String
			}
		case payment.FieldExternalPaymentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field external_payment_id", values[i])
			} else if value.Valid {
				pa.ExternalPaymentID = value.String
			}
		case payment.FieldAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				pa.Amount = value.Int64
			}
		case payment.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				pa.Currency = value.String
			}
		case payment.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				pa.Status = payment.Status(value.String)
			}
		case payment.FieldPaymentIntentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_intent_id", values[i])
			} else if value.Valid {
				pa.PaymentIntentID = value.String
			}
		case payment.FieldChargeID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field charge_id", values[i])
			} else if value.Valid {
				pa.ChargeID = value.String
			}
		case payment.FieldBillingDetails:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field billing_details", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pa.BillingDetails); err != nil {
					return fmt.Errorf("unmarshal field billing_details: %w", err)
				}
			}
		case payment.FieldPaymentMethodDetails:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field payment_method_details", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pa.PaymentMethodDetails); err != nil {
					return fmt.Errorf("unmarshal field payment_method_details: %w", err)
				}
			}
		case payment.FieldPaidAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field paid_at", values[i])
			} else if value.Valid {
				pa.PaidAt = value.Time
			}
		case payment.FieldFailedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field failed_at", values[i])
			} else if value.Valid {
				pa.FailedAt = value.Time
			}
		case payment.FieldFailureCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field failure_code", values[i])
			} else if value.Valid {
				pa.FailureCode = value.String
			}
		case payment.FieldFailureMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field failure_message", values[i])
			} else if value.Valid {
				pa.FailureMessage = value.String
			}
		case payment.FieldRefundedAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field refunded_amount", values[i])
			} else if value.Valid {
				pa.RefundedAmount = value.Int64
			}
		case payment.FieldRefundedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field refunded_at", values[i])
			} else if value.Valid {
				pa.RefundedAt = value.Time
			}
		case payment.FieldRefundReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field refund_reason", values[i])
			} else if value.Valid {
				pa.RefundReason = value.String
			}
		case payment.FieldWebhookData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field webhook_data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pa.WebhookData); err != nil {
					return fmt.Errorf("unmarshal field webhook_data: %w", err)
				}
			}
		case payment.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pa.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case payment.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pa.CreatedAt = value.Time
			}
		case payment.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pa.UpdatedAt = value.Time
			}
		default:
			pa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Payment.
// This includes values selected through modifiers, order, etc.
func (pa *Payment) Value(name string) (ent.Value, error) {
	return pa.selectValues.Get(name)
}

// Update returns a builder for updating this Payment.
// Note that you need to call Payment.Unwrap() before calling this method if this Payment
// was returned from a transaction, and the transaction was committed or rolled back.
func (pa *Payment) Update() *PaymentUpdateOne {
	return NewPaymentClient(pa.config).UpdateOne(pa)
}

// Unwrap unwraps the Payment entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pa *Payment) Unwrap() *Payment {
	_tx, ok := pa.config.driver.(*txDriver)
	if !ok {
		panic("ent: Payment is not a transactional entity")
	}
	pa.config.driver = _tx.drv
	return pa
}

// String implements the fmt.Stringer.
func (pa *Payment) String() string {
	var builder strings.Builder
	builder.WriteString("Payment(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pa.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.UserID))
	builder.WriteString(", ")
	builder.WriteString("order_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.OrderID))
	builder.WriteString(", ")
	builder.WriteString("subscription_id=")
	builder.WriteString(fmt.Sprintf("%v", pa.SubscriptionID))
	builder.WriteString(", ")
	builder.WriteString("payment_method=")
	builder.WriteString(pa.PaymentMethod)
	builder.WriteString(", ")
	builder.WriteString("payment_provider=")
	builder.WriteString(pa.PaymentProvider)
	builder.WriteString(", ")
	builder.WriteString("external_payment_id=")
	builder.WriteString(pa.ExternalPaymentID)
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", pa.Amount))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(pa.Currency)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", pa.Status))
	builder.WriteString(", ")
	builder.WriteString("payment_intent_id=")
	builder.WriteString(pa.PaymentIntentID)
	builder.WriteString(", ")
	builder.WriteString("charge_id=")
	builder.WriteString(pa.ChargeID)
	builder.WriteString(", ")
	builder.WriteString("billing_details=")
	builder.WriteString(fmt.Sprintf("%v", pa.BillingDetails))
	builder.WriteString(", ")
	builder.WriteString("payment_method_details=")
	builder.WriteString(fmt.Sprintf("%v", pa.PaymentMethodDetails))
	builder.WriteString(", ")
	builder.WriteString("paid_at=")
	builder.WriteString(pa.PaidAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("failed_at=")
	builder.WriteString(pa.FailedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("failure_code=")
	builder.WriteString(pa.FailureCode)
	builder.WriteString(", ")
	builder.WriteString("failure_message=")
	builder.WriteString(pa.FailureMessage)
	builder.WriteString(", ")
	builder.WriteString("refunded_amount=")
	builder.WriteString(fmt.Sprintf("%v", pa.RefundedAmount))
	builder.WriteString(", ")
	builder.WriteString("refunded_at=")
	builder.WriteString(pa.RefundedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("refund_reason=")
	builder.WriteString(pa.RefundReason)
	builder.WriteString(", ")
	builder.WriteString("webhook_data=")
	builder.WriteString(fmt.Sprintf("%v", pa.WebhookData))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", pa.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pa.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Payments is a parsable slice of Payment.
type Payments []*Payment

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notification"
)

// Notification is the model entity for the Notification schema.
type Notification struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User who should receive this notification
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Type of notification
	Type notification.Type `json:"type,omitempty"`
	// Notification title
	Title string `json:"title,omitempty"`
	// Notification message content
	Message string `json:"message,omitempty"`
	// Additional notification data
	Data map[string]interface{} `json:"data,omitempty"`
	// Notification priority
	Priority notification.Priority `json:"priority,omitempty"`
	// Delivery channels (email, push, in_app, sms)
	Channels []string `json:"channels,omitempty"`
	// Notification status
	Status notification.Status `json:"status,omitempty"`
	// Reference to related entity
	ReferenceID string `json:"reference_id,omitempty"`
	// Type of referenced entity
	ReferenceType string `json:"reference_type,omitempty"`
	// Status for each delivery channel
	DeliveryStatus map[string]interface{} `json:"delivery_status,omitempty"`
	// When the notification should be sent
	ScheduledAt time.Time `json:"scheduled_at,omitempty"`
	// When the notification was sent
	SentAt time.Time `json:"sent_at,omitempty"`
	// When the notification was delivered
	DeliveredAt time.Time `json:"delivered_at,omitempty"`
	// When the notification was read
	ReadAt time.Time `json:"read_at,omitempty"`
	// When the notification expires
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	// URL for notification action
	ActionURL string `json:"action_url,omitempty"`
	// Data for notification action
	ActionData map[string]interface{} `json:"action_data,omitempty"`
	// Number of delivery retry attempts
	RetryCount int `json:"retry_count,omitempty"`
	// When to retry delivery
	NextRetryAt time.Time `json:"next_retry_at,omitempty"`
	// Last error message if delivery failed
	ErrorMessage string `json:"error_message,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Notification) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notification.FieldData, notification.FieldChannels, notification.FieldDeliveryStatus, notification.FieldActionData, notification.FieldMetadata:
			values[i] = new([]byte)
		case notification.FieldRetryCount:
			values[i] = new(sql.NullInt64)
		case notification.FieldType, notification.FieldTitle, notification.FieldMessage, notification.FieldPriority, notification.FieldStatus, notification.FieldReferenceID, notification.FieldReferenceType, notification.FieldActionURL, notification.FieldErrorMessage:
			values[i] = new(sql.NullString)
		case notification.FieldScheduledAt, notification.FieldSentAt, notification.FieldDeliveredAt, notification.FieldReadAt, notification.FieldExpiresAt, notification.FieldNextRetryAt, notification.FieldCreatedAt, notification.FieldUpdatedAt, notification.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case notification.FieldID, notification.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Notification fields.
func (n *Notification) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notification.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				n.ID = *value
			}
		case notification.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				n.UserID = *value
			}
		case notification.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				n.Type = notification.Type(value.String)
			}
		case notification.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				n.Title = value.String
			}
		case notification.FieldMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field message", values[i])
			} else if value.Valid {
				n.Message = value.String
			}
		case notification.FieldData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.Data); err != nil {
					return fmt.Errorf("unmarshal field data: %w", err)
				}
			}
		case notification.FieldPriority:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field priority", values[i])
			} else if value.Valid {
				n.Priority = notification.Priority(value.String)
			}
		case notification.FieldChannels:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field channels", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.Channels); err != nil {
					return fmt.Errorf("unmarshal field channels: %w", err)
				}
			}
		case notification.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				n.Status = notification.Status(value.String)
			}
		case notification.FieldReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_id", values[i])
			} else if value.Valid {
				n.ReferenceID = value.String
			}
		case notification.FieldReferenceType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_type", values[i])
			} else if value.Valid {
				n.ReferenceType = value.String
			}
		case notification.FieldDeliveryStatus:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field delivery_status", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.DeliveryStatus); err != nil {
					return fmt.Errorf("unmarshal field delivery_status: %w", err)
				}
			}
		case notification.FieldScheduledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field scheduled_at", values[i])
			} else if value.Valid {
				n.ScheduledAt = value.Time
			}
		case notification.FieldSentAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field sent_at", values[i])
			} else if value.Valid {
				n.SentAt = value.Time
			}
		case notification.FieldDeliveredAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field delivered_at", values[i])
			} else if value.Valid {
				n.DeliveredAt = value.Time
			}
		case notification.FieldReadAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field read_at", values[i])
			} else if value.Valid {
				n.ReadAt = value.Time
			}
		case notification.FieldExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_at", values[i])
			} else if value.Valid {
				n.ExpiresAt = value.Time
			}
		case notification.FieldActionURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field action_url", values[i])
			} else if value.Valid {
				n.ActionURL = value.String
			}
		case notification.FieldActionData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field action_data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.ActionData); err != nil {
					return fmt.Errorf("unmarshal field action_data: %w", err)
				}
			}
		case notification.FieldRetryCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field retry_count", values[i])
			} else if value.Valid {
				n.RetryCount = int(value.Int64)
			}
		case notification.FieldNextRetryAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field next_retry_at", values[i])
			} else if value.Valid {
				n.NextRetryAt = value.Time
			}
		case notification.FieldErrorMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_message", values[i])
			} else if value.Valid {
				n.ErrorMessage = value.String
			}
		case notification.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case notification.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				n.CreatedAt = value.Time
			}
		case notification.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				n.UpdatedAt = value.Time
			}
		case notification.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				n.DeletedAt = value.Time
			}
		default:
			n.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Notification.
// This includes values selected through modifiers, order, etc.
func (n *Notification) Value(name string) (ent.Value, error) {
	return n.selectValues.Get(name)
}

// Update returns a builder for updating this Notification.
// Note that you need to call Notification.Unwrap() before calling this method if this Notification
// was returned from a transaction, and the transaction was committed or rolled back.
func (n *Notification) Update() *NotificationUpdateOne {
	return NewNotificationClient(n.config).UpdateOne(n)
}

// Unwrap unwraps the Notification entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (n *Notification) Unwrap() *Notification {
	_tx, ok := n.config.driver.(*txDriver)
	if !ok {
		panic("ent: Notification is not a transactional entity")
	}
	n.config.driver = _tx.drv
	return n
}

// String implements the fmt.Stringer.
func (n *Notification) String() string {
	var builder strings.Builder
	builder.WriteString("Notification(")
	builder.WriteString(fmt.Sprintf("id=%v, ", n.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", n.UserID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", n.Type))
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(n.Title)
	builder.WriteString(", ")
	builder.WriteString("message=")
	builder.WriteString(n.Message)
	builder.WriteString(", ")
	builder.WriteString("data=")
	builder.WriteString(fmt.Sprintf("%v", n.Data))
	builder.WriteString(", ")
	builder.WriteString("priority=")
	builder.WriteString(fmt.Sprintf("%v", n.Priority))
	builder.WriteString(", ")
	builder.WriteString("channels=")
	builder.WriteString(fmt.Sprintf("%v", n.Channels))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", n.Status))
	builder.WriteString(", ")
	builder.WriteString("reference_id=")
	builder.WriteString(n.ReferenceID)
	builder.WriteString(", ")
	builder.WriteString("reference_type=")
	builder.WriteString(n.ReferenceType)
	builder.WriteString(", ")
	builder.WriteString("delivery_status=")
	builder.WriteString(fmt.Sprintf("%v", n.DeliveryStatus))
	builder.WriteString(", ")
	builder.WriteString("scheduled_at=")
	builder.WriteString(n.ScheduledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sent_at=")
	builder.WriteString(n.SentAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("delivered_at=")
	builder.WriteString(n.DeliveredAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("read_at=")
	builder.WriteString(n.ReadAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("expires_at=")
	builder.WriteString(n.ExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("action_url=")
	builder.WriteString(n.ActionURL)
	builder.WriteString(", ")
	builder.WriteString("action_data=")
	builder.WriteString(fmt.Sprintf("%v", n.ActionData))
	builder.WriteString(", ")
	builder.WriteString("retry_count=")
	builder.WriteString(fmt.Sprintf("%v", n.RetryCount))
	builder.WriteString(", ")
	builder.WriteString("next_retry_at=")
	builder.WriteString(n.NextRetryAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("error_message=")
	builder.WriteString(n.ErrorMessage)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", n.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(n.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(n.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(n.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Notifications is a parsable slice of Notification.
type Notifications []*Notification

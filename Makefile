# Root Makefile for Social Content AI Platform

.PHONY: all build test clean run docker proto deps lint fmt vet help

# Variables
PROJECT_NAME := social-content-ai
VERSION ?= latest
DOCKER_REGISTRY ?= localhost:5000

# Services
SERVICES := user-service ai-content-service content-mgmt-service credit-service asset-service integration-service notification-service analytics-service api-gateway

# Build info
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Default target
all: deps proto build test

# Install dependencies for all services
deps:
	@echo "📦 Installing dependencies for all services..."
	@for service in $(SERVICES); do \
		echo "Installing dependencies for $$service..."; \
		cd $$service && go mod download && go mod tidy && cd ..; \
	done
	@echo "✅ Dependencies installed for all services"

# Generate protocol buffers
proto:
	@echo "🔄 Generating protocol buffers..."
	cd proto-shared && make all
	@echo "✅ Protocol buffers generated"

# Build all services
build:
	@echo "🔨 Building all services..."
	@for service in $(SERVICES); do \
		echo "Building $$service..."; \
		cd $$service && make build && cd ..; \
	done
	@echo "✅ All services built successfully"

# Build for local development
build-local:
	@echo "🔨 Building all services for local development..."
	@for service in $(SERVICES); do \
		echo "Building $$service locally..."; \
		cd $$service && make build-local && cd ..; \
	done
	@echo "✅ All services built for local development"

# Run tests for all services
test:
	@echo "🧪 Running tests for all services..."
	@for service in $(SERVICES); do \
		echo "Testing $$service..."; \
		cd $$service && make test && cd ..; \
	done
	@echo "✅ All tests completed"

# Run tests with coverage
test-coverage:
	@echo "🧪 Running tests with coverage for all services..."
	@for service in $(SERVICES); do \
		echo "Testing $$service with coverage..."; \
		cd $$service && make test-coverage && cd ..; \
	done
	@echo "📊 Coverage reports generated for all services"

# Build Docker images for all services
docker:
	@echo "🐳 Building Docker images for all services..."
	@for service in $(SERVICES); do \
		echo "Building Docker image for $$service..."; \
		cd $$service && make docker && cd ..; \
	done
	@echo "✅ All Docker images built"

# Start the entire platform with docker-compose
up:
	@echo "🚀 Starting Social Content AI platform..."
	docker-compose up -d
	@echo "✅ Platform started successfully"
	@echo "🌐 API Gateway: http://localhost:8080"
	@echo "📊 Grafana: http://localhost:3000 (admin/admin)"
	@echo "📈 Prometheus: http://localhost:9090"
	@echo "💾 MinIO Console: http://localhost:9001 (minioadmin/minioadmin)"

# Stop the platform
down:
	@echo "🛑 Stopping Social Content AI platform..."
	docker-compose down
	@echo "✅ Platform stopped"

# Stop and remove all containers, networks, and volumes
down-clean:
	@echo "🧹 Cleaning up platform..."
	docker-compose down -v --remove-orphans
	@echo "✅ Platform cleaned up"

# View logs
logs:
	@echo "📋 Viewing platform logs..."
	docker-compose logs -f

# View logs for specific service
logs-service:
	@read -p "Enter service name: " service; \
	docker-compose logs -f $$service

# Restart specific service
restart-service:
	@read -p "Enter service name: " service; \
	docker-compose restart $$service

# Scale specific service
scale-service:
	@read -p "Enter service name: " service; \
	read -p "Enter number of replicas: " replicas; \
	docker-compose up -d --scale $$service=$$replicas

# Health check for all services
health:
	@echo "🏥 Checking health of all services..."
	@curl -f http://localhost:8080/health || echo "❌ API Gateway unhealthy"
	@echo ""

# Format code for all services
fmt:
	@echo "✨ Formatting code for all services..."
	@for service in $(SERVICES); do \
		echo "Formatting $$service..."; \
		cd $$service && make fmt && cd ..; \
	done
	@echo "✅ Code formatted for all services"

# Lint code for all services
lint:
	@echo "🔍 Linting code for all services..."
	@for service in $(SERVICES); do \
		echo "Linting $$service..."; \
		cd $$service && make lint && cd ..; \
	done
	@echo "✅ Code linted for all services"

# Vet code for all services
vet:
	@echo "🔍 Vetting code for all services..."
	@for service in $(SERVICES); do \
		echo "Vetting $$service..."; \
		cd $$service && make vet && cd ..; \
	done
	@echo "✅ Code vetted for all services"

# Security scan for all services
security:
	@echo "🔒 Running security scan for all services..."
	@for service in $(SERVICES); do \
		echo "Scanning $$service..."; \
		cd $$service && make security && cd ..; \
	done
	@echo "✅ Security scan completed for all services"

# Clean build artifacts for all services
clean:
	@echo "🧹 Cleaning build artifacts for all services..."
	@for service in $(SERVICES); do \
		echo "Cleaning $$service..."; \
		cd $$service && make clean && cd ..; \
	done
	@echo "✅ Build artifacts cleaned for all services"

# Database migrations
migrate-up:
	@echo "⬆️ Running database migrations..."
	docker-compose exec user-service make migrate-up
	docker-compose exec ai-content-service make migrate-up
	docker-compose exec content-mgmt-service make migrate-up
	docker-compose exec credit-service make migrate-up
	@echo "✅ Database migrations completed"

migrate-down:
	@echo "⬇️ Rolling back database migrations..."
	docker-compose exec user-service make migrate-down
	docker-compose exec ai-content-service make migrate-down
	docker-compose exec content-mgmt-service make migrate-down
	docker-compose exec credit-service make migrate-down
	@echo "✅ Database migrations rolled back"

# Development environment setup
dev-setup:
	@echo "🛠️ Setting up development environment..."
	@echo "Installing development tools..."
	@for service in $(SERVICES); do \
		echo "Installing tools for $$service..."; \
		cd $$service && make install-tools && cd ..; \
	done
	@echo "Creating .env file..."
	@cp .env.example .env || echo "Please create .env file manually"
	@echo "✅ Development environment setup completed"

# Install development tools
install-tools:
	@echo "🛠️ Installing development tools..."
	@for service in $(SERVICES); do \
		echo "Installing tools for $$service..."; \
		cd $$service && make install-tools && cd ..; \
	done
	@echo "✅ Development tools installed"

# Generate mocks for all services
mocks:
	@echo "🎭 Generating mocks for all services..."
	@for service in $(SERVICES); do \
		echo "Generating mocks for $$service..."; \
		cd $$service && make mocks && cd ..; \
	done
	@echo "✅ Mocks generated for all services"

# Full CI pipeline
ci: deps proto fmt vet lint security test

# Release build
release: clean ci build docker
	@echo "🎉 Release build completed!"
	@echo "📦 Version: $(VERSION)"
	@echo "🕐 Build time: $(BUILD_TIME)"
	@echo "📝 Git commit: $(GIT_COMMIT)"

# Push Docker images to registry
push:
	@echo "📤 Pushing Docker images to registry..."
	@for service in $(SERVICES); do \
		echo "Pushing $$service..."; \
		docker tag $$service:$(VERSION) $(DOCKER_REGISTRY)/$$service:$(VERSION); \
		docker push $(DOCKER_REGISTRY)/$$service:$(VERSION); \
	done
	@echo "✅ All images pushed to registry"

# Deploy to staging
deploy-staging:
	@echo "🚀 Deploying to staging..."
	# Add your staging deployment commands here
	@echo "✅ Deployed to staging"

# Deploy to production
deploy-prod:
	@echo "🚀 Deploying to production..."
	# Add your production deployment commands here
	@echo "✅ Deployed to production"

# Backup database
backup-db:
	@echo "💾 Backing up database..."
	docker-compose exec postgres pg_dump -U postgres socialai > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup completed"

# Restore database
restore-db:
	@read -p "Enter backup file path: " backup_file; \
	docker-compose exec -T postgres psql -U postgres socialai < $$backup_file
	@echo "✅ Database restored"

# Monitor platform
monitor:
	@echo "📊 Opening monitoring dashboards..."
	@echo "Grafana: http://localhost:3000"
	@echo "Prometheus: http://localhost:9090"
	@echo "MinIO Console: http://localhost:9001"

# Load test
load-test:
	@echo "⚡ Running load tests..."
	# Add your load testing commands here
	@echo "✅ Load tests completed"

# Help
help:
	@echo "Social Content AI Platform - Available Commands:"
	@echo ""
	@echo "🏗️  Build Commands:"
	@echo "  all              - Run deps, proto, build, and test"
	@echo "  deps             - Install dependencies for all services"
	@echo "  proto            - Generate protocol buffers"
	@echo "  build            - Build all services"
	@echo "  build-local      - Build all services for local development"
	@echo "  docker           - Build Docker images for all services"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  test             - Run tests for all services"
	@echo "  test-coverage    - Run tests with coverage"
	@echo "  load-test        - Run load tests"
	@echo ""
	@echo "🐳 Docker Commands:"
	@echo "  up               - Start the platform with docker-compose"
	@echo "  down             - Stop the platform"
	@echo "  down-clean       - Stop and clean up everything"
	@echo "  logs             - View platform logs"
	@echo "  logs-service     - View logs for specific service"
	@echo "  restart-service  - Restart specific service"
	@echo "  scale-service    - Scale specific service"
	@echo ""
	@echo "🔧 Development Commands:"
	@echo "  dev-setup        - Setup development environment"
	@echo "  install-tools    - Install development tools"
	@echo "  fmt              - Format code for all services"
	@echo "  lint             - Lint code for all services"
	@echo "  vet              - Vet code for all services"
	@echo "  security         - Run security scan"
	@echo "  mocks            - Generate mocks for all services"
	@echo "  clean            - Clean build artifacts"
	@echo ""
	@echo "🗄️  Database Commands:"
	@echo "  migrate-up       - Run database migrations"
	@echo "  migrate-down     - Rollback database migrations"
	@echo "  backup-db        - Backup database"
	@echo "  restore-db       - Restore database"
	@echo ""
	@echo "🚀 Deployment Commands:"
	@echo "  ci               - Run full CI pipeline"
	@echo "  release          - Build release version"
	@echo "  push             - Push Docker images to registry"
	@echo "  deploy-staging   - Deploy to staging"
	@echo "  deploy-prod      - Deploy to production"
	@echo ""
	@echo "📊 Monitoring Commands:"
	@echo "  health           - Check health of all services"
	@echo "  monitor          - Open monitoring dashboards"
	@echo ""
	@echo "❓ Help:"
	@echo "  help             - Show this help message"

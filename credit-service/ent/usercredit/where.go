// Code generated by ent, DO NOT EDIT.

package usercredit

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldUserID, v))
}

// CurrentCredits applies equality check predicate on the "current_credits" field. It's identical to CurrentCreditsEQ.
func CurrentCredits(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldCurrentCredits, v))
}

// TotalCredits applies equality check predicate on the "total_credits" field. It's identical to TotalCreditsEQ.
func TotalCredits(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldTotalCredits, v))
}

// PlanID applies equality check predicate on the "plan_id" field. It's identical to PlanIDEQ.
func PlanID(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanID, v))
}

// PlanName applies equality check predicate on the "plan_name" field. It's identical to PlanNameEQ.
func PlanName(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanName, v))
}

// PlanExpiresAt applies equality check predicate on the "plan_expires_at" field. It's identical to PlanExpiresAtEQ.
func PlanExpiresAt(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanExpiresAt, v))
}

// MonthlyLimit applies equality check predicate on the "monthly_limit" field. It's identical to MonthlyLimitEQ.
func MonthlyLimit(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyLimit, v))
}

// MonthlyUsed applies equality check predicate on the "monthly_used" field. It's identical to MonthlyUsedEQ.
func MonthlyUsed(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyUsed, v))
}

// MonthlyResetAt applies equality check predicate on the "monthly_reset_at" field. It's identical to MonthlyResetAtEQ.
func MonthlyResetAt(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyResetAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldUserID, v))
}

// CurrentCreditsEQ applies the EQ predicate on the "current_credits" field.
func CurrentCreditsEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldCurrentCredits, v))
}

// CurrentCreditsNEQ applies the NEQ predicate on the "current_credits" field.
func CurrentCreditsNEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldCurrentCredits, v))
}

// CurrentCreditsIn applies the In predicate on the "current_credits" field.
func CurrentCreditsIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldCurrentCredits, vs...))
}

// CurrentCreditsNotIn applies the NotIn predicate on the "current_credits" field.
func CurrentCreditsNotIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldCurrentCredits, vs...))
}

// CurrentCreditsGT applies the GT predicate on the "current_credits" field.
func CurrentCreditsGT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldCurrentCredits, v))
}

// CurrentCreditsGTE applies the GTE predicate on the "current_credits" field.
func CurrentCreditsGTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldCurrentCredits, v))
}

// CurrentCreditsLT applies the LT predicate on the "current_credits" field.
func CurrentCreditsLT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldCurrentCredits, v))
}

// CurrentCreditsLTE applies the LTE predicate on the "current_credits" field.
func CurrentCreditsLTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldCurrentCredits, v))
}

// TotalCreditsEQ applies the EQ predicate on the "total_credits" field.
func TotalCreditsEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldTotalCredits, v))
}

// TotalCreditsNEQ applies the NEQ predicate on the "total_credits" field.
func TotalCreditsNEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldTotalCredits, v))
}

// TotalCreditsIn applies the In predicate on the "total_credits" field.
func TotalCreditsIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldTotalCredits, vs...))
}

// TotalCreditsNotIn applies the NotIn predicate on the "total_credits" field.
func TotalCreditsNotIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldTotalCredits, vs...))
}

// TotalCreditsGT applies the GT predicate on the "total_credits" field.
func TotalCreditsGT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldTotalCredits, v))
}

// TotalCreditsGTE applies the GTE predicate on the "total_credits" field.
func TotalCreditsGTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldTotalCredits, v))
}

// TotalCreditsLT applies the LT predicate on the "total_credits" field.
func TotalCreditsLT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldTotalCredits, v))
}

// TotalCreditsLTE applies the LTE predicate on the "total_credits" field.
func TotalCreditsLTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldTotalCredits, v))
}

// PlanIDEQ applies the EQ predicate on the "plan_id" field.
func PlanIDEQ(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanID, v))
}

// PlanIDNEQ applies the NEQ predicate on the "plan_id" field.
func PlanIDNEQ(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldPlanID, v))
}

// PlanIDIn applies the In predicate on the "plan_id" field.
func PlanIDIn(vs ...string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldPlanID, vs...))
}

// PlanIDNotIn applies the NotIn predicate on the "plan_id" field.
func PlanIDNotIn(vs ...string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldPlanID, vs...))
}

// PlanIDGT applies the GT predicate on the "plan_id" field.
func PlanIDGT(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldPlanID, v))
}

// PlanIDGTE applies the GTE predicate on the "plan_id" field.
func PlanIDGTE(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldPlanID, v))
}

// PlanIDLT applies the LT predicate on the "plan_id" field.
func PlanIDLT(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldPlanID, v))
}

// PlanIDLTE applies the LTE predicate on the "plan_id" field.
func PlanIDLTE(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldPlanID, v))
}

// PlanIDContains applies the Contains predicate on the "plan_id" field.
func PlanIDContains(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldContains(FieldPlanID, v))
}

// PlanIDHasPrefix applies the HasPrefix predicate on the "plan_id" field.
func PlanIDHasPrefix(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldHasPrefix(FieldPlanID, v))
}

// PlanIDHasSuffix applies the HasSuffix predicate on the "plan_id" field.
func PlanIDHasSuffix(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldHasSuffix(FieldPlanID, v))
}

// PlanIDIsNil applies the IsNil predicate on the "plan_id" field.
func PlanIDIsNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIsNull(FieldPlanID))
}

// PlanIDNotNil applies the NotNil predicate on the "plan_id" field.
func PlanIDNotNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotNull(FieldPlanID))
}

// PlanIDEqualFold applies the EqualFold predicate on the "plan_id" field.
func PlanIDEqualFold(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEqualFold(FieldPlanID, v))
}

// PlanIDContainsFold applies the ContainsFold predicate on the "plan_id" field.
func PlanIDContainsFold(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldContainsFold(FieldPlanID, v))
}

// PlanNameEQ applies the EQ predicate on the "plan_name" field.
func PlanNameEQ(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanName, v))
}

// PlanNameNEQ applies the NEQ predicate on the "plan_name" field.
func PlanNameNEQ(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldPlanName, v))
}

// PlanNameIn applies the In predicate on the "plan_name" field.
func PlanNameIn(vs ...string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldPlanName, vs...))
}

// PlanNameNotIn applies the NotIn predicate on the "plan_name" field.
func PlanNameNotIn(vs ...string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldPlanName, vs...))
}

// PlanNameGT applies the GT predicate on the "plan_name" field.
func PlanNameGT(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldPlanName, v))
}

// PlanNameGTE applies the GTE predicate on the "plan_name" field.
func PlanNameGTE(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldPlanName, v))
}

// PlanNameLT applies the LT predicate on the "plan_name" field.
func PlanNameLT(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldPlanName, v))
}

// PlanNameLTE applies the LTE predicate on the "plan_name" field.
func PlanNameLTE(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldPlanName, v))
}

// PlanNameContains applies the Contains predicate on the "plan_name" field.
func PlanNameContains(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldContains(FieldPlanName, v))
}

// PlanNameHasPrefix applies the HasPrefix predicate on the "plan_name" field.
func PlanNameHasPrefix(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldHasPrefix(FieldPlanName, v))
}

// PlanNameHasSuffix applies the HasSuffix predicate on the "plan_name" field.
func PlanNameHasSuffix(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldHasSuffix(FieldPlanName, v))
}

// PlanNameIsNil applies the IsNil predicate on the "plan_name" field.
func PlanNameIsNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIsNull(FieldPlanName))
}

// PlanNameNotNil applies the NotNil predicate on the "plan_name" field.
func PlanNameNotNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotNull(FieldPlanName))
}

// PlanNameEqualFold applies the EqualFold predicate on the "plan_name" field.
func PlanNameEqualFold(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEqualFold(FieldPlanName, v))
}

// PlanNameContainsFold applies the ContainsFold predicate on the "plan_name" field.
func PlanNameContainsFold(v string) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldContainsFold(FieldPlanName, v))
}

// PlanExpiresAtEQ applies the EQ predicate on the "plan_expires_at" field.
func PlanExpiresAtEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldPlanExpiresAt, v))
}

// PlanExpiresAtNEQ applies the NEQ predicate on the "plan_expires_at" field.
func PlanExpiresAtNEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldPlanExpiresAt, v))
}

// PlanExpiresAtIn applies the In predicate on the "plan_expires_at" field.
func PlanExpiresAtIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldPlanExpiresAt, vs...))
}

// PlanExpiresAtNotIn applies the NotIn predicate on the "plan_expires_at" field.
func PlanExpiresAtNotIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldPlanExpiresAt, vs...))
}

// PlanExpiresAtGT applies the GT predicate on the "plan_expires_at" field.
func PlanExpiresAtGT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldPlanExpiresAt, v))
}

// PlanExpiresAtGTE applies the GTE predicate on the "plan_expires_at" field.
func PlanExpiresAtGTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldPlanExpiresAt, v))
}

// PlanExpiresAtLT applies the LT predicate on the "plan_expires_at" field.
func PlanExpiresAtLT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldPlanExpiresAt, v))
}

// PlanExpiresAtLTE applies the LTE predicate on the "plan_expires_at" field.
func PlanExpiresAtLTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldPlanExpiresAt, v))
}

// PlanExpiresAtIsNil applies the IsNil predicate on the "plan_expires_at" field.
func PlanExpiresAtIsNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIsNull(FieldPlanExpiresAt))
}

// PlanExpiresAtNotNil applies the NotNil predicate on the "plan_expires_at" field.
func PlanExpiresAtNotNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotNull(FieldPlanExpiresAt))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldStatus, vs...))
}

// MonthlyLimitEQ applies the EQ predicate on the "monthly_limit" field.
func MonthlyLimitEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyLimit, v))
}

// MonthlyLimitNEQ applies the NEQ predicate on the "monthly_limit" field.
func MonthlyLimitNEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldMonthlyLimit, v))
}

// MonthlyLimitIn applies the In predicate on the "monthly_limit" field.
func MonthlyLimitIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldMonthlyLimit, vs...))
}

// MonthlyLimitNotIn applies the NotIn predicate on the "monthly_limit" field.
func MonthlyLimitNotIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldMonthlyLimit, vs...))
}

// MonthlyLimitGT applies the GT predicate on the "monthly_limit" field.
func MonthlyLimitGT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldMonthlyLimit, v))
}

// MonthlyLimitGTE applies the GTE predicate on the "monthly_limit" field.
func MonthlyLimitGTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldMonthlyLimit, v))
}

// MonthlyLimitLT applies the LT predicate on the "monthly_limit" field.
func MonthlyLimitLT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldMonthlyLimit, v))
}

// MonthlyLimitLTE applies the LTE predicate on the "monthly_limit" field.
func MonthlyLimitLTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldMonthlyLimit, v))
}

// MonthlyUsedEQ applies the EQ predicate on the "monthly_used" field.
func MonthlyUsedEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyUsed, v))
}

// MonthlyUsedNEQ applies the NEQ predicate on the "monthly_used" field.
func MonthlyUsedNEQ(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldMonthlyUsed, v))
}

// MonthlyUsedIn applies the In predicate on the "monthly_used" field.
func MonthlyUsedIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldMonthlyUsed, vs...))
}

// MonthlyUsedNotIn applies the NotIn predicate on the "monthly_used" field.
func MonthlyUsedNotIn(vs ...int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldMonthlyUsed, vs...))
}

// MonthlyUsedGT applies the GT predicate on the "monthly_used" field.
func MonthlyUsedGT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldMonthlyUsed, v))
}

// MonthlyUsedGTE applies the GTE predicate on the "monthly_used" field.
func MonthlyUsedGTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldMonthlyUsed, v))
}

// MonthlyUsedLT applies the LT predicate on the "monthly_used" field.
func MonthlyUsedLT(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldMonthlyUsed, v))
}

// MonthlyUsedLTE applies the LTE predicate on the "monthly_used" field.
func MonthlyUsedLTE(v int) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldMonthlyUsed, v))
}

// MonthlyResetAtEQ applies the EQ predicate on the "monthly_reset_at" field.
func MonthlyResetAtEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldMonthlyResetAt, v))
}

// MonthlyResetAtNEQ applies the NEQ predicate on the "monthly_reset_at" field.
func MonthlyResetAtNEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldMonthlyResetAt, v))
}

// MonthlyResetAtIn applies the In predicate on the "monthly_reset_at" field.
func MonthlyResetAtIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldMonthlyResetAt, vs...))
}

// MonthlyResetAtNotIn applies the NotIn predicate on the "monthly_reset_at" field.
func MonthlyResetAtNotIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldMonthlyResetAt, vs...))
}

// MonthlyResetAtGT applies the GT predicate on the "monthly_reset_at" field.
func MonthlyResetAtGT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldMonthlyResetAt, v))
}

// MonthlyResetAtGTE applies the GTE predicate on the "monthly_reset_at" field.
func MonthlyResetAtGTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldMonthlyResetAt, v))
}

// MonthlyResetAtLT applies the LT predicate on the "monthly_reset_at" field.
func MonthlyResetAtLT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldMonthlyResetAt, v))
}

// MonthlyResetAtLTE applies the LTE predicate on the "monthly_reset_at" field.
func MonthlyResetAtLTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldMonthlyResetAt, v))
}

// MonthlyResetAtIsNil applies the IsNil predicate on the "monthly_reset_at" field.
func MonthlyResetAtIsNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIsNull(FieldMonthlyResetAt))
}

// MonthlyResetAtNotNil applies the NotNil predicate on the "monthly_reset_at" field.
func MonthlyResetAtNotNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotNull(FieldMonthlyResetAt))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.UserCredit {
	return predicate.UserCredit(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.UserCredit) predicate.UserCredit {
	return predicate.UserCredit(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.UserCredit) predicate.UserCredit {
	return predicate.UserCredit(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.UserCredit) predicate.UserCredit {
	return predicate.UserCredit(sql.NotPredicates(p))
}

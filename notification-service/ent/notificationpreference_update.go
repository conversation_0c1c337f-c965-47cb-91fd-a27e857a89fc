// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationPreferenceUpdate is the builder for updating NotificationPreference entities.
type NotificationPreferenceUpdate struct {
	config
	hooks    []Hook
	mutation *NotificationPreferenceMutation
}

// Where appends a list predicates to the NotificationPreferenceUpdate builder.
func (npu *NotificationPreferenceUpdate) Where(ps ...predicate.NotificationPreference) *NotificationPreferenceUpdate {
	npu.mutation.Where(ps...)
	return npu
}

// SetUserID sets the "user_id" field.
func (npu *NotificationPreferenceUpdate) SetUserID(u uuid.UUID) *NotificationPreferenceUpdate {
	npu.mutation.SetUserID(u)
	return npu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableUserID(u *uuid.UUID) *NotificationPreferenceUpdate {
	if u != nil {
		npu.SetUserID(*u)
	}
	return npu
}

// SetEmailEnabled sets the "email_enabled" field.
func (npu *NotificationPreferenceUpdate) SetEmailEnabled(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetEmailEnabled(b)
	return npu
}

// SetNillableEmailEnabled sets the "email_enabled" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableEmailEnabled(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetEmailEnabled(*b)
	}
	return npu
}

// SetPushEnabled sets the "push_enabled" field.
func (npu *NotificationPreferenceUpdate) SetPushEnabled(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetPushEnabled(b)
	return npu
}

// SetNillablePushEnabled sets the "push_enabled" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillablePushEnabled(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetPushEnabled(*b)
	}
	return npu
}

// SetInAppEnabled sets the "in_app_enabled" field.
func (npu *NotificationPreferenceUpdate) SetInAppEnabled(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetInAppEnabled(b)
	return npu
}

// SetNillableInAppEnabled sets the "in_app_enabled" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableInAppEnabled(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetInAppEnabled(*b)
	}
	return npu
}

// SetSmsEnabled sets the "sms_enabled" field.
func (npu *NotificationPreferenceUpdate) SetSmsEnabled(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetSmsEnabled(b)
	return npu
}

// SetNillableSmsEnabled sets the "sms_enabled" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableSmsEnabled(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetSmsEnabled(*b)
	}
	return npu
}

// SetNotificationTypes sets the "notification_types" field.
func (npu *NotificationPreferenceUpdate) SetNotificationTypes(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetNotificationTypes(m)
	return npu
}

// ClearNotificationTypes clears the value of the "notification_types" field.
func (npu *NotificationPreferenceUpdate) ClearNotificationTypes() *NotificationPreferenceUpdate {
	npu.mutation.ClearNotificationTypes()
	return npu
}

// SetQuietHours sets the "quiet_hours" field.
func (npu *NotificationPreferenceUpdate) SetQuietHours(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetQuietHours(m)
	return npu
}

// ClearQuietHours clears the value of the "quiet_hours" field.
func (npu *NotificationPreferenceUpdate) ClearQuietHours() *NotificationPreferenceUpdate {
	npu.mutation.ClearQuietHours()
	return npu
}

// SetTimezone sets the "timezone" field.
func (npu *NotificationPreferenceUpdate) SetTimezone(s string) *NotificationPreferenceUpdate {
	npu.mutation.SetTimezone(s)
	return npu
}

// SetNillableTimezone sets the "timezone" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableTimezone(s *string) *NotificationPreferenceUpdate {
	if s != nil {
		npu.SetTimezone(*s)
	}
	return npu
}

// SetLanguage sets the "language" field.
func (npu *NotificationPreferenceUpdate) SetLanguage(s string) *NotificationPreferenceUpdate {
	npu.mutation.SetLanguage(s)
	return npu
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableLanguage(s *string) *NotificationPreferenceUpdate {
	if s != nil {
		npu.SetLanguage(*s)
	}
	return npu
}

// SetEmailSettings sets the "email_settings" field.
func (npu *NotificationPreferenceUpdate) SetEmailSettings(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetEmailSettings(m)
	return npu
}

// ClearEmailSettings clears the value of the "email_settings" field.
func (npu *NotificationPreferenceUpdate) ClearEmailSettings() *NotificationPreferenceUpdate {
	npu.mutation.ClearEmailSettings()
	return npu
}

// SetPushSettings sets the "push_settings" field.
func (npu *NotificationPreferenceUpdate) SetPushSettings(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetPushSettings(m)
	return npu
}

// ClearPushSettings clears the value of the "push_settings" field.
func (npu *NotificationPreferenceUpdate) ClearPushSettings() *NotificationPreferenceUpdate {
	npu.mutation.ClearPushSettings()
	return npu
}

// SetFrequencySettings sets the "frequency_settings" field.
func (npu *NotificationPreferenceUpdate) SetFrequencySettings(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetFrequencySettings(m)
	return npu
}

// ClearFrequencySettings clears the value of the "frequency_settings" field.
func (npu *NotificationPreferenceUpdate) ClearFrequencySettings() *NotificationPreferenceUpdate {
	npu.mutation.ClearFrequencySettings()
	return npu
}

// SetMarketingEmails sets the "marketing_emails" field.
func (npu *NotificationPreferenceUpdate) SetMarketingEmails(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetMarketingEmails(b)
	return npu
}

// SetNillableMarketingEmails sets the "marketing_emails" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableMarketingEmails(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetMarketingEmails(*b)
	}
	return npu
}

// SetProductUpdates sets the "product_updates" field.
func (npu *NotificationPreferenceUpdate) SetProductUpdates(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetProductUpdates(b)
	return npu
}

// SetNillableProductUpdates sets the "product_updates" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableProductUpdates(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetProductUpdates(*b)
	}
	return npu
}

// SetSecurityAlerts sets the "security_alerts" field.
func (npu *NotificationPreferenceUpdate) SetSecurityAlerts(b bool) *NotificationPreferenceUpdate {
	npu.mutation.SetSecurityAlerts(b)
	return npu
}

// SetNillableSecurityAlerts sets the "security_alerts" field if the given value is not nil.
func (npu *NotificationPreferenceUpdate) SetNillableSecurityAlerts(b *bool) *NotificationPreferenceUpdate {
	if b != nil {
		npu.SetSecurityAlerts(*b)
	}
	return npu
}

// SetMetadata sets the "metadata" field.
func (npu *NotificationPreferenceUpdate) SetMetadata(m map[string]interface{}) *NotificationPreferenceUpdate {
	npu.mutation.SetMetadata(m)
	return npu
}

// ClearMetadata clears the value of the "metadata" field.
func (npu *NotificationPreferenceUpdate) ClearMetadata() *NotificationPreferenceUpdate {
	npu.mutation.ClearMetadata()
	return npu
}

// SetUpdatedAt sets the "updated_at" field.
func (npu *NotificationPreferenceUpdate) SetUpdatedAt(t time.Time) *NotificationPreferenceUpdate {
	npu.mutation.SetUpdatedAt(t)
	return npu
}

// Mutation returns the NotificationPreferenceMutation object of the builder.
func (npu *NotificationPreferenceUpdate) Mutation() *NotificationPreferenceMutation {
	return npu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (npu *NotificationPreferenceUpdate) Save(ctx context.Context) (int, error) {
	npu.defaults()
	return withHooks(ctx, npu.sqlSave, npu.mutation, npu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (npu *NotificationPreferenceUpdate) SaveX(ctx context.Context) int {
	affected, err := npu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (npu *NotificationPreferenceUpdate) Exec(ctx context.Context) error {
	_, err := npu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (npu *NotificationPreferenceUpdate) ExecX(ctx context.Context) {
	if err := npu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (npu *NotificationPreferenceUpdate) defaults() {
	if _, ok := npu.mutation.UpdatedAt(); !ok {
		v := notificationpreference.UpdateDefaultUpdatedAt()
		npu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (npu *NotificationPreferenceUpdate) check() error {
	if v, ok := npu.mutation.Timezone(); ok {
		if err := notificationpreference.TimezoneValidator(v); err != nil {
			return &ValidationError{Name: "timezone", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.timezone": %w`, err)}
		}
	}
	if v, ok := npu.mutation.Language(); ok {
		if err := notificationpreference.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.language": %w`, err)}
		}
	}
	return nil
}

func (npu *NotificationPreferenceUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := npu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationpreference.Table, notificationpreference.Columns, sqlgraph.NewFieldSpec(notificationpreference.FieldID, field.TypeUUID))
	if ps := npu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := npu.mutation.UserID(); ok {
		_spec.SetField(notificationpreference.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := npu.mutation.EmailEnabled(); ok {
		_spec.SetField(notificationpreference.FieldEmailEnabled, field.TypeBool, value)
	}
	if value, ok := npu.mutation.PushEnabled(); ok {
		_spec.SetField(notificationpreference.FieldPushEnabled, field.TypeBool, value)
	}
	if value, ok := npu.mutation.InAppEnabled(); ok {
		_spec.SetField(notificationpreference.FieldInAppEnabled, field.TypeBool, value)
	}
	if value, ok := npu.mutation.SmsEnabled(); ok {
		_spec.SetField(notificationpreference.FieldSmsEnabled, field.TypeBool, value)
	}
	if value, ok := npu.mutation.NotificationTypes(); ok {
		_spec.SetField(notificationpreference.FieldNotificationTypes, field.TypeJSON, value)
	}
	if npu.mutation.NotificationTypesCleared() {
		_spec.ClearField(notificationpreference.FieldNotificationTypes, field.TypeJSON)
	}
	if value, ok := npu.mutation.QuietHours(); ok {
		_spec.SetField(notificationpreference.FieldQuietHours, field.TypeJSON, value)
	}
	if npu.mutation.QuietHoursCleared() {
		_spec.ClearField(notificationpreference.FieldQuietHours, field.TypeJSON)
	}
	if value, ok := npu.mutation.Timezone(); ok {
		_spec.SetField(notificationpreference.FieldTimezone, field.TypeString, value)
	}
	if value, ok := npu.mutation.Language(); ok {
		_spec.SetField(notificationpreference.FieldLanguage, field.TypeString, value)
	}
	if value, ok := npu.mutation.EmailSettings(); ok {
		_spec.SetField(notificationpreference.FieldEmailSettings, field.TypeJSON, value)
	}
	if npu.mutation.EmailSettingsCleared() {
		_spec.ClearField(notificationpreference.FieldEmailSettings, field.TypeJSON)
	}
	if value, ok := npu.mutation.PushSettings(); ok {
		_spec.SetField(notificationpreference.FieldPushSettings, field.TypeJSON, value)
	}
	if npu.mutation.PushSettingsCleared() {
		_spec.ClearField(notificationpreference.FieldPushSettings, field.TypeJSON)
	}
	if value, ok := npu.mutation.FrequencySettings(); ok {
		_spec.SetField(notificationpreference.FieldFrequencySettings, field.TypeJSON, value)
	}
	if npu.mutation.FrequencySettingsCleared() {
		_spec.ClearField(notificationpreference.FieldFrequencySettings, field.TypeJSON)
	}
	if value, ok := npu.mutation.MarketingEmails(); ok {
		_spec.SetField(notificationpreference.FieldMarketingEmails, field.TypeBool, value)
	}
	if value, ok := npu.mutation.ProductUpdates(); ok {
		_spec.SetField(notificationpreference.FieldProductUpdates, field.TypeBool, value)
	}
	if value, ok := npu.mutation.SecurityAlerts(); ok {
		_spec.SetField(notificationpreference.FieldSecurityAlerts, field.TypeBool, value)
	}
	if value, ok := npu.mutation.Metadata(); ok {
		_spec.SetField(notificationpreference.FieldMetadata, field.TypeJSON, value)
	}
	if npu.mutation.MetadataCleared() {
		_spec.ClearField(notificationpreference.FieldMetadata, field.TypeJSON)
	}
	if value, ok := npu.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationpreference.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, npu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationpreference.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	npu.mutation.done = true
	return n, nil
}

// NotificationPreferenceUpdateOne is the builder for updating a single NotificationPreference entity.
type NotificationPreferenceUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *NotificationPreferenceMutation
}

// SetUserID sets the "user_id" field.
func (npuo *NotificationPreferenceUpdateOne) SetUserID(u uuid.UUID) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetUserID(u)
	return npuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableUserID(u *uuid.UUID) *NotificationPreferenceUpdateOne {
	if u != nil {
		npuo.SetUserID(*u)
	}
	return npuo
}

// SetEmailEnabled sets the "email_enabled" field.
func (npuo *NotificationPreferenceUpdateOne) SetEmailEnabled(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetEmailEnabled(b)
	return npuo
}

// SetNillableEmailEnabled sets the "email_enabled" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableEmailEnabled(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetEmailEnabled(*b)
	}
	return npuo
}

// SetPushEnabled sets the "push_enabled" field.
func (npuo *NotificationPreferenceUpdateOne) SetPushEnabled(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetPushEnabled(b)
	return npuo
}

// SetNillablePushEnabled sets the "push_enabled" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillablePushEnabled(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetPushEnabled(*b)
	}
	return npuo
}

// SetInAppEnabled sets the "in_app_enabled" field.
func (npuo *NotificationPreferenceUpdateOne) SetInAppEnabled(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetInAppEnabled(b)
	return npuo
}

// SetNillableInAppEnabled sets the "in_app_enabled" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableInAppEnabled(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetInAppEnabled(*b)
	}
	return npuo
}

// SetSmsEnabled sets the "sms_enabled" field.
func (npuo *NotificationPreferenceUpdateOne) SetSmsEnabled(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetSmsEnabled(b)
	return npuo
}

// SetNillableSmsEnabled sets the "sms_enabled" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableSmsEnabled(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetSmsEnabled(*b)
	}
	return npuo
}

// SetNotificationTypes sets the "notification_types" field.
func (npuo *NotificationPreferenceUpdateOne) SetNotificationTypes(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetNotificationTypes(m)
	return npuo
}

// ClearNotificationTypes clears the value of the "notification_types" field.
func (npuo *NotificationPreferenceUpdateOne) ClearNotificationTypes() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearNotificationTypes()
	return npuo
}

// SetQuietHours sets the "quiet_hours" field.
func (npuo *NotificationPreferenceUpdateOne) SetQuietHours(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetQuietHours(m)
	return npuo
}

// ClearQuietHours clears the value of the "quiet_hours" field.
func (npuo *NotificationPreferenceUpdateOne) ClearQuietHours() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearQuietHours()
	return npuo
}

// SetTimezone sets the "timezone" field.
func (npuo *NotificationPreferenceUpdateOne) SetTimezone(s string) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetTimezone(s)
	return npuo
}

// SetNillableTimezone sets the "timezone" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableTimezone(s *string) *NotificationPreferenceUpdateOne {
	if s != nil {
		npuo.SetTimezone(*s)
	}
	return npuo
}

// SetLanguage sets the "language" field.
func (npuo *NotificationPreferenceUpdateOne) SetLanguage(s string) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetLanguage(s)
	return npuo
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableLanguage(s *string) *NotificationPreferenceUpdateOne {
	if s != nil {
		npuo.SetLanguage(*s)
	}
	return npuo
}

// SetEmailSettings sets the "email_settings" field.
func (npuo *NotificationPreferenceUpdateOne) SetEmailSettings(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetEmailSettings(m)
	return npuo
}

// ClearEmailSettings clears the value of the "email_settings" field.
func (npuo *NotificationPreferenceUpdateOne) ClearEmailSettings() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearEmailSettings()
	return npuo
}

// SetPushSettings sets the "push_settings" field.
func (npuo *NotificationPreferenceUpdateOne) SetPushSettings(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetPushSettings(m)
	return npuo
}

// ClearPushSettings clears the value of the "push_settings" field.
func (npuo *NotificationPreferenceUpdateOne) ClearPushSettings() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearPushSettings()
	return npuo
}

// SetFrequencySettings sets the "frequency_settings" field.
func (npuo *NotificationPreferenceUpdateOne) SetFrequencySettings(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetFrequencySettings(m)
	return npuo
}

// ClearFrequencySettings clears the value of the "frequency_settings" field.
func (npuo *NotificationPreferenceUpdateOne) ClearFrequencySettings() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearFrequencySettings()
	return npuo
}

// SetMarketingEmails sets the "marketing_emails" field.
func (npuo *NotificationPreferenceUpdateOne) SetMarketingEmails(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetMarketingEmails(b)
	return npuo
}

// SetNillableMarketingEmails sets the "marketing_emails" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableMarketingEmails(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetMarketingEmails(*b)
	}
	return npuo
}

// SetProductUpdates sets the "product_updates" field.
func (npuo *NotificationPreferenceUpdateOne) SetProductUpdates(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetProductUpdates(b)
	return npuo
}

// SetNillableProductUpdates sets the "product_updates" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableProductUpdates(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetProductUpdates(*b)
	}
	return npuo
}

// SetSecurityAlerts sets the "security_alerts" field.
func (npuo *NotificationPreferenceUpdateOne) SetSecurityAlerts(b bool) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetSecurityAlerts(b)
	return npuo
}

// SetNillableSecurityAlerts sets the "security_alerts" field if the given value is not nil.
func (npuo *NotificationPreferenceUpdateOne) SetNillableSecurityAlerts(b *bool) *NotificationPreferenceUpdateOne {
	if b != nil {
		npuo.SetSecurityAlerts(*b)
	}
	return npuo
}

// SetMetadata sets the "metadata" field.
func (npuo *NotificationPreferenceUpdateOne) SetMetadata(m map[string]interface{}) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetMetadata(m)
	return npuo
}

// ClearMetadata clears the value of the "metadata" field.
func (npuo *NotificationPreferenceUpdateOne) ClearMetadata() *NotificationPreferenceUpdateOne {
	npuo.mutation.ClearMetadata()
	return npuo
}

// SetUpdatedAt sets the "updated_at" field.
func (npuo *NotificationPreferenceUpdateOne) SetUpdatedAt(t time.Time) *NotificationPreferenceUpdateOne {
	npuo.mutation.SetUpdatedAt(t)
	return npuo
}

// Mutation returns the NotificationPreferenceMutation object of the builder.
func (npuo *NotificationPreferenceUpdateOne) Mutation() *NotificationPreferenceMutation {
	return npuo.mutation
}

// Where appends a list predicates to the NotificationPreferenceUpdate builder.
func (npuo *NotificationPreferenceUpdateOne) Where(ps ...predicate.NotificationPreference) *NotificationPreferenceUpdateOne {
	npuo.mutation.Where(ps...)
	return npuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (npuo *NotificationPreferenceUpdateOne) Select(field string, fields ...string) *NotificationPreferenceUpdateOne {
	npuo.fields = append([]string{field}, fields...)
	return npuo
}

// Save executes the query and returns the updated NotificationPreference entity.
func (npuo *NotificationPreferenceUpdateOne) Save(ctx context.Context) (*NotificationPreference, error) {
	npuo.defaults()
	return withHooks(ctx, npuo.sqlSave, npuo.mutation, npuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (npuo *NotificationPreferenceUpdateOne) SaveX(ctx context.Context) *NotificationPreference {
	node, err := npuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (npuo *NotificationPreferenceUpdateOne) Exec(ctx context.Context) error {
	_, err := npuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (npuo *NotificationPreferenceUpdateOne) ExecX(ctx context.Context) {
	if err := npuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (npuo *NotificationPreferenceUpdateOne) defaults() {
	if _, ok := npuo.mutation.UpdatedAt(); !ok {
		v := notificationpreference.UpdateDefaultUpdatedAt()
		npuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (npuo *NotificationPreferenceUpdateOne) check() error {
	if v, ok := npuo.mutation.Timezone(); ok {
		if err := notificationpreference.TimezoneValidator(v); err != nil {
			return &ValidationError{Name: "timezone", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.timezone": %w`, err)}
		}
	}
	if v, ok := npuo.mutation.Language(); ok {
		if err := notificationpreference.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.language": %w`, err)}
		}
	}
	return nil
}

func (npuo *NotificationPreferenceUpdateOne) sqlSave(ctx context.Context) (_node *NotificationPreference, err error) {
	if err := npuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notificationpreference.Table, notificationpreference.Columns, sqlgraph.NewFieldSpec(notificationpreference.FieldID, field.TypeUUID))
	id, ok := npuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "NotificationPreference.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := npuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationpreference.FieldID)
		for _, f := range fields {
			if !notificationpreference.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notificationpreference.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := npuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := npuo.mutation.UserID(); ok {
		_spec.SetField(notificationpreference.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := npuo.mutation.EmailEnabled(); ok {
		_spec.SetField(notificationpreference.FieldEmailEnabled, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.PushEnabled(); ok {
		_spec.SetField(notificationpreference.FieldPushEnabled, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.InAppEnabled(); ok {
		_spec.SetField(notificationpreference.FieldInAppEnabled, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.SmsEnabled(); ok {
		_spec.SetField(notificationpreference.FieldSmsEnabled, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.NotificationTypes(); ok {
		_spec.SetField(notificationpreference.FieldNotificationTypes, field.TypeJSON, value)
	}
	if npuo.mutation.NotificationTypesCleared() {
		_spec.ClearField(notificationpreference.FieldNotificationTypes, field.TypeJSON)
	}
	if value, ok := npuo.mutation.QuietHours(); ok {
		_spec.SetField(notificationpreference.FieldQuietHours, field.TypeJSON, value)
	}
	if npuo.mutation.QuietHoursCleared() {
		_spec.ClearField(notificationpreference.FieldQuietHours, field.TypeJSON)
	}
	if value, ok := npuo.mutation.Timezone(); ok {
		_spec.SetField(notificationpreference.FieldTimezone, field.TypeString, value)
	}
	if value, ok := npuo.mutation.Language(); ok {
		_spec.SetField(notificationpreference.FieldLanguage, field.TypeString, value)
	}
	if value, ok := npuo.mutation.EmailSettings(); ok {
		_spec.SetField(notificationpreference.FieldEmailSettings, field.TypeJSON, value)
	}
	if npuo.mutation.EmailSettingsCleared() {
		_spec.ClearField(notificationpreference.FieldEmailSettings, field.TypeJSON)
	}
	if value, ok := npuo.mutation.PushSettings(); ok {
		_spec.SetField(notificationpreference.FieldPushSettings, field.TypeJSON, value)
	}
	if npuo.mutation.PushSettingsCleared() {
		_spec.ClearField(notificationpreference.FieldPushSettings, field.TypeJSON)
	}
	if value, ok := npuo.mutation.FrequencySettings(); ok {
		_spec.SetField(notificationpreference.FieldFrequencySettings, field.TypeJSON, value)
	}
	if npuo.mutation.FrequencySettingsCleared() {
		_spec.ClearField(notificationpreference.FieldFrequencySettings, field.TypeJSON)
	}
	if value, ok := npuo.mutation.MarketingEmails(); ok {
		_spec.SetField(notificationpreference.FieldMarketingEmails, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.ProductUpdates(); ok {
		_spec.SetField(notificationpreference.FieldProductUpdates, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.SecurityAlerts(); ok {
		_spec.SetField(notificationpreference.FieldSecurityAlerts, field.TypeBool, value)
	}
	if value, ok := npuo.mutation.Metadata(); ok {
		_spec.SetField(notificationpreference.FieldMetadata, field.TypeJSON, value)
	}
	if npuo.mutation.MetadataCleared() {
		_spec.ClearField(notificationpreference.FieldMetadata, field.TypeJSON)
	}
	if value, ok := npuo.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationpreference.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &NotificationPreference{config: npuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, npuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notificationpreference.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	npuo.mutation.done = true
	return _node, nil
}

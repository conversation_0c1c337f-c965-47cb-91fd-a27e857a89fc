package models

import "time"

// AssetResponse represents an asset response
type AssetResponse struct {
	BaseModel
	UserID          string           `json:"user_id"`
	FolderID        string           `json:"folder_id,omitempty"`
	Name            string           `json:"name"`
	Description     string           `json:"description,omitempty"`
	Type            string           `json:"type"` // image, video, document, audio, other
	Status          string           `json:"status"` // uploading, processing, ready, failed
	FileInfo        FileInfo         `json:"file_info"`
	ImageInfo       *ImageInfo       `json:"image_info,omitempty"`
	VideoInfo       *VideoInfo       `json:"video_info,omitempty"`
	DocumentInfo    *DocumentInfo    `json:"document_info,omitempty"`
	StorageInfo     StorageInfo      `json:"storage_info"`
	SecurityInfo    SecurityInfo     `json:"security_info"`
	AccessControl   AccessControl    `json:"access_control"`
	ProcessingStatus *ProcessingStatus `json:"processing_status,omitempty"`
	Variants        []AssetVariant   `json:"variants,omitempty"`
	Tags            []string         `json:"tags,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	DownloadCount   int64            `json:"download_count"`
	ViewCount       int64            `json:"view_count"`
	LastAccessedAt  *time.Time       `json:"last_accessed_at,omitempty"`
}

// RequestUploadRequest represents upload request
type RequestUploadRequest struct {
	UserID      string            `json:"user_id"`
	FolderID    string            `json:"folder_id,omitempty"`
	Name        string            `json:"name" validate:"required"`
	Size        int64             `json:"size" validate:"required,min=1"`
	MimeType    string            `json:"mime_type" validate:"required"`
	Type        string            `json:"type,omitempty"` // auto-detected if not provided
	Description string            `json:"description,omitempty"`
	Tags        []string          `json:"tags,omitempty"`
	IsPublic    bool              `json:"is_public"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// UploadURLResponse represents upload URL response
type UploadURLResponse struct {
	UploadID    string            `json:"upload_id"`
	UploadURL   string            `json:"upload_url"`
	Method      string            `json:"method"` // PUT, POST
	Headers     map[string]string `json:"headers,omitempty"`
	Fields      map[string]string `json:"fields,omitempty"` // for POST uploads
	ExpiresAt   time.Time         `json:"expires_at"`
	MaxSize     int64             `json:"max_size"`
}

// ConfirmUploadRequest represents confirm upload request
type ConfirmUploadRequest struct {
	UploadID    string            `json:"upload_id"`
	UserID      string            `json:"user_id"`
	ETag        string            `json:"etag,omitempty"`
	Size        int64             `json:"size,omitempty"`
	Checksum    string            `json:"checksum,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateAssetRequest represents update asset request
type UpdateAssetRequest struct {
	AssetID     string            `json:"asset_id"`
	UserID      string            `json:"user_id"`
	Name        string            `json:"name,omitempty"`
	Description string            `json:"description,omitempty"`
	Tags        []string          `json:"tags,omitempty"`
	FolderID    string            `json:"folder_id,omitempty"`
	IsPublic    bool              `json:"is_public,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ListAssetsRequest represents list assets request
type ListAssetsRequest struct {
	UserID    string   `json:"user_id"`
	FolderID  string   `json:"folder_id,omitempty"`
	Type      string   `json:"type,omitempty"`
	Status    string   `json:"status,omitempty"`
	Search    string   `json:"search,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	Format    string   `json:"format,omitempty"`
	Page      int      `json:"page" validate:"min=1"`
	Limit     int      `json:"limit" validate:"min=1,max=100"`
	SortBy    string   `json:"sort_by,omitempty"`
	SortOrder string   `json:"sort_order,omitempty"`
	DateFrom  string   `json:"date_from,omitempty"`
	DateTo    string   `json:"date_to,omitempty"`
}

// ListAssetsResponse represents list assets response
type ListAssetsResponse struct {
	Assets     []AssetResponse `json:"assets"`
	Pagination PaginationMeta  `json:"pagination"`
	Filters    AssetFilters    `json:"filters"`
}

// AssetFilters represents available asset filters
type AssetFilters struct {
	Types    []string `json:"types"`
	Formats  []string `json:"formats"`
	Tags     []string `json:"tags"`
	Folders  []string `json:"folders"`
}

// BulkUploadRequest represents bulk upload request
type BulkUploadRequest struct {
	UserID   string                 `json:"user_id"`
	FolderID string                 `json:"folder_id,omitempty"`
	Assets   []RequestUploadRequest `json:"assets" validate:"required,min=1,max=50"`
}

// BulkUploadResponse represents bulk upload response
type BulkUploadResponse struct {
	BulkOperationResult
	Uploads []UploadURLResponse `json:"uploads"`
}

// BulkDeleteRequest represents bulk delete request
type BulkDeleteRequest struct {
	UserID   string   `json:"user_id"`
	AssetIDs []string `json:"asset_ids" validate:"required,min=1,max=100"`
}

// BulkMoveRequest represents bulk move request
type BulkMoveRequest struct {
	UserID     string   `json:"user_id"`
	AssetIDs   []string `json:"asset_ids" validate:"required,min=1,max=100"`
	FolderID   string   `json:"folder_id"`
}

// BulkTagRequest represents bulk tag request
type BulkTagRequest struct {
	UserID   string   `json:"user_id"`
	AssetIDs []string `json:"asset_ids" validate:"required,min=1,max=100"`
	Tags     []string `json:"tags" validate:"required,min=1"`
	Action   string   `json:"action" validate:"required,oneof=add remove replace"`
}

// ResizeImageRequest represents resize image request
type ResizeImageRequest struct {
	AssetID string `json:"asset_id"`
	UserID  string `json:"user_id"`
	Width   int    `json:"width" validate:"required,min=1,max=10000"`
	Height  int    `json:"height" validate:"required,min=1,max=10000"`
	Quality int    `json:"quality,omitempty" validate:"min=1,max=100"`
	Format  string `json:"format,omitempty"`
}

// OptimizeImageRequest represents optimize image request
type OptimizeImageRequest struct {
	AssetID string `json:"asset_id"`
	UserID  string `json:"user_id"`
	Quality int    `json:"quality,omitempty" validate:"min=1,max=100"`
	Format  string `json:"format,omitempty"`
}

// ConvertImageFormatRequest represents convert image format request
type ConvertImageFormatRequest struct {
	AssetID string `json:"asset_id"`
	UserID  string `json:"user_id"`
	Format  string `json:"format" validate:"required,oneof=jpeg png webp avif"`
	Quality int    `json:"quality,omitempty" validate:"min=1,max=100"`
}

// SearchAssetsRequest represents search assets request
type SearchAssetsRequest struct {
	UserID    string   `json:"user_id"`
	Query     string   `json:"query" validate:"required,min=1"`
	Type      string   `json:"type,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	FolderID  string   `json:"folder_id,omitempty"`
	Page      int      `json:"page" validate:"min=1"`
	Limit     int      `json:"limit" validate:"min=1,max=100"`
	SortBy    string   `json:"sort_by,omitempty"`
	SortOrder string   `json:"sort_order,omitempty"`
}

// VisualSearchRequest represents visual search request
type VisualSearchRequest struct {
	UserID      string `json:"user_id"`
	ImageAssetID string `json:"image_asset_id,omitempty"`
	ImageURL    string `json:"image_url,omitempty"`
	Similarity  float64 `json:"similarity,omitempty" validate:"min=0,max=1"`
	Limit       int    `json:"limit" validate:"min=1,max=50"`
}

// VisualSearchResponse represents visual search response
type VisualSearchResponse struct {
	Query   string                `json:"query"`
	Results []VisualSearchResult  `json:"results"`
}

// VisualSearchResult represents a visual search result
type VisualSearchResult struct {
	Asset      AssetResponse `json:"asset"`
	Similarity float64       `json:"similarity"`
	Features   []string      `json:"features,omitempty"`
}

// StorageAnalyticsResponse represents storage analytics
type StorageAnalyticsResponse struct {
	TotalSize       int64                    `json:"total_size"`
	TotalAssets     int64                    `json:"total_assets"`
	ByType          map[string]StorageStats  `json:"by_type"`
	ByFolder        map[string]StorageStats  `json:"by_folder"`
	RecentUploads   []AssetResponse          `json:"recent_uploads"`
	LargestAssets   []AssetResponse          `json:"largest_assets"`
	QuotaUsed       int64                    `json:"quota_used"`
	QuotaLimit      int64                    `json:"quota_limit"`
	QuotaPercentage float64                  `json:"quota_percentage"`
}

// StorageStats represents storage statistics
type StorageStats struct {
	Count int64 `json:"count"`
	Size  int64 `json:"size"`
}

// UsageAnalyticsResponse represents usage analytics
type UsageAnalyticsResponse struct {
	Period          string                   `json:"period"`
	TotalDownloads  int64                    `json:"total_downloads"`
	TotalViews      int64                    `json:"total_views"`
	TotalUploads    int64                    `json:"total_uploads"`
	Timeline        []UsageTimelinePoint     `json:"timeline"`
	TopAssets       []AssetUsageStats        `json:"top_assets"`
	ByType          map[string]int64         `json:"by_type"`
}

// UsageTimelinePoint represents a point in usage timeline
type UsageTimelinePoint struct {
	Date      string `json:"date"`
	Downloads int64  `json:"downloads"`
	Views     int64  `json:"views"`
	Uploads   int64  `json:"uploads"`
}

// AssetUsageStats represents asset usage statistics
type AssetUsageStats struct {
	Asset     AssetResponse `json:"asset"`
	Downloads int64         `json:"downloads"`
	Views     int64         `json:"views"`
}

// PerformanceAnalyticsResponse represents performance analytics
type PerformanceAnalyticsResponse struct {
	Period              string                      `json:"period"`
	AverageUploadTime   float64                     `json:"average_upload_time"`
	AverageProcessTime  float64                     `json:"average_process_time"`
	SuccessRate         float64                     `json:"success_rate"`
	ErrorRate           float64                     `json:"error_rate"`
	Timeline            []PerformanceTimelinePoint  `json:"timeline"`
	ByType              map[string]PerformanceStats `json:"by_type"`
}

// PerformanceTimelinePoint represents a point in performance timeline
type PerformanceTimelinePoint struct {
	Date              string  `json:"date"`
	AverageUploadTime float64 `json:"average_upload_time"`
	AverageProcessTime float64 `json:"average_process_time"`
	SuccessRate       float64 `json:"success_rate"`
}

// PerformanceStats represents performance statistics
type PerformanceStats struct {
	AverageUploadTime  float64 `json:"average_upload_time"`
	AverageProcessTime float64 `json:"average_process_time"`
	SuccessRate        float64 `json:"success_rate"`
	Count              int64   `json:"count"`
}

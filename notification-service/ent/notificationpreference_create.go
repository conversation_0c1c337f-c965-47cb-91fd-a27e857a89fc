// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
)

// NotificationPreferenceCreate is the builder for creating a NotificationPreference entity.
type NotificationPreferenceCreate struct {
	config
	mutation *NotificationPreferenceMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (npc *NotificationPreferenceCreate) SetUserID(u uuid.UUID) *NotificationPreferenceCreate {
	npc.mutation.SetUserID(u)
	return npc
}

// SetEmailEnabled sets the "email_enabled" field.
func (npc *NotificationPreferenceCreate) SetEmailEnabled(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetEmailEnabled(b)
	return npc
}

// SetNillableEmailEnabled sets the "email_enabled" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableEmailEnabled(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetEmailEnabled(*b)
	}
	return npc
}

// SetPushEnabled sets the "push_enabled" field.
func (npc *NotificationPreferenceCreate) SetPushEnabled(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetPushEnabled(b)
	return npc
}

// SetNillablePushEnabled sets the "push_enabled" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillablePushEnabled(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetPushEnabled(*b)
	}
	return npc
}

// SetInAppEnabled sets the "in_app_enabled" field.
func (npc *NotificationPreferenceCreate) SetInAppEnabled(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetInAppEnabled(b)
	return npc
}

// SetNillableInAppEnabled sets the "in_app_enabled" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableInAppEnabled(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetInAppEnabled(*b)
	}
	return npc
}

// SetSmsEnabled sets the "sms_enabled" field.
func (npc *NotificationPreferenceCreate) SetSmsEnabled(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetSmsEnabled(b)
	return npc
}

// SetNillableSmsEnabled sets the "sms_enabled" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableSmsEnabled(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetSmsEnabled(*b)
	}
	return npc
}

// SetNotificationTypes sets the "notification_types" field.
func (npc *NotificationPreferenceCreate) SetNotificationTypes(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetNotificationTypes(m)
	return npc
}

// SetQuietHours sets the "quiet_hours" field.
func (npc *NotificationPreferenceCreate) SetQuietHours(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetQuietHours(m)
	return npc
}

// SetTimezone sets the "timezone" field.
func (npc *NotificationPreferenceCreate) SetTimezone(s string) *NotificationPreferenceCreate {
	npc.mutation.SetTimezone(s)
	return npc
}

// SetNillableTimezone sets the "timezone" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableTimezone(s *string) *NotificationPreferenceCreate {
	if s != nil {
		npc.SetTimezone(*s)
	}
	return npc
}

// SetLanguage sets the "language" field.
func (npc *NotificationPreferenceCreate) SetLanguage(s string) *NotificationPreferenceCreate {
	npc.mutation.SetLanguage(s)
	return npc
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableLanguage(s *string) *NotificationPreferenceCreate {
	if s != nil {
		npc.SetLanguage(*s)
	}
	return npc
}

// SetEmailSettings sets the "email_settings" field.
func (npc *NotificationPreferenceCreate) SetEmailSettings(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetEmailSettings(m)
	return npc
}

// SetPushSettings sets the "push_settings" field.
func (npc *NotificationPreferenceCreate) SetPushSettings(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetPushSettings(m)
	return npc
}

// SetFrequencySettings sets the "frequency_settings" field.
func (npc *NotificationPreferenceCreate) SetFrequencySettings(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetFrequencySettings(m)
	return npc
}

// SetMarketingEmails sets the "marketing_emails" field.
func (npc *NotificationPreferenceCreate) SetMarketingEmails(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetMarketingEmails(b)
	return npc
}

// SetNillableMarketingEmails sets the "marketing_emails" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableMarketingEmails(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetMarketingEmails(*b)
	}
	return npc
}

// SetProductUpdates sets the "product_updates" field.
func (npc *NotificationPreferenceCreate) SetProductUpdates(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetProductUpdates(b)
	return npc
}

// SetNillableProductUpdates sets the "product_updates" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableProductUpdates(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetProductUpdates(*b)
	}
	return npc
}

// SetSecurityAlerts sets the "security_alerts" field.
func (npc *NotificationPreferenceCreate) SetSecurityAlerts(b bool) *NotificationPreferenceCreate {
	npc.mutation.SetSecurityAlerts(b)
	return npc
}

// SetNillableSecurityAlerts sets the "security_alerts" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableSecurityAlerts(b *bool) *NotificationPreferenceCreate {
	if b != nil {
		npc.SetSecurityAlerts(*b)
	}
	return npc
}

// SetMetadata sets the "metadata" field.
func (npc *NotificationPreferenceCreate) SetMetadata(m map[string]interface{}) *NotificationPreferenceCreate {
	npc.mutation.SetMetadata(m)
	return npc
}

// SetCreatedAt sets the "created_at" field.
func (npc *NotificationPreferenceCreate) SetCreatedAt(t time.Time) *NotificationPreferenceCreate {
	npc.mutation.SetCreatedAt(t)
	return npc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableCreatedAt(t *time.Time) *NotificationPreferenceCreate {
	if t != nil {
		npc.SetCreatedAt(*t)
	}
	return npc
}

// SetUpdatedAt sets the "updated_at" field.
func (npc *NotificationPreferenceCreate) SetUpdatedAt(t time.Time) *NotificationPreferenceCreate {
	npc.mutation.SetUpdatedAt(t)
	return npc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableUpdatedAt(t *time.Time) *NotificationPreferenceCreate {
	if t != nil {
		npc.SetUpdatedAt(*t)
	}
	return npc
}

// SetID sets the "id" field.
func (npc *NotificationPreferenceCreate) SetID(u uuid.UUID) *NotificationPreferenceCreate {
	npc.mutation.SetID(u)
	return npc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (npc *NotificationPreferenceCreate) SetNillableID(u *uuid.UUID) *NotificationPreferenceCreate {
	if u != nil {
		npc.SetID(*u)
	}
	return npc
}

// Mutation returns the NotificationPreferenceMutation object of the builder.
func (npc *NotificationPreferenceCreate) Mutation() *NotificationPreferenceMutation {
	return npc.mutation
}

// Save creates the NotificationPreference in the database.
func (npc *NotificationPreferenceCreate) Save(ctx context.Context) (*NotificationPreference, error) {
	npc.defaults()
	return withHooks(ctx, npc.sqlSave, npc.mutation, npc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (npc *NotificationPreferenceCreate) SaveX(ctx context.Context) *NotificationPreference {
	v, err := npc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (npc *NotificationPreferenceCreate) Exec(ctx context.Context) error {
	_, err := npc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (npc *NotificationPreferenceCreate) ExecX(ctx context.Context) {
	if err := npc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (npc *NotificationPreferenceCreate) defaults() {
	if _, ok := npc.mutation.EmailEnabled(); !ok {
		v := notificationpreference.DefaultEmailEnabled
		npc.mutation.SetEmailEnabled(v)
	}
	if _, ok := npc.mutation.PushEnabled(); !ok {
		v := notificationpreference.DefaultPushEnabled
		npc.mutation.SetPushEnabled(v)
	}
	if _, ok := npc.mutation.InAppEnabled(); !ok {
		v := notificationpreference.DefaultInAppEnabled
		npc.mutation.SetInAppEnabled(v)
	}
	if _, ok := npc.mutation.SmsEnabled(); !ok {
		v := notificationpreference.DefaultSmsEnabled
		npc.mutation.SetSmsEnabled(v)
	}
	if _, ok := npc.mutation.Timezone(); !ok {
		v := notificationpreference.DefaultTimezone
		npc.mutation.SetTimezone(v)
	}
	if _, ok := npc.mutation.Language(); !ok {
		v := notificationpreference.DefaultLanguage
		npc.mutation.SetLanguage(v)
	}
	if _, ok := npc.mutation.MarketingEmails(); !ok {
		v := notificationpreference.DefaultMarketingEmails
		npc.mutation.SetMarketingEmails(v)
	}
	if _, ok := npc.mutation.ProductUpdates(); !ok {
		v := notificationpreference.DefaultProductUpdates
		npc.mutation.SetProductUpdates(v)
	}
	if _, ok := npc.mutation.SecurityAlerts(); !ok {
		v := notificationpreference.DefaultSecurityAlerts
		npc.mutation.SetSecurityAlerts(v)
	}
	if _, ok := npc.mutation.CreatedAt(); !ok {
		v := notificationpreference.DefaultCreatedAt()
		npc.mutation.SetCreatedAt(v)
	}
	if _, ok := npc.mutation.UpdatedAt(); !ok {
		v := notificationpreference.DefaultUpdatedAt()
		npc.mutation.SetUpdatedAt(v)
	}
	if _, ok := npc.mutation.ID(); !ok {
		v := notificationpreference.DefaultID()
		npc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (npc *NotificationPreferenceCreate) check() error {
	if _, ok := npc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "NotificationPreference.user_id"`)}
	}
	if _, ok := npc.mutation.EmailEnabled(); !ok {
		return &ValidationError{Name: "email_enabled", err: errors.New(`ent: missing required field "NotificationPreference.email_enabled"`)}
	}
	if _, ok := npc.mutation.PushEnabled(); !ok {
		return &ValidationError{Name: "push_enabled", err: errors.New(`ent: missing required field "NotificationPreference.push_enabled"`)}
	}
	if _, ok := npc.mutation.InAppEnabled(); !ok {
		return &ValidationError{Name: "in_app_enabled", err: errors.New(`ent: missing required field "NotificationPreference.in_app_enabled"`)}
	}
	if _, ok := npc.mutation.SmsEnabled(); !ok {
		return &ValidationError{Name: "sms_enabled", err: errors.New(`ent: missing required field "NotificationPreference.sms_enabled"`)}
	}
	if _, ok := npc.mutation.Timezone(); !ok {
		return &ValidationError{Name: "timezone", err: errors.New(`ent: missing required field "NotificationPreference.timezone"`)}
	}
	if v, ok := npc.mutation.Timezone(); ok {
		if err := notificationpreference.TimezoneValidator(v); err != nil {
			return &ValidationError{Name: "timezone", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.timezone": %w`, err)}
		}
	}
	if _, ok := npc.mutation.Language(); !ok {
		return &ValidationError{Name: "language", err: errors.New(`ent: missing required field "NotificationPreference.language"`)}
	}
	if v, ok := npc.mutation.Language(); ok {
		if err := notificationpreference.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "NotificationPreference.language": %w`, err)}
		}
	}
	if _, ok := npc.mutation.MarketingEmails(); !ok {
		return &ValidationError{Name: "marketing_emails", err: errors.New(`ent: missing required field "NotificationPreference.marketing_emails"`)}
	}
	if _, ok := npc.mutation.ProductUpdates(); !ok {
		return &ValidationError{Name: "product_updates", err: errors.New(`ent: missing required field "NotificationPreference.product_updates"`)}
	}
	if _, ok := npc.mutation.SecurityAlerts(); !ok {
		return &ValidationError{Name: "security_alerts", err: errors.New(`ent: missing required field "NotificationPreference.security_alerts"`)}
	}
	if _, ok := npc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "NotificationPreference.created_at"`)}
	}
	if _, ok := npc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "NotificationPreference.updated_at"`)}
	}
	return nil
}

func (npc *NotificationPreferenceCreate) sqlSave(ctx context.Context) (*NotificationPreference, error) {
	if err := npc.check(); err != nil {
		return nil, err
	}
	_node, _spec := npc.createSpec()
	if err := sqlgraph.CreateNode(ctx, npc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	npc.mutation.id = &_node.ID
	npc.mutation.done = true
	return _node, nil
}

func (npc *NotificationPreferenceCreate) createSpec() (*NotificationPreference, *sqlgraph.CreateSpec) {
	var (
		_node = &NotificationPreference{config: npc.config}
		_spec = sqlgraph.NewCreateSpec(notificationpreference.Table, sqlgraph.NewFieldSpec(notificationpreference.FieldID, field.TypeUUID))
	)
	if id, ok := npc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := npc.mutation.UserID(); ok {
		_spec.SetField(notificationpreference.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := npc.mutation.EmailEnabled(); ok {
		_spec.SetField(notificationpreference.FieldEmailEnabled, field.TypeBool, value)
		_node.EmailEnabled = value
	}
	if value, ok := npc.mutation.PushEnabled(); ok {
		_spec.SetField(notificationpreference.FieldPushEnabled, field.TypeBool, value)
		_node.PushEnabled = value
	}
	if value, ok := npc.mutation.InAppEnabled(); ok {
		_spec.SetField(notificationpreference.FieldInAppEnabled, field.TypeBool, value)
		_node.InAppEnabled = value
	}
	if value, ok := npc.mutation.SmsEnabled(); ok {
		_spec.SetField(notificationpreference.FieldSmsEnabled, field.TypeBool, value)
		_node.SmsEnabled = value
	}
	if value, ok := npc.mutation.NotificationTypes(); ok {
		_spec.SetField(notificationpreference.FieldNotificationTypes, field.TypeJSON, value)
		_node.NotificationTypes = value
	}
	if value, ok := npc.mutation.QuietHours(); ok {
		_spec.SetField(notificationpreference.FieldQuietHours, field.TypeJSON, value)
		_node.QuietHours = value
	}
	if value, ok := npc.mutation.Timezone(); ok {
		_spec.SetField(notificationpreference.FieldTimezone, field.TypeString, value)
		_node.Timezone = value
	}
	if value, ok := npc.mutation.Language(); ok {
		_spec.SetField(notificationpreference.FieldLanguage, field.TypeString, value)
		_node.Language = value
	}
	if value, ok := npc.mutation.EmailSettings(); ok {
		_spec.SetField(notificationpreference.FieldEmailSettings, field.TypeJSON, value)
		_node.EmailSettings = value
	}
	if value, ok := npc.mutation.PushSettings(); ok {
		_spec.SetField(notificationpreference.FieldPushSettings, field.TypeJSON, value)
		_node.PushSettings = value
	}
	if value, ok := npc.mutation.FrequencySettings(); ok {
		_spec.SetField(notificationpreference.FieldFrequencySettings, field.TypeJSON, value)
		_node.FrequencySettings = value
	}
	if value, ok := npc.mutation.MarketingEmails(); ok {
		_spec.SetField(notificationpreference.FieldMarketingEmails, field.TypeBool, value)
		_node.MarketingEmails = value
	}
	if value, ok := npc.mutation.ProductUpdates(); ok {
		_spec.SetField(notificationpreference.FieldProductUpdates, field.TypeBool, value)
		_node.ProductUpdates = value
	}
	if value, ok := npc.mutation.SecurityAlerts(); ok {
		_spec.SetField(notificationpreference.FieldSecurityAlerts, field.TypeBool, value)
		_node.SecurityAlerts = value
	}
	if value, ok := npc.mutation.Metadata(); ok {
		_spec.SetField(notificationpreference.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := npc.mutation.CreatedAt(); ok {
		_spec.SetField(notificationpreference.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := npc.mutation.UpdatedAt(); ok {
		_spec.SetField(notificationpreference.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// NotificationPreferenceCreateBulk is the builder for creating many NotificationPreference entities in bulk.
type NotificationPreferenceCreateBulk struct {
	config
	err      error
	builders []*NotificationPreferenceCreate
}

// Save creates the NotificationPreference entities in the database.
func (npcb *NotificationPreferenceCreateBulk) Save(ctx context.Context) ([]*NotificationPreference, error) {
	if npcb.err != nil {
		return nil, npcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(npcb.builders))
	nodes := make([]*NotificationPreference, len(npcb.builders))
	mutators := make([]Mutator, len(npcb.builders))
	for i := range npcb.builders {
		func(i int, root context.Context) {
			builder := npcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationPreferenceMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, npcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, npcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, npcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (npcb *NotificationPreferenceCreateBulk) SaveX(ctx context.Context) []*NotificationPreference {
	v, err := npcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (npcb *NotificationPreferenceCreateBulk) Exec(ctx context.Context) error {
	_, err := npcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (npcb *NotificationPreferenceCreateBulk) ExecX(ctx context.Context) {
	if err := npcb.Exec(ctx); err != nil {
		panic(err)
	}
}

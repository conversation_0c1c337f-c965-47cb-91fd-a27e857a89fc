// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: asset/v1/asset.proto

package assetv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AssetServiceClient is the client API for AssetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetServiceClient interface {
	// Upload operations
	RequestUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*UploadResponse, error)
	ConfirmUpload(ctx context.Context, in *ConfirmUploadRequest, opts ...grpc.CallOption) (*ConfirmUploadResponse, error)
	// Asset management operations
	GetAsset(ctx context.Context, in *GetAssetRequest, opts ...grpc.CallOption) (*Asset, error)
	ListAssets(ctx context.Context, in *ListAssetsRequest, opts ...grpc.CallOption) (*ListAssetsResponse, error)
	UpdateAsset(ctx context.Context, in *UpdateAssetRequest, opts ...grpc.CallOption) (*Asset, error)
	DeleteAsset(ctx context.Context, in *DeleteAssetRequest, opts ...grpc.CallOption) (*Empty, error)
	// Validation and processing operations
	ValidateAsset(ctx context.Context, in *ValidateAssetRequest, opts ...grpc.CallOption) (*ValidationResult, error)
	ProcessAsset(ctx context.Context, in *ProcessAssetRequest, opts ...grpc.CallOption) (*ProcessAssetResponse, error)
	GetProcessingStatus(ctx context.Context, in *GetProcessingStatusRequest, opts ...grpc.CallOption) (*ProcessingStatus, error)
	// Download operations
	GetDownloadUrl(ctx context.Context, in *GetDownloadUrlRequest, opts ...grpc.CallOption) (*GetDownloadUrlResponse, error)
	// Utility operations
	CheckDuplicate(ctx context.Context, in *DuplicateCheckRequest, opts ...grpc.CallOption) (*DuplicateCheckResponse, error)
	GetStorageStats(ctx context.Context, in *GetStorageStatsRequest, opts ...grpc.CallOption) (*StorageStats, error)
}

type assetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetServiceClient(cc grpc.ClientConnInterface) AssetServiceClient {
	return &assetServiceClient{cc}
}

func (c *assetServiceClient) RequestUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*UploadResponse, error) {
	out := new(UploadResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/RequestUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) ConfirmUpload(ctx context.Context, in *ConfirmUploadRequest, opts ...grpc.CallOption) (*ConfirmUploadResponse, error) {
	out := new(ConfirmUploadResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/ConfirmUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) GetAsset(ctx context.Context, in *GetAssetRequest, opts ...grpc.CallOption) (*Asset, error) {
	out := new(Asset)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/GetAsset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) ListAssets(ctx context.Context, in *ListAssetsRequest, opts ...grpc.CallOption) (*ListAssetsResponse, error) {
	out := new(ListAssetsResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/ListAssets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) UpdateAsset(ctx context.Context, in *UpdateAssetRequest, opts ...grpc.CallOption) (*Asset, error) {
	out := new(Asset)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/UpdateAsset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) DeleteAsset(ctx context.Context, in *DeleteAssetRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/DeleteAsset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) ValidateAsset(ctx context.Context, in *ValidateAssetRequest, opts ...grpc.CallOption) (*ValidationResult, error) {
	out := new(ValidationResult)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/ValidateAsset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) ProcessAsset(ctx context.Context, in *ProcessAssetRequest, opts ...grpc.CallOption) (*ProcessAssetResponse, error) {
	out := new(ProcessAssetResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/ProcessAsset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) GetProcessingStatus(ctx context.Context, in *GetProcessingStatusRequest, opts ...grpc.CallOption) (*ProcessingStatus, error) {
	out := new(ProcessingStatus)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/GetProcessingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) GetDownloadUrl(ctx context.Context, in *GetDownloadUrlRequest, opts ...grpc.CallOption) (*GetDownloadUrlResponse, error) {
	out := new(GetDownloadUrlResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/GetDownloadUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) CheckDuplicate(ctx context.Context, in *DuplicateCheckRequest, opts ...grpc.CallOption) (*DuplicateCheckResponse, error) {
	out := new(DuplicateCheckResponse)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/CheckDuplicate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) GetStorageStats(ctx context.Context, in *GetStorageStatsRequest, opts ...grpc.CallOption) (*StorageStats, error) {
	out := new(StorageStats)
	err := c.cc.Invoke(ctx, "/asset.v1.AssetService/GetStorageStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetServiceServer is the server API for AssetService service.
// All implementations must embed UnimplementedAssetServiceServer
// for forward compatibility
type AssetServiceServer interface {
	// Upload operations
	RequestUpload(context.Context, *UploadRequest) (*UploadResponse, error)
	ConfirmUpload(context.Context, *ConfirmUploadRequest) (*ConfirmUploadResponse, error)
	// Asset management operations
	GetAsset(context.Context, *GetAssetRequest) (*Asset, error)
	ListAssets(context.Context, *ListAssetsRequest) (*ListAssetsResponse, error)
	UpdateAsset(context.Context, *UpdateAssetRequest) (*Asset, error)
	DeleteAsset(context.Context, *DeleteAssetRequest) (*Empty, error)
	// Validation and processing operations
	ValidateAsset(context.Context, *ValidateAssetRequest) (*ValidationResult, error)
	ProcessAsset(context.Context, *ProcessAssetRequest) (*ProcessAssetResponse, error)
	GetProcessingStatus(context.Context, *GetProcessingStatusRequest) (*ProcessingStatus, error)
	// Download operations
	GetDownloadUrl(context.Context, *GetDownloadUrlRequest) (*GetDownloadUrlResponse, error)
	// Utility operations
	CheckDuplicate(context.Context, *DuplicateCheckRequest) (*DuplicateCheckResponse, error)
	GetStorageStats(context.Context, *GetStorageStatsRequest) (*StorageStats, error)
	mustEmbedUnimplementedAssetServiceServer()
}

// UnimplementedAssetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAssetServiceServer struct {
}

func (UnimplementedAssetServiceServer) RequestUpload(context.Context, *UploadRequest) (*UploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestUpload not implemented")
}
func (UnimplementedAssetServiceServer) ConfirmUpload(context.Context, *ConfirmUploadRequest) (*ConfirmUploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmUpload not implemented")
}
func (UnimplementedAssetServiceServer) GetAsset(context.Context, *GetAssetRequest) (*Asset, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAsset not implemented")
}
func (UnimplementedAssetServiceServer) ListAssets(context.Context, *ListAssetsRequest) (*ListAssetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssets not implemented")
}
func (UnimplementedAssetServiceServer) UpdateAsset(context.Context, *UpdateAssetRequest) (*Asset, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAsset not implemented")
}
func (UnimplementedAssetServiceServer) DeleteAsset(context.Context, *DeleteAssetRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAsset not implemented")
}
func (UnimplementedAssetServiceServer) ValidateAsset(context.Context, *ValidateAssetRequest) (*ValidationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAsset not implemented")
}
func (UnimplementedAssetServiceServer) ProcessAsset(context.Context, *ProcessAssetRequest) (*ProcessAssetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAsset not implemented")
}
func (UnimplementedAssetServiceServer) GetProcessingStatus(context.Context, *GetProcessingStatusRequest) (*ProcessingStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessingStatus not implemented")
}
func (UnimplementedAssetServiceServer) GetDownloadUrl(context.Context, *GetDownloadUrlRequest) (*GetDownloadUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadUrl not implemented")
}
func (UnimplementedAssetServiceServer) CheckDuplicate(context.Context, *DuplicateCheckRequest) (*DuplicateCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDuplicate not implemented")
}
func (UnimplementedAssetServiceServer) GetStorageStats(context.Context, *GetStorageStatsRequest) (*StorageStats, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStorageStats not implemented")
}
func (UnimplementedAssetServiceServer) mustEmbedUnimplementedAssetServiceServer() {}

// UnsafeAssetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetServiceServer will
// result in compilation errors.
type UnsafeAssetServiceServer interface {
	mustEmbedUnimplementedAssetServiceServer()
}

func RegisterAssetServiceServer(s grpc.ServiceRegistrar, srv AssetServiceServer) {
	s.RegisterService(&AssetService_ServiceDesc, srv)
}

func _AssetService_RequestUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).RequestUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/RequestUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).RequestUpload(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_ConfirmUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).ConfirmUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/ConfirmUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).ConfirmUpload(ctx, req.(*ConfirmUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_GetAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).GetAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/GetAsset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).GetAsset(ctx, req.(*GetAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_ListAssets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAssetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).ListAssets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/ListAssets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).ListAssets(ctx, req.(*ListAssetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_UpdateAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).UpdateAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/UpdateAsset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).UpdateAsset(ctx, req.(*UpdateAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_DeleteAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).DeleteAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/DeleteAsset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).DeleteAsset(ctx, req.(*DeleteAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_ValidateAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).ValidateAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/ValidateAsset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).ValidateAsset(ctx, req.(*ValidateAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_ProcessAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).ProcessAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/ProcessAsset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).ProcessAsset(ctx, req.(*ProcessAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_GetProcessingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProcessingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).GetProcessingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/GetProcessingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).GetProcessingStatus(ctx, req.(*GetProcessingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_GetDownloadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDownloadUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).GetDownloadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/GetDownloadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).GetDownloadUrl(ctx, req.(*GetDownloadUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_CheckDuplicate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DuplicateCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).CheckDuplicate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/CheckDuplicate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).CheckDuplicate(ctx, req.(*DuplicateCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_GetStorageStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStorageStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).GetStorageStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/asset.v1.AssetService/GetStorageStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).GetStorageStats(ctx, req.(*GetStorageStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AssetService_ServiceDesc is the grpc.ServiceDesc for AssetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asset.v1.AssetService",
	HandlerType: (*AssetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RequestUpload",
			Handler:    _AssetService_RequestUpload_Handler,
		},
		{
			MethodName: "ConfirmUpload",
			Handler:    _AssetService_ConfirmUpload_Handler,
		},
		{
			MethodName: "GetAsset",
			Handler:    _AssetService_GetAsset_Handler,
		},
		{
			MethodName: "ListAssets",
			Handler:    _AssetService_ListAssets_Handler,
		},
		{
			MethodName: "UpdateAsset",
			Handler:    _AssetService_UpdateAsset_Handler,
		},
		{
			MethodName: "DeleteAsset",
			Handler:    _AssetService_DeleteAsset_Handler,
		},
		{
			MethodName: "ValidateAsset",
			Handler:    _AssetService_ValidateAsset_Handler,
		},
		{
			MethodName: "ProcessAsset",
			Handler:    _AssetService_ProcessAsset_Handler,
		},
		{
			MethodName: "GetProcessingStatus",
			Handler:    _AssetService_GetProcessingStatus_Handler,
		},
		{
			MethodName: "GetDownloadUrl",
			Handler:    _AssetService_GetDownloadUrl_Handler,
		},
		{
			MethodName: "CheckDuplicate",
			Handler:    _AssetService_CheckDuplicate_Handler,
		},
		{
			MethodName: "GetStorageStats",
			Handler:    _AssetService_GetStorageStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "asset/v1/asset.proto",
}

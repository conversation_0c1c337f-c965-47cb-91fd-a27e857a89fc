package handlers

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/notification-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	notificationv1 "github.com/social-content-ai/proto-shared/notification/v1"
)

// NotificationHandler implements the gRPC NotificationService
type NotificationHandler struct {
	notificationv1.UnimplementedNotificationServiceServer
	notificationUC notification.UseCase
	preferenceUC   preference.UseCase
	templateUC     template.UseCase
	logger         logging.Logger
}

// NewNotificationHand<PERSON> creates a new notification handler
func NewNotificationHandler(
	notificationUC notification.UseCase,
	preferenceUC preference.UseCase,
	templateUC template.UseCase,
	logger logging.Logger,
) *NotificationHandler {
	return &NotificationHandler{
		notificationUC: notificationUC,
		preferenceUC:   preferenceUC,
		templateUC:     templateUC,
		logger:         logger,
	}
}

// SendNotification sends a notification to a user
func (h *NotificationHandler) SendNotification(ctx context.Context, req *notificationv1.SendNotificationRequest) (*notificationv1.SendNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
		"title":   req.Title,
	}).Info("gRPC SendNotification called")

	// Convert data map
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert gRPC request to usecase request
	sendReq := &notification.SendNotificationRequest{
		UserID:   req.UserId,
		Type:     req.Type,
		Title:    req.Title,
		Message:  req.Message,
		Channels: convertChannelsFromProto(req.Channels),
		Priority: convertPriorityFromProto(req.Priority),
		Data:     data,
	}

	// Send notification
	response, err := h.notificationUC.SendNotification(ctx, sendReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send notification")
		return nil, status.Errorf(codes.Internal, "failed to send notification: %v", err)
	}

	return &notificationv1.SendNotificationResponse{
		NotificationId: response.NotificationID,
		Status:         response.Status,
	}, nil
}

// SendTemplatedNotification sends a templated notification
func (h *NotificationHandler) SendTemplatedNotification(ctx context.Context, req *notificationv1.SendTemplatedNotificationRequest) (*notificationv1.SendNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":     req.UserId,
		"template_id": req.TemplateId,
	}).Info("gRPC SendTemplatedNotification called")

	// Convert data map
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert gRPC request to usecase request
	sendReq := &notification.SendTemplatedNotificationRequest{
		UserID:     req.UserId,
		TemplateID: req.TemplateId,
		Data:       data,
		Channels:   convertChannelsFromProto(req.Channels),
		Priority:   convertPriorityFromProto(req.Priority),
	}

	// Send templated notification
	response, err := h.notificationUC.SendTemplatedNotification(ctx, sendReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send templated notification")
		return nil, status.Errorf(codes.Internal, "failed to send templated notification: %v", err)
	}

	return &notificationv1.SendNotificationResponse{
		NotificationId: response.NotificationID,
		Status:         response.Status,
	}, nil
}

// SendBulkNotification sends notifications to multiple users
func (h *NotificationHandler) SendBulkNotification(ctx context.Context, req *notificationv1.BulkNotificationRequest) (*notificationv1.BulkNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_count": len(req.UserIds),
		"type":       req.Type,
		"title":      req.Title,
		"channels":   req.Channels,
	}).Info("Processing bulk notification request")

	// Validate request
	if len(req.UserIds) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "user_ids cannot be empty")
	}
	if req.Type == "" {
		return nil, status.Errorf(codes.InvalidArgument, "type is required")
	}
	if req.Title == "" {
		return nil, status.Errorf(codes.InvalidArgument, "title is required")
	}
	if req.Message == "" {
		return nil, status.Errorf(codes.InvalidArgument, "message is required")
	}
	if len(req.Channels) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "channels cannot be empty")
	}
	if req.Priority == notificationv1.NotificationPriority_NOTIFICATION_PRIORITY_UNSPECIFIED {
		return nil, status.Errorf(codes.InvalidArgument, "priority is required")
	}

	// Convert proto data to map[string]interface{}
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert proto channels to strings
	var channels []string
	for _, channel := range req.Channels {
		switch channel {
		case notificationv1.NotificationChannel_EMAIL:
			channels = append(channels, "email")
		case notificationv1.NotificationChannel_SMS:
			channels = append(channels, "sms")
		case notificationv1.NotificationChannel_PUSH:
			channels = append(channels, "push")
		case notificationv1.NotificationChannel_IN_APP:
			channels = append(channels, "in_app")
		case notificationv1.NotificationChannel_WEBHOOK:
			channels = append(channels, "webhook")
		}
	}

	// Convert proto priority to string
	var priority string
	switch req.Priority {
	case notificationv1.NotificationPriority_LOW:
		priority = "low"
	case notificationv1.NotificationPriority_NORMAL:
		priority = "normal"
	case notificationv1.NotificationPriority_HIGH:
		priority = "high"
	case notificationv1.NotificationPriority_URGENT:
		priority = "urgent"
	default:
		priority = "normal"
	}

	// Create usecase request
	usecaseReq := &notification.BulkNotificationRequest{
		UserIDs:     req.UserIds,
		WorkspaceID: req.WorkspaceId,
		Type:        req.Type,
		Title:       req.Title,
		Message:     req.Message,
		Channels:    channels,
		Priority:    priority,
		Data:        data,
	}

	// Call usecase
	response, err := h.notificationUC.SendBulkNotification(ctx, usecaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send bulk notification")
		return nil, status.Errorf(codes.Internal, "failed to send bulk notification: %v", err)
	}

	// Convert results to proto format - collect notification IDs
	var notificationIDs []string
	for _, result := range response.Results {
		if result.NotificationID != "" {
			notificationIDs = append(notificationIDs, result.NotificationID)
		}
	}

	// Generate bulk ID for tracking
	bulkID := fmt.Sprintf("bulk_%d_%s", len(req.UserIds), time.Now().Format("20060102150405"))

	return &notificationv1.BulkNotificationResponse{
		BulkId:              bulkID,
		TotalRecipients:     int32(len(req.UserIds)),
		QueuedNotifications: int32(response.TotalSent),
		FailedNotifications: int32(response.TotalFailed),
		NotificationIds:     notificationIDs,
	}, nil
}

// GetNotification retrieves a notification by ID
func (h *NotificationHandler) GetNotification(ctx context.Context, req *notificationv1.GetNotificationRequest) (*notificationv1.Notification, error) {
	h.logger.WithFields(map[string]interface{}{
		"notification_id": req.NotificationId,
		"user_id":         req.UserId,
	}).Info("gRPC GetNotification called")

	// Get notification
	notification, err := h.notificationUC.GetNotification(ctx, req.NotificationId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification")
		return nil, status.Errorf(codes.NotFound, "notification not found: %v", err)
	}

	return convertNotificationToProto(notification), nil
}

// ListNotifications retrieves notifications for a user
func (h *NotificationHandler) ListNotifications(ctx context.Context, req *notificationv1.ListNotificationsRequest) (*notificationv1.ListNotificationsResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
	}).Info("gRPC ListNotifications called")

	// Validate request
	if req.UserId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "user_id is required")
	}

	// Set default pagination if not provided
	page := int(req.Pagination.Page)
	limit := int(req.Pagination.Limit)
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	// Create usecase request
	usecaseReq := &notification.ListNotificationsRequest{
		UserID:      req.UserId,
		WorkspaceID: req.WorkspaceId,
		Type:        req.Type,
		Status:      req.Status,
		Page:        page,
		Limit:       limit,
		SortBy:      "created_at",
		Order:       "desc",
	}

	response, err := h.notificationUC.ListNotifications(ctx, usecaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list notifications")
		return nil, status.Errorf(codes.Internal, "failed to list notifications: %v", err)
	}

	// Convert to proto format
	var protoNotifications []*notificationv1.Notification
	for _, notif := range response.Notifications {
		// Convert channels to proto enum
		var protoChannels []notificationv1.NotificationChannel
		for _, channel := range notif.Channels {
			protoChannels = append(protoChannels, convertChannelToProto(channel))
		}

		// Convert priority to proto enum
		protoPriority := convertPriorityToProto(notif.Priority)

		protoNotifications = append(protoNotifications, &notificationv1.Notification{
			Id:          notif.ID,
			UserId:      notif.UserID,
			WorkspaceId: notif.WorkspaceID,
			Type:        notif.Type,
			Title:       notif.Title,
			Message:     notif.Message,
			Channels:    protoChannels,
			Priority:    protoPriority,
			Data:        convertDataToProtoMap(notif.Data),
			Status:      notif.Status,
			CreatedAt:   convertTimeToProto(notif.CreatedAt),
			SentAt:      convertTimeToProto(notif.SentAt),
		})
	}

	return &notificationv1.ListNotificationsResponse{
		Notifications: protoNotifications,
		Pagination: &commonv1.PaginationResponse{
			Page:       int32(response.Pagination.Page),
			Limit:      int32(response.Pagination.Limit),
			TotalItems: int32(response.Pagination.Total),
			TotalPages: int32(response.Pagination.TotalPages),
			HasNext:    response.Pagination.Page < response.Pagination.TotalPages,
			HasPrev:    response.Pagination.Page > 1,
		},
	}, nil
}

// MarkAsRead marks notifications as read
func (h *NotificationHandler) MarkAsRead(ctx context.Context, req *notificationv1.MarkAsReadRequest) (*notificationv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"notification_ids": req.NotificationIds,
		"user_id":          req.UserId,
	}).Info("gRPC MarkAsRead called")

	// Validate request
	if len(req.NotificationIds) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "notification_ids cannot be empty")
	}
	if req.UserId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "user_id is required")
	}

	// Create usecase request
	usecaseReq := &notification.MarkAsReadRequest{
		NotificationIDs: req.NotificationIds,
		UserID:          req.UserId,
	}

	err := h.notificationUC.MarkAsRead(ctx, usecaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to mark notifications as read")
		return nil, status.Errorf(codes.Internal, "failed to mark notifications as read: %v", err)
	}

	return &notificationv1.Empty{}, nil
}

// GetDeliveryStatus gets delivery status for a notification
func (h *NotificationHandler) GetDeliveryStatus(ctx context.Context, req *notificationv1.GetDeliveryStatusRequest) (*notificationv1.DeliveryStatus, error) {
	// TODO: Fix proto field names and implement properly
	return nil, status.Errorf(codes.Unimplemented, "GetDeliveryStatus not implemented yet")
}

// Helper functions for converting between proto and domain models

// convertChannelsFromProto converts proto channels to domain channels
func convertChannelsFromProto(protoChannels []notificationv1.NotificationChannel) []string {
	var channels []string
	for _, ch := range protoChannels {
		switch ch {
		case notificationv1.NotificationChannel_EMAIL:
			channels = append(channels, "email")
		case notificationv1.NotificationChannel_SMS:
			channels = append(channels, "sms")
		case notificationv1.NotificationChannel_PUSH:
			channels = append(channels, "push")
		case notificationv1.NotificationChannel_IN_APP:
			channels = append(channels, "in_app")
		}
	}
	return channels
}

// convertPriorityFromProto converts proto priority to domain priority
func convertPriorityFromProto(protoPriority notificationv1.NotificationPriority) string {
	switch protoPriority {
	case notificationv1.NotificationPriority_LOW:
		return "low"
	case notificationv1.NotificationPriority_NORMAL:
		return "normal"
	case notificationv1.NotificationPriority_HIGH:
		return "high"
	default:
		return "normal"
	}
}

// convertNotificationToProto converts domain notification to proto
func convertNotificationToProto(notif *notification.NotificationResponse) *notificationv1.Notification {
	// TODO: Fix data type conversions and proto field mappings
	return &notificationv1.Notification{
		Id:       notif.ID,
		UserId:   notif.UserID,
		Type:     notif.Type,
		Title:    notif.Title,
		Message:  notif.Message,
		Status:   notif.Status,
		Priority: convertPriorityToProto(notif.Priority),
		Channels: convertChannelsToProto(notif.Channels),
	}
}

// convertDeliveryStatusToProto converts domain delivery status to proto
func convertDeliveryStatusToProto(status *notification.DeliveryStatusResponse) *notificationv1.DeliveryStatus {
	// TODO: Fix proto field mappings and data type conversions
	return &notificationv1.DeliveryStatus{
		NotificationId: status.NotificationID,
	}
}

// convertPriorityToProto converts domain priority to proto
func convertPriorityToProto(priority string) notificationv1.NotificationPriority {
	switch priority {
	case "low":
		return notificationv1.NotificationPriority_LOW
	case "normal":
		return notificationv1.NotificationPriority_NORMAL
	case "high":
		return notificationv1.NotificationPriority_HIGH
	default:
		return notificationv1.NotificationPriority_NORMAL
	}
}

// convertChannelsToProto converts domain channels to proto
func convertChannelsToProto(channels []string) []notificationv1.NotificationChannel {
	var protoChannels []notificationv1.NotificationChannel
	for _, ch := range channels {
		switch ch {
		case "email":
			protoChannels = append(protoChannels, notificationv1.NotificationChannel_EMAIL)
		case "sms":
			protoChannels = append(protoChannels, notificationv1.NotificationChannel_SMS)
		case "push":
			protoChannels = append(protoChannels, notificationv1.NotificationChannel_PUSH)
		case "in_app":
			protoChannels = append(protoChannels, notificationv1.NotificationChannel_IN_APP)
		}
	}
	return protoChannels
}

// convertChannelToProto converts domain channel to proto
func convertChannelToProto(channel string) notificationv1.NotificationChannel {
	switch channel {
	case "email":
		return notificationv1.NotificationChannel_EMAIL
	case "sms":
		return notificationv1.NotificationChannel_SMS
	case "push":
		return notificationv1.NotificationChannel_PUSH
	case "in_app":
		return notificationv1.NotificationChannel_IN_APP
	default:
		return notificationv1.NotificationChannel_EMAIL
	}
}

// convertDataToProtoMap converts map[string]interface{} to map[string]string
func convertDataToProtoMap(data map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range data {
		if str, ok := v.(string); ok {
			result[k] = str
		} else {
			result[k] = fmt.Sprintf("%v", v)
		}
	}
	return result
}

// convertTimeToProto converts time string to proto timestamp
func convertTimeToProto(timeStr string) *timestamppb.Timestamp {
	if timeStr == "" {
		return nil
	}

	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return nil
	}

	return timestamppb.New(t)
}

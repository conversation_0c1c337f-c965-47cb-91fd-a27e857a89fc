package events

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaConsumer defines interface for consuming Kafka messages
type KafkaConsumer interface {
	// Subscribe subscribes to the given topics
	Subscribe(topics []string) error

	// Consume starts consuming messages and calls the handler for each message
	Consume(ctx context.Context, handler MessageHandler) error

	// ConsumeWithConfig starts consuming with custom configuration
	ConsumeWithConfig(ctx context.Context, config *ConsumerConfig, handler MessageHandler) error

	// Close closes the consumer and releases resources
	Close() error

	// GetMetrics returns consumer metrics
	GetMetrics() *ConsumerMetrics

	// IsHealthy checks if the consumer is healthy
	IsHealthy() bool
}

// MessageHandler defines interface for handling Kafka messages
type MessageHandler interface {
	HandleMessage(ctx context.Context, topic string, message []byte) error
}

// ConsumerConfig holds configuration for Kafka consumer
type ConsumerConfig struct {
	GroupID             string `json:"group_id"`
	AutoOffsetReset     string `json:"auto_offset_reset"` // "earliest", "latest"
	EnableAutoCommit    bool   `json:"enable_auto_commit"`
	SessionTimeoutMs    int    `json:"session_timeout_ms"`
	HeartbeatIntervalMs int    `json:"heartbeat_interval_ms"`
	MaxPollRecords      int    `json:"max_poll_records"`
	FetchMinBytes       int    `json:"fetch_min_bytes"`
	FetchMaxWaitMs      int    `json:"fetch_max_wait_ms"`
	RetryBackoffMs      int    `json:"retry_backoff_ms"`
	ReconnectBackoffMs  int    `json:"reconnect_backoff_ms"`
	MaxRetries          int    `json:"max_retries"`
	EnableMetrics       bool   `json:"enable_metrics"`
}

// ConsumerMetrics holds metrics for Kafka consumer
type ConsumerMetrics struct {
	MessagesConsumed     int64    `json:"messages_consumed"`
	MessagesProcessed    int64    `json:"messages_processed"`
	MessagesFailed       int64    `json:"messages_failed"`
	LastMessageTimestamp int64    `json:"last_message_timestamp"`
	ConsumerLag          int64    `json:"consumer_lag"`
	IsConnected          bool     `json:"is_connected"`
	Partitions           int      `json:"partitions"`
	Topics               []string `json:"topics"`
}

// DefaultConsumerConfig returns default consumer configuration
func DefaultConsumerConfig() *ConsumerConfig {
	return &ConsumerConfig{
		GroupID:             "credit-service-subscription-events",
		AutoOffsetReset:     "latest",
		EnableAutoCommit:    true,
		SessionTimeoutMs:    30000,
		HeartbeatIntervalMs: 3000,
		MaxPollRecords:      500,
		FetchMinBytes:       1,
		FetchMaxWaitMs:      500,
		RetryBackoffMs:      100,
		ReconnectBackoffMs:  50,
		MaxRetries:          3,
		EnableMetrics:       true,
	}
}

// SubscriptionEventConsumer consumes subscription events from Kafka
type SubscriptionEventConsumer struct {
	consumer     KafkaConsumer
	handler      MessageHandler
	logger       logging.Logger
	config       *ConsumerConfig
	statsTracker *StatsTracker
	isRunning    bool
	topics       []string
}

// SubscriptionEventConsumerConfig holds configuration for subscription event consumer
type SubscriptionEventConsumerConfig struct {
	Topics         []string        `json:"topics"`
	ConsumerConfig *ConsumerConfig `json:"consumer_config"`
	EnableStats    bool            `json:"enable_stats"`
}

// NewSubscriptionEventConsumer creates a new subscription event consumer
func NewSubscriptionEventConsumer(consumer KafkaConsumer, handler MessageHandler, logger logging.Logger) *SubscriptionEventConsumer {
	return &SubscriptionEventConsumer{
		consumer: consumer,
		handler:  handler,
		logger:   logger,
		config:   DefaultConsumerConfig(),
		topics: []string{
			TopicSubscriptionEvents,
			TopicSubscriptionPaymentCompleted,
			TopicSubscriptionPaymentFailed,
			TopicSubscriptionActivated,
			TopicSubscriptionCancelled,
		},
	}
}

// NewSubscriptionEventConsumerWithConfig creates a new subscription event consumer with custom config
func NewSubscriptionEventConsumerWithConfig(consumer KafkaConsumer, handler MessageHandler, logger logging.Logger, config *SubscriptionEventConsumerConfig) *SubscriptionEventConsumer {
	topics := config.Topics
	if len(topics) == 0 {
		topics = []string{
			TopicSubscriptionEvents,
			TopicSubscriptionPaymentCompleted,
			TopicSubscriptionPaymentFailed,
			TopicSubscriptionActivated,
			TopicSubscriptionCancelled,
		}
	}

	consumerConfig := config.ConsumerConfig
	if consumerConfig == nil {
		consumerConfig = DefaultConsumerConfig()
	}

	eventConsumer := &SubscriptionEventConsumer{
		consumer: consumer,
		handler:  handler,
		logger:   logger,
		config:   consumerConfig,
		topics:   topics,
	}

	if config.EnableStats {
		eventConsumer.statsTracker = NewStatsTracker(logger)
	}

	return eventConsumer
}

// Start starts consuming subscription events from all topics
func (c *SubscriptionEventConsumer) Start(ctx context.Context) error {
	c.logger.Info("Starting subscription event consumer")

	c.isRunning = true

	// Subscribe to configured topics
	if err := c.consumer.Subscribe(c.topics); err != nil {
		c.isRunning = false
		return fmt.Errorf("failed to subscribe to topics: %w", err)
	}

	c.logger.WithField("topics", c.topics).Info("Subscribed to subscription topics")

	// Start consuming messages with configuration
	err := c.consumer.ConsumeWithConfig(ctx, c.config, c)
	c.isRunning = false

	return err
}

// StartWithDefaultConfig starts consuming with default configuration
func (c *SubscriptionEventConsumer) StartWithDefaultConfig(ctx context.Context) error {
	return c.consumer.Consume(ctx, c)
}

// IsRunning returns whether the consumer is currently running
func (c *SubscriptionEventConsumer) IsRunning() bool {
	return c.isRunning
}

// GetTopics returns the topics this consumer is subscribed to
func (c *SubscriptionEventConsumer) GetTopics() []string {
	return c.topics
}

// GetConfig returns the consumer configuration
func (c *SubscriptionEventConsumer) GetConfig() *ConsumerConfig {
	return c.config
}

// GetMetrics returns consumer metrics if available
func (c *SubscriptionEventConsumer) GetMetrics() *ConsumerMetrics {
	if c.consumer != nil {
		return c.consumer.GetMetrics()
	}
	return nil
}

// GetStats returns event processing statistics
func (c *SubscriptionEventConsumer) GetStats() *EventProcessingStats {
	if c.statsTracker != nil {
		return c.statsTracker.GetStats()
	}
	return nil
}

// HandleMessage implements MessageHandler interface
func (c *SubscriptionEventConsumer) HandleMessage(ctx context.Context, topic string, message []byte) error {
	c.logger.WithFields(map[string]interface{}{
		"topic":        topic,
		"message_size": len(message),
	}).Info("Received subscription event message")

	// Track stats if enabled
	if c.statsTracker != nil {
		defer func() {
			// This will be called after the handler returns
		}()
	}

	// Delegate to the actual handler
	err := c.handler.HandleMessage(ctx, topic, message)

	// Track stats
	if c.statsTracker != nil {
		c.statsTracker.TrackEvent(topic, err == nil)
	}

	if err != nil {
		c.logger.WithError(err).WithField("topic", topic).Error("Failed to handle message")
		return fmt.Errorf("failed to handle message from topic %s: %w", topic, err)
	}

	return nil
}

// Stop stops the consumer
func (c *SubscriptionEventConsumer) Stop() error {
	c.logger.Info("Stopping subscription event consumer")
	return c.consumer.Close()
}

// MockKafkaConsumer is a mock implementation for testing
type MockKafkaConsumer struct {
	logger    logging.Logger
	metrics   *ConsumerMetrics
	isHealthy bool
	topics    []string
}

// NewMockKafkaConsumer creates a new mock Kafka consumer
func NewMockKafkaConsumer(logger logging.Logger) *MockKafkaConsumer {
	return &MockKafkaConsumer{
		logger:    logger,
		isHealthy: true,
		metrics: &ConsumerMetrics{
			MessagesConsumed:     0,
			MessagesProcessed:    0,
			MessagesFailed:       0,
			LastMessageTimestamp: 0,
			ConsumerLag:          0,
			IsConnected:          true,
			Partitions:           1,
			Topics:               []string{},
		},
	}
}

// Subscribe implements KafkaConsumer interface
func (m *MockKafkaConsumer) Subscribe(topics []string) error {
	m.logger.WithField("topics", topics).Info("Mock: Subscribed to topics")
	m.topics = topics
	m.metrics.Topics = topics
	return nil
}

// Consume implements KafkaConsumer interface
func (m *MockKafkaConsumer) Consume(ctx context.Context, handler MessageHandler) error {
	m.logger.Info("Mock: Starting to consume messages")

	// Simulate consuming messages
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			m.logger.Info("Mock: Context cancelled, stopping consumer")
			return ctx.Err()
		case <-ticker.C:
			// Simulate a subscription event
			mockEvent := NewGeneralSubscriptionEvent(
				EventTypeSubscriptionPaymentCompleted,
				"user_123",
				"sub_456",
				"active",
			)

			mockEvent.PaymentID = "pay_789"
			mockEvent.Amount = 2999 // $29.99
			mockEvent.Currency = "USD"

			message, _ := json.Marshal(mockEvent)

			m.metrics.MessagesConsumed++
			err := handler.HandleMessage(ctx, TopicSubscriptionEvents, message)
			if err != nil {
				m.logger.WithError(err).Error("Mock: Failed to handle message")
				m.metrics.MessagesFailed++
			} else {
				m.metrics.MessagesProcessed++
			}
			m.metrics.LastMessageTimestamp = time.Now().Unix()
		}
	}
}

// ConsumeWithConfig implements KafkaConsumer interface
func (m *MockKafkaConsumer) ConsumeWithConfig(ctx context.Context, config *ConsumerConfig, handler MessageHandler) error {
	m.logger.WithField("config", config).Info("Mock: Starting to consume messages with config")
	return m.Consume(ctx, handler)
}

// Close implements KafkaConsumer interface
func (m *MockKafkaConsumer) Close() error {
	m.logger.Info("Mock: Closing consumer")
	m.metrics.IsConnected = false
	return nil
}

// GetMetrics implements KafkaConsumer interface
func (m *MockKafkaConsumer) GetMetrics() *ConsumerMetrics {
	return m.metrics
}

// IsHealthy implements KafkaConsumer interface
func (m *MockKafkaConsumer) IsHealthy() bool {
	return m.isHealthy
}

// EventProcessingStats tracks event processing statistics
type EventProcessingStats struct {
	TotalProcessed        int64     `json:"total_processed"`
	PaymentCompleted      int64     `json:"payment_completed"`
	PaymentFailed         int64     `json:"payment_failed"`
	SubscriptionActivated int64     `json:"subscription_activated"`
	SubscriptionCancelled int64     `json:"subscription_cancelled"`
	GeneralEvents         int64     `json:"general_events"`
	LastProcessedAt       time.Time `json:"last_processed_at"`
	Errors                int64     `json:"errors"`
}

// StatsTracker tracks event processing statistics
type StatsTracker struct {
	stats  *EventProcessingStats
	logger logging.Logger
}

// NewStatsTracker creates a new stats tracker
func NewStatsTracker(logger logging.Logger) *StatsTracker {
	return &StatsTracker{
		stats: &EventProcessingStats{
			LastProcessedAt: time.Now(),
		},
		logger: logger,
	}
}

// TrackEvent tracks an event processing
func (s *StatsTracker) TrackEvent(topic string, success bool) {
	s.stats.TotalProcessed++
	s.stats.LastProcessedAt = time.Now()

	if !success {
		s.stats.Errors++
		return
	}

	switch topic {
	case TopicSubscriptionPaymentCompleted:
		s.stats.PaymentCompleted++
	case TopicSubscriptionPaymentFailed:
		s.stats.PaymentFailed++
	case TopicSubscriptionActivated:
		s.stats.SubscriptionActivated++
	case TopicSubscriptionCancelled:
		s.stats.SubscriptionCancelled++
	case TopicSubscriptionEvents:
		s.stats.GeneralEvents++
	}

	// Log stats every 100 events
	if s.stats.TotalProcessed%100 == 0 {
		s.logger.WithFields(map[string]interface{}{
			"total_processed":        s.stats.TotalProcessed,
			"payment_completed":      s.stats.PaymentCompleted,
			"payment_failed":         s.stats.PaymentFailed,
			"subscription_activated": s.stats.SubscriptionActivated,
			"subscription_cancelled": s.stats.SubscriptionCancelled,
			"general_events":         s.stats.GeneralEvents,
			"errors":                 s.stats.Errors,
		}).Info("Event processing statistics")
	}
}

// GetStats returns current statistics
func (s *StatsTracker) GetStats() *EventProcessingStats {
	return s.stats
}

// Reset resets the statistics
func (s *StatsTracker) Reset() {
	s.stats = &EventProcessingStats{
		LastProcessedAt: time.Now(),
	}
	s.logger.Info("Event processing statistics reset")
}

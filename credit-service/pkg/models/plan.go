package models

import "time"

// SubscriptionPlan represents a subscription plan
type SubscriptionPlan struct {
	BaseModel
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	Price           int64                  `json:"price"` // in cents
	Currency        string                 `json:"currency"`
	BillingInterval string                 `json:"billing_interval"` // monthly, yearly
	CreditsIncluded int                    `json:"credits_included"`
	MonthlyLimit    int                    `json:"monthly_limit"`
	Features        []PlanFeature          `json:"features"`
	Limits          PlanLimits             `json:"limits"`
	Popular         bool                   `json:"popular"`
	Active          bool                   `json:"active"`
	TrialDays       int                    `json:"trial_days,omitempty"`
	SetupFee        int64                  `json:"setup_fee,omitempty"` // in cents
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	SortOrder       int                    `json:"sort_order"`
}

// PlanFeature represents a plan feature
type PlanFeature struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Included    bool   `json:"included"`
	Limit       int    `json:"limit,omitempty"` // 0 = unlimited, -1 = not available
	Unit        string `json:"unit,omitempty"`
}

// PlanLimits represents plan limits
type PlanLimits struct {
	MaxPosts            int `json:"max_posts"`             // per month
	MaxTemplates        int `json:"max_templates"`         // total
	MaxWorkspaces       int `json:"max_workspaces"`        // total
	MaxTeamMembers      int `json:"max_team_members"`      // per workspace
	MaxScheduledPosts   int `json:"max_scheduled_posts"`   // total
	MaxAIGenerations    int `json:"max_ai_generations"`    // per month
	MaxImageGenerations int `json:"max_image_generations"` // per month
	MaxStorageGB        int `json:"max_storage_gb"`        // total
}

// ListPlansRequest represents list plans request
type ListPlansRequest struct {
	Active          *bool  `json:"active,omitempty"`
	BillingInterval string `json:"billing_interval,omitempty"`
	Currency        string `json:"currency,omitempty"`
	Page            int    `json:"page" validate:"min=1"`
	Limit           int    `json:"limit" validate:"min=1,max=100"`
	SortBy          string `json:"sort_by,omitempty"`
	SortOrder       string `json:"sort_order,omitempty"`
}

// ListPlansResponse represents list plans response
type ListPlansResponse struct {
	Plans      []SubscriptionPlan `json:"plans"`
	Pagination PaginationMeta     `json:"pagination"`
}

// SubscribeRequest represents subscription request
type SubscribeRequest struct {
	UserID         string                 `json:"user_id"`
	PlanID         string                 `json:"plan_id" validate:"required"`
	PaymentMethod  string                 `json:"payment_method" validate:"required"`
	BillingAddress *BillingAddress        `json:"billing_address,omitempty"`
	PromoCode      string                 `json:"promo_code,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscribeResponse represents subscription response
type SubscribeResponse struct {
	SubscriptionID string     `json:"subscription_id"`
	OrderID        string     `json:"order_id,omitempty"`
	PaymentURL     string     `json:"payment_url,omitempty"`
	Status         string     `json:"status"`
	TrialEndsAt    *time.Time `json:"trial_ends_at,omitempty"`
	NextBillingAt  time.Time  `json:"next_billing_at"`
}

// Subscription represents a user subscription
type Subscription struct {
	BaseModel
	UserID             string                 `json:"user_id"`
	PlanID             string                 `json:"plan_id"`
	Plan               *SubscriptionPlan      `json:"plan,omitempty"`
	Status             string                 `json:"status"` // active, cancelled, expired, trial, past_due
	CurrentPeriodStart time.Time              `json:"current_period_start"`
	CurrentPeriodEnd   time.Time              `json:"current_period_end"`
	TrialStart         *time.Time             `json:"trial_start,omitempty"`
	TrialEnd           *time.Time             `json:"trial_end,omitempty"`
	CancelledAt        *time.Time             `json:"cancelled_at,omitempty"`
	CancelAtPeriodEnd  bool                   `json:"cancel_at_period_end"`
	PaymentMethod      string                 `json:"payment_method"`
	BillingAddress     *BillingAddress        `json:"billing_address,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
	LastBilledAt       *time.Time             `json:"last_billed_at,omitempty"`
	NextBillingAt      *time.Time             `json:"next_billing_at,omitempty"`
}

// UpgradePlanRequest represents upgrade plan request
type UpgradePlanRequest struct {
	UserID    string                 `json:"user_id"`
	NewPlanID string                 `json:"new_plan_id" validate:"required"`
	PromoCode string                 `json:"promo_code,omitempty"`
	Prorate   bool                   `json:"prorate"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// DowngradePlanRequest represents downgrade plan request
type DowngradePlanRequest struct {
	UserID        string                 `json:"user_id"`
	NewPlanID     string                 `json:"new_plan_id" validate:"required"`
	EffectiveDate string                 `json:"effective_date,omitempty"` // immediate, next_billing_cycle
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// Note: SubscriptionHistoryRequest, SubscriptionHistoryResponse, and SubscriptionSummary
// are now defined in subscription.go to avoid duplication

// PlanAnalyticsResponse represents plan analytics
type PlanAnalyticsResponse struct {
	Period                 string               `json:"period"`
	TotalSubscriptions     int                  `json:"total_subscriptions"`
	ActiveSubscriptions    int                  `json:"active_subscriptions"`
	NewSubscriptions       int                  `json:"new_subscriptions"`
	CancelledSubscriptions int                  `json:"cancelled_subscriptions"`
	ChurnRate              float64              `json:"churn_rate"`
	MRR                    int64                `json:"mrr"` // Monthly Recurring Revenue in cents
	ARR                    int64                `json:"arr"` // Annual Recurring Revenue in cents
	Timeline               []PlanAnalyticsPoint `json:"timeline"`
	SubscriptionsByPlan    map[string]int       `json:"subscriptions_by_plan"`
	RevenueByPlan          map[string]int64     `json:"revenue_by_plan"`
	TopPlans               []PlanUsage          `json:"top_plans"`
	ConversionFunnel       ConversionFunnel     `json:"conversion_funnel"`
}

// PlanAnalyticsPoint represents a point in plan analytics timeline
type PlanAnalyticsPoint struct {
	Date                   string `json:"date"`
	NewSubscriptions       int    `json:"new_subscriptions"`
	CancelledSubscriptions int    `json:"cancelled_subscriptions"`
	ActiveSubscriptions    int    `json:"active_subscriptions"`
	MRR                    int64  `json:"mrr"`
}

// PlanUsage represents plan usage
type PlanUsage struct {
	PlanID        string  `json:"plan_id"`
	PlanName      string  `json:"plan_name"`
	Subscriptions int     `json:"subscriptions"`
	Revenue       int64   `json:"revenue"`
	ChurnRate     float64 `json:"churn_rate"`
}

// ConversionFunnel represents conversion funnel
type ConversionFunnel struct {
	Visitors       int     `json:"visitors"`
	TrialSignups   int     `json:"trial_signups"`
	Conversions    int     `json:"conversions"`
	ConversionRate float64 `json:"conversion_rate"`
}

// CreatePlanRequest represents create plan request
type CreatePlanRequest struct {
	Name            string                 `json:"name" validate:"required"`
	Description     string                 `json:"description"`
	Price           int64                  `json:"price" validate:"required,min=0"`
	Currency        string                 `json:"currency" validate:"required"`
	BillingInterval string                 `json:"billing_interval" validate:"required,oneof=monthly yearly"`
	CreditsIncluded int                    `json:"credits_included" validate:"min=0"`
	MonthlyLimit    int                    `json:"monthly_limit" validate:"min=0"`
	Features        []PlanFeature          `json:"features"`
	Limits          PlanLimits             `json:"limits"`
	Popular         bool                   `json:"popular"`
	Active          bool                   `json:"active"`
	TrialDays       int                    `json:"trial_days,omitempty"`
	SetupFee        int64                  `json:"setup_fee,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	SortOrder       int                    `json:"sort_order"`
}

// UpdatePlanRequest represents update plan request
type UpdatePlanRequest struct {
	PlanID          string                 `json:"plan_id"`
	Name            string                 `json:"name,omitempty"`
	Description     string                 `json:"description,omitempty"`
	Price           *int64                 `json:"price,omitempty"`
	CreditsIncluded *int                   `json:"credits_included,omitempty"`
	MonthlyLimit    *int                   `json:"monthly_limit,omitempty"`
	Features        []PlanFeature          `json:"features,omitempty"`
	Limits          *PlanLimits            `json:"limits,omitempty"`
	Popular         *bool                  `json:"popular,omitempty"`
	Active          *bool                  `json:"active,omitempty"`
	TrialDays       *int                   `json:"trial_days,omitempty"`
	SetupFee        *int64                 `json:"setup_fee,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	SortOrder       *int                   `json:"sort_order,omitempty"`
}

// PricingResponse represents pricing response
type PricingResponse struct {
	Plans    []SubscriptionPlan `json:"plans"`
	Packages []CreditPackage    `json:"packages"`
	Currency string             `json:"currency"`
	Features []PricingFeature   `json:"features"`
}

// PricingFeature represents a pricing feature
type PricingFeature struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
}

// PromoCode represents a promotional code
type PromoCode struct {
	BaseModel
	Code            string                 `json:"code"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description,omitempty"`
	Type            string                 `json:"type"` // percentage, fixed_amount, credits
	Value           int64                  `json:"value"`
	Currency        string                 `json:"currency,omitempty"`
	MaxUses         int                    `json:"max_uses,omitempty"`
	UsedCount       int                    `json:"used_count"`
	ValidFrom       time.Time              `json:"valid_from"`
	ValidUntil      time.Time              `json:"valid_until"`
	ApplicablePlans []string               `json:"applicable_plans,omitempty"`
	MinAmount       int64                  `json:"min_amount,omitempty"`
	Active          bool                   `json:"active"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

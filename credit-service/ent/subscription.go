// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/subscription"
)

// Subscription is the model entity for the Subscription schema.
type Subscription struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// PlanID holds the value of the "plan_id" field.
	PlanID uuid.UUID `json:"plan_id,omitempty"`
	// Status holds the value of the "status" field.
	Status subscription.Status `json:"status,omitempty"`
	// CurrentPeriodStart holds the value of the "current_period_start" field.
	CurrentPeriodStart time.Time `json:"current_period_start,omitempty"`
	// CurrentPeriodEnd holds the value of the "current_period_end" field.
	CurrentPeriodEnd time.Time `json:"current_period_end,omitempty"`
	// TrialStart holds the value of the "trial_start" field.
	TrialStart time.Time `json:"trial_start,omitempty"`
	// TrialEnd holds the value of the "trial_end" field.
	TrialEnd time.Time `json:"trial_end,omitempty"`
	// CancelledAt holds the value of the "cancelled_at" field.
	CancelledAt time.Time `json:"cancelled_at,omitempty"`
	// CancelAtPeriodEnd holds the value of the "cancel_at_period_end" field.
	CancelAtPeriodEnd bool `json:"cancel_at_period_end,omitempty"`
	// PaymentMethod holds the value of the "payment_method" field.
	PaymentMethod string `json:"payment_method,omitempty"`
	// BillingAddress holds the value of the "billing_address" field.
	BillingAddress map[string]interface{} `json:"billing_address,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// LastBilledAt holds the value of the "last_billed_at" field.
	LastBilledAt time.Time `json:"last_billed_at,omitempty"`
	// NextBillingAt holds the value of the "next_billing_at" field.
	NextBillingAt time.Time `json:"next_billing_at,omitempty"`
	// ExternalSubscriptionID holds the value of the "external_subscription_id" field.
	ExternalSubscriptionID string `json:"external_subscription_id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Subscription) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case subscription.FieldBillingAddress, subscription.FieldMetadata:
			values[i] = new([]byte)
		case subscription.FieldCancelAtPeriodEnd:
			values[i] = new(sql.NullBool)
		case subscription.FieldStatus, subscription.FieldPaymentMethod, subscription.FieldExternalSubscriptionID:
			values[i] = new(sql.NullString)
		case subscription.FieldCurrentPeriodStart, subscription.FieldCurrentPeriodEnd, subscription.FieldTrialStart, subscription.FieldTrialEnd, subscription.FieldCancelledAt, subscription.FieldLastBilledAt, subscription.FieldNextBillingAt, subscription.FieldCreatedAt, subscription.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case subscription.FieldID, subscription.FieldUserID, subscription.FieldPlanID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Subscription fields.
func (s *Subscription) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case subscription.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				s.ID = *value
			}
		case subscription.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				s.UserID = *value
			}
		case subscription.FieldPlanID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field plan_id", values[i])
			} else if value != nil {
				s.PlanID = *value
			}
		case subscription.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				s.Status = subscription.Status(value.String)
			}
		case subscription.FieldCurrentPeriodStart:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field current_period_start", values[i])
			} else if value.Valid {
				s.CurrentPeriodStart = value.Time
			}
		case subscription.FieldCurrentPeriodEnd:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field current_period_end", values[i])
			} else if value.Valid {
				s.CurrentPeriodEnd = value.Time
			}
		case subscription.FieldTrialStart:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field trial_start", values[i])
			} else if value.Valid {
				s.TrialStart = value.Time
			}
		case subscription.FieldTrialEnd:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field trial_end", values[i])
			} else if value.Valid {
				s.TrialEnd = value.Time
			}
		case subscription.FieldCancelledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field cancelled_at", values[i])
			} else if value.Valid {
				s.CancelledAt = value.Time
			}
		case subscription.FieldCancelAtPeriodEnd:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field cancel_at_period_end", values[i])
			} else if value.Valid {
				s.CancelAtPeriodEnd = value.Bool
			}
		case subscription.FieldPaymentMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_method", values[i])
			} else if value.Valid {
				s.PaymentMethod = value.String
			}
		case subscription.FieldBillingAddress:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field billing_address", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &s.BillingAddress); err != nil {
					return fmt.Errorf("unmarshal field billing_address: %w", err)
				}
			}
		case subscription.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &s.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case subscription.FieldLastBilledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_billed_at", values[i])
			} else if value.Valid {
				s.LastBilledAt = value.Time
			}
		case subscription.FieldNextBillingAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field next_billing_at", values[i])
			} else if value.Valid {
				s.NextBillingAt = value.Time
			}
		case subscription.FieldExternalSubscriptionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field external_subscription_id", values[i])
			} else if value.Valid {
				s.ExternalSubscriptionID = value.String
			}
		case subscription.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				s.CreatedAt = value.Time
			}
		case subscription.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				s.UpdatedAt = value.Time
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Subscription.
// This includes values selected through modifiers, order, etc.
func (s *Subscription) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// Update returns a builder for updating this Subscription.
// Note that you need to call Subscription.Unwrap() before calling this method if this Subscription
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Subscription) Update() *SubscriptionUpdateOne {
	return NewSubscriptionClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Subscription entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Subscription) Unwrap() *Subscription {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("ent: Subscription is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Subscription) String() string {
	var builder strings.Builder
	builder.WriteString("Subscription(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", s.UserID))
	builder.WriteString(", ")
	builder.WriteString("plan_id=")
	builder.WriteString(fmt.Sprintf("%v", s.PlanID))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", s.Status))
	builder.WriteString(", ")
	builder.WriteString("current_period_start=")
	builder.WriteString(s.CurrentPeriodStart.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("current_period_end=")
	builder.WriteString(s.CurrentPeriodEnd.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("trial_start=")
	builder.WriteString(s.TrialStart.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("trial_end=")
	builder.WriteString(s.TrialEnd.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("cancelled_at=")
	builder.WriteString(s.CancelledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("cancel_at_period_end=")
	builder.WriteString(fmt.Sprintf("%v", s.CancelAtPeriodEnd))
	builder.WriteString(", ")
	builder.WriteString("payment_method=")
	builder.WriteString(s.PaymentMethod)
	builder.WriteString(", ")
	builder.WriteString("billing_address=")
	builder.WriteString(fmt.Sprintf("%v", s.BillingAddress))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", s.Metadata))
	builder.WriteString(", ")
	builder.WriteString("last_billed_at=")
	builder.WriteString(s.LastBilledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("next_billing_at=")
	builder.WriteString(s.NextBillingAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("external_subscription_id=")
	builder.WriteString(s.ExternalSubscriptionID)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(s.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(s.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Subscriptions is a parsable slice of Subscription.
type Subscriptions []*Subscription

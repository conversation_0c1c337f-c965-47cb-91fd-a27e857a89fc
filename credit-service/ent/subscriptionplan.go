// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
)

// SubscriptionPlan is the model entity for the SubscriptionPlan schema.
type SubscriptionPlan struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// DisplayName holds the value of the "display_name" field.
	DisplayName string `json:"display_name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Price holds the value of the "price" field.
	Price int64 `json:"price,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// BillingInterval holds the value of the "billing_interval" field.
	BillingInterval subscriptionplan.BillingInterval `json:"billing_interval,omitempty"`
	// CreditsIncluded holds the value of the "credits_included" field.
	CreditsIncluded int `json:"credits_included,omitempty"`
	// Credits renewed each month
	MonthlyCredits int `json:"monthly_credits,omitempty"`
	// Whether this plan has unlimited credits
	IsUnlimited bool `json:"is_unlimited,omitempty"`
	// List of features included in this plan
	Features []string `json:"features,omitempty"`
	// Usage limits for this plan
	Limits map[string]interface{} `json:"limits,omitempty"`
	// Maximum number of users for this plan
	MaxUsers int `json:"max_users,omitempty"`
	// Maximum number of workspaces
	MaxWorkspaces int `json:"max_workspaces,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// IsFeatured holds the value of the "is_featured" field.
	IsFeatured bool `json:"is_featured,omitempty"`
	// SortOrder holds the value of the "sort_order" field.
	SortOrder int `json:"sort_order,omitempty"`
	// Stripe price ID for billing
	StripePriceID string `json:"stripe_price_id,omitempty"`
	// Stripe product ID
	StripeProductID string `json:"stripe_product_id,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SubscriptionPlan) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case subscriptionplan.FieldFeatures, subscriptionplan.FieldLimits, subscriptionplan.FieldMetadata:
			values[i] = new([]byte)
		case subscriptionplan.FieldIsUnlimited, subscriptionplan.FieldIsActive, subscriptionplan.FieldIsFeatured:
			values[i] = new(sql.NullBool)
		case subscriptionplan.FieldPrice, subscriptionplan.FieldCreditsIncluded, subscriptionplan.FieldMonthlyCredits, subscriptionplan.FieldMaxUsers, subscriptionplan.FieldMaxWorkspaces, subscriptionplan.FieldSortOrder:
			values[i] = new(sql.NullInt64)
		case subscriptionplan.FieldName, subscriptionplan.FieldDisplayName, subscriptionplan.FieldDescription, subscriptionplan.FieldCurrency, subscriptionplan.FieldBillingInterval, subscriptionplan.FieldStripePriceID, subscriptionplan.FieldStripeProductID:
			values[i] = new(sql.NullString)
		case subscriptionplan.FieldCreatedAt, subscriptionplan.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case subscriptionplan.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SubscriptionPlan fields.
func (sp *SubscriptionPlan) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case subscriptionplan.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				sp.ID = *value
			}
		case subscriptionplan.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				sp.Name = value.String
			}
		case subscriptionplan.FieldDisplayName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field display_name", values[i])
			} else if value.Valid {
				sp.DisplayName = value.String
			}
		case subscriptionplan.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				sp.Description = value.String
			}
		case subscriptionplan.FieldPrice:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field price", values[i])
			} else if value.Valid {
				sp.Price = value.Int64
			}
		case subscriptionplan.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				sp.Currency = value.String
			}
		case subscriptionplan.FieldBillingInterval:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field billing_interval", values[i])
			} else if value.Valid {
				sp.BillingInterval = subscriptionplan.BillingInterval(value.String)
			}
		case subscriptionplan.FieldCreditsIncluded:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field credits_included", values[i])
			} else if value.Valid {
				sp.CreditsIncluded = int(value.Int64)
			}
		case subscriptionplan.FieldMonthlyCredits:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field monthly_credits", values[i])
			} else if value.Valid {
				sp.MonthlyCredits = int(value.Int64)
			}
		case subscriptionplan.FieldIsUnlimited:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_unlimited", values[i])
			} else if value.Valid {
				sp.IsUnlimited = value.Bool
			}
		case subscriptionplan.FieldFeatures:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field features", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sp.Features); err != nil {
					return fmt.Errorf("unmarshal field features: %w", err)
				}
			}
		case subscriptionplan.FieldLimits:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field limits", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sp.Limits); err != nil {
					return fmt.Errorf("unmarshal field limits: %w", err)
				}
			}
		case subscriptionplan.FieldMaxUsers:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_users", values[i])
			} else if value.Valid {
				sp.MaxUsers = int(value.Int64)
			}
		case subscriptionplan.FieldMaxWorkspaces:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_workspaces", values[i])
			} else if value.Valid {
				sp.MaxWorkspaces = int(value.Int64)
			}
		case subscriptionplan.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				sp.IsActive = value.Bool
			}
		case subscriptionplan.FieldIsFeatured:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_featured", values[i])
			} else if value.Valid {
				sp.IsFeatured = value.Bool
			}
		case subscriptionplan.FieldSortOrder:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort_order", values[i])
			} else if value.Valid {
				sp.SortOrder = int(value.Int64)
			}
		case subscriptionplan.FieldStripePriceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field stripe_price_id", values[i])
			} else if value.Valid {
				sp.StripePriceID = value.String
			}
		case subscriptionplan.FieldStripeProductID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field stripe_product_id", values[i])
			} else if value.Valid {
				sp.StripeProductID = value.String
			}
		case subscriptionplan.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sp.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case subscriptionplan.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sp.CreatedAt = value.Time
			}
		case subscriptionplan.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sp.UpdatedAt = value.Time
			}
		default:
			sp.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SubscriptionPlan.
// This includes values selected through modifiers, order, etc.
func (sp *SubscriptionPlan) Value(name string) (ent.Value, error) {
	return sp.selectValues.Get(name)
}

// Update returns a builder for updating this SubscriptionPlan.
// Note that you need to call SubscriptionPlan.Unwrap() before calling this method if this SubscriptionPlan
// was returned from a transaction, and the transaction was committed or rolled back.
func (sp *SubscriptionPlan) Update() *SubscriptionPlanUpdateOne {
	return NewSubscriptionPlanClient(sp.config).UpdateOne(sp)
}

// Unwrap unwraps the SubscriptionPlan entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sp *SubscriptionPlan) Unwrap() *SubscriptionPlan {
	_tx, ok := sp.config.driver.(*txDriver)
	if !ok {
		panic("ent: SubscriptionPlan is not a transactional entity")
	}
	sp.config.driver = _tx.drv
	return sp
}

// String implements the fmt.Stringer.
func (sp *SubscriptionPlan) String() string {
	var builder strings.Builder
	builder.WriteString("SubscriptionPlan(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sp.ID))
	builder.WriteString("name=")
	builder.WriteString(sp.Name)
	builder.WriteString(", ")
	builder.WriteString("display_name=")
	builder.WriteString(sp.DisplayName)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(sp.Description)
	builder.WriteString(", ")
	builder.WriteString("price=")
	builder.WriteString(fmt.Sprintf("%v", sp.Price))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(sp.Currency)
	builder.WriteString(", ")
	builder.WriteString("billing_interval=")
	builder.WriteString(fmt.Sprintf("%v", sp.BillingInterval))
	builder.WriteString(", ")
	builder.WriteString("credits_included=")
	builder.WriteString(fmt.Sprintf("%v", sp.CreditsIncluded))
	builder.WriteString(", ")
	builder.WriteString("monthly_credits=")
	builder.WriteString(fmt.Sprintf("%v", sp.MonthlyCredits))
	builder.WriteString(", ")
	builder.WriteString("is_unlimited=")
	builder.WriteString(fmt.Sprintf("%v", sp.IsUnlimited))
	builder.WriteString(", ")
	builder.WriteString("features=")
	builder.WriteString(fmt.Sprintf("%v", sp.Features))
	builder.WriteString(", ")
	builder.WriteString("limits=")
	builder.WriteString(fmt.Sprintf("%v", sp.Limits))
	builder.WriteString(", ")
	builder.WriteString("max_users=")
	builder.WriteString(fmt.Sprintf("%v", sp.MaxUsers))
	builder.WriteString(", ")
	builder.WriteString("max_workspaces=")
	builder.WriteString(fmt.Sprintf("%v", sp.MaxWorkspaces))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", sp.IsActive))
	builder.WriteString(", ")
	builder.WriteString("is_featured=")
	builder.WriteString(fmt.Sprintf("%v", sp.IsFeatured))
	builder.WriteString(", ")
	builder.WriteString("sort_order=")
	builder.WriteString(fmt.Sprintf("%v", sp.SortOrder))
	builder.WriteString(", ")
	builder.WriteString("stripe_price_id=")
	builder.WriteString(sp.StripePriceID)
	builder.WriteString(", ")
	builder.WriteString("stripe_product_id=")
	builder.WriteString(sp.StripeProductID)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", sp.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(sp.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sp.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// SubscriptionPlans is a parsable slice of SubscriptionPlan.
type SubscriptionPlans []*SubscriptionPlan

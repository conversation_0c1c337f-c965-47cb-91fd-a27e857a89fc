syntax = "proto3";

package asset.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/asset/v1;assetv1";

// Asset represents a file asset
message Asset {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string template_id = 4;        // Optional template association
  string file_name = 5;
  string original_name = 6;      // Original uploaded filename
  string file_type = 7;          // image/jpeg, application/pdf, etc.
  string file_extension = 8;     // jpg, pdf, docx, etc.
  int64 file_size = 9;           // File size in bytes
  string s3_key = 10;            // S3/MinIO object key
  string s3_bucket = 11;         // S3/MinIO bucket name
  string content_hash = 12;      // SHA256 hash for deduplication
  string purpose = 13;           // profile_image, post_image, rag_training, template_thumbnail
  string status = 14;            // pending, validated, processing, ready, failed
  bool is_public = 15;           // Whether asset is publicly accessible
  string thumbnail_url = 16;     // Thumbnail URL for images
  map<string, string> metadata = 17; // Additional metadata
  google.protobuf.Timestamp created_at = 18;
  google.protobuf.Timestamp updated_at = 19;
  ValidationResult validation = 20;
}

// ValidationResult contains file validation information
message ValidationResult {
  bool is_valid = 1;
  repeated string errors = 2;    // Validation error messages
  repeated string warnings = 3;  // Validation warnings
  bool is_safe = 4;              // Malware/virus scan result
  bool is_corrupted = 5;         // File corruption check
  string mime_type = 6;          // Detected MIME type
  map<string, string> properties = 7; // File-specific properties
  google.protobuf.Timestamp validated_at = 8;
}

// UploadRequest for requesting file upload
message UploadRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace context
  string template_id = 3;        // Optional template association
  string file_name = 4;
  string file_type = 5;          // MIME type
  int64 file_size = 6;           // File size in bytes
  string purpose = 7;            // profile_image, post_image, rag_training, template_thumbnail
  bool is_public = 8;            // Whether asset should be publicly accessible
  map<string, string> metadata = 9; // Additional metadata
}

// UploadResponse with presigned URL
message UploadResponse {
  string upload_id = 1;          // Unique upload identifier
  string presigned_url = 2;      // S3 presigned upload URL
  string s3_key = 3;             // S3 object key
  string s3_bucket = 4;          // S3 bucket name
  google.protobuf.Timestamp expires_at = 5; // URL expiry time
  map<string, string> upload_fields = 6; // Additional form fields for upload
}

// ConfirmUploadRequest for confirming upload completion
message ConfirmUploadRequest {
  string upload_id = 1;
  string user_id = 2;
  string etag = 3;               // S3 ETag from upload response
  bool trigger_validation = 4;   // Whether to trigger file validation
  bool trigger_processing = 5;   // Whether to trigger RAG processing (for documents)
}

// ConfirmUploadResponse with asset information
message ConfirmUploadResponse {
  string asset_id = 1;
  string status = 2;             // pending_validation, validated, processing
  bool validation_triggered = 3;
  bool processing_triggered = 4;
  Asset asset = 5;
}

// GetAssetRequest for retrieving asset information
message GetAssetRequest {
  string asset_id = 1;
  string user_id = 2;            // For access control
}

// ListAssetsRequest for listing user assets
message ListAssetsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  string template_id = 3;        // Optional template filter
  common.v1.PaginationRequest pagination = 4;
  string purpose = 5;            // Filter by purpose
  string file_type = 6;          // Filter by file type
  string status = 7;             // Filter by status
  bool include_deleted = 8;      // Include soft-deleted assets
}

// ListAssetsResponse with paginated assets
message ListAssetsResponse {
  repeated Asset assets = 1;
  common.v1.PaginationResponse pagination = 2;
}

// UpdateAssetRequest for updating asset metadata
message UpdateAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  optional string file_name = 3;
  optional string purpose = 4;
  optional bool is_public = 5;
  map<string, string> metadata = 6;
}

// DeleteAssetRequest for deleting an asset
message DeleteAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  bool hard_delete = 3;          // Permanently delete from S3
  bool cleanup_references = 4;   // Clean up references in other services
}

// ValidateAssetRequest for triggering asset validation
message ValidateAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  bool force_revalidation = 3;   // Force revalidation even if already validated
  repeated string validation_types = 4; // specific validations to run
}

// GetDownloadUrlRequest for getting download URL
message GetDownloadUrlRequest {
  string asset_id = 1;
  string user_id = 2;
  int32 expiry_minutes = 3;      // URL expiry in minutes (default: 60)
  bool inline = 4;               // Whether to display inline or download
}

// GetDownloadUrlResponse with presigned download URL
message GetDownloadUrlResponse {
  string download_url = 1;
  google.protobuf.Timestamp expires_at = 2;
  string content_type = 3;
  int64 content_length = 4;
}

// ProcessAssetRequest for triggering asset processing
message ProcessAssetRequest {
  string asset_id = 1;
  string user_id = 2;
  string processing_type = 3;    // rag_training, thumbnail_generation, image_optimization
  map<string, string> processing_options = 4;
}

// ProcessAssetResponse with processing job info
message ProcessAssetResponse {
  string processing_id = 1;
  string status = 2;             // queued, processing, completed, failed
  google.protobuf.Timestamp started_at = 3;
  int32 estimated_duration = 4;  // Estimated processing time in seconds
}

// GetProcessingStatusRequest for checking processing status
message GetProcessingStatusRequest {
  string processing_id = 1;
  string asset_id = 2;           // Alternative lookup
  string user_id = 3;
}

// ProcessingStatus with current processing status
message ProcessingStatus {
  string processing_id = 1;
  string asset_id = 2;
  string processing_type = 3;
  string status = 4;             // processing, completed, failed
  float progress = 5;            // Progress percentage (0-100)
  string current_stage = 6;      // Current processing stage
  google.protobuf.Timestamp started_at = 7;
  google.protobuf.Timestamp completed_at = 8;
  string error_message = 9;      // If failed
  map<string, string> results = 10; // Processing results
}

// DuplicateCheckRequest for checking file duplicates
message DuplicateCheckRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  string content_hash = 3;       // File hash to check
  string file_name = 4;          // Optional filename check
  int64 file_size = 5;           // Optional file size check
}

// DuplicateCheckResponse with duplicate information
message DuplicateCheckResponse {
  bool has_duplicate = 1;
  repeated Asset duplicates = 2; // Existing duplicate assets
  string recommended_action = 3; // reuse, replace, upload_anyway
}

// GetStorageStatsRequest for storage statistics
message GetStorageStatsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
}

// StorageStats with storage usage information
message StorageStats {
  int64 total_size = 1;          // Total storage used in bytes
  int32 total_files = 2;         // Total number of files
  int64 images_size = 3;         // Storage used by images
  int32 images_count = 4;        // Number of image files
  int64 documents_size = 5;      // Storage used by documents
  int32 documents_count = 6;     // Number of document files
  int64 quota_limit = 7;         // Storage quota limit in bytes
  float quota_usage_percent = 8; // Quota usage percentage
}

// Empty response
message Empty {}

// AssetService provides file asset management operations
service AssetService {
  // Upload operations
  rpc RequestUpload(UploadRequest) returns (UploadResponse);
  rpc ConfirmUpload(ConfirmUploadRequest) returns (ConfirmUploadResponse);
  
  // Asset management operations
  rpc GetAsset(GetAssetRequest) returns (Asset);
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse);
  rpc UpdateAsset(UpdateAssetRequest) returns (Asset);
  rpc DeleteAsset(DeleteAssetRequest) returns (Empty);
  
  // Validation and processing operations
  rpc ValidateAsset(ValidateAssetRequest) returns (ValidationResult);
  rpc ProcessAsset(ProcessAssetRequest) returns (ProcessAssetResponse);
  rpc GetProcessingStatus(GetProcessingStatusRequest) returns (ProcessingStatus);
  
  // Download operations
  rpc GetDownloadUrl(GetDownloadUrlRequest) returns (GetDownloadUrlResponse);
  
  // Utility operations
  rpc CheckDuplicate(DuplicateCheckRequest) returns (DuplicateCheckResponse);
  rpc GetStorageStats(GetStorageStatsRequest) returns (StorageStats);
}

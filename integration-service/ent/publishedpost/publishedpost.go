// Code generated by ent, DO NOT EDIT.

package publishedpost

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the publishedpost type in the database.
	Label = "published_post"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldPostID holds the string denoting the post_id field in the database.
	FieldPostID = "post_id"
	// FieldIntegrationID holds the string denoting the integration_id field in the database.
	FieldIntegrationID = "integration_id"
	// FieldPlatform holds the string denoting the platform field in the database.
	FieldPlatform = "platform"
	// FieldPlatformPostID holds the string denoting the platform_post_id field in the database.
	FieldPlatformPostID = "platform_post_id"
	// FieldPlatformPostURL holds the string denoting the platform_post_url field in the database.
	FieldPlatformPostURL = "platform_post_url"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldMediaUrls holds the string denoting the media_urls field in the database.
	FieldMediaUrls = "media_urls"
	// FieldHashtags holds the string denoting the hashtags field in the database.
	FieldHashtags = "hashtags"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldErrorMessage holds the string denoting the error_message field in the database.
	FieldErrorMessage = "error_message"
	// FieldScheduledAt holds the string denoting the scheduled_at field in the database.
	FieldScheduledAt = "scheduled_at"
	// FieldPublishedAt holds the string denoting the published_at field in the database.
	FieldPublishedAt = "published_at"
	// FieldPlatformResponse holds the string denoting the platform_response field in the database.
	FieldPlatformResponse = "platform_response"
	// FieldAnalytics holds the string denoting the analytics field in the database.
	FieldAnalytics = "analytics"
	// FieldLastAnalyticsSync holds the string denoting the last_analytics_sync field in the database.
	FieldLastAnalyticsSync = "last_analytics_sync"
	// FieldRetryCount holds the string denoting the retry_count field in the database.
	FieldRetryCount = "retry_count"
	// FieldNextRetryAt holds the string denoting the next_retry_at field in the database.
	FieldNextRetryAt = "next_retry_at"
	// FieldPublishOptions holds the string denoting the publish_options field in the database.
	FieldPublishOptions = "publish_options"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the publishedpost in the database.
	Table = "published_posts"
)

// Columns holds all SQL columns for publishedpost fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldPostID,
	FieldIntegrationID,
	FieldPlatform,
	FieldPlatformPostID,
	FieldPlatformPostURL,
	FieldContent,
	FieldMediaUrls,
	FieldHashtags,
	FieldStatus,
	FieldErrorMessage,
	FieldScheduledAt,
	FieldPublishedAt,
	FieldPlatformResponse,
	FieldAnalytics,
	FieldLastAnalyticsSync,
	FieldRetryCount,
	FieldNextRetryAt,
	FieldPublishOptions,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// PostIDValidator is a validator for the "post_id" field. It is called by the builders before save.
	PostIDValidator func(string) error
	// IntegrationIDValidator is a validator for the "integration_id" field. It is called by the builders before save.
	IntegrationIDValidator func(string) error
	// PlatformPostIDValidator is a validator for the "platform_post_id" field. It is called by the builders before save.
	PlatformPostIDValidator func(string) error
	// PlatformPostURLValidator is a validator for the "platform_post_url" field. It is called by the builders before save.
	PlatformPostURLValidator func(string) error
	// ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	ErrorMessageValidator func(string) error
	// DefaultRetryCount holds the default value on creation for the "retry_count" field.
	DefaultRetryCount int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Platform defines the type for the "platform" enum field.
type Platform string

// Platform values.
const (
	PlatformFacebook  Platform = "facebook"
	PlatformInstagram Platform = "instagram"
	PlatformTwitter   Platform = "twitter"
	PlatformLinkedin  Platform = "linkedin"
	PlatformTiktok    Platform = "tiktok"
	PlatformYoutube   Platform = "youtube"
	PlatformPinterest Platform = "pinterest"
	PlatformSnapchat  Platform = "snapchat"
)

func (pl Platform) String() string {
	return string(pl)
}

// PlatformValidator is a validator for the "platform" field enum values. It is called by the builders before save.
func PlatformValidator(pl Platform) error {
	switch pl {
	case PlatformFacebook, PlatformInstagram, PlatformTwitter, PlatformLinkedin, PlatformTiktok, PlatformYoutube, PlatformPinterest, PlatformSnapchat:
		return nil
	default:
		return fmt.Errorf("publishedpost: invalid enum value for platform field: %q", pl)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusPending is the default value of the Status enum.
const DefaultStatus = StatusPending

// Status values.
const (
	StatusPending   Status = "pending"
	StatusPublished Status = "published"
	StatusFailed    Status = "failed"
	StatusDeleted   Status = "deleted"
	StatusScheduled Status = "scheduled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusPending, StatusPublished, StatusFailed, StatusDeleted, StatusScheduled:
		return nil
	default:
		return fmt.Errorf("publishedpost: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the PublishedPost queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByPostID orders the results by the post_id field.
func ByPostID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPostID, opts...).ToFunc()
}

// ByIntegrationID orders the results by the integration_id field.
func ByIntegrationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIntegrationID, opts...).ToFunc()
}

// ByPlatform orders the results by the platform field.
func ByPlatform(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatform, opts...).ToFunc()
}

// ByPlatformPostID orders the results by the platform_post_id field.
func ByPlatformPostID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformPostID, opts...).ToFunc()
}

// ByPlatformPostURL orders the results by the platform_post_url field.
func ByPlatformPostURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatformPostURL, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByErrorMessage orders the results by the error_message field.
func ByErrorMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorMessage, opts...).ToFunc()
}

// ByScheduledAt orders the results by the scheduled_at field.
func ByScheduledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldScheduledAt, opts...).ToFunc()
}

// ByPublishedAt orders the results by the published_at field.
func ByPublishedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPublishedAt, opts...).ToFunc()
}

// ByLastAnalyticsSync orders the results by the last_analytics_sync field.
func ByLastAnalyticsSync(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastAnalyticsSync, opts...).ToFunc()
}

// ByRetryCount orders the results by the retry_count field.
func ByRetryCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRetryCount, opts...).ToFunc()
}

// ByNextRetryAt orders the results by the next_retry_at field.
func ByNextRetryAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNextRetryAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

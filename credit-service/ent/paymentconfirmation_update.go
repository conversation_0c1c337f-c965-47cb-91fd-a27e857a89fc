// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// PaymentConfirmationUpdate is the builder for updating PaymentConfirmation entities.
type PaymentConfirmationUpdate struct {
	config
	hooks    []Hook
	mutation *PaymentConfirmationMutation
}

// Where appends a list predicates to the PaymentConfirmationUpdate builder.
func (pcu *PaymentConfirmationUpdate) Where(ps ...predicate.PaymentConfirmation) *PaymentConfirmationUpdate {
	pcu.mutation.Where(ps...)
	return pcu
}

// SetBankTransferID sets the "bank_transfer_id" field.
func (pcu *PaymentConfirmationUpdate) SetBankTransferID(u uuid.UUID) *PaymentConfirmationUpdate {
	pcu.mutation.SetBankTransferID(u)
	return pcu
}

// SetNillableBankTransferID sets the "bank_transfer_id" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableBankTransferID(u *uuid.UUID) *PaymentConfirmationUpdate {
	if u != nil {
		pcu.SetBankTransferID(*u)
	}
	return pcu
}

// ClearBankTransferID clears the value of the "bank_transfer_id" field.
func (pcu *PaymentConfirmationUpdate) ClearBankTransferID() *PaymentConfirmationUpdate {
	pcu.mutation.ClearBankTransferID()
	return pcu
}

// SetOrderID sets the "order_id" field.
func (pcu *PaymentConfirmationUpdate) SetOrderID(u uuid.UUID) *PaymentConfirmationUpdate {
	pcu.mutation.SetOrderID(u)
	return pcu
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableOrderID(u *uuid.UUID) *PaymentConfirmationUpdate {
	if u != nil {
		pcu.SetOrderID(*u)
	}
	return pcu
}

// ClearOrderID clears the value of the "order_id" field.
func (pcu *PaymentConfirmationUpdate) ClearOrderID() *PaymentConfirmationUpdate {
	pcu.mutation.ClearOrderID()
	return pcu
}

// SetSubscriptionID sets the "subscription_id" field.
func (pcu *PaymentConfirmationUpdate) SetSubscriptionID(u uuid.UUID) *PaymentConfirmationUpdate {
	pcu.mutation.SetSubscriptionID(u)
	return pcu
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableSubscriptionID(u *uuid.UUID) *PaymentConfirmationUpdate {
	if u != nil {
		pcu.SetSubscriptionID(*u)
	}
	return pcu
}

// ClearSubscriptionID clears the value of the "subscription_id" field.
func (pcu *PaymentConfirmationUpdate) ClearSubscriptionID() *PaymentConfirmationUpdate {
	pcu.mutation.ClearSubscriptionID()
	return pcu
}

// SetPaymentMethod sets the "payment_method" field.
func (pcu *PaymentConfirmationUpdate) SetPaymentMethod(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetPaymentMethod(s)
	return pcu
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillablePaymentMethod(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetPaymentMethod(*s)
	}
	return pcu
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (pcu *PaymentConfirmationUpdate) SetExternalPaymentID(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetExternalPaymentID(s)
	return pcu
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableExternalPaymentID(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetExternalPaymentID(*s)
	}
	return pcu
}

// ClearExternalPaymentID clears the value of the "external_payment_id" field.
func (pcu *PaymentConfirmationUpdate) ClearExternalPaymentID() *PaymentConfirmationUpdate {
	pcu.mutation.ClearExternalPaymentID()
	return pcu
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (pcu *PaymentConfirmationUpdate) SetBankTransactionID(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetBankTransactionID(s)
	return pcu
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableBankTransactionID(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetBankTransactionID(*s)
	}
	return pcu
}

// ClearBankTransactionID clears the value of the "bank_transaction_id" field.
func (pcu *PaymentConfirmationUpdate) ClearBankTransactionID() *PaymentConfirmationUpdate {
	pcu.mutation.ClearBankTransactionID()
	return pcu
}

// SetAmountPaid sets the "amount_paid" field.
func (pcu *PaymentConfirmationUpdate) SetAmountPaid(i int64) *PaymentConfirmationUpdate {
	pcu.mutation.ResetAmountPaid()
	pcu.mutation.SetAmountPaid(i)
	return pcu
}

// SetNillableAmountPaid sets the "amount_paid" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableAmountPaid(i *int64) *PaymentConfirmationUpdate {
	if i != nil {
		pcu.SetAmountPaid(*i)
	}
	return pcu
}

// AddAmountPaid adds i to the "amount_paid" field.
func (pcu *PaymentConfirmationUpdate) AddAmountPaid(i int64) *PaymentConfirmationUpdate {
	pcu.mutation.AddAmountPaid(i)
	return pcu
}

// SetCurrency sets the "currency" field.
func (pcu *PaymentConfirmationUpdate) SetCurrency(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetCurrency(s)
	return pcu
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableCurrency(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetCurrency(*s)
	}
	return pcu
}

// SetStatus sets the "status" field.
func (pcu *PaymentConfirmationUpdate) SetStatus(pa paymentconfirmation.Status) *PaymentConfirmationUpdate {
	pcu.mutation.SetStatus(pa)
	return pcu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableStatus(pa *paymentconfirmation.Status) *PaymentConfirmationUpdate {
	if pa != nil {
		pcu.SetStatus(*pa)
	}
	return pcu
}

// SetConfirmedBy sets the "confirmed_by" field.
func (pcu *PaymentConfirmationUpdate) SetConfirmedBy(u uuid.UUID) *PaymentConfirmationUpdate {
	pcu.mutation.SetConfirmedBy(u)
	return pcu
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableConfirmedBy(u *uuid.UUID) *PaymentConfirmationUpdate {
	if u != nil {
		pcu.SetConfirmedBy(*u)
	}
	return pcu
}

// ClearConfirmedBy clears the value of the "confirmed_by" field.
func (pcu *PaymentConfirmationUpdate) ClearConfirmedBy() *PaymentConfirmationUpdate {
	pcu.mutation.ClearConfirmedBy()
	return pcu
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (pcu *PaymentConfirmationUpdate) SetConfirmationMethod(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetConfirmationMethod(s)
	return pcu
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableConfirmationMethod(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetConfirmationMethod(*s)
	}
	return pcu
}

// SetConfirmationNotes sets the "confirmation_notes" field.
func (pcu *PaymentConfirmationUpdate) SetConfirmationNotes(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetConfirmationNotes(s)
	return pcu
}

// SetNillableConfirmationNotes sets the "confirmation_notes" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableConfirmationNotes(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetConfirmationNotes(*s)
	}
	return pcu
}

// ClearConfirmationNotes clears the value of the "confirmation_notes" field.
func (pcu *PaymentConfirmationUpdate) ClearConfirmationNotes() *PaymentConfirmationUpdate {
	pcu.mutation.ClearConfirmationNotes()
	return pcu
}

// SetPaymentDetails sets the "payment_details" field.
func (pcu *PaymentConfirmationUpdate) SetPaymentDetails(m map[string]interface{}) *PaymentConfirmationUpdate {
	pcu.mutation.SetPaymentDetails(m)
	return pcu
}

// ClearPaymentDetails clears the value of the "payment_details" field.
func (pcu *PaymentConfirmationUpdate) ClearPaymentDetails() *PaymentConfirmationUpdate {
	pcu.mutation.ClearPaymentDetails()
	return pcu
}

// SetWebhookData sets the "webhook_data" field.
func (pcu *PaymentConfirmationUpdate) SetWebhookData(m map[string]interface{}) *PaymentConfirmationUpdate {
	pcu.mutation.SetWebhookData(m)
	return pcu
}

// ClearWebhookData clears the value of the "webhook_data" field.
func (pcu *PaymentConfirmationUpdate) ClearWebhookData() *PaymentConfirmationUpdate {
	pcu.mutation.ClearWebhookData()
	return pcu
}

// SetPaymentDate sets the "payment_date" field.
func (pcu *PaymentConfirmationUpdate) SetPaymentDate(t time.Time) *PaymentConfirmationUpdate {
	pcu.mutation.SetPaymentDate(t)
	return pcu
}

// SetNillablePaymentDate sets the "payment_date" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillablePaymentDate(t *time.Time) *PaymentConfirmationUpdate {
	if t != nil {
		pcu.SetPaymentDate(*t)
	}
	return pcu
}

// ClearPaymentDate clears the value of the "payment_date" field.
func (pcu *PaymentConfirmationUpdate) ClearPaymentDate() *PaymentConfirmationUpdate {
	pcu.mutation.ClearPaymentDate()
	return pcu
}

// SetConfirmedAt sets the "confirmed_at" field.
func (pcu *PaymentConfirmationUpdate) SetConfirmedAt(t time.Time) *PaymentConfirmationUpdate {
	pcu.mutation.SetConfirmedAt(t)
	return pcu
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableConfirmedAt(t *time.Time) *PaymentConfirmationUpdate {
	if t != nil {
		pcu.SetConfirmedAt(*t)
	}
	return pcu
}

// ClearConfirmedAt clears the value of the "confirmed_at" field.
func (pcu *PaymentConfirmationUpdate) ClearConfirmedAt() *PaymentConfirmationUpdate {
	pcu.mutation.ClearConfirmedAt()
	return pcu
}

// SetFailureReason sets the "failure_reason" field.
func (pcu *PaymentConfirmationUpdate) SetFailureReason(s string) *PaymentConfirmationUpdate {
	pcu.mutation.SetFailureReason(s)
	return pcu
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (pcu *PaymentConfirmationUpdate) SetNillableFailureReason(s *string) *PaymentConfirmationUpdate {
	if s != nil {
		pcu.SetFailureReason(*s)
	}
	return pcu
}

// ClearFailureReason clears the value of the "failure_reason" field.
func (pcu *PaymentConfirmationUpdate) ClearFailureReason() *PaymentConfirmationUpdate {
	pcu.mutation.ClearFailureReason()
	return pcu
}

// SetMetadata sets the "metadata" field.
func (pcu *PaymentConfirmationUpdate) SetMetadata(m map[string]interface{}) *PaymentConfirmationUpdate {
	pcu.mutation.SetMetadata(m)
	return pcu
}

// ClearMetadata clears the value of the "metadata" field.
func (pcu *PaymentConfirmationUpdate) ClearMetadata() *PaymentConfirmationUpdate {
	pcu.mutation.ClearMetadata()
	return pcu
}

// SetUpdatedAt sets the "updated_at" field.
func (pcu *PaymentConfirmationUpdate) SetUpdatedAt(t time.Time) *PaymentConfirmationUpdate {
	pcu.mutation.SetUpdatedAt(t)
	return pcu
}

// Mutation returns the PaymentConfirmationMutation object of the builder.
func (pcu *PaymentConfirmationUpdate) Mutation() *PaymentConfirmationMutation {
	return pcu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pcu *PaymentConfirmationUpdate) Save(ctx context.Context) (int, error) {
	pcu.defaults()
	return withHooks(ctx, pcu.sqlSave, pcu.mutation, pcu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pcu *PaymentConfirmationUpdate) SaveX(ctx context.Context) int {
	affected, err := pcu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pcu *PaymentConfirmationUpdate) Exec(ctx context.Context) error {
	_, err := pcu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcu *PaymentConfirmationUpdate) ExecX(ctx context.Context) {
	if err := pcu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pcu *PaymentConfirmationUpdate) defaults() {
	if _, ok := pcu.mutation.UpdatedAt(); !ok {
		v := paymentconfirmation.UpdateDefaultUpdatedAt()
		pcu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pcu *PaymentConfirmationUpdate) check() error {
	if v, ok := pcu.mutation.PaymentMethod(); ok {
		if err := paymentconfirmation.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.payment_method": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.ExternalPaymentID(); ok {
		if err := paymentconfirmation.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.external_payment_id": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.BankTransactionID(); ok {
		if err := paymentconfirmation.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.bank_transaction_id": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.AmountPaid(); ok {
		if err := paymentconfirmation.AmountPaidValidator(v); err != nil {
			return &ValidationError{Name: "amount_paid", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.amount_paid": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.Currency(); ok {
		if err := paymentconfirmation.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.currency": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.Status(); ok {
		if err := paymentconfirmation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.status": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.ConfirmationMethod(); ok {
		if err := paymentconfirmation.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.confirmation_method": %w`, err)}
		}
	}
	if v, ok := pcu.mutation.FailureReason(); ok {
		if err := paymentconfirmation.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.failure_reason": %w`, err)}
		}
	}
	return nil
}

func (pcu *PaymentConfirmationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pcu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(paymentconfirmation.Table, paymentconfirmation.Columns, sqlgraph.NewFieldSpec(paymentconfirmation.FieldID, field.TypeUUID))
	if ps := pcu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pcu.mutation.BankTransferID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransferID, field.TypeUUID, value)
	}
	if pcu.mutation.BankTransferIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldBankTransferID, field.TypeUUID)
	}
	if value, ok := pcu.mutation.OrderID(); ok {
		_spec.SetField(paymentconfirmation.FieldOrderID, field.TypeUUID, value)
	}
	if pcu.mutation.OrderIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldOrderID, field.TypeUUID)
	}
	if value, ok := pcu.mutation.SubscriptionID(); ok {
		_spec.SetField(paymentconfirmation.FieldSubscriptionID, field.TypeUUID, value)
	}
	if pcu.mutation.SubscriptionIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldSubscriptionID, field.TypeUUID)
	}
	if value, ok := pcu.mutation.PaymentMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := pcu.mutation.ExternalPaymentID(); ok {
		_spec.SetField(paymentconfirmation.FieldExternalPaymentID, field.TypeString, value)
	}
	if pcu.mutation.ExternalPaymentIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldExternalPaymentID, field.TypeString)
	}
	if value, ok := pcu.mutation.BankTransactionID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransactionID, field.TypeString, value)
	}
	if pcu.mutation.BankTransactionIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldBankTransactionID, field.TypeString)
	}
	if value, ok := pcu.mutation.AmountPaid(); ok {
		_spec.SetField(paymentconfirmation.FieldAmountPaid, field.TypeInt64, value)
	}
	if value, ok := pcu.mutation.AddedAmountPaid(); ok {
		_spec.AddField(paymentconfirmation.FieldAmountPaid, field.TypeInt64, value)
	}
	if value, ok := pcu.mutation.Currency(); ok {
		_spec.SetField(paymentconfirmation.FieldCurrency, field.TypeString, value)
	}
	if value, ok := pcu.mutation.Status(); ok {
		_spec.SetField(paymentconfirmation.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := pcu.mutation.ConfirmedBy(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedBy, field.TypeUUID, value)
	}
	if pcu.mutation.ConfirmedByCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmedBy, field.TypeUUID)
	}
	if value, ok := pcu.mutation.ConfirmationMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationMethod, field.TypeString, value)
	}
	if value, ok := pcu.mutation.ConfirmationNotes(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationNotes, field.TypeString, value)
	}
	if pcu.mutation.ConfirmationNotesCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmationNotes, field.TypeString)
	}
	if value, ok := pcu.mutation.PaymentDetails(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDetails, field.TypeJSON, value)
	}
	if pcu.mutation.PaymentDetailsCleared() {
		_spec.ClearField(paymentconfirmation.FieldPaymentDetails, field.TypeJSON)
	}
	if value, ok := pcu.mutation.WebhookData(); ok {
		_spec.SetField(paymentconfirmation.FieldWebhookData, field.TypeJSON, value)
	}
	if pcu.mutation.WebhookDataCleared() {
		_spec.ClearField(paymentconfirmation.FieldWebhookData, field.TypeJSON)
	}
	if value, ok := pcu.mutation.PaymentDate(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDate, field.TypeTime, value)
	}
	if pcu.mutation.PaymentDateCleared() {
		_spec.ClearField(paymentconfirmation.FieldPaymentDate, field.TypeTime)
	}
	if value, ok := pcu.mutation.ConfirmedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedAt, field.TypeTime, value)
	}
	if pcu.mutation.ConfirmedAtCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmedAt, field.TypeTime)
	}
	if value, ok := pcu.mutation.FailureReason(); ok {
		_spec.SetField(paymentconfirmation.FieldFailureReason, field.TypeString, value)
	}
	if pcu.mutation.FailureReasonCleared() {
		_spec.ClearField(paymentconfirmation.FieldFailureReason, field.TypeString)
	}
	if value, ok := pcu.mutation.Metadata(); ok {
		_spec.SetField(paymentconfirmation.FieldMetadata, field.TypeJSON, value)
	}
	if pcu.mutation.MetadataCleared() {
		_spec.ClearField(paymentconfirmation.FieldMetadata, field.TypeJSON)
	}
	if value, ok := pcu.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, pcu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{paymentconfirmation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pcu.mutation.done = true
	return n, nil
}

// PaymentConfirmationUpdateOne is the builder for updating a single PaymentConfirmation entity.
type PaymentConfirmationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PaymentConfirmationMutation
}

// SetBankTransferID sets the "bank_transfer_id" field.
func (pcuo *PaymentConfirmationUpdateOne) SetBankTransferID(u uuid.UUID) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetBankTransferID(u)
	return pcuo
}

// SetNillableBankTransferID sets the "bank_transfer_id" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableBankTransferID(u *uuid.UUID) *PaymentConfirmationUpdateOne {
	if u != nil {
		pcuo.SetBankTransferID(*u)
	}
	return pcuo
}

// ClearBankTransferID clears the value of the "bank_transfer_id" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearBankTransferID() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearBankTransferID()
	return pcuo
}

// SetOrderID sets the "order_id" field.
func (pcuo *PaymentConfirmationUpdateOne) SetOrderID(u uuid.UUID) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetOrderID(u)
	return pcuo
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableOrderID(u *uuid.UUID) *PaymentConfirmationUpdateOne {
	if u != nil {
		pcuo.SetOrderID(*u)
	}
	return pcuo
}

// ClearOrderID clears the value of the "order_id" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearOrderID() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearOrderID()
	return pcuo
}

// SetSubscriptionID sets the "subscription_id" field.
func (pcuo *PaymentConfirmationUpdateOne) SetSubscriptionID(u uuid.UUID) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetSubscriptionID(u)
	return pcuo
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableSubscriptionID(u *uuid.UUID) *PaymentConfirmationUpdateOne {
	if u != nil {
		pcuo.SetSubscriptionID(*u)
	}
	return pcuo
}

// ClearSubscriptionID clears the value of the "subscription_id" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearSubscriptionID() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearSubscriptionID()
	return pcuo
}

// SetPaymentMethod sets the "payment_method" field.
func (pcuo *PaymentConfirmationUpdateOne) SetPaymentMethod(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetPaymentMethod(s)
	return pcuo
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillablePaymentMethod(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetPaymentMethod(*s)
	}
	return pcuo
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (pcuo *PaymentConfirmationUpdateOne) SetExternalPaymentID(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetExternalPaymentID(s)
	return pcuo
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableExternalPaymentID(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetExternalPaymentID(*s)
	}
	return pcuo
}

// ClearExternalPaymentID clears the value of the "external_payment_id" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearExternalPaymentID() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearExternalPaymentID()
	return pcuo
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (pcuo *PaymentConfirmationUpdateOne) SetBankTransactionID(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetBankTransactionID(s)
	return pcuo
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableBankTransactionID(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetBankTransactionID(*s)
	}
	return pcuo
}

// ClearBankTransactionID clears the value of the "bank_transaction_id" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearBankTransactionID() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearBankTransactionID()
	return pcuo
}

// SetAmountPaid sets the "amount_paid" field.
func (pcuo *PaymentConfirmationUpdateOne) SetAmountPaid(i int64) *PaymentConfirmationUpdateOne {
	pcuo.mutation.ResetAmountPaid()
	pcuo.mutation.SetAmountPaid(i)
	return pcuo
}

// SetNillableAmountPaid sets the "amount_paid" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableAmountPaid(i *int64) *PaymentConfirmationUpdateOne {
	if i != nil {
		pcuo.SetAmountPaid(*i)
	}
	return pcuo
}

// AddAmountPaid adds i to the "amount_paid" field.
func (pcuo *PaymentConfirmationUpdateOne) AddAmountPaid(i int64) *PaymentConfirmationUpdateOne {
	pcuo.mutation.AddAmountPaid(i)
	return pcuo
}

// SetCurrency sets the "currency" field.
func (pcuo *PaymentConfirmationUpdateOne) SetCurrency(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetCurrency(s)
	return pcuo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableCurrency(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetCurrency(*s)
	}
	return pcuo
}

// SetStatus sets the "status" field.
func (pcuo *PaymentConfirmationUpdateOne) SetStatus(pa paymentconfirmation.Status) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetStatus(pa)
	return pcuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableStatus(pa *paymentconfirmation.Status) *PaymentConfirmationUpdateOne {
	if pa != nil {
		pcuo.SetStatus(*pa)
	}
	return pcuo
}

// SetConfirmedBy sets the "confirmed_by" field.
func (pcuo *PaymentConfirmationUpdateOne) SetConfirmedBy(u uuid.UUID) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetConfirmedBy(u)
	return pcuo
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableConfirmedBy(u *uuid.UUID) *PaymentConfirmationUpdateOne {
	if u != nil {
		pcuo.SetConfirmedBy(*u)
	}
	return pcuo
}

// ClearConfirmedBy clears the value of the "confirmed_by" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearConfirmedBy() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearConfirmedBy()
	return pcuo
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (pcuo *PaymentConfirmationUpdateOne) SetConfirmationMethod(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetConfirmationMethod(s)
	return pcuo
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableConfirmationMethod(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetConfirmationMethod(*s)
	}
	return pcuo
}

// SetConfirmationNotes sets the "confirmation_notes" field.
func (pcuo *PaymentConfirmationUpdateOne) SetConfirmationNotes(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetConfirmationNotes(s)
	return pcuo
}

// SetNillableConfirmationNotes sets the "confirmation_notes" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableConfirmationNotes(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetConfirmationNotes(*s)
	}
	return pcuo
}

// ClearConfirmationNotes clears the value of the "confirmation_notes" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearConfirmationNotes() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearConfirmationNotes()
	return pcuo
}

// SetPaymentDetails sets the "payment_details" field.
func (pcuo *PaymentConfirmationUpdateOne) SetPaymentDetails(m map[string]interface{}) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetPaymentDetails(m)
	return pcuo
}

// ClearPaymentDetails clears the value of the "payment_details" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearPaymentDetails() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearPaymentDetails()
	return pcuo
}

// SetWebhookData sets the "webhook_data" field.
func (pcuo *PaymentConfirmationUpdateOne) SetWebhookData(m map[string]interface{}) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetWebhookData(m)
	return pcuo
}

// ClearWebhookData clears the value of the "webhook_data" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearWebhookData() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearWebhookData()
	return pcuo
}

// SetPaymentDate sets the "payment_date" field.
func (pcuo *PaymentConfirmationUpdateOne) SetPaymentDate(t time.Time) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetPaymentDate(t)
	return pcuo
}

// SetNillablePaymentDate sets the "payment_date" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillablePaymentDate(t *time.Time) *PaymentConfirmationUpdateOne {
	if t != nil {
		pcuo.SetPaymentDate(*t)
	}
	return pcuo
}

// ClearPaymentDate clears the value of the "payment_date" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearPaymentDate() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearPaymentDate()
	return pcuo
}

// SetConfirmedAt sets the "confirmed_at" field.
func (pcuo *PaymentConfirmationUpdateOne) SetConfirmedAt(t time.Time) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetConfirmedAt(t)
	return pcuo
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableConfirmedAt(t *time.Time) *PaymentConfirmationUpdateOne {
	if t != nil {
		pcuo.SetConfirmedAt(*t)
	}
	return pcuo
}

// ClearConfirmedAt clears the value of the "confirmed_at" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearConfirmedAt() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearConfirmedAt()
	return pcuo
}

// SetFailureReason sets the "failure_reason" field.
func (pcuo *PaymentConfirmationUpdateOne) SetFailureReason(s string) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetFailureReason(s)
	return pcuo
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (pcuo *PaymentConfirmationUpdateOne) SetNillableFailureReason(s *string) *PaymentConfirmationUpdateOne {
	if s != nil {
		pcuo.SetFailureReason(*s)
	}
	return pcuo
}

// ClearFailureReason clears the value of the "failure_reason" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearFailureReason() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearFailureReason()
	return pcuo
}

// SetMetadata sets the "metadata" field.
func (pcuo *PaymentConfirmationUpdateOne) SetMetadata(m map[string]interface{}) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetMetadata(m)
	return pcuo
}

// ClearMetadata clears the value of the "metadata" field.
func (pcuo *PaymentConfirmationUpdateOne) ClearMetadata() *PaymentConfirmationUpdateOne {
	pcuo.mutation.ClearMetadata()
	return pcuo
}

// SetUpdatedAt sets the "updated_at" field.
func (pcuo *PaymentConfirmationUpdateOne) SetUpdatedAt(t time.Time) *PaymentConfirmationUpdateOne {
	pcuo.mutation.SetUpdatedAt(t)
	return pcuo
}

// Mutation returns the PaymentConfirmationMutation object of the builder.
func (pcuo *PaymentConfirmationUpdateOne) Mutation() *PaymentConfirmationMutation {
	return pcuo.mutation
}

// Where appends a list predicates to the PaymentConfirmationUpdate builder.
func (pcuo *PaymentConfirmationUpdateOne) Where(ps ...predicate.PaymentConfirmation) *PaymentConfirmationUpdateOne {
	pcuo.mutation.Where(ps...)
	return pcuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (pcuo *PaymentConfirmationUpdateOne) Select(field string, fields ...string) *PaymentConfirmationUpdateOne {
	pcuo.fields = append([]string{field}, fields...)
	return pcuo
}

// Save executes the query and returns the updated PaymentConfirmation entity.
func (pcuo *PaymentConfirmationUpdateOne) Save(ctx context.Context) (*PaymentConfirmation, error) {
	pcuo.defaults()
	return withHooks(ctx, pcuo.sqlSave, pcuo.mutation, pcuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pcuo *PaymentConfirmationUpdateOne) SaveX(ctx context.Context) *PaymentConfirmation {
	node, err := pcuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (pcuo *PaymentConfirmationUpdateOne) Exec(ctx context.Context) error {
	_, err := pcuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcuo *PaymentConfirmationUpdateOne) ExecX(ctx context.Context) {
	if err := pcuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pcuo *PaymentConfirmationUpdateOne) defaults() {
	if _, ok := pcuo.mutation.UpdatedAt(); !ok {
		v := paymentconfirmation.UpdateDefaultUpdatedAt()
		pcuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pcuo *PaymentConfirmationUpdateOne) check() error {
	if v, ok := pcuo.mutation.PaymentMethod(); ok {
		if err := paymentconfirmation.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.payment_method": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.ExternalPaymentID(); ok {
		if err := paymentconfirmation.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.external_payment_id": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.BankTransactionID(); ok {
		if err := paymentconfirmation.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.bank_transaction_id": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.AmountPaid(); ok {
		if err := paymentconfirmation.AmountPaidValidator(v); err != nil {
			return &ValidationError{Name: "amount_paid", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.amount_paid": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.Currency(); ok {
		if err := paymentconfirmation.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.currency": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.Status(); ok {
		if err := paymentconfirmation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.status": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.ConfirmationMethod(); ok {
		if err := paymentconfirmation.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.confirmation_method": %w`, err)}
		}
	}
	if v, ok := pcuo.mutation.FailureReason(); ok {
		if err := paymentconfirmation.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "PaymentConfirmation.failure_reason": %w`, err)}
		}
	}
	return nil
}

func (pcuo *PaymentConfirmationUpdateOne) sqlSave(ctx context.Context) (_node *PaymentConfirmation, err error) {
	if err := pcuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(paymentconfirmation.Table, paymentconfirmation.Columns, sqlgraph.NewFieldSpec(paymentconfirmation.FieldID, field.TypeUUID))
	id, ok := pcuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "PaymentConfirmation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := pcuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, paymentconfirmation.FieldID)
		for _, f := range fields {
			if !paymentconfirmation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != paymentconfirmation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := pcuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pcuo.mutation.BankTransferID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransferID, field.TypeUUID, value)
	}
	if pcuo.mutation.BankTransferIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldBankTransferID, field.TypeUUID)
	}
	if value, ok := pcuo.mutation.OrderID(); ok {
		_spec.SetField(paymentconfirmation.FieldOrderID, field.TypeUUID, value)
	}
	if pcuo.mutation.OrderIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldOrderID, field.TypeUUID)
	}
	if value, ok := pcuo.mutation.SubscriptionID(); ok {
		_spec.SetField(paymentconfirmation.FieldSubscriptionID, field.TypeUUID, value)
	}
	if pcuo.mutation.SubscriptionIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldSubscriptionID, field.TypeUUID)
	}
	if value, ok := pcuo.mutation.PaymentMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := pcuo.mutation.ExternalPaymentID(); ok {
		_spec.SetField(paymentconfirmation.FieldExternalPaymentID, field.TypeString, value)
	}
	if pcuo.mutation.ExternalPaymentIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldExternalPaymentID, field.TypeString)
	}
	if value, ok := pcuo.mutation.BankTransactionID(); ok {
		_spec.SetField(paymentconfirmation.FieldBankTransactionID, field.TypeString, value)
	}
	if pcuo.mutation.BankTransactionIDCleared() {
		_spec.ClearField(paymentconfirmation.FieldBankTransactionID, field.TypeString)
	}
	if value, ok := pcuo.mutation.AmountPaid(); ok {
		_spec.SetField(paymentconfirmation.FieldAmountPaid, field.TypeInt64, value)
	}
	if value, ok := pcuo.mutation.AddedAmountPaid(); ok {
		_spec.AddField(paymentconfirmation.FieldAmountPaid, field.TypeInt64, value)
	}
	if value, ok := pcuo.mutation.Currency(); ok {
		_spec.SetField(paymentconfirmation.FieldCurrency, field.TypeString, value)
	}
	if value, ok := pcuo.mutation.Status(); ok {
		_spec.SetField(paymentconfirmation.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := pcuo.mutation.ConfirmedBy(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedBy, field.TypeUUID, value)
	}
	if pcuo.mutation.ConfirmedByCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmedBy, field.TypeUUID)
	}
	if value, ok := pcuo.mutation.ConfirmationMethod(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationMethod, field.TypeString, value)
	}
	if value, ok := pcuo.mutation.ConfirmationNotes(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmationNotes, field.TypeString, value)
	}
	if pcuo.mutation.ConfirmationNotesCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmationNotes, field.TypeString)
	}
	if value, ok := pcuo.mutation.PaymentDetails(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDetails, field.TypeJSON, value)
	}
	if pcuo.mutation.PaymentDetailsCleared() {
		_spec.ClearField(paymentconfirmation.FieldPaymentDetails, field.TypeJSON)
	}
	if value, ok := pcuo.mutation.WebhookData(); ok {
		_spec.SetField(paymentconfirmation.FieldWebhookData, field.TypeJSON, value)
	}
	if pcuo.mutation.WebhookDataCleared() {
		_spec.ClearField(paymentconfirmation.FieldWebhookData, field.TypeJSON)
	}
	if value, ok := pcuo.mutation.PaymentDate(); ok {
		_spec.SetField(paymentconfirmation.FieldPaymentDate, field.TypeTime, value)
	}
	if pcuo.mutation.PaymentDateCleared() {
		_spec.ClearField(paymentconfirmation.FieldPaymentDate, field.TypeTime)
	}
	if value, ok := pcuo.mutation.ConfirmedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldConfirmedAt, field.TypeTime, value)
	}
	if pcuo.mutation.ConfirmedAtCleared() {
		_spec.ClearField(paymentconfirmation.FieldConfirmedAt, field.TypeTime)
	}
	if value, ok := pcuo.mutation.FailureReason(); ok {
		_spec.SetField(paymentconfirmation.FieldFailureReason, field.TypeString, value)
	}
	if pcuo.mutation.FailureReasonCleared() {
		_spec.ClearField(paymentconfirmation.FieldFailureReason, field.TypeString)
	}
	if value, ok := pcuo.mutation.Metadata(); ok {
		_spec.SetField(paymentconfirmation.FieldMetadata, field.TypeJSON, value)
	}
	if pcuo.mutation.MetadataCleared() {
		_spec.ClearField(paymentconfirmation.FieldMetadata, field.TypeJSON)
	}
	if value, ok := pcuo.mutation.UpdatedAt(); ok {
		_spec.SetField(paymentconfirmation.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &PaymentConfirmation{config: pcuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, pcuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{paymentconfirmation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	pcuo.mutation.done = true
	return _node, nil
}

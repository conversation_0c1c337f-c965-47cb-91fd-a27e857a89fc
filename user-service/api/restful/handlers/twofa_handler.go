package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/usecase/twofa"
)

// TwoFAHandler handles Two-Factor Authentication HTTP requests
type TwoFAHandler struct {
	twofaUseCase twofa.UseCase
	logger       logging.Logger
}

// NewTwoFAHandler creates a new 2FA handler
func NewTwoFAHandler(twofaUseCase twofa.UseCase, logger logging.Logger) *TwoFAHandler {
	return &TwoFAHandler{
		twofaUseCase: twofaUseCase,
		logger:       logger,
	}
}

// SetupTwoFA godoc
// @Summary Setup Two-Factor Authentication
// @Description Generate QR code and backup codes for setting up 2FA. User must verify with TOTP token to enable.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SetupTwoFAResponse "2FA setup data with QR code and backup codes"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 409 {object} models.ErrorResponse "2FA already enabled"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/setup [post]
func (h *TwoFAHandler) SetupTwoFA(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	req := &twofa.SetupTwoFARequest{
		UserID: userID.(uint64),
	}

	resp, err := h.twofaUseCase.SetupTwoFA(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to setup 2FA")

		switch err.Error() {
		case "2FA is already enabled for this user":
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "TWOFA_ALREADY_ENABLED",
				Message: "2FA is already enabled for this user",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "SETUP_TWOFA_FAILED",
				Message: "Failed to setup 2FA",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SetupTwoFAResponse{
		Secret:      resp.Secret,
		QRCodeURL:   resp.QRCodeURL,
		QRCode:      resp.QRCode,
		BackupCodes: resp.BackupCodes,
	})
}

// EnableTwoFA godoc
// @Summary Enable Two-Factor Authentication
// @Description Enable 2FA by verifying the TOTP token from authenticator app.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.EnableTwoFARequest true "TOTP token for verification"
// @Success 200 {object} models.EnableTwoFAResponse "2FA successfully enabled"
// @Failure 400 {object} models.ErrorResponse "Invalid token or request"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 409 {object} models.ErrorResponse "2FA already enabled"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/enable [post]
func (h *TwoFAHandler) EnableTwoFA(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.EnableTwoFARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid enable 2FA request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	enableReq := &twofa.EnableTwoFARequest{
		UserID: userID.(uint64),
		Token:  req.Token,
	}

	resp, err := h.twofaUseCase.EnableTwoFA(c.Request.Context(), enableReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to enable 2FA")

		switch err.Error() {
		case "2FA is already enabled for this user":
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "TWOFA_ALREADY_ENABLED",
				Message: "2FA is already enabled for this user",
			})
		case "invalid token provided":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid TOTP token provided",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "ENABLE_TWOFA_FAILED",
				Message: "Failed to enable 2FA",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.EnableTwoFAResponse{
		Enabled:     resp.Enabled,
		BackupCodes: resp.BackupCodes,
		EnabledAt:   resp.EnabledAt,
	})
}

// DisableTwoFA godoc
// @Summary Disable Two-Factor Authentication
// @Description Disable 2FA by providing password and optionally TOTP token or backup code.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.DisableTwoFARequest true "Password and optional token for verification"
// @Success 200 {object} models.SuccessResponse "2FA successfully disabled"
// @Failure 400 {object} models.ErrorResponse "Invalid password or token"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 404 {object} models.ErrorResponse "2FA not enabled"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/disable [post]
func (h *TwoFAHandler) DisableTwoFA(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.DisableTwoFARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid disable 2FA request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	disableReq := &twofa.DisableTwoFARequest{
		UserID:   userID.(uint64),
		Password: req.Password,
		Token:    req.Token,
	}

	err := h.twofaUseCase.DisableTwoFA(c.Request.Context(), disableReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to disable 2FA")

		switch err.Error() {
		case "2FA is not enabled for this user":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TWOFA_NOT_ENABLED",
				Message: "2FA is not enabled for this user",
			})
		case "invalid password provided":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_PASSWORD",
				Message: "Invalid password provided",
			})
		case "invalid token or backup code provided":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid token or backup code provided",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DISABLE_TWOFA_FAILED",
				Message: "Failed to disable 2FA",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "2FA disabled successfully",
	})
}

// VerifyTwoFA godoc
// @Summary Verify Two-Factor Authentication
// @Description Verify TOTP token or backup code during login process.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.VerifyTwoFARequest true "TOTP token or backup code"
// @Success 200 {object} models.VerifyTwoFAResponse "Token verification result"
// @Failure 400 {object} models.ErrorResponse "Invalid token"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 404 {object} models.ErrorResponse "2FA not enabled"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/verify [post]
func (h *TwoFAHandler) VerifyTwoFA(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.VerifyTwoFARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid verify 2FA request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	verifyReq := &twofa.VerifyTwoFARequest{
		UserID: userID.(uint64),
		Token:  req.Token,
	}

	resp, err := h.twofaUseCase.VerifyTwoFA(c.Request.Context(), verifyReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to verify 2FA")

		switch err.Error() {
		case "2FA is not enabled for this user":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TWOFA_NOT_ENABLED",
				Message: "2FA is not enabled for this user",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "VERIFY_TWOFA_FAILED",
				Message: "Failed to verify 2FA",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.VerifyTwoFAResponse{
		Valid:          resp.Valid,
		BackupCodeUsed: resp.BackupCodeUsed,
		RemainingCodes: resp.RemainingCodes,
		Message:        resp.Message,
	})
}

// GenerateBackupCodes godoc
// @Summary Generate new backup codes
// @Description Generate new backup codes for 2FA recovery. Requires password verification.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.GenerateBackupCodesRequest true "Password for verification"
// @Success 200 {object} models.GenerateBackupCodesResponse "New backup codes generated"
// @Failure 400 {object} models.ErrorResponse "Invalid password"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 404 {object} models.ErrorResponse "2FA not enabled"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/backup-codes [post]
func (h *TwoFAHandler) GenerateBackupCodes(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.GenerateBackupCodesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid generate backup codes request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	generateReq := &twofa.GenerateBackupCodesRequest{
		UserID:   userID.(uint64),
		Password: req.Password,
	}

	resp, err := h.twofaUseCase.GenerateBackupCodes(c.Request.Context(), generateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to generate backup codes")

		switch err.Error() {
		case "2FA is not enabled for this user":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TWOFA_NOT_ENABLED",
				Message: "2FA is not enabled for this user",
			})
		case "invalid password provided":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_PASSWORD",
				Message: "Invalid password provided",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GENERATE_BACKUP_CODES_FAILED",
				Message: "Failed to generate backup codes",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.GenerateBackupCodesResponse{
		BackupCodes: resp.BackupCodes,
		GeneratedAt: resp.GeneratedAt,
	})
}

// GetTwoFAStatus godoc
// @Summary Get 2FA status
// @Description Get the current 2FA status for the authenticated user.
// @Tags auth,2fa
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.GetTwoFAStatusResponse "2FA status information"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/2fa/status [get]
func (h *TwoFAHandler) GetTwoFAStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	resp, err := h.twofaUseCase.GetTwoFAStatus(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get 2FA status")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_TWOFA_STATUS_FAILED",
			Message: "Failed to get 2FA status",
		})
		return
	}

	c.JSON(http.StatusOK, models.GetTwoFAStatusResponse{
		UserID:             resp.UserID,
		TwoFAEnabled:       resp.TwoFAEnabled,
		EnabledAt:          resp.EnabledAt,
		BackupCodesCount:   resp.BackupCodesCount,
		HasBackupCodes:     resp.HasBackupCodes,
		LastVerificationAt: resp.LastVerificationAt,
	})
}

// Code generated by ent, DO NOT EDIT.

package usercredit

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the usercredit type in the database.
	Label = "user_credit"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldCurrentCredits holds the string denoting the current_credits field in the database.
	FieldCurrentCredits = "current_credits"
	// FieldTotalCredits holds the string denoting the total_credits field in the database.
	FieldTotalCredits = "total_credits"
	// FieldPlanID holds the string denoting the plan_id field in the database.
	FieldPlanID = "plan_id"
	// FieldPlanName holds the string denoting the plan_name field in the database.
	FieldPlanName = "plan_name"
	// FieldPlanExpiresAt holds the string denoting the plan_expires_at field in the database.
	FieldPlanExpiresAt = "plan_expires_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldMonthlyLimit holds the string denoting the monthly_limit field in the database.
	FieldMonthlyLimit = "monthly_limit"
	// FieldMonthlyUsed holds the string denoting the monthly_used field in the database.
	FieldMonthlyUsed = "monthly_used"
	// FieldMonthlyResetAt holds the string denoting the monthly_reset_at field in the database.
	FieldMonthlyResetAt = "monthly_reset_at"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the usercredit in the database.
	Table = "user_credits"
)

// Columns holds all SQL columns for usercredit fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldCurrentCredits,
	FieldTotalCredits,
	FieldPlanID,
	FieldPlanName,
	FieldPlanExpiresAt,
	FieldStatus,
	FieldMonthlyLimit,
	FieldMonthlyUsed,
	FieldMonthlyResetAt,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCurrentCredits holds the default value on creation for the "current_credits" field.
	DefaultCurrentCredits int
	// CurrentCreditsValidator is a validator for the "current_credits" field. It is called by the builders before save.
	CurrentCreditsValidator func(int) error
	// DefaultTotalCredits holds the default value on creation for the "total_credits" field.
	DefaultTotalCredits int
	// TotalCreditsValidator is a validator for the "total_credits" field. It is called by the builders before save.
	TotalCreditsValidator func(int) error
	// PlanIDValidator is a validator for the "plan_id" field. It is called by the builders before save.
	PlanIDValidator func(string) error
	// PlanNameValidator is a validator for the "plan_name" field. It is called by the builders before save.
	PlanNameValidator func(string) error
	// DefaultMonthlyLimit holds the default value on creation for the "monthly_limit" field.
	DefaultMonthlyLimit int
	// MonthlyLimitValidator is a validator for the "monthly_limit" field. It is called by the builders before save.
	MonthlyLimitValidator func(int) error
	// DefaultMonthlyUsed holds the default value on creation for the "monthly_used" field.
	DefaultMonthlyUsed int
	// MonthlyUsedValidator is a validator for the "monthly_used" field. It is called by the builders before save.
	MonthlyUsedValidator func(int) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusActive is the default value of the Status enum.
const DefaultStatus = StatusActive

// Status values.
const (
	StatusActive    Status = "active"
	StatusSuspended Status = "suspended"
	StatusCancelled Status = "cancelled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusActive, StatusSuspended, StatusCancelled:
		return nil
	default:
		return fmt.Errorf("usercredit: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the UserCredit queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByCurrentCredits orders the results by the current_credits field.
func ByCurrentCredits(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrentCredits, opts...).ToFunc()
}

// ByTotalCredits orders the results by the total_credits field.
func ByTotalCredits(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalCredits, opts...).ToFunc()
}

// ByPlanID orders the results by the plan_id field.
func ByPlanID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanID, opts...).ToFunc()
}

// ByPlanName orders the results by the plan_name field.
func ByPlanName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanName, opts...).ToFunc()
}

// ByPlanExpiresAt orders the results by the plan_expires_at field.
func ByPlanExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanExpiresAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByMonthlyLimit orders the results by the monthly_limit field.
func ByMonthlyLimit(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonthlyLimit, opts...).ToFunc()
}

// ByMonthlyUsed orders the results by the monthly_used field.
func ByMonthlyUsed(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonthlyUsed, opts...).ToFunc()
}

// ByMonthlyResetAt orders the results by the monthly_reset_at field.
func ByMonthlyResetAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonthlyResetAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

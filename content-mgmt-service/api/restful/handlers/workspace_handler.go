package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/content-mgmt-service/usecase/workspace"
	"github.com/social-content-ai/pkg-shared/logging"
)

// WorkspaceHandler handles workspace-related HTTP requests
type WorkspaceHandler struct {
	workspaceUseCase workspace.UseCase
	logger           logging.Logger
}

// NewWorkspaceHandler creates a new workspace handler
func NewWorkspaceHandler(workspaceUseCase workspace.UseCase, logger logging.Logger) *WorkspaceHandler {
	return &WorkspaceHandler{
		workspaceUseCase: workspaceUseCase,
		logger:           logger,
	}
}

// CreateWorkspace godoc
// @Summary Create a new workspace
// @Description Create a new team workspace
// @Tags workspaces
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.CreateWorkspaceRequest true "Create workspace request"
// @Success 201 {object} models.WorkspaceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/workspaces [post]
func (h *WorkspaceHandler) CreateWorkspace(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.CreateWorkspaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid create workspace request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.OwnerID = userID.(string)

	workspace, err := h.workspaceUseCase.CreateWorkspace(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create workspace")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CREATE_FAILED",
			Message: "Failed to create workspace",
		})
		return
	}

	c.JSON(http.StatusCreated, workspace)
}

// ListWorkspaces godoc
// @Summary List workspaces
// @Description List user's workspaces
// @Tags workspaces
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} models.ListWorkspacesResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/workspaces [get]
func (h *WorkspaceHandler) ListWorkspaces(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	req := &models.ListWorkspacesRequest{
		UserID: userID.(string),
		Page:   page,
		Limit:  limit,
	}

	resp, err := h.workspaceUseCase.ListWorkspaces(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list workspaces")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list workspaces",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetWorkspace godoc
// @Summary Get workspace by ID
// @Description Get workspace information by ID
// @Tags workspaces
// @Produce json
// @Security BearerAuth
// @Param id path string true "Workspace ID"
// @Success 200 {object} models.WorkspaceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/workspaces/{id} [get]
func (h *WorkspaceHandler) GetWorkspace(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	workspaceID := c.Param("id")
	if workspaceID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_WORKSPACE_ID",
			Message: "Workspace ID is required",
		})
		return
	}

	workspace, err := h.workspaceUseCase.GetWorkspace(c.Request.Context(), workspaceID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get workspace")

		switch err.Error() {
		case "WORKSPACE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "WORKSPACE_NOT_FOUND",
				Message: "Workspace not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to this workspace",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_WORKSPACE_FAILED",
				Message: "Failed to get workspace",
			})
		}
		return
	}

	c.JSON(http.StatusOK, workspace)
}

// UpdateWorkspace godoc
// @Summary Update workspace
// @Description Update workspace information
// @Tags workspaces
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Workspace ID"
// @Param request body models.UpdateWorkspaceRequest true "Update workspace request"
// @Success 200 {object} models.WorkspaceResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/workspaces/{id} [put]
func (h *WorkspaceHandler) UpdateWorkspace(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	workspaceID := c.Param("id")
	if workspaceID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_WORKSPACE_ID",
			Message: "Workspace ID is required",
		})
		return
	}

	var req models.UpdateWorkspaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update workspace request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = workspaceID
	req.UserID = userID.(string)

	workspace, err := h.workspaceUseCase.UpdateWorkspace(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update workspace")

		switch err.Error() {
		case "WORKSPACE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "WORKSPACE_NOT_FOUND",
				Message: "Workspace not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to update this workspace",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UPDATE_FAILED",
				Message: "Failed to update workspace",
			})
		}
		return
	}

	c.JSON(http.StatusOK, workspace)
}

// DeleteWorkspace godoc
// @Summary Delete workspace
// @Description Delete a workspace
// @Tags workspaces
// @Produce json
// @Security BearerAuth
// @Param id path string true "Workspace ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/workspaces/{id} [delete]
func (h *WorkspaceHandler) DeleteWorkspace(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	workspaceID := c.Param("id")
	if workspaceID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_WORKSPACE_ID",
			Message: "Workspace ID is required",
		})
		return
	}

	err := h.workspaceUseCase.DeleteWorkspace(c.Request.Context(), workspaceID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete workspace")

		switch err.Error() {
		case "WORKSPACE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "WORKSPACE_NOT_FOUND",
				Message: "Workspace not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to delete this workspace",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DELETE_FAILED",
				Message: "Failed to delete workspace",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Workspace deleted successfully",
	})
}

// AddMember adds a member to workspace
func (h *WorkspaceHandler) AddMember(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Add member functionality not implemented yet",
	})
}

// ListMembers lists workspace members
func (h *WorkspaceHandler) ListMembers(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "List members functionality not implemented yet",
	})
}

// UpdateMemberRole updates member role
func (h *WorkspaceHandler) UpdateMemberRole(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Update member role functionality not implemented yet",
	})
}

// RemoveMember removes a member from workspace
func (h *WorkspaceHandler) RemoveMember(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Remove member functionality not implemented yet",
	})
}

// GetWorkspacePosts gets workspace posts
func (h *WorkspaceHandler) GetWorkspacePosts(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Get workspace posts functionality not implemented yet",
	})
}

// GetWorkspaceTemplates gets workspace templates
func (h *WorkspaceHandler) GetWorkspaceTemplates(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Get workspace templates functionality not implemented yet",
	})
}

// GetWorkspaceAnalytics gets workspace analytics
func (h *WorkspaceHandler) GetWorkspaceAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	workspaceID := c.Param("id")
	if workspaceID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_WORKSPACE_ID",
			Message: "Workspace ID is required",
		})
		return
	}

	req := &models.WorkspaceAnalyticsRequest{
		WorkspaceID: workspaceID,
		UserID:      userID.(string),
		DateFrom:    c.Query("date_from"),
		DateTo:      c.Query("date_to"),
	}

	analytics, err := h.workspaceUseCase.GetWorkspaceAnalytics(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get workspace analytics")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "ANALYTICS_FAILED",
			Message: "Failed to get workspace analytics",
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// UpdateWorkspaceSettings updates workspace settings
func (h *WorkspaceHandler) UpdateWorkspaceSettings(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Update workspace settings functionality not implemented yet",
	})
}

// GetWorkspaceSettings gets workspace settings
func (h *WorkspaceHandler) GetWorkspaceSettings(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Get workspace settings functionality not implemented yet",
	})
}

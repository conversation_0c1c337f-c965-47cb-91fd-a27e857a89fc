// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
)

// CreditReservation is the model entity for the CreditReservation schema.
type CreditReservation struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Amount holds the value of the "amount" field.
	Amount int `json:"amount,omitempty"`
	// Purpose holds the value of the "purpose" field.
	Purpose string `json:"purpose,omitempty"`
	// ReferenceID holds the value of the "reference_id" field.
	ReferenceID string `json:"reference_id,omitempty"`
	// ReferenceType holds the value of the "reference_type" field.
	ReferenceType string `json:"reference_type,omitempty"`
	// Status holds the value of the "status" field.
	Status creditreservation.Status `json:"status,omitempty"`
	// ExpiresAt holds the value of the "expires_at" field.
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	// ConsumedAt holds the value of the "consumed_at" field.
	ConsumedAt time.Time `json:"consumed_at,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CreditReservation) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case creditreservation.FieldMetadata:
			values[i] = new([]byte)
		case creditreservation.FieldAmount:
			values[i] = new(sql.NullInt64)
		case creditreservation.FieldPurpose, creditreservation.FieldReferenceID, creditreservation.FieldReferenceType, creditreservation.FieldStatus:
			values[i] = new(sql.NullString)
		case creditreservation.FieldExpiresAt, creditreservation.FieldConsumedAt, creditreservation.FieldCreatedAt, creditreservation.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case creditreservation.FieldID, creditreservation.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CreditReservation fields.
func (cr *CreditReservation) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case creditreservation.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				cr.ID = *value
			}
		case creditreservation.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				cr.UserID = *value
			}
		case creditreservation.FieldAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				cr.Amount = int(value.Int64)
			}
		case creditreservation.FieldPurpose:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field purpose", values[i])
			} else if value.Valid {
				cr.Purpose = value.String
			}
		case creditreservation.FieldReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_id", values[i])
			} else if value.Valid {
				cr.ReferenceID = value.String
			}
		case creditreservation.FieldReferenceType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_type", values[i])
			} else if value.Valid {
				cr.ReferenceType = value.String
			}
		case creditreservation.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				cr.Status = creditreservation.Status(value.String)
			}
		case creditreservation.FieldExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_at", values[i])
			} else if value.Valid {
				cr.ExpiresAt = value.Time
			}
		case creditreservation.FieldConsumedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field consumed_at", values[i])
			} else if value.Valid {
				cr.ConsumedAt = value.Time
			}
		case creditreservation.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &cr.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case creditreservation.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cr.CreatedAt = value.Time
			}
		case creditreservation.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				cr.UpdatedAt = value.Time
			}
		default:
			cr.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CreditReservation.
// This includes values selected through modifiers, order, etc.
func (cr *CreditReservation) Value(name string) (ent.Value, error) {
	return cr.selectValues.Get(name)
}

// Update returns a builder for updating this CreditReservation.
// Note that you need to call CreditReservation.Unwrap() before calling this method if this CreditReservation
// was returned from a transaction, and the transaction was committed or rolled back.
func (cr *CreditReservation) Update() *CreditReservationUpdateOne {
	return NewCreditReservationClient(cr.config).UpdateOne(cr)
}

// Unwrap unwraps the CreditReservation entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cr *CreditReservation) Unwrap() *CreditReservation {
	_tx, ok := cr.config.driver.(*txDriver)
	if !ok {
		panic("ent: CreditReservation is not a transactional entity")
	}
	cr.config.driver = _tx.drv
	return cr
}

// String implements the fmt.Stringer.
func (cr *CreditReservation) String() string {
	var builder strings.Builder
	builder.WriteString("CreditReservation(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cr.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", cr.UserID))
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", cr.Amount))
	builder.WriteString(", ")
	builder.WriteString("purpose=")
	builder.WriteString(cr.Purpose)
	builder.WriteString(", ")
	builder.WriteString("reference_id=")
	builder.WriteString(cr.ReferenceID)
	builder.WriteString(", ")
	builder.WriteString("reference_type=")
	builder.WriteString(cr.ReferenceType)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", cr.Status))
	builder.WriteString(", ")
	builder.WriteString("expires_at=")
	builder.WriteString(cr.ExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("consumed_at=")
	builder.WriteString(cr.ConsumedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", cr.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(cr.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(cr.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CreditReservations is a parsable slice of CreditReservation.
type CreditReservations []*CreditReservation

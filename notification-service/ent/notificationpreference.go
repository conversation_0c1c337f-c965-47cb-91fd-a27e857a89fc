// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
)

// NotificationPreference is the model entity for the NotificationPreference schema.
type NotificationPreference struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// User these preferences belong to
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Whether email notifications are enabled
	EmailEnabled bool `json:"email_enabled,omitempty"`
	// Whether push notifications are enabled
	PushEnabled bool `json:"push_enabled,omitempty"`
	// Whether in-app notifications are enabled
	InAppEnabled bool `json:"in_app_enabled,omitempty"`
	// Whether SMS notifications are enabled
	SmsEnabled bool `json:"sms_enabled,omitempty"`
	// Per-type notification preferences
	NotificationTypes map[string]interface{} `json:"notification_types,omitempty"`
	// Quiet hours configuration
	QuietHours map[string]interface{} `json:"quiet_hours,omitempty"`
	// User timezone for scheduling
	Timezone string `json:"timezone,omitempty"`
	// Preferred language for notifications
	Language string `json:"language,omitempty"`
	// Email-specific settings
	EmailSettings map[string]interface{} `json:"email_settings,omitempty"`
	// Push notification settings
	PushSettings map[string]interface{} `json:"push_settings,omitempty"`
	// Frequency settings for different notification types
	FrequencySettings map[string]interface{} `json:"frequency_settings,omitempty"`
	// Whether to receive marketing emails
	MarketingEmails bool `json:"marketing_emails,omitempty"`
	// Whether to receive product update notifications
	ProductUpdates bool `json:"product_updates,omitempty"`
	// Whether to receive security alerts
	SecurityAlerts bool `json:"security_alerts,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*NotificationPreference) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notificationpreference.FieldNotificationTypes, notificationpreference.FieldQuietHours, notificationpreference.FieldEmailSettings, notificationpreference.FieldPushSettings, notificationpreference.FieldFrequencySettings, notificationpreference.FieldMetadata:
			values[i] = new([]byte)
		case notificationpreference.FieldEmailEnabled, notificationpreference.FieldPushEnabled, notificationpreference.FieldInAppEnabled, notificationpreference.FieldSmsEnabled, notificationpreference.FieldMarketingEmails, notificationpreference.FieldProductUpdates, notificationpreference.FieldSecurityAlerts:
			values[i] = new(sql.NullBool)
		case notificationpreference.FieldTimezone, notificationpreference.FieldLanguage:
			values[i] = new(sql.NullString)
		case notificationpreference.FieldCreatedAt, notificationpreference.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case notificationpreference.FieldID, notificationpreference.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the NotificationPreference fields.
func (np *NotificationPreference) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notificationpreference.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				np.ID = *value
			}
		case notificationpreference.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				np.UserID = *value
			}
		case notificationpreference.FieldEmailEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field email_enabled", values[i])
			} else if value.Valid {
				np.EmailEnabled = value.Bool
			}
		case notificationpreference.FieldPushEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field push_enabled", values[i])
			} else if value.Valid {
				np.PushEnabled = value.Bool
			}
		case notificationpreference.FieldInAppEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field in_app_enabled", values[i])
			} else if value.Valid {
				np.InAppEnabled = value.Bool
			}
		case notificationpreference.FieldSmsEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field sms_enabled", values[i])
			} else if value.Valid {
				np.SmsEnabled = value.Bool
			}
		case notificationpreference.FieldNotificationTypes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field notification_types", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.NotificationTypes); err != nil {
					return fmt.Errorf("unmarshal field notification_types: %w", err)
				}
			}
		case notificationpreference.FieldQuietHours:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field quiet_hours", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.QuietHours); err != nil {
					return fmt.Errorf("unmarshal field quiet_hours: %w", err)
				}
			}
		case notificationpreference.FieldTimezone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field timezone", values[i])
			} else if value.Valid {
				np.Timezone = value.String
			}
		case notificationpreference.FieldLanguage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field language", values[i])
			} else if value.Valid {
				np.Language = value.String
			}
		case notificationpreference.FieldEmailSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field email_settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.EmailSettings); err != nil {
					return fmt.Errorf("unmarshal field email_settings: %w", err)
				}
			}
		case notificationpreference.FieldPushSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field push_settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.PushSettings); err != nil {
					return fmt.Errorf("unmarshal field push_settings: %w", err)
				}
			}
		case notificationpreference.FieldFrequencySettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field frequency_settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.FrequencySettings); err != nil {
					return fmt.Errorf("unmarshal field frequency_settings: %w", err)
				}
			}
		case notificationpreference.FieldMarketingEmails:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field marketing_emails", values[i])
			} else if value.Valid {
				np.MarketingEmails = value.Bool
			}
		case notificationpreference.FieldProductUpdates:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field product_updates", values[i])
			} else if value.Valid {
				np.ProductUpdates = value.Bool
			}
		case notificationpreference.FieldSecurityAlerts:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field security_alerts", values[i])
			} else if value.Valid {
				np.SecurityAlerts = value.Bool
			}
		case notificationpreference.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &np.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case notificationpreference.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				np.CreatedAt = value.Time
			}
		case notificationpreference.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				np.UpdatedAt = value.Time
			}
		default:
			np.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the NotificationPreference.
// This includes values selected through modifiers, order, etc.
func (np *NotificationPreference) Value(name string) (ent.Value, error) {
	return np.selectValues.Get(name)
}

// Update returns a builder for updating this NotificationPreference.
// Note that you need to call NotificationPreference.Unwrap() before calling this method if this NotificationPreference
// was returned from a transaction, and the transaction was committed or rolled back.
func (np *NotificationPreference) Update() *NotificationPreferenceUpdateOne {
	return NewNotificationPreferenceClient(np.config).UpdateOne(np)
}

// Unwrap unwraps the NotificationPreference entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (np *NotificationPreference) Unwrap() *NotificationPreference {
	_tx, ok := np.config.driver.(*txDriver)
	if !ok {
		panic("ent: NotificationPreference is not a transactional entity")
	}
	np.config.driver = _tx.drv
	return np
}

// String implements the fmt.Stringer.
func (np *NotificationPreference) String() string {
	var builder strings.Builder
	builder.WriteString("NotificationPreference(")
	builder.WriteString(fmt.Sprintf("id=%v, ", np.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", np.UserID))
	builder.WriteString(", ")
	builder.WriteString("email_enabled=")
	builder.WriteString(fmt.Sprintf("%v", np.EmailEnabled))
	builder.WriteString(", ")
	builder.WriteString("push_enabled=")
	builder.WriteString(fmt.Sprintf("%v", np.PushEnabled))
	builder.WriteString(", ")
	builder.WriteString("in_app_enabled=")
	builder.WriteString(fmt.Sprintf("%v", np.InAppEnabled))
	builder.WriteString(", ")
	builder.WriteString("sms_enabled=")
	builder.WriteString(fmt.Sprintf("%v", np.SmsEnabled))
	builder.WriteString(", ")
	builder.WriteString("notification_types=")
	builder.WriteString(fmt.Sprintf("%v", np.NotificationTypes))
	builder.WriteString(", ")
	builder.WriteString("quiet_hours=")
	builder.WriteString(fmt.Sprintf("%v", np.QuietHours))
	builder.WriteString(", ")
	builder.WriteString("timezone=")
	builder.WriteString(np.Timezone)
	builder.WriteString(", ")
	builder.WriteString("language=")
	builder.WriteString(np.Language)
	builder.WriteString(", ")
	builder.WriteString("email_settings=")
	builder.WriteString(fmt.Sprintf("%v", np.EmailSettings))
	builder.WriteString(", ")
	builder.WriteString("push_settings=")
	builder.WriteString(fmt.Sprintf("%v", np.PushSettings))
	builder.WriteString(", ")
	builder.WriteString("frequency_settings=")
	builder.WriteString(fmt.Sprintf("%v", np.FrequencySettings))
	builder.WriteString(", ")
	builder.WriteString("marketing_emails=")
	builder.WriteString(fmt.Sprintf("%v", np.MarketingEmails))
	builder.WriteString(", ")
	builder.WriteString("product_updates=")
	builder.WriteString(fmt.Sprintf("%v", np.ProductUpdates))
	builder.WriteString(", ")
	builder.WriteString("security_alerts=")
	builder.WriteString(fmt.Sprintf("%v", np.SecurityAlerts))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", np.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(np.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(np.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// NotificationPreferences is a parsable slice of NotificationPreference.
type NotificationPreferences []*NotificationPreference

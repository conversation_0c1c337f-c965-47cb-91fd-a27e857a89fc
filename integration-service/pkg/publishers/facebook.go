package publishers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// FacebookPublisher handles Facebook content publishing
type FacebookPublisher struct {
	httpClient *http.Client
	logger     logging.Logger
}

// NewFacebookPublisher creates a new Facebook publisher
func NewFacebookPublisher(logger logging.Logger) Publisher {
	return &FacebookPublisher{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// FacebookPostRequest represents a Facebook post request
type FacebookPostRequest struct {
	Message     string   `json:"message,omitempty"`
	Link        string   `json:"link,omitempty"`
	PhotoURLs   []string `json:"photo_urls,omitempty"`
	VideoURL    string   `json:"video_url,omitempty"`
	Scheduled   bool     `json:"scheduled,omitempty"`
	PublishTime int64    `json:"publish_time,omitempty"`
}

// FacebookPostResponse represents Facebook post response
type FacebookPostResponse struct {
	ID          string `json:"id"`
	PostID      string `json:"post_id,omitempty"`
	Message     string `json:"message,omitempty"`
	CreatedTime string `json:"created_time,omitempty"`
}

// FacebookErrorResponse represents Facebook API error
type FacebookErrorResponse struct {
	Error struct {
		Message   string `json:"message"`
		Type      string `json:"type"`
		Code      int    `json:"code"`
		Subcode   int    `json:"error_subcode,omitempty"`
		UserTitle string `json:"error_user_title,omitempty"`
		UserMsg   string `json:"error_user_msg,omitempty"`
	} `json:"error"`
}

// PublishFacebookPost publishes a post to Facebook page
func (f *FacebookPublisher) PublishFacebookPost(ctx context.Context, pageID, accessToken string, req *FacebookPostRequest) (*FacebookPostResponse, error) {
	postURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/feed", pageID)

	// Prepare post data
	postData := url.Values{}

	if req.Message != "" {
		postData.Set("message", req.Message)
	}

	if req.Link != "" {
		postData.Set("link", req.Link)
	}

	if req.Scheduled && req.PublishTime > 0 {
		postData.Set("published", "false")
		postData.Set("scheduled_publish_time", fmt.Sprintf("%d", req.PublishTime))
	}

	postData.Set("access_token", accessToken)

	// Handle photo uploads
	if len(req.PhotoURLs) > 0 {
		return f.publishPhotoPost(ctx, pageID, accessToken, req)
	}

	// Handle video upload
	if req.VideoURL != "" {
		return f.publishVideoPost(ctx, pageID, accessToken, req)
	}

	// Regular text/link post
	httpReq, err := http.NewRequestWithContext(ctx, "POST", postURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to publish post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("Facebook API error: %s (code: %d)", errorResp.Error.Message, errorResp.Error.Code)
	}

	var postResp FacebookPostResponse
	if err := json.NewDecoder(resp.Body).Decode(&postResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"page_id": pageID,
		"post_id": postResp.ID,
	}).Info("Facebook post published successfully")

	return &postResp, nil
}

// publishPhotoPost publishes a post with photos
func (f *FacebookPublisher) publishPhotoPost(ctx context.Context, pageID, accessToken string, req *FacebookPostRequest) (*FacebookPostResponse, error) {
	// For multiple photos, we need to create a batch upload
	if len(req.PhotoURLs) > 1 {
		return f.publishMultiPhotoPost(ctx, pageID, accessToken, req)
	}

	// Single photo post
	photoURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/photos", pageID)

	postData := url.Values{}
	postData.Set("url", req.PhotoURLs[0])
	postData.Set("access_token", accessToken)

	if req.Message != "" {
		postData.Set("caption", req.Message)
	}

	if req.Scheduled && req.PublishTime > 0 {
		postData.Set("published", "false")
		postData.Set("scheduled_publish_time", fmt.Sprintf("%d", req.PublishTime))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", photoURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create photo request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to publish photo: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("Facebook photo API error: %s", errorResp.Error.Message)
	}

	var photoResp FacebookPostResponse
	if err := json.NewDecoder(resp.Body).Decode(&photoResp); err != nil {
		return nil, fmt.Errorf("failed to decode photo response: %w", err)
	}

	return &photoResp, nil
}

// publishMultiPhotoPost publishes a post with multiple photos
func (f *FacebookPublisher) publishMultiPhotoPost(ctx context.Context, pageID, accessToken string, req *FacebookPostRequest) (*FacebookPostResponse, error) {
	// First, upload all photos and get their IDs
	var photoIDs []string

	for _, photoURL := range req.PhotoURLs {
		photoID, err := f.uploadPhoto(ctx, pageID, accessToken, photoURL, false)
		if err != nil {
			return nil, fmt.Errorf("failed to upload photo %s: %w", photoURL, err)
		}
		photoIDs = append(photoIDs, photoID)
	}

	// Create the multi-photo post
	postURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/feed", pageID)

	// Prepare attached media
	var attachedMedia []map[string]string
	for _, photoID := range photoIDs {
		attachedMedia = append(attachedMedia, map[string]string{
			"media_fbid": photoID,
		})
	}

	attachedMediaJSON, _ := json.Marshal(attachedMedia)

	postData := url.Values{}
	postData.Set("attached_media", string(attachedMediaJSON))
	postData.Set("access_token", accessToken)

	if req.Message != "" {
		postData.Set("message", req.Message)
	}

	if req.Scheduled && req.PublishTime > 0 {
		postData.Set("published", "false")
		postData.Set("scheduled_publish_time", fmt.Sprintf("%d", req.PublishTime))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", postURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create multi-photo request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to publish multi-photo post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("Facebook multi-photo API error: %s", errorResp.Error.Message)
	}

	var postResp FacebookPostResponse
	if err := json.NewDecoder(resp.Body).Decode(&postResp); err != nil {
		return nil, fmt.Errorf("failed to decode multi-photo response: %w", err)
	}

	return &postResp, nil
}

// uploadPhoto uploads a photo to Facebook
func (f *FacebookPublisher) uploadPhoto(ctx context.Context, pageID, accessToken, photoURL string, published bool) (string, error) {
	uploadURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/photos", pageID)

	postData := url.Values{}
	postData.Set("url", photoURL)
	postData.Set("published", fmt.Sprintf("%t", published))
	postData.Set("access_token", accessToken)

	httpReq, err := http.NewRequestWithContext(ctx, "POST", uploadURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create upload request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to upload photo: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return "", fmt.Errorf("Facebook upload API error: %s", errorResp.Error.Message)
	}

	var uploadResp struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&uploadResp); err != nil {
		return "", fmt.Errorf("failed to decode upload response: %w", err)
	}

	return uploadResp.ID, nil
}

// publishVideoPost publishes a video post
func (f *FacebookPublisher) publishVideoPost(ctx context.Context, pageID, accessToken string, req *FacebookPostRequest) (*FacebookPostResponse, error) {
	videoURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/videos", pageID)

	postData := url.Values{}
	postData.Set("file_url", req.VideoURL)
	postData.Set("access_token", accessToken)

	if req.Message != "" {
		postData.Set("description", req.Message)
	}

	if req.Scheduled && req.PublishTime > 0 {
		postData.Set("published", "false")
		postData.Set("scheduled_publish_time", fmt.Sprintf("%d", req.PublishTime))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", videoURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create video request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to publish video: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("Facebook video API error: %s", errorResp.Error.Message)
	}

	var videoResp FacebookPostResponse
	if err := json.NewDecoder(resp.Body).Decode(&videoResp); err != nil {
		return nil, fmt.Errorf("failed to decode video response: %w", err)
	}

	return &videoResp, nil
}

// DeleteFacebookPost deletes a Facebook post
func (f *FacebookPublisher) DeleteFacebookPost(ctx context.Context, postID, accessToken string) error {
	deleteURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s", postID)

	params := url.Values{}
	params.Set("access_token", accessToken)

	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", fmt.Sprintf("%s?%s", deleteURL, params.Encode()), nil)
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return fmt.Errorf("Facebook delete API error: %s", errorResp.Error.Message)
	}

	f.logger.WithField("post_id", postID).Info("Facebook post deleted successfully")
	return nil
}

// GetPostInsights retrieves post insights/analytics
func (f *FacebookPublisher) GetPostInsights(ctx context.Context, postID, accessToken string) (map[string]interface{}, error) {
	insightsURL := fmt.Sprintf("https://graph.facebook.com/v23.0/%s/insights", postID)

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("metric", "post_impressions,post_engaged_users,post_clicks,post_reactions_like_total")

	httpReq, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", insightsURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create insights request: %w", err)
	}

	resp, err := f.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get insights: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp FacebookErrorResponse
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return nil, fmt.Errorf("Facebook insights API error: %s", errorResp.Error.Message)
	}

	var insights map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&insights); err != nil {
		return nil, fmt.Errorf("failed to decode insights response: %w", err)
	}

	return insights, nil
}

// Interface implementation methods

// PublishPost implements Publisher interface
func (f *FacebookPublisher) PublishPost(ctx context.Context, req *PublishRequest) (*PublishResponse, error) {
	// Convert PublishRequest to FacebookPostRequest
	fbReq := &FacebookPostRequest{
		Message:     req.Content,
		PhotoURLs:   req.ImageURLs,
		VideoURL:    req.VideoURL,
		Scheduled:   req.ScheduledAt > 0,
		PublishTime: req.ScheduledAt,
	}

	// Extract page ID from platform options
	pageID, exists := req.PlatformOptions["page_id"]
	if !exists {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: "page_id is required in platform_options",
		}, nil
	}

	// Call the original method
	fbResp, err := f.PublishFacebookPost(ctx, pageID, req.AccessToken, fbReq)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		}, nil
	}

	// Convert response
	return &PublishResponse{
		PlatformPostID: fbResp.ID,
		PlatformURL:    fmt.Sprintf("https://facebook.com/%s", fbResp.ID),
		Success:        true,
		PublishedAt:    time.Now().Unix(),
		Metadata: map[string]string{
			"post_id":      fbResp.PostID,
			"created_time": fbResp.CreatedTime,
		},
	}, nil
}

// GetPostAnalytics implements Publisher interface
func (f *FacebookPublisher) GetPostAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error) {
	insights, err := f.GetPostInsights(ctx, req.PlatformPostID, req.AccessToken)
	if err != nil {
		return nil, err
	}

	// Convert insights to standard format
	metrics := make(map[string]int64)
	if data, ok := insights["data"].([]interface{}); ok {
		for _, item := range data {
			if metric, ok := item.(map[string]interface{}); ok {
				if name, ok := metric["name"].(string); ok {
					if values, ok := metric["values"].([]interface{}); ok && len(values) > 0 {
						if value, ok := values[0].(map[string]interface{}); ok {
							if val, ok := value["value"].(float64); ok {
								metrics[name] = int64(val)
							}
						}
					}
				}
			}
		}
	}

	return &AnalyticsResponse{
		PlatformPostID: req.PlatformPostID,
		Metrics:        metrics,
		CollectedAt:    time.Now().Unix(),
	}, nil
}

// DeletePost implements Publisher interface
func (f *FacebookPublisher) DeletePost(ctx context.Context, req *DeleteRequest) error {
	return f.DeleteFacebookPost(ctx, req.PlatformPostID, req.AccessToken)
}

// GetPlatformLimits implements Publisher interface
func (f *FacebookPublisher) GetPlatformLimits(ctx context.Context) (*PlatformLimits, error) {
	// Facebook doesn't provide detailed rate limit info via API
	// Return default limits based on documentation
	return &PlatformLimits{
		RateLimits: map[string]int32{
			"posts_per_hour": 25,
			"posts_per_day":  200,
		},
		CurrentUsage: map[string]int32{
			"posts_per_hour": 0,
			"posts_per_day":  0,
		},
		ResetTimes: map[string]int64{
			"posts_per_hour": time.Now().Add(time.Hour).Unix(),
			"posts_per_day":  time.Now().Add(24 * time.Hour).Unix(),
		},
	}, nil
}

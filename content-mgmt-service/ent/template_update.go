// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
)

// TemplateUpdate is the builder for updating Template entities.
type TemplateUpdate struct {
	config
	hooks    []Hook
	mutation *TemplateMutation
}

// Where appends a list predicates to the TemplateUpdate builder.
func (tu *TemplateUpdate) Where(ps ...predicate.Template) *TemplateUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetUserID sets the "user_id" field.
func (tu *TemplateUpdate) SetUserID(u uuid.UUID) *TemplateUpdate {
	tu.mutation.SetUserID(u)
	return tu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableUserID(u *uuid.UUID) *TemplateUpdate {
	if u != nil {
		tu.SetUserID(*u)
	}
	return tu
}

// SetWorkspaceID sets the "workspace_id" field.
func (tu *TemplateUpdate) SetWorkspaceID(s string) *TemplateUpdate {
	tu.mutation.SetWorkspaceID(s)
	return tu
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableWorkspaceID(s *string) *TemplateUpdate {
	if s != nil {
		tu.SetWorkspaceID(*s)
	}
	return tu
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (tu *TemplateUpdate) ClearWorkspaceID() *TemplateUpdate {
	tu.mutation.ClearWorkspaceID()
	return tu
}

// SetName sets the "name" field.
func (tu *TemplateUpdate) SetName(s string) *TemplateUpdate {
	tu.mutation.SetName(s)
	return tu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableName(s *string) *TemplateUpdate {
	if s != nil {
		tu.SetName(*s)
	}
	return tu
}

// SetDescription sets the "description" field.
func (tu *TemplateUpdate) SetDescription(s string) *TemplateUpdate {
	tu.mutation.SetDescription(s)
	return tu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableDescription(s *string) *TemplateUpdate {
	if s != nil {
		tu.SetDescription(*s)
	}
	return tu
}

// ClearDescription clears the value of the "description" field.
func (tu *TemplateUpdate) ClearDescription() *TemplateUpdate {
	tu.mutation.ClearDescription()
	return tu
}

// SetContent sets the "content" field.
func (tu *TemplateUpdate) SetContent(s string) *TemplateUpdate {
	tu.mutation.SetContent(s)
	return tu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableContent(s *string) *TemplateUpdate {
	if s != nil {
		tu.SetContent(*s)
	}
	return tu
}

// SetVariables sets the "variables" field.
func (tu *TemplateUpdate) SetVariables(s []string) *TemplateUpdate {
	tu.mutation.SetVariables(s)
	return tu
}

// AppendVariables appends s to the "variables" field.
func (tu *TemplateUpdate) AppendVariables(s []string) *TemplateUpdate {
	tu.mutation.AppendVariables(s)
	return tu
}

// ClearVariables clears the value of the "variables" field.
func (tu *TemplateUpdate) ClearVariables() *TemplateUpdate {
	tu.mutation.ClearVariables()
	return tu
}

// SetVariableDescriptions sets the "variable_descriptions" field.
func (tu *TemplateUpdate) SetVariableDescriptions(m map[string]string) *TemplateUpdate {
	tu.mutation.SetVariableDescriptions(m)
	return tu
}

// ClearVariableDescriptions clears the value of the "variable_descriptions" field.
func (tu *TemplateUpdate) ClearVariableDescriptions() *TemplateUpdate {
	tu.mutation.ClearVariableDescriptions()
	return tu
}

// SetCategory sets the "category" field.
func (tu *TemplateUpdate) SetCategory(t template.Category) *TemplateUpdate {
	tu.mutation.SetCategory(t)
	return tu
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableCategory(t *template.Category) *TemplateUpdate {
	if t != nil {
		tu.SetCategory(*t)
	}
	return tu
}

// SetPlatforms sets the "platforms" field.
func (tu *TemplateUpdate) SetPlatforms(s []string) *TemplateUpdate {
	tu.mutation.SetPlatforms(s)
	return tu
}

// AppendPlatforms appends s to the "platforms" field.
func (tu *TemplateUpdate) AppendPlatforms(s []string) *TemplateUpdate {
	tu.mutation.AppendPlatforms(s)
	return tu
}

// ClearPlatforms clears the value of the "platforms" field.
func (tu *TemplateUpdate) ClearPlatforms() *TemplateUpdate {
	tu.mutation.ClearPlatforms()
	return tu
}

// SetTone sets the "tone" field.
func (tu *TemplateUpdate) SetTone(t template.Tone) *TemplateUpdate {
	tu.mutation.SetTone(t)
	return tu
}

// SetNillableTone sets the "tone" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableTone(t *template.Tone) *TemplateUpdate {
	if t != nil {
		tu.SetTone(*t)
	}
	return tu
}

// SetContentType sets the "content_type" field.
func (tu *TemplateUpdate) SetContentType(tt template.ContentType) *TemplateUpdate {
	tu.mutation.SetContentType(tt)
	return tu
}

// SetNillableContentType sets the "content_type" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableContentType(tt *template.ContentType) *TemplateUpdate {
	if tt != nil {
		tu.SetContentType(*tt)
	}
	return tu
}

// SetHashtags sets the "hashtags" field.
func (tu *TemplateUpdate) SetHashtags(s []string) *TemplateUpdate {
	tu.mutation.SetHashtags(s)
	return tu
}

// AppendHashtags appends s to the "hashtags" field.
func (tu *TemplateUpdate) AppendHashtags(s []string) *TemplateUpdate {
	tu.mutation.AppendHashtags(s)
	return tu
}

// ClearHashtags clears the value of the "hashtags" field.
func (tu *TemplateUpdate) ClearHashtags() *TemplateUpdate {
	tu.mutation.ClearHashtags()
	return tu
}

// SetIsPublic sets the "is_public" field.
func (tu *TemplateUpdate) SetIsPublic(b bool) *TemplateUpdate {
	tu.mutation.SetIsPublic(b)
	return tu
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableIsPublic(b *bool) *TemplateUpdate {
	if b != nil {
		tu.SetIsPublic(*b)
	}
	return tu
}

// SetIsFeatured sets the "is_featured" field.
func (tu *TemplateUpdate) SetIsFeatured(b bool) *TemplateUpdate {
	tu.mutation.SetIsFeatured(b)
	return tu
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableIsFeatured(b *bool) *TemplateUpdate {
	if b != nil {
		tu.SetIsFeatured(*b)
	}
	return tu
}

// SetUsageCount sets the "usage_count" field.
func (tu *TemplateUpdate) SetUsageCount(i int) *TemplateUpdate {
	tu.mutation.ResetUsageCount()
	tu.mutation.SetUsageCount(i)
	return tu
}

// SetNillableUsageCount sets the "usage_count" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableUsageCount(i *int) *TemplateUpdate {
	if i != nil {
		tu.SetUsageCount(*i)
	}
	return tu
}

// AddUsageCount adds i to the "usage_count" field.
func (tu *TemplateUpdate) AddUsageCount(i int) *TemplateUpdate {
	tu.mutation.AddUsageCount(i)
	return tu
}

// SetRating sets the "rating" field.
func (tu *TemplateUpdate) SetRating(f float64) *TemplateUpdate {
	tu.mutation.ResetRating()
	tu.mutation.SetRating(f)
	return tu
}

// SetNillableRating sets the "rating" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableRating(f *float64) *TemplateUpdate {
	if f != nil {
		tu.SetRating(*f)
	}
	return tu
}

// AddRating adds f to the "rating" field.
func (tu *TemplateUpdate) AddRating(f float64) *TemplateUpdate {
	tu.mutation.AddRating(f)
	return tu
}

// SetRatingCount sets the "rating_count" field.
func (tu *TemplateUpdate) SetRatingCount(i int) *TemplateUpdate {
	tu.mutation.ResetRatingCount()
	tu.mutation.SetRatingCount(i)
	return tu
}

// SetNillableRatingCount sets the "rating_count" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableRatingCount(i *int) *TemplateUpdate {
	if i != nil {
		tu.SetRatingCount(*i)
	}
	return tu
}

// AddRatingCount adds i to the "rating_count" field.
func (tu *TemplateUpdate) AddRatingCount(i int) *TemplateUpdate {
	tu.mutation.AddRatingCount(i)
	return tu
}

// SetTags sets the "tags" field.
func (tu *TemplateUpdate) SetTags(s []string) *TemplateUpdate {
	tu.mutation.SetTags(s)
	return tu
}

// AppendTags appends s to the "tags" field.
func (tu *TemplateUpdate) AppendTags(s []string) *TemplateUpdate {
	tu.mutation.AppendTags(s)
	return tu
}

// ClearTags clears the value of the "tags" field.
func (tu *TemplateUpdate) ClearTags() *TemplateUpdate {
	tu.mutation.ClearTags()
	return tu
}

// SetLanguage sets the "language" field.
func (tu *TemplateUpdate) SetLanguage(s string) *TemplateUpdate {
	tu.mutation.SetLanguage(s)
	return tu
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableLanguage(s *string) *TemplateUpdate {
	if s != nil {
		tu.SetLanguage(*s)
	}
	return tu
}

// SetTrainingFiles sets the "training_files" field.
func (tu *TemplateUpdate) SetTrainingFiles(s []string) *TemplateUpdate {
	tu.mutation.SetTrainingFiles(s)
	return tu
}

// AppendTrainingFiles appends s to the "training_files" field.
func (tu *TemplateUpdate) AppendTrainingFiles(s []string) *TemplateUpdate {
	tu.mutation.AppendTrainingFiles(s)
	return tu
}

// ClearTrainingFiles clears the value of the "training_files" field.
func (tu *TemplateUpdate) ClearTrainingFiles() *TemplateUpdate {
	tu.mutation.ClearTrainingFiles()
	return tu
}

// SetRagTrained sets the "rag_trained" field.
func (tu *TemplateUpdate) SetRagTrained(b bool) *TemplateUpdate {
	tu.mutation.SetRagTrained(b)
	return tu
}

// SetNillableRagTrained sets the "rag_trained" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableRagTrained(b *bool) *TemplateUpdate {
	if b != nil {
		tu.SetRagTrained(*b)
	}
	return tu
}

// SetRagTrainedAt sets the "rag_trained_at" field.
func (tu *TemplateUpdate) SetRagTrainedAt(t time.Time) *TemplateUpdate {
	tu.mutation.SetRagTrainedAt(t)
	return tu
}

// SetNillableRagTrainedAt sets the "rag_trained_at" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableRagTrainedAt(t *time.Time) *TemplateUpdate {
	if t != nil {
		tu.SetRagTrainedAt(*t)
	}
	return tu
}

// ClearRagTrainedAt clears the value of the "rag_trained_at" field.
func (tu *TemplateUpdate) ClearRagTrainedAt() *TemplateUpdate {
	tu.mutation.ClearRagTrainedAt()
	return tu
}

// SetMetadata sets the "metadata" field.
func (tu *TemplateUpdate) SetMetadata(m map[string]interface{}) *TemplateUpdate {
	tu.mutation.SetMetadata(m)
	return tu
}

// ClearMetadata clears the value of the "metadata" field.
func (tu *TemplateUpdate) ClearMetadata() *TemplateUpdate {
	tu.mutation.ClearMetadata()
	return tu
}

// SetUpdatedAt sets the "updated_at" field.
func (tu *TemplateUpdate) SetUpdatedAt(t time.Time) *TemplateUpdate {
	tu.mutation.SetUpdatedAt(t)
	return tu
}

// SetDeletedAt sets the "deleted_at" field.
func (tu *TemplateUpdate) SetDeletedAt(t time.Time) *TemplateUpdate {
	tu.mutation.SetDeletedAt(t)
	return tu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tu *TemplateUpdate) SetNillableDeletedAt(t *time.Time) *TemplateUpdate {
	if t != nil {
		tu.SetDeletedAt(*t)
	}
	return tu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tu *TemplateUpdate) ClearDeletedAt() *TemplateUpdate {
	tu.mutation.ClearDeletedAt()
	return tu
}

// Mutation returns the TemplateMutation object of the builder.
func (tu *TemplateUpdate) Mutation() *TemplateMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TemplateUpdate) Save(ctx context.Context) (int, error) {
	tu.defaults()
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TemplateUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TemplateUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TemplateUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tu *TemplateUpdate) defaults() {
	if _, ok := tu.mutation.UpdatedAt(); !ok {
		v := template.UpdateDefaultUpdatedAt()
		tu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TemplateUpdate) check() error {
	if v, ok := tu.mutation.WorkspaceID(); ok {
		if err := template.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Template.workspace_id": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Name(); ok {
		if err := template.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Template.name": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Category(); ok {
		if err := template.CategoryValidator(v); err != nil {
			return &ValidationError{Name: "category", err: fmt.Errorf(`ent: validator failed for field "Template.category": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Tone(); ok {
		if err := template.ToneValidator(v); err != nil {
			return &ValidationError{Name: "tone", err: fmt.Errorf(`ent: validator failed for field "Template.tone": %w`, err)}
		}
	}
	if v, ok := tu.mutation.ContentType(); ok {
		if err := template.ContentTypeValidator(v); err != nil {
			return &ValidationError{Name: "content_type", err: fmt.Errorf(`ent: validator failed for field "Template.content_type": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Rating(); ok {
		if err := template.RatingValidator(v); err != nil {
			return &ValidationError{Name: "rating", err: fmt.Errorf(`ent: validator failed for field "Template.rating": %w`, err)}
		}
	}
	if v, ok := tu.mutation.Language(); ok {
		if err := template.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Template.language": %w`, err)}
		}
	}
	return nil
}

func (tu *TemplateUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(template.Table, template.Columns, sqlgraph.NewFieldSpec(template.FieldID, field.TypeUUID))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.UserID(); ok {
		_spec.SetField(template.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := tu.mutation.WorkspaceID(); ok {
		_spec.SetField(template.FieldWorkspaceID, field.TypeString, value)
	}
	if tu.mutation.WorkspaceIDCleared() {
		_spec.ClearField(template.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := tu.mutation.Name(); ok {
		_spec.SetField(template.FieldName, field.TypeString, value)
	}
	if value, ok := tu.mutation.Description(); ok {
		_spec.SetField(template.FieldDescription, field.TypeString, value)
	}
	if tu.mutation.DescriptionCleared() {
		_spec.ClearField(template.FieldDescription, field.TypeString)
	}
	if value, ok := tu.mutation.Content(); ok {
		_spec.SetField(template.FieldContent, field.TypeString, value)
	}
	if value, ok := tu.mutation.Variables(); ok {
		_spec.SetField(template.FieldVariables, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedVariables(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldVariables, value)
		})
	}
	if tu.mutation.VariablesCleared() {
		_spec.ClearField(template.FieldVariables, field.TypeJSON)
	}
	if value, ok := tu.mutation.VariableDescriptions(); ok {
		_spec.SetField(template.FieldVariableDescriptions, field.TypeJSON, value)
	}
	if tu.mutation.VariableDescriptionsCleared() {
		_spec.ClearField(template.FieldVariableDescriptions, field.TypeJSON)
	}
	if value, ok := tu.mutation.Category(); ok {
		_spec.SetField(template.FieldCategory, field.TypeEnum, value)
	}
	if value, ok := tu.mutation.Platforms(); ok {
		_spec.SetField(template.FieldPlatforms, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldPlatforms, value)
		})
	}
	if tu.mutation.PlatformsCleared() {
		_spec.ClearField(template.FieldPlatforms, field.TypeJSON)
	}
	if value, ok := tu.mutation.Tone(); ok {
		_spec.SetField(template.FieldTone, field.TypeEnum, value)
	}
	if value, ok := tu.mutation.ContentType(); ok {
		_spec.SetField(template.FieldContentType, field.TypeEnum, value)
	}
	if value, ok := tu.mutation.Hashtags(); ok {
		_spec.SetField(template.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldHashtags, value)
		})
	}
	if tu.mutation.HashtagsCleared() {
		_spec.ClearField(template.FieldHashtags, field.TypeJSON)
	}
	if value, ok := tu.mutation.IsPublic(); ok {
		_spec.SetField(template.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := tu.mutation.IsFeatured(); ok {
		_spec.SetField(template.FieldIsFeatured, field.TypeBool, value)
	}
	if value, ok := tu.mutation.UsageCount(); ok {
		_spec.SetField(template.FieldUsageCount, field.TypeInt, value)
	}
	if value, ok := tu.mutation.AddedUsageCount(); ok {
		_spec.AddField(template.FieldUsageCount, field.TypeInt, value)
	}
	if value, ok := tu.mutation.Rating(); ok {
		_spec.SetField(template.FieldRating, field.TypeFloat64, value)
	}
	if value, ok := tu.mutation.AddedRating(); ok {
		_spec.AddField(template.FieldRating, field.TypeFloat64, value)
	}
	if value, ok := tu.mutation.RatingCount(); ok {
		_spec.SetField(template.FieldRatingCount, field.TypeInt, value)
	}
	if value, ok := tu.mutation.AddedRatingCount(); ok {
		_spec.AddField(template.FieldRatingCount, field.TypeInt, value)
	}
	if value, ok := tu.mutation.Tags(); ok {
		_spec.SetField(template.FieldTags, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedTags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldTags, value)
		})
	}
	if tu.mutation.TagsCleared() {
		_spec.ClearField(template.FieldTags, field.TypeJSON)
	}
	if value, ok := tu.mutation.Language(); ok {
		_spec.SetField(template.FieldLanguage, field.TypeString, value)
	}
	if value, ok := tu.mutation.TrainingFiles(); ok {
		_spec.SetField(template.FieldTrainingFiles, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedTrainingFiles(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldTrainingFiles, value)
		})
	}
	if tu.mutation.TrainingFilesCleared() {
		_spec.ClearField(template.FieldTrainingFiles, field.TypeJSON)
	}
	if value, ok := tu.mutation.RagTrained(); ok {
		_spec.SetField(template.FieldRagTrained, field.TypeBool, value)
	}
	if value, ok := tu.mutation.RagTrainedAt(); ok {
		_spec.SetField(template.FieldRagTrainedAt, field.TypeTime, value)
	}
	if tu.mutation.RagTrainedAtCleared() {
		_spec.ClearField(template.FieldRagTrainedAt, field.TypeTime)
	}
	if value, ok := tu.mutation.Metadata(); ok {
		_spec.SetField(template.FieldMetadata, field.TypeJSON, value)
	}
	if tu.mutation.MetadataCleared() {
		_spec.ClearField(template.FieldMetadata, field.TypeJSON)
	}
	if value, ok := tu.mutation.UpdatedAt(); ok {
		_spec.SetField(template.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tu.mutation.DeletedAt(); ok {
		_spec.SetField(template.FieldDeletedAt, field.TypeTime, value)
	}
	if tu.mutation.DeletedAtCleared() {
		_spec.ClearField(template.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{template.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TemplateUpdateOne is the builder for updating a single Template entity.
type TemplateUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TemplateMutation
}

// SetUserID sets the "user_id" field.
func (tuo *TemplateUpdateOne) SetUserID(u uuid.UUID) *TemplateUpdateOne {
	tuo.mutation.SetUserID(u)
	return tuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableUserID(u *uuid.UUID) *TemplateUpdateOne {
	if u != nil {
		tuo.SetUserID(*u)
	}
	return tuo
}

// SetWorkspaceID sets the "workspace_id" field.
func (tuo *TemplateUpdateOne) SetWorkspaceID(s string) *TemplateUpdateOne {
	tuo.mutation.SetWorkspaceID(s)
	return tuo
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableWorkspaceID(s *string) *TemplateUpdateOne {
	if s != nil {
		tuo.SetWorkspaceID(*s)
	}
	return tuo
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (tuo *TemplateUpdateOne) ClearWorkspaceID() *TemplateUpdateOne {
	tuo.mutation.ClearWorkspaceID()
	return tuo
}

// SetName sets the "name" field.
func (tuo *TemplateUpdateOne) SetName(s string) *TemplateUpdateOne {
	tuo.mutation.SetName(s)
	return tuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableName(s *string) *TemplateUpdateOne {
	if s != nil {
		tuo.SetName(*s)
	}
	return tuo
}

// SetDescription sets the "description" field.
func (tuo *TemplateUpdateOne) SetDescription(s string) *TemplateUpdateOne {
	tuo.mutation.SetDescription(s)
	return tuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableDescription(s *string) *TemplateUpdateOne {
	if s != nil {
		tuo.SetDescription(*s)
	}
	return tuo
}

// ClearDescription clears the value of the "description" field.
func (tuo *TemplateUpdateOne) ClearDescription() *TemplateUpdateOne {
	tuo.mutation.ClearDescription()
	return tuo
}

// SetContent sets the "content" field.
func (tuo *TemplateUpdateOne) SetContent(s string) *TemplateUpdateOne {
	tuo.mutation.SetContent(s)
	return tuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableContent(s *string) *TemplateUpdateOne {
	if s != nil {
		tuo.SetContent(*s)
	}
	return tuo
}

// SetVariables sets the "variables" field.
func (tuo *TemplateUpdateOne) SetVariables(s []string) *TemplateUpdateOne {
	tuo.mutation.SetVariables(s)
	return tuo
}

// AppendVariables appends s to the "variables" field.
func (tuo *TemplateUpdateOne) AppendVariables(s []string) *TemplateUpdateOne {
	tuo.mutation.AppendVariables(s)
	return tuo
}

// ClearVariables clears the value of the "variables" field.
func (tuo *TemplateUpdateOne) ClearVariables() *TemplateUpdateOne {
	tuo.mutation.ClearVariables()
	return tuo
}

// SetVariableDescriptions sets the "variable_descriptions" field.
func (tuo *TemplateUpdateOne) SetVariableDescriptions(m map[string]string) *TemplateUpdateOne {
	tuo.mutation.SetVariableDescriptions(m)
	return tuo
}

// ClearVariableDescriptions clears the value of the "variable_descriptions" field.
func (tuo *TemplateUpdateOne) ClearVariableDescriptions() *TemplateUpdateOne {
	tuo.mutation.ClearVariableDescriptions()
	return tuo
}

// SetCategory sets the "category" field.
func (tuo *TemplateUpdateOne) SetCategory(t template.Category) *TemplateUpdateOne {
	tuo.mutation.SetCategory(t)
	return tuo
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableCategory(t *template.Category) *TemplateUpdateOne {
	if t != nil {
		tuo.SetCategory(*t)
	}
	return tuo
}

// SetPlatforms sets the "platforms" field.
func (tuo *TemplateUpdateOne) SetPlatforms(s []string) *TemplateUpdateOne {
	tuo.mutation.SetPlatforms(s)
	return tuo
}

// AppendPlatforms appends s to the "platforms" field.
func (tuo *TemplateUpdateOne) AppendPlatforms(s []string) *TemplateUpdateOne {
	tuo.mutation.AppendPlatforms(s)
	return tuo
}

// ClearPlatforms clears the value of the "platforms" field.
func (tuo *TemplateUpdateOne) ClearPlatforms() *TemplateUpdateOne {
	tuo.mutation.ClearPlatforms()
	return tuo
}

// SetTone sets the "tone" field.
func (tuo *TemplateUpdateOne) SetTone(t template.Tone) *TemplateUpdateOne {
	tuo.mutation.SetTone(t)
	return tuo
}

// SetNillableTone sets the "tone" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableTone(t *template.Tone) *TemplateUpdateOne {
	if t != nil {
		tuo.SetTone(*t)
	}
	return tuo
}

// SetContentType sets the "content_type" field.
func (tuo *TemplateUpdateOne) SetContentType(tt template.ContentType) *TemplateUpdateOne {
	tuo.mutation.SetContentType(tt)
	return tuo
}

// SetNillableContentType sets the "content_type" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableContentType(tt *template.ContentType) *TemplateUpdateOne {
	if tt != nil {
		tuo.SetContentType(*tt)
	}
	return tuo
}

// SetHashtags sets the "hashtags" field.
func (tuo *TemplateUpdateOne) SetHashtags(s []string) *TemplateUpdateOne {
	tuo.mutation.SetHashtags(s)
	return tuo
}

// AppendHashtags appends s to the "hashtags" field.
func (tuo *TemplateUpdateOne) AppendHashtags(s []string) *TemplateUpdateOne {
	tuo.mutation.AppendHashtags(s)
	return tuo
}

// ClearHashtags clears the value of the "hashtags" field.
func (tuo *TemplateUpdateOne) ClearHashtags() *TemplateUpdateOne {
	tuo.mutation.ClearHashtags()
	return tuo
}

// SetIsPublic sets the "is_public" field.
func (tuo *TemplateUpdateOne) SetIsPublic(b bool) *TemplateUpdateOne {
	tuo.mutation.SetIsPublic(b)
	return tuo
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableIsPublic(b *bool) *TemplateUpdateOne {
	if b != nil {
		tuo.SetIsPublic(*b)
	}
	return tuo
}

// SetIsFeatured sets the "is_featured" field.
func (tuo *TemplateUpdateOne) SetIsFeatured(b bool) *TemplateUpdateOne {
	tuo.mutation.SetIsFeatured(b)
	return tuo
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableIsFeatured(b *bool) *TemplateUpdateOne {
	if b != nil {
		tuo.SetIsFeatured(*b)
	}
	return tuo
}

// SetUsageCount sets the "usage_count" field.
func (tuo *TemplateUpdateOne) SetUsageCount(i int) *TemplateUpdateOne {
	tuo.mutation.ResetUsageCount()
	tuo.mutation.SetUsageCount(i)
	return tuo
}

// SetNillableUsageCount sets the "usage_count" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableUsageCount(i *int) *TemplateUpdateOne {
	if i != nil {
		tuo.SetUsageCount(*i)
	}
	return tuo
}

// AddUsageCount adds i to the "usage_count" field.
func (tuo *TemplateUpdateOne) AddUsageCount(i int) *TemplateUpdateOne {
	tuo.mutation.AddUsageCount(i)
	return tuo
}

// SetRating sets the "rating" field.
func (tuo *TemplateUpdateOne) SetRating(f float64) *TemplateUpdateOne {
	tuo.mutation.ResetRating()
	tuo.mutation.SetRating(f)
	return tuo
}

// SetNillableRating sets the "rating" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableRating(f *float64) *TemplateUpdateOne {
	if f != nil {
		tuo.SetRating(*f)
	}
	return tuo
}

// AddRating adds f to the "rating" field.
func (tuo *TemplateUpdateOne) AddRating(f float64) *TemplateUpdateOne {
	tuo.mutation.AddRating(f)
	return tuo
}

// SetRatingCount sets the "rating_count" field.
func (tuo *TemplateUpdateOne) SetRatingCount(i int) *TemplateUpdateOne {
	tuo.mutation.ResetRatingCount()
	tuo.mutation.SetRatingCount(i)
	return tuo
}

// SetNillableRatingCount sets the "rating_count" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableRatingCount(i *int) *TemplateUpdateOne {
	if i != nil {
		tuo.SetRatingCount(*i)
	}
	return tuo
}

// AddRatingCount adds i to the "rating_count" field.
func (tuo *TemplateUpdateOne) AddRatingCount(i int) *TemplateUpdateOne {
	tuo.mutation.AddRatingCount(i)
	return tuo
}

// SetTags sets the "tags" field.
func (tuo *TemplateUpdateOne) SetTags(s []string) *TemplateUpdateOne {
	tuo.mutation.SetTags(s)
	return tuo
}

// AppendTags appends s to the "tags" field.
func (tuo *TemplateUpdateOne) AppendTags(s []string) *TemplateUpdateOne {
	tuo.mutation.AppendTags(s)
	return tuo
}

// ClearTags clears the value of the "tags" field.
func (tuo *TemplateUpdateOne) ClearTags() *TemplateUpdateOne {
	tuo.mutation.ClearTags()
	return tuo
}

// SetLanguage sets the "language" field.
func (tuo *TemplateUpdateOne) SetLanguage(s string) *TemplateUpdateOne {
	tuo.mutation.SetLanguage(s)
	return tuo
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableLanguage(s *string) *TemplateUpdateOne {
	if s != nil {
		tuo.SetLanguage(*s)
	}
	return tuo
}

// SetTrainingFiles sets the "training_files" field.
func (tuo *TemplateUpdateOne) SetTrainingFiles(s []string) *TemplateUpdateOne {
	tuo.mutation.SetTrainingFiles(s)
	return tuo
}

// AppendTrainingFiles appends s to the "training_files" field.
func (tuo *TemplateUpdateOne) AppendTrainingFiles(s []string) *TemplateUpdateOne {
	tuo.mutation.AppendTrainingFiles(s)
	return tuo
}

// ClearTrainingFiles clears the value of the "training_files" field.
func (tuo *TemplateUpdateOne) ClearTrainingFiles() *TemplateUpdateOne {
	tuo.mutation.ClearTrainingFiles()
	return tuo
}

// SetRagTrained sets the "rag_trained" field.
func (tuo *TemplateUpdateOne) SetRagTrained(b bool) *TemplateUpdateOne {
	tuo.mutation.SetRagTrained(b)
	return tuo
}

// SetNillableRagTrained sets the "rag_trained" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableRagTrained(b *bool) *TemplateUpdateOne {
	if b != nil {
		tuo.SetRagTrained(*b)
	}
	return tuo
}

// SetRagTrainedAt sets the "rag_trained_at" field.
func (tuo *TemplateUpdateOne) SetRagTrainedAt(t time.Time) *TemplateUpdateOne {
	tuo.mutation.SetRagTrainedAt(t)
	return tuo
}

// SetNillableRagTrainedAt sets the "rag_trained_at" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableRagTrainedAt(t *time.Time) *TemplateUpdateOne {
	if t != nil {
		tuo.SetRagTrainedAt(*t)
	}
	return tuo
}

// ClearRagTrainedAt clears the value of the "rag_trained_at" field.
func (tuo *TemplateUpdateOne) ClearRagTrainedAt() *TemplateUpdateOne {
	tuo.mutation.ClearRagTrainedAt()
	return tuo
}

// SetMetadata sets the "metadata" field.
func (tuo *TemplateUpdateOne) SetMetadata(m map[string]interface{}) *TemplateUpdateOne {
	tuo.mutation.SetMetadata(m)
	return tuo
}

// ClearMetadata clears the value of the "metadata" field.
func (tuo *TemplateUpdateOne) ClearMetadata() *TemplateUpdateOne {
	tuo.mutation.ClearMetadata()
	return tuo
}

// SetUpdatedAt sets the "updated_at" field.
func (tuo *TemplateUpdateOne) SetUpdatedAt(t time.Time) *TemplateUpdateOne {
	tuo.mutation.SetUpdatedAt(t)
	return tuo
}

// SetDeletedAt sets the "deleted_at" field.
func (tuo *TemplateUpdateOne) SetDeletedAt(t time.Time) *TemplateUpdateOne {
	tuo.mutation.SetDeletedAt(t)
	return tuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuo *TemplateUpdateOne) SetNillableDeletedAt(t *time.Time) *TemplateUpdateOne {
	if t != nil {
		tuo.SetDeletedAt(*t)
	}
	return tuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuo *TemplateUpdateOne) ClearDeletedAt() *TemplateUpdateOne {
	tuo.mutation.ClearDeletedAt()
	return tuo
}

// Mutation returns the TemplateMutation object of the builder.
func (tuo *TemplateUpdateOne) Mutation() *TemplateMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TemplateUpdate builder.
func (tuo *TemplateUpdateOne) Where(ps ...predicate.Template) *TemplateUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TemplateUpdateOne) Select(field string, fields ...string) *TemplateUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Template entity.
func (tuo *TemplateUpdateOne) Save(ctx context.Context) (*Template, error) {
	tuo.defaults()
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TemplateUpdateOne) SaveX(ctx context.Context) *Template {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TemplateUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TemplateUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuo *TemplateUpdateOne) defaults() {
	if _, ok := tuo.mutation.UpdatedAt(); !ok {
		v := template.UpdateDefaultUpdatedAt()
		tuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TemplateUpdateOne) check() error {
	if v, ok := tuo.mutation.WorkspaceID(); ok {
		if err := template.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Template.workspace_id": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Name(); ok {
		if err := template.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Template.name": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Category(); ok {
		if err := template.CategoryValidator(v); err != nil {
			return &ValidationError{Name: "category", err: fmt.Errorf(`ent: validator failed for field "Template.category": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Tone(); ok {
		if err := template.ToneValidator(v); err != nil {
			return &ValidationError{Name: "tone", err: fmt.Errorf(`ent: validator failed for field "Template.tone": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.ContentType(); ok {
		if err := template.ContentTypeValidator(v); err != nil {
			return &ValidationError{Name: "content_type", err: fmt.Errorf(`ent: validator failed for field "Template.content_type": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Rating(); ok {
		if err := template.RatingValidator(v); err != nil {
			return &ValidationError{Name: "rating", err: fmt.Errorf(`ent: validator failed for field "Template.rating": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.Language(); ok {
		if err := template.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Template.language": %w`, err)}
		}
	}
	return nil
}

func (tuo *TemplateUpdateOne) sqlSave(ctx context.Context) (_node *Template, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(template.Table, template.Columns, sqlgraph.NewFieldSpec(template.FieldID, field.TypeUUID))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Template.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, template.FieldID)
		for _, f := range fields {
			if !template.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != template.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.UserID(); ok {
		_spec.SetField(template.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := tuo.mutation.WorkspaceID(); ok {
		_spec.SetField(template.FieldWorkspaceID, field.TypeString, value)
	}
	if tuo.mutation.WorkspaceIDCleared() {
		_spec.ClearField(template.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := tuo.mutation.Name(); ok {
		_spec.SetField(template.FieldName, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Description(); ok {
		_spec.SetField(template.FieldDescription, field.TypeString, value)
	}
	if tuo.mutation.DescriptionCleared() {
		_spec.ClearField(template.FieldDescription, field.TypeString)
	}
	if value, ok := tuo.mutation.Content(); ok {
		_spec.SetField(template.FieldContent, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Variables(); ok {
		_spec.SetField(template.FieldVariables, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedVariables(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldVariables, value)
		})
	}
	if tuo.mutation.VariablesCleared() {
		_spec.ClearField(template.FieldVariables, field.TypeJSON)
	}
	if value, ok := tuo.mutation.VariableDescriptions(); ok {
		_spec.SetField(template.FieldVariableDescriptions, field.TypeJSON, value)
	}
	if tuo.mutation.VariableDescriptionsCleared() {
		_spec.ClearField(template.FieldVariableDescriptions, field.TypeJSON)
	}
	if value, ok := tuo.mutation.Category(); ok {
		_spec.SetField(template.FieldCategory, field.TypeEnum, value)
	}
	if value, ok := tuo.mutation.Platforms(); ok {
		_spec.SetField(template.FieldPlatforms, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldPlatforms, value)
		})
	}
	if tuo.mutation.PlatformsCleared() {
		_spec.ClearField(template.FieldPlatforms, field.TypeJSON)
	}
	if value, ok := tuo.mutation.Tone(); ok {
		_spec.SetField(template.FieldTone, field.TypeEnum, value)
	}
	if value, ok := tuo.mutation.ContentType(); ok {
		_spec.SetField(template.FieldContentType, field.TypeEnum, value)
	}
	if value, ok := tuo.mutation.Hashtags(); ok {
		_spec.SetField(template.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldHashtags, value)
		})
	}
	if tuo.mutation.HashtagsCleared() {
		_spec.ClearField(template.FieldHashtags, field.TypeJSON)
	}
	if value, ok := tuo.mutation.IsPublic(); ok {
		_spec.SetField(template.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := tuo.mutation.IsFeatured(); ok {
		_spec.SetField(template.FieldIsFeatured, field.TypeBool, value)
	}
	if value, ok := tuo.mutation.UsageCount(); ok {
		_spec.SetField(template.FieldUsageCount, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.AddedUsageCount(); ok {
		_spec.AddField(template.FieldUsageCount, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.Rating(); ok {
		_spec.SetField(template.FieldRating, field.TypeFloat64, value)
	}
	if value, ok := tuo.mutation.AddedRating(); ok {
		_spec.AddField(template.FieldRating, field.TypeFloat64, value)
	}
	if value, ok := tuo.mutation.RatingCount(); ok {
		_spec.SetField(template.FieldRatingCount, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.AddedRatingCount(); ok {
		_spec.AddField(template.FieldRatingCount, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.Tags(); ok {
		_spec.SetField(template.FieldTags, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedTags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldTags, value)
		})
	}
	if tuo.mutation.TagsCleared() {
		_spec.ClearField(template.FieldTags, field.TypeJSON)
	}
	if value, ok := tuo.mutation.Language(); ok {
		_spec.SetField(template.FieldLanguage, field.TypeString, value)
	}
	if value, ok := tuo.mutation.TrainingFiles(); ok {
		_spec.SetField(template.FieldTrainingFiles, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedTrainingFiles(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, template.FieldTrainingFiles, value)
		})
	}
	if tuo.mutation.TrainingFilesCleared() {
		_spec.ClearField(template.FieldTrainingFiles, field.TypeJSON)
	}
	if value, ok := tuo.mutation.RagTrained(); ok {
		_spec.SetField(template.FieldRagTrained, field.TypeBool, value)
	}
	if value, ok := tuo.mutation.RagTrainedAt(); ok {
		_spec.SetField(template.FieldRagTrainedAt, field.TypeTime, value)
	}
	if tuo.mutation.RagTrainedAtCleared() {
		_spec.ClearField(template.FieldRagTrainedAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.Metadata(); ok {
		_spec.SetField(template.FieldMetadata, field.TypeJSON, value)
	}
	if tuo.mutation.MetadataCleared() {
		_spec.ClearField(template.FieldMetadata, field.TypeJSON)
	}
	if value, ok := tuo.mutation.UpdatedAt(); ok {
		_spec.SetField(template.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuo.mutation.DeletedAt(); ok {
		_spec.SetField(template.FieldDeletedAt, field.TypeTime, value)
	}
	if tuo.mutation.DeletedAtCleared() {
		_spec.ClearField(template.FieldDeletedAt, field.TypeTime)
	}
	_node = &Template{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{template.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}

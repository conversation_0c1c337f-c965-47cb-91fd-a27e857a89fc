package publishing

// Request/Response types

// PublishPostRequest represents a request to publish content to a platform
type PublishPostRequest struct {
	AccountID       string                 `json:"account_id" validate:"required"`
	UserID          string                 `json:"user_id" validate:"required"`
	PostID          string                 `json:"post_id" validate:"required"`
	Content         string                 `json:"content" validate:"required"`
	ImageURLs       []string               `json:"image_urls"`
	Hashtags        []string               `json:"hashtags"`
	PlatformOptions map[string]interface{} `json:"platform_options"`
	ScheduledAt     int64                  `json:"scheduled_at"`
}

// PublishPostResponse represents the response after publishing content
type PublishPostResponse struct {
	PlatformPostID string `json:"platform_post_id,omitempty"`
	PlatformURL    string `json:"platform_url,omitempty"`
	Success        bool   `json:"success"`
	ErrorMessage   string `json:"error_message,omitempty"`
	PublishedAt    int64  `json:"published_at"`
}

// GetPostAnalyticsRequest represents a request to get post analytics
type GetPostAnalyticsRequest struct {
	AccountID      string   `json:"account_id" validate:"required"`
	PlatformPostID string   `json:"platform_post_id" validate:"required"`
	UserID         string   `json:"user_id" validate:"required"`
	Metrics        []string `json:"metrics"`
}

// GetPostAnalyticsResponse represents the response with post analytics
type GetPostAnalyticsResponse struct {
	PlatformPostID  string           `json:"platform_post_id"`
	Platform        string           `json:"platform"`
	Metrics         map[string]int64 `json:"metrics"`
	CollectedAt     int64            `json:"collected_at"`
	PostPublishedAt int64            `json:"post_published_at"`
}

// GetAccountAnalyticsRequest represents a request to get account analytics
type GetAccountAnalyticsRequest struct {
	AccountID string   `json:"account_id" validate:"required"`
	UserID    string   `json:"user_id" validate:"required"`
	DateFrom  string   `json:"date_from"`
	DateTo    string   `json:"date_to"`
	Metrics   []string `json:"metrics"`
}

// GetAccountAnalyticsResponse represents the response with account analytics
type GetAccountAnalyticsResponse struct {
	AccountID   string             `json:"account_id"`
	Platform    string             `json:"platform"`
	Metrics     map[string]int64   `json:"metrics"`
	TimeSeries  map[string][]int64 `json:"time_series"`
	CollectedAt int64              `json:"collected_at"`
}

# JWT Authentication Implementation

## 🎯 **Tổng quan**

Integration Service đã được cập nhật để sử dụng **JWT token authentication** thay vì truyền `user_id` trong request body. Điều này đảm bảo security và consistency với các service khác trong hệ thống.

## 🔧 **Các thay đổi đã thực hiện**

### **1. Authentication Middleware**

#### **File: `api/restful/middleware/auth_middleware.go`**

Tạo middleware xử lý JWT authentication:

```go
type AuthMiddleware struct {
    userClient userv1.AuthServiceClient
    logger     logging.Logger
}

// RequireAuth middleware - bắt buộc authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc

// OptionalAuth middleware - authentication tùy chọn  
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc

// RequireRole middleware - y<PERSON><PERSON> c<PERSON> role cụ thể
func (m *AuthMiddleware) RequireRole(requiredRole string) gin.HandlerFunc
```

**Tính năng:**
- ✅ Validate JWT token với User Service
- ✅ Extract user info từ token claims
- ✅ Set user context trong Gin context
- ✅ Role-based access control
- ✅ Comprehensive error handling

### **2. Routes Protection**

#### **File: `api/restful/routes.go`**

Áp dụng authentication cho protected routes:

```go
// Integration management routes (protected)
integrations := v1.Group("/integrations")
integrations.Use(authMiddleware.RequireAuth())

// Publishing routes (protected)
publishing := v1.Group("/publishing")
publishing.Use(authMiddleware.RequireAuth())

// Platform routes (mixed)
platform.GET("/info", platformHandler.GetPlatformInfo) // Public
platform.POST("/limits", authMiddleware.RequireAuth(), platformHandler.GetPlatformLimits) // Protected
```

### **3. Handler Updates**

#### **Updated Handlers:**

**IntegrationHandler:**
- ✅ `GetOAuthURL()` - Lấy user_id từ context
- ✅ `ListIntegrations()` - Loại bỏ user_id query param
- ✅ `GetIntegration()` - Lấy user_id từ context
- ✅ `DeleteIntegration()` - Lấy user_id từ context
- ✅ `RefreshIntegration()` - Lấy user_id từ context

**PublishingHandler:**
- ✅ `PublishPost()` - Lấy user_id từ context
- ✅ `GetPostAnalytics()` - Lấy user_id từ context
- ✅ `GetAccountAnalytics()` - Lấy user_id từ context

### **4. Request/Response Types**

#### **File: `api/restful/handlers/types.go`**

Loại bỏ `user_id` từ request bodies:

```go
// Before
type GetOAuthURLRequest struct {
    UserID   string `json:"user_id" binding:"required"`
    Platform string `json:"platform" binding:"required"`
    // ...
}

// After  
type GetOAuthURLRequest struct {
    Platform string `json:"platform" binding:"required"`
    // ... (user_id lấy từ JWT token)
}
```

**Các request types đã cập nhật:**
- ✅ `GetOAuthURLRequest`
- ✅ `DisconnectIntegrationRequest`
- ✅ `RefreshIntegrationRequest`
- ✅ `PublishPostRequest`
- ✅ `GetPostAnalyticsRequest`
- ✅ `GetAccountAnalyticsRequest`

### **5. API Documentation**

#### **File: `socialai-api.yaml`**

Cập nhật OpenAPI specification:

```yaml
# Before
required: [user_id, platform]
properties:
  user_id:
    type: string
    example: "user_123"
  platform:
    type: string

# After
required: [platform]
properties:
  platform:
    type: string
    # user_id lấy từ JWT token trong Authorization header
```

## 🔐 **Authentication Flow**

### **1. Client Request**
```http
POST /api/v1/integrations/oauth/url
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "platform": "facebook",
  "redirect_uri": "http://localhost:3000/callback"
}
```

### **2. Middleware Processing**
1. Extract token từ `Authorization: Bearer <token>`
2. Validate token với User Service
3. Extract user info từ token claims
4. Set user context: `c.Set("user_id", userId)`

### **3. Handler Processing**
```go
// Get user ID từ context
userIDInterface, exists := c.Get("user_id")
if !exists {
    return unauthorized_error
}
userID := userIDInterface.(string)

// Sử dụng userID trong business logic
useCaseReq := &integration.GetOAuthURLRequest{
    UserID:   userID,  // Từ JWT token
    Platform: req.Platform,  // Từ request body
}
```

## 📊 **Security Benefits**

### **1. Enhanced Security**
- ✅ **No user_id spoofing** - User không thể fake user_id
- ✅ **Token validation** - Mọi request đều validate token
- ✅ **Centralized auth** - Consistent với các service khác
- ✅ **Role-based access** - Support role và permissions

### **2. Better UX**
- ✅ **Simplified requests** - Client không cần truyền user_id
- ✅ **Automatic user context** - User info tự động extract
- ✅ **Consistent API** - Giống với các service khác

### **3. Maintainability**
- ✅ **Single source of truth** - User info từ JWT token
- ✅ **Centralized middleware** - Reusable authentication logic
- ✅ **Clear separation** - Auth logic tách biệt khỏi business logic

## 🔄 **Migration Guide**

### **Frontend Changes Required**

#### **Before (Old API):**
```javascript
// Old way - truyền user_id trong body
const response = await fetch('/api/v1/integrations/oauth/url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    user_id: 'user_123',
    platform: 'facebook'
  })
});
```

#### **After (New API):**
```javascript
// New way - user_id từ JWT token
const response = await fetch('/api/v1/integrations/oauth/url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${jwtToken}`  // JWT token required
  },
  body: JSON.stringify({
    platform: 'facebook'  // Không cần user_id
  })
});
```

### **Breaking Changes**

1. **Authorization Header Required**
   - Tất cả protected endpoints cần `Authorization: Bearer <token>`

2. **Request Body Changes**
   - Loại bỏ `user_id` từ request bodies
   - Query parameter `user_id` cũng bị loại bỏ

3. **Error Responses**
   - New error codes: `MISSING_TOKEN`, `INVALID_TOKEN_FORMAT`, `INVALID_TOKEN`

## 🧪 **Testing**

### **Authentication Test Cases**

```bash
# 1. Valid token
curl -X POST /api/v1/integrations/oauth/url \
  -H "Authorization: Bearer valid_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"platform": "facebook"}'

# 2. Missing token
curl -X POST /api/v1/integrations/oauth/url \
  -H "Content-Type: application/json" \
  -d '{"platform": "facebook"}'
# Expected: 401 Unauthorized

# 3. Invalid token format
curl -X POST /api/v1/integrations/oauth/url \
  -H "Authorization: invalid_format" \
  -H "Content-Type: application/json" \
  -d '{"platform": "facebook"}'
# Expected: 401 Unauthorized

# 4. Expired token
curl -X POST /api/v1/integrations/oauth/url \
  -H "Authorization: Bearer expired_token" \
  -H "Content-Type: application/json" \
  -d '{"platform": "facebook"}'
# Expected: 401 Unauthorized
```

## ✅ **Implementation Status**

### **Completed Features**
- ✅ **Authentication Middleware** - Complete implementation
- ✅ **Protected Routes** - All sensitive endpoints protected
- ✅ **Handler Updates** - All handlers use JWT user context
- ✅ **Request Types** - Cleaned up request structures
- ✅ **API Documentation** - Updated OpenAPI spec
- ✅ **Error Handling** - Comprehensive auth error responses

### **Public Endpoints (No Auth Required)**
- ✅ `GET /health` - Health check
- ✅ `GET /oauth/{platform}/callback` - OAuth callbacks
- ✅ `POST /webhooks/{platform}` - Platform webhooks
- ✅ `GET /api/v1/platform/info` - Platform information

### **Protected Endpoints (Auth Required)**
- ✅ All `/api/v1/integrations/*` endpoints
- ✅ All `/api/v1/publishing/*` endpoints  
- ✅ `POST /api/v1/platform/limits` endpoint

## 🚀 **Ready for Production**

Integration Service hiện đã implement **JWT authentication** hoàn chỉnh:

- **Security-first approach** với token validation
- **Consistent với microservices architecture**
- **Clean API design** không cần user_id trong requests
- **Comprehensive error handling** cho auth failures
- **Role-based access control** ready for future features

**Frontend có thể migrate ngay** bằng cách thêm Authorization header và loại bỏ user_id từ request bodies!

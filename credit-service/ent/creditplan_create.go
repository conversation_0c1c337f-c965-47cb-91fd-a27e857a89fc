// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditplan"
)

// CreditPlanCreate is the builder for creating a CreditPlan entity.
type CreditPlanCreate struct {
	config
	mutation *CreditPlanMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (cpc *CreditPlanCreate) SetName(s string) *CreditPlanCreate {
	cpc.mutation.SetName(s)
	return cpc
}

// SetDisplayName sets the "display_name" field.
func (cpc *CreditPlanCreate) SetDisplayName(s string) *CreditPlanCreate {
	cpc.mutation.SetDisplayName(s)
	return cpc
}

// SetDescription sets the "description" field.
func (cpc *CreditPlanCreate) SetDescription(s string) *CreditPlanCreate {
	cpc.mutation.SetDescription(s)
	return cpc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableDescription(s *string) *CreditPlanCreate {
	if s != nil {
		cpc.SetDescription(*s)
	}
	return cpc
}

// SetCredits sets the "credits" field.
func (cpc *CreditPlanCreate) SetCredits(i int) *CreditPlanCreate {
	cpc.mutation.SetCredits(i)
	return cpc
}

// SetPrice sets the "price" field.
func (cpc *CreditPlanCreate) SetPrice(i int64) *CreditPlanCreate {
	cpc.mutation.SetPrice(i)
	return cpc
}

// SetCurrency sets the "currency" field.
func (cpc *CreditPlanCreate) SetCurrency(s string) *CreditPlanCreate {
	cpc.mutation.SetCurrency(s)
	return cpc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableCurrency(s *string) *CreditPlanCreate {
	if s != nil {
		cpc.SetCurrency(*s)
	}
	return cpc
}

// SetDiscountPercentage sets the "discount_percentage" field.
func (cpc *CreditPlanCreate) SetDiscountPercentage(i int) *CreditPlanCreate {
	cpc.mutation.SetDiscountPercentage(i)
	return cpc
}

// SetNillableDiscountPercentage sets the "discount_percentage" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableDiscountPercentage(i *int) *CreditPlanCreate {
	if i != nil {
		cpc.SetDiscountPercentage(*i)
	}
	return cpc
}

// SetIsPopular sets the "is_popular" field.
func (cpc *CreditPlanCreate) SetIsPopular(b bool) *CreditPlanCreate {
	cpc.mutation.SetIsPopular(b)
	return cpc
}

// SetNillableIsPopular sets the "is_popular" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableIsPopular(b *bool) *CreditPlanCreate {
	if b != nil {
		cpc.SetIsPopular(*b)
	}
	return cpc
}

// SetIsActive sets the "is_active" field.
func (cpc *CreditPlanCreate) SetIsActive(b bool) *CreditPlanCreate {
	cpc.mutation.SetIsActive(b)
	return cpc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableIsActive(b *bool) *CreditPlanCreate {
	if b != nil {
		cpc.SetIsActive(*b)
	}
	return cpc
}

// SetFeatures sets the "features" field.
func (cpc *CreditPlanCreate) SetFeatures(s []string) *CreditPlanCreate {
	cpc.mutation.SetFeatures(s)
	return cpc
}

// SetValidUntil sets the "valid_until" field.
func (cpc *CreditPlanCreate) SetValidUntil(t time.Time) *CreditPlanCreate {
	cpc.mutation.SetValidUntil(t)
	return cpc
}

// SetNillableValidUntil sets the "valid_until" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableValidUntil(t *time.Time) *CreditPlanCreate {
	if t != nil {
		cpc.SetValidUntil(*t)
	}
	return cpc
}

// SetSortOrder sets the "sort_order" field.
func (cpc *CreditPlanCreate) SetSortOrder(i int) *CreditPlanCreate {
	cpc.mutation.SetSortOrder(i)
	return cpc
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableSortOrder(i *int) *CreditPlanCreate {
	if i != nil {
		cpc.SetSortOrder(*i)
	}
	return cpc
}

// SetStripePriceID sets the "stripe_price_id" field.
func (cpc *CreditPlanCreate) SetStripePriceID(s string) *CreditPlanCreate {
	cpc.mutation.SetStripePriceID(s)
	return cpc
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableStripePriceID(s *string) *CreditPlanCreate {
	if s != nil {
		cpc.SetStripePriceID(*s)
	}
	return cpc
}

// SetStripeProductID sets the "stripe_product_id" field.
func (cpc *CreditPlanCreate) SetStripeProductID(s string) *CreditPlanCreate {
	cpc.mutation.SetStripeProductID(s)
	return cpc
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableStripeProductID(s *string) *CreditPlanCreate {
	if s != nil {
		cpc.SetStripeProductID(*s)
	}
	return cpc
}

// SetMetadata sets the "metadata" field.
func (cpc *CreditPlanCreate) SetMetadata(m map[string]interface{}) *CreditPlanCreate {
	cpc.mutation.SetMetadata(m)
	return cpc
}

// SetCreatedAt sets the "created_at" field.
func (cpc *CreditPlanCreate) SetCreatedAt(t time.Time) *CreditPlanCreate {
	cpc.mutation.SetCreatedAt(t)
	return cpc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableCreatedAt(t *time.Time) *CreditPlanCreate {
	if t != nil {
		cpc.SetCreatedAt(*t)
	}
	return cpc
}

// SetUpdatedAt sets the "updated_at" field.
func (cpc *CreditPlanCreate) SetUpdatedAt(t time.Time) *CreditPlanCreate {
	cpc.mutation.SetUpdatedAt(t)
	return cpc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableUpdatedAt(t *time.Time) *CreditPlanCreate {
	if t != nil {
		cpc.SetUpdatedAt(*t)
	}
	return cpc
}

// SetID sets the "id" field.
func (cpc *CreditPlanCreate) SetID(u uuid.UUID) *CreditPlanCreate {
	cpc.mutation.SetID(u)
	return cpc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (cpc *CreditPlanCreate) SetNillableID(u *uuid.UUID) *CreditPlanCreate {
	if u != nil {
		cpc.SetID(*u)
	}
	return cpc
}

// Mutation returns the CreditPlanMutation object of the builder.
func (cpc *CreditPlanCreate) Mutation() *CreditPlanMutation {
	return cpc.mutation
}

// Save creates the CreditPlan in the database.
func (cpc *CreditPlanCreate) Save(ctx context.Context) (*CreditPlan, error) {
	cpc.defaults()
	return withHooks(ctx, cpc.sqlSave, cpc.mutation, cpc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cpc *CreditPlanCreate) SaveX(ctx context.Context) *CreditPlan {
	v, err := cpc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cpc *CreditPlanCreate) Exec(ctx context.Context) error {
	_, err := cpc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cpc *CreditPlanCreate) ExecX(ctx context.Context) {
	if err := cpc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cpc *CreditPlanCreate) defaults() {
	if _, ok := cpc.mutation.Currency(); !ok {
		v := creditplan.DefaultCurrency
		cpc.mutation.SetCurrency(v)
	}
	if _, ok := cpc.mutation.DiscountPercentage(); !ok {
		v := creditplan.DefaultDiscountPercentage
		cpc.mutation.SetDiscountPercentage(v)
	}
	if _, ok := cpc.mutation.IsPopular(); !ok {
		v := creditplan.DefaultIsPopular
		cpc.mutation.SetIsPopular(v)
	}
	if _, ok := cpc.mutation.IsActive(); !ok {
		v := creditplan.DefaultIsActive
		cpc.mutation.SetIsActive(v)
	}
	if _, ok := cpc.mutation.SortOrder(); !ok {
		v := creditplan.DefaultSortOrder
		cpc.mutation.SetSortOrder(v)
	}
	if _, ok := cpc.mutation.CreatedAt(); !ok {
		v := creditplan.DefaultCreatedAt()
		cpc.mutation.SetCreatedAt(v)
	}
	if _, ok := cpc.mutation.UpdatedAt(); !ok {
		v := creditplan.DefaultUpdatedAt()
		cpc.mutation.SetUpdatedAt(v)
	}
	if _, ok := cpc.mutation.ID(); !ok {
		v := creditplan.DefaultID()
		cpc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cpc *CreditPlanCreate) check() error {
	if _, ok := cpc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "CreditPlan.name"`)}
	}
	if v, ok := cpc.mutation.Name(); ok {
		if err := creditplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.name": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.DisplayName(); !ok {
		return &ValidationError{Name: "display_name", err: errors.New(`ent: missing required field "CreditPlan.display_name"`)}
	}
	if v, ok := cpc.mutation.DisplayName(); ok {
		if err := creditplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.display_name": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.Credits(); !ok {
		return &ValidationError{Name: "credits", err: errors.New(`ent: missing required field "CreditPlan.credits"`)}
	}
	if v, ok := cpc.mutation.Credits(); ok {
		if err := creditplan.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.credits": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.Price(); !ok {
		return &ValidationError{Name: "price", err: errors.New(`ent: missing required field "CreditPlan.price"`)}
	}
	if v, ok := cpc.mutation.Price(); ok {
		if err := creditplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.price": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "CreditPlan.currency"`)}
	}
	if v, ok := cpc.mutation.Currency(); ok {
		if err := creditplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.currency": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.DiscountPercentage(); !ok {
		return &ValidationError{Name: "discount_percentage", err: errors.New(`ent: missing required field "CreditPlan.discount_percentage"`)}
	}
	if v, ok := cpc.mutation.DiscountPercentage(); ok {
		if err := creditplan.DiscountPercentageValidator(v); err != nil {
			return &ValidationError{Name: "discount_percentage", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.discount_percentage": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.IsPopular(); !ok {
		return &ValidationError{Name: "is_popular", err: errors.New(`ent: missing required field "CreditPlan.is_popular"`)}
	}
	if _, ok := cpc.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "CreditPlan.is_active"`)}
	}
	if _, ok := cpc.mutation.SortOrder(); !ok {
		return &ValidationError{Name: "sort_order", err: errors.New(`ent: missing required field "CreditPlan.sort_order"`)}
	}
	if v, ok := cpc.mutation.StripePriceID(); ok {
		if err := creditplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := cpc.mutation.StripeProductID(); ok {
		if err := creditplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "CreditPlan.stripe_product_id": %w`, err)}
		}
	}
	if _, ok := cpc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CreditPlan.created_at"`)}
	}
	if _, ok := cpc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CreditPlan.updated_at"`)}
	}
	return nil
}

func (cpc *CreditPlanCreate) sqlSave(ctx context.Context) (*CreditPlan, error) {
	if err := cpc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cpc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cpc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	cpc.mutation.id = &_node.ID
	cpc.mutation.done = true
	return _node, nil
}

func (cpc *CreditPlanCreate) createSpec() (*CreditPlan, *sqlgraph.CreateSpec) {
	var (
		_node = &CreditPlan{config: cpc.config}
		_spec = sqlgraph.NewCreateSpec(creditplan.Table, sqlgraph.NewFieldSpec(creditplan.FieldID, field.TypeUUID))
	)
	if id, ok := cpc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := cpc.mutation.Name(); ok {
		_spec.SetField(creditplan.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := cpc.mutation.DisplayName(); ok {
		_spec.SetField(creditplan.FieldDisplayName, field.TypeString, value)
		_node.DisplayName = value
	}
	if value, ok := cpc.mutation.Description(); ok {
		_spec.SetField(creditplan.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := cpc.mutation.Credits(); ok {
		_spec.SetField(creditplan.FieldCredits, field.TypeInt, value)
		_node.Credits = value
	}
	if value, ok := cpc.mutation.Price(); ok {
		_spec.SetField(creditplan.FieldPrice, field.TypeInt64, value)
		_node.Price = value
	}
	if value, ok := cpc.mutation.Currency(); ok {
		_spec.SetField(creditplan.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := cpc.mutation.DiscountPercentage(); ok {
		_spec.SetField(creditplan.FieldDiscountPercentage, field.TypeInt, value)
		_node.DiscountPercentage = value
	}
	if value, ok := cpc.mutation.IsPopular(); ok {
		_spec.SetField(creditplan.FieldIsPopular, field.TypeBool, value)
		_node.IsPopular = value
	}
	if value, ok := cpc.mutation.IsActive(); ok {
		_spec.SetField(creditplan.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := cpc.mutation.Features(); ok {
		_spec.SetField(creditplan.FieldFeatures, field.TypeJSON, value)
		_node.Features = value
	}
	if value, ok := cpc.mutation.ValidUntil(); ok {
		_spec.SetField(creditplan.FieldValidUntil, field.TypeTime, value)
		_node.ValidUntil = value
	}
	if value, ok := cpc.mutation.SortOrder(); ok {
		_spec.SetField(creditplan.FieldSortOrder, field.TypeInt, value)
		_node.SortOrder = value
	}
	if value, ok := cpc.mutation.StripePriceID(); ok {
		_spec.SetField(creditplan.FieldStripePriceID, field.TypeString, value)
		_node.StripePriceID = value
	}
	if value, ok := cpc.mutation.StripeProductID(); ok {
		_spec.SetField(creditplan.FieldStripeProductID, field.TypeString, value)
		_node.StripeProductID = value
	}
	if value, ok := cpc.mutation.Metadata(); ok {
		_spec.SetField(creditplan.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := cpc.mutation.CreatedAt(); ok {
		_spec.SetField(creditplan.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cpc.mutation.UpdatedAt(); ok {
		_spec.SetField(creditplan.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// CreditPlanCreateBulk is the builder for creating many CreditPlan entities in bulk.
type CreditPlanCreateBulk struct {
	config
	err      error
	builders []*CreditPlanCreate
}

// Save creates the CreditPlan entities in the database.
func (cpcb *CreditPlanCreateBulk) Save(ctx context.Context) ([]*CreditPlan, error) {
	if cpcb.err != nil {
		return nil, cpcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cpcb.builders))
	nodes := make([]*CreditPlan, len(cpcb.builders))
	mutators := make([]Mutator, len(cpcb.builders))
	for i := range cpcb.builders {
		func(i int, root context.Context) {
			builder := cpcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CreditPlanMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cpcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cpcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cpcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cpcb *CreditPlanCreateBulk) SaveX(ctx context.Context) []*CreditPlan {
	v, err := cpcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cpcb *CreditPlanCreateBulk) Exec(ctx context.Context) error {
	_, err := cpcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cpcb *CreditPlanCreateBulk) ExecX(ctx context.Context) {
	if err := cpcb.Exec(ctx); err != nil {
		panic(err)
	}
}

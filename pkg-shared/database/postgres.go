package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// PostgresConfig holds PostgreSQL connection configuration
type PostgresConfig struct {
	Host            string
	Port            int
	Database        string
	Username        string
	Password        string
	SSLMode         string
	MaxConnections  int32
	MinConnections  int32
	MaxConnLifetime time.Duration
	MaxConnIdleTime time.Duration
}

// PostgresClient wraps pgxpool.Pool with additional functionality
type PostgresClient struct {
	Pool   *pgxpool.Pool
	config *PostgresConfig
}

// NewPostgresClient creates a new PostgreSQL client
func NewPostgresClient(config *PostgresConfig) (*PostgresClient, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		config.Host,
		config.Port,
		config.Database,
		config.Username,
		config.Password,
		config.SSLMode,
	)

	poolConfig, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Configure connection pool
	poolConfig.MaxConns = config.MaxConnections
	poolConfig.MinConns = config.MinConnections
	poolConfig.MaxConnLifetime = config.MaxConnLifetime
	poolConfig.MaxConnIdleTime = config.MaxConnIdleTime

	// Configure connection settings
	poolConfig.ConnConfig.ConnectTimeout = 30 * time.Second
	poolConfig.ConnConfig.RuntimeParams = map[string]string{
		"timezone": "UTC",
	}

	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	client := &PostgresClient{
		Pool:   pool,
		config: config,
	}

	// Test connection
	if err := client.Ping(context.Background()); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return client, nil
}

// Ping tests the database connection
func (c *PostgresClient) Ping(ctx context.Context) error {
	return c.Pool.Ping(ctx)
}

// Close closes the database connection pool
func (c *PostgresClient) Close() error {
	c.Pool.Close()
	return nil
}

// Stats returns connection pool statistics
func (c *PostgresClient) Stats() *pgxpool.Stat {
	return c.Pool.Stat()
}

// BeginTx starts a new transaction
func (c *PostgresClient) BeginTx(ctx context.Context) (pgx.Tx, error) {
	return c.Pool.Begin(ctx)
}

// WithTransaction executes a function within a transaction
func (c *PostgresClient) WithTransaction(ctx context.Context, fn func(pgx.Tx) error) error {
	tx, err := c.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback(ctx)
			panic(p)
		}
	}()

	if err := fn(tx); err != nil {
		if rbErr := tx.Rollback(ctx); rbErr != nil {
			return fmt.Errorf("transaction error: %v, rollback error: %w", err, rbErr)
		}
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// DefaultPostgresConfig returns default PostgreSQL configuration
func DefaultPostgresConfig() *PostgresConfig {
	return &PostgresConfig{
		Host:            "localhost",
		Port:            5432,
		Database:        "socialai",
		Username:        "postgres",
		Password:        "password",
		SSLMode:         "disable",
		MaxConnections:  25,
		MinConnections:  5,
		MaxConnLifetime: time.Hour,
		MaxConnIdleTime: 30 * time.Minute,
	}
}

// NewPostgresClientFromEnv creates a PostgreSQL client from environment variables
func NewPostgresClientFromEnv() (*PostgresClient, error) {
	config := &PostgresConfig{
		Host:            getEnvOrDefault("DB_HOST", "localhost"),
		Port:            getEnvIntOrDefault("DB_PORT", 5432),
		Database:        getEnvOrDefault("DB_NAME", "socialai"),
		Username:        getEnvOrDefault("DB_USER", "postgres"),
		Password:        getEnvOrDefault("DB_PASSWORD", "password"),
		SSLMode:         getEnvOrDefault("DB_SSL_MODE", "disable"),
		MaxConnections:  int32(getEnvIntOrDefault("DB_MAX_CONNECTIONS", 25)),
		MinConnections:  int32(getEnvIntOrDefault("DB_MIN_CONNECTIONS", 5)),
		MaxConnLifetime: time.Duration(getEnvIntOrDefault("DB_MAX_CONN_LIFETIME", 3600)) * time.Second,
		MaxConnIdleTime: time.Duration(getEnvIntOrDefault("DB_MAX_CONN_IDLE_TIME", 1800)) * time.Second,
	}

	return NewPostgresClient(config)
}

// Helper functions for environment variables
func getEnvOrDefault(key, defaultValue string) string {
	// This would typically use os.Getenv, but for simplicity we'll return default
	return defaultValue
}

func getEnvIntOrDefault(key string, defaultValue int) int {
	// This would typically parse os.Getenv, but for simplicity we'll return default
	return defaultValue
}

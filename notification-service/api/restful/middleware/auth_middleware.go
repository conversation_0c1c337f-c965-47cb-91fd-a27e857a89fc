package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// AuthMiddleware handles authentication for HTTP requests
type AuthMiddleware struct {
	userClient userv1.AuthServiceClient
	logger     logging.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(userClient userv1.AuthServiceClient, logger logging.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		userClient: userClient,
		logger:     logger,
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil {
			m.logger.WithError(err).Error("Failed to validate token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		if !resp.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token is not valid"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		// Extract email and role from claims if available
		if email, exists := resp.Claims["email"]; exists {
			c.Set("user_email", email)
		}
		if role, exists := resp.Claims["role"]; exists {
			c.Set("user_role", role)
		} else {
			c.Set("user_role", "user") // default role
		}

		c.Next()
	}
}

// OptionalAuth middleware that optionally validates authentication
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.Next()
			return
		}

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil {
			m.logger.WithError(err).Warn("Failed to validate optional token")
			c.Next()
			return
		}

		if resp.Valid {
			// Set user information in context
			c.Set("user_id", resp.UserId)
			// Extract email and role from claims if available
			if email, exists := resp.Claims["email"]; exists {
				c.Set("user_email", email)
			}
			if role, exists := resp.Claims["role"]; exists {
				c.Set("user_role", role)
			} else {
				c.Set("user_role", "user") // default role
			}
		}

		c.Next()
	}
}

// AdminAuth middleware that requires admin authentication
func (m *AuthMiddleware) AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil {
			m.logger.WithError(err).Error("Failed to validate admin token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		if !resp.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token is not valid"})
			c.Abort()
			return
		}

		// Check if user is admin
		role, exists := resp.Claims["role"]
		if !exists || (role != "admin" && role != "super_admin") {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		if email, exists := resp.Claims["email"]; exists {
			c.Set("user_email", email)
		}
		c.Set("user_role", role)

		c.Next()
	}
}

// extractToken extracts the JWT token from the Authorization header
func extractToken(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return ""
	}

	// Check if it starts with "Bearer "
	if !strings.HasPrefix(authHeader, "Bearer ") {
		return ""
	}

	// Extract the token part
	return strings.TrimPrefix(authHeader, "Bearer ")
}

// Code generated by ent, DO NOT EDIT.

package notification

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldUserID, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldTitle, v))
}

// Message applies equality check predicate on the "message" field. It's identical to MessageEQ.
func Message(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldMessage, v))
}

// ReferenceID applies equality check predicate on the "reference_id" field. It's identical to ReferenceIDEQ.
func ReferenceID(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceType applies equality check predicate on the "reference_type" field. It's identical to ReferenceTypeEQ.
func ReferenceType(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReferenceType, v))
}

// ScheduledAt applies equality check predicate on the "scheduled_at" field. It's identical to ScheduledAtEQ.
func ScheduledAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldScheduledAt, v))
}

// SentAt applies equality check predicate on the "sent_at" field. It's identical to SentAtEQ.
func SentAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldSentAt, v))
}

// DeliveredAt applies equality check predicate on the "delivered_at" field. It's identical to DeliveredAtEQ.
func DeliveredAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldDeliveredAt, v))
}

// ReadAt applies equality check predicate on the "read_at" field. It's identical to ReadAtEQ.
func ReadAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReadAt, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldExpiresAt, v))
}

// ActionURL applies equality check predicate on the "action_url" field. It's identical to ActionURLEQ.
func ActionURL(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldActionURL, v))
}

// RetryCount applies equality check predicate on the "retry_count" field. It's identical to RetryCountEQ.
func RetryCount(v int) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldRetryCount, v))
}

// NextRetryAt applies equality check predicate on the "next_retry_at" field. It's identical to NextRetryAtEQ.
func NextRetryAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldNextRetryAt, v))
}

// ErrorMessage applies equality check predicate on the "error_message" field. It's identical to ErrorMessageEQ.
func ErrorMessage(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldErrorMessage, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldUserID, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldType, vs...))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldTitle, v))
}

// MessageEQ applies the EQ predicate on the "message" field.
func MessageEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldMessage, v))
}

// MessageNEQ applies the NEQ predicate on the "message" field.
func MessageNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldMessage, v))
}

// MessageIn applies the In predicate on the "message" field.
func MessageIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldMessage, vs...))
}

// MessageNotIn applies the NotIn predicate on the "message" field.
func MessageNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldMessage, vs...))
}

// MessageGT applies the GT predicate on the "message" field.
func MessageGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldMessage, v))
}

// MessageGTE applies the GTE predicate on the "message" field.
func MessageGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldMessage, v))
}

// MessageLT applies the LT predicate on the "message" field.
func MessageLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldMessage, v))
}

// MessageLTE applies the LTE predicate on the "message" field.
func MessageLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldMessage, v))
}

// MessageContains applies the Contains predicate on the "message" field.
func MessageContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldMessage, v))
}

// MessageHasPrefix applies the HasPrefix predicate on the "message" field.
func MessageHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldMessage, v))
}

// MessageHasSuffix applies the HasSuffix predicate on the "message" field.
func MessageHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldMessage, v))
}

// MessageEqualFold applies the EqualFold predicate on the "message" field.
func MessageEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldMessage, v))
}

// MessageContainsFold applies the ContainsFold predicate on the "message" field.
func MessageContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldMessage, v))
}

// DataIsNil applies the IsNil predicate on the "data" field.
func DataIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldData))
}

// DataNotNil applies the NotNil predicate on the "data" field.
func DataNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldData))
}

// PriorityEQ applies the EQ predicate on the "priority" field.
func PriorityEQ(v Priority) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldPriority, v))
}

// PriorityNEQ applies the NEQ predicate on the "priority" field.
func PriorityNEQ(v Priority) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldPriority, v))
}

// PriorityIn applies the In predicate on the "priority" field.
func PriorityIn(vs ...Priority) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldPriority, vs...))
}

// PriorityNotIn applies the NotIn predicate on the "priority" field.
func PriorityNotIn(vs ...Priority) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldPriority, vs...))
}

// ChannelsIsNil applies the IsNil predicate on the "channels" field.
func ChannelsIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldChannels))
}

// ChannelsNotNil applies the NotNil predicate on the "channels" field.
func ChannelsNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldChannels))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldStatus, vs...))
}

// ReferenceIDEQ applies the EQ predicate on the "reference_id" field.
func ReferenceIDEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceIDNEQ applies the NEQ predicate on the "reference_id" field.
func ReferenceIDNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldReferenceID, v))
}

// ReferenceIDIn applies the In predicate on the "reference_id" field.
func ReferenceIDIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldReferenceID, vs...))
}

// ReferenceIDNotIn applies the NotIn predicate on the "reference_id" field.
func ReferenceIDNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldReferenceID, vs...))
}

// ReferenceIDGT applies the GT predicate on the "reference_id" field.
func ReferenceIDGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldReferenceID, v))
}

// ReferenceIDGTE applies the GTE predicate on the "reference_id" field.
func ReferenceIDGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldReferenceID, v))
}

// ReferenceIDLT applies the LT predicate on the "reference_id" field.
func ReferenceIDLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldReferenceID, v))
}

// ReferenceIDLTE applies the LTE predicate on the "reference_id" field.
func ReferenceIDLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldReferenceID, v))
}

// ReferenceIDContains applies the Contains predicate on the "reference_id" field.
func ReferenceIDContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldReferenceID, v))
}

// ReferenceIDHasPrefix applies the HasPrefix predicate on the "reference_id" field.
func ReferenceIDHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldReferenceID, v))
}

// ReferenceIDHasSuffix applies the HasSuffix predicate on the "reference_id" field.
func ReferenceIDHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldReferenceID, v))
}

// ReferenceIDIsNil applies the IsNil predicate on the "reference_id" field.
func ReferenceIDIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldReferenceID))
}

// ReferenceIDNotNil applies the NotNil predicate on the "reference_id" field.
func ReferenceIDNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldReferenceID))
}

// ReferenceIDEqualFold applies the EqualFold predicate on the "reference_id" field.
func ReferenceIDEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldReferenceID, v))
}

// ReferenceIDContainsFold applies the ContainsFold predicate on the "reference_id" field.
func ReferenceIDContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldReferenceID, v))
}

// ReferenceTypeEQ applies the EQ predicate on the "reference_type" field.
func ReferenceTypeEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReferenceType, v))
}

// ReferenceTypeNEQ applies the NEQ predicate on the "reference_type" field.
func ReferenceTypeNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldReferenceType, v))
}

// ReferenceTypeIn applies the In predicate on the "reference_type" field.
func ReferenceTypeIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldReferenceType, vs...))
}

// ReferenceTypeNotIn applies the NotIn predicate on the "reference_type" field.
func ReferenceTypeNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldReferenceType, vs...))
}

// ReferenceTypeGT applies the GT predicate on the "reference_type" field.
func ReferenceTypeGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldReferenceType, v))
}

// ReferenceTypeGTE applies the GTE predicate on the "reference_type" field.
func ReferenceTypeGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldReferenceType, v))
}

// ReferenceTypeLT applies the LT predicate on the "reference_type" field.
func ReferenceTypeLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldReferenceType, v))
}

// ReferenceTypeLTE applies the LTE predicate on the "reference_type" field.
func ReferenceTypeLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldReferenceType, v))
}

// ReferenceTypeContains applies the Contains predicate on the "reference_type" field.
func ReferenceTypeContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldReferenceType, v))
}

// ReferenceTypeHasPrefix applies the HasPrefix predicate on the "reference_type" field.
func ReferenceTypeHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldReferenceType, v))
}

// ReferenceTypeHasSuffix applies the HasSuffix predicate on the "reference_type" field.
func ReferenceTypeHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldReferenceType, v))
}

// ReferenceTypeIsNil applies the IsNil predicate on the "reference_type" field.
func ReferenceTypeIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldReferenceType))
}

// ReferenceTypeNotNil applies the NotNil predicate on the "reference_type" field.
func ReferenceTypeNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldReferenceType))
}

// ReferenceTypeEqualFold applies the EqualFold predicate on the "reference_type" field.
func ReferenceTypeEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldReferenceType, v))
}

// ReferenceTypeContainsFold applies the ContainsFold predicate on the "reference_type" field.
func ReferenceTypeContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldReferenceType, v))
}

// DeliveryStatusIsNil applies the IsNil predicate on the "delivery_status" field.
func DeliveryStatusIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldDeliveryStatus))
}

// DeliveryStatusNotNil applies the NotNil predicate on the "delivery_status" field.
func DeliveryStatusNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldDeliveryStatus))
}

// ScheduledAtEQ applies the EQ predicate on the "scheduled_at" field.
func ScheduledAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldScheduledAt, v))
}

// ScheduledAtNEQ applies the NEQ predicate on the "scheduled_at" field.
func ScheduledAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldScheduledAt, v))
}

// ScheduledAtIn applies the In predicate on the "scheduled_at" field.
func ScheduledAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldScheduledAt, vs...))
}

// ScheduledAtNotIn applies the NotIn predicate on the "scheduled_at" field.
func ScheduledAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldScheduledAt, vs...))
}

// ScheduledAtGT applies the GT predicate on the "scheduled_at" field.
func ScheduledAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldScheduledAt, v))
}

// ScheduledAtGTE applies the GTE predicate on the "scheduled_at" field.
func ScheduledAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldScheduledAt, v))
}

// ScheduledAtLT applies the LT predicate on the "scheduled_at" field.
func ScheduledAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldScheduledAt, v))
}

// ScheduledAtLTE applies the LTE predicate on the "scheduled_at" field.
func ScheduledAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldScheduledAt, v))
}

// ScheduledAtIsNil applies the IsNil predicate on the "scheduled_at" field.
func ScheduledAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldScheduledAt))
}

// ScheduledAtNotNil applies the NotNil predicate on the "scheduled_at" field.
func ScheduledAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldScheduledAt))
}

// SentAtEQ applies the EQ predicate on the "sent_at" field.
func SentAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldSentAt, v))
}

// SentAtNEQ applies the NEQ predicate on the "sent_at" field.
func SentAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldSentAt, v))
}

// SentAtIn applies the In predicate on the "sent_at" field.
func SentAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldSentAt, vs...))
}

// SentAtNotIn applies the NotIn predicate on the "sent_at" field.
func SentAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldSentAt, vs...))
}

// SentAtGT applies the GT predicate on the "sent_at" field.
func SentAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldSentAt, v))
}

// SentAtGTE applies the GTE predicate on the "sent_at" field.
func SentAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldSentAt, v))
}

// SentAtLT applies the LT predicate on the "sent_at" field.
func SentAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldSentAt, v))
}

// SentAtLTE applies the LTE predicate on the "sent_at" field.
func SentAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldSentAt, v))
}

// SentAtIsNil applies the IsNil predicate on the "sent_at" field.
func SentAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldSentAt))
}

// SentAtNotNil applies the NotNil predicate on the "sent_at" field.
func SentAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldSentAt))
}

// DeliveredAtEQ applies the EQ predicate on the "delivered_at" field.
func DeliveredAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldDeliveredAt, v))
}

// DeliveredAtNEQ applies the NEQ predicate on the "delivered_at" field.
func DeliveredAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldDeliveredAt, v))
}

// DeliveredAtIn applies the In predicate on the "delivered_at" field.
func DeliveredAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldDeliveredAt, vs...))
}

// DeliveredAtNotIn applies the NotIn predicate on the "delivered_at" field.
func DeliveredAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldDeliveredAt, vs...))
}

// DeliveredAtGT applies the GT predicate on the "delivered_at" field.
func DeliveredAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldDeliveredAt, v))
}

// DeliveredAtGTE applies the GTE predicate on the "delivered_at" field.
func DeliveredAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldDeliveredAt, v))
}

// DeliveredAtLT applies the LT predicate on the "delivered_at" field.
func DeliveredAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldDeliveredAt, v))
}

// DeliveredAtLTE applies the LTE predicate on the "delivered_at" field.
func DeliveredAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldDeliveredAt, v))
}

// DeliveredAtIsNil applies the IsNil predicate on the "delivered_at" field.
func DeliveredAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldDeliveredAt))
}

// DeliveredAtNotNil applies the NotNil predicate on the "delivered_at" field.
func DeliveredAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldDeliveredAt))
}

// ReadAtEQ applies the EQ predicate on the "read_at" field.
func ReadAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldReadAt, v))
}

// ReadAtNEQ applies the NEQ predicate on the "read_at" field.
func ReadAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldReadAt, v))
}

// ReadAtIn applies the In predicate on the "read_at" field.
func ReadAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldReadAt, vs...))
}

// ReadAtNotIn applies the NotIn predicate on the "read_at" field.
func ReadAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldReadAt, vs...))
}

// ReadAtGT applies the GT predicate on the "read_at" field.
func ReadAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldReadAt, v))
}

// ReadAtGTE applies the GTE predicate on the "read_at" field.
func ReadAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldReadAt, v))
}

// ReadAtLT applies the LT predicate on the "read_at" field.
func ReadAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldReadAt, v))
}

// ReadAtLTE applies the LTE predicate on the "read_at" field.
func ReadAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldReadAt, v))
}

// ReadAtIsNil applies the IsNil predicate on the "read_at" field.
func ReadAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldReadAt))
}

// ReadAtNotNil applies the NotNil predicate on the "read_at" field.
func ReadAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldReadAt))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldExpiresAt, v))
}

// ExpiresAtIsNil applies the IsNil predicate on the "expires_at" field.
func ExpiresAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldExpiresAt))
}

// ExpiresAtNotNil applies the NotNil predicate on the "expires_at" field.
func ExpiresAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldExpiresAt))
}

// ActionURLEQ applies the EQ predicate on the "action_url" field.
func ActionURLEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldActionURL, v))
}

// ActionURLNEQ applies the NEQ predicate on the "action_url" field.
func ActionURLNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldActionURL, v))
}

// ActionURLIn applies the In predicate on the "action_url" field.
func ActionURLIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldActionURL, vs...))
}

// ActionURLNotIn applies the NotIn predicate on the "action_url" field.
func ActionURLNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldActionURL, vs...))
}

// ActionURLGT applies the GT predicate on the "action_url" field.
func ActionURLGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldActionURL, v))
}

// ActionURLGTE applies the GTE predicate on the "action_url" field.
func ActionURLGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldActionURL, v))
}

// ActionURLLT applies the LT predicate on the "action_url" field.
func ActionURLLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldActionURL, v))
}

// ActionURLLTE applies the LTE predicate on the "action_url" field.
func ActionURLLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldActionURL, v))
}

// ActionURLContains applies the Contains predicate on the "action_url" field.
func ActionURLContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldActionURL, v))
}

// ActionURLHasPrefix applies the HasPrefix predicate on the "action_url" field.
func ActionURLHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldActionURL, v))
}

// ActionURLHasSuffix applies the HasSuffix predicate on the "action_url" field.
func ActionURLHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldActionURL, v))
}

// ActionURLIsNil applies the IsNil predicate on the "action_url" field.
func ActionURLIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldActionURL))
}

// ActionURLNotNil applies the NotNil predicate on the "action_url" field.
func ActionURLNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldActionURL))
}

// ActionURLEqualFold applies the EqualFold predicate on the "action_url" field.
func ActionURLEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldActionURL, v))
}

// ActionURLContainsFold applies the ContainsFold predicate on the "action_url" field.
func ActionURLContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldActionURL, v))
}

// ActionDataIsNil applies the IsNil predicate on the "action_data" field.
func ActionDataIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldActionData))
}

// ActionDataNotNil applies the NotNil predicate on the "action_data" field.
func ActionDataNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldActionData))
}

// RetryCountEQ applies the EQ predicate on the "retry_count" field.
func RetryCountEQ(v int) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldRetryCount, v))
}

// RetryCountNEQ applies the NEQ predicate on the "retry_count" field.
func RetryCountNEQ(v int) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldRetryCount, v))
}

// RetryCountIn applies the In predicate on the "retry_count" field.
func RetryCountIn(vs ...int) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldRetryCount, vs...))
}

// RetryCountNotIn applies the NotIn predicate on the "retry_count" field.
func RetryCountNotIn(vs ...int) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldRetryCount, vs...))
}

// RetryCountGT applies the GT predicate on the "retry_count" field.
func RetryCountGT(v int) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldRetryCount, v))
}

// RetryCountGTE applies the GTE predicate on the "retry_count" field.
func RetryCountGTE(v int) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldRetryCount, v))
}

// RetryCountLT applies the LT predicate on the "retry_count" field.
func RetryCountLT(v int) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldRetryCount, v))
}

// RetryCountLTE applies the LTE predicate on the "retry_count" field.
func RetryCountLTE(v int) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldRetryCount, v))
}

// NextRetryAtEQ applies the EQ predicate on the "next_retry_at" field.
func NextRetryAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldNextRetryAt, v))
}

// NextRetryAtNEQ applies the NEQ predicate on the "next_retry_at" field.
func NextRetryAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldNextRetryAt, v))
}

// NextRetryAtIn applies the In predicate on the "next_retry_at" field.
func NextRetryAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldNextRetryAt, vs...))
}

// NextRetryAtNotIn applies the NotIn predicate on the "next_retry_at" field.
func NextRetryAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldNextRetryAt, vs...))
}

// NextRetryAtGT applies the GT predicate on the "next_retry_at" field.
func NextRetryAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldNextRetryAt, v))
}

// NextRetryAtGTE applies the GTE predicate on the "next_retry_at" field.
func NextRetryAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldNextRetryAt, v))
}

// NextRetryAtLT applies the LT predicate on the "next_retry_at" field.
func NextRetryAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldNextRetryAt, v))
}

// NextRetryAtLTE applies the LTE predicate on the "next_retry_at" field.
func NextRetryAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldNextRetryAt, v))
}

// NextRetryAtIsNil applies the IsNil predicate on the "next_retry_at" field.
func NextRetryAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldNextRetryAt))
}

// NextRetryAtNotNil applies the NotNil predicate on the "next_retry_at" field.
func NextRetryAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldNextRetryAt))
}

// ErrorMessageEQ applies the EQ predicate on the "error_message" field.
func ErrorMessageEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldErrorMessage, v))
}

// ErrorMessageNEQ applies the NEQ predicate on the "error_message" field.
func ErrorMessageNEQ(v string) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldErrorMessage, v))
}

// ErrorMessageIn applies the In predicate on the "error_message" field.
func ErrorMessageIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldErrorMessage, vs...))
}

// ErrorMessageNotIn applies the NotIn predicate on the "error_message" field.
func ErrorMessageNotIn(vs ...string) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldErrorMessage, vs...))
}

// ErrorMessageGT applies the GT predicate on the "error_message" field.
func ErrorMessageGT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldErrorMessage, v))
}

// ErrorMessageGTE applies the GTE predicate on the "error_message" field.
func ErrorMessageGTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldErrorMessage, v))
}

// ErrorMessageLT applies the LT predicate on the "error_message" field.
func ErrorMessageLT(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldErrorMessage, v))
}

// ErrorMessageLTE applies the LTE predicate on the "error_message" field.
func ErrorMessageLTE(v string) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldErrorMessage, v))
}

// ErrorMessageContains applies the Contains predicate on the "error_message" field.
func ErrorMessageContains(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContains(FieldErrorMessage, v))
}

// ErrorMessageHasPrefix applies the HasPrefix predicate on the "error_message" field.
func ErrorMessageHasPrefix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasPrefix(FieldErrorMessage, v))
}

// ErrorMessageHasSuffix applies the HasSuffix predicate on the "error_message" field.
func ErrorMessageHasSuffix(v string) predicate.Notification {
	return predicate.Notification(sql.FieldHasSuffix(FieldErrorMessage, v))
}

// ErrorMessageIsNil applies the IsNil predicate on the "error_message" field.
func ErrorMessageIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldErrorMessage))
}

// ErrorMessageNotNil applies the NotNil predicate on the "error_message" field.
func ErrorMessageNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldErrorMessage))
}

// ErrorMessageEqualFold applies the EqualFold predicate on the "error_message" field.
func ErrorMessageEqualFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldEqualFold(FieldErrorMessage, v))
}

// ErrorMessageContainsFold applies the ContainsFold predicate on the "error_message" field.
func ErrorMessageContainsFold(v string) predicate.Notification {
	return predicate.Notification(sql.FieldContainsFold(FieldErrorMessage, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Notification {
	return predicate.Notification(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Notification {
	return predicate.Notification(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Notification {
	return predicate.Notification(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Notification) predicate.Notification {
	return predicate.Notification(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Notification) predicate.Notification {
	return predicate.Notification(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Notification) predicate.Notification {
	return predicate.Notification(sql.NotPredicates(p))
}

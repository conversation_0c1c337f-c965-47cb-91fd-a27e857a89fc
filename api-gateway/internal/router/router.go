package router

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/social-content-ai/api-gateway/internal/handlers"
	"github.com/social-content-ai/api-gateway/internal/middleware"
	"github.com/social-content-ai/api-gateway/internal/services"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// SetupRouter configures and returns the main router
func SetupRouter(serviceClients *services.ServiceClients, logger logging.Logger) *gin.Engine {
	// Create router
	router := gin.New()

	// Add middleware
	router.Use(middleware.RequestLogger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	router.Use(middleware.SecurityHeaders())
	router.Use(middleware.RequestID())
	router.Use(middleware.RateLimit())

	// Create handlers
	h := handlers.NewHandlers(serviceClients, logger)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "api-gateway",
			"version": "1.0.0",
		})
	})

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		setupAuthRoutes(v1, h, serviceClients.UserService, logger)
		setupUserRoutes(v1, h, serviceClients.UserService, logger)
		setupContentRoutes(v1, h, serviceClients.UserService, logger)
		setupPostRoutes(v1, h, serviceClients.UserService, logger)
		setupTemplateRoutes(v1, h, serviceClients.UserService, logger)
		setupWorkspaceRoutes(v1, h, serviceClients.UserService, logger)
		setupCreditRoutes(v1, h, serviceClients.UserService, logger)
		setupAssetRoutes(v1, h, serviceClients.UserService, logger)
		setupIntegrationRoutes(v1, h, serviceClients.UserService, logger)
		setupNotificationRoutes(v1, h, serviceClients.UserService, logger)
		setupAnalyticsRoutes(v1, h, serviceClients.UserService, logger)
		setupAdminRoutes(v1, h, serviceClients.UserService, logger)
	}

	return router
}

// setupAuthRoutes configures authentication routes
func setupAuthRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	auth := rg.Group("/auth")
	{
		auth.POST("/register", h.Register)
		auth.POST("/login", h.Login)
		auth.POST("/refresh", h.RefreshToken)
		auth.POST("/forgot-password", h.ForgotPassword)
		auth.POST("/reset-password", h.ResetPassword)

		// Protected auth routes
		authProtected := auth.Group("")
		authProtected.Use(middleware.AuthRequired(userService, logger))
		{
			authProtected.POST("/logout", h.Logout)
		}
	}
}

// setupUserRoutes configures user management routes
func setupUserRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	users := rg.Group("/users")
	users.Use(middleware.AuthRequired(userService, logger))
	{
		users.GET("/me", h.GetCurrentUser)
		users.PUT("/me", h.UpdateCurrentUser)
		users.POST("/me/avatar", h.UploadAvatar)
		users.GET("/me/credits", h.GetUserCredits)
		users.GET("/me/transactions", h.GetUserTransactions)
	}
}

// setupContentRoutes configures content generation routes
func setupContentRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	content := rg.Group("/content")
	content.Use(middleware.AuthRequired(userService, logger))
	{
		content.POST("/generate", h.GenerateContent)
		content.POST("/improve", h.ImproveContent)
		content.POST("/hashtags", h.GenerateHashtags)
		content.GET("/generations", h.ListGenerations)
		content.GET("/generations/:id", h.GetGeneration)
		content.DELETE("/generations/:id", h.DeleteGeneration)
	}
}

// setupPostRoutes configures post management routes
func setupPostRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	posts := rg.Group("/posts")
	posts.Use(middleware.AuthRequired(userService, logger))
	{
		posts.POST("", h.CreatePost)
		posts.GET("", h.ListPosts)
		posts.GET("/:id", h.GetPost)
		posts.PUT("/:id", h.UpdatePost)
		posts.DELETE("/:id", h.DeletePost)
		posts.POST("/:id/schedule", h.SchedulePost)
		posts.POST("/:id/publish", h.PublishPost)
	}
}

// setupTemplateRoutes configures template management routes
func setupTemplateRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	templates := rg.Group("/templates")
	templates.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement template handlers
		// templates.POST("", h.CreateTemplate)
		// templates.GET("", h.ListTemplates)
		// templates.GET("/:id", h.GetTemplate)
		// templates.PUT("/:id", h.UpdateTemplate)
		// templates.DELETE("/:id", h.DeleteTemplate)
		// templates.POST("/:id/use", h.UseTemplate)
	}
}

// setupWorkspaceRoutes configures workspace management routes
func setupWorkspaceRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	workspaces := rg.Group("/workspaces")
	workspaces.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement workspace handlers
		// workspaces.POST("", h.CreateWorkspace)
		// workspaces.GET("", h.ListWorkspaces)
		// workspaces.GET("/:id", h.GetWorkspace)
		// workspaces.PUT("/:id", h.UpdateWorkspace)
		// workspaces.DELETE("/:id", h.DeleteWorkspace)
		// workspaces.POST("/:id/members", h.AddWorkspaceMember)
		// workspaces.DELETE("/:id/members/:user_id", h.RemoveWorkspaceMember)
	}
}

// setupCreditRoutes configures credit management routes
func setupCreditRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	credits := rg.Group("/credits")
	credits.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement credit handlers
		// credits.GET("/balance", h.GetCreditBalance)
		// credits.GET("/transactions", h.GetCreditTransactions)
		// credits.POST("/purchase", h.PurchaseCredits)
		// credits.GET("/plans", h.GetSubscriptionPlans)
		// credits.POST("/subscribe", h.CreateSubscription)
		// credits.PUT("/subscription", h.UpdateSubscription)
		// credits.DELETE("/subscription", h.CancelSubscription)
	}
}

// setupAssetRoutes configures asset management routes
func setupAssetRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	assets := rg.Group("/assets")
	assets.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement asset handlers
		// assets.POST("/upload-url", h.GenerateUploadURL)
		// assets.POST("/upload", h.UploadAsset)
		// assets.POST("/:id/complete", h.CompleteUpload)
		// assets.GET("", h.ListAssets)
		// assets.GET("/:id", h.GetAsset)
		// assets.PUT("/:id", h.UpdateAsset)
		// assets.DELETE("/:id", h.DeleteAsset)
		// assets.GET("/:id/download", h.DownloadAsset)
	}
}

// setupIntegrationRoutes configures social media integration routes
func setupIntegrationRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	integrations := rg.Group("/integrations")
	integrations.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement integration handlers
		// integrations.GET("", h.ListIntegrations)
		// integrations.GET("/:platform/auth", h.StartOAuthFlow)
		// integrations.POST("/:platform/callback", h.HandleOAuthCallback)
		// integrations.DELETE("/:id", h.DisconnectIntegration)
		// integrations.POST("/:id/test", h.TestIntegration)
		// integrations.GET("/:id/analytics", h.GetIntegrationAnalytics)
	}
}

// setupNotificationRoutes configures notification routes
func setupNotificationRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	notifications := rg.Group("/notifications")
	notifications.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement notification handlers
		// notifications.GET("", h.ListNotifications)
		// notifications.PUT("/:id/read", h.MarkNotificationRead)
		// notifications.PUT("/read-all", h.MarkAllNotificationsRead)
		// notifications.DELETE("/:id", h.DeleteNotification)
		// notifications.GET("/preferences", h.GetNotificationPreferences)
		// notifications.PUT("/preferences", h.UpdateNotificationPreferences)
	}
}

// setupAnalyticsRoutes configures analytics routes
func setupAnalyticsRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	analytics := rg.Group("/analytics")
	analytics.Use(middleware.AuthRequired(userService, logger))
	{
		// TODO: Implement analytics handlers
		// analytics.GET("/dashboard", h.GetAnalyticsDashboard)
		// analytics.GET("/posts/:id", h.GetPostAnalytics)
		// analytics.GET("/content-performance", h.GetContentPerformance)
		// analytics.GET("/audience-insights", h.GetAudienceInsights)
		// analytics.GET("/engagement-trends", h.GetEngagementTrends)
		// analytics.GET("/reports", h.GetAnalyticsReports)
		// analytics.POST("/reports", h.GenerateAnalyticsReport)
	}
}

// setupAdminRoutes configures admin-only routes
func setupAdminRoutes(rg *gin.RouterGroup, h *handlers.Handlers, userService userv1.AuthServiceClient, logger logging.Logger) {
	admin := rg.Group("/admin")
	admin.Use(middleware.AuthRequired(userService, logger))
	admin.Use(middleware.AdminRequired())
	{
		// User management
		admin.GET("/users", h.ListAllUsers)
		admin.GET("/users/:id", h.GetUserByID)
		admin.PUT("/users/:id", h.UpdateUserByID)
		admin.DELETE("/users/:id", h.DeleteUserByID)

		// TODO: Implement additional admin handlers
		// admin.GET("/stats", h.GetSystemStats)
		// admin.GET("/health", h.GetSystemHealth)
		// admin.GET("/logs", h.GetSystemLogs)
		// admin.POST("/maintenance", h.EnableMaintenanceMode)
		// admin.DELETE("/maintenance", h.DisableMaintenanceMode)
	}
}

package publishers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// LinkedInPublisher handles LinkedIn content publishing
type LinkedInPublisher struct {
	httpClient *http.Client
	logger     logging.Logger
}

// NewLinkedInPublisher creates a new LinkedIn publisher
func NewLinkedInPublisher(logger logging.Logger) Publisher {
	return &LinkedInPublisher{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// LinkedInPostRequest represents a LinkedIn post request
type LinkedInPostRequest struct {
	Author          string                  `json:"author"`
	LifecycleState  string                  `json:"lifecycleState"`
	SpecificContent LinkedInSpecificContent `json:"specificContent"`
	Visibility      LinkedInVisibility      `json:"visibility"`
}

// LinkedInSpecificContent represents LinkedIn post content
type LinkedInSpecificContent struct {
	ShareContent LinkedInShareContent `json:"com.linkedin.ugc.ShareContent"`
}

// LinkedInShareContent represents LinkedIn share content
type LinkedInShareContent struct {
	ShareCommentary    LinkedInShareCommentary `json:"shareCommentary"`
	ShareMediaCategory string                  `json:"shareMediaCategory"`
	Media              []LinkedInMedia         `json:"media,omitempty"`
}

// LinkedInShareCommentary represents LinkedIn post text
type LinkedInShareCommentary struct {
	Text string `json:"text"`
}

// LinkedInMedia represents LinkedIn media
type LinkedInMedia struct {
	Status      string                   `json:"status"`
	Description LinkedInMediaDescription `json:"description"`
	Media       string                   `json:"media"`
	Title       LinkedInMediaTitle       `json:"title"`
}

// LinkedInMediaDescription represents media description
type LinkedInMediaDescription struct {
	Text string `json:"text"`
}

// LinkedInMediaTitle represents media title
type LinkedInMediaTitle struct {
	Text string `json:"text"`
}

// LinkedInVisibility represents post visibility
type LinkedInVisibility struct {
	MemberNetworkVisibility string `json:"com.linkedin.ugc.MemberNetworkVisibility"`
}

// LinkedInPostResponse represents LinkedIn post response
type LinkedInPostResponse struct {
	ID string `json:"id"`
}

// LinkedInAnalyticsResponse represents LinkedIn analytics response
type LinkedInAnalyticsResponse struct {
	Elements []struct {
		TotalShareStatistics struct {
			ShareCount      int64 `json:"shareCount"`
			LikeCount       int64 `json:"likeCount"`
			CommentCount    int64 `json:"commentCount"`
			ClickCount      int64 `json:"clickCount"`
			ImpressionCount int64 `json:"impressionCount"`
		} `json:"totalShareStatistics"`
	} `json:"elements"`
}

// PublishPost publishes content to LinkedIn
func (l *LinkedInPublisher) PublishPost(ctx context.Context, req *PublishRequest) (*PublishResponse, error) {
	l.logger.Info("Publishing post to LinkedIn")

	// Get user profile to get author URN
	authorURN, err := l.getUserURN(ctx, req.AccessToken)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to get user URN: %v", err),
		}, nil
	}

	// Prepare post content
	postReq := LinkedInPostRequest{
		Author:         authorURN,
		LifecycleState: "PUBLISHED",
		SpecificContent: LinkedInSpecificContent{
			ShareContent: LinkedInShareContent{
				ShareCommentary: LinkedInShareCommentary{
					Text: req.Content,
				},
				ShareMediaCategory: "NONE",
			},
		},
		Visibility: LinkedInVisibility{
			MemberNetworkVisibility: "PUBLIC",
		},
	}

	// Add media if provided
	if len(req.ImageURLs) > 0 {
		postReq.SpecificContent.ShareContent.ShareMediaCategory = "IMAGE"
		media := make([]LinkedInMedia, len(req.ImageURLs))
		for i, imageURL := range req.ImageURLs {
			// Upload media first
			mediaURN, err := l.uploadMedia(ctx, req.AccessToken, imageURL)
			if err != nil {
				l.logger.WithError(err).Warn("Failed to upload media, posting without media")
				postReq.SpecificContent.ShareContent.ShareMediaCategory = "NONE"
				break
			}

			media[i] = LinkedInMedia{
				Status: "READY",
				Description: LinkedInMediaDescription{
					Text: "Image",
				},
				Media: mediaURN,
				Title: LinkedInMediaTitle{
					Text: "Image",
				},
			}
		}
		if len(media) > 0 {
			postReq.SpecificContent.ShareContent.Media = media
		}
	}

	// Post to LinkedIn
	postURL := "https://api.linkedin.com/v2/ugcPosts"

	jsonData, err := json.Marshal(postReq)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to marshal post request: %v", err),
		}, nil
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", postURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create request: %v", err),
		}, nil
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Restli-Protocol-Version", "2.0.0")

	resp, err := l.httpClient.Do(httpReq)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to post to LinkedIn: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("LinkedIn API error, status: %d", resp.StatusCode),
		}, nil
	}

	var postResp LinkedInPostResponse
	if err := json.NewDecoder(resp.Body).Decode(&postResp); err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to decode response: %v", err),
		}, nil
	}

	// Extract post ID from URN
	postID := postResp.ID
	if len(postID) > 0 {
		// LinkedIn returns URN format, extract the actual ID
		if idx := len(postID) - 1; idx > 0 {
			for i := len(postID) - 1; i >= 0; i-- {
				if postID[i] == ':' {
					postID = postID[i+1:]
					break
				}
			}
		}
	}

	platformURL := fmt.Sprintf("https://www.linkedin.com/feed/update/%s/", postID)

	return &PublishResponse{
		PlatformPostID: postResp.ID,
		PlatformURL:    platformURL,
		Success:        true,
		PublishedAt:    time.Now().Unix(),
		Metadata: map[string]string{
			"author_urn": authorURN,
		},
	}, nil
}

// getUserURN gets the user's LinkedIn URN
func (l *LinkedInPublisher) getUserURN(ctx context.Context, accessToken string) (string, error) {
	userURL := "https://api.linkedin.com/v2/people/~"

	req, err := http.NewRequestWithContext(ctx, "GET", userURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := l.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var userResp struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userResp); err != nil {
		return "", fmt.Errorf("failed to decode user response: %w", err)
	}

	return fmt.Sprintf("urn:li:person:%s", userResp.ID), nil
}

// uploadMedia uploads media to LinkedIn (placeholder implementation)
func (l *LinkedInPublisher) uploadMedia(ctx context.Context, accessToken, imageURL string) (string, error) {
	// LinkedIn media upload is complex and requires multiple steps
	// This is a placeholder implementation
	l.logger.Info("LinkedIn media upload not fully implemented")
	return "", fmt.Errorf("media upload not implemented")
}

// GetPostAnalytics retrieves analytics for a LinkedIn post
func (l *LinkedInPublisher) GetPostAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error) {
	l.logger.WithField("post_id", req.PlatformPostID).Info("Getting LinkedIn post analytics")

	analyticsURL := "https://api.linkedin.com/v2/socialActions"

	httpReq, err := http.NewRequestWithContext(ctx, "GET", analyticsURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))

	resp, err := l.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("analytics request failed with status: %d", resp.StatusCode)
	}

	var analyticsResp LinkedInAnalyticsResponse
	if err := json.NewDecoder(resp.Body).Decode(&analyticsResp); err != nil {
		return nil, fmt.Errorf("failed to decode analytics response: %w", err)
	}

	metrics := make(map[string]int64)
	if len(analyticsResp.Elements) > 0 {
		stats := analyticsResp.Elements[0].TotalShareStatistics
		metrics["shares"] = stats.ShareCount
		metrics["likes"] = stats.LikeCount
		metrics["comments"] = stats.CommentCount
		metrics["clicks"] = stats.ClickCount
		metrics["impressions"] = stats.ImpressionCount
		metrics["engagement"] = stats.LikeCount + stats.CommentCount + stats.ShareCount
	}

	return &AnalyticsResponse{
		PlatformPostID: req.PlatformPostID,
		Metrics:        metrics,
		CollectedAt:    time.Now().Unix(),
	}, nil
}

// DeletePost deletes a LinkedIn post
func (l *LinkedInPublisher) DeletePost(ctx context.Context, req *DeleteRequest) error {
	l.logger.WithField("post_id", req.PlatformPostID).Info("Deleting LinkedIn post")

	deleteURL := fmt.Sprintf("https://api.linkedin.com/v2/ugcPosts/%s", req.PlatformPostID)

	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", deleteURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", req.AccessToken))

	resp, err := l.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		return fmt.Errorf("delete request failed with status: %d", resp.StatusCode)
	}

	return nil
}

// GetPlatformLimits gets LinkedIn rate limits
func (l *LinkedInPublisher) GetPlatformLimits(ctx context.Context) (*PlatformLimits, error) {
	// LinkedIn rate limits (approximate)
	return &PlatformLimits{
		RateLimits: map[string]int32{
			"posts":     100, // 100 posts per day
			"api_calls": 500, // 500 API calls per day
		},
		CurrentUsage: map[string]int32{
			"posts":     0,
			"api_calls": 0,
		},
		ResetTimes: map[string]int64{
			"posts":     time.Now().Add(24 * time.Hour).Unix(),
			"api_calls": time.Now().Add(24 * time.Hour).Unix(),
		},
	}, nil
}

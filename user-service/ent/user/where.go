// Code generated by ent, DO NOT EDIT.

package user

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.User {
	return predicate.User(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.User {
	return predicate.User(sql.FieldLTE(FieldID, id))
}

// FullName applies equality check predicate on the "full_name" field. It's identical to FullNameEQ.
func FullName(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldFullName, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPhone, v))
}

// AvatarURL applies equality check predicate on the "avatar_url" field. It's identical to AvatarURLEQ.
func AvatarURL(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatarURL, v))
}

// Bio applies equality check predicate on the "bio" field. It's identical to BioEQ.
func Bio(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldBio, v))
}

// Company applies equality check predicate on the "company" field. It's identical to CompanyEQ.
func Company(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCompany, v))
}

// Industry applies equality check predicate on the "industry" field. It's identical to IndustryEQ.
func Industry(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIndustry, v))
}

// PasswordHash applies equality check predicate on the "password_hash" field. It's identical to PasswordHashEQ.
func PasswordHash(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPasswordHash, v))
}

// IsVerified applies equality check predicate on the "is_verified" field. It's identical to IsVerifiedEQ.
func IsVerified(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIsVerified, v))
}

// TwoFactorEnabled applies equality check predicate on the "two_factor_enabled" field. It's identical to TwoFactorEnabledEQ.
func TwoFactorEnabled(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorEnabled, v))
}

// TwoFactorSecret applies equality check predicate on the "two_factor_secret" field. It's identical to TwoFactorSecretEQ.
func TwoFactorSecret(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorSecret, v))
}

// TwoFactorEnabledAt applies equality check predicate on the "two_factor_enabled_at" field. It's identical to TwoFactorEnabledAtEQ.
func TwoFactorEnabledAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorEnabledAt, v))
}

// InviteCode applies equality check predicate on the "invite_code" field. It's identical to InviteCodeEQ.
func InviteCode(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldInviteCode, v))
}

// AffCode applies equality check predicate on the "aff_code" field. It's identical to AffCodeEQ.
func AffCode(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAffCode, v))
}

// AffJoinedAt applies equality check predicate on the "aff_joined_at" field. It's identical to AffJoinedAtEQ.
func AffJoinedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAffJoinedAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// FullNameEQ applies the EQ predicate on the "full_name" field.
func FullNameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldFullName, v))
}

// FullNameNEQ applies the NEQ predicate on the "full_name" field.
func FullNameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldFullName, v))
}

// FullNameIn applies the In predicate on the "full_name" field.
func FullNameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldFullName, vs...))
}

// FullNameNotIn applies the NotIn predicate on the "full_name" field.
func FullNameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldFullName, vs...))
}

// FullNameGT applies the GT predicate on the "full_name" field.
func FullNameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldFullName, v))
}

// FullNameGTE applies the GTE predicate on the "full_name" field.
func FullNameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldFullName, v))
}

// FullNameLT applies the LT predicate on the "full_name" field.
func FullNameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldFullName, v))
}

// FullNameLTE applies the LTE predicate on the "full_name" field.
func FullNameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldFullName, v))
}

// FullNameContains applies the Contains predicate on the "full_name" field.
func FullNameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldFullName, v))
}

// FullNameHasPrefix applies the HasPrefix predicate on the "full_name" field.
func FullNameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldFullName, v))
}

// FullNameHasSuffix applies the HasSuffix predicate on the "full_name" field.
func FullNameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldFullName, v))
}

// FullNameIsNil applies the IsNil predicate on the "full_name" field.
func FullNameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldFullName))
}

// FullNameNotNil applies the NotNil predicate on the "full_name" field.
func FullNameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldFullName))
}

// FullNameEqualFold applies the EqualFold predicate on the "full_name" field.
func FullNameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldFullName, v))
}

// FullNameContainsFold applies the ContainsFold predicate on the "full_name" field.
func FullNameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldFullName, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldEmail, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPhone, v))
}

// PhoneIn applies the In predicate on the "phone" field.
func PhoneIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPhone, vs...))
}

// PhoneNotIn applies the NotIn predicate on the "phone" field.
func PhoneNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPhone, vs...))
}

// PhoneGT applies the GT predicate on the "phone" field.
func PhoneGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPhone, v))
}

// PhoneGTE applies the GTE predicate on the "phone" field.
func PhoneGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPhone, v))
}

// PhoneLT applies the LT predicate on the "phone" field.
func PhoneLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPhone, v))
}

// PhoneLTE applies the LTE predicate on the "phone" field.
func PhoneLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPhone, v))
}

// PhoneContains applies the Contains predicate on the "phone" field.
func PhoneContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPhone, v))
}

// PhoneHasPrefix applies the HasPrefix predicate on the "phone" field.
func PhoneHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPhone, v))
}

// PhoneHasSuffix applies the HasSuffix predicate on the "phone" field.
func PhoneHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPhone, v))
}

// PhoneIsNil applies the IsNil predicate on the "phone" field.
func PhoneIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldPhone))
}

// PhoneNotNil applies the NotNil predicate on the "phone" field.
func PhoneNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldPhone))
}

// PhoneEqualFold applies the EqualFold predicate on the "phone" field.
func PhoneEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPhone, v))
}

// PhoneContainsFold applies the ContainsFold predicate on the "phone" field.
func PhoneContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPhone, v))
}

// AvatarURLEQ applies the EQ predicate on the "avatar_url" field.
func AvatarURLEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatarURL, v))
}

// AvatarURLNEQ applies the NEQ predicate on the "avatar_url" field.
func AvatarURLNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAvatarURL, v))
}

// AvatarURLIn applies the In predicate on the "avatar_url" field.
func AvatarURLIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldAvatarURL, vs...))
}

// AvatarURLNotIn applies the NotIn predicate on the "avatar_url" field.
func AvatarURLNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAvatarURL, vs...))
}

// AvatarURLGT applies the GT predicate on the "avatar_url" field.
func AvatarURLGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldAvatarURL, v))
}

// AvatarURLGTE applies the GTE predicate on the "avatar_url" field.
func AvatarURLGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAvatarURL, v))
}

// AvatarURLLT applies the LT predicate on the "avatar_url" field.
func AvatarURLLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldAvatarURL, v))
}

// AvatarURLLTE applies the LTE predicate on the "avatar_url" field.
func AvatarURLLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAvatarURL, v))
}

// AvatarURLContains applies the Contains predicate on the "avatar_url" field.
func AvatarURLContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldAvatarURL, v))
}

// AvatarURLHasPrefix applies the HasPrefix predicate on the "avatar_url" field.
func AvatarURLHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldAvatarURL, v))
}

// AvatarURLHasSuffix applies the HasSuffix predicate on the "avatar_url" field.
func AvatarURLHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldAvatarURL, v))
}

// AvatarURLIsNil applies the IsNil predicate on the "avatar_url" field.
func AvatarURLIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAvatarURL))
}

// AvatarURLNotNil applies the NotNil predicate on the "avatar_url" field.
func AvatarURLNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAvatarURL))
}

// AvatarURLEqualFold applies the EqualFold predicate on the "avatar_url" field.
func AvatarURLEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldAvatarURL, v))
}

// AvatarURLContainsFold applies the ContainsFold predicate on the "avatar_url" field.
func AvatarURLContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldAvatarURL, v))
}

// BioEQ applies the EQ predicate on the "bio" field.
func BioEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldBio, v))
}

// BioNEQ applies the NEQ predicate on the "bio" field.
func BioNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldBio, v))
}

// BioIn applies the In predicate on the "bio" field.
func BioIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldBio, vs...))
}

// BioNotIn applies the NotIn predicate on the "bio" field.
func BioNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldBio, vs...))
}

// BioGT applies the GT predicate on the "bio" field.
func BioGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldBio, v))
}

// BioGTE applies the GTE predicate on the "bio" field.
func BioGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldBio, v))
}

// BioLT applies the LT predicate on the "bio" field.
func BioLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldBio, v))
}

// BioLTE applies the LTE predicate on the "bio" field.
func BioLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldBio, v))
}

// BioContains applies the Contains predicate on the "bio" field.
func BioContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldBio, v))
}

// BioHasPrefix applies the HasPrefix predicate on the "bio" field.
func BioHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldBio, v))
}

// BioHasSuffix applies the HasSuffix predicate on the "bio" field.
func BioHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldBio, v))
}

// BioIsNil applies the IsNil predicate on the "bio" field.
func BioIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldBio))
}

// BioNotNil applies the NotNil predicate on the "bio" field.
func BioNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldBio))
}

// BioEqualFold applies the EqualFold predicate on the "bio" field.
func BioEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldBio, v))
}

// BioContainsFold applies the ContainsFold predicate on the "bio" field.
func BioContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldBio, v))
}

// CompanyEQ applies the EQ predicate on the "company" field.
func CompanyEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCompany, v))
}

// CompanyNEQ applies the NEQ predicate on the "company" field.
func CompanyNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCompany, v))
}

// CompanyIn applies the In predicate on the "company" field.
func CompanyIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldCompany, vs...))
}

// CompanyNotIn applies the NotIn predicate on the "company" field.
func CompanyNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCompany, vs...))
}

// CompanyGT applies the GT predicate on the "company" field.
func CompanyGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldCompany, v))
}

// CompanyGTE applies the GTE predicate on the "company" field.
func CompanyGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCompany, v))
}

// CompanyLT applies the LT predicate on the "company" field.
func CompanyLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldCompany, v))
}

// CompanyLTE applies the LTE predicate on the "company" field.
func CompanyLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCompany, v))
}

// CompanyContains applies the Contains predicate on the "company" field.
func CompanyContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldCompany, v))
}

// CompanyHasPrefix applies the HasPrefix predicate on the "company" field.
func CompanyHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldCompany, v))
}

// CompanyHasSuffix applies the HasSuffix predicate on the "company" field.
func CompanyHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldCompany, v))
}

// CompanyIsNil applies the IsNil predicate on the "company" field.
func CompanyIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldCompany))
}

// CompanyNotNil applies the NotNil predicate on the "company" field.
func CompanyNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldCompany))
}

// CompanyEqualFold applies the EqualFold predicate on the "company" field.
func CompanyEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldCompany, v))
}

// CompanyContainsFold applies the ContainsFold predicate on the "company" field.
func CompanyContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldCompany, v))
}

// IndustryEQ applies the EQ predicate on the "industry" field.
func IndustryEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIndustry, v))
}

// IndustryNEQ applies the NEQ predicate on the "industry" field.
func IndustryNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldIndustry, v))
}

// IndustryIn applies the In predicate on the "industry" field.
func IndustryIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldIndustry, vs...))
}

// IndustryNotIn applies the NotIn predicate on the "industry" field.
func IndustryNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldIndustry, vs...))
}

// IndustryGT applies the GT predicate on the "industry" field.
func IndustryGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldIndustry, v))
}

// IndustryGTE applies the GTE predicate on the "industry" field.
func IndustryGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldIndustry, v))
}

// IndustryLT applies the LT predicate on the "industry" field.
func IndustryLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldIndustry, v))
}

// IndustryLTE applies the LTE predicate on the "industry" field.
func IndustryLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldIndustry, v))
}

// IndustryContains applies the Contains predicate on the "industry" field.
func IndustryContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldIndustry, v))
}

// IndustryHasPrefix applies the HasPrefix predicate on the "industry" field.
func IndustryHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldIndustry, v))
}

// IndustryHasSuffix applies the HasSuffix predicate on the "industry" field.
func IndustryHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldIndustry, v))
}

// IndustryIsNil applies the IsNil predicate on the "industry" field.
func IndustryIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldIndustry))
}

// IndustryNotNil applies the NotNil predicate on the "industry" field.
func IndustryNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldIndustry))
}

// IndustryEqualFold applies the EqualFold predicate on the "industry" field.
func IndustryEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldIndustry, v))
}

// IndustryContainsFold applies the ContainsFold predicate on the "industry" field.
func IndustryContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldIndustry, v))
}

// RoleEQ applies the EQ predicate on the "role" field.
func RoleEQ(v Role) predicate.User {
	return predicate.User(sql.FieldEQ(FieldRole, v))
}

// RoleNEQ applies the NEQ predicate on the "role" field.
func RoleNEQ(v Role) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldRole, v))
}

// RoleIn applies the In predicate on the "role" field.
func RoleIn(vs ...Role) predicate.User {
	return predicate.User(sql.FieldIn(FieldRole, vs...))
}

// RoleNotIn applies the NotIn predicate on the "role" field.
func RoleNotIn(vs ...Role) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldRole, vs...))
}

// PasswordHashEQ applies the EQ predicate on the "password_hash" field.
func PasswordHashEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPasswordHash, v))
}

// PasswordHashNEQ applies the NEQ predicate on the "password_hash" field.
func PasswordHashNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPasswordHash, v))
}

// PasswordHashIn applies the In predicate on the "password_hash" field.
func PasswordHashIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPasswordHash, vs...))
}

// PasswordHashNotIn applies the NotIn predicate on the "password_hash" field.
func PasswordHashNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPasswordHash, vs...))
}

// PasswordHashGT applies the GT predicate on the "password_hash" field.
func PasswordHashGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPasswordHash, v))
}

// PasswordHashGTE applies the GTE predicate on the "password_hash" field.
func PasswordHashGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPasswordHash, v))
}

// PasswordHashLT applies the LT predicate on the "password_hash" field.
func PasswordHashLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPasswordHash, v))
}

// PasswordHashLTE applies the LTE predicate on the "password_hash" field.
func PasswordHashLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPasswordHash, v))
}

// PasswordHashContains applies the Contains predicate on the "password_hash" field.
func PasswordHashContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPasswordHash, v))
}

// PasswordHashHasPrefix applies the HasPrefix predicate on the "password_hash" field.
func PasswordHashHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPasswordHash, v))
}

// PasswordHashHasSuffix applies the HasSuffix predicate on the "password_hash" field.
func PasswordHashHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPasswordHash, v))
}

// PasswordHashEqualFold applies the EqualFold predicate on the "password_hash" field.
func PasswordHashEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPasswordHash, v))
}

// PasswordHashContainsFold applies the ContainsFold predicate on the "password_hash" field.
func PasswordHashContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPasswordHash, v))
}

// IsVerifiedEQ applies the EQ predicate on the "is_verified" field.
func IsVerifiedEQ(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIsVerified, v))
}

// IsVerifiedNEQ applies the NEQ predicate on the "is_verified" field.
func IsVerifiedNEQ(v bool) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldIsVerified, v))
}

// TwoFactorEnabledEQ applies the EQ predicate on the "two_factor_enabled" field.
func TwoFactorEnabledEQ(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorEnabled, v))
}

// TwoFactorEnabledNEQ applies the NEQ predicate on the "two_factor_enabled" field.
func TwoFactorEnabledNEQ(v bool) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldTwoFactorEnabled, v))
}

// TwoFactorSecretEQ applies the EQ predicate on the "two_factor_secret" field.
func TwoFactorSecretEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorSecret, v))
}

// TwoFactorSecretNEQ applies the NEQ predicate on the "two_factor_secret" field.
func TwoFactorSecretNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldTwoFactorSecret, v))
}

// TwoFactorSecretIn applies the In predicate on the "two_factor_secret" field.
func TwoFactorSecretIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldTwoFactorSecret, vs...))
}

// TwoFactorSecretNotIn applies the NotIn predicate on the "two_factor_secret" field.
func TwoFactorSecretNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldTwoFactorSecret, vs...))
}

// TwoFactorSecretGT applies the GT predicate on the "two_factor_secret" field.
func TwoFactorSecretGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldTwoFactorSecret, v))
}

// TwoFactorSecretGTE applies the GTE predicate on the "two_factor_secret" field.
func TwoFactorSecretGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldTwoFactorSecret, v))
}

// TwoFactorSecretLT applies the LT predicate on the "two_factor_secret" field.
func TwoFactorSecretLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldTwoFactorSecret, v))
}

// TwoFactorSecretLTE applies the LTE predicate on the "two_factor_secret" field.
func TwoFactorSecretLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldTwoFactorSecret, v))
}

// TwoFactorSecretContains applies the Contains predicate on the "two_factor_secret" field.
func TwoFactorSecretContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldTwoFactorSecret, v))
}

// TwoFactorSecretHasPrefix applies the HasPrefix predicate on the "two_factor_secret" field.
func TwoFactorSecretHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldTwoFactorSecret, v))
}

// TwoFactorSecretHasSuffix applies the HasSuffix predicate on the "two_factor_secret" field.
func TwoFactorSecretHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldTwoFactorSecret, v))
}

// TwoFactorSecretIsNil applies the IsNil predicate on the "two_factor_secret" field.
func TwoFactorSecretIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldTwoFactorSecret))
}

// TwoFactorSecretNotNil applies the NotNil predicate on the "two_factor_secret" field.
func TwoFactorSecretNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldTwoFactorSecret))
}

// TwoFactorSecretEqualFold applies the EqualFold predicate on the "two_factor_secret" field.
func TwoFactorSecretEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldTwoFactorSecret, v))
}

// TwoFactorSecretContainsFold applies the ContainsFold predicate on the "two_factor_secret" field.
func TwoFactorSecretContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldTwoFactorSecret, v))
}

// TwoFactorBackupCodesIsNil applies the IsNil predicate on the "two_factor_backup_codes" field.
func TwoFactorBackupCodesIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldTwoFactorBackupCodes))
}

// TwoFactorBackupCodesNotNil applies the NotNil predicate on the "two_factor_backup_codes" field.
func TwoFactorBackupCodesNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldTwoFactorBackupCodes))
}

// TwoFactorEnabledAtEQ applies the EQ predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtNEQ applies the NEQ predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtIn applies the In predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldTwoFactorEnabledAt, vs...))
}

// TwoFactorEnabledAtNotIn applies the NotIn predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldTwoFactorEnabledAt, vs...))
}

// TwoFactorEnabledAtGT applies the GT predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtGTE applies the GTE predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtLT applies the LT predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtLTE applies the LTE predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldTwoFactorEnabledAt, v))
}

// TwoFactorEnabledAtIsNil applies the IsNil predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldTwoFactorEnabledAt))
}

// TwoFactorEnabledAtNotNil applies the NotNil predicate on the "two_factor_enabled_at" field.
func TwoFactorEnabledAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldTwoFactorEnabledAt))
}

// NotificationSettingsIsNil applies the IsNil predicate on the "notification_settings" field.
func NotificationSettingsIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldNotificationSettings))
}

// NotificationSettingsNotNil applies the NotNil predicate on the "notification_settings" field.
func NotificationSettingsNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldNotificationSettings))
}

// InviteCodeEQ applies the EQ predicate on the "invite_code" field.
func InviteCodeEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldInviteCode, v))
}

// InviteCodeNEQ applies the NEQ predicate on the "invite_code" field.
func InviteCodeNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldInviteCode, v))
}

// InviteCodeIn applies the In predicate on the "invite_code" field.
func InviteCodeIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldInviteCode, vs...))
}

// InviteCodeNotIn applies the NotIn predicate on the "invite_code" field.
func InviteCodeNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldInviteCode, vs...))
}

// InviteCodeGT applies the GT predicate on the "invite_code" field.
func InviteCodeGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldInviteCode, v))
}

// InviteCodeGTE applies the GTE predicate on the "invite_code" field.
func InviteCodeGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldInviteCode, v))
}

// InviteCodeLT applies the LT predicate on the "invite_code" field.
func InviteCodeLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldInviteCode, v))
}

// InviteCodeLTE applies the LTE predicate on the "invite_code" field.
func InviteCodeLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldInviteCode, v))
}

// InviteCodeContains applies the Contains predicate on the "invite_code" field.
func InviteCodeContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldInviteCode, v))
}

// InviteCodeHasPrefix applies the HasPrefix predicate on the "invite_code" field.
func InviteCodeHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldInviteCode, v))
}

// InviteCodeHasSuffix applies the HasSuffix predicate on the "invite_code" field.
func InviteCodeHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldInviteCode, v))
}

// InviteCodeIsNil applies the IsNil predicate on the "invite_code" field.
func InviteCodeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldInviteCode))
}

// InviteCodeNotNil applies the NotNil predicate on the "invite_code" field.
func InviteCodeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldInviteCode))
}

// InviteCodeEqualFold applies the EqualFold predicate on the "invite_code" field.
func InviteCodeEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldInviteCode, v))
}

// InviteCodeContainsFold applies the ContainsFold predicate on the "invite_code" field.
func InviteCodeContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldInviteCode, v))
}

// AffCodeEQ applies the EQ predicate on the "aff_code" field.
func AffCodeEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAffCode, v))
}

// AffCodeNEQ applies the NEQ predicate on the "aff_code" field.
func AffCodeNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAffCode, v))
}

// AffCodeIn applies the In predicate on the "aff_code" field.
func AffCodeIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldAffCode, vs...))
}

// AffCodeNotIn applies the NotIn predicate on the "aff_code" field.
func AffCodeNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAffCode, vs...))
}

// AffCodeGT applies the GT predicate on the "aff_code" field.
func AffCodeGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldAffCode, v))
}

// AffCodeGTE applies the GTE predicate on the "aff_code" field.
func AffCodeGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAffCode, v))
}

// AffCodeLT applies the LT predicate on the "aff_code" field.
func AffCodeLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldAffCode, v))
}

// AffCodeLTE applies the LTE predicate on the "aff_code" field.
func AffCodeLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAffCode, v))
}

// AffCodeContains applies the Contains predicate on the "aff_code" field.
func AffCodeContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldAffCode, v))
}

// AffCodeHasPrefix applies the HasPrefix predicate on the "aff_code" field.
func AffCodeHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldAffCode, v))
}

// AffCodeHasSuffix applies the HasSuffix predicate on the "aff_code" field.
func AffCodeHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldAffCode, v))
}

// AffCodeIsNil applies the IsNil predicate on the "aff_code" field.
func AffCodeIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAffCode))
}

// AffCodeNotNil applies the NotNil predicate on the "aff_code" field.
func AffCodeNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAffCode))
}

// AffCodeEqualFold applies the EqualFold predicate on the "aff_code" field.
func AffCodeEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldAffCode, v))
}

// AffCodeContainsFold applies the ContainsFold predicate on the "aff_code" field.
func AffCodeContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldAffCode, v))
}

// AffJoinedAtEQ applies the EQ predicate on the "aff_joined_at" field.
func AffJoinedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAffJoinedAt, v))
}

// AffJoinedAtNEQ applies the NEQ predicate on the "aff_joined_at" field.
func AffJoinedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAffJoinedAt, v))
}

// AffJoinedAtIn applies the In predicate on the "aff_joined_at" field.
func AffJoinedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldAffJoinedAt, vs...))
}

// AffJoinedAtNotIn applies the NotIn predicate on the "aff_joined_at" field.
func AffJoinedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAffJoinedAt, vs...))
}

// AffJoinedAtGT applies the GT predicate on the "aff_joined_at" field.
func AffJoinedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldAffJoinedAt, v))
}

// AffJoinedAtGTE applies the GTE predicate on the "aff_joined_at" field.
func AffJoinedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAffJoinedAt, v))
}

// AffJoinedAtLT applies the LT predicate on the "aff_joined_at" field.
func AffJoinedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldAffJoinedAt, v))
}

// AffJoinedAtLTE applies the LTE predicate on the "aff_joined_at" field.
func AffJoinedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAffJoinedAt, v))
}

// AffJoinedAtIsNil applies the IsNil predicate on the "aff_joined_at" field.
func AffJoinedAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAffJoinedAt))
}

// AffJoinedAtNotNil applies the NotNil predicate on the "aff_joined_at" field.
func AffJoinedAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAffJoinedAt))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDeletedAt))
}

// HasSessions applies the HasEdge predicate on the "sessions" edge.
func HasSessions() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, SessionsTable, SessionsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSessionsWith applies the HasEdge predicate on the "sessions" edge with a given conditions (other predicates).
func HasSessionsWith(preds ...predicate.Session) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newSessionsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.User) predicate.User {
	return predicate.User(sql.NotPredicates(p))
}

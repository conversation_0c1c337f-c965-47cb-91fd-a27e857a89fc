package events

import (
	"time"

	"github.com/social-content-ai/pkg-shared/kafka"
)

// TODO: Fix type redeclaration errors
// Re-export common types from pkg-shared when available
// type (
// 	EventType = kafka.EventType
// 	Priority  = kafka.Priority
// 	BaseEvent = kafka.BaseEvent
// 	EventMetadata = kafka.EventMetadata
// 	UserEvent = kafka.UserEvent
// 	ContentEvent = kafka.ContentEvent
// 	BillingEvent = kafka.BillingEvent
// 	IntegrationEvent = kafka.IntegrationEvent
// 	AssetEvent = kafka.AssetEvent
// 	RAGEvent = kafka.RAGEvent
// 	SystemEvent = kafka.SystemEvent
// )

// Re-export constants
const (
	// Event Types
	EventTypeUserRegistered     = kafka.EventTypeUserRegistered
	EventTypeUserUpdated        = kafka.EventTypeUserUpdated
	EventTypeUserDeleted        = kafka.EventTypeUserDeleted
	EventTypeUserWelcome        = kafka.EventTypeUserWelcome
	EventTypeUserUpgraded       = kafka.EventTypeUserUpgraded
	EventTypeUserProfileUpdated = kafka.EventTypeUserProfileUpdated
	EventTypeUserPasswordReset  = kafka.EventTypeUserPasswordReset
	EventTypeUserEmailVerified  = kafka.EventTypeUserEmailVerified
	EventTypeUserSecurityAlert  = kafka.EventTypeUserSecurityAlert

	EventTypeContentGenerated = kafka.EventTypeContentGenerated
	EventTypeContentImproved  = kafka.EventTypeContentImproved
	EventTypePostPublished    = kafka.EventTypePostPublished
	EventTypePostScheduled    = kafka.EventTypePostScheduled
	EventTypePostFailed       = kafka.EventTypePostFailed
	EventTypeTemplateCreated  = kafka.EventTypeTemplateCreated
	EventTypeTemplateUpdated  = kafka.EventTypeTemplateUpdated
	EventTypeTemplateDeleted  = kafka.EventTypeTemplateDeleted

	EventTypeCreditLow           = kafka.EventTypeCreditLow
	EventTypeCreditPurchased     = kafka.EventTypeCreditPurchased
	EventTypeCreditConsumed      = kafka.EventTypeCreditConsumed
	EventTypeSubscriptionRenewed = kafka.EventTypeSubscriptionRenewed
	EventTypeSubscriptionExpired = kafka.EventTypeSubscriptionExpired
	EventTypePaymentFailed       = kafka.EventTypePaymentFailed
	EventTypePaymentSucceeded    = kafka.EventTypePaymentSucceeded

	EventTypeIntegrationConnected    = kafka.EventTypeIntegrationConnected
	EventTypeIntegrationFailed       = kafka.EventTypeIntegrationFailed
	EventTypeIntegrationDisconnected = kafka.EventTypeIntegrationDisconnected

	EventTypeAnalyticsReport  = kafka.EventTypeAnalyticsReport
	EventTypeAnalyticsInsight = kafka.EventTypeAnalyticsInsight

	EventTypeSystemMaintenance = kafka.EventTypeSystemMaintenance
	EventTypeSystemUpdate      = kafka.EventTypeSystemUpdate
	EventTypeSystemAlert       = kafka.EventTypeSystemAlert

	EventTypeAssetUploaded  = kafka.EventTypeAssetUploaded
	EventTypeAssetDeleted   = kafka.EventTypeAssetDeleted
	EventTypeAssetValidated = kafka.EventTypeAssetValidated

	EventTypeRAGProcessingStarted   = kafka.EventTypeRAGProcessingStarted
	EventTypeRAGProcessingCompleted = kafka.EventTypeRAGProcessingCompleted
	EventTypeRAGProcessingFailed    = kafka.EventTypeRAGProcessingFailed
	EventTypeRAGRetrainingStarted   = kafka.EventTypeRAGRetrainingStarted
	EventTypeRAGRetrainingCompleted = kafka.EventTypeRAGRetrainingCompleted

	EventTypeNotificationSent      = kafka.EventTypeNotificationSent
	EventTypeNotificationDelivered = kafka.EventTypeNotificationDelivered
	EventTypeNotificationFailed    = kafka.EventTypeNotificationFailed

	// TODO: Fix priority redeclaration
	// Priorities
	// PriorityLow      = kafka.PriorityLow
	// PriorityNormal   = kafka.PriorityNormal
	// PriorityHigh     = kafka.PriorityHigh
	// PriorityCritical = kafka.PriorityCritical
)

// Re-export constructor functions
var (
	NewBaseEvent        = kafka.NewBaseEvent
	NewUserEvent        = kafka.NewUserEvent
	NewContentEvent     = kafka.NewContentEvent
	NewBillingEvent     = kafka.NewBillingEvent
	NewIntegrationEvent = kafka.NewIntegrationEvent
	NewAssetEvent       = kafka.NewAssetEvent
	NewRAGEvent         = kafka.NewRAGEvent
	NewSystemEvent      = kafka.NewSystemEvent
)

// NotificationPriority represents the priority level of a notification
type NotificationPriority string

const (
	PriorityLow      NotificationPriority = "low"
	PriorityNormal   NotificationPriority = "normal"
	PriorityHigh     NotificationPriority = "high"
	PriorityCritical NotificationPriority = "critical"
)

// NotificationChannel represents the delivery channel for notifications
type NotificationChannel string

const (
	ChannelEmail     NotificationChannel = "email"
	ChannelSMS       NotificationChannel = "sms"
	ChannelPush      NotificationChannel = "push"
	ChannelWebSocket NotificationChannel = "websocket"
	ChannelInApp     NotificationChannel = "in_app"
)

// NotificationEventMessage represents the standardized message format for notification events
type NotificationEventMessage struct {
	MessageID   string                 `json:"message_id" validate:"required,uuid"`
	EventType   string                 `json:"event_type" validate:"required"`
	Source      string                 `json:"source" validate:"required"`
	Version     string                 `json:"version" validate:"required"`
	UserID      string                 `json:"user_id,omitempty"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Priority    NotificationPriority   `json:"priority" validate:"required,oneof=low normal high critical"`
	Channels    []NotificationChannel  `json:"channels" validate:"required,min=1"`
	EventData   map[string]interface{} `json:"event_data" validate:"required"`
	Timestamp   time.Time              `json:"timestamp" validate:"required"`
}

// UserEventMessage represents user events for notification processing
type UserEventMessage struct {
	MessageID string                 `json:"message_id" validate:"required,uuid"`
	EventType string                 `json:"event_type" validate:"required"`
	UserID    string                 `json:"user_id" validate:"required"`
	EventData map[string]interface{} `json:"event_data" validate:"required"`
	Timestamp time.Time              `json:"timestamp" validate:"required"`
}

// ContentEventMessage represents content events for notification processing
type ContentEventMessage struct {
	MessageID   string                 `json:"message_id" validate:"required,uuid"`
	EventType   string                 `json:"event_type" validate:"required"`
	UserID      string                 `json:"user_id" validate:"required"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	ContentID   string                 `json:"content_id,omitempty"`
	EventData   map[string]interface{} `json:"event_data" validate:"required"`
	Timestamp   time.Time              `json:"timestamp" validate:"required"`
}

// BillingEventMessage represents billing events for notification processing
type BillingEventMessage struct {
	MessageID      string                 `json:"message_id" validate:"required,uuid"`
	EventType      string                 `json:"event_type" validate:"required"`
	UserID         string                 `json:"user_id" validate:"required"`
	SubscriptionID string                 `json:"subscription_id,omitempty"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	EventData      map[string]interface{} `json:"event_data" validate:"required"`
	Timestamp      time.Time              `json:"timestamp" validate:"required"`
}

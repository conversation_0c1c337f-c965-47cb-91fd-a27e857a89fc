package push

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/pkg-shared/logging"
)

// FCMConfig holds Firebase Cloud Messaging configuration
type FCMConfig struct {
	ServerKey string
	BaseURL   string
}

// FCMSender implements PushSender using Firebase Cloud Messaging
type FCMSender struct {
	config FCMConfig
	client *http.Client
	logger logging.Logger
}

// FCMMessage represents the FCM message structure
type FCMMessage struct {
	To           string                 `json:"to,omitempty"`
	RegistrationIDs []string           `json:"registration_ids,omitempty"`
	Notification FCMNotification        `json:"notification"`
	Data         map[string]string      `json:"data,omitempty"`
	Priority     string                 `json:"priority,omitempty"`
}

// FCMNotification represents the notification payload
type FCMNotification struct {
	Title string `json:"title"`
	Body  string `json:"body"`
}

// FCMResponse represents the FCM API response
type FCMResponse struct {
	MulticastID  int64       `json:"multicast_id"`
	Success      int         `json:"success"`
	Failure      int         `json:"failure"`
	CanonicalIDs int         `json:"canonical_ids"`
	Results      []FCMResult `json:"results"`
}

// FCMResult represents individual result in FCM response
type FCMResult struct {
	MessageID      string `json:"message_id,omitempty"`
	RegistrationID string `json:"registration_id,omitempty"`
	Error          string `json:"error,omitempty"`
}

// NewFCMSender creates a new FCM push sender
func NewFCMSender(config FCMConfig, logger logging.Logger) channels.PushSender {
	if config.BaseURL == "" {
		config.BaseURL = "https://fcm.googleapis.com/fcm/send"
	}

	return &FCMSender{
		config: config,
		client: &http.Client{},
		logger: logger,
	}
}

// SendPush sends a push notification using FCM
func (f *FCMSender) SendPush(ctx context.Context, message *channels.PushMessage) error {
	f.logger.WithFields(map[string]interface{}{
		"device_tokens": len(message.DeviceTokens),
		"title":         message.Title,
		"body":          message.Body,
	}).Info("Sending push notification via FCM")

	// Validate message
	if len(message.DeviceTokens) == 0 {
		return fmt.Errorf("at least one device token is required")
	}
	if message.Title == "" && message.Body == "" {
		return fmt.Errorf("title or body is required")
	}

	// Prepare FCM message
	fcmMsg := FCMMessage{
		Notification: FCMNotification{
			Title: message.Title,
			Body:  message.Body,
		},
		Data:     message.Data,
		Priority: "high",
	}

	// Set recipients
	if len(message.DeviceTokens) == 1 {
		fcmMsg.To = message.DeviceTokens[0]
	} else {
		fcmMsg.RegistrationIDs = message.DeviceTokens
	}

	// Marshal to JSON
	payload, err := json.Marshal(fcmMsg)
	if err != nil {
		f.logger.WithError(err).Error("Failed to marshal FCM message")
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", f.config.BaseURL, bytes.NewBuffer(payload))
	if err != nil {
		f.logger.WithError(err).Error("Failed to create FCM request")
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "key="+f.config.ServerKey)

	// Send request
	resp, err := f.client.Do(req)
	if err != nil {
		f.logger.WithError(err).Error("Failed to send push notification via FCM")
		return fmt.Errorf("failed to send push notification: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var fcmResp FCMResponse
	if err := json.NewDecoder(resp.Body).Decode(&fcmResp); err != nil {
		f.logger.WithError(err).Error("Failed to decode FCM response")
		return fmt.Errorf("failed to decode response: %w", err)
	}

	// Check for errors
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		f.logger.WithFields(map[string]interface{}{
			"status_code": resp.StatusCode,
			"response":    fcmResp,
		}).Error("FCM API returned error")
		return fmt.Errorf("FCM API error: status code %d", resp.StatusCode)
	}

	// Log results
	f.logger.WithFields(map[string]interface{}{
		"success": fcmResp.Success,
		"failure": fcmResp.Failure,
	}).Info("Push notification sent via FCM")

	// Check for partial failures
	if fcmResp.Failure > 0 {
		f.logger.WithField("failures", fcmResp.Failure).Warn("Some push notifications failed to send")
		for i, result := range fcmResp.Results {
			if result.Error != "" {
				f.logger.WithFields(map[string]interface{}{
					"token": message.DeviceTokens[i],
					"error": result.Error,
				}).Error("Failed to send to device token")
			}
		}
	}

	return nil
}

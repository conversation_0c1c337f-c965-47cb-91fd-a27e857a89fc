"""Configuration settings for RAG Processing Service"""

import os
from functools import lru_cache
from typing import List

from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database settings
    database_url: str = Field(env="DATABASE_URL")
    
    # Qdrant (Vector Database) settings
    qdrant_url: str = Field(env="QDRANT_URL")
    qdrant_api_key: str = Field(default="", env="QDRANT_API_KEY")
    qdrant_collection_name: str = Field(default="documents", env="QDRANT_COLLECTION_NAME")
    vector_dimension: int = Field(default=384, env="VECTOR_DIMENSION")
    
    # Neo4j (Graph Database) settings
    neo4j_uri: str = Field(env="NEO4J_URI")
    neo4j_user: str = Field(env="NEO4J_USER")
    neo4j_password: str = Field(env="NEO4J_PASSWORD")
    neo4j_database: str = Field(default="neo4j", env="NEO4J_DATABASE")
    
    # Kafka settings
    kafka_bootstrap_servers: str = Field(env="KAFKA_BOOTSTRAP_SERVERS")
    kafka_group_id: str = Field(default="rag-processing", env="KAFKA_GROUP_ID")
    kafka_auto_offset_reset: str = Field(default="earliest", env="KAFKA_AUTO_OFFSET_RESET")
    
    # AI Provider settings
    openai_api_key: str = Field(env="OPENAI_API_KEY")
    anthropic_api_key: str = Field(default="", env="ANTHROPIC_API_KEY")
    google_api_key: str = Field(default="", env="GOOGLE_API_KEY")
    
    # Embedding model settings
    embedding_model: str = Field(default="all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    embedding_batch_size: int = Field(default=32, env="EMBEDDING_BATCH_SIZE")
    
    # Document processing settings
    max_file_size: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # RAG settings
    max_context_length: int = Field(default=4000, env="MAX_CONTEXT_LENGTH")
    similarity_threshold: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    max_retrieved_docs: int = Field(default=10, env="MAX_RETRIEVED_DOCS")
    
    # Processing settings
    max_concurrent_jobs: int = Field(default=5, env="MAX_CONCURRENT_JOBS")
    job_timeout: int = Field(default=300, env="JOB_TIMEOUT")  # 5 minutes
    
    # Storage settings
    temp_dir: str = Field(default="/tmp/rag-processing", env="TEMP_DIR")
    
    # Security settings
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

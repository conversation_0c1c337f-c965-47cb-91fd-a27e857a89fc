package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB   *ent.Client
	writeDB  *ent.Client
	creditUC credit.UseCase
	logger   logging.Logger
}

// NewService creates a new transaction service
func NewService(readDB, writeDB *ent.Client, creditUC credit.UseCase, logger logging.Logger) UseCase {
	return &service{
		readDB:   readDB,
		writeDB:  writeDB,
		creditUC: creditUC,
		logger:   logger,
	}
}

// ListTransactions lists transactions with pagination and filters
func (s *service) ListTransactions(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing transactions")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query
	query := s.readDB.CreditTransaction.Query().
		Where(credittransaction.UserID(userUUID))

	// Apply filters
	if req.Type != "" {
		query = query.Where(credittransaction.TypeEQ(credittransaction.Type(req.Type)))
	}
	if req.ReferenceType != "" {
		query = query.Where(credittransaction.ReferenceType(req.ReferenceType))
	}
	if req.DateFrom != nil {
		query = query.Where(credittransaction.CreatedAtGTE(*req.DateFrom))
	}
	if req.DateTo != nil {
		query = query.Where(credittransaction.CreatedAtLTE(*req.DateTo))
	}

	// Count total records
	total, err := query.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count transactions: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortOrder == "asc" {
		query = query.Order(ent.Asc(credittransaction.FieldCreatedAt))
	} else {
		query = query.Order(ent.Desc(credittransaction.FieldCreatedAt))
	}

	// Execute query
	transactions, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// Convert to response format
	var transactionResponses []models.CreditTransactionResponse
	for _, tx := range transactions {
		var processedAt *time.Time
		if !tx.ProcessedAt.IsZero() {
			processedAt = &tx.ProcessedAt
		}

		transactionResponses = append(transactionResponses, models.CreditTransactionResponse{
			BaseModel: models.BaseModel{
				ID:        tx.ID.String(),
				CreatedAt: tx.CreatedAt,
				UpdatedAt: tx.UpdatedAt,
			},
			UserID:        tx.UserID.String(),
			Type:          string(tx.Type),
			Amount:        tx.Amount,
			Description:   tx.Description,
			ReferenceID:   tx.ReferenceID,
			ReferenceType: tx.ReferenceType,
			Status:        string(tx.Status),
			ProcessedAt:   processedAt,
		})
	}

	// Calculate pagination meta
	totalPages := (total + req.Limit - 1) / req.Limit
	pagination := models.PaginationMeta{
		Page:       req.Page,
		Limit:      req.Limit,
		Total:      total,
		TotalPages: totalPages,
	}

	// Calculate summary
	var totalCreditsEarned, totalCreditsUsed int
	typeCount := make(map[string]int)
	categoryCount := make(map[string]int)

	for _, tx := range transactionResponses {
		typeCount[tx.Type]++

		// Categorize by description
		if tx.Description != "" {
			categoryCount[tx.Description]++
		} else {
			categoryCount["other"]++
		}

		// Calculate credits earned vs used
		if tx.Type == "purchase" || tx.Type == "bonus" || tx.Type == "refund" {
			totalCreditsEarned += tx.Amount
		} else {
			totalCreditsUsed += tx.Amount
		}
	}

	summary := models.CreditHistorySummary{
		TotalTransactions:  total,
		TotalCreditsEarned: totalCreditsEarned,
		TotalCreditsUsed:   totalCreditsUsed,
		NetChange:          totalCreditsEarned - totalCreditsUsed,
		ByType:             typeCount,
		ByCategory:         categoryCount,
	}

	return &models.CreditHistoryResponse{
		Transactions: transactionResponses,
		Pagination:   pagination,
		Summary:      summary,
	}, nil
}

// GetTransaction gets a transaction by ID
func (s *service) GetTransaction(ctx context.Context, transactionID, userID string) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"user_id":        userID,
	}).Info("Getting transaction")

	// Parse IDs
	transactionUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get transaction
	tx, err := s.readDB.CreditTransaction.Query().
		Where(
			credittransaction.ID(transactionUUID),
			credittransaction.UserID(userUUID),
		).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Convert to response format
	var processedAt *time.Time
	if !tx.ProcessedAt.IsZero() {
		processedAt = &tx.ProcessedAt
	}

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        string(tx.Status),
		ProcessedAt:   processedAt,
	}, nil
}

// ExportTransactions exports transactions to file
func (s *service) ExportTransactions(ctx context.Context, req *models.ExportTransactionsRequest) (*models.ExportTransactionsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"format":  req.Format,
		"type":    req.Type,
	}).Info("Exporting transactions")

	// Get transactions for export
	historyReq := &models.CreditHistoryRequest{
		UserID:    req.UserID,
		Type:      req.Type,
		DateFrom:  req.DateFrom,
		DateTo:    req.DateTo,
		Limit:     10000, // Large limit for export
		Page:      1,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	history, err := s.ListTransactions(ctx, historyReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions for export: %w", err)
	}

	// Generate export file (simplified implementation)
	filename := fmt.Sprintf("transactions_%s_%s.%s", req.UserID, time.Now().Format("20060102"), req.Format)

	// In a real implementation, you would:
	// 1. Generate CSV/Excel file with transaction data
	// 2. Upload to S3 or file storage
	// 3. Return download URL

	return &models.ExportTransactionsResponse{
		FileURL:   fmt.Sprintf("/api/v1/transactions/exports/%s/download", filename),
		FileName:  filename,
		FileSize:  int64(len(history.Transactions) * 100), // Estimated file size
		ExpiresAt: time.Now().Add(24 * time.Hour),         // 24 hour expiry
		Format:    req.Format,
	}, nil
}

// GetTransactionSummary gets transaction summary
func (s *service) GetTransactionSummary(ctx context.Context, userID, period string) (*models.CreditHistorySummary, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"period":  period,
	}).Info("Getting transaction summary")

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate date range based on period
	now := time.Now()
	var startDate time.Time
	switch period {
	case "day":
		startDate = now.AddDate(0, 0, -1)
	case "week":
		startDate = now.AddDate(0, 0, -7)
	case "month":
		startDate = now.AddDate(0, -1, 0)
	case "year":
		startDate = now.AddDate(-1, 0, 0)
	default:
		startDate = now.AddDate(0, -1, 0) // Default to month
	}

	// Get transactions in period
	transactions, err := s.readDB.CreditTransaction.Query().
		Where(credittransaction.UserID(userUUID)).
		Where(credittransaction.CreatedAtGTE(startDate)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// Calculate summary
	var totalCreditsEarned, totalCreditsUsed, totalTransactions int
	typeCount := make(map[string]int)
	categoryCount := make(map[string]int)

	for _, tx := range transactions {
		totalTransactions++
		typeCount[string(tx.Type)]++

		// Categorize by purpose/description
		if tx.Description != "" {
			categoryCount[tx.Description]++
		} else {
			categoryCount["other"]++
		}

		// Calculate credits earned vs used
		if tx.Type == credittransaction.TypePurchase || tx.Type == credittransaction.TypeBonus || tx.Type == credittransaction.TypeRefund {
			totalCreditsEarned += tx.Amount
		} else {
			totalCreditsUsed += tx.Amount
		}
	}

	return &models.CreditHistorySummary{
		TotalTransactions:  totalTransactions,
		TotalCreditsEarned: totalCreditsEarned,
		TotalCreditsUsed:   totalCreditsUsed,
		NetChange:          totalCreditsEarned - totalCreditsUsed,
		ByType:             typeCount,
		ByCategory:         categoryCount,
	}, nil
}

// CreateTransaction creates a new transaction
func (s *service) CreateTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Creating transaction")

	return s.createCreditTransaction(ctx, req, "usage")
}

// UpdateTransactionStatus updates transaction status
func (s *service) UpdateTransactionStatus(ctx context.Context, transactionID, status string) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"status":         status,
	}).Info("Updating transaction status")

	// Parse transaction ID
	txUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	// Get transaction first to get user ID
	tx, err := s.readDB.CreditTransaction.Get(ctx, txUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Update transaction status
	err = s.updateTransactionStatusInternal(ctx, transactionID, status)
	if err != nil {
		return nil, fmt.Errorf("failed to update transaction status: %w", err)
	}

	// Return updated transaction
	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: time.Now(),
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        status,
		ProcessedAt:   &[]time.Time{time.Now()}[0],
	}, nil
}

// CancelTransaction cancels a transaction
func (s *service) CancelTransaction(ctx context.Context, transactionID, userID string) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"user_id":        userID,
	}).Info("Cancelling transaction")

	// Parse IDs
	txUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get transaction and verify ownership
	tx, err := s.readDB.CreditTransaction.Query().
		Where(credittransaction.ID(txUUID)).
		Where(credittransaction.UserID(userUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Check if transaction can be cancelled
	if tx.Status != credittransaction.StatusPending {
		return nil, fmt.Errorf("transaction cannot be cancelled (status: %s)", tx.Status)
	}

	// Update transaction status to cancelled
	err = s.updateTransactionStatusInternal(ctx, transactionID, "cancelled")
	if err != nil {
		return nil, fmt.Errorf("failed to cancel transaction: %w", err)
	}

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: time.Now(),
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        "cancelled",
		ProcessedAt:   &[]time.Time{time.Now()}[0],
	}, nil
}

// AdminListTransactions lists transactions (admin)
func (s *service) AdminListTransactions(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"type":    req.Type,
		"page":    req.Page,
		"limit":   req.Limit,
	}).Info("Admin listing transactions")

	// Admin can list all transactions, not just for specific user
	// If UserID is provided, filter by user, otherwise list all
	query := s.readDB.CreditTransaction.Query()

	if req.UserID != "" {
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID format: %w", err)
		}
		query = query.Where(credittransaction.UserID(userUUID))
	}

	// Apply filters
	if req.Type != "" {
		query = query.Where(credittransaction.TypeEQ(credittransaction.Type(req.Type)))
	}
	if req.ReferenceType != "" {
		query = query.Where(credittransaction.ReferenceType(req.ReferenceType))
	}
	if req.DateFrom != nil {
		query = query.Where(credittransaction.CreatedAtGTE(*req.DateFrom))
	}
	if req.DateTo != nil {
		query = query.Where(credittransaction.CreatedAtLTE(*req.DateTo))
	}

	// Count total records
	total, err := query.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count transactions: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortOrder == "asc" {
		query = query.Order(ent.Asc(credittransaction.FieldCreatedAt))
	} else {
		query = query.Order(ent.Desc(credittransaction.FieldCreatedAt))
	}

	// Execute query
	transactions, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// Convert to response format
	var transactionResponses []models.CreditTransactionResponse
	for _, tx := range transactions {
		var processedAt *time.Time
		if !tx.ProcessedAt.IsZero() {
			processedAt = &tx.ProcessedAt
		}

		transactionResponses = append(transactionResponses, models.CreditTransactionResponse{
			BaseModel: models.BaseModel{
				ID:        tx.ID.String(),
				CreatedAt: tx.CreatedAt,
				UpdatedAt: tx.UpdatedAt,
			},
			UserID:        tx.UserID.String(),
			Type:          string(tx.Type),
			Amount:        tx.Amount,
			Description:   tx.Description,
			ReferenceID:   tx.ReferenceID,
			ReferenceType: tx.ReferenceType,
			Status:        string(tx.Status),
			ProcessedAt:   processedAt,
		})
	}

	// Calculate pagination meta
	totalPages := (total + req.Limit - 1) / req.Limit
	pagination := models.PaginationMeta{
		Page:       req.Page,
		Limit:      req.Limit,
		Total:      total,
		TotalPages: totalPages,
	}

	// Calculate summary
	summary := models.CreditHistorySummary{
		TotalTransactions: total,
		// TODO: Calculate other summary fields for admin
	}

	return &models.CreditHistoryResponse{
		Transactions: transactionResponses,
		Pagination:   pagination,
		Summary:      summary,
	}, nil
}

// AdminGetTransaction gets transaction (admin)
func (s *service) AdminGetTransaction(ctx context.Context, transactionID string) (*models.CreditTransactionResponse, error) {
	s.logger.WithField("transaction_id", transactionID).Info("Admin getting transaction")

	// Parse transaction ID
	transactionUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	// Get transaction (admin can access any transaction)
	tx, err := s.readDB.CreditTransaction.Get(ctx, transactionUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Convert to response format
	var processedAt *time.Time
	if !tx.ProcessedAt.IsZero() {
		processedAt = &tx.ProcessedAt
	}

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        string(tx.Status),
		ProcessedAt:   processedAt,
	}, nil
}

// AdminApproveTransaction approves a transaction (admin)
func (s *service) AdminApproveTransaction(ctx context.Context, transactionID string) (*models.CreditTransactionResponse, error) {
	s.logger.WithField("transaction_id", transactionID).Info("Admin approving transaction")

	// Parse transaction ID
	txUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	// Get transaction
	tx, err := s.readDB.CreditTransaction.Get(ctx, txUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Check if transaction can be approved
	if tx.Status != credittransaction.StatusPending {
		return nil, fmt.Errorf("transaction cannot be approved (status: %s)", tx.Status)
	}

	// Update transaction status to completed
	err = s.updateTransactionStatusInternal(ctx, transactionID, "completed")
	if err != nil {
		return nil, fmt.Errorf("failed to approve transaction: %w", err)
	}

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: time.Now(),
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        "completed",
		ProcessedAt:   &[]time.Time{time.Now()}[0],
	}, nil
}

// AdminRejectTransaction rejects a transaction (admin)
func (s *service) AdminRejectTransaction(ctx context.Context, transactionID, reason string) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"transaction_id": transactionID,
		"reason":         reason,
	}).Info("Admin rejecting transaction")

	// Parse transaction ID
	txUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return nil, fmt.Errorf("invalid transaction ID format: %w", err)
	}

	// Get transaction
	tx, err := s.readDB.CreditTransaction.Get(ctx, txUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Check if transaction can be rejected
	if tx.Status != credittransaction.StatusPending {
		return nil, fmt.Errorf("transaction cannot be rejected (status: %s)", tx.Status)
	}

	// Update transaction status to failed with reason
	err = s.updateTransactionStatusInternal(ctx, transactionID, "failed")
	if err != nil {
		return nil, fmt.Errorf("failed to reject transaction: %w", err)
	}

	// TODO: Store rejection reason in metadata or separate field

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: time.Now(),
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   fmt.Sprintf("%s (Rejected: %s)", tx.Description, reason),
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        "failed",
		ProcessedAt:   &[]time.Time{time.Now()}[0],
	}, nil
}

// InternalGetUserTransactions gets user transactions (internal)
func (s *service) InternalGetUserTransactions(ctx context.Context, userID string, limit int) (*models.CreditHistoryResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"limit":   limit,
	}).Info("Getting user transactions (internal)")

	// Use ListTransactions with internal request
	req := &models.CreditHistoryRequest{
		UserID:    userID,
		Limit:     limit,
		Page:      1,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	return s.ListTransactions(ctx, req)
}

// InternalCreateTransaction creates transaction (internal)
func (s *service) InternalCreateTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Creating transaction (internal)")

	// Use CreateTransaction method
	return s.CreateTransaction(ctx, req)
}

// GetTransactionAnalytics gets transaction analytics
func (s *service) GetTransactionAnalytics(ctx context.Context, userID, period string) (*models.CreditAnalyticsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"period":  period,
	}).Info("Getting transaction analytics")

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate date range based on period
	now := time.Now()
	var startDate time.Time
	switch period {
	case "day":
		startDate = now.AddDate(0, 0, -1)
	case "week":
		startDate = now.AddDate(0, 0, -7)
	case "month":
		startDate = now.AddDate(0, -1, 0)
	case "year":
		startDate = now.AddDate(-1, 0, 0)
	default:
		startDate = now.AddDate(0, -1, 0) // Default to month
	}

	// Get transactions in period
	transactions, err := s.readDB.CreditTransaction.Query().
		Where(credittransaction.UserID(userUUID)).
		Where(credittransaction.CreatedAtGTE(startDate)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions: %w", err)
	}

	// Calculate analytics
	var totalCreditsIssued, totalCreditsUsed int64
	usageByCategory := make(map[string]int64)
	usageByModel := make(map[string]int64)

	for _, tx := range transactions {
		if tx.Type == credittransaction.TypePurchase || tx.Type == credittransaction.TypeBonus || tx.Type == credittransaction.TypeRefund {
			totalCreditsIssued += int64(tx.Amount)
		} else {
			totalCreditsUsed += int64(tx.Amount)
		}

		// Categorize by description
		category := tx.Description
		if category == "" {
			category = "other"
		}
		usageByCategory[category] += int64(tx.Amount)

		// Model usage (simplified - could be extracted from metadata)
		usageByModel["default"] += int64(tx.Amount)
	}

	return &models.CreditAnalyticsResponse{
		Period:                period,
		TotalUsers:            1, // Single user analytics
		ActiveUsers:           1,
		TotalCreditsIssued:    totalCreditsIssued,
		TotalCreditsUsed:      totalCreditsUsed,
		AverageCreditsPerUser: float64(totalCreditsUsed),
		Timeline:              []models.CreditAnalyticsPoint{}, // TODO: Implement timeline
		UsageByCategory:       usageByCategory,
		UsageByModel:          usageByModel,
		TopUsers:              []models.UserCreditUsage{},  // Single user
		CreditDistribution:    models.CreditDistribution{}, // TODO: Implement
	}, nil
}

// GetTransactionTrends gets transaction trends
func (s *service) GetTransactionTrends(ctx context.Context, userID, period string) (*models.UsageTrends, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"period":  period,
	}).Info("Getting transaction trends")

	// Convert to trends format
	// This is a simplified implementation
	return &models.UsageTrends{
		CreditsTrend:       "stable", // TODO: Calculate actual trend
		PercentageChange:   0.0,      // TODO: Calculate growth
		ComparedToPrevious: fmt.Sprintf("Compared to previous %s", period),
	}, nil
}

// BulkProcessTransactions processes transactions in bulk
func (s *service) BulkProcessTransactions(ctx context.Context, transactionIDs []string, action string) (*models.BulkOperationResult, error) {
	s.logger.WithFields(map[string]interface{}{
		"transaction_count": len(transactionIDs),
		"action":            action,
	}).Info("Bulk processing transactions")

	var successCount, failureCount int
	var errors []models.BulkOperationError

	for i, transactionID := range transactionIDs {
		switch action {
		case "approve":
			_, err := s.AdminApproveTransaction(ctx, transactionID)
			if err != nil {
				failureCount++
				errors = append(errors, models.BulkOperationError{
					ID:     transactionID,
					Index:  i,
					Error:  err.Error(),
					Reason: "approve_failed",
				})
			} else {
				successCount++
			}
		case "reject":
			_, err := s.AdminRejectTransaction(ctx, transactionID, "Bulk rejection")
			if err != nil {
				failureCount++
				errors = append(errors, models.BulkOperationError{
					ID:     transactionID,
					Index:  i,
					Error:  err.Error(),
					Reason: "reject_failed",
				})
			} else {
				successCount++
			}
		case "cancel":
			// For bulk cancel, we need userID - this is simplified
			err := s.updateTransactionStatusInternal(ctx, transactionID, "cancelled")
			if err != nil {
				failureCount++
				errors = append(errors, models.BulkOperationError{
					ID:     transactionID,
					Index:  i,
					Error:  err.Error(),
					Reason: "cancel_failed",
				})
			} else {
				successCount++
			}
		default:
			failureCount++
			errors = append(errors, models.BulkOperationError{
				ID:     transactionID,
				Index:  i,
				Error:  fmt.Sprintf("Unknown action: %s", action),
				Reason: "invalid_action",
			})
		}
	}

	return &models.BulkOperationResult{
		Total:   len(transactionIDs),
		Success: successCount,
		Failed:  failureCount,
		Errors:  errors,
	}, nil
}

// BulkExportTransactions exports transactions in bulk
func (s *service) BulkExportTransactions(ctx context.Context, userIDs []string, req *models.ExportTransactionsRequest) (*models.BulkOperationResult, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_count": len(userIDs),
		"format":     req.Format,
	}).Info("Bulk exporting transactions")

	var successCount, failureCount int
	var errors []models.BulkOperationError
	var results []interface{}

	for i, userID := range userIDs {
		// Create export request for each user
		userReq := &models.ExportTransactionsRequest{
			UserID:   userID,
			Format:   req.Format,
			Type:     req.Type,
			Status:   req.Status,
			DateFrom: req.DateFrom,
			DateTo:   req.DateTo,
		}

		export, err := s.ExportTransactions(ctx, userReq)
		if err != nil {
			failureCount++
			errors = append(errors, models.BulkOperationError{
				ID:     userID,
				Index:  i,
				Error:  err.Error(),
				Reason: "export_failed",
			})
		} else {
			successCount++
			results = append(results, export)
		}
	}

	return &models.BulkOperationResult{
		Total:   len(userIDs),
		Success: successCount,
		Failed:  failureCount,
		Errors:  errors,
		Results: results,
	}, nil
}

// AddCreditsWithTransaction adds credits through transaction flow
func (s *service) AddCreditsWithTransaction(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"source":  req.Source,
	}).Info("Adding credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       "credit_addition",
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata: map[string]interface{}{
			"source":      req.Source,
			"description": req.Description,
			"payment_id":  req.PaymentID,
			"invoice_id":  req.InvoiceID,
		},
	}, "add")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Add credits using credit usecase
	_, err = s.creditUC.AddCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to add credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// DeductCreditsWithTransaction deducts credits through transaction flow
func (s *service) DeductCreditsWithTransaction(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Deducting credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       req.Purpose,
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata: map[string]interface{}{
			"description": req.Description,
		},
	}, "deduct")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Deduct credits using credit usecase
	_, err = s.creditUC.DeductCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to deduct credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// createCreditTransaction creates a transaction record
func (s *service) createCreditTransaction(ctx context.Context, req *models.ConsumeCreditsRequest, transactionType string) (*models.CreditTransactionResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Determine transaction type
	var txType credittransaction.Type
	switch transactionType {
	case "add":
		txType = credittransaction.TypePurchase
	case "deduct":
		txType = credittransaction.TypeUsage
	case "consume":
		txType = credittransaction.TypeUsage
	case "refund":
		txType = credittransaction.TypeRefund
	case "transfer":
		txType = credittransaction.TypeBonus // Use bonus for transfers
	case "adjust":
		txType = credittransaction.TypeAdjustment
	default:
		txType = credittransaction.TypeUsage
	}

	// Create transaction
	tx, err := s.writeDB.CreditTransaction.Create().
		SetUserID(userUUID).
		SetType(txType).
		SetAmount(req.Amount).
		SetDescription(req.Purpose).
		SetReferenceID(req.ReferenceID).
		SetReferenceType(req.ReferenceType).
		SetStatus(credittransaction.StatusPending).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        tx.ID.String(),
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		},
		UserID:        tx.UserID.String(),
		Type:          string(tx.Type),
		Amount:        tx.Amount,
		Description:   tx.Description,
		ReferenceID:   tx.ReferenceID,
		ReferenceType: tx.ReferenceType,
		Status:        string(tx.Status),
	}, nil
}

// updateTransactionStatusInternal updates transaction status
func (s *service) updateTransactionStatusInternal(ctx context.Context, transactionID, status string) error {
	txUUID, err := uuid.Parse(transactionID)
	if err != nil {
		return fmt.Errorf("invalid transaction ID format: %w", err)
	}

	var txStatus credittransaction.Status
	switch status {
	case "pending":
		txStatus = credittransaction.StatusPending
	case "completed":
		txStatus = credittransaction.StatusCompleted
	case "failed":
		txStatus = credittransaction.StatusFailed
	case "cancelled":
		txStatus = credittransaction.StatusCancelled
	default:
		txStatus = credittransaction.StatusPending
	}

	now := time.Now()
	_, err = s.writeDB.CreditTransaction.UpdateOneID(txUUID).
		SetStatus(txStatus).
		SetProcessedAt(now).
		SetUpdatedAt(now).
		Save(ctx)

	return err
}

// TransferCreditsWithTransaction transfers credits through transaction flow
func (s *service) TransferCreditsWithTransaction(ctx context.Context, req *models.TransferCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"from_user_id": req.FromUserID,
		"to_user_id":   req.ToUserID,
		"amount":       req.Amount,
		"reason":       req.Reason,
	}).Info("Transferring credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.FromUserID,
		Amount:        req.Amount,
		Purpose:       "credit_transfer",
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata: map[string]interface{}{
			"to_user_id": req.ToUserID,
			"reason":     req.Reason,
		},
	}, "transfer")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Transfer credits using credit usecase
	_, err = s.creditUC.TransferCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to transfer credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// AdjustCreditsWithTransaction adjusts credits through transaction flow
func (s *service) AdjustCreditsWithTransaction(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"reason":  req.Reason,
	}).Info("Adjusting credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       "credit_adjustment",
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata: map[string]interface{}{
			"reason": req.Reason,
		},
	}, "adjust")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Adjust credits using credit usecase
	_, err = s.creditUC.AdjustCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to adjust credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// ConsumeCreditsWithTransaction consumes credits through transaction flow
func (s *service) ConsumeCreditsWithTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Consuming credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, req, "consume")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Consume credits using credit usecase
	_, err = s.creditUC.ConsumeCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to consume credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// RefundCreditsWithTransaction refunds credits through transaction flow
func (s *service) RefundCreditsWithTransaction(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"reason":  req.Reason,
	}).Info("Refunding credits with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       "credit_refund",
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Metadata: map[string]interface{}{
			"reason":         req.Reason,
			"transaction_id": req.TransactionID,
		},
	}, "refund")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Refund credits using credit usecase
	_, err = s.creditUC.RefundCredits(ctx, req)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to refund credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// ProcessCreditPurchase processes credit purchase through transaction flow
func (s *service) ProcessCreditPurchase(ctx context.Context, req *models.ProcessCreditPurchaseRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"payment_id": req.PaymentID,
		"amount":     req.Amount,
		"price":      req.Price,
		"source":     req.Source,
	}).Info("Processing credit purchase with transaction")

	// 1. Create transaction record first
	transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Purpose:       "credit_purchase",
		ReferenceID:   req.PaymentID,
		ReferenceType: "payment",
		Metadata: map[string]interface{}{
			"payment_id":   req.PaymentID,
			"package_id":   req.PackageID,
			"package_name": req.PackageName,
			"currency":     req.Currency,
			"price":        req.Price,
			"source":       req.Source,
		},
	}, "add")
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// 2. Add credits using credit usecase
	addReq := &models.AddCreditsRequest{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Source:        req.Source,
		Description:   fmt.Sprintf("Credit purchase via %s", req.Source),
		PaymentID:     req.PaymentID,
		ReferenceID:   req.PaymentID,
		ReferenceType: "payment",
		Metadata:      req.Metadata,
	}

	_, err = s.creditUC.AddCredits(ctx, addReq)
	if err != nil {
		// Update transaction status to failed
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
		return nil, fmt.Errorf("failed to add credits: %w", err)
	}

	// 3. Update transaction status to completed
	transactionResp.Status = "completed"
	now := time.Now()
	transactionResp.ProcessedAt = &now
	s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

	return transactionResp, nil
}

// ProcessSubscriptionPayment processes subscription payment through transaction flow
func (s *service) ProcessSubscriptionPayment(ctx context.Context, req *models.ProcessSubscriptionPaymentRequest) (*models.CreditTransactionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":         req.UserID,
		"subscription_id": req.SubscriptionID,
		"payment_id":      req.PaymentID,
		"plan_id":         req.PlanID,
		"credits":         req.Credits,
		"amount":          req.Amount,
		"source":          req.Source,
	}).Info("Processing subscription payment with transaction")

	// Only add credits if the plan includes credits
	if req.Credits > 0 {
		// 1. Create transaction record first
		transactionResp, err := s.createCreditTransaction(ctx, &models.ConsumeCreditsRequest{
			UserID:        req.UserID,
			Amount:        req.Credits,
			Purpose:       "subscription_payment",
			ReferenceID:   req.SubscriptionID,
			ReferenceType: "subscription",
			Metadata: map[string]interface{}{
				"subscription_id": req.SubscriptionID,
				"payment_id":      req.PaymentID,
				"plan_id":         req.PlanID,
				"plan_name":       req.PlanName,
				"currency":        req.Currency,
				"amount":          req.Amount,
				"source":          req.Source,
			},
		}, "add")
		if err != nil {
			return nil, fmt.Errorf("failed to create transaction: %w", err)
		}

		// 2. Add credits using credit usecase
		addReq := &models.AddCreditsRequest{
			UserID:        req.UserID,
			Amount:        req.Credits,
			Source:        "subscription",
			Description:   fmt.Sprintf("Credits from %s subscription", req.PlanName),
			PaymentID:     req.PaymentID,
			ReferenceID:   req.SubscriptionID,
			ReferenceType: "subscription",
			Metadata:      req.Metadata,
		}

		_, err = s.creditUC.AddCredits(ctx, addReq)
		if err != nil {
			// Update transaction status to failed
			s.updateTransactionStatusInternal(ctx, transactionResp.ID, "failed")
			return nil, fmt.Errorf("failed to add credits: %w", err)
		}

		// 3. Update transaction status to completed
		transactionResp.Status = "completed"
		now := time.Now()
		transactionResp.ProcessedAt = &now
		s.updateTransactionStatusInternal(ctx, transactionResp.ID, "completed")

		return transactionResp, nil
	}

	// For plans without credits, just return a success response
	return &models.CreditTransactionResponse{
		BaseModel: models.BaseModel{
			ID:        uuid.New().String(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:        req.UserID,
		Type:          "subscription",
		Amount:        0,
		Description:   fmt.Sprintf("Subscription payment for %s plan (no credits)", req.PlanName),
		ReferenceID:   req.SubscriptionID,
		ReferenceType: "subscription",
		Status:        "completed",
		ProcessedAt:   &[]time.Time{time.Now()}[0],
	}, nil
}

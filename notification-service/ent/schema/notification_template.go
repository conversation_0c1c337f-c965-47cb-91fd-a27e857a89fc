package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// NotificationTemplate holds the schema definition for the NotificationTemplate entity.
type NotificationTemplate struct {
	ent.Schema
}

// Fields of the NotificationTemplate.
func (NotificationTemplate) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.String("event_type").
			MaxLen(100).
			Comment("Event type this template is for"),
		field.String("name").
			MaxLen(200).
			Comment("Template name"),
		field.Text("description").
			Optional().
			Comment("Template description"),
		field.JSON("templates", map[string]interface{}{}).
			Comment("Channel-specific templates (email, sms, push, etc.)"),
		field.Bool("is_active").
			Default(true).
			Comment("Whether the template is active"),
		field.JSON("variables", []string{}).
			Optional().
			Comment("List of variables used in the template"),
		field.JSON("default_channels", []string{}).
			Optional().
			Comment("Default channels for this template"),
		field.Enum("default_priority").
			Values("low", "normal", "high", "urgent").
			Default("normal").
			Comment("Default priority for notifications using this template"),
		field.String("language").
			MaxLen(5).
			Default("en").
			Comment("Template language"),
		field.String("version").
			MaxLen(20).
			Default("1.0.0").
			Comment("Template version"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("Additional template metadata"),
		field.String("created_by").
			MaxLen(100).
			Optional().
			Comment("User who created the template"),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the NotificationTemplate.
func (NotificationTemplate) Edges() []ent.Edge {
	return nil
}

// Indexes of the NotificationTemplate.
func (NotificationTemplate) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("event_type"),
		index.Fields("is_active"),
		index.Fields("language"),
		index.Fields("version"),
		index.Fields("created_by"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("event_type", "is_active"),
		index.Fields("event_type", "language"),
		index.Fields("is_active", "language"),
	}
}

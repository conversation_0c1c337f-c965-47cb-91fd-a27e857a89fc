package grpc

import (
	"google.golang.org/grpc"

	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TODO: This file is temporarily disabled until proto-shared is available
// Handlers contains all gRPC handlers
type Handlers struct {
	logger logging.Logger
}

// NewHandlers creates a new handlers instance
// TODO: Implement when proto-shared is available
func NewHandlers(
	creditUseCase credit.UseCase,
	planUseCase plan.UseCase,
	subscriptionUseCase subscription.UseCase,
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *Handlers {
	return &Handlers{
		logger: logger,
	}
}

// RegisterServices registers all gRPC services
// TODO: Implement when proto-shared is available
func (h *Handlers) RegisterServices(server *grpc.Server) {
	// TODO: Register services when proto-shared is available
	// pb.RegisterCreditServiceServer(server, h.creditHandler)
	// pb.RegisterPlanServiceServer(server, h.planHandler)
	// pb.RegisterSubscriptionServiceServer(server, h.subscriptionHandler)
	// pb.RegisterTransactionServiceServer(server, h.transactionHandler)
	// pb.RegisterAICreditServiceServer(server, h.aiCreditHandler)

	h.logger.Info("gRPC services registration disabled until proto-shared is available")
}

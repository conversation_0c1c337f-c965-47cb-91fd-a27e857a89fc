// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/user"
)

// User is the model entity for the User schema.
type User struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// FullName holds the value of the "full_name" field.
	FullName string `json:"full_name,omitempty"`
	// Email holds the value of the "email" field.
	Email string `json:"email,omitempty"`
	// Phone holds the value of the "phone" field.
	Phone string `json:"phone,omitempty"`
	// AvatarURL holds the value of the "avatar_url" field.
	AvatarURL string `json:"avatar_url,omitempty"`
	// Bio holds the value of the "bio" field.
	Bio string `json:"bio,omitempty"`
	// Company holds the value of the "company" field.
	Company string `json:"company,omitempty"`
	// Industry holds the value of the "industry" field.
	Industry string `json:"industry,omitempty"`
	// Role holds the value of the "role" field.
	Role user.Role `json:"role,omitempty"`
	// PasswordHash holds the value of the "password_hash" field.
	PasswordHash string `json:"-"`
	// IsVerified holds the value of the "is_verified" field.
	IsVerified bool `json:"is_verified,omitempty"`
	// TwoFactorEnabled holds the value of the "two_factor_enabled" field.
	TwoFactorEnabled bool `json:"two_factor_enabled,omitempty"`
	// TwoFactorSecret holds the value of the "two_factor_secret" field.
	TwoFactorSecret string `json:"-"`
	// TwoFactorBackupCodes holds the value of the "two_factor_backup_codes" field.
	TwoFactorBackupCodes []string `json:"-"`
	// TwoFactorEnabledAt holds the value of the "two_factor_enabled_at" field.
	TwoFactorEnabledAt time.Time `json:"two_factor_enabled_at,omitempty"`
	// NotificationSettings holds the value of the "notification_settings" field.
	NotificationSettings map[string]interface{} `json:"notification_settings,omitempty"`
	// InviteCode holds the value of the "invite_code" field.
	InviteCode string `json:"invite_code,omitempty"`
	// AffCode holds the value of the "aff_code" field.
	AffCode string `json:"aff_code,omitempty"`
	// AffJoinedAt holds the value of the "aff_joined_at" field.
	AffJoinedAt time.Time `json:"aff_joined_at,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UserQuery when eager-loading is set.
	Edges        UserEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UserEdges holds the relations/edges for other nodes in the graph.
type UserEdges struct {
	// Sessions holds the value of the sessions edge.
	Sessions []*Session `json:"sessions,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// SessionsOrErr returns the Sessions value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) SessionsOrErr() ([]*Session, error) {
	if e.loadedTypes[0] {
		return e.Sessions, nil
	}
	return nil, &NotLoadedError{edge: "sessions"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*User) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case user.FieldTwoFactorBackupCodes, user.FieldNotificationSettings:
			values[i] = new([]byte)
		case user.FieldIsVerified, user.FieldTwoFactorEnabled:
			values[i] = new(sql.NullBool)
		case user.FieldFullName, user.FieldEmail, user.FieldPhone, user.FieldAvatarURL, user.FieldBio, user.FieldCompany, user.FieldIndustry, user.FieldRole, user.FieldPasswordHash, user.FieldTwoFactorSecret, user.FieldInviteCode, user.FieldAffCode:
			values[i] = new(sql.NullString)
		case user.FieldTwoFactorEnabledAt, user.FieldAffJoinedAt, user.FieldCreatedAt, user.FieldUpdatedAt, user.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case user.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the User fields.
func (u *User) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case user.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				u.ID = *value
			}
		case user.FieldFullName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field full_name", values[i])
			} else if value.Valid {
				u.FullName = value.String
			}
		case user.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				u.Email = value.String
			}
		case user.FieldPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				u.Phone = value.String
			}
		case user.FieldAvatarURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field avatar_url", values[i])
			} else if value.Valid {
				u.AvatarURL = value.String
			}
		case user.FieldBio:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field bio", values[i])
			} else if value.Valid {
				u.Bio = value.String
			}
		case user.FieldCompany:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field company", values[i])
			} else if value.Valid {
				u.Company = value.String
			}
		case user.FieldIndustry:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field industry", values[i])
			} else if value.Valid {
				u.Industry = value.String
			}
		case user.FieldRole:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field role", values[i])
			} else if value.Valid {
				u.Role = user.Role(value.String)
			}
		case user.FieldPasswordHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password_hash", values[i])
			} else if value.Valid {
				u.PasswordHash = value.String
			}
		case user.FieldIsVerified:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_verified", values[i])
			} else if value.Valid {
				u.IsVerified = value.Bool
			}
		case user.FieldTwoFactorEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field two_factor_enabled", values[i])
			} else if value.Valid {
				u.TwoFactorEnabled = value.Bool
			}
		case user.FieldTwoFactorSecret:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field two_factor_secret", values[i])
			} else if value.Valid {
				u.TwoFactorSecret = value.String
			}
		case user.FieldTwoFactorBackupCodes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field two_factor_backup_codes", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &u.TwoFactorBackupCodes); err != nil {
					return fmt.Errorf("unmarshal field two_factor_backup_codes: %w", err)
				}
			}
		case user.FieldTwoFactorEnabledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field two_factor_enabled_at", values[i])
			} else if value.Valid {
				u.TwoFactorEnabledAt = value.Time
			}
		case user.FieldNotificationSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field notification_settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &u.NotificationSettings); err != nil {
					return fmt.Errorf("unmarshal field notification_settings: %w", err)
				}
			}
		case user.FieldInviteCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field invite_code", values[i])
			} else if value.Valid {
				u.InviteCode = value.String
			}
		case user.FieldAffCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field aff_code", values[i])
			} else if value.Valid {
				u.AffCode = value.String
			}
		case user.FieldAffJoinedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field aff_joined_at", values[i])
			} else if value.Valid {
				u.AffJoinedAt = value.Time
			}
		case user.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				u.CreatedAt = value.Time
			}
		case user.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				u.UpdatedAt = value.Time
			}
		case user.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				u.DeletedAt = value.Time
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the User.
// This includes values selected through modifiers, order, etc.
func (u *User) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QuerySessions queries the "sessions" edge of the User entity.
func (u *User) QuerySessions() *SessionQuery {
	return NewUserClient(u.config).QuerySessions(u)
}

// Update returns a builder for updating this User.
// Note that you need to call User.Unwrap() before calling this method if this User
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *User) Update() *UserUpdateOne {
	return NewUserClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the User entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *User) Unwrap() *User {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: User is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *User) String() string {
	var builder strings.Builder
	builder.WriteString("User(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("full_name=")
	builder.WriteString(u.FullName)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(u.Email)
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(u.Phone)
	builder.WriteString(", ")
	builder.WriteString("avatar_url=")
	builder.WriteString(u.AvatarURL)
	builder.WriteString(", ")
	builder.WriteString("bio=")
	builder.WriteString(u.Bio)
	builder.WriteString(", ")
	builder.WriteString("company=")
	builder.WriteString(u.Company)
	builder.WriteString(", ")
	builder.WriteString("industry=")
	builder.WriteString(u.Industry)
	builder.WriteString(", ")
	builder.WriteString("role=")
	builder.WriteString(fmt.Sprintf("%v", u.Role))
	builder.WriteString(", ")
	builder.WriteString("password_hash=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("is_verified=")
	builder.WriteString(fmt.Sprintf("%v", u.IsVerified))
	builder.WriteString(", ")
	builder.WriteString("two_factor_enabled=")
	builder.WriteString(fmt.Sprintf("%v", u.TwoFactorEnabled))
	builder.WriteString(", ")
	builder.WriteString("two_factor_secret=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("two_factor_backup_codes=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("two_factor_enabled_at=")
	builder.WriteString(u.TwoFactorEnabledAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("notification_settings=")
	builder.WriteString(fmt.Sprintf("%v", u.NotificationSettings))
	builder.WriteString(", ")
	builder.WriteString("invite_code=")
	builder.WriteString(u.InviteCode)
	builder.WriteString(", ")
	builder.WriteString("aff_code=")
	builder.WriteString(u.AffCode)
	builder.WriteString(", ")
	builder.WriteString("aff_joined_at=")
	builder.WriteString(u.AffJoinedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(u.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(u.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(u.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Users is a parsable slice of User.
type Users []*User

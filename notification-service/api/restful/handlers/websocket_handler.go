package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/notification-service/pkg/websocket"
	"github.com/social-content-ai/pkg-shared/logging"
)

// WebSocketHandler handles WebSocket connections
type WebSocketHandler struct {
	wsHub  *websocket.Hub
	logger logging.Logger
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(wsHub *websocket.Hub, logger logging.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		wsHub:  wsHub,
		logger: logger,
	}
}

// HandleWebSocket handles WebSocket connection upgrade
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Use the Hub's HandleWebSocket method
	h.wsHub.HandleWebSocket(c.Writer, c.Request, userID.(string), "web-client")

	h.logger.WithField("user_id", userID).Info("WebSocket client connected")
}

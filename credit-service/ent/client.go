// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/ent/creditplan"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/order"
	"github.com/social-content-ai/credit-service/ent/payment"
	"github.com/social-content-ai/credit-service/ent/paymentconfirmation"
	"github.com/social-content-ai/credit-service/ent/referral"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// BankTransfer is the client for interacting with the BankTransfer builders.
	BankTransfer *BankTransferClient
	// CreditPlan is the client for interacting with the CreditPlan builders.
	CreditPlan *CreditPlanClient
	// CreditReservation is the client for interacting with the CreditReservation builders.
	CreditReservation *CreditReservationClient
	// CreditTransaction is the client for interacting with the CreditTransaction builders.
	CreditTransaction *CreditTransactionClient
	// Order is the client for interacting with the Order builders.
	Order *OrderClient
	// Payment is the client for interacting with the Payment builders.
	Payment *PaymentClient
	// PaymentConfirmation is the client for interacting with the PaymentConfirmation builders.
	PaymentConfirmation *PaymentConfirmationClient
	// Referral is the client for interacting with the Referral builders.
	Referral *ReferralClient
	// Subscription is the client for interacting with the Subscription builders.
	Subscription *SubscriptionClient
	// SubscriptionPlan is the client for interacting with the SubscriptionPlan builders.
	SubscriptionPlan *SubscriptionPlanClient
	// UserCredit is the client for interacting with the UserCredit builders.
	UserCredit *UserCreditClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.BankTransfer = NewBankTransferClient(c.config)
	c.CreditPlan = NewCreditPlanClient(c.config)
	c.CreditReservation = NewCreditReservationClient(c.config)
	c.CreditTransaction = NewCreditTransactionClient(c.config)
	c.Order = NewOrderClient(c.config)
	c.Payment = NewPaymentClient(c.config)
	c.PaymentConfirmation = NewPaymentConfirmationClient(c.config)
	c.Referral = NewReferralClient(c.config)
	c.Subscription = NewSubscriptionClient(c.config)
	c.SubscriptionPlan = NewSubscriptionPlanClient(c.config)
	c.UserCredit = NewUserCreditClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		BankTransfer:        NewBankTransferClient(cfg),
		CreditPlan:          NewCreditPlanClient(cfg),
		CreditReservation:   NewCreditReservationClient(cfg),
		CreditTransaction:   NewCreditTransactionClient(cfg),
		Order:               NewOrderClient(cfg),
		Payment:             NewPaymentClient(cfg),
		PaymentConfirmation: NewPaymentConfirmationClient(cfg),
		Referral:            NewReferralClient(cfg),
		Subscription:        NewSubscriptionClient(cfg),
		SubscriptionPlan:    NewSubscriptionPlanClient(cfg),
		UserCredit:          NewUserCreditClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		BankTransfer:        NewBankTransferClient(cfg),
		CreditPlan:          NewCreditPlanClient(cfg),
		CreditReservation:   NewCreditReservationClient(cfg),
		CreditTransaction:   NewCreditTransactionClient(cfg),
		Order:               NewOrderClient(cfg),
		Payment:             NewPaymentClient(cfg),
		PaymentConfirmation: NewPaymentConfirmationClient(cfg),
		Referral:            NewReferralClient(cfg),
		Subscription:        NewSubscriptionClient(cfg),
		SubscriptionPlan:    NewSubscriptionPlanClient(cfg),
		UserCredit:          NewUserCreditClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		BankTransfer.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.BankTransfer, c.CreditPlan, c.CreditReservation, c.CreditTransaction, c.Order,
		c.Payment, c.PaymentConfirmation, c.Referral, c.Subscription,
		c.SubscriptionPlan, c.UserCredit,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.BankTransfer, c.CreditPlan, c.CreditReservation, c.CreditTransaction, c.Order,
		c.Payment, c.PaymentConfirmation, c.Referral, c.Subscription,
		c.SubscriptionPlan, c.UserCredit,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *BankTransferMutation:
		return c.BankTransfer.mutate(ctx, m)
	case *CreditPlanMutation:
		return c.CreditPlan.mutate(ctx, m)
	case *CreditReservationMutation:
		return c.CreditReservation.mutate(ctx, m)
	case *CreditTransactionMutation:
		return c.CreditTransaction.mutate(ctx, m)
	case *OrderMutation:
		return c.Order.mutate(ctx, m)
	case *PaymentMutation:
		return c.Payment.mutate(ctx, m)
	case *PaymentConfirmationMutation:
		return c.PaymentConfirmation.mutate(ctx, m)
	case *ReferralMutation:
		return c.Referral.mutate(ctx, m)
	case *SubscriptionMutation:
		return c.Subscription.mutate(ctx, m)
	case *SubscriptionPlanMutation:
		return c.SubscriptionPlan.mutate(ctx, m)
	case *UserCreditMutation:
		return c.UserCredit.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// BankTransferClient is a client for the BankTransfer schema.
type BankTransferClient struct {
	config
}

// NewBankTransferClient returns a client for the BankTransfer from the given config.
func NewBankTransferClient(c config) *BankTransferClient {
	return &BankTransferClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `banktransfer.Hooks(f(g(h())))`.
func (c *BankTransferClient) Use(hooks ...Hook) {
	c.hooks.BankTransfer = append(c.hooks.BankTransfer, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `banktransfer.Intercept(f(g(h())))`.
func (c *BankTransferClient) Intercept(interceptors ...Interceptor) {
	c.inters.BankTransfer = append(c.inters.BankTransfer, interceptors...)
}

// Create returns a builder for creating a BankTransfer entity.
func (c *BankTransferClient) Create() *BankTransferCreate {
	mutation := newBankTransferMutation(c.config, OpCreate)
	return &BankTransferCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of BankTransfer entities.
func (c *BankTransferClient) CreateBulk(builders ...*BankTransferCreate) *BankTransferCreateBulk {
	return &BankTransferCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BankTransferClient) MapCreateBulk(slice any, setFunc func(*BankTransferCreate, int)) *BankTransferCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BankTransferCreateBulk{err: fmt.Errorf("calling to BankTransferClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BankTransferCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BankTransferCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for BankTransfer.
func (c *BankTransferClient) Update() *BankTransferUpdate {
	mutation := newBankTransferMutation(c.config, OpUpdate)
	return &BankTransferUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BankTransferClient) UpdateOne(bt *BankTransfer) *BankTransferUpdateOne {
	mutation := newBankTransferMutation(c.config, OpUpdateOne, withBankTransfer(bt))
	return &BankTransferUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BankTransferClient) UpdateOneID(id uuid.UUID) *BankTransferUpdateOne {
	mutation := newBankTransferMutation(c.config, OpUpdateOne, withBankTransferID(id))
	return &BankTransferUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for BankTransfer.
func (c *BankTransferClient) Delete() *BankTransferDelete {
	mutation := newBankTransferMutation(c.config, OpDelete)
	return &BankTransferDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BankTransferClient) DeleteOne(bt *BankTransfer) *BankTransferDeleteOne {
	return c.DeleteOneID(bt.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BankTransferClient) DeleteOneID(id uuid.UUID) *BankTransferDeleteOne {
	builder := c.Delete().Where(banktransfer.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BankTransferDeleteOne{builder}
}

// Query returns a query builder for BankTransfer.
func (c *BankTransferClient) Query() *BankTransferQuery {
	return &BankTransferQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBankTransfer},
		inters: c.Interceptors(),
	}
}

// Get returns a BankTransfer entity by its id.
func (c *BankTransferClient) Get(ctx context.Context, id uuid.UUID) (*BankTransfer, error) {
	return c.Query().Where(banktransfer.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BankTransferClient) GetX(ctx context.Context, id uuid.UUID) *BankTransfer {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *BankTransferClient) Hooks() []Hook {
	return c.hooks.BankTransfer
}

// Interceptors returns the client interceptors.
func (c *BankTransferClient) Interceptors() []Interceptor {
	return c.inters.BankTransfer
}

func (c *BankTransferClient) mutate(ctx context.Context, m *BankTransferMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BankTransferCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BankTransferUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BankTransferUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BankTransferDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown BankTransfer mutation op: %q", m.Op())
	}
}

// CreditPlanClient is a client for the CreditPlan schema.
type CreditPlanClient struct {
	config
}

// NewCreditPlanClient returns a client for the CreditPlan from the given config.
func NewCreditPlanClient(c config) *CreditPlanClient {
	return &CreditPlanClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `creditplan.Hooks(f(g(h())))`.
func (c *CreditPlanClient) Use(hooks ...Hook) {
	c.hooks.CreditPlan = append(c.hooks.CreditPlan, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `creditplan.Intercept(f(g(h())))`.
func (c *CreditPlanClient) Intercept(interceptors ...Interceptor) {
	c.inters.CreditPlan = append(c.inters.CreditPlan, interceptors...)
}

// Create returns a builder for creating a CreditPlan entity.
func (c *CreditPlanClient) Create() *CreditPlanCreate {
	mutation := newCreditPlanMutation(c.config, OpCreate)
	return &CreditPlanCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CreditPlan entities.
func (c *CreditPlanClient) CreateBulk(builders ...*CreditPlanCreate) *CreditPlanCreateBulk {
	return &CreditPlanCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CreditPlanClient) MapCreateBulk(slice any, setFunc func(*CreditPlanCreate, int)) *CreditPlanCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CreditPlanCreateBulk{err: fmt.Errorf("calling to CreditPlanClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CreditPlanCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CreditPlanCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CreditPlan.
func (c *CreditPlanClient) Update() *CreditPlanUpdate {
	mutation := newCreditPlanMutation(c.config, OpUpdate)
	return &CreditPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CreditPlanClient) UpdateOne(cp *CreditPlan) *CreditPlanUpdateOne {
	mutation := newCreditPlanMutation(c.config, OpUpdateOne, withCreditPlan(cp))
	return &CreditPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CreditPlanClient) UpdateOneID(id uuid.UUID) *CreditPlanUpdateOne {
	mutation := newCreditPlanMutation(c.config, OpUpdateOne, withCreditPlanID(id))
	return &CreditPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CreditPlan.
func (c *CreditPlanClient) Delete() *CreditPlanDelete {
	mutation := newCreditPlanMutation(c.config, OpDelete)
	return &CreditPlanDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CreditPlanClient) DeleteOne(cp *CreditPlan) *CreditPlanDeleteOne {
	return c.DeleteOneID(cp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CreditPlanClient) DeleteOneID(id uuid.UUID) *CreditPlanDeleteOne {
	builder := c.Delete().Where(creditplan.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CreditPlanDeleteOne{builder}
}

// Query returns a query builder for CreditPlan.
func (c *CreditPlanClient) Query() *CreditPlanQuery {
	return &CreditPlanQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCreditPlan},
		inters: c.Interceptors(),
	}
}

// Get returns a CreditPlan entity by its id.
func (c *CreditPlanClient) Get(ctx context.Context, id uuid.UUID) (*CreditPlan, error) {
	return c.Query().Where(creditplan.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CreditPlanClient) GetX(ctx context.Context, id uuid.UUID) *CreditPlan {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *CreditPlanClient) Hooks() []Hook {
	return c.hooks.CreditPlan
}

// Interceptors returns the client interceptors.
func (c *CreditPlanClient) Interceptors() []Interceptor {
	return c.inters.CreditPlan
}

func (c *CreditPlanClient) mutate(ctx context.Context, m *CreditPlanMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CreditPlanCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CreditPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CreditPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CreditPlanDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CreditPlan mutation op: %q", m.Op())
	}
}

// CreditReservationClient is a client for the CreditReservation schema.
type CreditReservationClient struct {
	config
}

// NewCreditReservationClient returns a client for the CreditReservation from the given config.
func NewCreditReservationClient(c config) *CreditReservationClient {
	return &CreditReservationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `creditreservation.Hooks(f(g(h())))`.
func (c *CreditReservationClient) Use(hooks ...Hook) {
	c.hooks.CreditReservation = append(c.hooks.CreditReservation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `creditreservation.Intercept(f(g(h())))`.
func (c *CreditReservationClient) Intercept(interceptors ...Interceptor) {
	c.inters.CreditReservation = append(c.inters.CreditReservation, interceptors...)
}

// Create returns a builder for creating a CreditReservation entity.
func (c *CreditReservationClient) Create() *CreditReservationCreate {
	mutation := newCreditReservationMutation(c.config, OpCreate)
	return &CreditReservationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CreditReservation entities.
func (c *CreditReservationClient) CreateBulk(builders ...*CreditReservationCreate) *CreditReservationCreateBulk {
	return &CreditReservationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CreditReservationClient) MapCreateBulk(slice any, setFunc func(*CreditReservationCreate, int)) *CreditReservationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CreditReservationCreateBulk{err: fmt.Errorf("calling to CreditReservationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CreditReservationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CreditReservationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CreditReservation.
func (c *CreditReservationClient) Update() *CreditReservationUpdate {
	mutation := newCreditReservationMutation(c.config, OpUpdate)
	return &CreditReservationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CreditReservationClient) UpdateOne(cr *CreditReservation) *CreditReservationUpdateOne {
	mutation := newCreditReservationMutation(c.config, OpUpdateOne, withCreditReservation(cr))
	return &CreditReservationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CreditReservationClient) UpdateOneID(id uuid.UUID) *CreditReservationUpdateOne {
	mutation := newCreditReservationMutation(c.config, OpUpdateOne, withCreditReservationID(id))
	return &CreditReservationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CreditReservation.
func (c *CreditReservationClient) Delete() *CreditReservationDelete {
	mutation := newCreditReservationMutation(c.config, OpDelete)
	return &CreditReservationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CreditReservationClient) DeleteOne(cr *CreditReservation) *CreditReservationDeleteOne {
	return c.DeleteOneID(cr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CreditReservationClient) DeleteOneID(id uuid.UUID) *CreditReservationDeleteOne {
	builder := c.Delete().Where(creditreservation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CreditReservationDeleteOne{builder}
}

// Query returns a query builder for CreditReservation.
func (c *CreditReservationClient) Query() *CreditReservationQuery {
	return &CreditReservationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCreditReservation},
		inters: c.Interceptors(),
	}
}

// Get returns a CreditReservation entity by its id.
func (c *CreditReservationClient) Get(ctx context.Context, id uuid.UUID) (*CreditReservation, error) {
	return c.Query().Where(creditreservation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CreditReservationClient) GetX(ctx context.Context, id uuid.UUID) *CreditReservation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *CreditReservationClient) Hooks() []Hook {
	return c.hooks.CreditReservation
}

// Interceptors returns the client interceptors.
func (c *CreditReservationClient) Interceptors() []Interceptor {
	return c.inters.CreditReservation
}

func (c *CreditReservationClient) mutate(ctx context.Context, m *CreditReservationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CreditReservationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CreditReservationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CreditReservationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CreditReservationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CreditReservation mutation op: %q", m.Op())
	}
}

// CreditTransactionClient is a client for the CreditTransaction schema.
type CreditTransactionClient struct {
	config
}

// NewCreditTransactionClient returns a client for the CreditTransaction from the given config.
func NewCreditTransactionClient(c config) *CreditTransactionClient {
	return &CreditTransactionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `credittransaction.Hooks(f(g(h())))`.
func (c *CreditTransactionClient) Use(hooks ...Hook) {
	c.hooks.CreditTransaction = append(c.hooks.CreditTransaction, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `credittransaction.Intercept(f(g(h())))`.
func (c *CreditTransactionClient) Intercept(interceptors ...Interceptor) {
	c.inters.CreditTransaction = append(c.inters.CreditTransaction, interceptors...)
}

// Create returns a builder for creating a CreditTransaction entity.
func (c *CreditTransactionClient) Create() *CreditTransactionCreate {
	mutation := newCreditTransactionMutation(c.config, OpCreate)
	return &CreditTransactionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CreditTransaction entities.
func (c *CreditTransactionClient) CreateBulk(builders ...*CreditTransactionCreate) *CreditTransactionCreateBulk {
	return &CreditTransactionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CreditTransactionClient) MapCreateBulk(slice any, setFunc func(*CreditTransactionCreate, int)) *CreditTransactionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CreditTransactionCreateBulk{err: fmt.Errorf("calling to CreditTransactionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CreditTransactionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CreditTransactionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CreditTransaction.
func (c *CreditTransactionClient) Update() *CreditTransactionUpdate {
	mutation := newCreditTransactionMutation(c.config, OpUpdate)
	return &CreditTransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CreditTransactionClient) UpdateOne(ct *CreditTransaction) *CreditTransactionUpdateOne {
	mutation := newCreditTransactionMutation(c.config, OpUpdateOne, withCreditTransaction(ct))
	return &CreditTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CreditTransactionClient) UpdateOneID(id uuid.UUID) *CreditTransactionUpdateOne {
	mutation := newCreditTransactionMutation(c.config, OpUpdateOne, withCreditTransactionID(id))
	return &CreditTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CreditTransaction.
func (c *CreditTransactionClient) Delete() *CreditTransactionDelete {
	mutation := newCreditTransactionMutation(c.config, OpDelete)
	return &CreditTransactionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CreditTransactionClient) DeleteOne(ct *CreditTransaction) *CreditTransactionDeleteOne {
	return c.DeleteOneID(ct.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CreditTransactionClient) DeleteOneID(id uuid.UUID) *CreditTransactionDeleteOne {
	builder := c.Delete().Where(credittransaction.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CreditTransactionDeleteOne{builder}
}

// Query returns a query builder for CreditTransaction.
func (c *CreditTransactionClient) Query() *CreditTransactionQuery {
	return &CreditTransactionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCreditTransaction},
		inters: c.Interceptors(),
	}
}

// Get returns a CreditTransaction entity by its id.
func (c *CreditTransactionClient) Get(ctx context.Context, id uuid.UUID) (*CreditTransaction, error) {
	return c.Query().Where(credittransaction.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CreditTransactionClient) GetX(ctx context.Context, id uuid.UUID) *CreditTransaction {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *CreditTransactionClient) Hooks() []Hook {
	return c.hooks.CreditTransaction
}

// Interceptors returns the client interceptors.
func (c *CreditTransactionClient) Interceptors() []Interceptor {
	return c.inters.CreditTransaction
}

func (c *CreditTransactionClient) mutate(ctx context.Context, m *CreditTransactionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CreditTransactionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CreditTransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CreditTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CreditTransactionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CreditTransaction mutation op: %q", m.Op())
	}
}

// OrderClient is a client for the Order schema.
type OrderClient struct {
	config
}

// NewOrderClient returns a client for the Order from the given config.
func NewOrderClient(c config) *OrderClient {
	return &OrderClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `order.Hooks(f(g(h())))`.
func (c *OrderClient) Use(hooks ...Hook) {
	c.hooks.Order = append(c.hooks.Order, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `order.Intercept(f(g(h())))`.
func (c *OrderClient) Intercept(interceptors ...Interceptor) {
	c.inters.Order = append(c.inters.Order, interceptors...)
}

// Create returns a builder for creating a Order entity.
func (c *OrderClient) Create() *OrderCreate {
	mutation := newOrderMutation(c.config, OpCreate)
	return &OrderCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Order entities.
func (c *OrderClient) CreateBulk(builders ...*OrderCreate) *OrderCreateBulk {
	return &OrderCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrderClient) MapCreateBulk(slice any, setFunc func(*OrderCreate, int)) *OrderCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrderCreateBulk{err: fmt.Errorf("calling to OrderClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrderCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrderCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Order.
func (c *OrderClient) Update() *OrderUpdate {
	mutation := newOrderMutation(c.config, OpUpdate)
	return &OrderUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrderClient) UpdateOne(o *Order) *OrderUpdateOne {
	mutation := newOrderMutation(c.config, OpUpdateOne, withOrder(o))
	return &OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrderClient) UpdateOneID(id uuid.UUID) *OrderUpdateOne {
	mutation := newOrderMutation(c.config, OpUpdateOne, withOrderID(id))
	return &OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Order.
func (c *OrderClient) Delete() *OrderDelete {
	mutation := newOrderMutation(c.config, OpDelete)
	return &OrderDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrderClient) DeleteOne(o *Order) *OrderDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrderClient) DeleteOneID(id uuid.UUID) *OrderDeleteOne {
	builder := c.Delete().Where(order.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrderDeleteOne{builder}
}

// Query returns a query builder for Order.
func (c *OrderClient) Query() *OrderQuery {
	return &OrderQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrder},
		inters: c.Interceptors(),
	}
}

// Get returns a Order entity by its id.
func (c *OrderClient) Get(ctx context.Context, id uuid.UUID) (*Order, error) {
	return c.Query().Where(order.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrderClient) GetX(ctx context.Context, id uuid.UUID) *Order {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *OrderClient) Hooks() []Hook {
	return c.hooks.Order
}

// Interceptors returns the client interceptors.
func (c *OrderClient) Interceptors() []Interceptor {
	return c.inters.Order
}

func (c *OrderClient) mutate(ctx context.Context, m *OrderMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrderCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrderUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrderDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Order mutation op: %q", m.Op())
	}
}

// PaymentClient is a client for the Payment schema.
type PaymentClient struct {
	config
}

// NewPaymentClient returns a client for the Payment from the given config.
func NewPaymentClient(c config) *PaymentClient {
	return &PaymentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `payment.Hooks(f(g(h())))`.
func (c *PaymentClient) Use(hooks ...Hook) {
	c.hooks.Payment = append(c.hooks.Payment, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `payment.Intercept(f(g(h())))`.
func (c *PaymentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Payment = append(c.inters.Payment, interceptors...)
}

// Create returns a builder for creating a Payment entity.
func (c *PaymentClient) Create() *PaymentCreate {
	mutation := newPaymentMutation(c.config, OpCreate)
	return &PaymentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Payment entities.
func (c *PaymentClient) CreateBulk(builders ...*PaymentCreate) *PaymentCreateBulk {
	return &PaymentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PaymentClient) MapCreateBulk(slice any, setFunc func(*PaymentCreate, int)) *PaymentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PaymentCreateBulk{err: fmt.Errorf("calling to PaymentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PaymentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PaymentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Payment.
func (c *PaymentClient) Update() *PaymentUpdate {
	mutation := newPaymentMutation(c.config, OpUpdate)
	return &PaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PaymentClient) UpdateOne(pa *Payment) *PaymentUpdateOne {
	mutation := newPaymentMutation(c.config, OpUpdateOne, withPayment(pa))
	return &PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PaymentClient) UpdateOneID(id uuid.UUID) *PaymentUpdateOne {
	mutation := newPaymentMutation(c.config, OpUpdateOne, withPaymentID(id))
	return &PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Payment.
func (c *PaymentClient) Delete() *PaymentDelete {
	mutation := newPaymentMutation(c.config, OpDelete)
	return &PaymentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PaymentClient) DeleteOne(pa *Payment) *PaymentDeleteOne {
	return c.DeleteOneID(pa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PaymentClient) DeleteOneID(id uuid.UUID) *PaymentDeleteOne {
	builder := c.Delete().Where(payment.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PaymentDeleteOne{builder}
}

// Query returns a query builder for Payment.
func (c *PaymentClient) Query() *PaymentQuery {
	return &PaymentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePayment},
		inters: c.Interceptors(),
	}
}

// Get returns a Payment entity by its id.
func (c *PaymentClient) Get(ctx context.Context, id uuid.UUID) (*Payment, error) {
	return c.Query().Where(payment.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PaymentClient) GetX(ctx context.Context, id uuid.UUID) *Payment {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PaymentClient) Hooks() []Hook {
	return c.hooks.Payment
}

// Interceptors returns the client interceptors.
func (c *PaymentClient) Interceptors() []Interceptor {
	return c.inters.Payment
}

func (c *PaymentClient) mutate(ctx context.Context, m *PaymentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PaymentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PaymentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PaymentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PaymentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Payment mutation op: %q", m.Op())
	}
}

// PaymentConfirmationClient is a client for the PaymentConfirmation schema.
type PaymentConfirmationClient struct {
	config
}

// NewPaymentConfirmationClient returns a client for the PaymentConfirmation from the given config.
func NewPaymentConfirmationClient(c config) *PaymentConfirmationClient {
	return &PaymentConfirmationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `paymentconfirmation.Hooks(f(g(h())))`.
func (c *PaymentConfirmationClient) Use(hooks ...Hook) {
	c.hooks.PaymentConfirmation = append(c.hooks.PaymentConfirmation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `paymentconfirmation.Intercept(f(g(h())))`.
func (c *PaymentConfirmationClient) Intercept(interceptors ...Interceptor) {
	c.inters.PaymentConfirmation = append(c.inters.PaymentConfirmation, interceptors...)
}

// Create returns a builder for creating a PaymentConfirmation entity.
func (c *PaymentConfirmationClient) Create() *PaymentConfirmationCreate {
	mutation := newPaymentConfirmationMutation(c.config, OpCreate)
	return &PaymentConfirmationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PaymentConfirmation entities.
func (c *PaymentConfirmationClient) CreateBulk(builders ...*PaymentConfirmationCreate) *PaymentConfirmationCreateBulk {
	return &PaymentConfirmationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PaymentConfirmationClient) MapCreateBulk(slice any, setFunc func(*PaymentConfirmationCreate, int)) *PaymentConfirmationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PaymentConfirmationCreateBulk{err: fmt.Errorf("calling to PaymentConfirmationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PaymentConfirmationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PaymentConfirmationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PaymentConfirmation.
func (c *PaymentConfirmationClient) Update() *PaymentConfirmationUpdate {
	mutation := newPaymentConfirmationMutation(c.config, OpUpdate)
	return &PaymentConfirmationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PaymentConfirmationClient) UpdateOne(pc *PaymentConfirmation) *PaymentConfirmationUpdateOne {
	mutation := newPaymentConfirmationMutation(c.config, OpUpdateOne, withPaymentConfirmation(pc))
	return &PaymentConfirmationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PaymentConfirmationClient) UpdateOneID(id uuid.UUID) *PaymentConfirmationUpdateOne {
	mutation := newPaymentConfirmationMutation(c.config, OpUpdateOne, withPaymentConfirmationID(id))
	return &PaymentConfirmationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PaymentConfirmation.
func (c *PaymentConfirmationClient) Delete() *PaymentConfirmationDelete {
	mutation := newPaymentConfirmationMutation(c.config, OpDelete)
	return &PaymentConfirmationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PaymentConfirmationClient) DeleteOne(pc *PaymentConfirmation) *PaymentConfirmationDeleteOne {
	return c.DeleteOneID(pc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PaymentConfirmationClient) DeleteOneID(id uuid.UUID) *PaymentConfirmationDeleteOne {
	builder := c.Delete().Where(paymentconfirmation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PaymentConfirmationDeleteOne{builder}
}

// Query returns a query builder for PaymentConfirmation.
func (c *PaymentConfirmationClient) Query() *PaymentConfirmationQuery {
	return &PaymentConfirmationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePaymentConfirmation},
		inters: c.Interceptors(),
	}
}

// Get returns a PaymentConfirmation entity by its id.
func (c *PaymentConfirmationClient) Get(ctx context.Context, id uuid.UUID) (*PaymentConfirmation, error) {
	return c.Query().Where(paymentconfirmation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PaymentConfirmationClient) GetX(ctx context.Context, id uuid.UUID) *PaymentConfirmation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PaymentConfirmationClient) Hooks() []Hook {
	return c.hooks.PaymentConfirmation
}

// Interceptors returns the client interceptors.
func (c *PaymentConfirmationClient) Interceptors() []Interceptor {
	return c.inters.PaymentConfirmation
}

func (c *PaymentConfirmationClient) mutate(ctx context.Context, m *PaymentConfirmationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PaymentConfirmationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PaymentConfirmationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PaymentConfirmationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PaymentConfirmationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PaymentConfirmation mutation op: %q", m.Op())
	}
}

// ReferralClient is a client for the Referral schema.
type ReferralClient struct {
	config
}

// NewReferralClient returns a client for the Referral from the given config.
func NewReferralClient(c config) *ReferralClient {
	return &ReferralClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `referral.Hooks(f(g(h())))`.
func (c *ReferralClient) Use(hooks ...Hook) {
	c.hooks.Referral = append(c.hooks.Referral, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `referral.Intercept(f(g(h())))`.
func (c *ReferralClient) Intercept(interceptors ...Interceptor) {
	c.inters.Referral = append(c.inters.Referral, interceptors...)
}

// Create returns a builder for creating a Referral entity.
func (c *ReferralClient) Create() *ReferralCreate {
	mutation := newReferralMutation(c.config, OpCreate)
	return &ReferralCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Referral entities.
func (c *ReferralClient) CreateBulk(builders ...*ReferralCreate) *ReferralCreateBulk {
	return &ReferralCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ReferralClient) MapCreateBulk(slice any, setFunc func(*ReferralCreate, int)) *ReferralCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ReferralCreateBulk{err: fmt.Errorf("calling to ReferralClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ReferralCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ReferralCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Referral.
func (c *ReferralClient) Update() *ReferralUpdate {
	mutation := newReferralMutation(c.config, OpUpdate)
	return &ReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ReferralClient) UpdateOne(r *Referral) *ReferralUpdateOne {
	mutation := newReferralMutation(c.config, OpUpdateOne, withReferral(r))
	return &ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ReferralClient) UpdateOneID(id uuid.UUID) *ReferralUpdateOne {
	mutation := newReferralMutation(c.config, OpUpdateOne, withReferralID(id))
	return &ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Referral.
func (c *ReferralClient) Delete() *ReferralDelete {
	mutation := newReferralMutation(c.config, OpDelete)
	return &ReferralDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ReferralClient) DeleteOne(r *Referral) *ReferralDeleteOne {
	return c.DeleteOneID(r.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ReferralClient) DeleteOneID(id uuid.UUID) *ReferralDeleteOne {
	builder := c.Delete().Where(referral.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ReferralDeleteOne{builder}
}

// Query returns a query builder for Referral.
func (c *ReferralClient) Query() *ReferralQuery {
	return &ReferralQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeReferral},
		inters: c.Interceptors(),
	}
}

// Get returns a Referral entity by its id.
func (c *ReferralClient) Get(ctx context.Context, id uuid.UUID) (*Referral, error) {
	return c.Query().Where(referral.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ReferralClient) GetX(ctx context.Context, id uuid.UUID) *Referral {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ReferralClient) Hooks() []Hook {
	return c.hooks.Referral
}

// Interceptors returns the client interceptors.
func (c *ReferralClient) Interceptors() []Interceptor {
	return c.inters.Referral
}

func (c *ReferralClient) mutate(ctx context.Context, m *ReferralMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ReferralCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ReferralUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ReferralUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ReferralDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Referral mutation op: %q", m.Op())
	}
}

// SubscriptionClient is a client for the Subscription schema.
type SubscriptionClient struct {
	config
}

// NewSubscriptionClient returns a client for the Subscription from the given config.
func NewSubscriptionClient(c config) *SubscriptionClient {
	return &SubscriptionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `subscription.Hooks(f(g(h())))`.
func (c *SubscriptionClient) Use(hooks ...Hook) {
	c.hooks.Subscription = append(c.hooks.Subscription, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `subscription.Intercept(f(g(h())))`.
func (c *SubscriptionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Subscription = append(c.inters.Subscription, interceptors...)
}

// Create returns a builder for creating a Subscription entity.
func (c *SubscriptionClient) Create() *SubscriptionCreate {
	mutation := newSubscriptionMutation(c.config, OpCreate)
	return &SubscriptionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Subscription entities.
func (c *SubscriptionClient) CreateBulk(builders ...*SubscriptionCreate) *SubscriptionCreateBulk {
	return &SubscriptionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SubscriptionClient) MapCreateBulk(slice any, setFunc func(*SubscriptionCreate, int)) *SubscriptionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SubscriptionCreateBulk{err: fmt.Errorf("calling to SubscriptionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SubscriptionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SubscriptionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Subscription.
func (c *SubscriptionClient) Update() *SubscriptionUpdate {
	mutation := newSubscriptionMutation(c.config, OpUpdate)
	return &SubscriptionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SubscriptionClient) UpdateOne(s *Subscription) *SubscriptionUpdateOne {
	mutation := newSubscriptionMutation(c.config, OpUpdateOne, withSubscription(s))
	return &SubscriptionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SubscriptionClient) UpdateOneID(id uuid.UUID) *SubscriptionUpdateOne {
	mutation := newSubscriptionMutation(c.config, OpUpdateOne, withSubscriptionID(id))
	return &SubscriptionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Subscription.
func (c *SubscriptionClient) Delete() *SubscriptionDelete {
	mutation := newSubscriptionMutation(c.config, OpDelete)
	return &SubscriptionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SubscriptionClient) DeleteOne(s *Subscription) *SubscriptionDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SubscriptionClient) DeleteOneID(id uuid.UUID) *SubscriptionDeleteOne {
	builder := c.Delete().Where(subscription.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SubscriptionDeleteOne{builder}
}

// Query returns a query builder for Subscription.
func (c *SubscriptionClient) Query() *SubscriptionQuery {
	return &SubscriptionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSubscription},
		inters: c.Interceptors(),
	}
}

// Get returns a Subscription entity by its id.
func (c *SubscriptionClient) Get(ctx context.Context, id uuid.UUID) (*Subscription, error) {
	return c.Query().Where(subscription.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SubscriptionClient) GetX(ctx context.Context, id uuid.UUID) *Subscription {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SubscriptionClient) Hooks() []Hook {
	return c.hooks.Subscription
}

// Interceptors returns the client interceptors.
func (c *SubscriptionClient) Interceptors() []Interceptor {
	return c.inters.Subscription
}

func (c *SubscriptionClient) mutate(ctx context.Context, m *SubscriptionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SubscriptionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SubscriptionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SubscriptionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SubscriptionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Subscription mutation op: %q", m.Op())
	}
}

// SubscriptionPlanClient is a client for the SubscriptionPlan schema.
type SubscriptionPlanClient struct {
	config
}

// NewSubscriptionPlanClient returns a client for the SubscriptionPlan from the given config.
func NewSubscriptionPlanClient(c config) *SubscriptionPlanClient {
	return &SubscriptionPlanClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `subscriptionplan.Hooks(f(g(h())))`.
func (c *SubscriptionPlanClient) Use(hooks ...Hook) {
	c.hooks.SubscriptionPlan = append(c.hooks.SubscriptionPlan, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `subscriptionplan.Intercept(f(g(h())))`.
func (c *SubscriptionPlanClient) Intercept(interceptors ...Interceptor) {
	c.inters.SubscriptionPlan = append(c.inters.SubscriptionPlan, interceptors...)
}

// Create returns a builder for creating a SubscriptionPlan entity.
func (c *SubscriptionPlanClient) Create() *SubscriptionPlanCreate {
	mutation := newSubscriptionPlanMutation(c.config, OpCreate)
	return &SubscriptionPlanCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SubscriptionPlan entities.
func (c *SubscriptionPlanClient) CreateBulk(builders ...*SubscriptionPlanCreate) *SubscriptionPlanCreateBulk {
	return &SubscriptionPlanCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SubscriptionPlanClient) MapCreateBulk(slice any, setFunc func(*SubscriptionPlanCreate, int)) *SubscriptionPlanCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SubscriptionPlanCreateBulk{err: fmt.Errorf("calling to SubscriptionPlanClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SubscriptionPlanCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SubscriptionPlanCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SubscriptionPlan.
func (c *SubscriptionPlanClient) Update() *SubscriptionPlanUpdate {
	mutation := newSubscriptionPlanMutation(c.config, OpUpdate)
	return &SubscriptionPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SubscriptionPlanClient) UpdateOne(sp *SubscriptionPlan) *SubscriptionPlanUpdateOne {
	mutation := newSubscriptionPlanMutation(c.config, OpUpdateOne, withSubscriptionPlan(sp))
	return &SubscriptionPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SubscriptionPlanClient) UpdateOneID(id uuid.UUID) *SubscriptionPlanUpdateOne {
	mutation := newSubscriptionPlanMutation(c.config, OpUpdateOne, withSubscriptionPlanID(id))
	return &SubscriptionPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SubscriptionPlan.
func (c *SubscriptionPlanClient) Delete() *SubscriptionPlanDelete {
	mutation := newSubscriptionPlanMutation(c.config, OpDelete)
	return &SubscriptionPlanDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SubscriptionPlanClient) DeleteOne(sp *SubscriptionPlan) *SubscriptionPlanDeleteOne {
	return c.DeleteOneID(sp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SubscriptionPlanClient) DeleteOneID(id uuid.UUID) *SubscriptionPlanDeleteOne {
	builder := c.Delete().Where(subscriptionplan.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SubscriptionPlanDeleteOne{builder}
}

// Query returns a query builder for SubscriptionPlan.
func (c *SubscriptionPlanClient) Query() *SubscriptionPlanQuery {
	return &SubscriptionPlanQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSubscriptionPlan},
		inters: c.Interceptors(),
	}
}

// Get returns a SubscriptionPlan entity by its id.
func (c *SubscriptionPlanClient) Get(ctx context.Context, id uuid.UUID) (*SubscriptionPlan, error) {
	return c.Query().Where(subscriptionplan.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SubscriptionPlanClient) GetX(ctx context.Context, id uuid.UUID) *SubscriptionPlan {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SubscriptionPlanClient) Hooks() []Hook {
	return c.hooks.SubscriptionPlan
}

// Interceptors returns the client interceptors.
func (c *SubscriptionPlanClient) Interceptors() []Interceptor {
	return c.inters.SubscriptionPlan
}

func (c *SubscriptionPlanClient) mutate(ctx context.Context, m *SubscriptionPlanMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SubscriptionPlanCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SubscriptionPlanUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SubscriptionPlanUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SubscriptionPlanDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SubscriptionPlan mutation op: %q", m.Op())
	}
}

// UserCreditClient is a client for the UserCredit schema.
type UserCreditClient struct {
	config
}

// NewUserCreditClient returns a client for the UserCredit from the given config.
func NewUserCreditClient(c config) *UserCreditClient {
	return &UserCreditClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `usercredit.Hooks(f(g(h())))`.
func (c *UserCreditClient) Use(hooks ...Hook) {
	c.hooks.UserCredit = append(c.hooks.UserCredit, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `usercredit.Intercept(f(g(h())))`.
func (c *UserCreditClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserCredit = append(c.inters.UserCredit, interceptors...)
}

// Create returns a builder for creating a UserCredit entity.
func (c *UserCreditClient) Create() *UserCreditCreate {
	mutation := newUserCreditMutation(c.config, OpCreate)
	return &UserCreditCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserCredit entities.
func (c *UserCreditClient) CreateBulk(builders ...*UserCreditCreate) *UserCreditCreateBulk {
	return &UserCreditCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserCreditClient) MapCreateBulk(slice any, setFunc func(*UserCreditCreate, int)) *UserCreditCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreditCreateBulk{err: fmt.Errorf("calling to UserCreditClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreditCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreditCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserCredit.
func (c *UserCreditClient) Update() *UserCreditUpdate {
	mutation := newUserCreditMutation(c.config, OpUpdate)
	return &UserCreditUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserCreditClient) UpdateOne(uc *UserCredit) *UserCreditUpdateOne {
	mutation := newUserCreditMutation(c.config, OpUpdateOne, withUserCredit(uc))
	return &UserCreditUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserCreditClient) UpdateOneID(id uuid.UUID) *UserCreditUpdateOne {
	mutation := newUserCreditMutation(c.config, OpUpdateOne, withUserCreditID(id))
	return &UserCreditUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserCredit.
func (c *UserCreditClient) Delete() *UserCreditDelete {
	mutation := newUserCreditMutation(c.config, OpDelete)
	return &UserCreditDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserCreditClient) DeleteOne(uc *UserCredit) *UserCreditDeleteOne {
	return c.DeleteOneID(uc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserCreditClient) DeleteOneID(id uuid.UUID) *UserCreditDeleteOne {
	builder := c.Delete().Where(usercredit.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserCreditDeleteOne{builder}
}

// Query returns a query builder for UserCredit.
func (c *UserCreditClient) Query() *UserCreditQuery {
	return &UserCreditQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserCredit},
		inters: c.Interceptors(),
	}
}

// Get returns a UserCredit entity by its id.
func (c *UserCreditClient) Get(ctx context.Context, id uuid.UUID) (*UserCredit, error) {
	return c.Query().Where(usercredit.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserCreditClient) GetX(ctx context.Context, id uuid.UUID) *UserCredit {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *UserCreditClient) Hooks() []Hook {
	return c.hooks.UserCredit
}

// Interceptors returns the client interceptors.
func (c *UserCreditClient) Interceptors() []Interceptor {
	return c.inters.UserCredit
}

func (c *UserCreditClient) mutate(ctx context.Context, m *UserCreditMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreditCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserCreditUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserCreditUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserCreditDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserCredit mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		BankTransfer, CreditPlan, CreditReservation, CreditTransaction, Order, Payment,
		PaymentConfirmation, Referral, Subscription, SubscriptionPlan,
		UserCredit []ent.Hook
	}
	inters struct {
		BankTransfer, CreditPlan, CreditReservation, CreditTransaction, Order, Payment,
		PaymentConfirmation, Referral, Subscription, SubscriptionPlan,
		UserCredit []ent.Interceptor
	}
)

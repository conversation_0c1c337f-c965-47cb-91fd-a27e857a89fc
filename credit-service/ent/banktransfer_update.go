// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// BankTransferUpdate is the builder for updating BankTransfer entities.
type BankTransferUpdate struct {
	config
	hooks    []Hook
	mutation *BankTransferMutation
}

// Where appends a list predicates to the BankTransferUpdate builder.
func (btu *BankTransferUpdate) Where(ps ...predicate.BankTransfer) *BankTransferUpdate {
	btu.mutation.Where(ps...)
	return btu
}

// SetUserID sets the "user_id" field.
func (btu *BankTransferUpdate) SetUserID(u uuid.UUID) *BankTransferUpdate {
	btu.mutation.SetUserID(u)
	return btu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableUserID(u *uuid.UUID) *BankTransferUpdate {
	if u != nil {
		btu.SetUserID(*u)
	}
	return btu
}

// SetPlanID sets the "plan_id" field.
func (btu *BankTransferUpdate) SetPlanID(u uuid.UUID) *BankTransferUpdate {
	btu.mutation.SetPlanID(u)
	return btu
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillablePlanID(u *uuid.UUID) *BankTransferUpdate {
	if u != nil {
		btu.SetPlanID(*u)
	}
	return btu
}

// ClearPlanID clears the value of the "plan_id" field.
func (btu *BankTransferUpdate) ClearPlanID() *BankTransferUpdate {
	btu.mutation.ClearPlanID()
	return btu
}

// SetAmount sets the "amount" field.
func (btu *BankTransferUpdate) SetAmount(i int64) *BankTransferUpdate {
	btu.mutation.ResetAmount()
	btu.mutation.SetAmount(i)
	return btu
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableAmount(i *int64) *BankTransferUpdate {
	if i != nil {
		btu.SetAmount(*i)
	}
	return btu
}

// AddAmount adds i to the "amount" field.
func (btu *BankTransferUpdate) AddAmount(i int64) *BankTransferUpdate {
	btu.mutation.AddAmount(i)
	return btu
}

// SetCurrency sets the "currency" field.
func (btu *BankTransferUpdate) SetCurrency(s string) *BankTransferUpdate {
	btu.mutation.SetCurrency(s)
	return btu
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableCurrency(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetCurrency(*s)
	}
	return btu
}

// SetStatus sets the "status" field.
func (btu *BankTransferUpdate) SetStatus(b banktransfer.Status) *BankTransferUpdate {
	btu.mutation.SetStatus(b)
	return btu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableStatus(b *banktransfer.Status) *BankTransferUpdate {
	if b != nil {
		btu.SetStatus(*b)
	}
	return btu
}

// SetBankAccount sets the "bank_account" field.
func (btu *BankTransferUpdate) SetBankAccount(s string) *BankTransferUpdate {
	btu.mutation.SetBankAccount(s)
	return btu
}

// SetNillableBankAccount sets the "bank_account" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableBankAccount(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetBankAccount(*s)
	}
	return btu
}

// SetReferenceCode sets the "reference_code" field.
func (btu *BankTransferUpdate) SetReferenceCode(s string) *BankTransferUpdate {
	btu.mutation.SetReferenceCode(s)
	return btu
}

// SetNillableReferenceCode sets the "reference_code" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableReferenceCode(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetReferenceCode(*s)
	}
	return btu
}

// SetTransferInstructions sets the "transfer_instructions" field.
func (btu *BankTransferUpdate) SetTransferInstructions(s string) *BankTransferUpdate {
	btu.mutation.SetTransferInstructions(s)
	return btu
}

// SetNillableTransferInstructions sets the "transfer_instructions" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableTransferInstructions(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetTransferInstructions(*s)
	}
	return btu
}

// SetConfirmedAt sets the "confirmed_at" field.
func (btu *BankTransferUpdate) SetConfirmedAt(t time.Time) *BankTransferUpdate {
	btu.mutation.SetConfirmedAt(t)
	return btu
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableConfirmedAt(t *time.Time) *BankTransferUpdate {
	if t != nil {
		btu.SetConfirmedAt(*t)
	}
	return btu
}

// ClearConfirmedAt clears the value of the "confirmed_at" field.
func (btu *BankTransferUpdate) ClearConfirmedAt() *BankTransferUpdate {
	btu.mutation.ClearConfirmedAt()
	return btu
}

// SetExpiresAt sets the "expires_at" field.
func (btu *BankTransferUpdate) SetExpiresAt(t time.Time) *BankTransferUpdate {
	btu.mutation.SetExpiresAt(t)
	return btu
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableExpiresAt(t *time.Time) *BankTransferUpdate {
	if t != nil {
		btu.SetExpiresAt(*t)
	}
	return btu
}

// SetCreditsToAdd sets the "credits_to_add" field.
func (btu *BankTransferUpdate) SetCreditsToAdd(i int) *BankTransferUpdate {
	btu.mutation.ResetCreditsToAdd()
	btu.mutation.SetCreditsToAdd(i)
	return btu
}

// SetNillableCreditsToAdd sets the "credits_to_add" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableCreditsToAdd(i *int) *BankTransferUpdate {
	if i != nil {
		btu.SetCreditsToAdd(*i)
	}
	return btu
}

// AddCreditsToAdd adds i to the "credits_to_add" field.
func (btu *BankTransferUpdate) AddCreditsToAdd(i int) *BankTransferUpdate {
	btu.mutation.AddCreditsToAdd(i)
	return btu
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (btu *BankTransferUpdate) SetBankTransactionID(s string) *BankTransferUpdate {
	btu.mutation.SetBankTransactionID(s)
	return btu
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableBankTransactionID(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetBankTransactionID(*s)
	}
	return btu
}

// ClearBankTransactionID clears the value of the "bank_transaction_id" field.
func (btu *BankTransferUpdate) ClearBankTransactionID() *BankTransferUpdate {
	btu.mutation.ClearBankTransactionID()
	return btu
}

// SetActualAmountReceived sets the "actual_amount_received" field.
func (btu *BankTransferUpdate) SetActualAmountReceived(i int64) *BankTransferUpdate {
	btu.mutation.ResetActualAmountReceived()
	btu.mutation.SetActualAmountReceived(i)
	return btu
}

// SetNillableActualAmountReceived sets the "actual_amount_received" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableActualAmountReceived(i *int64) *BankTransferUpdate {
	if i != nil {
		btu.SetActualAmountReceived(*i)
	}
	return btu
}

// AddActualAmountReceived adds i to the "actual_amount_received" field.
func (btu *BankTransferUpdate) AddActualAmountReceived(i int64) *BankTransferUpdate {
	btu.mutation.AddActualAmountReceived(i)
	return btu
}

// ClearActualAmountReceived clears the value of the "actual_amount_received" field.
func (btu *BankTransferUpdate) ClearActualAmountReceived() *BankTransferUpdate {
	btu.mutation.ClearActualAmountReceived()
	return btu
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (btu *BankTransferUpdate) SetConfirmationMethod(s string) *BankTransferUpdate {
	btu.mutation.SetConfirmationMethod(s)
	return btu
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableConfirmationMethod(s *string) *BankTransferUpdate {
	if s != nil {
		btu.SetConfirmationMethod(*s)
	}
	return btu
}

// SetConfirmedBy sets the "confirmed_by" field.
func (btu *BankTransferUpdate) SetConfirmedBy(u uuid.UUID) *BankTransferUpdate {
	btu.mutation.SetConfirmedBy(u)
	return btu
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (btu *BankTransferUpdate) SetNillableConfirmedBy(u *uuid.UUID) *BankTransferUpdate {
	if u != nil {
		btu.SetConfirmedBy(*u)
	}
	return btu
}

// ClearConfirmedBy clears the value of the "confirmed_by" field.
func (btu *BankTransferUpdate) ClearConfirmedBy() *BankTransferUpdate {
	btu.mutation.ClearConfirmedBy()
	return btu
}

// SetMetadata sets the "metadata" field.
func (btu *BankTransferUpdate) SetMetadata(m map[string]interface{}) *BankTransferUpdate {
	btu.mutation.SetMetadata(m)
	return btu
}

// ClearMetadata clears the value of the "metadata" field.
func (btu *BankTransferUpdate) ClearMetadata() *BankTransferUpdate {
	btu.mutation.ClearMetadata()
	return btu
}

// SetUpdatedAt sets the "updated_at" field.
func (btu *BankTransferUpdate) SetUpdatedAt(t time.Time) *BankTransferUpdate {
	btu.mutation.SetUpdatedAt(t)
	return btu
}

// Mutation returns the BankTransferMutation object of the builder.
func (btu *BankTransferUpdate) Mutation() *BankTransferMutation {
	return btu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (btu *BankTransferUpdate) Save(ctx context.Context) (int, error) {
	btu.defaults()
	return withHooks(ctx, btu.sqlSave, btu.mutation, btu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (btu *BankTransferUpdate) SaveX(ctx context.Context) int {
	affected, err := btu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (btu *BankTransferUpdate) Exec(ctx context.Context) error {
	_, err := btu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (btu *BankTransferUpdate) ExecX(ctx context.Context) {
	if err := btu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (btu *BankTransferUpdate) defaults() {
	if _, ok := btu.mutation.UpdatedAt(); !ok {
		v := banktransfer.UpdateDefaultUpdatedAt()
		btu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (btu *BankTransferUpdate) check() error {
	if v, ok := btu.mutation.Amount(); ok {
		if err := banktransfer.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.amount": %w`, err)}
		}
	}
	if v, ok := btu.mutation.Currency(); ok {
		if err := banktransfer.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.currency": %w`, err)}
		}
	}
	if v, ok := btu.mutation.Status(); ok {
		if err := banktransfer.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.status": %w`, err)}
		}
	}
	if v, ok := btu.mutation.BankAccount(); ok {
		if err := banktransfer.BankAccountValidator(v); err != nil {
			return &ValidationError{Name: "bank_account", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_account": %w`, err)}
		}
	}
	if v, ok := btu.mutation.ReferenceCode(); ok {
		if err := banktransfer.ReferenceCodeValidator(v); err != nil {
			return &ValidationError{Name: "reference_code", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.reference_code": %w`, err)}
		}
	}
	if v, ok := btu.mutation.CreditsToAdd(); ok {
		if err := banktransfer.CreditsToAddValidator(v); err != nil {
			return &ValidationError{Name: "credits_to_add", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.credits_to_add": %w`, err)}
		}
	}
	if v, ok := btu.mutation.BankTransactionID(); ok {
		if err := banktransfer.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_transaction_id": %w`, err)}
		}
	}
	if v, ok := btu.mutation.ActualAmountReceived(); ok {
		if err := banktransfer.ActualAmountReceivedValidator(v); err != nil {
			return &ValidationError{Name: "actual_amount_received", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.actual_amount_received": %w`, err)}
		}
	}
	if v, ok := btu.mutation.ConfirmationMethod(); ok {
		if err := banktransfer.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.confirmation_method": %w`, err)}
		}
	}
	return nil
}

func (btu *BankTransferUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := btu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(banktransfer.Table, banktransfer.Columns, sqlgraph.NewFieldSpec(banktransfer.FieldID, field.TypeUUID))
	if ps := btu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := btu.mutation.UserID(); ok {
		_spec.SetField(banktransfer.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := btu.mutation.PlanID(); ok {
		_spec.SetField(banktransfer.FieldPlanID, field.TypeUUID, value)
	}
	if btu.mutation.PlanIDCleared() {
		_spec.ClearField(banktransfer.FieldPlanID, field.TypeUUID)
	}
	if value, ok := btu.mutation.Amount(); ok {
		_spec.SetField(banktransfer.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := btu.mutation.AddedAmount(); ok {
		_spec.AddField(banktransfer.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := btu.mutation.Currency(); ok {
		_spec.SetField(banktransfer.FieldCurrency, field.TypeString, value)
	}
	if value, ok := btu.mutation.Status(); ok {
		_spec.SetField(banktransfer.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := btu.mutation.BankAccount(); ok {
		_spec.SetField(banktransfer.FieldBankAccount, field.TypeString, value)
	}
	if value, ok := btu.mutation.ReferenceCode(); ok {
		_spec.SetField(banktransfer.FieldReferenceCode, field.TypeString, value)
	}
	if value, ok := btu.mutation.TransferInstructions(); ok {
		_spec.SetField(banktransfer.FieldTransferInstructions, field.TypeString, value)
	}
	if value, ok := btu.mutation.ConfirmedAt(); ok {
		_spec.SetField(banktransfer.FieldConfirmedAt, field.TypeTime, value)
	}
	if btu.mutation.ConfirmedAtCleared() {
		_spec.ClearField(banktransfer.FieldConfirmedAt, field.TypeTime)
	}
	if value, ok := btu.mutation.ExpiresAt(); ok {
		_spec.SetField(banktransfer.FieldExpiresAt, field.TypeTime, value)
	}
	if value, ok := btu.mutation.CreditsToAdd(); ok {
		_spec.SetField(banktransfer.FieldCreditsToAdd, field.TypeInt, value)
	}
	if value, ok := btu.mutation.AddedCreditsToAdd(); ok {
		_spec.AddField(banktransfer.FieldCreditsToAdd, field.TypeInt, value)
	}
	if value, ok := btu.mutation.BankTransactionID(); ok {
		_spec.SetField(banktransfer.FieldBankTransactionID, field.TypeString, value)
	}
	if btu.mutation.BankTransactionIDCleared() {
		_spec.ClearField(banktransfer.FieldBankTransactionID, field.TypeString)
	}
	if value, ok := btu.mutation.ActualAmountReceived(); ok {
		_spec.SetField(banktransfer.FieldActualAmountReceived, field.TypeInt64, value)
	}
	if value, ok := btu.mutation.AddedActualAmountReceived(); ok {
		_spec.AddField(banktransfer.FieldActualAmountReceived, field.TypeInt64, value)
	}
	if btu.mutation.ActualAmountReceivedCleared() {
		_spec.ClearField(banktransfer.FieldActualAmountReceived, field.TypeInt64)
	}
	if value, ok := btu.mutation.ConfirmationMethod(); ok {
		_spec.SetField(banktransfer.FieldConfirmationMethod, field.TypeString, value)
	}
	if value, ok := btu.mutation.ConfirmedBy(); ok {
		_spec.SetField(banktransfer.FieldConfirmedBy, field.TypeUUID, value)
	}
	if btu.mutation.ConfirmedByCleared() {
		_spec.ClearField(banktransfer.FieldConfirmedBy, field.TypeUUID)
	}
	if value, ok := btu.mutation.Metadata(); ok {
		_spec.SetField(banktransfer.FieldMetadata, field.TypeJSON, value)
	}
	if btu.mutation.MetadataCleared() {
		_spec.ClearField(banktransfer.FieldMetadata, field.TypeJSON)
	}
	if value, ok := btu.mutation.UpdatedAt(); ok {
		_spec.SetField(banktransfer.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, btu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{banktransfer.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	btu.mutation.done = true
	return n, nil
}

// BankTransferUpdateOne is the builder for updating a single BankTransfer entity.
type BankTransferUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *BankTransferMutation
}

// SetUserID sets the "user_id" field.
func (btuo *BankTransferUpdateOne) SetUserID(u uuid.UUID) *BankTransferUpdateOne {
	btuo.mutation.SetUserID(u)
	return btuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableUserID(u *uuid.UUID) *BankTransferUpdateOne {
	if u != nil {
		btuo.SetUserID(*u)
	}
	return btuo
}

// SetPlanID sets the "plan_id" field.
func (btuo *BankTransferUpdateOne) SetPlanID(u uuid.UUID) *BankTransferUpdateOne {
	btuo.mutation.SetPlanID(u)
	return btuo
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillablePlanID(u *uuid.UUID) *BankTransferUpdateOne {
	if u != nil {
		btuo.SetPlanID(*u)
	}
	return btuo
}

// ClearPlanID clears the value of the "plan_id" field.
func (btuo *BankTransferUpdateOne) ClearPlanID() *BankTransferUpdateOne {
	btuo.mutation.ClearPlanID()
	return btuo
}

// SetAmount sets the "amount" field.
func (btuo *BankTransferUpdateOne) SetAmount(i int64) *BankTransferUpdateOne {
	btuo.mutation.ResetAmount()
	btuo.mutation.SetAmount(i)
	return btuo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableAmount(i *int64) *BankTransferUpdateOne {
	if i != nil {
		btuo.SetAmount(*i)
	}
	return btuo
}

// AddAmount adds i to the "amount" field.
func (btuo *BankTransferUpdateOne) AddAmount(i int64) *BankTransferUpdateOne {
	btuo.mutation.AddAmount(i)
	return btuo
}

// SetCurrency sets the "currency" field.
func (btuo *BankTransferUpdateOne) SetCurrency(s string) *BankTransferUpdateOne {
	btuo.mutation.SetCurrency(s)
	return btuo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableCurrency(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetCurrency(*s)
	}
	return btuo
}

// SetStatus sets the "status" field.
func (btuo *BankTransferUpdateOne) SetStatus(b banktransfer.Status) *BankTransferUpdateOne {
	btuo.mutation.SetStatus(b)
	return btuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableStatus(b *banktransfer.Status) *BankTransferUpdateOne {
	if b != nil {
		btuo.SetStatus(*b)
	}
	return btuo
}

// SetBankAccount sets the "bank_account" field.
func (btuo *BankTransferUpdateOne) SetBankAccount(s string) *BankTransferUpdateOne {
	btuo.mutation.SetBankAccount(s)
	return btuo
}

// SetNillableBankAccount sets the "bank_account" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableBankAccount(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetBankAccount(*s)
	}
	return btuo
}

// SetReferenceCode sets the "reference_code" field.
func (btuo *BankTransferUpdateOne) SetReferenceCode(s string) *BankTransferUpdateOne {
	btuo.mutation.SetReferenceCode(s)
	return btuo
}

// SetNillableReferenceCode sets the "reference_code" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableReferenceCode(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetReferenceCode(*s)
	}
	return btuo
}

// SetTransferInstructions sets the "transfer_instructions" field.
func (btuo *BankTransferUpdateOne) SetTransferInstructions(s string) *BankTransferUpdateOne {
	btuo.mutation.SetTransferInstructions(s)
	return btuo
}

// SetNillableTransferInstructions sets the "transfer_instructions" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableTransferInstructions(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetTransferInstructions(*s)
	}
	return btuo
}

// SetConfirmedAt sets the "confirmed_at" field.
func (btuo *BankTransferUpdateOne) SetConfirmedAt(t time.Time) *BankTransferUpdateOne {
	btuo.mutation.SetConfirmedAt(t)
	return btuo
}

// SetNillableConfirmedAt sets the "confirmed_at" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableConfirmedAt(t *time.Time) *BankTransferUpdateOne {
	if t != nil {
		btuo.SetConfirmedAt(*t)
	}
	return btuo
}

// ClearConfirmedAt clears the value of the "confirmed_at" field.
func (btuo *BankTransferUpdateOne) ClearConfirmedAt() *BankTransferUpdateOne {
	btuo.mutation.ClearConfirmedAt()
	return btuo
}

// SetExpiresAt sets the "expires_at" field.
func (btuo *BankTransferUpdateOne) SetExpiresAt(t time.Time) *BankTransferUpdateOne {
	btuo.mutation.SetExpiresAt(t)
	return btuo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableExpiresAt(t *time.Time) *BankTransferUpdateOne {
	if t != nil {
		btuo.SetExpiresAt(*t)
	}
	return btuo
}

// SetCreditsToAdd sets the "credits_to_add" field.
func (btuo *BankTransferUpdateOne) SetCreditsToAdd(i int) *BankTransferUpdateOne {
	btuo.mutation.ResetCreditsToAdd()
	btuo.mutation.SetCreditsToAdd(i)
	return btuo
}

// SetNillableCreditsToAdd sets the "credits_to_add" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableCreditsToAdd(i *int) *BankTransferUpdateOne {
	if i != nil {
		btuo.SetCreditsToAdd(*i)
	}
	return btuo
}

// AddCreditsToAdd adds i to the "credits_to_add" field.
func (btuo *BankTransferUpdateOne) AddCreditsToAdd(i int) *BankTransferUpdateOne {
	btuo.mutation.AddCreditsToAdd(i)
	return btuo
}

// SetBankTransactionID sets the "bank_transaction_id" field.
func (btuo *BankTransferUpdateOne) SetBankTransactionID(s string) *BankTransferUpdateOne {
	btuo.mutation.SetBankTransactionID(s)
	return btuo
}

// SetNillableBankTransactionID sets the "bank_transaction_id" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableBankTransactionID(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetBankTransactionID(*s)
	}
	return btuo
}

// ClearBankTransactionID clears the value of the "bank_transaction_id" field.
func (btuo *BankTransferUpdateOne) ClearBankTransactionID() *BankTransferUpdateOne {
	btuo.mutation.ClearBankTransactionID()
	return btuo
}

// SetActualAmountReceived sets the "actual_amount_received" field.
func (btuo *BankTransferUpdateOne) SetActualAmountReceived(i int64) *BankTransferUpdateOne {
	btuo.mutation.ResetActualAmountReceived()
	btuo.mutation.SetActualAmountReceived(i)
	return btuo
}

// SetNillableActualAmountReceived sets the "actual_amount_received" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableActualAmountReceived(i *int64) *BankTransferUpdateOne {
	if i != nil {
		btuo.SetActualAmountReceived(*i)
	}
	return btuo
}

// AddActualAmountReceived adds i to the "actual_amount_received" field.
func (btuo *BankTransferUpdateOne) AddActualAmountReceived(i int64) *BankTransferUpdateOne {
	btuo.mutation.AddActualAmountReceived(i)
	return btuo
}

// ClearActualAmountReceived clears the value of the "actual_amount_received" field.
func (btuo *BankTransferUpdateOne) ClearActualAmountReceived() *BankTransferUpdateOne {
	btuo.mutation.ClearActualAmountReceived()
	return btuo
}

// SetConfirmationMethod sets the "confirmation_method" field.
func (btuo *BankTransferUpdateOne) SetConfirmationMethod(s string) *BankTransferUpdateOne {
	btuo.mutation.SetConfirmationMethod(s)
	return btuo
}

// SetNillableConfirmationMethod sets the "confirmation_method" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableConfirmationMethod(s *string) *BankTransferUpdateOne {
	if s != nil {
		btuo.SetConfirmationMethod(*s)
	}
	return btuo
}

// SetConfirmedBy sets the "confirmed_by" field.
func (btuo *BankTransferUpdateOne) SetConfirmedBy(u uuid.UUID) *BankTransferUpdateOne {
	btuo.mutation.SetConfirmedBy(u)
	return btuo
}

// SetNillableConfirmedBy sets the "confirmed_by" field if the given value is not nil.
func (btuo *BankTransferUpdateOne) SetNillableConfirmedBy(u *uuid.UUID) *BankTransferUpdateOne {
	if u != nil {
		btuo.SetConfirmedBy(*u)
	}
	return btuo
}

// ClearConfirmedBy clears the value of the "confirmed_by" field.
func (btuo *BankTransferUpdateOne) ClearConfirmedBy() *BankTransferUpdateOne {
	btuo.mutation.ClearConfirmedBy()
	return btuo
}

// SetMetadata sets the "metadata" field.
func (btuo *BankTransferUpdateOne) SetMetadata(m map[string]interface{}) *BankTransferUpdateOne {
	btuo.mutation.SetMetadata(m)
	return btuo
}

// ClearMetadata clears the value of the "metadata" field.
func (btuo *BankTransferUpdateOne) ClearMetadata() *BankTransferUpdateOne {
	btuo.mutation.ClearMetadata()
	return btuo
}

// SetUpdatedAt sets the "updated_at" field.
func (btuo *BankTransferUpdateOne) SetUpdatedAt(t time.Time) *BankTransferUpdateOne {
	btuo.mutation.SetUpdatedAt(t)
	return btuo
}

// Mutation returns the BankTransferMutation object of the builder.
func (btuo *BankTransferUpdateOne) Mutation() *BankTransferMutation {
	return btuo.mutation
}

// Where appends a list predicates to the BankTransferUpdate builder.
func (btuo *BankTransferUpdateOne) Where(ps ...predicate.BankTransfer) *BankTransferUpdateOne {
	btuo.mutation.Where(ps...)
	return btuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (btuo *BankTransferUpdateOne) Select(field string, fields ...string) *BankTransferUpdateOne {
	btuo.fields = append([]string{field}, fields...)
	return btuo
}

// Save executes the query and returns the updated BankTransfer entity.
func (btuo *BankTransferUpdateOne) Save(ctx context.Context) (*BankTransfer, error) {
	btuo.defaults()
	return withHooks(ctx, btuo.sqlSave, btuo.mutation, btuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (btuo *BankTransferUpdateOne) SaveX(ctx context.Context) *BankTransfer {
	node, err := btuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (btuo *BankTransferUpdateOne) Exec(ctx context.Context) error {
	_, err := btuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (btuo *BankTransferUpdateOne) ExecX(ctx context.Context) {
	if err := btuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (btuo *BankTransferUpdateOne) defaults() {
	if _, ok := btuo.mutation.UpdatedAt(); !ok {
		v := banktransfer.UpdateDefaultUpdatedAt()
		btuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (btuo *BankTransferUpdateOne) check() error {
	if v, ok := btuo.mutation.Amount(); ok {
		if err := banktransfer.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.amount": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.Currency(); ok {
		if err := banktransfer.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.currency": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.Status(); ok {
		if err := banktransfer.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.status": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.BankAccount(); ok {
		if err := banktransfer.BankAccountValidator(v); err != nil {
			return &ValidationError{Name: "bank_account", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_account": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.ReferenceCode(); ok {
		if err := banktransfer.ReferenceCodeValidator(v); err != nil {
			return &ValidationError{Name: "reference_code", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.reference_code": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.CreditsToAdd(); ok {
		if err := banktransfer.CreditsToAddValidator(v); err != nil {
			return &ValidationError{Name: "credits_to_add", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.credits_to_add": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.BankTransactionID(); ok {
		if err := banktransfer.BankTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "bank_transaction_id", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.bank_transaction_id": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.ActualAmountReceived(); ok {
		if err := banktransfer.ActualAmountReceivedValidator(v); err != nil {
			return &ValidationError{Name: "actual_amount_received", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.actual_amount_received": %w`, err)}
		}
	}
	if v, ok := btuo.mutation.ConfirmationMethod(); ok {
		if err := banktransfer.ConfirmationMethodValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_method", err: fmt.Errorf(`ent: validator failed for field "BankTransfer.confirmation_method": %w`, err)}
		}
	}
	return nil
}

func (btuo *BankTransferUpdateOne) sqlSave(ctx context.Context) (_node *BankTransfer, err error) {
	if err := btuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(banktransfer.Table, banktransfer.Columns, sqlgraph.NewFieldSpec(banktransfer.FieldID, field.TypeUUID))
	id, ok := btuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "BankTransfer.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := btuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, banktransfer.FieldID)
		for _, f := range fields {
			if !banktransfer.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != banktransfer.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := btuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := btuo.mutation.UserID(); ok {
		_spec.SetField(banktransfer.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := btuo.mutation.PlanID(); ok {
		_spec.SetField(banktransfer.FieldPlanID, field.TypeUUID, value)
	}
	if btuo.mutation.PlanIDCleared() {
		_spec.ClearField(banktransfer.FieldPlanID, field.TypeUUID)
	}
	if value, ok := btuo.mutation.Amount(); ok {
		_spec.SetField(banktransfer.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := btuo.mutation.AddedAmount(); ok {
		_spec.AddField(banktransfer.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := btuo.mutation.Currency(); ok {
		_spec.SetField(banktransfer.FieldCurrency, field.TypeString, value)
	}
	if value, ok := btuo.mutation.Status(); ok {
		_spec.SetField(banktransfer.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := btuo.mutation.BankAccount(); ok {
		_spec.SetField(banktransfer.FieldBankAccount, field.TypeString, value)
	}
	if value, ok := btuo.mutation.ReferenceCode(); ok {
		_spec.SetField(banktransfer.FieldReferenceCode, field.TypeString, value)
	}
	if value, ok := btuo.mutation.TransferInstructions(); ok {
		_spec.SetField(banktransfer.FieldTransferInstructions, field.TypeString, value)
	}
	if value, ok := btuo.mutation.ConfirmedAt(); ok {
		_spec.SetField(banktransfer.FieldConfirmedAt, field.TypeTime, value)
	}
	if btuo.mutation.ConfirmedAtCleared() {
		_spec.ClearField(banktransfer.FieldConfirmedAt, field.TypeTime)
	}
	if value, ok := btuo.mutation.ExpiresAt(); ok {
		_spec.SetField(banktransfer.FieldExpiresAt, field.TypeTime, value)
	}
	if value, ok := btuo.mutation.CreditsToAdd(); ok {
		_spec.SetField(banktransfer.FieldCreditsToAdd, field.TypeInt, value)
	}
	if value, ok := btuo.mutation.AddedCreditsToAdd(); ok {
		_spec.AddField(banktransfer.FieldCreditsToAdd, field.TypeInt, value)
	}
	if value, ok := btuo.mutation.BankTransactionID(); ok {
		_spec.SetField(banktransfer.FieldBankTransactionID, field.TypeString, value)
	}
	if btuo.mutation.BankTransactionIDCleared() {
		_spec.ClearField(banktransfer.FieldBankTransactionID, field.TypeString)
	}
	if value, ok := btuo.mutation.ActualAmountReceived(); ok {
		_spec.SetField(banktransfer.FieldActualAmountReceived, field.TypeInt64, value)
	}
	if value, ok := btuo.mutation.AddedActualAmountReceived(); ok {
		_spec.AddField(banktransfer.FieldActualAmountReceived, field.TypeInt64, value)
	}
	if btuo.mutation.ActualAmountReceivedCleared() {
		_spec.ClearField(banktransfer.FieldActualAmountReceived, field.TypeInt64)
	}
	if value, ok := btuo.mutation.ConfirmationMethod(); ok {
		_spec.SetField(banktransfer.FieldConfirmationMethod, field.TypeString, value)
	}
	if value, ok := btuo.mutation.ConfirmedBy(); ok {
		_spec.SetField(banktransfer.FieldConfirmedBy, field.TypeUUID, value)
	}
	if btuo.mutation.ConfirmedByCleared() {
		_spec.ClearField(banktransfer.FieldConfirmedBy, field.TypeUUID)
	}
	if value, ok := btuo.mutation.Metadata(); ok {
		_spec.SetField(banktransfer.FieldMetadata, field.TypeJSON, value)
	}
	if btuo.mutation.MetadataCleared() {
		_spec.ClearField(banktransfer.FieldMetadata, field.TypeJSON)
	}
	if value, ok := btuo.mutation.UpdatedAt(); ok {
		_spec.SetField(banktransfer.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &BankTransfer{config: btuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, btuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{banktransfer.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	btuo.mutation.done = true
	return _node, nil
}

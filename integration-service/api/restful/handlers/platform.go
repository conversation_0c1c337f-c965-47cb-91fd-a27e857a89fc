package handlers

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/integration-service/usecase/platform"
)

// PlatformHandler handles platform-specific REST API endpoints
type PlatformHandler struct {
	*BaseHandler
}

// NewPlatformHandler creates a new platform handler
func NewPlatformHandler(base *BaseHandler) *PlatformHandler {
	return &PlatformHandler{
		BaseHandler: base,
	}
}

// GetPlatformLimits retrieves platform rate limits and usage
// @Summary Get platform limits
// @Description Get rate limits and current usage for a platform
// @Tags platform
// @Accept json
// @Produce json
// @Param request body GetPlatformLimitsRequest true "Platform limits request"
// @Success 200 {object} GetPlatformLimitsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/platform/limits [post]
func (h *PlatformHandler) GetPlatformLimits(c *gin.Context) {
	var req GetPlatformLimitsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if req.Platform == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "missing_required_fields",
			Message: "platform is required",
		})
		return
	}

	// Convert to usecase request
	useCaseReq := &platform.GetPlatformLimitsRequest{
		Platform:  req.Platform,
		AccountID: req.AccountID,
	}

	response, err := h.platformUseCase.GetPlatformLimits(context.Background(), useCaseReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get platform limits")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "platform_limits_failed",
			Message: "Failed to get platform limits",
		})
		return
	}

	c.JSON(http.StatusOK, GetPlatformLimitsResponse{
		Platform:     response.Platform,
		RateLimits:   response.RateLimits,
		CurrentUsage: response.CurrentUsage,
		ResetTimes:   response.ResetTimes,
	})
}

// GetPlatformInfo retrieves general information about supported platforms
// @Summary Get platform information
// @Description Get information about supported platforms and their capabilities
// @Tags platform
// @Accept json
// @Produce json
// @Success 200 {object} PlatformInfoResponse
// @Router /api/v1/platform/info [get]
func (h *PlatformHandler) GetPlatformInfo(c *gin.Context) {
	platforms := []PlatformInfo{
		{
			Name:        "facebook",
			DisplayName: "Facebook",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  true,
				Analytics:   true,
				Webhooks:    true,
				Scheduling:  true,
				ImageUpload: true,
				VideoUpload: false,
			},
			Permissions: []string{
				"pages_manage_posts",
				"pages_read_engagement",
				"pages_show_list",
			},
			RateLimits: map[string]int32{
				"posts_per_hour": 25,
				"api_calls_per_hour": 200,
			},
		},
		{
			Name:        "twitter",
			DisplayName: "Twitter/X",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  true,
				Analytics:   true,
				Webhooks:    true,
				Scheduling:  false,
				ImageUpload: true,
				VideoUpload: true,
			},
			Permissions: []string{
				"tweet.read",
				"tweet.write",
				"users.read",
			},
			RateLimits: map[string]int32{
				"tweets_per_day": 300,
				"api_calls_per_hour": 100,
			},
		},
		{
			Name:        "instagram",
			DisplayName: "Instagram",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  true,
				Analytics:   true,
				Webhooks:    true,
				Scheduling:  true,
				ImageUpload: true,
				VideoUpload: true,
			},
			Permissions: []string{
				"instagram_basic",
				"instagram_content_publish",
				"pages_read_engagement",
			},
			RateLimits: map[string]int32{
				"posts_per_hour": 25,
				"api_calls_per_hour": 200,
			},
		},
		{
			Name:        "linkedin",
			DisplayName: "LinkedIn",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  true,
				Analytics:   true,
				Webhooks:    false,
				Scheduling:  false,
				ImageUpload: true,
				VideoUpload: true,
			},
			Permissions: []string{
				"w_member_social",
				"r_liteprofile",
				"r_organization_social",
			},
			RateLimits: map[string]int32{
				"posts_per_day": 150,
				"api_calls_per_hour": 500,
			},
		},
		{
			Name:        "youtube",
			DisplayName: "YouTube",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  false, // Video upload only
				Analytics:   true,
				Webhooks:    true,
				Scheduling:  true,
				ImageUpload: false,
				VideoUpload: true,
			},
			Permissions: []string{
				"https://www.googleapis.com/auth/youtube.upload",
				"https://www.googleapis.com/auth/youtube.readonly",
			},
			RateLimits: map[string]int32{
				"uploads_per_day": 6,
				"api_calls_per_hour": 10000,
			},
		},
		{
			Name:        "tiktok",
			DisplayName: "TikTok",
			Features: PlatformFeatures{
				OAuth:       true,
				Publishing:  false, // Video upload only
				Analytics:   true,
				Webhooks:    true,
				Scheduling:  false,
				ImageUpload: false,
				VideoUpload: true,
			},
			Permissions: []string{
				"user.info.basic",
				"video.publish",
				"video.list",
			},
			RateLimits: map[string]int32{
				"uploads_per_day": 10,
				"api_calls_per_hour": 100,
			},
		},
	}

	c.JSON(http.StatusOK, PlatformInfoResponse{
		Platforms: platforms,
		Total:     len(platforms),
	})
}

// Platform info types
type PlatformFeatures struct {
	OAuth       bool `json:"oauth"`
	Publishing  bool `json:"publishing"`
	Analytics   bool `json:"analytics"`
	Webhooks    bool `json:"webhooks"`
	Scheduling  bool `json:"scheduling"`
	ImageUpload bool `json:"image_upload"`
	VideoUpload bool `json:"video_upload"`
}

type PlatformInfo struct {
	Name        string                   `json:"name"`
	DisplayName string                   `json:"display_name"`
	Features    PlatformFeatures         `json:"features"`
	Permissions []string                 `json:"permissions"`
	RateLimits  map[string]int32         `json:"rate_limits"`
}

type PlatformInfoResponse struct {
	Platforms []PlatformInfo `json:"platforms"`
	Total     int            `json:"total"`
}

package events

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/social-content-ai/pkg-shared/logging"
)

// SubscriptionEventHandler handles subscription-related events from Kafka
type SubscriptionEventHandler struct {
	logger logging.Logger
}

// NewSubscriptionEventHandler creates a new subscription event handler
func NewSubscriptionEventHandler(logger logging.Logger) *SubscriptionEventHandler {
	return &SubscriptionEventHandler{
		logger: logger,
	}
}

// HandleSubscriptionEvents handles general subscription events from subscription.events topic
func (h *SubscriptionEventHandler) HandleSubscriptionEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.events message")

	var event GeneralSubscriptionEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription event")
		return fmt.Errorf("failed to unmarshal subscription event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"event_type":      event.EventType,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"status":          event.Status,
	}).Info("Processing general subscription event")

	// Handle different event types
	switch event.EventType {
	case EventTypeSubscriptionPaymentCompleted:
		return h.handlePaymentCompletedFromGeneral(ctx, &event)
	case EventTypeSubscriptionPaymentFailed:
		return h.handlePaymentFailedFromGeneral(ctx, &event)
	case EventTypeSubscriptionActivatedKafka:
		return h.handleActivationFromGeneral(ctx, &event)
	case EventTypeSubscriptionCancelledKafka:
		return h.handleCancellationFromGeneral(ctx, &event)
	default:
		h.logger.WithField("event_type", event.EventType).Info("Unhandled subscription event type")
	}

	return nil
}

// HandlePaymentCompletedEvents handles payment completed events from subscription.payment.completed topic
func (h *SubscriptionEventHandler) HandlePaymentCompletedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.payment.completed message")

	var event SubscriptionPaymentCompletedEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal payment completed event")
		return fmt.Errorf("failed to unmarshal payment completed event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"payment_id":      event.PaymentID,
		"amount":          event.Amount,
		"currency":        event.Currency,
	}).Info("Processing payment completed event")

	// TODO: Implement subscription activation logic
	// 1. Update subscription status to active
	// 2. Add credits to user account
	// 3. Send notification to user
	// 4. Update billing cycle

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription activated due to successful payment")
	return nil
}

// HandlePaymentFailedEvents handles payment failed events from subscription.payment.failed topic
func (h *SubscriptionEventHandler) HandlePaymentFailedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.payment.failed message")

	var event SubscriptionPaymentFailedEvent
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal payment failed event")
		return fmt.Errorf("failed to unmarshal payment failed event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"payment_id":      event.PaymentID,
		"failure_reason":  event.FailureReason,
		"amount":          event.Amount,
	}).Info("Processing payment failed event")

	// TODO: Implement subscription suspension logic
	// 1. Update subscription status to payment_failed
	// 2. Send notification to user about payment failure
	// 3. Set retry schedule for payment
	// 4. Suspend subscription if multiple failures

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription suspended due to payment failure")
	return nil
}

// HandleSubscriptionActivatedEvents handles subscription activated events from subscription.activated topic
func (h *SubscriptionEventHandler) HandleSubscriptionActivatedEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.activated message")

	var event SubscriptionActivatedEventKafka
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription activated event")
		return fmt.Errorf("failed to unmarshal subscription activated event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"plan_name":       event.PlanName,
		"credits_added":   event.CreditsAdded,
	}).Info("Processing subscription activated event")

	// TODO: Implement subscription activation logic
	// 1. Update subscription status to active
	// 2. Add plan credits to user account
	// 3. Send welcome notification
	// 4. Set billing cycle dates

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription activated successfully")
	return nil
}

// HandleSubscriptionCancelledEvents handles subscription cancelled events from subscription.cancelled topic
func (h *SubscriptionEventHandler) HandleSubscriptionCancelledEvents(ctx context.Context, message []byte) error {
	h.logger.Info("Processing subscription.cancelled message")

	var event SubscriptionCancelledEventKafka
	if err := json.Unmarshal(message, &event); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal subscription cancelled event")
		return fmt.Errorf("failed to unmarshal subscription cancelled event: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":        event.EventID,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_id":         event.PlanID,
		"reason":          event.Reason,
		"cancelled_at":    event.CancelledAt,
	}).Info("Processing subscription cancelled event")

	// TODO: Implement subscription cancellation logic
	// 1. Update subscription status to cancelled
	// 2. Stop billing cycle
	// 3. Send cancellation confirmation notification
	// 4. Handle refunds if applicable

	h.logger.WithField("subscription_id", event.SubscriptionID).Info("Subscription cancelled successfully")
	return nil
}

// Helper methods for handling events from general topic

func (h *SubscriptionEventHandler) handlePaymentCompletedFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling payment completed from general topic")

	// Convert to specific event type and process
	completedEvent := &SubscriptionPaymentCompletedEvent{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		PaymentID:      event.PaymentID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Amount:         event.Amount,
		Currency:       event.Currency,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as payment completed event
	message, _ := json.Marshal(completedEvent)
	return h.HandlePaymentCompletedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handlePaymentFailedFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling payment failed from general topic")

	// Convert to specific event type and process
	failedEvent := &SubscriptionPaymentFailedEvent{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		PaymentID:      event.PaymentID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Amount:         event.Amount,
		Currency:       event.Currency,
		FailureReason:  event.FailureReason,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as payment failed event
	message, _ := json.Marshal(failedEvent)
	return h.HandlePaymentFailedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handleActivationFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling activation from general topic")

	// Convert to specific event type and process
	activatedEvent := &SubscriptionActivatedEventKafka{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Status:         event.Status,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as activation event
	message, _ := json.Marshal(activatedEvent)
	return h.HandleSubscriptionActivatedEvents(ctx, message)
}

func (h *SubscriptionEventHandler) handleCancellationFromGeneral(ctx context.Context, event *GeneralSubscriptionEvent) error {
	h.logger.WithField("event_id", event.EventID).Info("Handling cancellation from general topic")

	// Convert to specific event type and process
	cancelledEvent := &SubscriptionCancelledEventKafka{
		EventID:        event.EventID,
		EventType:      event.EventType,
		UserID:         event.UserID,
		SubscriptionID: event.SubscriptionID,
		Timestamp:      event.Timestamp,
		Source:         event.Source,
		Version:        event.Version,
		Status:         event.Status,
		Reason:         event.FailureReason, // Use failure reason as cancellation reason
		CancelledAt:    event.Timestamp,
		EventData:      event.EventData,
		Metadata:       event.Metadata,
	}

	// Process as cancellation event
	message, _ := json.Marshal(cancelledEvent)
	return h.HandleSubscriptionCancelledEvents(ctx, message)
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationPreferenceQuery is the builder for querying NotificationPreference entities.
type NotificationPreferenceQuery struct {
	config
	ctx        *QueryContext
	order      []notificationpreference.OrderOption
	inters     []Interceptor
	predicates []predicate.NotificationPreference
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotificationPreferenceQuery builder.
func (npq *NotificationPreferenceQuery) Where(ps ...predicate.NotificationPreference) *NotificationPreferenceQuery {
	npq.predicates = append(npq.predicates, ps...)
	return npq
}

// Limit the number of records to be returned by this query.
func (npq *NotificationPreferenceQuery) Limit(limit int) *NotificationPreferenceQuery {
	npq.ctx.Limit = &limit
	return npq
}

// Offset to start from.
func (npq *NotificationPreferenceQuery) Offset(offset int) *NotificationPreferenceQuery {
	npq.ctx.Offset = &offset
	return npq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (npq *NotificationPreferenceQuery) Unique(unique bool) *NotificationPreferenceQuery {
	npq.ctx.Unique = &unique
	return npq
}

// Order specifies how the records should be ordered.
func (npq *NotificationPreferenceQuery) Order(o ...notificationpreference.OrderOption) *NotificationPreferenceQuery {
	npq.order = append(npq.order, o...)
	return npq
}

// First returns the first NotificationPreference entity from the query.
// Returns a *NotFoundError when no NotificationPreference was found.
func (npq *NotificationPreferenceQuery) First(ctx context.Context) (*NotificationPreference, error) {
	nodes, err := npq.Limit(1).All(setContextOp(ctx, npq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notificationpreference.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) FirstX(ctx context.Context) *NotificationPreference {
	node, err := npq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NotificationPreference ID from the query.
// Returns a *NotFoundError when no NotificationPreference ID was found.
func (npq *NotificationPreferenceQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = npq.Limit(1).IDs(setContextOp(ctx, npq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notificationpreference.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := npq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NotificationPreference entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NotificationPreference entity is found.
// Returns a *NotFoundError when no NotificationPreference entities are found.
func (npq *NotificationPreferenceQuery) Only(ctx context.Context) (*NotificationPreference, error) {
	nodes, err := npq.Limit(2).All(setContextOp(ctx, npq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notificationpreference.Label}
	default:
		return nil, &NotSingularError{notificationpreference.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) OnlyX(ctx context.Context) *NotificationPreference {
	node, err := npq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NotificationPreference ID in the query.
// Returns a *NotSingularError when more than one NotificationPreference ID is found.
// Returns a *NotFoundError when no entities are found.
func (npq *NotificationPreferenceQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = npq.Limit(2).IDs(setContextOp(ctx, npq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notificationpreference.Label}
	default:
		err = &NotSingularError{notificationpreference.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := npq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NotificationPreferences.
func (npq *NotificationPreferenceQuery) All(ctx context.Context) ([]*NotificationPreference, error) {
	ctx = setContextOp(ctx, npq.ctx, ent.OpQueryAll)
	if err := npq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NotificationPreference, *NotificationPreferenceQuery]()
	return withInterceptors[[]*NotificationPreference](ctx, npq, qr, npq.inters)
}

// AllX is like All, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) AllX(ctx context.Context) []*NotificationPreference {
	nodes, err := npq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NotificationPreference IDs.
func (npq *NotificationPreferenceQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if npq.ctx.Unique == nil && npq.path != nil {
		npq.Unique(true)
	}
	ctx = setContextOp(ctx, npq.ctx, ent.OpQueryIDs)
	if err = npq.Select(notificationpreference.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := npq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (npq *NotificationPreferenceQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, npq.ctx, ent.OpQueryCount)
	if err := npq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, npq, querierCount[*NotificationPreferenceQuery](), npq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) CountX(ctx context.Context) int {
	count, err := npq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (npq *NotificationPreferenceQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, npq.ctx, ent.OpQueryExist)
	switch _, err := npq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (npq *NotificationPreferenceQuery) ExistX(ctx context.Context) bool {
	exist, err := npq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotificationPreferenceQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (npq *NotificationPreferenceQuery) Clone() *NotificationPreferenceQuery {
	if npq == nil {
		return nil
	}
	return &NotificationPreferenceQuery{
		config:     npq.config,
		ctx:        npq.ctx.Clone(),
		order:      append([]notificationpreference.OrderOption{}, npq.order...),
		inters:     append([]Interceptor{}, npq.inters...),
		predicates: append([]predicate.NotificationPreference{}, npq.predicates...),
		// clone intermediate query.
		sql:  npq.sql.Clone(),
		path: npq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NotificationPreference.Query().
//		GroupBy(notificationpreference.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (npq *NotificationPreferenceQuery) GroupBy(field string, fields ...string) *NotificationPreferenceGroupBy {
	npq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotificationPreferenceGroupBy{build: npq}
	grbuild.flds = &npq.ctx.Fields
	grbuild.label = notificationpreference.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.NotificationPreference.Query().
//		Select(notificationpreference.FieldUserID).
//		Scan(ctx, &v)
func (npq *NotificationPreferenceQuery) Select(fields ...string) *NotificationPreferenceSelect {
	npq.ctx.Fields = append(npq.ctx.Fields, fields...)
	sbuild := &NotificationPreferenceSelect{NotificationPreferenceQuery: npq}
	sbuild.label = notificationpreference.Label
	sbuild.flds, sbuild.scan = &npq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotificationPreferenceSelect configured with the given aggregations.
func (npq *NotificationPreferenceQuery) Aggregate(fns ...AggregateFunc) *NotificationPreferenceSelect {
	return npq.Select().Aggregate(fns...)
}

func (npq *NotificationPreferenceQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range npq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, npq); err != nil {
				return err
			}
		}
	}
	for _, f := range npq.ctx.Fields {
		if !notificationpreference.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if npq.path != nil {
		prev, err := npq.path(ctx)
		if err != nil {
			return err
		}
		npq.sql = prev
	}
	return nil
}

func (npq *NotificationPreferenceQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NotificationPreference, error) {
	var (
		nodes = []*NotificationPreference{}
		_spec = npq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NotificationPreference).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NotificationPreference{config: npq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, npq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (npq *NotificationPreferenceQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := npq.querySpec()
	_spec.Node.Columns = npq.ctx.Fields
	if len(npq.ctx.Fields) > 0 {
		_spec.Unique = npq.ctx.Unique != nil && *npq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, npq.driver, _spec)
}

func (npq *NotificationPreferenceQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notificationpreference.Table, notificationpreference.Columns, sqlgraph.NewFieldSpec(notificationpreference.FieldID, field.TypeUUID))
	_spec.From = npq.sql
	if unique := npq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if npq.path != nil {
		_spec.Unique = true
	}
	if fields := npq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationpreference.FieldID)
		for i := range fields {
			if fields[i] != notificationpreference.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := npq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := npq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := npq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := npq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (npq *NotificationPreferenceQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(npq.driver.Dialect())
	t1 := builder.Table(notificationpreference.Table)
	columns := npq.ctx.Fields
	if len(columns) == 0 {
		columns = notificationpreference.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if npq.sql != nil {
		selector = npq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if npq.ctx.Unique != nil && *npq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range npq.predicates {
		p(selector)
	}
	for _, p := range npq.order {
		p(selector)
	}
	if offset := npq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := npq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// NotificationPreferenceGroupBy is the group-by builder for NotificationPreference entities.
type NotificationPreferenceGroupBy struct {
	selector
	build *NotificationPreferenceQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (npgb *NotificationPreferenceGroupBy) Aggregate(fns ...AggregateFunc) *NotificationPreferenceGroupBy {
	npgb.fns = append(npgb.fns, fns...)
	return npgb
}

// Scan applies the selector query and scans the result into the given value.
func (npgb *NotificationPreferenceGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, npgb.build.ctx, ent.OpQueryGroupBy)
	if err := npgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationPreferenceQuery, *NotificationPreferenceGroupBy](ctx, npgb.build, npgb, npgb.build.inters, v)
}

func (npgb *NotificationPreferenceGroupBy) sqlScan(ctx context.Context, root *NotificationPreferenceQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(npgb.fns))
	for _, fn := range npgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*npgb.flds)+len(npgb.fns))
		for _, f := range *npgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*npgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := npgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotificationPreferenceSelect is the builder for selecting fields of NotificationPreference entities.
type NotificationPreferenceSelect struct {
	*NotificationPreferenceQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nps *NotificationPreferenceSelect) Aggregate(fns ...AggregateFunc) *NotificationPreferenceSelect {
	nps.fns = append(nps.fns, fns...)
	return nps
}

// Scan applies the selector query and scans the result into the given value.
func (nps *NotificationPreferenceSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nps.ctx, ent.OpQuerySelect)
	if err := nps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationPreferenceQuery, *NotificationPreferenceSelect](ctx, nps.NotificationPreferenceQuery, nps, nps.inters, v)
}

func (nps *NotificationPreferenceSelect) sqlScan(ctx context.Context, root *NotificationPreferenceQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nps.fns))
	for _, fn := range nps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"

	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/pkg-shared/logging"
)

// AuthRequired middleware validates JW<PERSON> token and sets user context
func AuthRequired(userService userv1.AuthServiceClient, logger logging.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// Check Bearer prefix
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		// Extract token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token required",
			})
			c.Abort()
			return
		}

		// Validate token with user service
		req := &userv1.ValidateTokenRequest{
			Token: token,
		}

		resp, err := userService.ValidateToken(context.Background(), req)
		if err != nil {
			logger.WithError(err).Warn("Token validation failed")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		if !resp.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
			})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", resp.UserId)
		c.Set("user_email", resp.Email)
		c.Set("user_role", resp.Role)
		c.Set("session_id", resp.SessionId)

		c.Next()
	}
}

// AdminRequired middleware checks if user has admin role
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User role not found in context",
			})
			c.Abort()
			return
		}

		if userRole != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Admin access required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth middleware validates token if present but doesn't require it
func OptionalAuth(userService userv1.AuthServiceClient, logger logging.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.Next()
			return
		}

		// Try to validate token
		req := &userv1.ValidateTokenRequest{
			Token: token,
		}

		resp, err := userService.ValidateToken(context.Background(), req)
		if err != nil || !resp.Valid {
			// Token is invalid but we don't abort
			c.Next()
			return
		}

		// Set user context if token is valid
		c.Set("user_id", resp.UserId)
		c.Set("user_email", resp.Email)
		c.Set("user_role", resp.Role)
		c.Set("session_id", resp.SessionId)

		c.Next()
	}
}

// GetUserID extracts user ID from context
func GetUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	return userID.(string), true
}

// GetUserEmail extracts user email from context
func GetUserEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("user_email")
	if !exists {
		return "", false
	}
	return email.(string), true
}

// GetUserRole extracts user role from context
func GetUserRole(c *gin.Context) (string, bool) {
	role, exists := c.Get("user_role")
	if !exists {
		return "", false
	}
	return role.(string), true
}

// GetSessionID extracts session ID from context
func GetSessionID(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}
	return sessionID.(string), true
}

// RequireUserID middleware ensures user ID is present in context
func RequireUserID() gin.HandlerFunc {
	return func(c *gin.Context) {
		if _, exists := GetUserID(c); !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User authentication required",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

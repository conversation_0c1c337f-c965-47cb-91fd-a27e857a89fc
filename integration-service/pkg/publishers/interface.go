package publishers

import "context"

// Publisher defines the content publisher interface
type Publisher interface {
	// PublishPost publishes content to the platform
	PublishPost(ctx context.Context, req *PublishRequest) (*PublishResponse, error)
	
	// GetPostAnalytics retrieves analytics for a published post
	GetPostAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error)
	
	// DeletePost deletes a post from the platform
	DeletePost(ctx context.Context, req *DeleteRequest) error
	
	// GetPlatformLimits gets current rate limits for the platform
	GetPlatformLimits(ctx context.Context) (*PlatformLimits, error)
}

// PublishRequest represents a content publishing request
type PublishRequest struct {
	AccessToken     string            `json:"access_token"`
	Content         string            `json:"content"`
	ImageURLs       []string          `json:"image_urls"`
	VideoURL        string            `json:"video_url"`
	Hashtags        []string          `json:"hashtags"`
	ScheduledAt     int64             `json:"scheduled_at"`
	PlatformOptions map[string]string `json:"platform_options"`
}

// PublishResponse represents a content publishing response
type PublishResponse struct {
	PlatformPostID string            `json:"platform_post_id"`
	PlatformURL    string            `json:"platform_url"`
	Success        bool              `json:"success"`
	ErrorMessage   string            `json:"error_message"`
	PublishedAt    int64             `json:"published_at"`
	Metadata       map[string]string `json:"metadata"`
}

// AnalyticsRequest represents an analytics request
type AnalyticsRequest struct {
	AccessToken    string   `json:"access_token"`
	PlatformPostID string   `json:"platform_post_id"`
	Metrics        []string `json:"metrics"`
	DateFrom       string   `json:"date_from"`
	DateTo         string   `json:"date_to"`
}

// AnalyticsResponse represents an analytics response
type AnalyticsResponse struct {
	PlatformPostID  string            `json:"platform_post_id"`
	Metrics         map[string]int64  `json:"metrics"`
	TimeSeries      map[string][]int64 `json:"time_series"`
	CollectedAt     int64             `json:"collected_at"`
	PostPublishedAt int64             `json:"post_published_at"`
}

// DeleteRequest represents a post deletion request
type DeleteRequest struct {
	AccessToken    string `json:"access_token"`
	PlatformPostID string `json:"platform_post_id"`
}

// PlatformLimits represents platform rate limits
type PlatformLimits struct {
	RateLimits   map[string]int32 `json:"rate_limits"`
	CurrentUsage map[string]int32 `json:"current_usage"`
	ResetTimes   map[string]int64 `json:"reset_times"`
}

// ErrorResponse represents a publisher error response
type ErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	ErrorCode        int    `json:"error_code"`
}

syntax = "proto3";

package notification.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/notification/v1;notificationv1";

// NotificationChannel represents a notification delivery channel
enum NotificationChannel {
  NOTIFICATION_CHANNEL_UNSPECIFIED = 0;
  EMAIL = 1;
  SMS = 2;
  PUSH = 3;
  IN_APP = 4;
  WEBHOOK = 5;
}

// NotificationPriority represents notification priority levels
enum NotificationPriority {
  NOTIFICATION_PRIORITY_UNSPECIFIED = 0;
  LOW = 1;
  NORMAL = 2;
  HIGH = 3;
  URGENT = 4;
}

// Notification represents a notification message
message Notification {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;       // Optional workspace context
  string type = 4;               // notification type
  string title = 5;              // Notification title
  string message = 6;            // Notification content
  repeated NotificationChannel channels = 7; // Delivery channels
  NotificationPriority priority = 8;
  map<string, string> data = 9;  // Additional notification data
  string status = 10;            // pending, sent, delivered, failed
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp sent_at = 12;
  google.protobuf.Timestamp delivered_at = 13;
  repeated DeliveryAttempt delivery_attempts = 14;
}

// DeliveryAttempt represents a notification delivery attempt
message DeliveryAttempt {
  string id = 1;
  NotificationChannel channel = 2;
  string status = 3;             // pending, sent, delivered, failed
  string error_message = 4;      // If delivery failed
  google.protobuf.Timestamp attempted_at = 5;
  google.protobuf.Timestamp delivered_at = 6;
  map<string, string> metadata = 7; // Channel-specific metadata
}

// NotificationTemplate represents a notification template
message NotificationTemplate {
  string id = 1;
  string name = 2;
  string type = 3;               // Template type
  string title_template = 4;     // Title template with variables
  string message_template = 5;   // Message template with variables
  repeated NotificationChannel default_channels = 6;
  NotificationPriority default_priority = 7;
  map<string, string> variables = 8; // Template variables
  bool is_active = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}

// SendNotificationRequest for sending a notification
message SendNotificationRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace context
  string type = 3;               // Notification type
  string title = 4;              // Notification title
  string message = 5;            // Notification content
  repeated NotificationChannel channels = 6; // Delivery channels
  NotificationPriority priority = 7;
  map<string, string> data = 8;  // Additional notification data
  bool immediate = 9;            // Send immediately or queue
  google.protobuf.Timestamp scheduled_at = 10; // Optional scheduling
}

// SendNotificationResponse with notification info
message SendNotificationResponse {
  string notification_id = 1;
  string status = 2;             // queued, sent, failed
  repeated string delivery_ids = 3; // Delivery attempt IDs
  google.protobuf.Timestamp created_at = 4;
}

// SendTemplatedNotificationRequest for sending templated notification
message SendTemplatedNotificationRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace context
  string template_id = 3;        // Template to use
  map<string, string> variables = 4; // Template variable values
  repeated NotificationChannel channels = 5; // Override default channels
  NotificationPriority priority = 6; // Override default priority
  map<string, string> data = 7;  // Additional notification data
  bool immediate = 8;            // Send immediately or queue
}

// BulkNotificationRequest for sending bulk notifications
message BulkNotificationRequest {
  repeated string user_ids = 1;
  string workspace_id = 2;       // Optional workspace context
  string type = 3;               // Notification type
  string title = 4;              // Notification title
  string message = 5;            // Notification content
  repeated NotificationChannel channels = 6; // Delivery channels
  NotificationPriority priority = 7;
  map<string, string> data = 8;  // Additional notification data
  bool immediate = 9;            // Send immediately or queue
}

// BulkNotificationResponse with bulk notification info
message BulkNotificationResponse {
  string bulk_id = 1;            // Bulk notification job ID
  int32 total_recipients = 2;    // Total number of recipients
  int32 queued_notifications = 3; // Number of notifications queued
  int32 failed_notifications = 4; // Number of failed notifications
  repeated string notification_ids = 5; // Individual notification IDs
}

// GetNotificationRequest for retrieving notification
message GetNotificationRequest {
  string notification_id = 1;
  string user_id = 2;            // For access control
}

// ListNotificationsRequest for listing user notifications
message ListNotificationsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  common.v1.PaginationRequest pagination = 3;
  string type = 4;               // Filter by notification type
  string status = 5;             // Filter by status
  repeated NotificationChannel channels = 6; // Filter by channels
  bool unread_only = 7;          // Only unread notifications
}

// ListNotificationsResponse with paginated notifications
message ListNotificationsResponse {
  repeated Notification notifications = 1;
  common.v1.PaginationResponse pagination = 2;
  int32 unread_count = 3;        // Total unread notifications
}

// MarkAsReadRequest for marking notifications as read
message MarkAsReadRequest {
  repeated string notification_ids = 1;
  string user_id = 2;
}

// NotificationPreferences represents user notification preferences
message NotificationPreferences {
  string user_id = 1;
  map<string, ChannelPreferences> type_preferences = 2; // type -> preferences
  bool global_enabled = 3;       // Global notification toggle
  repeated string muted_types = 4; // Muted notification types
  string timezone = 5;           // User timezone
  QuietHours quiet_hours = 6;    // Quiet hours settings
}

// ChannelPreferences represents preferences for a notification channel
message ChannelPreferences {
  bool enabled = 1;              // Whether channel is enabled
  repeated NotificationChannel channels = 2; // Enabled channels
  NotificationPriority min_priority = 3; // Minimum priority to send
}

// QuietHours represents quiet hours settings
message QuietHours {
  bool enabled = 1;
  string start_time = 2;         // Format: HH:MM
  string end_time = 3;           // Format: HH:MM
  repeated string days = 4;      // Days of week (monday, tuesday, etc.)
}

// UpdatePreferencesRequest for updating notification preferences
message UpdatePreferencesRequest {
  string user_id = 1;
  NotificationPreferences preferences = 2;
}

// GetPreferencesRequest for retrieving notification preferences
message GetPreferencesRequest {
  string user_id = 1;
}

// KafkaEventRequest for processing Kafka events
message KafkaEventRequest {
  string topic = 1;              // Kafka topic
  string event_type = 2;         // Event type
  string user_id = 3;            // Target user ID
  string workspace_id = 4;       // Optional workspace context
  map<string, string> event_data = 5; // Event-specific data
  google.protobuf.Timestamp event_timestamp = 6;
}

// KafkaEventResponse with processing result
message KafkaEventResponse {
  bool success = 1;
  string notification_id = 2;    // Generated notification ID (if applicable)
  string error_message = 3;      // If processing failed
}

// GetDeliveryStatusRequest for checking delivery status
message GetDeliveryStatusRequest {
  string notification_id = 1;
  string user_id = 2;            // For access control
}

// DeliveryStatus with delivery information
message DeliveryStatus {
  string notification_id = 1;
  string overall_status = 2;     // pending, partial, delivered, failed
  repeated DeliveryAttempt attempts = 3;
  int32 successful_deliveries = 4;
  int32 failed_deliveries = 5;
  google.protobuf.Timestamp last_attempt = 6;
}

// Empty response
message Empty {}

// NotificationService provides notification management operations
service NotificationService {
  // Notification sending operations
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc SendTemplatedNotification(SendTemplatedNotificationRequest) returns (SendNotificationResponse);
  rpc SendBulkNotification(BulkNotificationRequest) returns (BulkNotificationResponse);
  
  // Notification management operations
  rpc GetNotification(GetNotificationRequest) returns (Notification);
  rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse);
  rpc MarkAsRead(MarkAsReadRequest) returns (Empty);
  
  // Delivery status operations
  rpc GetDeliveryStatus(GetDeliveryStatusRequest) returns (DeliveryStatus);
  
  // Preference management operations
  rpc GetPreferences(GetPreferencesRequest) returns (NotificationPreferences);
  rpc UpdatePreferences(UpdatePreferencesRequest) returns (NotificationPreferences);
  
  // Event processing operations
  rpc ProcessKafkaEvent(KafkaEventRequest) returns (KafkaEventResponse);
}

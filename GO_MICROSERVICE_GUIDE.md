
# 🏗️ Hướng dẫn khởi tạo **Go Microservice** mới  
*(Clean Architecture + Ent + gRPC + HTTP)*

> Tài liệu này tổng hợp khung dự án mẫu từ **Auth Service template** và **Generic Microservice template**,  
> lược bỏ mọi phần liên quan đến GraphQL để phù hợp với các service chỉ dùng **gRPC/REST**.  

---

## 1 ️⃣ Chuẩn bị môi trường

| Công cụ | Phiên bản khuyến nghị | Vai trò |
|--------|----------------------|---------|
| **Go** | ≥ 1.24            | Ngôn ngữ lập trình |
| **Ent** | ≥ 0.14              | ORM & code‑gen DB |
| **go‑micro v4** | 4.9+        | Framework service discovery / transport |
| **PostgreSQL** | 14+          | CSDL mặc định |
| **protoc / buf** | mới nhất   | Sinh mã gRPC/protobuf |
| **Docker + Kubernetes** | latest | Đóng gói & triển khai |

*Mẹo*: Cài đặt `golangci-lint`, `goreleaser` và sử dụng một IDE hỗ trợ Go để tăng năng suất.

---

## 2 ️⃣ Khởi tạo project

```bash
export MODULE=github.com/your-org/your-service
mkdir -p $GOPATH/src/$MODULE && cd $_

# Khởi tạo Git & Go module
git init
go mod init $MODULE
```

Nếu code nằm trong repo riêng, thêm:

```bash
go env -w GOPRIVATE=github.com/your-org
```

---

## 3 ️⃣ Sinh cấu trúc thư mục

```text
api/                    # Presentation layer
├── grpc/               # gRPC handlers & protobuf
└── restful/            # (tuỳ chọn) REST handlers

usecase/                # Business logic (Clean Architecture)
├── yourdomain1/
└── yourdomain2/

ent/                    # Data layer (Ent ORM)
└── schema/             # DB schema definitions

pkg/                    # Shared packages (constants, util, mapping, models)

kubernetes/             # Manifests triển khai

server.go               # gRPC entrypoint
web.go                  # REST gateway (tùy chọn)
version.go              # Thông tin build
Dockerfile
Makefile
```

> Thư mục trên tuân theo **Clean Architecture**: *api* (Presentation) → *usecase* (Domain) → *ent* (Data).

---

## 4 ️⃣ Cài đặt dependencies cốt lõi

```bash
# Framework / transport
go get go-micro.dev/v4

# ORM & migration
go get entgo.io/ent/cmd/ent
go get github.com/jackc/pgx/v4

# gRPC & Protobuf
go get google.golang.org/grpc
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Proto-shared (Social Content AI)
go get github.com/social-content-ai/proto-shared

# Router (REST)
go get github.com/go-chi/chi/v5
```

Thêm `github.com/dgrijalva/jwt-go` hoặc `github.com/markbates/goth` nếu service cần authentication.

### 4.1 ️⃣ Sử dụng Proto-shared

**Proto-shared** chứa tất cả gRPC service definitions được chia sẻ giữa các microservices:

```bash
# Import proto-shared vào service của bạn
go get github.com/social-content-ai/proto-shared

# Các package có sẵn:
# - github.com/social-content-ai/proto-shared/user/v1
# - github.com/social-content-ai/proto-shared/ai-content/v1
# - github.com/social-content-ai/proto-shared/content-mgmt/v1
# - github.com/social-content-ai/proto-shared/integration/v1
# - github.com/social-content-ai/proto-shared/asset/v1
# - github.com/social-content-ai/proto-shared/credit/v1
# - github.com/social-content-ai/proto-shared/analytics/v1
# - github.com/social-content-ai/proto-shared/notification/v1
# - github.com/social-content-ai/proto-shared/rag-processing/v1
# - github.com/social-content-ai/proto-shared/data-mgmt/v1
# - github.com/social-content-ai/proto-shared/common/v1
```

---

## 5 ️⃣ Định nghĩa schema Ent

```bash
go run entgo.io/ent/cmd/ent init YourEntity
go run entgo.io/ent/cmd/ent generate --feature privacy ./ent/schema
```

Khuyến nghị các trường mặc định: `id`, `created_at`, `updated_at`, `deleted_at` (soft‑delete).

---

## 6 ️⃣ Thiết kế Use Case & Dependency Injection

**Interface**

```go
// usecase/yourdomain/interface.go
type UseCase interface {
    List(ctx context.Context, req *ListReq, auth *models.AuthInfo) ([]*ent.YourEntity, int, error)
    Create(ctx context.Context, data *CreateReq, auth *models.AuthInfo) error
}
```

**Implementation**

```go
// usecase/yourdomain/service.go
type service struct {
    readDB  *ent.Client
    writeDB *ent.Client
}

func New(read *ent.Client, write *ent.Client) UseCase {
    return &service{readDB: read, writeDB: write}
}
```

Constructor injection giúp **testability** & **loose coupling**.

---

## 7 ️⃣ Ví dụ gRPC handler với Proto-shared

### 7.1 Import Proto-shared Packages

```go
import (
    userv1 "github.com/social-content-ai/proto-shared/user/v1"
    aicontentv1 "github.com/social-content-ai/proto-shared/ai-content/v1"
    contentmgmtv1 "github.com/social-content-ai/proto-shared/content-mgmt/v1"
    creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
    assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
    commonv1 "github.com/social-content-ai/proto-shared/common/v1"
)
```

### 7.2 Implement gRPC Service

```go
type UserServiceGrpc struct {
    userv1.UnimplementedUserServiceServer
    svc yourdomain.UseCase
}

func (g *UserServiceGrpc) CreateUser(ctx context.Context,
    in *userv1.CreateUserRequest) (*userv1.User, error) {

    user, err := g.svc.CreateUser(ctx, mapping.ToCreateUserDomain(in), auth.FromCtx(ctx))
    if err != nil {
        return nil, err
    }
    return mapping.ToUserProto(user), nil
}

func (g *UserServiceGrpc) GetUser(ctx context.Context,
    in *userv1.GetUserRequest) (*userv1.User, error) {

    user, err := g.svc.GetUser(ctx, in.Id, auth.FromCtx(ctx))
    if err != nil {
        return nil, err
    }
    return mapping.ToUserProto(user), nil
}
```

### 7.3 gRPC Client Example

```go
// Tạo gRPC client để gọi services khác
func NewUserServiceClient(conn *grpc.ClientConn) userv1.UserServiceClient {
    return userv1.NewUserServiceClient(conn)
}

// Sử dụng client
func (s *service) ValidateUser(ctx context.Context, userID string) (*userv1.User, error) {
    req := &userv1.GetUserRequest{Id: userID}
    return s.userClient.GetUser(ctx, req)
}
```

Tách mapping request ↔ domain model vào `pkg/mapping`.

---

## 8 ️⃣ Main server & REST gateway (tùy chọn)

```go
func main() {
    // Load config
    cfg := config.Load()

    // Init DB clients
    rdb := ent.NewClient(ent.Driver(sql.OpenDB(dialect.Postgres, pgxPoolRead)))
    wdb := ent.NewClient(ent.Driver(sql.OpenDB(dialect.Postgres, pgxPoolWrite)))

    // Auto‑migration (non‑prod)
    if cfg.Env != "prod" { wdb.Schema.Create(context.TODO()) }

    // Init usecases
    yourSvc := yourdomain.New(rdb, wdb)

    // gRPC service
    srv := micro.NewService(micro.Name("grpc-your-service"), micro.Version(cfg.Version))
    pb.RegisterYourServiceHandler(srv.Server(), grpc.NewYourServiceGrpc(yourSvc))

    // (Optional) REST gateway
    webSrv := web.NewService(web.Address(":8080"))
    webSrv.Handle("/", api.Router(cfg, yourSvc))

    go webSrv.Run()
    srv.Run()
}
```

`go‑micro` cho phép dùng chung middleware (tracing, logging, auth) giữa gRPC và REST.

---

## 9 ️⃣ Makefile & Dockerfile tối thiểu

**Makefile**

```makefile
Date=$(shell date +%FT%T%Z)
Version?=Dev
Service?=your-service

build:
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags "-X 'main.Version=$(Version)' -X 'main.Date=$(Date)'" -o build/$(Service)

docker:
	docker build -t $(Service):$(Version) .
```

**Dockerfile**

```dockerfile
FROM alpine:3.19
RUN apk add --no-cache ca-certificates
ADD build/your-service /your-service
ENTRYPOINT ["/your-service"]
```

Thêm **grpc‑health‑probe** để Kubernetes readiness/liveness nếu cần.

---

## 🔟 Triển khai Kubernetes (phác thảo)

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: your-service
  template:
    metadata:
      labels:
        app: your-service
    spec:
      containers:
      - name: app
        image: your-registry/your-service:${TAG}
        ports:
        - containerPort: 50051 # gRPC
        - containerPort: 8080  # REST (optional)
        readinessProbe:
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:50051"]
```

---

## 1️⃣1️⃣ Best Practices

1. **Separation of Concerns** – API → UseCase → Data.  
2. **Interface‑First** – code trên interface, không concrete.  
3. **Transaction & Soft‑Delete** cho thao tác DB.  
4. **Structured Logging + Trace ID**.  
5. **Unit Tests** ≥ 80 % + integration tests.  
6. **12‑Factor Config** – qua env hoặc config service.  
7. **CI/CD** – `go test`, `make build`, `docker scan`.  

---

> Sau khi hoàn tất khung này, bạn chỉ cần tập trung vào **domain logic**;  
> phần build & deploy đã được chuẩn bị sẵn. Chúc bạn thành công! 🎉

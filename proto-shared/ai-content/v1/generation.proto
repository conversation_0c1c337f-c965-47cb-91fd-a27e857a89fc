syntax = "proto3";

package ai_content.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/ai-content/v1;aicontentv1";

// ContentGenerationRequest for generating content with AI
message ContentGenerationRequest {
  string user_id = 1;
  string topic = 2;
  string content_type = 3;   // educational, promotional, entertainment, etc.
  string tone = 4;           // professional, friendly, humorous, etc.
  string length = 5;         // short, medium, long
  repeated string platforms = 6; // Target platforms
  string model = 7;          // AI model to use (gpt-4, claude, gemini)
  string template_id = 8;    // Optional template ID
  map<string, string> variables = 9; // Template variables
  string workspace_id = 10;  // Workspace context
  bool use_rag = 11;         // Whether to use RAG for context
  repeated string rag_document_ids = 12; // Specific documents for RAG context
  string rag_query = 13;     // Custom RAG query (if different from topic)
  int32 max_rag_chunks = 14; // Maximum RAG chunks to use
  bool validate_credits = 15; // Whether to validate credits before generation
  string generation_purpose = 16; // content_creation, improvement, template_usage
}

// ContentGenerationResponse with generated content
message ContentGenerationResponse {
  string id = 1;
  string content = 2;
  repeated string hashtags = 3;
  repeated string suggested_images = 4;
  int32 credits_used = 5;
  float generation_time = 6;
  string model_used = 7;
  google.protobuf.Timestamp created_at = 8;
  bool rag_used = 9;         // Whether RAG was used
  int32 rag_chunks_used = 10; // Number of RAG chunks used
  repeated string rag_sources = 11; // Source documents used for RAG
  string credit_transaction_id = 12; // Credit transaction reference
  CreditValidation credit_validation = 13; // Credit validation result
}

// GetGenerationRequest for retrieving a generation
message GetGenerationRequest {
  string id = 1;
}

// ListGenerationsRequest for listing user generations
message ListGenerationsRequest {
  string user_id = 1;
  common.v1.PaginationRequest pagination = 2;
  string model = 3;          // Filter by model
  string content_type = 4;   // Filter by content type
}

// ListGenerationsResponse with paginated generations
message ListGenerationsResponse {
  repeated ContentGenerationResponse generations = 1;
  common.v1.PaginationResponse pagination = 2;
}

// RegenerateContentRequest for regenerating content
message RegenerateContentRequest {
  string generation_id = 1;
  string new_topic = 2;      // Optional new topic
  string new_tone = 3;       // Optional new tone
  string new_length = 4;     // Optional new length
}

// CreditValidation contains credit validation information
message CreditValidation {
  bool has_sufficient_credits = 1;
  int32 required_credits = 2;
  int32 current_credits = 3;
  int32 credits_after = 4;      // Credits remaining after operation
  string validation_id = 5;     // Credit validation transaction ID
}

// ValidateCreditsRequest for checking credits before generation
message ValidateCreditsRequest {
  string user_id = 1;
  string model = 2;             // AI model to use
  string generation_type = 3;   // content, image, improvement
  int32 estimated_tokens = 4;   // Estimated token usage
  string workspace_id = 5;      // Workspace context
}

// ValidateCreditsResponse with validation result
message ValidateCreditsResponse {
  CreditValidation validation = 1;
  string error_message = 2;     // If validation failed
}

// RAGContextRequest for retrieving RAG context
message RAGContextRequest {
  string user_id = 1;
  string workspace_id = 2;
  string template_id = 3;       // Optional template filter
  string query = 4;             // RAG query
  int32 max_chunks = 5;         // Maximum chunks to retrieve
  repeated string document_ids = 6; // Specific documents to search
}

// RAGContextResponse with retrieved context
message RAGContextResponse {
  repeated RAGChunk chunks = 1;
  repeated RAGEntity entities = 2;
  string reasoning_path = 3;    // Graph reasoning path
  float relevance_score = 4;    // Overall relevance score
}

// RAGChunk represents a relevant context chunk
message RAGChunk {
  string chunk_id = 1;
  string document_id = 2;
  string content = 3;
  float similarity_score = 4;
  string source_file = 5;       // Original filename
}

// RAGEntity represents an extracted entity
message RAGEntity {
  string entity_id = 1;
  string name = 2;
  string type = 3;              // PERSON, ORG, CONCEPT, etc.
  float confidence = 4;
  string description = 5;
}

// GenerateWithRAGRequest for RAG-enhanced generation
message GenerateWithRAGRequest {
  ContentGenerationRequest generation_request = 1;
  RAGContextRequest rag_request = 2;
  bool validate_credits_first = 3;
}

// GenerateWithRAGResponse with RAG-enhanced content
message GenerateWithRAGResponse {
  ContentGenerationResponse generation_response = 1;
  RAGContextResponse rag_context = 2;
  string combined_prompt = 3;   // Final prompt used for generation
}

// Empty response
message Empty {}

// ContentGenerationService provides AI content generation
service ContentGenerationService {
  rpc GenerateContent(ContentGenerationRequest) returns (ContentGenerationResponse);
  rpc GetGeneration(GetGenerationRequest) returns (ContentGenerationResponse);
  rpc ListGenerations(ListGenerationsRequest) returns (ListGenerationsResponse);
  rpc RegenerateContent(RegenerateContentRequest) returns (ContentGenerationResponse);

  // Credit validation operations
  rpc ValidateCredits(ValidateCreditsRequest) returns (ValidateCreditsResponse);

  // RAG-enhanced generation operations
  rpc GetRAGContext(RAGContextRequest) returns (RAGContextResponse);
  rpc GenerateWithRAG(GenerateWithRAGRequest) returns (GenerateWithRAGResponse);
}

// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AssetsColumns holds the columns for the "assets" table.
	AssetsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "workspace_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "file_name", Type: field.TypeString, Size: 255},
		{Name: "original_name", Type: field.TypeString, Size: 255},
		{Name: "file_type", Type: field.TypeString, Size: 100},
		{Name: "file_extension", Type: field.TypeString, Nullable: true, Size: 10},
		{Name: "s3_key", Type: field.TypeString, Size: 500},
		{Name: "s3_bucket", Type: field.TypeString, Size: 100},
		{Name: "content_hash", Type: field.TypeString, Nullable: true, Size: 64},
		{Name: "purpose", Type: field.TypeString, Size: 50, Default: "general"},
		{Name: "file_size", Type: field.TypeInt64, Default: 0},
		{Name: "thumbnail_url", Type: field.TypeString, Nullable: true, Size: 1000},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeString, Size: 20, Default: "uploaded"},
		{Name: "is_public", Type: field.TypeBool, Default: false},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "folder_id", Type: field.TypeUUID, Nullable: true},
	}
	// AssetsTable holds the schema information for the "assets" table.
	AssetsTable = &schema.Table{
		Name:       "assets",
		Columns:    AssetsColumns,
		PrimaryKey: []*schema.Column{AssetsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "assets_folders_assets",
				Columns:    []*schema.Column{AssetsColumns[19]},
				RefColumns: []*schema.Column{FoldersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "asset_user_id",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[1]},
			},
			{
				Name:    "asset_folder_id",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[19]},
			},
			{
				Name:    "asset_workspace_id",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[2]},
			},
			{
				Name:    "asset_status",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[14]},
			},
			{
				Name:    "asset_file_type",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[5]},
			},
			{
				Name:    "asset_content_hash",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[9]},
			},
			{
				Name:    "asset_is_public",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[15]},
			},
			{
				Name:    "asset_created_at",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[16]},
			},
			{
				Name:    "asset_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[18]},
			},
			{
				Name:    "asset_user_id_folder_id",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[1], AssetsColumns[19]},
			},
			{
				Name:    "asset_user_id_created_at",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[1], AssetsColumns[16]},
			},
			{
				Name:    "asset_user_id_status",
				Unique:  false,
				Columns: []*schema.Column{AssetsColumns[1], AssetsColumns[14]},
			},
		},
	}
	// FoldersColumns holds the columns for the "folders" table.
	FoldersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString, Size: 100},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "path", Type: field.TypeString},
		{Name: "level", Type: field.TypeInt, Default: 0},
		{Name: "is_shared", Type: field.TypeBool, Default: false},
		{Name: "shared_with", Type: field.TypeJSON, Nullable: true},
		{Name: "permissions", Type: field.TypeJSON, Nullable: true},
		{Name: "asset_count", Type: field.TypeInt64, Default: 0},
		{Name: "total_size", Type: field.TypeInt64, Default: 0},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "parent_id", Type: field.TypeUUID, Nullable: true},
	}
	// FoldersTable holds the schema information for the "folders" table.
	FoldersTable = &schema.Table{
		Name:       "folders",
		Columns:    FoldersColumns,
		PrimaryKey: []*schema.Column{FoldersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "folders_folders_children",
				Columns:    []*schema.Column{FoldersColumns[14]},
				RefColumns: []*schema.Column{FoldersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "folder_user_id",
				Unique:  false,
				Columns: []*schema.Column{FoldersColumns[1]},
			},
			{
				Name:    "folder_parent_id",
				Unique:  false,
				Columns: []*schema.Column{FoldersColumns[14]},
			},
			{
				Name:    "folder_path",
				Unique:  false,
				Columns: []*schema.Column{FoldersColumns[4]},
			},
			{
				Name:    "folder_user_id_parent_id",
				Unique:  false,
				Columns: []*schema.Column{FoldersColumns[1], FoldersColumns[14]},
			},
			{
				Name:    "folder_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{FoldersColumns[13]},
			},
			{
				Name:    "folder_user_id_parent_id_name",
				Unique:  true,
				Columns: []*schema.Column{FoldersColumns[1], FoldersColumns[14], FoldersColumns[2]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AssetsTable,
		FoldersTable,
	}
)

func init() {
	AssetsTable.ForeignKeys[0].RefTable = FoldersTable
	FoldersTable.ForeignKeys[0].RefTable = FoldersTable
}

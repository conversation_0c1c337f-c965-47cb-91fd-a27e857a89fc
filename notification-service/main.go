package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/notification-service/api/grpc/handlers"
	"github.com/social-content-ai/notification-service/api/restful"
	"github.com/social-content-ai/notification-service/config"
	"github.com/social-content-ai/notification-service/pkg/adapters"
	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/notification-service/pkg/database"
	"github.com/social-content-ai/notification-service/pkg/email"
	eventHandlers "github.com/social-content-ai/notification-service/pkg/handlers"
	localKafka "github.com/social-content-ai/notification-service/pkg/kafka"
	"github.com/social-content-ai/notification-service/pkg/push"
	"github.com/social-content-ai/notification-service/pkg/sms"
	"github.com/social-content-ai/notification-service/pkg/websocket"
	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/notification-service/usecase/template"

	"github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/pkg-shared/metrics"
	"github.com/social-content-ai/pkg-shared/tracing"

	notificationv1 "github.com/social-content-ai/proto-shared/notification/v1"
)

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithField("service", "notification-service").Info("Starting notification service")

	// Initialize tracing
	tracer, err := tracing.NewTracer("notification-service")
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize tracer")
	}
	defer tracer.Close()

	// Initialize metrics
	metricsServer := metrics.NewServer(fmt.Sprintf("%d", cfg.Monitoring.MetricsPort))
	go func() {
		if err := metricsServer.Start(); err != nil {
			logger.WithError(err).Error("Failed to start metrics server")
		}
	}()

	// Initialize database manager
	dbManager := database.NewDatabaseManager(&cfg.Database, logger)

	// Connect to database
	readDB, writeDB, err := dbManager.ConnectReadWrite(ctx)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to database")
	}
	defer dbManager.Close(readDB, writeDB)

	// Run database migrations
	if err := dbManager.Migrate(ctx, writeDB); err != nil {
		logger.WithError(err).Fatal("Failed to run migrations")
	}

	// Initialize email sender
	smtpConfig := &email.SMTPConfig{
		Host:     cfg.Email.SMTPHost,
		Port:     cfg.Email.SMTPPort,
		Username: cfg.Email.Username,
		Password: cfg.Email.Password,
		From:     cfg.Email.FromEmail,
		FromName: cfg.Email.FromName,
		UseTLS:   cfg.Email.UseTLS,
	}
	smtpSender := email.NewSMTPSender(smtpConfig, logger)
	emailSender := adapters.NewEmailAdapter(smtpSender)

	// Initialize SMS sender
	var smsSender channels.SMSSender
	if cfg.Features.SMSNotifications {
		twilioConfig := sms.TwilioConfig{
			AccountSID: cfg.SMS.APIKey,
			AuthToken:  cfg.SMS.APISecret,
			FromNumber: cfg.SMS.FromNumber,
		}
		smsSender = sms.NewTwilioSender(twilioConfig, logger)
	} else {
		smsSender = sms.NewMockSender(logger)
	}

	// Initialize push sender
	var pushSender channels.PushSender
	if cfg.Features.PushNotifications {
		fcmConfig := push.FCMConfig{
			ServerKey: cfg.Push.APIKey,
		}
		pushSender = push.NewFCMSender(fcmConfig, logger)
	} else {
		pushSender = push.NewMockSender(logger)
	}

	// Initialize WebSocket hub
	var wsHub *websocket.Hub
	if cfg.WebSocket.Enabled {
		wsHub = websocket.NewHub(ctx, logger)
		go wsHub.Run()
	}

	// TODO: Initialize external service clients when needed
	// userConn, err := grpc.NewClient(fmt.Sprintf("localhost:%d", 50050), grpc.WithTransportCredentials(insecure.NewCredentials()))
	// if err != nil {
	// 	logger.WithError(err).Fatal("Failed to connect to user service")
	// }
	// defer userConn.Close()
	// userClient := userv1.NewAuthServiceClient(userConn)

	// Initialize use cases with real implementations
	templateUseCase := template.NewService(readDB, writeDB, logger)
	preferenceUseCase := preference.NewService(readDB, writeDB, logger)
	notificationUseCase := notification.NewService(readDB, writeDB, emailSender, smsSender, pushSender, wsHub, templateUseCase, logger)

	// Initialize Kafka event-driven notifications if enabled
	var kafkaConsumer *kafka.Consumer
	if cfg.Features.EventDrivenNotifications {
		logger.Info("Initializing Kafka event-driven notifications")

		// Create Kafka configuration
		kafkaConfig := localKafka.NewNotificationConfig()
		kafkaConfig.Brokers = cfg.Kafka.Brokers
		kafkaConfig.Consumer.GroupID = "notification-service"

		// Create event handler
		eventHandler := eventHandlers.NewKafkaEventHandler(notificationUseCase, logger)

		// Create Kafka consumer
		kafkaConsumer = kafka.NewConsumer(kafkaConfig, eventHandler, logger)

		// Start consuming events in background
		go func() {
			topics := eventHandler.GetSupportedTopics()
			logger.WithField("topics", topics).Info("Starting Kafka consumer for event-driven notifications")
			if err := kafkaConsumer.Start(topics); err != nil {
				logger.WithError(err).Error("Failed to start Kafka consumer")
			}
		}()
	} else {
		logger.Info("Event-driven notifications disabled in configuration")
	}

	// Initialize gRPC server
	grpcServer := grpc.NewServer()
	notificationv1.RegisterNotificationServiceServer(grpcServer, handlers.NewSimpleNotificationHandler(
		notificationUseCase,
		preferenceUseCase,
		templateUseCase,
		logger,
	))

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())

	restful.SetupRoutes(httpRouter, notificationUseCase, preferenceUseCase, templateUseCase, wsHub, nil, logger)

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start HTTP server
	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler: httpRouter,
	}

	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down servers...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Servers stopped")
}

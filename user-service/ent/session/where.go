// Code generated by ent, DO NOT EDIT.

package session

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUserID, v))
}

// DeviceInfo applies equality check predicate on the "device_info" field. It's identical to DeviceInfoEQ.
func DeviceInfo(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldDeviceInfo, v))
}

// IPAddress applies equality check predicate on the "ip_address" field. It's identical to IPAddressEQ.
func IPAddress(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldIPAddress, v))
}

// LoginTime applies equality check predicate on the "login_time" field. It's identical to LoginTimeEQ.
func LoginTime(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldLoginTime, v))
}

// LogoutTime applies equality check predicate on the "logout_time" field. It's identical to LogoutTimeEQ.
func LogoutTime(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldLogoutTime, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldExpiresAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldUserID, vs...))
}

// DeviceInfoEQ applies the EQ predicate on the "device_info" field.
func DeviceInfoEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldDeviceInfo, v))
}

// DeviceInfoNEQ applies the NEQ predicate on the "device_info" field.
func DeviceInfoNEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldDeviceInfo, v))
}

// DeviceInfoIn applies the In predicate on the "device_info" field.
func DeviceInfoIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldDeviceInfo, vs...))
}

// DeviceInfoNotIn applies the NotIn predicate on the "device_info" field.
func DeviceInfoNotIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldDeviceInfo, vs...))
}

// DeviceInfoGT applies the GT predicate on the "device_info" field.
func DeviceInfoGT(v string) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldDeviceInfo, v))
}

// DeviceInfoGTE applies the GTE predicate on the "device_info" field.
func DeviceInfoGTE(v string) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldDeviceInfo, v))
}

// DeviceInfoLT applies the LT predicate on the "device_info" field.
func DeviceInfoLT(v string) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldDeviceInfo, v))
}

// DeviceInfoLTE applies the LTE predicate on the "device_info" field.
func DeviceInfoLTE(v string) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldDeviceInfo, v))
}

// DeviceInfoContains applies the Contains predicate on the "device_info" field.
func DeviceInfoContains(v string) predicate.Session {
	return predicate.Session(sql.FieldContains(FieldDeviceInfo, v))
}

// DeviceInfoHasPrefix applies the HasPrefix predicate on the "device_info" field.
func DeviceInfoHasPrefix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasPrefix(FieldDeviceInfo, v))
}

// DeviceInfoHasSuffix applies the HasSuffix predicate on the "device_info" field.
func DeviceInfoHasSuffix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasSuffix(FieldDeviceInfo, v))
}

// DeviceInfoIsNil applies the IsNil predicate on the "device_info" field.
func DeviceInfoIsNil() predicate.Session {
	return predicate.Session(sql.FieldIsNull(FieldDeviceInfo))
}

// DeviceInfoNotNil applies the NotNil predicate on the "device_info" field.
func DeviceInfoNotNil() predicate.Session {
	return predicate.Session(sql.FieldNotNull(FieldDeviceInfo))
}

// DeviceInfoEqualFold applies the EqualFold predicate on the "device_info" field.
func DeviceInfoEqualFold(v string) predicate.Session {
	return predicate.Session(sql.FieldEqualFold(FieldDeviceInfo, v))
}

// DeviceInfoContainsFold applies the ContainsFold predicate on the "device_info" field.
func DeviceInfoContainsFold(v string) predicate.Session {
	return predicate.Session(sql.FieldContainsFold(FieldDeviceInfo, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldStatus, vs...))
}

// IPAddressEQ applies the EQ predicate on the "ip_address" field.
func IPAddressEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldIPAddress, v))
}

// IPAddressNEQ applies the NEQ predicate on the "ip_address" field.
func IPAddressNEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldIPAddress, v))
}

// IPAddressIn applies the In predicate on the "ip_address" field.
func IPAddressIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldIPAddress, vs...))
}

// IPAddressNotIn applies the NotIn predicate on the "ip_address" field.
func IPAddressNotIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldIPAddress, vs...))
}

// IPAddressGT applies the GT predicate on the "ip_address" field.
func IPAddressGT(v string) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldIPAddress, v))
}

// IPAddressGTE applies the GTE predicate on the "ip_address" field.
func IPAddressGTE(v string) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldIPAddress, v))
}

// IPAddressLT applies the LT predicate on the "ip_address" field.
func IPAddressLT(v string) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldIPAddress, v))
}

// IPAddressLTE applies the LTE predicate on the "ip_address" field.
func IPAddressLTE(v string) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldIPAddress, v))
}

// IPAddressContains applies the Contains predicate on the "ip_address" field.
func IPAddressContains(v string) predicate.Session {
	return predicate.Session(sql.FieldContains(FieldIPAddress, v))
}

// IPAddressHasPrefix applies the HasPrefix predicate on the "ip_address" field.
func IPAddressHasPrefix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasPrefix(FieldIPAddress, v))
}

// IPAddressHasSuffix applies the HasSuffix predicate on the "ip_address" field.
func IPAddressHasSuffix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasSuffix(FieldIPAddress, v))
}

// IPAddressIsNil applies the IsNil predicate on the "ip_address" field.
func IPAddressIsNil() predicate.Session {
	return predicate.Session(sql.FieldIsNull(FieldIPAddress))
}

// IPAddressNotNil applies the NotNil predicate on the "ip_address" field.
func IPAddressNotNil() predicate.Session {
	return predicate.Session(sql.FieldNotNull(FieldIPAddress))
}

// IPAddressEqualFold applies the EqualFold predicate on the "ip_address" field.
func IPAddressEqualFold(v string) predicate.Session {
	return predicate.Session(sql.FieldEqualFold(FieldIPAddress, v))
}

// IPAddressContainsFold applies the ContainsFold predicate on the "ip_address" field.
func IPAddressContainsFold(v string) predicate.Session {
	return predicate.Session(sql.FieldContainsFold(FieldIPAddress, v))
}

// LoginTimeEQ applies the EQ predicate on the "login_time" field.
func LoginTimeEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldLoginTime, v))
}

// LoginTimeNEQ applies the NEQ predicate on the "login_time" field.
func LoginTimeNEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldLoginTime, v))
}

// LoginTimeIn applies the In predicate on the "login_time" field.
func LoginTimeIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldLoginTime, vs...))
}

// LoginTimeNotIn applies the NotIn predicate on the "login_time" field.
func LoginTimeNotIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldLoginTime, vs...))
}

// LoginTimeGT applies the GT predicate on the "login_time" field.
func LoginTimeGT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldLoginTime, v))
}

// LoginTimeGTE applies the GTE predicate on the "login_time" field.
func LoginTimeGTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldLoginTime, v))
}

// LoginTimeLT applies the LT predicate on the "login_time" field.
func LoginTimeLT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldLoginTime, v))
}

// LoginTimeLTE applies the LTE predicate on the "login_time" field.
func LoginTimeLTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldLoginTime, v))
}

// LogoutTimeEQ applies the EQ predicate on the "logout_time" field.
func LogoutTimeEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldLogoutTime, v))
}

// LogoutTimeNEQ applies the NEQ predicate on the "logout_time" field.
func LogoutTimeNEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldLogoutTime, v))
}

// LogoutTimeIn applies the In predicate on the "logout_time" field.
func LogoutTimeIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldLogoutTime, vs...))
}

// LogoutTimeNotIn applies the NotIn predicate on the "logout_time" field.
func LogoutTimeNotIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldLogoutTime, vs...))
}

// LogoutTimeGT applies the GT predicate on the "logout_time" field.
func LogoutTimeGT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldLogoutTime, v))
}

// LogoutTimeGTE applies the GTE predicate on the "logout_time" field.
func LogoutTimeGTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldLogoutTime, v))
}

// LogoutTimeLT applies the LT predicate on the "logout_time" field.
func LogoutTimeLT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldLogoutTime, v))
}

// LogoutTimeLTE applies the LTE predicate on the "logout_time" field.
func LogoutTimeLTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldLogoutTime, v))
}

// LogoutTimeIsNil applies the IsNil predicate on the "logout_time" field.
func LogoutTimeIsNil() predicate.Session {
	return predicate.Session(sql.FieldIsNull(FieldLogoutTime))
}

// LogoutTimeNotNil applies the NotNil predicate on the "logout_time" field.
func LogoutTimeNotNil() predicate.Session {
	return predicate.Session(sql.FieldNotNull(FieldLogoutTime))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldExpiresAt, v))
}

// ExpiresAtIsNil applies the IsNil predicate on the "expires_at" field.
func ExpiresAtIsNil() predicate.Session {
	return predicate.Session(sql.FieldIsNull(FieldExpiresAt))
}

// ExpiresAtNotNil applies the NotNil predicate on the "expires_at" field.
func ExpiresAtNotNil() predicate.Session {
	return predicate.Session(sql.FieldNotNull(FieldExpiresAt))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.Session {
	return predicate.Session(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.Session {
	return predicate.Session(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Session) predicate.Session {
	return predicate.Session(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Session) predicate.Session {
	return predicate.Session(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Session) predicate.Session {
	return predicate.Session(sql.NotPredicates(p))
}

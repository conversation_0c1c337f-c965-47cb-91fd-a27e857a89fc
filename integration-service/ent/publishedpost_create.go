// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// PublishedPostCreate is the builder for creating a PublishedPost entity.
type PublishedPostCreate struct {
	config
	mutation *PublishedPostMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (ppc *PublishedPostCreate) SetUserID(u uuid.UUID) *PublishedPostCreate {
	ppc.mutation.SetUserID(u)
	return ppc
}

// SetPostID sets the "post_id" field.
func (ppc *PublishedPostCreate) SetPostID(s string) *PublishedPostCreate {
	ppc.mutation.SetPostID(s)
	return ppc
}

// SetIntegrationID sets the "integration_id" field.
func (ppc *PublishedPostCreate) SetIntegrationID(s string) *PublishedPostCreate {
	ppc.mutation.SetIntegrationID(s)
	return ppc
}

// SetPlatform sets the "platform" field.
func (ppc *PublishedPostCreate) SetPlatform(pu publishedpost.Platform) *PublishedPostCreate {
	ppc.mutation.SetPlatform(pu)
	return ppc
}

// SetPlatformPostID sets the "platform_post_id" field.
func (ppc *PublishedPostCreate) SetPlatformPostID(s string) *PublishedPostCreate {
	ppc.mutation.SetPlatformPostID(s)
	return ppc
}

// SetPlatformPostURL sets the "platform_post_url" field.
func (ppc *PublishedPostCreate) SetPlatformPostURL(s string) *PublishedPostCreate {
	ppc.mutation.SetPlatformPostURL(s)
	return ppc
}

// SetNillablePlatformPostURL sets the "platform_post_url" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillablePlatformPostURL(s *string) *PublishedPostCreate {
	if s != nil {
		ppc.SetPlatformPostURL(*s)
	}
	return ppc
}

// SetContent sets the "content" field.
func (ppc *PublishedPostCreate) SetContent(s string) *PublishedPostCreate {
	ppc.mutation.SetContent(s)
	return ppc
}

// SetMediaUrls sets the "media_urls" field.
func (ppc *PublishedPostCreate) SetMediaUrls(s []string) *PublishedPostCreate {
	ppc.mutation.SetMediaUrls(s)
	return ppc
}

// SetHashtags sets the "hashtags" field.
func (ppc *PublishedPostCreate) SetHashtags(s []string) *PublishedPostCreate {
	ppc.mutation.SetHashtags(s)
	return ppc
}

// SetStatus sets the "status" field.
func (ppc *PublishedPostCreate) SetStatus(pu publishedpost.Status) *PublishedPostCreate {
	ppc.mutation.SetStatus(pu)
	return ppc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableStatus(pu *publishedpost.Status) *PublishedPostCreate {
	if pu != nil {
		ppc.SetStatus(*pu)
	}
	return ppc
}

// SetErrorMessage sets the "error_message" field.
func (ppc *PublishedPostCreate) SetErrorMessage(s string) *PublishedPostCreate {
	ppc.mutation.SetErrorMessage(s)
	return ppc
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableErrorMessage(s *string) *PublishedPostCreate {
	if s != nil {
		ppc.SetErrorMessage(*s)
	}
	return ppc
}

// SetScheduledAt sets the "scheduled_at" field.
func (ppc *PublishedPostCreate) SetScheduledAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetScheduledAt(t)
	return ppc
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableScheduledAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetScheduledAt(*t)
	}
	return ppc
}

// SetPublishedAt sets the "published_at" field.
func (ppc *PublishedPostCreate) SetPublishedAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetPublishedAt(t)
	return ppc
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillablePublishedAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetPublishedAt(*t)
	}
	return ppc
}

// SetPlatformResponse sets the "platform_response" field.
func (ppc *PublishedPostCreate) SetPlatformResponse(m map[string]interface{}) *PublishedPostCreate {
	ppc.mutation.SetPlatformResponse(m)
	return ppc
}

// SetAnalytics sets the "analytics" field.
func (ppc *PublishedPostCreate) SetAnalytics(m map[string]interface{}) *PublishedPostCreate {
	ppc.mutation.SetAnalytics(m)
	return ppc
}

// SetLastAnalyticsSync sets the "last_analytics_sync" field.
func (ppc *PublishedPostCreate) SetLastAnalyticsSync(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetLastAnalyticsSync(t)
	return ppc
}

// SetNillableLastAnalyticsSync sets the "last_analytics_sync" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableLastAnalyticsSync(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetLastAnalyticsSync(*t)
	}
	return ppc
}

// SetRetryCount sets the "retry_count" field.
func (ppc *PublishedPostCreate) SetRetryCount(i int) *PublishedPostCreate {
	ppc.mutation.SetRetryCount(i)
	return ppc
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableRetryCount(i *int) *PublishedPostCreate {
	if i != nil {
		ppc.SetRetryCount(*i)
	}
	return ppc
}

// SetNextRetryAt sets the "next_retry_at" field.
func (ppc *PublishedPostCreate) SetNextRetryAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetNextRetryAt(t)
	return ppc
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableNextRetryAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetNextRetryAt(*t)
	}
	return ppc
}

// SetPublishOptions sets the "publish_options" field.
func (ppc *PublishedPostCreate) SetPublishOptions(m map[string]interface{}) *PublishedPostCreate {
	ppc.mutation.SetPublishOptions(m)
	return ppc
}

// SetMetadata sets the "metadata" field.
func (ppc *PublishedPostCreate) SetMetadata(m map[string]interface{}) *PublishedPostCreate {
	ppc.mutation.SetMetadata(m)
	return ppc
}

// SetCreatedAt sets the "created_at" field.
func (ppc *PublishedPostCreate) SetCreatedAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetCreatedAt(t)
	return ppc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableCreatedAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetCreatedAt(*t)
	}
	return ppc
}

// SetUpdatedAt sets the "updated_at" field.
func (ppc *PublishedPostCreate) SetUpdatedAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetUpdatedAt(t)
	return ppc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableUpdatedAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetUpdatedAt(*t)
	}
	return ppc
}

// SetDeletedAt sets the "deleted_at" field.
func (ppc *PublishedPostCreate) SetDeletedAt(t time.Time) *PublishedPostCreate {
	ppc.mutation.SetDeletedAt(t)
	return ppc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableDeletedAt(t *time.Time) *PublishedPostCreate {
	if t != nil {
		ppc.SetDeletedAt(*t)
	}
	return ppc
}

// SetID sets the "id" field.
func (ppc *PublishedPostCreate) SetID(u uuid.UUID) *PublishedPostCreate {
	ppc.mutation.SetID(u)
	return ppc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ppc *PublishedPostCreate) SetNillableID(u *uuid.UUID) *PublishedPostCreate {
	if u != nil {
		ppc.SetID(*u)
	}
	return ppc
}

// Mutation returns the PublishedPostMutation object of the builder.
func (ppc *PublishedPostCreate) Mutation() *PublishedPostMutation {
	return ppc.mutation
}

// Save creates the PublishedPost in the database.
func (ppc *PublishedPostCreate) Save(ctx context.Context) (*PublishedPost, error) {
	ppc.defaults()
	return withHooks(ctx, ppc.sqlSave, ppc.mutation, ppc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ppc *PublishedPostCreate) SaveX(ctx context.Context) *PublishedPost {
	v, err := ppc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ppc *PublishedPostCreate) Exec(ctx context.Context) error {
	_, err := ppc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ppc *PublishedPostCreate) ExecX(ctx context.Context) {
	if err := ppc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ppc *PublishedPostCreate) defaults() {
	if _, ok := ppc.mutation.Status(); !ok {
		v := publishedpost.DefaultStatus
		ppc.mutation.SetStatus(v)
	}
	if _, ok := ppc.mutation.RetryCount(); !ok {
		v := publishedpost.DefaultRetryCount
		ppc.mutation.SetRetryCount(v)
	}
	if _, ok := ppc.mutation.CreatedAt(); !ok {
		v := publishedpost.DefaultCreatedAt()
		ppc.mutation.SetCreatedAt(v)
	}
	if _, ok := ppc.mutation.UpdatedAt(); !ok {
		v := publishedpost.DefaultUpdatedAt()
		ppc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ppc.mutation.ID(); !ok {
		v := publishedpost.DefaultID()
		ppc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ppc *PublishedPostCreate) check() error {
	if _, ok := ppc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "PublishedPost.user_id"`)}
	}
	if _, ok := ppc.mutation.PostID(); !ok {
		return &ValidationError{Name: "post_id", err: errors.New(`ent: missing required field "PublishedPost.post_id"`)}
	}
	if v, ok := ppc.mutation.PostID(); ok {
		if err := publishedpost.PostIDValidator(v); err != nil {
			return &ValidationError{Name: "post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.post_id": %w`, err)}
		}
	}
	if _, ok := ppc.mutation.IntegrationID(); !ok {
		return &ValidationError{Name: "integration_id", err: errors.New(`ent: missing required field "PublishedPost.integration_id"`)}
	}
	if v, ok := ppc.mutation.IntegrationID(); ok {
		if err := publishedpost.IntegrationIDValidator(v); err != nil {
			return &ValidationError{Name: "integration_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.integration_id": %w`, err)}
		}
	}
	if _, ok := ppc.mutation.Platform(); !ok {
		return &ValidationError{Name: "platform", err: errors.New(`ent: missing required field "PublishedPost.platform"`)}
	}
	if v, ok := ppc.mutation.Platform(); ok {
		if err := publishedpost.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform": %w`, err)}
		}
	}
	if _, ok := ppc.mutation.PlatformPostID(); !ok {
		return &ValidationError{Name: "platform_post_id", err: errors.New(`ent: missing required field "PublishedPost.platform_post_id"`)}
	}
	if v, ok := ppc.mutation.PlatformPostID(); ok {
		if err := publishedpost.PlatformPostIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_id", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_id": %w`, err)}
		}
	}
	if v, ok := ppc.mutation.PlatformPostURL(); ok {
		if err := publishedpost.PlatformPostURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_post_url", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.platform_post_url": %w`, err)}
		}
	}
	if _, ok := ppc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "PublishedPost.content"`)}
	}
	if _, ok := ppc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "PublishedPost.status"`)}
	}
	if v, ok := ppc.mutation.Status(); ok {
		if err := publishedpost.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.status": %w`, err)}
		}
	}
	if v, ok := ppc.mutation.ErrorMessage(); ok {
		if err := publishedpost.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PublishedPost.error_message": %w`, err)}
		}
	}
	if _, ok := ppc.mutation.RetryCount(); !ok {
		return &ValidationError{Name: "retry_count", err: errors.New(`ent: missing required field "PublishedPost.retry_count"`)}
	}
	if _, ok := ppc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "PublishedPost.created_at"`)}
	}
	if _, ok := ppc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "PublishedPost.updated_at"`)}
	}
	return nil
}

func (ppc *PublishedPostCreate) sqlSave(ctx context.Context) (*PublishedPost, error) {
	if err := ppc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ppc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ppc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ppc.mutation.id = &_node.ID
	ppc.mutation.done = true
	return _node, nil
}

func (ppc *PublishedPostCreate) createSpec() (*PublishedPost, *sqlgraph.CreateSpec) {
	var (
		_node = &PublishedPost{config: ppc.config}
		_spec = sqlgraph.NewCreateSpec(publishedpost.Table, sqlgraph.NewFieldSpec(publishedpost.FieldID, field.TypeUUID))
	)
	if id, ok := ppc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ppc.mutation.UserID(); ok {
		_spec.SetField(publishedpost.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := ppc.mutation.PostID(); ok {
		_spec.SetField(publishedpost.FieldPostID, field.TypeString, value)
		_node.PostID = value
	}
	if value, ok := ppc.mutation.IntegrationID(); ok {
		_spec.SetField(publishedpost.FieldIntegrationID, field.TypeString, value)
		_node.IntegrationID = value
	}
	if value, ok := ppc.mutation.Platform(); ok {
		_spec.SetField(publishedpost.FieldPlatform, field.TypeEnum, value)
		_node.Platform = value
	}
	if value, ok := ppc.mutation.PlatformPostID(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostID, field.TypeString, value)
		_node.PlatformPostID = value
	}
	if value, ok := ppc.mutation.PlatformPostURL(); ok {
		_spec.SetField(publishedpost.FieldPlatformPostURL, field.TypeString, value)
		_node.PlatformPostURL = value
	}
	if value, ok := ppc.mutation.Content(); ok {
		_spec.SetField(publishedpost.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := ppc.mutation.MediaUrls(); ok {
		_spec.SetField(publishedpost.FieldMediaUrls, field.TypeJSON, value)
		_node.MediaUrls = value
	}
	if value, ok := ppc.mutation.Hashtags(); ok {
		_spec.SetField(publishedpost.FieldHashtags, field.TypeJSON, value)
		_node.Hashtags = value
	}
	if value, ok := ppc.mutation.Status(); ok {
		_spec.SetField(publishedpost.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := ppc.mutation.ErrorMessage(); ok {
		_spec.SetField(publishedpost.FieldErrorMessage, field.TypeString, value)
		_node.ErrorMessage = value
	}
	if value, ok := ppc.mutation.ScheduledAt(); ok {
		_spec.SetField(publishedpost.FieldScheduledAt, field.TypeTime, value)
		_node.ScheduledAt = value
	}
	if value, ok := ppc.mutation.PublishedAt(); ok {
		_spec.SetField(publishedpost.FieldPublishedAt, field.TypeTime, value)
		_node.PublishedAt = value
	}
	if value, ok := ppc.mutation.PlatformResponse(); ok {
		_spec.SetField(publishedpost.FieldPlatformResponse, field.TypeJSON, value)
		_node.PlatformResponse = value
	}
	if value, ok := ppc.mutation.Analytics(); ok {
		_spec.SetField(publishedpost.FieldAnalytics, field.TypeJSON, value)
		_node.Analytics = value
	}
	if value, ok := ppc.mutation.LastAnalyticsSync(); ok {
		_spec.SetField(publishedpost.FieldLastAnalyticsSync, field.TypeTime, value)
		_node.LastAnalyticsSync = value
	}
	if value, ok := ppc.mutation.RetryCount(); ok {
		_spec.SetField(publishedpost.FieldRetryCount, field.TypeInt, value)
		_node.RetryCount = value
	}
	if value, ok := ppc.mutation.NextRetryAt(); ok {
		_spec.SetField(publishedpost.FieldNextRetryAt, field.TypeTime, value)
		_node.NextRetryAt = value
	}
	if value, ok := ppc.mutation.PublishOptions(); ok {
		_spec.SetField(publishedpost.FieldPublishOptions, field.TypeJSON, value)
		_node.PublishOptions = value
	}
	if value, ok := ppc.mutation.Metadata(); ok {
		_spec.SetField(publishedpost.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ppc.mutation.CreatedAt(); ok {
		_spec.SetField(publishedpost.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ppc.mutation.UpdatedAt(); ok {
		_spec.SetField(publishedpost.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ppc.mutation.DeletedAt(); ok {
		_spec.SetField(publishedpost.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// PublishedPostCreateBulk is the builder for creating many PublishedPost entities in bulk.
type PublishedPostCreateBulk struct {
	config
	err      error
	builders []*PublishedPostCreate
}

// Save creates the PublishedPost entities in the database.
func (ppcb *PublishedPostCreateBulk) Save(ctx context.Context) ([]*PublishedPost, error) {
	if ppcb.err != nil {
		return nil, ppcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ppcb.builders))
	nodes := make([]*PublishedPost, len(ppcb.builders))
	mutators := make([]Mutator, len(ppcb.builders))
	for i := range ppcb.builders {
		func(i int, root context.Context) {
			builder := ppcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PublishedPostMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ppcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ppcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ppcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ppcb *PublishedPostCreateBulk) SaveX(ctx context.Context) []*PublishedPost {
	v, err := ppcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ppcb *PublishedPostCreateBulk) Exec(ctx context.Context) error {
	_, err := ppcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ppcb *PublishedPostCreateBulk) ExecX(ctx context.Context) {
	if err := ppcb.Exec(ctx); err != nil {
		panic(err)
	}
}

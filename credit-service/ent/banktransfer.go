// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
)

// BankTransfer is the model entity for the BankTransfer schema.
type BankTransfer struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// PlanID holds the value of the "plan_id" field.
	PlanID uuid.UUID `json:"plan_id,omitempty"`
	// Amount holds the value of the "amount" field.
	Amount int64 `json:"amount,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// Status holds the value of the "status" field.
	Status banktransfer.Status `json:"status,omitempty"`
	// BankAccount holds the value of the "bank_account" field.
	BankAccount string `json:"bank_account,omitempty"`
	// ReferenceCode holds the value of the "reference_code" field.
	ReferenceCode string `json:"reference_code,omitempty"`
	// TransferInstructions holds the value of the "transfer_instructions" field.
	TransferInstructions string `json:"transfer_instructions,omitempty"`
	// ConfirmedAt holds the value of the "confirmed_at" field.
	ConfirmedAt time.Time `json:"confirmed_at,omitempty"`
	// ExpiresAt holds the value of the "expires_at" field.
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	// CreditsToAdd holds the value of the "credits_to_add" field.
	CreditsToAdd int `json:"credits_to_add,omitempty"`
	// BankTransactionID holds the value of the "bank_transaction_id" field.
	BankTransactionID string `json:"bank_transaction_id,omitempty"`
	// ActualAmountReceived holds the value of the "actual_amount_received" field.
	ActualAmountReceived int64 `json:"actual_amount_received,omitempty"`
	// ConfirmationMethod holds the value of the "confirmation_method" field.
	ConfirmationMethod string `json:"confirmation_method,omitempty"`
	// ConfirmedBy holds the value of the "confirmed_by" field.
	ConfirmedBy uuid.UUID `json:"confirmed_by,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*BankTransfer) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case banktransfer.FieldMetadata:
			values[i] = new([]byte)
		case banktransfer.FieldAmount, banktransfer.FieldCreditsToAdd, banktransfer.FieldActualAmountReceived:
			values[i] = new(sql.NullInt64)
		case banktransfer.FieldCurrency, banktransfer.FieldStatus, banktransfer.FieldBankAccount, banktransfer.FieldReferenceCode, banktransfer.FieldTransferInstructions, banktransfer.FieldBankTransactionID, banktransfer.FieldConfirmationMethod:
			values[i] = new(sql.NullString)
		case banktransfer.FieldConfirmedAt, banktransfer.FieldExpiresAt, banktransfer.FieldCreatedAt, banktransfer.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case banktransfer.FieldID, banktransfer.FieldUserID, banktransfer.FieldPlanID, banktransfer.FieldConfirmedBy:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the BankTransfer fields.
func (bt *BankTransfer) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case banktransfer.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				bt.ID = *value
			}
		case banktransfer.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				bt.UserID = *value
			}
		case banktransfer.FieldPlanID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field plan_id", values[i])
			} else if value != nil {
				bt.PlanID = *value
			}
		case banktransfer.FieldAmount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				bt.Amount = value.Int64
			}
		case banktransfer.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				bt.Currency = value.String
			}
		case banktransfer.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				bt.Status = banktransfer.Status(value.String)
			}
		case banktransfer.FieldBankAccount:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field bank_account", values[i])
			} else if value.Valid {
				bt.BankAccount = value.String
			}
		case banktransfer.FieldReferenceCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_code", values[i])
			} else if value.Valid {
				bt.ReferenceCode = value.String
			}
		case banktransfer.FieldTransferInstructions:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transfer_instructions", values[i])
			} else if value.Valid {
				bt.TransferInstructions = value.String
			}
		case banktransfer.FieldConfirmedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field confirmed_at", values[i])
			} else if value.Valid {
				bt.ConfirmedAt = value.Time
			}
		case banktransfer.FieldExpiresAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_at", values[i])
			} else if value.Valid {
				bt.ExpiresAt = value.Time
			}
		case banktransfer.FieldCreditsToAdd:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field credits_to_add", values[i])
			} else if value.Valid {
				bt.CreditsToAdd = int(value.Int64)
			}
		case banktransfer.FieldBankTransactionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field bank_transaction_id", values[i])
			} else if value.Valid {
				bt.BankTransactionID = value.String
			}
		case banktransfer.FieldActualAmountReceived:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field actual_amount_received", values[i])
			} else if value.Valid {
				bt.ActualAmountReceived = value.Int64
			}
		case banktransfer.FieldConfirmationMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field confirmation_method", values[i])
			} else if value.Valid {
				bt.ConfirmationMethod = value.String
			}
		case banktransfer.FieldConfirmedBy:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field confirmed_by", values[i])
			} else if value != nil {
				bt.ConfirmedBy = *value
			}
		case banktransfer.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &bt.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case banktransfer.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				bt.CreatedAt = value.Time
			}
		case banktransfer.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				bt.UpdatedAt = value.Time
			}
		default:
			bt.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the BankTransfer.
// This includes values selected through modifiers, order, etc.
func (bt *BankTransfer) Value(name string) (ent.Value, error) {
	return bt.selectValues.Get(name)
}

// Update returns a builder for updating this BankTransfer.
// Note that you need to call BankTransfer.Unwrap() before calling this method if this BankTransfer
// was returned from a transaction, and the transaction was committed or rolled back.
func (bt *BankTransfer) Update() *BankTransferUpdateOne {
	return NewBankTransferClient(bt.config).UpdateOne(bt)
}

// Unwrap unwraps the BankTransfer entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (bt *BankTransfer) Unwrap() *BankTransfer {
	_tx, ok := bt.config.driver.(*txDriver)
	if !ok {
		panic("ent: BankTransfer is not a transactional entity")
	}
	bt.config.driver = _tx.drv
	return bt
}

// String implements the fmt.Stringer.
func (bt *BankTransfer) String() string {
	var builder strings.Builder
	builder.WriteString("BankTransfer(")
	builder.WriteString(fmt.Sprintf("id=%v, ", bt.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", bt.UserID))
	builder.WriteString(", ")
	builder.WriteString("plan_id=")
	builder.WriteString(fmt.Sprintf("%v", bt.PlanID))
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", bt.Amount))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(bt.Currency)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", bt.Status))
	builder.WriteString(", ")
	builder.WriteString("bank_account=")
	builder.WriteString(bt.BankAccount)
	builder.WriteString(", ")
	builder.WriteString("reference_code=")
	builder.WriteString(bt.ReferenceCode)
	builder.WriteString(", ")
	builder.WriteString("transfer_instructions=")
	builder.WriteString(bt.TransferInstructions)
	builder.WriteString(", ")
	builder.WriteString("confirmed_at=")
	builder.WriteString(bt.ConfirmedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("expires_at=")
	builder.WriteString(bt.ExpiresAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("credits_to_add=")
	builder.WriteString(fmt.Sprintf("%v", bt.CreditsToAdd))
	builder.WriteString(", ")
	builder.WriteString("bank_transaction_id=")
	builder.WriteString(bt.BankTransactionID)
	builder.WriteString(", ")
	builder.WriteString("actual_amount_received=")
	builder.WriteString(fmt.Sprintf("%v", bt.ActualAmountReceived))
	builder.WriteString(", ")
	builder.WriteString("confirmation_method=")
	builder.WriteString(bt.ConfirmationMethod)
	builder.WriteString(", ")
	builder.WriteString("confirmed_by=")
	builder.WriteString(fmt.Sprintf("%v", bt.ConfirmedBy))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", bt.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(bt.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(bt.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// BankTransfers is a parsable slice of BankTransfer.
type BankTransfers []*BankTransfer

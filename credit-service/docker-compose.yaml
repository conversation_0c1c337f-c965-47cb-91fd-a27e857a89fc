version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: credit-service-postgres
    environment:
      POSTGRES_DB: credit_service_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - credit-service-network

  # Redis (for caching - optional)
  redis:
    image: redis:7-alpine
    container_name: credit-service-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - credit-service-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: credit-service-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - credit-service-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: credit-service-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - credit-service-network

  # Kafka UI (for development)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: credit-service-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - credit-service-network

  # Prometheus (for metrics)
  prometheus:
    image: prom/prometheus:latest
    container_name: credit-service-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - credit-service-network

  # Grafana (for visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: credit-service-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - credit-service-network

  # Jaeger (for distributed tracing)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: credit-service-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - credit-service-network

  # Credit Service (main application)
  credit-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: credit-service-app
    depends_on:
      postgres:
        condition: service_healthy
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "8083:8083"  # HTTP port
      - "50053:50053"  # gRPC port
      - "9093:9093"  # Metrics port
    environment:
      # Database configuration
      CREDIT_SERVICE_DATABASE_TYPE: postgres
      CREDIT_SERVICE_DATABASE_HOST: postgres
      CREDIT_SERVICE_DATABASE_PORT: 5432
      CREDIT_SERVICE_DATABASE_USER: postgres
      CREDIT_SERVICE_DATABASE_PASSWORD: postgres
      CREDIT_SERVICE_DATABASE_NAME: credit_service_db
      CREDIT_SERVICE_DATABASE_SSL_MODE: disable
      
      # Kafka configuration
      CREDIT_SERVICE_KAFKA_BROKERS: kafka:29092
      
      # Redis configuration
      CREDIT_SERVICE_CACHE_REDIS_HOST: redis
      CREDIT_SERVICE_CACHE_REDIS_PORT: 6379
      
      # External services
      CREDIT_SERVICE_SERVICES_USER_SERVICE: user-service:50051
      
      # Security
      CREDIT_SERVICE_JWT_SECRET_KEY: your-development-secret-key
      
      # Payment gateways (development)
      STRIPE_SECRET_KEY: sk_test_your_stripe_secret_key
      STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_publishable_key
      STRIPE_WEBHOOK_SECRET: whsec_your_webhook_secret
      
      PAYPAL_CLIENT_ID: your_paypal_client_id
      PAYPAL_CLIENT_SECRET: your_paypal_client_secret
      PAYPAL_WEBHOOK_ID: your_paypal_webhook_id
      
      # Monitoring
      CREDIT_SERVICE_MONITORING_TRACING_JAEGER_ENDPOINT: http://jaeger:14268/api/traces
    volumes:
      - ./config:/app/config
      - ./data:/app/data
    networks:
      - credit-service-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database migration service (runs once)
  migrate:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: credit-service-migrate
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      CREDIT_SERVICE_DATABASE_TYPE: postgres
      CREDIT_SERVICE_DATABASE_HOST: postgres
      CREDIT_SERVICE_DATABASE_PORT: 5432
      CREDIT_SERVICE_DATABASE_USER: postgres
      CREDIT_SERVICE_DATABASE_PASSWORD: postgres
      CREDIT_SERVICE_DATABASE_NAME: credit_service_db
      CREDIT_SERVICE_DATABASE_SSL_MODE: disable
    command: ["./credit-service", "migrate"]
    networks:
      - credit-service-network
    restart: "no"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  credit-service-network:
    driver: bridge
    name: credit-service-network

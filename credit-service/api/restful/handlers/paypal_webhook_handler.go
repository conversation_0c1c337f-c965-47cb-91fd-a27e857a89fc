package handlers

import (
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PayPalWebhookHandler handles PayPal webhook requests
type PayPalWebhookHandler struct {
	subscriptionUC subscription.UseCase
	logger         logging.Logger
}

// NewPayPalWebhookHandler creates a new PayPal webhook handler
func NewPayPalWebhookHandler(subscriptionUC subscription.UseCase, logger logging.Logger) *PayPalWebhookHandler {
	return &PayPalWebhookHandler{
		subscriptionUC: subscriptionUC,
		logger:         logger,
	}
}

// HandlePayPalWebhook godoc
// @Summary Handle PayPal webhook
// @Description Handle PayPal webhook events for payment processing
// @Tags webhooks
// @Accept json
// @Produce json
// @Param payload body models.PayPalWebhookRequest true "PayPal webhook payload"
// @Success 200 {object} models.PayPalWebhookResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /webhooks/paypal [post]
func (h *PayPalWebhookHandler) HandlePayPalWebhook(c *gin.Context) {
	h.logger.Info("Received PayPal webhook")

	// Read raw body
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook body")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Failed to read request body",
		})
		return
	}

	// Parse webhook request
	var webhookReq models.PayPalWebhookRequest
	if err := json.Unmarshal(body, &webhookReq); err != nil {
		h.logger.WithError(err).Error("Failed to parse PayPal webhook")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PAYLOAD",
			Message: "Invalid JSON payload",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":   webhookReq.ID,
		"event_type": webhookReq.EventType,
		"summary":    webhookReq.Summary,
	}).Info("Processing PayPal webhook event")

	// Process webhook based on event type
	switch webhookReq.EventType {
	case "CHECKOUT.ORDER.APPROVED":
		h.handleOrderApproved(c, &webhookReq)
	case "CHECKOUT.ORDER.COMPLETED":
		h.handleOrderCompleted(c, &webhookReq)
	case "PAYMENT.CAPTURE.COMPLETED":
		h.handlePaymentCaptureCompleted(c, &webhookReq)
	case "PAYMENT.CAPTURE.DENIED":
		h.handlePaymentCaptureDenied(c, &webhookReq)
	case "CHECKOUT.ORDER.CANCELLED":
		h.handleOrderCancelled(c, &webhookReq)
	default:
		h.logger.WithField("event_type", webhookReq.EventType).Info("Unhandled PayPal webhook event type")
		c.JSON(http.StatusOK, models.PayPalWebhookResponse{
			Success: true,
			Message: "Event type not handled",
		})
	}
}

// handleOrderApproved handles order approved events
func (h *PayPalWebhookHandler) handleOrderApproved(c *gin.Context, webhook *models.PayPalWebhookRequest) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order approved event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// For order approved, we typically just log it
	// The actual processing happens on order completed
	h.logger.WithFields(map[string]interface{}{
		"order_id":   orderID,
		"event_type": webhook.EventType,
	}).Info("PayPal order approved")

	c.JSON(http.StatusOK, models.PayPalWebhookResponse{
		Success:   true,
		Message:   "Order approved event processed",
		PaymentID: orderID,
	})
}

// handleOrderCompleted handles order completed events
func (h *PayPalWebhookHandler) handleOrderCompleted(c *gin.Context, webhook *models.PayPalWebhookRequest) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order completed event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// Extract payer information
	var payerID string
	if payer, ok := webhook.Resource["payer"].(map[string]interface{}); ok {
		if id, ok := payer["payer_id"].(string); ok {
			payerID = id
		}
	}

	// Handle payment success
	err := h.subscriptionUC.HandlePaymentSuccess(c.Request.Context(), orderID, payerID)
	if err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to handle payment success")
		c.JSON(http.StatusInternalServerError, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment success",
			PaymentID:    orderID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"order_id": orderID,
		"payer_id": payerID,
	}).Info("Successfully processed PayPal order completed")

	c.JSON(http.StatusOK, models.PayPalWebhookResponse{
		Success:   true,
		Message:   "Order completed event processed",
		PaymentID: orderID,
	})
}

// handlePaymentCaptureCompleted handles payment capture completed events
func (h *PayPalWebhookHandler) handlePaymentCaptureCompleted(c *gin.Context, webhook *models.PayPalWebhookRequest) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling payment capture completed event")

	// Extract capture information from resource
	captureID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing capture ID in webhook resource")
		c.JSON(http.StatusBadRequest, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Missing capture ID",
		})
		return
	}

	// Extract custom ID (our order/subscription ID)
	var customID string
	if purchaseUnits, ok := webhook.Resource["purchase_units"].([]interface{}); ok && len(purchaseUnits) > 0 {
		if unit, ok := purchaseUnits[0].(map[string]interface{}); ok {
			if id, ok := unit["custom_id"].(string); ok {
				customID = id
			}
		}
	}

	// Handle payment success using custom ID as payment ID
	paymentID := customID
	if paymentID == "" {
		paymentID = captureID
	}

	err := h.subscriptionUC.HandlePaymentSuccess(c.Request.Context(), paymentID, "")
	if err != nil {
		h.logger.WithError(err).WithField("capture_id", captureID).Error("Failed to handle payment capture success")
		c.JSON(http.StatusInternalServerError, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment capture",
			PaymentID:    captureID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"capture_id": captureID,
		"payment_id": paymentID,
	}).Info("Successfully processed PayPal payment capture completed")

	c.JSON(http.StatusOK, models.PayPalWebhookResponse{
		Success:   true,
		Message:   "Payment capture completed event processed",
		PaymentID: captureID,
	})
}

// handlePaymentCaptureDenied handles payment capture denied events
func (h *PayPalWebhookHandler) handlePaymentCaptureDenied(c *gin.Context, webhook *models.PayPalWebhookRequest) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling payment capture denied event")

	// Extract capture information from resource
	captureID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing capture ID in webhook resource")
		c.JSON(http.StatusBadRequest, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Missing capture ID",
		})
		return
	}

	// Extract reason
	reason := "Payment capture denied"
	if statusDetails, ok := webhook.Resource["status_details"].(map[string]interface{}); ok {
		if reasonCode, ok := statusDetails["reason"].(string); ok {
			reason = reasonCode
		}
	}

	// Handle payment failure
	err := h.subscriptionUC.HandlePaymentFailure(c.Request.Context(), captureID, reason)
	if err != nil {
		h.logger.WithError(err).WithField("capture_id", captureID).Error("Failed to handle payment capture denial")
		c.JSON(http.StatusInternalServerError, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process payment denial",
			PaymentID:    captureID,
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"capture_id": captureID,
		"reason":     reason,
	}).Info("Successfully processed PayPal payment capture denied")

	c.JSON(http.StatusOK, models.PayPalWebhookResponse{
		Success:   true,
		Message:   "Payment capture denied event processed",
		PaymentID: captureID,
	})
}

// handleOrderCancelled handles order cancelled events
func (h *PayPalWebhookHandler) handleOrderCancelled(c *gin.Context, webhook *models.PayPalWebhookRequest) {
	h.logger.WithField("event_id", webhook.ID).Info("Handling order cancelled event")

	// Extract order information from resource
	orderID, ok := webhook.Resource["id"].(string)
	if !ok {
		h.logger.Error("Missing order ID in webhook resource")
		c.JSON(http.StatusBadRequest, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Missing order ID",
		})
		return
	}

	// Handle payment failure
	err := h.subscriptionUC.HandlePaymentFailure(c.Request.Context(), orderID, "Order cancelled by user")
	if err != nil {
		h.logger.WithError(err).WithField("order_id", orderID).Error("Failed to handle order cancellation")
		c.JSON(http.StatusInternalServerError, models.PayPalWebhookResponse{
			Success:      false,
			ErrorMessage: "Failed to process order cancellation",
			PaymentID:    orderID,
		})
		return
	}

	h.logger.WithField("order_id", orderID).Info("Successfully processed PayPal order cancelled")

	c.JSON(http.StatusOK, models.PayPalWebhookResponse{
		Success:   true,
		Message:   "Order cancelled event processed",
		PaymentID: orderID,
	})
}

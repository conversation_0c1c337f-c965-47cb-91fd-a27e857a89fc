// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
)

// AssetCreate is the builder for creating a Asset entity.
type AssetCreate struct {
	config
	mutation *AssetMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (ac *AssetCreate) SetUserID(u uuid.UUID) *AssetCreate {
	ac.mutation.SetUserID(u)
	return ac
}

// SetWorkspaceID sets the "workspace_id" field.
func (ac *AssetCreate) SetWorkspaceID(s string) *AssetCreate {
	ac.mutation.SetWorkspaceID(s)
	return ac
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (ac *AssetCreate) SetNillableWorkspaceID(s *string) *AssetCreate {
	if s != nil {
		ac.SetWorkspaceID(*s)
	}
	return ac
}

// SetFolderID sets the "folder_id" field.
func (ac *AssetCreate) SetFolderID(u uuid.UUID) *AssetCreate {
	ac.mutation.SetFolderID(u)
	return ac
}

// SetNillableFolderID sets the "folder_id" field if the given value is not nil.
func (ac *AssetCreate) SetNillableFolderID(u *uuid.UUID) *AssetCreate {
	if u != nil {
		ac.SetFolderID(*u)
	}
	return ac
}

// SetFileName sets the "file_name" field.
func (ac *AssetCreate) SetFileName(s string) *AssetCreate {
	ac.mutation.SetFileName(s)
	return ac
}

// SetOriginalName sets the "original_name" field.
func (ac *AssetCreate) SetOriginalName(s string) *AssetCreate {
	ac.mutation.SetOriginalName(s)
	return ac
}

// SetFileType sets the "file_type" field.
func (ac *AssetCreate) SetFileType(s string) *AssetCreate {
	ac.mutation.SetFileType(s)
	return ac
}

// SetFileExtension sets the "file_extension" field.
func (ac *AssetCreate) SetFileExtension(s string) *AssetCreate {
	ac.mutation.SetFileExtension(s)
	return ac
}

// SetNillableFileExtension sets the "file_extension" field if the given value is not nil.
func (ac *AssetCreate) SetNillableFileExtension(s *string) *AssetCreate {
	if s != nil {
		ac.SetFileExtension(*s)
	}
	return ac
}

// SetS3Key sets the "s3_key" field.
func (ac *AssetCreate) SetS3Key(s string) *AssetCreate {
	ac.mutation.SetS3Key(s)
	return ac
}

// SetS3Bucket sets the "s3_bucket" field.
func (ac *AssetCreate) SetS3Bucket(s string) *AssetCreate {
	ac.mutation.SetS3Bucket(s)
	return ac
}

// SetContentHash sets the "content_hash" field.
func (ac *AssetCreate) SetContentHash(s string) *AssetCreate {
	ac.mutation.SetContentHash(s)
	return ac
}

// SetNillableContentHash sets the "content_hash" field if the given value is not nil.
func (ac *AssetCreate) SetNillableContentHash(s *string) *AssetCreate {
	if s != nil {
		ac.SetContentHash(*s)
	}
	return ac
}

// SetPurpose sets the "purpose" field.
func (ac *AssetCreate) SetPurpose(s string) *AssetCreate {
	ac.mutation.SetPurpose(s)
	return ac
}

// SetNillablePurpose sets the "purpose" field if the given value is not nil.
func (ac *AssetCreate) SetNillablePurpose(s *string) *AssetCreate {
	if s != nil {
		ac.SetPurpose(*s)
	}
	return ac
}

// SetFileSize sets the "file_size" field.
func (ac *AssetCreate) SetFileSize(i int64) *AssetCreate {
	ac.mutation.SetFileSize(i)
	return ac
}

// SetNillableFileSize sets the "file_size" field if the given value is not nil.
func (ac *AssetCreate) SetNillableFileSize(i *int64) *AssetCreate {
	if i != nil {
		ac.SetFileSize(*i)
	}
	return ac
}

// SetThumbnailURL sets the "thumbnail_url" field.
func (ac *AssetCreate) SetThumbnailURL(s string) *AssetCreate {
	ac.mutation.SetThumbnailURL(s)
	return ac
}

// SetNillableThumbnailURL sets the "thumbnail_url" field if the given value is not nil.
func (ac *AssetCreate) SetNillableThumbnailURL(s *string) *AssetCreate {
	if s != nil {
		ac.SetThumbnailURL(*s)
	}
	return ac
}

// SetMetadata sets the "metadata" field.
func (ac *AssetCreate) SetMetadata(m map[string]interface{}) *AssetCreate {
	ac.mutation.SetMetadata(m)
	return ac
}

// SetStatus sets the "status" field.
func (ac *AssetCreate) SetStatus(s string) *AssetCreate {
	ac.mutation.SetStatus(s)
	return ac
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ac *AssetCreate) SetNillableStatus(s *string) *AssetCreate {
	if s != nil {
		ac.SetStatus(*s)
	}
	return ac
}

// SetIsPublic sets the "is_public" field.
func (ac *AssetCreate) SetIsPublic(b bool) *AssetCreate {
	ac.mutation.SetIsPublic(b)
	return ac
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (ac *AssetCreate) SetNillableIsPublic(b *bool) *AssetCreate {
	if b != nil {
		ac.SetIsPublic(*b)
	}
	return ac
}

// SetCreatedAt sets the "created_at" field.
func (ac *AssetCreate) SetCreatedAt(t time.Time) *AssetCreate {
	ac.mutation.SetCreatedAt(t)
	return ac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ac *AssetCreate) SetNillableCreatedAt(t *time.Time) *AssetCreate {
	if t != nil {
		ac.SetCreatedAt(*t)
	}
	return ac
}

// SetUpdatedAt sets the "updated_at" field.
func (ac *AssetCreate) SetUpdatedAt(t time.Time) *AssetCreate {
	ac.mutation.SetUpdatedAt(t)
	return ac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ac *AssetCreate) SetNillableUpdatedAt(t *time.Time) *AssetCreate {
	if t != nil {
		ac.SetUpdatedAt(*t)
	}
	return ac
}

// SetDeletedAt sets the "deleted_at" field.
func (ac *AssetCreate) SetDeletedAt(t time.Time) *AssetCreate {
	ac.mutation.SetDeletedAt(t)
	return ac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ac *AssetCreate) SetNillableDeletedAt(t *time.Time) *AssetCreate {
	if t != nil {
		ac.SetDeletedAt(*t)
	}
	return ac
}

// SetID sets the "id" field.
func (ac *AssetCreate) SetID(u uuid.UUID) *AssetCreate {
	ac.mutation.SetID(u)
	return ac
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ac *AssetCreate) SetNillableID(u *uuid.UUID) *AssetCreate {
	if u != nil {
		ac.SetID(*u)
	}
	return ac
}

// SetFolder sets the "folder" edge to the Folder entity.
func (ac *AssetCreate) SetFolder(f *Folder) *AssetCreate {
	return ac.SetFolderID(f.ID)
}

// Mutation returns the AssetMutation object of the builder.
func (ac *AssetCreate) Mutation() *AssetMutation {
	return ac.mutation
}

// Save creates the Asset in the database.
func (ac *AssetCreate) Save(ctx context.Context) (*Asset, error) {
	ac.defaults()
	return withHooks(ctx, ac.sqlSave, ac.mutation, ac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ac *AssetCreate) SaveX(ctx context.Context) *Asset {
	v, err := ac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ac *AssetCreate) Exec(ctx context.Context) error {
	_, err := ac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ac *AssetCreate) ExecX(ctx context.Context) {
	if err := ac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ac *AssetCreate) defaults() {
	if _, ok := ac.mutation.Purpose(); !ok {
		v := asset.DefaultPurpose
		ac.mutation.SetPurpose(v)
	}
	if _, ok := ac.mutation.FileSize(); !ok {
		v := asset.DefaultFileSize
		ac.mutation.SetFileSize(v)
	}
	if _, ok := ac.mutation.Status(); !ok {
		v := asset.DefaultStatus
		ac.mutation.SetStatus(v)
	}
	if _, ok := ac.mutation.IsPublic(); !ok {
		v := asset.DefaultIsPublic
		ac.mutation.SetIsPublic(v)
	}
	if _, ok := ac.mutation.CreatedAt(); !ok {
		v := asset.DefaultCreatedAt()
		ac.mutation.SetCreatedAt(v)
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		v := asset.DefaultUpdatedAt()
		ac.mutation.SetUpdatedAt(v)
	}
	if _, ok := ac.mutation.ID(); !ok {
		v := asset.DefaultID()
		ac.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ac *AssetCreate) check() error {
	if _, ok := ac.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Asset.user_id"`)}
	}
	if v, ok := ac.mutation.WorkspaceID(); ok {
		if err := asset.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Asset.workspace_id": %w`, err)}
		}
	}
	if _, ok := ac.mutation.FileName(); !ok {
		return &ValidationError{Name: "file_name", err: errors.New(`ent: missing required field "Asset.file_name"`)}
	}
	if v, ok := ac.mutation.FileName(); ok {
		if err := asset.FileNameValidator(v); err != nil {
			return &ValidationError{Name: "file_name", err: fmt.Errorf(`ent: validator failed for field "Asset.file_name": %w`, err)}
		}
	}
	if _, ok := ac.mutation.OriginalName(); !ok {
		return &ValidationError{Name: "original_name", err: errors.New(`ent: missing required field "Asset.original_name"`)}
	}
	if v, ok := ac.mutation.OriginalName(); ok {
		if err := asset.OriginalNameValidator(v); err != nil {
			return &ValidationError{Name: "original_name", err: fmt.Errorf(`ent: validator failed for field "Asset.original_name": %w`, err)}
		}
	}
	if _, ok := ac.mutation.FileType(); !ok {
		return &ValidationError{Name: "file_type", err: errors.New(`ent: missing required field "Asset.file_type"`)}
	}
	if v, ok := ac.mutation.FileType(); ok {
		if err := asset.FileTypeValidator(v); err != nil {
			return &ValidationError{Name: "file_type", err: fmt.Errorf(`ent: validator failed for field "Asset.file_type": %w`, err)}
		}
	}
	if v, ok := ac.mutation.FileExtension(); ok {
		if err := asset.FileExtensionValidator(v); err != nil {
			return &ValidationError{Name: "file_extension", err: fmt.Errorf(`ent: validator failed for field "Asset.file_extension": %w`, err)}
		}
	}
	if _, ok := ac.mutation.S3Key(); !ok {
		return &ValidationError{Name: "s3_key", err: errors.New(`ent: missing required field "Asset.s3_key"`)}
	}
	if v, ok := ac.mutation.S3Key(); ok {
		if err := asset.S3KeyValidator(v); err != nil {
			return &ValidationError{Name: "s3_key", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_key": %w`, err)}
		}
	}
	if _, ok := ac.mutation.S3Bucket(); !ok {
		return &ValidationError{Name: "s3_bucket", err: errors.New(`ent: missing required field "Asset.s3_bucket"`)}
	}
	if v, ok := ac.mutation.S3Bucket(); ok {
		if err := asset.S3BucketValidator(v); err != nil {
			return &ValidationError{Name: "s3_bucket", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_bucket": %w`, err)}
		}
	}
	if v, ok := ac.mutation.ContentHash(); ok {
		if err := asset.ContentHashValidator(v); err != nil {
			return &ValidationError{Name: "content_hash", err: fmt.Errorf(`ent: validator failed for field "Asset.content_hash": %w`, err)}
		}
	}
	if _, ok := ac.mutation.Purpose(); !ok {
		return &ValidationError{Name: "purpose", err: errors.New(`ent: missing required field "Asset.purpose"`)}
	}
	if v, ok := ac.mutation.Purpose(); ok {
		if err := asset.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "Asset.purpose": %w`, err)}
		}
	}
	if _, ok := ac.mutation.FileSize(); !ok {
		return &ValidationError{Name: "file_size", err: errors.New(`ent: missing required field "Asset.file_size"`)}
	}
	if v, ok := ac.mutation.ThumbnailURL(); ok {
		if err := asset.ThumbnailURLValidator(v); err != nil {
			return &ValidationError{Name: "thumbnail_url", err: fmt.Errorf(`ent: validator failed for field "Asset.thumbnail_url": %w`, err)}
		}
	}
	if _, ok := ac.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Asset.status"`)}
	}
	if v, ok := ac.mutation.Status(); ok {
		if err := asset.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Asset.status": %w`, err)}
		}
	}
	if _, ok := ac.mutation.IsPublic(); !ok {
		return &ValidationError{Name: "is_public", err: errors.New(`ent: missing required field "Asset.is_public"`)}
	}
	if _, ok := ac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Asset.created_at"`)}
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Asset.updated_at"`)}
	}
	return nil
}

func (ac *AssetCreate) sqlSave(ctx context.Context) (*Asset, error) {
	if err := ac.check(); err != nil {
		return nil, err
	}
	_node, _spec := ac.createSpec()
	if err := sqlgraph.CreateNode(ctx, ac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ac.mutation.id = &_node.ID
	ac.mutation.done = true
	return _node, nil
}

func (ac *AssetCreate) createSpec() (*Asset, *sqlgraph.CreateSpec) {
	var (
		_node = &Asset{config: ac.config}
		_spec = sqlgraph.NewCreateSpec(asset.Table, sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID))
	)
	if id, ok := ac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ac.mutation.UserID(); ok {
		_spec.SetField(asset.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := ac.mutation.WorkspaceID(); ok {
		_spec.SetField(asset.FieldWorkspaceID, field.TypeString, value)
		_node.WorkspaceID = value
	}
	if value, ok := ac.mutation.FileName(); ok {
		_spec.SetField(asset.FieldFileName, field.TypeString, value)
		_node.FileName = value
	}
	if value, ok := ac.mutation.OriginalName(); ok {
		_spec.SetField(asset.FieldOriginalName, field.TypeString, value)
		_node.OriginalName = value
	}
	if value, ok := ac.mutation.FileType(); ok {
		_spec.SetField(asset.FieldFileType, field.TypeString, value)
		_node.FileType = value
	}
	if value, ok := ac.mutation.FileExtension(); ok {
		_spec.SetField(asset.FieldFileExtension, field.TypeString, value)
		_node.FileExtension = value
	}
	if value, ok := ac.mutation.S3Key(); ok {
		_spec.SetField(asset.FieldS3Key, field.TypeString, value)
		_node.S3Key = value
	}
	if value, ok := ac.mutation.S3Bucket(); ok {
		_spec.SetField(asset.FieldS3Bucket, field.TypeString, value)
		_node.S3Bucket = value
	}
	if value, ok := ac.mutation.ContentHash(); ok {
		_spec.SetField(asset.FieldContentHash, field.TypeString, value)
		_node.ContentHash = value
	}
	if value, ok := ac.mutation.Purpose(); ok {
		_spec.SetField(asset.FieldPurpose, field.TypeString, value)
		_node.Purpose = value
	}
	if value, ok := ac.mutation.FileSize(); ok {
		_spec.SetField(asset.FieldFileSize, field.TypeInt64, value)
		_node.FileSize = value
	}
	if value, ok := ac.mutation.ThumbnailURL(); ok {
		_spec.SetField(asset.FieldThumbnailURL, field.TypeString, value)
		_node.ThumbnailURL = value
	}
	if value, ok := ac.mutation.Metadata(); ok {
		_spec.SetField(asset.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ac.mutation.Status(); ok {
		_spec.SetField(asset.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := ac.mutation.IsPublic(); ok {
		_spec.SetField(asset.FieldIsPublic, field.TypeBool, value)
		_node.IsPublic = value
	}
	if value, ok := ac.mutation.CreatedAt(); ok {
		_spec.SetField(asset.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ac.mutation.UpdatedAt(); ok {
		_spec.SetField(asset.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ac.mutation.DeletedAt(); ok {
		_spec.SetField(asset.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	if nodes := ac.mutation.FolderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   asset.FolderTable,
			Columns: []string{asset.FolderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.FolderID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// AssetCreateBulk is the builder for creating many Asset entities in bulk.
type AssetCreateBulk struct {
	config
	err      error
	builders []*AssetCreate
}

// Save creates the Asset entities in the database.
func (acb *AssetCreateBulk) Save(ctx context.Context) ([]*Asset, error) {
	if acb.err != nil {
		return nil, acb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acb.builders))
	nodes := make([]*Asset, len(acb.builders))
	mutators := make([]Mutator, len(acb.builders))
	for i := range acb.builders {
		func(i int, root context.Context) {
			builder := acb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AssetMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acb *AssetCreateBulk) SaveX(ctx context.Context) []*Asset {
	v, err := acb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acb *AssetCreateBulk) Exec(ctx context.Context) error {
	_, err := acb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acb *AssetCreateBulk) ExecX(ctx context.Context) {
	if err := acb.Exec(ctx); err != nil {
		panic(err)
	}
}

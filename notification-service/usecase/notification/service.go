package notification

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent"
	"github.com/social-content-ai/notification-service/ent/notification"
	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/notification-service/pkg/websocket"
	"github.com/social-content-ai/notification-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockNotification represents a notification for sending
type MockNotification struct {
	ID       string
	UserID   string
	Type     string
	Title    string
	Message  string
	Priority string
	Data     map[string]interface{}
}

// Service implements the notification UseCase interface
type Service struct {
	readDB      *ent.Client
	writeDB     *ent.Client
	emailSender channels.EmailSender
	smsSender   channels.SMSSender
	pushSender  channels.PushSender
	wsHub       *websocket.Hub
	templateUC  template.UseCase
	logger      logging.Logger
}

// NewService creates a new notification service
func NewService(
	readDB *ent.Client,
	writeDB *ent.Client,
	emailSender channels.EmailSender,
	smsSender channels.SMSSender,
	pushSender channels.PushSender,
	wsHub *websocket.Hub,
	templateUC template.UseCase,
	logger logging.Logger,
) UseCase {
	return &Service{
		readDB:      readDB,
		writeDB:     writeDB,
		emailSender: emailSender,
		smsSender:   smsSender,
		pushSender:  pushSender,
		wsHub:       wsHub,
		templateUC:  templateUC,
		logger:      logger,
	}
}

// SendNotification sends a single notification
func (s *Service) SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"type":    req.Type,
		"title":   req.Title,
	}).Info("Sending notification")

	// Generate notification ID
	notificationID := uuid.New()

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Create notification record in database
	notif, err := s.writeDB.Notification.Create().
		SetID(notificationID).
		SetUserID(userID).
		SetType(notification.Type(req.Type)).
		SetTitle(req.Title).
		SetMessage(req.Message).
		SetChannels(req.Channels).
		SetPriority(notification.Priority(req.Priority)).
		SetData(req.Data).
		SetStatus(notification.StatusPending).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create notification record")
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Send notification through specified channels
	go s.sendThroughChannels(context.Background(), notif, req.Channels)

	return &SendNotificationResponse{
		NotificationID: notificationID.String(),
		Status:         "queued",
		Message:        "Notification queued for delivery",
	}, nil
}

// SendTemplatedNotification sends a templated notification
func (s *Service) SendTemplatedNotification(ctx context.Context, req *SendTemplatedNotificationRequest) (*SendNotificationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":     req.UserID,
		"template_id": req.TemplateID,
	}).Info("Sending templated notification")

	// Load template
	tmpl, err := s.templateUC.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get template")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Generate notification ID
	notificationID := uuid.New()

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Use template's default channels if not specified
	channels := req.Channels
	if len(channels) == 0 && len(tmpl.Templates) > 0 {
		for channel := range tmpl.Templates {
			channels = append(channels, channel)
		}
	}

	// Render template for the first available channel to get title and message
	var title, message string
	if len(channels) > 0 {
		renderReq := &template.RenderTemplateRequest{
			TemplateID: req.TemplateID,
			Channel:    channels[0],
			Data:       req.Data,
		}

		rendered, err := s.templateUC.RenderTemplate(ctx, renderReq)
		if err != nil {
			s.logger.WithError(err).Error("Failed to render template")
			return nil, fmt.Errorf("failed to render template: %w", err)
		}

		title = rendered.Subject
		if title == "" {
			title = tmpl.Name
		}
		message = rendered.Body
	} else {
		title = tmpl.Name
		message = tmpl.Description
	}

	// Create notification record
	notif, err := s.writeDB.Notification.Create().
		SetID(notificationID).
		SetUserID(userID).
		SetType(notification.Type(tmpl.EventType)).
		SetTitle(title).
		SetMessage(message).
		SetChannels(channels).
		SetPriority(notification.Priority(req.Priority)).
		SetData(req.Data).
		SetStatus(notification.StatusPending).
		SetReferenceID(req.TemplateID).
		SetReferenceType("template").
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create templated notification record")
		return nil, fmt.Errorf("failed to create templated notification: %w", err)
	}

	// Send notification through specified channels
	go s.sendThroughChannels(context.Background(), notif, channels)

	return &SendNotificationResponse{
		NotificationID: notificationID.String(),
		Status:         "queued",
		Message:        "Templated notification queued for delivery",
	}, nil
}

// SendBulkNotification sends bulk notifications
func (s *Service) SendBulkNotification(ctx context.Context, req *BulkNotificationRequest) (*BulkNotificationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_count": len(req.UserIDs),
		"type":       req.Type,
		"title":      req.Title,
	}).Info("Sending bulk notification")

	var results []SendNotificationResponse
	var failedUserIDs []string
	totalSent := 0
	totalFailed := 0

	// Send notification to each user
	for _, userIDStr := range req.UserIDs {
		notificationID := uuid.New()

		// Parse user ID
		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userIDStr).Error("Invalid user ID format")
			failedUserIDs = append(failedUserIDs, userIDStr)
			results = append(results, SendNotificationResponse{
				NotificationID: "",
				Status:         "failed",
				Message:        fmt.Sprintf("invalid user ID format: %v", err),
			})
			totalFailed++
			continue
		}

		// Create notification record
		notif, err := s.writeDB.Notification.Create().
			SetID(notificationID).
			SetUserID(userID).
			SetType(notification.Type(req.Type)).
			SetTitle(req.Title).
			SetMessage(req.Message).
			SetChannels(req.Channels).
			SetPriority(notification.Priority(req.Priority)).
			SetData(req.Data).
			SetStatus(notification.StatusPending).
			Save(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("user_id", userIDStr).Error("Failed to create bulk notification record")
			failedUserIDs = append(failedUserIDs, userIDStr)
			results = append(results, SendNotificationResponse{
				NotificationID: "",
				Status:         "failed",
				Message:        err.Error(),
			})
			totalFailed++
			continue
		}

		// Send notification through specified channels
		go s.sendThroughChannels(context.Background(), notif, req.Channels)

		results = append(results, SendNotificationResponse{
			NotificationID: notificationID.String(),
			Status:         "queued",
			Message:        "Notification queued for delivery",
		})
		totalSent++
	}

	return &BulkNotificationResponse{
		TotalSent:     totalSent,
		TotalFailed:   totalFailed,
		Results:       results,
		FailedUserIDs: failedUserIDs,
	}, nil
}

// GetNotification gets a notification by ID
func (s *Service) GetNotification(ctx context.Context, id string) (*NotificationResponse, error) {
	s.logger.WithField("notification_id", id).Debug("Getting notification")

	// Parse notification ID
	notificationID, err := uuid.Parse(id)
	if err != nil {
		s.logger.WithError(err).Error("Invalid notification ID format")
		return nil, fmt.Errorf("invalid notification ID format: %w", err)
	}

	notif, err := s.readDB.Notification.
		Query().
		Where(notification.ID(notificationID)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("notification not found")
		}
		s.logger.WithError(err).Error("Failed to get notification")
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return s.convertToNotificationResponse(notif), nil
}

// ListNotifications lists notifications for a user
func (s *Service) ListNotifications(ctx context.Context, req *ListNotificationsRequest) (*ListNotificationsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"page":    req.Page,
		"limit":   req.Limit,
	}).Debug("Listing notifications")

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query
	query := s.readDB.Notification.Query().
		Where(notification.UserID(userID))

	// Add filters
	// Note: WorkspaceID is not available in the current schema
	if req.Type != "" {
		query = query.Where(notification.TypeEQ(notification.Type(req.Type)))
	}
	if req.Status != "" {
		query = query.Where(notification.StatusEQ(notification.Status(req.Status)))
	}

	// Add sorting
	if req.SortBy != "" {
		switch req.SortBy {
		case "created_at":
			if req.Order == "asc" {
				query = query.Order(ent.Asc(notification.FieldCreatedAt))
			} else {
				query = query.Order(ent.Desc(notification.FieldCreatedAt))
			}
		case "priority":
			if req.Order == "asc" {
				query = query.Order(ent.Asc(notification.FieldPriority))
			} else {
				query = query.Order(ent.Desc(notification.FieldPriority))
			}
		}
	} else {
		// Default sort by created_at desc
		query = query.Order(ent.Desc(notification.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count notifications")
		return nil, fmt.Errorf("failed to count notifications: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	notifications, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list notifications")
		return nil, fmt.Errorf("failed to list notifications: %w", err)
	}

	// Convert to response format
	var notificationResponses []NotificationResponse
	for _, notif := range notifications {
		notificationResponses = append(notificationResponses, *s.convertToNotificationResponse(notif))
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	return &ListNotificationsResponse{
		Notifications: notificationResponses,
		Pagination: PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// MarkAsRead marks notifications as read
func (s *Service) MarkAsRead(ctx context.Context, req *MarkAsReadRequest) error {
	s.logger.WithFields(map[string]interface{}{
		"user_id":            req.UserID,
		"notification_count": len(req.NotificationIDs),
	}).Info("Marking notifications as read")

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Parse notification IDs
	var notificationIDs []uuid.UUID
	for _, idStr := range req.NotificationIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			s.logger.WithError(err).WithField("notification_id", idStr).Error("Invalid notification ID format")
			return fmt.Errorf("invalid notification ID format: %w", err)
		}
		notificationIDs = append(notificationIDs, id)
	}

	// Update notifications
	_, err = s.writeDB.Notification.
		Update().
		Where(
			notification.And(
				notification.UserID(userID),
				notification.IDIn(notificationIDs...),
			),
		).
		SetStatus(notification.StatusRead).
		SetReadAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to mark notifications as read")
		return fmt.Errorf("failed to mark notifications as read: %w", err)
	}

	return nil
}

// GetDeliveryStatus gets delivery status for a notification
func (s *Service) GetDeliveryStatus(ctx context.Context, req *GetDeliveryStatusRequest) (*DeliveryStatusResponse, error) {
	s.logger.WithField("notification_id", req.NotificationID).Debug("Getting delivery status")

	// Parse notification ID
	notificationID, err := uuid.Parse(req.NotificationID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid notification ID format")
		return nil, fmt.Errorf("invalid notification ID format: %w", err)
	}

	notif, err := s.readDB.Notification.
		Query().
		Where(notification.ID(notificationID)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("notification not found")
		}
		s.logger.WithError(err).Error("Failed to get notification for delivery status")
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	// Get actual delivery status from delivery tracking
	var channelStatuses []ChannelStatus
	for _, channel := range notif.Channels {
		channelStatus := ChannelStatus{
			Channel: channel,
			Status:  "pending",
			SentAt:  "",
		}

		// Check if we have delivery status for this channel
		if notif.DeliveryStatus != nil {
			if statusData, exists := notif.DeliveryStatus[channel]; exists {
				if statusMap, ok := statusData.(map[string]interface{}); ok {
					if status, ok := statusMap["status"].(string); ok {
						channelStatus.Status = status
					}
					if updatedAt, ok := statusMap["updated_at"].(string); ok {
						channelStatus.SentAt = updatedAt
						if channelStatus.Status == "delivered" {
							channelStatus.DeliveredAt = updatedAt
						}
					}
					if errorMsg, ok := statusMap["error"].(string); ok {
						channelStatus.Error = errorMsg
					}
				}
			}
		}

		channelStatuses = append(channelStatuses, channelStatus)
	}

	return &DeliveryStatusResponse{
		NotificationID: req.NotificationID,
		Status:         "delivered",
		Channels:       channelStatuses,
		CreatedAt:      notif.CreatedAt.Format(time.RFC3339),
		LastUpdatedAt:  notif.UpdatedAt.Format(time.RFC3339),
	}, nil
}

// MarkAllAsRead marks all notifications as read for a user
func (s *Service) MarkAllAsRead(ctx context.Context, userID string) error {
	s.logger.WithField("user_id", userID).Info("Marking all notifications as read")

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Update all unread notifications for the user
	_, err = s.writeDB.Notification.
		Update().
		Where(
			notification.And(
				notification.UserID(userUUID),
				notification.StatusNEQ(notification.StatusRead),
			),
		).
		SetStatus(notification.StatusRead).
		SetReadAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to mark all notifications as read")
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	return nil
}

// DeleteNotification deletes a notification
func (s *Service) DeleteNotification(ctx context.Context, notificationID, userID string) error {
	s.logger.WithFields(map[string]interface{}{
		"notification_id": notificationID,
		"user_id":         userID,
	}).Info("Deleting notification")

	// Parse notification ID
	notifUUID, err := uuid.Parse(notificationID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid notification ID format")
		return fmt.Errorf("invalid notification ID format: %w", err)
	}

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Delete notification (only if it belongs to the user)
	err = s.writeDB.Notification.
		DeleteOneID(notifUUID).
		Where(notification.UserID(userUUID)).
		Exec(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("notification not found or access denied")
		}
		s.logger.WithError(err).Error("Failed to delete notification")
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	return nil
}

// Helper methods

// sendThroughChannels sends notification through specified channels
func (s *Service) sendThroughChannels(ctx context.Context, notif *ent.Notification, channels []string) {
	for _, channel := range channels {
		switch channel {
		case "email":
			if s.emailSender != nil {
				go s.sendEmail(ctx, notif)
			}
		case "sms":
			if s.smsSender != nil {
				go s.sendSMS(ctx, notif)
			}
		case "push":
			if s.pushSender != nil {
				go s.sendPush(ctx, notif)
			}
		case "websocket", "in_app":
			if s.wsHub != nil {
				go s.sendWebSocket(ctx, notif)
			}
		default:
			s.logger.WithField("channel", channel).Warn("Unknown notification channel")
		}
	}
}

// sendEmail sends notification via email
func (s *Service) sendEmail(ctx context.Context, notif *ent.Notification) {
	s.logger.WithFields(map[string]interface{}{
		"notification_id": notif.ID,
		"user_id":         notif.UserID,
		"channel":         "email",
	}).Debug("Sending email notification")

	// TODO: Get user email from user service
	// For now, use placeholder
	err := s.emailSender.SendEmail(ctx, &channels.EmailMessage{
		To:      "<EMAIL>", // TODO: Get from user service
		Subject: notif.Title,
		Body:    notif.Message,
		IsHTML:  false,
	})

	if err != nil {
		s.logger.WithError(err).Error("Failed to send email notification")
		s.updateDeliveryStatus(context.Background(), notif.ID, "email", "failed", err.Error())
	} else {
		s.logger.WithField("notification_id", notif.ID).Info("Email notification sent successfully")
		s.updateDeliveryStatus(context.Background(), notif.ID, "email", "sent", "")
	}
}

// sendSMS sends notification via SMS
func (s *Service) sendSMS(ctx context.Context, notif *ent.Notification) {
	s.logger.WithFields(map[string]interface{}{
		"notification_id": notif.ID,
		"user_id":         notif.UserID,
		"channel":         "sms",
	}).Debug("Sending SMS notification")

	// TODO: Get user phone from user service
	// For now, use placeholder
	err := s.smsSender.SendSMS(ctx, &channels.SMSMessage{
		To:   "+1234567890", // TODO: Get from user service
		Body: fmt.Sprintf("%s: %s", notif.Title, notif.Message),
	})

	if err != nil {
		s.logger.WithError(err).Error("Failed to send SMS notification")
		s.updateDeliveryStatus(context.Background(), notif.ID, "sms", "failed", err.Error())
	} else {
		s.logger.WithField("notification_id", notif.ID).Info("SMS notification sent successfully")
		s.updateDeliveryStatus(context.Background(), notif.ID, "sms", "sent", "")
	}
}

// sendPush sends notification via push
func (s *Service) sendPush(ctx context.Context, notif *ent.Notification) {
	s.logger.WithFields(map[string]interface{}{
		"notification_id": notif.ID,
		"user_id":         notif.UserID,
		"channel":         "push",
	}).Debug("Sending push notification")

	// TODO: Get user device tokens from user service
	// For now, use placeholder
	// Convert data to map[string]string for push message
	dataStr := make(map[string]string)
	for k, v := range notif.Data {
		if str, ok := v.(string); ok {
			dataStr[k] = str
		} else {
			dataStr[k] = fmt.Sprintf("%v", v)
		}
	}

	err := s.pushSender.SendPush(ctx, &channels.PushMessage{
		DeviceTokens: []string{"device-token"}, // TODO: Get from user service
		Title:        notif.Title,
		Body:         notif.Message,
		Data:         dataStr,
	})

	if err != nil {
		s.logger.WithError(err).Error("Failed to send push notification")
		s.updateDeliveryStatus(context.Background(), notif.ID, "push", "failed", err.Error())
	} else {
		s.logger.WithField("notification_id", notif.ID).Info("Push notification sent successfully")
		s.updateDeliveryStatus(context.Background(), notif.ID, "push", "sent", "")
	}
}

// sendWebSocket sends notification via WebSocket
func (s *Service) sendWebSocket(ctx context.Context, notif *ent.Notification) {
	s.logger.WithFields(map[string]interface{}{
		"notification_id": notif.ID,
		"user_id":         notif.UserID,
		"channel":         "websocket",
	}).Debug("Sending WebSocket notification")

	// Send to WebSocket hub
	message := &websocket.Message{
		Type: "notification",
		Data: map[string]interface{}{
			"id":         notif.ID.String(),
			"type":       string(notif.Type),
			"title":      notif.Title,
			"message":    notif.Message,
			"priority":   string(notif.Priority),
			"data":       notif.Data,
			"created_at": notif.CreatedAt.Format(time.RFC3339),
		},
	}

	err := s.wsHub.SendToUser(notif.UserID.String(), message)
	if err != nil {
		s.logger.WithError(err).Error("Failed to send WebSocket notification")
		s.updateDeliveryStatus(context.Background(), notif.ID, "websocket", "failed", err.Error())
	} else {
		s.logger.WithField("notification_id", notif.ID).Info("WebSocket notification sent successfully")
		s.updateDeliveryStatus(context.Background(), notif.ID, "websocket", "sent", "")
	}
}

// convertToNotificationResponse converts ent notification to response
func (s *Service) convertToNotificationResponse(notif *ent.Notification) *NotificationResponse {
	var readAt string
	var sentAt string

	// Check if ReadAt is not zero time
	if !notif.ReadAt.IsZero() {
		readAt = notif.ReadAt.Format(time.RFC3339)
	}

	// Check if SentAt is not zero time
	if !notif.SentAt.IsZero() {
		sentAt = notif.SentAt.Format(time.RFC3339)
	}

	return &NotificationResponse{
		ID:          notif.ID.String(),
		UserID:      notif.UserID.String(),
		WorkspaceID: "", // WorkspaceID is not available in current schema
		Type:        string(notif.Type),
		Title:       notif.Title,
		Message:     notif.Message,
		Status:      string(notif.Status),
		Priority:    string(notif.Priority),
		Channels:    notif.Channels,
		Data:        notif.Data,
		CreatedAt:   notif.CreatedAt.Format(time.RFC3339),
		SentAt:      sentAt,
		ReadAt:      readAt,
	}
}

// updateDeliveryStatus updates delivery status for a specific channel
func (s *Service) updateDeliveryStatus(ctx context.Context, notificationID uuid.UUID, channel, status, errorMsg string) {
	// Get current delivery status
	notif, err := s.readDB.Notification.
		Query().
		Where(notification.ID(notificationID)).
		Only(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get notification for delivery status update")
		return
	}

	// Update delivery status
	deliveryStatus := notif.DeliveryStatus
	if deliveryStatus == nil {
		deliveryStatus = make(map[string]interface{})
	}

	channelStatus := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now().Format(time.RFC3339),
	}
	if errorMsg != "" {
		channelStatus["error"] = errorMsg
	}
	deliveryStatus[channel] = channelStatus

	// Update notification
	update := s.writeDB.Notification.UpdateOne(notif).
		SetDeliveryStatus(deliveryStatus)

	// Update sent_at if this is the first successful send
	if status == "sent" && notif.SentAt.IsZero() {
		update = update.SetSentAt(time.Now()).SetStatus(notification.StatusSent)
	}

	_, err = update.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update delivery status")
	}
}

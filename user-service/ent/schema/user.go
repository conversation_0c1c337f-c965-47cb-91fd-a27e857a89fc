package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.String("full_name").
			MaxLen(100).
			Optional(),
		field.String("email").
			MaxLen(255).
			Unique(),
		field.String("phone").
			MaxLen(15).
			Optional(),
		field.String("avatar_url").
			Optional(),
		field.Text("bio").
			Optional(),
		field.String("company").
			MaxLen(100).
			Optional(),
		field.String("industry").
			MaxLen(100).
			Optional(),
		field.Enum("role").
			Values("user", "admin").
			Default("user"),
		field.String("password_hash").
			Sensitive(),
		field.Bool("is_verified").
			Default(false),
		field.Bool("two_factor_enabled").
			Default(false),
		field.String("two_factor_secret").
			Optional().
			Sensitive(),
		field.JSON("two_factor_backup_codes", []string{}).
			Optional().
			Sensitive(),
		field.Time("two_factor_enabled_at").
			Optional(),
		field.JSON("notification_settings", map[string]interface{}{}).
			Optional(),
		field.String("invite_code").
			MaxLen(20).
			Unique().
			Optional(),
		field.String("aff_code").
			MaxLen(20).
			Optional(),
		field.Time("aff_joined_at").
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the User.
func (User) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("sessions", Session.Type),
	}
}

// Indexes of the User.
func (User) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("email").Unique(),
		index.Fields("invite_code").Unique(),
		index.Fields("role"),
		index.Fields("is_verified"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
	}
}

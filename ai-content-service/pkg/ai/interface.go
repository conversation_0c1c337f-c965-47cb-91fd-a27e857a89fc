package ai

import (
	"context"
)

// Service defines the AI service interface
type Service interface {
	// Content generation
	GenerateContent(ctx context.Context, req *GenerationRequest) (*GenerationResult, error)
	ImproveContent(ctx context.Context, req *ImprovementRequest) (*ImprovementResult, error)
	
	// Model management
	GetAvailableModels(ctx context.Context) ([]*ModelInfo, error)
	GetModelInfo(ctx context.Context, modelID string) (*ModelInfo, error)
	
	// Health check
	HealthCheck(ctx context.Context) error
}

// GenerationRequest represents content generation request
type GenerationRequest struct {
	Topic       string            `json:"topic"`
	ContentType string            `json:"content_type"`
	Tone        string            `json:"tone"`
	Length      string            `json:"length"`
	Platforms   []string          `json:"platforms,omitempty"`
	Model       string            `json:"model"`
	Context     string            `json:"context,omitempty"`
	Variables   map[string]string `json:"variables,omitempty"`
	MaxTokens   int               `json:"max_tokens,omitempty"`
	Temperature float32           `json:"temperature,omitempty"`
}

// GenerationResult represents content generation result
type GenerationResult struct {
	Content         string   `json:"content"`
	Hashtags        []string `json:"hashtags,omitempty"`
	SuggestedImages []string `json:"suggested_images,omitempty"`
	TokensUsed      int      `json:"tokens_used"`
	Model           string   `json:"model"`
	FinishReason    string   `json:"finish_reason"`
}

// ImprovementRequest represents content improvement request
type ImprovementRequest struct {
	OriginalText     string `json:"original_text"`
	ImprovementType  string `json:"improvement_type"`
	TargetTone       string `json:"target_tone,omitempty"`
	TargetLength     string `json:"target_length,omitempty"`
	Platform         string `json:"platform,omitempty"`
	Context          string `json:"context,omitempty"`
	Model            string `json:"model"`
	MaxTokens        int    `json:"max_tokens,omitempty"`
	Temperature      float32 `json:"temperature,omitempty"`
}

// ImprovementResult represents content improvement result
type ImprovementResult struct {
	ImprovedText        string            `json:"improved_text"`
	ReadabilityScore    float64           `json:"readability_score"`
	EngagementPotential float64           `json:"engagement_potential"`
	SentimentScore      float64           `json:"sentiment_score"`
	AnalysisDetails     map[string]interface{} `json:"analysis_details,omitempty"`
	Suggestions         []string          `json:"suggestions,omitempty"`
	TokensUsed          int               `json:"tokens_used"`
	Model               string            `json:"model"`
}

// ModelInfo represents AI model information
type ModelInfo struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	Provider     string   `json:"provider"`
	Type         string   `json:"type"`
	MaxTokens    int      `json:"max_tokens"`
	CostPerToken float64  `json:"cost_per_token"`
	Capabilities []string `json:"capabilities"`
	IsAvailable  bool     `json:"is_available"`
	Description  string   `json:"description"`
}

// Provider defines AI provider interface
type Provider interface {
	// Provider info
	GetProviderName() string
	GetSupportedModels() []string
	
	// Content operations
	GenerateText(ctx context.Context, req *TextGenerationRequest) (*TextGenerationResponse, error)
	AnalyzeText(ctx context.Context, req *TextAnalysisRequest) (*TextAnalysisResponse, error)
	
	// Health check
	IsHealthy(ctx context.Context) bool
}

// TextGenerationRequest represents text generation request to provider
type TextGenerationRequest struct {
	Model       string            `json:"model"`
	Prompt      string            `json:"prompt"`
	MaxTokens   int               `json:"max_tokens"`
	Temperature float32           `json:"temperature"`
	TopP        float32           `json:"top_p,omitempty"`
	Stop        []string          `json:"stop,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// TextGenerationResponse represents text generation response from provider
type TextGenerationResponse struct {
	Text         string `json:"text"`
	TokensUsed   int    `json:"tokens_used"`
	FinishReason string `json:"finish_reason"`
	Model        string `json:"model"`
}

// TextAnalysisRequest represents text analysis request
type TextAnalysisRequest struct {
	Text        string   `json:"text"`
	AnalysisTypes []string `json:"analysis_types"` // sentiment, readability, engagement, etc.
	Model       string   `json:"model"`
}

// TextAnalysisResponse represents text analysis response
type TextAnalysisResponse struct {
	SentimentScore      float64                `json:"sentiment_score"`
	ReadabilityScore    float64                `json:"readability_score"`
	EngagementScore     float64                `json:"engagement_score"`
	AnalysisDetails     map[string]interface{} `json:"analysis_details"`
	Suggestions         []string               `json:"suggestions"`
	TokensUsed          int                    `json:"tokens_used"`
}

// PromptTemplate represents a prompt template
type PromptTemplate struct {
	Name        string            `json:"name"`
	Template    string            `json:"template"`
	Variables   []string          `json:"variables"`
	ContentType string            `json:"content_type"`
	Tone        string            `json:"tone"`
	Length      string            `json:"length"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// PromptBuilder helps build prompts for different scenarios
type PromptBuilder interface {
	BuildGenerationPrompt(req *GenerationRequest) (string, error)
	BuildImprovementPrompt(req *ImprovementRequest) (string, error)
	GetTemplate(contentType, tone, length string) (*PromptTemplate, error)
}

// Config represents AI service configuration
type Config struct {
	// OpenAI configuration
	OpenAI OpenAIConfig `json:"openai"`
	
	// Anthropic configuration
	Anthropic AnthropicConfig `json:"anthropic"`
	
	// Google configuration
	Google GoogleConfig `json:"google"`
	
	// Default settings
	DefaultModel       string  `json:"default_model"`
	DefaultTemperature float32 `json:"default_temperature"`
	DefaultMaxTokens   int     `json:"default_max_tokens"`
	
	// Rate limiting
	RateLimit RateLimitConfig `json:"rate_limit"`
	
	// Caching
	CacheEnabled bool `json:"cache_enabled"`
	CacheTTL     int  `json:"cache_ttl_seconds"`
}

// OpenAIConfig represents OpenAI configuration
type OpenAIConfig struct {
	APIKey      string `json:"api_key"`
	BaseURL     string `json:"base_url,omitempty"`
	OrgID       string `json:"org_id,omitempty"`
	Enabled     bool   `json:"enabled"`
	MaxRetries  int    `json:"max_retries"`
	Timeout     int    `json:"timeout_seconds"`
}

// AnthropicConfig represents Anthropic configuration
type AnthropicConfig struct {
	APIKey     string `json:"api_key"`
	BaseURL    string `json:"base_url,omitempty"`
	Enabled    bool   `json:"enabled"`
	MaxRetries int    `json:"max_retries"`
	Timeout    int    `json:"timeout_seconds"`
}

// GoogleConfig represents Google AI configuration
type GoogleConfig struct {
	APIKey     string `json:"api_key"`
	ProjectID  string `json:"project_id,omitempty"`
	Enabled    bool   `json:"enabled"`
	MaxRetries int    `json:"max_retries"`
	Timeout    int    `json:"timeout_seconds"`
}

// RateLimitConfig represents rate limiting configuration
type RateLimitConfig struct {
	RequestsPerMinute int `json:"requests_per_minute"`
	TokensPerMinute   int `json:"tokens_per_minute"`
	Enabled           bool `json:"enabled"`
}

// Error types
type AIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AIError) Error() string {
	return e.Message
}

// Common error codes
const (
	ErrCodeInvalidModel     = "INVALID_MODEL"
	ErrCodeRateLimited      = "RATE_LIMITED"
	ErrCodeInsufficientQuota = "INSUFFICIENT_QUOTA"
	ErrCodeInvalidRequest   = "INVALID_REQUEST"
	ErrCodeProviderError    = "PROVIDER_ERROR"
	ErrCodeTimeout          = "TIMEOUT"
	ErrCodeUnknown          = "UNKNOWN"
)

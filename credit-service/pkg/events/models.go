package events

import "time"

// CreditEvent represents a credit-related event
type CreditEvent struct {
	EventID   string                 `json:"event_id"`
	EventType string                 `json:"event_type"`
	UserID    string                 `json:"user_id"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Version   string                 `json:"version"`
	Data      map[string]interface{} `json:"data"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// CreditAddedEvent represents credit added event
type CreditAddedEvent struct {
	EventID       string    `json:"event_id"`
	EventType     string    `json:"event_type"`
	UserID        string    `json:"user_id"`
	Timestamp     time.Time `json:"timestamp"`
	Source        string    `json:"source"`
	Version       string    `json:"version"`
	Amount        int       `json:"amount"`
	NewBalance    int       `json:"new_balance"`
	Source_       string    `json:"credit_source"`
	Description   string    `json:"description"`
	TransactionID string    `json:"transaction_id"`
	ReferenceID   string    `json:"reference_id,omitempty"`
	ReferenceType string    `json:"reference_type,omitempty"`
}

// CreditDeductedEvent represents credit deducted event
type CreditDeductedEvent struct {
	EventID       string    `json:"event_id"`
	EventType     string    `json:"event_type"`
	UserID        string    `json:"user_id"`
	Timestamp     time.Time `json:"timestamp"`
	Source        string    `json:"source"`
	Version       string    `json:"version"`
	Amount        int       `json:"amount"`
	NewBalance    int       `json:"new_balance"`
	Purpose       string    `json:"purpose"`
	Description   string    `json:"description"`
	TransactionID string    `json:"transaction_id"`
	ReferenceID   string    `json:"reference_id,omitempty"`
	ReferenceType string    `json:"reference_type,omitempty"`
}

// CreditTransferredEvent represents credit transferred event
type CreditTransferredEvent struct {
	EventID       string    `json:"event_id"`
	EventType     string    `json:"event_type"`
	FromUserID    string    `json:"from_user_id"`
	ToUserID      string    `json:"to_user_id"`
	Timestamp     time.Time `json:"timestamp"`
	Source        string    `json:"source"`
	Version       string    `json:"version"`
	Amount        int       `json:"amount"`
	Reason        string    `json:"reason"`
	TransactionID string    `json:"transaction_id"`
	ReferenceID   string    `json:"reference_id,omitempty"`
	ReferenceType string    `json:"reference_type,omitempty"`
}

// CreditBalanceUpdatedEvent represents credit balance updated event
type CreditBalanceUpdatedEvent struct {
	EventID      string    `json:"event_id"`
	EventType    string    `json:"event_type"`
	UserID       string    `json:"user_id"`
	Timestamp    time.Time `json:"timestamp"`
	Source       string    `json:"source"`
	Version      string    `json:"version"`
	OldBalance   int       `json:"old_balance"`
	NewBalance   int       `json:"new_balance"`
	ChangeAmount int       `json:"change_amount"`
	ChangeType   string    `json:"change_type"` // "addition", "deduction", "transfer"
	Reason       string    `json:"reason"`
}

// SubscriptionEvent represents a subscription-related event
type SubscriptionEvent struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	UserID         string                 `json:"user_id"`
	SubscriptionID string                 `json:"subscription_id"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
	Version        string                 `json:"version"`
	Data           map[string]interface{} `json:"data"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionActivatedEvent represents subscription activated event
type SubscriptionActivatedEvent struct {
	EventID        string    `json:"event_id"`
	EventType      string    `json:"event_type"`
	UserID         string    `json:"user_id"`
	SubscriptionID string    `json:"subscription_id"`
	Timestamp      time.Time `json:"timestamp"`
	Source         string    `json:"source"`
	Version        string    `json:"version"`
	PlanID         string    `json:"plan_id"`
	PlanName       string    `json:"plan_name"`
	Status         string    `json:"status"`
	StartsAt       time.Time `json:"starts_at"`
	EndsAt         time.Time `json:"ends_at"`
	CreditsAdded   int       `json:"credits_added"`
	PaymentID      string    `json:"payment_id,omitempty"`
}

// SubscriptionCancelledEvent represents subscription cancelled event
type SubscriptionCancelledEvent struct {
	EventID           string    `json:"event_id"`
	EventType         string    `json:"event_type"`
	UserID            string    `json:"user_id"`
	SubscriptionID    string    `json:"subscription_id"`
	Timestamp         time.Time `json:"timestamp"`
	Source            string    `json:"source"`
	Version           string    `json:"version"`
	PlanID            string    `json:"plan_id"`
	PlanName          string    `json:"plan_name"`
	CancelledAt       time.Time `json:"cancelled_at"`
	Reason            string    `json:"reason"`
	CancelAtPeriodEnd bool      `json:"cancel_at_period_end"`
}

// SubscriptionRenewedEvent represents subscription renewed event
type SubscriptionRenewedEvent struct {
	EventID        string    `json:"event_id"`
	EventType      string    `json:"event_type"`
	UserID         string    `json:"user_id"`
	SubscriptionID string    `json:"subscription_id"`
	Timestamp      time.Time `json:"timestamp"`
	Source         string    `json:"source"`
	Version        string    `json:"version"`
	PlanID         string    `json:"plan_id"`
	PlanName       string    `json:"plan_name"`
	NewPeriodStart time.Time `json:"new_period_start"`
	NewPeriodEnd   time.Time `json:"new_period_end"`
	CreditsAdded   int       `json:"credits_added"`
}

// PaymentEvent represents a payment-related event
type PaymentEvent struct {
	EventID   string                 `json:"event_id"`
	EventType string                 `json:"event_type"`
	UserID    string                 `json:"user_id"`
	PaymentID string                 `json:"payment_id"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Version   string                 `json:"version"`
	Data      map[string]interface{} `json:"data"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentSucceededEvent represents payment succeeded event
type PaymentSucceededEvent struct {
	EventID        string    `json:"event_id"`
	EventType      string    `json:"event_type"`
	UserID         string    `json:"user_id"`
	PaymentID      string    `json:"payment_id"`
	Timestamp      time.Time `json:"timestamp"`
	Source         string    `json:"source"`
	Version        string    `json:"version"`
	Amount         int64     `json:"amount"`
	Currency       string    `json:"currency"`
	PaymentMethod  string    `json:"payment_method"`
	SubscriptionID string    `json:"subscription_id,omitempty"`
	InvoiceID      string    `json:"invoice_id,omitempty"`
	Purpose        string    `json:"purpose"` // "subscription", "credit_topup", "one_time"
}

// PaymentFailedEvent represents payment failed event
type PaymentFailedEvent struct {
	EventID        string    `json:"event_id"`
	EventType      string    `json:"event_type"`
	UserID         string    `json:"user_id"`
	PaymentID      string    `json:"payment_id"`
	Timestamp      time.Time `json:"timestamp"`
	Source         string    `json:"source"`
	Version        string    `json:"version"`
	Amount         int64     `json:"amount"`
	Currency       string    `json:"currency"`
	PaymentMethod  string    `json:"payment_method"`
	FailureCode    string    `json:"failure_code"`
	FailureMessage string    `json:"failure_message"`
	SubscriptionID string    `json:"subscription_id,omitempty"`
	InvoiceID      string    `json:"invoice_id,omitempty"`
}

// NotificationEvent represents a notification event to be sent to notification service
type NotificationEvent struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	UserID      string                 `json:"user_id"`
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"`
	Version     string                 `json:"version"`
	Type        string                 `json:"type"`                   // "email", "push", "sms", "in_app"
	Template    string                 `json:"template"`               // notification template name
	Subject     string                 `json:"subject"`                // for email notifications
	Message     string                 `json:"message"`                // notification message
	Data        map[string]interface{} `json:"data"`                   // template data
	Priority    string                 `json:"priority"`               // "low", "normal", "high", "urgent"
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"` // for scheduled notifications
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Subscription Payment Events for Kafka Topics

// SubscriptionPaymentCompletedEvent represents subscription payment completed event
// Topic: subscription.payment.completed
type SubscriptionPaymentCompletedEvent struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	UserID         string                 `json:"user_id"`
	SubscriptionID string                 `json:"subscription_id"`
	PaymentID      string                 `json:"payment_id"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
	Version        string                 `json:"version"`
	Amount         int64                  `json:"amount"`
	Currency       string                 `json:"currency"`
	PaymentMethod  string                 `json:"payment_method"`
	PlanID         string                 `json:"plan_id,omitempty"`
	PlanName       string                 `json:"plan_name,omitempty"`
	CreditsAdded   int                    `json:"credits_added,omitempty"`
	EventData      map[string]interface{} `json:"event_data,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionPaymentFailedEvent represents subscription payment failed event
// Topic: subscription.payment.failed
type SubscriptionPaymentFailedEvent struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	UserID         string                 `json:"user_id"`
	SubscriptionID string                 `json:"subscription_id"`
	PaymentID      string                 `json:"payment_id"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
	Version        string                 `json:"version"`
	Amount         int64                  `json:"amount"`
	Currency       string                 `json:"currency"`
	PaymentMethod  string                 `json:"payment_method"`
	FailureReason  string                 `json:"failure_reason"`
	FailureCode    string                 `json:"failure_code,omitempty"`
	PlanID         string                 `json:"plan_id,omitempty"`
	PlanName       string                 `json:"plan_name,omitempty"`
	EventData      map[string]interface{} `json:"event_data,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionActivatedEventKafka represents subscription activated event for Kafka
// Topic: subscription.activated
type SubscriptionActivatedEventKafka struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	UserID         string                 `json:"user_id"`
	SubscriptionID string                 `json:"subscription_id"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
	Version        string                 `json:"version"`
	PlanID         string                 `json:"plan_id"`
	PlanName       string                 `json:"plan_name"`
	Status         string                 `json:"status"`
	StartsAt       time.Time              `json:"starts_at"`
	EndsAt         time.Time              `json:"ends_at"`
	CreditsAdded   int                    `json:"credits_added"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	EventData      map[string]interface{} `json:"event_data,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionCancelledEventKafka represents subscription cancelled event for Kafka
// Topic: subscription.cancelled
type SubscriptionCancelledEventKafka struct {
	EventID           string                 `json:"event_id"`
	EventType         string                 `json:"event_type"`
	UserID            string                 `json:"user_id"`
	SubscriptionID    string                 `json:"subscription_id"`
	Timestamp         time.Time              `json:"timestamp"`
	Source            string                 `json:"source"`
	Version           string                 `json:"version"`
	PlanID            string                 `json:"plan_id"`
	PlanName          string                 `json:"plan_name"`
	Status            string                 `json:"status"`
	CancelledAt       time.Time              `json:"cancelled_at"`
	Reason            string                 `json:"reason"`
	CancelAtPeriodEnd bool                   `json:"cancel_at_period_end"`
	EventData         map[string]interface{} `json:"event_data,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// GeneralSubscriptionEvent represents general subscription events
// Topic: subscription.events
type GeneralSubscriptionEvent struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	UserID         string                 `json:"user_id"`
	SubscriptionID string                 `json:"subscription_id"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
	Version        string                 `json:"version"`
	Status         string                 `json:"status"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	Amount         int64                  `json:"amount,omitempty"`
	Currency       string                 `json:"currency,omitempty"`
	FailureReason  string                 `json:"failure_reason,omitempty"`
	EventData      map[string]interface{} `json:"event_data"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// Event Helper Functions

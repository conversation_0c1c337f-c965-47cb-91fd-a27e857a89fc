package verification

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTokenService_GenerateEmailVerificationToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("successful token generation", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)

		require.NoError(t, err)
		assert.NotEmpty(t, token)

		// Validate the generated token
		claims, err := service.ValidateEmailVerificationToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "email_verification", claims.TokenType)
		assert.Equal(t, "user-service", claims.Issuer)
		assert.Equal(t, userID, claims.Subject)
	})

	t.Run("token has correct expiry", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidateEmailVerificationToken(token)
		require.NoError(t, err)

		// Should expire in approximately 24 hours
		expectedExpiry := time.Now().Add(24 * time.Hour)
		actualExpiry := claims.ExpiresAt.Time
		
		// Allow 1 minute tolerance
		assert.WithinDuration(t, expectedExpiry, actualExpiry, time.Minute)
	})
}

func TestTokenService_GeneratePasswordResetToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("successful token generation", func(t *testing.T) {
		token, err := service.GeneratePasswordResetToken(userID, email)

		require.NoError(t, err)
		assert.NotEmpty(t, token)

		// Validate the generated token
		claims, err := service.ValidatePasswordResetToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "password_reset", claims.TokenType)
		assert.Equal(t, "user-service", claims.Issuer)
		assert.Equal(t, userID, claims.Subject)
	})

	t.Run("token has correct expiry", func(t *testing.T) {
		token, err := service.GeneratePasswordResetToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidatePasswordResetToken(token)
		require.NoError(t, err)

		// Should expire in approximately 1 hour
		expectedExpiry := time.Now().Add(1 * time.Hour)
		actualExpiry := claims.ExpiresAt.Time
		
		// Allow 1 minute tolerance
		assert.WithinDuration(t, expectedExpiry, actualExpiry, time.Minute)
	})
}

func TestTokenService_ValidateToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("valid email verification token", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidateToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "email_verification", claims.TokenType)
	})

	t.Run("valid password reset token", func(t *testing.T) {
		token, err := service.GeneratePasswordResetToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidateToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "password_reset", claims.TokenType)
	})

	t.Run("invalid token", func(t *testing.T) {
		invalidToken := "invalid.token.here"

		claims, err := service.ValidateToken(invalidToken)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "failed to parse token")
	})

	t.Run("token with wrong secret", func(t *testing.T) {
		// Generate token with different service
		wrongService := NewTokenService("wrong-secret-key")
		token, err := wrongService.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		// Try to validate with correct service
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("expired token", func(t *testing.T) {
		// This test would require mocking time or creating a token with past expiry
		// For now, we'll test the expiry logic with GetTokenInfo
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		// Check if token is not expired
		expired, err := service.IsTokenExpired(token)
		require.NoError(t, err)
		assert.False(t, expired)
	})
}

func TestTokenService_ValidateEmailVerificationToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("valid email verification token", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidateEmailVerificationToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "email_verification", claims.TokenType)
	})

	t.Run("wrong token type", func(t *testing.T) {
		// Generate password reset token
		token, err := service.GeneratePasswordResetToken(userID, email)
		require.NoError(t, err)

		// Try to validate as email verification token
		claims, err := service.ValidateEmailVerificationToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "invalid token type")
	})
}

func TestTokenService_ValidatePasswordResetToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("valid password reset token", func(t *testing.T) {
		token, err := service.GeneratePasswordResetToken(userID, email)
		require.NoError(t, err)

		claims, err := service.ValidatePasswordResetToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, "password_reset", claims.TokenType)
	})

	t.Run("wrong token type", func(t *testing.T) {
		// Generate email verification token
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		// Try to validate as password reset token
		claims, err := service.ValidatePasswordResetToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "invalid token type")
	})
}

func TestTokenService_GetTokenInfo(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("get token info without validation", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		info, err := service.GetTokenInfo(token)
		require.NoError(t, err)
		assert.Equal(t, userID, info.UserID)
		assert.Equal(t, email, info.Email)
		assert.Equal(t, "email_verification", info.TokenType)
		assert.NotEmpty(t, info.TokenID)
		assert.False(t, info.IssuedAt.IsZero())
		assert.False(t, info.ExpiresAt.IsZero())
	})
}

func TestTokenService_IsTokenExpired(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("fresh token is not expired", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		expired, err := service.IsTokenExpired(token)
		require.NoError(t, err)
		assert.False(t, expired)
	})

	t.Run("invalid token returns error", func(t *testing.T) {
		expired, err := service.IsTokenExpired("invalid.token")
		assert.Error(t, err)
		assert.True(t, expired) // Should return true for safety
	})
}

func TestTokenService_RevokeToken(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("revoke token placeholder", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		// This is a placeholder implementation
		err = service.RevokeToken(token)
		assert.NoError(t, err)
	})
}

func TestTokenService_IsTokenRevoked(t *testing.T) {
	secretKey := "test-secret-key-for-testing-purposes-only"
	service := NewTokenService(secretKey)

	userID := "user-123"
	email := "<EMAIL>"

	t.Run("check token revocation placeholder", func(t *testing.T) {
		token, err := service.GenerateEmailVerificationToken(userID, email)
		require.NoError(t, err)

		// This is a placeholder implementation
		revoked, err := service.IsTokenRevoked(token)
		require.NoError(t, err)
		assert.False(t, revoked) // Should return false as placeholder
	})
}

func TestNewTokenService(t *testing.T) {
	t.Run("create token service with secret", func(t *testing.T) {
		secretKey := "test-secret-key"
		service := NewTokenService(secretKey)

		assert.NotNil(t, service)
		assert.Equal(t, []byte(secretKey), service.secretKey)
	})

	t.Run("create token service with empty secret", func(t *testing.T) {
		service := NewTokenService("")

		assert.NotNil(t, service)
		assert.Equal(t, []byte(""), service.secretKey)
	})
}

package events

// import (
// "context"

// "github.com/social-content-ai/credit-service/usecase/credit"
// sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
// "github.com/social-content-ai/pkg-shared/logging"
// )

// // Consumer handles consuming events from Kafka
// type Consumer struct {
// 	consumer *sharedKafka.Consumer
// 	handler  *Handler
// 	logger   logging.Logger
// }

// // NewConsumer creates a new event consumer
// func NewConsumer(kafkaConfig *sharedKafka.Config, creditUC credit.UseCase, logger logging.Logger) *Consumer {
// handler := NewHandler(creditUC, logger)
// consumer := sharedKafka.NewConsumer(kafkaConfig, handler, logger)
// return &Consumer{
// consumer: consumer,
// handler:  handler,
// logger:   logger,
// }
// }

// // Start starts consuming events from subscribed topics
// func (c *Consumer) Start(ctx context.Context) error {
// // Subscribe to topics that credit service needs to handle
// topics := []string{
// "user.events",    // UserRegistered, UserUpgraded
// "billing.events", // BankTransferConfirmed, PaymentCompleted, SubscriptionUpgraded
// }

// // Start consuming
// return c.consumer.Start(topics)
// }

// // Stop stops the consumer
// func (c *Consumer) Stop() error {
// return c.consumer.Stop()
// }

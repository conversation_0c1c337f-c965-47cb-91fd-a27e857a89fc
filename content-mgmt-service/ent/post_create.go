// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
)

// PostCreate is the builder for creating a Post entity.
type PostCreate struct {
	config
	mutation *PostMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (pc *PostCreate) SetUserID(u uuid.UUID) *PostCreate {
	pc.mutation.SetUserID(u)
	return pc
}

// SetWorkspaceID sets the "workspace_id" field.
func (pc *PostCreate) SetWorkspaceID(s string) *PostCreate {
	pc.mutation.SetWorkspaceID(s)
	return pc
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (pc *PostCreate) SetNillableWorkspaceID(s *string) *PostCreate {
	if s != nil {
		pc.SetWorkspaceID(*s)
	}
	return pc
}

// SetTemplateID sets the "template_id" field.
func (pc *PostCreate) SetTemplateID(s string) *PostCreate {
	pc.mutation.SetTemplateID(s)
	return pc
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (pc *PostCreate) SetNillableTemplateID(s *string) *PostCreate {
	if s != nil {
		pc.SetTemplateID(*s)
	}
	return pc
}

// SetGenerationID sets the "generation_id" field.
func (pc *PostCreate) SetGenerationID(s string) *PostCreate {
	pc.mutation.SetGenerationID(s)
	return pc
}

// SetNillableGenerationID sets the "generation_id" field if the given value is not nil.
func (pc *PostCreate) SetNillableGenerationID(s *string) *PostCreate {
	if s != nil {
		pc.SetGenerationID(*s)
	}
	return pc
}

// SetTitle sets the "title" field.
func (pc *PostCreate) SetTitle(s string) *PostCreate {
	pc.mutation.SetTitle(s)
	return pc
}

// SetContent sets the "content" field.
func (pc *PostCreate) SetContent(s string) *PostCreate {
	pc.mutation.SetContent(s)
	return pc
}

// SetOriginalContent sets the "original_content" field.
func (pc *PostCreate) SetOriginalContent(s string) *PostCreate {
	pc.mutation.SetOriginalContent(s)
	return pc
}

// SetNillableOriginalContent sets the "original_content" field if the given value is not nil.
func (pc *PostCreate) SetNillableOriginalContent(s *string) *PostCreate {
	if s != nil {
		pc.SetOriginalContent(*s)
	}
	return pc
}

// SetPlatforms sets the "platforms" field.
func (pc *PostCreate) SetPlatforms(s []string) *PostCreate {
	pc.mutation.SetPlatforms(s)
	return pc
}

// SetStatus sets the "status" field.
func (pc *PostCreate) SetStatus(po post.Status) *PostCreate {
	pc.mutation.SetStatus(po)
	return pc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pc *PostCreate) SetNillableStatus(po *post.Status) *PostCreate {
	if po != nil {
		pc.SetStatus(*po)
	}
	return pc
}

// SetScheduledAt sets the "scheduled_at" field.
func (pc *PostCreate) SetScheduledAt(t time.Time) *PostCreate {
	pc.mutation.SetScheduledAt(t)
	return pc
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (pc *PostCreate) SetNillableScheduledAt(t *time.Time) *PostCreate {
	if t != nil {
		pc.SetScheduledAt(*t)
	}
	return pc
}

// SetPublishedAt sets the "published_at" field.
func (pc *PostCreate) SetPublishedAt(t time.Time) *PostCreate {
	pc.mutation.SetPublishedAt(t)
	return pc
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (pc *PostCreate) SetNillablePublishedAt(t *time.Time) *PostCreate {
	if t != nil {
		pc.SetPublishedAt(*t)
	}
	return pc
}

// SetPlatformPosts sets the "platform_posts" field.
func (pc *PostCreate) SetPlatformPosts(m map[string]interface{}) *PostCreate {
	pc.mutation.SetPlatformPosts(m)
	return pc
}

// SetHashtags sets the "hashtags" field.
func (pc *PostCreate) SetHashtags(s []string) *PostCreate {
	pc.mutation.SetHashtags(s)
	return pc
}

// SetMentions sets the "mentions" field.
func (pc *PostCreate) SetMentions(s []string) *PostCreate {
	pc.mutation.SetMentions(s)
	return pc
}

// SetMediaUrls sets the "media_urls" field.
func (pc *PostCreate) SetMediaUrls(s []string) *PostCreate {
	pc.mutation.SetMediaUrls(s)
	return pc
}

// SetVariables sets the "variables" field.
func (pc *PostCreate) SetVariables(m map[string]string) *PostCreate {
	pc.mutation.SetVariables(m)
	return pc
}

// SetVisibility sets the "visibility" field.
func (pc *PostCreate) SetVisibility(po post.Visibility) *PostCreate {
	pc.mutation.SetVisibility(po)
	return pc
}

// SetNillableVisibility sets the "visibility" field if the given value is not nil.
func (pc *PostCreate) SetNillableVisibility(po *post.Visibility) *PostCreate {
	if po != nil {
		pc.SetVisibility(*po)
	}
	return pc
}

// SetLanguage sets the "language" field.
func (pc *PostCreate) SetLanguage(s string) *PostCreate {
	pc.mutation.SetLanguage(s)
	return pc
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (pc *PostCreate) SetNillableLanguage(s *string) *PostCreate {
	if s != nil {
		pc.SetLanguage(*s)
	}
	return pc
}

// SetAnalytics sets the "analytics" field.
func (pc *PostCreate) SetAnalytics(m map[string]interface{}) *PostCreate {
	pc.mutation.SetAnalytics(m)
	return pc
}

// SetMetadata sets the "metadata" field.
func (pc *PostCreate) SetMetadata(m map[string]interface{}) *PostCreate {
	pc.mutation.SetMetadata(m)
	return pc
}

// SetCreatedAt sets the "created_at" field.
func (pc *PostCreate) SetCreatedAt(t time.Time) *PostCreate {
	pc.mutation.SetCreatedAt(t)
	return pc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pc *PostCreate) SetNillableCreatedAt(t *time.Time) *PostCreate {
	if t != nil {
		pc.SetCreatedAt(*t)
	}
	return pc
}

// SetUpdatedAt sets the "updated_at" field.
func (pc *PostCreate) SetUpdatedAt(t time.Time) *PostCreate {
	pc.mutation.SetUpdatedAt(t)
	return pc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pc *PostCreate) SetNillableUpdatedAt(t *time.Time) *PostCreate {
	if t != nil {
		pc.SetUpdatedAt(*t)
	}
	return pc
}

// SetDeletedAt sets the "deleted_at" field.
func (pc *PostCreate) SetDeletedAt(t time.Time) *PostCreate {
	pc.mutation.SetDeletedAt(t)
	return pc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pc *PostCreate) SetNillableDeletedAt(t *time.Time) *PostCreate {
	if t != nil {
		pc.SetDeletedAt(*t)
	}
	return pc
}

// SetID sets the "id" field.
func (pc *PostCreate) SetID(u uuid.UUID) *PostCreate {
	pc.mutation.SetID(u)
	return pc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (pc *PostCreate) SetNillableID(u *uuid.UUID) *PostCreate {
	if u != nil {
		pc.SetID(*u)
	}
	return pc
}

// Mutation returns the PostMutation object of the builder.
func (pc *PostCreate) Mutation() *PostMutation {
	return pc.mutation
}

// Save creates the Post in the database.
func (pc *PostCreate) Save(ctx context.Context) (*Post, error) {
	pc.defaults()
	return withHooks(ctx, pc.sqlSave, pc.mutation, pc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pc *PostCreate) SaveX(ctx context.Context) *Post {
	v, err := pc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pc *PostCreate) Exec(ctx context.Context) error {
	_, err := pc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pc *PostCreate) ExecX(ctx context.Context) {
	if err := pc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pc *PostCreate) defaults() {
	if _, ok := pc.mutation.Status(); !ok {
		v := post.DefaultStatus
		pc.mutation.SetStatus(v)
	}
	if _, ok := pc.mutation.Visibility(); !ok {
		v := post.DefaultVisibility
		pc.mutation.SetVisibility(v)
	}
	if _, ok := pc.mutation.Language(); !ok {
		v := post.DefaultLanguage
		pc.mutation.SetLanguage(v)
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		v := post.DefaultCreatedAt()
		pc.mutation.SetCreatedAt(v)
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		v := post.DefaultUpdatedAt()
		pc.mutation.SetUpdatedAt(v)
	}
	if _, ok := pc.mutation.ID(); !ok {
		v := post.DefaultID()
		pc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pc *PostCreate) check() error {
	if _, ok := pc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Post.user_id"`)}
	}
	if v, ok := pc.mutation.WorkspaceID(); ok {
		if err := post.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Post.workspace_id": %w`, err)}
		}
	}
	if v, ok := pc.mutation.TemplateID(); ok {
		if err := post.TemplateIDValidator(v); err != nil {
			return &ValidationError{Name: "template_id", err: fmt.Errorf(`ent: validator failed for field "Post.template_id": %w`, err)}
		}
	}
	if v, ok := pc.mutation.GenerationID(); ok {
		if err := post.GenerationIDValidator(v); err != nil {
			return &ValidationError{Name: "generation_id", err: fmt.Errorf(`ent: validator failed for field "Post.generation_id": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "Post.title"`)}
	}
	if v, ok := pc.mutation.Title(); ok {
		if err := post.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Post.title": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "Post.content"`)}
	}
	if _, ok := pc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Post.status"`)}
	}
	if v, ok := pc.mutation.Status(); ok {
		if err := post.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Post.status": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Visibility(); !ok {
		return &ValidationError{Name: "visibility", err: errors.New(`ent: missing required field "Post.visibility"`)}
	}
	if v, ok := pc.mutation.Visibility(); ok {
		if err := post.VisibilityValidator(v); err != nil {
			return &ValidationError{Name: "visibility", err: fmt.Errorf(`ent: validator failed for field "Post.visibility": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Language(); !ok {
		return &ValidationError{Name: "language", err: errors.New(`ent: missing required field "Post.language"`)}
	}
	if v, ok := pc.mutation.Language(); ok {
		if err := post.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Post.language": %w`, err)}
		}
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Post.created_at"`)}
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Post.updated_at"`)}
	}
	return nil
}

func (pc *PostCreate) sqlSave(ctx context.Context) (*Post, error) {
	if err := pc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	pc.mutation.id = &_node.ID
	pc.mutation.done = true
	return _node, nil
}

func (pc *PostCreate) createSpec() (*Post, *sqlgraph.CreateSpec) {
	var (
		_node = &Post{config: pc.config}
		_spec = sqlgraph.NewCreateSpec(post.Table, sqlgraph.NewFieldSpec(post.FieldID, field.TypeUUID))
	)
	if id, ok := pc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := pc.mutation.UserID(); ok {
		_spec.SetField(post.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := pc.mutation.WorkspaceID(); ok {
		_spec.SetField(post.FieldWorkspaceID, field.TypeString, value)
		_node.WorkspaceID = value
	}
	if value, ok := pc.mutation.TemplateID(); ok {
		_spec.SetField(post.FieldTemplateID, field.TypeString, value)
		_node.TemplateID = value
	}
	if value, ok := pc.mutation.GenerationID(); ok {
		_spec.SetField(post.FieldGenerationID, field.TypeString, value)
		_node.GenerationID = value
	}
	if value, ok := pc.mutation.Title(); ok {
		_spec.SetField(post.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := pc.mutation.Content(); ok {
		_spec.SetField(post.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := pc.mutation.OriginalContent(); ok {
		_spec.SetField(post.FieldOriginalContent, field.TypeString, value)
		_node.OriginalContent = value
	}
	if value, ok := pc.mutation.Platforms(); ok {
		_spec.SetField(post.FieldPlatforms, field.TypeJSON, value)
		_node.Platforms = value
	}
	if value, ok := pc.mutation.Status(); ok {
		_spec.SetField(post.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := pc.mutation.ScheduledAt(); ok {
		_spec.SetField(post.FieldScheduledAt, field.TypeTime, value)
		_node.ScheduledAt = value
	}
	if value, ok := pc.mutation.PublishedAt(); ok {
		_spec.SetField(post.FieldPublishedAt, field.TypeTime, value)
		_node.PublishedAt = value
	}
	if value, ok := pc.mutation.PlatformPosts(); ok {
		_spec.SetField(post.FieldPlatformPosts, field.TypeJSON, value)
		_node.PlatformPosts = value
	}
	if value, ok := pc.mutation.Hashtags(); ok {
		_spec.SetField(post.FieldHashtags, field.TypeJSON, value)
		_node.Hashtags = value
	}
	if value, ok := pc.mutation.Mentions(); ok {
		_spec.SetField(post.FieldMentions, field.TypeJSON, value)
		_node.Mentions = value
	}
	if value, ok := pc.mutation.MediaUrls(); ok {
		_spec.SetField(post.FieldMediaUrls, field.TypeJSON, value)
		_node.MediaUrls = value
	}
	if value, ok := pc.mutation.Variables(); ok {
		_spec.SetField(post.FieldVariables, field.TypeJSON, value)
		_node.Variables = value
	}
	if value, ok := pc.mutation.Visibility(); ok {
		_spec.SetField(post.FieldVisibility, field.TypeEnum, value)
		_node.Visibility = value
	}
	if value, ok := pc.mutation.Language(); ok {
		_spec.SetField(post.FieldLanguage, field.TypeString, value)
		_node.Language = value
	}
	if value, ok := pc.mutation.Analytics(); ok {
		_spec.SetField(post.FieldAnalytics, field.TypeJSON, value)
		_node.Analytics = value
	}
	if value, ok := pc.mutation.Metadata(); ok {
		_spec.SetField(post.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := pc.mutation.CreatedAt(); ok {
		_spec.SetField(post.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pc.mutation.UpdatedAt(); ok {
		_spec.SetField(post.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pc.mutation.DeletedAt(); ok {
		_spec.SetField(post.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// PostCreateBulk is the builder for creating many Post entities in bulk.
type PostCreateBulk struct {
	config
	err      error
	builders []*PostCreate
}

// Save creates the Post entities in the database.
func (pcb *PostCreateBulk) Save(ctx context.Context) ([]*Post, error) {
	if pcb.err != nil {
		return nil, pcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pcb.builders))
	nodes := make([]*Post, len(pcb.builders))
	mutators := make([]Mutator, len(pcb.builders))
	for i := range pcb.builders {
		func(i int, root context.Context) {
			builder := pcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PostMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pcb *PostCreateBulk) SaveX(ctx context.Context) []*Post {
	v, err := pcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcb *PostCreateBulk) Exec(ctx context.Context) error {
	_, err := pcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcb *PostCreateBulk) ExecX(ctx context.Context) {
	if err := pcb.Exec(ctx); err != nil {
		panic(err)
	}
}

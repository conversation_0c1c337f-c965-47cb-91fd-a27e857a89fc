package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Improvement holds the schema definition for the Improvement entity.
type Improvement struct {
	ent.Schema
}

// Fields of the Improvement.
func (Improvement) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.Text("original_text"),
		field.Text("improved_text"),
		field.Enum("improvement_type").
			Values("grammar", "style", "engagement", "clarity", "tone", "length", "seo").
			Default("grammar"),
		field.String("target_tone").
			MaxLen(50).
			Optional(),
		field.Enum("target_length").
			Values("shorter", "longer", "same").
			Default("same"),
		field.String("platform").
			MaxLen(50).
			Optional(),
		field.Text("context").
			Optional(),
		field.String("model").
			MaxLen(50).
			Default("gpt-4"),
		field.String("workspace_id").
			MaxLen(36).
			Optional(),
		field.Float("readability_score").
			Default(0.0).
			Range(0.0, 10.0),
		field.Float("engagement_potential").
			Default(0.0).
			Range(0.0, 10.0),
		field.Float("sentiment_score").
			Default(0.0).
			Range(-1.0, 1.0),
		field.Int("credits_used").
			Default(1),
		field.Float("processing_time").
			Default(0.0),
		field.Enum("status").
			Values("pending", "completed", "failed").
			Default("pending"),
		field.Text("error_message").
			Optional(),
		field.JSON("analysis_details", map[string]interface{}{}).
			Optional(),
		field.JSON("suggestions", []string{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Improvement.
func (Improvement) Edges() []ent.Edge {
	return nil
}

// Indexes of the Improvement.
func (Improvement) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("improvement_type"),
		index.Fields("platform"),
		index.Fields("status"),
		index.Fields("workspace_id"),
		index.Fields("created_at"),
		index.Fields("user_id", "created_at"),
	}
}

package sms

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TwilioConfig holds Twilio SMS configuration
type TwilioConfig struct {
	AccountSID string
	AuthToken  string
	FromNumber string
	BaseURL    string
}

// TwilioSender implements SMSSender using Twilio API
type TwilioSender struct {
	config TwilioConfig
	client *http.Client
	logger logging.Logger
}

// NewTwilioSender creates a new Twilio SMS sender
func NewTwilioSender(config TwilioConfig, logger logging.Logger) channels.SMSSender {
	if config.BaseURL == "" {
		config.BaseURL = "https://api.twilio.com/2010-04-01"
	}

	return &TwilioSender{
		config: config,
		client: &http.Client{},
		logger: logger,
	}
}

// SendSMS sends an SMS message using Twilio
func (t *TwilioSender) SendSMS(ctx context.Context, message *channels.SMSMessage) error {
	t.logger.WithFields(map[string]interface{}{
		"to":   message.To,
		"body": message.Body,
	}).Info("Sending SMS via Twilio")

	// Validate message
	if message.To == "" {
		return fmt.Errorf("recipient phone number is required")
	}
	if message.Body == "" {
		return fmt.Errorf("message body is required")
	}

	// Prepare request data
	data := url.Values{}
	data.Set("To", message.To)
	data.Set("From", t.config.FromNumber)
	data.Set("Body", message.Body)

	// Create request
	apiURL := fmt.Sprintf("%s/Accounts/%s/Messages.json", t.config.BaseURL, t.config.AccountSID)
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		t.logger.WithError(err).Error("Failed to create Twilio request")
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(t.config.AccountSID, t.config.AuthToken)

	// Send request
	resp, err := t.client.Do(req)
	if err != nil {
		t.logger.WithError(err).Error("Failed to send SMS via Twilio")
		return fmt.Errorf("failed to send SMS: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		t.logger.WithField("status_code", resp.StatusCode).Error("Twilio API returned error")
		return fmt.Errorf("Twilio API error: status code %d", resp.StatusCode)
	}

	t.logger.WithField("to", message.To).Info("SMS sent successfully via Twilio")
	return nil
}

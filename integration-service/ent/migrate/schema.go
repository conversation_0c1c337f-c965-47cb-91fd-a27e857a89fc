// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// PlatformIntegrationsColumns holds the columns for the "platform_integrations" table.
	PlatformIntegrationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "workspace_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "platform", Type: field.TypeEnum, Enums: []string{"facebook", "instagram", "twitter", "linkedin", "tiktok", "youtube", "pinterest", "snapchat"}},
		{Name: "platform_user_id", Type: field.TypeString, Size: 100},
		{Name: "platform_username", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "platform_display_name", Type: field.TypeString, Nullable: true, Size: 200},
		{Name: "platform_avatar_url", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "access_token", Type: field.TypeString, Size: 2147483647},
		{Name: "refresh_token", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "token_expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "scopes", Type: field.TypeJSON, Nullable: true},
		{Name: "platform_data", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"active", "expired", "revoked", "error", "pending"}, Default: "pending"},
		{Name: "error_message", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "last_sync_at", Type: field.TypeTime, Nullable: true},
		{Name: "last_post_at", Type: field.TypeTime, Nullable: true},
		{Name: "capabilities", Type: field.TypeJSON, Nullable: true},
		{Name: "settings", Type: field.TypeJSON, Nullable: true},
		{Name: "auto_publish", Type: field.TypeBool, Default: false},
		{Name: "is_primary", Type: field.TypeBool, Default: false},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// PlatformIntegrationsTable holds the schema information for the "platform_integrations" table.
	PlatformIntegrationsTable = &schema.Table{
		Name:       "platform_integrations",
		Columns:    PlatformIntegrationsColumns,
		PrimaryKey: []*schema.Column{PlatformIntegrationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "platformintegration_user_id",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[1]},
			},
			{
				Name:    "platformintegration_workspace_id",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[2]},
			},
			{
				Name:    "platformintegration_platform",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[3]},
			},
			{
				Name:    "platformintegration_platform_user_id",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[4]},
			},
			{
				Name:    "platformintegration_status",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[13]},
			},
			{
				Name:    "platformintegration_token_expires_at",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[10]},
			},
			{
				Name:    "platformintegration_is_primary",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[20]},
			},
			{
				Name:    "platformintegration_created_at",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[22]},
			},
			{
				Name:    "platformintegration_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[24]},
			},
			{
				Name:    "platformintegration_user_id_platform",
				Unique:  false,
				Columns: []*schema.Column{PlatformIntegrationsColumns[1], PlatformIntegrationsColumns[3]},
			},
			{
				Name:    "platformintegration_user_id_platform_platform_user_id",
				Unique:  true,
				Columns: []*schema.Column{PlatformIntegrationsColumns[1], PlatformIntegrationsColumns[3], PlatformIntegrationsColumns[4]},
			},
		},
	}
	// PublishedPostsColumns holds the columns for the "published_posts" table.
	PublishedPostsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "post_id", Type: field.TypeString, Size: 36},
		{Name: "integration_id", Type: field.TypeString, Size: 36},
		{Name: "platform", Type: field.TypeEnum, Enums: []string{"facebook", "instagram", "twitter", "linkedin", "tiktok", "youtube", "pinterest", "snapchat"}},
		{Name: "platform_post_id", Type: field.TypeString, Size: 200},
		{Name: "platform_post_url", Type: field.TypeString, Nullable: true, Size: 1000},
		{Name: "content", Type: field.TypeString, Size: 2147483647},
		{Name: "media_urls", Type: field.TypeJSON, Nullable: true},
		{Name: "hashtags", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "published", "failed", "deleted", "scheduled"}, Default: "pending"},
		{Name: "error_message", Type: field.TypeString, Nullable: true, Size: 1000},
		{Name: "scheduled_at", Type: field.TypeTime, Nullable: true},
		{Name: "published_at", Type: field.TypeTime, Nullable: true},
		{Name: "platform_response", Type: field.TypeJSON, Nullable: true},
		{Name: "analytics", Type: field.TypeJSON, Nullable: true},
		{Name: "last_analytics_sync", Type: field.TypeTime, Nullable: true},
		{Name: "retry_count", Type: field.TypeInt, Default: 0},
		{Name: "next_retry_at", Type: field.TypeTime, Nullable: true},
		{Name: "publish_options", Type: field.TypeJSON, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
	}
	// PublishedPostsTable holds the schema information for the "published_posts" table.
	PublishedPostsTable = &schema.Table{
		Name:       "published_posts",
		Columns:    PublishedPostsColumns,
		PrimaryKey: []*schema.Column{PublishedPostsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "publishedpost_user_id",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[1]},
			},
			{
				Name:    "publishedpost_post_id",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[2]},
			},
			{
				Name:    "publishedpost_integration_id",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[3]},
			},
			{
				Name:    "publishedpost_platform",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[4]},
			},
			{
				Name:    "publishedpost_platform_post_id",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[5]},
			},
			{
				Name:    "publishedpost_status",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[10]},
			},
			{
				Name:    "publishedpost_scheduled_at",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[12]},
			},
			{
				Name:    "publishedpost_published_at",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[13]},
			},
			{
				Name:    "publishedpost_next_retry_at",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[18]},
			},
			{
				Name:    "publishedpost_created_at",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[21]},
			},
			{
				Name:    "publishedpost_deleted_at",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[23]},
			},
			{
				Name:    "publishedpost_user_id_platform",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[1], PublishedPostsColumns[4]},
			},
			{
				Name:    "publishedpost_post_id_platform",
				Unique:  false,
				Columns: []*schema.Column{PublishedPostsColumns[2], PublishedPostsColumns[4]},
			},
			{
				Name:    "publishedpost_platform_platform_post_id",
				Unique:  true,
				Columns: []*schema.Column{PublishedPostsColumns[4], PublishedPostsColumns[5]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		PlatformIntegrationsTable,
		PublishedPostsTable,
	}
)

func init() {
}

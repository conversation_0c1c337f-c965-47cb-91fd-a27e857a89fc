// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: data-mgmt/v1/backup.proto

package datamgmtv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BackupServiceClient is the client API for BackupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BackupServiceClient interface {
	// Backup operations
	CreateBackup(ctx context.Context, in *CreateBackupRequest, opts ...grpc.CallOption) (*CreateBackupResponse, error)
	GetBackup(ctx context.Context, in *GetBackupRequest, opts ...grpc.CallOption) (*BackupJob, error)
	ListBackups(ctx context.Context, in *ListBackupsRequest, opts ...grpc.CallOption) (*ListBackupsResponse, error)
	DeleteBackup(ctx context.Context, in *DeleteBackupRequest, opts ...grpc.CallOption) (*Empty, error)
	RefreshDownloadUrl(ctx context.Context, in *RefreshDownloadUrlRequest, opts ...grpc.CallOption) (*RefreshDownloadUrlResponse, error)
	// Data export operations
	ExportData(ctx context.Context, in *DataExportRequest, opts ...grpc.CallOption) (*DataExportResponse, error)
	GetExport(ctx context.Context, in *GetExportRequest, opts ...grpc.CallOption) (*DataExportResponse, error)
	ListExports(ctx context.Context, in *ListExportsRequest, opts ...grpc.CallOption) (*ListExportsResponse, error)
	// GDPR compliance operations
	RequestAccountDeletion(ctx context.Context, in *AccountDeletionRequest, opts ...grpc.CallOption) (*AccountDeletionResponse, error)
	GetDeletionStatus(ctx context.Context, in *GetDeletionStatusRequest, opts ...grpc.CallOption) (*DeletionStatus, error)
	// Maintenance operations
	CleanupExpired(ctx context.Context, in *CleanupExpiredRequest, opts ...grpc.CallOption) (*CleanupExpiredResponse, error)
}

type backupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBackupServiceClient(cc grpc.ClientConnInterface) BackupServiceClient {
	return &backupServiceClient{cc}
}

func (c *backupServiceClient) CreateBackup(ctx context.Context, in *CreateBackupRequest, opts ...grpc.CallOption) (*CreateBackupResponse, error) {
	out := new(CreateBackupResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/CreateBackup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) GetBackup(ctx context.Context, in *GetBackupRequest, opts ...grpc.CallOption) (*BackupJob, error) {
	out := new(BackupJob)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/GetBackup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) ListBackups(ctx context.Context, in *ListBackupsRequest, opts ...grpc.CallOption) (*ListBackupsResponse, error) {
	out := new(ListBackupsResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/ListBackups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) DeleteBackup(ctx context.Context, in *DeleteBackupRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/DeleteBackup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) RefreshDownloadUrl(ctx context.Context, in *RefreshDownloadUrlRequest, opts ...grpc.CallOption) (*RefreshDownloadUrlResponse, error) {
	out := new(RefreshDownloadUrlResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/RefreshDownloadUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) ExportData(ctx context.Context, in *DataExportRequest, opts ...grpc.CallOption) (*DataExportResponse, error) {
	out := new(DataExportResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/ExportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) GetExport(ctx context.Context, in *GetExportRequest, opts ...grpc.CallOption) (*DataExportResponse, error) {
	out := new(DataExportResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/GetExport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) ListExports(ctx context.Context, in *ListExportsRequest, opts ...grpc.CallOption) (*ListExportsResponse, error) {
	out := new(ListExportsResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/ListExports", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) RequestAccountDeletion(ctx context.Context, in *AccountDeletionRequest, opts ...grpc.CallOption) (*AccountDeletionResponse, error) {
	out := new(AccountDeletionResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/RequestAccountDeletion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) GetDeletionStatus(ctx context.Context, in *GetDeletionStatusRequest, opts ...grpc.CallOption) (*DeletionStatus, error) {
	out := new(DeletionStatus)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/GetDeletionStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backupServiceClient) CleanupExpired(ctx context.Context, in *CleanupExpiredRequest, opts ...grpc.CallOption) (*CleanupExpiredResponse, error) {
	out := new(CleanupExpiredResponse)
	err := c.cc.Invoke(ctx, "/data_mgmt.v1.BackupService/CleanupExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackupServiceServer is the server API for BackupService service.
// All implementations must embed UnimplementedBackupServiceServer
// for forward compatibility
type BackupServiceServer interface {
	// Backup operations
	CreateBackup(context.Context, *CreateBackupRequest) (*CreateBackupResponse, error)
	GetBackup(context.Context, *GetBackupRequest) (*BackupJob, error)
	ListBackups(context.Context, *ListBackupsRequest) (*ListBackupsResponse, error)
	DeleteBackup(context.Context, *DeleteBackupRequest) (*Empty, error)
	RefreshDownloadUrl(context.Context, *RefreshDownloadUrlRequest) (*RefreshDownloadUrlResponse, error)
	// Data export operations
	ExportData(context.Context, *DataExportRequest) (*DataExportResponse, error)
	GetExport(context.Context, *GetExportRequest) (*DataExportResponse, error)
	ListExports(context.Context, *ListExportsRequest) (*ListExportsResponse, error)
	// GDPR compliance operations
	RequestAccountDeletion(context.Context, *AccountDeletionRequest) (*AccountDeletionResponse, error)
	GetDeletionStatus(context.Context, *GetDeletionStatusRequest) (*DeletionStatus, error)
	// Maintenance operations
	CleanupExpired(context.Context, *CleanupExpiredRequest) (*CleanupExpiredResponse, error)
	mustEmbedUnimplementedBackupServiceServer()
}

// UnimplementedBackupServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBackupServiceServer struct {
}

func (UnimplementedBackupServiceServer) CreateBackup(context.Context, *CreateBackupRequest) (*CreateBackupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBackup not implemented")
}
func (UnimplementedBackupServiceServer) GetBackup(context.Context, *GetBackupRequest) (*BackupJob, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBackup not implemented")
}
func (UnimplementedBackupServiceServer) ListBackups(context.Context, *ListBackupsRequest) (*ListBackupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBackups not implemented")
}
func (UnimplementedBackupServiceServer) DeleteBackup(context.Context, *DeleteBackupRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBackup not implemented")
}
func (UnimplementedBackupServiceServer) RefreshDownloadUrl(context.Context, *RefreshDownloadUrlRequest) (*RefreshDownloadUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDownloadUrl not implemented")
}
func (UnimplementedBackupServiceServer) ExportData(context.Context, *DataExportRequest) (*DataExportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportData not implemented")
}
func (UnimplementedBackupServiceServer) GetExport(context.Context, *GetExportRequest) (*DataExportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExport not implemented")
}
func (UnimplementedBackupServiceServer) ListExports(context.Context, *ListExportsRequest) (*ListExportsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExports not implemented")
}
func (UnimplementedBackupServiceServer) RequestAccountDeletion(context.Context, *AccountDeletionRequest) (*AccountDeletionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestAccountDeletion not implemented")
}
func (UnimplementedBackupServiceServer) GetDeletionStatus(context.Context, *GetDeletionStatusRequest) (*DeletionStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeletionStatus not implemented")
}
func (UnimplementedBackupServiceServer) CleanupExpired(context.Context, *CleanupExpiredRequest) (*CleanupExpiredResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanupExpired not implemented")
}
func (UnimplementedBackupServiceServer) mustEmbedUnimplementedBackupServiceServer() {}

// UnsafeBackupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BackupServiceServer will
// result in compilation errors.
type UnsafeBackupServiceServer interface {
	mustEmbedUnimplementedBackupServiceServer()
}

func RegisterBackupServiceServer(s grpc.ServiceRegistrar, srv BackupServiceServer) {
	s.RegisterService(&BackupService_ServiceDesc, srv)
}

func _BackupService_CreateBackup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBackupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).CreateBackup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/CreateBackup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).CreateBackup(ctx, req.(*CreateBackupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_GetBackup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBackupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).GetBackup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/GetBackup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).GetBackup(ctx, req.(*GetBackupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_ListBackups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBackupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).ListBackups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/ListBackups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).ListBackups(ctx, req.(*ListBackupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_DeleteBackup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBackupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).DeleteBackup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/DeleteBackup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).DeleteBackup(ctx, req.(*DeleteBackupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_RefreshDownloadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshDownloadUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).RefreshDownloadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/RefreshDownloadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).RefreshDownloadUrl(ctx, req.(*RefreshDownloadUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_ExportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataExportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).ExportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/ExportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).ExportData(ctx, req.(*DataExportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_GetExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).GetExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/GetExport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).GetExport(ctx, req.(*GetExportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_ListExports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExportsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).ListExports(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/ListExports",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).ListExports(ctx, req.(*ListExportsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_RequestAccountDeletion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountDeletionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).RequestAccountDeletion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/RequestAccountDeletion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).RequestAccountDeletion(ctx, req.(*AccountDeletionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_GetDeletionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeletionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).GetDeletionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/GetDeletionStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).GetDeletionStatus(ctx, req.(*GetDeletionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BackupService_CleanupExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanupExpiredRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackupServiceServer).CleanupExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_mgmt.v1.BackupService/CleanupExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackupServiceServer).CleanupExpired(ctx, req.(*CleanupExpiredRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BackupService_ServiceDesc is the grpc.ServiceDesc for BackupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BackupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "data_mgmt.v1.BackupService",
	HandlerType: (*BackupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBackup",
			Handler:    _BackupService_CreateBackup_Handler,
		},
		{
			MethodName: "GetBackup",
			Handler:    _BackupService_GetBackup_Handler,
		},
		{
			MethodName: "ListBackups",
			Handler:    _BackupService_ListBackups_Handler,
		},
		{
			MethodName: "DeleteBackup",
			Handler:    _BackupService_DeleteBackup_Handler,
		},
		{
			MethodName: "RefreshDownloadUrl",
			Handler:    _BackupService_RefreshDownloadUrl_Handler,
		},
		{
			MethodName: "ExportData",
			Handler:    _BackupService_ExportData_Handler,
		},
		{
			MethodName: "GetExport",
			Handler:    _BackupService_GetExport_Handler,
		},
		{
			MethodName: "ListExports",
			Handler:    _BackupService_ListExports_Handler,
		},
		{
			MethodName: "RequestAccountDeletion",
			Handler:    _BackupService_RequestAccountDeletion_Handler,
		},
		{
			MethodName: "GetDeletionStatus",
			Handler:    _BackupService_GetDeletionStatus_Handler,
		},
		{
			MethodName: "CleanupExpired",
			Handler:    _BackupService_CleanupExpired_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data-mgmt/v1/backup.proto",
}

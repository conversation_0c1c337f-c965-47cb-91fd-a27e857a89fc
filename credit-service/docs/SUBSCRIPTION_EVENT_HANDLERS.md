# Subscription Event Handlers

This document describes the subscription event handlers implemented in the credit service for processing Kafka events related to subscription management.

## Overview

The subscription event handlers provide a comprehensive event-driven architecture for processing subscription-related events from various Kafka topics. This enables decoupled, scalable, and reliable subscription management.

## Kafka Topics

The following Kafka topics are supported:

### 1. `subscription.events` - General Subscription Events
- **Purpose**: Handles all general subscription events
- **Event Types**: All subscription-related events
- **Usage**: Central topic for subscription event routing

### 2. `subscription.payment.completed` - Payment Success Events
- **Purpose**: Handles successful payment events
- **Event Types**: `subscription.payment.completed`
- **Usage**: Triggers subscription activation and credit addition

### 3. `subscription.payment.failed` - Payment Failure Events
- **Purpose**: Handles failed payment events
- **Event Types**: `subscription.payment.failed`
- **Usage**: Triggers subscription suspension and retry logic

### 4. `subscription.activated` - Subscription Activation Events
- **Purpose**: Handles subscription activation events
- **Event Types**: `subscription.activated`
- **Usage**: Activates subscriptions and adds plan credits

### 5. `subscription.cancelled` - Subscription Cancellation Events
- **Purpose**: Handles subscription cancellation events
- **Event Types**: `subscription.cancelled`
- **Usage**: Cancels subscriptions and stops billing

## Event Structures

### SubscriptionPaymentCompletedEvent
```go
type SubscriptionPaymentCompletedEvent struct {
    EventID        string                 `json:"event_id"`
    EventType      string                 `json:"event_type"`
    UserID         string                 `json:"user_id"`
    SubscriptionID string                 `json:"subscription_id"`
    PaymentID      string                 `json:"payment_id"`
    Timestamp      time.Time              `json:"timestamp"`
    Source         string                 `json:"source"`
    Version        string                 `json:"version"`
    Amount         int64                  `json:"amount"`
    Currency       string                 `json:"currency"`
    PaymentMethod  string                 `json:"payment_method"`
    PlanID         string                 `json:"plan_id,omitempty"`
    PlanName       string                 `json:"plan_name,omitempty"`
    CreditsAdded   int                    `json:"credits_added,omitempty"`
    EventData      map[string]interface{} `json:"event_data,omitempty"`
    Metadata       map[string]interface{} `json:"metadata,omitempty"`
}
```

### SubscriptionPaymentFailedEvent
```go
type SubscriptionPaymentFailedEvent struct {
    EventID        string                 `json:"event_id"`
    EventType      string                 `json:"event_type"`
    UserID         string                 `json:"user_id"`
    SubscriptionID string                 `json:"subscription_id"`
    PaymentID      string                 `json:"payment_id"`
    Timestamp      time.Time              `json:"timestamp"`
    Source         string                 `json:"source"`
    Version        string                 `json:"version"`
    Amount         int64                  `json:"amount"`
    Currency       string                 `json:"currency"`
    PaymentMethod  string                 `json:"payment_method"`
    FailureReason  string                 `json:"failure_reason"`
    FailureCode    string                 `json:"failure_code,omitempty"`
    PlanID         string                 `json:"plan_id,omitempty"`
    PlanName       string                 `json:"plan_name,omitempty"`
    EventData      map[string]interface{} `json:"event_data,omitempty"`
    Metadata       map[string]interface{} `json:"metadata,omitempty"`
}
```

## Event Handlers

### SubscriptionEventHandler

The main event handler class that processes all subscription events:

```go
type SubscriptionEventHandler struct {
    logger         logging.Logger
    subscriptionUC subscription.UseCase
    creditUC       credit.UseCase
    notificationUC NotificationService
}
```

#### Methods

1. **HandleSubscriptionEvents(ctx context.Context, message []byte) error**
   - Handles general subscription events from `subscription.events` topic
   - Routes events to specific handlers based on event type

2. **HandlePaymentCompletedEvents(ctx context.Context, message []byte) error**
   - Handles payment completed events from `subscription.payment.completed` topic
   - ✅ **Implemented**: Calls `subscriptionUC.HandlePaymentSuccess()`
   - ✅ **Implemented**: Adds credits via `creditUC.AddCredits()`
   - ✅ **Implemented**: Sends notification via `notificationUC.SendSubscriptionActivatedNotification()`

3. **HandlePaymentFailedEvents(ctx context.Context, message []byte) error**
   - Handles payment failed events from `subscription.payment.failed` topic
   - ✅ **Implemented**: Calls `subscriptionUC.HandlePaymentFailure()`
   - ✅ **Implemented**: Sends notification via `notificationUC.SendPaymentFailedNotification()`
   - ✅ **Implemented**: Logs failure details for monitoring

4. **HandleSubscriptionActivatedEvents(ctx context.Context, message []byte) error**
   - Handles subscription activated events from `subscription.activated` topic
   - ✅ **Implemented**: Calls `subscriptionUC.HandlePaymentSuccess()` if payment ID provided
   - ✅ **Implemented**: Adds plan credits via `creditUC.AddCredits()`
   - ✅ **Implemented**: Sends welcome notification via `notificationUC.SendSubscriptionActivatedNotification()`

5. **HandleSubscriptionCancelledEvents(ctx context.Context, message []byte) error**
   - Handles subscription cancelled events from `subscription.cancelled` topic
   - ✅ **Implemented**: Calls `subscriptionUC.HandlePaymentFailure()` to cancel subscription
   - ✅ **Implemented**: Sends cancellation notification via `notificationUC.SendSubscriptionCancelledNotification()`
   - ✅ **Implemented**: Logs cancellation details for audit

## Consumer Implementation

### SubscriptionEventConsumer

The Kafka consumer that manages subscription event consumption:

```go
type SubscriptionEventConsumer struct {
    consumer KafkaConsumer
    handler  *SubscriptionEventHandler
    logger   logging.Logger
}
```

#### Usage Example

```go
// Create event handler
eventHandler := events.NewSubscriptionEventHandler(logger)

// Create Kafka consumer (mock or real)
kafkaConsumer := events.NewMockKafkaConsumer(logger)

// Create subscription event consumer
consumer := events.NewSubscriptionEventConsumer(kafkaConsumer, eventHandler, logger)

// Start consuming events
ctx := context.Background()
err := consumer.Start(ctx)
```

## EventService - Complete Integration

### EventService

The `EventService` provides a complete integration layer that wires up all dependencies:

```go
type EventService struct {
    logger             logging.Logger
    subscriptionUC     subscription.UseCase
    creditUC           credit.UseCase
    notificationSvc    NotificationService
    eventHandler       *SubscriptionEventHandler
    eventConsumer      *SubscriptionEventConsumer
    statsTracker       *StatsTracker
    kafkaConsumer      KafkaConsumer
}
```

#### Usage with Builder Pattern

```go
// Create event service with all dependencies
eventService, err := events.NewEventServiceBuilder().
    WithLogger(logger).
    WithSubscriptionUseCase(subscriptionUC).
    WithCreditUseCase(creditUC).
    WithNotificationService(notificationSvc).
    WithConfig(&events.EventServiceConfig{
        KafkaBrokers: []string{"localhost:9092"},
        GroupID:      "credit-service-subscription-events",
        EnableMock:   false,
    }).
    Build()

if err != nil {
    log.Fatal(err)
}

// Start the service
ctx := context.Background()
err = eventService.Start(ctx)
```

#### Features

- **Dependency Injection**: Automatically wires up all required dependencies
- **Health Checks**: Built-in health check functionality
- **Statistics**: Comprehensive event processing statistics
- **Configuration**: Flexible configuration for different environments
- **Mock Support**: Built-in mock implementations for testing

## Event Creation Helpers

### Helper Functions

1. **NewSubscriptionPaymentCompletedEvent(userID, subscriptionID, paymentID string, amount int64, currency string)**
   - Creates a new payment completed event

2. **NewSubscriptionPaymentFailedEvent(userID, subscriptionID, paymentID string, amount int64, currency, failureReason string)**
   - Creates a new payment failed event

3. **NewSubscriptionActivatedEventKafka(userID, subscriptionID, planID, planName string)**
   - Creates a new subscription activated event

4. **NewSubscriptionCancelledEventKafka(userID, subscriptionID, planID, planName, reason string)**
   - Creates a new subscription cancelled event

5. **NewGeneralSubscriptionEvent(eventType, userID, subscriptionID, status string)**
   - Creates a new general subscription event

### Usage Example

```go
// Create payment completed event
event := events.NewSubscriptionPaymentCompletedEvent(
    "user_123",
    "sub_456", 
    "pay_789",
    2999, // $29.99 in cents
    "USD",
)
event.PlanID = "plan_premium"
event.CreditsAdded = 1000
```

## Statistics Tracking

### StatsTracker

Tracks event processing statistics:

```go
type StatsTracker struct {
    stats  *EventProcessingStats
    logger logging.Logger
}
```

#### Features

- Tracks total events processed
- Tracks events by topic type
- Tracks processing errors
- Logs statistics periodically
- Provides reset functionality

#### Usage Example

```go
// Create stats tracker
statsTracker := events.NewStatsTracker(logger)

// Track event processing
statsTracker.TrackEvent(events.TopicSubscriptionPaymentCompleted, true)

// Get current statistics
stats := statsTracker.GetStats()
```

## Integration with Payment Factory

The event handlers integrate with the payment factory webhook processing:

```go
// In payment factory webhook handler
func (f *Factory) handlePaymentCompletedEvent(ctx context.Context, result *WebhookProcessResult) error {
    // Create subscription update event
    subscriptionEvent := &SubscriptionUpdateEvent{
        EventID:        result.EventID,
        EventType:      "SUBSCRIPTION_PAYMENT_COMPLETED",
        // ... other fields
    }

    // Publish to Kafka
    if f.eventPublisher != nil {
        err := f.eventPublisher.PublishEvent(ctx, "subscription.payment.completed", subscriptionEvent)
        // Handle error
    }
    
    return nil
}
```

## Error Handling

- All event handlers include comprehensive error handling
- Failed events are logged with detailed error information
- Processing continues even if individual events fail
- Statistics track error rates for monitoring

## Monitoring and Observability

- Detailed logging for all event processing
- Statistics tracking for performance monitoring
- Event processing metrics
- Error rate tracking
- Processing time monitoring

## Testing

### Mock Implementation

A mock Kafka consumer is provided for testing:

```go
mockConsumer := events.NewMockKafkaConsumer(logger)
```

### Example Application

See `examples/subscription_event_consumer.go` for a complete example of how to use the event handlers.

## Configuration

### Environment Variables

- `KAFKA_BROKERS`: Kafka broker addresses
- `KAFKA_GROUP_ID`: Consumer group ID
- `LOG_LEVEL`: Logging level

### Topics Configuration

All topic names are defined as constants in `pkg/events/credit_events.go`:

```go
const (
    TopicSubscriptionEvents           = "subscription.events"
    TopicSubscriptionPaymentCompleted = "subscription.payment.completed"
    TopicSubscriptionPaymentFailed    = "subscription.payment.failed"
    TopicSubscriptionActivated        = "subscription.activated"
    TopicSubscriptionCancelled        = "subscription.cancelled"
)
```

## Best Practices

1. **Event Ordering**: Ensure events are processed in the correct order
2. **Idempotency**: Handle duplicate events gracefully
3. **Error Recovery**: Implement retry logic for failed events
4. **Monitoring**: Track event processing metrics
5. **Testing**: Use mock consumers for unit testing
6. **Documentation**: Keep event schemas documented and versioned

package kafka

import (
	"time"
)

// Config represents Kafka configuration
type Config struct {
	Brokers []string `env:"KAFKA_BROKERS" envDefault:"localhost:9092"`
	
	// Consumer configuration
	Consumer ConsumerConfig `json:"consumer"`
	
	// Producer configuration
	Producer ProducerConfig `json:"producer"`
	
	// Topic configuration
	Topics TopicConfig `json:"topics"`
	
	// Security configuration
	Security SecurityConfig `json:"security"`
}

// ConsumerConfig represents Kafka consumer configuration
type ConsumerConfig struct {
	GroupID          string        `env:"KAFKA_CONSUMER_GROUP_ID" envDefault:"default-consumer-group"`
	MinBytes         int           `env:"KAFKA_CONSUMER_MIN_BYTES" envDefault:"1"`
	MaxBytes         int           `env:"KAFKA_CONSUMER_MAX_BYTES" envDefault:"10485760"` // 10MB
	MaxWait          time.Duration `env:"KAFKA_CONSUMER_MAX_WAIT" envDefault:"1s"`
	CommitInterval   time.Duration `env:"KAFKA_CONSUMER_COMMIT_INTERVAL" envDefault:"1s"`
	StartOffset      int64         `env:"KAFKA_CONSUMER_START_OFFSET" envDefault:"-1"` // Latest
	ReadTimeout      time.Duration `env:"KAFKA_CONSUMER_READ_TIMEOUT" envDefault:"10s"`
	HeartbeatInterval time.Duration `env:"KAFKA_CONSUMER_HEARTBEAT_INTERVAL" envDefault:"3s"`
	SessionTimeout   time.Duration `env:"KAFKA_CONSUMER_SESSION_TIMEOUT" envDefault:"30s"`
	RebalanceTimeout time.Duration `env:"KAFKA_CONSUMER_REBALANCE_TIMEOUT" envDefault:"30s"`
	RetentionTime    time.Duration `env:"KAFKA_CONSUMER_RETENTION_TIME" envDefault:"24h"`
}

// ProducerConfig represents Kafka producer configuration
type ProducerConfig struct {
	MaxMessageBytes  int           `env:"KAFKA_PRODUCER_MAX_MESSAGE_BYTES" envDefault:"1048576"` // 1MB
	RequiredAcks     int           `env:"KAFKA_PRODUCER_REQUIRED_ACKS" envDefault:"1"`
	WriteTimeout     time.Duration `env:"KAFKA_PRODUCER_WRITE_TIMEOUT" envDefault:"10s"`
	ReadTimeout      time.Duration `env:"KAFKA_PRODUCER_READ_TIMEOUT" envDefault:"10s"`
	BatchSize        int           `env:"KAFKA_PRODUCER_BATCH_SIZE" envDefault:"100"`
	BatchTimeout     time.Duration `env:"KAFKA_PRODUCER_BATCH_TIMEOUT" envDefault:"1s"`
	Compression      string        `env:"KAFKA_PRODUCER_COMPRESSION" envDefault:"snappy"`
	Async            bool          `env:"KAFKA_PRODUCER_ASYNC" envDefault:"false"`
	RetryMax         int           `env:"KAFKA_PRODUCER_RETRY_MAX" envDefault:"3"`
	RetryBackoff     time.Duration `env:"KAFKA_PRODUCER_RETRY_BACKOFF" envDefault:"100ms"`
}

// TopicConfig represents Kafka topic configuration
type TopicConfig struct {
	UserEvents         string `env:"KAFKA_TOPIC_USER_EVENTS" envDefault:"user.events"`
	ContentEvents      string `env:"KAFKA_TOPIC_CONTENT_EVENTS" envDefault:"content.events"`
	BillingEvents      string `env:"KAFKA_TOPIC_BILLING_EVENTS" envDefault:"billing.events"`
	IntegrationEvents  string `env:"KAFKA_TOPIC_INTEGRATION_EVENTS" envDefault:"integration.events"`
	AssetEvents        string `env:"KAFKA_TOPIC_ASSET_EVENTS" envDefault:"asset.events"`
	RAGEvents          string `env:"KAFKA_TOPIC_RAG_EVENTS" envDefault:"rag.events"`
	AnalyticsEvents    string `env:"KAFKA_TOPIC_ANALYTICS_EVENTS" envDefault:"analytics.events"`
	SystemEvents       string `env:"KAFKA_TOPIC_SYSTEM_EVENTS" envDefault:"system.events"`
	NotificationEvents string `env:"KAFKA_TOPIC_NOTIFICATION_EVENTS" envDefault:"notification.events"`
	
	// Dead letter queue topics
	DeadLetterQueue    string `env:"KAFKA_TOPIC_DLQ" envDefault:"events.dlq"`
	RetryQueue         string `env:"KAFKA_TOPIC_RETRY" envDefault:"events.retry"`
}

// SecurityConfig represents Kafka security configuration
type SecurityConfig struct {
	Enabled   bool   `env:"KAFKA_SECURITY_ENABLED" envDefault:"false"`
	Protocol  string `env:"KAFKA_SECURITY_PROTOCOL" envDefault:"PLAINTEXT"`
	Username  string `env:"KAFKA_SECURITY_USERNAME"`
	Password  string `env:"KAFKA_SECURITY_PASSWORD"`
	CertFile  string `env:"KAFKA_SECURITY_CERT_FILE"`
	KeyFile   string `env:"KAFKA_SECURITY_KEY_FILE"`
	CAFile    string `env:"KAFKA_SECURITY_CA_FILE"`
	VerifySSL bool   `env:"KAFKA_SECURITY_VERIFY_SSL" envDefault:"true"`
}

// NewDefaultConfig creates a default Kafka configuration
func NewDefaultConfig() *Config {
	return &Config{
		Brokers: []string{"localhost:9092"},
		Consumer: ConsumerConfig{
			GroupID:          "default-consumer-group",
			MinBytes:         1,
			MaxBytes:         10 * 1024 * 1024, // 10MB
			MaxWait:          1 * time.Second,
			CommitInterval:   1 * time.Second,
			StartOffset:      -1, // Latest
			ReadTimeout:      10 * time.Second,
			HeartbeatInterval: 3 * time.Second,
			SessionTimeout:   30 * time.Second,
			RebalanceTimeout: 30 * time.Second,
			RetentionTime:    24 * time.Hour,
		},
		Producer: ProducerConfig{
			MaxMessageBytes: 1024 * 1024, // 1MB
			RequiredAcks:    1,
			WriteTimeout:    10 * time.Second,
			ReadTimeout:     10 * time.Second,
			BatchSize:       100,
			BatchTimeout:    1 * time.Second,
			Compression:     "snappy",
			Async:           false,
			RetryMax:        3,
			RetryBackoff:    100 * time.Millisecond,
		},
		Topics: TopicConfig{
			UserEvents:         TopicNames.UserEvents,
			ContentEvents:      TopicNames.ContentEvents,
			BillingEvents:      TopicNames.BillingEvents,
			IntegrationEvents:  TopicNames.IntegrationEvents,
			AssetEvents:        TopicNames.AssetEvents,
			RAGEvents:          TopicNames.RAGEvents,
			AnalyticsEvents:    TopicNames.AnalyticsEvents,
			SystemEvents:       TopicNames.SystemEvents,
			NotificationEvents: TopicNames.NotificationEvents,
			DeadLetterQueue:    TopicNames.DeadLetterQueue,
			RetryQueue:         TopicNames.RetryQueue,
		},
		Security: SecurityConfig{
			Enabled:   false,
			Protocol:  "PLAINTEXT",
			VerifySSL: true,
		},
	}
}

// GetAllTopics returns list of all event topics
func (c *Config) GetAllTopics() []string {
	return []string{
		c.Topics.UserEvents,
		c.Topics.ContentEvents,
		c.Topics.BillingEvents,
		c.Topics.IntegrationEvents,
		c.Topics.AssetEvents,
		c.Topics.RAGEvents,
		c.Topics.AnalyticsEvents,
		c.Topics.SystemEvents,
		c.Topics.NotificationEvents,
		c.Topics.DeadLetterQueue,
		c.Topics.RetryQueue,
	}
}

// GetEventTopics returns list of event topics (excluding DLQ and retry)
func (c *Config) GetEventTopics() []string {
	return []string{
		c.Topics.UserEvents,
		c.Topics.ContentEvents,
		c.Topics.BillingEvents,
		c.Topics.IntegrationEvents,
		c.Topics.AssetEvents,
		c.Topics.RAGEvents,
		c.Topics.AnalyticsEvents,
		c.Topics.SystemEvents,
		c.Topics.NotificationEvents,
	}
}

// GetTopicByName returns topic name by identifier
func (c *Config) GetTopicByName(name string) string {
	switch name {
	case "user":
		return c.Topics.UserEvents
	case "content":
		return c.Topics.ContentEvents
	case "billing":
		return c.Topics.BillingEvents
	case "integration":
		return c.Topics.IntegrationEvents
	case "asset":
		return c.Topics.AssetEvents
	case "rag":
		return c.Topics.RAGEvents
	case "analytics":
		return c.Topics.AnalyticsEvents
	case "system":
		return c.Topics.SystemEvents
	case "notification":
		return c.Topics.NotificationEvents
	case "dlq":
		return c.Topics.DeadLetterQueue
	case "retry":
		return c.Topics.RetryQueue
	default:
		return ""
	}
}

// TopicPartitionConfig represents topic partition configuration
type TopicPartitionConfig struct {
	Topic             string `json:"topic"`
	NumPartitions     int    `json:"num_partitions"`
	ReplicationFactor int    `json:"replication_factor"`
}

// GetDefaultTopicConfigs returns default topic configurations
func GetDefaultTopicConfigs() []TopicPartitionConfig {
	return []TopicPartitionConfig{
		{
			Topic:             TopicNames.UserEvents,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.ContentEvents,
			NumPartitions:     6, // Higher volume expected
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.BillingEvents,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.IntegrationEvents,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.AssetEvents,
			NumPartitions:     6, // Higher volume expected
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.RAGEvents,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.AnalyticsEvents,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.SystemEvents,
			NumPartitions:     1, // Low volume, order important
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.NotificationEvents,
			NumPartitions:     6, // Higher volume expected
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.DeadLetterQueue,
			NumPartitions:     1,
			ReplicationFactor: 1,
		},
		{
			Topic:             TopicNames.RetryQueue,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}
}

// RetryPolicy represents retry configuration
type RetryPolicy struct {
	MaxRetries      int           `json:"max_retries"`
	InitialInterval time.Duration `json:"initial_interval"`
	MaxInterval     time.Duration `json:"max_interval"`
	Multiplier      float64       `json:"multiplier"`
}

// GetDefaultRetryPolicy returns default retry policy
func GetDefaultRetryPolicy() RetryPolicy {
	return RetryPolicy{
		MaxRetries:      3,
		InitialInterval: 1 * time.Second,
		MaxInterval:     30 * time.Second,
		Multiplier:      2.0,
	}
}

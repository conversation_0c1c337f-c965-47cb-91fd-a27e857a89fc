package repository

import (
	"context"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/usecase/publishing"
)

// PublishingRepository adapter that combines platform integration and published post repositories
type PublishingRepository struct {
	platformRepo    *PlatformIntegrationRepository
	publishedPostRepo *PublishedPostRepository
}

// NewPublishingRepository creates a new publishing repository adapter
func NewPublishingRepository(platformRepo *PlatformIntegrationRepository, publishedPostRepo *PublishedPostRepository) *PublishingRepository {
	return &PublishingRepository{
		platformRepo:      platformRepo,
		publishedPostRepo: publishedPostRepo,
	}
}

// Ensure PublishingRepository implements the interfaces
var _ publishing.Reader = (*PublishingRepository)(nil)
var _ publishing.Writer = (*PublishingRepository)(nil)

// GetPlatformIntegration delegates to platform repository
func (r *PublishingRepository) GetPlatformIntegration(ctx context.Context, id string) (*ent.PlatformIntegration, error) {
	return r.platformRepo.GetPlatformIntegration(ctx, id)
}

// GetPublishedPost delegates to published post repository
func (r *PublishingRepository) GetPublishedPost(ctx context.Context, id string) (*ent.PublishedPost, error) {
	return r.publishedPostRepo.GetPublishedPost(ctx, id)
}

// ListPublishedPosts delegates to published post repository
func (r *PublishingRepository) ListPublishedPosts(ctx context.Context, userID, platform string, limit, offset int32) ([]*ent.PublishedPost, int32, error) {
	return r.publishedPostRepo.ListPublishedPosts(ctx, userID, platform, limit, offset)
}

// CreatePublishedPost delegates to published post repository
func (r *PublishingRepository) CreatePublishedPost(ctx context.Context, post *ent.PublishedPost) (*ent.PublishedPost, error) {
	return r.publishedPostRepo.CreatePublishedPost(ctx, post)
}

// UpdatePublishedPost delegates to published post repository
func (r *PublishingRepository) UpdatePublishedPost(ctx context.Context, id string, updates map[string]interface{}) (*ent.PublishedPost, error) {
	return r.publishedPostRepo.UpdatePublishedPost(ctx, id, updates)
}

// DeletePublishedPost delegates to published post repository
func (r *PublishingRepository) DeletePublishedPost(ctx context.Context, id string) error {
	return r.publishedPostRepo.DeletePublishedPost(ctx, id)
}

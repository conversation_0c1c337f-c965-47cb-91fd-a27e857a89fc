package banktransfer

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB              *ent.Client
	writeDB             *ent.Client
	creditUseCase       credit.UseCase
	subscriptionUseCase subscription.UseCase
	logger              logging.Logger
}

// NewService creates a new bank transfer service
func NewService(readDB, writeDB *ent.Client, creditUseCase credit.UseCase, subscriptionUseCase subscription.UseCase, logger logging.Logger) UseCase {
	return &service{
		readDB:              readDB,
		writeDB:             writeDB,
		creditUseCase:       creditUseCase,
		subscriptionUseCase: subscriptionUseCase,
		logger:              logger,
	}
}

// InitiateBankTransfer initiates a bank transfer payment
func (s *service) InitiateBankTransfer(ctx context.Context, req *models.InitiateBankTransferRequest) (*models.InitiateBankTransferResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Initiating bank transfer")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Generate reference code
	referenceCode, err := s.generateReferenceCode()
	if err != nil {
		return nil, fmt.Errorf("failed to generate reference code: %w", err)
	}

	// Set expiration time (24 hours from now)
	expiresAt := time.Now().Add(24 * time.Hour)

	// Get bank configuration (default to Vietcombank)
	bankConfig := models.DefaultBankConfigs["vietcombank"]

	// Generate transfer instructions
	instructions := s.generateTransferInstructions(bankConfig, req, referenceCode)

	// Parse plan ID if provided
	var planUUID *uuid.UUID
	if req.PlanID != "" {
		parsed, err := uuid.Parse(req.PlanID)
		if err != nil {
			return nil, fmt.Errorf("invalid plan ID format: %w", err)
		}
		planUUID = &parsed
	}

	// Create bank transfer record
	transfer, err := s.writeDB.BankTransfer.Create().
		SetUserID(userUUID).
		SetNillablePlanID(planUUID).
		SetAmount(req.Amount).
		SetCurrency(req.Currency).
		SetStatus(banktransfer.StatusPending).
		SetBankAccount(bankConfig.AccountNumber).
		SetReferenceCode(referenceCode).
		SetTransferInstructions(instructions.TransferContent).
		SetExpiresAt(expiresAt).
		SetCreditsToAdd(s.calculateCreditsToAdd(req.Amount, req.Purpose)).
		SetMetadata(req.Metadata).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create bank transfer record")
		return nil, fmt.Errorf("failed to create bank transfer record: %w", err)
	}

	// TODO: Publish BankTransferInitiated event to Kafka

	return &models.InitiateBankTransferResponse{
		Success:       true,
		TransferID:    transfer.ID.String(),
		ReferenceCode: referenceCode,
		Instructions:  instructions,
		ExpiresAt:     expiresAt,
	}, nil
}

// generateReferenceCode generates a unique reference code
func (s *service) generateReferenceCode() (string, error) {
	// Generate 6 random bytes
	bytes := make([]byte, 6)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	// Convert to hex and uppercase
	code := strings.ToUpper(hex.EncodeToString(bytes))
	
	// Format as SOCIALAI + code
	return fmt.Sprintf("SOCIALAI%s", code), nil
}

// generateTransferInstructions generates bank transfer instructions
func (s *service) generateTransferInstructions(bankConfig models.BankConfig, req *models.InitiateBankTransferRequest, referenceCode string) models.BankTransferInstructions {
	// Get user name from user info
	userName := req.UserInfo["full_name"]
	if userName == "" {
		userName = req.UserInfo["name"]
	}
	if userName == "" {
		userName = "Khach hang"
	}

	// Generate transfer content
	transferContent := fmt.Sprintf("SOCIALAI %s %s", referenceCode, userName)

	// Generate instructions
	instructions := []string{
		"1. Đăng nhập vào ứng dụng ngân hàng hoặc đến quầy giao dịch",
		"2. Chọn chức năng chuyển khoản",
		fmt.Sprintf("3. Nhập số tài khoản: %s", bankConfig.AccountNumber),
		fmt.Sprintf("4. Nhập tên người nhận: %s", bankConfig.AccountName),
		fmt.Sprintf("5. Nhập số tiền: %s VND", s.formatAmount(req.Amount)),
		fmt.Sprintf("6. Nhập nội dung chuyển khoản: %s", transferContent),
		"7. Xác nhận và thực hiện chuyển khoản",
		"8. Lưu lại biên lai để đối chiếu",
	}

	return models.BankTransferInstructions{
		BankName:        bankConfig.BankName,
		AccountNumber:   bankConfig.AccountNumber,
		AccountName:     bankConfig.AccountName,
		Amount:          req.Amount,
		TransferContent: transferContent,
		ReferenceCode:   referenceCode,
		Instructions:    instructions,
		// TODO: Generate QR code URL
		QRCodeURL: "",
	}
}

// formatAmount formats amount for display
func (s *service) formatAmount(amount int64) string {
	// Convert from smallest unit to VND
	vnd := amount / 100
	return fmt.Sprintf("%d", vnd)
}

// calculateCreditsToAdd calculates credits to add based on amount and purpose
func (s *service) calculateCreditsToAdd(amount int64, purpose string) int32 {
	switch purpose {
	case "subscription":
		// For subscription, credits will be added based on plan
		return 0
	case "credit_topup":
		// For credit topup, 1 VND = 1 credit (adjust as needed)
		return int32(amount / 100)
	default:
		return 0
	}
}

// GetBankTransferStatus gets the status of a bank transfer
func (s *service) GetBankTransferStatus(ctx context.Context, transferID, userID string) (*models.BankTransferInfo, error) {
	s.logger.WithFields(map[string]interface{}{
		"transfer_id": transferID,
		"user_id":     userID,
	}).Info("Getting bank transfer status")

	// Parse IDs
	transferUUID, err := uuid.Parse(transferID)
	if err != nil {
		return nil, fmt.Errorf("invalid transfer ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get bank transfer
	transfer, err := s.readDB.BankTransfer.Query().
		Where(
			banktransfer.ID(transferUUID),
			banktransfer.UserID(userUUID),
		).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("bank transfer not found")
		}
		return nil, fmt.Errorf("failed to get bank transfer: %w", err)
	}

	return s.convertToModel(transfer), nil
}

// convertToModel converts ent bank transfer to model
func (s *service) convertToModel(transfer *ent.BankTransfer) *models.BankTransferInfo {
	info := &models.BankTransferInfo{
		BaseModel: models.BaseModel{
			ID:        transfer.ID.String(),
			CreatedAt: transfer.CreatedAt,
			UpdatedAt: transfer.UpdatedAt,
		},
		TransferID:           transfer.ID.String(),
		UserID:               transfer.UserID.String(),
		Amount:               transfer.Amount,
		Currency:             transfer.Currency,
		Status:               string(transfer.Status),
		ReferenceCode:        transfer.ReferenceCode,
		ExpiresAt:            transfer.ExpiresAt,
		BankTransactionID:    transfer.BankTransactionID,
		ActualAmountReceived: transfer.ActualAmountReceived,
		ConfirmationMethod:   transfer.ConfirmationMethod,
		ConfirmedBy:          transfer.ConfirmedBy,
		Purpose:              "subscription", // TODO: Add purpose field to schema
		Metadata:             transfer.Metadata,
	}

	if transfer.PlanID != nil {
		info.PlanID = transfer.PlanID.String()
	}

	if !transfer.ConfirmedAt.IsZero() {
		info.ConfirmedAt = &transfer.ConfirmedAt
	}

	// TODO: Parse and set instructions from transfer.TransferInstructions

	return info
}

// ConfirmBankTransfer confirms a bank transfer payment
func (s *service) ConfirmBankTransfer(ctx context.Context, req *models.ConfirmBankTransferRequest) (*models.ConfirmBankTransferResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"transfer_id":          req.TransferID,
		"reference_code":       req.ReferenceCode,
		"actual_amount":        req.ActualAmount,
		"bank_transaction_id":  req.BankTransactionID,
		"confirmed_by":         req.ConfirmedBy,
	}).Info("Confirming bank transfer")

	// Parse transfer ID
	transferUUID, err := uuid.Parse(req.TransferID)
	if err != nil {
		return nil, fmt.Errorf("invalid transfer ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get bank transfer
	transfer, err := tx.BankTransfer.Query().
		Where(
			banktransfer.ID(transferUUID),
			banktransfer.ReferenceCode(req.ReferenceCode),
		).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &models.ConfirmBankTransferResponse{
				Success:      false,
				ErrorMessage: "Bank transfer not found",
			}, nil
		}
		return nil, fmt.Errorf("failed to get bank transfer: %w", err)
	}

	// Check if already confirmed
	if transfer.Status == banktransfer.StatusConfirmed {
		return &models.ConfirmBankTransferResponse{
			Success:      false,
			ErrorMessage: "Bank transfer already confirmed",
		}, nil
	}

	// Check if expired
	if time.Now().After(transfer.ExpiresAt) && transfer.Status == banktransfer.StatusPending {
		return &models.ConfirmBankTransferResponse{
			Success:      false,
			ErrorMessage: "Bank transfer has expired",
		}, nil
	}

	// Update bank transfer status
	confirmedAt := time.Now()
	confirmationMethod := req.ConfirmationMethod
	if confirmationMethod == "" {
		confirmationMethod = "manual"
	}

	transfer, err = tx.BankTransfer.UpdateOne(transfer).
		SetStatus(banktransfer.StatusConfirmed).
		SetConfirmedAt(confirmedAt).
		SetBankTransactionID(req.BankTransactionID).
		SetActualAmountReceived(req.ActualAmount).
		SetConfirmationMethod(confirmationMethod).
		SetConfirmedBy(req.ConfirmedBy).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update bank transfer: %w", err)
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Process subscription activation and credit addition
	response := &models.ConfirmBankTransferResponse{
		Success: true,
	}

	// TODO: Implement subscription activation and credit addition
	// TODO: Publish BankTransferConfirmed event to Kafka

	return response, nil
}

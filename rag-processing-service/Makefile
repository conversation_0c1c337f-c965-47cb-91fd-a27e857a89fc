# RAG Processing Service Makefile

.PHONY: build test clean run docker deps lint fmt help

# Variables
SERVICE_NAME := rag-processing-service
VERSION ?= latest
DOCKER_IMAGE := $(SERVICE_NAME):$(VERSION)
PYTHON := python3
PIP := pip3

# Default target
all: deps test lint

# Install dependencies
deps:
	@echo "📦 Installing Python dependencies..."
	$(PIP) install -r requirements.txt
	@echo "📦 Downloading spaCy model..."
	$(PYTHON) -m spacy download en_core_web_sm
	@echo "✅ Dependencies installed"

# Install development dependencies
deps-dev:
	@echo "📦 Installing development dependencies..."
	$(PIP) install -r requirements.txt
	$(PIP) install pytest pytest-asyncio pytest-cov black isort flake8 mypy
	$(PYTHON) -m spacy download en_core_web_sm
	@echo "✅ Development dependencies installed"

# Run tests
test:
	@echo "🧪 Running tests..."
	$(PYTHON) -m pytest tests/ -v
	@echo "✅ Tests completed"

# Run tests with coverage
test-coverage:
	@echo "🧪 Running tests with coverage..."
	$(PYTHON) -m pytest tests/ -v --cov=app --cov-report=html --cov-report=term
	@echo "📊 Coverage report generated: htmlcov/index.html"

# Run the service locally
run:
	@echo "🚀 Running $(SERVICE_NAME)..."
	$(PYTHON) main.py

# Run with development settings
dev:
	@echo "🔄 Running $(SERVICE_NAME) in development mode..."
	DEBUG=true $(PYTHON) main.py --reload

# Build Docker image
docker:
	@echo "🐳 Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .
	@echo "✅ Docker image built: $(DOCKER_IMAGE)"

# Push Docker image
docker-push:
	@echo "📤 Pushing Docker image..."
	docker push $(DOCKER_IMAGE)
	@echo "✅ Docker image pushed: $(DOCKER_IMAGE)"

# Format code
fmt:
	@echo "✨ Formatting code..."
	$(PYTHON) -m black app/ tests/ main.py
	$(PYTHON) -m isort app/ tests/ main.py
	@echo "✅ Code formatted"

# Lint code
lint:
	@echo "🔍 Linting code..."
	$(PYTHON) -m flake8 app/ tests/ main.py
	$(PYTHON) -m mypy app/ --ignore-missing-imports
	@echo "✅ Linting completed"

# Type check
typecheck:
	@echo "🔍 Running type checks..."
	$(PYTHON) -m mypy app/ --ignore-missing-imports
	@echo "✅ Type checking completed"

# Security scan
security:
	@echo "🔒 Running security scan..."
	@if command -v bandit > /dev/null; then \
		bandit -r app/; \
	else \
		echo "⚠️  bandit not found. Install with: pip install bandit"; \
	fi
	@echo "✅ Security scan completed"

# Install development tools
install-tools:
	@echo "🛠️ Installing development tools..."
	$(PIP) install black isort flake8 mypy pytest pytest-asyncio pytest-cov bandit
	@echo "✅ Development tools installed"

# Database migrations (if using Alembic)
migrate-up:
	@echo "⬆️ Running database migrations..."
	alembic upgrade head
	@echo "✅ Migrations completed"

migrate-down:
	@echo "⬇️ Rolling back database migrations..."
	alembic downgrade -1
	@echo "✅ Migration rolled back"

# Generate migration
migrate-generate:
	@echo "📝 Generating new migration..."
	@read -p "Enter migration message: " message; \
	alembic revision --autogenerate -m "$$message"
	@echo "✅ Migration generated"

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf dist/
	rm -rf build/
	@echo "✅ Clean completed"

# Create virtual environment
venv:
	@echo "🐍 Creating virtual environment..."
	$(PYTHON) -m venv venv
	@echo "✅ Virtual environment created"
	@echo "💡 Activate with: source venv/bin/activate"

# Freeze requirements
freeze:
	@echo "❄️ Freezing requirements..."
	$(PIP) freeze > requirements-frozen.txt
	@echo "✅ Requirements frozen to requirements-frozen.txt"

# Check for outdated packages
outdated:
	@echo "📊 Checking for outdated packages..."
	$(PIP) list --outdated
	@echo "✅ Outdated packages check completed"

# Show help
help:
	@echo "$(SERVICE_NAME) - Available Commands:"
	@echo ""
	@echo "🏗️  Build Commands:"
	@echo "  docker        - Build Docker image"
	@echo "  docker-push   - Push Docker image to registry"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo ""
	@echo "🚀 Development Commands:"
	@echo "  run           - Run the service locally"
	@echo "  dev           - Run in development mode with reload"
	@echo "  deps          - Install dependencies"
	@echo "  deps-dev      - Install development dependencies"
	@echo ""
	@echo "🔧 Code Quality Commands:"
	@echo "  fmt           - Format code with black and isort"
	@echo "  lint          - Lint code with flake8"
	@echo "  typecheck     - Run type checking with mypy"
	@echo "  security      - Run security scan with bandit"
	@echo ""
	@echo "🗄️  Database Commands:"
	@echo "  migrate-up       - Run database migrations"
	@echo "  migrate-down     - Rollback last migration"
	@echo "  migrate-generate - Generate new migration"
	@echo ""
	@echo "🛠️  Utility Commands:"
	@echo "  install-tools - Install development tools"
	@echo "  venv          - Create virtual environment"
	@echo "  freeze        - Freeze requirements"
	@echo "  outdated      - Check for outdated packages"
	@echo "  clean         - Clean build artifacts"
	@echo "  help          - Show this help message"

package models

import "time"

// InitiatePayPalPaymentRequest represents initiate PayPal payment request
type InitiatePayPalPaymentRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	PlanID      string                 `json:"plan_id,omitempty"` // Optional: if purchasing a plan
	Amount      int64                  `json:"amount" validate:"required,min=1"`
	Currency    string                 `json:"currency" validate:"required"`
	Purpose     string                 `json:"purpose" validate:"required"` // "subscription", "credit_topup"
	Description string                 `json:"description,omitempty"`
	ReturnURL   string                 `json:"return_url" validate:"required"`
	CancelURL   string                 `json:"cancel_url" validate:"required"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// InitiatePayPalPaymentResponse represents initiate PayPal payment response
type InitiatePayPalPaymentResponse struct {
	Success       bool   `json:"success"`
	PaymentID     string `json:"payment_id"`
	PayPalOrderID string `json:"paypal_order_id"`
	ApprovalURL   string `json:"approval_url"`
	ErrorMessage  string `json:"error_message,omitempty"`
}

// PayPalPaymentInfo represents PayPal payment information
type PayPalPaymentInfo struct {
	BaseModel
	PaymentID       string                 `json:"payment_id"`
	UserID          string                 `json:"user_id"`
	PlanID          string                 `json:"plan_id,omitempty"`
	Amount          int64                  `json:"amount"`
	Currency        string                 `json:"currency"`
	Status          string                 `json:"status"` // "created", "approved", "completed", "cancelled", "failed"
	PayPalOrderID   string                 `json:"paypal_order_id"`
	PayPalPaymentID string                 `json:"paypal_payment_id,omitempty"`
	PayerID         string                 `json:"payer_id,omitempty"`
	PayerEmail      string                 `json:"payer_email,omitempty"`
	ApprovalURL     string                 `json:"approval_url,omitempty"`
	Purpose         string                 `json:"purpose"`
	Description     string                 `json:"description,omitempty"`
	ReturnURL       string                 `json:"return_url"`
	CancelURL       string                 `json:"cancel_url"`
	CompletedAt     *time.Time             `json:"completed_at,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// ExecutePayPalPaymentRequest represents execute PayPal payment request
type ExecutePayPalPaymentRequest struct {
	PaymentID string `json:"payment_id" validate:"required"`
	PayerID   string `json:"payer_id" validate:"required"`
	UserID    string `json:"user_id" validate:"required"`
}

// ExecutePayPalPaymentResponse represents execute PayPal payment response
type ExecutePayPalPaymentResponse struct {
	Success          bool                        `json:"success"`
	PaymentID        string                      `json:"payment_id"`
	TransactionID    string                      `json:"transaction_id,omitempty"`
	SubscriptionID   string                      `json:"subscription_id,omitempty"`
	CreditsAdded     int32                       `json:"credits_added"`
	ErrorMessage     string                      `json:"error_message,omitempty"`
	SubscriptionInfo *SubscriptionActivationInfo `json:"subscription_info,omitempty"`
}

// CancelPayPalPaymentRequest represents cancel PayPal payment request
type CancelPayPalPaymentRequest struct {
	PaymentID string `json:"payment_id" validate:"required"`
	UserID    string `json:"user_id" validate:"required"`
	Reason    string `json:"reason,omitempty"`
}

// ListPayPalPaymentsRequest represents list PayPal payments request
type ListPayPalPaymentsRequest struct {
	UserID   string     `json:"user_id" validate:"required"`
	Status   string     `json:"status,omitempty"`
	Purpose  string     `json:"purpose,omitempty"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Page     int        `json:"page" validate:"min=1"`
	Limit    int        `json:"limit" validate:"min=1,max=100"`
}

// ListPayPalPaymentsResponse represents list PayPal payments response
type ListPayPalPaymentsResponse struct {
	Payments   []PayPalPaymentInfo `json:"payments"`
	Pagination PaginationMeta      `json:"pagination"`
	Summary    PayPalPaymentSummary `json:"summary"`
}

// PayPalPaymentSummary represents PayPal payment summary
type PayPalPaymentSummary struct {
	TotalPayments     int   `json:"total_payments"`
	CompletedPayments int   `json:"completed_payments"`
	PendingPayments   int   `json:"pending_payments"`
	FailedPayments    int   `json:"failed_payments"`
	TotalAmount       int64 `json:"total_amount"`
	CompletedAmount   int64 `json:"completed_amount"`
}

// PayPalWebhookRequest represents PayPal webhook request
type PayPalWebhookRequest struct {
	ID               string                 `json:"id"`
	EventType        string                 `json:"event_type"`
	EventVersion     string                 `json:"event_version"`
	CreateTime       time.Time              `json:"create_time"`
	ResourceType     string                 `json:"resource_type"`
	ResourceVersion  string                 `json:"resource_version"`
	Summary          string                 `json:"summary"`
	Resource         map[string]interface{} `json:"resource"`
	Links            []PayPalLink           `json:"links,omitempty"`
	TransmissionID   string                 `json:"transmission_id,omitempty"`
	CertID           string                 `json:"cert_id,omitempty"`
	AuthAlgo         string                 `json:"auth_algo,omitempty"`
	TransmissionTime time.Time              `json:"transmission_time,omitempty"`
}

// PayPalWebhookResponse represents PayPal webhook response
type PayPalWebhookResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	PaymentID    string `json:"payment_id,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// PayPalLink represents PayPal HATEOAS link
type PayPalLink struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method,omitempty"`
}

// PayPalOrder represents PayPal order structure
type PayPalOrder struct {
	ID            string                 `json:"id"`
	Intent        string                 `json:"intent"`
	Status        string                 `json:"status"`
	PurchaseUnits []PayPalPurchaseUnit   `json:"purchase_units"`
	Payer         *PayPalPayer           `json:"payer,omitempty"`
	CreateTime    time.Time              `json:"create_time"`
	UpdateTime    time.Time              `json:"update_time"`
	Links         []PayPalLink           `json:"links"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// PayPalPurchaseUnit represents PayPal purchase unit
type PayPalPurchaseUnit struct {
	ReferenceID string         `json:"reference_id,omitempty"`
	Amount      PayPalAmount   `json:"amount"`
	Payee       *PayPalPayee   `json:"payee,omitempty"`
	Description string         `json:"description,omitempty"`
	CustomID    string         `json:"custom_id,omitempty"`
	InvoiceID   string         `json:"invoice_id,omitempty"`
	Payments    *PayPalPayment `json:"payments,omitempty"`
}

// PayPalAmount represents PayPal amount
type PayPalAmount struct {
	CurrencyCode string               `json:"currency_code"`
	Value        string               `json:"value"`
	Breakdown    *PayPalAmountDetails `json:"breakdown,omitempty"`
}

// PayPalAmountDetails represents PayPal amount breakdown
type PayPalAmountDetails struct {
	ItemTotal        *PayPalMoney `json:"item_total,omitempty"`
	Shipping         *PayPalMoney `json:"shipping,omitempty"`
	Handling         *PayPalMoney `json:"handling,omitempty"`
	TaxTotal         *PayPalMoney `json:"tax_total,omitempty"`
	Insurance        *PayPalMoney `json:"insurance,omitempty"`
	ShippingDiscount *PayPalMoney `json:"shipping_discount,omitempty"`
	Discount         *PayPalMoney `json:"discount,omitempty"`
}

// PayPalMoney represents PayPal money
type PayPalMoney struct {
	CurrencyCode string `json:"currency_code"`
	Value        string `json:"value"`
}

// PayPalPayee represents PayPal payee
type PayPalPayee struct {
	EmailAddress string `json:"email_address,omitempty"`
	MerchantID   string `json:"merchant_id,omitempty"`
}

// PayPalPayer represents PayPal payer
type PayPalPayer struct {
	PayerID      string           `json:"payer_id,omitempty"`
	EmailAddress string           `json:"email_address,omitempty"`
	Name         *PayPalName      `json:"name,omitempty"`
	Address      *PayPalAddress   `json:"address,omitempty"`
	Phone        *PayPalPhone     `json:"phone,omitempty"`
	BirthDate    string           `json:"birth_date,omitempty"`
	TaxInfo      *PayPalTaxInfo   `json:"tax_info,omitempty"`
}

// PayPalName represents PayPal name
type PayPalName struct {
	GivenName string `json:"given_name,omitempty"`
	Surname   string `json:"surname,omitempty"`
	FullName  string `json:"full_name,omitempty"`
}

// PayPalAddress represents PayPal address
type PayPalAddress struct {
	AddressLine1 string `json:"address_line_1,omitempty"`
	AddressLine2 string `json:"address_line_2,omitempty"`
	AdminArea2   string `json:"admin_area_2,omitempty"` // City
	AdminArea1   string `json:"admin_area_1,omitempty"` // State
	PostalCode   string `json:"postal_code,omitempty"`
	CountryCode  string `json:"country_code,omitempty"`
}

// PayPalPhone represents PayPal phone
type PayPalPhone struct {
	PhoneType   string `json:"phone_type,omitempty"`
	PhoneNumber string `json:"phone_number,omitempty"`
}

// PayPalTaxInfo represents PayPal tax info
type PayPalTaxInfo struct {
	TaxID     string `json:"tax_id,omitempty"`
	TaxIDType string `json:"tax_id_type,omitempty"`
}

// PayPalPayment represents PayPal payment
type PayPalPayment struct {
	Captures []PayPalCapture `json:"captures,omitempty"`
}

// PayPalCapture represents PayPal capture
type PayPalCapture struct {
	ID                        string                 `json:"id"`
	Status                    string                 `json:"status"`
	Amount                    PayPalAmount           `json:"amount"`
	FinalCapture              bool                   `json:"final_capture,omitempty"`
	SellerProtection          *PayPalSellerProtection `json:"seller_protection,omitempty"`
	SellerReceivableBreakdown *PayPalSellerReceivableBreakdown `json:"seller_receivable_breakdown,omitempty"`
	CreateTime                time.Time              `json:"create_time"`
	UpdateTime                time.Time              `json:"update_time"`
	Links                     []PayPalLink           `json:"links"`
}

// PayPalSellerProtection represents PayPal seller protection
type PayPalSellerProtection struct {
	Status            string   `json:"status"`
	DisputeCategories []string `json:"dispute_categories,omitempty"`
}

// PayPalSellerReceivableBreakdown represents PayPal seller receivable breakdown
type PayPalSellerReceivableBreakdown struct {
	GrossAmount                   PayPalMoney `json:"gross_amount"`
	PayPalFee                     PayPalMoney `json:"paypal_fee,omitempty"`
	NetAmount                     PayPalMoney `json:"net_amount"`
	ReceivableAmount              PayPalMoney `json:"receivable_amount,omitempty"`
	ExchangeRate                  PayPalMoney `json:"exchange_rate,omitempty"`
}

// PayPal configuration
type PayPalConfig struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Environment  string `json:"environment"` // "sandbox" or "live"
	WebhookID    string `json:"webhook_id"`
}

// PayPal API endpoints
var PayPalEndpoints = map[string]map[string]string{
	"sandbox": {
		"base":    "https://api-m.sandbox.paypal.com",
		"oauth":   "https://api-m.sandbox.paypal.com/v1/oauth2/token",
		"orders":  "https://api-m.sandbox.paypal.com/v2/checkout/orders",
		"webhook": "https://api-m.sandbox.paypal.com/v1/notifications/webhooks",
	},
	"live": {
		"base":    "https://api-m.paypal.com",
		"oauth":   "https://api-m.paypal.com/v1/oauth2/token",
		"orders":  "https://api-m.paypal.com/v2/checkout/orders",
		"webhook": "https://api-m.paypal.com/v1/notifications/webhooks",
	},
}

package handlers

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	contentmgmtv1 "github.com/social-content-ai/proto-shared/content-mgmt/v1"
	"github.com/social-content-ai/api-gateway/internal/middleware"
)

// CreatePostRequest represents post creation request
type CreatePostRequest struct {
	WorkspaceID     string            `json:"workspace_id,omitempty"`
	TemplateID      string            `json:"template_id,omitempty"`
	GenerationID    string            `json:"generation_id,omitempty"`
	Title           string            `json:"title" binding:"required,max=500"`
	Content         string            `json:"content" binding:"required"`
	OriginalContent string            `json:"original_content,omitempty"`
	Platforms       []string          `json:"platforms,omitempty"`
	Hashtags        []string          `json:"hashtags,omitempty"`
	Mentions        []string          `json:"mentions,omitempty"`
	MediaURLs       []string          `json:"media_urls,omitempty"`
	Variables       map[string]string `json:"variables,omitempty"`
	Visibility      string            `json:"visibility,omitempty" binding:"omitempty,oneof=public private unlisted"`
	Language        string            `json:"language,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdatePostRequest represents post update request
type UpdatePostRequest struct {
	Title      *string           `json:"title,omitempty" binding:"omitempty,max=500"`
	Content    *string           `json:"content,omitempty"`
	Platforms  []string          `json:"platforms,omitempty"`
	Hashtags   []string          `json:"hashtags,omitempty"`
	Mentions   []string          `json:"mentions,omitempty"`
	MediaURLs  []string          `json:"media_urls,omitempty"`
	Variables  map[string]string `json:"variables,omitempty"`
	Visibility *string           `json:"visibility,omitempty" binding:"omitempty,oneof=public private unlisted"`
	Language   *string           `json:"language,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// SchedulePostRequest represents post scheduling request
type SchedulePostRequest struct {
	ScheduledAt string   `json:"scheduled_at" binding:"required"`
	Platforms   []string `json:"platforms,omitempty"`
	Timezone    string   `json:"timezone,omitempty"`
}

// PublishPostRequest represents post publishing request
type PublishPostRequest struct {
	Platforms []string `json:"platforms,omitempty"`
	Immediate bool     `json:"immediate,omitempty"`
}

// CreatePost handles post creation
// @Summary Create post
// @Description Create a new post
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreatePostRequest true "Post creation data"
// @Success 201 {object} map[string]interface{} "Post created"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts [post]
func (h *Handlers) CreatePost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Create post
	createReq := &contentmgmtv1.CreatePostRequest{
		UserId:          userID,
		WorkspaceId:     req.WorkspaceID,
		TemplateId:      req.TemplateID,
		GenerationId:    req.GenerationID,
		Title:           req.Title,
		Content:         req.Content,
		OriginalContent: req.OriginalContent,
		Platforms:       req.Platforms,
		Hashtags:        req.Hashtags,
		Mentions:        req.Mentions,
		MediaUrls:       req.MediaURLs,
		Variables:       req.Variables,
		Visibility:      req.Visibility,
		Language:        req.Language,
	}

	// Convert metadata
	if req.Metadata != nil {
		// Convert to protobuf Struct if needed
		// For now, we'll skip this conversion
	}

	resp, err := h.services.ContentMgmtService.CreatePost(context.Background(), createReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to create post")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create post",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"post_id": resp.Post.Id,
	}).Info("Post created successfully")

	c.JSON(http.StatusCreated, gin.H{
		"message": "Post created successfully",
		"post":    resp.Post,
	})
}

// ListPosts handles listing user's posts
// @Summary List posts
// @Description Get list of user's posts with pagination and filtering
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param workspace_id query string false "Filter by workspace"
// @Param status query string false "Filter by status"
// @Param platform query string false "Filter by platform"
// @Param search query string false "Search in title and content"
// @Success 200 {object} map[string]interface{} "List of posts"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts [get]
func (h *Handlers) ListPosts(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	workspaceID := c.Query("workspace_id")
	status := c.Query("status")
	platform := c.Query("platform")
	search := c.Query("search")

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// List posts
	listReq := &contentmgmtv1.ListPostsRequest{
		UserId:      userID,
		Page:        int32(page),
		Limit:       int32(limit),
		WorkspaceId: workspaceID,
		Status:      status,
		Platform:    platform,
		Search:      search,
	}

	resp, err := h.services.ContentMgmtService.ListPosts(context.Background(), listReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to list posts")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list posts",
		})
		return
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, int(resp.Total))

	c.JSON(http.StatusOK, gin.H{
		"message":    "Posts retrieved successfully",
		"posts":      resp.Posts,
		"pagination": pagination,
	})
}

// GetPost handles getting a specific post
// @Summary Get post
// @Description Get details of a specific post
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} map[string]interface{} "Post details"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Post not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts/{id} [get]
func (h *Handlers) GetPost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Post ID is required",
		})
		return
	}

	// Get post
	getReq := &contentmgmtv1.GetPostRequest{
		UserId: userID,
		PostId: postID,
	}

	resp, err := h.services.ContentMgmtService.GetPost(context.Background(), getReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Post not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
		}).Error("Failed to get post")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get post",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Post retrieved successfully",
		"post":    resp.Post,
	})
}

// UpdatePost handles post updates
// @Summary Update post
// @Description Update an existing post
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body UpdatePostRequest true "Post update data"
// @Success 200 {object} map[string]interface{} "Post updated"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Post not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts/{id} [put]
func (h *Handlers) UpdatePost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Post ID is required",
		})
		return
	}

	var req UpdatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Update post
	updateReq := &contentmgmtv1.UpdatePostRequest{
		UserId:     userID,
		PostId:     postID,
		Platforms:  req.Platforms,
		Hashtags:   req.Hashtags,
		Mentions:   req.Mentions,
		MediaUrls:  req.MediaURLs,
		Variables:  req.Variables,
	}

	// Set optional fields
	if req.Title != nil {
		updateReq.Title = *req.Title
	}
	if req.Content != nil {
		updateReq.Content = *req.Content
	}
	if req.Visibility != nil {
		updateReq.Visibility = *req.Visibility
	}
	if req.Language != nil {
		updateReq.Language = *req.Language
	}

	resp, err := h.services.ContentMgmtService.UpdatePost(context.Background(), updateReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Post not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
		}).Error("Failed to update post")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update post",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"post_id": postID,
	}).Info("Post updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Post updated successfully",
		"post":    resp.Post,
	})
}

// DeletePost handles post deletion
// @Summary Delete post
// @Description Delete a specific post
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} map[string]interface{} "Post deleted"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Post not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts/{id} [delete]
func (h *Handlers) DeletePost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Post ID is required",
		})
		return
	}

	// Delete post
	deleteReq := &contentmgmtv1.DeletePostRequest{
		UserId: userID,
		PostId: postID,
	}

	_, err := h.services.ContentMgmtService.DeletePost(context.Background(), deleteReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Post not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
		}).Error("Failed to delete post")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete post",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"post_id": postID,
	}).Info("Post deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Post deleted successfully",
	})
}

// SchedulePost handles post scheduling
// @Summary Schedule post
// @Description Schedule a post for future publishing
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body SchedulePostRequest true "Scheduling data"
// @Success 200 {object} map[string]interface{} "Post scheduled"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Post not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts/{id}/schedule [post]
func (h *Handlers) SchedulePost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Post ID is required",
		})
		return
	}

	var req SchedulePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Schedule post
	scheduleReq := &contentmgmtv1.SchedulePostRequest{
		UserId:      userID,
		PostId:      postID,
		ScheduledAt: req.ScheduledAt,
		Platforms:   req.Platforms,
		Timezone:    req.Timezone,
	}

	resp, err := h.services.ContentMgmtService.SchedulePost(context.Background(), scheduleReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Post not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
		}).Error("Failed to schedule post")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to schedule post",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":      userID,
		"post_id":      postID,
		"scheduled_at": req.ScheduledAt,
	}).Info("Post scheduled successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":      "Post scheduled successfully",
		"post":         resp.Post,
		"scheduled_at": req.ScheduledAt,
	})
}

// PublishPost handles immediate post publishing
// @Summary Publish post
// @Description Publish a post immediately to specified platforms
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body PublishPostRequest true "Publishing data"
// @Success 200 {object} map[string]interface{} "Post published"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Post not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /posts/{id}/publish [post]
func (h *Handlers) PublishPost(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Post ID is required",
		})
		return
	}

	var req PublishPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Publish post
	publishReq := &contentmgmtv1.PublishPostRequest{
		UserId:    userID,
		PostId:    postID,
		Platforms: req.Platforms,
		Immediate: req.Immediate,
	}

	resp, err := h.services.ContentMgmtService.PublishPost(context.Background(), publishReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Post not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": userID,
			"post_id": postID,
		}).Error("Failed to publish post")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to publish post",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":   userID,
		"post_id":   postID,
		"platforms": req.Platforms,
	}).Info("Post published successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":         "Post published successfully",
		"post":            resp.Post,
		"publish_results": resp.PublishResults,
	})
}

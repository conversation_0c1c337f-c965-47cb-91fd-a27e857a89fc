package anthropic

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// Client represents Anthropic API client
type Client struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     logging.Logger
}

// NewClient creates a new Anthropic client
func NewClient(apiKey, baseURL string, logger logging.Logger) *Client {
	if baseURL == "" {
		baseURL = "https://api.anthropic.com"
	}

	return &Client{
		apiKey:  apiKey,
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// GenerateContentRequest represents content generation request
type GenerateContentRequest struct {
	Model       string    `json:"model"`
	MaxTokens   int       `json:"max_tokens"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature,omitempty"`
	TopP        float64   `json:"top_p,omitempty"`
	TopK        int       `json:"top_k,omitempty"`
	StopSequences []string `json:"stop_sequences,omitempty"`
	System      string    `json:"system,omitempty"`
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// GenerateContentResponse represents content generation response
type GenerateContentResponse struct {
	ID           string   `json:"id"`
	Type         string   `json:"type"`
	Role         string   `json:"role"`
	Content      []Content `json:"content"`
	Model        string   `json:"model"`
	StopReason   string   `json:"stop_reason"`
	StopSequence string   `json:"stop_sequence,omitempty"`
	Usage        Usage    `json:"usage"`
}

// Content represents response content
type Content struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// Usage represents token usage
type Usage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// ErrorResponse represents API error response
type ErrorResponse struct {
	Type    string `json:"type"`
	Error   Error  `json:"error"`
}

// Error represents error details
type Error struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

// GenerateContent generates content using Anthropic API
func (c *Client) GenerateContent(ctx context.Context, req *GenerateContentRequest) (*GenerateContentResponse, error) {
	// Set default model if not specified
	if req.Model == "" {
		req.Model = "claude-3-sonnet-20240229"
	}

	// Set default max tokens if not specified
	if req.MaxTokens == 0 {
		req.MaxTokens = 1000
	}

	// Set default temperature if not specified
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/v1/messages", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", c.apiKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	// Log request
	c.logger.WithFields(map[string]interface{}{
		"model":       req.Model,
		"messages":    len(req.Messages),
		"max_tokens":  req.MaxTokens,
		"temperature": req.Temperature,
	}).Debug("Making Anthropic API request")

	// Make request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(respBody))
		}
		return nil, fmt.Errorf("Anthropic API error: %s", errResp.Error.Message)
	}

	// Parse successful response
	var result GenerateContentResponse
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Log response
	c.logger.WithFields(map[string]interface{}{
		"id":            result.ID,
		"model":         result.Model,
		"stop_reason":   result.StopReason,
		"input_tokens":  result.Usage.InputTokens,
		"output_tokens": result.Usage.OutputTokens,
	}).Debug("Received Anthropic API response")

	return &result, nil
}

// ImproveContent improves existing content
func (c *Client) ImproveContent(ctx context.Context, content, instructions string) (*GenerateContentResponse, error) {
	systemPrompt := `You are an expert content writer and editor. Your task is to improve the given content based on the provided instructions.

Focus on:
- Clarity and readability
- Engagement and appeal
- Grammar and style
- SEO optimization when relevant
- Platform-specific best practices

Maintain the original tone and intent while making improvements. Return only the improved content without explanations.`

	messages := []Message{
		{
			Role:    "user",
			Content: fmt.Sprintf("Please improve this content:\n\n%s\n\nInstructions: %s", content, instructions),
		},
	}

	req := &GenerateContentRequest{
		Model:       "claude-3-sonnet-20240229",
		MaxTokens:   1500,
		Messages:    messages,
		Temperature: 0.7,
		System:      systemPrompt,
	}

	return c.GenerateContent(ctx, req)
}

// GenerateHashtags generates hashtags for content
func (c *Client) GenerateHashtags(ctx context.Context, content string, platform string, count int) ([]string, error) {
	if count <= 0 {
		count = 10
	}

	systemPrompt := `You are a social media expert. Generate relevant hashtags for the given content and platform.

Rules:
- Generate hashtags that are relevant and trending
- Consider platform-specific best practices
- Mix popular and niche hashtags
- Avoid banned or problematic hashtags
- Return only the hashtags, one per line, without the # symbol
- No explanations or additional text`

	messages := []Message{
		{
			Role:    "user",
			Content: fmt.Sprintf("Generate %d hashtags for this %s content:\n\n%s", count, platform, content),
		},
	}

	req := &GenerateContentRequest{
		Model:       "claude-3-haiku-20240307", // Use faster model for hashtags
		MaxTokens:   300,
		Messages:    messages,
		Temperature: 0.8,
		System:      systemPrompt,
	}

	resp, err := c.GenerateContent(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(resp.Content) == 0 {
		return nil, fmt.Errorf("no hashtags generated")
	}

	// Parse hashtags from response
	hashtagsText := resp.Content[0].Text
	hashtags := parseHashtags(hashtagsText)

	return hashtags, nil
}

// GenerateCaption generates social media caption
func (c *Client) GenerateCaption(ctx context.Context, content, platform, tone string) (string, error) {
	systemPrompt := fmt.Sprintf(`You are a social media expert specializing in %s content creation.

Create an engaging caption that:
- Matches the %s tone
- Is optimized for %s platform
- Includes relevant emojis where appropriate
- Encourages engagement
- Follows platform best practices

Return only the caption without explanations.`, platform, tone, platform)

	messages := []Message{
		{
			Role:    "user",
			Content: fmt.Sprintf("Create a %s caption for this content:\n\n%s", platform, content),
		},
	}

	req := &GenerateContentRequest{
		Model:       "claude-3-sonnet-20240229",
		MaxTokens:   500,
		Messages:    messages,
		Temperature: 0.8,
		System:      systemPrompt,
	}

	resp, err := c.GenerateContent(ctx, req)
	if err != nil {
		return "", err
	}

	if len(resp.Content) == 0 {
		return "", fmt.Errorf("no caption generated")
	}

	return strings.TrimSpace(resp.Content[0].Text), nil
}

// parseHashtags parses hashtags from text response
func parseHashtags(text string) []string {
	lines := strings.Split(strings.TrimSpace(text), "\n")
	var hashtags []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Remove # if present
			line = strings.TrimPrefix(line, "#")
			// Remove numbers and dots from beginning
			line = strings.TrimLeft(line, "**********. ")
			if line != "" {
				hashtags = append(hashtags, line)
			}
		}
	}

	return hashtags
}

// HealthCheck checks if the Anthropic API is accessible
func (c *Client) HealthCheck(ctx context.Context) error {
	// Simple test request
	req := &GenerateContentRequest{
		Model:     "claude-3-haiku-20240307",
		MaxTokens: 10,
		Messages: []Message{
			{
				Role:    "user",
				Content: "Hello",
			},
		},
	}

	_, err := c.GenerateContent(ctx, req)
	return err
}

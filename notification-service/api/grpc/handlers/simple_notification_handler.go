package handlers

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/notification-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
	notificationv1 "github.com/social-content-ai/proto-shared/notification/v1"
)

// SimpleNotificationHandler implements the gRPC notification service with simplified logic
type SimpleNotificationHandler struct {
	notificationv1.UnimplementedNotificationServiceServer
	notificationUC notification.UseCase
	preferenceUC   preference.UseCase
	templateUC     template.UseCase
	logger         logging.Logger
}

// NewSimpleNotificationHandler creates a new simple notification handler
func NewSimpleNotificationHandler(
	notificationUC notification.UseCase,
	preferenceUC preference.UseCase,
	templateUC template.UseCase,
	logger logging.Logger,
) *SimpleNotificationHandler {
	return &SimpleNotificationHandler{
		notificationUC: notificationUC,
		preferenceUC:   preferenceUC,
		templateUC:     templateUC,
		logger:         logger,
	}
}

// SendNotification sends a single notification
func (h *SimpleNotificationHandler) SendNotification(ctx context.Context, req *notificationv1.SendNotificationRequest) (*notificationv1.SendNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
		"title":   req.Title,
	}).Info("gRPC SendNotification called")

	// Convert data map
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert channels
	var channels []string
	for _, ch := range req.Channels {
		switch ch {
		case notificationv1.NotificationChannel_EMAIL:
			channels = append(channels, "email")
		case notificationv1.NotificationChannel_SMS:
			channels = append(channels, "sms")
		case notificationv1.NotificationChannel_PUSH:
			channels = append(channels, "push")
		case notificationv1.NotificationChannel_IN_APP:
			channels = append(channels, "in_app")
		case notificationv1.NotificationChannel_WEBHOOK:
			channels = append(channels, "webhook")
		}
	}

	// Convert priority
	priority := "normal"
	switch req.Priority {
	case notificationv1.NotificationPriority_LOW:
		priority = "low"
	case notificationv1.NotificationPriority_HIGH:
		priority = "high"
	case notificationv1.NotificationPriority_URGENT:
		priority = "urgent"
	}

	// Create request
	sendReq := &notification.SendNotificationRequest{
		UserID:   req.UserId,
		Type:     req.Type,
		Title:    req.Title,
		Message:  req.Message,
		Channels: channels,
		Priority: priority,
		Data:     data,
	}

	// Send notification
	response, err := h.notificationUC.SendNotification(ctx, sendReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send notification")
		return nil, status.Errorf(codes.Internal, "failed to send notification: %v", err)
	}

	return &notificationv1.SendNotificationResponse{
		NotificationId: response.NotificationID,
		Status:         response.Status,
	}, nil
}

// SendTemplatedNotification sends a templated notification
func (h *SimpleNotificationHandler) SendTemplatedNotification(ctx context.Context, req *notificationv1.SendTemplatedNotificationRequest) (*notificationv1.SendNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":     req.UserId,
		"template_id": req.TemplateId,
	}).Info("gRPC SendTemplatedNotification called")

	// Convert data map
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert channels
	var channels []string
	for _, ch := range req.Channels {
		switch ch {
		case notificationv1.NotificationChannel_EMAIL:
			channels = append(channels, "email")
		case notificationv1.NotificationChannel_SMS:
			channels = append(channels, "sms")
		case notificationv1.NotificationChannel_PUSH:
			channels = append(channels, "push")
		case notificationv1.NotificationChannel_IN_APP:
			channels = append(channels, "in_app")
		case notificationv1.NotificationChannel_WEBHOOK:
			channels = append(channels, "webhook")
		}
	}

	// Convert priority
	priority := "normal"
	switch req.Priority {
	case notificationv1.NotificationPriority_LOW:
		priority = "low"
	case notificationv1.NotificationPriority_HIGH:
		priority = "high"
	case notificationv1.NotificationPriority_URGENT:
		priority = "urgent"
	}

	// Create request
	sendReq := &notification.SendTemplatedNotificationRequest{
		UserID:     req.UserId,
		TemplateID: req.TemplateId,
		Data:       data,
		Channels:   channels,
		Priority:   priority,
	}

	// Send templated notification
	response, err := h.notificationUC.SendTemplatedNotification(ctx, sendReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send templated notification")
		return nil, status.Errorf(codes.Internal, "failed to send templated notification: %v", err)
	}

	return &notificationv1.SendNotificationResponse{
		NotificationId: response.NotificationID,
		Status:         response.Status,
	}, nil
}

// SendBulkNotification sends notifications to multiple users
func (h *SimpleNotificationHandler) SendBulkNotification(ctx context.Context, req *notificationv1.BulkNotificationRequest) (*notificationv1.BulkNotificationResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_count": len(req.UserIds),
		"type":       req.Type,
	}).Info("gRPC SendBulkNotification called")

	// Convert data map
	data := make(map[string]interface{})
	for k, v := range req.Data {
		data[k] = v
	}

	// Convert channels
	var channels []string
	for _, ch := range req.Channels {
		switch ch {
		case notificationv1.NotificationChannel_EMAIL:
			channels = append(channels, "email")
		case notificationv1.NotificationChannel_SMS:
			channels = append(channels, "sms")
		case notificationv1.NotificationChannel_PUSH:
			channels = append(channels, "push")
		case notificationv1.NotificationChannel_IN_APP:
			channels = append(channels, "in_app")
		case notificationv1.NotificationChannel_WEBHOOK:
			channels = append(channels, "webhook")
		}
	}

	// Convert priority
	priority := "normal"
	switch req.Priority {
	case notificationv1.NotificationPriority_LOW:
		priority = "low"
	case notificationv1.NotificationPriority_HIGH:
		priority = "high"
	case notificationv1.NotificationPriority_URGENT:
		priority = "urgent"
	}

	NotificationIds := make([]string, 0, len(req.UserIds))
	var TotalRecipients int32 = 0
	for _, userID := range req.UserIds {
		h.logger.WithField("user_id", userID).Info("Sending bulk notification to user")

		// Create request
		sendReq := &notification.SendNotificationRequest{
			UserID:   userID,
			Type:     req.Type,
			Title:    req.Title,
			Message:  req.Message,
			Channels: channels,
			Priority: priority,
			Data:     data,
		}

		// Send notification
		result, err := h.notificationUC.SendNotification(ctx, sendReq)
		if err != nil {
			h.logger.WithError(err).Error("Failed to send notification")
			continue
		}
		NotificationIds = append(NotificationIds, result.NotificationID)
		TotalRecipients++
	}

	h.logger.Info("Bulk notification not implemented yet")

	return &notificationv1.BulkNotificationResponse{
		NotificationIds: NotificationIds,
		TotalRecipients: TotalRecipients,
	}, nil
}

// GetNotification retrieves a notification by ID
func (h *SimpleNotificationHandler) GetNotification(ctx context.Context, req *notificationv1.GetNotificationRequest) (*notificationv1.Notification, error) {
	h.logger.WithFields(map[string]interface{}{
		"notification_id": req.NotificationId,
		"user_id":         req.UserId,
	}).Info("gRPC GetNotification called")

	// TODO: Fix GetNotification method signature
	h.logger.Info("GetNotification not implemented yet")

	return &notificationv1.Notification{}, nil
}

// ListNotifications retrieves notifications for a user
func (h *SimpleNotificationHandler) ListNotifications(ctx context.Context, req *notificationv1.ListNotificationsRequest) (*notificationv1.ListNotificationsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC ListNotifications called")

	// TODO: Fix proto field mismatches and implement properly
	h.logger.Info("ListNotifications not implemented yet")

	return &notificationv1.ListNotificationsResponse{
		Notifications: []*notificationv1.Notification{},
	}, nil
}

// MarkAsRead marks a notification as read
func (h *SimpleNotificationHandler) MarkAsRead(ctx context.Context, req *notificationv1.MarkAsReadRequest) (*notificationv1.Empty, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC MarkAsRead called")

	// TODO: Fix proto field mismatches and method signature
	h.logger.Info("MarkAsRead not implemented yet")

	return &notificationv1.Empty{}, nil
}

// GetDeliveryStatus gets delivery status for a notification
func (h *SimpleNotificationHandler) GetDeliveryStatus(ctx context.Context, req *notificationv1.GetDeliveryStatusRequest) (*notificationv1.DeliveryStatus, error) {
	h.logger.WithFields(map[string]interface{}{
		"notification_id": req.NotificationId,
		"user_id":         req.UserId,
	}).Info("gRPC GetDeliveryStatus called")

	// TODO: Fix method signature and response fields
	h.logger.Info("GetDeliveryStatus not implemented yet")

	return &notificationv1.DeliveryStatus{
		NotificationId: req.NotificationId,
	}, nil
}

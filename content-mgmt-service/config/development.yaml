# Development Configuration for Content Management Service

# Server configuration for development
server:
  grpc_port: 50052
  http_port: 8082

# Development database settings
database:
  # Use SQLite for development for easier setup
  type: "sqlite"
  sqlite_path: "./data/content_mgmt_dev.db"
  
  # PostgreSQL fallback configuration
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "content_mgmt_dev"
  ssl_mode: "disable"
  
  # Connection pool settings
  max_open_conns: 10
  max_idle_conns: 2

# Development services configuration
services:
  user_service:
    host: "localhost"
    port: 50051
    timeout: "30s"
    retries: 3
  
  asset_service:
    host: "localhost"
    port: 50053
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "localhost"
    port: 50054
    timeout: "60s"
    retries: 2

# Development storage (local)
storage:
  provider: "local"
  local:
    base_path: "./dev-uploads"

# Development processing settings
processing:
  max_file_size: "50MB"
  processing_timeout: "2m"
  worker_count: 2
  queue_size: 50

# Development features (enable all for testing)
features:
  enable_templates: true
  enable_scheduling: true
  enable_collaboration: true
  enable_versioning: true
  enable_analytics: true
  enable_ai_generation: true

# Development rate limiting (more permissive)
rate_limit:
  enabled: false
  requests_per_minute: 1000
  burst: 100

# Development CORS (allow all origins)
cors:
  allowed_origins:
    - "*"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "*"
  allow_credentials: true

# Development monitoring
monitoring:
  enabled: true
  metrics_port: 9092

# Development logging
logging:
  level: "debug"
  format: "text"
  output: "stdout"

# Development tracing
tracing:
  enabled: false

package models

import (
	"time"
)

// RegisterRequest represents user registration request
type RegisterRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required,min=2,max=50"`
	LastName  string `json:"last_name" validate:"required,min=2,max=50"`
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// RegisterResponse represents user registration response
type RegisterResponse struct {
	UserID           uint64 `json:"user_id"`
	Email            string `json:"email"`
	FirstName        string `json:"first_name"`
	LastName         string `json:"last_name"`
	EmailVerified    bool   `json:"email_verified"`
	VerificationSent bool   `json:"verification_sent"`
	Message          string `json:"message"`
}

// LoginRequest represents user login request
type LoginRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required"`
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// LoginResponse represents user login response
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	TokenType    string    `json:"token_type"`
	ExpiresIn    int64     `json:"expires_in"`
	User         UserInfo  `json:"user"`
	LastLoginAt  time.Time `json:"last_login_at"`
}

// UserInfo represents basic user information
type UserInfo struct {
	ID            uint64    `json:"id"`
	Email         string    `json:"email"`
	FirstName     string    `json:"first_name"`
	LastName      string    `json:"last_name"`
	Role          string    `json:"role"`
	Status        string    `json:"status"`
	EmailVerified bool      `json:"email_verified"`
	AvatarURL     string    `json:"avatar_url,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
}

// LogoutRequest represents user logout request
type LogoutRequest struct {
	UserID    uint64 `json:"user_id"`
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// RefreshTokenRequest represents refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// TokenResponse represents token response
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// ForgotPasswordRequest represents forgot password request
type ForgotPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// ResetPasswordRequest represents reset password request
type ResetPasswordRequest struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// VerifyEmailRequest represents email verification request
type VerifyEmailRequest struct {
	Token string `json:"token" validate:"required"`
}

// ResendVerificationRequest represents resend verification request
type ResendVerificationRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// ChangePasswordRequest represents change password request
type ChangePasswordRequest struct {
	UserID          uint64 `json:"user_id"`
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID    uint64 `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	SessionID string `json:"session_id"`
	ExpiresAt int64  `json:"exp"`
	IssuedAt  int64  `json:"iat"`
}

// AuthAnalyticsRequest represents auth analytics request
type AuthAnalyticsRequest struct {
	Period string `json:"period"`
}

// AuthAnalyticsResponse represents auth analytics response
type AuthAnalyticsResponse struct {
	Period             string           `json:"period"`
	TotalLogins        int64            `json:"total_logins"`
	TotalRegistrations int64            `json:"total_registrations"`
	FailedLogins       int64            `json:"failed_logins"`
	UniqueUsers        int64            `json:"unique_users"`
	DailyStats         []DailyAuthStats `json:"daily_stats"`
	TopCountries       []CountryStats   `json:"top_countries"`
	DeviceStats        []DeviceStats    `json:"device_stats"`
}

// DailyAuthStats represents daily authentication statistics
type DailyAuthStats struct {
	Date          string `json:"date"`
	Logins        int64  `json:"logins"`
	Registrations int64  `json:"registrations"`
	FailedLogins  int64  `json:"failed_logins"`
}

// CountryStats represents country-based statistics
type CountryStats struct {
	Country string `json:"country"`
	Count   int64  `json:"count"`
}

// DeviceStats represents device-based statistics
type DeviceStats struct {
	Device string `json:"device"`
	Count  int64  `json:"count"`
}

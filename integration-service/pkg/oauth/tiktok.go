package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// TikTokOAuth handles TikTok OAuth flow
type TikTokOAuth struct {
	clientID     string
	clientSecret string
	redirectURL  string
	httpClient   *http.Client
	logger       logging.Logger
}

// NewTikTokOAuth creates a new TikTok OAuth handler
func NewTikTokOAuth(clientID, clientSecret, redirectURL string, logger logging.Logger) *TikTokOAuth {
	return &TikTokOAuth{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// TikTokTokenResponse represents TikTok token response
type TikTokTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
	OpenID       string `json:"open_id"`
}

// TikTokUserProfile represents TikTok user profile
type TikTokUserProfile struct {
	Data struct {
		User struct {
			OpenID      string `json:"open_id"`
			UnionID     string `json:"union_id"`
			AvatarURL   string `json:"avatar_url"`
			DisplayName string `json:"display_name"`
		} `json:"user"`
	} `json:"data"`
}

// GetAuthURL generates TikTok OAuth authorization URL
func (t *TikTokOAuth) GetAuthURL(state string, scopes []string) string {
	baseURL := "https://www.tiktok.com/auth/authorize/"

	params := url.Values{}
	params.Set("client_key", t.clientID)
	params.Set("response_type", "code")
	params.Set("redirect_uri", t.redirectURL)
	params.Set("state", state)

	if len(scopes) > 0 {
		params.Set("scope", strings.Join(scopes, ","))
	} else {
		// Default scopes for content publishing
		params.Set("scope", "user.info.basic,video.upload")
	}

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

// ExchangeCodeForToken exchanges authorization code for access token
func (t *TikTokOAuth) ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error) {
	tokenURL := "https://open-api.tiktok.com/oauth/access_token/"

	data := url.Values{}
	data.Set("client_key", t.clientID)
	data.Set("client_secret", t.clientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", t.redirectURL)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	var tokenResp struct {
		Data TikTokTokenResponse `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	t.logger.WithFields(map[string]interface{}{
		"expires_in": tokenResp.Data.ExpiresIn,
		"scope":      tokenResp.Data.Scope,
		"open_id":    tokenResp.Data.OpenID,
	}).Info("TikTok token exchanged successfully")

	// Convert TikTokTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.Data.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.Data.AccessToken,
		RefreshToken: tokenResp.Data.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.Data.TokenType,
		Scope:        tokenResp.Data.Scope,
	}, nil
}

// RefreshToken refreshes TikTok access token
func (t *TikTokOAuth) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	tokenURL := "https://open-api.tiktok.com/oauth/refresh_token/"

	data := url.Values{}
	data.Set("client_key", t.clientID)
	data.Set("client_secret", t.clientSecret)
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token refresh failed with status: %d", resp.StatusCode)
	}

	var tokenResp struct {
		Data TikTokTokenResponse `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode refresh response: %w", err)
	}

	// Convert TikTokTokenResponse to TokenResponse
	expiresAt := time.Now().Add(time.Duration(tokenResp.Data.ExpiresIn) * time.Second).Unix()

	return &TokenResponse{
		AccessToken:  tokenResp.Data.AccessToken,
		RefreshToken: tokenResp.Data.RefreshToken,
		ExpiresAt:    expiresAt,
		TokenType:    tokenResp.Data.TokenType,
		Scope:        tokenResp.Data.Scope,
	}, nil
}

// RevokeToken revokes TikTok access token
func (t *TikTokOAuth) RevokeToken(ctx context.Context, accessToken string) error {
	revokeURL := "https://open-api.tiktok.com/oauth/revoke/"

	data := url.Values{}
	data.Set("client_key", t.clientID)
	data.Set("client_secret", t.clientSecret)
	data.Set("token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "POST", revokeURL, strings.NewReader(data.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("token revocation failed with status: %d", resp.StatusCode)
	}

	t.logger.Info("TikTok token revoked successfully")
	return nil
}

// GetUserProfile implements Provider interface
func (t *TikTokOAuth) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	userURL := "https://open-api.tiktok.com/user/info/"

	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("fields", "open_id,union_id,avatar_url,display_name")

	req, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", userURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := t.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user profile failed with status: %d", resp.StatusCode)
	}

	var profile TikTokUserProfile
	if err := json.NewDecoder(resp.Body).Decode(&profile); err != nil {
		return nil, fmt.Errorf("failed to decode user profile: %w", err)
	}

	return &UserProfile{
		ID:          profile.Data.User.OpenID,
		Username:    profile.Data.User.DisplayName,
		DisplayName: profile.Data.User.DisplayName,
		AvatarURL:   profile.Data.User.AvatarURL,
	}, nil
}

// GetPermissions implements Provider interface
func (t *TikTokOAuth) GetPermissions(ctx context.Context, accessToken string) ([]string, error) {
	// TikTok doesn't have a specific permissions endpoint
	// Return default permissions based on successful authentication
	return []string{"user.info.basic", "video.upload"}, nil
}

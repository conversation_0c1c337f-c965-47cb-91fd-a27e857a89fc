package handlers

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	"github.com/social-content-ai/api-gateway/internal/middleware"
)

// UpdateUserRequest represents user update request
type UpdateUserRequest struct {
	FirstName   *string `json:"first_name,omitempty" binding:"omitempty,min=2,max=50"`
	LastName    *string `json:"last_name,omitempty" binding:"omitempty,min=2,max=50"`
	DisplayName *string `json:"display_name,omitempty" binding:"omitempty,max=100"`
	Bio         *string `json:"bio,omitempty" binding:"omitempty,max=500"`
	Website     *string `json:"website,omitempty" binding:"omitempty,url"`
	Location    *string `json:"location,omitempty" binding:"omitempty,max=100"`
	Timezone    *string `json:"timezone,omitempty"`
	Language    *string `json:"language,omitempty" binding:"omitempty,len=2"`
}

// UploadAvatarRequest represents avatar upload request
type UploadAvatarRequest struct {
	AvatarURL string `json:"avatar_url" binding:"required,url"`
}

// GetCurrentUser handles getting current user profile
// @Summary Get current user
// @Description Get current authenticated user's profile
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "User profile"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /users/me [get]
func (h *Handlers) GetCurrentUser(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get user profile
	getUserReq := &userv1.GetUserRequest{
		UserId: userID,
	}

	resp, err := h.services.UserMgmtService.GetUser(context.Background(), getUserReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user profile")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user profile",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User profile retrieved successfully",
		"user":    resp.User,
	})
}

// UpdateCurrentUser handles updating current user profile
// @Summary Update current user
// @Description Update current authenticated user's profile
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body UpdateUserRequest true "User update data"
// @Success 200 {object} map[string]interface{} "User updated"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /users/me [put]
func (h *Handlers) UpdateCurrentUser(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Update user profile
	updateReq := &userv1.UpdateUserRequest{
		UserId: userID,
	}

	// Set optional fields
	if req.FirstName != nil {
		updateReq.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		updateReq.LastName = *req.LastName
	}
	if req.DisplayName != nil {
		updateReq.DisplayName = *req.DisplayName
	}
	if req.Bio != nil {
		updateReq.Bio = *req.Bio
	}
	if req.Website != nil {
		updateReq.Website = *req.Website
	}
	if req.Location != nil {
		updateReq.Location = *req.Location
	}
	if req.Timezone != nil {
		updateReq.Timezone = *req.Timezone
	}
	if req.Language != nil {
		updateReq.Language = *req.Language
	}

	resp, err := h.services.UserMgmtService.UpdateUser(context.Background(), updateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to update user profile")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update user profile",
		})
		return
	}

	h.logger.WithField("user_id", userID).Info("User profile updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "User profile updated successfully",
		"user":    resp.User,
	})
}

// UploadAvatar handles avatar upload
// @Summary Upload avatar
// @Description Upload user avatar image
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body UploadAvatarRequest true "Avatar URL"
// @Success 200 {object} map[string]interface{} "Avatar uploaded"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /users/me/avatar [post]
func (h *Handlers) UploadAvatar(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req UploadAvatarRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Update user avatar
	updateReq := &userv1.UpdateUserRequest{
		UserId:    userID,
		AvatarUrl: req.AvatarURL,
	}

	resp, err := h.services.UserMgmtService.UpdateUser(context.Background(), updateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to upload avatar")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to upload avatar",
		})
		return
	}

	h.logger.WithField("user_id", userID).Info("Avatar uploaded successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":    "Avatar uploaded successfully",
		"avatar_url": req.AvatarURL,
		"user":       resp.User,
	})
}

// GetUserCredits handles getting user credit information
// @Summary Get user credits
// @Description Get current user's credit balance and usage
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "User credits"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /users/me/credits [get]
func (h *Handlers) GetUserCredits(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get user credits
	creditsReq := &creditv1.GetUserCreditsRequest{
		UserId: userID,
	}

	resp, err := h.services.CreditService.GetUserCredits(context.Background(), creditsReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user credits")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user credits",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":         "User credits retrieved successfully",
		"current_credits": resp.CurrentCredits,
		"total_earned":    resp.TotalEarned,
		"total_spent":     resp.TotalSpent,
		"subscription":    resp.Subscription,
		"usage_stats":     resp.UsageStats,
	})
}

// GetUserTransactions handles getting user transaction history
// @Summary Get user transactions
// @Description Get current user's transaction history
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param type query string false "Filter by transaction type"
// @Success 200 {object} map[string]interface{} "User transactions"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /users/me/transactions [get]
func (h *Handlers) GetUserTransactions(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Parse query parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	transactionType := c.Query("type")

	// Get user transactions
	transactionsReq := &creditv1.GetUserTransactionsRequest{
		UserId: userID,
		Page:   int32(page),
		Limit:  int32(limit),
		Type:   transactionType,
	}

	resp, err := h.services.CreditService.GetUserTransactions(context.Background(), transactionsReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user transactions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user transactions",
		})
		return
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, int(resp.Total))

	c.JSON(http.StatusOK, gin.H{
		"message":      "User transactions retrieved successfully",
		"transactions": resp.Transactions,
		"pagination":   pagination,
	})
}

// Admin-only handlers

// ListAllUsers handles listing all users (admin only)
// @Summary List all users
// @Description Get list of all users (admin only)
// @Tags admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search users"
// @Param role query string false "Filter by role"
// @Success 200 {object} map[string]interface{} "List of users"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /admin/users [get]
func (h *Handlers) ListAllUsers(c *gin.Context) {
	// Parse query parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 20
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	search := c.Query("search")
	role := c.Query("role")

	// List all users
	listReq := &userv1.ListUsersRequest{
		Page:   int32(page),
		Limit:  int32(limit),
		Search: search,
		Role:   role,
	}

	resp, err := h.services.UserMgmtService.ListUsers(context.Background(), listReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list users",
		})
		return
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, int(resp.Total))

	c.JSON(http.StatusOK, gin.H{
		"message":    "Users retrieved successfully",
		"users":      resp.Users,
		"pagination": pagination,
	})
}

// GetUserByID handles getting user by ID (admin only)
// @Summary Get user by ID
// @Description Get user details by ID (admin only)
// @Tags admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} map[string]interface{} "User details"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /admin/users/{id} [get]
func (h *Handlers) GetUserByID(c *gin.Context) {
	targetUserID := c.Param("id")
	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "User ID is required",
		})
		return
	}

	// Get user by ID
	getUserReq := &userv1.GetUserRequest{
		UserId: targetUserID,
	}

	resp, err := h.services.UserMgmtService.GetUser(context.Background(), getUserReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "User not found",
			})
			return
		}

		h.logger.WithError(err).WithField("target_user_id", targetUserID).Error("Failed to get user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get user",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User retrieved successfully",
		"user":    resp.User,
	})
}

// UpdateUserByID handles updating user by ID (admin only)
// @Summary Update user by ID
// @Description Update user details by ID (admin only)
// @Tags admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param request body UpdateUserRequest true "User update data"
// @Success 200 {object} map[string]interface{} "User updated"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /admin/users/{id} [put]
func (h *Handlers) UpdateUserByID(c *gin.Context) {
	targetUserID := c.Param("id")
	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "User ID is required",
		})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Update user
	updateReq := &userv1.UpdateUserRequest{
		UserId: targetUserID,
	}

	// Set optional fields
	if req.FirstName != nil {
		updateReq.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		updateReq.LastName = *req.LastName
	}
	if req.DisplayName != nil {
		updateReq.DisplayName = *req.DisplayName
	}
	if req.Bio != nil {
		updateReq.Bio = *req.Bio
	}
	if req.Website != nil {
		updateReq.Website = *req.Website
	}
	if req.Location != nil {
		updateReq.Location = *req.Location
	}
	if req.Timezone != nil {
		updateReq.Timezone = *req.Timezone
	}
	if req.Language != nil {
		updateReq.Language = *req.Language
	}

	resp, err := h.services.UserMgmtService.UpdateUser(context.Background(), updateReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "User not found",
			})
			return
		}

		h.logger.WithError(err).WithField("target_user_id", targetUserID).Error("Failed to update user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update user",
		})
		return
	}

	h.logger.WithField("target_user_id", targetUserID).Info("User updated successfully by admin")

	c.JSON(http.StatusOK, gin.H{
		"message": "User updated successfully",
		"user":    resp.User,
	})
}

// DeleteUserByID handles deleting user by ID (admin only)
// @Summary Delete user by ID
// @Description Delete user by ID (admin only)
// @Tags admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} map[string]interface{} "User deleted"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /admin/users/{id} [delete]
func (h *Handlers) DeleteUserByID(c *gin.Context) {
	targetUserID := c.Param("id")
	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "User ID is required",
		})
		return
	}

	// Delete user
	deleteReq := &userv1.DeleteUserRequest{
		UserId: targetUserID,
	}

	_, err := h.services.UserMgmtService.DeleteUser(context.Background(), deleteReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "User not found",
			})
			return
		}

		h.logger.WithError(err).WithField("target_user_id", targetUserID).Error("Failed to delete user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete user",
		})
		return
	}

	h.logger.WithField("target_user_id", targetUserID).Info("User deleted successfully by admin")

	c.JSON(http.StatusOK, gin.H{
		"message": "User deleted successfully",
	})
}

syntax = "proto3";

package rag_processing.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/rag-processing/v1;ragprocessingv1";

// Document represents a document for RAG processing
message Document {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string template_id = 4;        // Optional template association
  string file_path = 5;          // S3/MinIO path
  string file_name = 6;
  string file_type = 7;          // pdf, docx, txt, md
  int64 file_size = 8;
  string content_hash = 9;       // For change detection
  string status = 10;            // pending, processing, completed, failed
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
}

// ProcessDocumentRequest for starting document processing
message ProcessDocumentRequest {
  string document_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string template_id = 4;        // Optional template ID
  string file_path = 5;          // S3/MinIO path
  string document_type = 6;      // pdf, docx, txt, md
  map<string, string> processing_options = 7;
  string training_purpose = 8;   // template_creation, template_update, direct_upload
  int32 priority = 9;            // Processing priority (1-10)
}

// ProcessDocumentResponse with processing status
message ProcessDocumentResponse {
  string document_id = 1;
  string processing_id = 2;      // Unique processing job ID
  string status = 3;             // started, queued
  google.protobuf.Timestamp started_at = 4;
  int32 estimated_duration = 5;  // Estimated processing time in seconds
}

// ProcessingStatusRequest for checking processing status
message ProcessingStatusRequest {
  string processing_id = 1;
  string document_id = 2;        // Alternative lookup
}

// ProcessingStatusResponse with current status
message ProcessingStatusResponse {
  string processing_id = 1;
  string document_id = 2;
  string status = 3;             // processing, completed, failed
  float progress = 4;            // Progress percentage (0-100)
  string current_stage = 5;      // text_extraction, chunking, embedding, graph_building
  google.protobuf.Timestamp started_at = 6;
  google.protobuf.Timestamp completed_at = 7;
  string error_message = 8;      // If failed
  ProcessingStats stats = 9;
}

// ProcessingStats with processing metrics
message ProcessingStats {
  int32 chunks_count = 1;
  int32 entities_count = 2;
  int32 relationships_count = 3;
  int32 concepts_count = 4;
  float processing_time = 5;     // Total processing time in seconds
  int64 tokens_processed = 6;
}

// ContextRetrievalRequest for retrieving relevant context
message ContextRetrievalRequest {
  string query = 1;
  string user_id = 2;
  string workspace_id = 3;
  string template_id = 4;        // Optional template filter
  int32 max_chunks = 5;          // Maximum chunks to return
  float similarity_threshold = 6; // Minimum similarity score
  repeated string entity_types = 7; // Filter by entity types
  bool include_metadata = 8;     // Include chunk metadata
}

// ContextChunk represents a relevant text chunk
message ContextChunk {
  string chunk_id = 1;
  string document_id = 2;
  string content = 3;
  float similarity_score = 4;
  int32 chunk_index = 5;         // Position in original document
  map<string, string> metadata = 6;
  repeated Entity entities = 7;  // Entities in this chunk
}

// Entity represents an extracted entity
message Entity {
  string id = 1;
  string name = 2;
  string type = 3;               // PERSON, ORG, LOCATION, CONCEPT, etc.
  float confidence = 4;          // Extraction confidence (0-1)
  string description = 5;        // Entity description
  repeated string aliases = 6;   // Alternative names
}

// Relationship represents entity relationships
message Relationship {
  string id = 1;
  string source_entity_id = 2;
  string target_entity_id = 3;
  string relationship_type = 4;  // RELATES_TO, PART_OF, CAUSES, etc.
  float confidence = 5;          // Relationship confidence (0-1)
  string description = 6;        // Relationship description
}

// ContextRetrievalResponse with relevant context
message ContextRetrievalResponse {
  repeated ContextChunk chunks = 1;
  repeated Entity entities = 2;
  repeated Relationship relationships = 3;
  float max_similarity = 4;      // Highest similarity score
  int32 total_chunks_found = 5;  // Total chunks before filtering
  string reasoning_path = 6;     // Graph reasoning path (if applicable)
}

// GraphQueryRequest for complex graph-based queries
message GraphQueryRequest {
  string question = 1;
  string user_id = 2;
  string workspace_id = 3;
  string template_id = 4;        // Optional template filter
  repeated string focus_entities = 5; // Entities to focus on
  int32 max_hops = 6;            // Maximum graph traversal depth
  bool include_reasoning_path = 7; // Include reasoning explanation
  string query_type = 8;         // simple, multi_hop, causal, temporal
}

// GraphQueryResponse with graph-based answer
message GraphQueryResponse {
  string answer = 1;
  repeated ContextChunk supporting_chunks = 2;
  repeated Entity relevant_entities = 3;
  repeated Relationship reasoning_path = 4;
  float confidence_score = 5;    // Answer confidence (0-1)
  string reasoning_explanation = 6; // Human-readable reasoning
  map<string, string> metadata = 7;
}

// SimilaritySearchRequest for finding similar documents
message SimilaritySearchRequest {
  string query = 1;              // Text query or document ID
  string user_id = 2;
  string workspace_id = 3;
  int32 max_results = 4;         // Maximum documents to return
  float similarity_threshold = 5; // Minimum similarity score
  repeated string document_types = 6; // Filter by document types
}

// SimilarDocument represents a similar document
message SimilarDocument {
  string document_id = 1;
  string file_name = 2;
  string file_type = 3;
  float similarity_score = 4;
  string summary = 5;            // Document summary
  repeated string key_entities = 6; // Main entities in document
  google.protobuf.Timestamp created_at = 7;
}

// SimilaritySearchResponse with similar documents
message SimilaritySearchResponse {
  repeated SimilarDocument documents = 1;
  int32 total_found = 2;         // Total documents found
  float max_similarity = 3;      // Highest similarity score
}

// ReprocessDocumentRequest for reprocessing existing document
message ReprocessDocumentRequest {
  string document_id = 1;
  string user_id = 2;
  bool cleanup_old_data = 3;     // Whether to clean up old RAG data
  map<string, string> processing_options = 4;
  string reason = 5;             // Reason for reprocessing
}

// DeleteDocumentRequest for removing document from RAG
message DeleteDocumentRequest {
  string document_id = 1;
  string user_id = 2;
  bool cleanup_vectors = 3;      // Whether to clean up vector embeddings
  bool cleanup_graph = 4;        // Whether to clean up graph data
}

// GetDocumentRequest for retrieving document info
message GetDocumentRequest {
  string document_id = 1;
  string user_id = 2;
}

// ListDocumentsRequest for listing user documents
message ListDocumentsRequest {
  string user_id = 1;
  string workspace_id = 2;
  string template_id = 3;        // Optional template filter
  common.v1.PaginationRequest pagination = 4;
  string status = 5;             // Filter by processing status
  string file_type = 6;          // Filter by file type
}

// ListDocumentsResponse with paginated documents
message ListDocumentsResponse {
  repeated Document documents = 1;
  common.v1.PaginationResponse pagination = 2;
}

// Empty response
message Empty {}

// RAGProcessingService provides document processing and retrieval
service RAGProcessingService {
  // Document processing operations
  rpc ProcessDocument(ProcessDocumentRequest) returns (ProcessDocumentResponse);
  rpc GetProcessingStatus(ProcessingStatusRequest) returns (ProcessingStatusResponse);
  rpc ReprocessDocument(ReprocessDocumentRequest) returns (ProcessDocumentResponse);
  rpc DeleteDocument(DeleteDocumentRequest) returns (Empty);
  
  // Document management operations
  rpc GetDocument(GetDocumentRequest) returns (Document);
  rpc ListDocuments(ListDocumentsRequest) returns (ListDocumentsResponse);
  
  // Context retrieval operations
  rpc RetrieveContext(ContextRetrievalRequest) returns (ContextRetrievalResponse);
  rpc GraphRAGQuery(GraphQueryRequest) returns (GraphQueryResponse);
  rpc SearchSimilarDocuments(SimilaritySearchRequest) returns (SimilaritySearchResponse);
}

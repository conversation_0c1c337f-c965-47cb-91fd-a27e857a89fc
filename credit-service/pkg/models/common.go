package models

import "time"

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// BaseModel represents common fields for all models
type BaseModel struct {
	ID        string     `json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
}

// DateRange represents a date range filter
type DateRange struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}

// CreditValidationResult represents credit validation result
type CreditValidationResult struct {
	Valid               bool `json:"valid"`
	CurrentCredits      int  `json:"current_credits"`
	RequiredCredits     int  `json:"required_credits"`
	HasSufficientCredits bool `json:"has_sufficient_credits"`
	Shortage            int  `json:"shortage,omitempty"`
	MonthlyUsed         int  `json:"monthly_used"`
	MonthlyLimit        int  `json:"monthly_limit"`
	MonthlyRemaining    int  `json:"monthly_remaining"`
}

// MonthlyLimitResult represents monthly limit check result
type MonthlyLimitResult struct {
	WithinLimit      bool `json:"within_limit"`
	MonthlyUsed      int  `json:"monthly_used"`
	MonthlyLimit     int  `json:"monthly_limit"`
	MonthlyRemaining int  `json:"monthly_remaining"`
	ExceedsBy        int  `json:"exceeds_by,omitempty"`
}

// CreditReservation represents a credit reservation
type CreditReservation struct {
	ID              string    `json:"id"`
	UserID          string    `json:"user_id"`
	Amount          int       `json:"amount"`
	Purpose         string    `json:"purpose"`
	ReferenceID     string    `json:"reference_id,omitempty"`
	ReferenceType   string    `json:"reference_type,omitempty"`
	Status          string    `json:"status"` // reserved, consumed, expired, cancelled
	ExpiresAt       time.Time `json:"expires_at"`
	CreatedAt       time.Time `json:"created_at"`
	ConsumedAt      *time.Time `json:"consumed_at,omitempty"`
}

// PaymentMethod represents payment method information
type PaymentMethod struct {
	Type        string                 `json:"type"` // card, bank_transfer, momo, zalopay
	Provider    string                 `json:"provider,omitempty"`
	Details     map[string]interface{} `json:"details,omitempty"`
	IsDefault   bool                   `json:"is_default"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
}

// BillingAddress represents billing address
type BillingAddress struct {
	Name        string `json:"name"`
	Company     string `json:"company,omitempty"`
	Address1    string `json:"address1"`
	Address2    string `json:"address2,omitempty"`
	City        string `json:"city"`
	State       string `json:"state,omitempty"`
	PostalCode  string `json:"postal_code"`
	Country     string `json:"country"`
	TaxID       string `json:"tax_id,omitempty"`
}

// Invoice represents an invoice
type Invoice struct {
	BaseModel
	InvoiceNumber   string                 `json:"invoice_number"`
	UserID          string                 `json:"user_id"`
	OrderID         string                 `json:"order_id,omitempty"`
	Amount          int64                  `json:"amount"` // in cents
	Currency        string                 `json:"currency"`
	Status          string                 `json:"status"` // draft, sent, paid, overdue, cancelled
	DueDate         time.Time              `json:"due_date"`
	PaidAt          *time.Time             `json:"paid_at,omitempty"`
	Items           []InvoiceItem          `json:"items"`
	BillingAddress  BillingAddress         `json:"billing_address"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	DownloadURL     string                 `json:"download_url,omitempty"`
}

// InvoiceItem represents an invoice item
type InvoiceItem struct {
	Description string `json:"description"`
	Quantity    int    `json:"quantity"`
	UnitPrice   int64  `json:"unit_price"` // in cents
	Amount      int64  `json:"amount"`     // in cents
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// CreditStats represents credit statistics
type CreditStats struct {
	Period              string                    `json:"period"`
	TotalCreditsEarned  int                       `json:"total_credits_earned"`
	TotalCreditsUsed    int                       `json:"total_credits_used"`
	CurrentBalance      int                       `json:"current_balance"`
	MonthlyUsed         int                       `json:"monthly_used"`
	MonthlyLimit        int                       `json:"monthly_limit"`
	MonthlyRemaining    int                       `json:"monthly_remaining"`
	UsageByCategory     map[string]int            `json:"usage_by_category"`
	UsageByModel        map[string]int            `json:"usage_by_model"`
	Timeline            []CreditTimelinePoint     `json:"timeline"`
	TopUsageCategories  []CategoryUsage           `json:"top_usage_categories"`
	RecentTransactions  []TransactionSummary      `json:"recent_transactions"`
}

// CreditTimelinePoint represents a point in credit timeline
type CreditTimelinePoint struct {
	Date         string `json:"date"`
	CreditsEarned int   `json:"credits_earned"`
	CreditsUsed   int   `json:"credits_used"`
	Balance      int    `json:"balance"`
}

// CategoryUsage represents usage by category
type CategoryUsage struct {
	Category string `json:"category"`
	Credits  int    `json:"credits"`
	Count    int    `json:"count"`
}

// TransactionSummary represents transaction summary
type TransactionSummary struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Amount      int       `json:"amount"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// UsageReport represents usage report
type UsageReport struct {
	Period           string                    `json:"period"`
	TotalCreditsUsed int                       `json:"total_credits_used"`
	TotalTransactions int                      `json:"total_transactions"`
	AveragePerDay    float64                   `json:"average_per_day"`
	UsageByDay       []DailyUsage              `json:"usage_by_day"`
	UsageByCategory  map[string]int            `json:"usage_by_category"`
	UsageByModel     map[string]int            `json:"usage_by_model"`
	TopCategories    []CategoryUsage           `json:"top_categories"`
	Trends           UsageTrends               `json:"trends"`
}

// DailyUsage represents daily usage
type DailyUsage struct {
	Date         string `json:"date"`
	Credits      int    `json:"credits"`
	Transactions int    `json:"transactions"`
}

// UsageTrends represents usage trends
type UsageTrends struct {
	CreditsTrend      string  `json:"credits_trend"` // increasing, decreasing, stable
	PercentageChange  float64 `json:"percentage_change"`
	ComparedToPrevious string `json:"compared_to_previous"`
}

// ModelCreditConfig represents credit configuration for AI models
type ModelCreditConfig struct {
	Models map[string]ModelConfig `json:"models"`
}

// ModelConfig represents configuration for a specific model
type ModelConfig struct {
	Type         string `json:"type"` // text, image
	BaseCost     int    `json:"base_cost,omitempty"`
	Per1000Tokens int   `json:"per_1000_tokens,omitempty"`
	PerImage     int    `json:"per_image,omitempty"`
	Description  string `json:"description"`
	Enabled      bool   `json:"enabled"`
}

// BulkOperationResult represents result of bulk operations
type BulkOperationResult struct {
	Total     int                    `json:"total"`
	Success   int                    `json:"success"`
	Failed    int                    `json:"failed"`
	Errors    []BulkOperationError   `json:"errors,omitempty"`
	Results   []interface{}          `json:"results,omitempty"`
}

// BulkOperationError represents an error in bulk operations
type BulkOperationError struct {
	ID     string `json:"id,omitempty"`
	Index  int    `json:"index"`
	Error  string `json:"error"`
	Reason string `json:"reason"`
}

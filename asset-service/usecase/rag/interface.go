package rag

import (
	"context"

	"github.com/social-content-ai/asset-service/pkg/models"
)

// UseCase defines the RAG document management business logic interface
type UseCase interface {
	// Basic CRUD operations
	UploadRAGDocument(ctx context.Context, req *models.UploadRAGDocumentRequest) (*models.RAGDocumentResponse, error)
	GetRAGDocument(ctx context.Context, documentID, userID string) (*models.RAGDocumentResponse, error)
	UpdateRAGDocument(ctx context.Context, req *models.UpdateRAGDocumentRequest) (*models.RAGDocumentResponse, error)
	DeleteRAGDocument(ctx context.Context, documentID, userID string) error
	ListRAGDocuments(ctx context.Context, req *models.ListRAGDocumentsRequest) (*models.ListRAGDocumentsResponse, error)

	// RAG processing
	ProcessRAGDocument(ctx context.Context, req *models.ProcessRAGDocumentRequest) (*models.RAGDocumentResponse, error)
	ReindexRAGDocument(ctx context.Context, documentID, userID string) (*models.RAGDocumentResponse, error)
	GetRAGProcessingStatus(ctx context.Context, documentID, userID string) (*models.RAGProcessingStatus, error)

	// RAG search
	SearchRAGDocuments(ctx context.Context, req *models.SearchRAGDocumentsRequest) (*models.SearchRAGDocumentsResponse, error)
	SemanticSearch(ctx context.Context, req *models.SemanticSearchRequest) (*models.SearchRAGDocumentsResponse, error)
	GetRAGCategories(ctx context.Context, userID string) (*models.RAGCategoriesResponse, error)

	// Analytics
	GetRAGAnalytics(ctx context.Context, userID, period string) (*models.RAGAnalyticsResponse, error)

	// Admin operations
	AdminListRAGDocuments(ctx context.Context, req *models.ListRAGDocumentsRequest) (*models.ListRAGDocumentsResponse, error)
	ReindexAllRAGDocuments(ctx context.Context) (*models.BulkOperationResult, error)
	GetRAGStats(ctx context.Context) (*models.RAGAnalyticsResponse, error)

	// Webhook operations
	RAGProcessingCompleteWebhook(ctx context.Context, documentID string, status string, chunks []models.RAGChunk) error

	// Vector operations
	GetSimilarDocuments(ctx context.Context, documentID, userID string, limit int) ([]models.RAGSearchResult, error)
	GetDocumentEmbeddings(ctx context.Context, documentID, userID string) ([]models.RAGEmbedding, error)
	UpdateDocumentEmbeddings(ctx context.Context, documentID, userID string, embeddings []models.RAGEmbedding) error

	// Content extraction
	ExtractTextFromAsset(ctx context.Context, assetID, userID string) (string, error)
	ExtractMetadataFromAsset(ctx context.Context, assetID, userID string) (map[string]interface{}, error)
	ChunkDocument(ctx context.Context, content string, options models.RAGProcessingOptions) ([]models.RAGChunk, error)
}

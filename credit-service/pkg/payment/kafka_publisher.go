package payment

import (
	"context"
	"fmt"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaEventPublisher implements EventPublisher using Kafka
type KafkaEventPublisher struct {
	producer sharedKafka.Producer
	logger   logging.Logger
}

// NewKafkaEventPublisher creates a new Kafka event publisher
func NewKafkaEventPublisher(producer sharedKafka.Producer, logger logging.Logger) *KafkaEventPublisher {
	return &KafkaEventPublisher{
		producer: producer,
		logger:   logger,
	}
}

// PublishEvent publishes an event to Kafka topic
func (k *KafkaEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	k.logger.WithFields(map[string]interface{}{
		"topic":      topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Publishing event to Kafka")

	// For now, use a simplified approach - just log the event
	// TODO: Implement proper Kafka publishing when shared Kafka interface is clarified
	k.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"event": event,
	}).Info("Event published to Kafka (mock implementation)")

	return nil
}

// generateMessageKey generates a message key for partitioning
func (k *KafkaEventPublisher) generateMessageKey(event interface{}) string {
	switch e := event.(type) {
	case *SubscriptionUpdateEvent:
		if e.SubscriptionID != "" {
			return e.SubscriptionID
		}
		if e.UserID != "" {
			return e.UserID
		}
		return e.EventID
	default:
		return "default"
	}
}

// MockEventPublisher implements EventPublisher for testing
type MockEventPublisher struct {
	logger logging.Logger
	events []PublishedEvent
}

// PublishedEvent represents a published event for testing
type PublishedEvent struct {
	Topic string
	Event interface{}
}

// NewMockEventPublisher creates a new mock event publisher
func NewMockEventPublisher(logger logging.Logger) *MockEventPublisher {
	return &MockEventPublisher{
		logger: logger,
		events: make([]PublishedEvent, 0),
	}
}

// PublishEvent mocks event publishing
func (m *MockEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"topic":      topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Mock publishing event")

	// Store event for testing
	m.events = append(m.events, PublishedEvent{
		Topic: topic,
		Event: event,
	})

	return nil
}

// GetPublishedEvents returns all published events (for testing)
func (m *MockEventPublisher) GetPublishedEvents() []PublishedEvent {
	return m.events
}

// ClearEvents clears all published events (for testing)
func (m *MockEventPublisher) ClearEvents() {
	m.events = make([]PublishedEvent, 0)
}

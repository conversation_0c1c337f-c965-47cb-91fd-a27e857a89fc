# Production Configuration for Content Management Service

# Server configuration for production
server:
  grpc_port: 50052
  http_port: 8082
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Production database settings (use environment variables)
database:
  # Always use PostgreSQL in production
  type: "postgres"
  host: "${DB_HOST}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  name: "${DB_NAME}"
  ssl_mode: "require"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Production services configuration
services:
  user_service:
    host: "${USER_SERVICE_HOST:user-service}"
    port: "${USER_SERVICE_PORT:50051}"
    timeout: "30s"
    retries: 3
  
  asset_service:
    host: "${ASSET_SERVICE_HOST:asset-service}"
    port: "${ASSET_SERVICE_PORT:50053}"
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "${AI_SERVICE_HOST:ai-service}"
    port: "${AI_SERVICE_PORT:50054}"
    timeout: "60s"
    retries: 2
  
  notification_service:
    host: "${NOTIFICATION_SERVICE_HOST:notification-service}"
    port: "${NOTIFICATION_SERVICE_PORT:50055}"
    timeout: "30s"
    retries: 3

# Production storage (S3)
storage:
  provider: "${STORAGE_PROVIDER:s3}"
  
  s3:
    bucket: "${S3_BUCKET}"
    region: "${S3_REGION:us-east-1}"
    access_key: "${S3_ACCESS_KEY}"
    secret_key: "${S3_SECRET_KEY}"
    endpoint: "${S3_ENDPOINT}"

# Production processing settings
processing:
  max_file_size: "${MAX_FILE_SIZE:200MB}"
  processing_timeout: "10m"
  worker_count: "${WORKER_COUNT:8}"
  queue_size: "${QUEUE_SIZE:500}"

# Production features
features:
  enable_templates: "${ENABLE_TEMPLATES:true}"
  enable_scheduling: "${ENABLE_SCHEDULING:true}"
  enable_collaboration: "${ENABLE_COLLABORATION:true}"
  enable_versioning: "${ENABLE_VERSIONING:true}"
  enable_analytics: "${ENABLE_ANALYTICS:true}"
  enable_ai_generation: "${ENABLE_AI_GENERATION:true}"

# Production rate limiting
rate_limit:
  enabled: true
  requests_per_minute: "${RATE_LIMIT_RPM:200}"
  burst: "${RATE_LIMIT_BURST:50}"

# Production CORS (restrictive)
cors:
  allowed_origins:
    - "${FRONTEND_URL}"
    - "${ADMIN_URL}"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
    - "X-User-ID"
  allow_credentials: true
  max_age: 86400

# Production monitoring
monitoring:
  enabled: true
  metrics_port: "${METRICS_PORT:9092}"
  health_check_path: "/health"
  metrics_path: "/metrics"

# Production logging
logging:
  level: "${LOG_LEVEL:info}"
  format: "json"
  output: "stdout"

# Production tracing
tracing:
  enabled: "${TRACING_ENABLED:true}"
  service_name: "content-mgmt-service"
  jaeger_endpoint: "${JAEGER_ENDPOINT}"

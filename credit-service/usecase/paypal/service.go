package paypal

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/payment"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB              *ent.Client
	writeDB             *ent.Client
	creditUseCase       credit.UseCase
	subscriptionUseCase subscription.UseCase
	config              *models.PayPalConfig
	httpClient          *http.Client
	logger              logging.Logger
}

// NewService creates a new PayPal service
func NewService(
	readDB, writeDB *ent.Client,
	creditUseCase credit.UseCase,
	subscriptionUseCase subscription.UseCase,
	config *models.PayPalConfig,
	logger logging.Logger,
) UseCase {
	return &service{
		readDB:              readDB,
		writeDB:             writeDB,
		creditUseCase:       creditUseCase,
		subscriptionUseCase: subscriptionUseCase,
		config:              config,
		httpClient:          &http.Client{Timeout: 30 * time.Second},
		logger:              logger,
	}
}

// InitiatePayment initiates a PayPal payment
func (s *service) InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Initiating PayPal payment")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Create PayPal order
	order, err := s.CreateOrder(ctx, req.Amount, req.Currency, req.Description, req.Metadata)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create PayPal order")
		return nil, fmt.Errorf("failed to create PayPal order: %w", err)
	}

	// Parse plan ID if provided
	var planUUID *uuid.UUID
	if req.PlanID != "" {
		parsed, err := uuid.Parse(req.PlanID)
		if err != nil {
			return nil, fmt.Errorf("invalid plan ID format: %w", err)
		}
		planUUID = &parsed
	}

	// Create payment record in database
	paymentRecord, err := s.writeDB.Payment.Create().
		SetUserID(userUUID).
		SetNillableSubscriptionID(planUUID). // Using subscription_id field for plan_id
		SetPaymentMethod("paypal").
		SetPaymentProvider("paypal").
		SetExternalPaymentID(order.ID).
		SetAmount(req.Amount).
		SetCurrency(req.Currency).
		SetStatus(payment.StatusPending).
		SetMetadata(req.Metadata).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create payment record")
		return nil, fmt.Errorf("failed to create payment record: %w", err)
	}

	// Find approval URL from PayPal order links
	var approvalURL string
	for _, link := range order.Links {
		if link.Rel == "approve" {
			approvalURL = link.Href
			break
		}
	}

	if approvalURL == "" {
		return nil, fmt.Errorf("no approval URL found in PayPal order response")
	}

	// TODO: Publish PayPalPaymentInitiated event to Kafka

	return &models.InitiatePayPalPaymentResponse{
		Success:       true,
		PaymentID:     paymentRecord.ID.String(),
		PayPalOrderID: order.ID,
		ApprovalURL:   approvalURL,
	}, nil
}

// CreateOrder creates a PayPal order
func (s *service) CreateOrder(ctx context.Context, amount int64, currency, description string, metadata map[string]interface{}) (*models.PayPalOrder, error) {
	// Get access token
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// Format amount for PayPal
	amountStr := s.formatAmountForPayPal(amount, currency)

	// Create order request
	orderReq := map[string]interface{}{
		"intent": "CAPTURE",
		"purchase_units": []map[string]interface{}{
			{
				"amount": map[string]interface{}{
					"currency_code": currency,
					"value":         amountStr,
				},
				"description": description,
			},
		},
		"application_context": map[string]interface{}{
			"brand_name":          "Social AI",
			"landing_page":        "BILLING",
			"shipping_preference": "NO_SHIPPING",
			"user_action":         "PAY_NOW",
		},
	}

	// Convert to JSON
	reqBody, err := json.Marshal(orderReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal order request: %w", err)
	}

	// Get PayPal API endpoint
	endpoint := models.PayPalEndpoints[s.config.Environment]["orders"]

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("PayPal-Request-Id", uuid.New().String())

	// Send request
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusCreated {
		s.logger.WithFields(map[string]interface{}{
			"status_code": resp.StatusCode,
			"response":    string(respBody),
		}).Error("PayPal order creation failed")
		return nil, fmt.Errorf("PayPal order creation failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var order models.PayPalOrder
	if err := json.Unmarshal(respBody, &order); err != nil {
		return nil, fmt.Errorf("failed to unmarshal order response: %w", err)
	}

	return &order, nil
}

// GetAccessToken gets PayPal access token
func (s *service) GetAccessToken(ctx context.Context) (string, error) {
	// Create request body
	reqBody := strings.NewReader("grant_type=client_credentials")

	// Get OAuth endpoint
	endpoint := models.PayPalEndpoints[s.config.Environment]["oauth"]

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", endpoint, reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Accept", "application/json")
	httpReq.Header.Set("Accept-Language", "en_US")
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	httpReq.SetBasicAuth(s.config.ClientID, s.config.ClientSecret)

	// Send request
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OAuth request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var tokenResp struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		ExpiresIn   int    `json:"expires_in"`
	}
	if err := json.Unmarshal(respBody, &tokenResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal token response: %w", err)
	}

	return tokenResp.AccessToken, nil
}

// formatAmountForPayPal formats amount for PayPal API (converts cents to dollars)
func (s *service) formatAmountForPayPal(amount int64, currency string) string {
	switch currency {
	case "USD", "EUR", "GBP", "CAD", "AUD":
		// These currencies use 2 decimal places
		dollars := float64(amount) / 100.0
		return fmt.Sprintf("%.2f", dollars)
	case "JPY", "KRW":
		// These currencies don't use decimal places
		return fmt.Sprintf("%d", amount)
	case "VND":
		// Vietnamese Dong - convert from smallest unit
		return fmt.Sprintf("%d", amount)
	default:
		// Default to 2 decimal places
		dollars := float64(amount) / 100.0
		return fmt.Sprintf("%.2f", dollars)
	}
}

// ExecutePayment executes a PayPal payment after user approval
func (s *service) ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"payer_id":   req.PayerID,
		"user_id":    req.UserID,
	}).Info("Executing PayPal payment")

	// Parse payment ID
	paymentUUID, err := uuid.Parse(req.PaymentID)
	if err != nil {
		return nil, fmt.Errorf("invalid payment ID format: %w", err)
	}

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get payment record
	paymentRecord, err := s.readDB.Payment.Query().
		Where(
			payment.ID(paymentUUID),
			payment.UserID(userUUID),
		).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return &models.ExecutePayPalPaymentResponse{
				Success:      false,
				ErrorMessage: "Payment not found",
			}, nil
		}
		return nil, fmt.Errorf("failed to get payment record: %w", err)
	}

	// Check if payment is in correct status
	if paymentRecord.Status != payment.StatusPending {
		return &models.ExecutePayPalPaymentResponse{
			Success:      false,
			ErrorMessage: "Payment is not in pending status",
		}, nil
	}

	// Capture PayPal order
	order, err := s.CaptureOrder(ctx, paymentRecord.ExternalPaymentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to capture PayPal order")
		return &models.ExecutePayPalPaymentResponse{
			Success:      false,
			ErrorMessage: "Failed to capture PayPal payment",
		}, nil
	}

	// Check if capture was successful
	if order.Status != "COMPLETED" {
		return &models.ExecutePayPalPaymentResponse{
			Success:      false,
			ErrorMessage: "PayPal payment capture failed",
		}, nil
	}

	// Update payment record
	now := time.Now()
	_, err = s.writeDB.Payment.UpdateOne(paymentRecord).
		SetStatus(payment.StatusCompleted).
		SetPaidAt(now).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update payment record")
		return nil, fmt.Errorf("failed to update payment record: %w", err)
	}

	// Process subscription activation or credit addition based on purpose
	response := &models.ExecutePayPalPaymentResponse{
		Success:   true,
		PaymentID: req.PaymentID,
	}

	// TODO: Implement subscription activation and credit addition
	// TODO: Publish PayPalPaymentCompleted event to Kafka

	return response, nil
}

// CaptureOrder captures a PayPal order
func (s *service) CaptureOrder(ctx context.Context, orderID string) (*models.PayPalOrder, error) {
	// Get access token
	accessToken, err := s.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// Get PayPal API endpoint
	endpoint := fmt.Sprintf("%s/%s/capture", models.PayPalEndpoints[s.config.Environment]["orders"], orderID)

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("PayPal-Request-Id", uuid.New().String())

	// Send request
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusCreated {
		s.logger.WithFields(map[string]interface{}{
			"status_code": resp.StatusCode,
			"response":    string(respBody),
		}).Error("PayPal order capture failed")
		return nil, fmt.Errorf("PayPal order capture failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var order models.PayPalOrder
	if err := json.Unmarshal(respBody, &order); err != nil {
		return nil, fmt.Errorf("failed to unmarshal order response: %w", err)
	}

	return &order, nil
}

// Placeholder implementations for remaining interface methods
// TODO: Implement all methods according to interface requirements

func (s *service) GetOrder(ctx context.Context, orderID string) (*models.PayPalOrder, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetPaymentStatus(ctx context.Context, paymentID, userID string) (*models.PayPalPaymentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) CancelPayment(ctx context.Context, req *models.CancelPayPalPaymentRequest) error {
	return fmt.Errorf("not implemented")
}

func (s *service) ListPayments(ctx context.Context, req *models.ListPayPalPaymentsRequest) (*models.ListPayPalPaymentsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) RefundPayment(ctx context.Context, paymentID string, amount int64, reason string) error {
	return fmt.Errorf("not implemented")
}

func (s *service) GetPaymentDetails(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ValidateWebhookSignature(ctx context.Context, headers map[string]string, body []byte) (bool, error) {
	return false, fmt.Errorf("not implemented")
}

func (s *service) HandleWebhook(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ProcessWebhookEvent(ctx context.Context, eventType string, resource map[string]interface{}) error {
	return fmt.Errorf("not implemented")
}

func (s *service) GetPaymentStatistics(ctx context.Context, userID string, period string) (*models.PayPalPaymentSummary, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminListPayments(ctx context.Context, req *models.ListPayPalPaymentsRequest) (*models.ListPayPalPaymentsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminRefundPayment(ctx context.Context, paymentID string, amount int64, reason string, adminUserID string) error {
	return fmt.Errorf("not implemented")
}

func (s *service) AdminGetPaymentDetails(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ProcessSubscriptionPayment(ctx context.Context, paymentID string) (*models.ExecutePayPalPaymentResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ProcessCreditTopup(ctx context.Context, paymentID string) (*models.ExecutePayPalPaymentResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) InternalGetPayment(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) InternalUpdatePaymentStatus(ctx context.Context, paymentID string, status string, metadata map[string]interface{}) error {
	return fmt.Errorf("not implemented")
}

func (s *service) InternalCreatePaymentRecord(ctx context.Context, order *models.PayPalOrder, userID, purpose string) (*models.PayPalPaymentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationTemplateQuery is the builder for querying NotificationTemplate entities.
type NotificationTemplateQuery struct {
	config
	ctx        *QueryContext
	order      []notificationtemplate.OrderOption
	inters     []Interceptor
	predicates []predicate.NotificationTemplate
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotificationTemplateQuery builder.
func (ntq *NotificationTemplateQuery) Where(ps ...predicate.NotificationTemplate) *NotificationTemplateQuery {
	ntq.predicates = append(ntq.predicates, ps...)
	return ntq
}

// Limit the number of records to be returned by this query.
func (ntq *NotificationTemplateQuery) Limit(limit int) *NotificationTemplateQuery {
	ntq.ctx.Limit = &limit
	return ntq
}

// Offset to start from.
func (ntq *NotificationTemplateQuery) Offset(offset int) *NotificationTemplateQuery {
	ntq.ctx.Offset = &offset
	return ntq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ntq *NotificationTemplateQuery) Unique(unique bool) *NotificationTemplateQuery {
	ntq.ctx.Unique = &unique
	return ntq
}

// Order specifies how the records should be ordered.
func (ntq *NotificationTemplateQuery) Order(o ...notificationtemplate.OrderOption) *NotificationTemplateQuery {
	ntq.order = append(ntq.order, o...)
	return ntq
}

// First returns the first NotificationTemplate entity from the query.
// Returns a *NotFoundError when no NotificationTemplate was found.
func (ntq *NotificationTemplateQuery) First(ctx context.Context) (*NotificationTemplate, error) {
	nodes, err := ntq.Limit(1).All(setContextOp(ctx, ntq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notificationtemplate.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) FirstX(ctx context.Context) *NotificationTemplate {
	node, err := ntq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NotificationTemplate ID from the query.
// Returns a *NotFoundError when no NotificationTemplate ID was found.
func (ntq *NotificationTemplateQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ntq.Limit(1).IDs(setContextOp(ctx, ntq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notificationtemplate.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ntq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NotificationTemplate entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NotificationTemplate entity is found.
// Returns a *NotFoundError when no NotificationTemplate entities are found.
func (ntq *NotificationTemplateQuery) Only(ctx context.Context) (*NotificationTemplate, error) {
	nodes, err := ntq.Limit(2).All(setContextOp(ctx, ntq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notificationtemplate.Label}
	default:
		return nil, &NotSingularError{notificationtemplate.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) OnlyX(ctx context.Context) *NotificationTemplate {
	node, err := ntq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NotificationTemplate ID in the query.
// Returns a *NotSingularError when more than one NotificationTemplate ID is found.
// Returns a *NotFoundError when no entities are found.
func (ntq *NotificationTemplateQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ntq.Limit(2).IDs(setContextOp(ctx, ntq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notificationtemplate.Label}
	default:
		err = &NotSingularError{notificationtemplate.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ntq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NotificationTemplates.
func (ntq *NotificationTemplateQuery) All(ctx context.Context) ([]*NotificationTemplate, error) {
	ctx = setContextOp(ctx, ntq.ctx, ent.OpQueryAll)
	if err := ntq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NotificationTemplate, *NotificationTemplateQuery]()
	return withInterceptors[[]*NotificationTemplate](ctx, ntq, qr, ntq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) AllX(ctx context.Context) []*NotificationTemplate {
	nodes, err := ntq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NotificationTemplate IDs.
func (ntq *NotificationTemplateQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ntq.ctx.Unique == nil && ntq.path != nil {
		ntq.Unique(true)
	}
	ctx = setContextOp(ctx, ntq.ctx, ent.OpQueryIDs)
	if err = ntq.Select(notificationtemplate.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ntq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ntq *NotificationTemplateQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ntq.ctx, ent.OpQueryCount)
	if err := ntq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ntq, querierCount[*NotificationTemplateQuery](), ntq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) CountX(ctx context.Context) int {
	count, err := ntq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ntq *NotificationTemplateQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ntq.ctx, ent.OpQueryExist)
	switch _, err := ntq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ntq *NotificationTemplateQuery) ExistX(ctx context.Context) bool {
	exist, err := ntq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotificationTemplateQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ntq *NotificationTemplateQuery) Clone() *NotificationTemplateQuery {
	if ntq == nil {
		return nil
	}
	return &NotificationTemplateQuery{
		config:     ntq.config,
		ctx:        ntq.ctx.Clone(),
		order:      append([]notificationtemplate.OrderOption{}, ntq.order...),
		inters:     append([]Interceptor{}, ntq.inters...),
		predicates: append([]predicate.NotificationTemplate{}, ntq.predicates...),
		// clone intermediate query.
		sql:  ntq.sql.Clone(),
		path: ntq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		EventType string `json:"event_type,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NotificationTemplate.Query().
//		GroupBy(notificationtemplate.FieldEventType).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ntq *NotificationTemplateQuery) GroupBy(field string, fields ...string) *NotificationTemplateGroupBy {
	ntq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotificationTemplateGroupBy{build: ntq}
	grbuild.flds = &ntq.ctx.Fields
	grbuild.label = notificationtemplate.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		EventType string `json:"event_type,omitempty"`
//	}
//
//	client.NotificationTemplate.Query().
//		Select(notificationtemplate.FieldEventType).
//		Scan(ctx, &v)
func (ntq *NotificationTemplateQuery) Select(fields ...string) *NotificationTemplateSelect {
	ntq.ctx.Fields = append(ntq.ctx.Fields, fields...)
	sbuild := &NotificationTemplateSelect{NotificationTemplateQuery: ntq}
	sbuild.label = notificationtemplate.Label
	sbuild.flds, sbuild.scan = &ntq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotificationTemplateSelect configured with the given aggregations.
func (ntq *NotificationTemplateQuery) Aggregate(fns ...AggregateFunc) *NotificationTemplateSelect {
	return ntq.Select().Aggregate(fns...)
}

func (ntq *NotificationTemplateQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ntq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ntq); err != nil {
				return err
			}
		}
	}
	for _, f := range ntq.ctx.Fields {
		if !notificationtemplate.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ntq.path != nil {
		prev, err := ntq.path(ctx)
		if err != nil {
			return err
		}
		ntq.sql = prev
	}
	return nil
}

func (ntq *NotificationTemplateQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NotificationTemplate, error) {
	var (
		nodes = []*NotificationTemplate{}
		_spec = ntq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NotificationTemplate).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NotificationTemplate{config: ntq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ntq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ntq *NotificationTemplateQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ntq.querySpec()
	_spec.Node.Columns = ntq.ctx.Fields
	if len(ntq.ctx.Fields) > 0 {
		_spec.Unique = ntq.ctx.Unique != nil && *ntq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ntq.driver, _spec)
}

func (ntq *NotificationTemplateQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notificationtemplate.Table, notificationtemplate.Columns, sqlgraph.NewFieldSpec(notificationtemplate.FieldID, field.TypeUUID))
	_spec.From = ntq.sql
	if unique := ntq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ntq.path != nil {
		_spec.Unique = true
	}
	if fields := ntq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notificationtemplate.FieldID)
		for i := range fields {
			if fields[i] != notificationtemplate.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ntq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ntq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ntq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ntq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ntq *NotificationTemplateQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ntq.driver.Dialect())
	t1 := builder.Table(notificationtemplate.Table)
	columns := ntq.ctx.Fields
	if len(columns) == 0 {
		columns = notificationtemplate.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ntq.sql != nil {
		selector = ntq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ntq.ctx.Unique != nil && *ntq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ntq.predicates {
		p(selector)
	}
	for _, p := range ntq.order {
		p(selector)
	}
	if offset := ntq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ntq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// NotificationTemplateGroupBy is the group-by builder for NotificationTemplate entities.
type NotificationTemplateGroupBy struct {
	selector
	build *NotificationTemplateQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ntgb *NotificationTemplateGroupBy) Aggregate(fns ...AggregateFunc) *NotificationTemplateGroupBy {
	ntgb.fns = append(ntgb.fns, fns...)
	return ntgb
}

// Scan applies the selector query and scans the result into the given value.
func (ntgb *NotificationTemplateGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ntgb.build.ctx, ent.OpQueryGroupBy)
	if err := ntgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationTemplateQuery, *NotificationTemplateGroupBy](ctx, ntgb.build, ntgb, ntgb.build.inters, v)
}

func (ntgb *NotificationTemplateGroupBy) sqlScan(ctx context.Context, root *NotificationTemplateQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ntgb.fns))
	for _, fn := range ntgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ntgb.flds)+len(ntgb.fns))
		for _, f := range *ntgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ntgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ntgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotificationTemplateSelect is the builder for selecting fields of NotificationTemplate entities.
type NotificationTemplateSelect struct {
	*NotificationTemplateQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nts *NotificationTemplateSelect) Aggregate(fns ...AggregateFunc) *NotificationTemplateSelect {
	nts.fns = append(nts.fns, fns...)
	return nts
}

// Scan applies the selector query and scans the result into the given value.
func (nts *NotificationTemplateSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nts.ctx, ent.OpQuerySelect)
	if err := nts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotificationTemplateQuery, *NotificationTemplateSelect](ctx, nts.NotificationTemplateQuery, nts, nts.inters, v)
}

func (nts *NotificationTemplateSelect) sqlScan(ctx context.Context, root *NotificationTemplateQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nts.fns))
	for _, fn := range nts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

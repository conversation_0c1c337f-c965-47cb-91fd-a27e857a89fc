package verification

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TokenService handles verification token generation and validation
type TokenService struct {
	secretKey []byte
}

// NewTokenService creates a new token service
func NewTokenService(secretKey string) *TokenService {
	return &TokenService{
		secretKey: []byte(secretKey),
	}
}

// VerificationClaims represents the claims in a verification token
type VerificationClaims struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	TokenType string `json:"token_type"` // "email_verification", "password_reset"
	jwt.RegisteredClaims
}

// GenerateEmailVerificationToken generates a token for email verification
func (s *TokenService) GenerateEmailVerificationToken(userID, email string) (string, error) {
	claims := &VerificationClaims{
		UserID:    userID,
		Email:     email,
		TokenType: "email_verification",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // 24 hours
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-service",
			Subject:   userID,
			ID:        generateRandomID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secretKey)
}

// GeneratePasswordResetToken generates a token for password reset
func (s *TokenService) GeneratePasswordResetToken(userID, email string) (string, error) {
	claims := &VerificationClaims{
		UserID:    userID,
		Email:     email,
		TokenType: "password_reset",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Hour)), // 1 hour
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-service",
			Subject:   userID,
			ID:        generateRandomID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secretKey)
}

// ValidateToken validates a verification token and returns the claims
func (s *TokenService) ValidateToken(tokenString string) (*VerificationClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &VerificationClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(*VerificationClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Additional validation
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("token has expired")
	}

	if claims.NotBefore != nil && claims.NotBefore.After(time.Now()) {
		return nil, fmt.Errorf("token not yet valid")
	}

	return claims, nil
}

// ValidateEmailVerificationToken validates an email verification token
func (s *TokenService) ValidateEmailVerificationToken(tokenString string) (*VerificationClaims, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != "email_verification" {
		return nil, fmt.Errorf("invalid token type: expected email_verification, got %s", claims.TokenType)
	}

	return claims, nil
}

// ValidatePasswordResetToken validates a password reset token
func (s *TokenService) ValidatePasswordResetToken(tokenString string) (*VerificationClaims, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != "password_reset" {
		return nil, fmt.Errorf("invalid token type: expected password_reset, got %s", claims.TokenType)
	}

	return claims, nil
}

// generateRandomID generates a random ID for the token
func generateRandomID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID if random generation fails
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return base64.URLEncoding.EncodeToString(bytes)
}

// TokenInfo represents information about a token
type TokenInfo struct {
	UserID    string    `json:"user_id"`
	Email     string    `json:"email"`
	TokenType string    `json:"token_type"`
	IssuedAt  time.Time `json:"issued_at"`
	ExpiresAt time.Time `json:"expires_at"`
	TokenID   string    `json:"token_id"`
}

// GetTokenInfo extracts information from a token without validating expiry
func (s *TokenService) GetTokenInfo(tokenString string) (*TokenInfo, error) {
	token, err := jwt.ParseWithClaims(tokenString, &VerificationClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	}, jwt.WithoutClaimsValidation()) // Skip expiry validation

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*VerificationClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	info := &TokenInfo{
		UserID:    claims.UserID,
		Email:     claims.Email,
		TokenType: claims.TokenType,
		TokenID:   claims.ID,
	}

	if claims.IssuedAt != nil {
		info.IssuedAt = claims.IssuedAt.Time
	}

	if claims.ExpiresAt != nil {
		info.ExpiresAt = claims.ExpiresAt.Time
	}

	return info, nil
}

// IsTokenExpired checks if a token is expired without full validation
func (s *TokenService) IsTokenExpired(tokenString string) (bool, error) {
	info, err := s.GetTokenInfo(tokenString)
	if err != nil {
		return true, err
	}

	return time.Now().After(info.ExpiresAt), nil
}

// RevokeToken adds a token to a revocation list (this would typically be stored in Redis/DB)
// For now, this is a placeholder - in production, you'd want to store revoked tokens
func (s *TokenService) RevokeToken(tokenString string) error {
	// TODO: Implement token revocation storage
	// This could be done by storing the token ID in Redis with expiry
	return nil
}

// IsTokenRevoked checks if a token has been revoked
// For now, this is a placeholder - in production, you'd check against stored revoked tokens
func (s *TokenService) IsTokenRevoked(tokenString string) (bool, error) {
	// TODO: Implement token revocation checking
	// This could be done by checking if the token ID exists in Redis
	return false, nil
}

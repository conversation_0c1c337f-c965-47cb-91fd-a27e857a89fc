// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// UserCreditCreate is the builder for creating a UserCredit entity.
type UserCreditCreate struct {
	config
	mutation *UserCreditMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (ucc *UserCreditCreate) SetUserID(u uuid.UUID) *UserCreditCreate {
	ucc.mutation.SetUserID(u)
	return ucc
}

// SetCurrentCredits sets the "current_credits" field.
func (ucc *UserCreditCreate) SetCurrentCredits(i int) *UserCreditCreate {
	ucc.mutation.SetCurrentCredits(i)
	return ucc
}

// SetNillableCurrentCredits sets the "current_credits" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableCurrentCredits(i *int) *UserCreditCreate {
	if i != nil {
		ucc.SetCurrentCredits(*i)
	}
	return ucc
}

// SetTotalCredits sets the "total_credits" field.
func (ucc *UserCreditCreate) SetTotalCredits(i int) *UserCreditCreate {
	ucc.mutation.SetTotalCredits(i)
	return ucc
}

// SetNillableTotalCredits sets the "total_credits" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableTotalCredits(i *int) *UserCreditCreate {
	if i != nil {
		ucc.SetTotalCredits(*i)
	}
	return ucc
}

// SetPlanID sets the "plan_id" field.
func (ucc *UserCreditCreate) SetPlanID(s string) *UserCreditCreate {
	ucc.mutation.SetPlanID(s)
	return ucc
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillablePlanID(s *string) *UserCreditCreate {
	if s != nil {
		ucc.SetPlanID(*s)
	}
	return ucc
}

// SetPlanName sets the "plan_name" field.
func (ucc *UserCreditCreate) SetPlanName(s string) *UserCreditCreate {
	ucc.mutation.SetPlanName(s)
	return ucc
}

// SetNillablePlanName sets the "plan_name" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillablePlanName(s *string) *UserCreditCreate {
	if s != nil {
		ucc.SetPlanName(*s)
	}
	return ucc
}

// SetPlanExpiresAt sets the "plan_expires_at" field.
func (ucc *UserCreditCreate) SetPlanExpiresAt(t time.Time) *UserCreditCreate {
	ucc.mutation.SetPlanExpiresAt(t)
	return ucc
}

// SetNillablePlanExpiresAt sets the "plan_expires_at" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillablePlanExpiresAt(t *time.Time) *UserCreditCreate {
	if t != nil {
		ucc.SetPlanExpiresAt(*t)
	}
	return ucc
}

// SetStatus sets the "status" field.
func (ucc *UserCreditCreate) SetStatus(u usercredit.Status) *UserCreditCreate {
	ucc.mutation.SetStatus(u)
	return ucc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableStatus(u *usercredit.Status) *UserCreditCreate {
	if u != nil {
		ucc.SetStatus(*u)
	}
	return ucc
}

// SetMonthlyLimit sets the "monthly_limit" field.
func (ucc *UserCreditCreate) SetMonthlyLimit(i int) *UserCreditCreate {
	ucc.mutation.SetMonthlyLimit(i)
	return ucc
}

// SetNillableMonthlyLimit sets the "monthly_limit" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableMonthlyLimit(i *int) *UserCreditCreate {
	if i != nil {
		ucc.SetMonthlyLimit(*i)
	}
	return ucc
}

// SetMonthlyUsed sets the "monthly_used" field.
func (ucc *UserCreditCreate) SetMonthlyUsed(i int) *UserCreditCreate {
	ucc.mutation.SetMonthlyUsed(i)
	return ucc
}

// SetNillableMonthlyUsed sets the "monthly_used" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableMonthlyUsed(i *int) *UserCreditCreate {
	if i != nil {
		ucc.SetMonthlyUsed(*i)
	}
	return ucc
}

// SetMonthlyResetAt sets the "monthly_reset_at" field.
func (ucc *UserCreditCreate) SetMonthlyResetAt(t time.Time) *UserCreditCreate {
	ucc.mutation.SetMonthlyResetAt(t)
	return ucc
}

// SetNillableMonthlyResetAt sets the "monthly_reset_at" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableMonthlyResetAt(t *time.Time) *UserCreditCreate {
	if t != nil {
		ucc.SetMonthlyResetAt(*t)
	}
	return ucc
}

// SetMetadata sets the "metadata" field.
func (ucc *UserCreditCreate) SetMetadata(m map[string]interface{}) *UserCreditCreate {
	ucc.mutation.SetMetadata(m)
	return ucc
}

// SetCreatedAt sets the "created_at" field.
func (ucc *UserCreditCreate) SetCreatedAt(t time.Time) *UserCreditCreate {
	ucc.mutation.SetCreatedAt(t)
	return ucc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableCreatedAt(t *time.Time) *UserCreditCreate {
	if t != nil {
		ucc.SetCreatedAt(*t)
	}
	return ucc
}

// SetUpdatedAt sets the "updated_at" field.
func (ucc *UserCreditCreate) SetUpdatedAt(t time.Time) *UserCreditCreate {
	ucc.mutation.SetUpdatedAt(t)
	return ucc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableUpdatedAt(t *time.Time) *UserCreditCreate {
	if t != nil {
		ucc.SetUpdatedAt(*t)
	}
	return ucc
}

// SetID sets the "id" field.
func (ucc *UserCreditCreate) SetID(u uuid.UUID) *UserCreditCreate {
	ucc.mutation.SetID(u)
	return ucc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ucc *UserCreditCreate) SetNillableID(u *uuid.UUID) *UserCreditCreate {
	if u != nil {
		ucc.SetID(*u)
	}
	return ucc
}

// Mutation returns the UserCreditMutation object of the builder.
func (ucc *UserCreditCreate) Mutation() *UserCreditMutation {
	return ucc.mutation
}

// Save creates the UserCredit in the database.
func (ucc *UserCreditCreate) Save(ctx context.Context) (*UserCredit, error) {
	ucc.defaults()
	return withHooks(ctx, ucc.sqlSave, ucc.mutation, ucc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ucc *UserCreditCreate) SaveX(ctx context.Context) *UserCredit {
	v, err := ucc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucc *UserCreditCreate) Exec(ctx context.Context) error {
	_, err := ucc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucc *UserCreditCreate) ExecX(ctx context.Context) {
	if err := ucc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ucc *UserCreditCreate) defaults() {
	if _, ok := ucc.mutation.CurrentCredits(); !ok {
		v := usercredit.DefaultCurrentCredits
		ucc.mutation.SetCurrentCredits(v)
	}
	if _, ok := ucc.mutation.TotalCredits(); !ok {
		v := usercredit.DefaultTotalCredits
		ucc.mutation.SetTotalCredits(v)
	}
	if _, ok := ucc.mutation.Status(); !ok {
		v := usercredit.DefaultStatus
		ucc.mutation.SetStatus(v)
	}
	if _, ok := ucc.mutation.MonthlyLimit(); !ok {
		v := usercredit.DefaultMonthlyLimit
		ucc.mutation.SetMonthlyLimit(v)
	}
	if _, ok := ucc.mutation.MonthlyUsed(); !ok {
		v := usercredit.DefaultMonthlyUsed
		ucc.mutation.SetMonthlyUsed(v)
	}
	if _, ok := ucc.mutation.CreatedAt(); !ok {
		v := usercredit.DefaultCreatedAt()
		ucc.mutation.SetCreatedAt(v)
	}
	if _, ok := ucc.mutation.UpdatedAt(); !ok {
		v := usercredit.DefaultUpdatedAt()
		ucc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ucc.mutation.ID(); !ok {
		v := usercredit.DefaultID()
		ucc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ucc *UserCreditCreate) check() error {
	if _, ok := ucc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "UserCredit.user_id"`)}
	}
	if _, ok := ucc.mutation.CurrentCredits(); !ok {
		return &ValidationError{Name: "current_credits", err: errors.New(`ent: missing required field "UserCredit.current_credits"`)}
	}
	if v, ok := ucc.mutation.CurrentCredits(); ok {
		if err := usercredit.CurrentCreditsValidator(v); err != nil {
			return &ValidationError{Name: "current_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.current_credits": %w`, err)}
		}
	}
	if _, ok := ucc.mutation.TotalCredits(); !ok {
		return &ValidationError{Name: "total_credits", err: errors.New(`ent: missing required field "UserCredit.total_credits"`)}
	}
	if v, ok := ucc.mutation.TotalCredits(); ok {
		if err := usercredit.TotalCreditsValidator(v); err != nil {
			return &ValidationError{Name: "total_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.total_credits": %w`, err)}
		}
	}
	if v, ok := ucc.mutation.PlanID(); ok {
		if err := usercredit.PlanIDValidator(v); err != nil {
			return &ValidationError{Name: "plan_id", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_id": %w`, err)}
		}
	}
	if v, ok := ucc.mutation.PlanName(); ok {
		if err := usercredit.PlanNameValidator(v); err != nil {
			return &ValidationError{Name: "plan_name", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_name": %w`, err)}
		}
	}
	if _, ok := ucc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "UserCredit.status"`)}
	}
	if v, ok := ucc.mutation.Status(); ok {
		if err := usercredit.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "UserCredit.status": %w`, err)}
		}
	}
	if _, ok := ucc.mutation.MonthlyLimit(); !ok {
		return &ValidationError{Name: "monthly_limit", err: errors.New(`ent: missing required field "UserCredit.monthly_limit"`)}
	}
	if v, ok := ucc.mutation.MonthlyLimit(); ok {
		if err := usercredit.MonthlyLimitValidator(v); err != nil {
			return &ValidationError{Name: "monthly_limit", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_limit": %w`, err)}
		}
	}
	if _, ok := ucc.mutation.MonthlyUsed(); !ok {
		return &ValidationError{Name: "monthly_used", err: errors.New(`ent: missing required field "UserCredit.monthly_used"`)}
	}
	if v, ok := ucc.mutation.MonthlyUsed(); ok {
		if err := usercredit.MonthlyUsedValidator(v); err != nil {
			return &ValidationError{Name: "monthly_used", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_used": %w`, err)}
		}
	}
	if _, ok := ucc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "UserCredit.created_at"`)}
	}
	if _, ok := ucc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "UserCredit.updated_at"`)}
	}
	return nil
}

func (ucc *UserCreditCreate) sqlSave(ctx context.Context) (*UserCredit, error) {
	if err := ucc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ucc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ucc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ucc.mutation.id = &_node.ID
	ucc.mutation.done = true
	return _node, nil
}

func (ucc *UserCreditCreate) createSpec() (*UserCredit, *sqlgraph.CreateSpec) {
	var (
		_node = &UserCredit{config: ucc.config}
		_spec = sqlgraph.NewCreateSpec(usercredit.Table, sqlgraph.NewFieldSpec(usercredit.FieldID, field.TypeUUID))
	)
	if id, ok := ucc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ucc.mutation.UserID(); ok {
		_spec.SetField(usercredit.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := ucc.mutation.CurrentCredits(); ok {
		_spec.SetField(usercredit.FieldCurrentCredits, field.TypeInt, value)
		_node.CurrentCredits = value
	}
	if value, ok := ucc.mutation.TotalCredits(); ok {
		_spec.SetField(usercredit.FieldTotalCredits, field.TypeInt, value)
		_node.TotalCredits = value
	}
	if value, ok := ucc.mutation.PlanID(); ok {
		_spec.SetField(usercredit.FieldPlanID, field.TypeString, value)
		_node.PlanID = value
	}
	if value, ok := ucc.mutation.PlanName(); ok {
		_spec.SetField(usercredit.FieldPlanName, field.TypeString, value)
		_node.PlanName = value
	}
	if value, ok := ucc.mutation.PlanExpiresAt(); ok {
		_spec.SetField(usercredit.FieldPlanExpiresAt, field.TypeTime, value)
		_node.PlanExpiresAt = value
	}
	if value, ok := ucc.mutation.Status(); ok {
		_spec.SetField(usercredit.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := ucc.mutation.MonthlyLimit(); ok {
		_spec.SetField(usercredit.FieldMonthlyLimit, field.TypeInt, value)
		_node.MonthlyLimit = value
	}
	if value, ok := ucc.mutation.MonthlyUsed(); ok {
		_spec.SetField(usercredit.FieldMonthlyUsed, field.TypeInt, value)
		_node.MonthlyUsed = value
	}
	if value, ok := ucc.mutation.MonthlyResetAt(); ok {
		_spec.SetField(usercredit.FieldMonthlyResetAt, field.TypeTime, value)
		_node.MonthlyResetAt = value
	}
	if value, ok := ucc.mutation.Metadata(); ok {
		_spec.SetField(usercredit.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ucc.mutation.CreatedAt(); ok {
		_spec.SetField(usercredit.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ucc.mutation.UpdatedAt(); ok {
		_spec.SetField(usercredit.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// UserCreditCreateBulk is the builder for creating many UserCredit entities in bulk.
type UserCreditCreateBulk struct {
	config
	err      error
	builders []*UserCreditCreate
}

// Save creates the UserCredit entities in the database.
func (uccb *UserCreditCreateBulk) Save(ctx context.Context) ([]*UserCredit, error) {
	if uccb.err != nil {
		return nil, uccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(uccb.builders))
	nodes := make([]*UserCredit, len(uccb.builders))
	mutators := make([]Mutator, len(uccb.builders))
	for i := range uccb.builders {
		func(i int, root context.Context) {
			builder := uccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserCreditMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, uccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, uccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, uccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (uccb *UserCreditCreateBulk) SaveX(ctx context.Context) []*UserCredit {
	v, err := uccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uccb *UserCreditCreateBulk) Exec(ctx context.Context) error {
	_, err := uccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uccb *UserCreditCreateBulk) ExecX(ctx context.Context) {
	if err := uccb.Exec(ctx); err != nil {
		panic(err)
	}
}

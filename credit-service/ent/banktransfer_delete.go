// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/banktransfer"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// BankTransferDelete is the builder for deleting a BankTransfer entity.
type BankTransferDelete struct {
	config
	hooks    []Hook
	mutation *BankTransferMutation
}

// Where appends a list predicates to the BankTransferDelete builder.
func (btd *BankTransferDelete) Where(ps ...predicate.BankTransfer) *BankTransferDelete {
	btd.mutation.Where(ps...)
	return btd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (btd *BankTransferDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, btd.sqlExec, btd.mutation, btd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (btd *BankTransferDelete) ExecX(ctx context.Context) int {
	n, err := btd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (btd *BankTransferDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(banktransfer.Table, sqlgraph.NewFieldSpec(banktransfer.FieldID, field.TypeUUID))
	if ps := btd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, btd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	btd.mutation.done = true
	return affected, err
}

// BankTransferDeleteOne is the builder for deleting a single BankTransfer entity.
type BankTransferDeleteOne struct {
	btd *BankTransferDelete
}

// Where appends a list predicates to the BankTransferDelete builder.
func (btdo *BankTransferDeleteOne) Where(ps ...predicate.BankTransfer) *BankTransferDeleteOne {
	btdo.btd.mutation.Where(ps...)
	return btdo
}

// Exec executes the deletion query.
func (btdo *BankTransferDeleteOne) Exec(ctx context.Context) error {
	n, err := btdo.btd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{banktransfer.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (btdo *BankTransferDeleteOne) ExecX(ctx context.Context) {
	if err := btdo.Exec(ctx); err != nil {
		panic(err)
	}
}

package validation

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/google/uuid"

	"github.com/social-content-ai/integration-service/internal/errors"
)

// Validator handles input validation
type Validator struct {
	errors []string
}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{
		errors: make([]string, 0),
	}
}

// AddError adds a validation error
func (v *Validator) AddError(field, message string) {
	v.errors = append(v.errors, fmt.Sprintf("%s: %s", field, message))
}

// HasErrors returns true if there are validation errors
func (v *Validator) HasErrors() bool {
	return len(v.errors) > 0
}

// GetErrors returns all validation errors
func (v *Validator) GetErrors() []string {
	return v.errors
}

// GetError returns a combined validation error
func (v *Validator) GetError() error {
	if !v.HasErrors() {
		return nil
	}
	return errors.NewValidationError("multiple_fields", strings.Join(v.errors, "; "))
}

// Required validates that a field is not empty
func (v *Validator) Required(field, value string) *Validator {
	if strings.TrimSpace(value) == "" {
		v.AddError(field, "is required")
	}
	return v
}

// MinLength validates minimum string length
func (v *Validator) MinLength(field, value string, min int) *Validator {
	if utf8.RuneCountInString(value) < min {
		v.AddError(field, fmt.Sprintf("must be at least %d characters", min))
	}
	return v
}

// MaxLength validates maximum string length
func (v *Validator) MaxLength(field, value string, max int) *Validator {
	if utf8.RuneCountInString(value) > max {
		v.AddError(field, fmt.Sprintf("must be at most %d characters", max))
	}
	return v
}

// UUID validates that a string is a valid UUID
func (v *Validator) UUID(field, value string) *Validator {
	if value != "" {
		if _, err := uuid.Parse(value); err != nil {
			v.AddError(field, "must be a valid UUID")
		}
	}
	return v
}

// URL validates that a string is a valid URL
func (v *Validator) URL(field, value string) *Validator {
	if value != "" {
		if _, err := url.Parse(value); err != nil {
			v.AddError(field, "must be a valid URL")
		}
	}
	return v
}

// Email validates that a string is a valid email
func (v *Validator) Email(field, value string) *Validator {
	if value != "" {
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(value) {
			v.AddError(field, "must be a valid email address")
		}
	}
	return v
}

// Platform validates that a platform is supported
func (v *Validator) Platform(field, value string) *Validator {
	supportedPlatforms := map[string]bool{
		"facebook":  true,
		"twitter":   true,
		"instagram": true,
		"linkedin":  true,
		"youtube":   true,
		"tiktok":    true,
	}

	if value != "" && !supportedPlatforms[strings.ToLower(value)] {
		v.AddError(field, "must be a supported platform (facebook, twitter, instagram, linkedin, youtube, tiktok)")
	}
	return v
}

// OneOf validates that a value is one of the allowed values
func (v *Validator) OneOf(field, value string, allowed []string) *Validator {
	if value != "" {
		for _, allowedValue := range allowed {
			if value == allowedValue {
				return v
			}
		}
		v.AddError(field, fmt.Sprintf("must be one of: %s", strings.Join(allowed, ", ")))
	}
	return v
}

// Regex validates that a string matches a regex pattern
func (v *Validator) Regex(field, value, pattern, message string) *Validator {
	if value != "" {
		regex := regexp.MustCompile(pattern)
		if !regex.MatchString(value) {
			v.AddError(field, message)
		}
	}
	return v
}

// ContentValidation validates content for publishing
type ContentValidation struct {
	Platform    string
	Content     string
	ImageURLs   []string
	VideoURL    string
	Hashtags    []string
}

// ValidateContent validates content for publishing to a specific platform
func ValidateContent(cv ContentValidation) error {
	validator := NewValidator()

	// Validate platform
	validator.Required("platform", cv.Platform).Platform("platform", cv.Platform)

	// Validate content based on platform
	switch strings.ToLower(cv.Platform) {
	case "twitter":
		validator.Required("content", cv.Content).MaxLength("content", cv.Content, 280)
		if len(cv.ImageURLs) > 4 {
			validator.AddError("image_urls", "Twitter allows maximum 4 images")
		}

	case "facebook":
		validator.MaxLength("content", cv.Content, 63206)
		if len(cv.ImageURLs) > 10 {
			validator.AddError("image_urls", "Facebook allows maximum 10 images")
		}

	case "instagram":
		validator.MaxLength("content", cv.Content, 2200)
		if len(cv.ImageURLs) == 0 && cv.VideoURL == "" {
			validator.AddError("media", "Instagram requires at least one image or video")
		}
		if len(cv.ImageURLs) > 10 {
			validator.AddError("image_urls", "Instagram allows maximum 10 images")
		}

	case "linkedin":
		validator.MaxLength("content", cv.Content, 3000)
		if len(cv.ImageURLs) > 9 {
			validator.AddError("image_urls", "LinkedIn allows maximum 9 images")
		}

	case "youtube":
		validator.Required("content", cv.Content).MaxLength("content", cv.Content, 5000)
		if cv.VideoURL == "" {
			validator.AddError("video_url", "YouTube requires a video")
		}

	case "tiktok":
		validator.MaxLength("content", cv.Content, 2200)
		if cv.VideoURL == "" {
			validator.AddError("video_url", "TikTok requires a video")
		}
	}

	// Validate URLs
	for i, imageURL := range cv.ImageURLs {
		validator.URL(fmt.Sprintf("image_urls[%d]", i), imageURL)
	}
	if cv.VideoURL != "" {
		validator.URL("video_url", cv.VideoURL)
	}

	// Validate hashtags
	hashtagRegex := regexp.MustCompile(`^#?[a-zA-Z0-9_]+$`)
	for i, hashtag := range cv.Hashtags {
		if !hashtagRegex.MatchString(hashtag) {
			validator.AddError(fmt.Sprintf("hashtags[%d]", i), "must contain only letters, numbers, and underscores")
		}
	}

	return validator.GetError()
}

// OAuthValidation validates OAuth parameters
type OAuthValidation struct {
	Platform     string
	RedirectURI  string
	Scopes       []string
	State        string
	Code         string
}

// ValidateOAuth validates OAuth parameters
func ValidateOAuth(ov OAuthValidation) error {
	validator := NewValidator()

	// Validate platform
	validator.Required("platform", ov.Platform).Platform("platform", ov.Platform)

	// Validate redirect URI
	validator.Required("redirect_uri", ov.RedirectURI).URL("redirect_uri", ov.RedirectURI)

	// Validate state (should be present and not empty for security)
	validator.Required("state", ov.State).MinLength("state", ov.State, 8)

	// Validate authorization code if present
	if ov.Code != "" {
		validator.MinLength("code", ov.Code, 10)
	}

	// Validate scopes based on platform
	if len(ov.Scopes) > 0 {
		switch strings.ToLower(ov.Platform) {
		case "facebook":
			validScopes := map[string]bool{
				"pages_manage_posts":    true,
				"pages_read_engagement": true,
				"pages_show_list":       true,
				"instagram_basic":       true,
				"instagram_content_publish": true,
			}
			for _, scope := range ov.Scopes {
				if !validScopes[scope] {
					validator.AddError("scopes", fmt.Sprintf("invalid Facebook scope: %s", scope))
				}
			}

		case "twitter":
			validScopes := map[string]bool{
				"tweet.read":    true,
				"tweet.write":   true,
				"users.read":    true,
				"offline.access": true,
			}
			for _, scope := range ov.Scopes {
				if !validScopes[scope] {
					validator.AddError("scopes", fmt.Sprintf("invalid Twitter scope: %s", scope))
				}
			}

		case "linkedin":
			validScopes := map[string]bool{
				"r_liteprofile":   true,
				"r_emailaddress":  true,
				"w_member_social": true,
			}
			for _, scope := range ov.Scopes {
				if !validScopes[scope] {
					validator.AddError("scopes", fmt.Sprintf("invalid LinkedIn scope: %s", scope))
				}
			}
		}
	}

	return validator.GetError()
}

// SanitizeContent sanitizes content for safe storage and display
func SanitizeContent(content string) string {
	// Remove null bytes
	content = strings.ReplaceAll(content, "\x00", "")
	
	// Normalize line endings
	content = strings.ReplaceAll(content, "\r\n", "\n")
	content = strings.ReplaceAll(content, "\r", "\n")
	
	// Trim whitespace
	content = strings.TrimSpace(content)
	
	return content
}

// SanitizeHashtags sanitizes and normalizes hashtags
func SanitizeHashtags(hashtags []string) []string {
	sanitized := make([]string, 0, len(hashtags))
	seen := make(map[string]bool)
	
	for _, hashtag := range hashtags {
		// Remove whitespace and convert to lowercase
		hashtag = strings.ToLower(strings.TrimSpace(hashtag))
		
		// Add # prefix if missing
		if !strings.HasPrefix(hashtag, "#") {
			hashtag = "#" + hashtag
		}
		
		// Remove duplicates
		if !seen[hashtag] && hashtag != "#" {
			sanitized = append(sanitized, hashtag)
			seen[hashtag] = true
		}
	}
	
	return sanitized
}

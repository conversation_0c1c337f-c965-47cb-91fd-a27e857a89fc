// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// BankTransfersColumns holds the columns for the "bank_transfers" table.
	BankTransfersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "plan_id", Type: field.TypeUUID, Nullable: true},
		{Name: "amount", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "VND"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "confirmed", "failed", "expired"}, Default: "pending"},
		{Name: "bank_account", Type: field.TypeString, Size: 100},
		{Name: "reference_code", Type: field.TypeString, Unique: true, Size: 50},
		{Name: "transfer_instructions", Type: field.TypeString, Size: **********},
		{Name: "confirmed_at", Type: field.TypeTime, Nullable: true},
		{Name: "expires_at", Type: field.TypeTime},
		{Name: "credits_to_add", Type: field.TypeInt, Default: 0},
		{Name: "bank_transaction_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "actual_amount_received", Type: field.TypeInt64, Nullable: true},
		{Name: "confirmation_method", Type: field.TypeString, Size: 20, Default: "manual"},
		{Name: "confirmed_by", Type: field.TypeUUID, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// BankTransfersTable holds the schema information for the "bank_transfers" table.
	BankTransfersTable = &schema.Table{
		Name:       "bank_transfers",
		Columns:    BankTransfersColumns,
		PrimaryKey: []*schema.Column{BankTransfersColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "banktransfer_user_id",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[1]},
			},
			{
				Name:    "banktransfer_plan_id",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[2]},
			},
			{
				Name:    "banktransfer_status",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[5]},
			},
			{
				Name:    "banktransfer_reference_code",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[7]},
			},
			{
				Name:    "banktransfer_expires_at",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[10]},
			},
			{
				Name:    "banktransfer_confirmed_at",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[9]},
			},
			{
				Name:    "banktransfer_bank_transaction_id",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[12]},
			},
			{
				Name:    "banktransfer_created_at",
				Unique:  false,
				Columns: []*schema.Column{BankTransfersColumns[17]},
			},
		},
	}
	// CreditPlansColumns holds the columns for the "credit_plans" table.
	CreditPlansColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString, Size: 100},
		{Name: "display_name", Type: field.TypeString, Size: 100},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: **********},
		{Name: "credits", Type: field.TypeInt},
		{Name: "price", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "USD"},
		{Name: "discount_percentage", Type: field.TypeInt, Default: 0},
		{Name: "is_popular", Type: field.TypeBool, Default: false},
		{Name: "is_active", Type: field.TypeBool, Default: true},
		{Name: "features", Type: field.TypeJSON, Nullable: true},
		{Name: "valid_until", Type: field.TypeTime, Nullable: true},
		{Name: "sort_order", Type: field.TypeInt, Default: 0},
		{Name: "stripe_price_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "stripe_product_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// CreditPlansTable holds the schema information for the "credit_plans" table.
	CreditPlansTable = &schema.Table{
		Name:       "credit_plans",
		Columns:    CreditPlansColumns,
		PrimaryKey: []*schema.Column{CreditPlansColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "creditplan_is_active",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[9]},
			},
			{
				Name:    "creditplan_is_popular",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[8]},
			},
			{
				Name:    "creditplan_sort_order",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[12]},
			},
			{
				Name:    "creditplan_valid_until",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[11]},
			},
			{
				Name:    "creditplan_stripe_price_id",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[13]},
			},
			{
				Name:    "creditplan_created_at",
				Unique:  false,
				Columns: []*schema.Column{CreditPlansColumns[16]},
			},
		},
	}
	// CreditReservationsColumns holds the columns for the "credit_reservations" table.
	CreditReservationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "amount", Type: field.TypeInt},
		{Name: "purpose", Type: field.TypeString, Size: 100},
		{Name: "reference_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "reference_type", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"reserved", "consumed", "expired", "cancelled"}, Default: "reserved"},
		{Name: "expires_at", Type: field.TypeTime},
		{Name: "consumed_at", Type: field.TypeTime, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// CreditReservationsTable holds the schema information for the "credit_reservations" table.
	CreditReservationsTable = &schema.Table{
		Name:       "credit_reservations",
		Columns:    CreditReservationsColumns,
		PrimaryKey: []*schema.Column{CreditReservationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "creditreservation_user_id",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[1]},
			},
			{
				Name:    "creditreservation_status",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[6]},
			},
			{
				Name:    "creditreservation_expires_at",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[7]},
			},
			{
				Name:    "creditreservation_reference_id",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[4]},
			},
			{
				Name:    "creditreservation_reference_type",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[5]},
			},
			{
				Name:    "creditreservation_created_at",
				Unique:  false,
				Columns: []*schema.Column{CreditReservationsColumns[10]},
			},
		},
	}
	// CreditTransactionsColumns holds the columns for the "credit_transactions" table.
	CreditTransactionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"purchase", "usage", "bonus", "refund", "adjustment", "subscription", "referral"}, Default: "usage"},
		{Name: "amount", Type: field.TypeInt},
		{Name: "description", Type: field.TypeString, Size: 500},
		{Name: "reference_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "reference_type", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "payment_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "invoice_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "completed", "failed", "cancelled"}, Default: "completed"},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "processed_at", Type: field.TypeTime, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// CreditTransactionsTable holds the schema information for the "credit_transactions" table.
	CreditTransactionsTable = &schema.Table{
		Name:       "credit_transactions",
		Columns:    CreditTransactionsColumns,
		PrimaryKey: []*schema.Column{CreditTransactionsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "credittransaction_user_id",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[1]},
			},
			{
				Name:    "credittransaction_type",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[2]},
			},
			{
				Name:    "credittransaction_status",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[9]},
			},
			{
				Name:    "credittransaction_reference_id",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[5]},
			},
			{
				Name:    "credittransaction_reference_type",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[6]},
			},
			{
				Name:    "credittransaction_payment_id",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[7]},
			},
			{
				Name:    "credittransaction_created_at",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[12]},
			},
			{
				Name:    "credittransaction_user_id_created_at",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[1], CreditTransactionsColumns[12]},
			},
			{
				Name:    "credittransaction_user_id_type",
				Unique:  false,
				Columns: []*schema.Column{CreditTransactionsColumns[1], CreditTransactionsColumns[2]},
			},
		},
	}
	// OrdersColumns holds the columns for the "orders" table.
	OrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "order_number", Type: field.TypeString, Unique: true, Size: 50},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "package_id", Type: field.TypeUUID, Nullable: true},
		{Name: "plan_id", Type: field.TypeUUID, Nullable: true},
		{Name: "type", Type: field.TypeEnum, Enums: []string{"credit_purchase", "subscription", "upgrade", "downgrade"}, Default: "credit_purchase"},
		{Name: "amount", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "USD"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "processing", "completed", "failed", "cancelled", "refunded"}, Default: "pending"},
		{Name: "payment_method", Type: field.TypeString, Size: 50},
		{Name: "payment_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "invoice_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "credits", Type: field.TypeInt, Default: 0},
		{Name: "billing_address", Type: field.TypeJSON, Nullable: true},
		{Name: "items", Type: field.TypeJSON, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "processed_at", Type: field.TypeTime, Nullable: true},
		{Name: "expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "failure_reason", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "external_order_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// OrdersTable holds the schema information for the "orders" table.
	OrdersTable = &schema.Table{
		Name:       "orders",
		Columns:    OrdersColumns,
		PrimaryKey: []*schema.Column{OrdersColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "order_order_number",
				Unique:  true,
				Columns: []*schema.Column{OrdersColumns[1]},
			},
			{
				Name:    "order_user_id",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[2]},
			},
			{
				Name:    "order_package_id",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[3]},
			},
			{
				Name:    "order_plan_id",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[4]},
			},
			{
				Name:    "order_type",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[5]},
			},
			{
				Name:    "order_status",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[8]},
			},
			{
				Name:    "order_payment_method",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[9]},
			},
			{
				Name:    "order_payment_id",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[10]},
			},
			{
				Name:    "order_external_order_id",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[19]},
			},
			{
				Name:    "order_created_at",
				Unique:  false,
				Columns: []*schema.Column{OrdersColumns[20]},
			},
		},
	}
	// PaymentsColumns holds the columns for the "payments" table.
	PaymentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "order_id", Type: field.TypeUUID, Nullable: true},
		{Name: "subscription_id", Type: field.TypeUUID, Nullable: true},
		{Name: "payment_method", Type: field.TypeString, Size: 50},
		{Name: "payment_provider", Type: field.TypeString, Size: 50},
		{Name: "external_payment_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "amount", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "USD"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "processing", "completed", "failed", "cancelled", "refunded"}, Default: "pending"},
		{Name: "payment_intent_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "charge_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "billing_details", Type: field.TypeJSON, Nullable: true},
		{Name: "payment_method_details", Type: field.TypeJSON, Nullable: true},
		{Name: "paid_at", Type: field.TypeTime, Nullable: true},
		{Name: "failed_at", Type: field.TypeTime, Nullable: true},
		{Name: "failure_code", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "failure_message", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "refunded_amount", Type: field.TypeInt64, Default: 0},
		{Name: "refunded_at", Type: field.TypeTime, Nullable: true},
		{Name: "refund_reason", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "webhook_data", Type: field.TypeJSON, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// PaymentsTable holds the schema information for the "payments" table.
	PaymentsTable = &schema.Table{
		Name:       "payments",
		Columns:    PaymentsColumns,
		PrimaryKey: []*schema.Column{PaymentsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "payment_user_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[1]},
			},
			{
				Name:    "payment_order_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[2]},
			},
			{
				Name:    "payment_subscription_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[3]},
			},
			{
				Name:    "payment_payment_method",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[4]},
			},
			{
				Name:    "payment_payment_provider",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[5]},
			},
			{
				Name:    "payment_external_payment_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[6]},
			},
			{
				Name:    "payment_status",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[9]},
			},
			{
				Name:    "payment_payment_intent_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[10]},
			},
			{
				Name:    "payment_charge_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[11]},
			},
			{
				Name:    "payment_paid_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[14]},
			},
			{
				Name:    "payment_failed_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[15]},
			},
			{
				Name:    "payment_created_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentsColumns[23]},
			},
		},
	}
	// PaymentConfirmationsColumns holds the columns for the "payment_confirmations" table.
	PaymentConfirmationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "bank_transfer_id", Type: field.TypeUUID, Nullable: true},
		{Name: "order_id", Type: field.TypeUUID, Nullable: true},
		{Name: "subscription_id", Type: field.TypeUUID, Nullable: true},
		{Name: "payment_method", Type: field.TypeString, Size: 50},
		{Name: "external_payment_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "bank_transaction_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "amount_paid", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "VND"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "confirmed", "failed", "disputed"}, Default: "pending"},
		{Name: "confirmed_by", Type: field.TypeUUID, Nullable: true},
		{Name: "confirmation_method", Type: field.TypeString, Size: 20, Default: "manual"},
		{Name: "confirmation_notes", Type: field.TypeString, Nullable: true, Size: **********},
		{Name: "payment_details", Type: field.TypeJSON, Nullable: true},
		{Name: "webhook_data", Type: field.TypeJSON, Nullable: true},
		{Name: "payment_date", Type: field.TypeTime, Nullable: true},
		{Name: "confirmed_at", Type: field.TypeTime, Nullable: true},
		{Name: "failure_reason", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// PaymentConfirmationsTable holds the schema information for the "payment_confirmations" table.
	PaymentConfirmationsTable = &schema.Table{
		Name:       "payment_confirmations",
		Columns:    PaymentConfirmationsColumns,
		PrimaryKey: []*schema.Column{PaymentConfirmationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "paymentconfirmation_bank_transfer_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[1]},
			},
			{
				Name:    "paymentconfirmation_order_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[2]},
			},
			{
				Name:    "paymentconfirmation_subscription_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[3]},
			},
			{
				Name:    "paymentconfirmation_payment_method",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[4]},
			},
			{
				Name:    "paymentconfirmation_external_payment_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[5]},
			},
			{
				Name:    "paymentconfirmation_bank_transaction_id",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[6]},
			},
			{
				Name:    "paymentconfirmation_status",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[9]},
			},
			{
				Name:    "paymentconfirmation_confirmed_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[16]},
			},
			{
				Name:    "paymentconfirmation_payment_date",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[15]},
			},
			{
				Name:    "paymentconfirmation_created_at",
				Unique:  false,
				Columns: []*schema.Column{PaymentConfirmationsColumns[19]},
			},
		},
	}
	// ReferralsColumns holds the columns for the "referrals" table.
	ReferralsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "referrer_id", Type: field.TypeUUID},
		{Name: "referred_id", Type: field.TypeUUID, Nullable: true},
		{Name: "referral_code", Type: field.TypeString, Unique: true, Size: 20},
		{Name: "referred_email", Type: field.TypeString, Nullable: true, Size: 255},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"pending", "completed", "expired", "cancelled"}, Default: "pending"},
		{Name: "referrer_credits_earned", Type: field.TypeInt, Default: 0},
		{Name: "referred_credits_earned", Type: field.TypeInt, Default: 0},
		{Name: "referral_type", Type: field.TypeString, Size: 50, Default: "signup"},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "referral_conditions", Type: field.TypeJSON, Nullable: true},
		{Name: "tracking_data", Type: field.TypeJSON, Nullable: true},
		{Name: "campaign_id", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "source", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// ReferralsTable holds the schema information for the "referrals" table.
	ReferralsTable = &schema.Table{
		Name:       "referrals",
		Columns:    ReferralsColumns,
		PrimaryKey: []*schema.Column{ReferralsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "referral_referrer_id",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[1]},
			},
			{
				Name:    "referral_referred_id",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[2]},
			},
			{
				Name:    "referral_referral_code",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[3]},
			},
			{
				Name:    "referral_referred_email",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[4]},
			},
			{
				Name:    "referral_status",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[5]},
			},
			{
				Name:    "referral_referral_type",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[8]},
			},
			{
				Name:    "referral_completed_at",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[9]},
			},
			{
				Name:    "referral_expires_at",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[10]},
			},
			{
				Name:    "referral_campaign_id",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[13]},
			},
			{
				Name:    "referral_source",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[14]},
			},
			{
				Name:    "referral_created_at",
				Unique:  false,
				Columns: []*schema.Column{ReferralsColumns[16]},
			},
		},
	}
	// SubscriptionsColumns holds the columns for the "subscriptions" table.
	SubscriptionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "plan_id", Type: field.TypeUUID},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"active", "cancelled", "expired", "trial", "past_due"}, Default: "trial"},
		{Name: "current_period_start", Type: field.TypeTime},
		{Name: "current_period_end", Type: field.TypeTime},
		{Name: "trial_start", Type: field.TypeTime, Nullable: true},
		{Name: "trial_end", Type: field.TypeTime, Nullable: true},
		{Name: "cancelled_at", Type: field.TypeTime, Nullable: true},
		{Name: "cancel_at_period_end", Type: field.TypeBool, Default: false},
		{Name: "payment_method", Type: field.TypeString, Size: 50},
		{Name: "billing_address", Type: field.TypeJSON, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "last_billed_at", Type: field.TypeTime, Nullable: true},
		{Name: "next_billing_at", Type: field.TypeTime, Nullable: true},
		{Name: "external_subscription_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// SubscriptionsTable holds the schema information for the "subscriptions" table.
	SubscriptionsTable = &schema.Table{
		Name:       "subscriptions",
		Columns:    SubscriptionsColumns,
		PrimaryKey: []*schema.Column{SubscriptionsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "subscription_user_id",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[1]},
			},
			{
				Name:    "subscription_plan_id",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[2]},
			},
			{
				Name:    "subscription_status",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[3]},
			},
			{
				Name:    "subscription_current_period_end",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[5]},
			},
			{
				Name:    "subscription_trial_end",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[7]},
			},
			{
				Name:    "subscription_next_billing_at",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[14]},
			},
			{
				Name:    "subscription_external_subscription_id",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[15]},
			},
			{
				Name:    "subscription_created_at",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionsColumns[16]},
			},
		},
	}
	// SubscriptionPlansColumns holds the columns for the "subscription_plans" table.
	SubscriptionPlansColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString, Unique: true, Size: 100},
		{Name: "display_name", Type: field.TypeString, Size: 100},
		{Name: "description", Type: field.TypeString, Nullable: true, Size: **********},
		{Name: "price", Type: field.TypeInt64},
		{Name: "currency", Type: field.TypeString, Size: 3, Default: "USD"},
		{Name: "billing_interval", Type: field.TypeEnum, Enums: []string{"monthly", "yearly"}, Default: "monthly"},
		{Name: "credits_included", Type: field.TypeInt, Default: 0},
		{Name: "monthly_credits", Type: field.TypeInt, Default: 0},
		{Name: "is_unlimited", Type: field.TypeBool, Default: false},
		{Name: "features", Type: field.TypeJSON, Nullable: true},
		{Name: "limits", Type: field.TypeJSON, Nullable: true},
		{Name: "max_users", Type: field.TypeInt, Default: 1},
		{Name: "max_workspaces", Type: field.TypeInt, Default: 1},
		{Name: "is_active", Type: field.TypeBool, Default: true},
		{Name: "is_featured", Type: field.TypeBool, Default: false},
		{Name: "sort_order", Type: field.TypeInt, Default: 0},
		{Name: "stripe_price_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "stripe_product_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// SubscriptionPlansTable holds the schema information for the "subscription_plans" table.
	SubscriptionPlansTable = &schema.Table{
		Name:       "subscription_plans",
		Columns:    SubscriptionPlansColumns,
		PrimaryKey: []*schema.Column{SubscriptionPlansColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "subscriptionplan_name",
				Unique:  true,
				Columns: []*schema.Column{SubscriptionPlansColumns[1]},
			},
			{
				Name:    "subscriptionplan_is_active",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionPlansColumns[14]},
			},
			{
				Name:    "subscriptionplan_billing_interval",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionPlansColumns[6]},
			},
			{
				Name:    "subscriptionplan_sort_order",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionPlansColumns[16]},
			},
			{
				Name:    "subscriptionplan_stripe_price_id",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionPlansColumns[17]},
			},
			{
				Name:    "subscriptionplan_created_at",
				Unique:  false,
				Columns: []*schema.Column{SubscriptionPlansColumns[20]},
			},
		},
	}
	// UserCreditsColumns holds the columns for the "user_credits" table.
	UserCreditsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "user_id", Type: field.TypeUUID, Unique: true},
		{Name: "current_credits", Type: field.TypeInt, Default: 0},
		{Name: "total_credits", Type: field.TypeInt, Default: 0},
		{Name: "plan_id", Type: field.TypeString, Nullable: true, Size: 36},
		{Name: "plan_name", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "plan_expires_at", Type: field.TypeTime, Nullable: true},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"active", "suspended", "cancelled"}, Default: "active"},
		{Name: "monthly_limit", Type: field.TypeInt, Default: 0},
		{Name: "monthly_used", Type: field.TypeInt, Default: 0},
		{Name: "monthly_reset_at", Type: field.TypeTime, Nullable: true},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// UserCreditsTable holds the schema information for the "user_credits" table.
	UserCreditsTable = &schema.Table{
		Name:       "user_credits",
		Columns:    UserCreditsColumns,
		PrimaryKey: []*schema.Column{UserCreditsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "usercredit_user_id",
				Unique:  true,
				Columns: []*schema.Column{UserCreditsColumns[1]},
			},
			{
				Name:    "usercredit_plan_id",
				Unique:  false,
				Columns: []*schema.Column{UserCreditsColumns[4]},
			},
			{
				Name:    "usercredit_status",
				Unique:  false,
				Columns: []*schema.Column{UserCreditsColumns[7]},
			},
			{
				Name:    "usercredit_plan_expires_at",
				Unique:  false,
				Columns: []*schema.Column{UserCreditsColumns[6]},
			},
			{
				Name:    "usercredit_monthly_reset_at",
				Unique:  false,
				Columns: []*schema.Column{UserCreditsColumns[10]},
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		BankTransfersTable,
		CreditPlansTable,
		CreditReservationsTable,
		CreditTransactionsTable,
		OrdersTable,
		PaymentsTable,
		PaymentConfirmationsTable,
		ReferralsTable,
		SubscriptionsTable,
		SubscriptionPlansTable,
		UserCreditsTable,
	}
)

func init() {
}

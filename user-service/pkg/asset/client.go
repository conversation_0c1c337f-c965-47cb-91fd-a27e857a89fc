package asset

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/social-content-ai/pkg-shared/logging"
	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
)

// Client wraps the asset service gRPC client
type Client struct {
	client assetv1.AssetServiceClient
	conn   *grpc.ClientConn
	logger logging.Logger
}

// NewClient creates a new asset service client
func NewClient(address string, logger logging.Logger) (*Client, error) {
	conn, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to asset service: %w", err)
	}

	client := assetv1.NewAssetServiceClient(conn)

	return &Client{
		client: client,
		conn:   conn,
		logger: logger,
	}, nil
}

// Close closes the gRPC connection
func (c *Client) Close() error {
	return c.conn.Close()
}

// RequestAvatarUpload requests a presigned URL for avatar upload
func (c *Client) RequestAvatarUpload(ctx context.Context, userID, fileName, fileType string, fileSize int64) (*UploadResponse, error) {
	req := &assetv1.UploadRequest{
		UserId:   userID,
		FileName: fileName,
		FileType: fileType,
		FileSize: fileSize,
		Purpose:  "profile_image",
		IsPublic: true, // Avatar images are public
		Metadata: map[string]string{
			"upload_source": "user_service",
			"image_type":    "avatar",
		},
	}

	resp, err := c.client.RequestUpload(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":   userID,
			"file_name": fileName,
			"file_type": fileType,
			"file_size": fileSize,
		}).Error("Failed to request avatar upload")
		return nil, fmt.Errorf("failed to request avatar upload: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"user_id":   userID,
		"upload_id": resp.UploadId,
		"s3_key":    resp.S3Key,
	}).Info("Avatar upload requested successfully")

	return &UploadResponse{
		UploadID:     resp.UploadId,
		PresignedURL: resp.PresignedUrl,
		S3Key:        resp.S3Key,
		S3Bucket:     resp.S3Bucket,
		ExpiresAt:    resp.ExpiresAt.AsTime(),
		UploadFields: resp.UploadFields,
	}, nil
}

// ConfirmAvatarUpload confirms the avatar upload completion
func (c *Client) ConfirmAvatarUpload(ctx context.Context, uploadID, userID, etag string) (*ConfirmUploadResponse, error) {
	req := &assetv1.ConfirmUploadRequest{
		UploadId:          uploadID,
		UserId:            userID,
		Etag:              etag,
		TriggerValidation: true,  // Always validate avatar images
		TriggerProcessing: false, // No RAG processing for avatars
	}

	resp, err := c.client.ConfirmUpload(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"upload_id": uploadID,
			"user_id":   userID,
			"etag":      etag,
		}).Error("Failed to confirm avatar upload")
		return nil, fmt.Errorf("failed to confirm avatar upload: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"upload_id": uploadID,
		"user_id":   userID,
		"asset_id":  resp.AssetId,
		"status":    resp.Status,
	}).Info("Avatar upload confirmed successfully")

	return &ConfirmUploadResponse{
		AssetID:             resp.AssetId,
		Status:              resp.Status,
		ValidationTriggered: resp.ValidationTriggered,
		ProcessingTriggered: resp.ProcessingTriggered,
		Asset:               convertAsset(resp.Asset),
	}, nil
}

// GetAsset retrieves asset information
func (c *Client) GetAsset(ctx context.Context, assetID, userID string) (*Asset, error) {
	req := &assetv1.GetAssetRequest{
		AssetId: assetID,
		UserId:  userID,
	}

	resp, err := c.client.GetAsset(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"asset_id": assetID,
			"user_id":  userID,
		}).Error("Failed to get asset")
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}

	return convertAsset(resp), nil
}

// GetAvatarDownloadURL gets a download URL for avatar
func (c *Client) GetAvatarDownloadURL(ctx context.Context, assetID, userID string, expiryMinutes int32) (*DownloadURLResponse, error) {
	req := &assetv1.GetDownloadUrlRequest{
		AssetId:       assetID,
		UserId:        userID,
		ExpiryMinutes: expiryMinutes,
		Inline:        true, // Display inline for avatars
	}

	resp, err := c.client.GetDownloadUrl(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"asset_id": assetID,
			"user_id":  userID,
		}).Error("Failed to get avatar download URL")
		return nil, fmt.Errorf("failed to get avatar download URL: %w", err)
	}

	return &DownloadURLResponse{
		DownloadURL:   resp.DownloadUrl,
		ExpiresAt:     resp.ExpiresAt.AsTime(),
		ContentType:   resp.ContentType,
		ContentLength: resp.ContentLength,
	}, nil
}

// DeleteAvatar deletes an avatar asset
func (c *Client) DeleteAvatar(ctx context.Context, assetID, userID string) error {
	req := &assetv1.DeleteAssetRequest{
		AssetId:           assetID,
		UserId:            userID,
		HardDelete:        true, // Permanently delete avatar from S3
		CleanupReferences: true, // Clean up references
	}

	_, err := c.client.DeleteAsset(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"asset_id": assetID,
			"user_id":  userID,
		}).Error("Failed to delete avatar")
		return fmt.Errorf("failed to delete avatar: %w", err)
	}

	c.logger.WithFields(map[string]interface{}{
		"asset_id": assetID,
		"user_id":  userID,
	}).Info("Avatar deleted successfully")

	return nil
}

// ListUserAvatars lists all avatar assets for a user
func (c *Client) ListUserAvatars(ctx context.Context, userID string) ([]*Asset, error) {
	req := &assetv1.ListAssetsRequest{
		UserId:  userID,
		Purpose: "profile_image",
		Status:  "ready", // Only get ready assets
		Pagination: &commonv1.PaginationRequest{
			Page:  1,
			Limit: 10, // Should be enough for avatars
		},
	}

	resp, err := c.client.ListAssets(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithField("user_id", userID).Error("Failed to list user avatars")
		return nil, fmt.Errorf("failed to list user avatars: %w", err)
	}

	assets := make([]*Asset, len(resp.Assets))
	for i, asset := range resp.Assets {
		assets[i] = convertAsset(asset)
	}

	return assets, nil
}

// ValidateAvatar triggers validation for an avatar asset
func (c *Client) ValidateAvatar(ctx context.Context, assetID, userID string) (*ValidationResult, error) {
	req := &assetv1.ValidateAssetRequest{
		AssetId:           assetID,
		UserId:            userID,
		ForceRevalidation: false,
		ValidationTypes:   []string{"image", "safety", "corruption"},
	}

	resp, err := c.client.ValidateAsset(ctx, req)
	if err != nil {
		c.logger.WithError(err).WithFields(map[string]interface{}{
			"asset_id": assetID,
			"user_id":  userID,
		}).Error("Failed to validate avatar")
		return nil, fmt.Errorf("failed to validate avatar: %w", err)
	}

	return convertValidationResult(resp), nil
}

// convertAsset converts protobuf Asset to local Asset struct
func convertAsset(pbAsset *assetv1.Asset) *Asset {
	if pbAsset == nil {
		return nil
	}

	asset := &Asset{
		ID:            pbAsset.Id,
		UserID:        pbAsset.UserId,
		WorkspaceID:   pbAsset.WorkspaceId,
		TemplateID:    pbAsset.TemplateId,
		FileName:      pbAsset.FileName,
		OriginalName:  pbAsset.OriginalName,
		FileType:      pbAsset.FileType,
		FileExtension: pbAsset.FileExtension,
		FileSize:      pbAsset.FileSize,
		S3Key:         pbAsset.S3Key,
		S3Bucket:      pbAsset.S3Bucket,
		ContentHash:   pbAsset.ContentHash,
		Purpose:       pbAsset.Purpose,
		Status:        pbAsset.Status,
		IsPublic:      pbAsset.IsPublic,
		ThumbnailURL:  pbAsset.ThumbnailUrl,
		Metadata:      pbAsset.Metadata,
	}

	if pbAsset.CreatedAt != nil {
		asset.CreatedAt = pbAsset.CreatedAt.AsTime()
	}

	if pbAsset.UpdatedAt != nil {
		asset.UpdatedAt = pbAsset.UpdatedAt.AsTime()
	}

	if pbAsset.Validation != nil {
		asset.Validation = convertValidationResult(pbAsset.Validation)
	}

	return asset
}

// convertValidationResult converts protobuf ValidationResult to local struct
func convertValidationResult(pbResult *assetv1.ValidationResult) *ValidationResult {
	if pbResult == nil {
		return nil
	}

	result := &ValidationResult{
		IsValid:     pbResult.IsValid,
		Errors:      pbResult.Errors,
		Warnings:    pbResult.Warnings,
		IsSafe:      pbResult.IsSafe,
		IsCorrupted: pbResult.IsCorrupted,
		MimeType:    pbResult.MimeType,
		Properties:  pbResult.Properties,
	}

	if pbResult.ValidatedAt != nil {
		result.ValidatedAt = pbResult.ValidatedAt.AsTime()
	}

	return result
}

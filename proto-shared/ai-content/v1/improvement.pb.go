// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: ai-content/v1/improvement.proto

package aicontentv1

import (
	v1 "github.com/social-content-ai/proto-shared/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ContentImprovementRequest for improving existing content
type ContentImprovementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OriginalText    string `protobuf:"bytes,2,opt,name=original_text,json=originalText,proto3" json:"original_text,omitempty"`
	ImprovementType string `protobuf:"bytes,3,opt,name=improvement_type,json=improvementType,proto3" json:"improvement_type,omitempty"` // grammar, style, engagement, etc.
	TargetTone      string `protobuf:"bytes,4,opt,name=target_tone,json=targetTone,proto3" json:"target_tone,omitempty"`                // Target tone for improvement
	TargetLength    string `protobuf:"bytes,5,opt,name=target_length,json=targetLength,proto3" json:"target_length,omitempty"`          // shorter, longer, same
	Platform        string `protobuf:"bytes,6,opt,name=platform,proto3" json:"platform,omitempty"`                                      // Target platform for optimization
	Context         string `protobuf:"bytes,7,opt,name=context,proto3" json:"context,omitempty"`                                        // Additional context
	Model           string `protobuf:"bytes,8,opt,name=model,proto3" json:"model,omitempty"`                                            // AI model to use
	WorkspaceId     string `protobuf:"bytes,9,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`             // Workspace context
}

func (x *ContentImprovementRequest) Reset() {
	*x = ContentImprovementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_improvement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentImprovementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentImprovementRequest) ProtoMessage() {}

func (x *ContentImprovementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_improvement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentImprovementRequest.ProtoReflect.Descriptor instead.
func (*ContentImprovementRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_improvement_proto_rawDescGZIP(), []int{0}
}

func (x *ContentImprovementRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ContentImprovementRequest) GetOriginalText() string {
	if x != nil {
		return x.OriginalText
	}
	return ""
}

func (x *ContentImprovementRequest) GetImprovementType() string {
	if x != nil {
		return x.ImprovementType
	}
	return ""
}

func (x *ContentImprovementRequest) GetTargetTone() string {
	if x != nil {
		return x.TargetTone
	}
	return ""
}

func (x *ContentImprovementRequest) GetTargetLength() string {
	if x != nil {
		return x.TargetLength
	}
	return ""
}

func (x *ContentImprovementRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ContentImprovementRequest) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *ContentImprovementRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ContentImprovementRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

// ContentImprovementResponse with improved content
type ContentImprovementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OriginalText        string                 `protobuf:"bytes,2,opt,name=original_text,json=originalText,proto3" json:"original_text,omitempty"`
	ImprovedText        string                 `protobuf:"bytes,3,opt,name=improved_text,json=improvedText,proto3" json:"improved_text,omitempty"`
	ImprovementType     string                 `protobuf:"bytes,4,opt,name=improvement_type,json=improvementType,proto3" json:"improvement_type,omitempty"`
	TargetTone          string                 `protobuf:"bytes,5,opt,name=target_tone,json=targetTone,proto3" json:"target_tone,omitempty"`
	TargetLength        string                 `protobuf:"bytes,6,opt,name=target_length,json=targetLength,proto3" json:"target_length,omitempty"`
	Platform            string                 `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform,omitempty"`
	ReadabilityScore    float32                `protobuf:"fixed32,8,opt,name=readability_score,json=readabilityScore,proto3" json:"readability_score,omitempty"`          // 1-10 scale
	EngagementPotential float32                `protobuf:"fixed32,9,opt,name=engagement_potential,json=engagementPotential,proto3" json:"engagement_potential,omitempty"` // 1-10 scale
	SentimentScore      float32                `protobuf:"fixed32,10,opt,name=sentiment_score,json=sentimentScore,proto3" json:"sentiment_score,omitempty"`               // -1 to 1 scale
	CreditsUsed         int32                  `protobuf:"varint,11,opt,name=credits_used,json=creditsUsed,proto3" json:"credits_used,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *ContentImprovementResponse) Reset() {
	*x = ContentImprovementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_improvement_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentImprovementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentImprovementResponse) ProtoMessage() {}

func (x *ContentImprovementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_improvement_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentImprovementResponse.ProtoReflect.Descriptor instead.
func (*ContentImprovementResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_improvement_proto_rawDescGZIP(), []int{1}
}

func (x *ContentImprovementResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContentImprovementResponse) GetOriginalText() string {
	if x != nil {
		return x.OriginalText
	}
	return ""
}

func (x *ContentImprovementResponse) GetImprovedText() string {
	if x != nil {
		return x.ImprovedText
	}
	return ""
}

func (x *ContentImprovementResponse) GetImprovementType() string {
	if x != nil {
		return x.ImprovementType
	}
	return ""
}

func (x *ContentImprovementResponse) GetTargetTone() string {
	if x != nil {
		return x.TargetTone
	}
	return ""
}

func (x *ContentImprovementResponse) GetTargetLength() string {
	if x != nil {
		return x.TargetLength
	}
	return ""
}

func (x *ContentImprovementResponse) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *ContentImprovementResponse) GetReadabilityScore() float32 {
	if x != nil {
		return x.ReadabilityScore
	}
	return 0
}

func (x *ContentImprovementResponse) GetEngagementPotential() float32 {
	if x != nil {
		return x.EngagementPotential
	}
	return 0
}

func (x *ContentImprovementResponse) GetSentimentScore() float32 {
	if x != nil {
		return x.SentimentScore
	}
	return 0
}

func (x *ContentImprovementResponse) GetCreditsUsed() int32 {
	if x != nil {
		return x.CreditsUsed
	}
	return 0
}

func (x *ContentImprovementResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// GetImprovementRequest for retrieving an improvement
type GetImprovementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetImprovementRequest) Reset() {
	*x = GetImprovementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_improvement_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetImprovementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetImprovementRequest) ProtoMessage() {}

func (x *GetImprovementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_improvement_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetImprovementRequest.ProtoReflect.Descriptor instead.
func (*GetImprovementRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_improvement_proto_rawDescGZIP(), []int{2}
}

func (x *GetImprovementRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ListImprovementsRequest for listing user improvements
type ListImprovementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pagination      *v1.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ImprovementType string                `protobuf:"bytes,3,opt,name=improvement_type,json=improvementType,proto3" json:"improvement_type,omitempty"` // Filter by improvement type
	Platform        string                `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`                                      // Filter by platform
}

func (x *ListImprovementsRequest) Reset() {
	*x = ListImprovementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_improvement_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImprovementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImprovementsRequest) ProtoMessage() {}

func (x *ListImprovementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_improvement_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImprovementsRequest.ProtoReflect.Descriptor instead.
func (*ListImprovementsRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_improvement_proto_rawDescGZIP(), []int{3}
}

func (x *ListImprovementsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListImprovementsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListImprovementsRequest) GetImprovementType() string {
	if x != nil {
		return x.ImprovementType
	}
	return ""
}

func (x *ListImprovementsRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// ListImprovementsResponse with paginated improvements
type ListImprovementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Improvements []*ContentImprovementResponse `protobuf:"bytes,1,rep,name=improvements,proto3" json:"improvements,omitempty"`
	Pagination   *v1.PaginationResponse        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListImprovementsResponse) Reset() {
	*x = ListImprovementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_improvement_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImprovementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImprovementsResponse) ProtoMessage() {}

func (x *ListImprovementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_improvement_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImprovementsResponse.ProtoReflect.Descriptor instead.
func (*ListImprovementsResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_improvement_proto_rawDescGZIP(), []int{4}
}

func (x *ListImprovementsResponse) GetImprovements() []*ContentImprovementResponse {
	if x != nil {
		return x.Improvements
	}
	return nil
}

func (x *ListImprovementsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_ai_content_v1_improvement_proto protoreflect.FileDescriptor

var file_ai_content_v1_improvement_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb9, 0x02,
	0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x54, 0x6f, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0xea, 0x03, 0x0a, 0x1a, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x6f, 0x6e, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x61, 0x64, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x72, 0x65, 0x61, 0x64,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x14,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6f, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12,
	0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xb7, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xa8, 0x01, 0x0a, 0x18, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61,
	0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0xca, 0x02, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x65, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49,
	0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x29, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x26, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x61,
	0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x61,
	0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ai_content_v1_improvement_proto_rawDescOnce sync.Once
	file_ai_content_v1_improvement_proto_rawDescData = file_ai_content_v1_improvement_proto_rawDesc
)

func file_ai_content_v1_improvement_proto_rawDescGZIP() []byte {
	file_ai_content_v1_improvement_proto_rawDescOnce.Do(func() {
		file_ai_content_v1_improvement_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_content_v1_improvement_proto_rawDescData)
	})
	return file_ai_content_v1_improvement_proto_rawDescData
}

var file_ai_content_v1_improvement_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_ai_content_v1_improvement_proto_goTypes = []interface{}{
	(*ContentImprovementRequest)(nil),  // 0: ai_content.v1.ContentImprovementRequest
	(*ContentImprovementResponse)(nil), // 1: ai_content.v1.ContentImprovementResponse
	(*GetImprovementRequest)(nil),      // 2: ai_content.v1.GetImprovementRequest
	(*ListImprovementsRequest)(nil),    // 3: ai_content.v1.ListImprovementsRequest
	(*ListImprovementsResponse)(nil),   // 4: ai_content.v1.ListImprovementsResponse
	(*timestamppb.Timestamp)(nil),      // 5: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),       // 6: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),      // 7: common.v1.PaginationResponse
}
var file_ai_content_v1_improvement_proto_depIdxs = []int32{
	5, // 0: ai_content.v1.ContentImprovementResponse.created_at:type_name -> google.protobuf.Timestamp
	6, // 1: ai_content.v1.ListImprovementsRequest.pagination:type_name -> common.v1.PaginationRequest
	1, // 2: ai_content.v1.ListImprovementsResponse.improvements:type_name -> ai_content.v1.ContentImprovementResponse
	7, // 3: ai_content.v1.ListImprovementsResponse.pagination:type_name -> common.v1.PaginationResponse
	0, // 4: ai_content.v1.ContentImprovementService.ImproveContent:input_type -> ai_content.v1.ContentImprovementRequest
	2, // 5: ai_content.v1.ContentImprovementService.GetImprovement:input_type -> ai_content.v1.GetImprovementRequest
	3, // 6: ai_content.v1.ContentImprovementService.ListImprovements:input_type -> ai_content.v1.ListImprovementsRequest
	1, // 7: ai_content.v1.ContentImprovementService.ImproveContent:output_type -> ai_content.v1.ContentImprovementResponse
	1, // 8: ai_content.v1.ContentImprovementService.GetImprovement:output_type -> ai_content.v1.ContentImprovementResponse
	4, // 9: ai_content.v1.ContentImprovementService.ListImprovements:output_type -> ai_content.v1.ListImprovementsResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_ai_content_v1_improvement_proto_init() }
func file_ai_content_v1_improvement_proto_init() {
	if File_ai_content_v1_improvement_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ai_content_v1_improvement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentImprovementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_improvement_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentImprovementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_improvement_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetImprovementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_improvement_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListImprovementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_improvement_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListImprovementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_content_v1_improvement_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_content_v1_improvement_proto_goTypes,
		DependencyIndexes: file_ai_content_v1_improvement_proto_depIdxs,
		MessageInfos:      file_ai_content_v1_improvement_proto_msgTypes,
	}.Build()
	File_ai_content_v1_improvement_proto = out.File
	file_ai_content_v1_improvement_proto_rawDesc = nil
	file_ai_content_v1_improvement_proto_goTypes = nil
	file_ai_content_v1_improvement_proto_depIdxs = nil
}

package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/usecase/auth"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authUseCase auth.UseCase
	logger      logging.Logger
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authUseCase auth.UseCase, logger logging.Logger) *AuthHandler {
	return &AuthHandler{
		authUseCase: authUseCase,
		logger:      logger,
	}
}

// Register godoc
// @Summary Register a new user
// @Description Create a new user account with email verification. Password must meet security requirements.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.RegisterRequest true "User registration details"
// @Success 201 {object} models.RegisterResponse "User successfully registered"
// @Failure 400 {object} models.ErrorResponse "Invalid request data or weak password"
// @Failure 409 {object} models.ErrorResponse "Email already exists"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid registration request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	// Add client info
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")

	resp, err := h.authUseCase.Register(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Registration failed")

		switch err.Error() {
		case "EMAIL_ALREADY_EXISTS":
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "EMAIL_ALREADY_EXISTS",
				Message: "Email address is already registered",
			})
		case "WEAK_PASSWORD":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "WEAK_PASSWORD",
				Message: "Password does not meet security requirements",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REGISTRATION_FAILED",
				Message: "Failed to register user",
			})
		}
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// Login godoc
// @Summary User login
// @Description Authenticate user with email and password. Returns access and refresh tokens on success.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body auth.LoginRequest true "User login credentials"
// @Success 200 {object} models.LoginResponse "Login successful with tokens"
// @Failure 400 {object} models.ErrorResponse "Invalid request format"
// @Failure 401 {object} models.ErrorResponse "Invalid credentials, account locked, or email not verified"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid login request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	// Add client info
	req.IPAddress = c.ClientIP()
	req.DeviceInfo = c.GetHeader("User-Agent")

	resp, err := h.authUseCase.Login(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Login failed")

		switch err.Error() {
		case "INVALID_CREDENTIALS":
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_CREDENTIALS",
				Message: "Invalid email or password",
			})
		case "USER_LOCKED":
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "USER_LOCKED",
				Message: "Account is locked",
			})
		case "EMAIL_NOT_VERIFIED":
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "EMAIL_NOT_VERIFIED",
				Message: "Please verify your email address",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "LOGIN_FAILED",
				Message: "Login failed",
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// Logout godoc
// @Summary User logout
// @Description Logout the authenticated user and invalidate the current session. The access token will be blacklisted.
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "Successfully logged out"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	sessionID, _ := c.Get("session_id")
	var sessionIDStr string
	if sessionID != nil {
		sessionIDStr = sessionID.(string)
	}

	err := h.authUseCase.Logout(c.Request.Context(), sessionIDStr)
	if err != nil {
		h.logger.WithError(err).Error("Logout failed")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LOGOUT_FAILED",
			Message: "Failed to logout",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Logged out successfully",
	})
}

// RefreshToken godoc
// @Summary Refresh access token
// @Description Generate a new access token using a valid refresh token. The refresh token must not be expired.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.RefreshTokenRequest true "Refresh token details"
// @Success 200 {object} models.TokenResponse "New access token generated"
// @Failure 400 {object} models.ErrorResponse "Invalid request format"
// @Failure 401 {object} models.ErrorResponse "Invalid or expired refresh token"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid refresh token request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	resp, err := h.authUseCase.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).Error("Token refresh failed")

		switch err.Error() {
		case "INVALID_TOKEN":
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid refresh token",
			})
		case "TOKEN_EXPIRED":
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "TOKEN_EXPIRED",
				Message: "Refresh token has expired",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REFRESH_FAILED",
				Message: "Failed to refresh token",
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ForgotPassword godoc
// @Summary Request password reset
// @Description Send a password reset email to the user. For security, the response is the same whether the email exists or not.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.ForgotPasswordRequest true "Email address for password reset"
// @Success 200 {object} models.SuccessResponse "Password reset email sent (if email exists)"
// @Failure 400 {object} models.ErrorResponse "Invalid request format"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/forgot-password [post]
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req models.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid forgot password request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	err := h.authUseCase.ResetPassword(c.Request.Context(), req.Email)
	if err != nil {
		h.logger.WithError(err).Error("Forgot password failed")
		// Don't reveal if email exists or not for security
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "If the email exists, a password reset link has been sent",
	})
}

// ResetPassword godoc
// @Summary Reset password
// @Description Reset user password using a valid reset token. The new password must meet security requirements.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.ResetPasswordRequest true "Reset token and new password"
// @Success 200 {object} models.SuccessResponse "Password successfully reset"
// @Failure 400 {object} models.ErrorResponse "Invalid request, token, or weak password"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/reset-password [post]
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req models.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid reset password request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	confirmReq := &auth.ConfirmPasswordResetRequest{
		Token:       req.Token,
		NewPassword: req.NewPassword,
	}
	err := h.authUseCase.ConfirmPasswordReset(c.Request.Context(), confirmReq)
	if err != nil {
		h.logger.WithError(err).Error("Password reset failed")

		switch err.Error() {
		case "INVALID_TOKEN":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid or expired reset token",
			})
		case "WEAK_PASSWORD":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "WEAK_PASSWORD",
				Message: "Password does not meet security requirements",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "RESET_FAILED",
				Message: "Failed to reset password",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Password reset successfully",
	})
}

// VerifyEmail godoc
// @Summary Verify email address
// @Description Verify user email address using the verification token sent during registration.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.VerifyEmailRequest true "Email verification token"
// @Success 200 {object} models.SuccessResponse "Email successfully verified"
// @Failure 400 {object} models.ErrorResponse "Invalid request or verification token"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/auth/verify-email [post]
func (h *AuthHandler) VerifyEmail(c *gin.Context) {
	var req models.VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid email verification request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	err := h.authUseCase.VerifyEmail(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Email verification failed")

		switch err.Error() {
		case "INVALID_TOKEN":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid or expired verification token",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "VERIFICATION_FAILED",
				Message: "Failed to verify email",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Email verified successfully",
	})
}

// ResendVerification godoc
// @Summary Resend email verification
// @Description Resend email verification link to the user. For security, the response is the same whether the email exists or not.
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.ResendVerificationRequest true "Email address for verification resend"
// @Success 200 {object} models.SuccessResponse "Verification email sent (if email exists)"
// @Failure 400 {object} models.ErrorResponse "Invalid request format"
// @Failure 501 {object} models.ErrorResponse "Feature not implemented"
// @Router /api/v1/auth/resend-verification [post]
func (h *AuthHandler) ResendVerification(c *gin.Context) {
	// TODO: Implement ResendVerification in auth usecase
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Resend verification not implemented yet",
	})
}

// GetAuthAnalytics godoc
// @Summary Get authentication analytics
// @Description Get detailed authentication analytics including login patterns, failed attempts, and user registration trends. Admin access required.
// @Tags admin,analytics,auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param period query string false "Time period for analytics" Enums(day,week,month,year) default(week)
// @Param start_date query string false "Start date (YYYY-MM-DD)" format(date)
// @Param end_date query string false "End date (YYYY-MM-DD)" format(date)
// @Success 200 {object} models.AuthAnalyticsResponse "Authentication analytics data"
// @Failure 400 {object} models.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 501 {object} models.ErrorResponse "Feature not implemented"
// @Router /admin/v1/analytics/auth [get]
func (h *AuthHandler) GetAuthAnalytics(c *gin.Context) {
	// TODO: Implement GetAuthAnalytics in auth usecase
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Auth analytics not implemented yet",
	})
}

package cleanup

import (
	"context"
	"fmt"
	"time"

	"github.com/social-content-ai/asset-service/ent"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/pkg/storage"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Config holds cleanup service configuration
type Config struct {
	// Upload cleanup settings
	PendingUploadTTL     time.Duration // Time to live for pending uploads (default: 1h)
	OrphanedFileTTL      time.Duration // Time to live for orphaned files (default: 24h)
	DeletedAssetTTL      time.Duration // Time to live for deleted assets (default: 30d)
	
	// Cleanup intervals
	CleanupInterval      time.Duration // How often to run cleanup (default: 30m)
	BatchSize           int           // Number of assets to process in one batch (default: 100)
	
	// Storage settings
	EnableStorageCleanup bool          // Whether to cleanup storage files
	DryRun              bool          // Whether to run in dry-run mode (no actual deletion)
}

// DefaultConfig returns default cleanup configuration
func DefaultConfig() *Config {
	return &Config{
		PendingUploadTTL:     1 * time.Hour,
		OrphanedFileTTL:      24 * time.Hour,
		DeletedAssetTTL:      30 * 24 * time.Hour, // 30 days
		CleanupInterval:      30 * time.Minute,
		BatchSize:           100,
		EnableStorageCleanup: true,
		DryRun:              false,
	}
}

// Service handles asset cleanup operations
type Service struct {
	config        *Config
	readDB        *ent.Client
	writeDB       *ent.Client
	storageClient storage.Storage
	logger        logging.Logger
}

// NewService creates a new cleanup service
func NewService(
	config *Config,
	readDB, writeDB *ent.Client,
	storageClient storage.Storage,
	logger logging.Logger,
) *Service {
	if config == nil {
		config = DefaultConfig()
	}

	return &Service{
		config:        config,
		readDB:        readDB,
		writeDB:       writeDB,
		storageClient: storageClient,
		logger:        logger,
	}
}

// Start starts the cleanup service background worker
func (s *Service) Start(ctx context.Context) error {
	s.logger.WithFields(map[string]interface{}{
		"pending_upload_ttl": s.config.PendingUploadTTL,
		"orphaned_file_ttl":  s.config.OrphanedFileTTL,
		"deleted_asset_ttl":  s.config.DeletedAssetTTL,
		"cleanup_interval":   s.config.CleanupInterval,
		"batch_size":         s.config.BatchSize,
		"dry_run":           s.config.DryRun,
	}).Info("Starting asset cleanup service")

	ticker := time.NewTicker(s.config.CleanupInterval)
	defer ticker.Stop()

	// Run initial cleanup
	if err := s.runCleanup(ctx); err != nil {
		s.logger.WithError(err).Error("Initial cleanup failed")
	}

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Asset cleanup service stopped")
			return ctx.Err()
		case <-ticker.C:
			if err := s.runCleanup(ctx); err != nil {
				s.logger.WithError(err).Error("Cleanup cycle failed")
			}
		}
	}
}

// runCleanup performs the cleanup operation
func (s *Service) runCleanup(ctx context.Context) error {
	s.logger.Debug("Starting asset cleanup cycle")

	var totalCleaned int

	// 1. Cleanup pending uploads older than TTL
	pendingCleaned, err := s.cleanupPendingUploads(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cleanup pending uploads")
	} else {
		totalCleaned += pendingCleaned
	}

	// 2. Cleanup orphaned files
	orphanedCleaned, err := s.cleanupOrphanedFiles(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cleanup orphaned files")
	} else {
		totalCleaned += orphanedCleaned
	}

	// 3. Cleanup deleted assets older than TTL
	deletedCleaned, err := s.cleanupDeletedAssets(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cleanup deleted assets")
	} else {
		totalCleaned += deletedCleaned
	}

	// 4. Cleanup quarantined assets older than TTL
	quarantinedCleaned, err := s.cleanupQuarantinedAssets(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cleanup quarantined assets")
	} else {
		totalCleaned += quarantinedCleaned
	}

	if totalCleaned > 0 {
		s.logger.WithField("total_cleaned", totalCleaned).Info("Asset cleanup cycle completed")
	} else {
		s.logger.Debug("Asset cleanup cycle completed - nothing to clean")
	}

	return nil
}

// cleanupPendingUploads cleans up uploads that are stuck in pending/validating state
func (s *Service) cleanupPendingUploads(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-s.config.PendingUploadTTL)

	// Find assets stuck in pending/validating state
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.Or(
				asset.StatusEQ("pending"),
				asset.StatusEQ("validating"),
				asset.StatusEQ("uploaded"),
			),
			asset.CreatedAtLT(cutoffTime),
			asset.DeletedAtIsNil(),
		).
		Limit(s.config.BatchSize).
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query pending uploads: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		if s.config.DryRun {
			s.logger.WithFields(map[string]interface{}{
				"asset_id": assetEntity.ID.String(),
				"status":   assetEntity.Status,
				"age":      time.Since(assetEntity.CreatedAt),
			}).Info("Would cleanup pending upload (dry-run)")
			cleanedCount++
			continue
		}

		// Check if file exists in storage
		if s.config.EnableStorageCleanup && s.storageClient != nil {
			exists, err := s.storageClient.FileExists(ctx, assetEntity.S3Key)
			if err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Warn("Failed to check file existence")
			} else if exists {
				// Delete file from storage
				if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
					s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete file from storage")
					continue
				}
			}
		}

		// Mark asset as expired
		_, err = s.writeDB.Asset.
			UpdateOneID(assetEntity.ID).
			SetStatus("expired").
			SetUpdatedAt(time.Now()).
			Save(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to mark asset as expired")
			continue
		}

		s.logger.WithFields(map[string]interface{}{
			"asset_id": assetEntity.ID.String(),
			"status":   assetEntity.Status,
			"age":      time.Since(assetEntity.CreatedAt),
		}).Info("Cleaned up pending upload")

		cleanedCount++
	}

	return cleanedCount, nil
}

// cleanupOrphanedFiles cleans up files that exist in storage but have no valid database record
func (s *Service) cleanupOrphanedFiles(ctx context.Context) (int, error) {
	// TODO: Implement orphaned file detection
	// This would require listing files in storage and cross-referencing with database
	// For now, return 0 as placeholder
	return 0, nil
}

// cleanupDeletedAssets permanently removes assets that have been soft-deleted for TTL period
func (s *Service) cleanupDeletedAssets(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-s.config.DeletedAssetTTL)

	// Find soft-deleted assets older than TTL
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.DeletedAtNotNil(),
			asset.DeletedAtLT(cutoffTime),
		).
		Limit(s.config.BatchSize).
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query deleted assets: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		if s.config.DryRun {
			s.logger.WithFields(map[string]interface{}{
				"asset_id":   assetEntity.ID.String(),
				"deleted_at": assetEntity.DeletedAt,
				"age":        time.Since(*assetEntity.DeletedAt),
			}).Info("Would permanently delete asset (dry-run)")
			cleanedCount++
			continue
		}

		// Delete file from storage
		if s.config.EnableStorageCleanup && s.storageClient != nil {
			if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete file from storage")
			}
		}

		// Hard delete from database
		err = s.writeDB.Asset.DeleteOneID(assetEntity.ID).Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to hard delete asset")
			continue
		}

		s.logger.WithFields(map[string]interface{}{
			"asset_id":   assetEntity.ID.String(),
			"deleted_at": assetEntity.DeletedAt,
			"age":        time.Since(*assetEntity.DeletedAt),
		}).Info("Permanently deleted asset")

		cleanedCount++
	}

	return cleanedCount, nil
}

// cleanupQuarantinedAssets cleans up quarantined assets older than TTL
func (s *Service) cleanupQuarantinedAssets(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-s.config.DeletedAssetTTL) // Use same TTL as deleted assets

	// Find quarantined assets older than TTL
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.StatusEQ("quarantined"),
			asset.CreatedAtLT(cutoffTime),
			asset.DeletedAtIsNil(),
		).
		Limit(s.config.BatchSize).
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query quarantined assets: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		if s.config.DryRun {
			s.logger.WithFields(map[string]interface{}{
				"asset_id": assetEntity.ID.String(),
				"age":      time.Since(assetEntity.CreatedAt),
			}).Info("Would delete quarantined asset (dry-run)")
			cleanedCount++
			continue
		}

		// Delete file from storage
		if s.config.EnableStorageCleanup && s.storageClient != nil {
			if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete quarantined file from storage")
			}
		}

		// Hard delete quarantined asset
		err = s.writeDB.Asset.DeleteOneID(assetEntity.ID).Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete quarantined asset")
			continue
		}

		s.logger.WithFields(map[string]interface{}{
			"asset_id": assetEntity.ID.String(),
			"age":      time.Since(assetEntity.CreatedAt),
		}).Info("Deleted quarantined asset")

		cleanedCount++
	}

	return cleanedCount, nil
}

package models

import "time"

// PurchaseCreditsRequest represents purchase credits request
type PurchaseCreditsRequest struct {
	UserID         string                 `json:"user_id"`
	PackageID      string                 `json:"package_id" validate:"required"`
	PaymentMethod  string                 `json:"payment_method" validate:"required"`
	BillingAddress *BillingAddress        `json:"billing_address,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// PurchaseCreditsResponse represents purchase credits response
type PurchaseCreditsResponse struct {
	OrderID    string     `json:"order_id"`
	PaymentURL string     `json:"payment_url,omitempty"`
	Amount     int64      `json:"amount"` // in cents
	Currency   string     `json:"currency"`
	Status     string     `json:"status"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty"`
}

// CreditPackage represents a credit package
type CreditPackage struct {
	BaseModel
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Credits     int                    `json:"credits"`
	Price       int64                  `json:"price"` // in cents
	Currency    string                 `json:"currency"`
	Discount    int                    `json:"discount,omitempty"` // percentage
	Popular     bool                   `json:"popular"`
	Active      bool                   `json:"active"`
	Features    []string               `json:"features,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ValidUntil  *time.Time             `json:"valid_until,omitempty"`
}

// CreditPackagesResponse represents credit packages response
type CreditPackagesResponse struct {
	Packages []CreditPackage `json:"packages"`
	Currency string          `json:"currency"`
}

// Order represents an order
type Order struct {
	BaseModel
	OrderNumber    string                 `json:"order_number"`
	UserID         string                 `json:"user_id"`
	PackageID      string                 `json:"package_id,omitempty"`
	PlanID         string                 `json:"plan_id,omitempty"`
	Type           string                 `json:"type"`   // credit_purchase, subscription, upgrade, etc.
	Amount         int64                  `json:"amount"` // in cents
	Currency       string                 `json:"currency"`
	Status         string                 `json:"status"` // pending, processing, completed, failed, cancelled, refunded
	PaymentMethod  string                 `json:"payment_method"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	InvoiceID      string                 `json:"invoice_id,omitempty"`
	Credits        int                    `json:"credits,omitempty"`
	BillingAddress *BillingAddress        `json:"billing_address,omitempty"`
	Items          []OrderItem            `json:"items"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	ProcessedAt    *time.Time             `json:"processed_at,omitempty"`
	ExpiresAt      *time.Time             `json:"expires_at,omitempty"`
}

// OrderItem represents an order item
type OrderItem struct {
	Type        string                 `json:"type"` // credits, subscription, addon
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	Quantity    int                    `json:"quantity"`
	UnitPrice   int64                  `json:"unit_price"` // in cents
	Amount      int64                  `json:"amount"`     // in cents
	Credits     int                    `json:"credits,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ListOrdersRequest represents list orders request
type ListOrdersRequest struct {
	UserID    string     `json:"user_id"`
	Status    string     `json:"status,omitempty"`
	Type      string     `json:"type,omitempty"`
	DateFrom  *time.Time `json:"date_from,omitempty"`
	DateTo    *time.Time `json:"date_to,omitempty"`
	Page      int        `json:"page" validate:"min=1"`
	Limit     int        `json:"limit" validate:"min=1,max=100"`
	SortBy    string     `json:"sort_by,omitempty"`
	SortOrder string     `json:"sort_order,omitempty"`
}

// ListOrdersResponse represents list orders response
type ListOrdersResponse struct {
	Orders     []Order        `json:"orders"`
	Pagination PaginationMeta `json:"pagination"`
	Summary    OrderSummary   `json:"summary"`
}

// OrderSummary represents order summary
type OrderSummary struct {
	TotalOrders     int            `json:"total_orders"`
	TotalAmount     int64          `json:"total_amount"`
	TotalCredits    int            `json:"total_credits"`
	ByStatus        map[string]int `json:"by_status"`
	ByType          map[string]int `json:"by_type"`
	ByPaymentMethod map[string]int `json:"by_payment_method"`
}

// CancelOrderRequest represents cancel order request
type CancelOrderRequest struct {
	OrderID string `json:"order_id" validate:"required"`
	UserID  string `json:"user_id" validate:"required"`
	Reason  string `json:"reason,omitempty"`
}

// RefundOrderRequest represents refund order request
type RefundOrderRequest struct {
	OrderID  string                 `json:"order_id" validate:"required"`
	Amount   int64                  `json:"amount,omitempty"` // partial refund amount in cents
	Reason   string                 `json:"reason" validate:"required"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentWebhookRequest represents payment webhook request
type PaymentWebhookRequest struct {
	Provider  string                 `json:"provider"` // stripe, momo, zalopay, bank_transfer
	Event     string                 `json:"event"`    // payment.succeeded, payment.failed, etc.
	PaymentID string                 `json:"payment_id"`
	OrderID   string                 `json:"order_id,omitempty"`
	Amount    int64                  `json:"amount,omitempty"`
	Currency  string                 `json:"currency,omitempty"`
	Status    string                 `json:"status"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	RawData   map[string]interface{} `json:"raw_data,omitempty"`
}

// RevenueAnalyticsResponse represents revenue analytics
type RevenueAnalyticsResponse struct {
	Period            string                 `json:"period"`
	TotalRevenue      int64                  `json:"total_revenue"` // in cents
	TotalOrders       int                    `json:"total_orders"`
	AverageOrderValue int64                  `json:"average_order_value"` // in cents
	Timeline          []RevenueTimelinePoint `json:"timeline"`
	RevenueByMethod   map[string]int64       `json:"revenue_by_method"`
	RevenueByPackage  map[string]int64       `json:"revenue_by_package"`
	TopPackages       []PackageRevenue       `json:"top_packages"`
	ConversionRate    float64                `json:"conversion_rate"`
	RefundRate        float64                `json:"refund_rate"`
}

// RevenueTimelinePoint represents a point in revenue timeline
type RevenueTimelinePoint struct {
	Date    string `json:"date"`
	Revenue int64  `json:"revenue"`
	Orders  int    `json:"orders"`
	Refunds int64  `json:"refunds"`
}

// PackageRevenue represents package revenue
type PackageRevenue struct {
	PackageID   string `json:"package_id"`
	PackageName string `json:"package_name"`
	Revenue     int64  `json:"revenue"`
	Orders      int    `json:"orders"`
	Credits     int    `json:"credits"`
}

// BankTransferInfo represents bank transfer information
type BankTransferInfo struct {
	BankName      string    `json:"bank_name"`
	AccountNumber string    `json:"account_number"`
	AccountName   string    `json:"account_name"`
	TransferCode  string    `json:"transfer_code"`
	Amount        int64     `json:"amount"`
	Currency      string    `json:"currency"`
	Instructions  string    `json:"instructions"`
	ExpiresAt     time.Time `json:"expires_at"`
}

// MomoPaymentInfo represents MoMo payment information
type MomoPaymentInfo struct {
	PartnerCode string `json:"partner_code"`
	RequestID   string `json:"request_id"`
	OrderID     string `json:"order_id"`
	Amount      int64  `json:"amount"`
	PayURL      string `json:"pay_url"`
	DeepLink    string `json:"deep_link,omitempty"`
	QRCodeURL   string `json:"qr_code_url,omitempty"`
}

// ZaloPayPaymentInfo represents ZaloPay payment information
type ZaloPayPaymentInfo struct {
	AppID      string `json:"app_id"`
	AppTransID string `json:"app_trans_id"`
	OrderURL   string `json:"order_url"`
	ZPTransID  string `json:"zp_trans_id,omitempty"`
	QRCode     string `json:"qr_code,omitempty"`
}

// StripePaymentInfo represents Stripe payment information
type StripePaymentInfo struct {
	PaymentIntentID    string   `json:"payment_intent_id"`
	ClientSecret       string   `json:"client_secret"`
	PublishableKey     string   `json:"publishable_key"`
	PaymentMethodTypes []string `json:"payment_method_types"`
}

// PaymentStatusResponse represents payment status response
type PaymentStatusResponse struct {
	OrderID       string                 `json:"order_id"`
	PaymentID     string                 `json:"payment_id"`
	Status        string                 `json:"status"`
	Amount        int64                  `json:"amount"`
	Currency      string                 `json:"currency"`
	PaymentMethod string                 `json:"payment_method"`
	ProcessedAt   *time.Time             `json:"processed_at,omitempty"`
	FailureReason string                 `json:"failure_reason,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// ExportTransactionsRequest represents export transactions request
type ExportTransactionsRequest struct {
	UserID   string     `json:"user_id"`
	Format   string     `json:"format" validate:"required,oneof=csv xlsx pdf"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Type     string     `json:"type,omitempty"`
	Status   string     `json:"status,omitempty"`
}

// ExportTransactionsResponse represents export transactions response
type ExportTransactionsResponse struct {
	FileURL   string    `json:"file_url"`
	FileName  string    `json:"file_name"`
	FileSize  int64     `json:"file_size"`
	ExpiresAt time.Time `json:"expires_at"`
	Format    string    `json:"format"`
}

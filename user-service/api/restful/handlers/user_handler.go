package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/usecase/user"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userUseCase user.UseCase
	logger      logging.Logger
}

// NewUserHandler creates a new user handler
func NewU<PERSON><PERSON>andler(userUseCase user.UseCase, logger logging.Logger) *UserHandler {
	return &UserHandler{
		userUseCase: userUseCase,
		logger:      logger,
	}
}

// GetCurrentUser godoc
// @Summary Get current user
// @Description Get detailed information about the currently authenticated user including profile data and settings.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.UserResponse "User information retrieved successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me [get]
func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	user, err := h.userUseCase.GetUserByID(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get current user")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "USER_NOT_FOUND",
			Message: "Failed to get user information",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateCurrentUser godoc
// @Summary Update current user
// @Description Update profile information for the currently authenticated user. Only provided fields will be updated.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UpdateUserRequest true "User profile update data"
// @Success 200 {object} models.UserResponse "User successfully updated"
// @Failure 400 {object} models.ErrorResponse "Invalid request data"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me [put]
func (h *UserHandler) UpdateCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update user request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = userID.(uint64)

	user, err := h.userUseCase.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: "Failed to update user",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteCurrentUser godoc
// @Summary Delete current user
// @Description Permanently delete the current authenticated user account. This action cannot be undone.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "User account successfully deleted"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me [delete]
func (h *UserHandler) DeleteCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	err := h.userUseCase.DeleteUser(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete user")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "DELETE_FAILED",
			Message: "Failed to delete user",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "User deleted successfully",
	})
}

// ChangePassword godoc
// @Summary Change password
// @Description Change the current user's password. Requires current password for verification and new password must meet security requirements.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ChangePasswordRequest true "Current and new password"
// @Success 200 {object} models.SuccessResponse "Password successfully changed"
// @Failure 400 {object} models.ErrorResponse "Invalid current password or weak new password"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/change-password [post]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid change password request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(uint64)

	err := h.userUseCase.ChangePassword(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to change password")

		switch err.Error() {
		case "INVALID_CURRENT_PASSWORD":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_CURRENT_PASSWORD",
				Message: "Current password is incorrect",
			})
		case "WEAK_PASSWORD":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "WEAK_PASSWORD",
				Message: "New password does not meet security requirements",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "PASSWORD_CHANGE_FAILED",
				Message: "Failed to change password",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Password changed successfully",
	})
}

// RequestAvatarUpload godoc
// @Summary Request avatar upload URL
// @Description Request a presigned URL for uploading avatar image via Asset Service. Returns upload URL and instructions.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.RequestAvatarUploadRequest true "Avatar upload request"
// @Success 200 {object} models.RequestAvatarUploadResponse "Upload URL generated successfully"
// @Failure 400 {object} models.ErrorResponse "Invalid request data"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/avatar/upload-url [post]
func (h *UserHandler) RequestAvatarUpload(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.RequestAvatarUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid avatar upload request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(uint64)

	resp, err := h.userUseCase.RequestAvatarUpload(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to request avatar upload")

		switch err.Error() {
		case "INVALID_FILE_TYPE":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_FILE_TYPE",
				Message: "Only image files are allowed",
			})
		case "FILE_TOO_LARGE":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "FILE_TOO_LARGE",
				Message: "File size exceeds maximum limit",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REQUEST_UPLOAD_FAILED",
				Message: "Failed to request avatar upload",
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ListUsers godoc
// @Summary List users
// @Description Get a paginated list of all users with optional search and filtering. Admin access required.
// @Tags admin,users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Param search query string false "Search term (name, email)"
// @Param status query string false "User status filter" Enums(active,inactive,locked,pending)
// @Param role query string false "User role filter" Enums(user,admin,moderator)
// @Success 200 {object} models.ListUsersResponse "Users retrieved successfully"
// @Failure 400 {object} models.ErrorResponse "Invalid query parameters"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	req := &models.ListUsersRequest{
		Page:   page,
		Limit:  limit,
		Search: search,
		Status: status,
	}

	resp, err := h.userUseCase.ListUsers(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list users",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetUser godoc
// @Summary Get user by ID
// @Description Get user information by ID (admin only)
// @Tags users
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	user, err := h.userUseCase.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_USER_FAILED",
				Message: "Failed to get user",
			})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUser godoc
// @Summary Update user
// @Description Update user information (admin only)
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Param request body models.UpdateUserRequest true "Update user request"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update user request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = userID

	user, err := h.userUseCase.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update user")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UPDATE_FAILED",
				Message: "Failed to update user",
			})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUser godoc
// @Summary Delete user
// @Description Delete user account (admin only)
// @Tags users
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	err = h.userUseCase.DeleteUser(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete user")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DELETE_FAILED",
				Message: "Failed to delete user",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "User deleted successfully",
	})
}

// LockUser godoc
// @Summary Lock user account
// @Description Lock user account (admin only)
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Param request body models.LockUserRequest true "Lock user request"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id}/lock [post]
func (h *UserHandler) LockUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	var req models.LockUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid lock user request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID

	err = h.userUseCase.LockUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to lock user")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "LOCK_FAILED",
				Message: "Failed to lock user",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "User locked successfully",
	})
}

// UnlockUser godoc
// @Summary Unlock user account
// @Description Unlock user account (admin only)
// @Tags users
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id}/unlock [post]
func (h *UserHandler) UnlockUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	err = h.userUseCase.UnlockUser(c.Request.Context(), userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to unlock user")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UNLOCK_FAILED",
				Message: "Failed to unlock user",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "User unlocked successfully",
	})
}

// AdminResetPassword godoc
// @Summary Admin reset user password
// @Description Reset user password (admin only)
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Param request body models.ResetPasswordRequest true "Reset password request"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/users/{id}/reset-password [post]
func (h *UserHandler) AdminResetPassword(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "Invalid user ID",
		})
		return
	}

	var req models.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid reset password request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	err = h.userUseCase.AdminResetPassword(c.Request.Context(), userID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to reset password")

		switch err.Error() {
		case "USER_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "USER_NOT_FOUND",
				Message: "User not found",
			})
		case "WEAK_PASSWORD":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "WEAK_PASSWORD",
				Message: "Password does not meet security requirements",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "RESET_FAILED",
				Message: "Failed to reset password",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Password reset successfully",
	})
}

// GetProfile godoc
// @Summary Get user profile
// @Description Get current user profile information
// @Tags profile
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.UserResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	user, err := h.userUseCase.GetUserByID(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user profile")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "PROFILE_NOT_FOUND",
			Message: "Failed to get user profile",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateProfile godoc
// @Summary Update user profile
// @Description Update current user profile information
// @Tags profile
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UpdateUserRequest true "Update profile request"
// @Success 200 {object} models.UserResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update profile request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = userID.(uint64)

	user, err := h.userUseCase.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update profile")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "UPDATE_FAILED",
			Message: "Failed to update profile",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}

// ConfirmAvatarUpload godoc
// @Summary Confirm avatar upload
// @Description Confirm avatar upload completion after uploading to S3 via presigned URL.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ConfirmAvatarUploadRequest true "Avatar upload confirmation"
// @Success 200 {object} models.ConfirmAvatarUploadResponse "Avatar upload confirmed successfully"
// @Failure 400 {object} models.ErrorResponse "Invalid request data"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/avatar/confirm [post]
func (h *UserHandler) ConfirmAvatarUpload(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.ConfirmAvatarUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid confirm avatar upload request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(uint64)

	resp, err := h.userUseCase.ConfirmAvatarUpload(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm avatar upload")

		switch err.Error() {
		case "UPLOAD_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "UPLOAD_NOT_FOUND",
				Message: "Upload not found or expired",
			})
		case "VALIDATION_FAILED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "VALIDATION_FAILED",
				Message: "Avatar validation failed",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "CONFIRM_UPLOAD_FAILED",
				Message: "Failed to confirm avatar upload",
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetCurrentAvatar godoc
// @Summary Get current user avatar
// @Description Get current user's avatar information and download URL.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.GetAvatarResponse "Avatar information retrieved successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 404 {object} models.ErrorResponse "Avatar not found"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/avatar [get]
func (h *UserHandler) GetCurrentAvatar(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	resp, err := h.userUseCase.GetCurrentAvatar(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get current avatar")

		switch err.Error() {
		case "AVATAR_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "AVATAR_NOT_FOUND",
				Message: "User has no avatar",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_AVATAR_FAILED",
				Message: "Failed to get avatar",
			})
		}
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteCurrentAvatar godoc
// @Summary Delete current user avatar
// @Description Delete current user's avatar from Asset Service and S3.
// @Tags users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "Avatar deleted successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 404 {object} models.ErrorResponse "Avatar not found"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/users/me/avatar [delete]
func (h *UserHandler) DeleteCurrentAvatar(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	err := h.userUseCase.DeleteCurrentAvatar(c.Request.Context(), userID.(uint64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete current avatar")

		switch err.Error() {
		case "AVATAR_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "AVATAR_NOT_FOUND",
				Message: "User has no avatar to delete",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DELETE_AVATAR_FAILED",
				Message: "Failed to delete avatar",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Avatar deleted successfully",
	})
}

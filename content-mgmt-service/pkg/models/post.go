package models

import "time"

// PostResponse represents a post response
type PostResponse struct {
	BaseModel
	UserID       string             `json:"user_id"`
	WorkspaceID  string             `json:"workspace_id,omitempty"`
	Title        string             `json:"title"`
	Content      string             `json:"content"`
	Type         string             `json:"type"`         // manual, ai-generated
	Status       string             `json:"status"`       // draft, scheduled, published, failed
	Platforms    []string           `json:"platforms"`
	MediaAssets  []MediaAsset       `json:"media_assets,omitempty"`
	Tags         []string           `json:"tags,omitempty"`
	Hashtags     []string           `json:"hashtags,omitempty"`
	ScheduledAt  *time.Time         `json:"scheduled_at,omitempty"`
	PublishedAt  *time.Time         `json:"published_at,omitempty"`
	PublishStatus []PublishStatus   `json:"publish_status,omitempty"`
	Analytics    *Analytics         `json:"analytics,omitempty"`
	AIGeneration *AIGenerationInfo  `json:"ai_generation,omitempty"`
	Variations   []ContentVariation `json:"variations,omitempty"`
	TemplateID   string             `json:"template_id,omitempty"`
}

// CreatePostRequest represents create post request
type CreatePostRequest struct {
	UserID      string             `json:"user_id"`
	WorkspaceID string             `json:"workspace_id,omitempty"`
	Title       string             `json:"title" validate:"required,min=1,max=200"`
	Content     string             `json:"content" validate:"required,min=1,max=10000"`
	Type        string             `json:"type" validate:"required,oneof=manual ai-generated"`
	Platforms   []string           `json:"platforms" validate:"required,min=1"`
	MediaAssets []MediaAsset       `json:"media_assets,omitempty"`
	Tags        []string           `json:"tags,omitempty"`
	Hashtags    []string           `json:"hashtags,omitempty"`
	Variations  []ContentVariation `json:"variations,omitempty"`
	TemplateID  string             `json:"template_id,omitempty"`
	AIGeneration *AIGenerationInfo `json:"ai_generation,omitempty"`
}

// UpdatePostRequest represents update post request
type UpdatePostRequest struct {
	ID          string             `json:"id"`
	UserID      string             `json:"user_id"`
	Title       string             `json:"title,omitempty"`
	Content     string             `json:"content,omitempty"`
	Platforms   []string           `json:"platforms,omitempty"`
	MediaAssets []MediaAsset       `json:"media_assets,omitempty"`
	Tags        []string           `json:"tags,omitempty"`
	Hashtags    []string           `json:"hashtags,omitempty"`
	Variations  []ContentVariation `json:"variations,omitempty"`
}

// ListPostsRequest represents list posts request
type ListPostsRequest struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	Page        int    `json:"page" validate:"min=1"`
	Limit       int    `json:"limit" validate:"min=1,max=100"`
	Status      string `json:"status,omitempty"`
	Type        string `json:"type,omitempty"`
	Platform    string `json:"platform,omitempty"`
	Search      string `json:"search,omitempty"`
	SortBy      string `json:"sort_by,omitempty"`
	SortOrder   string `json:"sort_order,omitempty"`
	DateFrom    string `json:"date_from,omitempty"`
	DateTo      string `json:"date_to,omitempty"`
}

// ListPostsResponse represents list posts response
type ListPostsResponse struct {
	Posts      []PostResponse `json:"posts"`
	Pagination PaginationMeta `json:"pagination"`
}

// SchedulePostRequest represents schedule post request
type SchedulePostRequest struct {
	PostID      string           `json:"post_id"`
	UserID      string           `json:"user_id"`
	ScheduledAt time.Time        `json:"scheduled_at" validate:"required"`
	Timezone    string           `json:"timezone,omitempty"`
	AutoPublish bool             `json:"auto_publish"`
	OptimalTime bool             `json:"optimal_time"`
	Platforms   []string         `json:"platforms,omitempty"`
}

// PublishPostRequest represents publish post request
type PublishPostRequest struct {
	PostID    string   `json:"post_id"`
	UserID    string   `json:"user_id"`
	Platforms []string `json:"platforms,omitempty"`
	Immediate bool     `json:"immediate"`
}

// PostAnalyticsRequest represents post analytics request
type PostAnalyticsRequest struct {
	PostID   string `json:"post_id"`
	UserID   string `json:"user_id"`
	Platform string `json:"platform,omitempty"`
	DateFrom string `json:"date_from,omitempty"`
	DateTo   string `json:"date_to,omitempty"`
}

// PostAnalyticsResponse represents post analytics response
type PostAnalyticsResponse struct {
	PostID      string                    `json:"post_id"`
	Overall     Analytics                 `json:"overall"`
	ByPlatform  map[string]Analytics      `json:"by_platform"`
	Timeline    []TimelineAnalytics       `json:"timeline"`
	Demographics map[string]interface{}   `json:"demographics,omitempty"`
	TopComments []Comment                 `json:"top_comments,omitempty"`
}

// TimelineAnalytics represents analytics over time
type TimelineAnalytics struct {
	Date      string    `json:"date"`
	Analytics Analytics `json:"analytics"`
}

// Comment represents a comment on a post
type Comment struct {
	ID        string    `json:"id"`
	Platform  string    `json:"platform"`
	Author    string    `json:"author"`
	Content   string    `json:"content"`
	Likes     int64     `json:"likes"`
	CreatedAt time.Time `json:"created_at"`
}

// BulkScheduleRequest represents bulk schedule request
type BulkScheduleRequest struct {
	UserID      string                `json:"user_id"`
	WorkspaceID string                `json:"workspace_id,omitempty"`
	Posts       []BulkSchedulePost    `json:"posts" validate:"required,min=1,max=50"`
	Settings    ScheduleSettings      `json:"settings"`
}

// BulkSchedulePost represents a post in bulk schedule
type BulkSchedulePost struct {
	PostID      string    `json:"post_id" validate:"required"`
	ScheduledAt time.Time `json:"scheduled_at" validate:"required"`
	Platforms   []string  `json:"platforms,omitempty"`
}

// BulkScheduleResponse represents bulk schedule response
type BulkScheduleResponse struct {
	BulkOperationResult
	ScheduledPosts []PostResponse `json:"scheduled_posts"`
}

// PostStatusResponse represents post status response
type PostStatusResponse struct {
	PostID        string          `json:"post_id"`
	Status        string          `json:"status"`
	PublishStatus []PublishStatus `json:"publish_status"`
	LastUpdated   time.Time       `json:"last_updated"`
	NextAction    string          `json:"next_action,omitempty"`
	NextActionAt  *time.Time      `json:"next_action_at,omitempty"`
}

// ScheduleCalendarRequest represents schedule calendar request
type ScheduleCalendarRequest struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	Year        int    `json:"year" validate:"required,min=2020,max=2030"`
	Month       int    `json:"month" validate:"required,min=1,max=12"`
	Platform    string `json:"platform,omitempty"`
}

// ScheduleCalendarResponse represents schedule calendar response
type ScheduleCalendarResponse struct {
	Year     int                      `json:"year"`
	Month    int                      `json:"month"`
	Calendar []ScheduleCalendarDay    `json:"calendar"`
	Summary  ScheduleCalendarSummary  `json:"summary"`
}

// ScheduleCalendarDay represents a day in schedule calendar
type ScheduleCalendarDay struct {
	Date  string         `json:"date"`
	Posts []PostResponse `json:"posts"`
	Count int            `json:"count"`
}

// ScheduleCalendarSummary represents schedule calendar summary
type ScheduleCalendarSummary struct {
	TotalPosts      int                    `json:"total_posts"`
	ByStatus        map[string]int         `json:"by_status"`
	ByPlatform      map[string]int         `json:"by_platform"`
	BusiestDay      string                 `json:"busiest_day"`
	BusiestDayCount int                    `json:"busiest_day_count"`
}

// PostEngagementRequest represents post engagement request
type PostEngagementRequest struct {
	PostID   string `json:"post_id"`
	UserID   string `json:"user_id"`
	Platform string `json:"platform,omitempty"`
	Period   string `json:"period,omitempty"` // hour, day, week, month
}

// PostEngagementResponse represents post engagement response
type PostEngagementResponse struct {
	PostID       string                 `json:"post_id"`
	Platform     string                 `json:"platform,omitempty"`
	Period       string                 `json:"period"`
	Engagement   []EngagementDataPoint  `json:"engagement"`
	Summary      EngagementSummary      `json:"summary"`
	Predictions  []EngagementPrediction `json:"predictions,omitempty"`
}

// EngagementDataPoint represents a single engagement data point
type EngagementDataPoint struct {
	Timestamp   time.Time `json:"timestamp"`
	Views       int64     `json:"views"`
	Likes       int64     `json:"likes"`
	Comments    int64     `json:"comments"`
	Shares      int64     `json:"shares"`
	Engagement  int64     `json:"engagement"`
}

// EngagementSummary represents engagement summary
type EngagementSummary struct {
	TotalEngagement   int64   `json:"total_engagement"`
	EngagementRate    float64 `json:"engagement_rate"`
	PeakEngagement    int64   `json:"peak_engagement"`
	PeakEngagementAt  time.Time `json:"peak_engagement_at"`
	AverageEngagement float64 `json:"average_engagement"`
}

// EngagementPrediction represents engagement prediction
type EngagementPrediction struct {
	Timestamp          time.Time `json:"timestamp"`
	PredictedEngagement int64     `json:"predicted_engagement"`
	Confidence         float64   `json:"confidence"`
}

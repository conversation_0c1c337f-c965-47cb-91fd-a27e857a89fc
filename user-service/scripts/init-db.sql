-- Initialize User Service Database
-- This script is run when PostgreSQL container starts for the first time

-- Create additional databases if needed
-- CREATE DATABASE user_service_test;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE user_service_dev TO postgres;

-- Set timezone
SET timezone = 'UTC';

-- Create schema if needed (Ent will handle table creation)
-- Tables will be created automatically by Ent migrations

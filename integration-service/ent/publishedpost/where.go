// Code generated by ent, DO NOT EDIT.

package publishedpost

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldUserID, v))
}

// PostID applies equality check predicate on the "post_id" field. It's identical to PostIDEQ.
func PostID(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPostID, v))
}

// IntegrationID applies equality check predicate on the "integration_id" field. It's identical to IntegrationIDEQ.
func IntegrationID(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldIntegrationID, v))
}

// PlatformPostID applies equality check predicate on the "platform_post_id" field. It's identical to PlatformPostIDEQ.
func PlatformPostID(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPlatformPostID, v))
}

// PlatformPostURL applies equality check predicate on the "platform_post_url" field. It's identical to PlatformPostURLEQ.
func PlatformPostURL(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPlatformPostURL, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldContent, v))
}

// ErrorMessage applies equality check predicate on the "error_message" field. It's identical to ErrorMessageEQ.
func ErrorMessage(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldErrorMessage, v))
}

// ScheduledAt applies equality check predicate on the "scheduled_at" field. It's identical to ScheduledAtEQ.
func ScheduledAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldScheduledAt, v))
}

// PublishedAt applies equality check predicate on the "published_at" field. It's identical to PublishedAtEQ.
func PublishedAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPublishedAt, v))
}

// LastAnalyticsSync applies equality check predicate on the "last_analytics_sync" field. It's identical to LastAnalyticsSyncEQ.
func LastAnalyticsSync(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldLastAnalyticsSync, v))
}

// RetryCount applies equality check predicate on the "retry_count" field. It's identical to RetryCountEQ.
func RetryCount(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldRetryCount, v))
}

// NextRetryAt applies equality check predicate on the "next_retry_at" field. It's identical to NextRetryAtEQ.
func NextRetryAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldNextRetryAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldUserID, v))
}

// PostIDEQ applies the EQ predicate on the "post_id" field.
func PostIDEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPostID, v))
}

// PostIDNEQ applies the NEQ predicate on the "post_id" field.
func PostIDNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldPostID, v))
}

// PostIDIn applies the In predicate on the "post_id" field.
func PostIDIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldPostID, vs...))
}

// PostIDNotIn applies the NotIn predicate on the "post_id" field.
func PostIDNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldPostID, vs...))
}

// PostIDGT applies the GT predicate on the "post_id" field.
func PostIDGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldPostID, v))
}

// PostIDGTE applies the GTE predicate on the "post_id" field.
func PostIDGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldPostID, v))
}

// PostIDLT applies the LT predicate on the "post_id" field.
func PostIDLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldPostID, v))
}

// PostIDLTE applies the LTE predicate on the "post_id" field.
func PostIDLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldPostID, v))
}

// PostIDContains applies the Contains predicate on the "post_id" field.
func PostIDContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldPostID, v))
}

// PostIDHasPrefix applies the HasPrefix predicate on the "post_id" field.
func PostIDHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldPostID, v))
}

// PostIDHasSuffix applies the HasSuffix predicate on the "post_id" field.
func PostIDHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldPostID, v))
}

// PostIDEqualFold applies the EqualFold predicate on the "post_id" field.
func PostIDEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldPostID, v))
}

// PostIDContainsFold applies the ContainsFold predicate on the "post_id" field.
func PostIDContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldPostID, v))
}

// IntegrationIDEQ applies the EQ predicate on the "integration_id" field.
func IntegrationIDEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldIntegrationID, v))
}

// IntegrationIDNEQ applies the NEQ predicate on the "integration_id" field.
func IntegrationIDNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldIntegrationID, v))
}

// IntegrationIDIn applies the In predicate on the "integration_id" field.
func IntegrationIDIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldIntegrationID, vs...))
}

// IntegrationIDNotIn applies the NotIn predicate on the "integration_id" field.
func IntegrationIDNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldIntegrationID, vs...))
}

// IntegrationIDGT applies the GT predicate on the "integration_id" field.
func IntegrationIDGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldIntegrationID, v))
}

// IntegrationIDGTE applies the GTE predicate on the "integration_id" field.
func IntegrationIDGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldIntegrationID, v))
}

// IntegrationIDLT applies the LT predicate on the "integration_id" field.
func IntegrationIDLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldIntegrationID, v))
}

// IntegrationIDLTE applies the LTE predicate on the "integration_id" field.
func IntegrationIDLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldIntegrationID, v))
}

// IntegrationIDContains applies the Contains predicate on the "integration_id" field.
func IntegrationIDContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldIntegrationID, v))
}

// IntegrationIDHasPrefix applies the HasPrefix predicate on the "integration_id" field.
func IntegrationIDHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldIntegrationID, v))
}

// IntegrationIDHasSuffix applies the HasSuffix predicate on the "integration_id" field.
func IntegrationIDHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldIntegrationID, v))
}

// IntegrationIDEqualFold applies the EqualFold predicate on the "integration_id" field.
func IntegrationIDEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldIntegrationID, v))
}

// IntegrationIDContainsFold applies the ContainsFold predicate on the "integration_id" field.
func IntegrationIDContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldIntegrationID, v))
}

// PlatformEQ applies the EQ predicate on the "platform" field.
func PlatformEQ(v Platform) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPlatform, v))
}

// PlatformNEQ applies the NEQ predicate on the "platform" field.
func PlatformNEQ(v Platform) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldPlatform, v))
}

// PlatformIn applies the In predicate on the "platform" field.
func PlatformIn(vs ...Platform) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldPlatform, vs...))
}

// PlatformNotIn applies the NotIn predicate on the "platform" field.
func PlatformNotIn(vs ...Platform) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldPlatform, vs...))
}

// PlatformPostIDEQ applies the EQ predicate on the "platform_post_id" field.
func PlatformPostIDEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPlatformPostID, v))
}

// PlatformPostIDNEQ applies the NEQ predicate on the "platform_post_id" field.
func PlatformPostIDNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldPlatformPostID, v))
}

// PlatformPostIDIn applies the In predicate on the "platform_post_id" field.
func PlatformPostIDIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldPlatformPostID, vs...))
}

// PlatformPostIDNotIn applies the NotIn predicate on the "platform_post_id" field.
func PlatformPostIDNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldPlatformPostID, vs...))
}

// PlatformPostIDGT applies the GT predicate on the "platform_post_id" field.
func PlatformPostIDGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldPlatformPostID, v))
}

// PlatformPostIDGTE applies the GTE predicate on the "platform_post_id" field.
func PlatformPostIDGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldPlatformPostID, v))
}

// PlatformPostIDLT applies the LT predicate on the "platform_post_id" field.
func PlatformPostIDLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldPlatformPostID, v))
}

// PlatformPostIDLTE applies the LTE predicate on the "platform_post_id" field.
func PlatformPostIDLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldPlatformPostID, v))
}

// PlatformPostIDContains applies the Contains predicate on the "platform_post_id" field.
func PlatformPostIDContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldPlatformPostID, v))
}

// PlatformPostIDHasPrefix applies the HasPrefix predicate on the "platform_post_id" field.
func PlatformPostIDHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldPlatformPostID, v))
}

// PlatformPostIDHasSuffix applies the HasSuffix predicate on the "platform_post_id" field.
func PlatformPostIDHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldPlatformPostID, v))
}

// PlatformPostIDEqualFold applies the EqualFold predicate on the "platform_post_id" field.
func PlatformPostIDEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldPlatformPostID, v))
}

// PlatformPostIDContainsFold applies the ContainsFold predicate on the "platform_post_id" field.
func PlatformPostIDContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldPlatformPostID, v))
}

// PlatformPostURLEQ applies the EQ predicate on the "platform_post_url" field.
func PlatformPostURLEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPlatformPostURL, v))
}

// PlatformPostURLNEQ applies the NEQ predicate on the "platform_post_url" field.
func PlatformPostURLNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldPlatformPostURL, v))
}

// PlatformPostURLIn applies the In predicate on the "platform_post_url" field.
func PlatformPostURLIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldPlatformPostURL, vs...))
}

// PlatformPostURLNotIn applies the NotIn predicate on the "platform_post_url" field.
func PlatformPostURLNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldPlatformPostURL, vs...))
}

// PlatformPostURLGT applies the GT predicate on the "platform_post_url" field.
func PlatformPostURLGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldPlatformPostURL, v))
}

// PlatformPostURLGTE applies the GTE predicate on the "platform_post_url" field.
func PlatformPostURLGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldPlatformPostURL, v))
}

// PlatformPostURLLT applies the LT predicate on the "platform_post_url" field.
func PlatformPostURLLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldPlatformPostURL, v))
}

// PlatformPostURLLTE applies the LTE predicate on the "platform_post_url" field.
func PlatformPostURLLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldPlatformPostURL, v))
}

// PlatformPostURLContains applies the Contains predicate on the "platform_post_url" field.
func PlatformPostURLContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldPlatformPostURL, v))
}

// PlatformPostURLHasPrefix applies the HasPrefix predicate on the "platform_post_url" field.
func PlatformPostURLHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldPlatformPostURL, v))
}

// PlatformPostURLHasSuffix applies the HasSuffix predicate on the "platform_post_url" field.
func PlatformPostURLHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldPlatformPostURL, v))
}

// PlatformPostURLIsNil applies the IsNil predicate on the "platform_post_url" field.
func PlatformPostURLIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldPlatformPostURL))
}

// PlatformPostURLNotNil applies the NotNil predicate on the "platform_post_url" field.
func PlatformPostURLNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldPlatformPostURL))
}

// PlatformPostURLEqualFold applies the EqualFold predicate on the "platform_post_url" field.
func PlatformPostURLEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldPlatformPostURL, v))
}

// PlatformPostURLContainsFold applies the ContainsFold predicate on the "platform_post_url" field.
func PlatformPostURLContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldPlatformPostURL, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldContent, v))
}

// MediaUrlsIsNil applies the IsNil predicate on the "media_urls" field.
func MediaUrlsIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldMediaUrls))
}

// MediaUrlsNotNil applies the NotNil predicate on the "media_urls" field.
func MediaUrlsNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldMediaUrls))
}

// HashtagsIsNil applies the IsNil predicate on the "hashtags" field.
func HashtagsIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldHashtags))
}

// HashtagsNotNil applies the NotNil predicate on the "hashtags" field.
func HashtagsNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldHashtags))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldStatus, vs...))
}

// ErrorMessageEQ applies the EQ predicate on the "error_message" field.
func ErrorMessageEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldErrorMessage, v))
}

// ErrorMessageNEQ applies the NEQ predicate on the "error_message" field.
func ErrorMessageNEQ(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldErrorMessage, v))
}

// ErrorMessageIn applies the In predicate on the "error_message" field.
func ErrorMessageIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldErrorMessage, vs...))
}

// ErrorMessageNotIn applies the NotIn predicate on the "error_message" field.
func ErrorMessageNotIn(vs ...string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldErrorMessage, vs...))
}

// ErrorMessageGT applies the GT predicate on the "error_message" field.
func ErrorMessageGT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldErrorMessage, v))
}

// ErrorMessageGTE applies the GTE predicate on the "error_message" field.
func ErrorMessageGTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldErrorMessage, v))
}

// ErrorMessageLT applies the LT predicate on the "error_message" field.
func ErrorMessageLT(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldErrorMessage, v))
}

// ErrorMessageLTE applies the LTE predicate on the "error_message" field.
func ErrorMessageLTE(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldErrorMessage, v))
}

// ErrorMessageContains applies the Contains predicate on the "error_message" field.
func ErrorMessageContains(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContains(FieldErrorMessage, v))
}

// ErrorMessageHasPrefix applies the HasPrefix predicate on the "error_message" field.
func ErrorMessageHasPrefix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasPrefix(FieldErrorMessage, v))
}

// ErrorMessageHasSuffix applies the HasSuffix predicate on the "error_message" field.
func ErrorMessageHasSuffix(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldHasSuffix(FieldErrorMessage, v))
}

// ErrorMessageIsNil applies the IsNil predicate on the "error_message" field.
func ErrorMessageIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldErrorMessage))
}

// ErrorMessageNotNil applies the NotNil predicate on the "error_message" field.
func ErrorMessageNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldErrorMessage))
}

// ErrorMessageEqualFold applies the EqualFold predicate on the "error_message" field.
func ErrorMessageEqualFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEqualFold(FieldErrorMessage, v))
}

// ErrorMessageContainsFold applies the ContainsFold predicate on the "error_message" field.
func ErrorMessageContainsFold(v string) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldContainsFold(FieldErrorMessage, v))
}

// ScheduledAtEQ applies the EQ predicate on the "scheduled_at" field.
func ScheduledAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldScheduledAt, v))
}

// ScheduledAtNEQ applies the NEQ predicate on the "scheduled_at" field.
func ScheduledAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldScheduledAt, v))
}

// ScheduledAtIn applies the In predicate on the "scheduled_at" field.
func ScheduledAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldScheduledAt, vs...))
}

// ScheduledAtNotIn applies the NotIn predicate on the "scheduled_at" field.
func ScheduledAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldScheduledAt, vs...))
}

// ScheduledAtGT applies the GT predicate on the "scheduled_at" field.
func ScheduledAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldScheduledAt, v))
}

// ScheduledAtGTE applies the GTE predicate on the "scheduled_at" field.
func ScheduledAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldScheduledAt, v))
}

// ScheduledAtLT applies the LT predicate on the "scheduled_at" field.
func ScheduledAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldScheduledAt, v))
}

// ScheduledAtLTE applies the LTE predicate on the "scheduled_at" field.
func ScheduledAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldScheduledAt, v))
}

// ScheduledAtIsNil applies the IsNil predicate on the "scheduled_at" field.
func ScheduledAtIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldScheduledAt))
}

// ScheduledAtNotNil applies the NotNil predicate on the "scheduled_at" field.
func ScheduledAtNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldScheduledAt))
}

// PublishedAtEQ applies the EQ predicate on the "published_at" field.
func PublishedAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldPublishedAt, v))
}

// PublishedAtNEQ applies the NEQ predicate on the "published_at" field.
func PublishedAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldPublishedAt, v))
}

// PublishedAtIn applies the In predicate on the "published_at" field.
func PublishedAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldPublishedAt, vs...))
}

// PublishedAtNotIn applies the NotIn predicate on the "published_at" field.
func PublishedAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldPublishedAt, vs...))
}

// PublishedAtGT applies the GT predicate on the "published_at" field.
func PublishedAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldPublishedAt, v))
}

// PublishedAtGTE applies the GTE predicate on the "published_at" field.
func PublishedAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldPublishedAt, v))
}

// PublishedAtLT applies the LT predicate on the "published_at" field.
func PublishedAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldPublishedAt, v))
}

// PublishedAtLTE applies the LTE predicate on the "published_at" field.
func PublishedAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldPublishedAt, v))
}

// PublishedAtIsNil applies the IsNil predicate on the "published_at" field.
func PublishedAtIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldPublishedAt))
}

// PublishedAtNotNil applies the NotNil predicate on the "published_at" field.
func PublishedAtNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldPublishedAt))
}

// PlatformResponseIsNil applies the IsNil predicate on the "platform_response" field.
func PlatformResponseIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldPlatformResponse))
}

// PlatformResponseNotNil applies the NotNil predicate on the "platform_response" field.
func PlatformResponseNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldPlatformResponse))
}

// AnalyticsIsNil applies the IsNil predicate on the "analytics" field.
func AnalyticsIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldAnalytics))
}

// AnalyticsNotNil applies the NotNil predicate on the "analytics" field.
func AnalyticsNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldAnalytics))
}

// LastAnalyticsSyncEQ applies the EQ predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncNEQ applies the NEQ predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncIn applies the In predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldLastAnalyticsSync, vs...))
}

// LastAnalyticsSyncNotIn applies the NotIn predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldLastAnalyticsSync, vs...))
}

// LastAnalyticsSyncGT applies the GT predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncGTE applies the GTE predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncLT applies the LT predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncLTE applies the LTE predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldLastAnalyticsSync, v))
}

// LastAnalyticsSyncIsNil applies the IsNil predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldLastAnalyticsSync))
}

// LastAnalyticsSyncNotNil applies the NotNil predicate on the "last_analytics_sync" field.
func LastAnalyticsSyncNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldLastAnalyticsSync))
}

// RetryCountEQ applies the EQ predicate on the "retry_count" field.
func RetryCountEQ(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldRetryCount, v))
}

// RetryCountNEQ applies the NEQ predicate on the "retry_count" field.
func RetryCountNEQ(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldRetryCount, v))
}

// RetryCountIn applies the In predicate on the "retry_count" field.
func RetryCountIn(vs ...int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldRetryCount, vs...))
}

// RetryCountNotIn applies the NotIn predicate on the "retry_count" field.
func RetryCountNotIn(vs ...int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldRetryCount, vs...))
}

// RetryCountGT applies the GT predicate on the "retry_count" field.
func RetryCountGT(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldRetryCount, v))
}

// RetryCountGTE applies the GTE predicate on the "retry_count" field.
func RetryCountGTE(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldRetryCount, v))
}

// RetryCountLT applies the LT predicate on the "retry_count" field.
func RetryCountLT(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldRetryCount, v))
}

// RetryCountLTE applies the LTE predicate on the "retry_count" field.
func RetryCountLTE(v int) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldRetryCount, v))
}

// NextRetryAtEQ applies the EQ predicate on the "next_retry_at" field.
func NextRetryAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldNextRetryAt, v))
}

// NextRetryAtNEQ applies the NEQ predicate on the "next_retry_at" field.
func NextRetryAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldNextRetryAt, v))
}

// NextRetryAtIn applies the In predicate on the "next_retry_at" field.
func NextRetryAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldNextRetryAt, vs...))
}

// NextRetryAtNotIn applies the NotIn predicate on the "next_retry_at" field.
func NextRetryAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldNextRetryAt, vs...))
}

// NextRetryAtGT applies the GT predicate on the "next_retry_at" field.
func NextRetryAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldNextRetryAt, v))
}

// NextRetryAtGTE applies the GTE predicate on the "next_retry_at" field.
func NextRetryAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldNextRetryAt, v))
}

// NextRetryAtLT applies the LT predicate on the "next_retry_at" field.
func NextRetryAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldNextRetryAt, v))
}

// NextRetryAtLTE applies the LTE predicate on the "next_retry_at" field.
func NextRetryAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldNextRetryAt, v))
}

// NextRetryAtIsNil applies the IsNil predicate on the "next_retry_at" field.
func NextRetryAtIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldNextRetryAt))
}

// NextRetryAtNotNil applies the NotNil predicate on the "next_retry_at" field.
func NextRetryAtNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldNextRetryAt))
}

// PublishOptionsIsNil applies the IsNil predicate on the "publish_options" field.
func PublishOptionsIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldPublishOptions))
}

// PublishOptionsNotNil applies the NotNil predicate on the "publish_options" field.
func PublishOptionsNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldPublishOptions))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.PublishedPost {
	return predicate.PublishedPost(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PublishedPost) predicate.PublishedPost {
	return predicate.PublishedPost(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PublishedPost) predicate.PublishedPost {
	return predicate.PublishedPost(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PublishedPost) predicate.PublishedPost {
	return predicate.PublishedPost(sql.NotPredicates(p))
}

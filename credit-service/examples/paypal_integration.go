package main

import (
	"context"
	"fmt"
	"log"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/payment"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Example demonstrating PayPal processor integration with PayPal usecase
func main() {
	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  "debug",
		Format: "text",
	})

	// Create PayPal client configuration
	paypalConfig := paypal.Config{
		ClientID:     "your-paypal-client-id",
		ClientSecret: "your-paypal-client-secret",
		Environment:  "sandbox", // or "live" for production
		Timeout:      30,
	}

	// Create PayPal client
	paypalClient := paypal.NewClient(paypalConfig, logger)

	// Create PayPal usecase (this would normally be injected with dependencies)
	// For this example, we'll create a mock implementation
	paypalUseCase := &mockPayPalUseCase{
		logger: logger,
	}

	// Create payment processor factory with dependencies
	paymentConfig := payment.Config{
		DefaultProcessor: payment.ProcessorTypePayPal,
		PayPal: payment.PayPalConfig{
			ClientID:     paypalConfig.ClientID,
			ClientSecret: paypalConfig.ClientSecret,
			Environment:  paypalConfig.Environment,
			BaseURL:      "https://api.sandbox.paypal.com",
			ReturnURL:    "http://localhost:8080/payment/success",
			CancelURL:    "http://localhost:8080/payment/cancel",
		},
	}

	// Create factory with dependencies
	factory := payment.NewFactoryWithDependencies(
		paymentConfig,
		logger,
		paypalUseCase,
		paypalClient,
	)

	// Create PayPal processor
	processor, err := factory.CreateProcessor(payment.ProcessorTypePayPal)
	if err != nil {
		log.Fatalf("Failed to create PayPal processor: %v", err)
	}

	// Example 1: Process a credit topup payment
	fmt.Println("=== Example 1: Credit Topup Payment ===")
	creditPaymentReq := &payment.PaymentRequest{
		UserID:        "user-123",
		Amount:        1000, // $10.00 in cents
		Currency:      "USD",
		PaymentMethod: "paypal",
		Description:   "Credit topup",
		OrderID:       "order-123",
		Metadata: map[string]interface{}{
			"purpose": "credit_topup",
		},
	}

	paymentResp, err := processor.ProcessPayment(context.Background(), creditPaymentReq)
	if err != nil {
		log.Printf("Failed to process credit payment: %v", err)
	} else {
		fmt.Printf("Credit Payment Response: %+v\n", paymentResp)
	}

	// Example 2: Process a subscription payment
	fmt.Println("\n=== Example 2: Subscription Payment ===")
	subscriptionPaymentReq := &payment.PaymentRequest{
		UserID:        "user-123",
		Amount:        2999, // $29.99 in cents
		Currency:      "USD",
		PaymentMethod: "paypal",
		Description:   "Monthly subscription",
		OrderID:       "order-456",
		Metadata: map[string]interface{}{
			"purpose": "subscription",
			"plan_id": "plan-premium",
		},
	}

	subPaymentResp, err := processor.ProcessPayment(context.Background(), subscriptionPaymentReq)
	if err != nil {
		log.Printf("Failed to process subscription payment: %v", err)
	} else {
		fmt.Printf("Subscription Payment Response: %+v\n", subPaymentResp)
	}

	// Example 3: Get payment status
	fmt.Println("\n=== Example 3: Get Payment Status ===")
	if paymentResp != nil {
		status, err := processor.GetPaymentStatus(context.Background(), paymentResp.PaymentID)
		if err != nil {
			log.Printf("Failed to get payment status: %v", err)
		} else {
			fmt.Printf("Payment Status: %+v\n", status)
		}
	}

	// Example 4: Refund a payment
	fmt.Println("\n=== Example 4: Refund Payment ===")
	if paymentResp != nil {
		refundReq := &payment.RefundRequest{
			PaymentID: paymentResp.PaymentID,
			Amount:    500, // Partial refund of $5.00
			Reason:    "Customer requested refund",
		}

		refundResp, err := processor.RefundPayment(context.Background(), refundReq)
		if err != nil {
			log.Printf("Failed to refund payment: %v", err)
		} else {
			fmt.Printf("Refund Response: %+v\n", refundResp)
		}
	}

	fmt.Println("\n=== PayPal Integration Example Complete ===")
}

// mockPayPalUseCase is a mock implementation of PayPal usecase for demonstration
type mockPayPalUseCase struct {
	logger logging.Logger
}

func (m *mockPayPalUseCase) InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error) {
	m.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Mock: Initiating PayPal payment")

	return &models.InitiatePayPalPaymentResponse{
		Success:       true,
		PaymentID:     "mock-payment-123",
		PayPalOrderID: "mock-paypal-order-456",
		ApprovalURL:   "https://www.sandbox.paypal.com/checkoutnow?token=mock-token",
	}, nil
}

func (m *mockPayPalUseCase) ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Executing PayPal payment")

	return &models.ExecutePayPalPaymentResponse{
		Success:      true,
		PaymentID:    req.PaymentID,
		Status:       "completed",
		Message:      "Payment completed successfully",
		CreditsAdded: 1000,
		Amount:       1000,
		Currency:     "USD",
	}, nil
}

func (m *mockPayPalUseCase) GetPaymentStatus(ctx context.Context, req *models.GetPayPalPaymentStatusRequest) (*models.GetPayPalPaymentStatusResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Getting PayPal payment status")

	return &models.GetPayPalPaymentStatusResponse{
		PaymentID: req.PaymentID,
		OrderID:   "mock-order-123",
		Status:    "completed",
		Amount:    1000,
		Currency:  "USD",
		Purpose:   "credit_topup",
	}, nil
}

func (m *mockPayPalUseCase) RefundPayment(ctx context.Context, req *models.RefundPayPalPaymentRequest) (*models.RefundPayPalPaymentResponse, error) {
	m.logger.WithField("payment_id", req.PaymentID).Info("Mock: Refunding PayPal payment")

	return &models.RefundPayPalPaymentResponse{
		Success:   true,
		RefundID:  "mock-refund-123",
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Message:   "Refund processed successfully",
	}, nil
}

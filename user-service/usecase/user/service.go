package user

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/ent/user"
	"github.com/social-content-ai/user-service/pkg/models"
	"golang.org/x/crypto/bcrypt"
)

// service implements the UseCase interface
type service struct {
	readDB      *ent.Client
	writeDB     *ent.Client
	logger      logging.Logger
	assetClient interface{} // *asset.Client - using interface{} to avoid import issues
}

// NewService creates a new user service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:      readDB,
		writeDB:     writeDB,
		logger:      logger,
		assetClient: nil, // TODO: Initialize asset client when needed
	}
}

// Create<PERSON><PERSON> creates a new user
func (s *service) CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error) {
	// Validate email format
	email := strings.ToLower(strings.TrimSpace(req.Email))
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	// Check if user already exists
	exists, err := s.readDB.User.Query().
		Where(user.Email(email)).
		Where(user.DeletedAtIsNil()).
		Exist(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to check if user exists")
		return nil, fmt.Errorf("failed to check user existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("user with email %s already exists", email)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash password")
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Combine first and last name
	fullName := strings.TrimSpace(req.FirstName + " " + req.LastName)

	// Create user
	userBuilder := s.writeDB.User.Create().
		SetEmail(email).
		SetPasswordHash(string(hashedPassword)).
		SetFullName(fullName).
		SetRole(user.Role(req.Role))

	// Set profile fields if provided
	if req.Profile != nil {
		if req.Profile.Phone != "" {
			userBuilder.SetPhone(req.Profile.Phone)
		}
		if req.Profile.Company != "" {
			userBuilder.SetCompany(req.Profile.Company)
		}
		if req.Profile.Bio != "" {
			userBuilder.SetBio(req.Profile.Bio)
		}
		if req.Profile.JobTitle != "" {
			userBuilder.SetIndustry(req.Profile.JobTitle)
		}
	}

	// Set preferences if provided
	if req.Preferences != nil {
		userBuilder.SetNotificationSettings(req.Preferences)
	}

	newUser, err := userBuilder.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("email", email).Error("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.WithField("user_id", newUser.ID).Info("User created successfully")
	return ToUserResponse(newUser), nil
}

// GetUserByID gets user by ID
func (s *service) GetUserByID(ctx context.Context, id uint64) (*models.UserResponse, error) {
	// Try to find user by converting uint64 to UUID
	userEntity, err := s.findUserByUint64ID(ctx, id)
	if err != nil {
		return nil, err
	}

	return ToUserResponse(userEntity), nil
}

// findUserByUint64ID finds user by uint64 ID (helper method)
func (s *service) findUserByUint64ID(ctx context.Context, id uint64) (*ent.User, error) {
	// First try direct UUID conversion if possible
	if id > 0 {
		// Try to construct UUID from uint64
		// This is a workaround - in production you'd want proper ID mapping
		uuidStr := fmt.Sprintf("%016x-0000-0000-0000-000000000000", id)
		if parsedUUID, err := uuid.Parse(uuidStr); err == nil {
			userEntity, err := s.readDB.User.
				Query().
				Where(user.ID(parsedUUID)).
				Where(user.DeletedAtIsNil()).
				First(ctx)
			if err == nil {
				return userEntity, nil
			}
		}
	}

	// Fallback: query all users and find by ID conversion
	users, err := s.readDB.User.
		Query().
		Where(user.DeletedAtIsNil()).
		All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to query users")
		return nil, fmt.Errorf("failed to query users: %w", err)
	}

	// Find user with matching ID (convert UUID to uint64)
	for _, userEntity := range users {
		if uint64(userEntity.ID.ID()) == id {
			return userEntity, nil
		}
	}

	return nil, fmt.Errorf("user not found")
}

// GetUserByEmail gets user by email
func (s *service) GetUserByEmail(ctx context.Context, email string) (*models.UserResponse, error) {
	email = strings.ToLower(strings.TrimSpace(email))
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	userEntity, err := s.readDB.User.
		Query().
		Where(user.Email(email)).
		Where(user.DeletedAtIsNil()).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("user not found")
		}
		s.logger.WithError(err).WithField("email", email).Error("Failed to get user by email")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return ToUserResponse(userEntity), nil
}

// UpdateUser updates user information
func (s *service) UpdateUser(ctx context.Context, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// Build update query
	updateBuilder := s.writeDB.User.UpdateOneID(targetUser.ID)

	// Update fields if provided
	if req.FirstName != "" || req.LastName != "" {
		fullName := strings.TrimSpace(req.FirstName + " " + req.LastName)
		if fullName != "" {
			updateBuilder.SetFullName(fullName)
		}
	}

	if req.Role != "" {
		updateBuilder.SetRole(user.Role(req.Role))
	}

	// Update profile fields if provided
	if req.Profile != nil {
		if req.Profile.Phone != "" {
			updateBuilder.SetPhone(req.Profile.Phone)
		}
		if req.Profile.Company != "" {
			updateBuilder.SetCompany(req.Profile.Company)
		}
		if req.Profile.Bio != "" {
			updateBuilder.SetBio(req.Profile.Bio)
		}
		if req.Profile.JobTitle != "" {
			updateBuilder.SetIndustry(req.Profile.JobTitle)
		}
	}

	// Update preferences if provided
	if req.Preferences != nil {
		updateBuilder.SetNotificationSettings(req.Preferences)
	}

	updatedUser, err := updateBuilder.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.ID).Error("Failed to update user")
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	s.logger.WithField("user_id", req.ID).Info("User updated successfully")
	return ToUserResponse(updatedUser), nil
}

// DeleteUser deletes a user (soft delete)
func (s *service) DeleteUser(ctx context.Context, id uint64) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, id)
	if err != nil {
		return err
	}

	// Soft delete by setting deleted_at
	now := time.Now()
	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetDeletedAt(now).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", id).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	s.logger.WithField("user_id", id).Info("User deleted successfully")
	return nil
}

// ListUsers lists users with pagination
func (s *service) ListUsers(ctx context.Context, req *models.ListUsersRequest) (*models.ListUsersResponse, error) {
	// Set default pagination
	page := req.Page
	if page < 1 {
		page = 1
	}
	limit := req.Limit
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// Build query
	query := s.readDB.User.Query().Where(user.DeletedAtIsNil())

	// Add filters
	if req.Search != "" {
		query = query.Where(
			user.Or(
				user.FullNameContains(req.Search),
				user.EmailContains(req.Search),
			),
		)
	}

	if req.Role != "" {
		query = query.Where(user.RoleEQ(user.Role(req.Role)))
	}

	// Add sorting
	switch req.SortBy {
	case "email":
		if req.Order == "desc" {
			query = query.Order(ent.Desc(user.FieldEmail))
		} else {
			query = query.Order(ent.Asc(user.FieldEmail))
		}
	case "created_at":
		if req.Order == "desc" {
			query = query.Order(ent.Desc(user.FieldCreatedAt))
		} else {
			query = query.Order(ent.Asc(user.FieldCreatedAt))
		}
	default:
		query = query.Order(ent.Desc(user.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * limit
	users, err := query.Offset(offset).Limit(limit).All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list users")
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response format
	userResponses := make([]models.UserResponse, len(users))
	for i, userEntity := range users {
		if response := ToUserResponse(userEntity); response != nil {
			userResponses[i] = *response
		}
	}

	// Calculate total pages
	totalPages := (total + limit - 1) / limit

	return &models.ListUsersResponse{
		Users: userResponses,
		Pagination: models.PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// LockUser locks a user account
func (s *service) LockUser(ctx context.Context, req *models.LockUserRequest) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, req.UserID)
	if err != nil {
		return err
	}

	// Lock user by storing lock info in notification_settings
	lockedUntil := time.Now().Add(24 * time.Hour) // Default 24 hours lock
	lockInfo := map[string]interface{}{
		"locked":       true,
		"locked_until": lockedUntil.Unix(),
		"lock_reason":  req.Reason,
		"locked_at":    time.Now().Unix(),
	}

	// Merge with existing notification settings
	if targetUser.NotificationSettings != nil {
		for k, v := range targetUser.NotificationSettings {
			lockInfo[k] = v
		}
	}

	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetNotificationSettings(lockInfo).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to lock user")
		return fmt.Errorf("failed to lock user: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserID,
		"locked_until": lockedUntil,
		"reason":       req.Reason,
	}).Info("User locked successfully")

	return nil
}

// UnlockUser unlocks a user account
func (s *service) UnlockUser(ctx context.Context, id uint64) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, id)
	if err != nil {
		return err
	}

	// Unlock user by removing lock info from notification_settings
	unlockInfo := make(map[string]interface{})
	if targetUser.NotificationSettings != nil {
		for k, v := range targetUser.NotificationSettings {
			// Skip lock-related keys
			if k != "locked" && k != "locked_until" && k != "lock_reason" && k != "locked_at" {
				unlockInfo[k] = v
			}
		}
	}

	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetNotificationSettings(unlockInfo).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", id).Error("Failed to unlock user")
		return fmt.Errorf("failed to unlock user: %w", err)
	}

	s.logger.WithField("user_id", id).Info("User unlocked successfully")
	return nil
}

// ChangePassword changes user password
func (s *service) ChangePassword(ctx context.Context, req *models.ChangePasswordRequest) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, req.UserID)
	if err != nil {
		return err
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(targetUser.PasswordHash), []byte(req.CurrentPassword))
	if err != nil {
		s.logger.WithField("user_id", req.UserID).Warn("Invalid current password provided")
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash new password")
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password
	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetPasswordHash(string(hashedPassword)).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to update password")
		return fmt.Errorf("failed to update password: %w", err)
	}

	s.logger.WithField("user_id", req.UserID).Info("Password changed successfully")
	return nil
}

// AdminResetPassword resets user password (admin)
func (s *service) AdminResetPassword(ctx context.Context, userID uint64, req *models.ResetPasswordRequest) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, userID)
	if err != nil {
		return err
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to hash new password")
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password
	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		SetPasswordHash(string(hashedPassword)).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to reset password")
		return fmt.Errorf("failed to reset password: %w", err)
	}

	s.logger.WithField("user_id", userID).Info("Password reset by admin successfully")
	return nil
}

// RequestAvatarUpload requests a presigned URL for avatar upload
func (s *service) RequestAvatarUpload(ctx context.Context, req *models.RequestAvatarUploadRequest) (*models.RequestAvatarUploadResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// For now, return a mock response since asset client is not initialized
	// TODO: Implement actual asset service integration
	return &models.RequestAvatarUploadResponse{
		UploadID:     "mock_upload_id",
		PresignedURL: "https://mock-s3-url.com/upload",
		S3Key:        models.BuildAvatarS3Key(req.UserID, req.FileName),
		S3Bucket:     "social-content-ai-assets",
		ExpiresAt:    time.Now().Add(1 * time.Hour),
		Instructions: &models.UploadInstructions{
			Method:       "PUT",
			URL:          "https://mock-s3-url.com/upload",
			MaxFileSize:  models.MaxAvatarFileSize,
			AllowedTypes: models.GetAllowedAvatarTypes(),
		},
	}, nil
}

// ConfirmAvatarUpload confirms avatar upload completion
func (s *service) ConfirmAvatarUpload(ctx context.Context, req *models.ConfirmAvatarUploadRequest) (*models.ConfirmAvatarUploadResponse, error) {
	if req.UploadID == "" {
		return nil, fmt.Errorf("upload_id is required")
	}
	if req.ETag == "" {
		return nil, fmt.Errorf("etag is required")
	}

	// For now, return a mock response since asset client is not initialized
	// TODO: Implement actual asset service integration
	return &models.ConfirmAvatarUploadResponse{
		AssetID:   "mock_asset_id",
		Status:    models.AvatarStatusReady,
		AvatarURL: "https://cdn.social-content-ai.com/avatars/mock_avatar.jpg",
		Message:   "Avatar uploaded successfully",
	}, nil
}

// GetCurrentAvatar gets current user's avatar
func (s *service) GetCurrentAvatar(ctx context.Context, userID uint64) (*models.GetAvatarResponse, error) {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Check if user has avatar
	if targetUser.AvatarURL == "" {
		return nil, fmt.Errorf("AVATAR_NOT_FOUND")
	}

	// For now, return basic avatar info from user record
	// TODO: Get detailed avatar info from asset service
	return &models.GetAvatarResponse{
		AssetID:    "mock_asset_id",
		AvatarURL:  targetUser.AvatarURL,
		FileSize:   0,            // Unknown from user record
		FileType:   "image/jpeg", // Default
		Status:     models.AvatarStatusReady,
		IsValid:    true,
		UploadedAt: targetUser.UpdatedAt,
	}, nil
}

// DeleteCurrentAvatar deletes current user's avatar
func (s *service) DeleteCurrentAvatar(ctx context.Context, userID uint64) error {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, userID)
	if err != nil {
		return err
	}

	// Check if user has avatar
	if targetUser.AvatarURL == "" {
		return fmt.Errorf("AVATAR_NOT_FOUND")
	}

	// Clear avatar URL from user record
	_, err = s.writeDB.User.UpdateOneID(targetUser.ID).
		ClearAvatarURL().
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to clear avatar URL")
		return fmt.Errorf("failed to delete avatar: %w", err)
	}

	// TODO: Delete avatar from asset service

	s.logger.WithField("user_id", userID).Info("Avatar deleted successfully")
	return nil
}

// AdminListUsers lists users (admin)
func (s *service) AdminListUsers(ctx context.Context, req *models.ListUsersRequest) (*models.ListUsersResponse, error) {
	// Admin can see all users including deleted ones
	// Set default pagination
	page := req.Page
	if page < 1 {
		page = 1
	}
	limit := req.Limit
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// Build query (admin can see deleted users too)
	query := s.readDB.User.Query()

	// Add filters
	if req.Search != "" {
		query = query.Where(
			user.Or(
				user.FullNameContains(req.Search),
				user.EmailContains(req.Search),
			),
		)
	}

	if req.Role != "" {
		query = query.Where(user.RoleEQ(user.Role(req.Role)))
	}

	// Add sorting
	switch req.SortBy {
	case "email":
		if req.Order == "desc" {
			query = query.Order(ent.Desc(user.FieldEmail))
		} else {
			query = query.Order(ent.Asc(user.FieldEmail))
		}
	case "created_at":
		if req.Order == "desc" {
			query = query.Order(ent.Desc(user.FieldCreatedAt))
		} else {
			query = query.Order(ent.Asc(user.FieldCreatedAt))
		}
	default:
		query = query.Order(ent.Desc(user.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * limit
	users, err := query.Offset(offset).Limit(limit).All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list users")
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response format
	userResponses := make([]models.UserResponse, len(users))
	for i, userEntity := range users {
		if response := ToUserResponse(userEntity); response != nil {
			userResponses[i] = *response
		}
	}

	// Calculate total pages
	totalPages := (total + limit - 1) / limit

	return &models.ListUsersResponse{
		Users: userResponses,
		Pagination: models.PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// AdminGetUser gets user (admin)
func (s *service) AdminGetUser(ctx context.Context, id uint64) (*models.UserResponse, error) {
	// Admin can see any user including deleted ones
	users, err := s.readDB.User.
		Query().
		All(ctx) // No filter for deleted_at
	if err != nil {
		s.logger.WithError(err).Error("Failed to query users")
		return nil, fmt.Errorf("failed to query users: %w", err)
	}

	// Find user with matching ID
	for _, userEntity := range users {
		if uint64(userEntity.ID.ID()) == id {
			return ToUserResponse(userEntity), nil
		}
	}

	return nil, fmt.Errorf("user not found")
}

// AdminCreateUser creates user (admin)
func (s *service) AdminCreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error) {
	// TODO: Implement admin user creation
	return nil, fmt.Errorf("not implemented")
}

// AdminUpdateUser updates user (admin)
func (s *service) AdminUpdateUser(ctx context.Context, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	// TODO: Implement admin user update
	return nil, fmt.Errorf("not implemented")
}

// AdminDeleteUser deletes user (admin)
func (s *service) AdminDeleteUser(ctx context.Context, id uint64) error {
	// TODO: Implement admin user deletion
	return fmt.Errorf("not implemented")
}

// BulkCreateUsers creates multiple users
func (s *service) BulkCreateUsers(ctx context.Context, req *models.BulkCreateUsersRequest) (*models.BulkCreateUsersResponse, error) {
	// TODO: Implement bulk user creation
	return nil, fmt.Errorf("not implemented")
}

// BulkUpdateUsers updates multiple users
func (s *service) BulkUpdateUsers(ctx context.Context, req *models.BulkUpdateUsersRequest) (*models.BulkUpdateUsersResponse, error) {
	// TODO: Implement bulk user update
	return nil, fmt.Errorf("not implemented")
}

// BulkDeleteUsers deletes multiple users
func (s *service) BulkDeleteUsers(ctx context.Context, req *models.BulkDeleteUsersRequest) (*models.BulkDeleteUsersResponse, error) {
	// TODO: Implement bulk user deletion
	return nil, fmt.Errorf("not implemented")
}

// GetUserAnalytics gets user analytics
func (s *service) GetUserAnalytics(ctx context.Context, req *models.UserAnalyticsRequest) (*models.UserAnalyticsResponse, error) {
	// TODO: Implement user analytics
	return nil, fmt.Errorf("not implemented")
}

// UpdatePreferences updates user preferences
func (s *service) UpdatePreferences(ctx context.Context, userID uint64, req *models.UpdatePreferencesRequest) (*models.UserResponse, error) {
	// Find user first using optimized method
	targetUser, err := s.findUserByUint64ID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Merge new preferences with existing ones
	preferences := make(map[string]interface{})
	if targetUser.NotificationSettings != nil {
		for k, v := range targetUser.NotificationSettings {
			preferences[k] = v
		}
	}

	// Update with new preferences
	if req.Preferences != nil {
		for k, v := range req.Preferences {
			preferences[k] = v
		}
	}

	// Update user preferences
	updatedUser, err := s.writeDB.User.UpdateOneID(targetUser.ID).
		SetNotificationSettings(preferences).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to update preferences")
		return nil, fmt.Errorf("failed to update preferences: %w", err)
	}

	s.logger.WithField("user_id", userID).Info("User preferences updated successfully")
	return ToUserResponse(updatedUser), nil
}

// GetActivity gets user activity
func (s *service) GetActivity(ctx context.Context, userID uint64, req *models.GetActivityRequest) (*models.GetActivityResponse, error) {
	// TODO: Implement get activity
	return nil, fmt.Errorf("not implemented")
}

// ToUserResponse converts ent.User to models.UserResponse
func ToUserResponse(userEntity *ent.User) *models.UserResponse {
	if userEntity == nil {
		return nil
	}

	// Split full name into first and last name
	firstName := userEntity.FullName
	lastName := ""
	if parts := strings.Fields(userEntity.FullName); len(parts) > 1 {
		firstName = parts[0]
		lastName = strings.Join(parts[1:], " ")
	}

	response := &models.UserResponse{
		BaseModel: models.BaseModel{
			ID:        uint64(userEntity.ID.ID()), // Convert UUID to uint64 (this might need adjustment)
			CreatedAt: userEntity.CreatedAt,
			UpdatedAt: userEntity.UpdatedAt,
		},
		FirstName:     firstName,
		LastName:      lastName,
		Email:         userEntity.Email,
		Role:          string(userEntity.Role),
		Status:        "active", // Default status since schema doesn't have status
		EmailVerified: userEntity.IsVerified,
		AvatarURL:     userEntity.AvatarURL,
		Preferences:   userEntity.NotificationSettings,
	}

	// Set deleted_at if not zero
	if !userEntity.DeletedAt.IsZero() {
		response.BaseModel.DeletedAt = &userEntity.DeletedAt
	}

	// Set profile if any profile fields exist
	if userEntity.Phone != "" || userEntity.Company != "" || userEntity.Bio != "" || userEntity.Industry != "" {
		response.Profile = &models.UserProfile{
			Phone:    userEntity.Phone,
			Company:  userEntity.Company,
			Bio:      userEntity.Bio,
			JobTitle: userEntity.Industry,
		}
	}

	return response
}

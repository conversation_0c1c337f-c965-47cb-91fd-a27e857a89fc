package services

import (
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	aicontentv1 "github.com/social-content-ai/proto-shared/ai-content/v1"
	contentmgmtv1 "github.com/social-content-ai/proto-shared/content-mgmt/v1"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
	integrationv1 "github.com/social-content-ai/proto-shared/integration/v1"
	notificationv1 "github.com/social-content-ai/proto-shared/notification/v1"
	analyticsv1 "github.com/social-content-ai/proto-shared/analytics/v1"
	"github.com/social-content-ai/pkg-shared/config"
	"github.com/social-content-ai/pkg-shared/logging"
)

// ServiceClients contains all gRPC service clients
type ServiceClients struct {
	UserService         userv1.AuthServiceClient
	UserMgmtService     userv1.UserServiceClient
	AIContentService    aicontentv1.AIContentServiceClient
	ContentMgmtService  contentmgmtv1.ContentServiceClient
	CreditService       creditv1.CreditServiceClient
	AssetService        assetv1.AssetServiceClient
	IntegrationService  integrationv1.IntegrationServiceClient
	NotificationService notificationv1.NotificationServiceClient
	AnalyticsService    analyticsv1.AnalyticsServiceClient

	// gRPC connections for cleanup
	connections []*grpc.ClientConn
}

// NewServiceClients creates new service clients
func NewServiceClients(cfg *config.Config, logger logging.Logger) (*ServiceClients, error) {
	clients := &ServiceClients{
		connections: make([]*grpc.ClientConn, 0),
	}

	// User Service
	userConn, err := createGRPCConnection(cfg.Services.UserService.Host, cfg.Services.UserService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to user service: %w", err)
	}
	clients.connections = append(clients.connections, userConn)
	clients.UserService = userv1.NewAuthServiceClient(userConn)
	clients.UserMgmtService = userv1.NewUserServiceClient(userConn)

	// AI Content Service
	aiContentConn, err := createGRPCConnection(cfg.Services.AIContentService.Host, cfg.Services.AIContentService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ai content service: %w", err)
	}
	clients.connections = append(clients.connections, aiContentConn)
	clients.AIContentService = aicontentv1.NewAIContentServiceClient(aiContentConn)

	// Content Management Service
	contentMgmtConn, err := createGRPCConnection(cfg.Services.ContentMgmtService.Host, cfg.Services.ContentMgmtService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to content mgmt service: %w", err)
	}
	clients.connections = append(clients.connections, contentMgmtConn)
	clients.ContentMgmtService = contentmgmtv1.NewContentServiceClient(contentMgmtConn)

	// Credit Service
	creditConn, err := createGRPCConnection(cfg.Services.CreditService.Host, cfg.Services.CreditService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to credit service: %w", err)
	}
	clients.connections = append(clients.connections, creditConn)
	clients.CreditService = creditv1.NewCreditServiceClient(creditConn)

	// Asset Service
	assetConn, err := createGRPCConnection(cfg.Services.AssetService.Host, cfg.Services.AssetService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to asset service: %w", err)
	}
	clients.connections = append(clients.connections, assetConn)
	clients.AssetService = assetv1.NewAssetServiceClient(assetConn)

	// Integration Service
	integrationConn, err := createGRPCConnection(cfg.Services.IntegrationService.Host, cfg.Services.IntegrationService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to integration service: %w", err)
	}
	clients.connections = append(clients.connections, integrationConn)
	clients.IntegrationService = integrationv1.NewIntegrationServiceClient(integrationConn)

	// Notification Service
	notificationConn, err := createGRPCConnection(cfg.Services.NotificationService.Host, cfg.Services.NotificationService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to notification service: %w", err)
	}
	clients.connections = append(clients.connections, notificationConn)
	clients.NotificationService = notificationv1.NewNotificationServiceClient(notificationConn)

	// Analytics Service
	analyticsConn, err := createGRPCConnection(cfg.Services.AnalyticsService.Host, cfg.Services.AnalyticsService.Port)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to analytics service: %w", err)
	}
	clients.connections = append(clients.connections, analyticsConn)
	clients.AnalyticsService = analyticsv1.NewAnalyticsServiceClient(analyticsConn)

	logger.Info("All gRPC service clients initialized successfully")
	return clients, nil
}

// createGRPCConnection creates a gRPC connection
func createGRPCConnection(host string, port int) (*grpc.ClientConn, error) {
	address := fmt.Sprintf("%s:%d", host, port)
	
	// For development, use insecure connection
	// In production, you should use proper TLS credentials
	conn, err := grpc.Dial(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("failed to dial %s: %w", address, err)
	}

	return conn, nil
}

// Close closes all gRPC connections
func (sc *ServiceClients) Close() {
	for _, conn := range sc.connections {
		if err := conn.Close(); err != nil {
			// Log error but don't fail
			fmt.Printf("Error closing gRPC connection: %v\n", err)
		}
	}
}

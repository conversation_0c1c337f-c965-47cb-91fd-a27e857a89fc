// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: rag-processing/v1/rag.proto

package ragprocessingv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RAGProcessingServiceClient is the client API for RAGProcessingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RAGProcessingServiceClient interface {
	// Document processing operations
	ProcessDocument(ctx context.Context, in *ProcessDocumentRequest, opts ...grpc.CallOption) (*ProcessDocumentResponse, error)
	GetProcessingStatus(ctx context.Context, in *ProcessingStatusRequest, opts ...grpc.CallOption) (*ProcessingStatusResponse, error)
	ReprocessDocument(ctx context.Context, in *ReprocessDocumentRequest, opts ...grpc.CallOption) (*ProcessDocumentResponse, error)
	DeleteDocument(ctx context.Context, in *DeleteDocumentRequest, opts ...grpc.CallOption) (*Empty, error)
	// Document management operations
	GetDocument(ctx context.Context, in *GetDocumentRequest, opts ...grpc.CallOption) (*Document, error)
	ListDocuments(ctx context.Context, in *ListDocumentsRequest, opts ...grpc.CallOption) (*ListDocumentsResponse, error)
	// Context retrieval operations
	RetrieveContext(ctx context.Context, in *ContextRetrievalRequest, opts ...grpc.CallOption) (*ContextRetrievalResponse, error)
	GraphRAGQuery(ctx context.Context, in *GraphQueryRequest, opts ...grpc.CallOption) (*GraphQueryResponse, error)
	SearchSimilarDocuments(ctx context.Context, in *SimilaritySearchRequest, opts ...grpc.CallOption) (*SimilaritySearchResponse, error)
}

type rAGProcessingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRAGProcessingServiceClient(cc grpc.ClientConnInterface) RAGProcessingServiceClient {
	return &rAGProcessingServiceClient{cc}
}

func (c *rAGProcessingServiceClient) ProcessDocument(ctx context.Context, in *ProcessDocumentRequest, opts ...grpc.CallOption) (*ProcessDocumentResponse, error) {
	out := new(ProcessDocumentResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/ProcessDocument", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) GetProcessingStatus(ctx context.Context, in *ProcessingStatusRequest, opts ...grpc.CallOption) (*ProcessingStatusResponse, error) {
	out := new(ProcessingStatusResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/GetProcessingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) ReprocessDocument(ctx context.Context, in *ReprocessDocumentRequest, opts ...grpc.CallOption) (*ProcessDocumentResponse, error) {
	out := new(ProcessDocumentResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/ReprocessDocument", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) DeleteDocument(ctx context.Context, in *DeleteDocumentRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/DeleteDocument", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) GetDocument(ctx context.Context, in *GetDocumentRequest, opts ...grpc.CallOption) (*Document, error) {
	out := new(Document)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/GetDocument", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) ListDocuments(ctx context.Context, in *ListDocumentsRequest, opts ...grpc.CallOption) (*ListDocumentsResponse, error) {
	out := new(ListDocumentsResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/ListDocuments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) RetrieveContext(ctx context.Context, in *ContextRetrievalRequest, opts ...grpc.CallOption) (*ContextRetrievalResponse, error) {
	out := new(ContextRetrievalResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/RetrieveContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) GraphRAGQuery(ctx context.Context, in *GraphQueryRequest, opts ...grpc.CallOption) (*GraphQueryResponse, error) {
	out := new(GraphQueryResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/GraphRAGQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rAGProcessingServiceClient) SearchSimilarDocuments(ctx context.Context, in *SimilaritySearchRequest, opts ...grpc.CallOption) (*SimilaritySearchResponse, error) {
	out := new(SimilaritySearchResponse)
	err := c.cc.Invoke(ctx, "/rag_processing.v1.RAGProcessingService/SearchSimilarDocuments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RAGProcessingServiceServer is the server API for RAGProcessingService service.
// All implementations must embed UnimplementedRAGProcessingServiceServer
// for forward compatibility
type RAGProcessingServiceServer interface {
	// Document processing operations
	ProcessDocument(context.Context, *ProcessDocumentRequest) (*ProcessDocumentResponse, error)
	GetProcessingStatus(context.Context, *ProcessingStatusRequest) (*ProcessingStatusResponse, error)
	ReprocessDocument(context.Context, *ReprocessDocumentRequest) (*ProcessDocumentResponse, error)
	DeleteDocument(context.Context, *DeleteDocumentRequest) (*Empty, error)
	// Document management operations
	GetDocument(context.Context, *GetDocumentRequest) (*Document, error)
	ListDocuments(context.Context, *ListDocumentsRequest) (*ListDocumentsResponse, error)
	// Context retrieval operations
	RetrieveContext(context.Context, *ContextRetrievalRequest) (*ContextRetrievalResponse, error)
	GraphRAGQuery(context.Context, *GraphQueryRequest) (*GraphQueryResponse, error)
	SearchSimilarDocuments(context.Context, *SimilaritySearchRequest) (*SimilaritySearchResponse, error)
	mustEmbedUnimplementedRAGProcessingServiceServer()
}

// UnimplementedRAGProcessingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRAGProcessingServiceServer struct {
}

func (UnimplementedRAGProcessingServiceServer) ProcessDocument(context.Context, *ProcessDocumentRequest) (*ProcessDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessDocument not implemented")
}
func (UnimplementedRAGProcessingServiceServer) GetProcessingStatus(context.Context, *ProcessingStatusRequest) (*ProcessingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessingStatus not implemented")
}
func (UnimplementedRAGProcessingServiceServer) ReprocessDocument(context.Context, *ReprocessDocumentRequest) (*ProcessDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReprocessDocument not implemented")
}
func (UnimplementedRAGProcessingServiceServer) DeleteDocument(context.Context, *DeleteDocumentRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDocument not implemented")
}
func (UnimplementedRAGProcessingServiceServer) GetDocument(context.Context, *GetDocumentRequest) (*Document, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocument not implemented")
}
func (UnimplementedRAGProcessingServiceServer) ListDocuments(context.Context, *ListDocumentsRequest) (*ListDocumentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDocuments not implemented")
}
func (UnimplementedRAGProcessingServiceServer) RetrieveContext(context.Context, *ContextRetrievalRequest) (*ContextRetrievalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveContext not implemented")
}
func (UnimplementedRAGProcessingServiceServer) GraphRAGQuery(context.Context, *GraphQueryRequest) (*GraphQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GraphRAGQuery not implemented")
}
func (UnimplementedRAGProcessingServiceServer) SearchSimilarDocuments(context.Context, *SimilaritySearchRequest) (*SimilaritySearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchSimilarDocuments not implemented")
}
func (UnimplementedRAGProcessingServiceServer) mustEmbedUnimplementedRAGProcessingServiceServer() {}

// UnsafeRAGProcessingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RAGProcessingServiceServer will
// result in compilation errors.
type UnsafeRAGProcessingServiceServer interface {
	mustEmbedUnimplementedRAGProcessingServiceServer()
}

func RegisterRAGProcessingServiceServer(s grpc.ServiceRegistrar, srv RAGProcessingServiceServer) {
	s.RegisterService(&RAGProcessingService_ServiceDesc, srv)
}

func _RAGProcessingService_ProcessDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).ProcessDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/ProcessDocument",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).ProcessDocument(ctx, req.(*ProcessDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_GetProcessingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).GetProcessingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/GetProcessingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).GetProcessingStatus(ctx, req.(*ProcessingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_ReprocessDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReprocessDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).ReprocessDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/ReprocessDocument",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).ReprocessDocument(ctx, req.(*ReprocessDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_DeleteDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).DeleteDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/DeleteDocument",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).DeleteDocument(ctx, req.(*DeleteDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_GetDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).GetDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/GetDocument",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).GetDocument(ctx, req.(*GetDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_ListDocuments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDocumentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).ListDocuments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/ListDocuments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).ListDocuments(ctx, req.(*ListDocumentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_RetrieveContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContextRetrievalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).RetrieveContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/RetrieveContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).RetrieveContext(ctx, req.(*ContextRetrievalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_GraphRAGQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GraphQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).GraphRAGQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/GraphRAGQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).GraphRAGQuery(ctx, req.(*GraphQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RAGProcessingService_SearchSimilarDocuments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimilaritySearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RAGProcessingServiceServer).SearchSimilarDocuments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rag_processing.v1.RAGProcessingService/SearchSimilarDocuments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RAGProcessingServiceServer).SearchSimilarDocuments(ctx, req.(*SimilaritySearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RAGProcessingService_ServiceDesc is the grpc.ServiceDesc for RAGProcessingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RAGProcessingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rag_processing.v1.RAGProcessingService",
	HandlerType: (*RAGProcessingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessDocument",
			Handler:    _RAGProcessingService_ProcessDocument_Handler,
		},
		{
			MethodName: "GetProcessingStatus",
			Handler:    _RAGProcessingService_GetProcessingStatus_Handler,
		},
		{
			MethodName: "ReprocessDocument",
			Handler:    _RAGProcessingService_ReprocessDocument_Handler,
		},
		{
			MethodName: "DeleteDocument",
			Handler:    _RAGProcessingService_DeleteDocument_Handler,
		},
		{
			MethodName: "GetDocument",
			Handler:    _RAGProcessingService_GetDocument_Handler,
		},
		{
			MethodName: "ListDocuments",
			Handler:    _RAGProcessingService_ListDocuments_Handler,
		},
		{
			MethodName: "RetrieveContext",
			Handler:    _RAGProcessingService_RetrieveContext_Handler,
		},
		{
			MethodName: "GraphRAGQuery",
			Handler:    _RAGProcessingService_GraphRAGQuery_Handler,
		},
		{
			MethodName: "SearchSimilarDocuments",
			Handler:    _RAGProcessingService_SearchSimilarDocuments_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rag-processing/v1/rag.proto",
}

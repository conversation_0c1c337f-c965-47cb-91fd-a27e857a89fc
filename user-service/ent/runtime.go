// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/schema"
	"github.com/social-content-ai/user-service/ent/session"
	"github.com/social-content-ai/user-service/ent/user"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	sessionFields := schema.Session{}.Fields()
	_ = sessionFields
	// sessionDescIPAddress is the schema descriptor for ip_address field.
	sessionDescIPAddress := sessionFields[4].Descriptor()
	// session.IPAddressValidator is a validator for the "ip_address" field. It is called by the builders before save.
	session.IPAddressValidator = sessionDescIPAddress.Validators[0].(func(string) error)
	// sessionDescLoginTime is the schema descriptor for login_time field.
	sessionDescLoginTime := sessionFields[5].Descriptor()
	// session.DefaultLoginTime holds the default value on creation for the login_time field.
	session.DefaultLoginTime = sessionDescLoginTime.Default.(func() time.Time)
	// sessionDescCreatedAt is the schema descriptor for created_at field.
	sessionDescCreatedAt := sessionFields[8].Descriptor()
	// session.DefaultCreatedAt holds the default value on creation for the created_at field.
	session.DefaultCreatedAt = sessionDescCreatedAt.Default.(func() time.Time)
	// sessionDescUpdatedAt is the schema descriptor for updated_at field.
	sessionDescUpdatedAt := sessionFields[9].Descriptor()
	// session.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	session.DefaultUpdatedAt = sessionDescUpdatedAt.Default.(func() time.Time)
	// session.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	session.UpdateDefaultUpdatedAt = sessionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// sessionDescID is the schema descriptor for id field.
	sessionDescID := sessionFields[0].Descriptor()
	// session.DefaultID holds the default value on creation for the id field.
	session.DefaultID = sessionDescID.Default.(func() uuid.UUID)
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescFullName is the schema descriptor for full_name field.
	userDescFullName := userFields[1].Descriptor()
	// user.FullNameValidator is a validator for the "full_name" field. It is called by the builders before save.
	user.FullNameValidator = userDescFullName.Validators[0].(func(string) error)
	// userDescEmail is the schema descriptor for email field.
	userDescEmail := userFields[2].Descriptor()
	// user.EmailValidator is a validator for the "email" field. It is called by the builders before save.
	user.EmailValidator = userDescEmail.Validators[0].(func(string) error)
	// userDescPhone is the schema descriptor for phone field.
	userDescPhone := userFields[3].Descriptor()
	// user.PhoneValidator is a validator for the "phone" field. It is called by the builders before save.
	user.PhoneValidator = userDescPhone.Validators[0].(func(string) error)
	// userDescCompany is the schema descriptor for company field.
	userDescCompany := userFields[6].Descriptor()
	// user.CompanyValidator is a validator for the "company" field. It is called by the builders before save.
	user.CompanyValidator = userDescCompany.Validators[0].(func(string) error)
	// userDescIndustry is the schema descriptor for industry field.
	userDescIndustry := userFields[7].Descriptor()
	// user.IndustryValidator is a validator for the "industry" field. It is called by the builders before save.
	user.IndustryValidator = userDescIndustry.Validators[0].(func(string) error)
	// userDescIsVerified is the schema descriptor for is_verified field.
	userDescIsVerified := userFields[10].Descriptor()
	// user.DefaultIsVerified holds the default value on creation for the is_verified field.
	user.DefaultIsVerified = userDescIsVerified.Default.(bool)
	// userDescTwoFactorEnabled is the schema descriptor for two_factor_enabled field.
	userDescTwoFactorEnabled := userFields[11].Descriptor()
	// user.DefaultTwoFactorEnabled holds the default value on creation for the two_factor_enabled field.
	user.DefaultTwoFactorEnabled = userDescTwoFactorEnabled.Default.(bool)
	// userDescInviteCode is the schema descriptor for invite_code field.
	userDescInviteCode := userFields[16].Descriptor()
	// user.InviteCodeValidator is a validator for the "invite_code" field. It is called by the builders before save.
	user.InviteCodeValidator = userDescInviteCode.Validators[0].(func(string) error)
	// userDescAffCode is the schema descriptor for aff_code field.
	userDescAffCode := userFields[17].Descriptor()
	// user.AffCodeValidator is a validator for the "aff_code" field. It is called by the builders before save.
	user.AffCodeValidator = userDescAffCode.Validators[0].(func(string) error)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userFields[19].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userFields[20].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userDescID is the schema descriptor for id field.
	userDescID := userFields[0].Descriptor()
	// user.DefaultID holds the default value on creation for the id field.
	user.DefaultID = userDescID.Default.(func() uuid.UUID)
}

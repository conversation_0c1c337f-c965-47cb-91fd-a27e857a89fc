# Credit Service Makefile
# This Makefile provides common development tasks for the credit service

.PHONY: help build run test clean docker-up docker-down migrate lint format deps proto

# Default target
help: ## Show this help message
	@echo "Credit Service - Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# =============================================================================
# BUILD AND RUN
# =============================================================================

build: ## Build the credit service binary
	@echo "Building credit service..."
	go build -o bin/credit-service ./main.go

run: ## Run the credit service locally
	@echo "Running credit service..."
	go run main.go

run-dev: ## Run the credit service in development mode with hot reload
	@echo "Running credit service in development mode..."
	air -c .air.toml

# =============================================================================
# TESTING
# =============================================================================

test: ## Run all tests
	@echo "Running tests..."
	go test -v ./...

test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	go test -v -tags=integration ./...

benchmark: ## Run benchmark tests
	@echo "Running benchmark tests..."
	go test -bench=. -benchmem ./...

# =============================================================================
# DATABASE
# =============================================================================

migrate: ## Run database migrations
	@echo "Running database migrations..."
	go run main.go migrate

migrate-down: ## Rollback database migrations
	@echo "Rolling back database migrations..."
	go run main.go migrate-down

seed: ## Seed database with test data
	@echo "Seeding database..."
	go run main.go seed

db-reset: ## Reset database (drop and recreate)
	@echo "Resetting database..."
	go run main.go db-reset

# =============================================================================
# DOCKER
# =============================================================================

docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t credit-service:latest .

docker-up: ## Start all services with Docker Compose
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

docker-down: ## Stop all services
	@echo "Stopping services..."
	docker-compose down

docker-logs: ## Show logs from all services
	@echo "Showing logs..."
	docker-compose logs -f

docker-restart: ## Restart the credit service container
	@echo "Restarting credit service..."
	docker-compose restart credit-service

docker-clean: ## Clean up Docker resources
	@echo "Cleaning up Docker resources..."
	docker-compose down -v
	docker system prune -f

# =============================================================================
# DEVELOPMENT
# =============================================================================

deps: ## Download and tidy dependencies
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

deps-update: ## Update all dependencies
	@echo "Updating dependencies..."
	go get -u ./...
	go mod tidy

lint: ## Run linter
	@echo "Running linter..."
	golangci-lint run

lint-fix: ## Run linter with auto-fix
	@echo "Running linter with auto-fix..."
	golangci-lint run --fix

format: ## Format code
	@echo "Formatting code..."
	go fmt ./...
	goimports -w .

vet: ## Run go vet
	@echo "Running go vet..."
	go vet ./...

# =============================================================================
# CODE GENERATION
# =============================================================================

generate: ## Run go generate
	@echo "Running go generate..."
	go generate ./...

proto: ## Generate protobuf files
	@echo "Generating protobuf files..."
	@if [ -d "../proto-shared" ]; then \
		cd ../proto-shared && make generate; \
	else \
		echo "proto-shared directory not found. Please ensure proto-shared is available."; \
	fi

ent-generate: ## Generate Ent code
	@echo "Generating Ent code..."
	go run -mod=mod entgo.io/ent/cmd/ent generate ./ent/schema

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

metrics: ## Show metrics endpoint
	@echo "Metrics available at: http://localhost:9093/metrics"
	@curl -s http://localhost:9093/metrics | head -20

health: ## Check service health
	@echo "Checking service health..."
	@curl -s http://localhost:8083/health | jq .

logs: ## Show service logs
	@echo "Showing service logs..."
	docker-compose logs -f credit-service

# =============================================================================
# KAFKA
# =============================================================================

kafka-topics: ## List Kafka topics
	@echo "Listing Kafka topics..."
	docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --list

kafka-create-topics: ## Create required Kafka topics
	@echo "Creating Kafka topics..."
	docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --create --topic credit.events --partitions 3 --replication-factor 1
	docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --create --topic subscription.events --partitions 3 --replication-factor 1
	docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --create --topic payment.events --partitions 3 --replication-factor 1
	docker-compose exec kafka kafka-topics --bootstrap-server localhost:9092 --create --topic notification.events --partitions 3 --replication-factor 1

kafka-console-consumer: ## Start Kafka console consumer for credit events
	@echo "Starting Kafka console consumer..."
	docker-compose exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic credit.events --from-beginning

# =============================================================================
# DATABASE UTILITIES
# =============================================================================

db-connect: ## Connect to PostgreSQL database
	@echo "Connecting to database..."
	docker-compose exec postgres psql -U postgres -d credit_service_db

db-backup: ## Backup database
	@echo "Backing up database..."
	docker-compose exec postgres pg_dump -U postgres credit_service_db > backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore: ## Restore database from backup (usage: make db-restore BACKUP_FILE=backup.sql)
	@echo "Restoring database from $(BACKUP_FILE)..."
	docker-compose exec -T postgres psql -U postgres credit_service_db < $(BACKUP_FILE)

# =============================================================================
# SECURITY
# =============================================================================

security-scan: ## Run security scan
	@echo "Running security scan..."
	gosec ./...

vulnerability-check: ## Check for known vulnerabilities
	@echo "Checking for vulnerabilities..."
	govulncheck ./...

# =============================================================================
# CLEANUP
# =============================================================================

clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	go clean -cache
	go clean -testcache

clean-all: clean docker-clean ## Clean everything including Docker resources

# =============================================================================
# ENVIRONMENT SETUP
# =============================================================================

setup: ## Setup development environment
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from .env.example"; fi
	@echo "Installing dependencies..."
	$(MAKE) deps
	@echo "Starting infrastructure services..."
	$(MAKE) docker-up
	@echo "Waiting for services to be ready..."
	sleep 10
	@echo "Creating Kafka topics..."
	$(MAKE) kafka-create-topics
	@echo "Running migrations..."
	$(MAKE) migrate
	@echo "Setup complete! You can now run 'make run' to start the service."

# =============================================================================
# RELEASE
# =============================================================================

version: ## Show current version
	@echo "Current version: $(shell git describe --tags --always --dirty)"

tag: ## Create a new git tag (usage: make tag VERSION=v1.0.0)
	@echo "Creating tag $(VERSION)..."
	git tag -a $(VERSION) -m "Release $(VERSION)"
	git push origin $(VERSION)

# =============================================================================
# DOCUMENTATION
# =============================================================================

docs: ## Generate API documentation
	@echo "Generating API documentation..."
	swag init -g main.go -o ./docs

docs-serve: ## Serve API documentation
	@echo "API documentation available at: http://localhost:8083/swagger/index.html"

# =============================================================================
# VARIABLES
# =============================================================================

# Default backup file for restore
BACKUP_FILE ?= backup.sql

# Default version for tagging
VERSION ?= v0.1.0

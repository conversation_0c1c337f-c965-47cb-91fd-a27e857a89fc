// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
)

// SubscriptionPlanUpdate is the builder for updating SubscriptionPlan entities.
type SubscriptionPlanUpdate struct {
	config
	hooks    []Hook
	mutation *SubscriptionPlanMutation
}

// Where appends a list predicates to the SubscriptionPlanUpdate builder.
func (spu *SubscriptionPlanUpdate) Where(ps ...predicate.SubscriptionPlan) *SubscriptionPlanUpdate {
	spu.mutation.Where(ps...)
	return spu
}

// SetName sets the "name" field.
func (spu *SubscriptionPlanUpdate) SetName(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetName(s)
	return spu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableName(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetName(*s)
	}
	return spu
}

// SetDisplayName sets the "display_name" field.
func (spu *SubscriptionPlanUpdate) SetDisplayName(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetDisplayName(s)
	return spu
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableDisplayName(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetDisplayName(*s)
	}
	return spu
}

// SetDescription sets the "description" field.
func (spu *SubscriptionPlanUpdate) SetDescription(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetDescription(s)
	return spu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableDescription(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetDescription(*s)
	}
	return spu
}

// ClearDescription clears the value of the "description" field.
func (spu *SubscriptionPlanUpdate) ClearDescription() *SubscriptionPlanUpdate {
	spu.mutation.ClearDescription()
	return spu
}

// SetPrice sets the "price" field.
func (spu *SubscriptionPlanUpdate) SetPrice(i int64) *SubscriptionPlanUpdate {
	spu.mutation.ResetPrice()
	spu.mutation.SetPrice(i)
	return spu
}

// SetNillablePrice sets the "price" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillablePrice(i *int64) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetPrice(*i)
	}
	return spu
}

// AddPrice adds i to the "price" field.
func (spu *SubscriptionPlanUpdate) AddPrice(i int64) *SubscriptionPlanUpdate {
	spu.mutation.AddPrice(i)
	return spu
}

// SetCurrency sets the "currency" field.
func (spu *SubscriptionPlanUpdate) SetCurrency(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetCurrency(s)
	return spu
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableCurrency(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetCurrency(*s)
	}
	return spu
}

// SetBillingInterval sets the "billing_interval" field.
func (spu *SubscriptionPlanUpdate) SetBillingInterval(si subscriptionplan.BillingInterval) *SubscriptionPlanUpdate {
	spu.mutation.SetBillingInterval(si)
	return spu
}

// SetNillableBillingInterval sets the "billing_interval" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableBillingInterval(si *subscriptionplan.BillingInterval) *SubscriptionPlanUpdate {
	if si != nil {
		spu.SetBillingInterval(*si)
	}
	return spu
}

// SetCreditsIncluded sets the "credits_included" field.
func (spu *SubscriptionPlanUpdate) SetCreditsIncluded(i int) *SubscriptionPlanUpdate {
	spu.mutation.ResetCreditsIncluded()
	spu.mutation.SetCreditsIncluded(i)
	return spu
}

// SetNillableCreditsIncluded sets the "credits_included" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableCreditsIncluded(i *int) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetCreditsIncluded(*i)
	}
	return spu
}

// AddCreditsIncluded adds i to the "credits_included" field.
func (spu *SubscriptionPlanUpdate) AddCreditsIncluded(i int) *SubscriptionPlanUpdate {
	spu.mutation.AddCreditsIncluded(i)
	return spu
}

// SetMonthlyCredits sets the "monthly_credits" field.
func (spu *SubscriptionPlanUpdate) SetMonthlyCredits(i int) *SubscriptionPlanUpdate {
	spu.mutation.ResetMonthlyCredits()
	spu.mutation.SetMonthlyCredits(i)
	return spu
}

// SetNillableMonthlyCredits sets the "monthly_credits" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableMonthlyCredits(i *int) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetMonthlyCredits(*i)
	}
	return spu
}

// AddMonthlyCredits adds i to the "monthly_credits" field.
func (spu *SubscriptionPlanUpdate) AddMonthlyCredits(i int) *SubscriptionPlanUpdate {
	spu.mutation.AddMonthlyCredits(i)
	return spu
}

// SetIsUnlimited sets the "is_unlimited" field.
func (spu *SubscriptionPlanUpdate) SetIsUnlimited(b bool) *SubscriptionPlanUpdate {
	spu.mutation.SetIsUnlimited(b)
	return spu
}

// SetNillableIsUnlimited sets the "is_unlimited" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableIsUnlimited(b *bool) *SubscriptionPlanUpdate {
	if b != nil {
		spu.SetIsUnlimited(*b)
	}
	return spu
}

// SetFeatures sets the "features" field.
func (spu *SubscriptionPlanUpdate) SetFeatures(s []string) *SubscriptionPlanUpdate {
	spu.mutation.SetFeatures(s)
	return spu
}

// AppendFeatures appends s to the "features" field.
func (spu *SubscriptionPlanUpdate) AppendFeatures(s []string) *SubscriptionPlanUpdate {
	spu.mutation.AppendFeatures(s)
	return spu
}

// ClearFeatures clears the value of the "features" field.
func (spu *SubscriptionPlanUpdate) ClearFeatures() *SubscriptionPlanUpdate {
	spu.mutation.ClearFeatures()
	return spu
}

// SetLimits sets the "limits" field.
func (spu *SubscriptionPlanUpdate) SetLimits(m map[string]interface{}) *SubscriptionPlanUpdate {
	spu.mutation.SetLimits(m)
	return spu
}

// ClearLimits clears the value of the "limits" field.
func (spu *SubscriptionPlanUpdate) ClearLimits() *SubscriptionPlanUpdate {
	spu.mutation.ClearLimits()
	return spu
}

// SetMaxUsers sets the "max_users" field.
func (spu *SubscriptionPlanUpdate) SetMaxUsers(i int) *SubscriptionPlanUpdate {
	spu.mutation.ResetMaxUsers()
	spu.mutation.SetMaxUsers(i)
	return spu
}

// SetNillableMaxUsers sets the "max_users" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableMaxUsers(i *int) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetMaxUsers(*i)
	}
	return spu
}

// AddMaxUsers adds i to the "max_users" field.
func (spu *SubscriptionPlanUpdate) AddMaxUsers(i int) *SubscriptionPlanUpdate {
	spu.mutation.AddMaxUsers(i)
	return spu
}

// SetMaxWorkspaces sets the "max_workspaces" field.
func (spu *SubscriptionPlanUpdate) SetMaxWorkspaces(i int) *SubscriptionPlanUpdate {
	spu.mutation.ResetMaxWorkspaces()
	spu.mutation.SetMaxWorkspaces(i)
	return spu
}

// SetNillableMaxWorkspaces sets the "max_workspaces" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableMaxWorkspaces(i *int) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetMaxWorkspaces(*i)
	}
	return spu
}

// AddMaxWorkspaces adds i to the "max_workspaces" field.
func (spu *SubscriptionPlanUpdate) AddMaxWorkspaces(i int) *SubscriptionPlanUpdate {
	spu.mutation.AddMaxWorkspaces(i)
	return spu
}

// SetIsActive sets the "is_active" field.
func (spu *SubscriptionPlanUpdate) SetIsActive(b bool) *SubscriptionPlanUpdate {
	spu.mutation.SetIsActive(b)
	return spu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableIsActive(b *bool) *SubscriptionPlanUpdate {
	if b != nil {
		spu.SetIsActive(*b)
	}
	return spu
}

// SetIsFeatured sets the "is_featured" field.
func (spu *SubscriptionPlanUpdate) SetIsFeatured(b bool) *SubscriptionPlanUpdate {
	spu.mutation.SetIsFeatured(b)
	return spu
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableIsFeatured(b *bool) *SubscriptionPlanUpdate {
	if b != nil {
		spu.SetIsFeatured(*b)
	}
	return spu
}

// SetSortOrder sets the "sort_order" field.
func (spu *SubscriptionPlanUpdate) SetSortOrder(i int) *SubscriptionPlanUpdate {
	spu.mutation.ResetSortOrder()
	spu.mutation.SetSortOrder(i)
	return spu
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableSortOrder(i *int) *SubscriptionPlanUpdate {
	if i != nil {
		spu.SetSortOrder(*i)
	}
	return spu
}

// AddSortOrder adds i to the "sort_order" field.
func (spu *SubscriptionPlanUpdate) AddSortOrder(i int) *SubscriptionPlanUpdate {
	spu.mutation.AddSortOrder(i)
	return spu
}

// SetStripePriceID sets the "stripe_price_id" field.
func (spu *SubscriptionPlanUpdate) SetStripePriceID(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetStripePriceID(s)
	return spu
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableStripePriceID(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetStripePriceID(*s)
	}
	return spu
}

// ClearStripePriceID clears the value of the "stripe_price_id" field.
func (spu *SubscriptionPlanUpdate) ClearStripePriceID() *SubscriptionPlanUpdate {
	spu.mutation.ClearStripePriceID()
	return spu
}

// SetStripeProductID sets the "stripe_product_id" field.
func (spu *SubscriptionPlanUpdate) SetStripeProductID(s string) *SubscriptionPlanUpdate {
	spu.mutation.SetStripeProductID(s)
	return spu
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (spu *SubscriptionPlanUpdate) SetNillableStripeProductID(s *string) *SubscriptionPlanUpdate {
	if s != nil {
		spu.SetStripeProductID(*s)
	}
	return spu
}

// ClearStripeProductID clears the value of the "stripe_product_id" field.
func (spu *SubscriptionPlanUpdate) ClearStripeProductID() *SubscriptionPlanUpdate {
	spu.mutation.ClearStripeProductID()
	return spu
}

// SetMetadata sets the "metadata" field.
func (spu *SubscriptionPlanUpdate) SetMetadata(m map[string]interface{}) *SubscriptionPlanUpdate {
	spu.mutation.SetMetadata(m)
	return spu
}

// ClearMetadata clears the value of the "metadata" field.
func (spu *SubscriptionPlanUpdate) ClearMetadata() *SubscriptionPlanUpdate {
	spu.mutation.ClearMetadata()
	return spu
}

// SetUpdatedAt sets the "updated_at" field.
func (spu *SubscriptionPlanUpdate) SetUpdatedAt(t time.Time) *SubscriptionPlanUpdate {
	spu.mutation.SetUpdatedAt(t)
	return spu
}

// Mutation returns the SubscriptionPlanMutation object of the builder.
func (spu *SubscriptionPlanUpdate) Mutation() *SubscriptionPlanMutation {
	return spu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (spu *SubscriptionPlanUpdate) Save(ctx context.Context) (int, error) {
	spu.defaults()
	return withHooks(ctx, spu.sqlSave, spu.mutation, spu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (spu *SubscriptionPlanUpdate) SaveX(ctx context.Context) int {
	affected, err := spu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (spu *SubscriptionPlanUpdate) Exec(ctx context.Context) error {
	_, err := spu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (spu *SubscriptionPlanUpdate) ExecX(ctx context.Context) {
	if err := spu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (spu *SubscriptionPlanUpdate) defaults() {
	if _, ok := spu.mutation.UpdatedAt(); !ok {
		v := subscriptionplan.UpdateDefaultUpdatedAt()
		spu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (spu *SubscriptionPlanUpdate) check() error {
	if v, ok := spu.mutation.Name(); ok {
		if err := subscriptionplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.name": %w`, err)}
		}
	}
	if v, ok := spu.mutation.DisplayName(); ok {
		if err := subscriptionplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.display_name": %w`, err)}
		}
	}
	if v, ok := spu.mutation.Price(); ok {
		if err := subscriptionplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.price": %w`, err)}
		}
	}
	if v, ok := spu.mutation.Currency(); ok {
		if err := subscriptionplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.currency": %w`, err)}
		}
	}
	if v, ok := spu.mutation.BillingInterval(); ok {
		if err := subscriptionplan.BillingIntervalValidator(v); err != nil {
			return &ValidationError{Name: "billing_interval", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.billing_interval": %w`, err)}
		}
	}
	if v, ok := spu.mutation.CreditsIncluded(); ok {
		if err := subscriptionplan.CreditsIncludedValidator(v); err != nil {
			return &ValidationError{Name: "credits_included", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.credits_included": %w`, err)}
		}
	}
	if v, ok := spu.mutation.MonthlyCredits(); ok {
		if err := subscriptionplan.MonthlyCreditsValidator(v); err != nil {
			return &ValidationError{Name: "monthly_credits", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.monthly_credits": %w`, err)}
		}
	}
	if v, ok := spu.mutation.MaxUsers(); ok {
		if err := subscriptionplan.MaxUsersValidator(v); err != nil {
			return &ValidationError{Name: "max_users", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_users": %w`, err)}
		}
	}
	if v, ok := spu.mutation.MaxWorkspaces(); ok {
		if err := subscriptionplan.MaxWorkspacesValidator(v); err != nil {
			return &ValidationError{Name: "max_workspaces", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_workspaces": %w`, err)}
		}
	}
	if v, ok := spu.mutation.StripePriceID(); ok {
		if err := subscriptionplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := spu.mutation.StripeProductID(); ok {
		if err := subscriptionplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_product_id": %w`, err)}
		}
	}
	return nil
}

func (spu *SubscriptionPlanUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := spu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(subscriptionplan.Table, subscriptionplan.Columns, sqlgraph.NewFieldSpec(subscriptionplan.FieldID, field.TypeUUID))
	if ps := spu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := spu.mutation.Name(); ok {
		_spec.SetField(subscriptionplan.FieldName, field.TypeString, value)
	}
	if value, ok := spu.mutation.DisplayName(); ok {
		_spec.SetField(subscriptionplan.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := spu.mutation.Description(); ok {
		_spec.SetField(subscriptionplan.FieldDescription, field.TypeString, value)
	}
	if spu.mutation.DescriptionCleared() {
		_spec.ClearField(subscriptionplan.FieldDescription, field.TypeString)
	}
	if value, ok := spu.mutation.Price(); ok {
		_spec.SetField(subscriptionplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := spu.mutation.AddedPrice(); ok {
		_spec.AddField(subscriptionplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := spu.mutation.Currency(); ok {
		_spec.SetField(subscriptionplan.FieldCurrency, field.TypeString, value)
	}
	if value, ok := spu.mutation.BillingInterval(); ok {
		_spec.SetField(subscriptionplan.FieldBillingInterval, field.TypeEnum, value)
	}
	if value, ok := spu.mutation.CreditsIncluded(); ok {
		_spec.SetField(subscriptionplan.FieldCreditsIncluded, field.TypeInt, value)
	}
	if value, ok := spu.mutation.AddedCreditsIncluded(); ok {
		_spec.AddField(subscriptionplan.FieldCreditsIncluded, field.TypeInt, value)
	}
	if value, ok := spu.mutation.MonthlyCredits(); ok {
		_spec.SetField(subscriptionplan.FieldMonthlyCredits, field.TypeInt, value)
	}
	if value, ok := spu.mutation.AddedMonthlyCredits(); ok {
		_spec.AddField(subscriptionplan.FieldMonthlyCredits, field.TypeInt, value)
	}
	if value, ok := spu.mutation.IsUnlimited(); ok {
		_spec.SetField(subscriptionplan.FieldIsUnlimited, field.TypeBool, value)
	}
	if value, ok := spu.mutation.Features(); ok {
		_spec.SetField(subscriptionplan.FieldFeatures, field.TypeJSON, value)
	}
	if value, ok := spu.mutation.AppendedFeatures(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, subscriptionplan.FieldFeatures, value)
		})
	}
	if spu.mutation.FeaturesCleared() {
		_spec.ClearField(subscriptionplan.FieldFeatures, field.TypeJSON)
	}
	if value, ok := spu.mutation.Limits(); ok {
		_spec.SetField(subscriptionplan.FieldLimits, field.TypeJSON, value)
	}
	if spu.mutation.LimitsCleared() {
		_spec.ClearField(subscriptionplan.FieldLimits, field.TypeJSON)
	}
	if value, ok := spu.mutation.MaxUsers(); ok {
		_spec.SetField(subscriptionplan.FieldMaxUsers, field.TypeInt, value)
	}
	if value, ok := spu.mutation.AddedMaxUsers(); ok {
		_spec.AddField(subscriptionplan.FieldMaxUsers, field.TypeInt, value)
	}
	if value, ok := spu.mutation.MaxWorkspaces(); ok {
		_spec.SetField(subscriptionplan.FieldMaxWorkspaces, field.TypeInt, value)
	}
	if value, ok := spu.mutation.AddedMaxWorkspaces(); ok {
		_spec.AddField(subscriptionplan.FieldMaxWorkspaces, field.TypeInt, value)
	}
	if value, ok := spu.mutation.IsActive(); ok {
		_spec.SetField(subscriptionplan.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := spu.mutation.IsFeatured(); ok {
		_spec.SetField(subscriptionplan.FieldIsFeatured, field.TypeBool, value)
	}
	if value, ok := spu.mutation.SortOrder(); ok {
		_spec.SetField(subscriptionplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := spu.mutation.AddedSortOrder(); ok {
		_spec.AddField(subscriptionplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := spu.mutation.StripePriceID(); ok {
		_spec.SetField(subscriptionplan.FieldStripePriceID, field.TypeString, value)
	}
	if spu.mutation.StripePriceIDCleared() {
		_spec.ClearField(subscriptionplan.FieldStripePriceID, field.TypeString)
	}
	if value, ok := spu.mutation.StripeProductID(); ok {
		_spec.SetField(subscriptionplan.FieldStripeProductID, field.TypeString, value)
	}
	if spu.mutation.StripeProductIDCleared() {
		_spec.ClearField(subscriptionplan.FieldStripeProductID, field.TypeString)
	}
	if value, ok := spu.mutation.Metadata(); ok {
		_spec.SetField(subscriptionplan.FieldMetadata, field.TypeJSON, value)
	}
	if spu.mutation.MetadataCleared() {
		_spec.ClearField(subscriptionplan.FieldMetadata, field.TypeJSON)
	}
	if value, ok := spu.mutation.UpdatedAt(); ok {
		_spec.SetField(subscriptionplan.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, spu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{subscriptionplan.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	spu.mutation.done = true
	return n, nil
}

// SubscriptionPlanUpdateOne is the builder for updating a single SubscriptionPlan entity.
type SubscriptionPlanUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SubscriptionPlanMutation
}

// SetName sets the "name" field.
func (spuo *SubscriptionPlanUpdateOne) SetName(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetName(s)
	return spuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableName(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetName(*s)
	}
	return spuo
}

// SetDisplayName sets the "display_name" field.
func (spuo *SubscriptionPlanUpdateOne) SetDisplayName(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetDisplayName(s)
	return spuo
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableDisplayName(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetDisplayName(*s)
	}
	return spuo
}

// SetDescription sets the "description" field.
func (spuo *SubscriptionPlanUpdateOne) SetDescription(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetDescription(s)
	return spuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableDescription(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetDescription(*s)
	}
	return spuo
}

// ClearDescription clears the value of the "description" field.
func (spuo *SubscriptionPlanUpdateOne) ClearDescription() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearDescription()
	return spuo
}

// SetPrice sets the "price" field.
func (spuo *SubscriptionPlanUpdateOne) SetPrice(i int64) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetPrice()
	spuo.mutation.SetPrice(i)
	return spuo
}

// SetNillablePrice sets the "price" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillablePrice(i *int64) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetPrice(*i)
	}
	return spuo
}

// AddPrice adds i to the "price" field.
func (spuo *SubscriptionPlanUpdateOne) AddPrice(i int64) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddPrice(i)
	return spuo
}

// SetCurrency sets the "currency" field.
func (spuo *SubscriptionPlanUpdateOne) SetCurrency(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetCurrency(s)
	return spuo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableCurrency(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetCurrency(*s)
	}
	return spuo
}

// SetBillingInterval sets the "billing_interval" field.
func (spuo *SubscriptionPlanUpdateOne) SetBillingInterval(si subscriptionplan.BillingInterval) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetBillingInterval(si)
	return spuo
}

// SetNillableBillingInterval sets the "billing_interval" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableBillingInterval(si *subscriptionplan.BillingInterval) *SubscriptionPlanUpdateOne {
	if si != nil {
		spuo.SetBillingInterval(*si)
	}
	return spuo
}

// SetCreditsIncluded sets the "credits_included" field.
func (spuo *SubscriptionPlanUpdateOne) SetCreditsIncluded(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetCreditsIncluded()
	spuo.mutation.SetCreditsIncluded(i)
	return spuo
}

// SetNillableCreditsIncluded sets the "credits_included" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableCreditsIncluded(i *int) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetCreditsIncluded(*i)
	}
	return spuo
}

// AddCreditsIncluded adds i to the "credits_included" field.
func (spuo *SubscriptionPlanUpdateOne) AddCreditsIncluded(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddCreditsIncluded(i)
	return spuo
}

// SetMonthlyCredits sets the "monthly_credits" field.
func (spuo *SubscriptionPlanUpdateOne) SetMonthlyCredits(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetMonthlyCredits()
	spuo.mutation.SetMonthlyCredits(i)
	return spuo
}

// SetNillableMonthlyCredits sets the "monthly_credits" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableMonthlyCredits(i *int) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetMonthlyCredits(*i)
	}
	return spuo
}

// AddMonthlyCredits adds i to the "monthly_credits" field.
func (spuo *SubscriptionPlanUpdateOne) AddMonthlyCredits(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddMonthlyCredits(i)
	return spuo
}

// SetIsUnlimited sets the "is_unlimited" field.
func (spuo *SubscriptionPlanUpdateOne) SetIsUnlimited(b bool) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetIsUnlimited(b)
	return spuo
}

// SetNillableIsUnlimited sets the "is_unlimited" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableIsUnlimited(b *bool) *SubscriptionPlanUpdateOne {
	if b != nil {
		spuo.SetIsUnlimited(*b)
	}
	return spuo
}

// SetFeatures sets the "features" field.
func (spuo *SubscriptionPlanUpdateOne) SetFeatures(s []string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetFeatures(s)
	return spuo
}

// AppendFeatures appends s to the "features" field.
func (spuo *SubscriptionPlanUpdateOne) AppendFeatures(s []string) *SubscriptionPlanUpdateOne {
	spuo.mutation.AppendFeatures(s)
	return spuo
}

// ClearFeatures clears the value of the "features" field.
func (spuo *SubscriptionPlanUpdateOne) ClearFeatures() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearFeatures()
	return spuo
}

// SetLimits sets the "limits" field.
func (spuo *SubscriptionPlanUpdateOne) SetLimits(m map[string]interface{}) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetLimits(m)
	return spuo
}

// ClearLimits clears the value of the "limits" field.
func (spuo *SubscriptionPlanUpdateOne) ClearLimits() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearLimits()
	return spuo
}

// SetMaxUsers sets the "max_users" field.
func (spuo *SubscriptionPlanUpdateOne) SetMaxUsers(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetMaxUsers()
	spuo.mutation.SetMaxUsers(i)
	return spuo
}

// SetNillableMaxUsers sets the "max_users" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableMaxUsers(i *int) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetMaxUsers(*i)
	}
	return spuo
}

// AddMaxUsers adds i to the "max_users" field.
func (spuo *SubscriptionPlanUpdateOne) AddMaxUsers(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddMaxUsers(i)
	return spuo
}

// SetMaxWorkspaces sets the "max_workspaces" field.
func (spuo *SubscriptionPlanUpdateOne) SetMaxWorkspaces(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetMaxWorkspaces()
	spuo.mutation.SetMaxWorkspaces(i)
	return spuo
}

// SetNillableMaxWorkspaces sets the "max_workspaces" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableMaxWorkspaces(i *int) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetMaxWorkspaces(*i)
	}
	return spuo
}

// AddMaxWorkspaces adds i to the "max_workspaces" field.
func (spuo *SubscriptionPlanUpdateOne) AddMaxWorkspaces(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddMaxWorkspaces(i)
	return spuo
}

// SetIsActive sets the "is_active" field.
func (spuo *SubscriptionPlanUpdateOne) SetIsActive(b bool) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetIsActive(b)
	return spuo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableIsActive(b *bool) *SubscriptionPlanUpdateOne {
	if b != nil {
		spuo.SetIsActive(*b)
	}
	return spuo
}

// SetIsFeatured sets the "is_featured" field.
func (spuo *SubscriptionPlanUpdateOne) SetIsFeatured(b bool) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetIsFeatured(b)
	return spuo
}

// SetNillableIsFeatured sets the "is_featured" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableIsFeatured(b *bool) *SubscriptionPlanUpdateOne {
	if b != nil {
		spuo.SetIsFeatured(*b)
	}
	return spuo
}

// SetSortOrder sets the "sort_order" field.
func (spuo *SubscriptionPlanUpdateOne) SetSortOrder(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.ResetSortOrder()
	spuo.mutation.SetSortOrder(i)
	return spuo
}

// SetNillableSortOrder sets the "sort_order" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableSortOrder(i *int) *SubscriptionPlanUpdateOne {
	if i != nil {
		spuo.SetSortOrder(*i)
	}
	return spuo
}

// AddSortOrder adds i to the "sort_order" field.
func (spuo *SubscriptionPlanUpdateOne) AddSortOrder(i int) *SubscriptionPlanUpdateOne {
	spuo.mutation.AddSortOrder(i)
	return spuo
}

// SetStripePriceID sets the "stripe_price_id" field.
func (spuo *SubscriptionPlanUpdateOne) SetStripePriceID(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetStripePriceID(s)
	return spuo
}

// SetNillableStripePriceID sets the "stripe_price_id" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableStripePriceID(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetStripePriceID(*s)
	}
	return spuo
}

// ClearStripePriceID clears the value of the "stripe_price_id" field.
func (spuo *SubscriptionPlanUpdateOne) ClearStripePriceID() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearStripePriceID()
	return spuo
}

// SetStripeProductID sets the "stripe_product_id" field.
func (spuo *SubscriptionPlanUpdateOne) SetStripeProductID(s string) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetStripeProductID(s)
	return spuo
}

// SetNillableStripeProductID sets the "stripe_product_id" field if the given value is not nil.
func (spuo *SubscriptionPlanUpdateOne) SetNillableStripeProductID(s *string) *SubscriptionPlanUpdateOne {
	if s != nil {
		spuo.SetStripeProductID(*s)
	}
	return spuo
}

// ClearStripeProductID clears the value of the "stripe_product_id" field.
func (spuo *SubscriptionPlanUpdateOne) ClearStripeProductID() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearStripeProductID()
	return spuo
}

// SetMetadata sets the "metadata" field.
func (spuo *SubscriptionPlanUpdateOne) SetMetadata(m map[string]interface{}) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetMetadata(m)
	return spuo
}

// ClearMetadata clears the value of the "metadata" field.
func (spuo *SubscriptionPlanUpdateOne) ClearMetadata() *SubscriptionPlanUpdateOne {
	spuo.mutation.ClearMetadata()
	return spuo
}

// SetUpdatedAt sets the "updated_at" field.
func (spuo *SubscriptionPlanUpdateOne) SetUpdatedAt(t time.Time) *SubscriptionPlanUpdateOne {
	spuo.mutation.SetUpdatedAt(t)
	return spuo
}

// Mutation returns the SubscriptionPlanMutation object of the builder.
func (spuo *SubscriptionPlanUpdateOne) Mutation() *SubscriptionPlanMutation {
	return spuo.mutation
}

// Where appends a list predicates to the SubscriptionPlanUpdate builder.
func (spuo *SubscriptionPlanUpdateOne) Where(ps ...predicate.SubscriptionPlan) *SubscriptionPlanUpdateOne {
	spuo.mutation.Where(ps...)
	return spuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (spuo *SubscriptionPlanUpdateOne) Select(field string, fields ...string) *SubscriptionPlanUpdateOne {
	spuo.fields = append([]string{field}, fields...)
	return spuo
}

// Save executes the query and returns the updated SubscriptionPlan entity.
func (spuo *SubscriptionPlanUpdateOne) Save(ctx context.Context) (*SubscriptionPlan, error) {
	spuo.defaults()
	return withHooks(ctx, spuo.sqlSave, spuo.mutation, spuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (spuo *SubscriptionPlanUpdateOne) SaveX(ctx context.Context) *SubscriptionPlan {
	node, err := spuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (spuo *SubscriptionPlanUpdateOne) Exec(ctx context.Context) error {
	_, err := spuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (spuo *SubscriptionPlanUpdateOne) ExecX(ctx context.Context) {
	if err := spuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (spuo *SubscriptionPlanUpdateOne) defaults() {
	if _, ok := spuo.mutation.UpdatedAt(); !ok {
		v := subscriptionplan.UpdateDefaultUpdatedAt()
		spuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (spuo *SubscriptionPlanUpdateOne) check() error {
	if v, ok := spuo.mutation.Name(); ok {
		if err := subscriptionplan.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.name": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.DisplayName(); ok {
		if err := subscriptionplan.DisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "display_name", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.display_name": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.Price(); ok {
		if err := subscriptionplan.PriceValidator(v); err != nil {
			return &ValidationError{Name: "price", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.price": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.Currency(); ok {
		if err := subscriptionplan.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.currency": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.BillingInterval(); ok {
		if err := subscriptionplan.BillingIntervalValidator(v); err != nil {
			return &ValidationError{Name: "billing_interval", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.billing_interval": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.CreditsIncluded(); ok {
		if err := subscriptionplan.CreditsIncludedValidator(v); err != nil {
			return &ValidationError{Name: "credits_included", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.credits_included": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.MonthlyCredits(); ok {
		if err := subscriptionplan.MonthlyCreditsValidator(v); err != nil {
			return &ValidationError{Name: "monthly_credits", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.monthly_credits": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.MaxUsers(); ok {
		if err := subscriptionplan.MaxUsersValidator(v); err != nil {
			return &ValidationError{Name: "max_users", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_users": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.MaxWorkspaces(); ok {
		if err := subscriptionplan.MaxWorkspacesValidator(v); err != nil {
			return &ValidationError{Name: "max_workspaces", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.max_workspaces": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.StripePriceID(); ok {
		if err := subscriptionplan.StripePriceIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_price_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_price_id": %w`, err)}
		}
	}
	if v, ok := spuo.mutation.StripeProductID(); ok {
		if err := subscriptionplan.StripeProductIDValidator(v); err != nil {
			return &ValidationError{Name: "stripe_product_id", err: fmt.Errorf(`ent: validator failed for field "SubscriptionPlan.stripe_product_id": %w`, err)}
		}
	}
	return nil
}

func (spuo *SubscriptionPlanUpdateOne) sqlSave(ctx context.Context) (_node *SubscriptionPlan, err error) {
	if err := spuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(subscriptionplan.Table, subscriptionplan.Columns, sqlgraph.NewFieldSpec(subscriptionplan.FieldID, field.TypeUUID))
	id, ok := spuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SubscriptionPlan.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := spuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, subscriptionplan.FieldID)
		for _, f := range fields {
			if !subscriptionplan.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != subscriptionplan.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := spuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := spuo.mutation.Name(); ok {
		_spec.SetField(subscriptionplan.FieldName, field.TypeString, value)
	}
	if value, ok := spuo.mutation.DisplayName(); ok {
		_spec.SetField(subscriptionplan.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := spuo.mutation.Description(); ok {
		_spec.SetField(subscriptionplan.FieldDescription, field.TypeString, value)
	}
	if spuo.mutation.DescriptionCleared() {
		_spec.ClearField(subscriptionplan.FieldDescription, field.TypeString)
	}
	if value, ok := spuo.mutation.Price(); ok {
		_spec.SetField(subscriptionplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := spuo.mutation.AddedPrice(); ok {
		_spec.AddField(subscriptionplan.FieldPrice, field.TypeInt64, value)
	}
	if value, ok := spuo.mutation.Currency(); ok {
		_spec.SetField(subscriptionplan.FieldCurrency, field.TypeString, value)
	}
	if value, ok := spuo.mutation.BillingInterval(); ok {
		_spec.SetField(subscriptionplan.FieldBillingInterval, field.TypeEnum, value)
	}
	if value, ok := spuo.mutation.CreditsIncluded(); ok {
		_spec.SetField(subscriptionplan.FieldCreditsIncluded, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.AddedCreditsIncluded(); ok {
		_spec.AddField(subscriptionplan.FieldCreditsIncluded, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.MonthlyCredits(); ok {
		_spec.SetField(subscriptionplan.FieldMonthlyCredits, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.AddedMonthlyCredits(); ok {
		_spec.AddField(subscriptionplan.FieldMonthlyCredits, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.IsUnlimited(); ok {
		_spec.SetField(subscriptionplan.FieldIsUnlimited, field.TypeBool, value)
	}
	if value, ok := spuo.mutation.Features(); ok {
		_spec.SetField(subscriptionplan.FieldFeatures, field.TypeJSON, value)
	}
	if value, ok := spuo.mutation.AppendedFeatures(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, subscriptionplan.FieldFeatures, value)
		})
	}
	if spuo.mutation.FeaturesCleared() {
		_spec.ClearField(subscriptionplan.FieldFeatures, field.TypeJSON)
	}
	if value, ok := spuo.mutation.Limits(); ok {
		_spec.SetField(subscriptionplan.FieldLimits, field.TypeJSON, value)
	}
	if spuo.mutation.LimitsCleared() {
		_spec.ClearField(subscriptionplan.FieldLimits, field.TypeJSON)
	}
	if value, ok := spuo.mutation.MaxUsers(); ok {
		_spec.SetField(subscriptionplan.FieldMaxUsers, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.AddedMaxUsers(); ok {
		_spec.AddField(subscriptionplan.FieldMaxUsers, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.MaxWorkspaces(); ok {
		_spec.SetField(subscriptionplan.FieldMaxWorkspaces, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.AddedMaxWorkspaces(); ok {
		_spec.AddField(subscriptionplan.FieldMaxWorkspaces, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.IsActive(); ok {
		_spec.SetField(subscriptionplan.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := spuo.mutation.IsFeatured(); ok {
		_spec.SetField(subscriptionplan.FieldIsFeatured, field.TypeBool, value)
	}
	if value, ok := spuo.mutation.SortOrder(); ok {
		_spec.SetField(subscriptionplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.AddedSortOrder(); ok {
		_spec.AddField(subscriptionplan.FieldSortOrder, field.TypeInt, value)
	}
	if value, ok := spuo.mutation.StripePriceID(); ok {
		_spec.SetField(subscriptionplan.FieldStripePriceID, field.TypeString, value)
	}
	if spuo.mutation.StripePriceIDCleared() {
		_spec.ClearField(subscriptionplan.FieldStripePriceID, field.TypeString)
	}
	if value, ok := spuo.mutation.StripeProductID(); ok {
		_spec.SetField(subscriptionplan.FieldStripeProductID, field.TypeString, value)
	}
	if spuo.mutation.StripeProductIDCleared() {
		_spec.ClearField(subscriptionplan.FieldStripeProductID, field.TypeString)
	}
	if value, ok := spuo.mutation.Metadata(); ok {
		_spec.SetField(subscriptionplan.FieldMetadata, field.TypeJSON, value)
	}
	if spuo.mutation.MetadataCleared() {
		_spec.ClearField(subscriptionplan.FieldMetadata, field.TypeJSON)
	}
	if value, ok := spuo.mutation.UpdatedAt(); ok {
		_spec.SetField(subscriptionplan.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &SubscriptionPlan{config: spuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, spuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{subscriptionplan.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	spuo.mutation.done = true
	return _node, nil
}

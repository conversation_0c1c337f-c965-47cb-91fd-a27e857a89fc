package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/social-content-ai/credit-service/config"
	"github.com/social-content-ai/pkg-shared/logging"
)

// RedisClient wraps Redis client with caching functionality
type RedisClient struct {
	client    *redis.Client
	keyPrefix string
	logger    logging.Logger
	
	// Cache TTL configurations
	creditBalanceTTL time.Duration
	defaultTTL       time.Duration
}

// NewRedisClient creates a new Redis client
func NewRedisClient(cfg *config.RedisConfig, logger logging.Logger) (*RedisClient, error) {
	// Create Redis client
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
		PoolSize: cfg.PoolSize,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	logger.WithFields(map[string]interface{}{
		"host": cfg.Host,
		"port": cfg.Port,
		"db":   cfg.DB,
	}).Info("Connected to Redis successfully")

	return &RedisClient{
		client:           rdb,
		keyPrefix:        cfg.Cache.KeyPrefix,
		logger:           logger,
		creditBalanceTTL: cfg.Cache.CreditBalanceTTL,
		defaultTTL:       cfg.Cache.DefaultTTL,
	}, nil
}

// Close closes the Redis connection
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// buildKey builds a cache key with prefix
func (r *RedisClient) buildKey(key string) string {
	return r.keyPrefix + key
}

// SetCreditBalance caches user credit balance
func (r *RedisClient) SetCreditBalance(ctx context.Context, userID string, balance interface{}) error {
	key := r.buildKey(fmt.Sprintf("credit_balance:%s", userID))
	
	data, err := json.Marshal(balance)
	if err != nil {
		return fmt.Errorf("failed to marshal credit balance: %w", err)
	}

	err = r.client.Set(ctx, key, data, r.creditBalanceTTL).Err()
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to cache credit balance")
		return fmt.Errorf("failed to cache credit balance: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"ttl":     r.creditBalanceTTL,
	}).Debug("Credit balance cached successfully")

	return nil
}

// GetCreditBalance retrieves cached user credit balance
func (r *RedisClient) GetCreditBalance(ctx context.Context, userID string, result interface{}) error {
	key := r.buildKey(fmt.Sprintf("credit_balance:%s", userID))
	
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get cached credit balance")
		return fmt.Errorf("failed to get cached credit balance: %w", err)
	}

	err = json.Unmarshal([]byte(data), result)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to unmarshal cached credit balance")
		return fmt.Errorf("failed to unmarshal cached credit balance: %w", err)
	}

	r.logger.WithField("user_id", userID).Debug("Credit balance retrieved from cache")
	return nil
}

// DeleteCreditBalance removes cached user credit balance
func (r *RedisClient) DeleteCreditBalance(ctx context.Context, userID string) error {
	key := r.buildKey(fmt.Sprintf("credit_balance:%s", userID))
	
	err := r.client.Del(ctx, key).Err()
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to delete cached credit balance")
		return fmt.Errorf("failed to delete cached credit balance: %w", err)
	}

	r.logger.WithField("user_id", userID).Debug("Credit balance cache invalidated")
	return nil
}

// Set caches any data with custom TTL
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	cacheKey := r.buildKey(key)
	
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	if ttl == 0 {
		ttl = r.defaultTTL
	}

	err = r.client.Set(ctx, cacheKey, data, ttl).Err()
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to cache data")
		return fmt.Errorf("failed to cache data: %w", err)
	}

	return nil
}

// Get retrieves cached data
func (r *RedisClient) Get(ctx context.Context, key string, result interface{}) error {
	cacheKey := r.buildKey(key)
	
	data, err := r.client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return fmt.Errorf("failed to get cached data: %w", err)
	}

	err = json.Unmarshal([]byte(data), result)
	if err != nil {
		return fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return nil
}

// Delete removes cached data
func (r *RedisClient) Delete(ctx context.Context, key string) error {
	cacheKey := r.buildKey(key)
	return r.client.Del(ctx, cacheKey).Err()
}

// Exists checks if key exists in cache
func (r *RedisClient) Exists(ctx context.Context, key string) (bool, error) {
	cacheKey := r.buildKey(key)
	count, err := r.client.Exists(ctx, cacheKey).Result()
	return count > 0, err
}

// TTL returns the remaining time to live of a key
func (r *RedisClient) TTL(ctx context.Context, key string) (time.Duration, error) {
	cacheKey := r.buildKey(key)
	return r.client.TTL(ctx, cacheKey).Result()
}

// FlushAll clears all cache (use with caution)
func (r *RedisClient) FlushAll(ctx context.Context) error {
	return r.client.FlushAll(ctx).Err()
}

// GetStats returns Redis client statistics
func (r *RedisClient) GetStats() *redis.PoolStats {
	return r.client.PoolStats()
}

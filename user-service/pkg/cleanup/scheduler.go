package cleanup

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// Scheduler manages cleanup jobs and schedules
type Scheduler struct {
	cleanupService *Service
	logger         logging.Logger
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	running        bool
	mu             sync.RWMutex
}

// NewScheduler creates a new cleanup scheduler
func NewScheduler(cleanupService *Service, logger logging.Logger) *Scheduler {
	return &Scheduler{
		cleanupService: cleanupService,
		logger:         logger,
	}
}

// Start starts the cleanup scheduler
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return nil // Already running
	}

	s.ctx, s.cancel = context.WithCancel(context.Background())
	s.running = true

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		if err := s.cleanupService.Start(s.ctx); err != nil && err != context.Canceled {
			s.logger.WithError(err).Error("Cleanup service error")
		}
	}()

	s.logger.Info("Cleanup scheduler started")
	return nil
}

// Stop stops the cleanup scheduler
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil // Already stopped
	}

	s.cancel()
	s.running = false

	// Wait for cleanup service to stop
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("Cleanup scheduler stopped")
		return nil
	case <-time.After(30 * time.Second):
		s.logger.Warn("Cleanup scheduler stop timeout")
		return nil
	}
}

// IsRunning returns whether the scheduler is running
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// GetStats returns cleanup statistics
func (s *Scheduler) GetStats() (*Stats, error) {
	if s.cleanupService == nil {
		return nil, nil
	}
	return s.cleanupService.GetStats(context.Background())
}

// TriggerCleanup manually triggers a cleanup cycle
func (s *Scheduler) TriggerCleanup() error {
	if !s.IsRunning() {
		return fmt.Errorf("scheduler is not running")
	}

	// Run cleanup in background
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cancel()

		if err := s.cleanupService.runCleanup(ctx); err != nil {
			s.logger.WithError(err).Error("Manual cleanup failed")
		} else {
			s.logger.Info("Manual cleanup completed")
		}
	}()

	return nil
}

# Integration Service - API Endpoints Summary

## 🎯 **Tổng quan**

Integration Service đã được cập nhật với **15 REST API endpoints mới** để client có thể tương tác đầy đủ với platform integrations. Tất cả endpoints đều được implement trong code và cập nhật trong `socialai-api.yaml`.

## 📋 **Danh sách API Endpoints**

### **1. Integration Management APIs**

#### **GET /api/v1/integrations**
- **Mục đích**: L<PERSON><PERSON> danh sách tài khoản platform đã kết nối
- **Query params**: `user_id` (required), `workspace_id`, `platform`, `status`, `page`, `limit`
- **Response**: <PERSON>h sách `PlatformAccount` với pagination
- **Handler**: `IntegrationHandler.ListIntegrations()`

#### **POST /api/v1/integrations/oauth/url**
- **<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> OAuth URL để kết nối platform
- **Body**: `user_id`, `platform`, `redirect_uri`, `requested_permissions`, `state`
- **Response**: OAuth authorization URL với state parameter
- **Handler**: `IntegrationHandler.GetOAuthURL()`

#### **GET /api/v1/integrations/{id}**
- **Mục đích**: Lấy thông tin chi tiết tài khoản platform
- **Path param**: `id` (integration ID)
- **Query param**: `user_id` (required)
- **Response**: `PlatformAccountDetail` với usage stats
- **Handler**: `IntegrationHandler.GetIntegration()`

#### **DELETE /api/v1/integrations/{id}**
- **Mục đích**: Ngắt kết nối tài khoản platform
- **Path param**: `id` (integration ID)
- **Body**: `user_id`, `revoke_token`
- **Response**: Success message
- **Handler**: `IntegrationHandler.DeleteIntegration()`

#### **POST /api/v1/integrations/{id}/refresh**
- **Mục đích**: Làm mới access token
- **Path param**: `id` (integration ID)
- **Body**: `user_id`, `force_refresh`
- **Response**: Token expiry info
- **Handler**: `IntegrationHandler.RefreshIntegration()`

### **2. Publishing APIs**

#### **POST /api/v1/publishing/posts**
- **Mục đích**: Đăng bài lên platform
- **Body**: `account_id`, `user_id`, `content`, `image_urls`, `hashtags`, `platform_options`, `scheduled_at`
- **Response**: Platform post ID và URL
- **Handler**: `PublishingHandler.PublishPost()`

#### **POST /api/v1/publishing/posts/analytics**
- **Mục đích**: Lấy analytics của bài viết
- **Body**: `account_id`, `platform_post_id`, `user_id`, `metrics`
- **Response**: Metrics data (likes, comments, shares, reach, impressions)
- **Handler**: `PublishingHandler.GetPostAnalytics()`

#### **POST /api/v1/publishing/accounts/analytics**
- **Mục đích**: Lấy analytics của tài khoản platform
- **Body**: `account_id`, `user_id`, `date_from`, `date_to`, `metrics`
- **Response**: Account metrics với time series data
- **Handler**: `PublishingHandler.GetAccountAnalytics()`

### **3. Platform Information APIs**

#### **GET /api/v1/platform/info**
- **Mục đích**: Lấy thông tin các platform được hỗ trợ
- **Response**: Danh sách `PlatformInfo` với features, permissions, rate limits
- **Handler**: `PlatformHandler.GetPlatformInfo()`

#### **POST /api/v1/platform/limits**
- **Mục đích**: Lấy giới hạn và usage của platform
- **Body**: `platform`, `account_id`
- **Response**: Rate limits, current usage, reset times
- **Handler**: `PlatformHandler.GetPlatformLimits()`

### **4. OAuth Callback APIs (Existing)**

#### **GET /oauth/{platform}/callback**
- **Platforms**: facebook, twitter, instagram, linkedin, youtube, tiktok
- **Mục đích**: Xử lý OAuth callback từ platforms
- **Handler**: `OAuthHandler.{Platform}Callback()`

### **5. Webhook APIs (Existing)**

#### **POST /webhooks/{platform}**
- **Platforms**: facebook, twitter, instagram, linkedin, youtube, tiktok
- **Mục đích**: Nhận webhook events từ platforms
- **Handler**: `WebhookHandler.{Platform}Webhook()`

## 🏗️ **Cấu trúc Code**

### **Handlers Structure**
```
api/restful/handlers/
├── base.go              # BaseHandler với shared dependencies
├── integration.go       # Integration management endpoints
├── publishing.go        # Publishing endpoints  
├── platform.go          # Platform info endpoints
├── oauth.go            # OAuth callback handlers
├── webhook.go          # Webhook handlers
├── health.go           # Health check
└── types.go            # Request/Response types
```

### **Routes Configuration**
```go
// API v1 routes
v1 := router.Group("/api/v1")
{
    // Integration management routes
    integrations := v1.Group("/integrations")
    {
        integrations.POST("/oauth/url", integrationHandler.GetOAuthURL)
        integrations.GET("", integrationHandler.ListIntegrations)
        integrations.GET("/:id", integrationHandler.GetIntegration)
        integrations.DELETE("/:id", integrationHandler.DeleteIntegration)
        integrations.POST("/:id/refresh", integrationHandler.RefreshIntegration)
    }

    // Publishing routes
    publishing := v1.Group("/publishing")
    {
        publishing.POST("/posts", publishingHandler.PublishPost)
        publishing.POST("/posts/analytics", publishingHandler.GetPostAnalytics)
        publishing.POST("/accounts/analytics", publishingHandler.GetAccountAnalytics)
    }

    // Platform routes
    platform := v1.Group("/platform")
    {
        platform.POST("/limits", platformHandler.GetPlatformLimits)
        platform.GET("/info", platformHandler.GetPlatformInfo)
    }
}
```

## 📊 **Data Models**

### **PlatformAccount Schema**
```yaml
PlatformAccount:
  properties:
    id: string                    # Integration ID
    user_id: string              # User ID
    workspace_id: string         # Workspace ID (optional)
    platform: enum               # facebook, instagram, etc.
    account_id: string           # Platform account ID
    username: string             # Platform username
    display_name: string         # Display name
    avatar_url: string           # Avatar URL
    status: enum                 # active, expired, error, disconnected
    permissions: array[string]   # Granted permissions
    metadata: object             # Platform-specific metadata
    connected_at: integer        # Unix timestamp
    last_used: integer           # Unix timestamp
```

### **PlatformInfo Schema**
```yaml
PlatformInfo:
  properties:
    name: string                 # Platform name
    display_name: string         # Display name
    features:                    # Supported features
      oauth: boolean
      publishing: boolean
      analytics: boolean
      webhooks: boolean
      scheduling: boolean
      image_upload: boolean
      video_upload: boolean
    permissions: array[string]   # Required permissions
    rate_limits: object          # Rate limit info
```

## 🔧 **Integration với Frontend**

### **OAuth Flow**
1. **Frontend** gọi `POST /api/v1/integrations/oauth/url` để lấy OAuth URL
2. **Frontend** redirect user đến OAuth URL
3. **Platform** redirect về callback URL với authorization code
4. **Integration Service** xử lý callback và kết nối tài khoản
5. **Frontend** có thể gọi `GET /api/v1/integrations` để kiểm tra kết nối

### **Publishing Flow**
1. **Frontend** gọi `GET /api/v1/integrations` để lấy danh sách tài khoản
2. **Frontend** gọi `POST /api/v1/publishing/posts` để đăng bài
3. **Frontend** gọi `POST /api/v1/publishing/posts/analytics` để xem metrics

### **Platform Management**
1. **Frontend** gọi `GET /api/v1/platform/info` để hiển thị platforms
2. **Frontend** gọi `POST /api/v1/platform/limits` để kiểm tra limits
3. **Frontend** gọi `DELETE /api/v1/integrations/{id}` để ngắt kết nối

## ✅ **Tính năng đã implement**

- ✅ **Complete OAuth flow** cho 6 platforms
- ✅ **Platform account management** (list, get, delete, refresh)
- ✅ **Content publishing** với platform options
- ✅ **Analytics retrieval** cho posts và accounts
- ✅ **Platform information** và rate limits
- ✅ **Webhook handling** cho tất cả platforms
- ✅ **Error handling** và validation
- ✅ **Security** với signature verification
- ✅ **Documentation** trong OpenAPI spec

## 🚀 **Ready for Production**

Integration Service hiện đã sẵn sàng cho production với:
- **15 REST API endpoints** đầy đủ chức năng
- **Complete OAuth implementation** cho 6 platforms
- **Comprehensive error handling** và logging
- **Security best practices** implemented
- **OpenAPI documentation** updated
- **Frontend-ready** với clear API contracts

Client có thể tích hợp ngay với các endpoints này để quản lý platform integrations một cách hoàn chỉnh!

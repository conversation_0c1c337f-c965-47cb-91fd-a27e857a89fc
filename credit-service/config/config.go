package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
	sharedconfig "github.com/social-content-ai/pkg-shared/config"
)

// Config represents the complete application configuration
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Payment  PaymentConfig  `mapstructure:"payment"`
	Services ServicesConfig `mapstructure:"services"`
	Metrics  MetricsConfig  `mapstructure:"metrics"`
	Logging  LoggingConfig  `mapstructure:"logging"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Kafka    KafkaConfig    `mapstructure:"kafka"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	Env             string        `mapstructure:"env"`
	GRPCPort        int           `mapstructure:"grpc_port"`
	HTTPPort        int           `mapstructure:"http_port"`
	Host            string        `mapstructure:"host"`
	ReadTimeout     time.Duration `mapstructure:"read_timeout"`
	WriteTimeout    time.Duration `mapstructure:"write_timeout"`
	IdleTimeout     time.Duration `mapstructure:"idle_timeout"`
	ShutdownTimeout time.Duration `mapstructure:"shutdown_timeout"`
}

// DatabaseConfig extends shared database config with SQLite support
type DatabaseConfig struct {
	sharedconfig.DatabaseConfig

	// Database type: "postgres" or "sqlite"
	Type string `mapstructure:"type"`

	// SQLite specific configuration
	SQLitePath string `mapstructure:"sqlite_path"`
}

// PaymentConfig contains payment processor configurations
type PaymentConfig struct {
	Stripe StripeConfig `mapstructure:"stripe"`
}

// StripeConfig contains Stripe payment configuration
type StripeConfig struct {
	SecretKey      string `mapstructure:"secret_key"`
	PublishableKey string `mapstructure:"publishable_key"`
	WebhookSecret  string `mapstructure:"webhook_secret"`
}

// ServicesConfig contains external service configurations
type ServicesConfig struct {
	UserService string `mapstructure:"user_service"`
}

// MetricsConfig contains metrics server configuration
type MetricsConfig struct {
	Port int `mapstructure:"port"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// JWTConfig contains JWT configuration
type JWTConfig struct {
	SecretKey     string        `mapstructure:"secret_key"`
	TokenDuration time.Duration `mapstructure:"token_duration"`
	Issuer        string        `mapstructure:"issuer"`
}

// KafkaConfig contains Kafka configuration
type KafkaConfig struct {
	Brokers []string `mapstructure:"brokers"`
	Topics  struct {
		CreditEvents  string `mapstructure:"credit_events"`
		BillingEvents string `mapstructure:"billing_events"`
	} `mapstructure:"topics"`
}

// GetDriverName returns the appropriate database driver name
func (c *DatabaseConfig) GetDriverName() string {
	switch c.Type {
	case "sqlite":
		return "sqlite3"
	case "postgres":
		return "postgres"
	default:
		return "postgres" // Default to postgres
	}
}

// GetDSN returns the database connection string based on the database type
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "sqlite":
		// Enable foreign keys and other SQLite pragmas
		return c.SQLitePath + "?_fk=1&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=1000"
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	default:
		// Default to postgres
		return fmt.Sprintf("host=%s port=%d user=%s dbname=%s password=%s sslmode=%s",
			c.Host, c.Port, c.User, c.Name, c.Password, c.SSLMode)
	}
}

// CreateDataDirectory creates the data directory for SQLite if needed
func (c *DatabaseConfig) CreateDataDirectory() error {
	if c.Type == "sqlite" {
		dir := filepath.Dir(c.SQLitePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create SQLite data directory %s: %w", dir, err)
		}
	}
	return nil
}

// Load loads configuration from files and environment variables
func Load(configPath string) (*Config, error) {
	// Set default values
	viper.SetDefault("server.env", "development")
	viper.SetDefault("server.grpc_port", 50053)
	viper.SetDefault("server.http_port", 8083)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")
	viper.SetDefault("server.shutdown_timeout", "30s")

	// Database defaults
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "postgres")
	viper.SetDefault("database.name", "credit_service_db")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.sqlite_path", "./data/credit_service.db")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")
	viper.SetDefault("database.conn_max_idle_time", "5m")

	// Services defaults
	viper.SetDefault("services.user_service", "localhost:50051")

	// Metrics defaults
	viper.SetDefault("metrics.port", 9093)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// JWT defaults
	viper.SetDefault("jwt.secret_key", "your-secret-key")
	viper.SetDefault("jwt.token_duration", "24h")
	viper.SetDefault("jwt.issuer", "credit-service")

	// Kafka defaults
	viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
	viper.SetDefault("kafka.topics.credit_events", "credit.events")
	viper.SetDefault("kafka.topics.billing_events", "billing.events")

	// Set config file path
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath("./config")
	viper.AddConfigPath(".")

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvPrefix("CREDIT_SERVICE")

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found, continue with defaults and env vars
	}

	// Unmarshal config
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

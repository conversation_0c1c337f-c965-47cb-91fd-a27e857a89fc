package handlers

import (
	"context"

	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
)

// CreditServiceServer delegates all methods to appropriate handlers
// This implements the full CreditService interface by combining all handlers

// Basic credit operations - delegated to CreditServiceHandler
func (s *CreditServiceServer) GetUserCredit(ctx context.Context, req *creditv1.GetUserCreditRequest) (*creditv1.UserCredit, error) {
	return s.creditHandler.GetUserCredit(ctx, req)
}

func (s *CreditServiceServer) ValidateCredit(ctx context.Context, req *creditv1.ValidateCreditRequest) (*creditv1.ValidateCreditResponse, error) {
	return s.creditHandler.ValidateCredit(ctx, req)
}

func (s *CreditServiceServer) AddCredits(ctx context.Context, req *creditv1.AddCreditsRequest) (*creditv1.UserCredit, error) {
	return s.creditHandler.AddCredits(ctx, req)
}

func (s *CreditServiceServer) DeductCredits(ctx context.Context, req *creditv1.DeductCreditsRequest) (*creditv1.UserCredit, error) {
	return s.creditHandler.DeductCredits(ctx, req)
}

// Transaction operations - delegated to TransactionServiceHandler
func (s *CreditServiceServer) ListTransactions(ctx context.Context, req *creditv1.ListTransactionsRequest) (*creditv1.ListTransactionsResponse, error) {
	// Create a transaction handler instance for this call
	transactionHandler := NewTransactionServiceHandler(
		s.creditHandler.transactionUseCase,
		s.creditHandler.logger,
	)
	return transactionHandler.ListTransactions(ctx, req)
}

// Subscription management - delegated to SubscriptionServiceHandler
func (s *CreditServiceServer) CreateSubscription(ctx context.Context, req *creditv1.CreateSubscriptionRequest) (*creditv1.CreateSubscriptionResponse, error) {
	return s.subscriptionHandler.CreateSubscription(ctx, req)
}

func (s *CreditServiceServer) GetSubscription(ctx context.Context, req *creditv1.GetSubscriptionRequest) (*creditv1.Subscription, error) {
	return s.subscriptionHandler.GetSubscription(ctx, req)
}

func (s *CreditServiceServer) ListSubscriptions(ctx context.Context, req *creditv1.ListSubscriptionsRequest) (*creditv1.ListSubscriptionsResponse, error) {
	return s.subscriptionHandler.ListSubscriptions(ctx, req)
}

func (s *CreditServiceServer) UpdateSubscription(ctx context.Context, req *creditv1.UpdateSubscriptionRequest) (*creditv1.Subscription, error) {
	return s.subscriptionHandler.UpdateSubscription(ctx, req)
}

func (s *CreditServiceServer) CancelSubscription(ctx context.Context, req *creditv1.CancelSubscriptionRequest) (*creditv1.Empty, error) {
	return s.subscriptionHandler.CancelSubscription(ctx, req)
}

// Payment method management - delegated to PaymentServiceHandler
func (s *CreditServiceServer) GetPaymentMethods(ctx context.Context, req *creditv1.GetPaymentMethodsRequest) (*creditv1.GetPaymentMethodsResponse, error) {
	return s.paymentHandler.GetPaymentMethods(ctx, req)
}

func (s *CreditServiceServer) AddPaymentMethod(ctx context.Context, req *creditv1.AddPaymentMethodRequest) (*creditv1.PaymentMethod, error) {
	return s.paymentHandler.AddPaymentMethod(ctx, req)
}

func (s *CreditServiceServer) RemovePaymentMethod(ctx context.Context, req *creditv1.RemovePaymentMethodRequest) (*creditv1.Empty, error) {
	return s.paymentHandler.RemovePaymentMethod(ctx, req)
}

// Bank transfer operations - delegated to PaymentServiceHandler
func (s *CreditServiceServer) ConfirmBankTransfer(ctx context.Context, req *creditv1.ConfirmBankTransferRequest) (*creditv1.ConfirmBankTransferResponse, error) {
	return s.paymentHandler.ConfirmBankTransfer(ctx, req)
}

// Recurring payment processing - delegated to PaymentServiceHandler
func (s *CreditServiceServer) ProcessRecurringPayment(ctx context.Context, req *creditv1.ProcessRecurringPaymentRequest) (*creditv1.ProcessRecurringPaymentResponse, error) {
	return s.paymentHandler.ProcessRecurringPayment(ctx, req)
}

package subscription

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/pkg/models"
)

// ActivateSubscription activates a subscription after successful payment
func (s *service) ActivateSubscription(ctx context.Context, req *models.ActivateSubscriptionRequest) (*models.SubscriptionActivationInfo, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"plan_id":    req.PlanID,
		"payment_id": req.PaymentID,
	}).Info("Activating subscription")

	// Parse IDs
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	planUUID, err := uuid.Parse(req.PlanID)
	if err != nil {
		return nil, fmt.Errorf("invalid plan ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get subscription plan
	plan, err := tx.SubscriptionPlan.Query().
		Where(subscriptionplan.ID(planUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("subscription plan not found")
		}
		return nil, fmt.Errorf("failed to get subscription plan: %w", err)
	}

	// Check if user already has an active subscription
	existingSubscription, err := tx.Subscription.Query().
		Where(
			subscription.UserID(userUUID),
			subscription.StatusIn(subscription.StatusActive, subscription.StatusTrial),
		).
		First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, fmt.Errorf("failed to check existing subscription: %w", err)
	}

	// If user has existing subscription, cancel it first
	if existingSubscription != nil {
		_, err = tx.Subscription.UpdateOne(existingSubscription).
			SetStatus(subscription.StatusCancelled).
			SetCancelledAt(time.Now()).
			Save(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to cancel existing subscription: %w", err)
		}
	}

	// Calculate subscription dates
	now := time.Now()
	var startsAt, endsAt time.Time

	// Check if plan has trial period (assume 7 days trial for all plans)
	trialDays := 7
	if trialDays > 0 && (existingSubscription == nil || existingSubscription.Status != subscription.StatusTrial) {
		// Start trial
		startsAt = now
		endsAt = now.AddDate(0, 0, trialDays)
		status := subscription.StatusTrial

		// Create subscription with trial
		newSubscription, err := tx.Subscription.Create().
			SetUserID(userUUID).
			SetPlanID(planUUID).
			SetStatus(status).
			SetCurrentPeriodStart(startsAt).
			SetCurrentPeriodEnd(endsAt).
			SetTrialStart(startsAt).
			SetTrialEnd(endsAt).
			SetCancelAtPeriodEnd(false).
			Save(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to create trial subscription: %w", err)
		}

		// Add trial credits if applicable
		if plan.CreditsIncluded > 0 {
			creditReq := &models.AddCreditsRequest{
				UserID:      req.UserID,
				Amount:      plan.CreditsIncluded,
				Source:      "subscription_trial",
				Description: fmt.Sprintf("Trial credits for plan: %s", plan.Name),
				PaymentID:   req.PaymentID,
			}

			_, err = s.transactionUC.AddCreditsWithTransaction(ctx, creditReq)
			if err != nil {
				s.logger.WithError(err).Error("Failed to add trial credits")
				// Don't fail the subscription activation for credit errors
				return nil, fmt.Errorf("failed to add trial credits: %w", err)
			}
		}

		// Commit transaction
		err = tx.Commit()
		if err != nil {
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}

		// TODO: Publish SubscriptionActivated event to Kafka

		return &models.SubscriptionActivationInfo{
			SubscriptionID: newSubscription.ID.String(),
			PlanName:       plan.Name,
			StartsAt:       startsAt,
			EndsAt:         endsAt,
			CreditsAdded:   int32(plan.CreditsIncluded),
		}, nil
	} else {
		// Start paid subscription immediately
		startsAt = now
		endsAt = s.calculateNextBillingDate(now, string(plan.BillingInterval))

		// Create active subscription
		newSubscription, err := tx.Subscription.Create().
			SetUserID(userUUID).
			SetPlanID(planUUID).
			SetStatus(subscription.StatusActive).
			SetCurrentPeriodStart(startsAt).
			SetCurrentPeriodEnd(endsAt).
			SetCancelAtPeriodEnd(false).
			Save(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to create subscription: %w", err)
		}

		// Add subscription credits
		if plan.CreditsIncluded > 0 {
			creditReq := &models.AddCreditsRequest{
				UserID:      req.UserID,
				Amount:      plan.CreditsIncluded,
				Source:      "subscription_activation",
				Description: fmt.Sprintf("Subscription credits for plan: %s", plan.Name),
				PaymentID:   req.PaymentID,
			}

			_, err = s.transactionUC.AddCreditsWithTransaction(ctx, creditReq)
			if err != nil {
				s.logger.WithError(err).Error("Failed to add subscription credits")
				// Don't fail the subscription activation for credit errors
				return nil, fmt.Errorf("failed to add subscription credits: %w", err)
			}
		}

		// Commit transaction
		err = tx.Commit()
		if err != nil {
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}

		// TODO: Publish SubscriptionActivated event to Kafka

		return &models.SubscriptionActivationInfo{
			SubscriptionID: newSubscription.ID.String(),
			PlanName:       plan.Name,
			StartsAt:       startsAt,
			EndsAt:         endsAt,
			CreditsAdded:   int32(plan.CreditsIncluded),
		}, nil
	}
}

// calculateNextBillingDate calculates the next billing date based on billing interval
func (s *service) calculateNextBillingDate(startDate time.Time, billingInterval string) time.Time {
	switch billingInterval {
	case "monthly":
		return startDate.AddDate(0, 1, 0)
	case "yearly":
		return startDate.AddDate(1, 0, 0)
	case "weekly":
		return startDate.AddDate(0, 0, 7)
	case "daily":
		return startDate.AddDate(0, 0, 1)
	default:
		// Default to monthly
		return startDate.AddDate(0, 1, 0)
	}
}

// RenewSubscription renews a subscription for the next billing period
func (s *service) RenewSubscription(ctx context.Context, subscriptionID string) (*models.SubscriptionResponse, error) {
	s.logger.WithField("subscription_id", subscriptionID).Info("Renewing subscription")

	// Parse subscription ID
	subscriptionUUID, err := uuid.Parse(subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("invalid subscription ID format: %w", err)
	}

	// Start transaction
	tx, err := s.writeDB.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Get subscription
	sub, err := tx.Subscription.Query().
		Where(subscription.ID(subscriptionUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("subscription not found")
		}
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// Check if subscription can be renewed
	if sub.Status != subscription.StatusActive && sub.Status != subscription.StatusTrial {
		return nil, fmt.Errorf("subscription cannot be renewed in current status: %s", sub.Status)
	}

	// Get plan separately
	plan, err := tx.SubscriptionPlan.Query().
		Where(subscriptionplan.ID(sub.PlanID)).
		First(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription plan: %w", err)
	}

	// Calculate new billing period
	newPeriodStart := sub.CurrentPeriodEnd
	newPeriodEnd := s.calculateNextBillingDate(newPeriodStart, string(plan.BillingInterval))

	// Update subscription
	updatedSub, err := tx.Subscription.UpdateOne(sub).
		SetStatus(subscription.StatusActive).
		SetCurrentPeriodStart(newPeriodStart).
		SetCurrentPeriodEnd(newPeriodEnd).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to update subscription: %w", err)
	}

	// Add renewal credits
	if plan.CreditsIncluded > 0 {
		creditReq := &models.AddCreditsRequest{
			UserID:      sub.UserID.String(),
			Amount:      plan.CreditsIncluded,
			Source:      "subscription_renewal",
			Description: fmt.Sprintf("Renewal credits for plan: %s", plan.Name),
		}

		_, err = s.transactionUC.AddCreditsWithTransaction(ctx, creditReq)
		if err != nil {
			s.logger.WithError(err).Error("Failed to add renewal credits")
			// Don't fail the renewal for credit errors
		}
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// TODO: Publish SubscriptionRenewed event to Kafka

	return s.convertSubscriptionToModel(updatedSub, plan), nil
}

// convertSubscriptionToModel converts ent subscription to model
func (s *service) convertSubscriptionToModel(sub *ent.Subscription, plan *ent.SubscriptionPlan) *models.SubscriptionResponse {
	response := &models.SubscriptionResponse{
		BaseModel: models.BaseModel{
			ID:        sub.ID.String(),
			CreatedAt: sub.CreatedAt,
			UpdatedAt: sub.UpdatedAt,
		},
		UserID:             sub.UserID.String(),
		PlanID:             sub.PlanID.String(),
		Status:             string(sub.Status),
		CurrentPeriodStart: sub.CurrentPeriodStart,
		CurrentPeriodEnd:   sub.CurrentPeriodEnd,
		CancelAtPeriodEnd:  sub.CancelAtPeriodEnd,
	}

	if plan != nil {
		response.PlanName = plan.Name
		response.BillingInterval = string(plan.BillingInterval)
		response.Amount = plan.Price
		response.Currency = plan.Currency
	}

	if !sub.TrialStart.IsZero() {
		response.TrialStart = &sub.TrialStart
	}

	if !sub.TrialEnd.IsZero() {
		response.TrialEnd = &sub.TrialEnd
	}

	if !sub.CancelledAt.IsZero() {
		response.CanceledAt = &sub.CancelledAt
	}

	return response
}

package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
)

// AuthMiddleware provides JWT authentication middleware
type AuthMiddleware struct {
	logger logging.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(logger logging.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		logger: logger,
	}
}

// RequireAuth middleware that requires valid JWT token
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		token := tokenParts[1]
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token is required"})
			c.Abort()
			return
		}

		// TODO: Validate JWT token with User Service
		// For now, extract user ID from token (mock implementation)
		userID, err := m.validateToken(token)
		if err != nil {
			m.logger.WithError(err).Error("Token validation failed")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Set user ID in context
		c.Set("user_id", userID)
		c.Set("token", token)

		c.Next()
	}
}

// OptionalAuth middleware that allows both authenticated and unauthenticated requests
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No auth header, continue without user context
			c.Next()
			return
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			// Invalid format, continue without user context
			c.Next()
			return
		}

		token := tokenParts[1]
		if token == "" {
			// Empty token, continue without user context
			c.Next()
			return
		}

		// Try to validate token
		userID, err := m.validateToken(token)
		if err != nil {
			// Invalid token, continue without user context
			m.logger.WithError(err).Debug("Optional auth token validation failed")
			c.Next()
			return
		}

		// Set user ID in context if token is valid
		c.Set("user_id", userID)
		c.Set("token", token)

		c.Next()
	}
}

// validateToken validates JWT token and returns user ID
// TODO: Implement proper JWT validation with User Service
func (m *AuthMiddleware) validateToken(token string) (string, error) {
	// Mock implementation - in production, this should:
	// 1. Validate JWT signature
	// 2. Check token expiration
	// 3. Verify with User Service if needed
	// 4. Extract user ID from claims

	// For development, accept any non-empty token and return mock user ID
	if token == "mock-token" {
		return "mock-user-id", nil
	}

	// For now, return error for any other token
	return "", gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic}
}

// AdminAuth middleware that requires admin privileges
func (m *AuthMiddleware) AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// First require authentication
		m.RequireAuth()(c)
		if c.IsAborted() {
			return
		}

		// TODO: Check if user has admin privileges
		// For now, allow all authenticated users
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		// TODO: Implement admin check with User Service
		// For now, mock admin check
		if userID.(string) == "admin-user-id" {
			c.Next()
			return
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "Admin privileges required"})
		c.Abort()
	}
}

// CORS middleware for handling cross-origin requests
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestLogger middleware for logging HTTP requests
func RequestLogger(logger logging.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.WithFields(map[string]interface{}{
			"method":     param.Method,
			"path":       param.Path,
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"user_agent": param.Request.UserAgent(),
		}).Info("HTTP request")
		return ""
	})
}

// ErrorHandler middleware for handling errors
func ErrorHandler(logger logging.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		logger.WithField("panic", recovered).Error("HTTP request panic")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
	})
}

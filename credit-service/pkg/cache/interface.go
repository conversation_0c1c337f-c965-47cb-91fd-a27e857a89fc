package cache

import (
	"context"
	"errors"
	"time"
)

// ErrCacheMiss is returned when a key is not found in cache
var ErrCacheMiss = errors.New("cache miss")

// Cache defines the interface for caching operations
type Cache interface {
	// Credit balance specific operations
	SetCreditBalance(ctx context.Context, userID string, balance interface{}) error
	GetCreditBalance(ctx context.Context, userID string, result interface{}) error
	DeleteCreditBalance(ctx context.Context, userID string) error

	// Generic cache operations
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Get(ctx context.Context, key string, result interface{}) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	TTL(ctx context.Context, key string) (time.Duration, error)

	// Utility operations
	FlushAll(ctx context.Context) error
	Close() error
}

// IsCacheMiss checks if the error is a cache miss
func IsCacheMiss(err error) bool {
	return errors.Is(err, ErrCacheMiss)
}

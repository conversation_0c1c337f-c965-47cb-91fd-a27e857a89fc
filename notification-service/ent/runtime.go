// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notification"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/notification-service/ent/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	notificationFields := schema.Notification{}.Fields()
	_ = notificationFields
	// notificationDescTitle is the schema descriptor for title field.
	notificationDescTitle := notificationFields[3].Descriptor()
	// notification.TitleValidator is a validator for the "title" field. It is called by the builders before save.
	notification.TitleValidator = notificationDescTitle.Validators[0].(func(string) error)
	// notificationDescReferenceID is the schema descriptor for reference_id field.
	notificationDescReferenceID := notificationFields[9].Descriptor()
	// notification.ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	notification.ReferenceIDValidator = notificationDescReferenceID.Validators[0].(func(string) error)
	// notificationDescReferenceType is the schema descriptor for reference_type field.
	notificationDescReferenceType := notificationFields[10].Descriptor()
	// notification.ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	notification.ReferenceTypeValidator = notificationDescReferenceType.Validators[0].(func(string) error)
	// notificationDescActionURL is the schema descriptor for action_url field.
	notificationDescActionURL := notificationFields[17].Descriptor()
	// notification.ActionURLValidator is a validator for the "action_url" field. It is called by the builders before save.
	notification.ActionURLValidator = notificationDescActionURL.Validators[0].(func(string) error)
	// notificationDescRetryCount is the schema descriptor for retry_count field.
	notificationDescRetryCount := notificationFields[19].Descriptor()
	// notification.DefaultRetryCount holds the default value on creation for the retry_count field.
	notification.DefaultRetryCount = notificationDescRetryCount.Default.(int)
	// notificationDescErrorMessage is the schema descriptor for error_message field.
	notificationDescErrorMessage := notificationFields[21].Descriptor()
	// notification.ErrorMessageValidator is a validator for the "error_message" field. It is called by the builders before save.
	notification.ErrorMessageValidator = notificationDescErrorMessage.Validators[0].(func(string) error)
	// notificationDescCreatedAt is the schema descriptor for created_at field.
	notificationDescCreatedAt := notificationFields[23].Descriptor()
	// notification.DefaultCreatedAt holds the default value on creation for the created_at field.
	notification.DefaultCreatedAt = notificationDescCreatedAt.Default.(func() time.Time)
	// notificationDescUpdatedAt is the schema descriptor for updated_at field.
	notificationDescUpdatedAt := notificationFields[24].Descriptor()
	// notification.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	notification.DefaultUpdatedAt = notificationDescUpdatedAt.Default.(func() time.Time)
	// notification.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	notification.UpdateDefaultUpdatedAt = notificationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// notificationDescID is the schema descriptor for id field.
	notificationDescID := notificationFields[0].Descriptor()
	// notification.DefaultID holds the default value on creation for the id field.
	notification.DefaultID = notificationDescID.Default.(func() uuid.UUID)
	notificationpreferenceFields := schema.NotificationPreference{}.Fields()
	_ = notificationpreferenceFields
	// notificationpreferenceDescEmailEnabled is the schema descriptor for email_enabled field.
	notificationpreferenceDescEmailEnabled := notificationpreferenceFields[2].Descriptor()
	// notificationpreference.DefaultEmailEnabled holds the default value on creation for the email_enabled field.
	notificationpreference.DefaultEmailEnabled = notificationpreferenceDescEmailEnabled.Default.(bool)
	// notificationpreferenceDescPushEnabled is the schema descriptor for push_enabled field.
	notificationpreferenceDescPushEnabled := notificationpreferenceFields[3].Descriptor()
	// notificationpreference.DefaultPushEnabled holds the default value on creation for the push_enabled field.
	notificationpreference.DefaultPushEnabled = notificationpreferenceDescPushEnabled.Default.(bool)
	// notificationpreferenceDescInAppEnabled is the schema descriptor for in_app_enabled field.
	notificationpreferenceDescInAppEnabled := notificationpreferenceFields[4].Descriptor()
	// notificationpreference.DefaultInAppEnabled holds the default value on creation for the in_app_enabled field.
	notificationpreference.DefaultInAppEnabled = notificationpreferenceDescInAppEnabled.Default.(bool)
	// notificationpreferenceDescSmsEnabled is the schema descriptor for sms_enabled field.
	notificationpreferenceDescSmsEnabled := notificationpreferenceFields[5].Descriptor()
	// notificationpreference.DefaultSmsEnabled holds the default value on creation for the sms_enabled field.
	notificationpreference.DefaultSmsEnabled = notificationpreferenceDescSmsEnabled.Default.(bool)
	// notificationpreferenceDescTimezone is the schema descriptor for timezone field.
	notificationpreferenceDescTimezone := notificationpreferenceFields[8].Descriptor()
	// notificationpreference.DefaultTimezone holds the default value on creation for the timezone field.
	notificationpreference.DefaultTimezone = notificationpreferenceDescTimezone.Default.(string)
	// notificationpreference.TimezoneValidator is a validator for the "timezone" field. It is called by the builders before save.
	notificationpreference.TimezoneValidator = notificationpreferenceDescTimezone.Validators[0].(func(string) error)
	// notificationpreferenceDescLanguage is the schema descriptor for language field.
	notificationpreferenceDescLanguage := notificationpreferenceFields[9].Descriptor()
	// notificationpreference.DefaultLanguage holds the default value on creation for the language field.
	notificationpreference.DefaultLanguage = notificationpreferenceDescLanguage.Default.(string)
	// notificationpreference.LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	notificationpreference.LanguageValidator = notificationpreferenceDescLanguage.Validators[0].(func(string) error)
	// notificationpreferenceDescMarketingEmails is the schema descriptor for marketing_emails field.
	notificationpreferenceDescMarketingEmails := notificationpreferenceFields[13].Descriptor()
	// notificationpreference.DefaultMarketingEmails holds the default value on creation for the marketing_emails field.
	notificationpreference.DefaultMarketingEmails = notificationpreferenceDescMarketingEmails.Default.(bool)
	// notificationpreferenceDescProductUpdates is the schema descriptor for product_updates field.
	notificationpreferenceDescProductUpdates := notificationpreferenceFields[14].Descriptor()
	// notificationpreference.DefaultProductUpdates holds the default value on creation for the product_updates field.
	notificationpreference.DefaultProductUpdates = notificationpreferenceDescProductUpdates.Default.(bool)
	// notificationpreferenceDescSecurityAlerts is the schema descriptor for security_alerts field.
	notificationpreferenceDescSecurityAlerts := notificationpreferenceFields[15].Descriptor()
	// notificationpreference.DefaultSecurityAlerts holds the default value on creation for the security_alerts field.
	notificationpreference.DefaultSecurityAlerts = notificationpreferenceDescSecurityAlerts.Default.(bool)
	// notificationpreferenceDescCreatedAt is the schema descriptor for created_at field.
	notificationpreferenceDescCreatedAt := notificationpreferenceFields[17].Descriptor()
	// notificationpreference.DefaultCreatedAt holds the default value on creation for the created_at field.
	notificationpreference.DefaultCreatedAt = notificationpreferenceDescCreatedAt.Default.(func() time.Time)
	// notificationpreferenceDescUpdatedAt is the schema descriptor for updated_at field.
	notificationpreferenceDescUpdatedAt := notificationpreferenceFields[18].Descriptor()
	// notificationpreference.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	notificationpreference.DefaultUpdatedAt = notificationpreferenceDescUpdatedAt.Default.(func() time.Time)
	// notificationpreference.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	notificationpreference.UpdateDefaultUpdatedAt = notificationpreferenceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// notificationpreferenceDescID is the schema descriptor for id field.
	notificationpreferenceDescID := notificationpreferenceFields[0].Descriptor()
	// notificationpreference.DefaultID holds the default value on creation for the id field.
	notificationpreference.DefaultID = notificationpreferenceDescID.Default.(func() uuid.UUID)
	notificationtemplateFields := schema.NotificationTemplate{}.Fields()
	_ = notificationtemplateFields
	// notificationtemplateDescEventType is the schema descriptor for event_type field.
	notificationtemplateDescEventType := notificationtemplateFields[1].Descriptor()
	// notificationtemplate.EventTypeValidator is a validator for the "event_type" field. It is called by the builders before save.
	notificationtemplate.EventTypeValidator = notificationtemplateDescEventType.Validators[0].(func(string) error)
	// notificationtemplateDescName is the schema descriptor for name field.
	notificationtemplateDescName := notificationtemplateFields[2].Descriptor()
	// notificationtemplate.NameValidator is a validator for the "name" field. It is called by the builders before save.
	notificationtemplate.NameValidator = notificationtemplateDescName.Validators[0].(func(string) error)
	// notificationtemplateDescIsActive is the schema descriptor for is_active field.
	notificationtemplateDescIsActive := notificationtemplateFields[5].Descriptor()
	// notificationtemplate.DefaultIsActive holds the default value on creation for the is_active field.
	notificationtemplate.DefaultIsActive = notificationtemplateDescIsActive.Default.(bool)
	// notificationtemplateDescLanguage is the schema descriptor for language field.
	notificationtemplateDescLanguage := notificationtemplateFields[9].Descriptor()
	// notificationtemplate.DefaultLanguage holds the default value on creation for the language field.
	notificationtemplate.DefaultLanguage = notificationtemplateDescLanguage.Default.(string)
	// notificationtemplate.LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	notificationtemplate.LanguageValidator = notificationtemplateDescLanguage.Validators[0].(func(string) error)
	// notificationtemplateDescVersion is the schema descriptor for version field.
	notificationtemplateDescVersion := notificationtemplateFields[10].Descriptor()
	// notificationtemplate.DefaultVersion holds the default value on creation for the version field.
	notificationtemplate.DefaultVersion = notificationtemplateDescVersion.Default.(string)
	// notificationtemplate.VersionValidator is a validator for the "version" field. It is called by the builders before save.
	notificationtemplate.VersionValidator = notificationtemplateDescVersion.Validators[0].(func(string) error)
	// notificationtemplateDescCreatedBy is the schema descriptor for created_by field.
	notificationtemplateDescCreatedBy := notificationtemplateFields[12].Descriptor()
	// notificationtemplate.CreatedByValidator is a validator for the "created_by" field. It is called by the builders before save.
	notificationtemplate.CreatedByValidator = notificationtemplateDescCreatedBy.Validators[0].(func(string) error)
	// notificationtemplateDescCreatedAt is the schema descriptor for created_at field.
	notificationtemplateDescCreatedAt := notificationtemplateFields[13].Descriptor()
	// notificationtemplate.DefaultCreatedAt holds the default value on creation for the created_at field.
	notificationtemplate.DefaultCreatedAt = notificationtemplateDescCreatedAt.Default.(func() time.Time)
	// notificationtemplateDescUpdatedAt is the schema descriptor for updated_at field.
	notificationtemplateDescUpdatedAt := notificationtemplateFields[14].Descriptor()
	// notificationtemplate.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	notificationtemplate.DefaultUpdatedAt = notificationtemplateDescUpdatedAt.Default.(func() time.Time)
	// notificationtemplate.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	notificationtemplate.UpdateDefaultUpdatedAt = notificationtemplateDescUpdatedAt.UpdateDefault.(func() time.Time)
	// notificationtemplateDescID is the schema descriptor for id field.
	notificationtemplateDescID := notificationtemplateFields[0].Descriptor()
	// notificationtemplate.DefaultID holds the default value on creation for the id field.
	notificationtemplate.DefaultID = notificationtemplateDescID.Default.(func() uuid.UUID)
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

// WorkspaceUpdate is the builder for updating Workspace entities.
type WorkspaceUpdate struct {
	config
	hooks    []Hook
	mutation *WorkspaceMutation
}

// Where appends a list predicates to the WorkspaceUpdate builder.
func (wu *WorkspaceUpdate) Where(ps ...predicate.Workspace) *WorkspaceUpdate {
	wu.mutation.Where(ps...)
	return wu
}

// SetOwnerID sets the "owner_id" field.
func (wu *WorkspaceUpdate) SetOwnerID(u uuid.UUID) *WorkspaceUpdate {
	wu.mutation.SetOwnerID(u)
	return wu
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableOwnerID(u *uuid.UUID) *WorkspaceUpdate {
	if u != nil {
		wu.SetOwnerID(*u)
	}
	return wu
}

// SetName sets the "name" field.
func (wu *WorkspaceUpdate) SetName(s string) *WorkspaceUpdate {
	wu.mutation.SetName(s)
	return wu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableName(s *string) *WorkspaceUpdate {
	if s != nil {
		wu.SetName(*s)
	}
	return wu
}

// SetDescription sets the "description" field.
func (wu *WorkspaceUpdate) SetDescription(s string) *WorkspaceUpdate {
	wu.mutation.SetDescription(s)
	return wu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableDescription(s *string) *WorkspaceUpdate {
	if s != nil {
		wu.SetDescription(*s)
	}
	return wu
}

// ClearDescription clears the value of the "description" field.
func (wu *WorkspaceUpdate) ClearDescription() *WorkspaceUpdate {
	wu.mutation.ClearDescription()
	return wu
}

// SetColor sets the "color" field.
func (wu *WorkspaceUpdate) SetColor(s string) *WorkspaceUpdate {
	wu.mutation.SetColor(s)
	return wu
}

// SetNillableColor sets the "color" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableColor(s *string) *WorkspaceUpdate {
	if s != nil {
		wu.SetColor(*s)
	}
	return wu
}

// SetIcon sets the "icon" field.
func (wu *WorkspaceUpdate) SetIcon(s string) *WorkspaceUpdate {
	wu.mutation.SetIcon(s)
	return wu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableIcon(s *string) *WorkspaceUpdate {
	if s != nil {
		wu.SetIcon(*s)
	}
	return wu
}

// ClearIcon clears the value of the "icon" field.
func (wu *WorkspaceUpdate) ClearIcon() *WorkspaceUpdate {
	wu.mutation.ClearIcon()
	return wu
}

// SetSettings sets the "settings" field.
func (wu *WorkspaceUpdate) SetSettings(m map[string]interface{}) *WorkspaceUpdate {
	wu.mutation.SetSettings(m)
	return wu
}

// ClearSettings clears the value of the "settings" field.
func (wu *WorkspaceUpdate) ClearSettings() *WorkspaceUpdate {
	wu.mutation.ClearSettings()
	return wu
}

// SetDefaultPlatforms sets the "default_platforms" field.
func (wu *WorkspaceUpdate) SetDefaultPlatforms(s []string) *WorkspaceUpdate {
	wu.mutation.SetDefaultPlatforms(s)
	return wu
}

// AppendDefaultPlatforms appends s to the "default_platforms" field.
func (wu *WorkspaceUpdate) AppendDefaultPlatforms(s []string) *WorkspaceUpdate {
	wu.mutation.AppendDefaultPlatforms(s)
	return wu
}

// ClearDefaultPlatforms clears the value of the "default_platforms" field.
func (wu *WorkspaceUpdate) ClearDefaultPlatforms() *WorkspaceUpdate {
	wu.mutation.ClearDefaultPlatforms()
	return wu
}

// SetBrandGuidelines sets the "brand_guidelines" field.
func (wu *WorkspaceUpdate) SetBrandGuidelines(m map[string]interface{}) *WorkspaceUpdate {
	wu.mutation.SetBrandGuidelines(m)
	return wu
}

// ClearBrandGuidelines clears the value of the "brand_guidelines" field.
func (wu *WorkspaceUpdate) ClearBrandGuidelines() *WorkspaceUpdate {
	wu.mutation.ClearBrandGuidelines()
	return wu
}

// SetIsPersonal sets the "is_personal" field.
func (wu *WorkspaceUpdate) SetIsPersonal(b bool) *WorkspaceUpdate {
	wu.mutation.SetIsPersonal(b)
	return wu
}

// SetNillableIsPersonal sets the "is_personal" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableIsPersonal(b *bool) *WorkspaceUpdate {
	if b != nil {
		wu.SetIsPersonal(*b)
	}
	return wu
}

// SetIsActive sets the "is_active" field.
func (wu *WorkspaceUpdate) SetIsActive(b bool) *WorkspaceUpdate {
	wu.mutation.SetIsActive(b)
	return wu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableIsActive(b *bool) *WorkspaceUpdate {
	if b != nil {
		wu.SetIsActive(*b)
	}
	return wu
}

// SetPostCount sets the "post_count" field.
func (wu *WorkspaceUpdate) SetPostCount(i int) *WorkspaceUpdate {
	wu.mutation.ResetPostCount()
	wu.mutation.SetPostCount(i)
	return wu
}

// SetNillablePostCount sets the "post_count" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillablePostCount(i *int) *WorkspaceUpdate {
	if i != nil {
		wu.SetPostCount(*i)
	}
	return wu
}

// AddPostCount adds i to the "post_count" field.
func (wu *WorkspaceUpdate) AddPostCount(i int) *WorkspaceUpdate {
	wu.mutation.AddPostCount(i)
	return wu
}

// SetTemplateCount sets the "template_count" field.
func (wu *WorkspaceUpdate) SetTemplateCount(i int) *WorkspaceUpdate {
	wu.mutation.ResetTemplateCount()
	wu.mutation.SetTemplateCount(i)
	return wu
}

// SetNillableTemplateCount sets the "template_count" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableTemplateCount(i *int) *WorkspaceUpdate {
	if i != nil {
		wu.SetTemplateCount(*i)
	}
	return wu
}

// AddTemplateCount adds i to the "template_count" field.
func (wu *WorkspaceUpdate) AddTemplateCount(i int) *WorkspaceUpdate {
	wu.mutation.AddTemplateCount(i)
	return wu
}

// SetLastActivityAt sets the "last_activity_at" field.
func (wu *WorkspaceUpdate) SetLastActivityAt(t time.Time) *WorkspaceUpdate {
	wu.mutation.SetLastActivityAt(t)
	return wu
}

// SetNillableLastActivityAt sets the "last_activity_at" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableLastActivityAt(t *time.Time) *WorkspaceUpdate {
	if t != nil {
		wu.SetLastActivityAt(*t)
	}
	return wu
}

// ClearLastActivityAt clears the value of the "last_activity_at" field.
func (wu *WorkspaceUpdate) ClearLastActivityAt() *WorkspaceUpdate {
	wu.mutation.ClearLastActivityAt()
	return wu
}

// SetMetadata sets the "metadata" field.
func (wu *WorkspaceUpdate) SetMetadata(m map[string]interface{}) *WorkspaceUpdate {
	wu.mutation.SetMetadata(m)
	return wu
}

// ClearMetadata clears the value of the "metadata" field.
func (wu *WorkspaceUpdate) ClearMetadata() *WorkspaceUpdate {
	wu.mutation.ClearMetadata()
	return wu
}

// SetUpdatedAt sets the "updated_at" field.
func (wu *WorkspaceUpdate) SetUpdatedAt(t time.Time) *WorkspaceUpdate {
	wu.mutation.SetUpdatedAt(t)
	return wu
}

// SetDeletedAt sets the "deleted_at" field.
func (wu *WorkspaceUpdate) SetDeletedAt(t time.Time) *WorkspaceUpdate {
	wu.mutation.SetDeletedAt(t)
	return wu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (wu *WorkspaceUpdate) SetNillableDeletedAt(t *time.Time) *WorkspaceUpdate {
	if t != nil {
		wu.SetDeletedAt(*t)
	}
	return wu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (wu *WorkspaceUpdate) ClearDeletedAt() *WorkspaceUpdate {
	wu.mutation.ClearDeletedAt()
	return wu
}

// Mutation returns the WorkspaceMutation object of the builder.
func (wu *WorkspaceUpdate) Mutation() *WorkspaceMutation {
	return wu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wu *WorkspaceUpdate) Save(ctx context.Context) (int, error) {
	wu.defaults()
	return withHooks(ctx, wu.sqlSave, wu.mutation, wu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wu *WorkspaceUpdate) SaveX(ctx context.Context) int {
	affected, err := wu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wu *WorkspaceUpdate) Exec(ctx context.Context) error {
	_, err := wu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wu *WorkspaceUpdate) ExecX(ctx context.Context) {
	if err := wu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wu *WorkspaceUpdate) defaults() {
	if _, ok := wu.mutation.UpdatedAt(); !ok {
		v := workspace.UpdateDefaultUpdatedAt()
		wu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wu *WorkspaceUpdate) check() error {
	if v, ok := wu.mutation.Name(); ok {
		if err := workspace.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Workspace.name": %w`, err)}
		}
	}
	if v, ok := wu.mutation.Color(); ok {
		if err := workspace.ColorValidator(v); err != nil {
			return &ValidationError{Name: "color", err: fmt.Errorf(`ent: validator failed for field "Workspace.color": %w`, err)}
		}
	}
	if v, ok := wu.mutation.Icon(); ok {
		if err := workspace.IconValidator(v); err != nil {
			return &ValidationError{Name: "icon", err: fmt.Errorf(`ent: validator failed for field "Workspace.icon": %w`, err)}
		}
	}
	return nil
}

func (wu *WorkspaceUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(workspace.Table, workspace.Columns, sqlgraph.NewFieldSpec(workspace.FieldID, field.TypeUUID))
	if ps := wu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wu.mutation.OwnerID(); ok {
		_spec.SetField(workspace.FieldOwnerID, field.TypeUUID, value)
	}
	if value, ok := wu.mutation.Name(); ok {
		_spec.SetField(workspace.FieldName, field.TypeString, value)
	}
	if value, ok := wu.mutation.Description(); ok {
		_spec.SetField(workspace.FieldDescription, field.TypeString, value)
	}
	if wu.mutation.DescriptionCleared() {
		_spec.ClearField(workspace.FieldDescription, field.TypeString)
	}
	if value, ok := wu.mutation.Color(); ok {
		_spec.SetField(workspace.FieldColor, field.TypeString, value)
	}
	if value, ok := wu.mutation.Icon(); ok {
		_spec.SetField(workspace.FieldIcon, field.TypeString, value)
	}
	if wu.mutation.IconCleared() {
		_spec.ClearField(workspace.FieldIcon, field.TypeString)
	}
	if value, ok := wu.mutation.Settings(); ok {
		_spec.SetField(workspace.FieldSettings, field.TypeJSON, value)
	}
	if wu.mutation.SettingsCleared() {
		_spec.ClearField(workspace.FieldSettings, field.TypeJSON)
	}
	if value, ok := wu.mutation.DefaultPlatforms(); ok {
		_spec.SetField(workspace.FieldDefaultPlatforms, field.TypeJSON, value)
	}
	if value, ok := wu.mutation.AppendedDefaultPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workspace.FieldDefaultPlatforms, value)
		})
	}
	if wu.mutation.DefaultPlatformsCleared() {
		_spec.ClearField(workspace.FieldDefaultPlatforms, field.TypeJSON)
	}
	if value, ok := wu.mutation.BrandGuidelines(); ok {
		_spec.SetField(workspace.FieldBrandGuidelines, field.TypeJSON, value)
	}
	if wu.mutation.BrandGuidelinesCleared() {
		_spec.ClearField(workspace.FieldBrandGuidelines, field.TypeJSON)
	}
	if value, ok := wu.mutation.IsPersonal(); ok {
		_spec.SetField(workspace.FieldIsPersonal, field.TypeBool, value)
	}
	if value, ok := wu.mutation.IsActive(); ok {
		_spec.SetField(workspace.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := wu.mutation.PostCount(); ok {
		_spec.SetField(workspace.FieldPostCount, field.TypeInt, value)
	}
	if value, ok := wu.mutation.AddedPostCount(); ok {
		_spec.AddField(workspace.FieldPostCount, field.TypeInt, value)
	}
	if value, ok := wu.mutation.TemplateCount(); ok {
		_spec.SetField(workspace.FieldTemplateCount, field.TypeInt, value)
	}
	if value, ok := wu.mutation.AddedTemplateCount(); ok {
		_spec.AddField(workspace.FieldTemplateCount, field.TypeInt, value)
	}
	if value, ok := wu.mutation.LastActivityAt(); ok {
		_spec.SetField(workspace.FieldLastActivityAt, field.TypeTime, value)
	}
	if wu.mutation.LastActivityAtCleared() {
		_spec.ClearField(workspace.FieldLastActivityAt, field.TypeTime)
	}
	if value, ok := wu.mutation.Metadata(); ok {
		_spec.SetField(workspace.FieldMetadata, field.TypeJSON, value)
	}
	if wu.mutation.MetadataCleared() {
		_spec.ClearField(workspace.FieldMetadata, field.TypeJSON)
	}
	if value, ok := wu.mutation.UpdatedAt(); ok {
		_spec.SetField(workspace.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wu.mutation.DeletedAt(); ok {
		_spec.SetField(workspace.FieldDeletedAt, field.TypeTime, value)
	}
	if wu.mutation.DeletedAtCleared() {
		_spec.ClearField(workspace.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workspace.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wu.mutation.done = true
	return n, nil
}

// WorkspaceUpdateOne is the builder for updating a single Workspace entity.
type WorkspaceUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkspaceMutation
}

// SetOwnerID sets the "owner_id" field.
func (wuo *WorkspaceUpdateOne) SetOwnerID(u uuid.UUID) *WorkspaceUpdateOne {
	wuo.mutation.SetOwnerID(u)
	return wuo
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableOwnerID(u *uuid.UUID) *WorkspaceUpdateOne {
	if u != nil {
		wuo.SetOwnerID(*u)
	}
	return wuo
}

// SetName sets the "name" field.
func (wuo *WorkspaceUpdateOne) SetName(s string) *WorkspaceUpdateOne {
	wuo.mutation.SetName(s)
	return wuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableName(s *string) *WorkspaceUpdateOne {
	if s != nil {
		wuo.SetName(*s)
	}
	return wuo
}

// SetDescription sets the "description" field.
func (wuo *WorkspaceUpdateOne) SetDescription(s string) *WorkspaceUpdateOne {
	wuo.mutation.SetDescription(s)
	return wuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableDescription(s *string) *WorkspaceUpdateOne {
	if s != nil {
		wuo.SetDescription(*s)
	}
	return wuo
}

// ClearDescription clears the value of the "description" field.
func (wuo *WorkspaceUpdateOne) ClearDescription() *WorkspaceUpdateOne {
	wuo.mutation.ClearDescription()
	return wuo
}

// SetColor sets the "color" field.
func (wuo *WorkspaceUpdateOne) SetColor(s string) *WorkspaceUpdateOne {
	wuo.mutation.SetColor(s)
	return wuo
}

// SetNillableColor sets the "color" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableColor(s *string) *WorkspaceUpdateOne {
	if s != nil {
		wuo.SetColor(*s)
	}
	return wuo
}

// SetIcon sets the "icon" field.
func (wuo *WorkspaceUpdateOne) SetIcon(s string) *WorkspaceUpdateOne {
	wuo.mutation.SetIcon(s)
	return wuo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableIcon(s *string) *WorkspaceUpdateOne {
	if s != nil {
		wuo.SetIcon(*s)
	}
	return wuo
}

// ClearIcon clears the value of the "icon" field.
func (wuo *WorkspaceUpdateOne) ClearIcon() *WorkspaceUpdateOne {
	wuo.mutation.ClearIcon()
	return wuo
}

// SetSettings sets the "settings" field.
func (wuo *WorkspaceUpdateOne) SetSettings(m map[string]interface{}) *WorkspaceUpdateOne {
	wuo.mutation.SetSettings(m)
	return wuo
}

// ClearSettings clears the value of the "settings" field.
func (wuo *WorkspaceUpdateOne) ClearSettings() *WorkspaceUpdateOne {
	wuo.mutation.ClearSettings()
	return wuo
}

// SetDefaultPlatforms sets the "default_platforms" field.
func (wuo *WorkspaceUpdateOne) SetDefaultPlatforms(s []string) *WorkspaceUpdateOne {
	wuo.mutation.SetDefaultPlatforms(s)
	return wuo
}

// AppendDefaultPlatforms appends s to the "default_platforms" field.
func (wuo *WorkspaceUpdateOne) AppendDefaultPlatforms(s []string) *WorkspaceUpdateOne {
	wuo.mutation.AppendDefaultPlatforms(s)
	return wuo
}

// ClearDefaultPlatforms clears the value of the "default_platforms" field.
func (wuo *WorkspaceUpdateOne) ClearDefaultPlatforms() *WorkspaceUpdateOne {
	wuo.mutation.ClearDefaultPlatforms()
	return wuo
}

// SetBrandGuidelines sets the "brand_guidelines" field.
func (wuo *WorkspaceUpdateOne) SetBrandGuidelines(m map[string]interface{}) *WorkspaceUpdateOne {
	wuo.mutation.SetBrandGuidelines(m)
	return wuo
}

// ClearBrandGuidelines clears the value of the "brand_guidelines" field.
func (wuo *WorkspaceUpdateOne) ClearBrandGuidelines() *WorkspaceUpdateOne {
	wuo.mutation.ClearBrandGuidelines()
	return wuo
}

// SetIsPersonal sets the "is_personal" field.
func (wuo *WorkspaceUpdateOne) SetIsPersonal(b bool) *WorkspaceUpdateOne {
	wuo.mutation.SetIsPersonal(b)
	return wuo
}

// SetNillableIsPersonal sets the "is_personal" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableIsPersonal(b *bool) *WorkspaceUpdateOne {
	if b != nil {
		wuo.SetIsPersonal(*b)
	}
	return wuo
}

// SetIsActive sets the "is_active" field.
func (wuo *WorkspaceUpdateOne) SetIsActive(b bool) *WorkspaceUpdateOne {
	wuo.mutation.SetIsActive(b)
	return wuo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableIsActive(b *bool) *WorkspaceUpdateOne {
	if b != nil {
		wuo.SetIsActive(*b)
	}
	return wuo
}

// SetPostCount sets the "post_count" field.
func (wuo *WorkspaceUpdateOne) SetPostCount(i int) *WorkspaceUpdateOne {
	wuo.mutation.ResetPostCount()
	wuo.mutation.SetPostCount(i)
	return wuo
}

// SetNillablePostCount sets the "post_count" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillablePostCount(i *int) *WorkspaceUpdateOne {
	if i != nil {
		wuo.SetPostCount(*i)
	}
	return wuo
}

// AddPostCount adds i to the "post_count" field.
func (wuo *WorkspaceUpdateOne) AddPostCount(i int) *WorkspaceUpdateOne {
	wuo.mutation.AddPostCount(i)
	return wuo
}

// SetTemplateCount sets the "template_count" field.
func (wuo *WorkspaceUpdateOne) SetTemplateCount(i int) *WorkspaceUpdateOne {
	wuo.mutation.ResetTemplateCount()
	wuo.mutation.SetTemplateCount(i)
	return wuo
}

// SetNillableTemplateCount sets the "template_count" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableTemplateCount(i *int) *WorkspaceUpdateOne {
	if i != nil {
		wuo.SetTemplateCount(*i)
	}
	return wuo
}

// AddTemplateCount adds i to the "template_count" field.
func (wuo *WorkspaceUpdateOne) AddTemplateCount(i int) *WorkspaceUpdateOne {
	wuo.mutation.AddTemplateCount(i)
	return wuo
}

// SetLastActivityAt sets the "last_activity_at" field.
func (wuo *WorkspaceUpdateOne) SetLastActivityAt(t time.Time) *WorkspaceUpdateOne {
	wuo.mutation.SetLastActivityAt(t)
	return wuo
}

// SetNillableLastActivityAt sets the "last_activity_at" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableLastActivityAt(t *time.Time) *WorkspaceUpdateOne {
	if t != nil {
		wuo.SetLastActivityAt(*t)
	}
	return wuo
}

// ClearLastActivityAt clears the value of the "last_activity_at" field.
func (wuo *WorkspaceUpdateOne) ClearLastActivityAt() *WorkspaceUpdateOne {
	wuo.mutation.ClearLastActivityAt()
	return wuo
}

// SetMetadata sets the "metadata" field.
func (wuo *WorkspaceUpdateOne) SetMetadata(m map[string]interface{}) *WorkspaceUpdateOne {
	wuo.mutation.SetMetadata(m)
	return wuo
}

// ClearMetadata clears the value of the "metadata" field.
func (wuo *WorkspaceUpdateOne) ClearMetadata() *WorkspaceUpdateOne {
	wuo.mutation.ClearMetadata()
	return wuo
}

// SetUpdatedAt sets the "updated_at" field.
func (wuo *WorkspaceUpdateOne) SetUpdatedAt(t time.Time) *WorkspaceUpdateOne {
	wuo.mutation.SetUpdatedAt(t)
	return wuo
}

// SetDeletedAt sets the "deleted_at" field.
func (wuo *WorkspaceUpdateOne) SetDeletedAt(t time.Time) *WorkspaceUpdateOne {
	wuo.mutation.SetDeletedAt(t)
	return wuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (wuo *WorkspaceUpdateOne) SetNillableDeletedAt(t *time.Time) *WorkspaceUpdateOne {
	if t != nil {
		wuo.SetDeletedAt(*t)
	}
	return wuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (wuo *WorkspaceUpdateOne) ClearDeletedAt() *WorkspaceUpdateOne {
	wuo.mutation.ClearDeletedAt()
	return wuo
}

// Mutation returns the WorkspaceMutation object of the builder.
func (wuo *WorkspaceUpdateOne) Mutation() *WorkspaceMutation {
	return wuo.mutation
}

// Where appends a list predicates to the WorkspaceUpdate builder.
func (wuo *WorkspaceUpdateOne) Where(ps ...predicate.Workspace) *WorkspaceUpdateOne {
	wuo.mutation.Where(ps...)
	return wuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wuo *WorkspaceUpdateOne) Select(field string, fields ...string) *WorkspaceUpdateOne {
	wuo.fields = append([]string{field}, fields...)
	return wuo
}

// Save executes the query and returns the updated Workspace entity.
func (wuo *WorkspaceUpdateOne) Save(ctx context.Context) (*Workspace, error) {
	wuo.defaults()
	return withHooks(ctx, wuo.sqlSave, wuo.mutation, wuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wuo *WorkspaceUpdateOne) SaveX(ctx context.Context) *Workspace {
	node, err := wuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wuo *WorkspaceUpdateOne) Exec(ctx context.Context) error {
	_, err := wuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wuo *WorkspaceUpdateOne) ExecX(ctx context.Context) {
	if err := wuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wuo *WorkspaceUpdateOne) defaults() {
	if _, ok := wuo.mutation.UpdatedAt(); !ok {
		v := workspace.UpdateDefaultUpdatedAt()
		wuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wuo *WorkspaceUpdateOne) check() error {
	if v, ok := wuo.mutation.Name(); ok {
		if err := workspace.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Workspace.name": %w`, err)}
		}
	}
	if v, ok := wuo.mutation.Color(); ok {
		if err := workspace.ColorValidator(v); err != nil {
			return &ValidationError{Name: "color", err: fmt.Errorf(`ent: validator failed for field "Workspace.color": %w`, err)}
		}
	}
	if v, ok := wuo.mutation.Icon(); ok {
		if err := workspace.IconValidator(v); err != nil {
			return &ValidationError{Name: "icon", err: fmt.Errorf(`ent: validator failed for field "Workspace.icon": %w`, err)}
		}
	}
	return nil
}

func (wuo *WorkspaceUpdateOne) sqlSave(ctx context.Context) (_node *Workspace, err error) {
	if err := wuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(workspace.Table, workspace.Columns, sqlgraph.NewFieldSpec(workspace.FieldID, field.TypeUUID))
	id, ok := wuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Workspace.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workspace.FieldID)
		for _, f := range fields {
			if !workspace.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workspace.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wuo.mutation.OwnerID(); ok {
		_spec.SetField(workspace.FieldOwnerID, field.TypeUUID, value)
	}
	if value, ok := wuo.mutation.Name(); ok {
		_spec.SetField(workspace.FieldName, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Description(); ok {
		_spec.SetField(workspace.FieldDescription, field.TypeString, value)
	}
	if wuo.mutation.DescriptionCleared() {
		_spec.ClearField(workspace.FieldDescription, field.TypeString)
	}
	if value, ok := wuo.mutation.Color(); ok {
		_spec.SetField(workspace.FieldColor, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Icon(); ok {
		_spec.SetField(workspace.FieldIcon, field.TypeString, value)
	}
	if wuo.mutation.IconCleared() {
		_spec.ClearField(workspace.FieldIcon, field.TypeString)
	}
	if value, ok := wuo.mutation.Settings(); ok {
		_spec.SetField(workspace.FieldSettings, field.TypeJSON, value)
	}
	if wuo.mutation.SettingsCleared() {
		_spec.ClearField(workspace.FieldSettings, field.TypeJSON)
	}
	if value, ok := wuo.mutation.DefaultPlatforms(); ok {
		_spec.SetField(workspace.FieldDefaultPlatforms, field.TypeJSON, value)
	}
	if value, ok := wuo.mutation.AppendedDefaultPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workspace.FieldDefaultPlatforms, value)
		})
	}
	if wuo.mutation.DefaultPlatformsCleared() {
		_spec.ClearField(workspace.FieldDefaultPlatforms, field.TypeJSON)
	}
	if value, ok := wuo.mutation.BrandGuidelines(); ok {
		_spec.SetField(workspace.FieldBrandGuidelines, field.TypeJSON, value)
	}
	if wuo.mutation.BrandGuidelinesCleared() {
		_spec.ClearField(workspace.FieldBrandGuidelines, field.TypeJSON)
	}
	if value, ok := wuo.mutation.IsPersonal(); ok {
		_spec.SetField(workspace.FieldIsPersonal, field.TypeBool, value)
	}
	if value, ok := wuo.mutation.IsActive(); ok {
		_spec.SetField(workspace.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := wuo.mutation.PostCount(); ok {
		_spec.SetField(workspace.FieldPostCount, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.AddedPostCount(); ok {
		_spec.AddField(workspace.FieldPostCount, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.TemplateCount(); ok {
		_spec.SetField(workspace.FieldTemplateCount, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.AddedTemplateCount(); ok {
		_spec.AddField(workspace.FieldTemplateCount, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.LastActivityAt(); ok {
		_spec.SetField(workspace.FieldLastActivityAt, field.TypeTime, value)
	}
	if wuo.mutation.LastActivityAtCleared() {
		_spec.ClearField(workspace.FieldLastActivityAt, field.TypeTime)
	}
	if value, ok := wuo.mutation.Metadata(); ok {
		_spec.SetField(workspace.FieldMetadata, field.TypeJSON, value)
	}
	if wuo.mutation.MetadataCleared() {
		_spec.ClearField(workspace.FieldMetadata, field.TypeJSON)
	}
	if value, ok := wuo.mutation.UpdatedAt(); ok {
		_spec.SetField(workspace.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wuo.mutation.DeletedAt(); ok {
		_spec.SetField(workspace.FieldDeletedAt, field.TypeTime, value)
	}
	if wuo.mutation.DeletedAtCleared() {
		_spec.ClearField(workspace.FieldDeletedAt, field.TypeTime)
	}
	_node = &Workspace{config: wuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workspace.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wuo.mutation.done = true
	return _node, nil
}

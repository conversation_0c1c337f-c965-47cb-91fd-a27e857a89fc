#!/bin/bash

# Script to switch between PostgreSQL and SQLite for User Service
# Usage: ./scripts/switch-database.sh [postgres|sqlite] [environment]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DB_TYPE=""
ENVIRONMENT="development"
CONFIG_DIR="./config"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [postgres|sqlite] [environment]"
    echo ""
    echo "Arguments:"
    echo "  postgres|sqlite    Database type to switch to"
    echo "  environment        Environment config to update (default: development)"
    echo ""
    echo "Examples:"
    echo "  $0 sqlite                    # Switch development to SQLite"
    echo "  $0 postgres development      # Switch development to PostgreSQL"
    echo "  $0 postgres production       # Switch production to PostgreSQL"
    echo ""
    echo "Available environments:"
    echo "  - development"
    echo "  - production"
    echo "  - config (main config.yaml)"
}

# Function to backup config file
backup_config() {
    local config_file="$1"
    local backup_file="${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$config_file" ]; then
        cp "$config_file" "$backup_file"
        print_info "Backed up $config_file to $backup_file"
    fi
}

# Function to update config file
update_config() {
    local config_file="$1"
    local db_type="$2"
    
    if [ ! -f "$config_file" ]; then
        print_error "Config file $config_file not found!"
        exit 1
    fi
    
    print_info "Updating $config_file for $db_type..."
    
    # Backup original file
    backup_config "$config_file"
    
    if [ "$db_type" = "sqlite" ]; then
        # Update to SQLite
        sed -i.tmp 's/type: "postgres"/type: "sqlite"/' "$config_file"
        sed -i.tmp 's/type: postgres/type: "sqlite"/' "$config_file"
        
        # Ensure SQLite path is set
        if ! grep -q "sqlite_path:" "$config_file"; then
            # Add sqlite_path after type line
            sed -i.tmp '/type: "sqlite"/a\
  sqlite_path: "./data/user_service_dev.db"' "$config_file"
        fi
        
        print_success "Updated $config_file to use SQLite"
        
    elif [ "$db_type" = "postgres" ]; then
        # Update to PostgreSQL
        sed -i.tmp 's/type: "sqlite"/type: "postgres"/' "$config_file"
        sed -i.tmp 's/type: sqlite/type: "postgres"/' "$config_file"
        
        print_success "Updated $config_file to use PostgreSQL"
    fi
    
    # Remove temporary file
    rm -f "${config_file}.tmp"
}

# Function to setup SQLite
setup_sqlite() {
    print_info "Setting up SQLite..."
    
    # Create data directory
    mkdir -p ./data
    print_success "Created data directory"
    
    # Set appropriate permissions
    chmod 755 ./data
    
    print_success "SQLite setup completed"
    print_info "Database file will be created at: ./data/user_service_dev.db"
}

# Function to setup PostgreSQL
setup_postgres() {
    print_info "Setting up PostgreSQL..."
    
    # Check if PostgreSQL is installed
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL client (psql) not found!"
        print_info "Please install PostgreSQL:"
        print_info "  macOS: brew install postgresql"
        print_info "  Ubuntu: sudo apt install postgresql-client"
        return 1
    fi
    
    # Check if PostgreSQL server is running
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        print_warning "PostgreSQL server is not running on localhost:5432"
        print_info "Please start PostgreSQL server:"
        print_info "  macOS: brew services start postgresql"
        print_info "  Ubuntu: sudo systemctl start postgresql"
        print_info "  Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres"
        return 1
    fi
    
    print_success "PostgreSQL setup verified"
}

# Function to show current configuration
show_current_config() {
    local config_file="$1"
    
    if [ -f "$config_file" ]; then
        print_info "Current database configuration in $config_file:"
        echo ""
        grep -A 10 "^database:" "$config_file" | head -15
        echo ""
    else
        print_warning "Config file $config_file not found"
    fi
}

# Parse arguments
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

DB_TYPE="$1"

if [ $# -ge 2 ]; then
    ENVIRONMENT="$2"
fi

# Validate database type
if [ "$DB_TYPE" != "postgres" ] && [ "$DB_TYPE" != "sqlite" ]; then
    print_error "Invalid database type: $DB_TYPE"
    print_error "Must be either 'postgres' or 'sqlite'"
    exit 1
fi

# Determine config file
if [ "$ENVIRONMENT" = "config" ]; then
    CONFIG_FILE="$CONFIG_DIR/config.yaml"
else
    CONFIG_FILE="$CONFIG_DIR/${ENVIRONMENT}.yaml"
fi

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    print_error "Config file $CONFIG_FILE not found!"
    print_info "Available config files:"
    ls -la "$CONFIG_DIR"/*.yaml 2>/dev/null || print_warning "No YAML config files found in $CONFIG_DIR"
    exit 1
fi

print_info "Switching to $DB_TYPE for environment: $ENVIRONMENT"
print_info "Config file: $CONFIG_FILE"
echo ""

# Show current configuration
show_current_config "$CONFIG_FILE"

# Setup database
if [ "$DB_TYPE" = "sqlite" ]; then
    setup_sqlite
elif [ "$DB_TYPE" = "postgres" ]; then
    if ! setup_postgres; then
        print_error "PostgreSQL setup failed. Please fix the issues above."
        exit 1
    fi
fi

# Update configuration
update_config "$CONFIG_FILE" "$DB_TYPE"

echo ""
print_success "Database switch completed!"
print_info "Updated configuration:"
echo ""
show_current_config "$CONFIG_FILE"

# Show next steps
echo ""
print_info "Next steps:"
if [ "$DB_TYPE" = "sqlite" ]; then
    echo "  1. Start the service: ./bin/user-service"
    echo "  2. The SQLite database will be created automatically"
    echo "  3. Check database file: ls -la ./data/"
elif [ "$DB_TYPE" = "postgres" ]; then
    echo "  1. Ensure PostgreSQL is running"
    echo "  2. Create database if needed: createdb user_service_dev"
    echo "  3. Start the service: ./bin/user-service"
    echo "  4. Check connection: psql -h localhost -U postgres -d user_service_dev"
fi

echo ""
print_info "To revert changes, restore from backup:"
echo "  ls -la $CONFIG_DIR/*.backup.*"

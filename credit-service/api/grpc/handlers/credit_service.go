package handlers

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
)

// CreditServiceHandler handles credit-related gRPC operations
type CreditServiceHandler struct {
	creditUseCase       credit.UseCase
	subscriptionUseCase subscription.UseCase
	transactionUseCase  transaction.UseCase
	logger              logging.Logger
}

// NewCreditServiceHandler creates a new credit service handler
func NewCreditServiceHandler(
	creditUseCase credit.UseCase,
	subscriptionUseCase subscription.UseCase,
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *CreditServiceHandler {
	return &CreditServiceHandler{
		creditUseCase:       creditUseCase,
		subscriptionUseCase: subscriptionUseCase,
		transactionUseCase:  transactionUseCase,
		logger:              logger,
	}
}

// GetUserCredit retrieves user credit information
func (h *CreditServiceHandler) GetUserCredit(ctx context.Context, req *creditv1.GetUserCreditRequest) (*creditv1.UserCredit, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting user credit")

	// Get credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user credit")
		return nil, status.Errorf(codes.Internal, "failed to get user credit: %v", err)
	}

	// Get user subscription for plan info
	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Warn("Failed to get user subscription")
		// Continue without subscription info
	}

	userCredit := &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}

	if subscription != nil {
		userCredit.PlanId = subscription.PlanID
		if !subscription.CurrentPeriodEnd.IsZero() {
			userCredit.RenewalDate = subscription.CurrentPeriodEnd.Format("2006-01-02")
		}
	}

	return userCredit, nil
}

// ValidateCredit checks if user has sufficient credits
func (h *CreditServiceHandler) ValidateCredit(ctx context.Context, req *creditv1.ValidateCreditRequest) (*creditv1.ValidateCreditResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":          req.UserId,
		"required_credits": req.RequiredCredits,
	}).Info("Validating credit")

	// Get current balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit balance for validation")
		return nil, status.Errorf(codes.Internal, "failed to get credit balance: %v", err)
	}

	hasSufficient := balance.CurrentCredits >= int(req.RequiredCredits)
	shortage := int32(0)
	if !hasSufficient {
		shortage = req.RequiredCredits - int32(balance.CurrentCredits)
	}

	return &creditv1.ValidateCreditResponse{
		HasSufficientCredits: hasSufficient,
		CurrentCredits:       int32(balance.CurrentCredits),
		RequiredCredits:      req.RequiredCredits,
		Shortage:             shortage,
	}, nil
}

// AddCredits adds credits to user account
func (h *CreditServiceHandler) AddCredits(ctx context.Context, req *creditv1.AddCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"amount":  req.Amount,
		"source":  req.Source,
	}).Info("Adding credits")

	// Convert proto request to internal model
	addReq := &models.AddCreditsRequest{
		UserID:      req.UserId,
		Amount:      int(req.Amount),
		Source:      req.Source,
		Description: req.Description,
		PaymentID:   req.PaymentId,
	}

	// Use transaction usecase for credit addition
	_, err := h.transactionUseCase.AddCreditsWithTransaction(ctx, addReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add credits")
		return nil, status.Errorf(codes.Internal, "failed to add credits: %v", err)
	}

	// Get updated credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get updated credit balance")
		return nil, status.Errorf(codes.Internal, "failed to get updated balance: %v", err)
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}, nil
}

// DeductCredits deducts credits from user account
func (h *CreditServiceHandler) DeductCredits(ctx context.Context, req *creditv1.DeductCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserId,
		"amount":       req.Amount,
		"purpose":      req.Purpose,
		"reference_id": req.ReferenceId,
	}).Info("Deducting credits")

	// Convert proto request to internal model
	deductReq := &models.DeductCreditsRequest{
		UserID:        req.UserId,
		Amount:        int(req.Amount),
		Purpose:       req.Purpose,
		Description:   req.Description,
		ReferenceID:   req.ReferenceId,
		ReferenceType: "grpc_request",
	}

	// Use transaction usecase for credit deduction
	_, err := h.transactionUseCase.DeductCreditsWithTransaction(ctx, deductReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to deduct credits")
		return nil, status.Errorf(codes.Internal, "failed to deduct credits: %v", err)
	}

	// Get updated credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get updated credit balance")
		return nil, status.Errorf(codes.Internal, "failed to get updated balance: %v", err)
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}, nil
}

# Credit Service Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50053
  http_port: 8083
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"

  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "credit_service_db"
  ssl_mode: "disable"

  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/credit_service.db"

  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Payment configuration
payment:
  stripe:
    secret_key: "${STRIPE_SECRET_KEY}"
    publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
    webhook_secret: "${STRIPE_WEBHOOK_SECRET}"

# External services configuration
services:
  user_service: "localhost:50051"

# Metrics configuration
metrics:
  port: 9093

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# JWT configuration
jwt:
  secret_key: "${JWT_SECRET_KEY:your-secret-key}"
  token_duration: "24h"
  issuer: "credit-service"

# Kafka configuration
kafka:
  brokers:
    - "localhost:9092"
  topics:
    credit_events: "credit.events"
    billing_events: "billing.events"

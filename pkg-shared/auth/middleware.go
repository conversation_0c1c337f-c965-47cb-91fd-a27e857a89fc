package auth

import (
	"context"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

const (
	AuthorizationHeader = "authorization"
	BearerPrefix        = "Bearer "
	UserIDKey           = "user_id"
	ClaimsKey           = "claims"
)

// AuthInterceptor provides gRPC authentication interceptor
type AuthInterceptor struct {
	jwtManager      *JWTManager
	publicMethods   map[string]bool
	requiredRoles   map[string][]string
	requiredPerms   map[string][]string
}

// NewAuthInterceptor creates a new auth interceptor
func NewAuthInterceptor(jwtManager *JWTManager) *AuthInterceptor {
	return &AuthInterceptor{
		jwtManager:    jwtManager,
		publicMethods: make(map[string]bool),
		requiredRoles: make(map[string][]string),
		requiredPerms: make(map[string][]string),
	}
}

// AddPublicMethod adds a method that doesn't require authentication
func (interceptor *AuthInterceptor) AddPublicMethod(method string) {
	interceptor.publicMethods[method] = true
}

// AddRoleRequirement adds role requirement for a method
func (interceptor *AuthInterceptor) AddRoleRequirement(method string, roles []string) {
	interceptor.requiredRoles[method] = roles
}

// AddPermissionRequirement adds permission requirement for a method
func (interceptor *AuthInterceptor) AddPermissionRequirement(method string, permissions []string) {
	interceptor.requiredPerms[method] = permissions
}

// UnaryInterceptor returns a gRPC unary server interceptor for authentication
func (interceptor *AuthInterceptor) UnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(
		ctx context.Context,
		req interface{},
		info *grpc.UnaryServerInfo,
		handler grpc.UnaryHandler,
	) (interface{}, error) {
		// Check if method is public
		if interceptor.publicMethods[info.FullMethod] {
			return handler(ctx, req)
		}

		// Extract and verify token
		claims, err := interceptor.authorize(ctx, info.FullMethod)
		if err != nil {
			return nil, err
		}

		// Add claims to context
		ctx = context.WithValue(ctx, ClaimsKey, claims)
		ctx = context.WithValue(ctx, UserIDKey, claims.UserID)

		return handler(ctx, req)
	}
}

// StreamInterceptor returns a gRPC stream server interceptor for authentication
func (interceptor *AuthInterceptor) StreamInterceptor() grpc.StreamServerInterceptor {
	return func(
		srv interface{},
		stream grpc.ServerStream,
		info *grpc.StreamServerInfo,
		handler grpc.StreamHandler,
	) error {
		// Check if method is public
		if interceptor.publicMethods[info.FullMethod] {
			return handler(srv, stream)
		}

		// Extract and verify token
		claims, err := interceptor.authorize(stream.Context(), info.FullMethod)
		if err != nil {
			return err
		}

		// Create new context with claims
		ctx := context.WithValue(stream.Context(), ClaimsKey, claims)
		ctx = context.WithValue(ctx, UserIDKey, claims.UserID)

		// Wrap stream with new context
		wrappedStream := &wrappedServerStream{
			ServerStream: stream,
			ctx:          ctx,
		}

		return handler(srv, wrappedStream)
	}
}

// authorize extracts token from metadata and verifies it
func (interceptor *AuthInterceptor) authorize(ctx context.Context, method string) (*Claims, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, status.Errorf(codes.Unauthenticated, "metadata not found")
	}

	values := md[AuthorizationHeader]
	if len(values) == 0 {
		return nil, status.Errorf(codes.Unauthenticated, "authorization header not found")
	}

	accessToken := values[0]
	if !strings.HasPrefix(accessToken, BearerPrefix) {
		return nil, status.Errorf(codes.Unauthenticated, "invalid authorization header format")
	}

	accessToken = strings.TrimPrefix(accessToken, BearerPrefix)
	claims, err := interceptor.jwtManager.VerifyToken(accessToken)
	if err != nil {
		return nil, status.Errorf(codes.Unauthenticated, "invalid access token: %v", err)
	}

	// Check role requirements
	if requiredRoles, exists := interceptor.requiredRoles[method]; exists {
		if !interceptor.hasRequiredRole(claims.Role, requiredRoles) {
			return nil, status.Errorf(codes.PermissionDenied, "insufficient role privileges")
		}
	}

	// Check permission requirements
	if requiredPerms, exists := interceptor.requiredPerms[method]; exists {
		if !interceptor.hasRequiredPermissions(claims.Permissions, requiredPerms) {
			return nil, status.Errorf(codes.PermissionDenied, "insufficient permissions")
		}
	}

	return claims, nil
}

// hasRequiredRole checks if user has one of the required roles
func (interceptor *AuthInterceptor) hasRequiredRole(userRole string, requiredRoles []string) bool {
	for _, role := range requiredRoles {
		if userRole == role {
			return true
		}
	}
	return false
}

// hasRequiredPermissions checks if user has all required permissions
func (interceptor *AuthInterceptor) hasRequiredPermissions(userPerms, requiredPerms []string) bool {
	permMap := make(map[string]bool)
	for _, perm := range userPerms {
		permMap[perm] = true
	}

	for _, required := range requiredPerms {
		if !permMap[required] {
			return false
		}
	}
	return true
}

// wrappedServerStream wraps grpc.ServerStream with custom context
type wrappedServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (w *wrappedServerStream) Context() context.Context {
	return w.ctx
}

// GetClaimsFromContext extracts claims from context
func GetClaimsFromContext(ctx context.Context) (*Claims, bool) {
	claims, ok := ctx.Value(ClaimsKey).(*Claims)
	return claims, ok
}

// GetUserIDFromContext extracts user ID from context
func GetUserIDFromContext(ctx context.Context) (string, bool) {
	userID, ok := ctx.Value(UserIDKey).(string)
	return userID, ok
}

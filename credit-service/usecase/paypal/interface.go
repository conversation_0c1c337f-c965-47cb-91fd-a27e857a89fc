package paypal

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the PayPal payment use case interface
type UseCase interface {
	// InitiatePayment initiates a PayPal payment
	InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error)

	// ExecutePayment executes a PayPal payment after user approval
	ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error)

	// CancelPayment cancels a PayPal payment
	CancelPayment(ctx context.Context, req *models.CancelPayPalPaymentRequest) error

	// GetPaymentStatus gets the status of a PayPal payment
	GetPaymentStatus(ctx context.Context, paymentID, userID string) (*models.PayPalPaymentInfo, error)

	// ListPayments lists PayPal payments for a user
	ListPayments(ctx context.Context, req *models.ListPayPalPaymentsRequest) (*models.ListPayPalPaymentsResponse, error)

	// RefundPayment refunds a PayPal payment
	RefundPayment(ctx context.Context, paymentID string, amount int64, reason string) error

	// GetPaymentDetails gets detailed PayPal payment information
	GetPaymentDetails(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error)

	// ValidateWebhookSignature validates PayPal webhook signature
	ValidateWebhookSignature(ctx context.Context, headers map[string]string, body []byte) (bool, error)

	// HandleWebhook handles PayPal webhook events
	HandleWebhook(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error)

	// ProcessWebhookEvent processes PayPal webhook events
	ProcessWebhookEvent(ctx context.Context, eventType string, resource map[string]interface{}) error

	// GetPaymentStatistics gets PayPal payment statistics
	GetPaymentStatistics(ctx context.Context, userID string, period string) (*models.PayPalPaymentSummary, error)

	// Admin methods
	AdminListPayments(ctx context.Context, req *models.ListPayPalPaymentsRequest) (*models.ListPayPalPaymentsResponse, error)
	AdminRefundPayment(ctx context.Context, paymentID string, amount int64, reason string, adminUserID string) error
	AdminGetPaymentDetails(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error)

	// Internal methods for PayPal API integration
	CreateOrder(ctx context.Context, amount int64, currency, description string, metadata map[string]interface{}) (*models.PayPalOrder, error)
	CaptureOrder(ctx context.Context, orderID string) (*models.PayPalOrder, error)
	GetOrder(ctx context.Context, orderID string) (*models.PayPalOrder, error)
	GetAccessToken(ctx context.Context) (string, error)

	// Subscription integration
	ProcessSubscriptionPayment(ctx context.Context, paymentID string) (*models.ExecutePayPalPaymentResponse, error)
	ProcessCreditTopup(ctx context.Context, paymentID string) (*models.ExecutePayPalPaymentResponse, error)

	// Internal utility methods
	InternalGetPayment(ctx context.Context, paymentID string) (*models.PayPalPaymentInfo, error)
	InternalUpdatePaymentStatus(ctx context.Context, paymentID string, status string, metadata map[string]interface{}) error
	InternalCreatePaymentRecord(ctx context.Context, order *models.PayPalOrder, userID, purpose string) (*models.PayPalPaymentInfo, error)
}

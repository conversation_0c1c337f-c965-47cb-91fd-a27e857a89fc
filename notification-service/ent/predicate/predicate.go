// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Notification is the predicate function for notification builders.
type Notification func(*sql.Selector)

// NotificationPreference is the predicate function for notificationpreference builders.
type NotificationPreference func(*sql.Selector)

// NotificationTemplate is the predicate function for notificationtemplate builders.
type NotificationTemplate func(*sql.Selector)

package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/reflection"

	grpcapi "github.com/social-content-ai/credit-service/api/grpc"
	"github.com/social-content-ai/credit-service/api/restful"
	"github.com/social-content-ai/credit-service/config"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/pkg/events"
	"github.com/social-content-ai/credit-service/pkg/scheduler"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/pkg-shared/metrics"
	"github.com/social-content-ai/pkg-shared/tracing"

	userv1 "github.com/social-content-ai/proto-shared/user/v1"

	_ "github.com/jackc/pgx/v5/stdlib"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

const (
	serviceName = "credit-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithField("service", serviceName).Info("Starting credit service")

	// Initialize tracing
	tracer, err := tracing.NewTracer(serviceName)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize tracer")
	}
	defer tracer.Close()

	// Initialize metrics
	metricsServer := metrics.NewServer(fmt.Sprintf(":%d", cfg.Metrics.Port))
	go func() {
		if err := metricsServer.Start(); err != nil {
			logger.WithError(err).Error("Failed to start metrics server")
		}
	}()

	// Initialize database connections
	readDB, writeDB, err := initDatabases(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize databases")
	}
	defer readDB.Close()
	defer writeDB.Close()

	// Run database migrations
	if err := runMigrations(writeDB, logger); err != nil {
		logger.WithError(err).Fatal("Failed to run migrations")
	}

	// TODO: Initialize payment processor when implemented
	// stripeConfig := &payment.StripeConfig{
	//	SecretKey:      cfg.Payment.Stripe.SecretKey,
	//	PublishableKey: cfg.Payment.Stripe.PublishableKey,
	//	WebhookSecret:  cfg.Payment.Stripe.WebhookSecret,
	// }
	// stripeProcessor := payment.NewStripeProcessor(stripeConfig, logger)

	// Initialize external service clients
	userConn, err := grpc.NewClient(cfg.Services.UserService, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to user service")
	}
	defer userConn.Close()
	userClient := userv1.NewAuthServiceClient(userConn)

	// Initialize Kafka configuration and producer
	kafkaConfig := &sharedKafka.Config{
		Brokers: cfg.Kafka.Brokers,
		Consumer: sharedKafka.ConsumerConfig{
			GroupID: cfg.Kafka.GroupID,
		},
		Producer: sharedKafka.ProducerConfig{
			BatchSize:    cfg.Kafka.Producer.BatchSize,
			BatchTimeout: cfg.Kafka.Producer.BatchTimeout,
			Compression:  cfg.Kafka.Producer.Compression,
		},
		Topics: sharedKafka.TopicConfig{
			BillingEvents:      "billing.events",      // Use standard billing events topic
			NotificationEvents: "notification.events", // Use standard notification events topic
		},
	}

	kafkaProducer := sharedKafka.NewProducer(kafkaConfig, logger)
	defer kafkaProducer.Close()

	// Initialize credit service event publisher (TODO: Pass to use cases)
	_ = events.NewPublisher(kafkaConfig, logger)

	// Initialize use cases
	creditUseCase := credit.NewService(writeDB, logger)
	// paypalUseCase := paypal.NewService(logger) // TODO: Add proper dependencies
	planUseCase := plan.NewService(readDB, writeDB, logger)
	subscriptionUseCase := subscription.NewService(readDB, writeDB, logger)
	transactionUseCase := transaction.NewService(readDB, writeDB, logger)

	// Initialize credit service event consumer
	eventConsumer := events.NewConsumer(kafkaConfig, creditUseCase, logger)

	// Initialize scheduler
	schedulerConfig, err := cfg.Scheduler.ToSchedulerConfig()
	if err != nil {
		logger.WithError(err).Fatal("Failed to convert scheduler config")
	}

	creditScheduler, err := scheduler.NewScheduler(
		schedulerConfig,
		readDB,
		writeDB,
		creditUseCase,
		logger.WithField("component", "scheduler"),
	)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize scheduler")
	}

	// Initialize gRPC server
	grpcServer := grpc.NewServer()

	// Initialize gRPC handlers
	grpcHandlers := grpcapi.NewHandlers(
		creditUseCase,
		planUseCase,
		subscriptionUseCase,
		transactionUseCase,
		logger,
	)

	// Register gRPC services
	grpcHandlers.RegisterServices(grpcServer)

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())

	// TODO: Update SetupRoutes when payment processor is implemented
	// paypalUseCase will be added when PayPal integration is complete
	restful.SetupRoutes(httpRouter, creditUseCase, planUseCase, transactionUseCase, nil, userClient, logger)

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start HTTP server
	httpServer := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler:      httpRouter,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Start Kafka consumer in background
	go func() {
		logger.Info("Starting Kafka event consumer")
		if err := eventConsumer.Start(context.Background()); err != nil {
			logger.WithError(err).Error("Kafka consumer failed")
		}
	}()

	// Start scheduler
	if err := creditScheduler.Start(); err != nil {
		logger.WithError(err).Fatal("Failed to start scheduler")
	}

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down servers...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	// Shutdown Kafka consumer
	if err := eventConsumer.Stop(); err != nil {
		logger.WithError(err).Error("Failed to stop Kafka consumer")
	}

	// Shutdown scheduler
	if err := creditScheduler.Stop(); err != nil {
		logger.WithError(err).Error("Failed to stop scheduler")
	}

	logger.Info("Servers stopped")
}

// initDatabases initializes read and write database connections
func initDatabases(cfg *config.Config, logger logging.Logger) (*ent.Client, *ent.Client, error) {
	// Get database driver and DSN based on configuration
	driverName := cfg.Database.GetDriverName()
	dsn := cfg.Database.GetDSN()

	logger.WithFields(map[string]interface{}{
		"driver": driverName,
		"type":   cfg.Database.Type,
	}).Info("Initializing database connection")

	// Create data directory for SQLite if needed
	if err := cfg.Database.CreateDataDirectory(); err != nil {
		return nil, nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// Create Ent client
	client, err := ent.Open(driverName, dsn)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"driver": driverName,
			"type":   cfg.Database.Type,
		}).Error("Failed opening database connection")
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// For simplicity, use the same client for read and write
	// In production, you might want separate read replicas
	logger.WithField("type", cfg.Database.Type).Info("Database connections established")
	return client, client, nil
}

// runMigrations runs database migrations
func runMigrations(client *ent.Client, logger logging.Logger) error {
	logger.Info("Running database migrations")

	ctx := context.Background()
	if err := client.Schema.Create(ctx); err != nil {
		logger.WithError(err).Error("Failed to run migrations")
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	logger.Info("Database migrations completed successfully")
	return nil
}

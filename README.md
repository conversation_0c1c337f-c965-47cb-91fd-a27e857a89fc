# 🚀 Social Content AI Platform

A comprehensive AI-powered social media content creation and management platform built with enterprise-grade microservices architecture.

## ✨ Features

### 🤖 AI Content Generation
- **Multi-Provider AI Integration**: OpenAI GPT, Anthropic Claude, Google Gemini
- **Advanced Prompt Engineering**: Optimized prompts for different content types
- **Content Improvement**: AI-powered content enhancement and optimization
- **Hashtag Generation**: Smart hashtag suggestions for different platforms
- **Template System**: Reusable content templates with AI integration

### 📱 Social Media Management
- **Multi-Platform Publishing**: Facebook, Instagram, Twitter, LinkedIn, YouTube, TikTok
- **OAuth Integration**: Secure social media account connections
- **Scheduled Publishing**: Advanced scheduling with timezone support
- **Content Optimization**: Platform-specific content formatting
- **Analytics Integration**: Performance tracking across platforms

### 👥 User Management
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Admin and user permissions
- **User Profiles**: Comprehensive user management
- **Session Management**: Secure session handling
- **Password Reset**: Email-based password recovery

### 💳 Credit & Billing System
- **Flexible Credit System**: Pay-per-use and subscription models
- **Stripe Integration**: Secure payment processing
- **Usage Tracking**: Detailed credit consumption analytics
- **Subscription Management**: Multiple subscription tiers
- **Transaction History**: Comprehensive billing records

### 📁 Asset Management
- **MinIO Storage**: S3-compatible object storage
- **Image Processing**: Automatic resizing, optimization, thumbnails
- **Document Processing**: PDF, DOCX, XLSX, PPTX text extraction
- **File Validation**: Content type and size validation
- **Presigned URLs**: Secure direct upload/download

### 📊 Analytics & Insights
- **Performance Tracking**: Content performance metrics
- **User Analytics**: Usage patterns and insights
- **Platform Analytics**: Cross-platform performance comparison
- **Custom Reports**: Detailed analytics reports
- **Real-time Dashboards**: Live performance monitoring

### 🔔 Real-time Notifications
- **WebSocket Support**: Real-time bidirectional communication
- **Email Notifications**: Template-based email system
- **Push Notifications**: Mobile and web push notifications
- **Notification Preferences**: User-customizable notification settings
- **Event-Driven Architecture**: Kafka-based message processing

## 🏗️ Architecture

The platform is built using microservices architecture with the following services:

### Core Services
- **API Gateway** (Port 8080): Central entry point and request routing
- **User Service** (Port 50050): User management and authentication
- **AI Content Service** (Port 50051): AI-powered content generation
- **Content Management Service** (Port 50052): Post and template management
- **Credit Service** (Port 50053): Billing and usage tracking

### Supporting Services
- **Asset Service** (Port 50054): File storage and processing
- **Integration Service** (Port 50055): Social media platform integrations
- **Notification Service** (Port 50056): Real-time notifications
- **Analytics Service** (Port 50057): Performance analytics
- **Schedule Processor**: Background job processing
- **RAG Processing Service**: Document processing and knowledge base

## 🛠️ Technology Stack

### Backend
- **Language**: Go (Golang) 1.21+
- **Framework**: Gin (HTTP), gRPC (Inter-service communication)
- **Database**: PostgreSQL with Ent ORM
- **Cache**: Redis
- **Message Queue**: Apache Kafka
- **Storage**: MinIO (S3-compatible)

### AI & ML
- **OpenAI**: GPT-3.5, GPT-4 models
- **Anthropic**: Claude models
- **Google AI**: Gemini models
- **RAG**: Neo4j (Graph DB) + Qdrant (Vector DB)

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Monitoring**: Prometheus + Grafana
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Distributed tracing support
- **Security**: JWT, RBAC, rate limiting

## 🚀 Quick Start

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- Make
- Git

### Installation

1. **Clone the repository**:
```bash
git clone https://github.com/your-org/social-content-ai.git
cd social-content-ai
```

2. **Copy environment configuration**:
```bash
cp .env.example .env
```

3. **Configure environment variables**:
Edit `.env` file with your API keys and configuration:
```bash
# AI Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Social Media OAuth
FACEBOOK_CLIENT_ID=your_facebook_app_id_here
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret_here
# ... (see .env.example for complete configuration)
```

4. **Start the platform**:
```bash
make up
```

5. **Verify installation**:
```bash
make health-check
```

### Access Points
- **API Gateway**: http://localhost:8080
- **Swagger Documentation**: http://localhost:8080/swagger/index.html
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Prometheus Metrics**: http://localhost:9090

## 🔧 Development

### Building Services

```bash
# Build all services
make build

# Build specific service
make build-user-service
make build-ai-content-service
make build-content-mgmt-service
```

### Testing

```bash
# Run all tests
make test

# Run tests for specific service
make test-user-service
make test-ai-content-service

# Run integration tests
make test-integration

# Run with coverage
make test-coverage
```

### Database Operations

```bash
# Run migrations for all services
make migrate

# Run migrations for specific service
make migrate-user-service

# Reset databases
make db-reset

# Seed test data
make db-seed
```

### Development Commands

```bash
# Start development environment
make dev

# View logs
make logs

# Stop all services
make down

# Clean up containers and volumes
make clean
```

## 📚 API Documentation

The platform provides comprehensive API documentation through Swagger UI:

- **Main API Documentation**: http://localhost:8080/swagger/index.html
- **User Service**: http://localhost:8081/swagger/index.html
- **AI Content Service**: http://localhost:8082/swagger/index.html
- **Content Management**: http://localhost:8083/swagger/index.html

### Key API Endpoints

#### Authentication
```bash
POST /api/v1/auth/register    # User registration
POST /api/v1/auth/login       # User login
POST /api/v1/auth/refresh     # Token refresh
```

#### Content Generation
```bash
POST /api/v1/content/generate    # Generate AI content
POST /api/v1/content/improve     # Improve existing content
POST /api/v1/content/hashtags    # Generate hashtags
```

#### Social Media Integration
```bash
GET  /api/v1/integrations           # List integrations
POST /api/v1/integrations/connect   # Connect social account
POST /api/v1/integrations/publish   # Publish content
```

## 🔍 Monitoring & Observability

### Grafana Dashboards
- **System Overview**: http://localhost:3000/d/system-overview
- **AI Content Service**: http://localhost:3000/d/ai-content-service
- **User Analytics**: http://localhost:3000/d/user-analytics

### Prometheus Metrics
- **Application Metrics**: http://localhost:9090
- **Custom Metrics**: Request rates, response times, error rates
- **Business Metrics**: Content generation, user activity, credit usage

### Health Checks
```bash
# Check all services health
make health-check

# Individual service health
curl http://localhost:8080/health
curl http://localhost:8081/health
```

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests for all services
make test-unit

# Run with verbose output
make test-unit-verbose

# Generate coverage report
make test-coverage
```

### Integration Tests
```bash
# Run integration tests
make test-integration

# Run specific integration test suite
make test-integration-auth
make test-integration-content
```

### Load Testing
```bash
# Run load tests
make test-load

# Custom load test scenarios
make test-load-content-generation
make test-load-social-publishing
```

## 🚀 Deployment

### Production Deployment

1. **Configure production environment**:
```bash
cp .env.example .env.production
# Edit .env.production with production values
```

2. **Build production images**:
```bash
make build-production
```

3. **Deploy with Docker Compose**:
```bash
make deploy-production
```

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n social-content-ai
```

### Environment-Specific Configurations
- **Development**: `.env.development`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

## 🔒 Security

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access Control**: Fine-grained permissions
- **API Rate Limiting**: Protection against abuse
- **CORS Configuration**: Cross-origin request security

### Data Protection
- **Encryption at Rest**: Database and file encryption
- **Encryption in Transit**: TLS/SSL for all communications
- **Secure Headers**: Security headers for web requests
- **Input Validation**: Comprehensive input sanitization

### Security Best Practices
- **Environment Variables**: Sensitive data in environment variables
- **Secrets Management**: Secure secret storage and rotation
- **Regular Updates**: Dependency and security updates
- **Security Scanning**: Automated vulnerability scanning

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**
4. **Add tests**: Ensure your changes are tested
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Submit a pull request**

### Development Guidelines
- Follow Go best practices and conventions
- Write comprehensive tests for new features
- Update documentation for API changes
- Use conventional commit messages
- Ensure all tests pass before submitting PR

### Code Review Process
- All changes require code review
- Automated tests must pass
- Documentation must be updated
- Security review for sensitive changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-org/social-content-ai/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/social-content-ai/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/social-content-ai/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- OpenAI for GPT models
- Anthropic for Claude models
- Google for Gemini models
- The Go community for excellent tools and libraries
- All contributors who help improve this platform

---

**Built with ❤️ by the Social Content AI Team**

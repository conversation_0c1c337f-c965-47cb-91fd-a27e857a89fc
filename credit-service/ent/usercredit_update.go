// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// UserCreditUpdate is the builder for updating UserCredit entities.
type UserCreditUpdate struct {
	config
	hooks    []Hook
	mutation *UserCreditMutation
}

// Where appends a list predicates to the UserCreditUpdate builder.
func (ucu *UserCreditUpdate) Where(ps ...predicate.UserCredit) *UserCreditUpdate {
	ucu.mutation.Where(ps...)
	return ucu
}

// SetUserID sets the "user_id" field.
func (ucu *UserCreditUpdate) SetUserID(u uuid.UUID) *UserCreditUpdate {
	ucu.mutation.SetUserID(u)
	return ucu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableUserID(u *uuid.UUID) *UserCreditUpdate {
	if u != nil {
		ucu.SetUserID(*u)
	}
	return ucu
}

// SetCurrentCredits sets the "current_credits" field.
func (ucu *UserCreditUpdate) SetCurrentCredits(i int) *UserCreditUpdate {
	ucu.mutation.ResetCurrentCredits()
	ucu.mutation.SetCurrentCredits(i)
	return ucu
}

// SetNillableCurrentCredits sets the "current_credits" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableCurrentCredits(i *int) *UserCreditUpdate {
	if i != nil {
		ucu.SetCurrentCredits(*i)
	}
	return ucu
}

// AddCurrentCredits adds i to the "current_credits" field.
func (ucu *UserCreditUpdate) AddCurrentCredits(i int) *UserCreditUpdate {
	ucu.mutation.AddCurrentCredits(i)
	return ucu
}

// SetTotalCredits sets the "total_credits" field.
func (ucu *UserCreditUpdate) SetTotalCredits(i int) *UserCreditUpdate {
	ucu.mutation.ResetTotalCredits()
	ucu.mutation.SetTotalCredits(i)
	return ucu
}

// SetNillableTotalCredits sets the "total_credits" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableTotalCredits(i *int) *UserCreditUpdate {
	if i != nil {
		ucu.SetTotalCredits(*i)
	}
	return ucu
}

// AddTotalCredits adds i to the "total_credits" field.
func (ucu *UserCreditUpdate) AddTotalCredits(i int) *UserCreditUpdate {
	ucu.mutation.AddTotalCredits(i)
	return ucu
}

// SetPlanID sets the "plan_id" field.
func (ucu *UserCreditUpdate) SetPlanID(s string) *UserCreditUpdate {
	ucu.mutation.SetPlanID(s)
	return ucu
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillablePlanID(s *string) *UserCreditUpdate {
	if s != nil {
		ucu.SetPlanID(*s)
	}
	return ucu
}

// ClearPlanID clears the value of the "plan_id" field.
func (ucu *UserCreditUpdate) ClearPlanID() *UserCreditUpdate {
	ucu.mutation.ClearPlanID()
	return ucu
}

// SetPlanName sets the "plan_name" field.
func (ucu *UserCreditUpdate) SetPlanName(s string) *UserCreditUpdate {
	ucu.mutation.SetPlanName(s)
	return ucu
}

// SetNillablePlanName sets the "plan_name" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillablePlanName(s *string) *UserCreditUpdate {
	if s != nil {
		ucu.SetPlanName(*s)
	}
	return ucu
}

// ClearPlanName clears the value of the "plan_name" field.
func (ucu *UserCreditUpdate) ClearPlanName() *UserCreditUpdate {
	ucu.mutation.ClearPlanName()
	return ucu
}

// SetPlanExpiresAt sets the "plan_expires_at" field.
func (ucu *UserCreditUpdate) SetPlanExpiresAt(t time.Time) *UserCreditUpdate {
	ucu.mutation.SetPlanExpiresAt(t)
	return ucu
}

// SetNillablePlanExpiresAt sets the "plan_expires_at" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillablePlanExpiresAt(t *time.Time) *UserCreditUpdate {
	if t != nil {
		ucu.SetPlanExpiresAt(*t)
	}
	return ucu
}

// ClearPlanExpiresAt clears the value of the "plan_expires_at" field.
func (ucu *UserCreditUpdate) ClearPlanExpiresAt() *UserCreditUpdate {
	ucu.mutation.ClearPlanExpiresAt()
	return ucu
}

// SetStatus sets the "status" field.
func (ucu *UserCreditUpdate) SetStatus(u usercredit.Status) *UserCreditUpdate {
	ucu.mutation.SetStatus(u)
	return ucu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableStatus(u *usercredit.Status) *UserCreditUpdate {
	if u != nil {
		ucu.SetStatus(*u)
	}
	return ucu
}

// SetMonthlyLimit sets the "monthly_limit" field.
func (ucu *UserCreditUpdate) SetMonthlyLimit(i int) *UserCreditUpdate {
	ucu.mutation.ResetMonthlyLimit()
	ucu.mutation.SetMonthlyLimit(i)
	return ucu
}

// SetNillableMonthlyLimit sets the "monthly_limit" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableMonthlyLimit(i *int) *UserCreditUpdate {
	if i != nil {
		ucu.SetMonthlyLimit(*i)
	}
	return ucu
}

// AddMonthlyLimit adds i to the "monthly_limit" field.
func (ucu *UserCreditUpdate) AddMonthlyLimit(i int) *UserCreditUpdate {
	ucu.mutation.AddMonthlyLimit(i)
	return ucu
}

// SetMonthlyUsed sets the "monthly_used" field.
func (ucu *UserCreditUpdate) SetMonthlyUsed(i int) *UserCreditUpdate {
	ucu.mutation.ResetMonthlyUsed()
	ucu.mutation.SetMonthlyUsed(i)
	return ucu
}

// SetNillableMonthlyUsed sets the "monthly_used" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableMonthlyUsed(i *int) *UserCreditUpdate {
	if i != nil {
		ucu.SetMonthlyUsed(*i)
	}
	return ucu
}

// AddMonthlyUsed adds i to the "monthly_used" field.
func (ucu *UserCreditUpdate) AddMonthlyUsed(i int) *UserCreditUpdate {
	ucu.mutation.AddMonthlyUsed(i)
	return ucu
}

// SetMonthlyResetAt sets the "monthly_reset_at" field.
func (ucu *UserCreditUpdate) SetMonthlyResetAt(t time.Time) *UserCreditUpdate {
	ucu.mutation.SetMonthlyResetAt(t)
	return ucu
}

// SetNillableMonthlyResetAt sets the "monthly_reset_at" field if the given value is not nil.
func (ucu *UserCreditUpdate) SetNillableMonthlyResetAt(t *time.Time) *UserCreditUpdate {
	if t != nil {
		ucu.SetMonthlyResetAt(*t)
	}
	return ucu
}

// ClearMonthlyResetAt clears the value of the "monthly_reset_at" field.
func (ucu *UserCreditUpdate) ClearMonthlyResetAt() *UserCreditUpdate {
	ucu.mutation.ClearMonthlyResetAt()
	return ucu
}

// SetMetadata sets the "metadata" field.
func (ucu *UserCreditUpdate) SetMetadata(m map[string]interface{}) *UserCreditUpdate {
	ucu.mutation.SetMetadata(m)
	return ucu
}

// ClearMetadata clears the value of the "metadata" field.
func (ucu *UserCreditUpdate) ClearMetadata() *UserCreditUpdate {
	ucu.mutation.ClearMetadata()
	return ucu
}

// SetUpdatedAt sets the "updated_at" field.
func (ucu *UserCreditUpdate) SetUpdatedAt(t time.Time) *UserCreditUpdate {
	ucu.mutation.SetUpdatedAt(t)
	return ucu
}

// Mutation returns the UserCreditMutation object of the builder.
func (ucu *UserCreditUpdate) Mutation() *UserCreditMutation {
	return ucu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ucu *UserCreditUpdate) Save(ctx context.Context) (int, error) {
	ucu.defaults()
	return withHooks(ctx, ucu.sqlSave, ucu.mutation, ucu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ucu *UserCreditUpdate) SaveX(ctx context.Context) int {
	affected, err := ucu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ucu *UserCreditUpdate) Exec(ctx context.Context) error {
	_, err := ucu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucu *UserCreditUpdate) ExecX(ctx context.Context) {
	if err := ucu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ucu *UserCreditUpdate) defaults() {
	if _, ok := ucu.mutation.UpdatedAt(); !ok {
		v := usercredit.UpdateDefaultUpdatedAt()
		ucu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ucu *UserCreditUpdate) check() error {
	if v, ok := ucu.mutation.CurrentCredits(); ok {
		if err := usercredit.CurrentCreditsValidator(v); err != nil {
			return &ValidationError{Name: "current_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.current_credits": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.TotalCredits(); ok {
		if err := usercredit.TotalCreditsValidator(v); err != nil {
			return &ValidationError{Name: "total_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.total_credits": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.PlanID(); ok {
		if err := usercredit.PlanIDValidator(v); err != nil {
			return &ValidationError{Name: "plan_id", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_id": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.PlanName(); ok {
		if err := usercredit.PlanNameValidator(v); err != nil {
			return &ValidationError{Name: "plan_name", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_name": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.Status(); ok {
		if err := usercredit.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "UserCredit.status": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.MonthlyLimit(); ok {
		if err := usercredit.MonthlyLimitValidator(v); err != nil {
			return &ValidationError{Name: "monthly_limit", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_limit": %w`, err)}
		}
	}
	if v, ok := ucu.mutation.MonthlyUsed(); ok {
		if err := usercredit.MonthlyUsedValidator(v); err != nil {
			return &ValidationError{Name: "monthly_used", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_used": %w`, err)}
		}
	}
	return nil
}

func (ucu *UserCreditUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ucu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(usercredit.Table, usercredit.Columns, sqlgraph.NewFieldSpec(usercredit.FieldID, field.TypeUUID))
	if ps := ucu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ucu.mutation.UserID(); ok {
		_spec.SetField(usercredit.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ucu.mutation.CurrentCredits(); ok {
		_spec.SetField(usercredit.FieldCurrentCredits, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.AddedCurrentCredits(); ok {
		_spec.AddField(usercredit.FieldCurrentCredits, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.TotalCredits(); ok {
		_spec.SetField(usercredit.FieldTotalCredits, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.AddedTotalCredits(); ok {
		_spec.AddField(usercredit.FieldTotalCredits, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.PlanID(); ok {
		_spec.SetField(usercredit.FieldPlanID, field.TypeString, value)
	}
	if ucu.mutation.PlanIDCleared() {
		_spec.ClearField(usercredit.FieldPlanID, field.TypeString)
	}
	if value, ok := ucu.mutation.PlanName(); ok {
		_spec.SetField(usercredit.FieldPlanName, field.TypeString, value)
	}
	if ucu.mutation.PlanNameCleared() {
		_spec.ClearField(usercredit.FieldPlanName, field.TypeString)
	}
	if value, ok := ucu.mutation.PlanExpiresAt(); ok {
		_spec.SetField(usercredit.FieldPlanExpiresAt, field.TypeTime, value)
	}
	if ucu.mutation.PlanExpiresAtCleared() {
		_spec.ClearField(usercredit.FieldPlanExpiresAt, field.TypeTime)
	}
	if value, ok := ucu.mutation.Status(); ok {
		_spec.SetField(usercredit.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ucu.mutation.MonthlyLimit(); ok {
		_spec.SetField(usercredit.FieldMonthlyLimit, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.AddedMonthlyLimit(); ok {
		_spec.AddField(usercredit.FieldMonthlyLimit, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.MonthlyUsed(); ok {
		_spec.SetField(usercredit.FieldMonthlyUsed, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.AddedMonthlyUsed(); ok {
		_spec.AddField(usercredit.FieldMonthlyUsed, field.TypeInt, value)
	}
	if value, ok := ucu.mutation.MonthlyResetAt(); ok {
		_spec.SetField(usercredit.FieldMonthlyResetAt, field.TypeTime, value)
	}
	if ucu.mutation.MonthlyResetAtCleared() {
		_spec.ClearField(usercredit.FieldMonthlyResetAt, field.TypeTime)
	}
	if value, ok := ucu.mutation.Metadata(); ok {
		_spec.SetField(usercredit.FieldMetadata, field.TypeJSON, value)
	}
	if ucu.mutation.MetadataCleared() {
		_spec.ClearField(usercredit.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ucu.mutation.UpdatedAt(); ok {
		_spec.SetField(usercredit.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ucu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{usercredit.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ucu.mutation.done = true
	return n, nil
}

// UserCreditUpdateOne is the builder for updating a single UserCredit entity.
type UserCreditUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserCreditMutation
}

// SetUserID sets the "user_id" field.
func (ucuo *UserCreditUpdateOne) SetUserID(u uuid.UUID) *UserCreditUpdateOne {
	ucuo.mutation.SetUserID(u)
	return ucuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableUserID(u *uuid.UUID) *UserCreditUpdateOne {
	if u != nil {
		ucuo.SetUserID(*u)
	}
	return ucuo
}

// SetCurrentCredits sets the "current_credits" field.
func (ucuo *UserCreditUpdateOne) SetCurrentCredits(i int) *UserCreditUpdateOne {
	ucuo.mutation.ResetCurrentCredits()
	ucuo.mutation.SetCurrentCredits(i)
	return ucuo
}

// SetNillableCurrentCredits sets the "current_credits" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableCurrentCredits(i *int) *UserCreditUpdateOne {
	if i != nil {
		ucuo.SetCurrentCredits(*i)
	}
	return ucuo
}

// AddCurrentCredits adds i to the "current_credits" field.
func (ucuo *UserCreditUpdateOne) AddCurrentCredits(i int) *UserCreditUpdateOne {
	ucuo.mutation.AddCurrentCredits(i)
	return ucuo
}

// SetTotalCredits sets the "total_credits" field.
func (ucuo *UserCreditUpdateOne) SetTotalCredits(i int) *UserCreditUpdateOne {
	ucuo.mutation.ResetTotalCredits()
	ucuo.mutation.SetTotalCredits(i)
	return ucuo
}

// SetNillableTotalCredits sets the "total_credits" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableTotalCredits(i *int) *UserCreditUpdateOne {
	if i != nil {
		ucuo.SetTotalCredits(*i)
	}
	return ucuo
}

// AddTotalCredits adds i to the "total_credits" field.
func (ucuo *UserCreditUpdateOne) AddTotalCredits(i int) *UserCreditUpdateOne {
	ucuo.mutation.AddTotalCredits(i)
	return ucuo
}

// SetPlanID sets the "plan_id" field.
func (ucuo *UserCreditUpdateOne) SetPlanID(s string) *UserCreditUpdateOne {
	ucuo.mutation.SetPlanID(s)
	return ucuo
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillablePlanID(s *string) *UserCreditUpdateOne {
	if s != nil {
		ucuo.SetPlanID(*s)
	}
	return ucuo
}

// ClearPlanID clears the value of the "plan_id" field.
func (ucuo *UserCreditUpdateOne) ClearPlanID() *UserCreditUpdateOne {
	ucuo.mutation.ClearPlanID()
	return ucuo
}

// SetPlanName sets the "plan_name" field.
func (ucuo *UserCreditUpdateOne) SetPlanName(s string) *UserCreditUpdateOne {
	ucuo.mutation.SetPlanName(s)
	return ucuo
}

// SetNillablePlanName sets the "plan_name" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillablePlanName(s *string) *UserCreditUpdateOne {
	if s != nil {
		ucuo.SetPlanName(*s)
	}
	return ucuo
}

// ClearPlanName clears the value of the "plan_name" field.
func (ucuo *UserCreditUpdateOne) ClearPlanName() *UserCreditUpdateOne {
	ucuo.mutation.ClearPlanName()
	return ucuo
}

// SetPlanExpiresAt sets the "plan_expires_at" field.
func (ucuo *UserCreditUpdateOne) SetPlanExpiresAt(t time.Time) *UserCreditUpdateOne {
	ucuo.mutation.SetPlanExpiresAt(t)
	return ucuo
}

// SetNillablePlanExpiresAt sets the "plan_expires_at" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillablePlanExpiresAt(t *time.Time) *UserCreditUpdateOne {
	if t != nil {
		ucuo.SetPlanExpiresAt(*t)
	}
	return ucuo
}

// ClearPlanExpiresAt clears the value of the "plan_expires_at" field.
func (ucuo *UserCreditUpdateOne) ClearPlanExpiresAt() *UserCreditUpdateOne {
	ucuo.mutation.ClearPlanExpiresAt()
	return ucuo
}

// SetStatus sets the "status" field.
func (ucuo *UserCreditUpdateOne) SetStatus(u usercredit.Status) *UserCreditUpdateOne {
	ucuo.mutation.SetStatus(u)
	return ucuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableStatus(u *usercredit.Status) *UserCreditUpdateOne {
	if u != nil {
		ucuo.SetStatus(*u)
	}
	return ucuo
}

// SetMonthlyLimit sets the "monthly_limit" field.
func (ucuo *UserCreditUpdateOne) SetMonthlyLimit(i int) *UserCreditUpdateOne {
	ucuo.mutation.ResetMonthlyLimit()
	ucuo.mutation.SetMonthlyLimit(i)
	return ucuo
}

// SetNillableMonthlyLimit sets the "monthly_limit" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableMonthlyLimit(i *int) *UserCreditUpdateOne {
	if i != nil {
		ucuo.SetMonthlyLimit(*i)
	}
	return ucuo
}

// AddMonthlyLimit adds i to the "monthly_limit" field.
func (ucuo *UserCreditUpdateOne) AddMonthlyLimit(i int) *UserCreditUpdateOne {
	ucuo.mutation.AddMonthlyLimit(i)
	return ucuo
}

// SetMonthlyUsed sets the "monthly_used" field.
func (ucuo *UserCreditUpdateOne) SetMonthlyUsed(i int) *UserCreditUpdateOne {
	ucuo.mutation.ResetMonthlyUsed()
	ucuo.mutation.SetMonthlyUsed(i)
	return ucuo
}

// SetNillableMonthlyUsed sets the "monthly_used" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableMonthlyUsed(i *int) *UserCreditUpdateOne {
	if i != nil {
		ucuo.SetMonthlyUsed(*i)
	}
	return ucuo
}

// AddMonthlyUsed adds i to the "monthly_used" field.
func (ucuo *UserCreditUpdateOne) AddMonthlyUsed(i int) *UserCreditUpdateOne {
	ucuo.mutation.AddMonthlyUsed(i)
	return ucuo
}

// SetMonthlyResetAt sets the "monthly_reset_at" field.
func (ucuo *UserCreditUpdateOne) SetMonthlyResetAt(t time.Time) *UserCreditUpdateOne {
	ucuo.mutation.SetMonthlyResetAt(t)
	return ucuo
}

// SetNillableMonthlyResetAt sets the "monthly_reset_at" field if the given value is not nil.
func (ucuo *UserCreditUpdateOne) SetNillableMonthlyResetAt(t *time.Time) *UserCreditUpdateOne {
	if t != nil {
		ucuo.SetMonthlyResetAt(*t)
	}
	return ucuo
}

// ClearMonthlyResetAt clears the value of the "monthly_reset_at" field.
func (ucuo *UserCreditUpdateOne) ClearMonthlyResetAt() *UserCreditUpdateOne {
	ucuo.mutation.ClearMonthlyResetAt()
	return ucuo
}

// SetMetadata sets the "metadata" field.
func (ucuo *UserCreditUpdateOne) SetMetadata(m map[string]interface{}) *UserCreditUpdateOne {
	ucuo.mutation.SetMetadata(m)
	return ucuo
}

// ClearMetadata clears the value of the "metadata" field.
func (ucuo *UserCreditUpdateOne) ClearMetadata() *UserCreditUpdateOne {
	ucuo.mutation.ClearMetadata()
	return ucuo
}

// SetUpdatedAt sets the "updated_at" field.
func (ucuo *UserCreditUpdateOne) SetUpdatedAt(t time.Time) *UserCreditUpdateOne {
	ucuo.mutation.SetUpdatedAt(t)
	return ucuo
}

// Mutation returns the UserCreditMutation object of the builder.
func (ucuo *UserCreditUpdateOne) Mutation() *UserCreditMutation {
	return ucuo.mutation
}

// Where appends a list predicates to the UserCreditUpdate builder.
func (ucuo *UserCreditUpdateOne) Where(ps ...predicate.UserCredit) *UserCreditUpdateOne {
	ucuo.mutation.Where(ps...)
	return ucuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ucuo *UserCreditUpdateOne) Select(field string, fields ...string) *UserCreditUpdateOne {
	ucuo.fields = append([]string{field}, fields...)
	return ucuo
}

// Save executes the query and returns the updated UserCredit entity.
func (ucuo *UserCreditUpdateOne) Save(ctx context.Context) (*UserCredit, error) {
	ucuo.defaults()
	return withHooks(ctx, ucuo.sqlSave, ucuo.mutation, ucuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ucuo *UserCreditUpdateOne) SaveX(ctx context.Context) *UserCredit {
	node, err := ucuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ucuo *UserCreditUpdateOne) Exec(ctx context.Context) error {
	_, err := ucuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucuo *UserCreditUpdateOne) ExecX(ctx context.Context) {
	if err := ucuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ucuo *UserCreditUpdateOne) defaults() {
	if _, ok := ucuo.mutation.UpdatedAt(); !ok {
		v := usercredit.UpdateDefaultUpdatedAt()
		ucuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ucuo *UserCreditUpdateOne) check() error {
	if v, ok := ucuo.mutation.CurrentCredits(); ok {
		if err := usercredit.CurrentCreditsValidator(v); err != nil {
			return &ValidationError{Name: "current_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.current_credits": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.TotalCredits(); ok {
		if err := usercredit.TotalCreditsValidator(v); err != nil {
			return &ValidationError{Name: "total_credits", err: fmt.Errorf(`ent: validator failed for field "UserCredit.total_credits": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.PlanID(); ok {
		if err := usercredit.PlanIDValidator(v); err != nil {
			return &ValidationError{Name: "plan_id", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_id": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.PlanName(); ok {
		if err := usercredit.PlanNameValidator(v); err != nil {
			return &ValidationError{Name: "plan_name", err: fmt.Errorf(`ent: validator failed for field "UserCredit.plan_name": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.Status(); ok {
		if err := usercredit.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "UserCredit.status": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.MonthlyLimit(); ok {
		if err := usercredit.MonthlyLimitValidator(v); err != nil {
			return &ValidationError{Name: "monthly_limit", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_limit": %w`, err)}
		}
	}
	if v, ok := ucuo.mutation.MonthlyUsed(); ok {
		if err := usercredit.MonthlyUsedValidator(v); err != nil {
			return &ValidationError{Name: "monthly_used", err: fmt.Errorf(`ent: validator failed for field "UserCredit.monthly_used": %w`, err)}
		}
	}
	return nil
}

func (ucuo *UserCreditUpdateOne) sqlSave(ctx context.Context) (_node *UserCredit, err error) {
	if err := ucuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(usercredit.Table, usercredit.Columns, sqlgraph.NewFieldSpec(usercredit.FieldID, field.TypeUUID))
	id, ok := ucuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "UserCredit.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ucuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, usercredit.FieldID)
		for _, f := range fields {
			if !usercredit.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != usercredit.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ucuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ucuo.mutation.UserID(); ok {
		_spec.SetField(usercredit.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ucuo.mutation.CurrentCredits(); ok {
		_spec.SetField(usercredit.FieldCurrentCredits, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.AddedCurrentCredits(); ok {
		_spec.AddField(usercredit.FieldCurrentCredits, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.TotalCredits(); ok {
		_spec.SetField(usercredit.FieldTotalCredits, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.AddedTotalCredits(); ok {
		_spec.AddField(usercredit.FieldTotalCredits, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.PlanID(); ok {
		_spec.SetField(usercredit.FieldPlanID, field.TypeString, value)
	}
	if ucuo.mutation.PlanIDCleared() {
		_spec.ClearField(usercredit.FieldPlanID, field.TypeString)
	}
	if value, ok := ucuo.mutation.PlanName(); ok {
		_spec.SetField(usercredit.FieldPlanName, field.TypeString, value)
	}
	if ucuo.mutation.PlanNameCleared() {
		_spec.ClearField(usercredit.FieldPlanName, field.TypeString)
	}
	if value, ok := ucuo.mutation.PlanExpiresAt(); ok {
		_spec.SetField(usercredit.FieldPlanExpiresAt, field.TypeTime, value)
	}
	if ucuo.mutation.PlanExpiresAtCleared() {
		_spec.ClearField(usercredit.FieldPlanExpiresAt, field.TypeTime)
	}
	if value, ok := ucuo.mutation.Status(); ok {
		_spec.SetField(usercredit.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ucuo.mutation.MonthlyLimit(); ok {
		_spec.SetField(usercredit.FieldMonthlyLimit, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.AddedMonthlyLimit(); ok {
		_spec.AddField(usercredit.FieldMonthlyLimit, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.MonthlyUsed(); ok {
		_spec.SetField(usercredit.FieldMonthlyUsed, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.AddedMonthlyUsed(); ok {
		_spec.AddField(usercredit.FieldMonthlyUsed, field.TypeInt, value)
	}
	if value, ok := ucuo.mutation.MonthlyResetAt(); ok {
		_spec.SetField(usercredit.FieldMonthlyResetAt, field.TypeTime, value)
	}
	if ucuo.mutation.MonthlyResetAtCleared() {
		_spec.ClearField(usercredit.FieldMonthlyResetAt, field.TypeTime)
	}
	if value, ok := ucuo.mutation.Metadata(); ok {
		_spec.SetField(usercredit.FieldMetadata, field.TypeJSON, value)
	}
	if ucuo.mutation.MetadataCleared() {
		_spec.ClearField(usercredit.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ucuo.mutation.UpdatedAt(); ok {
		_spec.SetField(usercredit.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &UserCredit{config: ucuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ucuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{usercredit.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ucuo.mutation.done = true
	return _node, nil
}

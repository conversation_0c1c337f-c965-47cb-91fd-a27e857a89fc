package handlers

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	aicontentv1 "github.com/social-content-ai/proto-shared/ai-content/v1"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	"github.com/social-content-ai/api-gateway/internal/middleware"
)

// GenerateContentRequest represents content generation request
type GenerateContentRequest struct {
	Topic         string            `json:"topic" binding:"required"`
	ContentType   string            `json:"content_type" binding:"required,oneof=social_post blog_post email advertisement caption"`
	Platform      string            `json:"platform,omitempty" binding:"omitempty,oneof=facebook instagram twitter linkedin tiktok youtube"`
	Tone          string            `json:"tone" binding:"required,oneof=professional friendly humorous formal casual enthusiastic authoritative"`
	Keywords      []string          `json:"keywords,omitempty"`
	TargetLength  int               `json:"target_length,omitempty"`
	Audience      string            `json:"audience,omitempty"`
	CallToAction  string            `json:"call_to_action,omitempty"`
	BrandVoice    string            `json:"brand_voice,omitempty"`
	Context       string            `json:"context,omitempty"`
	Variables     map[string]string `json:"variables,omitempty"`
	Constraints   []string          `json:"constraints,omitempty"`
	Examples      []string          `json:"examples,omitempty"`
	Provider      string            `json:"provider,omitempty" binding:"omitempty,oneof=openai anthropic google"`
	Model         string            `json:"model,omitempty"`
	Temperature   float64           `json:"temperature,omitempty"`
	MaxTokens     int               `json:"max_tokens,omitempty"`
}

// ImproveContentRequest represents content improvement request
type ImproveContentRequest struct {
	Content      string  `json:"content" binding:"required"`
	Instructions string  `json:"instructions" binding:"required"`
	Platform     string  `json:"platform,omitempty" binding:"omitempty,oneof=facebook instagram twitter linkedin tiktok youtube"`
	Provider     string  `json:"provider,omitempty" binding:"omitempty,oneof=openai anthropic google"`
	Model        string  `json:"model,omitempty"`
	Temperature  float64 `json:"temperature,omitempty"`
	MaxTokens    int     `json:"max_tokens,omitempty"`
}

// GenerateHashtagsRequest represents hashtag generation request
type GenerateHashtagsRequest struct {
	Content  string `json:"content" binding:"required"`
	Platform string `json:"platform" binding:"required,oneof=facebook instagram twitter linkedin tiktok youtube"`
	Count    int    `json:"count,omitempty"`
	Provider string `json:"provider,omitempty" binding:"omitempty,oneof=openai anthropic google"`
}

// GenerateContent handles content generation
// @Summary Generate content
// @Description Generate AI-powered content based on specifications
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateContentRequest true "Content generation parameters"
// @Success 200 {object} map[string]interface{} "Generated content"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 402 {object} map[string]interface{} "Insufficient credits"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/generate [post]
func (h *Handlers) GenerateContent(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req GenerateContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Check user credits first
	creditReq := &creditv1.ValidateCreditRequest{
		UserId:          userID,
		RequiredCredits: 1, // 1 credit per generation
	}

	creditResp, err := h.services.CreditService.ValidateCredit(context.Background(), creditReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to validate credits")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to validate credits",
		})
		return
	}

	if !creditResp.HasSufficientCredits {
		c.JSON(http.StatusPaymentRequired, gin.H{
			"error":            "Insufficient credits",
			"current_credits":  creditResp.CurrentCredits,
			"required_credits": creditResp.RequiredCredits,
			"shortage":         creditResp.Shortage,
		})
		return
	}

	// Generate content
	generateReq := &aicontentv1.GenerateContentRequest{
		UserId:        userID,
		Topic:         req.Topic,
		ContentType:   req.ContentType,
		Platform:      req.Platform,
		Tone:          req.Tone,
		Keywords:      req.Keywords,
		TargetLength:  int32(req.TargetLength),
		Audience:      req.Audience,
		CallToAction:  req.CallToAction,
		BrandVoice:    req.BrandVoice,
		Context:       req.Context,
		Variables:     req.Variables,
		Constraints:   req.Constraints,
		Examples:      req.Examples,
		Provider:      req.Provider,
		Model:         req.Model,
		Temperature:   float32(req.Temperature),
		MaxTokens:     int32(req.MaxTokens),
	}

	resp, err := h.services.AIContentService.GenerateContent(context.Background(), generateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Content generation failed")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Content generation failed",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":       userID,
		"generation_id": resp.GenerationId,
		"provider":      resp.Provider,
	}).Info("Content generated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":       "Content generated successfully",
		"generation_id": resp.GenerationId,
		"content":       resp.Content,
		"provider":      resp.Provider,
		"model":         resp.Model,
		"tokens_used":   resp.TokensUsed,
		"credits_used":  resp.CreditsUsed,
		"metadata":      resp.Metadata,
	})
}

// ImproveContent handles content improvement
// @Summary Improve content
// @Description Improve existing content based on instructions
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body ImproveContentRequest true "Content improvement parameters"
// @Success 200 {object} map[string]interface{} "Improved content"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 402 {object} map[string]interface{} "Insufficient credits"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/improve [post]
func (h *Handlers) ImproveContent(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req ImproveContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Check user credits
	creditReq := &creditv1.ValidateCreditRequest{
		UserId:          userID,
		RequiredCredits: 1, // 1 credit per improvement
	}

	creditResp, err := h.services.CreditService.ValidateCredit(context.Background(), creditReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to validate credits")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to validate credits",
		})
		return
	}

	if !creditResp.HasSufficientCredits {
		c.JSON(http.StatusPaymentRequired, gin.H{
			"error":            "Insufficient credits",
			"current_credits":  creditResp.CurrentCredits,
			"required_credits": creditResp.RequiredCredits,
		})
		return
	}

	// Improve content
	improveReq := &aicontentv1.ImproveContentRequest{
		UserId:       userID,
		Content:      req.Content,
		Instructions: req.Instructions,
		Platform:     req.Platform,
		Provider:     req.Provider,
		Model:        req.Model,
		Temperature:  float32(req.Temperature),
		MaxTokens:    int32(req.MaxTokens),
	}

	resp, err := h.services.AIContentService.ImproveContent(context.Background(), improveReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Content improvement failed")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Content improvement failed",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":       userID,
		"generation_id": resp.GenerationId,
		"provider":      resp.Provider,
	}).Info("Content improved successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":         "Content improved successfully",
		"generation_id":   resp.GenerationId,
		"improved_content": resp.ImprovedContent,
		"original_content": req.Content,
		"provider":        resp.Provider,
		"model":           resp.Model,
		"tokens_used":     resp.TokensUsed,
		"credits_used":    resp.CreditsUsed,
	})
}

// GenerateHashtags handles hashtag generation
// @Summary Generate hashtags
// @Description Generate relevant hashtags for content
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body GenerateHashtagsRequest true "Hashtag generation parameters"
// @Success 200 {object} map[string]interface{} "Generated hashtags"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/hashtags [post]
func (h *Handlers) GenerateHashtags(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	var req GenerateHashtagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Set default count if not provided
	if req.Count <= 0 {
		req.Count = 10
	}

	// Generate hashtags
	hashtagReq := &aicontentv1.GenerateHashtagsRequest{
		UserId:   userID,
		Content:  req.Content,
		Platform: req.Platform,
		Count:    int32(req.Count),
		Provider: req.Provider,
	}

	resp, err := h.services.AIContentService.GenerateHashtags(context.Background(), hashtagReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Hashtag generation failed")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Hashtag generation failed",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":  userID,
		"platform": req.Platform,
		"count":    len(resp.Hashtags),
	}).Info("Hashtags generated successfully")

	c.JSON(http.StatusOK, gin.H{
		"message":   "Hashtags generated successfully",
		"hashtags":  resp.Hashtags,
		"platform":  req.Platform,
		"provider":  resp.Provider,
	})
}

// ListGenerations handles listing user's content generations
// @Summary List content generations
// @Description Get list of user's content generations with pagination
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param content_type query string false "Filter by content type"
// @Param platform query string false "Filter by platform"
// @Param provider query string false "Filter by AI provider"
// @Success 200 {object} map[string]interface{} "List of generations"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/generations [get]
func (h *Handlers) ListGenerations(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	contentType := c.Query("content_type")
	platform := c.Query("platform")
	provider := c.Query("provider")

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// List generations
	listReq := &aicontentv1.ListGenerationsRequest{
		UserId:      userID,
		Page:        int32(page),
		Limit:       int32(limit),
		ContentType: contentType,
		Platform:    platform,
		Provider:    provider,
	}

	resp, err := h.services.AIContentService.ListGenerations(context.Background(), listReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to list generations")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to list generations",
		})
		return
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, int(resp.Total))

	c.JSON(http.StatusOK, gin.H{
		"message":     "Generations retrieved successfully",
		"generations": resp.Generations,
		"pagination":  pagination,
	})
}

// GetGeneration handles getting a specific generation
// @Summary Get content generation
// @Description Get details of a specific content generation
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Generation ID"
// @Success 200 {object} map[string]interface{} "Generation details"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Generation not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/generations/{id} [get]
func (h *Handlers) GetGeneration(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	generationID := c.Param("id")
	if generationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Generation ID is required",
		})
		return
	}

	// Get generation
	getReq := &aicontentv1.GetGenerationRequest{
		UserId:       userID,
		GenerationId: generationID,
	}

	resp, err := h.services.AIContentService.GetGeneration(context.Background(), getReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Generation not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":       userID,
			"generation_id": generationID,
		}).Error("Failed to get generation")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get generation",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Generation retrieved successfully",
		"generation": resp.Generation,
	})
}

// DeleteGeneration handles deleting a generation
// @Summary Delete content generation
// @Description Delete a specific content generation
// @Tags content
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Generation ID"
// @Success 200 {object} map[string]interface{} "Generation deleted"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Generation not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /content/generations/{id} [delete]
func (h *Handlers) DeleteGeneration(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	generationID := c.Param("id")
	if generationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Generation ID is required",
		})
		return
	}

	// Delete generation
	deleteReq := &aicontentv1.DeleteGenerationRequest{
		UserId:       userID,
		GenerationId: generationID,
	}

	_, err := h.services.AIContentService.DeleteGeneration(context.Background(), deleteReq)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Generation not found",
			})
			return
		}

		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id":       userID,
			"generation_id": generationID,
		}).Error("Failed to delete generation")

		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete generation",
		})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":       userID,
		"generation_id": generationID,
	}).Info("Generation deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Generation deleted successfully",
	})
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
)

// CreditReservationCreate is the builder for creating a CreditReservation entity.
type CreditReservationCreate struct {
	config
	mutation *CreditReservationMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (crc *CreditReservationCreate) SetUserID(u uuid.UUID) *CreditReservationCreate {
	crc.mutation.SetUserID(u)
	return crc
}

// SetAmount sets the "amount" field.
func (crc *CreditReservationCreate) SetAmount(i int) *CreditReservationCreate {
	crc.mutation.SetAmount(i)
	return crc
}

// SetPurpose sets the "purpose" field.
func (crc *CreditReservationCreate) SetPurpose(s string) *CreditReservationCreate {
	crc.mutation.SetPurpose(s)
	return crc
}

// SetReferenceID sets the "reference_id" field.
func (crc *CreditReservationCreate) SetReferenceID(s string) *CreditReservationCreate {
	crc.mutation.SetReferenceID(s)
	return crc
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableReferenceID(s *string) *CreditReservationCreate {
	if s != nil {
		crc.SetReferenceID(*s)
	}
	return crc
}

// SetReferenceType sets the "reference_type" field.
func (crc *CreditReservationCreate) SetReferenceType(s string) *CreditReservationCreate {
	crc.mutation.SetReferenceType(s)
	return crc
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableReferenceType(s *string) *CreditReservationCreate {
	if s != nil {
		crc.SetReferenceType(*s)
	}
	return crc
}

// SetStatus sets the "status" field.
func (crc *CreditReservationCreate) SetStatus(c creditreservation.Status) *CreditReservationCreate {
	crc.mutation.SetStatus(c)
	return crc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableStatus(c *creditreservation.Status) *CreditReservationCreate {
	if c != nil {
		crc.SetStatus(*c)
	}
	return crc
}

// SetExpiresAt sets the "expires_at" field.
func (crc *CreditReservationCreate) SetExpiresAt(t time.Time) *CreditReservationCreate {
	crc.mutation.SetExpiresAt(t)
	return crc
}

// SetConsumedAt sets the "consumed_at" field.
func (crc *CreditReservationCreate) SetConsumedAt(t time.Time) *CreditReservationCreate {
	crc.mutation.SetConsumedAt(t)
	return crc
}

// SetNillableConsumedAt sets the "consumed_at" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableConsumedAt(t *time.Time) *CreditReservationCreate {
	if t != nil {
		crc.SetConsumedAt(*t)
	}
	return crc
}

// SetMetadata sets the "metadata" field.
func (crc *CreditReservationCreate) SetMetadata(m map[string]interface{}) *CreditReservationCreate {
	crc.mutation.SetMetadata(m)
	return crc
}

// SetCreatedAt sets the "created_at" field.
func (crc *CreditReservationCreate) SetCreatedAt(t time.Time) *CreditReservationCreate {
	crc.mutation.SetCreatedAt(t)
	return crc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableCreatedAt(t *time.Time) *CreditReservationCreate {
	if t != nil {
		crc.SetCreatedAt(*t)
	}
	return crc
}

// SetUpdatedAt sets the "updated_at" field.
func (crc *CreditReservationCreate) SetUpdatedAt(t time.Time) *CreditReservationCreate {
	crc.mutation.SetUpdatedAt(t)
	return crc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableUpdatedAt(t *time.Time) *CreditReservationCreate {
	if t != nil {
		crc.SetUpdatedAt(*t)
	}
	return crc
}

// SetID sets the "id" field.
func (crc *CreditReservationCreate) SetID(u uuid.UUID) *CreditReservationCreate {
	crc.mutation.SetID(u)
	return crc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (crc *CreditReservationCreate) SetNillableID(u *uuid.UUID) *CreditReservationCreate {
	if u != nil {
		crc.SetID(*u)
	}
	return crc
}

// Mutation returns the CreditReservationMutation object of the builder.
func (crc *CreditReservationCreate) Mutation() *CreditReservationMutation {
	return crc.mutation
}

// Save creates the CreditReservation in the database.
func (crc *CreditReservationCreate) Save(ctx context.Context) (*CreditReservation, error) {
	crc.defaults()
	return withHooks(ctx, crc.sqlSave, crc.mutation, crc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (crc *CreditReservationCreate) SaveX(ctx context.Context) *CreditReservation {
	v, err := crc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (crc *CreditReservationCreate) Exec(ctx context.Context) error {
	_, err := crc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (crc *CreditReservationCreate) ExecX(ctx context.Context) {
	if err := crc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (crc *CreditReservationCreate) defaults() {
	if _, ok := crc.mutation.Status(); !ok {
		v := creditreservation.DefaultStatus
		crc.mutation.SetStatus(v)
	}
	if _, ok := crc.mutation.CreatedAt(); !ok {
		v := creditreservation.DefaultCreatedAt()
		crc.mutation.SetCreatedAt(v)
	}
	if _, ok := crc.mutation.UpdatedAt(); !ok {
		v := creditreservation.DefaultUpdatedAt()
		crc.mutation.SetUpdatedAt(v)
	}
	if _, ok := crc.mutation.ID(); !ok {
		v := creditreservation.DefaultID()
		crc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (crc *CreditReservationCreate) check() error {
	if _, ok := crc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "CreditReservation.user_id"`)}
	}
	if _, ok := crc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "CreditReservation.amount"`)}
	}
	if v, ok := crc.mutation.Amount(); ok {
		if err := creditreservation.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.amount": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Purpose(); !ok {
		return &ValidationError{Name: "purpose", err: errors.New(`ent: missing required field "CreditReservation.purpose"`)}
	}
	if v, ok := crc.mutation.Purpose(); ok {
		if err := creditreservation.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.purpose": %w`, err)}
		}
	}
	if v, ok := crc.mutation.ReferenceID(); ok {
		if err := creditreservation.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_id": %w`, err)}
		}
	}
	if v, ok := crc.mutation.ReferenceType(); ok {
		if err := creditreservation.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_type": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "CreditReservation.status"`)}
	}
	if v, ok := crc.mutation.Status(); ok {
		if err := creditreservation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.status": %w`, err)}
		}
	}
	if _, ok := crc.mutation.ExpiresAt(); !ok {
		return &ValidationError{Name: "expires_at", err: errors.New(`ent: missing required field "CreditReservation.expires_at"`)}
	}
	if _, ok := crc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CreditReservation.created_at"`)}
	}
	if _, ok := crc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CreditReservation.updated_at"`)}
	}
	return nil
}

func (crc *CreditReservationCreate) sqlSave(ctx context.Context) (*CreditReservation, error) {
	if err := crc.check(); err != nil {
		return nil, err
	}
	_node, _spec := crc.createSpec()
	if err := sqlgraph.CreateNode(ctx, crc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	crc.mutation.id = &_node.ID
	crc.mutation.done = true
	return _node, nil
}

func (crc *CreditReservationCreate) createSpec() (*CreditReservation, *sqlgraph.CreateSpec) {
	var (
		_node = &CreditReservation{config: crc.config}
		_spec = sqlgraph.NewCreateSpec(creditreservation.Table, sqlgraph.NewFieldSpec(creditreservation.FieldID, field.TypeUUID))
	)
	if id, ok := crc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := crc.mutation.UserID(); ok {
		_spec.SetField(creditreservation.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := crc.mutation.Amount(); ok {
		_spec.SetField(creditreservation.FieldAmount, field.TypeInt, value)
		_node.Amount = value
	}
	if value, ok := crc.mutation.Purpose(); ok {
		_spec.SetField(creditreservation.FieldPurpose, field.TypeString, value)
		_node.Purpose = value
	}
	if value, ok := crc.mutation.ReferenceID(); ok {
		_spec.SetField(creditreservation.FieldReferenceID, field.TypeString, value)
		_node.ReferenceID = value
	}
	if value, ok := crc.mutation.ReferenceType(); ok {
		_spec.SetField(creditreservation.FieldReferenceType, field.TypeString, value)
		_node.ReferenceType = value
	}
	if value, ok := crc.mutation.Status(); ok {
		_spec.SetField(creditreservation.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := crc.mutation.ExpiresAt(); ok {
		_spec.SetField(creditreservation.FieldExpiresAt, field.TypeTime, value)
		_node.ExpiresAt = value
	}
	if value, ok := crc.mutation.ConsumedAt(); ok {
		_spec.SetField(creditreservation.FieldConsumedAt, field.TypeTime, value)
		_node.ConsumedAt = value
	}
	if value, ok := crc.mutation.Metadata(); ok {
		_spec.SetField(creditreservation.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := crc.mutation.CreatedAt(); ok {
		_spec.SetField(creditreservation.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := crc.mutation.UpdatedAt(); ok {
		_spec.SetField(creditreservation.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// CreditReservationCreateBulk is the builder for creating many CreditReservation entities in bulk.
type CreditReservationCreateBulk struct {
	config
	err      error
	builders []*CreditReservationCreate
}

// Save creates the CreditReservation entities in the database.
func (crcb *CreditReservationCreateBulk) Save(ctx context.Context) ([]*CreditReservation, error) {
	if crcb.err != nil {
		return nil, crcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(crcb.builders))
	nodes := make([]*CreditReservation, len(crcb.builders))
	mutators := make([]Mutator, len(crcb.builders))
	for i := range crcb.builders {
		func(i int, root context.Context) {
			builder := crcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CreditReservationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, crcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, crcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, crcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (crcb *CreditReservationCreateBulk) SaveX(ctx context.Context) []*CreditReservation {
	v, err := crcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (crcb *CreditReservationCreateBulk) Exec(ctx context.Context) error {
	_, err := crcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (crcb *CreditReservationCreateBulk) ExecX(ctx context.Context) {
	if err := crcb.Exec(ctx); err != nil {
		panic(err)
	}
}

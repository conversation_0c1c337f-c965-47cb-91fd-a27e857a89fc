package events

import (
	"context"
	"fmt"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// <PERSON><PERSON> implements the shared Kafka event handler interface
type Handler struct {
	creditUC credit.UseCase
	logger   logging.Logger
}

// NewHandler creates a new event handler
func NewHandler(creditUC credit.UseCase, logger logging.Logger) *Handler {
	return &Handler{
		creditUC: creditUC,
		logger:   logger,
	}
}

// HandleUserEvent handles user events
func (h *Handler) HandleUserEvent(ctx context.Context, event *sharedKafka.UserEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"event_type": event.Type,
		"user_id":    event.UserID,
		"event_id":   event.ID,
	}).Info("Handling user event")

	switch event.Type {
	case sharedKafka.EventTypeUserRegistered:
		return h.handleUserRegistered(ctx, event)
	case sharedKafka.EventTypeUserUpgraded:
		return h.handleUserUpgraded(ctx, event)
	default:
		h.logger.WithField("event_type", event.Type).Debug("Ignoring unhandled user event")
		return nil
	}
}

// HandleContentEvent handles content events (not used by credit service)
func (h *Handler) HandleContentEvent(ctx context.Context, event *sharedKafka.ContentEvent) error {
	h.logger.WithField("event_type", event.Type).Debug("Ignoring content event")
	return nil
}

// HandleBillingEvent handles billing events
func (h *Handler) HandleBillingEvent(ctx context.Context, event *sharedKafka.BillingEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"event_type": event.Type,
		"user_id":    event.UserID,
		"event_id":   event.ID,
	}).Info("Handling billing event")

	switch event.Type {
	case sharedKafka.EventTypePaymentCompleted:
		return h.handlePaymentCompleted(ctx, event)
	case sharedKafka.EventTypeSubscriptionUpgraded:
		return h.handleSubscriptionUpgraded(ctx, event)
	case sharedKafka.EventTypeSubscriptionExpired:
		return h.handleSubscriptionExpired(ctx, event)
	case "bank.transfer.confirmed": // Custom event type
		return h.handleBankTransferConfirmed(ctx, event)
	default:
		h.logger.WithField("event_type", event.Type).Debug("Ignoring unhandled billing event")
		return nil
	}
}

// HandleIntegrationEvent handles integration events (not used by credit service)
func (h *Handler) HandleIntegrationEvent(ctx context.Context, event *sharedKafka.IntegrationEvent) error {
	h.logger.WithField("event_type", event.Type).Debug("Ignoring integration event")
	return nil
}

// HandleAssetEvent handles asset events (not used by credit service)
func (h *Handler) HandleAssetEvent(ctx context.Context, event *sharedKafka.AssetEvent) error {
	h.logger.WithField("event_type", event.Type).Debug("Ignoring asset event")
	return nil
}

// HandleRAGEvent handles RAG events (not used by credit service)
func (h *Handler) HandleRAGEvent(ctx context.Context, event *sharedKafka.RAGEvent) error {
	h.logger.WithField("event_type", event.Type).Debug("Ignoring RAG event")
	return nil
}

// HandleSystemEvent handles system events (not used by credit service)
func (h *Handler) HandleSystemEvent(ctx context.Context, event *sharedKafka.SystemEvent) error {
	h.logger.WithField("event_type", event.Type).Debug("Ignoring system event")
	return nil
}

// handleUserRegistered processes user registration events to add initial credits
func (h *Handler) handleUserRegistered(ctx context.Context, event *sharedKafka.UserEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"user_id": event.UserID,
		"email":   event.Email,
	}).Info("Processing user registered event")

	// Add initial credits for new user (e.g., 50 credits)
	initialCredits := 50
	req := &models.AddCreditsRequest{
		UserID:        event.UserID,
		Amount:        initialCredits,
		Source:        "user_registration",
		Description:   "Initial welcome credits",
		ReferenceID:   event.ID,
		ReferenceType: "user_registration",
	}
	_, err := h.creditUC.AddCredits(ctx, req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": event.UserID,
			"credits": initialCredits,
		}).Error("Failed to add initial credits for new user")
		return fmt.Errorf("failed to add initial credits: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id": event.UserID,
		"credits": initialCredits,
	}).Info("Successfully added initial credits for new user")

	return nil
}

// handleUserUpgraded processes user upgrade events to update credit limits
func (h *Handler) handleUserUpgraded(ctx context.Context, event *sharedKafka.UserEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"user_id": event.UserID,
		"role":    event.Role,
	}).Info("Processing user upgraded event")

	// Get upgrade bonus credits based on new role
	var bonusCredits int
	switch event.Role {
	case "pro":
		bonusCredits = 100
	case "premium":
		bonusCredits = 500
	case "enterprise":
		bonusCredits = 1000
	default:
		bonusCredits = 50
	}

	if bonusCredits > 0 {
		req := &models.AddCreditsRequest{
			UserID:        event.UserID,
			Amount:        bonusCredits,
			Source:        "user_upgrade",
			Description:   fmt.Sprintf("Upgrade bonus for %s plan", event.Role),
			ReferenceID:   event.ID,
			ReferenceType: "user_upgrade",
		}
		_, err := h.creditUC.AddCredits(ctx, req)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id": event.UserID,
				"credits": bonusCredits,
				"role":    event.Role,
			}).Error("Failed to add upgrade bonus credits")
			return fmt.Errorf("failed to add upgrade bonus credits: %w", err)
		}

		h.logger.WithFields(map[string]interface{}{
			"user_id": event.UserID,
			"credits": bonusCredits,
			"role":    event.Role,
		}).Info("Successfully added upgrade bonus credits")
	}

	return nil
}

// handlePaymentCompleted processes payment completion events
func (h *Handler) handlePaymentCompleted(ctx context.Context, event *sharedKafka.BillingEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"user_id":    event.UserID,
		"payment_id": event.PaymentID,
		"amount":     event.Amount,
		"credits":    event.Credits,
	}).Info("Processing payment completed event")

	// Add credits from payment
	if event.Credits > 0 {
		req := &models.AddCreditsRequest{
			UserID:        event.UserID,
			Amount:        event.Credits,
			Source:        "payment",
			Description:   "Payment completed",
			PaymentID:     event.PaymentID,
			ReferenceType: "payment",
		}
		_, err := h.creditUC.AddCredits(ctx, req)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id":    event.UserID,
				"payment_id": event.PaymentID,
				"credits":    event.Credits,
			}).Error("Failed to add credits from payment")
			return fmt.Errorf("failed to add credits from payment: %w", err)
		}

		h.logger.WithFields(map[string]interface{}{
			"user_id":    event.UserID,
			"payment_id": event.PaymentID,
			"credits":    event.Credits,
		}).Info("Successfully added credits from payment")
	}

	return nil
}

// handleSubscriptionUpgraded processes subscription upgrade events
func (h *Handler) handleSubscriptionUpgraded(ctx context.Context, event *sharedKafka.BillingEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_name":       event.PlanName,
		"credits":         event.Credits,
	}).Info("Processing subscription upgraded event")

	// Add credits from subscription upgrade
	if event.Credits > 0 {
		req := &models.AddCreditsRequest{
			UserID:        event.UserID,
			Amount:        event.Credits,
			Source:        "subscription_upgrade",
			Description:   fmt.Sprintf("Subscription upgrade to %s", event.PlanName),
			ReferenceID:   event.SubscriptionID,
			ReferenceType: "subscription",
		}
		_, err := h.creditUC.AddCredits(ctx, req)
		if err != nil {
			h.logger.WithError(err).WithFields(map[string]interface{}{
				"user_id":         event.UserID,
				"subscription_id": event.SubscriptionID,
				"credits":         event.Credits,
			}).Error("Failed to add credits from subscription upgrade")
			return fmt.Errorf("failed to add credits from subscription upgrade: %w", err)
		}

		h.logger.WithFields(map[string]interface{}{
			"user_id":         event.UserID,
			"subscription_id": event.SubscriptionID,
			"credits":         event.Credits,
		}).Info("Successfully added credits from subscription upgrade")
	}

	return nil
}

// handleSubscriptionExpired processes subscription expiration events
func (h *Handler) handleSubscriptionExpired(ctx context.Context, event *sharedKafka.BillingEvent) error {
	h.logger.WithFields(map[string]interface{}{
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"plan_name":       event.PlanName,
	}).Info("Processing subscription expired event")

	// TODO: Implement credit limit enforcement
	// This could involve:
	// 1. Setting user credit limits based on free plan
	// 2. Preventing further credit consumption beyond free tier
	// 3. Sending low credit warnings

	h.logger.WithFields(map[string]interface{}{
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
	}).Info("Subscription expired - credit limits should be enforced")

	return nil
}

// handleBankTransferConfirmed processes bank transfer confirmation events
func (h *Handler) handleBankTransferConfirmed(ctx context.Context, event *sharedKafka.BillingEvent) error {
	// Extract bank transfer specific data from event.Data
	creditsToAdd, ok := event.Data["credits_to_add"].(float64)
	if !ok {
		return fmt.Errorf("missing or invalid credits_to_add in bank transfer event")
	}

	referenceCode, _ := event.Data["reference_code"].(string)

	h.logger.WithFields(map[string]interface{}{
		"user_id":        event.UserID,
		"credits":        creditsToAdd,
		"reference_code": referenceCode,
	}).Info("Processing bank transfer confirmed event")

	req := &models.AddCreditsRequest{
		UserID:        event.UserID,
		Amount:        int(creditsToAdd),
		Source:        "bank_transfer",
		Description:   "Bank transfer payment",
		ReferenceID:   referenceCode,
		ReferenceType: "bank_transfer",
	}
	_, err := h.creditUC.AddCredits(ctx, req)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": event.UserID,
			"credits": creditsToAdd,
		}).Error("Failed to add credits from bank transfer")
		return fmt.Errorf("failed to add credits from bank transfer: %w", err)
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id": event.UserID,
		"credits": creditsToAdd,
	}).Info("Successfully added credits from bank transfer")

	return nil
}

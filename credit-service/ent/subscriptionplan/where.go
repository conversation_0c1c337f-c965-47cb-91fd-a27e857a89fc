// Code generated by ent, DO NOT EDIT.

package subscriptionplan

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldName, v))
}

// DisplayName applies equality check predicate on the "display_name" field. It's identical to DisplayNameEQ.
func DisplayName(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldDisplayName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldDescription, v))
}

// Price applies equality check predicate on the "price" field. It's identical to PriceEQ.
func Price(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldPrice, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCurrency, v))
}

// CreditsIncluded applies equality check predicate on the "credits_included" field. It's identical to CreditsIncludedEQ.
func CreditsIncluded(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCreditsIncluded, v))
}

// MonthlyCredits applies equality check predicate on the "monthly_credits" field. It's identical to MonthlyCreditsEQ.
func MonthlyCredits(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMonthlyCredits, v))
}

// IsUnlimited applies equality check predicate on the "is_unlimited" field. It's identical to IsUnlimitedEQ.
func IsUnlimited(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsUnlimited, v))
}

// MaxUsers applies equality check predicate on the "max_users" field. It's identical to MaxUsersEQ.
func MaxUsers(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMaxUsers, v))
}

// MaxWorkspaces applies equality check predicate on the "max_workspaces" field. It's identical to MaxWorkspacesEQ.
func MaxWorkspaces(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMaxWorkspaces, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsActive, v))
}

// IsFeatured applies equality check predicate on the "is_featured" field. It's identical to IsFeaturedEQ.
func IsFeatured(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsFeatured, v))
}

// SortOrder applies equality check predicate on the "sort_order" field. It's identical to SortOrderEQ.
func SortOrder(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldSortOrder, v))
}

// StripePriceID applies equality check predicate on the "stripe_price_id" field. It's identical to StripePriceIDEQ.
func StripePriceID(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldStripePriceID, v))
}

// StripeProductID applies equality check predicate on the "stripe_product_id" field. It's identical to StripeProductIDEQ.
func StripeProductID(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldStripeProductID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldUpdatedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldName, v))
}

// DisplayNameEQ applies the EQ predicate on the "display_name" field.
func DisplayNameEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldDisplayName, v))
}

// DisplayNameNEQ applies the NEQ predicate on the "display_name" field.
func DisplayNameNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldDisplayName, v))
}

// DisplayNameIn applies the In predicate on the "display_name" field.
func DisplayNameIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldDisplayName, vs...))
}

// DisplayNameNotIn applies the NotIn predicate on the "display_name" field.
func DisplayNameNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldDisplayName, vs...))
}

// DisplayNameGT applies the GT predicate on the "display_name" field.
func DisplayNameGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldDisplayName, v))
}

// DisplayNameGTE applies the GTE predicate on the "display_name" field.
func DisplayNameGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldDisplayName, v))
}

// DisplayNameLT applies the LT predicate on the "display_name" field.
func DisplayNameLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldDisplayName, v))
}

// DisplayNameLTE applies the LTE predicate on the "display_name" field.
func DisplayNameLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldDisplayName, v))
}

// DisplayNameContains applies the Contains predicate on the "display_name" field.
func DisplayNameContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldDisplayName, v))
}

// DisplayNameHasPrefix applies the HasPrefix predicate on the "display_name" field.
func DisplayNameHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldDisplayName, v))
}

// DisplayNameHasSuffix applies the HasSuffix predicate on the "display_name" field.
func DisplayNameHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldDisplayName, v))
}

// DisplayNameEqualFold applies the EqualFold predicate on the "display_name" field.
func DisplayNameEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldDisplayName, v))
}

// DisplayNameContainsFold applies the ContainsFold predicate on the "display_name" field.
func DisplayNameContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldDisplayName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldDescription, v))
}

// PriceEQ applies the EQ predicate on the "price" field.
func PriceEQ(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldPrice, v))
}

// PriceNEQ applies the NEQ predicate on the "price" field.
func PriceNEQ(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldPrice, v))
}

// PriceIn applies the In predicate on the "price" field.
func PriceIn(vs ...int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldPrice, vs...))
}

// PriceNotIn applies the NotIn predicate on the "price" field.
func PriceNotIn(vs ...int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldPrice, vs...))
}

// PriceGT applies the GT predicate on the "price" field.
func PriceGT(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldPrice, v))
}

// PriceGTE applies the GTE predicate on the "price" field.
func PriceGTE(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldPrice, v))
}

// PriceLT applies the LT predicate on the "price" field.
func PriceLT(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldPrice, v))
}

// PriceLTE applies the LTE predicate on the "price" field.
func PriceLTE(v int64) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldPrice, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldCurrency, v))
}

// BillingIntervalEQ applies the EQ predicate on the "billing_interval" field.
func BillingIntervalEQ(v BillingInterval) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldBillingInterval, v))
}

// BillingIntervalNEQ applies the NEQ predicate on the "billing_interval" field.
func BillingIntervalNEQ(v BillingInterval) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldBillingInterval, v))
}

// BillingIntervalIn applies the In predicate on the "billing_interval" field.
func BillingIntervalIn(vs ...BillingInterval) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldBillingInterval, vs...))
}

// BillingIntervalNotIn applies the NotIn predicate on the "billing_interval" field.
func BillingIntervalNotIn(vs ...BillingInterval) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldBillingInterval, vs...))
}

// CreditsIncludedEQ applies the EQ predicate on the "credits_included" field.
func CreditsIncludedEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCreditsIncluded, v))
}

// CreditsIncludedNEQ applies the NEQ predicate on the "credits_included" field.
func CreditsIncludedNEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldCreditsIncluded, v))
}

// CreditsIncludedIn applies the In predicate on the "credits_included" field.
func CreditsIncludedIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldCreditsIncluded, vs...))
}

// CreditsIncludedNotIn applies the NotIn predicate on the "credits_included" field.
func CreditsIncludedNotIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldCreditsIncluded, vs...))
}

// CreditsIncludedGT applies the GT predicate on the "credits_included" field.
func CreditsIncludedGT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldCreditsIncluded, v))
}

// CreditsIncludedGTE applies the GTE predicate on the "credits_included" field.
func CreditsIncludedGTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldCreditsIncluded, v))
}

// CreditsIncludedLT applies the LT predicate on the "credits_included" field.
func CreditsIncludedLT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldCreditsIncluded, v))
}

// CreditsIncludedLTE applies the LTE predicate on the "credits_included" field.
func CreditsIncludedLTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldCreditsIncluded, v))
}

// MonthlyCreditsEQ applies the EQ predicate on the "monthly_credits" field.
func MonthlyCreditsEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMonthlyCredits, v))
}

// MonthlyCreditsNEQ applies the NEQ predicate on the "monthly_credits" field.
func MonthlyCreditsNEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldMonthlyCredits, v))
}

// MonthlyCreditsIn applies the In predicate on the "monthly_credits" field.
func MonthlyCreditsIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldMonthlyCredits, vs...))
}

// MonthlyCreditsNotIn applies the NotIn predicate on the "monthly_credits" field.
func MonthlyCreditsNotIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldMonthlyCredits, vs...))
}

// MonthlyCreditsGT applies the GT predicate on the "monthly_credits" field.
func MonthlyCreditsGT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldMonthlyCredits, v))
}

// MonthlyCreditsGTE applies the GTE predicate on the "monthly_credits" field.
func MonthlyCreditsGTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldMonthlyCredits, v))
}

// MonthlyCreditsLT applies the LT predicate on the "monthly_credits" field.
func MonthlyCreditsLT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldMonthlyCredits, v))
}

// MonthlyCreditsLTE applies the LTE predicate on the "monthly_credits" field.
func MonthlyCreditsLTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldMonthlyCredits, v))
}

// IsUnlimitedEQ applies the EQ predicate on the "is_unlimited" field.
func IsUnlimitedEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsUnlimited, v))
}

// IsUnlimitedNEQ applies the NEQ predicate on the "is_unlimited" field.
func IsUnlimitedNEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldIsUnlimited, v))
}

// FeaturesIsNil applies the IsNil predicate on the "features" field.
func FeaturesIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldFeatures))
}

// FeaturesNotNil applies the NotNil predicate on the "features" field.
func FeaturesNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldFeatures))
}

// LimitsIsNil applies the IsNil predicate on the "limits" field.
func LimitsIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldLimits))
}

// LimitsNotNil applies the NotNil predicate on the "limits" field.
func LimitsNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldLimits))
}

// MaxUsersEQ applies the EQ predicate on the "max_users" field.
func MaxUsersEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMaxUsers, v))
}

// MaxUsersNEQ applies the NEQ predicate on the "max_users" field.
func MaxUsersNEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldMaxUsers, v))
}

// MaxUsersIn applies the In predicate on the "max_users" field.
func MaxUsersIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldMaxUsers, vs...))
}

// MaxUsersNotIn applies the NotIn predicate on the "max_users" field.
func MaxUsersNotIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldMaxUsers, vs...))
}

// MaxUsersGT applies the GT predicate on the "max_users" field.
func MaxUsersGT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldMaxUsers, v))
}

// MaxUsersGTE applies the GTE predicate on the "max_users" field.
func MaxUsersGTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldMaxUsers, v))
}

// MaxUsersLT applies the LT predicate on the "max_users" field.
func MaxUsersLT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldMaxUsers, v))
}

// MaxUsersLTE applies the LTE predicate on the "max_users" field.
func MaxUsersLTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldMaxUsers, v))
}

// MaxWorkspacesEQ applies the EQ predicate on the "max_workspaces" field.
func MaxWorkspacesEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldMaxWorkspaces, v))
}

// MaxWorkspacesNEQ applies the NEQ predicate on the "max_workspaces" field.
func MaxWorkspacesNEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldMaxWorkspaces, v))
}

// MaxWorkspacesIn applies the In predicate on the "max_workspaces" field.
func MaxWorkspacesIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldMaxWorkspaces, vs...))
}

// MaxWorkspacesNotIn applies the NotIn predicate on the "max_workspaces" field.
func MaxWorkspacesNotIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldMaxWorkspaces, vs...))
}

// MaxWorkspacesGT applies the GT predicate on the "max_workspaces" field.
func MaxWorkspacesGT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldMaxWorkspaces, v))
}

// MaxWorkspacesGTE applies the GTE predicate on the "max_workspaces" field.
func MaxWorkspacesGTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldMaxWorkspaces, v))
}

// MaxWorkspacesLT applies the LT predicate on the "max_workspaces" field.
func MaxWorkspacesLT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldMaxWorkspaces, v))
}

// MaxWorkspacesLTE applies the LTE predicate on the "max_workspaces" field.
func MaxWorkspacesLTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldMaxWorkspaces, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldIsActive, v))
}

// IsFeaturedEQ applies the EQ predicate on the "is_featured" field.
func IsFeaturedEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldIsFeatured, v))
}

// IsFeaturedNEQ applies the NEQ predicate on the "is_featured" field.
func IsFeaturedNEQ(v bool) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldIsFeatured, v))
}

// SortOrderEQ applies the EQ predicate on the "sort_order" field.
func SortOrderEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldSortOrder, v))
}

// SortOrderNEQ applies the NEQ predicate on the "sort_order" field.
func SortOrderNEQ(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldSortOrder, v))
}

// SortOrderIn applies the In predicate on the "sort_order" field.
func SortOrderIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldSortOrder, vs...))
}

// SortOrderNotIn applies the NotIn predicate on the "sort_order" field.
func SortOrderNotIn(vs ...int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldSortOrder, vs...))
}

// SortOrderGT applies the GT predicate on the "sort_order" field.
func SortOrderGT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldSortOrder, v))
}

// SortOrderGTE applies the GTE predicate on the "sort_order" field.
func SortOrderGTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldSortOrder, v))
}

// SortOrderLT applies the LT predicate on the "sort_order" field.
func SortOrderLT(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldSortOrder, v))
}

// SortOrderLTE applies the LTE predicate on the "sort_order" field.
func SortOrderLTE(v int) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldSortOrder, v))
}

// StripePriceIDEQ applies the EQ predicate on the "stripe_price_id" field.
func StripePriceIDEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldStripePriceID, v))
}

// StripePriceIDNEQ applies the NEQ predicate on the "stripe_price_id" field.
func StripePriceIDNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldStripePriceID, v))
}

// StripePriceIDIn applies the In predicate on the "stripe_price_id" field.
func StripePriceIDIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldStripePriceID, vs...))
}

// StripePriceIDNotIn applies the NotIn predicate on the "stripe_price_id" field.
func StripePriceIDNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldStripePriceID, vs...))
}

// StripePriceIDGT applies the GT predicate on the "stripe_price_id" field.
func StripePriceIDGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldStripePriceID, v))
}

// StripePriceIDGTE applies the GTE predicate on the "stripe_price_id" field.
func StripePriceIDGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldStripePriceID, v))
}

// StripePriceIDLT applies the LT predicate on the "stripe_price_id" field.
func StripePriceIDLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldStripePriceID, v))
}

// StripePriceIDLTE applies the LTE predicate on the "stripe_price_id" field.
func StripePriceIDLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldStripePriceID, v))
}

// StripePriceIDContains applies the Contains predicate on the "stripe_price_id" field.
func StripePriceIDContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldStripePriceID, v))
}

// StripePriceIDHasPrefix applies the HasPrefix predicate on the "stripe_price_id" field.
func StripePriceIDHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldStripePriceID, v))
}

// StripePriceIDHasSuffix applies the HasSuffix predicate on the "stripe_price_id" field.
func StripePriceIDHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldStripePriceID, v))
}

// StripePriceIDIsNil applies the IsNil predicate on the "stripe_price_id" field.
func StripePriceIDIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldStripePriceID))
}

// StripePriceIDNotNil applies the NotNil predicate on the "stripe_price_id" field.
func StripePriceIDNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldStripePriceID))
}

// StripePriceIDEqualFold applies the EqualFold predicate on the "stripe_price_id" field.
func StripePriceIDEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldStripePriceID, v))
}

// StripePriceIDContainsFold applies the ContainsFold predicate on the "stripe_price_id" field.
func StripePriceIDContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldStripePriceID, v))
}

// StripeProductIDEQ applies the EQ predicate on the "stripe_product_id" field.
func StripeProductIDEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldStripeProductID, v))
}

// StripeProductIDNEQ applies the NEQ predicate on the "stripe_product_id" field.
func StripeProductIDNEQ(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldStripeProductID, v))
}

// StripeProductIDIn applies the In predicate on the "stripe_product_id" field.
func StripeProductIDIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldStripeProductID, vs...))
}

// StripeProductIDNotIn applies the NotIn predicate on the "stripe_product_id" field.
func StripeProductIDNotIn(vs ...string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldStripeProductID, vs...))
}

// StripeProductIDGT applies the GT predicate on the "stripe_product_id" field.
func StripeProductIDGT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldStripeProductID, v))
}

// StripeProductIDGTE applies the GTE predicate on the "stripe_product_id" field.
func StripeProductIDGTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldStripeProductID, v))
}

// StripeProductIDLT applies the LT predicate on the "stripe_product_id" field.
func StripeProductIDLT(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldStripeProductID, v))
}

// StripeProductIDLTE applies the LTE predicate on the "stripe_product_id" field.
func StripeProductIDLTE(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldStripeProductID, v))
}

// StripeProductIDContains applies the Contains predicate on the "stripe_product_id" field.
func StripeProductIDContains(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContains(FieldStripeProductID, v))
}

// StripeProductIDHasPrefix applies the HasPrefix predicate on the "stripe_product_id" field.
func StripeProductIDHasPrefix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasPrefix(FieldStripeProductID, v))
}

// StripeProductIDHasSuffix applies the HasSuffix predicate on the "stripe_product_id" field.
func StripeProductIDHasSuffix(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldHasSuffix(FieldStripeProductID, v))
}

// StripeProductIDIsNil applies the IsNil predicate on the "stripe_product_id" field.
func StripeProductIDIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldStripeProductID))
}

// StripeProductIDNotNil applies the NotNil predicate on the "stripe_product_id" field.
func StripeProductIDNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldStripeProductID))
}

// StripeProductIDEqualFold applies the EqualFold predicate on the "stripe_product_id" field.
func StripeProductIDEqualFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEqualFold(FieldStripeProductID, v))
}

// StripeProductIDContainsFold applies the ContainsFold predicate on the "stripe_product_id" field.
func StripeProductIDContainsFold(v string) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldContainsFold(FieldStripeProductID, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SubscriptionPlan) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SubscriptionPlan) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SubscriptionPlan) predicate.SubscriptionPlan {
	return predicate.SubscriptionPlan(sql.NotPredicates(p))
}

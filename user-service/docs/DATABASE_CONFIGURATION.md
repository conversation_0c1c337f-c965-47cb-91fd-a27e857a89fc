# Database Configuration Guide

User Service hỗ trợ cả PostgreSQL và SQLite với cấu hình linh hoạt cho các môi trường khác nhau.

## Cấu hình Database

### 1. PostgreSQL (Khuyến nghị cho Production)

PostgreSQL là lựa chọn tốt nhất cho môi trường production với hiệu suất cao và tính năng đầy đủ.

#### Cấu hình trong config.yaml:

```yaml
database:
  type: "postgres"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "user_service_db"
  ssl_mode: "disable"  # "require" cho production
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"
```

#### Cài đặt PostgreSQL:

**macOS:**
```bash
brew install postgresql
brew services start postgresql
createdb user_service_db
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo -u postgres createdb user_service_db
```

**Docker:**
```bash
docker run --name postgres-user-service \
  -e POSTGRES_DB=user_service_db \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  -d postgres:15
```

### 2. SQLite (Khuyến nghị cho Development)

SQLite là lựa chọn tuyệt vời cho development vì không cần cài đặt server riêng.

#### Cấu hình trong config.yaml:

```yaml
database:
  type: "sqlite"
  sqlite_path: "./data/user_service.db"
  # Connection pool settings vẫn áp dụng
  max_open_conns: 10
  max_idle_conns: 2
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"
```

#### Ưu điểm SQLite:
- Không cần cài đặt database server
- File database duy nhất, dễ backup
- Hoàn hảo cho development và testing
- Hiệu suất tốt cho ứng dụng nhỏ

## Cấu hình theo Environment

### Development (development.yaml)

```yaml
database:
  type: "sqlite"
  sqlite_path: "./data/user_service_dev.db"
  
  # PostgreSQL fallback nếu cần
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "user_service_dev"
  ssl_mode: "disable"
```

### Production (production.yaml)

```yaml
database:
  type: "postgres"
  host: "${DB_HOST}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  name: "${DB_NAME}"
  ssl_mode: "require"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"
```

## Environment Variables

Bạn có thể override cấu hình database bằng environment variables:

```bash
# Database type
export DATABASE_TYPE=postgres

# PostgreSQL
export DATABASE_HOST=localhost
export DATABASE_PORT=5432
export DATABASE_USER=postgres
export DATABASE_PASSWORD=postgres
export DATABASE_NAME=user_service_db
export DATABASE_SSL_MODE=disable

# SQLite
export DATABASE_SQLITE_PATH=./data/user_service.db

# Connection pool
export DATABASE_MAX_OPEN_CONNS=25
export DATABASE_MAX_IDLE_CONNS=5
export DATABASE_CONN_MAX_LIFETIME=5m
export DATABASE_CONN_MAX_IDLE_TIME=5m
```

## Migration

Service sẽ tự động chạy migration khi khởi động:

```go
// Database migrations sẽ tự động tạo tables
if err := client.Schema.Create(ctx); err != nil {
    return fmt.Errorf("failed to create schema: %w", err)
}
```

## Chuyển đổi giữa Database Types

### Từ SQLite sang PostgreSQL:

1. Export data từ SQLite:
```bash
sqlite3 ./data/user_service.db .dump > backup.sql
```

2. Cập nhật config để sử dụng PostgreSQL
3. Import data vào PostgreSQL (cần chỉnh sửa SQL syntax)

### Từ PostgreSQL sang SQLite:

1. Export data từ PostgreSQL:
```bash
pg_dump user_service_db > backup.sql
```

2. Cập nhật config để sử dụng SQLite
3. Import data vào SQLite (cần chỉnh sửa SQL syntax)

## Performance Tuning

### PostgreSQL:
- Tăng `max_open_conns` cho high-traffic
- Sử dụng connection pooling
- Enable SSL trong production
- Cấu hình PostgreSQL parameters

### SQLite:
- Sử dụng WAL mode cho concurrent access
- Giới hạn `max_open_conns` = 1 cho write-heavy workloads
- Backup thường xuyên

## Troubleshooting

### SQLite Issues:
- **Database locked**: Đảm bảo không có process khác đang sử dụng
- **Permission denied**: Kiểm tra quyền write vào thư mục data
- **File not found**: Service sẽ tự tạo file nếu thư mục tồn tại

### PostgreSQL Issues:
- **Connection refused**: Kiểm tra PostgreSQL service đang chạy
- **Authentication failed**: Kiểm tra username/password
- **Database not found**: Tạo database trước khi chạy service

## Best Practices

1. **Development**: Sử dụng SQLite cho setup nhanh
2. **Testing**: SQLite với in-memory database (`:memory:`)
3. **Staging**: PostgreSQL giống production
4. **Production**: PostgreSQL với SSL và backup strategy

## Backup Strategy

### SQLite:
```bash
# Simple file copy
cp ./data/user_service.db ./backups/user_service_$(date +%Y%m%d_%H%M%S).db

# Using SQLite backup command
sqlite3 ./data/user_service.db ".backup ./backups/backup.db"
```

### PostgreSQL:
```bash
# Full backup
pg_dump user_service_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Compressed backup
pg_dump user_service_db | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz
```

## Monitoring

Service cung cấp health check endpoint để monitor database connection:

```bash
curl http://localhost:8080/health
```

Response sẽ bao gồm database status và connection pool metrics.

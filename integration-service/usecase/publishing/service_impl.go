package publishing

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
	"github.com/social-content-ai/integration-service/pkg/publishers"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service implements publishing business logic
type Service struct {
	reader     Reader
	writer     Writer
	logger     logging.Logger
	publishers map[string]publishers.Publisher
}

// NewService creates a new publishing service
func NewService(reader Reader, writer Writer, logger logging.Logger) UseCase {
	return &Service{
		reader:     reader,
		writer:     writer,
		logger:     logger,
		publishers: make(map[string]publishers.Publisher),
	}
}

// RegisterPublisher registers a publisher for a platform
func (s *Service) RegisterPublisher(platform string, publisher publishers.Publisher) {
	s.publishers[platform] = publisher
	s.logger.WithField("platform", platform).Info("Publisher registered")
}

// PublishPost publishes content to a platform
func (s *Service) PublishPost(ctx context.Context, req *PublishPostRequest) (*PublishPostResponse, error) {
	s.logger.WithField("account_id", req.AccountID).Info("Publishing post")

	// Get platform integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return &PublishPostResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to get platform integration: %v", err),
		}, nil
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return &PublishPostResponse{
			Success:      false,
			ErrorMessage: "Access denied: integration does not belong to user",
		}, nil
	}

	// Get publisher for platform
	publisher, exists := s.publishers[string(integration.Platform)]
	if !exists {
		return &PublishPostResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Publisher not found for platform: %s", integration.Platform),
		}, nil
	}

	// Create published post record
	publishedPost := &ent.PublishedPost{
		ID:             uuid.New(),
		UserID:         integration.UserID,
		PostID:         req.PostID,
		IntegrationID:  integration.ID.String(),
		Platform:       publishedpost.Platform(integration.Platform),
		Content:        req.Content,
		MediaUrls:      req.ImageURLs,
		Hashtags:       req.Hashtags,
		Status:         publishedpost.StatusPending,
		PublishOptions: req.PlatformOptions,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Handle scheduled publishing
	if req.ScheduledAt > 0 {
		scheduledTime := time.Unix(req.ScheduledAt, 0)
		publishedPost.ScheduledAt = scheduledTime
		publishedPost.Status = publishedpost.StatusScheduled
	}

	// Save to database
	createdPost, err := s.writer.CreatePublishedPost(ctx, publishedPost)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create published post record")
		return &PublishPostResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create published post record: %v", err),
		}, nil
	}

	// If not scheduled, publish immediately
	if req.ScheduledAt == 0 {
		return s.publishToPlatform(ctx, integration, createdPost, publisher)
	}

	// For scheduled posts, return success
	return &PublishPostResponse{
		Success:     true,
		PublishedAt: req.ScheduledAt,
	}, nil
}

// publishToPlatform handles the actual publishing to platform
func (s *Service) publishToPlatform(ctx context.Context, integration *ent.PlatformIntegration, post *ent.PublishedPost, publisher publishers.Publisher) (*PublishPostResponse, error) {
	// TODO: Implement platform-specific publishing logic
	// This is a placeholder implementation

	s.logger.WithFields(map[string]interface{}{
		"platform": integration.Platform,
		"post_id":  post.ID,
	}).Info("Publishing to platform")

	// Simulate publishing (replace with actual publisher calls)
	platformPostID := fmt.Sprintf("%s_%s", integration.Platform, post.ID.String())
	platformURL := fmt.Sprintf("https://%s.com/post/%s", integration.Platform, platformPostID)

	// Update post status
	updates := map[string]interface{}{
		"platform_post_id":  platformPostID,
		"platform_post_url": platformURL,
		"status":            "published",
		"published_at":      time.Now(),
		"updated_at":        time.Now(),
	}

	_, err := s.writer.UpdatePublishedPost(ctx, post.ID.String(), updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update published post")
		return &PublishPostResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to update published post: %v", err),
		}, nil
	}

	return &PublishPostResponse{
		PlatformPostID: platformPostID,
		PlatformURL:    platformURL,
		Success:        true,
		PublishedAt:    time.Now().Unix(),
	}, nil
}

// GetPostAnalytics retrieves analytics for a published post
func (s *Service) GetPostAnalytics(ctx context.Context, req *GetPostAnalyticsRequest) (*GetPostAnalyticsResponse, error) {
	s.logger.WithField("platform_post_id", req.PlatformPostID).Info("Getting post analytics")

	// Get platform integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return nil, fmt.Errorf("failed to get platform integration: %w", err)
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return nil, fmt.Errorf("access denied: integration does not belong to user")
	}

	// TODO: Implement platform-specific analytics retrieval
	// For now, return mock data
	metrics := map[string]int64{
		"views":      1000,
		"likes":      50,
		"comments":   10,
		"shares":     5,
		"engagement": 65,
	}

	return &GetPostAnalyticsResponse{
		PlatformPostID:  req.PlatformPostID,
		Platform:        string(integration.Platform),
		Metrics:         metrics,
		CollectedAt:     time.Now().Unix(),
		PostPublishedAt: time.Now().Add(-24 * time.Hour).Unix(),
	}, nil
}

// GetAccountAnalytics retrieves analytics for an account
func (s *Service) GetAccountAnalytics(ctx context.Context, req *GetAccountAnalyticsRequest) (*GetAccountAnalyticsResponse, error) {
	s.logger.WithField("account_id", req.AccountID).Info("Getting account analytics")

	// Get platform integration
	integration, err := s.reader.GetPlatformIntegration(ctx, req.AccountID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform integration")
		return nil, fmt.Errorf("failed to get platform integration: %w", err)
	}

	// Verify user ownership
	if integration.UserID.String() != req.UserID {
		return nil, fmt.Errorf("access denied: integration does not belong to user")
	}

	// TODO: Implement platform-specific account analytics retrieval
	// For now, return mock data
	metrics := map[string]int64{
		"followers":   5000,
		"following":   1000,
		"posts":       100,
		"engagement":  750,
		"reach":       10000,
		"impressions": 15000,
	}

	timeSeries := map[string][]int64{
		"daily_followers":  {100, 105, 110, 115, 120, 125, 130},
		"daily_posts":      {1, 2, 1, 3, 2, 1, 2},
		"daily_engagement": {50, 60, 45, 80, 70, 55, 65},
	}

	return &GetAccountAnalyticsResponse{
		AccountID:   req.AccountID,
		Platform:    string(integration.Platform),
		Metrics:     metrics,
		TimeSeries:  timeSeries,
		CollectedAt: time.Now().Unix(),
	}, nil
}

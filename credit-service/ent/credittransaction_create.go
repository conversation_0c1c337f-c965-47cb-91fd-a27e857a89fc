// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
)

// CreditTransactionCreate is the builder for creating a CreditTransaction entity.
type CreditTransactionCreate struct {
	config
	mutation *CreditTransactionMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (ctc *CreditTransactionCreate) SetUserID(u uuid.UUID) *CreditTransactionCreate {
	ctc.mutation.SetUserID(u)
	return ctc
}

// SetType sets the "type" field.
func (ctc *CreditTransactionCreate) SetType(c credittransaction.Type) *CreditTransactionCreate {
	ctc.mutation.SetType(c)
	return ctc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableType(c *credittransaction.Type) *CreditTransactionCreate {
	if c != nil {
		ctc.SetType(*c)
	}
	return ctc
}

// SetAmount sets the "amount" field.
func (ctc *CreditTransactionCreate) SetAmount(i int) *CreditTransactionCreate {
	ctc.mutation.SetAmount(i)
	return ctc
}

// SetDescription sets the "description" field.
func (ctc *CreditTransactionCreate) SetDescription(s string) *CreditTransactionCreate {
	ctc.mutation.SetDescription(s)
	return ctc
}

// SetReferenceID sets the "reference_id" field.
func (ctc *CreditTransactionCreate) SetReferenceID(s string) *CreditTransactionCreate {
	ctc.mutation.SetReferenceID(s)
	return ctc
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableReferenceID(s *string) *CreditTransactionCreate {
	if s != nil {
		ctc.SetReferenceID(*s)
	}
	return ctc
}

// SetReferenceType sets the "reference_type" field.
func (ctc *CreditTransactionCreate) SetReferenceType(s string) *CreditTransactionCreate {
	ctc.mutation.SetReferenceType(s)
	return ctc
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableReferenceType(s *string) *CreditTransactionCreate {
	if s != nil {
		ctc.SetReferenceType(*s)
	}
	return ctc
}

// SetPaymentID sets the "payment_id" field.
func (ctc *CreditTransactionCreate) SetPaymentID(s string) *CreditTransactionCreate {
	ctc.mutation.SetPaymentID(s)
	return ctc
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillablePaymentID(s *string) *CreditTransactionCreate {
	if s != nil {
		ctc.SetPaymentID(*s)
	}
	return ctc
}

// SetInvoiceID sets the "invoice_id" field.
func (ctc *CreditTransactionCreate) SetInvoiceID(s string) *CreditTransactionCreate {
	ctc.mutation.SetInvoiceID(s)
	return ctc
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableInvoiceID(s *string) *CreditTransactionCreate {
	if s != nil {
		ctc.SetInvoiceID(*s)
	}
	return ctc
}

// SetStatus sets the "status" field.
func (ctc *CreditTransactionCreate) SetStatus(c credittransaction.Status) *CreditTransactionCreate {
	ctc.mutation.SetStatus(c)
	return ctc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableStatus(c *credittransaction.Status) *CreditTransactionCreate {
	if c != nil {
		ctc.SetStatus(*c)
	}
	return ctc
}

// SetMetadata sets the "metadata" field.
func (ctc *CreditTransactionCreate) SetMetadata(m map[string]interface{}) *CreditTransactionCreate {
	ctc.mutation.SetMetadata(m)
	return ctc
}

// SetProcessedAt sets the "processed_at" field.
func (ctc *CreditTransactionCreate) SetProcessedAt(t time.Time) *CreditTransactionCreate {
	ctc.mutation.SetProcessedAt(t)
	return ctc
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableProcessedAt(t *time.Time) *CreditTransactionCreate {
	if t != nil {
		ctc.SetProcessedAt(*t)
	}
	return ctc
}

// SetCreatedAt sets the "created_at" field.
func (ctc *CreditTransactionCreate) SetCreatedAt(t time.Time) *CreditTransactionCreate {
	ctc.mutation.SetCreatedAt(t)
	return ctc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableCreatedAt(t *time.Time) *CreditTransactionCreate {
	if t != nil {
		ctc.SetCreatedAt(*t)
	}
	return ctc
}

// SetUpdatedAt sets the "updated_at" field.
func (ctc *CreditTransactionCreate) SetUpdatedAt(t time.Time) *CreditTransactionCreate {
	ctc.mutation.SetUpdatedAt(t)
	return ctc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableUpdatedAt(t *time.Time) *CreditTransactionCreate {
	if t != nil {
		ctc.SetUpdatedAt(*t)
	}
	return ctc
}

// SetID sets the "id" field.
func (ctc *CreditTransactionCreate) SetID(u uuid.UUID) *CreditTransactionCreate {
	ctc.mutation.SetID(u)
	return ctc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ctc *CreditTransactionCreate) SetNillableID(u *uuid.UUID) *CreditTransactionCreate {
	if u != nil {
		ctc.SetID(*u)
	}
	return ctc
}

// Mutation returns the CreditTransactionMutation object of the builder.
func (ctc *CreditTransactionCreate) Mutation() *CreditTransactionMutation {
	return ctc.mutation
}

// Save creates the CreditTransaction in the database.
func (ctc *CreditTransactionCreate) Save(ctx context.Context) (*CreditTransaction, error) {
	ctc.defaults()
	return withHooks(ctx, ctc.sqlSave, ctc.mutation, ctc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ctc *CreditTransactionCreate) SaveX(ctx context.Context) *CreditTransaction {
	v, err := ctc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ctc *CreditTransactionCreate) Exec(ctx context.Context) error {
	_, err := ctc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ctc *CreditTransactionCreate) ExecX(ctx context.Context) {
	if err := ctc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ctc *CreditTransactionCreate) defaults() {
	if _, ok := ctc.mutation.GetType(); !ok {
		v := credittransaction.DefaultType
		ctc.mutation.SetType(v)
	}
	if _, ok := ctc.mutation.Status(); !ok {
		v := credittransaction.DefaultStatus
		ctc.mutation.SetStatus(v)
	}
	if _, ok := ctc.mutation.CreatedAt(); !ok {
		v := credittransaction.DefaultCreatedAt()
		ctc.mutation.SetCreatedAt(v)
	}
	if _, ok := ctc.mutation.UpdatedAt(); !ok {
		v := credittransaction.DefaultUpdatedAt()
		ctc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ctc.mutation.ID(); !ok {
		v := credittransaction.DefaultID()
		ctc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ctc *CreditTransactionCreate) check() error {
	if _, ok := ctc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "CreditTransaction.user_id"`)}
	}
	if _, ok := ctc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "CreditTransaction.type"`)}
	}
	if v, ok := ctc.mutation.GetType(); ok {
		if err := credittransaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.type": %w`, err)}
		}
	}
	if _, ok := ctc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "CreditTransaction.amount"`)}
	}
	if _, ok := ctc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "CreditTransaction.description"`)}
	}
	if v, ok := ctc.mutation.Description(); ok {
		if err := credittransaction.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.description": %w`, err)}
		}
	}
	if v, ok := ctc.mutation.ReferenceID(); ok {
		if err := credittransaction.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_id": %w`, err)}
		}
	}
	if v, ok := ctc.mutation.ReferenceType(); ok {
		if err := credittransaction.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_type": %w`, err)}
		}
	}
	if v, ok := ctc.mutation.PaymentID(); ok {
		if err := credittransaction.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.payment_id": %w`, err)}
		}
	}
	if v, ok := ctc.mutation.InvoiceID(); ok {
		if err := credittransaction.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.invoice_id": %w`, err)}
		}
	}
	if _, ok := ctc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "CreditTransaction.status"`)}
	}
	if v, ok := ctc.mutation.Status(); ok {
		if err := credittransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.status": %w`, err)}
		}
	}
	if _, ok := ctc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CreditTransaction.created_at"`)}
	}
	if _, ok := ctc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CreditTransaction.updated_at"`)}
	}
	return nil
}

func (ctc *CreditTransactionCreate) sqlSave(ctx context.Context) (*CreditTransaction, error) {
	if err := ctc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ctc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ctc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ctc.mutation.id = &_node.ID
	ctc.mutation.done = true
	return _node, nil
}

func (ctc *CreditTransactionCreate) createSpec() (*CreditTransaction, *sqlgraph.CreateSpec) {
	var (
		_node = &CreditTransaction{config: ctc.config}
		_spec = sqlgraph.NewCreateSpec(credittransaction.Table, sqlgraph.NewFieldSpec(credittransaction.FieldID, field.TypeUUID))
	)
	if id, ok := ctc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ctc.mutation.UserID(); ok {
		_spec.SetField(credittransaction.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := ctc.mutation.GetType(); ok {
		_spec.SetField(credittransaction.FieldType, field.TypeEnum, value)
		_node.Type = value
	}
	if value, ok := ctc.mutation.Amount(); ok {
		_spec.SetField(credittransaction.FieldAmount, field.TypeInt, value)
		_node.Amount = value
	}
	if value, ok := ctc.mutation.Description(); ok {
		_spec.SetField(credittransaction.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := ctc.mutation.ReferenceID(); ok {
		_spec.SetField(credittransaction.FieldReferenceID, field.TypeString, value)
		_node.ReferenceID = value
	}
	if value, ok := ctc.mutation.ReferenceType(); ok {
		_spec.SetField(credittransaction.FieldReferenceType, field.TypeString, value)
		_node.ReferenceType = value
	}
	if value, ok := ctc.mutation.PaymentID(); ok {
		_spec.SetField(credittransaction.FieldPaymentID, field.TypeString, value)
		_node.PaymentID = value
	}
	if value, ok := ctc.mutation.InvoiceID(); ok {
		_spec.SetField(credittransaction.FieldInvoiceID, field.TypeString, value)
		_node.InvoiceID = value
	}
	if value, ok := ctc.mutation.Status(); ok {
		_spec.SetField(credittransaction.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := ctc.mutation.Metadata(); ok {
		_spec.SetField(credittransaction.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ctc.mutation.ProcessedAt(); ok {
		_spec.SetField(credittransaction.FieldProcessedAt, field.TypeTime, value)
		_node.ProcessedAt = value
	}
	if value, ok := ctc.mutation.CreatedAt(); ok {
		_spec.SetField(credittransaction.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ctc.mutation.UpdatedAt(); ok {
		_spec.SetField(credittransaction.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// CreditTransactionCreateBulk is the builder for creating many CreditTransaction entities in bulk.
type CreditTransactionCreateBulk struct {
	config
	err      error
	builders []*CreditTransactionCreate
}

// Save creates the CreditTransaction entities in the database.
func (ctcb *CreditTransactionCreateBulk) Save(ctx context.Context) ([]*CreditTransaction, error) {
	if ctcb.err != nil {
		return nil, ctcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ctcb.builders))
	nodes := make([]*CreditTransaction, len(ctcb.builders))
	mutators := make([]Mutator, len(ctcb.builders))
	for i := range ctcb.builders {
		func(i int, root context.Context) {
			builder := ctcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CreditTransactionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ctcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ctcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ctcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ctcb *CreditTransactionCreateBulk) SaveX(ctx context.Context) []*CreditTransaction {
	v, err := ctcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ctcb *CreditTransactionCreateBulk) Exec(ctx context.Context) error {
	_, err := ctcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ctcb *CreditTransactionCreateBulk) ExecX(ctx context.Context) {
	if err := ctcb.Exec(ctx); err != nil {
		panic(err)
	}
}

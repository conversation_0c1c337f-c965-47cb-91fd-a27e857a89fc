package subscription

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/payment"
	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
)

// CreateSubscriptionWithPayment creates a subscription with payment flow
func (s *service) CreateSubscriptionWithPayment(ctx context.Context, req *models.CreateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
	}).Info("Creating subscription with payment flow")

	// 1. Get plan details
	planUUID, err := uuid.Parse(req.PlanID)
	if err != nil {
		return nil, fmt.Errorf("invalid plan ID format: %w", err)
	}

	plan, err := s.readDB.SubscriptionPlan.Query().
		Where(subscriptionplan.ID(planUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("plan not found")
		}
		return nil, fmt.Errorf("failed to get plan: %w", err)
	}

	// 2. Check if plan requires payment
	if plan.Price == 0 {
		// Free plan - activate immediately
		return s.createFreeSubscription(ctx, req)
	}

	// 3. Paid plan - create payment first
	return s.createPaidSubscription(ctx, req, plan)
}

// createFreeSubscription creates a free subscription immediately
func (s *service) createFreeSubscription(ctx context.Context, req *models.CreateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Creating free subscription")

	// Activate subscription using activation flow
	activationReq := &models.ActivateSubscriptionRequest{
		UserID:   req.UserID,
		PlanID:   req.PlanID,
		Source:   "free_plan",
		Metadata: req.Metadata,
	}

	activationInfo, err := s.ActivateSubscription(ctx, activationReq)
	if err != nil {
		return nil, fmt.Errorf("failed to activate free subscription: %w", err)
	}

	// Get the created subscription
	return s.GetSubscriptionByID(ctx, activationInfo.SubscriptionID)
}

// createPaidSubscription creates a paid subscription with payment flow
func (s *service) createPaidSubscription(ctx context.Context, req *models.CreateSubscriptionRequest, plan *ent.SubscriptionPlan) (*models.SubscriptionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
		"amount":  plan.Price,
	}).Info("Creating paid subscription")

	// 1. Create pending subscription record
	pendingSub, err := s.createPendingSubscription(ctx, req, plan)
	if err != nil {
		return nil, fmt.Errorf("failed to create pending subscription: %w", err)
	}

	// 2. Create PayPal payment
	paymentReq := &payment.PaymentRequest{
		UserID:        req.UserID,
		Amount:        plan.Price,
		Currency:      plan.Currency,
		PaymentMethod: "paypal",
		Description:   fmt.Sprintf("Subscription to %s plan", plan.DisplayName),
		OrderID:       pendingSub.ID.String(),
		Metadata: map[string]interface{}{
			"subscription_id": pendingSub.ID,
			"plan_id":         plan.ID.String(),
			"user_id":         req.UserID,
			"type":            "subscription",
		},
	}

	paymentResp, err := s.paymentProcessor.ProcessPayment(ctx, paymentReq)
	if err != nil {
		// Update subscription status to failed
		s.updateSubscriptionStatus(ctx, pendingSub.ID, subscription.StatusCancelled)
		return nil, fmt.Errorf("failed to create payment: %w", err)
	}

	// 3. Update subscription with payment info
	err = s.updateSubscriptionPaymentInfo(ctx, pendingSub.ID, paymentResp.PaymentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update subscription payment info")
		// Continue - payment was created successfully
	}

	// 4. Return subscription response with payment URL
	return &models.SubscriptionResponse{
		BaseModel: models.BaseModel{
			ID:        pendingSub.ID.String(),
			CreatedAt: pendingSub.CreatedAt,
			UpdatedAt: pendingSub.UpdatedAt,
		},
		UserID:             req.UserID,
		PlanID:             req.PlanID,
		PlanName:           plan.DisplayName,
		Status:             string(pendingSub.Status),
		BillingInterval:    string(plan.BillingInterval),
		Amount:             plan.Price,
		Currency:           plan.Currency,
		CurrentPeriodStart: pendingSub.CurrentPeriodStart,
		CurrentPeriodEnd:   pendingSub.CurrentPeriodEnd,
		Metadata: map[string]interface{}{
			"payment_id":  paymentResp.PaymentID,
			"payment_url": paymentResp.PaymentURL,
			"expires_at":  paymentResp.ExpiresAt,
		},
	}, nil
}

// createPendingSubscription creates a subscription in pending status
func (s *service) createPendingSubscription(ctx context.Context, req *models.CreateSubscriptionRequest, plan *ent.SubscriptionPlan) (*ent.Subscription, error) {
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	now := time.Now()
	periodStart := now
	var periodEnd time.Time

	// Calculate period end based on billing interval
	switch plan.BillingInterval {
	case "monthly":
		periodEnd = periodStart.AddDate(0, 1, 0)
	case "yearly":
		periodEnd = periodStart.AddDate(1, 0, 0)
	default:
		periodEnd = periodStart.AddDate(0, 1, 0) // Default to monthly
	}

	// Create subscription in pending status
	sub, err := s.writeDB.Subscription.Create().
		SetUserID(userUUID).
		SetPlanID(plan.ID).
		SetStatus(subscription.StatusPending).
		SetCurrentPeriodStart(periodStart).
		SetCurrentPeriodEnd(periodEnd).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription: %w", err)
	}

	return sub, nil
}

// updateSubscriptionStatus updates subscription status
func (s *service) updateSubscriptionStatus(ctx context.Context, subscriptionID uuid.UUID, status subscription.Status) error {
	_, err := s.writeDB.Subscription.UpdateOneID(subscriptionID).
		SetStatus(status).
		SetUpdatedAt(time.Now()).
		Save(ctx)
	return err
}

// updateSubscriptionPaymentInfo updates subscription with payment information
func (s *service) updateSubscriptionPaymentInfo(ctx context.Context, subscriptionID uuid.UUID, paymentID string) error {
	_, err := s.writeDB.Subscription.UpdateOneID(subscriptionID).
		SetPaymentID(paymentID).
		SetUpdatedAt(time.Now()).
		Save(ctx)
	return err
}

// HandlePaymentSuccess handles successful payment callback
func (s *service) HandlePaymentSuccess(ctx context.Context, paymentID string, payerID string) error {
	s.logger.WithFields(map[string]interface{}{
		"payment_id": paymentID,
		"payer_id":   payerID,
	}).Info("Handling payment success")

	// 1. Find subscription by payment ID
	sub, err := s.readDB.Subscription.Query().
		Where(subscription.PaymentIDEQ(paymentID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("subscription not found for payment ID: %s", paymentID)
		}
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// 2. Activate subscription
	activationReq := &models.ActivateSubscriptionRequest{
		UserID:    sub.UserID.String(),
		PlanID:    sub.PlanID.String(),
		PaymentID: paymentID,
		Source:    "payment",
		Metadata: map[string]interface{}{
			"payer_id":        payerID,
			"subscription_id": sub.ID.String(),
		},
	}

	activationInfo, err := s.ActivateSubscription(ctx, activationReq)
	if err != nil {
		return fmt.Errorf("failed to activate subscription: %w", err)
	}

	// 3. Publish subscription activated event
	event := sharedKafka.NewBillingEvent(sharedKafka.EventTypeSubscriptionActivated, sub.UserID.String())
	event.SubscriptionID = activationInfo.SubscriptionID
	event.PaymentID = paymentID
	event.Amount = 0 // Will be filled from plan
	event.Currency = "USD"
	event.WithData("payer_id", payerID)
	event.WithData("activation_type", "payment_success")
	event.WithData("plan_id", sub.PlanID.String())

	if err := s.eventPublisher.PublishBillingEvent(ctx, event); err != nil {
		s.logger.WithError(err).Error("Failed to publish subscription activated event")
		// Don't fail the operation - subscription is already activated
	}

	s.logger.WithFields(map[string]interface{}{
		"subscription_id": activationInfo.SubscriptionID,
		"user_id":         sub.UserID.String(),
		"payment_id":      paymentID,
	}).Info("Successfully activated subscription after payment")

	return nil
}

// HandlePaymentFailure handles failed payment callback
func (s *service) HandlePaymentFailure(ctx context.Context, paymentID string, reason string) error {
	s.logger.WithFields(map[string]interface{}{
		"payment_id": paymentID,
		"reason":     reason,
	}).Info("Handling payment failure")

	// 1. Find subscription by payment ID
	sub, err := s.readDB.Subscription.Query().
		Where(subscription.PaymentIDEQ(paymentID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("subscription not found for payment ID: %s", paymentID)
		}
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// 2. Update subscription status to cancelled
	err = s.updateSubscriptionStatus(ctx, sub.ID, subscription.StatusCancelled)
	if err != nil {
		return fmt.Errorf("failed to update subscription status: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"subscription_id": sub.ID.String(),
		"user_id":         sub.UserID.String(),
		"payment_id":      paymentID,
		"reason":          reason,
	}).Info("Cancelled subscription due to payment failure")

	return nil
}

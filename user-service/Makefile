# Makefile for User Service

.PHONY: all build test clean run docker proto ent deps lint fmt vet

# Variables
SERVICE_NAME := user-service
VERSION ?= latest
DOCKER_IMAGE := $(SERVICE_NAME):$(VERSION)
GO_VERSION := 1.21

# Build info
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS := -ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Default target
all: deps proto ent swagger build test

# Install dependencies
deps:
	@echo "📦 Installing dependencies..."
	go mod download
	go mod tidy

# Generate protocol buffers
proto:
	@echo "🔄 Generating protocol buffers..."
	cd ../proto-shared && make user

# Generate Ent code
ent:
	@echo "🔄 Generating Ent code..."
	go generate ./ent

# Generate Swagger documentation
swagger:
	@echo "📚 Generating Swagger documentation..."
	swag init -g main.go --output docs/

# Build the service
build:
	@echo "🔨 Building $(SERVICE_NAME)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(SERVICE_NAME) .

# Build for local development
build-local:
	@echo "🔨 Building $(SERVICE_NAME) for local development..."
	go build $(LDFLAGS) -o bin/$(SERVICE_NAME) .

# Run tests
test:
	@echo "🧪 Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

# Run tests with coverage report
test-coverage:
	@echo "🧪 Running tests with coverage..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "📊 Coverage report generated: coverage.html"

# Run benchmarks
bench:
	@echo "⚡ Running benchmarks..."
	go test -bench=. -benchmem ./...

# Run the service locally
run: build-local
	@echo "🚀 Running $(SERVICE_NAME)..."
	./bin/$(SERVICE_NAME)

# Run with hot reload (requires air)
dev:
	@echo "🔥 Running $(SERVICE_NAME) with hot reload..."
	air

# Docker build
docker:
	@echo "🐳 Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

# Docker run
docker-run:
	@echo "🐳 Running Docker container..."
	docker run --rm -p 50051:50051 -p 8080:8080 $(DOCKER_IMAGE)

# Docker compose up
compose-up:
	@echo "🐳 Starting services with docker-compose..."
	docker-compose up -d

# Docker compose down
compose-down:
	@echo "🐳 Stopping services with docker-compose..."
	docker-compose down

# Development environment management
dev-up:
	@echo "🐳 Starting development dependencies..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 10
	@echo "✅ Development environment is ready!"
	@echo "  📊 PostgreSQL: localhost:5432 (user: postgres, password: postgres, db: user_service_dev)"
	@echo "  🔴 Redis: localhost:6379"
	@echo "  📧 MailHog UI: http://localhost:8025"
	@echo "  🗄️ pgAdmin: http://localhost:5050 (<EMAIL> / admin)"

dev-down:
	@echo "🐳 Stopping development dependencies..."
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	@echo "📋 Showing development logs..."
	docker-compose -f docker-compose.dev.yml logs -f

# Database switching
db-sqlite:
	@echo "🔄 Switching to SQLite database..."
	./scripts/switch-database.sh sqlite development
	@echo "✅ Database switched to SQLite. Run 'make run' to start the service."

db-postgres:
	@echo "🔄 Switching to PostgreSQL database..."
	@echo "💡 Make sure PostgreSQL is running (use 'make dev-up' to start)"
	./scripts/switch-database.sh postgres development
	@echo "✅ Database switched to PostgreSQL. Run 'make run' to start the service."

# Database management
db-status:
	@echo "📊 Current database configuration:"
	@grep -A 10 "^database:" config/development.yaml || echo "❌ Config file not found"

db-backup:
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@if grep -q 'type: "sqlite"' config/development.yaml; then \
		echo "📦 Backing up SQLite database..."; \
		cp ./data/user_service_dev.db ./backups/user_service_dev_$(shell date +%Y%m%d_%H%M%S).db; \
		echo "✅ SQLite backup created in ./backups/"; \
	else \
		echo "📦 Backing up PostgreSQL database..."; \
		docker exec user-service-postgres pg_dump -U postgres user_service_dev > ./backups/user_service_dev_$(shell date +%Y%m%d_%H%M%S).sql; \
		echo "✅ PostgreSQL backup created in ./backups/"; \
	fi

# Quick development setup
dev-setup: dev-up db-postgres build-local
	@echo "🎉 Development environment setup complete!"
	@echo "🚀 Run 'make run' to start the user service."

# Quick SQLite setup
sqlite-setup: db-sqlite build-local
	@echo "🎉 SQLite setup complete!"
	@echo "🚀 Run 'make run' to start the user service."

# Lint code
lint:
	@echo "🔍 Linting code..."
	golangci-lint run

# Format code
fmt:
	@echo "✨ Formatting code..."
	go fmt ./...
	goimports -w .

# Vet code
vet:
	@echo "🔍 Vetting code..."
	go vet ./...

# Security scan
security:
	@echo "🔒 Running security scan..."
	gosec ./...

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	go clean -cache

# Database migrations
migrate-up:
	@echo "⬆️ Running database migrations..."
	go run ./cmd/migrate up

migrate-down:
	@echo "⬇️ Rolling back database migrations..."
	go run ./cmd/migrate down

migrate-create:
	@echo "📝 Creating new migration..."
	@read -p "Enter migration name: " name; \
	go run ./cmd/migrate create $$name

# Generate mocks
mocks:
	@echo "🎭 Generating mocks..."
	mockgen -source=usecase/user/interface.go -destination=mocks/user_usecase.go
	mockgen -source=usecase/auth/interface.go -destination=mocks/auth_usecase.go
	mockgen -source=usecase/session/interface.go -destination=mocks/session_usecase.go

# Install development tools
install-tools:
	@echo "🛠️ Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	go install github.com/golang/mock/mockgen@latest
	go install github.com/cosmtrek/air@latest
	go install entgo.io/ent/cmd/ent@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# Check if tools are installed
check-tools:
	@echo "🔍 Checking development tools..."
	@which golangci-lint > /dev/null || echo "❌ golangci-lint not found"
	@which goimports > /dev/null || echo "❌ goimports not found"
	@which gosec > /dev/null || echo "❌ gosec not found"
	@which mockgen > /dev/null || echo "❌ mockgen not found"
	@which air > /dev/null || echo "❌ air not found"
	@which ent > /dev/null || echo "❌ ent not found"
	@which swag > /dev/null || echo "❌ swag not found"

# Full CI pipeline
ci: deps proto ent fmt vet lint security test

# Release build
release: clean ci build docker
	@echo "🎉 Release build completed!"

# Help
help:
	@echo "Available targets:"
	@echo ""
	@echo "🔨 Build & Run:"
	@echo "  all          - Run deps, proto, ent, build, and test"
	@echo "  build        - Build the service binary"
	@echo "  build-local  - Build for local development"
	@echo "  run          - Build and run the service"
	@echo "  dev          - Run with hot reload (requires air)"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage report"
	@echo "  bench        - Run benchmarks"
	@echo ""
	@echo "🐳 Docker & Compose:"
	@echo "  docker       - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  compose-up   - Start with docker-compose"
	@echo "  compose-down - Stop docker-compose services"
	@echo ""
	@echo "🛠️ Development Environment:"
	@echo "  dev-up       - Start development dependencies (PostgreSQL, Redis, MailHog)"
	@echo "  dev-down     - Stop development dependencies"
	@echo "  dev-logs     - Show development logs"
	@echo "  dev-setup    - Quick setup with PostgreSQL"
	@echo "  sqlite-setup - Quick setup with SQLite"
	@echo ""
	@echo "🗄️ Database Management:"
	@echo "  db-sqlite    - Switch to SQLite database"
	@echo "  db-postgres  - Switch to PostgreSQL database"
	@echo "  db-status    - Show current database configuration"
	@echo "  db-backup    - Backup current database"
	@echo ""
	@echo "🔧 Code Quality:"
	@echo "  deps         - Install Go dependencies"
	@echo "  proto        - Generate protocol buffer code"
	@echo "  ent          - Generate Ent ORM code"
	@echo "  lint         - Lint code with golangci-lint"
	@echo "  fmt          - Format code"
	@echo "  vet          - Vet code"
	@echo "  security     - Run security scan"
	@echo "  mocks        - Generate mocks"
	@echo ""
	@echo "🗃️ Database Migrations:"
	@echo "  migrate-up   - Run database migrations"
	@echo "  migrate-down - Rollback database migrations"
	@echo "  migrate-create - Create new migration"
	@echo ""
	@echo "🛠️ Tools:"
	@echo "  install-tools- Install development tools"
	@echo "  check-tools  - Check if tools are installed"
	@echo "  clean        - Clean build artifacts"
	@echo ""
	@echo "🚀 CI/CD:"
	@echo "  ci           - Run full CI pipeline"
	@echo "  release      - Build release version"
	@echo "  help         - Show this help message"

package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/user-service/pkg/models"
	"github.com/social-content-ai/user-service/usecase/auth"
	"github.com/social-content-ai/pkg-shared/logging"
)

// AuthMiddleware handles authentication middleware
type AuthMiddleware struct {
	authUseCase auth.UseCase
	logger      logging.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(authUseCase auth.UseCase, logger logging.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		authUseCase: authUseCase,
		logger:      logger,
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "MISSING_TOKEN",
				Message: "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_TOKEN_FORMAT",
				Message: "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Validate token
		claims, err := m.authUseCase.ValidateToken(c.Request.Context(), token)
		if err != nil {
			m.logger.WithError(err).Error("Token validation failed")
			
			switch err.Error() {
			case "TOKEN_EXPIRED":
				c.JSON(http.StatusUnauthorized, models.ErrorResponse{
					Error:   "TOKEN_EXPIRED",
					Message: "Access token has expired",
				})
			case "INVALID_TOKEN":
				c.JSON(http.StatusUnauthorized, models.ErrorResponse{
					Error:   "INVALID_TOKEN",
					Message: "Invalid access token",
				})
			default:
				c.JSON(http.StatusUnauthorized, models.ErrorResponse{
					Error:   "AUTHENTICATION_FAILED",
					Message: "Authentication failed",
				})
			}
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// RequireRole middleware that requires specific role
func (m *AuthMiddleware) RequireRole(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		if userRole != requiredRole && userRole != "admin" { // admin can access everything
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "INSUFFICIENT_PERMISSIONS",
				Message: "Insufficient permissions to access this resource",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRoles middleware that requires one of multiple roles
func (m *AuthMiddleware) RequireRoles(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		
		// Admin can access everything
		if userRole == "admin" {
			c.Next()
			return
		}

		// Check if user has one of the required roles
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error:   "INSUFFICIENT_PERMISSIONS",
			Message: "Insufficient permissions to access this resource",
		})
		c.Abort()
	}
}

// OptionalAuth middleware that optionally authenticates user
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		token := tokenParts[1]

		// Validate token
		claims, err := m.authUseCase.ValidateToken(c.Request.Context(), token)
		if err != nil {
			// Don't abort, just continue without authentication
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// CORS middleware
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		
		c.Next()
	})
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production, use UUID or similar
	return "req_" + strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(
		"2006-01-02T15:04:05.000Z", "-", ""), ":", ""), ".", "")
}

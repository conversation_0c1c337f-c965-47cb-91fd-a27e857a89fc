package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/integration-service/ent"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PlatformIntegrationRepository handles platform integration data operations
type PlatformIntegrationRepository struct {
	client *ent.Client
	logger logging.Logger
}

// NewPlatformIntegrationRepository creates a new platform integration repository
func NewPlatformIntegrationRepository(client *ent.Client, logger logging.Logger) *PlatformIntegrationRepository {
	return &PlatformIntegrationRepository{
		client: client,
		logger: logger,
	}
}

// GetPlatformIntegration retrieves a platform integration by ID
func (r *PlatformIntegrationRepository) GetPlatformIntegration(ctx context.Context, id string) (*ent.PlatformIntegration, error) {
	integrationID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid integration ID: %w", err)
	}

	integration, err := r.client.PlatformIntegration.
		Query().
		Where(platformintegration.ID(integrationID)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("platform integration not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get platform integration: %w", err)
	}

	return integration, nil
}

// GetPlatformIntegrationByUserAndPlatform retrieves a platform integration by user ID and platform
func (r *PlatformIntegrationRepository) GetPlatformIntegrationByUserAndPlatform(ctx context.Context, userID, platform string) (*ent.PlatformIntegration, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	integration, err := r.client.PlatformIntegration.
		Query().
		Where(
			platformintegration.UserID(userUUID),
			platformintegration.PlatformEQ(platformintegration.Platform(platform)),
		).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil // Return nil without error if not found
		}
		return nil, fmt.Errorf("failed to get platform integration: %w", err)
	}

	return integration, nil
}

// ListPlatformIntegrations retrieves platform integrations with filters
func (r *PlatformIntegrationRepository) ListPlatformIntegrations(ctx context.Context, userID, workspaceID, platform, status string, limit, offset int32) ([]*ent.PlatformIntegration, int32, error) {
	query := r.client.PlatformIntegration.Query()

	// Apply filters
	if userID != "" {
		userUUID, err := uuid.Parse(userID)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid user ID: %w", err)
		}
		query = query.Where(platformintegration.UserID(userUUID))
	}

	if workspaceID != "" {
		query = query.Where(platformintegration.WorkspaceID(workspaceID))
	}

	if platform != "" {
		query = query.Where(platformintegration.PlatformEQ(platformintegration.Platform(platform)))
	}

	if status != "" {
		query = query.Where(platformintegration.StatusEQ(platformintegration.Status(status)))
	}

	// Get total count
	totalCount, err := query.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count platform integrations: %w", err)
	}

	// Apply pagination
	if limit > 0 {
		query = query.Limit(int(limit))
	}
	if offset > 0 {
		query = query.Offset(int(offset))
	}

	// Order by creation date (newest first)
	query = query.Order(ent.Desc(platformintegration.FieldCreatedAt))

	integrations, err := query.All(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list platform integrations: %w", err)
	}

	return integrations, int32(totalCount), nil
}

// CreatePlatformIntegration creates a new platform integration
func (r *PlatformIntegrationRepository) CreatePlatformIntegration(ctx context.Context, integration *ent.PlatformIntegration) (*ent.PlatformIntegration, error) {
	create := r.client.PlatformIntegration.Create().
		SetID(integration.ID).
		SetUserID(integration.UserID).
		SetPlatform(integration.Platform).
		SetPlatformUserID(integration.PlatformUserID).
		SetAccessToken(integration.AccessToken).
		SetScopes(integration.Scopes).
		SetStatus(integration.Status).
		SetCreatedAt(integration.CreatedAt).
		SetUpdatedAt(integration.UpdatedAt)

	// Set optional fields
	if integration.WorkspaceID != "" {
		create = create.SetWorkspaceID(integration.WorkspaceID)
	}
	if integration.PlatformUsername != "" {
		create = create.SetPlatformUsername(integration.PlatformUsername)
	}
	if integration.PlatformDisplayName != "" {
		create = create.SetPlatformDisplayName(integration.PlatformDisplayName)
	}
	if integration.PlatformAvatarURL != "" {
		create = create.SetPlatformAvatarURL(integration.PlatformAvatarURL)
	}
	if integration.RefreshToken != "" {
		create = create.SetRefreshToken(integration.RefreshToken)
	}
	if !integration.TokenExpiresAt.IsZero() {
		create = create.SetTokenExpiresAt(integration.TokenExpiresAt)
	}
	if !integration.LastSyncAt.IsZero() {
		create = create.SetLastSyncAt(integration.LastSyncAt)
	}
	if integration.ErrorMessage != "" {
		create = create.SetErrorMessage(integration.ErrorMessage)
	}
	if integration.PlatformData != nil {
		create = create.SetPlatformData(integration.PlatformData)
	}

	created, err := create.Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create platform integration: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"integration_id": created.ID,
		"user_id":        created.UserID,
		"platform":       created.Platform,
	}).Info("Platform integration created")

	return created, nil
}

// UpdatePlatformIntegration updates a platform integration
func (r *PlatformIntegrationRepository) UpdatePlatformIntegration(ctx context.Context, id string, updates map[string]interface{}) (*ent.PlatformIntegration, error) {
	integrationID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid integration ID: %w", err)
	}

	update := r.client.PlatformIntegration.UpdateOneID(integrationID)

	// Apply updates based on the map
	for key, value := range updates {
		switch key {
		case "platform_user_id":
			if v, ok := value.(string); ok {
				update = update.SetPlatformUserID(v)
			}
		case "platform_username":
			if v, ok := value.(string); ok {
				update = update.SetPlatformUsername(v)
			}
		case "platform_display_name":
			if v, ok := value.(string); ok {
				update = update.SetPlatformDisplayName(v)
			}
		case "platform_avatar_url":
			if v, ok := value.(string); ok {
				update = update.SetPlatformAvatarURL(v)
			}
		case "access_token":
			if v, ok := value.(string); ok {
				update = update.SetAccessToken(v)
			}
		case "refresh_token":
			if v, ok := value.(string); ok {
				update = update.SetRefreshToken(v)
			}
		case "token_expires_at":
			if v, ok := value.(time.Time); ok {
				update = update.SetTokenExpiresAt(v)
			}
		case "scopes":
			if v, ok := value.([]string); ok {
				update = update.SetScopes(v)
			}
		case "status":
			if v, ok := value.(string); ok {
				update = update.SetStatus(platformintegration.Status(v))
			}
		case "error_message":
			if v, ok := value.(string); ok {
				update = update.SetErrorMessage(v)
			}
		case "last_sync_at":
			if v, ok := value.(time.Time); ok {
				update = update.SetLastSyncAt(v)
			}
		case "updated_at":
			if v, ok := value.(time.Time); ok {
				update = update.SetUpdatedAt(v)
			}
		case "platform_data":
			if v, ok := value.(map[string]interface{}); ok {
				update = update.SetPlatformData(v)
			}
		}
	}

	updated, err := update.Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("platform integration not found: %s", id)
		}
		return nil, fmt.Errorf("failed to update platform integration: %w", err)
	}

	r.logger.WithFields(map[string]interface{}{
		"integration_id": updated.ID,
		"updates":        len(updates),
	}).Info("Platform integration updated")

	return updated, nil
}

// DeletePlatformIntegration deletes a platform integration
func (r *PlatformIntegrationRepository) DeletePlatformIntegration(ctx context.Context, id string) error {
	integrationID, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid integration ID: %w", err)
	}

	err = r.client.PlatformIntegration.DeleteOneID(integrationID).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("platform integration not found: %s", id)
		}
		return fmt.Errorf("failed to delete platform integration: %w", err)
	}

	r.logger.WithField("integration_id", integrationID).Info("Platform integration deleted")
	return nil
}

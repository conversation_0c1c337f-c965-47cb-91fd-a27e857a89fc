package plan

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the subscription plan business logic interface
type UseCase interface {
	// Basic plan operations
	ListPlans(ctx context.Context, req *models.ListPlansRequest) (*models.ListPlansResponse, error)
	GetPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error)

	// Analytics
	GetPlanAnalytics(ctx context.Context, userID, period string) (*models.PlanAnalyticsResponse, error)

	// Public operations
	GetPublicPlans(ctx context.Context) (*models.ListPlansResponse, error)
	GetPublicPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error)
	GetPricing(ctx context.Context) (*models.PricingResponse, error)

	// Admin operations
	AdminListPlans(ctx context.Context, req *models.ListPlansRequest) (*models.ListPlansResponse, error)
	AdminCreatePlan(ctx context.Context, req *models.CreatePlanRequest) (*models.SubscriptionPlan, error)
	AdminUpdatePlan(ctx context.Context, req *models.UpdatePlanRequest) (*models.SubscriptionPlan, error)
	AdminDeletePlan(ctx context.Context, planID string) error
	AdminActivatePlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error)
	AdminDeactivatePlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error)
	GetAdminPlanAnalytics(ctx context.Context, period string) (*models.PlanAnalyticsResponse, error)
	AdminGetPlanConfig(ctx context.Context) (*models.PricingResponse, error)
	AdminUpdatePlanConfig(ctx context.Context, config *models.PricingResponse) (*models.PricingResponse, error)

	// Internal operations (for service-to-service communication)
	InternalGetPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error)

	// Promo codes
	ValidatePromoCode(ctx context.Context, code string, planID string) (*models.PromoCode, error)
}

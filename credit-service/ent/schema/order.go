package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Order holds the schema definition for the Order entity.
type Order struct {
	ent.Schema
}

// Fields of the Order.
func (Order) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.String("order_number").
			MaxLen(50).
			Unique(),
		field.UUID("user_id", uuid.UUID{}),
		field.UUID("package_id", uuid.UUID{}).
			Optional(),
		field.UUID("plan_id", uuid.UUID{}).
			Optional(),
		field.Enum("type").
			Values("credit_purchase", "subscription", "upgrade", "downgrade").
			Default("credit_purchase"),
		field.Int64("amount"). // in cents
			Min(0),
		field.String("currency").
			MaxLen(3).
			Default("USD"),
		field.Enum("status").
			Values("pending", "processing", "completed", "failed", "cancelled", "refunded").
			Default("pending"),
		field.String("payment_method").
			MaxLen(50),
		field.String("payment_id").
			MaxLen(100).
			Optional(),
		field.String("invoice_id").
			MaxLen(100).
			Optional(),
		field.Int("credits").
			Default(0).
			Min(0),
		field.JSON("billing_address", map[string]interface{}{}).
			Optional(),
		field.JSON("items", []map[string]interface{}{}).
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("processed_at").
			Optional(),
		field.Time("expires_at").
			Optional(),
		field.String("failure_reason").
			MaxLen(500).
			Optional(),
		field.String("external_order_id").
			MaxLen(100).
			Optional(), // For payment provider order ID
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the Order.
func (Order) Edges() []ent.Edge {
	return nil
}

// Indexes of the Order.
func (Order) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("order_number").Unique(),
		index.Fields("user_id"),
		index.Fields("package_id"),
		index.Fields("plan_id"),
		index.Fields("type"),
		index.Fields("status"),
		index.Fields("payment_method"),
		index.Fields("payment_id"),
		index.Fields("external_order_id"),
		index.Fields("created_at"),
	}
}

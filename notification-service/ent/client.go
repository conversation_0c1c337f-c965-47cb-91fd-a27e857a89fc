// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"github.com/social-content-ai/notification-service/ent/notification"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Notification is the client for interacting with the Notification builders.
	Notification *NotificationClient
	// NotificationPreference is the client for interacting with the NotificationPreference builders.
	NotificationPreference *NotificationPreferenceClient
	// NotificationTemplate is the client for interacting with the NotificationTemplate builders.
	NotificationTemplate *NotificationTemplateClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Notification = NewNotificationClient(c.config)
	c.NotificationPreference = NewNotificationPreferenceClient(c.config)
	c.NotificationTemplate = NewNotificationTemplateClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                    ctx,
		config:                 cfg,
		Notification:           NewNotificationClient(cfg),
		NotificationPreference: NewNotificationPreferenceClient(cfg),
		NotificationTemplate:   NewNotificationTemplateClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                    ctx,
		config:                 cfg,
		Notification:           NewNotificationClient(cfg),
		NotificationPreference: NewNotificationPreferenceClient(cfg),
		NotificationTemplate:   NewNotificationTemplateClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Notification.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Notification.Use(hooks...)
	c.NotificationPreference.Use(hooks...)
	c.NotificationTemplate.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Notification.Intercept(interceptors...)
	c.NotificationPreference.Intercept(interceptors...)
	c.NotificationTemplate.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *NotificationMutation:
		return c.Notification.mutate(ctx, m)
	case *NotificationPreferenceMutation:
		return c.NotificationPreference.mutate(ctx, m)
	case *NotificationTemplateMutation:
		return c.NotificationTemplate.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// NotificationClient is a client for the Notification schema.
type NotificationClient struct {
	config
}

// NewNotificationClient returns a client for the Notification from the given config.
func NewNotificationClient(c config) *NotificationClient {
	return &NotificationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notification.Hooks(f(g(h())))`.
func (c *NotificationClient) Use(hooks ...Hook) {
	c.hooks.Notification = append(c.hooks.Notification, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notification.Intercept(f(g(h())))`.
func (c *NotificationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Notification = append(c.inters.Notification, interceptors...)
}

// Create returns a builder for creating a Notification entity.
func (c *NotificationClient) Create() *NotificationCreate {
	mutation := newNotificationMutation(c.config, OpCreate)
	return &NotificationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Notification entities.
func (c *NotificationClient) CreateBulk(builders ...*NotificationCreate) *NotificationCreateBulk {
	return &NotificationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationClient) MapCreateBulk(slice any, setFunc func(*NotificationCreate, int)) *NotificationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationCreateBulk{err: fmt.Errorf("calling to NotificationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Notification.
func (c *NotificationClient) Update() *NotificationUpdate {
	mutation := newNotificationMutation(c.config, OpUpdate)
	return &NotificationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationClient) UpdateOne(n *Notification) *NotificationUpdateOne {
	mutation := newNotificationMutation(c.config, OpUpdateOne, withNotification(n))
	return &NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationClient) UpdateOneID(id uuid.UUID) *NotificationUpdateOne {
	mutation := newNotificationMutation(c.config, OpUpdateOne, withNotificationID(id))
	return &NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Notification.
func (c *NotificationClient) Delete() *NotificationDelete {
	mutation := newNotificationMutation(c.config, OpDelete)
	return &NotificationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationClient) DeleteOne(n *Notification) *NotificationDeleteOne {
	return c.DeleteOneID(n.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationClient) DeleteOneID(id uuid.UUID) *NotificationDeleteOne {
	builder := c.Delete().Where(notification.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationDeleteOne{builder}
}

// Query returns a query builder for Notification.
func (c *NotificationClient) Query() *NotificationQuery {
	return &NotificationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotification},
		inters: c.Interceptors(),
	}
}

// Get returns a Notification entity by its id.
func (c *NotificationClient) Get(ctx context.Context, id uuid.UUID) (*Notification, error) {
	return c.Query().Where(notification.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationClient) GetX(ctx context.Context, id uuid.UUID) *Notification {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NotificationClient) Hooks() []Hook {
	return c.hooks.Notification
}

// Interceptors returns the client interceptors.
func (c *NotificationClient) Interceptors() []Interceptor {
	return c.inters.Notification
}

func (c *NotificationClient) mutate(ctx context.Context, m *NotificationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Notification mutation op: %q", m.Op())
	}
}

// NotificationPreferenceClient is a client for the NotificationPreference schema.
type NotificationPreferenceClient struct {
	config
}

// NewNotificationPreferenceClient returns a client for the NotificationPreference from the given config.
func NewNotificationPreferenceClient(c config) *NotificationPreferenceClient {
	return &NotificationPreferenceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notificationpreference.Hooks(f(g(h())))`.
func (c *NotificationPreferenceClient) Use(hooks ...Hook) {
	c.hooks.NotificationPreference = append(c.hooks.NotificationPreference, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notificationpreference.Intercept(f(g(h())))`.
func (c *NotificationPreferenceClient) Intercept(interceptors ...Interceptor) {
	c.inters.NotificationPreference = append(c.inters.NotificationPreference, interceptors...)
}

// Create returns a builder for creating a NotificationPreference entity.
func (c *NotificationPreferenceClient) Create() *NotificationPreferenceCreate {
	mutation := newNotificationPreferenceMutation(c.config, OpCreate)
	return &NotificationPreferenceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NotificationPreference entities.
func (c *NotificationPreferenceClient) CreateBulk(builders ...*NotificationPreferenceCreate) *NotificationPreferenceCreateBulk {
	return &NotificationPreferenceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationPreferenceClient) MapCreateBulk(slice any, setFunc func(*NotificationPreferenceCreate, int)) *NotificationPreferenceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationPreferenceCreateBulk{err: fmt.Errorf("calling to NotificationPreferenceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationPreferenceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationPreferenceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NotificationPreference.
func (c *NotificationPreferenceClient) Update() *NotificationPreferenceUpdate {
	mutation := newNotificationPreferenceMutation(c.config, OpUpdate)
	return &NotificationPreferenceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationPreferenceClient) UpdateOne(np *NotificationPreference) *NotificationPreferenceUpdateOne {
	mutation := newNotificationPreferenceMutation(c.config, OpUpdateOne, withNotificationPreference(np))
	return &NotificationPreferenceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationPreferenceClient) UpdateOneID(id uuid.UUID) *NotificationPreferenceUpdateOne {
	mutation := newNotificationPreferenceMutation(c.config, OpUpdateOne, withNotificationPreferenceID(id))
	return &NotificationPreferenceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NotificationPreference.
func (c *NotificationPreferenceClient) Delete() *NotificationPreferenceDelete {
	mutation := newNotificationPreferenceMutation(c.config, OpDelete)
	return &NotificationPreferenceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationPreferenceClient) DeleteOne(np *NotificationPreference) *NotificationPreferenceDeleteOne {
	return c.DeleteOneID(np.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationPreferenceClient) DeleteOneID(id uuid.UUID) *NotificationPreferenceDeleteOne {
	builder := c.Delete().Where(notificationpreference.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationPreferenceDeleteOne{builder}
}

// Query returns a query builder for NotificationPreference.
func (c *NotificationPreferenceClient) Query() *NotificationPreferenceQuery {
	return &NotificationPreferenceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotificationPreference},
		inters: c.Interceptors(),
	}
}

// Get returns a NotificationPreference entity by its id.
func (c *NotificationPreferenceClient) Get(ctx context.Context, id uuid.UUID) (*NotificationPreference, error) {
	return c.Query().Where(notificationpreference.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationPreferenceClient) GetX(ctx context.Context, id uuid.UUID) *NotificationPreference {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NotificationPreferenceClient) Hooks() []Hook {
	return c.hooks.NotificationPreference
}

// Interceptors returns the client interceptors.
func (c *NotificationPreferenceClient) Interceptors() []Interceptor {
	return c.inters.NotificationPreference
}

func (c *NotificationPreferenceClient) mutate(ctx context.Context, m *NotificationPreferenceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationPreferenceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationPreferenceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationPreferenceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationPreferenceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NotificationPreference mutation op: %q", m.Op())
	}
}

// NotificationTemplateClient is a client for the NotificationTemplate schema.
type NotificationTemplateClient struct {
	config
}

// NewNotificationTemplateClient returns a client for the NotificationTemplate from the given config.
func NewNotificationTemplateClient(c config) *NotificationTemplateClient {
	return &NotificationTemplateClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notificationtemplate.Hooks(f(g(h())))`.
func (c *NotificationTemplateClient) Use(hooks ...Hook) {
	c.hooks.NotificationTemplate = append(c.hooks.NotificationTemplate, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notificationtemplate.Intercept(f(g(h())))`.
func (c *NotificationTemplateClient) Intercept(interceptors ...Interceptor) {
	c.inters.NotificationTemplate = append(c.inters.NotificationTemplate, interceptors...)
}

// Create returns a builder for creating a NotificationTemplate entity.
func (c *NotificationTemplateClient) Create() *NotificationTemplateCreate {
	mutation := newNotificationTemplateMutation(c.config, OpCreate)
	return &NotificationTemplateCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NotificationTemplate entities.
func (c *NotificationTemplateClient) CreateBulk(builders ...*NotificationTemplateCreate) *NotificationTemplateCreateBulk {
	return &NotificationTemplateCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotificationTemplateClient) MapCreateBulk(slice any, setFunc func(*NotificationTemplateCreate, int)) *NotificationTemplateCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotificationTemplateCreateBulk{err: fmt.Errorf("calling to NotificationTemplateClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotificationTemplateCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotificationTemplateCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NotificationTemplate.
func (c *NotificationTemplateClient) Update() *NotificationTemplateUpdate {
	mutation := newNotificationTemplateMutation(c.config, OpUpdate)
	return &NotificationTemplateUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotificationTemplateClient) UpdateOne(nt *NotificationTemplate) *NotificationTemplateUpdateOne {
	mutation := newNotificationTemplateMutation(c.config, OpUpdateOne, withNotificationTemplate(nt))
	return &NotificationTemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotificationTemplateClient) UpdateOneID(id uuid.UUID) *NotificationTemplateUpdateOne {
	mutation := newNotificationTemplateMutation(c.config, OpUpdateOne, withNotificationTemplateID(id))
	return &NotificationTemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NotificationTemplate.
func (c *NotificationTemplateClient) Delete() *NotificationTemplateDelete {
	mutation := newNotificationTemplateMutation(c.config, OpDelete)
	return &NotificationTemplateDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotificationTemplateClient) DeleteOne(nt *NotificationTemplate) *NotificationTemplateDeleteOne {
	return c.DeleteOneID(nt.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotificationTemplateClient) DeleteOneID(id uuid.UUID) *NotificationTemplateDeleteOne {
	builder := c.Delete().Where(notificationtemplate.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotificationTemplateDeleteOne{builder}
}

// Query returns a query builder for NotificationTemplate.
func (c *NotificationTemplateClient) Query() *NotificationTemplateQuery {
	return &NotificationTemplateQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotificationTemplate},
		inters: c.Interceptors(),
	}
}

// Get returns a NotificationTemplate entity by its id.
func (c *NotificationTemplateClient) Get(ctx context.Context, id uuid.UUID) (*NotificationTemplate, error) {
	return c.Query().Where(notificationtemplate.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotificationTemplateClient) GetX(ctx context.Context, id uuid.UUID) *NotificationTemplate {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NotificationTemplateClient) Hooks() []Hook {
	return c.hooks.NotificationTemplate
}

// Interceptors returns the client interceptors.
func (c *NotificationTemplateClient) Interceptors() []Interceptor {
	return c.inters.NotificationTemplate
}

func (c *NotificationTemplateClient) mutate(ctx context.Context, m *NotificationTemplateMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotificationTemplateCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotificationTemplateUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotificationTemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotificationTemplateDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NotificationTemplate mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Notification, NotificationPreference, NotificationTemplate []ent.Hook
	}
	inters struct {
		Notification, NotificationPreference, NotificationTemplate []ent.Interceptor
	}
)

package twofa

import (
	"context"
)

// UseCase defines the Two-Factor Authentication business logic interface
type UseCase interface {
	// Setup 2FA for a user
	SetupTwoFA(ctx context.Context, req *SetupTwoFARequest) (*SetupTwoFAResponse, error)
	
	// Enable 2FA after verification
	EnableTwoFA(ctx context.Context, req *EnableTwoFARequest) (*EnableTwoFAResponse, error)
	
	// Disable 2FA
	DisableTwoFA(ctx context.Context, req *DisableTwoFARequest) error
	
	// Verify 2FA token during login
	VerifyTwoFA(ctx context.Context, req *VerifyTwoFARequest) (*VerifyTwoFAResponse, error)
	
	// Generate new backup codes
	GenerateBackupCodes(ctx context.Context, req *GenerateBackupCodesRequest) (*GenerateBackupCodesResponse, error)
	
	// Verify backup code
	VerifyBackupCode(ctx context.Context, req *VerifyBackupCodeRequest) (*VerifyBackupCodeResponse, error)
	
	// Get 2FA status for a user
	GetTwoFAStatus(ctx context.Context, userID uint64) (*TwoFAStatusResponse, error)
}

// SetupTwoFARequest represents a request to setup 2FA
type SetupTwoFARequest struct {
	UserID uint64 `json:"user_id" validate:"required"`
}

// SetupTwoFAResponse represents the response from setting up 2FA
type SetupTwoFAResponse struct {
	Secret      string   `json:"secret"`
	QRCodeURL   string   `json:"qr_code_url"`
	QRCode      []byte   `json:"qr_code,omitempty"`
	BackupCodes []string `json:"backup_codes"`
}

// EnableTwoFARequest represents a request to enable 2FA
type EnableTwoFARequest struct {
	UserID uint64 `json:"user_id" validate:"required"`
	Token  string `json:"token" validate:"required,len=6"`
}

// EnableTwoFAResponse represents the response from enabling 2FA
type EnableTwoFAResponse struct {
	Enabled     bool     `json:"enabled"`
	BackupCodes []string `json:"backup_codes"`
	EnabledAt   string   `json:"enabled_at"`
}

// DisableTwoFARequest represents a request to disable 2FA
type DisableTwoFARequest struct {
	UserID   uint64 `json:"user_id" validate:"required"`
	Password string `json:"password" validate:"required"`
	Token    string `json:"token,omitempty"` // TOTP token or backup code
}

// VerifyTwoFARequest represents a request to verify 2FA during login
type VerifyTwoFARequest struct {
	UserID uint64 `json:"user_id" validate:"required"`
	Token  string `json:"token" validate:"required"`
}

// VerifyTwoFAResponse represents the response from 2FA verification
type VerifyTwoFAResponse struct {
	Valid         bool   `json:"valid"`
	BackupCodeUsed bool   `json:"backup_code_used,omitempty"`
	RemainingCodes int    `json:"remaining_codes,omitempty"`
	Message       string `json:"message,omitempty"`
}

// GenerateBackupCodesRequest represents a request to generate new backup codes
type GenerateBackupCodesRequest struct {
	UserID   uint64 `json:"user_id" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// GenerateBackupCodesResponse represents the response with new backup codes
type GenerateBackupCodesResponse struct {
	BackupCodes []string `json:"backup_codes"`
	GeneratedAt string   `json:"generated_at"`
}

// VerifyBackupCodeRequest represents a request to verify a backup code
type VerifyBackupCodeRequest struct {
	UserID     uint64 `json:"user_id" validate:"required"`
	BackupCode string `json:"backup_code" validate:"required"`
}

// VerifyBackupCodeResponse represents the response from backup code verification
type VerifyBackupCodeResponse struct {
	Valid          bool `json:"valid"`
	RemainingCodes int  `json:"remaining_codes"`
}

// TwoFAStatusResponse represents the 2FA status for a user
type TwoFAStatusResponse struct {
	UserID              uint64 `json:"user_id"`
	TwoFAEnabled        bool   `json:"two_fa_enabled"`
	EnabledAt           string `json:"enabled_at,omitempty"`
	BackupCodesCount    int    `json:"backup_codes_count"`
	HasBackupCodes      bool   `json:"has_backup_codes"`
	LastVerificationAt  string `json:"last_verification_at,omitempty"`
}

// TwoFAStatsResponse represents 2FA statistics
type TwoFAStatsResponse struct {
	TotalUsers           int     `json:"total_users"`
	UsersWithTwoFA       int     `json:"users_with_2fa"`
	TwoFAEnabledPercent  float64 `json:"2fa_enabled_percent"`
	RecentVerifications  int     `json:"recent_verifications"`
	FailedVerifications  int     `json:"failed_verifications"`
}

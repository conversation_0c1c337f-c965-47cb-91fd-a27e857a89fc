package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaEventPublisher implements EventPublisher using Kafka
type KafkaEventPublisher struct {
	producer *sharedKafka.Producer
	logger   logging.Logger
}

// NewKafkaEventPublisher creates a new Kafka event publisher
func NewKafkaEventPublisher(producer *sharedKafka.Producer, logger logging.Logger) *KafkaEventPublisher {
	return &KafkaEventPublisher{
		producer: producer,
		logger:   logger,
	}
}

// PublishEvent publishes an event to Kafka topic
func (k *KafkaEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	k.logger.WithFields(map[string]interface{}{
		"topic":      topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Publishing event to Kafka")

	// Convert event to BaseEvent for shared Kafka
	baseEvent := k.convertToBaseEvent(event)

	// Use shared Kafka producer to publish to specific topic
	err := k.producer.PublishToTopic(ctx, topic, baseEvent)
	if err != nil {
		k.logger.WithError(err).WithField("topic", topic).Error("Failed to publish event to Kafka")
		return fmt.Errorf("failed to publish event to Kafka: %w", err)
	}

	k.logger.WithFields(map[string]interface{}{
		"topic":    topic,
		"event_id": baseEvent.ID,
	}).Info("Successfully published event to Kafka")

	return nil
}

// MockEventPublisher implements EventPublisher for testing
type MockEventPublisher struct {
	logger logging.Logger
	events []PublishedEvent
}

// PublishedEvent represents a published event for testing
type PublishedEvent struct {
	Topic string
	Event interface{}
}

// NewMockEventPublisher creates a new mock event publisher
func NewMockEventPublisher(logger logging.Logger) *MockEventPublisher {
	return &MockEventPublisher{
		logger: logger,
		events: make([]PublishedEvent, 0),
	}
}

// PublishEvent mocks event publishing
func (m *MockEventPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	m.logger.WithFields(map[string]interface{}{
		"topic":      topic,
		"event_type": fmt.Sprintf("%T", event),
	}).Info("Mock publishing event")

	// Store event for testing
	m.events = append(m.events, PublishedEvent{
		Topic: topic,
		Event: event,
	})

	return nil
}

// GetPublishedEvents returns all published events (for testing)
func (m *MockEventPublisher) GetPublishedEvents() []PublishedEvent {
	return m.events
}

// ClearEvents clears all published events (for testing)
func (m *MockEventPublisher) ClearEvents() {
	m.events = make([]PublishedEvent, 0)
}

// convertToBaseEvent converts any event to BaseEvent for shared Kafka
func (k *KafkaEventPublisher) convertToBaseEvent(event interface{}) *sharedKafka.BaseEvent {
	switch e := event.(type) {
	case *SubscriptionUpdateEvent:
		// Create BaseEvent for subscription update
		baseEvent := &sharedKafka.BaseEvent{
			ID:        e.EventID,
			Type:      sharedKafka.EventType(e.EventType),
			Source:    "credit-service",
			Version:   "1.0",
			Timestamp: k.parseTimestamp(e.ProcessedAt),
			Priority:  sharedKafka.PriorityNormal,
			Data:      make(map[string]interface{}),
			Metadata:  sharedKafka.EventMetadata{},
		}

		// Copy event data
		for key, value := range e.EventData {
			baseEvent.Data[key] = value
		}

		// Add subscription-specific data
		if e.SubscriptionID != "" {
			baseEvent.Data["subscription_id"] = e.SubscriptionID
		}
		if e.UserID != "" {
			baseEvent.Data["user_id"] = e.UserID
		}
		if e.PaymentID != "" {
			baseEvent.Data["payment_id"] = e.PaymentID
		}
		if e.Status != "" {
			baseEvent.Data["status"] = e.Status
		}
		if e.Amount > 0 {
			baseEvent.Data["amount"] = e.Amount
		}
		if e.Currency != "" {
			baseEvent.Data["currency"] = e.Currency
		}

		return baseEvent

	default:
		// Generic conversion for unknown event types
		eventData, _ := json.Marshal(event)
		var data map[string]interface{}
		json.Unmarshal(eventData, &data)

		return &sharedKafka.BaseEvent{
			ID:        fmt.Sprintf("event-%d", time.Now().Unix()),
			Type:      sharedKafka.EventType(fmt.Sprintf("%T", event)),
			Source:    "credit-service",
			Version:   "1.0",
			Timestamp: time.Now(),
			Priority:  sharedKafka.PriorityNormal,
			Data:      data,
			Metadata:  sharedKafka.EventMetadata{},
		}
	}
}

// parseTimestamp parses timestamp string to time.Time
func (k *KafkaEventPublisher) parseTimestamp(timestampStr string) time.Time {
	if timestampStr == "" {
		return time.Now()
	}

	// Try to parse RFC3339 format
	if t, err := time.Parse(time.RFC3339, timestampStr); err == nil {
		return t
	}

	// Fallback to current time
	return time.Now()
}

// Code generated by ent, DO NOT EDIT.

package referral

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldID, id))
}

// ReferrerID applies equality check predicate on the "referrer_id" field. It's identical to ReferrerIDEQ.
func ReferrerID(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferrerID, v))
}

// ReferredID applies equality check predicate on the "referred_id" field. It's identical to ReferredIDEQ.
func ReferredID(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredID, v))
}

// ReferralCode applies equality check predicate on the "referral_code" field. It's identical to ReferralCodeEQ.
func ReferralCode(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferralCode, v))
}

// ReferredEmail applies equality check predicate on the "referred_email" field. It's identical to ReferredEmailEQ.
func ReferredEmail(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredEmail, v))
}

// ReferrerCreditsEarned applies equality check predicate on the "referrer_credits_earned" field. It's identical to ReferrerCreditsEarnedEQ.
func ReferrerCreditsEarned(v int) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferrerCreditsEarned, v))
}

// ReferredCreditsEarned applies equality check predicate on the "referred_credits_earned" field. It's identical to ReferredCreditsEarnedEQ.
func ReferredCreditsEarned(v int) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredCreditsEarned, v))
}

// ReferralType applies equality check predicate on the "referral_type" field. It's identical to ReferralTypeEQ.
func ReferralType(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferralType, v))
}

// CompletedAt applies equality check predicate on the "completed_at" field. It's identical to CompletedAtEQ.
func CompletedAt(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCompletedAt, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldExpiresAt, v))
}

// CampaignID applies equality check predicate on the "campaign_id" field. It's identical to CampaignIDEQ.
func CampaignID(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCampaignID, v))
}

// Source applies equality check predicate on the "source" field. It's identical to SourceEQ.
func Source(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldSource, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldUpdatedAt, v))
}

// ReferrerIDEQ applies the EQ predicate on the "referrer_id" field.
func ReferrerIDEQ(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferrerID, v))
}

// ReferrerIDNEQ applies the NEQ predicate on the "referrer_id" field.
func ReferrerIDNEQ(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferrerID, v))
}

// ReferrerIDIn applies the In predicate on the "referrer_id" field.
func ReferrerIDIn(vs ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferrerID, vs...))
}

// ReferrerIDNotIn applies the NotIn predicate on the "referrer_id" field.
func ReferrerIDNotIn(vs ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferrerID, vs...))
}

// ReferrerIDGT applies the GT predicate on the "referrer_id" field.
func ReferrerIDGT(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferrerID, v))
}

// ReferrerIDGTE applies the GTE predicate on the "referrer_id" field.
func ReferrerIDGTE(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferrerID, v))
}

// ReferrerIDLT applies the LT predicate on the "referrer_id" field.
func ReferrerIDLT(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferrerID, v))
}

// ReferrerIDLTE applies the LTE predicate on the "referrer_id" field.
func ReferrerIDLTE(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferrerID, v))
}

// ReferredIDEQ applies the EQ predicate on the "referred_id" field.
func ReferredIDEQ(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredID, v))
}

// ReferredIDNEQ applies the NEQ predicate on the "referred_id" field.
func ReferredIDNEQ(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferredID, v))
}

// ReferredIDIn applies the In predicate on the "referred_id" field.
func ReferredIDIn(vs ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferredID, vs...))
}

// ReferredIDNotIn applies the NotIn predicate on the "referred_id" field.
func ReferredIDNotIn(vs ...uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferredID, vs...))
}

// ReferredIDGT applies the GT predicate on the "referred_id" field.
func ReferredIDGT(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferredID, v))
}

// ReferredIDGTE applies the GTE predicate on the "referred_id" field.
func ReferredIDGTE(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferredID, v))
}

// ReferredIDLT applies the LT predicate on the "referred_id" field.
func ReferredIDLT(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferredID, v))
}

// ReferredIDLTE applies the LTE predicate on the "referred_id" field.
func ReferredIDLTE(v uuid.UUID) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferredID, v))
}

// ReferredIDIsNil applies the IsNil predicate on the "referred_id" field.
func ReferredIDIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldReferredID))
}

// ReferredIDNotNil applies the NotNil predicate on the "referred_id" field.
func ReferredIDNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldReferredID))
}

// ReferralCodeEQ applies the EQ predicate on the "referral_code" field.
func ReferralCodeEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferralCode, v))
}

// ReferralCodeNEQ applies the NEQ predicate on the "referral_code" field.
func ReferralCodeNEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferralCode, v))
}

// ReferralCodeIn applies the In predicate on the "referral_code" field.
func ReferralCodeIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferralCode, vs...))
}

// ReferralCodeNotIn applies the NotIn predicate on the "referral_code" field.
func ReferralCodeNotIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferralCode, vs...))
}

// ReferralCodeGT applies the GT predicate on the "referral_code" field.
func ReferralCodeGT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferralCode, v))
}

// ReferralCodeGTE applies the GTE predicate on the "referral_code" field.
func ReferralCodeGTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferralCode, v))
}

// ReferralCodeLT applies the LT predicate on the "referral_code" field.
func ReferralCodeLT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferralCode, v))
}

// ReferralCodeLTE applies the LTE predicate on the "referral_code" field.
func ReferralCodeLTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferralCode, v))
}

// ReferralCodeContains applies the Contains predicate on the "referral_code" field.
func ReferralCodeContains(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContains(FieldReferralCode, v))
}

// ReferralCodeHasPrefix applies the HasPrefix predicate on the "referral_code" field.
func ReferralCodeHasPrefix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasPrefix(FieldReferralCode, v))
}

// ReferralCodeHasSuffix applies the HasSuffix predicate on the "referral_code" field.
func ReferralCodeHasSuffix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasSuffix(FieldReferralCode, v))
}

// ReferralCodeEqualFold applies the EqualFold predicate on the "referral_code" field.
func ReferralCodeEqualFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEqualFold(FieldReferralCode, v))
}

// ReferralCodeContainsFold applies the ContainsFold predicate on the "referral_code" field.
func ReferralCodeContainsFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContainsFold(FieldReferralCode, v))
}

// ReferredEmailEQ applies the EQ predicate on the "referred_email" field.
func ReferredEmailEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredEmail, v))
}

// ReferredEmailNEQ applies the NEQ predicate on the "referred_email" field.
func ReferredEmailNEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferredEmail, v))
}

// ReferredEmailIn applies the In predicate on the "referred_email" field.
func ReferredEmailIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferredEmail, vs...))
}

// ReferredEmailNotIn applies the NotIn predicate on the "referred_email" field.
func ReferredEmailNotIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferredEmail, vs...))
}

// ReferredEmailGT applies the GT predicate on the "referred_email" field.
func ReferredEmailGT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferredEmail, v))
}

// ReferredEmailGTE applies the GTE predicate on the "referred_email" field.
func ReferredEmailGTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferredEmail, v))
}

// ReferredEmailLT applies the LT predicate on the "referred_email" field.
func ReferredEmailLT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferredEmail, v))
}

// ReferredEmailLTE applies the LTE predicate on the "referred_email" field.
func ReferredEmailLTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferredEmail, v))
}

// ReferredEmailContains applies the Contains predicate on the "referred_email" field.
func ReferredEmailContains(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContains(FieldReferredEmail, v))
}

// ReferredEmailHasPrefix applies the HasPrefix predicate on the "referred_email" field.
func ReferredEmailHasPrefix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasPrefix(FieldReferredEmail, v))
}

// ReferredEmailHasSuffix applies the HasSuffix predicate on the "referred_email" field.
func ReferredEmailHasSuffix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasSuffix(FieldReferredEmail, v))
}

// ReferredEmailIsNil applies the IsNil predicate on the "referred_email" field.
func ReferredEmailIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldReferredEmail))
}

// ReferredEmailNotNil applies the NotNil predicate on the "referred_email" field.
func ReferredEmailNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldReferredEmail))
}

// ReferredEmailEqualFold applies the EqualFold predicate on the "referred_email" field.
func ReferredEmailEqualFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEqualFold(FieldReferredEmail, v))
}

// ReferredEmailContainsFold applies the ContainsFold predicate on the "referred_email" field.
func ReferredEmailContainsFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContainsFold(FieldReferredEmail, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldStatus, vs...))
}

// ReferrerCreditsEarnedEQ applies the EQ predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedEQ(v int) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferrerCreditsEarned, v))
}

// ReferrerCreditsEarnedNEQ applies the NEQ predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedNEQ(v int) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferrerCreditsEarned, v))
}

// ReferrerCreditsEarnedIn applies the In predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedIn(vs ...int) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferrerCreditsEarned, vs...))
}

// ReferrerCreditsEarnedNotIn applies the NotIn predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedNotIn(vs ...int) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferrerCreditsEarned, vs...))
}

// ReferrerCreditsEarnedGT applies the GT predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedGT(v int) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferrerCreditsEarned, v))
}

// ReferrerCreditsEarnedGTE applies the GTE predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedGTE(v int) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferrerCreditsEarned, v))
}

// ReferrerCreditsEarnedLT applies the LT predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedLT(v int) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferrerCreditsEarned, v))
}

// ReferrerCreditsEarnedLTE applies the LTE predicate on the "referrer_credits_earned" field.
func ReferrerCreditsEarnedLTE(v int) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferrerCreditsEarned, v))
}

// ReferredCreditsEarnedEQ applies the EQ predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedEQ(v int) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferredCreditsEarned, v))
}

// ReferredCreditsEarnedNEQ applies the NEQ predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedNEQ(v int) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferredCreditsEarned, v))
}

// ReferredCreditsEarnedIn applies the In predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedIn(vs ...int) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferredCreditsEarned, vs...))
}

// ReferredCreditsEarnedNotIn applies the NotIn predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedNotIn(vs ...int) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferredCreditsEarned, vs...))
}

// ReferredCreditsEarnedGT applies the GT predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedGT(v int) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferredCreditsEarned, v))
}

// ReferredCreditsEarnedGTE applies the GTE predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedGTE(v int) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferredCreditsEarned, v))
}

// ReferredCreditsEarnedLT applies the LT predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedLT(v int) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferredCreditsEarned, v))
}

// ReferredCreditsEarnedLTE applies the LTE predicate on the "referred_credits_earned" field.
func ReferredCreditsEarnedLTE(v int) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferredCreditsEarned, v))
}

// ReferralTypeEQ applies the EQ predicate on the "referral_type" field.
func ReferralTypeEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldReferralType, v))
}

// ReferralTypeNEQ applies the NEQ predicate on the "referral_type" field.
func ReferralTypeNEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldReferralType, v))
}

// ReferralTypeIn applies the In predicate on the "referral_type" field.
func ReferralTypeIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldReferralType, vs...))
}

// ReferralTypeNotIn applies the NotIn predicate on the "referral_type" field.
func ReferralTypeNotIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldReferralType, vs...))
}

// ReferralTypeGT applies the GT predicate on the "referral_type" field.
func ReferralTypeGT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldReferralType, v))
}

// ReferralTypeGTE applies the GTE predicate on the "referral_type" field.
func ReferralTypeGTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldReferralType, v))
}

// ReferralTypeLT applies the LT predicate on the "referral_type" field.
func ReferralTypeLT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldReferralType, v))
}

// ReferralTypeLTE applies the LTE predicate on the "referral_type" field.
func ReferralTypeLTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldReferralType, v))
}

// ReferralTypeContains applies the Contains predicate on the "referral_type" field.
func ReferralTypeContains(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContains(FieldReferralType, v))
}

// ReferralTypeHasPrefix applies the HasPrefix predicate on the "referral_type" field.
func ReferralTypeHasPrefix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasPrefix(FieldReferralType, v))
}

// ReferralTypeHasSuffix applies the HasSuffix predicate on the "referral_type" field.
func ReferralTypeHasSuffix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasSuffix(FieldReferralType, v))
}

// ReferralTypeEqualFold applies the EqualFold predicate on the "referral_type" field.
func ReferralTypeEqualFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEqualFold(FieldReferralType, v))
}

// ReferralTypeContainsFold applies the ContainsFold predicate on the "referral_type" field.
func ReferralTypeContainsFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContainsFold(FieldReferralType, v))
}

// CompletedAtEQ applies the EQ predicate on the "completed_at" field.
func CompletedAtEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCompletedAt, v))
}

// CompletedAtNEQ applies the NEQ predicate on the "completed_at" field.
func CompletedAtNEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldCompletedAt, v))
}

// CompletedAtIn applies the In predicate on the "completed_at" field.
func CompletedAtIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldCompletedAt, vs...))
}

// CompletedAtNotIn applies the NotIn predicate on the "completed_at" field.
func CompletedAtNotIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldCompletedAt, vs...))
}

// CompletedAtGT applies the GT predicate on the "completed_at" field.
func CompletedAtGT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldCompletedAt, v))
}

// CompletedAtGTE applies the GTE predicate on the "completed_at" field.
func CompletedAtGTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldCompletedAt, v))
}

// CompletedAtLT applies the LT predicate on the "completed_at" field.
func CompletedAtLT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldCompletedAt, v))
}

// CompletedAtLTE applies the LTE predicate on the "completed_at" field.
func CompletedAtLTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldCompletedAt, v))
}

// CompletedAtIsNil applies the IsNil predicate on the "completed_at" field.
func CompletedAtIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldCompletedAt))
}

// CompletedAtNotNil applies the NotNil predicate on the "completed_at" field.
func CompletedAtNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldCompletedAt))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldExpiresAt, v))
}

// ExpiresAtIsNil applies the IsNil predicate on the "expires_at" field.
func ExpiresAtIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldExpiresAt))
}

// ExpiresAtNotNil applies the NotNil predicate on the "expires_at" field.
func ExpiresAtNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldExpiresAt))
}

// ReferralConditionsIsNil applies the IsNil predicate on the "referral_conditions" field.
func ReferralConditionsIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldReferralConditions))
}

// ReferralConditionsNotNil applies the NotNil predicate on the "referral_conditions" field.
func ReferralConditionsNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldReferralConditions))
}

// TrackingDataIsNil applies the IsNil predicate on the "tracking_data" field.
func TrackingDataIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldTrackingData))
}

// TrackingDataNotNil applies the NotNil predicate on the "tracking_data" field.
func TrackingDataNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldTrackingData))
}

// CampaignIDEQ applies the EQ predicate on the "campaign_id" field.
func CampaignIDEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCampaignID, v))
}

// CampaignIDNEQ applies the NEQ predicate on the "campaign_id" field.
func CampaignIDNEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldCampaignID, v))
}

// CampaignIDIn applies the In predicate on the "campaign_id" field.
func CampaignIDIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldCampaignID, vs...))
}

// CampaignIDNotIn applies the NotIn predicate on the "campaign_id" field.
func CampaignIDNotIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldCampaignID, vs...))
}

// CampaignIDGT applies the GT predicate on the "campaign_id" field.
func CampaignIDGT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldCampaignID, v))
}

// CampaignIDGTE applies the GTE predicate on the "campaign_id" field.
func CampaignIDGTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldCampaignID, v))
}

// CampaignIDLT applies the LT predicate on the "campaign_id" field.
func CampaignIDLT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldCampaignID, v))
}

// CampaignIDLTE applies the LTE predicate on the "campaign_id" field.
func CampaignIDLTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldCampaignID, v))
}

// CampaignIDContains applies the Contains predicate on the "campaign_id" field.
func CampaignIDContains(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContains(FieldCampaignID, v))
}

// CampaignIDHasPrefix applies the HasPrefix predicate on the "campaign_id" field.
func CampaignIDHasPrefix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasPrefix(FieldCampaignID, v))
}

// CampaignIDHasSuffix applies the HasSuffix predicate on the "campaign_id" field.
func CampaignIDHasSuffix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasSuffix(FieldCampaignID, v))
}

// CampaignIDIsNil applies the IsNil predicate on the "campaign_id" field.
func CampaignIDIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldCampaignID))
}

// CampaignIDNotNil applies the NotNil predicate on the "campaign_id" field.
func CampaignIDNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldCampaignID))
}

// CampaignIDEqualFold applies the EqualFold predicate on the "campaign_id" field.
func CampaignIDEqualFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEqualFold(FieldCampaignID, v))
}

// CampaignIDContainsFold applies the ContainsFold predicate on the "campaign_id" field.
func CampaignIDContainsFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContainsFold(FieldCampaignID, v))
}

// SourceEQ applies the EQ predicate on the "source" field.
func SourceEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldSource, v))
}

// SourceNEQ applies the NEQ predicate on the "source" field.
func SourceNEQ(v string) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldSource, v))
}

// SourceIn applies the In predicate on the "source" field.
func SourceIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldSource, vs...))
}

// SourceNotIn applies the NotIn predicate on the "source" field.
func SourceNotIn(vs ...string) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldSource, vs...))
}

// SourceGT applies the GT predicate on the "source" field.
func SourceGT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldSource, v))
}

// SourceGTE applies the GTE predicate on the "source" field.
func SourceGTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldSource, v))
}

// SourceLT applies the LT predicate on the "source" field.
func SourceLT(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldSource, v))
}

// SourceLTE applies the LTE predicate on the "source" field.
func SourceLTE(v string) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldSource, v))
}

// SourceContains applies the Contains predicate on the "source" field.
func SourceContains(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContains(FieldSource, v))
}

// SourceHasPrefix applies the HasPrefix predicate on the "source" field.
func SourceHasPrefix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasPrefix(FieldSource, v))
}

// SourceHasSuffix applies the HasSuffix predicate on the "source" field.
func SourceHasSuffix(v string) predicate.Referral {
	return predicate.Referral(sql.FieldHasSuffix(FieldSource, v))
}

// SourceIsNil applies the IsNil predicate on the "source" field.
func SourceIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldSource))
}

// SourceNotNil applies the NotNil predicate on the "source" field.
func SourceNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldSource))
}

// SourceEqualFold applies the EqualFold predicate on the "source" field.
func SourceEqualFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldEqualFold(FieldSource, v))
}

// SourceContainsFold applies the ContainsFold predicate on the "source" field.
func SourceContainsFold(v string) predicate.Referral {
	return predicate.Referral(sql.FieldContainsFold(FieldSource, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Referral {
	return predicate.Referral(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Referral {
	return predicate.Referral(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Referral {
	return predicate.Referral(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Referral) predicate.Referral {
	return predicate.Referral(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Referral) predicate.Referral {
	return predicate.Referral(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Referral) predicate.Referral {
	return predicate.Referral(sql.NotPredicates(p))
}

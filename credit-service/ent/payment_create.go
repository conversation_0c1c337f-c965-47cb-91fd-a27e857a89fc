// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/payment"
)

// PaymentCreate is the builder for creating a Payment entity.
type PaymentCreate struct {
	config
	mutation *PaymentMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (pc *PaymentCreate) SetUserID(u uuid.UUID) *PaymentCreate {
	pc.mutation.SetUserID(u)
	return pc
}

// SetOrderID sets the "order_id" field.
func (pc *PaymentCreate) SetOrderID(u uuid.UUID) *PaymentCreate {
	pc.mutation.SetOrderID(u)
	return pc
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableOrderID(u *uuid.UUID) *PaymentCreate {
	if u != nil {
		pc.SetOrderID(*u)
	}
	return pc
}

// SetSubscriptionID sets the "subscription_id" field.
func (pc *PaymentCreate) SetSubscriptionID(u uuid.UUID) *PaymentCreate {
	pc.mutation.SetSubscriptionID(u)
	return pc
}

// SetNillableSubscriptionID sets the "subscription_id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableSubscriptionID(u *uuid.UUID) *PaymentCreate {
	if u != nil {
		pc.SetSubscriptionID(*u)
	}
	return pc
}

// SetPaymentMethod sets the "payment_method" field.
func (pc *PaymentCreate) SetPaymentMethod(s string) *PaymentCreate {
	pc.mutation.SetPaymentMethod(s)
	return pc
}

// SetPaymentProvider sets the "payment_provider" field.
func (pc *PaymentCreate) SetPaymentProvider(s string) *PaymentCreate {
	pc.mutation.SetPaymentProvider(s)
	return pc
}

// SetExternalPaymentID sets the "external_payment_id" field.
func (pc *PaymentCreate) SetExternalPaymentID(s string) *PaymentCreate {
	pc.mutation.SetExternalPaymentID(s)
	return pc
}

// SetNillableExternalPaymentID sets the "external_payment_id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableExternalPaymentID(s *string) *PaymentCreate {
	if s != nil {
		pc.SetExternalPaymentID(*s)
	}
	return pc
}

// SetAmount sets the "amount" field.
func (pc *PaymentCreate) SetAmount(i int64) *PaymentCreate {
	pc.mutation.SetAmount(i)
	return pc
}

// SetCurrency sets the "currency" field.
func (pc *PaymentCreate) SetCurrency(s string) *PaymentCreate {
	pc.mutation.SetCurrency(s)
	return pc
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableCurrency(s *string) *PaymentCreate {
	if s != nil {
		pc.SetCurrency(*s)
	}
	return pc
}

// SetStatus sets the "status" field.
func (pc *PaymentCreate) SetStatus(pa payment.Status) *PaymentCreate {
	pc.mutation.SetStatus(pa)
	return pc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableStatus(pa *payment.Status) *PaymentCreate {
	if pa != nil {
		pc.SetStatus(*pa)
	}
	return pc
}

// SetPaymentIntentID sets the "payment_intent_id" field.
func (pc *PaymentCreate) SetPaymentIntentID(s string) *PaymentCreate {
	pc.mutation.SetPaymentIntentID(s)
	return pc
}

// SetNillablePaymentIntentID sets the "payment_intent_id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillablePaymentIntentID(s *string) *PaymentCreate {
	if s != nil {
		pc.SetPaymentIntentID(*s)
	}
	return pc
}

// SetChargeID sets the "charge_id" field.
func (pc *PaymentCreate) SetChargeID(s string) *PaymentCreate {
	pc.mutation.SetChargeID(s)
	return pc
}

// SetNillableChargeID sets the "charge_id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableChargeID(s *string) *PaymentCreate {
	if s != nil {
		pc.SetChargeID(*s)
	}
	return pc
}

// SetBillingDetails sets the "billing_details" field.
func (pc *PaymentCreate) SetBillingDetails(m map[string]interface{}) *PaymentCreate {
	pc.mutation.SetBillingDetails(m)
	return pc
}

// SetPaymentMethodDetails sets the "payment_method_details" field.
func (pc *PaymentCreate) SetPaymentMethodDetails(m map[string]interface{}) *PaymentCreate {
	pc.mutation.SetPaymentMethodDetails(m)
	return pc
}

// SetPaidAt sets the "paid_at" field.
func (pc *PaymentCreate) SetPaidAt(t time.Time) *PaymentCreate {
	pc.mutation.SetPaidAt(t)
	return pc
}

// SetNillablePaidAt sets the "paid_at" field if the given value is not nil.
func (pc *PaymentCreate) SetNillablePaidAt(t *time.Time) *PaymentCreate {
	if t != nil {
		pc.SetPaidAt(*t)
	}
	return pc
}

// SetFailedAt sets the "failed_at" field.
func (pc *PaymentCreate) SetFailedAt(t time.Time) *PaymentCreate {
	pc.mutation.SetFailedAt(t)
	return pc
}

// SetNillableFailedAt sets the "failed_at" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableFailedAt(t *time.Time) *PaymentCreate {
	if t != nil {
		pc.SetFailedAt(*t)
	}
	return pc
}

// SetFailureCode sets the "failure_code" field.
func (pc *PaymentCreate) SetFailureCode(s string) *PaymentCreate {
	pc.mutation.SetFailureCode(s)
	return pc
}

// SetNillableFailureCode sets the "failure_code" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableFailureCode(s *string) *PaymentCreate {
	if s != nil {
		pc.SetFailureCode(*s)
	}
	return pc
}

// SetFailureMessage sets the "failure_message" field.
func (pc *PaymentCreate) SetFailureMessage(s string) *PaymentCreate {
	pc.mutation.SetFailureMessage(s)
	return pc
}

// SetNillableFailureMessage sets the "failure_message" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableFailureMessage(s *string) *PaymentCreate {
	if s != nil {
		pc.SetFailureMessage(*s)
	}
	return pc
}

// SetRefundedAmount sets the "refunded_amount" field.
func (pc *PaymentCreate) SetRefundedAmount(i int64) *PaymentCreate {
	pc.mutation.SetRefundedAmount(i)
	return pc
}

// SetNillableRefundedAmount sets the "refunded_amount" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableRefundedAmount(i *int64) *PaymentCreate {
	if i != nil {
		pc.SetRefundedAmount(*i)
	}
	return pc
}

// SetRefundedAt sets the "refunded_at" field.
func (pc *PaymentCreate) SetRefundedAt(t time.Time) *PaymentCreate {
	pc.mutation.SetRefundedAt(t)
	return pc
}

// SetNillableRefundedAt sets the "refunded_at" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableRefundedAt(t *time.Time) *PaymentCreate {
	if t != nil {
		pc.SetRefundedAt(*t)
	}
	return pc
}

// SetRefundReason sets the "refund_reason" field.
func (pc *PaymentCreate) SetRefundReason(s string) *PaymentCreate {
	pc.mutation.SetRefundReason(s)
	return pc
}

// SetNillableRefundReason sets the "refund_reason" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableRefundReason(s *string) *PaymentCreate {
	if s != nil {
		pc.SetRefundReason(*s)
	}
	return pc
}

// SetWebhookData sets the "webhook_data" field.
func (pc *PaymentCreate) SetWebhookData(m map[string]interface{}) *PaymentCreate {
	pc.mutation.SetWebhookData(m)
	return pc
}

// SetMetadata sets the "metadata" field.
func (pc *PaymentCreate) SetMetadata(m map[string]interface{}) *PaymentCreate {
	pc.mutation.SetMetadata(m)
	return pc
}

// SetCreatedAt sets the "created_at" field.
func (pc *PaymentCreate) SetCreatedAt(t time.Time) *PaymentCreate {
	pc.mutation.SetCreatedAt(t)
	return pc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableCreatedAt(t *time.Time) *PaymentCreate {
	if t != nil {
		pc.SetCreatedAt(*t)
	}
	return pc
}

// SetUpdatedAt sets the "updated_at" field.
func (pc *PaymentCreate) SetUpdatedAt(t time.Time) *PaymentCreate {
	pc.mutation.SetUpdatedAt(t)
	return pc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableUpdatedAt(t *time.Time) *PaymentCreate {
	if t != nil {
		pc.SetUpdatedAt(*t)
	}
	return pc
}

// SetID sets the "id" field.
func (pc *PaymentCreate) SetID(u uuid.UUID) *PaymentCreate {
	pc.mutation.SetID(u)
	return pc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (pc *PaymentCreate) SetNillableID(u *uuid.UUID) *PaymentCreate {
	if u != nil {
		pc.SetID(*u)
	}
	return pc
}

// Mutation returns the PaymentMutation object of the builder.
func (pc *PaymentCreate) Mutation() *PaymentMutation {
	return pc.mutation
}

// Save creates the Payment in the database.
func (pc *PaymentCreate) Save(ctx context.Context) (*Payment, error) {
	pc.defaults()
	return withHooks(ctx, pc.sqlSave, pc.mutation, pc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pc *PaymentCreate) SaveX(ctx context.Context) *Payment {
	v, err := pc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pc *PaymentCreate) Exec(ctx context.Context) error {
	_, err := pc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pc *PaymentCreate) ExecX(ctx context.Context) {
	if err := pc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pc *PaymentCreate) defaults() {
	if _, ok := pc.mutation.Currency(); !ok {
		v := payment.DefaultCurrency
		pc.mutation.SetCurrency(v)
	}
	if _, ok := pc.mutation.Status(); !ok {
		v := payment.DefaultStatus
		pc.mutation.SetStatus(v)
	}
	if _, ok := pc.mutation.RefundedAmount(); !ok {
		v := payment.DefaultRefundedAmount
		pc.mutation.SetRefundedAmount(v)
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		v := payment.DefaultCreatedAt()
		pc.mutation.SetCreatedAt(v)
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		v := payment.DefaultUpdatedAt()
		pc.mutation.SetUpdatedAt(v)
	}
	if _, ok := pc.mutation.ID(); !ok {
		v := payment.DefaultID()
		pc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pc *PaymentCreate) check() error {
	if _, ok := pc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Payment.user_id"`)}
	}
	if _, ok := pc.mutation.PaymentMethod(); !ok {
		return &ValidationError{Name: "payment_method", err: errors.New(`ent: missing required field "Payment.payment_method"`)}
	}
	if v, ok := pc.mutation.PaymentMethod(); ok {
		if err := payment.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_method": %w`, err)}
		}
	}
	if _, ok := pc.mutation.PaymentProvider(); !ok {
		return &ValidationError{Name: "payment_provider", err: errors.New(`ent: missing required field "Payment.payment_provider"`)}
	}
	if v, ok := pc.mutation.PaymentProvider(); ok {
		if err := payment.PaymentProviderValidator(v); err != nil {
			return &ValidationError{Name: "payment_provider", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_provider": %w`, err)}
		}
	}
	if v, ok := pc.mutation.ExternalPaymentID(); ok {
		if err := payment.ExternalPaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "external_payment_id", err: fmt.Errorf(`ent: validator failed for field "Payment.external_payment_id": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`ent: missing required field "Payment.amount"`)}
	}
	if v, ok := pc.mutation.Amount(); ok {
		if err := payment.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Payment.amount": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "Payment.currency"`)}
	}
	if v, ok := pc.mutation.Currency(); ok {
		if err := payment.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Payment.currency": %w`, err)}
		}
	}
	if _, ok := pc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Payment.status"`)}
	}
	if v, ok := pc.mutation.Status(); ok {
		if err := payment.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Payment.status": %w`, err)}
		}
	}
	if v, ok := pc.mutation.PaymentIntentID(); ok {
		if err := payment.PaymentIntentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_intent_id", err: fmt.Errorf(`ent: validator failed for field "Payment.payment_intent_id": %w`, err)}
		}
	}
	if v, ok := pc.mutation.ChargeID(); ok {
		if err := payment.ChargeIDValidator(v); err != nil {
			return &ValidationError{Name: "charge_id", err: fmt.Errorf(`ent: validator failed for field "Payment.charge_id": %w`, err)}
		}
	}
	if v, ok := pc.mutation.FailureCode(); ok {
		if err := payment.FailureCodeValidator(v); err != nil {
			return &ValidationError{Name: "failure_code", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_code": %w`, err)}
		}
	}
	if v, ok := pc.mutation.FailureMessage(); ok {
		if err := payment.FailureMessageValidator(v); err != nil {
			return &ValidationError{Name: "failure_message", err: fmt.Errorf(`ent: validator failed for field "Payment.failure_message": %w`, err)}
		}
	}
	if _, ok := pc.mutation.RefundedAmount(); !ok {
		return &ValidationError{Name: "refunded_amount", err: errors.New(`ent: missing required field "Payment.refunded_amount"`)}
	}
	if v, ok := pc.mutation.RefundedAmount(); ok {
		if err := payment.RefundedAmountValidator(v); err != nil {
			return &ValidationError{Name: "refunded_amount", err: fmt.Errorf(`ent: validator failed for field "Payment.refunded_amount": %w`, err)}
		}
	}
	if v, ok := pc.mutation.RefundReason(); ok {
		if err := payment.RefundReasonValidator(v); err != nil {
			return &ValidationError{Name: "refund_reason", err: fmt.Errorf(`ent: validator failed for field "Payment.refund_reason": %w`, err)}
		}
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Payment.created_at"`)}
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Payment.updated_at"`)}
	}
	return nil
}

func (pc *PaymentCreate) sqlSave(ctx context.Context) (*Payment, error) {
	if err := pc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	pc.mutation.id = &_node.ID
	pc.mutation.done = true
	return _node, nil
}

func (pc *PaymentCreate) createSpec() (*Payment, *sqlgraph.CreateSpec) {
	var (
		_node = &Payment{config: pc.config}
		_spec = sqlgraph.NewCreateSpec(payment.Table, sqlgraph.NewFieldSpec(payment.FieldID, field.TypeUUID))
	)
	if id, ok := pc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := pc.mutation.UserID(); ok {
		_spec.SetField(payment.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := pc.mutation.OrderID(); ok {
		_spec.SetField(payment.FieldOrderID, field.TypeUUID, value)
		_node.OrderID = value
	}
	if value, ok := pc.mutation.SubscriptionID(); ok {
		_spec.SetField(payment.FieldSubscriptionID, field.TypeUUID, value)
		_node.SubscriptionID = value
	}
	if value, ok := pc.mutation.PaymentMethod(); ok {
		_spec.SetField(payment.FieldPaymentMethod, field.TypeString, value)
		_node.PaymentMethod = value
	}
	if value, ok := pc.mutation.PaymentProvider(); ok {
		_spec.SetField(payment.FieldPaymentProvider, field.TypeString, value)
		_node.PaymentProvider = value
	}
	if value, ok := pc.mutation.ExternalPaymentID(); ok {
		_spec.SetField(payment.FieldExternalPaymentID, field.TypeString, value)
		_node.ExternalPaymentID = value
	}
	if value, ok := pc.mutation.Amount(); ok {
		_spec.SetField(payment.FieldAmount, field.TypeInt64, value)
		_node.Amount = value
	}
	if value, ok := pc.mutation.Currency(); ok {
		_spec.SetField(payment.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := pc.mutation.Status(); ok {
		_spec.SetField(payment.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := pc.mutation.PaymentIntentID(); ok {
		_spec.SetField(payment.FieldPaymentIntentID, field.TypeString, value)
		_node.PaymentIntentID = value
	}
	if value, ok := pc.mutation.ChargeID(); ok {
		_spec.SetField(payment.FieldChargeID, field.TypeString, value)
		_node.ChargeID = value
	}
	if value, ok := pc.mutation.BillingDetails(); ok {
		_spec.SetField(payment.FieldBillingDetails, field.TypeJSON, value)
		_node.BillingDetails = value
	}
	if value, ok := pc.mutation.PaymentMethodDetails(); ok {
		_spec.SetField(payment.FieldPaymentMethodDetails, field.TypeJSON, value)
		_node.PaymentMethodDetails = value
	}
	if value, ok := pc.mutation.PaidAt(); ok {
		_spec.SetField(payment.FieldPaidAt, field.TypeTime, value)
		_node.PaidAt = value
	}
	if value, ok := pc.mutation.FailedAt(); ok {
		_spec.SetField(payment.FieldFailedAt, field.TypeTime, value)
		_node.FailedAt = value
	}
	if value, ok := pc.mutation.FailureCode(); ok {
		_spec.SetField(payment.FieldFailureCode, field.TypeString, value)
		_node.FailureCode = value
	}
	if value, ok := pc.mutation.FailureMessage(); ok {
		_spec.SetField(payment.FieldFailureMessage, field.TypeString, value)
		_node.FailureMessage = value
	}
	if value, ok := pc.mutation.RefundedAmount(); ok {
		_spec.SetField(payment.FieldRefundedAmount, field.TypeInt64, value)
		_node.RefundedAmount = value
	}
	if value, ok := pc.mutation.RefundedAt(); ok {
		_spec.SetField(payment.FieldRefundedAt, field.TypeTime, value)
		_node.RefundedAt = value
	}
	if value, ok := pc.mutation.RefundReason(); ok {
		_spec.SetField(payment.FieldRefundReason, field.TypeString, value)
		_node.RefundReason = value
	}
	if value, ok := pc.mutation.WebhookData(); ok {
		_spec.SetField(payment.FieldWebhookData, field.TypeJSON, value)
		_node.WebhookData = value
	}
	if value, ok := pc.mutation.Metadata(); ok {
		_spec.SetField(payment.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := pc.mutation.CreatedAt(); ok {
		_spec.SetField(payment.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pc.mutation.UpdatedAt(); ok {
		_spec.SetField(payment.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// PaymentCreateBulk is the builder for creating many Payment entities in bulk.
type PaymentCreateBulk struct {
	config
	err      error
	builders []*PaymentCreate
}

// Save creates the Payment entities in the database.
func (pcb *PaymentCreateBulk) Save(ctx context.Context) ([]*Payment, error) {
	if pcb.err != nil {
		return nil, pcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pcb.builders))
	nodes := make([]*Payment, len(pcb.builders))
	mutators := make([]Mutator, len(pcb.builders))
	for i := range pcb.builders {
		func(i int, root context.Context) {
			builder := pcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PaymentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pcb *PaymentCreateBulk) SaveX(ctx context.Context) []*Payment {
	v, err := pcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcb *PaymentCreateBulk) Exec(ctx context.Context) error {
	_, err := pcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcb *PaymentCreateBulk) ExecX(ctx context.Context) {
	if err := pcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package notificationpreference

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the notificationpreference type in the database.
	Label = "notification_preference"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldEmailEnabled holds the string denoting the email_enabled field in the database.
	FieldEmailEnabled = "email_enabled"
	// FieldPushEnabled holds the string denoting the push_enabled field in the database.
	FieldPushEnabled = "push_enabled"
	// FieldInAppEnabled holds the string denoting the in_app_enabled field in the database.
	FieldInAppEnabled = "in_app_enabled"
	// FieldSmsEnabled holds the string denoting the sms_enabled field in the database.
	FieldSmsEnabled = "sms_enabled"
	// FieldNotificationTypes holds the string denoting the notification_types field in the database.
	FieldNotificationTypes = "notification_types"
	// FieldQuietHours holds the string denoting the quiet_hours field in the database.
	FieldQuietHours = "quiet_hours"
	// FieldTimezone holds the string denoting the timezone field in the database.
	FieldTimezone = "timezone"
	// FieldLanguage holds the string denoting the language field in the database.
	FieldLanguage = "language"
	// FieldEmailSettings holds the string denoting the email_settings field in the database.
	FieldEmailSettings = "email_settings"
	// FieldPushSettings holds the string denoting the push_settings field in the database.
	FieldPushSettings = "push_settings"
	// FieldFrequencySettings holds the string denoting the frequency_settings field in the database.
	FieldFrequencySettings = "frequency_settings"
	// FieldMarketingEmails holds the string denoting the marketing_emails field in the database.
	FieldMarketingEmails = "marketing_emails"
	// FieldProductUpdates holds the string denoting the product_updates field in the database.
	FieldProductUpdates = "product_updates"
	// FieldSecurityAlerts holds the string denoting the security_alerts field in the database.
	FieldSecurityAlerts = "security_alerts"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the notificationpreference in the database.
	Table = "notification_preferences"
)

// Columns holds all SQL columns for notificationpreference fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldEmailEnabled,
	FieldPushEnabled,
	FieldInAppEnabled,
	FieldSmsEnabled,
	FieldNotificationTypes,
	FieldQuietHours,
	FieldTimezone,
	FieldLanguage,
	FieldEmailSettings,
	FieldPushSettings,
	FieldFrequencySettings,
	FieldMarketingEmails,
	FieldProductUpdates,
	FieldSecurityAlerts,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultEmailEnabled holds the default value on creation for the "email_enabled" field.
	DefaultEmailEnabled bool
	// DefaultPushEnabled holds the default value on creation for the "push_enabled" field.
	DefaultPushEnabled bool
	// DefaultInAppEnabled holds the default value on creation for the "in_app_enabled" field.
	DefaultInAppEnabled bool
	// DefaultSmsEnabled holds the default value on creation for the "sms_enabled" field.
	DefaultSmsEnabled bool
	// DefaultTimezone holds the default value on creation for the "timezone" field.
	DefaultTimezone string
	// TimezoneValidator is a validator for the "timezone" field. It is called by the builders before save.
	TimezoneValidator func(string) error
	// DefaultLanguage holds the default value on creation for the "language" field.
	DefaultLanguage string
	// LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	LanguageValidator func(string) error
	// DefaultMarketingEmails holds the default value on creation for the "marketing_emails" field.
	DefaultMarketingEmails bool
	// DefaultProductUpdates holds the default value on creation for the "product_updates" field.
	DefaultProductUpdates bool
	// DefaultSecurityAlerts holds the default value on creation for the "security_alerts" field.
	DefaultSecurityAlerts bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the NotificationPreference queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByEmailEnabled orders the results by the email_enabled field.
func ByEmailEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmailEnabled, opts...).ToFunc()
}

// ByPushEnabled orders the results by the push_enabled field.
func ByPushEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPushEnabled, opts...).ToFunc()
}

// ByInAppEnabled orders the results by the in_app_enabled field.
func ByInAppEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInAppEnabled, opts...).ToFunc()
}

// BySmsEnabled orders the results by the sms_enabled field.
func BySmsEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSmsEnabled, opts...).ToFunc()
}

// ByTimezone orders the results by the timezone field.
func ByTimezone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTimezone, opts...).ToFunc()
}

// ByLanguage orders the results by the language field.
func ByLanguage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLanguage, opts...).ToFunc()
}

// ByMarketingEmails orders the results by the marketing_emails field.
func ByMarketingEmails(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMarketingEmails, opts...).ToFunc()
}

// ByProductUpdates orders the results by the product_updates field.
func ByProductUpdates(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductUpdates, opts...).ToFunc()
}

// BySecurityAlerts orders the results by the security_alerts field.
func BySecurityAlerts(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecurityAlerts, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

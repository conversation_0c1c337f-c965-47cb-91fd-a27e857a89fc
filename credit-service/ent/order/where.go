// Code generated by ent, DO NOT EDIT.

package order

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldID, id))
}

// OrderNumber applies equality check predicate on the "order_number" field. It's identical to OrderNumberEQ.
func OrderNumber(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldOrderNumber, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUserID, v))
}

// PackageID applies equality check predicate on the "package_id" field. It's identical to PackageIDEQ.
func PackageID(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPackageID, v))
}

// PlanID applies equality check predicate on the "plan_id" field. It's identical to PlanIDEQ.
func PlanID(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPlanID, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldAmount, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCurrency, v))
}

// PaymentMethod applies equality check predicate on the "payment_method" field. It's identical to PaymentMethodEQ.
func PaymentMethod(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentID applies equality check predicate on the "payment_id" field. It's identical to PaymentIDEQ.
func PaymentID(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaymentID, v))
}

// InvoiceID applies equality check predicate on the "invoice_id" field. It's identical to InvoiceIDEQ.
func InvoiceID(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldInvoiceID, v))
}

// Credits applies equality check predicate on the "credits" field. It's identical to CreditsEQ.
func Credits(v int) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCredits, v))
}

// ProcessedAt applies equality check predicate on the "processed_at" field. It's identical to ProcessedAtEQ.
func ProcessedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldProcessedAt, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldExpiresAt, v))
}

// FailureReason applies equality check predicate on the "failure_reason" field. It's identical to FailureReasonEQ.
func FailureReason(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldFailureReason, v))
}

// ExternalOrderID applies equality check predicate on the "external_order_id" field. It's identical to ExternalOrderIDEQ.
func ExternalOrderID(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldExternalOrderID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUpdatedAt, v))
}

// OrderNumberEQ applies the EQ predicate on the "order_number" field.
func OrderNumberEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldOrderNumber, v))
}

// OrderNumberNEQ applies the NEQ predicate on the "order_number" field.
func OrderNumberNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldOrderNumber, v))
}

// OrderNumberIn applies the In predicate on the "order_number" field.
func OrderNumberIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldOrderNumber, vs...))
}

// OrderNumberNotIn applies the NotIn predicate on the "order_number" field.
func OrderNumberNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldOrderNumber, vs...))
}

// OrderNumberGT applies the GT predicate on the "order_number" field.
func OrderNumberGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldOrderNumber, v))
}

// OrderNumberGTE applies the GTE predicate on the "order_number" field.
func OrderNumberGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldOrderNumber, v))
}

// OrderNumberLT applies the LT predicate on the "order_number" field.
func OrderNumberLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldOrderNumber, v))
}

// OrderNumberLTE applies the LTE predicate on the "order_number" field.
func OrderNumberLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldOrderNumber, v))
}

// OrderNumberContains applies the Contains predicate on the "order_number" field.
func OrderNumberContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldOrderNumber, v))
}

// OrderNumberHasPrefix applies the HasPrefix predicate on the "order_number" field.
func OrderNumberHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldOrderNumber, v))
}

// OrderNumberHasSuffix applies the HasSuffix predicate on the "order_number" field.
func OrderNumberHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldOrderNumber, v))
}

// OrderNumberEqualFold applies the EqualFold predicate on the "order_number" field.
func OrderNumberEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldOrderNumber, v))
}

// OrderNumberContainsFold applies the ContainsFold predicate on the "order_number" field.
func OrderNumberContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldOrderNumber, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldUserID, v))
}

// PackageIDEQ applies the EQ predicate on the "package_id" field.
func PackageIDEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPackageID, v))
}

// PackageIDNEQ applies the NEQ predicate on the "package_id" field.
func PackageIDNEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldPackageID, v))
}

// PackageIDIn applies the In predicate on the "package_id" field.
func PackageIDIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldPackageID, vs...))
}

// PackageIDNotIn applies the NotIn predicate on the "package_id" field.
func PackageIDNotIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldPackageID, vs...))
}

// PackageIDGT applies the GT predicate on the "package_id" field.
func PackageIDGT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldPackageID, v))
}

// PackageIDGTE applies the GTE predicate on the "package_id" field.
func PackageIDGTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldPackageID, v))
}

// PackageIDLT applies the LT predicate on the "package_id" field.
func PackageIDLT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldPackageID, v))
}

// PackageIDLTE applies the LTE predicate on the "package_id" field.
func PackageIDLTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldPackageID, v))
}

// PackageIDIsNil applies the IsNil predicate on the "package_id" field.
func PackageIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldPackageID))
}

// PackageIDNotNil applies the NotNil predicate on the "package_id" field.
func PackageIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldPackageID))
}

// PlanIDEQ applies the EQ predicate on the "plan_id" field.
func PlanIDEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPlanID, v))
}

// PlanIDNEQ applies the NEQ predicate on the "plan_id" field.
func PlanIDNEQ(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldPlanID, v))
}

// PlanIDIn applies the In predicate on the "plan_id" field.
func PlanIDIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldPlanID, vs...))
}

// PlanIDNotIn applies the NotIn predicate on the "plan_id" field.
func PlanIDNotIn(vs ...uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldPlanID, vs...))
}

// PlanIDGT applies the GT predicate on the "plan_id" field.
func PlanIDGT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldPlanID, v))
}

// PlanIDGTE applies the GTE predicate on the "plan_id" field.
func PlanIDGTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldPlanID, v))
}

// PlanIDLT applies the LT predicate on the "plan_id" field.
func PlanIDLT(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldPlanID, v))
}

// PlanIDLTE applies the LTE predicate on the "plan_id" field.
func PlanIDLTE(v uuid.UUID) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldPlanID, v))
}

// PlanIDIsNil applies the IsNil predicate on the "plan_id" field.
func PlanIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldPlanID))
}

// PlanIDNotNil applies the NotNil predicate on the "plan_id" field.
func PlanIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldPlanID))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldType, vs...))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v int64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v int64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldAmount, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldCurrency, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldStatus, vs...))
}

// PaymentMethodEQ applies the EQ predicate on the "payment_method" field.
func PaymentMethodEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaymentMethod, v))
}

// PaymentMethodNEQ applies the NEQ predicate on the "payment_method" field.
func PaymentMethodNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldPaymentMethod, v))
}

// PaymentMethodIn applies the In predicate on the "payment_method" field.
func PaymentMethodIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldPaymentMethod, vs...))
}

// PaymentMethodNotIn applies the NotIn predicate on the "payment_method" field.
func PaymentMethodNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldPaymentMethod, vs...))
}

// PaymentMethodGT applies the GT predicate on the "payment_method" field.
func PaymentMethodGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldPaymentMethod, v))
}

// PaymentMethodGTE applies the GTE predicate on the "payment_method" field.
func PaymentMethodGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldPaymentMethod, v))
}

// PaymentMethodLT applies the LT predicate on the "payment_method" field.
func PaymentMethodLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldPaymentMethod, v))
}

// PaymentMethodLTE applies the LTE predicate on the "payment_method" field.
func PaymentMethodLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldPaymentMethod, v))
}

// PaymentMethodContains applies the Contains predicate on the "payment_method" field.
func PaymentMethodContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldPaymentMethod, v))
}

// PaymentMethodHasPrefix applies the HasPrefix predicate on the "payment_method" field.
func PaymentMethodHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldPaymentMethod, v))
}

// PaymentMethodHasSuffix applies the HasSuffix predicate on the "payment_method" field.
func PaymentMethodHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldPaymentMethod, v))
}

// PaymentMethodEqualFold applies the EqualFold predicate on the "payment_method" field.
func PaymentMethodEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldPaymentMethod, v))
}

// PaymentMethodContainsFold applies the ContainsFold predicate on the "payment_method" field.
func PaymentMethodContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldPaymentMethod, v))
}

// PaymentIDEQ applies the EQ predicate on the "payment_id" field.
func PaymentIDEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaymentID, v))
}

// PaymentIDNEQ applies the NEQ predicate on the "payment_id" field.
func PaymentIDNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldPaymentID, v))
}

// PaymentIDIn applies the In predicate on the "payment_id" field.
func PaymentIDIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldPaymentID, vs...))
}

// PaymentIDNotIn applies the NotIn predicate on the "payment_id" field.
func PaymentIDNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldPaymentID, vs...))
}

// PaymentIDGT applies the GT predicate on the "payment_id" field.
func PaymentIDGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldPaymentID, v))
}

// PaymentIDGTE applies the GTE predicate on the "payment_id" field.
func PaymentIDGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldPaymentID, v))
}

// PaymentIDLT applies the LT predicate on the "payment_id" field.
func PaymentIDLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldPaymentID, v))
}

// PaymentIDLTE applies the LTE predicate on the "payment_id" field.
func PaymentIDLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldPaymentID, v))
}

// PaymentIDContains applies the Contains predicate on the "payment_id" field.
func PaymentIDContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldPaymentID, v))
}

// PaymentIDHasPrefix applies the HasPrefix predicate on the "payment_id" field.
func PaymentIDHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldPaymentID, v))
}

// PaymentIDHasSuffix applies the HasSuffix predicate on the "payment_id" field.
func PaymentIDHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldPaymentID, v))
}

// PaymentIDIsNil applies the IsNil predicate on the "payment_id" field.
func PaymentIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldPaymentID))
}

// PaymentIDNotNil applies the NotNil predicate on the "payment_id" field.
func PaymentIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldPaymentID))
}

// PaymentIDEqualFold applies the EqualFold predicate on the "payment_id" field.
func PaymentIDEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldPaymentID, v))
}

// PaymentIDContainsFold applies the ContainsFold predicate on the "payment_id" field.
func PaymentIDContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldPaymentID, v))
}

// InvoiceIDEQ applies the EQ predicate on the "invoice_id" field.
func InvoiceIDEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldInvoiceID, v))
}

// InvoiceIDNEQ applies the NEQ predicate on the "invoice_id" field.
func InvoiceIDNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldInvoiceID, v))
}

// InvoiceIDIn applies the In predicate on the "invoice_id" field.
func InvoiceIDIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldInvoiceID, vs...))
}

// InvoiceIDNotIn applies the NotIn predicate on the "invoice_id" field.
func InvoiceIDNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldInvoiceID, vs...))
}

// InvoiceIDGT applies the GT predicate on the "invoice_id" field.
func InvoiceIDGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldInvoiceID, v))
}

// InvoiceIDGTE applies the GTE predicate on the "invoice_id" field.
func InvoiceIDGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldInvoiceID, v))
}

// InvoiceIDLT applies the LT predicate on the "invoice_id" field.
func InvoiceIDLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldInvoiceID, v))
}

// InvoiceIDLTE applies the LTE predicate on the "invoice_id" field.
func InvoiceIDLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldInvoiceID, v))
}

// InvoiceIDContains applies the Contains predicate on the "invoice_id" field.
func InvoiceIDContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldInvoiceID, v))
}

// InvoiceIDHasPrefix applies the HasPrefix predicate on the "invoice_id" field.
func InvoiceIDHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldInvoiceID, v))
}

// InvoiceIDHasSuffix applies the HasSuffix predicate on the "invoice_id" field.
func InvoiceIDHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldInvoiceID, v))
}

// InvoiceIDIsNil applies the IsNil predicate on the "invoice_id" field.
func InvoiceIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldInvoiceID))
}

// InvoiceIDNotNil applies the NotNil predicate on the "invoice_id" field.
func InvoiceIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldInvoiceID))
}

// InvoiceIDEqualFold applies the EqualFold predicate on the "invoice_id" field.
func InvoiceIDEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldInvoiceID, v))
}

// InvoiceIDContainsFold applies the ContainsFold predicate on the "invoice_id" field.
func InvoiceIDContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldInvoiceID, v))
}

// CreditsEQ applies the EQ predicate on the "credits" field.
func CreditsEQ(v int) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCredits, v))
}

// CreditsNEQ applies the NEQ predicate on the "credits" field.
func CreditsNEQ(v int) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCredits, v))
}

// CreditsIn applies the In predicate on the "credits" field.
func CreditsIn(vs ...int) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCredits, vs...))
}

// CreditsNotIn applies the NotIn predicate on the "credits" field.
func CreditsNotIn(vs ...int) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCredits, vs...))
}

// CreditsGT applies the GT predicate on the "credits" field.
func CreditsGT(v int) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCredits, v))
}

// CreditsGTE applies the GTE predicate on the "credits" field.
func CreditsGTE(v int) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCredits, v))
}

// CreditsLT applies the LT predicate on the "credits" field.
func CreditsLT(v int) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCredits, v))
}

// CreditsLTE applies the LTE predicate on the "credits" field.
func CreditsLTE(v int) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCredits, v))
}

// BillingAddressIsNil applies the IsNil predicate on the "billing_address" field.
func BillingAddressIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldBillingAddress))
}

// BillingAddressNotNil applies the NotNil predicate on the "billing_address" field.
func BillingAddressNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldBillingAddress))
}

// ItemsIsNil applies the IsNil predicate on the "items" field.
func ItemsIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldItems))
}

// ItemsNotNil applies the NotNil predicate on the "items" field.
func ItemsNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldItems))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldMetadata))
}

// ProcessedAtEQ applies the EQ predicate on the "processed_at" field.
func ProcessedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldProcessedAt, v))
}

// ProcessedAtNEQ applies the NEQ predicate on the "processed_at" field.
func ProcessedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldProcessedAt, v))
}

// ProcessedAtIn applies the In predicate on the "processed_at" field.
func ProcessedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldProcessedAt, vs...))
}

// ProcessedAtNotIn applies the NotIn predicate on the "processed_at" field.
func ProcessedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldProcessedAt, vs...))
}

// ProcessedAtGT applies the GT predicate on the "processed_at" field.
func ProcessedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldProcessedAt, v))
}

// ProcessedAtGTE applies the GTE predicate on the "processed_at" field.
func ProcessedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldProcessedAt, v))
}

// ProcessedAtLT applies the LT predicate on the "processed_at" field.
func ProcessedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldProcessedAt, v))
}

// ProcessedAtLTE applies the LTE predicate on the "processed_at" field.
func ProcessedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldProcessedAt, v))
}

// ProcessedAtIsNil applies the IsNil predicate on the "processed_at" field.
func ProcessedAtIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldProcessedAt))
}

// ProcessedAtNotNil applies the NotNil predicate on the "processed_at" field.
func ProcessedAtNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldProcessedAt))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldExpiresAt, v))
}

// ExpiresAtIsNil applies the IsNil predicate on the "expires_at" field.
func ExpiresAtIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldExpiresAt))
}

// ExpiresAtNotNil applies the NotNil predicate on the "expires_at" field.
func ExpiresAtNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldExpiresAt))
}

// FailureReasonEQ applies the EQ predicate on the "failure_reason" field.
func FailureReasonEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldFailureReason, v))
}

// FailureReasonNEQ applies the NEQ predicate on the "failure_reason" field.
func FailureReasonNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldFailureReason, v))
}

// FailureReasonIn applies the In predicate on the "failure_reason" field.
func FailureReasonIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldFailureReason, vs...))
}

// FailureReasonNotIn applies the NotIn predicate on the "failure_reason" field.
func FailureReasonNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldFailureReason, vs...))
}

// FailureReasonGT applies the GT predicate on the "failure_reason" field.
func FailureReasonGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldFailureReason, v))
}

// FailureReasonGTE applies the GTE predicate on the "failure_reason" field.
func FailureReasonGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldFailureReason, v))
}

// FailureReasonLT applies the LT predicate on the "failure_reason" field.
func FailureReasonLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldFailureReason, v))
}

// FailureReasonLTE applies the LTE predicate on the "failure_reason" field.
func FailureReasonLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldFailureReason, v))
}

// FailureReasonContains applies the Contains predicate on the "failure_reason" field.
func FailureReasonContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldFailureReason, v))
}

// FailureReasonHasPrefix applies the HasPrefix predicate on the "failure_reason" field.
func FailureReasonHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldFailureReason, v))
}

// FailureReasonHasSuffix applies the HasSuffix predicate on the "failure_reason" field.
func FailureReasonHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldFailureReason, v))
}

// FailureReasonIsNil applies the IsNil predicate on the "failure_reason" field.
func FailureReasonIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldFailureReason))
}

// FailureReasonNotNil applies the NotNil predicate on the "failure_reason" field.
func FailureReasonNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldFailureReason))
}

// FailureReasonEqualFold applies the EqualFold predicate on the "failure_reason" field.
func FailureReasonEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldFailureReason, v))
}

// FailureReasonContainsFold applies the ContainsFold predicate on the "failure_reason" field.
func FailureReasonContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldFailureReason, v))
}

// ExternalOrderIDEQ applies the EQ predicate on the "external_order_id" field.
func ExternalOrderIDEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldExternalOrderID, v))
}

// ExternalOrderIDNEQ applies the NEQ predicate on the "external_order_id" field.
func ExternalOrderIDNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldExternalOrderID, v))
}

// ExternalOrderIDIn applies the In predicate on the "external_order_id" field.
func ExternalOrderIDIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldExternalOrderID, vs...))
}

// ExternalOrderIDNotIn applies the NotIn predicate on the "external_order_id" field.
func ExternalOrderIDNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldExternalOrderID, vs...))
}

// ExternalOrderIDGT applies the GT predicate on the "external_order_id" field.
func ExternalOrderIDGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldExternalOrderID, v))
}

// ExternalOrderIDGTE applies the GTE predicate on the "external_order_id" field.
func ExternalOrderIDGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldExternalOrderID, v))
}

// ExternalOrderIDLT applies the LT predicate on the "external_order_id" field.
func ExternalOrderIDLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldExternalOrderID, v))
}

// ExternalOrderIDLTE applies the LTE predicate on the "external_order_id" field.
func ExternalOrderIDLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldExternalOrderID, v))
}

// ExternalOrderIDContains applies the Contains predicate on the "external_order_id" field.
func ExternalOrderIDContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldExternalOrderID, v))
}

// ExternalOrderIDHasPrefix applies the HasPrefix predicate on the "external_order_id" field.
func ExternalOrderIDHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldExternalOrderID, v))
}

// ExternalOrderIDHasSuffix applies the HasSuffix predicate on the "external_order_id" field.
func ExternalOrderIDHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldExternalOrderID, v))
}

// ExternalOrderIDIsNil applies the IsNil predicate on the "external_order_id" field.
func ExternalOrderIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldExternalOrderID))
}

// ExternalOrderIDNotNil applies the NotNil predicate on the "external_order_id" field.
func ExternalOrderIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldExternalOrderID))
}

// ExternalOrderIDEqualFold applies the EqualFold predicate on the "external_order_id" field.
func ExternalOrderIDEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldExternalOrderID, v))
}

// ExternalOrderIDContainsFold applies the ContainsFold predicate on the "external_order_id" field.
func ExternalOrderIDContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldExternalOrderID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Order) predicate.Order {
	return predicate.Order(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Order) predicate.Order {
	return predicate.Order(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Order) predicate.Order {
	return predicate.Order(sql.NotPredicates(p))
}

package kafka

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
)

// TopicNames defines standard Kafka topic names
var TopicNames = struct {
	UserEvents         string
	ContentEvents      string
	BillingEvents      string
	IntegrationEvents  string
	AssetEvents        string
	RAGEvents          string
	AnalyticsEvents    string
	SystemEvents       string
	NotificationEvents string
	DeadLetterQueue    string
	RetryQueue         string
}{
	UserEvents:         "user.events",
	ContentEvents:      "content.events",
	BillingEvents:      "billing.events",
	IntegrationEvents:  "integration.events",
	AssetEvents:        "asset.events",
	RAGEvents:          "rag.events",
	AnalyticsEvents:    "analytics.events",
	SystemEvents:       "system.events",
	NotificationEvents: "notification.events",
	DeadLetterQueue:    "events.dlq",
	RetryQueue:         "events.retry",
}

// Message represents a standardized Kafka message
type Message struct {
	Topic     string          `json:"topic" validate:"required"`
	Key       string          `json:"key" validate:"required"`
	Value     json.RawMessage `json:"value" validate:"required"`
	Headers   map[string]string `json:"headers,omitempty"`
	Timestamp time.Time       `json:"timestamp"`
	Partition int32           `json:"partition,omitempty"`
	Offset    int64           `json:"offset,omitempty"`
}

// EventMessage represents the standardized message format for all event topics
type EventMessage struct {
	MessageID   string                 `json:"message_id" validate:"required,uuid"`
	EventType   EventType              `json:"event_type" validate:"required"`
	Source      string                 `json:"source" validate:"required"`
	Version     string                 `json:"version" validate:"required"`
	Timestamp   time.Time              `json:"timestamp" validate:"required"`
	UserID      string                 `json:"user_id,omitempty"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Priority    Priority               `json:"priority" validate:"required,oneof=low normal high critical"`
	EventData   map[string]interface{} `json:"event_data" validate:"required"`
	Metadata    EventMetadata          `json:"metadata" validate:"required"`
}

// MessageValidator provides validation for event messages
type MessageValidator struct {
	validator *validator.Validate
}

// NewMessageValidator creates a new message validator
func NewMessageValidator() *MessageValidator {
	v := validator.New()
	
	// Register custom validations
	v.RegisterValidation("event_type", validateEventType)
	v.RegisterValidation("priority", validatePriority)
	
	return &MessageValidator{
		validator: v,
	}
}

// ValidateEventMessage validates an event message
func (mv *MessageValidator) ValidateEventMessage(msg *EventMessage) error {
	if err := mv.validator.Struct(msg); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	
	// Additional business logic validations
	if err := mv.validateEventData(msg.EventType, msg.EventData); err != nil {
		return fmt.Errorf("event data validation failed: %w", err)
	}
	
	return nil
}

// validateEventData validates event-specific data based on event type
func (mv *MessageValidator) validateEventData(eventType EventType, data map[string]interface{}) error {
	switch eventType {
	case EventTypeUserRegistered, EventTypeUserWelcome:
		return mv.validateUserEventData(data)
	case EventTypeContentGenerated, EventTypePostPublished:
		return mv.validateContentEventData(data)
	case EventTypeCreditLow, EventTypeSubscriptionExpired:
		return mv.validateBillingEventData(data)
	case EventTypeIntegrationConnected, EventTypeIntegrationFailed:
		return mv.validateIntegrationEventData(data)
	case EventTypeAssetUploaded, EventTypeAssetValidated:
		return mv.validateAssetEventData(data)
	case EventTypeRAGProcessingStarted, EventTypeRAGProcessingCompleted:
		return mv.validateRAGEventData(data)
	default:
		// For unknown event types, just check that data is not empty
		if len(data) == 0 {
			return fmt.Errorf("event data cannot be empty for event type: %s", eventType)
		}
	}
	return nil
}

// validateUserEventData validates user event data
func (mv *MessageValidator) validateUserEventData(data map[string]interface{}) error {
	requiredFields := []string{"email"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateContentEventData validates content event data
func (mv *MessageValidator) validateContentEventData(data map[string]interface{}) error {
	requiredFields := []string{"content_type"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateBillingEventData validates billing event data
func (mv *MessageValidator) validateBillingEventData(data map[string]interface{}) error {
	// Billing events should have at least one of: amount, credits, subscription_id
	hasAmount := false
	hasCredits := false
	hasSubscription := false
	
	if _, exists := data["amount"]; exists {
		hasAmount = true
	}
	if _, exists := data["credits"]; exists {
		hasCredits = true
	}
	if _, exists := data["subscription_id"]; exists {
		hasSubscription = true
	}
	
	if !hasAmount && !hasCredits && !hasSubscription {
		return fmt.Errorf("billing event must have at least one of: amount, credits, subscription_id")
	}
	
	return nil
}

// validateIntegrationEventData validates integration event data
func (mv *MessageValidator) validateIntegrationEventData(data map[string]interface{}) error {
	requiredFields := []string{"integration_type", "platform_name"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateAssetEventData validates asset event data
func (mv *MessageValidator) validateAssetEventData(data map[string]interface{}) error {
	requiredFields := []string{"asset_id", "asset_type"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateRAGEventData validates RAG event data
func (mv *MessageValidator) validateRAGEventData(data map[string]interface{}) error {
	requiredFields := []string{"processing_id"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// Custom validation functions
func validateEventType(fl validator.FieldLevel) bool {
	eventType := EventType(fl.Field().String())
	validTypes := []EventType{
		EventTypeUserRegistered, EventTypeUserUpdated, EventTypeUserDeleted,
		EventTypeUserWelcome, EventTypeUserUpgraded, EventTypeUserProfileUpdated,
		EventTypeUserPasswordReset, EventTypeUserEmailVerified, EventTypeUserSecurityAlert,
		EventTypeContentGenerated, EventTypeContentImproved, EventTypePostPublished,
		EventTypePostScheduled, EventTypePostFailed, EventTypeTemplateCreated,
		EventTypeTemplateUpdated, EventTypeTemplateDeleted,
		EventTypeCreditLow, EventTypeCreditPurchased, EventTypeCreditConsumed,
		EventTypeSubscriptionRenewed, EventTypeSubscriptionExpired,
		EventTypePaymentFailed, EventTypePaymentSucceeded,
		EventTypeIntegrationConnected, EventTypeIntegrationFailed, EventTypeIntegrationDisconnected,
		EventTypeAnalyticsReport, EventTypeAnalyticsInsight,
		EventTypeSystemMaintenance, EventTypeSystemUpdate, EventTypeSystemAlert,
		EventTypeAssetUploaded, EventTypeAssetDeleted, EventTypeAssetValidated,
		EventTypeRAGProcessingStarted, EventTypeRAGProcessingCompleted, EventTypeRAGProcessingFailed,
		EventTypeRAGRetrainingStarted, EventTypeRAGRetrainingCompleted,
		EventTypeNotificationSent, EventTypeNotificationDelivered, EventTypeNotificationFailed,
	}
	
	for _, validType := range validTypes {
		if eventType == validType {
			return true
		}
	}
	return false
}

func validatePriority(fl validator.FieldLevel) bool {
	priority := Priority(fl.Field().String())
	return priority == PriorityLow || priority == PriorityNormal || 
		   priority == PriorityHigh || priority == PriorityCritical
}

// MessageBuilder helps build standardized Kafka messages
type MessageBuilder struct {
	validator *MessageValidator
}

// NewMessageBuilder creates a new message builder
func NewMessageBuilder() *MessageBuilder {
	return &MessageBuilder{
		validator: NewMessageValidator(),
	}
}

// BuildEventMessage builds an event message from a base event
func (mb *MessageBuilder) BuildEventMessage(event *BaseEvent) (*EventMessage, error) {
	msg := &EventMessage{
		MessageID:   event.ID,
		EventType:   event.Type,
		Source:      event.Source,
		Version:     event.Version,
		Timestamp:   event.Timestamp,
		UserID:      event.UserID,
		WorkspaceID: event.WorkspaceID,
		Priority:    event.Priority,
		EventData:   event.Data,
		Metadata:    event.Metadata,
	}
	
	if err := mb.validator.ValidateEventMessage(msg); err != nil {
		return nil, fmt.Errorf("failed to validate event message: %w", err)
	}
	
	return msg, nil
}

// BuildKafkaMessage builds a Kafka message from an event message
func (mb *MessageBuilder) BuildKafkaMessage(topic string, msg *EventMessage) (*Message, error) {
	value, err := json.Marshal(msg)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message: %w", err)
	}
	
	// Use user ID as partition key for ordering, fallback to message ID
	key := msg.UserID
	if key == "" {
		key = msg.MessageID
	}
	
	return &Message{
		Topic:     topic,
		Key:       key,
		Value:     value,
		Headers: map[string]string{
			"event_type": string(msg.EventType),
			"source":     msg.Source,
			"version":    msg.Version,
			"priority":   string(msg.Priority),
		},
		Timestamp: msg.Timestamp,
	}, nil
}

// GetTopicForEventType returns the appropriate topic for an event type
func GetTopicForEventType(eventType EventType) string {
	switch eventType {
	case EventTypeUserRegistered, EventTypeUserUpdated, EventTypeUserDeleted,
		 EventTypeUserWelcome, EventTypeUserUpgraded, EventTypeUserProfileUpdated,
		 EventTypeUserPasswordReset, EventTypeUserEmailVerified, EventTypeUserSecurityAlert:
		return TopicNames.UserEvents
		
	case EventTypeContentGenerated, EventTypeContentImproved, EventTypePostPublished,
		 EventTypePostScheduled, EventTypePostFailed, EventTypeTemplateCreated,
		 EventTypeTemplateUpdated, EventTypeTemplateDeleted:
		return TopicNames.ContentEvents
		
	case EventTypeCreditLow, EventTypeCreditPurchased, EventTypeCreditConsumed,
		 EventTypeSubscriptionRenewed, EventTypeSubscriptionExpired,
		 EventTypePaymentFailed, EventTypePaymentSucceeded:
		return TopicNames.BillingEvents
		
	case EventTypeIntegrationConnected, EventTypeIntegrationFailed, EventTypeIntegrationDisconnected:
		return TopicNames.IntegrationEvents
		
	case EventTypeAssetUploaded, EventTypeAssetDeleted, EventTypeAssetValidated:
		return TopicNames.AssetEvents
		
	case EventTypeRAGProcessingStarted, EventTypeRAGProcessingCompleted, EventTypeRAGProcessingFailed,
		 EventTypeRAGRetrainingStarted, EventTypeRAGRetrainingCompleted:
		return TopicNames.RAGEvents
		
	case EventTypeAnalyticsReport, EventTypeAnalyticsInsight:
		return TopicNames.AnalyticsEvents
		
	case EventTypeSystemMaintenance, EventTypeSystemUpdate, EventTypeSystemAlert:
		return TopicNames.SystemEvents
		
	case EventTypeNotificationSent, EventTypeNotificationDelivered, EventTypeNotificationFailed:
		return TopicNames.NotificationEvents
		
	default:
		return TopicNames.SystemEvents // Default fallback
	}
}

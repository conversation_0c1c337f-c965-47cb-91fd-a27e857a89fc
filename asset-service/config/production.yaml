# Production Environment Configuration

# Production server settings
server:
  env: "production"
  grpc_port: 50054
  http_port: 8084
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Production database settings (use environment variables)
database:
  # Always use PostgreSQL in production
  type: "postgres"
  host: "${DB_HOST}"
  port: "${DB_PORT:5432}"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  name: "${DB_NAME}"
  ssl_mode: "require"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Production storage settings
storage:
  provider: "${STORAGE_PROVIDER:s3}"
  endpoint: "${STORAGE_ENDPOINT}"
  access_key: "${STORAGE_ACCESS_KEY}"
  secret_key: "${STORAGE_SECRET_KEY}"
  bucket: "${STORAGE_BUCKET}"
  use_ssl: "${STORAGE_USE_SSL:true}"
  public_url: "${STORAGE_PUBLIC_URL}"
  region: "${STORAGE_REGION:us-east-1}"

# Production cleanup settings
cleanup:
  enabled: true
  pending_upload_ttl: "1h"
  orphaned_file_ttl: "24h"
  deleted_asset_ttl: "720h"      # 30 days
  cleanup_interval: "30m"
  batch_size: 100
  enable_storage_cleanup: true
  dry_run: false

# Production security settings
security:
  max_file_size: 104857600       # 100MB
  allowed_mime_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"
    - "application/json"
    - "text/markdown"
    - "text/csv"
  enable_virus_scanning: true
  quarantine_ttl: "720h"         # 30 days

# Production feature flags
features:
  enable_image_processing: true
  enable_rag: true
  enable_folders: true
  enable_versioning: false

# Production logging
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Production JWT settings
jwt:
  secret_key: "${JWT_SECRET_KEY}"
  token_duration: "24h"
  refresh_duration: "168h"

# Production Kafka settings
kafka:
  enabled: true
  brokers:
    - "${KAFKA_BROKER_1}"
    - "${KAFKA_BROKER_2}"
    - "${KAFKA_BROKER_3}"
  topics:
    asset_events: "asset.events"
    notification_events: "notification.events"
  producer:
    timeout: "10s"
    retries: 3
    batch_size: 100
    flush_frequency: "1s"

# Production services configuration
services:
  user_service:
    host: "${USER_SERVICE_HOST}"
    port: "${USER_SERVICE_PORT:50051}"
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "${AI_SERVICE_HOST}"
    port: "${AI_SERVICE_PORT:50055}"
    timeout: "60s"
    retries: 2

# Production monitoring
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: "30s"

# Production rate limiting
rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 20
  upload_requests_per_minute: 10
  upload_burst: 5

package plan

import (
	"context"
	"fmt"

	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new plan service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// ListPlans lists subscription plans
func (s *service) ListPlans(ctx context.Context, req *models.ListPlansRequest) (*models.ListPlansResponse, error) {
	s.logger.Info("Listing subscription plans")

	// Build query
	query := s.readDB.SubscriptionPlan.Query()

	// Apply filters
	if req.Active != nil {
		query = query.Where(subscriptionplan.IsActive(*req.Active))
	}
	if req.BillingInterval != "" {
		query = query.Where(subscriptionplan.BillingIntervalEQ(subscriptionplan.BillingInterval(req.BillingInterval)))
	}

	// Apply sorting
	query = query.Order(ent.Asc(subscriptionplan.FieldSortOrder), ent.Asc(subscriptionplan.FieldCreatedAt))

	// Execute query
	plans, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get plans: %w", err)
	}

	// Convert to response format
	var planResponses []models.SubscriptionPlan
	for _, plan := range plans {
		// Convert features from []string to []PlanFeature
		var features []models.PlanFeature
		for _, feature := range plan.Features {
			features = append(features, models.PlanFeature{
				Name:     feature,
				Included: true,
			})
		}

		// Convert limits from map to PlanLimits
		limits := models.PlanLimits{}
		if plan.Limits != nil {
			if maxPosts, ok := plan.Limits["max_posts"].(float64); ok {
				limits.MaxPosts = int(maxPosts)
			}
			if maxTemplates, ok := plan.Limits["max_templates"].(float64); ok {
				limits.MaxTemplates = int(maxTemplates)
			}
			if maxWorkspaces, ok := plan.Limits["max_workspaces"].(float64); ok {
				limits.MaxWorkspaces = int(maxWorkspaces)
			}
		}

		planResponses = append(planResponses, models.SubscriptionPlan{
			BaseModel: models.BaseModel{
				ID:        plan.ID.String(),
				CreatedAt: plan.CreatedAt,
				UpdatedAt: plan.UpdatedAt,
			},
			Name:            plan.Name,
			Description:     plan.Description,
			BillingInterval: string(plan.BillingInterval),
			Price:           plan.Price,
			Currency:        plan.Currency,
			CreditsIncluded: plan.CreditsIncluded,
			MonthlyLimit:    plan.MonthlyCredits,
			Features:        features,
			Limits:          limits,
			Popular:         plan.IsFeatured,
			Active:          plan.IsActive,
			SortOrder:       plan.SortOrder,
			Metadata:        plan.Metadata,
		})
	}

	// Calculate pagination
	pagination := models.PaginationMeta{
		Page:       1,
		Limit:      len(planResponses),
		Total:      len(planResponses),
		TotalPages: 1,
	}

	return &models.ListPlansResponse{
		Plans:      planResponses,
		Pagination: pagination,
	}, nil
}

// GetPlan gets a plan by ID
func (s *service) GetPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error) {
	// TODO: Implement get plan
	return nil, fmt.Errorf("not implemented")
}

// GetPlanAnalytics gets plan analytics
func (s *service) GetPlanAnalytics(ctx context.Context, userID, period string) (*models.PlanAnalyticsResponse, error) {
	// TODO: Implement get plan analytics
	return nil, fmt.Errorf("not implemented")
}

// GetPublicPlans gets public plans
func (s *service) GetPublicPlans(ctx context.Context) (*models.ListPlansResponse, error) {
	// TODO: Implement get public plans
	return nil, fmt.Errorf("not implemented")
}

// GetPublicPlan gets public plan
func (s *service) GetPublicPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error) {
	// TODO: Implement get public plan
	return nil, fmt.Errorf("not implemented")
}

// GetPricing gets pricing information
func (s *service) GetPricing(ctx context.Context) (*models.PricingResponse, error) {
	// TODO: Implement get pricing
	return nil, fmt.Errorf("not implemented")
}

// AdminListPlans lists plans (admin)
func (s *service) AdminListPlans(ctx context.Context, req *models.ListPlansRequest) (*models.ListPlansResponse, error) {
	// TODO: Implement admin list plans
	return nil, fmt.Errorf("not implemented")
}

// AdminCreatePlan creates plan (admin)
func (s *service) AdminCreatePlan(ctx context.Context, req *models.CreatePlanRequest) (*models.SubscriptionPlan, error) {
	// TODO: Implement admin create plan
	return nil, fmt.Errorf("not implemented")
}

// AdminUpdatePlan updates plan (admin)
func (s *service) AdminUpdatePlan(ctx context.Context, req *models.UpdatePlanRequest) (*models.SubscriptionPlan, error) {
	// TODO: Implement admin update plan
	return nil, fmt.Errorf("not implemented")
}

// AdminDeletePlan deletes plan (admin)
func (s *service) AdminDeletePlan(ctx context.Context, planID string) error {
	// TODO: Implement admin delete plan
	return fmt.Errorf("not implemented")
}

// AdminActivatePlan activates plan (admin)
func (s *service) AdminActivatePlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error) {
	// TODO: Implement admin activate plan
	return nil, fmt.Errorf("not implemented")
}

// AdminDeactivatePlan deactivates plan (admin)
func (s *service) AdminDeactivatePlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error) {
	// TODO: Implement admin deactivate plan
	return nil, fmt.Errorf("not implemented")
}

// GetAdminPlanAnalytics gets admin plan analytics
func (s *service) GetAdminPlanAnalytics(ctx context.Context, period string) (*models.PlanAnalyticsResponse, error) {
	// TODO: Implement get admin plan analytics
	return nil, fmt.Errorf("not implemented")
}

// AdminGetPlanConfig gets plan config (admin)
func (s *service) AdminGetPlanConfig(ctx context.Context) (*models.PricingResponse, error) {
	// TODO: Implement admin get plan config
	return nil, fmt.Errorf("not implemented")
}

// AdminUpdatePlanConfig updates plan config (admin)
func (s *service) AdminUpdatePlanConfig(ctx context.Context, config *models.PricingResponse) (*models.PricingResponse, error) {
	// TODO: Implement admin update plan config
	return nil, fmt.Errorf("not implemented")
}

// InternalGetPlan gets plan (internal)
func (s *service) InternalGetPlan(ctx context.Context, planID string) (*models.SubscriptionPlan, error) {
	// TODO: Implement internal get plan
	return nil, fmt.Errorf("not implemented")
}

// ValidatePromoCode validates promo code
func (s *service) ValidatePromoCode(ctx context.Context, code string, planID string) (*models.PromoCode, error) {
	// TODO: Implement validate promo code
	return nil, fmt.Errorf("not implemented")
}

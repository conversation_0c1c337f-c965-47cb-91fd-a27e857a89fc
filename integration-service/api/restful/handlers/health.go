package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	*BaseHandler
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(base *BaseHandler) *HealthHandler {
	return &HealthHandler{
		BaseHandler: base,
	}
}

// HealthCheck handles health check requests
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	// Get current timestamp
	timestamp := time.Now().Unix()
	
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   "integration-service",
		"timestamp": strconv.FormatInt(timestamp, 10),
		"version":   "1.0.0",
	})
}

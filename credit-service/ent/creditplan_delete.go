// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/creditplan"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditPlanDelete is the builder for deleting a CreditPlan entity.
type CreditPlanDelete struct {
	config
	hooks    []Hook
	mutation *CreditPlanMutation
}

// Where appends a list predicates to the CreditPlanDelete builder.
func (cpd *CreditPlanDelete) Where(ps ...predicate.CreditPlan) *CreditPlanDelete {
	cpd.mutation.Where(ps...)
	return cpd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cpd *CreditPlanDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cpd.sqlExec, cpd.mutation, cpd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cpd *CreditPlanDelete) ExecX(ctx context.Context) int {
	n, err := cpd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cpd *CreditPlanDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(creditplan.Table, sqlgraph.NewFieldSpec(creditplan.FieldID, field.TypeUUID))
	if ps := cpd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cpd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cpd.mutation.done = true
	return affected, err
}

// CreditPlanDeleteOne is the builder for deleting a single CreditPlan entity.
type CreditPlanDeleteOne struct {
	cpd *CreditPlanDelete
}

// Where appends a list predicates to the CreditPlanDelete builder.
func (cpdo *CreditPlanDeleteOne) Where(ps ...predicate.CreditPlan) *CreditPlanDeleteOne {
	cpdo.cpd.mutation.Where(ps...)
	return cpdo
}

// Exec executes the deletion query.
func (cpdo *CreditPlanDeleteOne) Exec(ctx context.Context) error {
	n, err := cpdo.cpd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{creditplan.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cpdo *CreditPlanDeleteOne) ExecX(ctx context.Context) {
	if err := cpdo.Exec(ctx); err != nil {
		panic(err)
	}
}

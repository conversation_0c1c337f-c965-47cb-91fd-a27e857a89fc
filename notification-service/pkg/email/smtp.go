package email

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"html/template"
	"net/smtp"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// Sender defines the interface for email sending
type Sender interface {
	SendEmail(msg *EmailMessage) error
	SendTemplateEmail(to []string, template *EmailTemplate, data map[string]interface{}) error
	SendBulkEmail(messages []*EmailMessage) error
	TestConnection() error
}

// SMTPConfig represents SMTP configuration
type SMTPConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	FromName string `json:"from_name"`
	UseTLS   bool   `json:"use_tls"`
}

// SMTPSender handles email sending via SMTP
type SMTPSender struct {
	config *SMTPConfig
	logger logging.Logger
}

// NewSMTPSender creates a new SMTP email sender
func NewSMTPSender(config *SMTPConfig, logger logging.Logger) *SMTPSender {
	return &SMTPSender{
		config: config,
		logger: logger,
	}
}

// EmailMessage represents an email message
type EmailMessage struct {
	To          []string          `json:"to"`
	CC          []string          `json:"cc,omitempty"`
	BCC         []string          `json:"bcc,omitempty"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	HTMLBody    string            `json:"html_body,omitempty"`
	Attachments []EmailAttachment `json:"attachments,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
}

// EmailAttachment represents an email attachment
type EmailAttachment struct {
	Filename    string `json:"filename"`
	ContentType string `json:"content_type"`
	Data        []byte `json:"data"`
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	Name      string            `json:"name"`
	Subject   string            `json:"subject"`
	TextBody  string            `json:"text_body"`
	HTMLBody  string            `json:"html_body"`
	Variables map[string]string `json:"variables,omitempty"`
}

// SendEmail sends an email message
func (s *SMTPSender) SendEmail(ctx context.Context, msg *EmailMessage) error {
	if len(msg.To) == 0 {
		return fmt.Errorf("no recipients specified")
	}

	// Create SMTP client
	client, err := s.createSMTPClient()
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Close()

	// Set sender
	if err := client.Mail(s.config.From); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// Set recipients
	allRecipients := append(msg.To, msg.CC...)
	allRecipients = append(allRecipients, msg.BCC...)

	for _, recipient := range allRecipients {
		if err := client.Rcpt(recipient); err != nil {
			s.logger.WithError(err).WithField("recipient", recipient).Warn("Failed to add recipient")
			continue
		}
	}

	// Send email data
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %w", err)
	}
	defer writer.Close()

	// Build email content
	emailContent := s.buildEmailContent(msg)

	if _, err := writer.Write([]byte(emailContent)); err != nil {
		return fmt.Errorf("failed to write email content: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"to":      msg.To,
		"subject": msg.Subject,
	}).Info("Email sent successfully")

	return nil
}

// SendTemplateEmail sends an email using a template
func (s *SMTPSender) SendTemplateEmail(ctx context.Context, to []string, template *EmailTemplate, data map[string]interface{}) error {
	// Parse and execute templates
	subject, err := s.executeTemplate(template.Subject, data)
	if err != nil {
		return fmt.Errorf("failed to execute subject template: %w", err)
	}

	textBody, err := s.executeTemplate(template.TextBody, data)
	if err != nil {
		return fmt.Errorf("failed to execute text body template: %w", err)
	}

	var htmlBody string
	if template.HTMLBody != "" {
		htmlBody, err = s.executeTemplate(template.HTMLBody, data)
		if err != nil {
			return fmt.Errorf("failed to execute HTML body template: %w", err)
		}
	}

	// Create email message
	msg := &EmailMessage{
		To:       to,
		Subject:  subject,
		Body:     textBody,
		HTMLBody: htmlBody,
	}

	return s.SendEmail(ctx, msg)
}

// SendBulkEmail sends emails to multiple recipients
func (s *SMTPSender) SendBulkEmail(ctx context.Context, messages []*EmailMessage) error {
	var errors []string

	for i, msg := range messages {
		if err := s.SendEmail(ctx, msg); err != nil {
			errorMsg := fmt.Sprintf("message %d: %v", i, err)
			errors = append(errors, errorMsg)
			s.logger.WithError(err).WithField("message_index", i).Error("Failed to send bulk email")
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("bulk email errors: %s", strings.Join(errors, "; "))
	}

	s.logger.WithField("count", len(messages)).Info("Bulk emails sent successfully")
	return nil
}

// createSMTPClient creates and configures SMTP client
func (s *SMTPSender) createSMTPClient() (*smtp.Client, error) {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	// Create connection
	var client *smtp.Client
	var err error

	if s.config.UseTLS {
		// Use TLS connection
		tlsConfig := &tls.Config{
			ServerName: s.config.Host,
		}

		conn, err := tls.Dial("tcp", addr, tlsConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to dial TLS: %w", err)
		}

		client, err = smtp.NewClient(conn, s.config.Host)
		if err != nil {
			conn.Close()
			return nil, fmt.Errorf("failed to create TLS client: %w", err)
		}
	} else {
		// Use plain connection with STARTTLS
		client, err = smtp.Dial(addr)
		if err != nil {
			return nil, fmt.Errorf("failed to dial SMTP: %w", err)
		}

		// Try STARTTLS if available
		if ok, _ := client.Extension("STARTTLS"); ok {
			tlsConfig := &tls.Config{
				ServerName: s.config.Host,
			}
			if err := client.StartTLS(tlsConfig); err != nil {
				client.Close()
				return nil, fmt.Errorf("failed to start TLS: %w", err)
			}
		}
	}

	// Authenticate if credentials provided
	if s.config.Username != "" && s.config.Password != "" {
		auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)
		if err := client.Auth(auth); err != nil {
			client.Close()
			return nil, fmt.Errorf("failed to authenticate: %w", err)
		}
	}

	return client, nil
}

// buildEmailContent builds the complete email content
func (s *SMTPSender) buildEmailContent(msg *EmailMessage) string {
	var buffer bytes.Buffer

	// Headers
	buffer.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.config.FromName, s.config.From))
	buffer.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(msg.To, ", ")))

	if len(msg.CC) > 0 {
		buffer.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(msg.CC, ", ")))
	}

	buffer.WriteString(fmt.Sprintf("Subject: %s\r\n", msg.Subject))
	buffer.WriteString(fmt.Sprintf("Date: %s\r\n", time.Now().Format(time.RFC1123Z)))
	buffer.WriteString("MIME-Version: 1.0\r\n")

	// Custom headers
	for key, value := range msg.Headers {
		buffer.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
	}

	// Content type
	if msg.HTMLBody != "" {
		// Multipart message
		boundary := fmt.Sprintf("boundary_%d", time.Now().Unix())
		buffer.WriteString(fmt.Sprintf("Content-Type: multipart/alternative; boundary=\"%s\"\r\n", boundary))
		buffer.WriteString("\r\n")

		// Text part
		buffer.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		buffer.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		buffer.WriteString("Content-Transfer-Encoding: 8bit\r\n")
		buffer.WriteString("\r\n")
		buffer.WriteString(msg.Body)
		buffer.WriteString("\r\n")

		// HTML part
		buffer.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		buffer.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		buffer.WriteString("Content-Transfer-Encoding: 8bit\r\n")
		buffer.WriteString("\r\n")
		buffer.WriteString(msg.HTMLBody)
		buffer.WriteString("\r\n")

		buffer.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else {
		// Plain text message
		buffer.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		buffer.WriteString("Content-Transfer-Encoding: 8bit\r\n")
		buffer.WriteString("\r\n")
		buffer.WriteString(msg.Body)
	}

	return buffer.String()
}

// executeTemplate executes a template with given data
func (s *SMTPSender) executeTemplate(templateStr string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("email").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	var buffer bytes.Buffer
	if err := tmpl.Execute(&buffer, data); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buffer.String(), nil
}

// TestConnection tests the SMTP connection
func (s *SMTPSender) TestConnection() error {
	client, err := s.createSMTPClient()
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer client.Close()

	s.logger.Info("SMTP connection test successful")
	return nil
}

// GetDefaultTemplates returns default email templates
func GetDefaultTemplates() map[string]*EmailTemplate {
	return map[string]*EmailTemplate{
		"welcome": {
			Name:    "welcome",
			Subject: "Welcome to {{.AppName}}!",
			TextBody: `Hi {{.UserName}},

Welcome to {{.AppName}}! We're excited to have you on board.

Your account has been successfully created. You can now start creating amazing content with AI assistance.

Best regards,
The {{.AppName}} Team`,
			HTMLBody: `<html>
<body>
<h2>Hi {{.UserName}},</h2>
<p>Welcome to <strong>{{.AppName}}</strong>! We're excited to have you on board.</p>
<p>Your account has been successfully created. You can now start creating amazing content with AI assistance.</p>
<p>Best regards,<br>The {{.AppName}} Team</p>
</body>
</html>`,
		},
		"password_reset": {
			Name:    "password_reset",
			Subject: "Reset Your Password - {{.AppName}}",
			TextBody: `Hi {{.UserName}},

You requested to reset your password for {{.AppName}}.

Click the link below to reset your password:
{{.ResetLink}}

This link will expire in 24 hours.

If you didn't request this, please ignore this email.

Best regards,
The {{.AppName}} Team`,
			HTMLBody: `<html>
<body>
<h2>Hi {{.UserName}},</h2>
<p>You requested to reset your password for <strong>{{.AppName}}</strong>.</p>
<p><a href="{{.ResetLink}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
<p>This link will expire in 24 hours.</p>
<p>If you didn't request this, please ignore this email.</p>
<p>Best regards,<br>The {{.AppName}} Team</p>
</body>
</html>`,
		},
		"content_published": {
			Name:    "content_published",
			Subject: "Your content has been published - {{.AppName}}",
			TextBody: `Hi {{.UserName}},

Your content "{{.ContentTitle}}" has been successfully published to {{.Platform}}.

Published at: {{.PublishedAt}}
Platform: {{.Platform}}

You can view your published content and analytics in your dashboard.

Best regards,
The {{.AppName}} Team`,
			HTMLBody: `<html>
<body>
<h2>Hi {{.UserName}},</h2>
<p>Your content "<strong>{{.ContentTitle}}</strong>" has been successfully published to <strong>{{.Platform}}</strong>.</p>
<ul>
<li><strong>Published at:</strong> {{.PublishedAt}}</li>
<li><strong>Platform:</strong> {{.Platform}}</li>
</ul>
<p>You can view your published content and analytics in your dashboard.</p>
<p>Best regards,<br>The {{.AppName}} Team</p>
</body>
</html>`,
		},
	}
}

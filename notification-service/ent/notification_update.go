// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notification"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationUpdate is the builder for updating Notification entities.
type NotificationUpdate struct {
	config
	hooks    []Hook
	mutation *NotificationMutation
}

// Where appends a list predicates to the NotificationUpdate builder.
func (nu *NotificationUpdate) Where(ps ...predicate.Notification) *NotificationUpdate {
	nu.mutation.Where(ps...)
	return nu
}

// SetUserID sets the "user_id" field.
func (nu *NotificationUpdate) SetUserID(u uuid.UUID) *NotificationUpdate {
	nu.mutation.SetUserID(u)
	return nu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableUserID(u *uuid.UUID) *NotificationUpdate {
	if u != nil {
		nu.SetUserID(*u)
	}
	return nu
}

// SetType sets the "type" field.
func (nu *NotificationUpdate) SetType(n notification.Type) *NotificationUpdate {
	nu.mutation.SetType(n)
	return nu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableType(n *notification.Type) *NotificationUpdate {
	if n != nil {
		nu.SetType(*n)
	}
	return nu
}

// SetTitle sets the "title" field.
func (nu *NotificationUpdate) SetTitle(s string) *NotificationUpdate {
	nu.mutation.SetTitle(s)
	return nu
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableTitle(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetTitle(*s)
	}
	return nu
}

// SetMessage sets the "message" field.
func (nu *NotificationUpdate) SetMessage(s string) *NotificationUpdate {
	nu.mutation.SetMessage(s)
	return nu
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableMessage(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetMessage(*s)
	}
	return nu
}

// SetData sets the "data" field.
func (nu *NotificationUpdate) SetData(m map[string]interface{}) *NotificationUpdate {
	nu.mutation.SetData(m)
	return nu
}

// ClearData clears the value of the "data" field.
func (nu *NotificationUpdate) ClearData() *NotificationUpdate {
	nu.mutation.ClearData()
	return nu
}

// SetPriority sets the "priority" field.
func (nu *NotificationUpdate) SetPriority(n notification.Priority) *NotificationUpdate {
	nu.mutation.SetPriority(n)
	return nu
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillablePriority(n *notification.Priority) *NotificationUpdate {
	if n != nil {
		nu.SetPriority(*n)
	}
	return nu
}

// SetChannels sets the "channels" field.
func (nu *NotificationUpdate) SetChannels(s []string) *NotificationUpdate {
	nu.mutation.SetChannels(s)
	return nu
}

// AppendChannels appends s to the "channels" field.
func (nu *NotificationUpdate) AppendChannels(s []string) *NotificationUpdate {
	nu.mutation.AppendChannels(s)
	return nu
}

// ClearChannels clears the value of the "channels" field.
func (nu *NotificationUpdate) ClearChannels() *NotificationUpdate {
	nu.mutation.ClearChannels()
	return nu
}

// SetStatus sets the "status" field.
func (nu *NotificationUpdate) SetStatus(n notification.Status) *NotificationUpdate {
	nu.mutation.SetStatus(n)
	return nu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableStatus(n *notification.Status) *NotificationUpdate {
	if n != nil {
		nu.SetStatus(*n)
	}
	return nu
}

// SetReferenceID sets the "reference_id" field.
func (nu *NotificationUpdate) SetReferenceID(s string) *NotificationUpdate {
	nu.mutation.SetReferenceID(s)
	return nu
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableReferenceID(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetReferenceID(*s)
	}
	return nu
}

// ClearReferenceID clears the value of the "reference_id" field.
func (nu *NotificationUpdate) ClearReferenceID() *NotificationUpdate {
	nu.mutation.ClearReferenceID()
	return nu
}

// SetReferenceType sets the "reference_type" field.
func (nu *NotificationUpdate) SetReferenceType(s string) *NotificationUpdate {
	nu.mutation.SetReferenceType(s)
	return nu
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableReferenceType(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetReferenceType(*s)
	}
	return nu
}

// ClearReferenceType clears the value of the "reference_type" field.
func (nu *NotificationUpdate) ClearReferenceType() *NotificationUpdate {
	nu.mutation.ClearReferenceType()
	return nu
}

// SetDeliveryStatus sets the "delivery_status" field.
func (nu *NotificationUpdate) SetDeliveryStatus(m map[string]interface{}) *NotificationUpdate {
	nu.mutation.SetDeliveryStatus(m)
	return nu
}

// ClearDeliveryStatus clears the value of the "delivery_status" field.
func (nu *NotificationUpdate) ClearDeliveryStatus() *NotificationUpdate {
	nu.mutation.ClearDeliveryStatus()
	return nu
}

// SetScheduledAt sets the "scheduled_at" field.
func (nu *NotificationUpdate) SetScheduledAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetScheduledAt(t)
	return nu
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableScheduledAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetScheduledAt(*t)
	}
	return nu
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (nu *NotificationUpdate) ClearScheduledAt() *NotificationUpdate {
	nu.mutation.ClearScheduledAt()
	return nu
}

// SetSentAt sets the "sent_at" field.
func (nu *NotificationUpdate) SetSentAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetSentAt(t)
	return nu
}

// SetNillableSentAt sets the "sent_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableSentAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetSentAt(*t)
	}
	return nu
}

// ClearSentAt clears the value of the "sent_at" field.
func (nu *NotificationUpdate) ClearSentAt() *NotificationUpdate {
	nu.mutation.ClearSentAt()
	return nu
}

// SetDeliveredAt sets the "delivered_at" field.
func (nu *NotificationUpdate) SetDeliveredAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetDeliveredAt(t)
	return nu
}

// SetNillableDeliveredAt sets the "delivered_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableDeliveredAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetDeliveredAt(*t)
	}
	return nu
}

// ClearDeliveredAt clears the value of the "delivered_at" field.
func (nu *NotificationUpdate) ClearDeliveredAt() *NotificationUpdate {
	nu.mutation.ClearDeliveredAt()
	return nu
}

// SetReadAt sets the "read_at" field.
func (nu *NotificationUpdate) SetReadAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetReadAt(t)
	return nu
}

// SetNillableReadAt sets the "read_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableReadAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetReadAt(*t)
	}
	return nu
}

// ClearReadAt clears the value of the "read_at" field.
func (nu *NotificationUpdate) ClearReadAt() *NotificationUpdate {
	nu.mutation.ClearReadAt()
	return nu
}

// SetExpiresAt sets the "expires_at" field.
func (nu *NotificationUpdate) SetExpiresAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetExpiresAt(t)
	return nu
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableExpiresAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetExpiresAt(*t)
	}
	return nu
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (nu *NotificationUpdate) ClearExpiresAt() *NotificationUpdate {
	nu.mutation.ClearExpiresAt()
	return nu
}

// SetActionURL sets the "action_url" field.
func (nu *NotificationUpdate) SetActionURL(s string) *NotificationUpdate {
	nu.mutation.SetActionURL(s)
	return nu
}

// SetNillableActionURL sets the "action_url" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableActionURL(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetActionURL(*s)
	}
	return nu
}

// ClearActionURL clears the value of the "action_url" field.
func (nu *NotificationUpdate) ClearActionURL() *NotificationUpdate {
	nu.mutation.ClearActionURL()
	return nu
}

// SetActionData sets the "action_data" field.
func (nu *NotificationUpdate) SetActionData(m map[string]interface{}) *NotificationUpdate {
	nu.mutation.SetActionData(m)
	return nu
}

// ClearActionData clears the value of the "action_data" field.
func (nu *NotificationUpdate) ClearActionData() *NotificationUpdate {
	nu.mutation.ClearActionData()
	return nu
}

// SetRetryCount sets the "retry_count" field.
func (nu *NotificationUpdate) SetRetryCount(i int) *NotificationUpdate {
	nu.mutation.ResetRetryCount()
	nu.mutation.SetRetryCount(i)
	return nu
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableRetryCount(i *int) *NotificationUpdate {
	if i != nil {
		nu.SetRetryCount(*i)
	}
	return nu
}

// AddRetryCount adds i to the "retry_count" field.
func (nu *NotificationUpdate) AddRetryCount(i int) *NotificationUpdate {
	nu.mutation.AddRetryCount(i)
	return nu
}

// SetNextRetryAt sets the "next_retry_at" field.
func (nu *NotificationUpdate) SetNextRetryAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetNextRetryAt(t)
	return nu
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableNextRetryAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetNextRetryAt(*t)
	}
	return nu
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (nu *NotificationUpdate) ClearNextRetryAt() *NotificationUpdate {
	nu.mutation.ClearNextRetryAt()
	return nu
}

// SetErrorMessage sets the "error_message" field.
func (nu *NotificationUpdate) SetErrorMessage(s string) *NotificationUpdate {
	nu.mutation.SetErrorMessage(s)
	return nu
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableErrorMessage(s *string) *NotificationUpdate {
	if s != nil {
		nu.SetErrorMessage(*s)
	}
	return nu
}

// ClearErrorMessage clears the value of the "error_message" field.
func (nu *NotificationUpdate) ClearErrorMessage() *NotificationUpdate {
	nu.mutation.ClearErrorMessage()
	return nu
}

// SetMetadata sets the "metadata" field.
func (nu *NotificationUpdate) SetMetadata(m map[string]interface{}) *NotificationUpdate {
	nu.mutation.SetMetadata(m)
	return nu
}

// ClearMetadata clears the value of the "metadata" field.
func (nu *NotificationUpdate) ClearMetadata() *NotificationUpdate {
	nu.mutation.ClearMetadata()
	return nu
}

// SetUpdatedAt sets the "updated_at" field.
func (nu *NotificationUpdate) SetUpdatedAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetUpdatedAt(t)
	return nu
}

// SetDeletedAt sets the "deleted_at" field.
func (nu *NotificationUpdate) SetDeletedAt(t time.Time) *NotificationUpdate {
	nu.mutation.SetDeletedAt(t)
	return nu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (nu *NotificationUpdate) SetNillableDeletedAt(t *time.Time) *NotificationUpdate {
	if t != nil {
		nu.SetDeletedAt(*t)
	}
	return nu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (nu *NotificationUpdate) ClearDeletedAt() *NotificationUpdate {
	nu.mutation.ClearDeletedAt()
	return nu
}

// Mutation returns the NotificationMutation object of the builder.
func (nu *NotificationUpdate) Mutation() *NotificationMutation {
	return nu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (nu *NotificationUpdate) Save(ctx context.Context) (int, error) {
	nu.defaults()
	return withHooks(ctx, nu.sqlSave, nu.mutation, nu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nu *NotificationUpdate) SaveX(ctx context.Context) int {
	affected, err := nu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (nu *NotificationUpdate) Exec(ctx context.Context) error {
	_, err := nu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nu *NotificationUpdate) ExecX(ctx context.Context) {
	if err := nu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nu *NotificationUpdate) defaults() {
	if _, ok := nu.mutation.UpdatedAt(); !ok {
		v := notification.UpdateDefaultUpdatedAt()
		nu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nu *NotificationUpdate) check() error {
	if v, ok := nu.mutation.GetType(); ok {
		if err := notification.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Notification.type": %w`, err)}
		}
	}
	if v, ok := nu.mutation.Title(); ok {
		if err := notification.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Notification.title": %w`, err)}
		}
	}
	if v, ok := nu.mutation.Priority(); ok {
		if err := notification.PriorityValidator(v); err != nil {
			return &ValidationError{Name: "priority", err: fmt.Errorf(`ent: validator failed for field "Notification.priority": %w`, err)}
		}
	}
	if v, ok := nu.mutation.Status(); ok {
		if err := notification.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Notification.status": %w`, err)}
		}
	}
	if v, ok := nu.mutation.ReferenceID(); ok {
		if err := notification.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_id": %w`, err)}
		}
	}
	if v, ok := nu.mutation.ReferenceType(); ok {
		if err := notification.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_type": %w`, err)}
		}
	}
	if v, ok := nu.mutation.ActionURL(); ok {
		if err := notification.ActionURLValidator(v); err != nil {
			return &ValidationError{Name: "action_url", err: fmt.Errorf(`ent: validator failed for field "Notification.action_url": %w`, err)}
		}
	}
	if v, ok := nu.mutation.ErrorMessage(); ok {
		if err := notification.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "Notification.error_message": %w`, err)}
		}
	}
	return nil
}

func (nu *NotificationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := nu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notification.Table, notification.Columns, sqlgraph.NewFieldSpec(notification.FieldID, field.TypeUUID))
	if ps := nu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := nu.mutation.UserID(); ok {
		_spec.SetField(notification.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := nu.mutation.GetType(); ok {
		_spec.SetField(notification.FieldType, field.TypeEnum, value)
	}
	if value, ok := nu.mutation.Title(); ok {
		_spec.SetField(notification.FieldTitle, field.TypeString, value)
	}
	if value, ok := nu.mutation.Message(); ok {
		_spec.SetField(notification.FieldMessage, field.TypeString, value)
	}
	if value, ok := nu.mutation.Data(); ok {
		_spec.SetField(notification.FieldData, field.TypeJSON, value)
	}
	if nu.mutation.DataCleared() {
		_spec.ClearField(notification.FieldData, field.TypeJSON)
	}
	if value, ok := nu.mutation.Priority(); ok {
		_spec.SetField(notification.FieldPriority, field.TypeEnum, value)
	}
	if value, ok := nu.mutation.Channels(); ok {
		_spec.SetField(notification.FieldChannels, field.TypeJSON, value)
	}
	if value, ok := nu.mutation.AppendedChannels(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notification.FieldChannels, value)
		})
	}
	if nu.mutation.ChannelsCleared() {
		_spec.ClearField(notification.FieldChannels, field.TypeJSON)
	}
	if value, ok := nu.mutation.Status(); ok {
		_spec.SetField(notification.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := nu.mutation.ReferenceID(); ok {
		_spec.SetField(notification.FieldReferenceID, field.TypeString, value)
	}
	if nu.mutation.ReferenceIDCleared() {
		_spec.ClearField(notification.FieldReferenceID, field.TypeString)
	}
	if value, ok := nu.mutation.ReferenceType(); ok {
		_spec.SetField(notification.FieldReferenceType, field.TypeString, value)
	}
	if nu.mutation.ReferenceTypeCleared() {
		_spec.ClearField(notification.FieldReferenceType, field.TypeString)
	}
	if value, ok := nu.mutation.DeliveryStatus(); ok {
		_spec.SetField(notification.FieldDeliveryStatus, field.TypeJSON, value)
	}
	if nu.mutation.DeliveryStatusCleared() {
		_spec.ClearField(notification.FieldDeliveryStatus, field.TypeJSON)
	}
	if value, ok := nu.mutation.ScheduledAt(); ok {
		_spec.SetField(notification.FieldScheduledAt, field.TypeTime, value)
	}
	if nu.mutation.ScheduledAtCleared() {
		_spec.ClearField(notification.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := nu.mutation.SentAt(); ok {
		_spec.SetField(notification.FieldSentAt, field.TypeTime, value)
	}
	if nu.mutation.SentAtCleared() {
		_spec.ClearField(notification.FieldSentAt, field.TypeTime)
	}
	if value, ok := nu.mutation.DeliveredAt(); ok {
		_spec.SetField(notification.FieldDeliveredAt, field.TypeTime, value)
	}
	if nu.mutation.DeliveredAtCleared() {
		_spec.ClearField(notification.FieldDeliveredAt, field.TypeTime)
	}
	if value, ok := nu.mutation.ReadAt(); ok {
		_spec.SetField(notification.FieldReadAt, field.TypeTime, value)
	}
	if nu.mutation.ReadAtCleared() {
		_spec.ClearField(notification.FieldReadAt, field.TypeTime)
	}
	if value, ok := nu.mutation.ExpiresAt(); ok {
		_spec.SetField(notification.FieldExpiresAt, field.TypeTime, value)
	}
	if nu.mutation.ExpiresAtCleared() {
		_spec.ClearField(notification.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := nu.mutation.ActionURL(); ok {
		_spec.SetField(notification.FieldActionURL, field.TypeString, value)
	}
	if nu.mutation.ActionURLCleared() {
		_spec.ClearField(notification.FieldActionURL, field.TypeString)
	}
	if value, ok := nu.mutation.ActionData(); ok {
		_spec.SetField(notification.FieldActionData, field.TypeJSON, value)
	}
	if nu.mutation.ActionDataCleared() {
		_spec.ClearField(notification.FieldActionData, field.TypeJSON)
	}
	if value, ok := nu.mutation.RetryCount(); ok {
		_spec.SetField(notification.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := nu.mutation.AddedRetryCount(); ok {
		_spec.AddField(notification.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := nu.mutation.NextRetryAt(); ok {
		_spec.SetField(notification.FieldNextRetryAt, field.TypeTime, value)
	}
	if nu.mutation.NextRetryAtCleared() {
		_spec.ClearField(notification.FieldNextRetryAt, field.TypeTime)
	}
	if value, ok := nu.mutation.ErrorMessage(); ok {
		_spec.SetField(notification.FieldErrorMessage, field.TypeString, value)
	}
	if nu.mutation.ErrorMessageCleared() {
		_spec.ClearField(notification.FieldErrorMessage, field.TypeString)
	}
	if value, ok := nu.mutation.Metadata(); ok {
		_spec.SetField(notification.FieldMetadata, field.TypeJSON, value)
	}
	if nu.mutation.MetadataCleared() {
		_spec.ClearField(notification.FieldMetadata, field.TypeJSON)
	}
	if value, ok := nu.mutation.UpdatedAt(); ok {
		_spec.SetField(notification.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := nu.mutation.DeletedAt(); ok {
		_spec.SetField(notification.FieldDeletedAt, field.TypeTime, value)
	}
	if nu.mutation.DeletedAtCleared() {
		_spec.ClearField(notification.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, nu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notification.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	nu.mutation.done = true
	return n, nil
}

// NotificationUpdateOne is the builder for updating a single Notification entity.
type NotificationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *NotificationMutation
}

// SetUserID sets the "user_id" field.
func (nuo *NotificationUpdateOne) SetUserID(u uuid.UUID) *NotificationUpdateOne {
	nuo.mutation.SetUserID(u)
	return nuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableUserID(u *uuid.UUID) *NotificationUpdateOne {
	if u != nil {
		nuo.SetUserID(*u)
	}
	return nuo
}

// SetType sets the "type" field.
func (nuo *NotificationUpdateOne) SetType(n notification.Type) *NotificationUpdateOne {
	nuo.mutation.SetType(n)
	return nuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableType(n *notification.Type) *NotificationUpdateOne {
	if n != nil {
		nuo.SetType(*n)
	}
	return nuo
}

// SetTitle sets the "title" field.
func (nuo *NotificationUpdateOne) SetTitle(s string) *NotificationUpdateOne {
	nuo.mutation.SetTitle(s)
	return nuo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableTitle(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetTitle(*s)
	}
	return nuo
}

// SetMessage sets the "message" field.
func (nuo *NotificationUpdateOne) SetMessage(s string) *NotificationUpdateOne {
	nuo.mutation.SetMessage(s)
	return nuo
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableMessage(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetMessage(*s)
	}
	return nuo
}

// SetData sets the "data" field.
func (nuo *NotificationUpdateOne) SetData(m map[string]interface{}) *NotificationUpdateOne {
	nuo.mutation.SetData(m)
	return nuo
}

// ClearData clears the value of the "data" field.
func (nuo *NotificationUpdateOne) ClearData() *NotificationUpdateOne {
	nuo.mutation.ClearData()
	return nuo
}

// SetPriority sets the "priority" field.
func (nuo *NotificationUpdateOne) SetPriority(n notification.Priority) *NotificationUpdateOne {
	nuo.mutation.SetPriority(n)
	return nuo
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillablePriority(n *notification.Priority) *NotificationUpdateOne {
	if n != nil {
		nuo.SetPriority(*n)
	}
	return nuo
}

// SetChannels sets the "channels" field.
func (nuo *NotificationUpdateOne) SetChannels(s []string) *NotificationUpdateOne {
	nuo.mutation.SetChannels(s)
	return nuo
}

// AppendChannels appends s to the "channels" field.
func (nuo *NotificationUpdateOne) AppendChannels(s []string) *NotificationUpdateOne {
	nuo.mutation.AppendChannels(s)
	return nuo
}

// ClearChannels clears the value of the "channels" field.
func (nuo *NotificationUpdateOne) ClearChannels() *NotificationUpdateOne {
	nuo.mutation.ClearChannels()
	return nuo
}

// SetStatus sets the "status" field.
func (nuo *NotificationUpdateOne) SetStatus(n notification.Status) *NotificationUpdateOne {
	nuo.mutation.SetStatus(n)
	return nuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableStatus(n *notification.Status) *NotificationUpdateOne {
	if n != nil {
		nuo.SetStatus(*n)
	}
	return nuo
}

// SetReferenceID sets the "reference_id" field.
func (nuo *NotificationUpdateOne) SetReferenceID(s string) *NotificationUpdateOne {
	nuo.mutation.SetReferenceID(s)
	return nuo
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableReferenceID(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetReferenceID(*s)
	}
	return nuo
}

// ClearReferenceID clears the value of the "reference_id" field.
func (nuo *NotificationUpdateOne) ClearReferenceID() *NotificationUpdateOne {
	nuo.mutation.ClearReferenceID()
	return nuo
}

// SetReferenceType sets the "reference_type" field.
func (nuo *NotificationUpdateOne) SetReferenceType(s string) *NotificationUpdateOne {
	nuo.mutation.SetReferenceType(s)
	return nuo
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableReferenceType(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetReferenceType(*s)
	}
	return nuo
}

// ClearReferenceType clears the value of the "reference_type" field.
func (nuo *NotificationUpdateOne) ClearReferenceType() *NotificationUpdateOne {
	nuo.mutation.ClearReferenceType()
	return nuo
}

// SetDeliveryStatus sets the "delivery_status" field.
func (nuo *NotificationUpdateOne) SetDeliveryStatus(m map[string]interface{}) *NotificationUpdateOne {
	nuo.mutation.SetDeliveryStatus(m)
	return nuo
}

// ClearDeliveryStatus clears the value of the "delivery_status" field.
func (nuo *NotificationUpdateOne) ClearDeliveryStatus() *NotificationUpdateOne {
	nuo.mutation.ClearDeliveryStatus()
	return nuo
}

// SetScheduledAt sets the "scheduled_at" field.
func (nuo *NotificationUpdateOne) SetScheduledAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetScheduledAt(t)
	return nuo
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableScheduledAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetScheduledAt(*t)
	}
	return nuo
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (nuo *NotificationUpdateOne) ClearScheduledAt() *NotificationUpdateOne {
	nuo.mutation.ClearScheduledAt()
	return nuo
}

// SetSentAt sets the "sent_at" field.
func (nuo *NotificationUpdateOne) SetSentAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetSentAt(t)
	return nuo
}

// SetNillableSentAt sets the "sent_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableSentAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetSentAt(*t)
	}
	return nuo
}

// ClearSentAt clears the value of the "sent_at" field.
func (nuo *NotificationUpdateOne) ClearSentAt() *NotificationUpdateOne {
	nuo.mutation.ClearSentAt()
	return nuo
}

// SetDeliveredAt sets the "delivered_at" field.
func (nuo *NotificationUpdateOne) SetDeliveredAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetDeliveredAt(t)
	return nuo
}

// SetNillableDeliveredAt sets the "delivered_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableDeliveredAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetDeliveredAt(*t)
	}
	return nuo
}

// ClearDeliveredAt clears the value of the "delivered_at" field.
func (nuo *NotificationUpdateOne) ClearDeliveredAt() *NotificationUpdateOne {
	nuo.mutation.ClearDeliveredAt()
	return nuo
}

// SetReadAt sets the "read_at" field.
func (nuo *NotificationUpdateOne) SetReadAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetReadAt(t)
	return nuo
}

// SetNillableReadAt sets the "read_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableReadAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetReadAt(*t)
	}
	return nuo
}

// ClearReadAt clears the value of the "read_at" field.
func (nuo *NotificationUpdateOne) ClearReadAt() *NotificationUpdateOne {
	nuo.mutation.ClearReadAt()
	return nuo
}

// SetExpiresAt sets the "expires_at" field.
func (nuo *NotificationUpdateOne) SetExpiresAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetExpiresAt(t)
	return nuo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableExpiresAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetExpiresAt(*t)
	}
	return nuo
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (nuo *NotificationUpdateOne) ClearExpiresAt() *NotificationUpdateOne {
	nuo.mutation.ClearExpiresAt()
	return nuo
}

// SetActionURL sets the "action_url" field.
func (nuo *NotificationUpdateOne) SetActionURL(s string) *NotificationUpdateOne {
	nuo.mutation.SetActionURL(s)
	return nuo
}

// SetNillableActionURL sets the "action_url" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableActionURL(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetActionURL(*s)
	}
	return nuo
}

// ClearActionURL clears the value of the "action_url" field.
func (nuo *NotificationUpdateOne) ClearActionURL() *NotificationUpdateOne {
	nuo.mutation.ClearActionURL()
	return nuo
}

// SetActionData sets the "action_data" field.
func (nuo *NotificationUpdateOne) SetActionData(m map[string]interface{}) *NotificationUpdateOne {
	nuo.mutation.SetActionData(m)
	return nuo
}

// ClearActionData clears the value of the "action_data" field.
func (nuo *NotificationUpdateOne) ClearActionData() *NotificationUpdateOne {
	nuo.mutation.ClearActionData()
	return nuo
}

// SetRetryCount sets the "retry_count" field.
func (nuo *NotificationUpdateOne) SetRetryCount(i int) *NotificationUpdateOne {
	nuo.mutation.ResetRetryCount()
	nuo.mutation.SetRetryCount(i)
	return nuo
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableRetryCount(i *int) *NotificationUpdateOne {
	if i != nil {
		nuo.SetRetryCount(*i)
	}
	return nuo
}

// AddRetryCount adds i to the "retry_count" field.
func (nuo *NotificationUpdateOne) AddRetryCount(i int) *NotificationUpdateOne {
	nuo.mutation.AddRetryCount(i)
	return nuo
}

// SetNextRetryAt sets the "next_retry_at" field.
func (nuo *NotificationUpdateOne) SetNextRetryAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetNextRetryAt(t)
	return nuo
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableNextRetryAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetNextRetryAt(*t)
	}
	return nuo
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (nuo *NotificationUpdateOne) ClearNextRetryAt() *NotificationUpdateOne {
	nuo.mutation.ClearNextRetryAt()
	return nuo
}

// SetErrorMessage sets the "error_message" field.
func (nuo *NotificationUpdateOne) SetErrorMessage(s string) *NotificationUpdateOne {
	nuo.mutation.SetErrorMessage(s)
	return nuo
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableErrorMessage(s *string) *NotificationUpdateOne {
	if s != nil {
		nuo.SetErrorMessage(*s)
	}
	return nuo
}

// ClearErrorMessage clears the value of the "error_message" field.
func (nuo *NotificationUpdateOne) ClearErrorMessage() *NotificationUpdateOne {
	nuo.mutation.ClearErrorMessage()
	return nuo
}

// SetMetadata sets the "metadata" field.
func (nuo *NotificationUpdateOne) SetMetadata(m map[string]interface{}) *NotificationUpdateOne {
	nuo.mutation.SetMetadata(m)
	return nuo
}

// ClearMetadata clears the value of the "metadata" field.
func (nuo *NotificationUpdateOne) ClearMetadata() *NotificationUpdateOne {
	nuo.mutation.ClearMetadata()
	return nuo
}

// SetUpdatedAt sets the "updated_at" field.
func (nuo *NotificationUpdateOne) SetUpdatedAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetUpdatedAt(t)
	return nuo
}

// SetDeletedAt sets the "deleted_at" field.
func (nuo *NotificationUpdateOne) SetDeletedAt(t time.Time) *NotificationUpdateOne {
	nuo.mutation.SetDeletedAt(t)
	return nuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (nuo *NotificationUpdateOne) SetNillableDeletedAt(t *time.Time) *NotificationUpdateOne {
	if t != nil {
		nuo.SetDeletedAt(*t)
	}
	return nuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (nuo *NotificationUpdateOne) ClearDeletedAt() *NotificationUpdateOne {
	nuo.mutation.ClearDeletedAt()
	return nuo
}

// Mutation returns the NotificationMutation object of the builder.
func (nuo *NotificationUpdateOne) Mutation() *NotificationMutation {
	return nuo.mutation
}

// Where appends a list predicates to the NotificationUpdate builder.
func (nuo *NotificationUpdateOne) Where(ps ...predicate.Notification) *NotificationUpdateOne {
	nuo.mutation.Where(ps...)
	return nuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (nuo *NotificationUpdateOne) Select(field string, fields ...string) *NotificationUpdateOne {
	nuo.fields = append([]string{field}, fields...)
	return nuo
}

// Save executes the query and returns the updated Notification entity.
func (nuo *NotificationUpdateOne) Save(ctx context.Context) (*Notification, error) {
	nuo.defaults()
	return withHooks(ctx, nuo.sqlSave, nuo.mutation, nuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nuo *NotificationUpdateOne) SaveX(ctx context.Context) *Notification {
	node, err := nuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (nuo *NotificationUpdateOne) Exec(ctx context.Context) error {
	_, err := nuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nuo *NotificationUpdateOne) ExecX(ctx context.Context) {
	if err := nuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nuo *NotificationUpdateOne) defaults() {
	if _, ok := nuo.mutation.UpdatedAt(); !ok {
		v := notification.UpdateDefaultUpdatedAt()
		nuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nuo *NotificationUpdateOne) check() error {
	if v, ok := nuo.mutation.GetType(); ok {
		if err := notification.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Notification.type": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.Title(); ok {
		if err := notification.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Notification.title": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.Priority(); ok {
		if err := notification.PriorityValidator(v); err != nil {
			return &ValidationError{Name: "priority", err: fmt.Errorf(`ent: validator failed for field "Notification.priority": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.Status(); ok {
		if err := notification.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Notification.status": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.ReferenceID(); ok {
		if err := notification.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_id": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.ReferenceType(); ok {
		if err := notification.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_type": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.ActionURL(); ok {
		if err := notification.ActionURLValidator(v); err != nil {
			return &ValidationError{Name: "action_url", err: fmt.Errorf(`ent: validator failed for field "Notification.action_url": %w`, err)}
		}
	}
	if v, ok := nuo.mutation.ErrorMessage(); ok {
		if err := notification.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "Notification.error_message": %w`, err)}
		}
	}
	return nil
}

func (nuo *NotificationUpdateOne) sqlSave(ctx context.Context) (_node *Notification, err error) {
	if err := nuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notification.Table, notification.Columns, sqlgraph.NewFieldSpec(notification.FieldID, field.TypeUUID))
	id, ok := nuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Notification.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := nuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notification.FieldID)
		for _, f := range fields {
			if !notification.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notification.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := nuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := nuo.mutation.UserID(); ok {
		_spec.SetField(notification.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := nuo.mutation.GetType(); ok {
		_spec.SetField(notification.FieldType, field.TypeEnum, value)
	}
	if value, ok := nuo.mutation.Title(); ok {
		_spec.SetField(notification.FieldTitle, field.TypeString, value)
	}
	if value, ok := nuo.mutation.Message(); ok {
		_spec.SetField(notification.FieldMessage, field.TypeString, value)
	}
	if value, ok := nuo.mutation.Data(); ok {
		_spec.SetField(notification.FieldData, field.TypeJSON, value)
	}
	if nuo.mutation.DataCleared() {
		_spec.ClearField(notification.FieldData, field.TypeJSON)
	}
	if value, ok := nuo.mutation.Priority(); ok {
		_spec.SetField(notification.FieldPriority, field.TypeEnum, value)
	}
	if value, ok := nuo.mutation.Channels(); ok {
		_spec.SetField(notification.FieldChannels, field.TypeJSON, value)
	}
	if value, ok := nuo.mutation.AppendedChannels(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, notification.FieldChannels, value)
		})
	}
	if nuo.mutation.ChannelsCleared() {
		_spec.ClearField(notification.FieldChannels, field.TypeJSON)
	}
	if value, ok := nuo.mutation.Status(); ok {
		_spec.SetField(notification.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := nuo.mutation.ReferenceID(); ok {
		_spec.SetField(notification.FieldReferenceID, field.TypeString, value)
	}
	if nuo.mutation.ReferenceIDCleared() {
		_spec.ClearField(notification.FieldReferenceID, field.TypeString)
	}
	if value, ok := nuo.mutation.ReferenceType(); ok {
		_spec.SetField(notification.FieldReferenceType, field.TypeString, value)
	}
	if nuo.mutation.ReferenceTypeCleared() {
		_spec.ClearField(notification.FieldReferenceType, field.TypeString)
	}
	if value, ok := nuo.mutation.DeliveryStatus(); ok {
		_spec.SetField(notification.FieldDeliveryStatus, field.TypeJSON, value)
	}
	if nuo.mutation.DeliveryStatusCleared() {
		_spec.ClearField(notification.FieldDeliveryStatus, field.TypeJSON)
	}
	if value, ok := nuo.mutation.ScheduledAt(); ok {
		_spec.SetField(notification.FieldScheduledAt, field.TypeTime, value)
	}
	if nuo.mutation.ScheduledAtCleared() {
		_spec.ClearField(notification.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.SentAt(); ok {
		_spec.SetField(notification.FieldSentAt, field.TypeTime, value)
	}
	if nuo.mutation.SentAtCleared() {
		_spec.ClearField(notification.FieldSentAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.DeliveredAt(); ok {
		_spec.SetField(notification.FieldDeliveredAt, field.TypeTime, value)
	}
	if nuo.mutation.DeliveredAtCleared() {
		_spec.ClearField(notification.FieldDeliveredAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.ReadAt(); ok {
		_spec.SetField(notification.FieldReadAt, field.TypeTime, value)
	}
	if nuo.mutation.ReadAtCleared() {
		_spec.ClearField(notification.FieldReadAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.ExpiresAt(); ok {
		_spec.SetField(notification.FieldExpiresAt, field.TypeTime, value)
	}
	if nuo.mutation.ExpiresAtCleared() {
		_spec.ClearField(notification.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.ActionURL(); ok {
		_spec.SetField(notification.FieldActionURL, field.TypeString, value)
	}
	if nuo.mutation.ActionURLCleared() {
		_spec.ClearField(notification.FieldActionURL, field.TypeString)
	}
	if value, ok := nuo.mutation.ActionData(); ok {
		_spec.SetField(notification.FieldActionData, field.TypeJSON, value)
	}
	if nuo.mutation.ActionDataCleared() {
		_spec.ClearField(notification.FieldActionData, field.TypeJSON)
	}
	if value, ok := nuo.mutation.RetryCount(); ok {
		_spec.SetField(notification.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := nuo.mutation.AddedRetryCount(); ok {
		_spec.AddField(notification.FieldRetryCount, field.TypeInt, value)
	}
	if value, ok := nuo.mutation.NextRetryAt(); ok {
		_spec.SetField(notification.FieldNextRetryAt, field.TypeTime, value)
	}
	if nuo.mutation.NextRetryAtCleared() {
		_spec.ClearField(notification.FieldNextRetryAt, field.TypeTime)
	}
	if value, ok := nuo.mutation.ErrorMessage(); ok {
		_spec.SetField(notification.FieldErrorMessage, field.TypeString, value)
	}
	if nuo.mutation.ErrorMessageCleared() {
		_spec.ClearField(notification.FieldErrorMessage, field.TypeString)
	}
	if value, ok := nuo.mutation.Metadata(); ok {
		_spec.SetField(notification.FieldMetadata, field.TypeJSON, value)
	}
	if nuo.mutation.MetadataCleared() {
		_spec.ClearField(notification.FieldMetadata, field.TypeJSON)
	}
	if value, ok := nuo.mutation.UpdatedAt(); ok {
		_spec.SetField(notification.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := nuo.mutation.DeletedAt(); ok {
		_spec.SetField(notification.FieldDeletedAt, field.TypeTime, value)
	}
	if nuo.mutation.DeletedAtCleared() {
		_spec.ClearField(notification.FieldDeletedAt, field.TypeTime)
	}
	_node = &Notification{config: nuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, nuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notification.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	nuo.mutation.done = true
	return _node, nil
}

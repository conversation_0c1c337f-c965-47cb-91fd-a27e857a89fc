package models

// CleanupStatsResponse represents cleanup statistics response
type CleanupStatsResponse struct {
	TotalUnverified   int    `json:"total_unverified" example:"150"`
	ExpiredCount      int    `json:"expired_count" example:"25"`
	WarningCount      int    `json:"warning_count" example:"10"`
	NextCleanupIn     string `json:"next_cleanup_in" example:"45m30s"`
	UnverifiedUserTTL string `json:"unverified_user_ttl" example:"24h0m0s"`
	ServiceRunning    bool   `json:"service_running" example:"true"`
}

// CleanupStatusResponse represents cleanup service status response
type CleanupStatusResponse struct {
	ServiceRunning bool   `json:"service_running" example:"true"`
	ServiceName    string `json:"service_name" example:"user-cleanup-service"`
	Status         string `json:"status" example:"running"`
	Message        string `json:"message" example:"Cleanup service is running normally"`
}

// CleanupConfigResponse represents cleanup configuration response
type CleanupConfigResponse struct {
	UnverifiedUserTTL    string `json:"unverified_user_ttl" example:"24h0m0s"`
	CleanupInterval      string `json:"cleanup_interval" example:"1h0m0s"`
	BatchSize           int    `json:"batch_size" example:"100"`
	EnableNotifications bool   `json:"enable_notifications" example:"true"`
	WarningTime         string `json:"warning_time" example:"2h0m0s"`
}

// UserDeletionWarningEvent represents a user deletion warning event
type UserDeletionWarningEvent struct {
	UserID        string `json:"user_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Email         string `json:"email" example:"<EMAIL>"`
	FullName      string `json:"full_name" example:"John Doe"`
	CreatedAt     string `json:"created_at" example:"2023-12-01T10:00:00Z"`
	DeletionTime  string `json:"deletion_time" example:"2023-12-02T12:00:00Z"`
	HoursLeft     int    `json:"hours_left" example:"2"`
	VerificationURL string `json:"verification_url" example:"https://app.social-content-ai.com/auth/resend-verification?email=<EMAIL>"`
}

// UserDeletionEvent represents a user deletion event
type UserDeletionEvent struct {
	UserID    string `json:"user_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	Email     string `json:"email" example:"<EMAIL>"`
	FullName  string `json:"full_name" example:"John Doe"`
	CreatedAt string `json:"created_at" example:"2023-12-01T10:00:00Z"`
	DeletedAt string `json:"deleted_at" example:"2023-12-02T10:00:00Z"`
	Reason    string `json:"reason" example:"email_not_verified"`
	TTLHours  string `json:"ttl_hours" example:"24"`
	AgeHours  float64 `json:"age_hours" example:"24.5"`
}

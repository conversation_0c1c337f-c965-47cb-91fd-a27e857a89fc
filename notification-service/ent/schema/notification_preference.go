package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// NotificationPreference holds the schema definition for the NotificationPreference entity.
type NotificationPreference struct {
	ent.Schema
}

// Fields of the NotificationPreference.
func (NotificationPreference) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Unique().
			Comment("User these preferences belong to"),
		field.Bool("email_enabled").
			Default(true).
			Comment("Whether email notifications are enabled"),
		field.Bool("push_enabled").
			Default(true).
			Comment("Whether push notifications are enabled"),
		field.Bool("in_app_enabled").
			Default(true).
			Comment("Whether in-app notifications are enabled"),
		field.Bool("sms_enabled").
			Default(false).
			Comment("Whether SMS notifications are enabled"),
		field.JSON("notification_types", map[string]interface{}{}).
			Optional().
			Comment("Per-type notification preferences"),
		field.JSON("quiet_hours", map[string]interface{}{}).
			Optional().
			Comment("Quiet hours configuration"),
		field.String("timezone").
			MaxLen(50).
			Default("UTC").
			Comment("User timezone for scheduling"),
		field.String("language").
			MaxLen(5).
			Default("en").
			Comment("Preferred language for notifications"),
		field.JSON("email_settings", map[string]interface{}{}).
			Optional().
			Comment("Email-specific settings"),
		field.JSON("push_settings", map[string]interface{}{}).
			Optional().
			Comment("Push notification settings"),
		field.JSON("frequency_settings", map[string]interface{}{}).
			Optional().
			Comment("Frequency settings for different notification types"),
		field.Bool("marketing_emails").
			Default(false).
			Comment("Whether to receive marketing emails"),
		field.Bool("product_updates").
			Default(true).
			Comment("Whether to receive product update notifications"),
		field.Bool("security_alerts").
			Default(true).
			Comment("Whether to receive security alerts"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the NotificationPreference.
func (NotificationPreference) Edges() []ent.Edge {
	return nil
}

// Indexes of the NotificationPreference.
func (NotificationPreference) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id").Unique(),
		index.Fields("email_enabled"),
		index.Fields("push_enabled"),
		index.Fields("timezone"),
		index.Fields("language"),
	}
}

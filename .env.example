# Social Content AI Platform - Environment Configuration

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
SERVER_ENV=development
SERVER_HTTP_PORT=8080
SERVER_GRPC_PORT=50051

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=socialai
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_SSL_MODE=disable
DATABASE_MAX_CONNECTIONS=25
DATABASE_MIN_CONNECTIONS=5
DATABASE_MAX_CONN_LIFETIME=300
DATABASE_MAX_CONN_IDLE_TIME=60

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_POOL_SIZE=10

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
KAFKA_BROKERS=localhost:9092
KAFKA_GROUP_ID=socialai
KAFKA_AUTO_OFFSET_RESET=earliest
KAFKA_ENABLE_AUTO_COMMIT=true

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_TOKEN_DURATION=24h
JWT_REFRESH_DURATION=168h
JWT_ISSUER=socialai

# =============================================================================
# AI PROVIDERS CONFIGURATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# =============================================================================
# PAYMENT PROCESSING (STRIPE)
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# =============================================================================
# STORAGE CONFIGURATION (MINIO/S3)
# =============================================================================
STORAGE_TYPE=minio
STORAGE_ENDPOINT=localhost:9000
STORAGE_ACCESS_KEY=minioadmin
STORAGE_SECRET_KEY=minioadmin
STORAGE_BUCKET=social-content-ai-assets
STORAGE_USE_SSL=false
STORAGE_PUBLIC_URL=http://localhost:9000
STORAGE_REGION=us-east-1

# =============================================================================
# EMAIL CONFIGURATION (SMTP)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Social Content AI
SMTP_USE_TLS=true

# =============================================================================
# SOCIAL MEDIA OAUTH CONFIGURATION
# =============================================================================
# Facebook OAuth
FACEBOOK_CLIENT_ID=your_facebook_app_id_here
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret_here
FACEBOOK_REDIRECT_URL=http://localhost:8080/api/v1/integrations/facebook/callback

# Twitter OAuth
TWITTER_CLIENT_ID=your_twitter_client_id_here
TWITTER_CLIENT_SECRET=your_twitter_client_secret_here
TWITTER_REDIRECT_URL=http://localhost:8080/api/v1/integrations/twitter/callback

# Instagram OAuth
INSTAGRAM_CLIENT_ID=your_instagram_client_id_here
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret_here
INSTAGRAM_REDIRECT_URL=http://localhost:8080/api/v1/integrations/instagram/callback

# LinkedIn OAuth
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here
LINKEDIN_REDIRECT_URL=http://localhost:8080/api/v1/integrations/linkedin/callback

# YouTube OAuth
YOUTUBE_CLIENT_ID=your_youtube_client_id_here
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret_here
YOUTUBE_REDIRECT_URL=http://localhost:8080/api/v1/integrations/youtube/callback

# TikTok OAuth
TIKTOK_CLIENT_ID=your_tiktok_client_id_here
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret_here
TIKTOK_REDIRECT_URL=http://localhost:8080/api/v1/integrations/tiktok/callback

# =============================================================================
# AI PROVIDER API KEYS
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_ORG_ID=your-openai-org-id
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MAX_RETRIES=3
OPENAI_TIMEOUT=30

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_MAX_RETRIES=3
ANTHROPIC_TIMEOUT=30

# Google AI Configuration
GOOGLE_API_KEY=your-google-ai-api-key-here
GOOGLE_PROJECT_ID=your-google-project-id
GOOGLE_MAX_RETRIES=3
GOOGLE_TIMEOUT=30

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret-here
STRIPE_PRICE_ID_BASIC=price_your-basic-plan-price-id
STRIPE_PRICE_ID_PRO=price_your-pro-plan-price-id
STRIPE_PRICE_ID_ENTERPRISE=price_your-enterprise-plan-price-id

# =============================================================================
# SOCIAL MEDIA PLATFORM INTEGRATIONS
# =============================================================================

# Facebook/Meta Configuration
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=http://localhost:8080/api/v1/integrations/facebook/callback

# Twitter/X Configuration
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret

# Instagram Configuration
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret
INSTAGRAM_REDIRECT_URI=http://localhost:8080/api/v1/integrations/instagram/callback

# LinkedIn Configuration
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
LINKEDIN_REDIRECT_URI=http://localhost:8080/api/v1/integrations/linkedin/callback

# TikTok Configuration
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret
TIKTOK_REDIRECT_URI=http://localhost:8080/api/v1/integrations/tiktok/callback

# YouTube Configuration
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret
YOUTUBE_REDIRECT_URI=http://localhost:8080/api/v1/integrations/youtube/callback

# =============================================================================
# STORAGE CONFIGURATION (MinIO/S3)
# =============================================================================
STORAGE_ENDPOINT=localhost:9000
STORAGE_ACCESS_KEY=minioadmin
STORAGE_SECRET_KEY=minioadmin
STORAGE_BUCKET=socialai
STORAGE_REGION=us-east-1
STORAGE_USE_SSL=false
STORAGE_PUBLIC_URL=http://localhost:9000

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_PROVIDER=smtp
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your-app-password
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Social Content AI

# SendGrid Configuration (alternative)
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_ENABLED=true
GRAFANA_PORT=3000

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=10

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization,X-Requested-With

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_RAG_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_NOTIFICATIONS_ENABLED=true
FEATURE_INTEGRATIONS_ENABLED=true
FEATURE_SCHEDULING_ENABLED=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
DEBUG_ENABLED=true
SWAGGER_ENABLED=true
PROFILING_ENABLED=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=socialai-backups

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_TTL_DEFAULT=3600
CACHE_TTL_USER_SESSION=86400
CACHE_TTL_CONTENT_GENERATION=1800
CACHE_TTL_ANALYTICS=300

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=30
WEBHOOK_MAX_RETRIES=3

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=60
ANALYTICS_RETENTION_DAYS=365

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
NOTIFICATION_CHANNELS=email,push,webhook
NOTIFICATION_BATCH_SIZE=50
NOTIFICATION_RETRY_ATTEMPTS=3

# =============================================================================
# CONTENT MODERATION
# =============================================================================
MODERATION_ENABLED=true
MODERATION_PROVIDER=openai
MODERATION_THRESHOLD=0.8

# =============================================================================
# INTERNATIONALIZATION
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,it,pt,ja,ko,zh

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
MAX_CONCURRENT_GENERATIONS=10
MAX_CONCURRENT_IMPROVEMENTS=5
MAX_CONCURRENT_UPLOADS=20
REQUEST_TIMEOUT=30
WORKER_POOL_SIZE=10

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_RETRIES=3

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
)

// PlatformIntegrationCreate is the builder for creating a PlatformIntegration entity.
type PlatformIntegrationCreate struct {
	config
	mutation *PlatformIntegrationMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (pic *PlatformIntegrationCreate) SetUserID(u uuid.UUID) *PlatformIntegrationCreate {
	pic.mutation.SetUserID(u)
	return pic
}

// SetWorkspaceID sets the "workspace_id" field.
func (pic *PlatformIntegrationCreate) SetWorkspaceID(s string) *PlatformIntegrationCreate {
	pic.mutation.SetWorkspaceID(s)
	return pic
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableWorkspaceID(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetWorkspaceID(*s)
	}
	return pic
}

// SetPlatform sets the "platform" field.
func (pic *PlatformIntegrationCreate) SetPlatform(pl platformintegration.Platform) *PlatformIntegrationCreate {
	pic.mutation.SetPlatform(pl)
	return pic
}

// SetPlatformUserID sets the "platform_user_id" field.
func (pic *PlatformIntegrationCreate) SetPlatformUserID(s string) *PlatformIntegrationCreate {
	pic.mutation.SetPlatformUserID(s)
	return pic
}

// SetPlatformUsername sets the "platform_username" field.
func (pic *PlatformIntegrationCreate) SetPlatformUsername(s string) *PlatformIntegrationCreate {
	pic.mutation.SetPlatformUsername(s)
	return pic
}

// SetNillablePlatformUsername sets the "platform_username" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillablePlatformUsername(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetPlatformUsername(*s)
	}
	return pic
}

// SetPlatformDisplayName sets the "platform_display_name" field.
func (pic *PlatformIntegrationCreate) SetPlatformDisplayName(s string) *PlatformIntegrationCreate {
	pic.mutation.SetPlatformDisplayName(s)
	return pic
}

// SetNillablePlatformDisplayName sets the "platform_display_name" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillablePlatformDisplayName(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetPlatformDisplayName(*s)
	}
	return pic
}

// SetPlatformAvatarURL sets the "platform_avatar_url" field.
func (pic *PlatformIntegrationCreate) SetPlatformAvatarURL(s string) *PlatformIntegrationCreate {
	pic.mutation.SetPlatformAvatarURL(s)
	return pic
}

// SetNillablePlatformAvatarURL sets the "platform_avatar_url" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillablePlatformAvatarURL(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetPlatformAvatarURL(*s)
	}
	return pic
}

// SetAccessToken sets the "access_token" field.
func (pic *PlatformIntegrationCreate) SetAccessToken(s string) *PlatformIntegrationCreate {
	pic.mutation.SetAccessToken(s)
	return pic
}

// SetRefreshToken sets the "refresh_token" field.
func (pic *PlatformIntegrationCreate) SetRefreshToken(s string) *PlatformIntegrationCreate {
	pic.mutation.SetRefreshToken(s)
	return pic
}

// SetNillableRefreshToken sets the "refresh_token" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableRefreshToken(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetRefreshToken(*s)
	}
	return pic
}

// SetTokenExpiresAt sets the "token_expires_at" field.
func (pic *PlatformIntegrationCreate) SetTokenExpiresAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetTokenExpiresAt(t)
	return pic
}

// SetNillableTokenExpiresAt sets the "token_expires_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableTokenExpiresAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetTokenExpiresAt(*t)
	}
	return pic
}

// SetScopes sets the "scopes" field.
func (pic *PlatformIntegrationCreate) SetScopes(s []string) *PlatformIntegrationCreate {
	pic.mutation.SetScopes(s)
	return pic
}

// SetPlatformData sets the "platform_data" field.
func (pic *PlatformIntegrationCreate) SetPlatformData(m map[string]interface{}) *PlatformIntegrationCreate {
	pic.mutation.SetPlatformData(m)
	return pic
}

// SetStatus sets the "status" field.
func (pic *PlatformIntegrationCreate) SetStatus(pl platformintegration.Status) *PlatformIntegrationCreate {
	pic.mutation.SetStatus(pl)
	return pic
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableStatus(pl *platformintegration.Status) *PlatformIntegrationCreate {
	if pl != nil {
		pic.SetStatus(*pl)
	}
	return pic
}

// SetErrorMessage sets the "error_message" field.
func (pic *PlatformIntegrationCreate) SetErrorMessage(s string) *PlatformIntegrationCreate {
	pic.mutation.SetErrorMessage(s)
	return pic
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableErrorMessage(s *string) *PlatformIntegrationCreate {
	if s != nil {
		pic.SetErrorMessage(*s)
	}
	return pic
}

// SetLastSyncAt sets the "last_sync_at" field.
func (pic *PlatformIntegrationCreate) SetLastSyncAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetLastSyncAt(t)
	return pic
}

// SetNillableLastSyncAt sets the "last_sync_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableLastSyncAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetLastSyncAt(*t)
	}
	return pic
}

// SetLastPostAt sets the "last_post_at" field.
func (pic *PlatformIntegrationCreate) SetLastPostAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetLastPostAt(t)
	return pic
}

// SetNillableLastPostAt sets the "last_post_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableLastPostAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetLastPostAt(*t)
	}
	return pic
}

// SetCapabilities sets the "capabilities" field.
func (pic *PlatformIntegrationCreate) SetCapabilities(s []string) *PlatformIntegrationCreate {
	pic.mutation.SetCapabilities(s)
	return pic
}

// SetSettings sets the "settings" field.
func (pic *PlatformIntegrationCreate) SetSettings(m map[string]interface{}) *PlatformIntegrationCreate {
	pic.mutation.SetSettings(m)
	return pic
}

// SetAutoPublish sets the "auto_publish" field.
func (pic *PlatformIntegrationCreate) SetAutoPublish(b bool) *PlatformIntegrationCreate {
	pic.mutation.SetAutoPublish(b)
	return pic
}

// SetNillableAutoPublish sets the "auto_publish" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableAutoPublish(b *bool) *PlatformIntegrationCreate {
	if b != nil {
		pic.SetAutoPublish(*b)
	}
	return pic
}

// SetIsPrimary sets the "is_primary" field.
func (pic *PlatformIntegrationCreate) SetIsPrimary(b bool) *PlatformIntegrationCreate {
	pic.mutation.SetIsPrimary(b)
	return pic
}

// SetNillableIsPrimary sets the "is_primary" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableIsPrimary(b *bool) *PlatformIntegrationCreate {
	if b != nil {
		pic.SetIsPrimary(*b)
	}
	return pic
}

// SetMetadata sets the "metadata" field.
func (pic *PlatformIntegrationCreate) SetMetadata(m map[string]interface{}) *PlatformIntegrationCreate {
	pic.mutation.SetMetadata(m)
	return pic
}

// SetCreatedAt sets the "created_at" field.
func (pic *PlatformIntegrationCreate) SetCreatedAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetCreatedAt(t)
	return pic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableCreatedAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetCreatedAt(*t)
	}
	return pic
}

// SetUpdatedAt sets the "updated_at" field.
func (pic *PlatformIntegrationCreate) SetUpdatedAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetUpdatedAt(t)
	return pic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableUpdatedAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetUpdatedAt(*t)
	}
	return pic
}

// SetDeletedAt sets the "deleted_at" field.
func (pic *PlatformIntegrationCreate) SetDeletedAt(t time.Time) *PlatformIntegrationCreate {
	pic.mutation.SetDeletedAt(t)
	return pic
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableDeletedAt(t *time.Time) *PlatformIntegrationCreate {
	if t != nil {
		pic.SetDeletedAt(*t)
	}
	return pic
}

// SetID sets the "id" field.
func (pic *PlatformIntegrationCreate) SetID(u uuid.UUID) *PlatformIntegrationCreate {
	pic.mutation.SetID(u)
	return pic
}

// SetNillableID sets the "id" field if the given value is not nil.
func (pic *PlatformIntegrationCreate) SetNillableID(u *uuid.UUID) *PlatformIntegrationCreate {
	if u != nil {
		pic.SetID(*u)
	}
	return pic
}

// Mutation returns the PlatformIntegrationMutation object of the builder.
func (pic *PlatformIntegrationCreate) Mutation() *PlatformIntegrationMutation {
	return pic.mutation
}

// Save creates the PlatformIntegration in the database.
func (pic *PlatformIntegrationCreate) Save(ctx context.Context) (*PlatformIntegration, error) {
	pic.defaults()
	return withHooks(ctx, pic.sqlSave, pic.mutation, pic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pic *PlatformIntegrationCreate) SaveX(ctx context.Context) *PlatformIntegration {
	v, err := pic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pic *PlatformIntegrationCreate) Exec(ctx context.Context) error {
	_, err := pic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pic *PlatformIntegrationCreate) ExecX(ctx context.Context) {
	if err := pic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pic *PlatformIntegrationCreate) defaults() {
	if _, ok := pic.mutation.Status(); !ok {
		v := platformintegration.DefaultStatus
		pic.mutation.SetStatus(v)
	}
	if _, ok := pic.mutation.AutoPublish(); !ok {
		v := platformintegration.DefaultAutoPublish
		pic.mutation.SetAutoPublish(v)
	}
	if _, ok := pic.mutation.IsPrimary(); !ok {
		v := platformintegration.DefaultIsPrimary
		pic.mutation.SetIsPrimary(v)
	}
	if _, ok := pic.mutation.CreatedAt(); !ok {
		v := platformintegration.DefaultCreatedAt()
		pic.mutation.SetCreatedAt(v)
	}
	if _, ok := pic.mutation.UpdatedAt(); !ok {
		v := platformintegration.DefaultUpdatedAt()
		pic.mutation.SetUpdatedAt(v)
	}
	if _, ok := pic.mutation.ID(); !ok {
		v := platformintegration.DefaultID()
		pic.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pic *PlatformIntegrationCreate) check() error {
	if _, ok := pic.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "PlatformIntegration.user_id"`)}
	}
	if v, ok := pic.mutation.WorkspaceID(); ok {
		if err := platformintegration.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.workspace_id": %w`, err)}
		}
	}
	if _, ok := pic.mutation.Platform(); !ok {
		return &ValidationError{Name: "platform", err: errors.New(`ent: missing required field "PlatformIntegration.platform"`)}
	}
	if v, ok := pic.mutation.Platform(); ok {
		if err := platformintegration.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform": %w`, err)}
		}
	}
	if _, ok := pic.mutation.PlatformUserID(); !ok {
		return &ValidationError{Name: "platform_user_id", err: errors.New(`ent: missing required field "PlatformIntegration.platform_user_id"`)}
	}
	if v, ok := pic.mutation.PlatformUserID(); ok {
		if err := platformintegration.PlatformUserIDValidator(v); err != nil {
			return &ValidationError{Name: "platform_user_id", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_user_id": %w`, err)}
		}
	}
	if v, ok := pic.mutation.PlatformUsername(); ok {
		if err := platformintegration.PlatformUsernameValidator(v); err != nil {
			return &ValidationError{Name: "platform_username", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_username": %w`, err)}
		}
	}
	if v, ok := pic.mutation.PlatformDisplayName(); ok {
		if err := platformintegration.PlatformDisplayNameValidator(v); err != nil {
			return &ValidationError{Name: "platform_display_name", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_display_name": %w`, err)}
		}
	}
	if v, ok := pic.mutation.PlatformAvatarURL(); ok {
		if err := platformintegration.PlatformAvatarURLValidator(v); err != nil {
			return &ValidationError{Name: "platform_avatar_url", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.platform_avatar_url": %w`, err)}
		}
	}
	if _, ok := pic.mutation.AccessToken(); !ok {
		return &ValidationError{Name: "access_token", err: errors.New(`ent: missing required field "PlatformIntegration.access_token"`)}
	}
	if _, ok := pic.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "PlatformIntegration.status"`)}
	}
	if v, ok := pic.mutation.Status(); ok {
		if err := platformintegration.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.status": %w`, err)}
		}
	}
	if v, ok := pic.mutation.ErrorMessage(); ok {
		if err := platformintegration.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "PlatformIntegration.error_message": %w`, err)}
		}
	}
	if _, ok := pic.mutation.AutoPublish(); !ok {
		return &ValidationError{Name: "auto_publish", err: errors.New(`ent: missing required field "PlatformIntegration.auto_publish"`)}
	}
	if _, ok := pic.mutation.IsPrimary(); !ok {
		return &ValidationError{Name: "is_primary", err: errors.New(`ent: missing required field "PlatformIntegration.is_primary"`)}
	}
	if _, ok := pic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "PlatformIntegration.created_at"`)}
	}
	if _, ok := pic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "PlatformIntegration.updated_at"`)}
	}
	return nil
}

func (pic *PlatformIntegrationCreate) sqlSave(ctx context.Context) (*PlatformIntegration, error) {
	if err := pic.check(); err != nil {
		return nil, err
	}
	_node, _spec := pic.createSpec()
	if err := sqlgraph.CreateNode(ctx, pic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	pic.mutation.id = &_node.ID
	pic.mutation.done = true
	return _node, nil
}

func (pic *PlatformIntegrationCreate) createSpec() (*PlatformIntegration, *sqlgraph.CreateSpec) {
	var (
		_node = &PlatformIntegration{config: pic.config}
		_spec = sqlgraph.NewCreateSpec(platformintegration.Table, sqlgraph.NewFieldSpec(platformintegration.FieldID, field.TypeUUID))
	)
	if id, ok := pic.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := pic.mutation.UserID(); ok {
		_spec.SetField(platformintegration.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := pic.mutation.WorkspaceID(); ok {
		_spec.SetField(platformintegration.FieldWorkspaceID, field.TypeString, value)
		_node.WorkspaceID = value
	}
	if value, ok := pic.mutation.Platform(); ok {
		_spec.SetField(platformintegration.FieldPlatform, field.TypeEnum, value)
		_node.Platform = value
	}
	if value, ok := pic.mutation.PlatformUserID(); ok {
		_spec.SetField(platformintegration.FieldPlatformUserID, field.TypeString, value)
		_node.PlatformUserID = value
	}
	if value, ok := pic.mutation.PlatformUsername(); ok {
		_spec.SetField(platformintegration.FieldPlatformUsername, field.TypeString, value)
		_node.PlatformUsername = value
	}
	if value, ok := pic.mutation.PlatformDisplayName(); ok {
		_spec.SetField(platformintegration.FieldPlatformDisplayName, field.TypeString, value)
		_node.PlatformDisplayName = value
	}
	if value, ok := pic.mutation.PlatformAvatarURL(); ok {
		_spec.SetField(platformintegration.FieldPlatformAvatarURL, field.TypeString, value)
		_node.PlatformAvatarURL = value
	}
	if value, ok := pic.mutation.AccessToken(); ok {
		_spec.SetField(platformintegration.FieldAccessToken, field.TypeString, value)
		_node.AccessToken = value
	}
	if value, ok := pic.mutation.RefreshToken(); ok {
		_spec.SetField(platformintegration.FieldRefreshToken, field.TypeString, value)
		_node.RefreshToken = value
	}
	if value, ok := pic.mutation.TokenExpiresAt(); ok {
		_spec.SetField(platformintegration.FieldTokenExpiresAt, field.TypeTime, value)
		_node.TokenExpiresAt = value
	}
	if value, ok := pic.mutation.Scopes(); ok {
		_spec.SetField(platformintegration.FieldScopes, field.TypeJSON, value)
		_node.Scopes = value
	}
	if value, ok := pic.mutation.PlatformData(); ok {
		_spec.SetField(platformintegration.FieldPlatformData, field.TypeJSON, value)
		_node.PlatformData = value
	}
	if value, ok := pic.mutation.Status(); ok {
		_spec.SetField(platformintegration.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := pic.mutation.ErrorMessage(); ok {
		_spec.SetField(platformintegration.FieldErrorMessage, field.TypeString, value)
		_node.ErrorMessage = value
	}
	if value, ok := pic.mutation.LastSyncAt(); ok {
		_spec.SetField(platformintegration.FieldLastSyncAt, field.TypeTime, value)
		_node.LastSyncAt = value
	}
	if value, ok := pic.mutation.LastPostAt(); ok {
		_spec.SetField(platformintegration.FieldLastPostAt, field.TypeTime, value)
		_node.LastPostAt = value
	}
	if value, ok := pic.mutation.Capabilities(); ok {
		_spec.SetField(platformintegration.FieldCapabilities, field.TypeJSON, value)
		_node.Capabilities = value
	}
	if value, ok := pic.mutation.Settings(); ok {
		_spec.SetField(platformintegration.FieldSettings, field.TypeJSON, value)
		_node.Settings = value
	}
	if value, ok := pic.mutation.AutoPublish(); ok {
		_spec.SetField(platformintegration.FieldAutoPublish, field.TypeBool, value)
		_node.AutoPublish = value
	}
	if value, ok := pic.mutation.IsPrimary(); ok {
		_spec.SetField(platformintegration.FieldIsPrimary, field.TypeBool, value)
		_node.IsPrimary = value
	}
	if value, ok := pic.mutation.Metadata(); ok {
		_spec.SetField(platformintegration.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := pic.mutation.CreatedAt(); ok {
		_spec.SetField(platformintegration.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pic.mutation.UpdatedAt(); ok {
		_spec.SetField(platformintegration.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pic.mutation.DeletedAt(); ok {
		_spec.SetField(platformintegration.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// PlatformIntegrationCreateBulk is the builder for creating many PlatformIntegration entities in bulk.
type PlatformIntegrationCreateBulk struct {
	config
	err      error
	builders []*PlatformIntegrationCreate
}

// Save creates the PlatformIntegration entities in the database.
func (picb *PlatformIntegrationCreateBulk) Save(ctx context.Context) ([]*PlatformIntegration, error) {
	if picb.err != nil {
		return nil, picb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(picb.builders))
	nodes := make([]*PlatformIntegration, len(picb.builders))
	mutators := make([]Mutator, len(picb.builders))
	for i := range picb.builders {
		func(i int, root context.Context) {
			builder := picb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PlatformIntegrationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, picb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, picb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, picb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (picb *PlatformIntegrationCreateBulk) SaveX(ctx context.Context) []*PlatformIntegration {
	v, err := picb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (picb *PlatformIntegrationCreateBulk) Exec(ctx context.Context) error {
	_, err := picb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (picb *PlatformIntegrationCreateBulk) ExecX(ctx context.Context) {
	if err := picb.Exec(ctx); err != nil {
		panic(err)
	}
}

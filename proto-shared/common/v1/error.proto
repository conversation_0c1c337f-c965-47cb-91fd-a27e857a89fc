syntax = "proto3";

package common.v1;

option go_package = "github.com/social-content-ai/proto-shared/common/v1;commonv1";

// ErrorResponse represents a standard error response
message ErrorResponse {
  string code = 1;           // Error code (e.g., "INVALID_INPUT", "UNAUTHORIZED")
  string message = 2;        // Human-readable error message
  repeated ErrorDetail details = 3; // Additional error details
  string trace_id = 4;       // Request trace ID for debugging
}

// ErrorDetail provides additional context about an error
message ErrorDetail {
  string field = 1;          // Field name that caused the error
  string code = 2;           // Specific error code for this field
  string message = 3;        // Error message for this field
}

// ValidationError for input validation failures
message ValidationError {
  string field = 1;          // Field name
  string message = 2;        // Validation error message
  string code = 3;           // Validation error code
}

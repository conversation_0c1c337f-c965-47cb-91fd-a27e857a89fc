package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Hu<PERSON> maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Inbound messages from the clients
	broadcast chan []byte

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// User-specific channels
	userChannels map[string]map[*Client]bool

	// Mutex for thread safety
	mutex sync.RWMutex

	// Logger
	logger logging.Logger

	// Context for graceful shutdown
	ctx context.Context
}

// Client represents a WebSocket client
type Client struct {
	// The websocket connection
	conn *websocket.Conn

	// Buffered channel of outbound messages
	send chan []byte

	// User ID
	userID string

	// Client ID
	clientID string

	// Hub reference
	hub *Hub

	// Last ping time
	lastPing time.Time

	// Subscribed channels
	channels map[string]bool

	// Mutex for thread safety
	mutex sync.RWMutex
}

// Message represents a WebSocket message
type Message struct {
	Type      string                 `json:"type"`
	Channel   string                 `json:"channel,omitempty"`
	UserID    string                 `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewHub creates a new WebSocket hub
func NewHub(ctx context.Context, logger logging.Logger) *Hub {
	return &Hub{
		clients:      make(map[*Client]bool),
		broadcast:    make(chan []byte),
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		userChannels: make(map[string]map[*Client]bool),
		logger:       logger,
		ctx:          ctx,
	}
}

// Run starts the hub
func (h *Hub) Run() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-h.ctx.Done():
			h.logger.Info("WebSocket hub shutting down")
			return

		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case <-ticker.C:
			h.cleanupInactiveClients()
		}
	}
}

// registerClient registers a new client
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true

	// Add to user channels
	if client.userID != "" {
		if h.userChannels[client.userID] == nil {
			h.userChannels[client.userID] = make(map[*Client]bool)
		}
		h.userChannels[client.userID][client] = true
	}

	h.logger.WithFields(map[string]interface{}{
		"client_id": client.clientID,
		"user_id":   client.userID,
	}).Info("WebSocket client registered")

	// Send welcome message
	welcomeMsg := &Message{
		Type: "welcome",
		Data: map[string]interface{}{
			"client_id": client.clientID,
			"message":   "Connected to Social Content AI",
		},
		Timestamp: time.Now(),
	}

	if data, err := json.Marshal(welcomeMsg); err == nil {
		select {
		case client.send <- data:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

// unregisterClient unregisters a client
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)

		// Remove from user channels
		if client.userID != "" {
			if userClients, exists := h.userChannels[client.userID]; exists {
				delete(userClients, client)
				if len(userClients) == 0 {
					delete(h.userChannels, client.userID)
				}
			}
		}

		h.logger.WithFields(map[string]interface{}{
			"client_id": client.clientID,
			"user_id":   client.userID,
		}).Info("WebSocket client unregistered")
	}
}

// broadcastMessage broadcasts a message to all clients
func (h *Hub) broadcastMessage(message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

// SendToUser sends a message to a specific user
func (h *Hub) SendToUser(userID string, message *Message) error {
	h.mutex.RLock()
	userClients, exists := h.userChannels[userID]
	h.mutex.RUnlock()

	if !exists {
		h.logger.WithField("user_id", userID).Debug("No active connections for user")
		return nil
	}

	message.UserID = userID
	message.Timestamp = time.Now()

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range userClients {
		select {
		case client.send <- data:
		default:
			close(client.send)
			delete(h.clients, client)
			delete(userClients, client)
		}
	}

	return nil
}

// SendToChannel sends a message to all clients subscribed to a channel
func (h *Hub) SendToChannel(channel string, message *Message) error {
	message.Channel = channel
	message.Timestamp = time.Now()

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		client.mutex.RLock()
		subscribed := client.channels[channel]
		client.mutex.RUnlock()

		if subscribed {
			select {
			case client.send <- data:
			default:
				close(client.send)
				delete(h.clients, client)
			}
		}
	}

	return nil
}

// Broadcast sends a message to all connected clients
func (h *Hub) Broadcast(message *Message) error {
	message.Timestamp = time.Now()

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case h.broadcast <- data:
	default:
		return fmt.Errorf("broadcast channel full")
	}

	return nil
}

// GetStats returns hub statistics
func (h *Hub) GetStats() map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return map[string]interface{}{
		"total_clients":   len(h.clients),
		"connected_users": len(h.userChannels),
		"timestamp":       time.Now(),
	}
}

// cleanupInactiveClients removes inactive clients
func (h *Hub) cleanupInactiveClients() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	now := time.Now()
	timeout := 5 * time.Minute

	for client := range h.clients {
		if now.Sub(client.lastPing) > timeout {
			h.logger.WithField("client_id", client.clientID).Debug("Removing inactive client")
			delete(h.clients, client)
			close(client.send)

			// Remove from user channels
			if client.userID != "" {
				if userClients, exists := h.userChannels[client.userID]; exists {
					delete(userClients, client)
					if len(userClients) == 0 {
						delete(h.userChannels, client.userID)
					}
				}
			}
		}
	}
}

// Upgrader configures the websocket upgrader
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow connections from any origin in development
		// In production, implement proper origin checking
		return true
	},
}

// HandleWebSocket handles WebSocket connections
func (h *Hub) HandleWebSocket(w http.ResponseWriter, r *http.Request, userID, clientID string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}

	client := &Client{
		conn:     conn,
		send:     make(chan []byte, 256),
		userID:   userID,
		clientID: clientID,
		hub:      h,
		lastPing: time.Now(),
		channels: make(map[string]bool),
	}

	// Register client
	h.register <- client

	// Start goroutines
	go client.writePump()
	go client.readPump()
}

// readPump pumps messages from the websocket connection to the hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(512)
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.lastPing = time.Now()
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.hub.logger.WithError(err).Error("WebSocket error")
			}
			break
		}

		// Handle client messages (subscribe/unsubscribe to channels)
		c.handleMessage(message)
	}
}

// writePump pumps messages from the hub to the websocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued messages to the current message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage handles incoming client messages
func (c *Client) handleMessage(message []byte) {
	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		c.hub.logger.WithError(err).Error("Failed to unmarshal client message")
		return
	}

	switch msg.Type {
	case "subscribe":
		if channel, ok := msg.Data["channel"].(string); ok {
			c.mutex.Lock()
			c.channels[channel] = true
			c.mutex.Unlock()
			c.hub.logger.WithFields(map[string]interface{}{
				"client_id": c.clientID,
				"channel":   channel,
			}).Debug("Client subscribed to channel")
		}

	case "unsubscribe":
		if channel, ok := msg.Data["channel"].(string); ok {
			c.mutex.Lock()
			delete(c.channels, channel)
			c.mutex.Unlock()
			c.hub.logger.WithFields(map[string]interface{}{
				"client_id": c.clientID,
				"channel":   channel,
			}).Debug("Client unsubscribed from channel")
		}

	case "ping":
		c.lastPing = time.Now()
		pongMsg := &Message{
			Type: "pong",
			Data: map[string]interface{}{
				"timestamp": time.Now(),
			},
		}
		if data, err := json.Marshal(pongMsg); err == nil {
			select {
			case c.send <- data:
			default:
			}
		}
	}
}

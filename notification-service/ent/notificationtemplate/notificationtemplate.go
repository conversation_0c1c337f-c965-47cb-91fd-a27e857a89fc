// Code generated by ent, DO NOT EDIT.

package notificationtemplate

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the notificationtemplate type in the database.
	Label = "notification_template"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldEventType holds the string denoting the event_type field in the database.
	FieldEventType = "event_type"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldTemplates holds the string denoting the templates field in the database.
	FieldTemplates = "templates"
	// FieldIsActive holds the string denoting the is_active field in the database.
	FieldIsActive = "is_active"
	// FieldVariables holds the string denoting the variables field in the database.
	FieldVariables = "variables"
	// FieldDefaultChannels holds the string denoting the default_channels field in the database.
	FieldDefaultChannels = "default_channels"
	// FieldDefaultPriority holds the string denoting the default_priority field in the database.
	FieldDefaultPriority = "default_priority"
	// FieldLanguage holds the string denoting the language field in the database.
	FieldLanguage = "language"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the notificationtemplate in the database.
	Table = "notification_templates"
)

// Columns holds all SQL columns for notificationtemplate fields.
var Columns = []string{
	FieldID,
	FieldEventType,
	FieldName,
	FieldDescription,
	FieldTemplates,
	FieldIsActive,
	FieldVariables,
	FieldDefaultChannels,
	FieldDefaultPriority,
	FieldLanguage,
	FieldVersion,
	FieldMetadata,
	FieldCreatedBy,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// EventTypeValidator is a validator for the "event_type" field. It is called by the builders before save.
	EventTypeValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultIsActive holds the default value on creation for the "is_active" field.
	DefaultIsActive bool
	// DefaultLanguage holds the default value on creation for the "language" field.
	DefaultLanguage string
	// LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	LanguageValidator func(string) error
	// DefaultVersion holds the default value on creation for the "version" field.
	DefaultVersion string
	// VersionValidator is a validator for the "version" field. It is called by the builders before save.
	VersionValidator func(string) error
	// CreatedByValidator is a validator for the "created_by" field. It is called by the builders before save.
	CreatedByValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// DefaultPriority defines the type for the "default_priority" enum field.
type DefaultPriority string

// DefaultPriorityNormal is the default value of the DefaultPriority enum.
const DefaultDefaultPriority = DefaultPriorityNormal

// DefaultPriority values.
const (
	DefaultPriorityLow    DefaultPriority = "low"
	DefaultPriorityNormal DefaultPriority = "normal"
	DefaultPriorityHigh   DefaultPriority = "high"
	DefaultPriorityUrgent DefaultPriority = "urgent"
)

func (dp DefaultPriority) String() string {
	return string(dp)
}

// DefaultPriorityValidator is a validator for the "default_priority" field enum values. It is called by the builders before save.
func DefaultPriorityValidator(dp DefaultPriority) error {
	switch dp {
	case DefaultPriorityLow, DefaultPriorityNormal, DefaultPriorityHigh, DefaultPriorityUrgent:
		return nil
	default:
		return fmt.Errorf("notificationtemplate: invalid enum value for default_priority field: %q", dp)
	}
}

// OrderOption defines the ordering options for the NotificationTemplate queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByEventType orders the results by the event_type field.
func ByEventType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEventType, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByIsActive orders the results by the is_active field.
func ByIsActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsActive, opts...).ToFunc()
}

// ByDefaultPriority orders the results by the default_priority field.
func ByDefaultPriority(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDefaultPriority, opts...).ToFunc()
}

// ByLanguage orders the results by the language field.
func ByLanguage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLanguage, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

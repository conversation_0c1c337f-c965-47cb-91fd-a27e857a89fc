package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// payPalProcessor implements the Processor interface for PayPal payments
type payPalProcessor struct {
	config       PayPalConfig
	paypalClient paypal.Client
	logger       logging.Logger
}

// NewPayPalProcessor creates a new PayPal payment processor
func NewPayPalProcessor(config PayPalConfig, paypalClient paypal.Client, logger logging.Logger) Processor {
	return &payPalProcessor{
		config:       config,
		paypalClient: paypalClient,
		logger:       logger,
	}
}

// ProcessPayment processes a payment through PayPal
// Flow: Factory.CreatePayment -> PaypalProcess.InitiatePayment
func (p *payPalProcessor) ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"user_id":        req.UserID,
		"amount":         req.Amount,
		"currency":       req.Currency,
		"payment_method": req.PaymentMethod,
		"order_id":       req.OrderID,
	}).Info("Processing PayPal payment through processor")

	// Use PayPal client directly
	if p.paypalClient != nil {
		return p.processPaymentWithUseCase(ctx, req)
	}

	// Fallback to mock implementation
	return p.processPaymentMock(ctx, req)
}

// processPaymentWithUseCase processes payment using PayPal client directly
func (p *payPalProcessor) processPaymentWithUseCase(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// Create PayPal payment directly using client
	paypalPayment, err := p.createPayPalPayment(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal payment: %w", err)
	}

	return &PaymentResponse{
		PaymentID:  paypalPayment.PaymentID,
		Status:     paypalPayment.Status,
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: paypalPayment.ApprovalURL,
		PaymentInfo: map[string]interface{}{
			"payment_id":   paypalPayment.PaymentID,
			"approval_url": paypalPayment.ApprovalURL,
			"cancel_url":   paypalPayment.CancelURL,
			"return_url":   paypalPayment.ReturnURL,
		},
		ExpiresAt: time.Now().Add(3 * time.Hour).Format(time.RFC3339),
	}, nil
}

// processPaymentMock processes payment using mock implementation
func (p *payPalProcessor) processPaymentMock(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// Create PayPal payment
	paypalPayment, err := p.createPayPalPayment(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal payment: %w", err)
	}

	return &PaymentResponse{
		PaymentID:  paypalPayment.PaymentID,
		Status:     paypalPayment.Status,
		Amount:     req.Amount,
		Currency:   req.Currency,
		PaymentURL: paypalPayment.ApprovalURL,
		PaymentInfo: map[string]interface{}{
			"payment_id":   paypalPayment.PaymentID,
			"approval_url": paypalPayment.ApprovalURL,
			"cancel_url":   paypalPayment.CancelURL,
			"return_url":   paypalPayment.ReturnURL,
		},
		ExpiresAt: time.Now().Add(3 * time.Hour).Format(time.RFC3339), // PayPal payments expire in 3 hours
	}, nil
}

// createPayPalPayment creates a PayPal payment
func (p *payPalProcessor) createPayPalPayment(ctx context.Context, req *PaymentRequest) (*models.PayPalPaymentInfo, error) {
	// For now, this is a mock implementation
	// In production, this would call PayPal API

	paymentID := uuid.New().String()

	// Mock PayPal payment creation
	paymentInfo := &models.PayPalPaymentInfo{
		PaymentID:   paymentID,
		ApprovalURL: fmt.Sprintf("%s/checkout?payment_id=%s", p.config.BaseURL, paymentID),
		CancelURL:   p.config.CancelURL,
		ReturnURL:   p.config.ReturnURL,
		Status:      "created",
	}

	p.logger.WithFields(map[string]interface{}{
		"payment_id":   paymentID,
		"approval_url": paymentInfo.ApprovalURL,
		"amount":       req.Amount,
		"currency":     req.Currency,
	}).Info("Created PayPal payment")

	return paymentInfo, nil
}

// RefundPayment refunds a PayPal payment
func (p *payPalProcessor) RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	p.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"amount":     req.Amount,
		"reason":     req.Reason,
	}).Info("Processing PayPal refund")

	// Use PayPal client if available
	if p.paypalClient != nil {
		return p.refundPaymentWithUseCase(ctx, req)
	}

	// Fallback to mock implementation
	return p.refundPaymentMock(ctx, req)
}

// refundPaymentWithUseCase refunds payment using PayPal client directly
func (p *payPalProcessor) refundPaymentWithUseCase(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	// Use PayPal client to process refund
	// For now, this is a simplified implementation
	// In production, this would call PayPal API through the client

	refundID := uuid.New().String()

	p.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"refund_id":  refundID,
		"amount":     req.Amount,
	}).Info("Processing PayPal refund through client")

	return &RefundResponse{
		RefundID:  refundID,
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// refundPaymentMock refunds payment using mock implementation
func (p *payPalProcessor) refundPaymentMock(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	// Mock refund implementation
	refundID := uuid.New().String()

	return &RefundResponse{
		RefundID:  refundID,
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Reason:    req.Reason,
	}, nil
}

// GetPaymentStatus gets the status of a PayPal payment
func (p *payPalProcessor) GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	p.logger.WithField("payment_id", paymentID).Info("Getting PayPal payment status")

	// Use PayPal client if available
	if p.paypalClient != nil {
		return p.getPaymentStatusWithUseCase(ctx, paymentID)
	}

	// Fallback to mock implementation
	return p.getPaymentStatusMock(ctx, paymentID)
}

// getPaymentStatusWithUseCase gets payment status using PayPal client directly
func (p *payPalProcessor) getPaymentStatusWithUseCase(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	// Use PayPal client to get payment status
	// For now, this is a simplified implementation
	// In production, this would call PayPal API through the client

	p.logger.WithField("payment_id", paymentID).Info("Getting PayPal payment status through client")

	return &PaymentStatus{
		PaymentID:   paymentID,
		Status:      "completed",
		Amount:      1000, // Mock amount
		Currency:    "USD",
		ProcessedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// getPaymentStatusMock gets payment status using mock implementation
func (p *payPalProcessor) getPaymentStatusMock(ctx context.Context, paymentID string) (*PaymentStatus, error) {
	// Mock status check
	return &PaymentStatus{
		PaymentID:   paymentID,
		Status:      "completed",
		Amount:      1000, // Mock amount
		Currency:    "USD",
		ProcessedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// ValidateWebhook validates a PayPal webhook signature
func (p *payPalProcessor) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	p.logger.Info("Validating PayPal webhook signature")

	// Use PayPal client for webhook validation if available
	if p.paypalClient != nil {
		// In real implementation, PayPal client would handle signature validation
		// For now, we'll use a simple validation
		return len(signature) > 0, nil
	}

	// Fallback validation - in production, implement proper PayPal signature verification
	return len(signature) > 0, nil
}

// ProcessWebhook processes a PayPal webhook for payment flow completion
// Flow: Factory.WebHook -> PaypalProcess.WebHook -> data
func (p *payPalProcessor) ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error) {
	p.logger.Info("Processing PayPal webhook through processor")

	// Parse webhook payload
	var webhookData map[string]interface{}
	if err := json.Unmarshal(payload, &webhookData); err != nil {
		return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
	}

	eventType, ok := webhookData["event_type"].(string)
	if !ok {
		return nil, fmt.Errorf("missing event_type in webhook payload")
	}

	p.logger.WithField("event_type", eventType).Info("Processing PayPal webhook event")

	// Create webhook event for further processing
	// This data will be returned to Factory.WebHook for event pushing
	webhookEvent := &WebhookEvent{
		ID:        uuid.New().String(),
		Type:      eventType,
		Data:      webhookData,
		CreatedAt: time.Now().Format(time.RFC3339),
	}

	// Use PayPal client for webhook processing if available
	if p.paypalClient != nil {
		// In real implementation, PayPal client would handle webhook processing
		// For now, we'll just log the webhook processing
		p.logger.WithFields(map[string]interface{}{
			"event_id":   webhookEvent.ID,
			"event_type": eventType,
		}).Info("Processing webhook through PayPal client")
	}

	p.logger.WithFields(map[string]interface{}{
		"event_id":   webhookEvent.ID,
		"event_type": webhookEvent.Type,
	}).Info("PayPal webhook processed, returning data to factory")

	return webhookEvent, nil
}

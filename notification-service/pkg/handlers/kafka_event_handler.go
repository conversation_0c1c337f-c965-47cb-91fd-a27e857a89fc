package handlers

import (
	"context"

	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// KafkaEventHandler implements the kafka.EventHandler interface for notification service.
// It processes events from various services and converts them into notifications
// that need to be sent to users via different channels (email, SMS, push, etc.).
//
// Only handles events that require user notification according to the microservices
// architecture design document.
type KafkaEventHandler struct {
	notificationUC notification.UseCase
	logger         logging.Logger
}

// NewKafkaEventHandler creates a new Kafka event handler
func NewKafkaEventHandler(notificationUC notification.UseCase, logger logging.Logger) *KafkaEventHandler {
	return &KafkaEventHandler{
		notificationUC: notificationUC,
		logger:         logger,
	}
}

// HandleUserEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleUserEvent(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type": event.Type,
		"user_id":    event.UserID,
		"email":      event.Email,
	}).Info("Processing user event")

	switch event.Type {
	case kafka.EventTypeUserRegistered:
		return eh.handleUserRegistered(ctx, event)
	case kafka.EventTypeUserUpgraded:
		return eh.handleUserUpgraded(ctx, event)
	case kafka.EventTypeUserPasswordReset:
		return eh.handlePasswordReset(ctx, event)
	case kafka.EventTypeUserEmailVerified:
		return eh.handleEmailVerified(ctx, event)
	case kafka.EventTypeUserSecurityAlert:
		return eh.handleSecurityAlert(ctx, event)
	default:
		eh.logger.WithField("event_type", event.Type).Debug("User event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}
}

// HandleContentEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleContentEvent(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type":   event.Type,
		"user_id":      event.UserID,
		"content_id":   event.ContentID,
		"content_type": event.ContentType,
	}).Info("Processing content event")

	switch event.Type {
	case kafka.EventTypePostPublished:
		return eh.handlePostPublished(ctx, event)
	case kafka.EventTypePostScheduled:
		return eh.handlePostScheduled(ctx, event)
	case kafka.EventTypePostFailed:
		return eh.handlePostFailed(ctx, event)
	case kafka.EventTypeTemplateCreated:
		return eh.handleTemplateCreated(ctx, event)
	case kafka.EventTypeTemplateUpdated:
		return eh.handleTemplateUpdated(ctx, event)
	default:
		eh.logger.WithField("event_type", event.Type).Debug("Content event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}
}

// HandleBillingEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleBillingEvent(ctx context.Context, event *kafka.BillingEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type":      event.Type,
		"user_id":         event.UserID,
		"subscription_id": event.SubscriptionID,
		"amount":          event.Amount,
	}).Info("Processing billing event")

	switch event.Type {
	case kafka.EventTypeCreditLow:
		eh.logger.Info("Credits low - sending warning notification")
		// TODO: Send low credit alert + upgrade prompt
	case kafka.EventTypeCreditPurchased:
		eh.logger.Info("Credits purchased - sending confirmation")
		// TODO: Send credit purchase confirmation
	case kafka.EventTypeSubscriptionRenewed:
		eh.logger.Info("Subscription renewed - sending confirmation")
		// TODO: Send subscription renewal confirmation
	case kafka.EventTypeSubscriptionExpired:
		eh.logger.Info("Subscription expired - sending urgent notification")
		// TODO: Send subscription expiry notice + renewal reminder
	case kafka.EventTypePaymentFailed:
		eh.logger.Info("Payment failed - sending error notification")
		// TODO: Send payment failure notice + retry instructions
	case kafka.EventTypePaymentSucceeded:
		eh.logger.Info("Payment succeeded - sending confirmation")
		// TODO: Send payment receipt + credit added confirmation
	default:
		eh.logger.WithField("event_type", event.Type).Debug("Billing event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}

	// TODO: Convert to notification and send through notification usecase
	return nil
}

// HandleIntegrationEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleIntegrationEvent(ctx context.Context, event *kafka.IntegrationEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type":       event.Type,
		"user_id":          event.UserID,
		"integration_id":   event.IntegrationID,
		"integration_type": event.IntegrationType,
		"platform_name":    event.PlatformName,
	}).Info("Processing integration event")

	switch event.Type {
	case kafka.EventTypeIntegrationConnected:
		eh.logger.Info("Integration connected - sending confirmation")
		// TODO: Send platform connection success notification
	case kafka.EventTypeIntegrationFailed:
		eh.logger.Info("Integration failed - sending error notification")
		// TODO: Send platform connection error notification
	case kafka.EventTypeIntegrationDisconnected:
		eh.logger.Info("Integration disconnected - sending notification")
		// TODO: Send platform disconnection notification
	default:
		eh.logger.WithField("event_type", event.Type).Debug("Integration event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}

	// TODO: Convert to notification and send through notification usecase
	return nil
}

// HandleAssetEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleAssetEvent(ctx context.Context, event *kafka.AssetEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type": event.Type,
		"user_id":    event.UserID,
		"asset_id":   event.AssetID,
		"asset_type": event.AssetType,
		"file_name":  event.FileName,
	}).Info("Processing asset event")

	switch event.Type {
	case kafka.EventTypeAssetUploaded:
		eh.logger.Info("Asset uploaded - sending confirmation")
		// TODO: Send file upload confirmation notification
	case kafka.EventTypeAssetValidated:
		eh.logger.Info("Asset validated - sending status notification")
		// TODO: Send file processing completion notification
	default:
		eh.logger.WithField("event_type", event.Type).Debug("Asset event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}

	// TODO: Convert to notification and send through notification usecase
	return nil
}

// HandleRAGEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleRAGEvent(ctx context.Context, event *kafka.RAGEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type":    event.Type,
		"user_id":       event.UserID,
		"processing_id": event.ProcessingID,
		"document_id":   event.DocumentID,
		"status":        event.Status,
	}).Info("Processing RAG event")

	switch event.Type {
	case kafka.EventTypeRAGProcessingCompleted:
		eh.logger.Info("RAG processing completed - sending completion notification")
		// TODO: Send document processing completion notification
	case kafka.EventTypeRAGProcessingFailed:
		eh.logger.Info("RAG processing failed - sending error notification")
		// TODO: Send document processing error notification
	case kafka.EventTypeRAGRetrainingCompleted:
		eh.logger.Info("RAG retraining completed - sending completion notification")
		// TODO: Send knowledge base update completion notification
	default:
		eh.logger.WithField("event_type", event.Type).Debug("RAG event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}

	// TODO: Convert to notification and send through notification usecase
	return nil
}

// HandleSystemEvent implements kafka.EventHandler interface
func (eh *KafkaEventHandler) HandleSystemEvent(ctx context.Context, event *kafka.SystemEvent) error {
	eh.logger.WithFields(map[string]interface{}{
		"event_type":  event.Type,
		"component":   event.Component,
		"title":       event.Title,
		"description": event.Description,
	}).Info("Processing system event")

	switch event.Type {
	case kafka.EventTypeSystemMaintenance:
		eh.logger.Info("System maintenance - sending maintenance notification")
		// TODO: Send scheduled maintenance notification to all users
	case kafka.EventTypeSystemUpdate:
		eh.logger.Info("System update - sending update notification")
		// TODO: Send system update notification with new features
	case kafka.EventTypeSystemAlert:
		eh.logger.Info("System alert - sending alert notification")
		// TODO: Send system alert notification to admins
	default:
		eh.logger.WithField("event_type", event.Type).Debug("System event not handled by notification service")
		return nil // Ignore events not relevant to notifications
	}

	// TODO: Convert to notification and send through notification usecase
	return nil
}

// User event handlers

// handleUserRegistered sends welcome notification for new user registration
func (eh *KafkaEventHandler) handleUserRegistered(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.Info("Sending welcome notification for new user registration")

	// Get user name from event
	userName := event.FirstName
	if event.LastName != "" {
		if userName != "" {
			userName += " " + event.LastName
		} else {
			userName = event.LastName
		}
	}
	if userName == "" {
		userName = "User" // Fallback
	}

	// Create welcome notification
	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "welcome",
		Title:    "Welcome to Social Content AI!",
		Message:  "Thank you for joining us! Get started by creating your first template.",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"email":     event.Email,
			"user_name": userName,
			"setup_url": "/dashboard/setup",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send welcome notification")
		return err
	}

	return nil
}

// handleUserUpgraded sends upgrade confirmation notification
func (eh *KafkaEventHandler) handleUserUpgraded(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.Info("Sending upgrade confirmation notification")

	// Get user name from event
	userName := event.FirstName
	if event.LastName != "" {
		if userName != "" {
			userName += " " + event.LastName
		} else {
			userName = event.LastName
		}
	}
	if userName == "" {
		userName = "User" // Fallback
	}

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "upgrade",
		Title:    "Account Upgraded Successfully!",
		Message:  "Your account has been upgraded. Enjoy your new features!",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"email":        event.Email,
			"user_name":    userName,
			"features_url": "/dashboard/features",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send upgrade notification")
		return err
	}

	return nil
}

// handlePasswordReset sends password reset email
func (eh *KafkaEventHandler) handlePasswordReset(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.Info("Sending password reset notification")

	// Get user name from event
	userName := event.FirstName
	if event.LastName != "" {
		if userName != "" {
			userName += " " + event.LastName
		} else {
			userName = event.LastName
		}
	}
	if userName == "" {
		userName = "User" // Fallback
	}

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "password_reset",
		Title:    "Password Reset Request",
		Message:  "Click the link below to reset your password. This link expires in 1 hour.",
		Channels: []string{"email"},
		Priority: "high",
		Data: map[string]interface{}{
			"email":      event.Email,
			"user_name":  userName,
			"reset_url":  event.Data["reset_url"],
			"expires_at": event.Data["expires_at"],
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send password reset notification")
		return err
	}

	return nil
}

// handleEmailVerified sends email verification confirmation
func (eh *KafkaEventHandler) handleEmailVerified(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.Info("Sending email verification confirmation")

	// Get user name from event
	userName := event.FirstName
	if event.LastName != "" {
		if userName != "" {
			userName += " " + event.LastName
		} else {
			userName = event.LastName
		}
	}
	if userName == "" {
		userName = "User" // Fallback
	}

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "email_verified",
		Title:    "Email Verified Successfully!",
		Message:  "Your email has been verified. You can now access all features.",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"email":         event.Email,
			"user_name":     userName,
			"dashboard_url": "/dashboard",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send email verification notification")
		return err
	}

	return nil
}

// handleSecurityAlert sends security alert notification
func (eh *KafkaEventHandler) handleSecurityAlert(ctx context.Context, event *kafka.UserEvent) error {
	eh.logger.Info("Sending security alert notification")

	// Get user name from event
	userName := event.FirstName
	if event.LastName != "" {
		if userName != "" {
			userName += " " + event.LastName
		} else {
			userName = event.LastName
		}
	}
	if userName == "" {
		userName = "User" // Fallback
	}

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "security_alert",
		Title:    "Security Alert",
		Message:  "Unusual activity detected on your account. Please review your recent activity.",
		Channels: []string{"email"},
		Priority: "urgent",
		Data: map[string]interface{}{
			"email":        event.Email,
			"user_name":    userName,
			"alert_type":   event.Data["alert_type"],
			"ip_address":   event.Data["ip_address"],
			"location":     event.Data["location"],
			"security_url": "/dashboard/security",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send security alert notification")
		return err
	}

	return nil
}

// Content event handlers

// handlePostPublished sends notification when post is published successfully
func (eh *KafkaEventHandler) handlePostPublished(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.Info("Sending post published notification")

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "post_published",
		Title:    "Post Published Successfully!",
		Message:  "Your post has been published successfully across selected platforms.",
		Channels: []string{"email", "push"},
		Priority: "normal",
		Data: map[string]interface{}{
			"content_id":    event.ContentID,
			"content_title": event.ContentTitle,
			"platform":      event.Platform,
			"post_id":       event.PostID,
			"view_url":      event.Data["view_url"],
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send post published notification")
		return err
	}

	return nil
}

// handlePostScheduled sends notification when post is scheduled
func (eh *KafkaEventHandler) handlePostScheduled(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.Info("Sending post scheduled notification")

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "post_scheduled",
		Title:    "Post Scheduled Successfully!",
		Message:  "Your post has been scheduled and will be published automatically.",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"content_id":     event.ContentID,
			"content_title":  event.ContentTitle,
			"platform":       event.Platform,
			"scheduled_time": event.Data["scheduled_time"],
			"dashboard_url":  "/dashboard/posts",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send post scheduled notification")
		return err
	}

	return nil
}

// handlePostFailed sends notification when post publishing fails
func (eh *KafkaEventHandler) handlePostFailed(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.Info("Sending post failed notification")

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "post_failed",
		Title:    "Post Publishing Failed",
		Message:  "We encountered an issue publishing your post. Please try again or contact support.",
		Channels: []string{"email", "push"},
		Priority: "high",
		Data: map[string]interface{}{
			"content_id":    event.ContentID,
			"content_title": event.ContentTitle,
			"platform":      event.Platform,
			"error_message": event.Data["error_message"],
			"retry_url":     "/dashboard/posts/retry",
			"support_url":   "/support",
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send post failed notification")
		return err
	}

	return nil
}

// handleTemplateCreated sends notification when template is created
func (eh *KafkaEventHandler) handleTemplateCreated(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.Info("Sending template created notification")

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "template_created",
		Title:    "Template Created Successfully!",
		Message:  "Your new template is ready to use for content generation.",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"template_id":   event.TemplateID,
			"content_title": event.ContentTitle,
			"template_url":  "/dashboard/templates/" + event.TemplateID,
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send template created notification")
		return err
	}

	return nil
}

// handleTemplateUpdated sends notification when template is updated
func (eh *KafkaEventHandler) handleTemplateUpdated(ctx context.Context, event *kafka.ContentEvent) error {
	eh.logger.Info("Sending template updated notification")

	req := &notification.SendNotificationRequest{
		UserID:   event.UserID,
		Type:     "template_updated",
		Title:    "Template Updated Successfully!",
		Message:  "Your template has been updated with the latest changes.",
		Channels: []string{"email"},
		Priority: "normal",
		Data: map[string]interface{}{
			"template_id":   event.TemplateID,
			"content_title": event.ContentTitle,
			"template_url":  "/dashboard/templates/" + event.TemplateID,
			"changes":       event.Data["changes"],
		},
	}

	_, err := eh.notificationUC.SendNotification(ctx, req)
	if err != nil {
		eh.logger.WithError(err).Error("Failed to send template updated notification")
		return err
	}

	return nil
}

// GetSupportedTopics returns list of topics that require notification handling
func (eh *KafkaEventHandler) GetSupportedTopics() []string {
	return []string{
		"user.events",        // User registration, upgrades, security alerts
		"content.events",     // Post publishing, template management
		"billing.events",     // Payment, subscription, credit events
		"integration.events", // Platform connection status
		"asset.events",       // File upload confirmations
		"rag.events",         // Document processing status
		"system.events",      // Maintenance, updates, alerts
	}
}

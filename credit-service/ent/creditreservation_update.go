// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditreservation"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditReservationUpdate is the builder for updating CreditReservation entities.
type CreditReservationUpdate struct {
	config
	hooks    []Hook
	mutation *CreditReservationMutation
}

// Where appends a list predicates to the CreditReservationUpdate builder.
func (cru *CreditReservationUpdate) Where(ps ...predicate.CreditReservation) *CreditReservationUpdate {
	cru.mutation.Where(ps...)
	return cru
}

// SetUserID sets the "user_id" field.
func (cru *CreditReservationUpdate) SetUserID(u uuid.UUID) *CreditReservationUpdate {
	cru.mutation.SetUserID(u)
	return cru
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableUserID(u *uuid.UUID) *CreditReservationUpdate {
	if u != nil {
		cru.SetUserID(*u)
	}
	return cru
}

// SetAmount sets the "amount" field.
func (cru *CreditReservationUpdate) SetAmount(i int) *CreditReservationUpdate {
	cru.mutation.ResetAmount()
	cru.mutation.SetAmount(i)
	return cru
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableAmount(i *int) *CreditReservationUpdate {
	if i != nil {
		cru.SetAmount(*i)
	}
	return cru
}

// AddAmount adds i to the "amount" field.
func (cru *CreditReservationUpdate) AddAmount(i int) *CreditReservationUpdate {
	cru.mutation.AddAmount(i)
	return cru
}

// SetPurpose sets the "purpose" field.
func (cru *CreditReservationUpdate) SetPurpose(s string) *CreditReservationUpdate {
	cru.mutation.SetPurpose(s)
	return cru
}

// SetNillablePurpose sets the "purpose" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillablePurpose(s *string) *CreditReservationUpdate {
	if s != nil {
		cru.SetPurpose(*s)
	}
	return cru
}

// SetReferenceID sets the "reference_id" field.
func (cru *CreditReservationUpdate) SetReferenceID(s string) *CreditReservationUpdate {
	cru.mutation.SetReferenceID(s)
	return cru
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableReferenceID(s *string) *CreditReservationUpdate {
	if s != nil {
		cru.SetReferenceID(*s)
	}
	return cru
}

// ClearReferenceID clears the value of the "reference_id" field.
func (cru *CreditReservationUpdate) ClearReferenceID() *CreditReservationUpdate {
	cru.mutation.ClearReferenceID()
	return cru
}

// SetReferenceType sets the "reference_type" field.
func (cru *CreditReservationUpdate) SetReferenceType(s string) *CreditReservationUpdate {
	cru.mutation.SetReferenceType(s)
	return cru
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableReferenceType(s *string) *CreditReservationUpdate {
	if s != nil {
		cru.SetReferenceType(*s)
	}
	return cru
}

// ClearReferenceType clears the value of the "reference_type" field.
func (cru *CreditReservationUpdate) ClearReferenceType() *CreditReservationUpdate {
	cru.mutation.ClearReferenceType()
	return cru
}

// SetStatus sets the "status" field.
func (cru *CreditReservationUpdate) SetStatus(c creditreservation.Status) *CreditReservationUpdate {
	cru.mutation.SetStatus(c)
	return cru
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableStatus(c *creditreservation.Status) *CreditReservationUpdate {
	if c != nil {
		cru.SetStatus(*c)
	}
	return cru
}

// SetExpiresAt sets the "expires_at" field.
func (cru *CreditReservationUpdate) SetExpiresAt(t time.Time) *CreditReservationUpdate {
	cru.mutation.SetExpiresAt(t)
	return cru
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableExpiresAt(t *time.Time) *CreditReservationUpdate {
	if t != nil {
		cru.SetExpiresAt(*t)
	}
	return cru
}

// SetConsumedAt sets the "consumed_at" field.
func (cru *CreditReservationUpdate) SetConsumedAt(t time.Time) *CreditReservationUpdate {
	cru.mutation.SetConsumedAt(t)
	return cru
}

// SetNillableConsumedAt sets the "consumed_at" field if the given value is not nil.
func (cru *CreditReservationUpdate) SetNillableConsumedAt(t *time.Time) *CreditReservationUpdate {
	if t != nil {
		cru.SetConsumedAt(*t)
	}
	return cru
}

// ClearConsumedAt clears the value of the "consumed_at" field.
func (cru *CreditReservationUpdate) ClearConsumedAt() *CreditReservationUpdate {
	cru.mutation.ClearConsumedAt()
	return cru
}

// SetMetadata sets the "metadata" field.
func (cru *CreditReservationUpdate) SetMetadata(m map[string]interface{}) *CreditReservationUpdate {
	cru.mutation.SetMetadata(m)
	return cru
}

// ClearMetadata clears the value of the "metadata" field.
func (cru *CreditReservationUpdate) ClearMetadata() *CreditReservationUpdate {
	cru.mutation.ClearMetadata()
	return cru
}

// SetUpdatedAt sets the "updated_at" field.
func (cru *CreditReservationUpdate) SetUpdatedAt(t time.Time) *CreditReservationUpdate {
	cru.mutation.SetUpdatedAt(t)
	return cru
}

// Mutation returns the CreditReservationMutation object of the builder.
func (cru *CreditReservationUpdate) Mutation() *CreditReservationMutation {
	return cru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cru *CreditReservationUpdate) Save(ctx context.Context) (int, error) {
	cru.defaults()
	return withHooks(ctx, cru.sqlSave, cru.mutation, cru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cru *CreditReservationUpdate) SaveX(ctx context.Context) int {
	affected, err := cru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cru *CreditReservationUpdate) Exec(ctx context.Context) error {
	_, err := cru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cru *CreditReservationUpdate) ExecX(ctx context.Context) {
	if err := cru.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cru *CreditReservationUpdate) defaults() {
	if _, ok := cru.mutation.UpdatedAt(); !ok {
		v := creditreservation.UpdateDefaultUpdatedAt()
		cru.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cru *CreditReservationUpdate) check() error {
	if v, ok := cru.mutation.Amount(); ok {
		if err := creditreservation.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.amount": %w`, err)}
		}
	}
	if v, ok := cru.mutation.Purpose(); ok {
		if err := creditreservation.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.purpose": %w`, err)}
		}
	}
	if v, ok := cru.mutation.ReferenceID(); ok {
		if err := creditreservation.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_id": %w`, err)}
		}
	}
	if v, ok := cru.mutation.ReferenceType(); ok {
		if err := creditreservation.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_type": %w`, err)}
		}
	}
	if v, ok := cru.mutation.Status(); ok {
		if err := creditreservation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.status": %w`, err)}
		}
	}
	return nil
}

func (cru *CreditReservationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(creditreservation.Table, creditreservation.Columns, sqlgraph.NewFieldSpec(creditreservation.FieldID, field.TypeUUID))
	if ps := cru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cru.mutation.UserID(); ok {
		_spec.SetField(creditreservation.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := cru.mutation.Amount(); ok {
		_spec.SetField(creditreservation.FieldAmount, field.TypeInt, value)
	}
	if value, ok := cru.mutation.AddedAmount(); ok {
		_spec.AddField(creditreservation.FieldAmount, field.TypeInt, value)
	}
	if value, ok := cru.mutation.Purpose(); ok {
		_spec.SetField(creditreservation.FieldPurpose, field.TypeString, value)
	}
	if value, ok := cru.mutation.ReferenceID(); ok {
		_spec.SetField(creditreservation.FieldReferenceID, field.TypeString, value)
	}
	if cru.mutation.ReferenceIDCleared() {
		_spec.ClearField(creditreservation.FieldReferenceID, field.TypeString)
	}
	if value, ok := cru.mutation.ReferenceType(); ok {
		_spec.SetField(creditreservation.FieldReferenceType, field.TypeString, value)
	}
	if cru.mutation.ReferenceTypeCleared() {
		_spec.ClearField(creditreservation.FieldReferenceType, field.TypeString)
	}
	if value, ok := cru.mutation.Status(); ok {
		_spec.SetField(creditreservation.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := cru.mutation.ExpiresAt(); ok {
		_spec.SetField(creditreservation.FieldExpiresAt, field.TypeTime, value)
	}
	if value, ok := cru.mutation.ConsumedAt(); ok {
		_spec.SetField(creditreservation.FieldConsumedAt, field.TypeTime, value)
	}
	if cru.mutation.ConsumedAtCleared() {
		_spec.ClearField(creditreservation.FieldConsumedAt, field.TypeTime)
	}
	if value, ok := cru.mutation.Metadata(); ok {
		_spec.SetField(creditreservation.FieldMetadata, field.TypeJSON, value)
	}
	if cru.mutation.MetadataCleared() {
		_spec.ClearField(creditreservation.FieldMetadata, field.TypeJSON)
	}
	if value, ok := cru.mutation.UpdatedAt(); ok {
		_spec.SetField(creditreservation.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{creditreservation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cru.mutation.done = true
	return n, nil
}

// CreditReservationUpdateOne is the builder for updating a single CreditReservation entity.
type CreditReservationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CreditReservationMutation
}

// SetUserID sets the "user_id" field.
func (cruo *CreditReservationUpdateOne) SetUserID(u uuid.UUID) *CreditReservationUpdateOne {
	cruo.mutation.SetUserID(u)
	return cruo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableUserID(u *uuid.UUID) *CreditReservationUpdateOne {
	if u != nil {
		cruo.SetUserID(*u)
	}
	return cruo
}

// SetAmount sets the "amount" field.
func (cruo *CreditReservationUpdateOne) SetAmount(i int) *CreditReservationUpdateOne {
	cruo.mutation.ResetAmount()
	cruo.mutation.SetAmount(i)
	return cruo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableAmount(i *int) *CreditReservationUpdateOne {
	if i != nil {
		cruo.SetAmount(*i)
	}
	return cruo
}

// AddAmount adds i to the "amount" field.
func (cruo *CreditReservationUpdateOne) AddAmount(i int) *CreditReservationUpdateOne {
	cruo.mutation.AddAmount(i)
	return cruo
}

// SetPurpose sets the "purpose" field.
func (cruo *CreditReservationUpdateOne) SetPurpose(s string) *CreditReservationUpdateOne {
	cruo.mutation.SetPurpose(s)
	return cruo
}

// SetNillablePurpose sets the "purpose" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillablePurpose(s *string) *CreditReservationUpdateOne {
	if s != nil {
		cruo.SetPurpose(*s)
	}
	return cruo
}

// SetReferenceID sets the "reference_id" field.
func (cruo *CreditReservationUpdateOne) SetReferenceID(s string) *CreditReservationUpdateOne {
	cruo.mutation.SetReferenceID(s)
	return cruo
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableReferenceID(s *string) *CreditReservationUpdateOne {
	if s != nil {
		cruo.SetReferenceID(*s)
	}
	return cruo
}

// ClearReferenceID clears the value of the "reference_id" field.
func (cruo *CreditReservationUpdateOne) ClearReferenceID() *CreditReservationUpdateOne {
	cruo.mutation.ClearReferenceID()
	return cruo
}

// SetReferenceType sets the "reference_type" field.
func (cruo *CreditReservationUpdateOne) SetReferenceType(s string) *CreditReservationUpdateOne {
	cruo.mutation.SetReferenceType(s)
	return cruo
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableReferenceType(s *string) *CreditReservationUpdateOne {
	if s != nil {
		cruo.SetReferenceType(*s)
	}
	return cruo
}

// ClearReferenceType clears the value of the "reference_type" field.
func (cruo *CreditReservationUpdateOne) ClearReferenceType() *CreditReservationUpdateOne {
	cruo.mutation.ClearReferenceType()
	return cruo
}

// SetStatus sets the "status" field.
func (cruo *CreditReservationUpdateOne) SetStatus(c creditreservation.Status) *CreditReservationUpdateOne {
	cruo.mutation.SetStatus(c)
	return cruo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableStatus(c *creditreservation.Status) *CreditReservationUpdateOne {
	if c != nil {
		cruo.SetStatus(*c)
	}
	return cruo
}

// SetExpiresAt sets the "expires_at" field.
func (cruo *CreditReservationUpdateOne) SetExpiresAt(t time.Time) *CreditReservationUpdateOne {
	cruo.mutation.SetExpiresAt(t)
	return cruo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableExpiresAt(t *time.Time) *CreditReservationUpdateOne {
	if t != nil {
		cruo.SetExpiresAt(*t)
	}
	return cruo
}

// SetConsumedAt sets the "consumed_at" field.
func (cruo *CreditReservationUpdateOne) SetConsumedAt(t time.Time) *CreditReservationUpdateOne {
	cruo.mutation.SetConsumedAt(t)
	return cruo
}

// SetNillableConsumedAt sets the "consumed_at" field if the given value is not nil.
func (cruo *CreditReservationUpdateOne) SetNillableConsumedAt(t *time.Time) *CreditReservationUpdateOne {
	if t != nil {
		cruo.SetConsumedAt(*t)
	}
	return cruo
}

// ClearConsumedAt clears the value of the "consumed_at" field.
func (cruo *CreditReservationUpdateOne) ClearConsumedAt() *CreditReservationUpdateOne {
	cruo.mutation.ClearConsumedAt()
	return cruo
}

// SetMetadata sets the "metadata" field.
func (cruo *CreditReservationUpdateOne) SetMetadata(m map[string]interface{}) *CreditReservationUpdateOne {
	cruo.mutation.SetMetadata(m)
	return cruo
}

// ClearMetadata clears the value of the "metadata" field.
func (cruo *CreditReservationUpdateOne) ClearMetadata() *CreditReservationUpdateOne {
	cruo.mutation.ClearMetadata()
	return cruo
}

// SetUpdatedAt sets the "updated_at" field.
func (cruo *CreditReservationUpdateOne) SetUpdatedAt(t time.Time) *CreditReservationUpdateOne {
	cruo.mutation.SetUpdatedAt(t)
	return cruo
}

// Mutation returns the CreditReservationMutation object of the builder.
func (cruo *CreditReservationUpdateOne) Mutation() *CreditReservationMutation {
	return cruo.mutation
}

// Where appends a list predicates to the CreditReservationUpdate builder.
func (cruo *CreditReservationUpdateOne) Where(ps ...predicate.CreditReservation) *CreditReservationUpdateOne {
	cruo.mutation.Where(ps...)
	return cruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cruo *CreditReservationUpdateOne) Select(field string, fields ...string) *CreditReservationUpdateOne {
	cruo.fields = append([]string{field}, fields...)
	return cruo
}

// Save executes the query and returns the updated CreditReservation entity.
func (cruo *CreditReservationUpdateOne) Save(ctx context.Context) (*CreditReservation, error) {
	cruo.defaults()
	return withHooks(ctx, cruo.sqlSave, cruo.mutation, cruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cruo *CreditReservationUpdateOne) SaveX(ctx context.Context) *CreditReservation {
	node, err := cruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cruo *CreditReservationUpdateOne) Exec(ctx context.Context) error {
	_, err := cruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cruo *CreditReservationUpdateOne) ExecX(ctx context.Context) {
	if err := cruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cruo *CreditReservationUpdateOne) defaults() {
	if _, ok := cruo.mutation.UpdatedAt(); !ok {
		v := creditreservation.UpdateDefaultUpdatedAt()
		cruo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cruo *CreditReservationUpdateOne) check() error {
	if v, ok := cruo.mutation.Amount(); ok {
		if err := creditreservation.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.amount": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.Purpose(); ok {
		if err := creditreservation.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.purpose": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.ReferenceID(); ok {
		if err := creditreservation.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_id": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.ReferenceType(); ok {
		if err := creditreservation.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.reference_type": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.Status(); ok {
		if err := creditreservation.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditReservation.status": %w`, err)}
		}
	}
	return nil
}

func (cruo *CreditReservationUpdateOne) sqlSave(ctx context.Context) (_node *CreditReservation, err error) {
	if err := cruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(creditreservation.Table, creditreservation.Columns, sqlgraph.NewFieldSpec(creditreservation.FieldID, field.TypeUUID))
	id, ok := cruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CreditReservation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, creditreservation.FieldID)
		for _, f := range fields {
			if !creditreservation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != creditreservation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cruo.mutation.UserID(); ok {
		_spec.SetField(creditreservation.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := cruo.mutation.Amount(); ok {
		_spec.SetField(creditreservation.FieldAmount, field.TypeInt, value)
	}
	if value, ok := cruo.mutation.AddedAmount(); ok {
		_spec.AddField(creditreservation.FieldAmount, field.TypeInt, value)
	}
	if value, ok := cruo.mutation.Purpose(); ok {
		_spec.SetField(creditreservation.FieldPurpose, field.TypeString, value)
	}
	if value, ok := cruo.mutation.ReferenceID(); ok {
		_spec.SetField(creditreservation.FieldReferenceID, field.TypeString, value)
	}
	if cruo.mutation.ReferenceIDCleared() {
		_spec.ClearField(creditreservation.FieldReferenceID, field.TypeString)
	}
	if value, ok := cruo.mutation.ReferenceType(); ok {
		_spec.SetField(creditreservation.FieldReferenceType, field.TypeString, value)
	}
	if cruo.mutation.ReferenceTypeCleared() {
		_spec.ClearField(creditreservation.FieldReferenceType, field.TypeString)
	}
	if value, ok := cruo.mutation.Status(); ok {
		_spec.SetField(creditreservation.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := cruo.mutation.ExpiresAt(); ok {
		_spec.SetField(creditreservation.FieldExpiresAt, field.TypeTime, value)
	}
	if value, ok := cruo.mutation.ConsumedAt(); ok {
		_spec.SetField(creditreservation.FieldConsumedAt, field.TypeTime, value)
	}
	if cruo.mutation.ConsumedAtCleared() {
		_spec.ClearField(creditreservation.FieldConsumedAt, field.TypeTime)
	}
	if value, ok := cruo.mutation.Metadata(); ok {
		_spec.SetField(creditreservation.FieldMetadata, field.TypeJSON, value)
	}
	if cruo.mutation.MetadataCleared() {
		_spec.ClearField(creditreservation.FieldMetadata, field.TypeJSON)
	}
	if value, ok := cruo.mutation.UpdatedAt(); ok {
		_spec.SetField(creditreservation.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &CreditReservation{config: cruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{creditreservation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cruo.mutation.done = true
	return _node, nil
}

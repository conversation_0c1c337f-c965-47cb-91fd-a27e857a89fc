// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/ent/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	assetFields := schema.Asset{}.Fields()
	_ = assetFields
	// assetDescWorkspaceID is the schema descriptor for workspace_id field.
	assetDescWorkspaceID := assetFields[2].Descriptor()
	// asset.WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	asset.WorkspaceIDValidator = assetDescWorkspaceID.Validators[0].(func(string) error)
	// assetDescFileName is the schema descriptor for file_name field.
	assetDescFileName := assetFields[4].Descriptor()
	// asset.FileNameValidator is a validator for the "file_name" field. It is called by the builders before save.
	asset.FileNameValidator = assetDescFileName.Validators[0].(func(string) error)
	// assetDescOriginalName is the schema descriptor for original_name field.
	assetDescOriginalName := assetFields[5].Descriptor()
	// asset.OriginalNameValidator is a validator for the "original_name" field. It is called by the builders before save.
	asset.OriginalNameValidator = assetDescOriginalName.Validators[0].(func(string) error)
	// assetDescFileType is the schema descriptor for file_type field.
	assetDescFileType := assetFields[6].Descriptor()
	// asset.FileTypeValidator is a validator for the "file_type" field. It is called by the builders before save.
	asset.FileTypeValidator = assetDescFileType.Validators[0].(func(string) error)
	// assetDescFileExtension is the schema descriptor for file_extension field.
	assetDescFileExtension := assetFields[7].Descriptor()
	// asset.FileExtensionValidator is a validator for the "file_extension" field. It is called by the builders before save.
	asset.FileExtensionValidator = assetDescFileExtension.Validators[0].(func(string) error)
	// assetDescS3Key is the schema descriptor for s3_key field.
	assetDescS3Key := assetFields[8].Descriptor()
	// asset.S3KeyValidator is a validator for the "s3_key" field. It is called by the builders before save.
	asset.S3KeyValidator = assetDescS3Key.Validators[0].(func(string) error)
	// assetDescS3Bucket is the schema descriptor for s3_bucket field.
	assetDescS3Bucket := assetFields[9].Descriptor()
	// asset.S3BucketValidator is a validator for the "s3_bucket" field. It is called by the builders before save.
	asset.S3BucketValidator = assetDescS3Bucket.Validators[0].(func(string) error)
	// assetDescContentHash is the schema descriptor for content_hash field.
	assetDescContentHash := assetFields[10].Descriptor()
	// asset.ContentHashValidator is a validator for the "content_hash" field. It is called by the builders before save.
	asset.ContentHashValidator = assetDescContentHash.Validators[0].(func(string) error)
	// assetDescPurpose is the schema descriptor for purpose field.
	assetDescPurpose := assetFields[11].Descriptor()
	// asset.DefaultPurpose holds the default value on creation for the purpose field.
	asset.DefaultPurpose = assetDescPurpose.Default.(string)
	// asset.PurposeValidator is a validator for the "purpose" field. It is called by the builders before save.
	asset.PurposeValidator = assetDescPurpose.Validators[0].(func(string) error)
	// assetDescFileSize is the schema descriptor for file_size field.
	assetDescFileSize := assetFields[12].Descriptor()
	// asset.DefaultFileSize holds the default value on creation for the file_size field.
	asset.DefaultFileSize = assetDescFileSize.Default.(int64)
	// assetDescThumbnailURL is the schema descriptor for thumbnail_url field.
	assetDescThumbnailURL := assetFields[13].Descriptor()
	// asset.ThumbnailURLValidator is a validator for the "thumbnail_url" field. It is called by the builders before save.
	asset.ThumbnailURLValidator = assetDescThumbnailURL.Validators[0].(func(string) error)
	// assetDescStatus is the schema descriptor for status field.
	assetDescStatus := assetFields[15].Descriptor()
	// asset.DefaultStatus holds the default value on creation for the status field.
	asset.DefaultStatus = assetDescStatus.Default.(string)
	// asset.StatusValidator is a validator for the "status" field. It is called by the builders before save.
	asset.StatusValidator = assetDescStatus.Validators[0].(func(string) error)
	// assetDescIsPublic is the schema descriptor for is_public field.
	assetDescIsPublic := assetFields[16].Descriptor()
	// asset.DefaultIsPublic holds the default value on creation for the is_public field.
	asset.DefaultIsPublic = assetDescIsPublic.Default.(bool)
	// assetDescCreatedAt is the schema descriptor for created_at field.
	assetDescCreatedAt := assetFields[17].Descriptor()
	// asset.DefaultCreatedAt holds the default value on creation for the created_at field.
	asset.DefaultCreatedAt = assetDescCreatedAt.Default.(func() time.Time)
	// assetDescUpdatedAt is the schema descriptor for updated_at field.
	assetDescUpdatedAt := assetFields[18].Descriptor()
	// asset.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	asset.DefaultUpdatedAt = assetDescUpdatedAt.Default.(func() time.Time)
	// asset.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	asset.UpdateDefaultUpdatedAt = assetDescUpdatedAt.UpdateDefault.(func() time.Time)
	// assetDescID is the schema descriptor for id field.
	assetDescID := assetFields[0].Descriptor()
	// asset.DefaultID holds the default value on creation for the id field.
	asset.DefaultID = assetDescID.Default.(func() uuid.UUID)
	folderFields := schema.Folder{}.Fields()
	_ = folderFields
	// folderDescName is the schema descriptor for name field.
	folderDescName := folderFields[3].Descriptor()
	// folder.NameValidator is a validator for the "name" field. It is called by the builders before save.
	folder.NameValidator = func() func(string) error {
		validators := folderDescName.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(name string) error {
			for _, fn := range fns {
				if err := fn(name); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// folderDescDescription is the schema descriptor for description field.
	folderDescDescription := folderFields[4].Descriptor()
	// folder.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	folder.DescriptionValidator = folderDescDescription.Validators[0].(func(string) error)
	// folderDescPath is the schema descriptor for path field.
	folderDescPath := folderFields[5].Descriptor()
	// folder.PathValidator is a validator for the "path" field. It is called by the builders before save.
	folder.PathValidator = folderDescPath.Validators[0].(func(string) error)
	// folderDescLevel is the schema descriptor for level field.
	folderDescLevel := folderFields[6].Descriptor()
	// folder.DefaultLevel holds the default value on creation for the level field.
	folder.DefaultLevel = folderDescLevel.Default.(int)
	// folderDescIsShared is the schema descriptor for is_shared field.
	folderDescIsShared := folderFields[7].Descriptor()
	// folder.DefaultIsShared holds the default value on creation for the is_shared field.
	folder.DefaultIsShared = folderDescIsShared.Default.(bool)
	// folderDescAssetCount is the schema descriptor for asset_count field.
	folderDescAssetCount := folderFields[10].Descriptor()
	// folder.DefaultAssetCount holds the default value on creation for the asset_count field.
	folder.DefaultAssetCount = folderDescAssetCount.Default.(int64)
	// folderDescTotalSize is the schema descriptor for total_size field.
	folderDescTotalSize := folderFields[11].Descriptor()
	// folder.DefaultTotalSize holds the default value on creation for the total_size field.
	folder.DefaultTotalSize = folderDescTotalSize.Default.(int64)
	// folderDescCreatedAt is the schema descriptor for created_at field.
	folderDescCreatedAt := folderFields[12].Descriptor()
	// folder.DefaultCreatedAt holds the default value on creation for the created_at field.
	folder.DefaultCreatedAt = folderDescCreatedAt.Default.(func() time.Time)
	// folderDescUpdatedAt is the schema descriptor for updated_at field.
	folderDescUpdatedAt := folderFields[13].Descriptor()
	// folder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	folder.DefaultUpdatedAt = folderDescUpdatedAt.Default.(func() time.Time)
	// folder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	folder.UpdateDefaultUpdatedAt = folderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// folderDescID is the schema descriptor for id field.
	folderDescID := folderFields[0].Descriptor()
	// folder.DefaultID holds the default value on creation for the id field.
	folder.DefaultID = folderDescID.Default.(func() uuid.UUID)
}

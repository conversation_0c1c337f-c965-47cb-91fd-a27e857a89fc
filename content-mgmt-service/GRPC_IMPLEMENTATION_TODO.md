# 🚧 gRPC Implementation TODO - Content Management Service

## 📋 Tổng quan
Content Management Service hiện tại chưa implement đầy đủ các gRPC methods theo định nghĩa trong proto-shared. Tài liệu này liệt kê những gì cần hoàn thành.

## ✅ Đã sửa (2025-06-19)
- **Fixed linting issues**: Thay thế `interface{}` bằng `any` trong tất cả logging statements
- **Cleaned up gRPC handlers**: Comment out các method chưa implement để tránh build errors
- **Updated interface**: Thêm TODO comments cho các method cần implement
- **Build success**: Service hiện tại có thể build thành công

## ✅ Đã implement
### PostService (Ho<PERSON><PERSON> thành 100%)
- ✅ CreatePost
- ✅ GetPost  
- ✅ UpdatePost
- ✅ ListPosts
- ✅ DeletePost
- ✅ PublishPost
- ✅ SchedulePost

## ❌ Cần implement

### TemplateService (<PERSON><PERSON><PERSON> thành 60%)
**Đ<PERSON> có:**
- ✅ CreateTemplate
- ✅ GetTemplate
- ✅ UpdateTemplate  
- ✅ ListTemplates
- ✅ UseTemplate
- ✅ DeleteTemplate

**Còn thiếu:**
- ❌ RateTemplate
- ❌ GetTemplateRatings
- ❌ PurchaseTemplate
- ❌ GetPurchasedTemplates
- ❌ GetTemplateRevenue

### WorkspaceService (Hoàn thành 0%)
**Workspace Management:**
- ❌ CreateWorkspace
- ❌ GetWorkspace
- ❌ UpdateWorkspace
- ❌ ListWorkspaces
- ❌ DeleteWorkspace

**Member Management:**
- ❌ InviteMember
- ❌ AcceptInvitation
- ❌ UpdateMemberRole
- ❌ RemoveMember
- ❌ ListMembers

**Other:**
- ❌ ListInvitations
- ❌ GetWorkspaceStats

## 🔧 Cần tạo Domain Models

### Template Models (pkg/models/template.go)
```go
// Rating related
type RateTemplateRequest struct {
    TemplateID string
    UserID     string
    Rating     int    // 1-5 stars
    Review     string // Optional review text
}

type GetTemplateRatingsRequest struct {
    TemplateID string
    Page       int
    Limit      int
    MinRating  int
}

type TemplateRating struct {
    ID         string
    TemplateID string
    UserID     string
    Rating     int
    Review     string
    CreatedAt  time.Time
}

type GetTemplateRatingsResponse struct {
    Ratings            []TemplateRating
    Pagination         PaginationResponse
    AverageRating      float64
    TotalRatings       int
    RatingDistribution map[int]int // rating -> count
}

// Purchase related
type PurchaseTemplateRequest struct {
    TemplateID      string
    UserID          string
    WorkspaceID     string
    ValidateCredits bool
}

type PurchaseTemplateResponse struct {
    PurchaseID      string
    TemplateID      string
    CreditsCharged  int
    TransactionID   string
    PurchasedAt     time.Time
    Template        *TemplateResponse
}

type GetPurchasedTemplatesRequest struct {
    UserID      string
    WorkspaceID string
    Category    string
    Page        int
    Limit       int
}

type GetPurchasedTemplatesResponse struct {
    Templates  []TemplateResponse
    Pagination PaginationResponse
    Purchases  []PurchaseInfo
}

type PurchaseInfo struct {
    PurchaseID     string
    TemplateID     string
    CreditsCharged int
    PurchasedAt    time.Time
}

// Revenue related
type GetTemplateRevenueRequest struct {
    UserID     string
    TemplateID string
    DateFrom   string
    DateTo     string
    Page       int
    Limit      int
}

type TemplateRevenue struct {
    TemplateID       string
    TemplateName     string
    TotalPurchases   int
    TotalRevenue     int
    MonthlyPurchases int
    MonthlyRevenue   int
    AverageRating    float64
    LastPurchase     time.Time
}

type GetTemplateRevenueResponse struct {
    Revenues       []TemplateRevenue
    Pagination     PaginationResponse
    TotalRevenue   int
    TotalPurchases int
}
```

### Workspace Models (pkg/models/workspace.go)
```go
type CreateWorkspaceRequest struct {
    Name        string
    Description string
    OwnerID     string
    Plan        string
    Settings    WorkspaceSettings
}

type UpdateWorkspaceRequest struct {
    ID          string
    Name        string
    Description string
    Plan        string
    Settings    WorkspaceSettings
}

type WorkspaceResponse struct {
    ID          string
    Name        string
    Description string
    OwnerID     string
    Plan        string
    Settings    WorkspaceSettings
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

type WorkspaceSettings struct {
    AllowPublicTemplates bool
    RequireApproval      bool
    AllowAIGeneration    bool
    AllowedPlatforms     []string
    Branding             map[string]string
    EnableAnalytics      bool
}

type InviteMemberRequest struct {
    WorkspaceID string
    Email       string
    Role        string
    Message     string
}

type WorkspaceMember struct {
    ID          string
    WorkspaceID string
    UserID      string
    Role        string
    Status      string
    JoinedAt    time.Time
}

type WorkspaceStats struct {
    TotalMembers    int
    ActiveMembers   int
    TotalPosts      int
    PublishedPosts  int
    ScheduledPosts  int
    TotalTemplates  int
    PublicTemplates int
    StorageUsed     int64
    StorageLimit    int64
}
```

## 🎯 Cần implement UseCase Methods

### Template UseCase (usecase/template/interface.go)
```go
type UseCase interface {
    // Existing methods...
    
    // Rating methods
    RateTemplate(ctx context.Context, req *models.RateTemplateRequest) error
    GetTemplateRatings(ctx context.Context, req *models.GetTemplateRatingsRequest) (*models.GetTemplateRatingsResponse, error)
    
    // Purchase methods
    PurchaseTemplate(ctx context.Context, req *models.PurchaseTemplateRequest) (*models.PurchaseTemplateResponse, error)
    GetPurchasedTemplates(ctx context.Context, req *models.GetPurchasedTemplatesRequest) (*models.GetPurchasedTemplatesResponse, error)
    
    // Revenue methods
    GetTemplateRevenue(ctx context.Context, req *models.GetTemplateRevenueRequest) (*models.GetTemplateRevenueResponse, error)
}
```

### Workspace UseCase (usecase/workspace/interface.go)
```go
type UseCase interface {
    // Workspace management
    CreateWorkspace(ctx context.Context, req *models.CreateWorkspaceRequest) (*models.WorkspaceResponse, error)
    GetWorkspace(ctx context.Context, id, userID string) (*models.WorkspaceResponse, error)
    UpdateWorkspace(ctx context.Context, req *models.UpdateWorkspaceRequest) (*models.WorkspaceResponse, error)
    ListWorkspaces(ctx context.Context, req *models.ListWorkspacesRequest) (*models.ListWorkspacesResponse, error)
    DeleteWorkspace(ctx context.Context, id, userID string) error
    
    // Member management
    InviteMember(ctx context.Context, req *models.InviteMemberRequest) (*models.InviteMemberResponse, error)
    AcceptInvitation(ctx context.Context, req *models.AcceptInvitationRequest) (*models.AcceptInvitationResponse, error)
    UpdateMemberRole(ctx context.Context, req *models.UpdateMemberRoleRequest) (*models.WorkspaceMember, error)
    RemoveMember(ctx context.Context, req *models.RemoveMemberRequest) error
    ListMembers(ctx context.Context, req *models.ListMembersRequest) (*models.ListMembersResponse, error)
    
    // Other
    ListInvitations(ctx context.Context, req *models.ListInvitationsRequest) (*models.ListInvitationsResponse, error)
    GetWorkspaceStats(ctx context.Context, req *models.GetWorkspaceStatsRequest) (*models.WorkspaceStats, error)
}
```

## 📝 Các bước thực hiện

1. **Tạo domain models** trong `pkg/models/`
2. **Implement UseCase interfaces** trong `usecase/template/` và `usecase/workspace/`
3. **Implement database operations** trong repository layer
4. **Complete gRPC handlers** trong `api/grpc/handlers/`
5. **Add validation và error handling**
6. **Write unit tests**
7. **Update API documentation**

## 🔗 Liên quan đến microservices khác

- **Credit Service**: Cần gọi để validate và deduct credits cho template purchases
- **User Service**: Cần gọi để validate user permissions và get user info
- **Notification Service**: Cần gửi notifications cho workspace invitations
- **Analytics Service**: Workspace stats có thể cần data từ Analytics Service

## 📚 Tài liệu tham khảo

- Proto definitions: `proto-shared/content-mgmt/v1/`
- Microservices architecture: `microservices-architecture-design.md`
- API specification: `socialai-api.yaml`

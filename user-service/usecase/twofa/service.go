package twofa

import (
	"context"
	"fmt"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/ent/user"
	"github.com/social-content-ai/user-service/pkg/twofa"
	"golang.org/x/crypto/bcrypt"
)

// service implements the TwoFA UseCase interface
type service struct {
	readDB   *ent.Client
	writeDB  *ent.Client
	twofaSvc *twofa.Service
	logger   logging.Logger
}

// NewService creates a new TwoFA service
func NewService(
	readDB *ent.Client,
	writeDB *ent.Client,
	twofaSvc *twofa.Service,
	logger logging.Logger,
) UseCase {
	return &service{
		readDB:   readDB,
		writeDB:  writeDB,
		twofaSvc: twofaSvc,
		logger:   logger,
	}
}

// SetupTwoFA generates a new 2FA secret and backup codes for a user
func (s *service) SetupTwoFA(ctx context.Context, req *SetupTwoFARequest) (*SetupTwoFAResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Check if 2FA is already enabled
	if userEntity.TwoFactorEnabled {
		return nil, fmt.Errorf("2FA is already enabled for this user")
	}

	// Generate secret
	secretReq := &twofa.GenerateSecretRequest{
		UserEmail: userEntity.Email,
		UserID:    fmt.Sprintf("%d", req.UserID),
	}

	secretResp, err := s.twofaSvc.GenerateSecret(secretReq)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to generate 2FA secret")
		return nil, fmt.Errorf("failed to generate 2FA secret: %w", err)
	}

	// Generate QR code
	qrCode, err := s.twofaSvc.GenerateQRCode(secretResp.Secret, userEntity.Email)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to generate QR code")
		return nil, fmt.Errorf("failed to generate QR code: %w", err)
	}

	// Generate backup codes
	backupCodesResp, err := s.twofaSvc.GenerateBackupCodes()
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to generate backup codes")
		return nil, fmt.Errorf("failed to generate backup codes: %w", err)
	}

	// Store secret and backup codes (but don't enable 2FA yet)
	_, err = s.writeDB.User.UpdateOneID(userEntity.ID).
		SetTwoFactorSecret(secretResp.Secret).
		SetTwoFactorBackupCodes(backupCodesResp.Codes).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to store 2FA setup")
		return nil, fmt.Errorf("failed to store 2FA setup: %w", err)
	}

	s.logger.WithField("user_id", req.UserID).Info("2FA setup completed")

	return &SetupTwoFAResponse{
		Secret:      secretResp.Secret,
		QRCodeURL:   secretResp.QRCodeURL,
		QRCode:      qrCode,
		BackupCodes: backupCodesResp.Codes,
	}, nil
}

// EnableTwoFA enables 2FA after verifying the setup token
func (s *service) EnableTwoFA(ctx context.Context, req *EnableTwoFARequest) (*EnableTwoFAResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Check if user has a secret (setup was done)
	if userEntity.TwoFactorSecret == "" {
		return nil, fmt.Errorf("2FA setup not completed. Please setup 2FA first")
	}

	// Check if 2FA is already enabled
	if userEntity.TwoFactorEnabled {
		return nil, fmt.Errorf("2FA is already enabled for this user")
	}

	// Verify the token
	verifyReq := &twofa.VerifyTokenRequest{
		Secret: userEntity.TwoFactorSecret,
		Token:  req.Token,
	}

	valid, err := s.twofaSvc.VerifyToken(verifyReq)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to verify 2FA token")
		return nil, fmt.Errorf("failed to verify token: %w", err)
	}

	if !valid {
		s.logger.WithField("user_id", req.UserID).Warn("Invalid 2FA token provided during enable")
		return nil, fmt.Errorf("invalid token provided")
	}

	// Enable 2FA
	now := time.Now()
	updatedUser, err := s.writeDB.User.UpdateOneID(userEntity.ID).
		SetTwoFactorEnabled(true).
		SetTwoFactorEnabledAt(now).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to enable 2FA")
		return nil, fmt.Errorf("failed to enable 2FA: %w", err)
	}

	s.logger.WithField("user_id", req.UserID).Info("2FA enabled successfully")

	// Get backup codes
	backupCodes := []string{}
	if updatedUser.TwoFactorBackupCodes != nil {
		backupCodes = updatedUser.TwoFactorBackupCodes
	}

	return &EnableTwoFAResponse{
		Enabled:     true,
		BackupCodes: backupCodes,
		EnabledAt:   now.Format(time.RFC3339),
	}, nil
}

// DisableTwoFA disables 2FA for a user
func (s *service) DisableTwoFA(ctx context.Context, req *DisableTwoFARequest) error {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return err
	}

	// Check if 2FA is enabled
	if !userEntity.TwoFactorEnabled {
		return fmt.Errorf("2FA is not enabled for this user")
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(userEntity.PasswordHash), []byte(req.Password))
	if err != nil {
		s.logger.WithField("user_id", req.UserID).Warn("Invalid password provided during 2FA disable")
		return fmt.Errorf("invalid password provided")
	}

	// If token is provided, verify it
	if req.Token != "" {
		if userEntity.TwoFactorSecret != "" {
			// Try TOTP token first
			verifyReq := &twofa.VerifyTokenRequest{
				Secret: userEntity.TwoFactorSecret,
				Token:  req.Token,
			}

			valid, err := s.twofaSvc.VerifyToken(verifyReq)
			if err != nil {
				s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to verify 2FA token during disable")
				return fmt.Errorf("failed to verify token: %w", err)
			}

			if !valid {
				// Try backup code
				if userEntity.TwoFactorBackupCodes != nil {
					backupCodes := userEntity.TwoFactorBackupCodes
					isValid, _ := s.twofaSvc.VerifyBackupCode(req.Token, backupCodes)
					if !isValid {
						return fmt.Errorf("invalid token or backup code provided")
					}
				} else {
					return fmt.Errorf("invalid token provided")
				}
			}
		}
	}

	// Disable 2FA and clear secrets
	_, err = s.writeDB.User.UpdateOneID(userEntity.ID).
		SetTwoFactorEnabled(false).
		ClearTwoFactorSecret().
		ClearTwoFactorBackupCodes().
		ClearTwoFactorEnabledAt().
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to disable 2FA")
		return fmt.Errorf("failed to disable 2FA: %w", err)
	}

	s.logger.WithField("user_id", req.UserID).Info("2FA disabled successfully")
	return nil
}

// VerifyTwoFA verifies a 2FA token during login
func (s *service) VerifyTwoFA(ctx context.Context, req *VerifyTwoFARequest) (*VerifyTwoFAResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Check if 2FA is enabled
	if !userEntity.TwoFactorEnabled {
		return nil, fmt.Errorf("2FA is not enabled for this user")
	}

	// Check if user has a secret
	if userEntity.TwoFactorSecret == "" {
		return nil, fmt.Errorf("2FA secret not found")
	}

	// Try TOTP token first
	verifyReq := &twofa.VerifyTokenRequest{
		Secret: userEntity.TwoFactorSecret,
		Token:  req.Token,
	}

	valid, err := s.twofaSvc.VerifyToken(verifyReq)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to verify 2FA token")
		return nil, fmt.Errorf("failed to verify token: %w", err)
	}

	if valid {
		s.logger.WithField("user_id", req.UserID).Info("2FA token verified successfully")
		return &VerifyTwoFAResponse{
			Valid:   true,
			Message: "Token verified successfully",
		}, nil
	}

	// Try backup code
	if userEntity.TwoFactorBackupCodes != nil {
		backupCodes := userEntity.TwoFactorBackupCodes
		isValid, index := s.twofaSvc.VerifyBackupCode(req.Token, backupCodes)

		if isValid {
			// Remove used backup code
			newBackupCodes := s.twofaSvc.RemoveUsedBackupCode(backupCodes, index)

			// Update user with remaining backup codes
			_, err = s.writeDB.User.UpdateOneID(userEntity.ID).
				SetTwoFactorBackupCodes(newBackupCodes).
				Save(ctx)
			if err != nil {
				s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to update backup codes")
				// Don't fail the verification, just log the error
			}

			s.logger.WithFields(map[string]interface{}{
				"user_id":         req.UserID,
				"remaining_codes": len(newBackupCodes),
			}).Info("2FA backup code verified successfully")

			return &VerifyTwoFAResponse{
				Valid:          true,
				BackupCodeUsed: true,
				RemainingCodes: len(newBackupCodes),
				Message:        fmt.Sprintf("Backup code verified. %d codes remaining", len(newBackupCodes)),
			}, nil
		}
	}

	s.logger.WithField("user_id", req.UserID).Warn("Invalid 2FA token or backup code provided")
	return &VerifyTwoFAResponse{
		Valid:   false,
		Message: "Invalid token or backup code",
	}, nil
}

// GenerateBackupCodes generates new backup codes for a user
func (s *service) GenerateBackupCodes(ctx context.Context, req *GenerateBackupCodesRequest) (*GenerateBackupCodesResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Check if 2FA is enabled
	if !userEntity.TwoFactorEnabled {
		return nil, fmt.Errorf("2FA is not enabled for this user")
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(userEntity.PasswordHash), []byte(req.Password))
	if err != nil {
		s.logger.WithField("user_id", req.UserID).Warn("Invalid password provided during backup codes generation")
		return nil, fmt.Errorf("invalid password provided")
	}

	// Generate new backup codes
	backupCodesResp, err := s.twofaSvc.GenerateBackupCodes()
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to generate backup codes")
		return nil, fmt.Errorf("failed to generate backup codes: %w", err)
	}

	// Update user with new backup codes
	_, err = s.writeDB.User.UpdateOneID(userEntity.ID).
		SetTwoFactorBackupCodes(backupCodesResp.Codes).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to store new backup codes")
		return nil, fmt.Errorf("failed to store new backup codes: %w", err)
	}

	s.logger.WithField("user_id", req.UserID).Info("New backup codes generated successfully")

	return &GenerateBackupCodesResponse{
		BackupCodes: backupCodesResp.Codes,
		GeneratedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// VerifyBackupCode verifies a backup code
func (s *service) VerifyBackupCode(ctx context.Context, req *VerifyBackupCodeRequest) (*VerifyBackupCodeResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Check if 2FA is enabled
	if !userEntity.TwoFactorEnabled {
		return nil, fmt.Errorf("2FA is not enabled for this user")
	}

	// Check if user has backup codes
	if userEntity.TwoFactorBackupCodes == nil {
		return &VerifyBackupCodeResponse{
			Valid:          false,
			RemainingCodes: 0,
		}, nil
	}

	backupCodes := userEntity.TwoFactorBackupCodes
	isValid, index := s.twofaSvc.VerifyBackupCode(req.BackupCode, backupCodes)

	if !isValid {
		s.logger.WithField("user_id", req.UserID).Warn("Invalid backup code provided")
		return &VerifyBackupCodeResponse{
			Valid:          false,
			RemainingCodes: len(backupCodes),
		}, nil
	}

	// Remove used backup code
	newBackupCodes := s.twofaSvc.RemoveUsedBackupCode(backupCodes, index)

	// Update user with remaining backup codes
	_, err = s.writeDB.User.UpdateOneID(userEntity.ID).
		SetTwoFactorBackupCodes(newBackupCodes).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to update backup codes")
		return nil, fmt.Errorf("failed to update backup codes: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":         req.UserID,
		"remaining_codes": len(newBackupCodes),
	}).Info("Backup code verified and removed")

	return &VerifyBackupCodeResponse{
		Valid:          true,
		RemainingCodes: len(newBackupCodes),
	}, nil
}

// GetTwoFAStatus returns the 2FA status for a user
func (s *service) GetTwoFAStatus(ctx context.Context, userID uint64) (*TwoFAStatusResponse, error) {
	// Find user
	userEntity, err := s.findUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	status := &TwoFAStatusResponse{
		UserID:       userID,
		TwoFAEnabled: userEntity.TwoFactorEnabled,
	}

	if userEntity.TwoFactorEnabled {
		status.EnabledAt = userEntity.TwoFactorEnabledAt.Format(time.RFC3339)
	}

	if userEntity.TwoFactorBackupCodes != nil {
		backupCodes := userEntity.TwoFactorBackupCodes
		status.BackupCodesCount = len(backupCodes)
		status.HasBackupCodes = len(backupCodes) > 0
	}

	return status, nil
}

// findUserByID finds a user by ID
func (s *service) findUserByID(ctx context.Context, userID uint64) (*ent.User, error) {
	users, err := s.readDB.User.
		Query().
		Where(user.DeletedAtIsNil()).
		All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to query users")
		return nil, fmt.Errorf("failed to query users: %w", err)
	}

	// Find user with matching ID
	for _, userEntity := range users {
		if uint64(userEntity.ID.ID()) == userID {
			return userEntity, nil
		}
	}

	return nil, fmt.Errorf("user not found")
}

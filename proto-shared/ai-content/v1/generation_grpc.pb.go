// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: ai-content/v1/generation.proto

package aicontentv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ContentGenerationServiceClient is the client API for ContentGenerationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ContentGenerationServiceClient interface {
	GenerateContent(ctx context.Context, in *ContentGenerationRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error)
	GetGeneration(ctx context.Context, in *GetGenerationRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error)
	ListGenerations(ctx context.Context, in *ListGenerationsRequest, opts ...grpc.CallOption) (*ListGenerationsResponse, error)
	RegenerateContent(ctx context.Context, in *RegenerateContentRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error)
	// Credit validation operations
	ValidateCredits(ctx context.Context, in *ValidateCreditsRequest, opts ...grpc.CallOption) (*ValidateCreditsResponse, error)
	// RAG-enhanced generation operations
	GetRAGContext(ctx context.Context, in *RAGContextRequest, opts ...grpc.CallOption) (*RAGContextResponse, error)
	GenerateWithRAG(ctx context.Context, in *GenerateWithRAGRequest, opts ...grpc.CallOption) (*GenerateWithRAGResponse, error)
}

type contentGenerationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContentGenerationServiceClient(cc grpc.ClientConnInterface) ContentGenerationServiceClient {
	return &contentGenerationServiceClient{cc}
}

func (c *contentGenerationServiceClient) GenerateContent(ctx context.Context, in *ContentGenerationRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error) {
	out := new(ContentGenerationResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/GenerateContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) GetGeneration(ctx context.Context, in *GetGenerationRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error) {
	out := new(ContentGenerationResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/GetGeneration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) ListGenerations(ctx context.Context, in *ListGenerationsRequest, opts ...grpc.CallOption) (*ListGenerationsResponse, error) {
	out := new(ListGenerationsResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/ListGenerations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) RegenerateContent(ctx context.Context, in *RegenerateContentRequest, opts ...grpc.CallOption) (*ContentGenerationResponse, error) {
	out := new(ContentGenerationResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/RegenerateContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) ValidateCredits(ctx context.Context, in *ValidateCreditsRequest, opts ...grpc.CallOption) (*ValidateCreditsResponse, error) {
	out := new(ValidateCreditsResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/ValidateCredits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) GetRAGContext(ctx context.Context, in *RAGContextRequest, opts ...grpc.CallOption) (*RAGContextResponse, error) {
	out := new(RAGContextResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/GetRAGContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentGenerationServiceClient) GenerateWithRAG(ctx context.Context, in *GenerateWithRAGRequest, opts ...grpc.CallOption) (*GenerateWithRAGResponse, error) {
	out := new(GenerateWithRAGResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.ContentGenerationService/GenerateWithRAG", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContentGenerationServiceServer is the server API for ContentGenerationService service.
// All implementations must embed UnimplementedContentGenerationServiceServer
// for forward compatibility
type ContentGenerationServiceServer interface {
	GenerateContent(context.Context, *ContentGenerationRequest) (*ContentGenerationResponse, error)
	GetGeneration(context.Context, *GetGenerationRequest) (*ContentGenerationResponse, error)
	ListGenerations(context.Context, *ListGenerationsRequest) (*ListGenerationsResponse, error)
	RegenerateContent(context.Context, *RegenerateContentRequest) (*ContentGenerationResponse, error)
	// Credit validation operations
	ValidateCredits(context.Context, *ValidateCreditsRequest) (*ValidateCreditsResponse, error)
	// RAG-enhanced generation operations
	GetRAGContext(context.Context, *RAGContextRequest) (*RAGContextResponse, error)
	GenerateWithRAG(context.Context, *GenerateWithRAGRequest) (*GenerateWithRAGResponse, error)
	mustEmbedUnimplementedContentGenerationServiceServer()
}

// UnimplementedContentGenerationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedContentGenerationServiceServer struct {
}

func (UnimplementedContentGenerationServiceServer) GenerateContent(context.Context, *ContentGenerationRequest) (*ContentGenerationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateContent not implemented")
}
func (UnimplementedContentGenerationServiceServer) GetGeneration(context.Context, *GetGenerationRequest) (*ContentGenerationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGeneration not implemented")
}
func (UnimplementedContentGenerationServiceServer) ListGenerations(context.Context, *ListGenerationsRequest) (*ListGenerationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGenerations not implemented")
}
func (UnimplementedContentGenerationServiceServer) RegenerateContent(context.Context, *RegenerateContentRequest) (*ContentGenerationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegenerateContent not implemented")
}
func (UnimplementedContentGenerationServiceServer) ValidateCredits(context.Context, *ValidateCreditsRequest) (*ValidateCreditsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCredits not implemented")
}
func (UnimplementedContentGenerationServiceServer) GetRAGContext(context.Context, *RAGContextRequest) (*RAGContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRAGContext not implemented")
}
func (UnimplementedContentGenerationServiceServer) GenerateWithRAG(context.Context, *GenerateWithRAGRequest) (*GenerateWithRAGResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateWithRAG not implemented")
}
func (UnimplementedContentGenerationServiceServer) mustEmbedUnimplementedContentGenerationServiceServer() {
}

// UnsafeContentGenerationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContentGenerationServiceServer will
// result in compilation errors.
type UnsafeContentGenerationServiceServer interface {
	mustEmbedUnimplementedContentGenerationServiceServer()
}

func RegisterContentGenerationServiceServer(s grpc.ServiceRegistrar, srv ContentGenerationServiceServer) {
	s.RegisterService(&ContentGenerationService_ServiceDesc, srv)
}

func _ContentGenerationService_GenerateContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContentGenerationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).GenerateContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/GenerateContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).GenerateContent(ctx, req.(*ContentGenerationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_GetGeneration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGenerationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).GetGeneration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/GetGeneration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).GetGeneration(ctx, req.(*GetGenerationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_ListGenerations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGenerationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).ListGenerations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/ListGenerations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).ListGenerations(ctx, req.(*ListGenerationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_RegenerateContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegenerateContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).RegenerateContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/RegenerateContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).RegenerateContent(ctx, req.(*RegenerateContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_ValidateCredits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCreditsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).ValidateCredits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/ValidateCredits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).ValidateCredits(ctx, req.(*ValidateCreditsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_GetRAGContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RAGContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).GetRAGContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/GetRAGContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).GetRAGContext(ctx, req.(*RAGContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentGenerationService_GenerateWithRAG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateWithRAGRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentGenerationServiceServer).GenerateWithRAG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.ContentGenerationService/GenerateWithRAG",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentGenerationServiceServer).GenerateWithRAG(ctx, req.(*GenerateWithRAGRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ContentGenerationService_ServiceDesc is the grpc.ServiceDesc for ContentGenerationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ContentGenerationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai_content.v1.ContentGenerationService",
	HandlerType: (*ContentGenerationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateContent",
			Handler:    _ContentGenerationService_GenerateContent_Handler,
		},
		{
			MethodName: "GetGeneration",
			Handler:    _ContentGenerationService_GetGeneration_Handler,
		},
		{
			MethodName: "ListGenerations",
			Handler:    _ContentGenerationService_ListGenerations_Handler,
		},
		{
			MethodName: "RegenerateContent",
			Handler:    _ContentGenerationService_RegenerateContent_Handler,
		},
		{
			MethodName: "ValidateCredits",
			Handler:    _ContentGenerationService_ValidateCredits_Handler,
		},
		{
			MethodName: "GetRAGContext",
			Handler:    _ContentGenerationService_GetRAGContext_Handler,
		},
		{
			MethodName: "GenerateWithRAG",
			Handler:    _ContentGenerationService_GenerateWithRAG_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai-content/v1/generation.proto",
}

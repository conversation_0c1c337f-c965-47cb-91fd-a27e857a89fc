package service

import (
	"context"

	"github.com/social-content-ai/notification-service/usecase/notification"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/notification-service/usecase/template"
)

// Mock implementations for use cases (to be replaced with actual implementations)

type mockNotificationUseCase struct{}

func (m *mockNotificationUseCase) SendNotification(ctx context.Context, req *notification.SendNotificationRequest) (*notification.SendNotificationResponse, error) {
	return &notification.SendNotificationResponse{
		NotificationID: "mock-notification-id",
		Status:         "sent",
	}, nil
}

func (m *mockNotificationUseCase) SendTemplatedNotification(ctx context.Context, req *notification.SendTemplatedNotificationRequest) (*notification.SendNotificationResponse, error) {
	return &notification.SendNotificationResponse{
		NotificationID: "mock-notification-id",
		Status:         "sent",
	}, nil
}

func (m *mockNotificationUseCase) SendBulkNotification(ctx context.Context, req *notification.BulkNotificationRequest) (*notification.BulkNotificationResponse, error) {
	return &notification.BulkNotificationResponse{
		TotalSent:   len(req.UserIDs),
		TotalFailed: 0,
	}, nil
}

func (m *mockNotificationUseCase) GetNotification(ctx context.Context, id string) (*notification.NotificationResponse, error) {
	return &notification.NotificationResponse{
		ID:     id,
		Status: "sent",
	}, nil
}

func (m *mockNotificationUseCase) ListNotifications(ctx context.Context, req *notification.ListNotificationsRequest) (*notification.ListNotificationsResponse, error) {
	return &notification.ListNotificationsResponse{
		Notifications: []notification.NotificationResponse{},
		Pagination: notification.PaginationMeta{
			Page:  req.Page,
			Limit: req.Limit,
			Total: 0,
		},
	}, nil
}

func (m *mockNotificationUseCase) MarkAsRead(ctx context.Context, req *notification.MarkAsReadRequest) error {
	return nil
}

func (m *mockNotificationUseCase) GetDeliveryStatus(ctx context.Context, req *notification.GetDeliveryStatusRequest) (*notification.DeliveryStatusResponse, error) {
	return &notification.DeliveryStatusResponse{
		NotificationID: req.NotificationID,
		Status:         "delivered",
	}, nil
}

type mockPreferenceUseCase struct{}

func (m *mockPreferenceUseCase) GetPreferences(ctx context.Context, userID string) (*preference.PreferencesResponse, error) {
	return &preference.PreferencesResponse{
		UserID:           userID,
		EmailEnabled:     true,
		SMSEnabled:       false,
		PushEnabled:      true,
		WebSocketEnabled: true,
		InAppEnabled:     true,
		Timezone:         "UTC",
		Language:         "en",
	}, nil
}

func (m *mockPreferenceUseCase) UpdatePreferences(ctx context.Context, req *preference.UpdatePreferencesRequest) (*preference.PreferencesResponse, error) {
	return &preference.PreferencesResponse{
		UserID: req.UserID,
	}, nil
}

func (m *mockPreferenceUseCase) ResetPreferences(ctx context.Context, userID string) (*preference.PreferencesResponse, error) {
	return &preference.PreferencesResponse{
		UserID: userID,
	}, nil
}

func (m *mockPreferenceUseCase) GetDefaultPreferences(ctx context.Context) (*preference.PreferencesResponse, error) {
	return &preference.PreferencesResponse{}, nil
}

type mockTemplateUseCase struct{}

func (m *mockTemplateUseCase) CreateTemplate(ctx context.Context, req *template.CreateTemplateRequest) (*template.TemplateResponse, error) {
	return &template.TemplateResponse{
		ID:        "mock-template-id",
		EventType: req.EventType,
		Name:      req.Name,
	}, nil
}

func (m *mockTemplateUseCase) GetTemplate(ctx context.Context, id string) (*template.TemplateResponse, error) {
	return &template.TemplateResponse{
		ID: id,
	}, nil
}

func (m *mockTemplateUseCase) GetTemplateByEventType(ctx context.Context, eventType string) (*template.TemplateResponse, error) {
	return &template.TemplateResponse{
		EventType: eventType,
	}, nil
}

func (m *mockTemplateUseCase) UpdateTemplate(ctx context.Context, req *template.UpdateTemplateRequest) (*template.TemplateResponse, error) {
	return &template.TemplateResponse{
		ID: req.ID,
	}, nil
}

func (m *mockTemplateUseCase) DeleteTemplate(ctx context.Context, id string) error {
	return nil
}

func (m *mockTemplateUseCase) ListTemplates(ctx context.Context, req *template.ListTemplatesRequest) (*template.ListTemplatesResponse, error) {
	return &template.ListTemplatesResponse{
		Templates: []template.TemplateResponse{},
		Pagination: template.PaginationMeta{
			Page:  req.Page,
			Limit: req.Limit,
			Total: 0,
		},
	}, nil
}

func (m *mockTemplateUseCase) RenderTemplate(ctx context.Context, req *template.RenderTemplateRequest) (*template.RenderTemplateResponse, error) {
	return &template.RenderTemplateResponse{
		Body: "Rendered template content",
	}, nil
}

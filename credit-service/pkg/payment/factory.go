package payment

import (
	"context"
	"fmt"

	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// ProcessorType represents the type of payment processor
type ProcessorType string

const (
	ProcessorTypePayPal ProcessorType = "paypal"
	ProcessorTypeStripe ProcessorType = "stripe"
	ProcessorTypeMomo   ProcessorType = "momo"
	ProcessorTypeMock   ProcessorType = "mock"
)

// Config holds configuration for all payment processors
type Config struct {
	DefaultProcessor ProcessorType `json:"default_processor"`
	PayPal           PayPalConfig  `json:"paypal"`
	Stripe           StripeConfig  `json:"stripe"`
	Momo             MomoConfig    `json:"momo"`
	Mock             MockConfig    `json:"mock"`
}

// StripeConfig holds Stripe configuration
type StripeConfig struct {
	SecretKey      string `json:"secret_key"`
	PublishableKey string `json:"publishable_key"`
	WebhookSecret  string `json:"webhook_secret"`
	Environment    string `json:"environment"` // test or live
}

// MomoConfig holds MoMo configuration
type MomoConfig struct {
	PartnerCode string `json:"partner_code"`
	AccessKey   string `json:"access_key"`
	SecretKey   string `json:"secret_key"`
	Environment string `json:"environment"` // sandbox or production
	ReturnURL   string `json:"return_url"`
	NotifyURL   string `json:"notify_url"`
}

// MockConfig holds Mock processor configuration
type MockConfig struct {
	ShouldFail bool `json:"should_fail"`
}

// Factory creates payment processors
type Factory struct {
	config       Config
	logger       logging.Logger
	paypalUC     PayPalUseCase
	paypalClient paypal.Client
}

// NewFactory creates a new payment processor factory
func NewFactory(config Config, logger logging.Logger) *Factory {
	return &Factory{
		config: config,
		logger: logger,
	}
}

// NewFactoryWithDependencies creates a new payment processor factory with dependencies
func NewFactoryWithDependencies(config Config, logger logging.Logger, paypalUC PayPalUseCase, paypalClient paypal.Client) *Factory {
	return &Factory{
		config:       config,
		logger:       logger,
		paypalUC:     paypalUC,
		paypalClient: paypalClient,
	}
}

// CreateProcessor creates a payment processor of the specified type
func (f *Factory) CreateProcessor(processorType ProcessorType) (Processor, error) {
	switch processorType {
	case ProcessorTypePayPal:
		if f.paypalUC != nil && f.paypalClient != nil {
			return NewPayPalProcessor(f.config.PayPal, f.paypalUC, f.paypalClient, f.logger.WithField("processor", "paypal")), nil
		}
		// Fallback to mock if dependencies not provided
		f.logger.Warn("PayPal dependencies not provided, falling back to mock processor")
		return NewMockProcessor(false), nil
	case ProcessorTypeStripe:
		return f.createStripeProcessor()
	case ProcessorTypeMomo:
		return f.createMomoProcessor()
	case ProcessorTypeMock:
		return NewMockProcessor(f.config.Mock.ShouldFail), nil
	default:
		return nil, fmt.Errorf("unsupported payment processor type: %s", processorType)
	}
}

// CreateDefaultProcessor creates the default payment processor
func (f *Factory) CreateDefaultProcessor() (Processor, error) {
	return f.CreateProcessor(f.config.DefaultProcessor)
}

// GetSupportedProcessors returns a list of supported payment processors
func (f *Factory) GetSupportedProcessors() []ProcessorType {
	return []ProcessorType{
		ProcessorTypePayPal,
		ProcessorTypeStripe,
		ProcessorTypeMomo,
		ProcessorTypeMock,
	}
}

// createStripeProcessor creates a Stripe payment processor
func (f *Factory) createStripeProcessor() (Processor, error) {
	// TODO: Implement Stripe processor
	f.logger.Warn("Stripe processor not implemented yet, falling back to mock")
	return NewMockProcessor(false), nil
}

// createMomoProcessor creates a MoMo payment processor
func (f *Factory) createMomoProcessor() (Processor, error) {
	// TODO: Implement MoMo processor
	f.logger.Warn("MoMo processor not implemented yet, falling back to mock")
	return NewMockProcessor(false), nil
}

// ProcessorManager manages multiple payment processors
type ProcessorManager struct {
	factory          *Factory
	defaultProcessor Processor
	processors       map[ProcessorType]Processor
	logger           logging.Logger
}

// NewProcessorManager creates a new payment processor manager
func NewProcessorManager(config Config, logger logging.Logger) (*ProcessorManager, error) {
	factory := NewFactory(config, logger)

	// Create default processor
	defaultProcessor, err := factory.CreateDefaultProcessor()
	if err != nil {
		return nil, fmt.Errorf("failed to create default processor: %w", err)
	}

	return &ProcessorManager{
		factory:          factory,
		defaultProcessor: defaultProcessor,
		processors:       make(map[ProcessorType]Processor),
		logger:           logger,
	}, nil
}

// GetProcessor gets a payment processor by type
func (pm *ProcessorManager) GetProcessor(processorType ProcessorType) (Processor, error) {
	// Check if processor is already cached
	if processor, exists := pm.processors[processorType]; exists {
		return processor, nil
	}

	// Create new processor
	processor, err := pm.factory.CreateProcessor(processorType)
	if err != nil {
		return nil, err
	}

	// Cache the processor
	pm.processors[processorType] = processor
	return processor, nil
}

// GetDefaultProcessor gets the default payment processor
func (pm *ProcessorManager) GetDefaultProcessor() Processor {
	return pm.defaultProcessor
}

// GetProcessorForPaymentMethod gets the appropriate processor for a payment method
func (pm *ProcessorManager) GetProcessorForPaymentMethod(paymentMethod string) (Processor, error) {
	switch paymentMethod {
	case "paypal":
		return pm.GetProcessor(ProcessorTypePayPal)
	case "stripe", "card", "credit_card":
		return pm.GetProcessor(ProcessorTypeStripe)
	case "momo":
		return pm.GetProcessor(ProcessorTypeMomo)
	default:
		pm.logger.WithField("payment_method", paymentMethod).Warn("Unknown payment method, using default processor")
		return pm.defaultProcessor, nil
	}
}

// ListAvailableProcessors lists all available payment processors
func (pm *ProcessorManager) ListAvailableProcessors() []ProcessorType {
	return pm.factory.GetSupportedProcessors()
}

// ValidateConfig validates the payment configuration
func ValidateConfig(config Config) error {
	// Validate default processor is supported
	supportedProcessors := []ProcessorType{
		ProcessorTypePayPal,
		ProcessorTypeStripe,
		ProcessorTypeMomo,
		ProcessorTypeMock,
	}

	isSupported := false
	for _, processor := range supportedProcessors {
		if config.DefaultProcessor == processor {
			isSupported = true
			break
		}
	}

	if !isSupported {
		return fmt.Errorf("unsupported default processor: %s", config.DefaultProcessor)
	}

	// Validate processor-specific configurations
	switch config.DefaultProcessor {
	case ProcessorTypePayPal:
		if config.PayPal.ClientID == "" {
			return fmt.Errorf("PayPal client ID is required")
		}
		if config.PayPal.ClientSecret == "" {
			return fmt.Errorf("PayPal client secret is required")
		}
	case ProcessorTypeStripe:
		if config.Stripe.SecretKey == "" {
			return fmt.Errorf("Stripe secret key is required")
		}
	case ProcessorTypeMomo:
		if config.Momo.PartnerCode == "" {
			return fmt.Errorf("MoMo partner code is required")
		}
	}

	return nil
}

// DefaultConfig returns a default payment configuration
func DefaultConfig() Config {
	return Config{
		DefaultProcessor: ProcessorTypeMock,
		PayPal: PayPalConfig{
			Environment: "sandbox",
			BaseURL:     "https://api.sandbox.paypal.com",
			ReturnURL:   "http://localhost:8080/payment/success",
			CancelURL:   "http://localhost:8080/payment/cancel",
		},
		Stripe: StripeConfig{
			Environment: "test",
		},
		Momo: MomoConfig{
			Environment: "sandbox",
		},
		Mock: MockConfig{
			ShouldFail: false,
		},
	}
}

// CreatePayment creates a payment through the appropriate processor
// Flow: Subscription -> Factory.CreatePayment -> PaypalProcess.InitiatePayment
func (f *Factory) CreatePayment(ctx context.Context, processorType ProcessorType, req *PaymentRequest) (*PaymentResponse, error) {
	f.logger.WithFields(map[string]interface{}{
		"processor_type": processorType,
		"user_id":        req.UserID,
		"amount":         req.Amount,
		"currency":       req.Currency,
	}).Info("Creating payment through factory")

	// Create processor
	processor, err := f.CreateProcessor(processorType)
	if err != nil {
		return nil, fmt.Errorf("failed to create processor: %w", err)
	}

	// Process payment
	response, err := processor.ProcessPayment(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to process payment: %w", err)
	}

	f.logger.WithFields(map[string]interface{}{
		"payment_id": response.PaymentID,
		"status":     response.Status,
	}).Info("Payment created successfully")

	return response, nil
}

// WebHook processes webhook through the appropriate processor
// Flow: HTTP -> Factory.WebHook -> PaypalProcess.WebHook -> data -> on Factory.WebHook -> push event
func (f *Factory) WebHook(ctx context.Context, processorType ProcessorType, payload []byte, signature string) (*WebhookProcessResult, error) {
	f.logger.WithField("processor_type", processorType).Info("Processing webhook through factory")

	// Create processor
	processor, err := f.CreateProcessor(processorType)
	if err != nil {
		return nil, fmt.Errorf("failed to create processor: %w", err)
	}

	// Validate webhook signature
	valid, err := processor.ValidateWebhook(ctx, payload, signature)
	if err != nil {
		return nil, fmt.Errorf("failed to validate webhook: %w", err)
	}
	if !valid {
		return nil, fmt.Errorf("invalid webhook signature")
	}

	// Process webhook
	webhookEvent, err := processor.ProcessWebhook(ctx, payload)
	if err != nil {
		return nil, fmt.Errorf("failed to process webhook: %w", err)
	}

	// Create webhook result with event data
	result := &WebhookProcessResult{
		Success:     true,
		EventID:     webhookEvent.ID,
		EventType:   webhookEvent.Type,
		EventData:   webhookEvent.Data,
		ProcessedAt: webhookEvent.CreatedAt,
	}

	// Push event for subscription update
	err = f.pushSubscriptionUpdateEvent(ctx, result)
	if err != nil {
		f.logger.WithError(err).Error("Failed to push subscription update event")
		// Don't fail the webhook processing if event push fails
	}

	f.logger.WithFields(map[string]interface{}{
		"event_id":   result.EventID,
		"event_type": result.EventType,
	}).Info("Webhook processed successfully")

	return result, nil
}

// pushSubscriptionUpdateEvent pushes an event to update subscription
// This is called after webhook processing to trigger subscription updates
func (f *Factory) pushSubscriptionUpdateEvent(ctx context.Context, result *WebhookProcessResult) error {
	f.logger.WithFields(map[string]interface{}{
		"event_id":   result.EventID,
		"event_type": result.EventType,
	}).Info("Pushing subscription update event")

	// TODO: Implement event publishing to message queue (Kafka)
	// For now, we'll just log the event

	// Extract relevant data for subscription update
	switch result.EventType {
	case "PAYMENT.CAPTURE.COMPLETED":
		return f.handlePaymentCompletedEvent(ctx, result)
	case "PAYMENT.CAPTURE.DENIED":
		return f.handlePaymentFailedEvent(ctx, result)
	case "BILLING.SUBSCRIPTION.ACTIVATED":
		return f.handleSubscriptionActivatedEvent(ctx, result)
	case "BILLING.SUBSCRIPTION.CANCELLED":
		return f.handleSubscriptionCancelledEvent(ctx, result)
	default:
		f.logger.WithField("event_type", result.EventType).Info("Unhandled event type for subscription update")
	}

	return nil
}

// handlePaymentCompletedEvent handles payment completed events
func (f *Factory) handlePaymentCompletedEvent(ctx context.Context, result *WebhookProcessResult) error {
	f.logger.WithField("event_id", result.EventID).Info("Handling payment completed event")

	// TODO: Extract subscription ID from event data
	// TODO: Call subscription service to activate subscription
	// TODO: Update payment method status

	// Mock implementation
	f.logger.Info("Payment completed - subscription should be activated")
	return nil
}

// handlePaymentFailedEvent handles payment failed events
func (f *Factory) handlePaymentFailedEvent(ctx context.Context, result *WebhookProcessResult) error {
	f.logger.WithField("event_id", result.EventID).Info("Handling payment failed event")

	// TODO: Extract subscription ID from event data
	// TODO: Call subscription service to handle payment failure
	// TODO: Update payment method status

	// Mock implementation
	f.logger.Info("Payment failed - subscription should be suspended")
	return nil
}

// handleSubscriptionActivatedEvent handles subscription activated events
func (f *Factory) handleSubscriptionActivatedEvent(ctx context.Context, result *WebhookProcessResult) error {
	f.logger.WithField("event_id", result.EventID).Info("Handling subscription activated event")

	// TODO: Extract subscription ID from event data
	// TODO: Call subscription service to confirm activation

	// Mock implementation
	f.logger.Info("Subscription activated")
	return nil
}

// handleSubscriptionCancelledEvent handles subscription cancelled events
func (f *Factory) handleSubscriptionCancelledEvent(ctx context.Context, result *WebhookProcessResult) error {
	f.logger.WithField("event_id", result.EventID).Info("Handling subscription cancelled event")

	// TODO: Extract subscription ID from event data
	// TODO: Call subscription service to handle cancellation

	// Mock implementation
	f.logger.Info("Subscription cancelled")
	return nil
}

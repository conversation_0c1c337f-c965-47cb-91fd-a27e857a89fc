package template

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service implements the template UseCase interface
type Service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new template service
func NewService(
	readDB *ent.Client,
	writeDB *ent.Client,
	logger logging.Logger,
) UseCase {
	return &Service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreateTemplate creates a new notification template
func (s *Service) CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*TemplateResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"event_type": req.EventType,
		"name":       req.Name,
	}).Info("Creating notification template")

	// Validate templates
	if len(req.Templates) == 0 {
		return nil, fmt.Errorf("at least one channel template is required")
	}

	// Convert templates to map[string]interface{}
	var templatesMap map[string]interface{}
	templatesBytes, err := json.Marshal(req.Templates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to marshal templates")
		return nil, fmt.Errorf("failed to marshal templates: %w", err)
	}
	err = json.Unmarshal(templatesBytes, &templatesMap)
	if err != nil {
		s.logger.WithError(err).Error("Failed to unmarshal templates")
		return nil, fmt.Errorf("failed to unmarshal templates: %w", err)
	}

	// Extract variables from all templates
	variables := s.extractVariables(req.Templates)

	// Create template in database
	template, err := s.writeDB.NotificationTemplate.
		Create().
		SetEventType(req.EventType).
		SetName(req.Name).
		SetDescription(req.Description).
		SetTemplates(templatesMap).
		SetIsActive(req.IsActive).
		SetVariables(variables).
		SetCreatedBy(req.CreatedBy).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to create template")
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	return s.convertToTemplateResponse(template), nil
}

// GetTemplate gets template by ID
func (s *Service) GetTemplate(ctx context.Context, id string) (*TemplateResponse, error) {
	s.logger.WithField("template_id", id).Debug("Getting template")

	// Parse template ID
	templateID, err := uuid.Parse(id)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return nil, fmt.Errorf("invalid template ID format: %w", err)
	}

	template, err := s.readDB.NotificationTemplate.
		Query().
		Where(
			notificationtemplate.ID(templateID),
			notificationtemplate.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return s.convertToTemplateResponse(template), nil
}

// GetTemplateByEventType gets template by event type
func (s *Service) GetTemplateByEventType(ctx context.Context, eventType string) (*TemplateResponse, error) {
	s.logger.WithField("event_type", eventType).Debug("Getting template by event type")

	template, err := s.readDB.NotificationTemplate.
		Query().
		Where(
			notificationtemplate.EventType(eventType),
			notificationtemplate.IsActive(true),
			notificationtemplate.DeletedAtIsNil(),
		).
		Order(ent.Desc(notificationtemplate.FieldCreatedAt)).
		First(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("template not found for event type: %s", eventType)
		}
		s.logger.WithError(err).Error("Failed to get template by event type")
		return nil, fmt.Errorf("failed to get template by event type: %w", err)
	}

	return s.convertToTemplateResponse(template), nil
}

// UpdateTemplate updates template
func (s *Service) UpdateTemplate(ctx context.Context, req *UpdateTemplateRequest) (*TemplateResponse, error) {
	s.logger.WithField("template_id", req.ID).Info("Updating template")

	// Parse template ID
	templateID, err := uuid.Parse(req.ID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return nil, fmt.Errorf("invalid template ID format: %w", err)
	}

	// Get existing template
	existing, err := s.readDB.NotificationTemplate.
		Query().
		Where(
			notificationtemplate.ID(templateID),
			notificationtemplate.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get existing template")
		return nil, fmt.Errorf("failed to get existing template: %w", err)
	}

	// Build update query
	update := s.writeDB.NotificationTemplate.UpdateOne(existing)

	if req.Name != "" {
		update = update.SetName(req.Name)
	}
	if req.Description != "" {
		update = update.SetDescription(req.Description)
	}
	if req.Templates != nil {
		var templatesMap map[string]interface{}
		templatesBytes, err := json.Marshal(req.Templates)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal templates: %w", err)
		}
		err = json.Unmarshal(templatesBytes, &templatesMap)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal templates: %w", err)
		}
		update = update.SetTemplates(templatesMap)

		// Update variables
		variables := s.extractVariables(req.Templates)
		update = update.SetVariables(variables)
	}
	if req.IsActive != nil {
		update = update.SetIsActive(*req.IsActive)
	}
	if req.Variables != nil {
		update = update.SetVariables(req.Variables)
	}

	// Save updated template
	updatedTemplate, err := update.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update template")
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	return s.convertToTemplateResponse(updatedTemplate), nil
}

// DeleteTemplate deletes template
func (s *Service) DeleteTemplate(ctx context.Context, id string) error {
	s.logger.WithField("template_id", id).Info("Deleting template")

	// Parse template ID
	templateID, err := uuid.Parse(id)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return fmt.Errorf("invalid template ID format: %w", err)
	}

	// Soft delete by setting deleted_at
	_, err = s.writeDB.NotificationTemplate.
		Update().
		Where(
			notificationtemplate.ID(templateID),
			notificationtemplate.DeletedAtIsNil(),
		).
		SetDeletedAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete template")
		return fmt.Errorf("failed to delete template: %w", err)
	}

	return nil
}

// ListTemplates lists templates
func (s *Service) ListTemplates(ctx context.Context, req *ListTemplatesRequest) (*ListTemplatesResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"page":       req.Page,
		"limit":      req.Limit,
		"event_type": req.EventType,
	}).Debug("Listing templates")

	// Build query
	query := s.readDB.NotificationTemplate.
		Query().
		Where(notificationtemplate.DeletedAtIsNil())

	// Add filters
	if req.EventType != "" {
		query = query.Where(notificationtemplate.EventType(req.EventType))
	}
	if req.IsActive != nil {
		query = query.Where(notificationtemplate.IsActive(*req.IsActive))
	}

	// Add sorting
	if req.SortBy != "" {
		switch req.SortBy {
		case "created_at":
			if req.Order == "asc" {
				query = query.Order(ent.Asc(notificationtemplate.FieldCreatedAt))
			} else {
				query = query.Order(ent.Desc(notificationtemplate.FieldCreatedAt))
			}
		case "name":
			if req.Order == "asc" {
				query = query.Order(ent.Asc(notificationtemplate.FieldName))
			} else {
				query = query.Order(ent.Desc(notificationtemplate.FieldName))
			}
		}
	} else {
		// Default sort by created_at desc
		query = query.Order(ent.Desc(notificationtemplate.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count templates")
		return nil, fmt.Errorf("failed to count templates: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	templates, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list templates")
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	// Convert to response format
	var templateResponses []TemplateResponse
	for _, tmpl := range templates {
		templateResponses = append(templateResponses, *s.convertToTemplateResponse(tmpl))
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	return &ListTemplatesResponse{
		Templates: templateResponses,
		Pagination: PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// RenderTemplate renders template with data
func (s *Service) RenderTemplate(ctx context.Context, req *RenderTemplateRequest) (*RenderTemplateResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"template_id": req.TemplateID,
		"channel":     req.Channel,
	}).Debug("Rendering template")

	// Get template
	templateResp, err := s.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		return nil, err
	}

	// Get channel template
	channelTemplate, exists := templateResp.Templates[req.Channel]
	if !exists {
		return nil, fmt.Errorf("template not found for channel: %s", req.Channel)
	}

	// Render subject (if exists)
	var renderedSubject string
	if channelTemplate.Subject != "" {
		renderedSubject, err = s.renderText(channelTemplate.Subject, req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to render subject: %w", err)
		}
	}

	// Render body
	renderedBody, err := s.renderText(channelTemplate.Body, req.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to render body: %w", err)
	}

	// Render HTML body (if exists)
	var renderedHTMLBody string
	if channelTemplate.HTMLBody != "" {
		renderedHTMLBody, err = s.renderText(channelTemplate.HTMLBody, req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to render HTML body: %w", err)
		}
	}

	return &RenderTemplateResponse{
		Subject:  renderedSubject,
		Body:     renderedBody,
		HTMLBody: renderedHTMLBody,
		Metadata: channelTemplate.Metadata,
	}, nil
}

// Helper methods

// convertToTemplateResponse converts ent entity to response
func (s *Service) convertToTemplateResponse(tmpl *ent.NotificationTemplate) *TemplateResponse {
	// Parse templates from JSON
	var templates map[string]ChannelTemplate
	if tmpl.Templates != nil {
		templatesBytes, _ := json.Marshal(tmpl.Templates)
		json.Unmarshal(templatesBytes, &templates)
	}

	return &TemplateResponse{
		ID:          tmpl.ID.String(),
		EventType:   tmpl.EventType,
		Name:        tmpl.Name,
		Description: tmpl.Description,
		Templates:   templates,
		IsActive:    tmpl.IsActive,
		Variables:   tmpl.Variables,
		CreatedAt:   tmpl.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   tmpl.UpdatedAt.Format(time.RFC3339),
	}
}

// extractVariables extracts variables from all channel templates
func (s *Service) extractVariables(templates map[string]ChannelTemplate) []string {
	variableSet := make(map[string]bool)

	for _, channelTemplate := range templates {
		// Extract from subject
		if channelTemplate.Subject != "" {
			vars := s.extractVariablesFromText(channelTemplate.Subject)
			for _, v := range vars {
				variableSet[v] = true
			}
		}

		// Extract from body
		vars := s.extractVariablesFromText(channelTemplate.Body)
		for _, v := range vars {
			variableSet[v] = true
		}

		// Extract from HTML body
		if channelTemplate.HTMLBody != "" {
			vars := s.extractVariablesFromText(channelTemplate.HTMLBody)
			for _, v := range vars {
				variableSet[v] = true
			}
		}
	}

	// Convert set to slice
	var variables []string
	for variable := range variableSet {
		variables = append(variables, variable)
	}

	return variables
}

// extractVariablesFromText extracts variables from template text
func (s *Service) extractVariablesFromText(text string) []string {
	var variables []string

	// Simple regex-like extraction for {{variable}} patterns
	start := 0
	for {
		startIdx := strings.Index(text[start:], "{{")
		if startIdx == -1 {
			break
		}
		startIdx += start

		endIdx := strings.Index(text[startIdx:], "}}")
		if endIdx == -1 {
			break
		}
		endIdx += startIdx

		// Extract variable name (remove spaces and dots)
		variable := strings.TrimSpace(text[startIdx+2 : endIdx])
		if variable != "" && !strings.Contains(variable, " ") {
			variables = append(variables, variable)
		}

		start = endIdx + 2
	}

	return variables
}

// renderText renders template text with data
func (s *Service) renderText(templateText string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("template").Parse(templateText)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	var result strings.Builder
	err = tmpl.Execute(&result, data)
	if err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return result.String(), nil
}

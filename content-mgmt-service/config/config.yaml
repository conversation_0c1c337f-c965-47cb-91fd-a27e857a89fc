# Content Management Service Configuration

# Server configuration
server:
  grpc_port: 50052
  http_port: 8082
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"
  
  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "content_mgmt_db"
  ssl_mode: "disable"
  
  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/content_mgmt_service.db"
  
  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# External services configuration
services:
  user_service:
    host: "localhost"
    port: 50051
    timeout: "30s"
    retries: 3
  
  asset_service:
    host: "localhost"
    port: 50053
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "localhost"
    port: 50054
    timeout: "60s"
    retries: 2
  
  notification_service:
    host: "localhost"
    port: 50055
    timeout: "30s"
    retries: 3

# Storage configuration
storage:
  provider: "local"  # "local" or "s3"
  
  local:
    base_path: "./uploads"
  
  s3:
    bucket: "content-mgmt-bucket"
    region: "us-east-1"
    access_key: ""
    secret_key: ""
    endpoint: ""  # For MinIO or custom S3-compatible storage

# Content processing configuration
processing:
  max_file_size: "100MB"
  allowed_file_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"
    - "text/plain"
    - "text/markdown"
    - "application/json"
  processing_timeout: "5m"
  worker_count: 4
  queue_size: 100

# Feature flags
features:
  enable_templates: true
  enable_scheduling: true
  enable_collaboration: true
  enable_versioning: true
  enable_analytics: true
  enable_ai_generation: true

# Rate limiting configuration
rate_limit:
  enabled: true
  requests_per_minute: 100
  burst: 20

# CORS configuration
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:3001"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
    - "X-User-ID"
  exposed_headers:
    - "X-Total-Count"
    - "X-Page-Count"
  allow_credentials: true
  max_age: 86400

# Monitoring configuration
monitoring:
  enabled: true
  metrics_port: 9092
  health_check_path: "/health"
  metrics_path: "/metrics"

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Tracing configuration
tracing:
  enabled: false
  service_name: "content-mgmt-service"
  jaeger_endpoint: "http://localhost:14268/api/traces"

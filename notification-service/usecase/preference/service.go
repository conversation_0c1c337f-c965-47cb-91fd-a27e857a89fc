package preference

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Service implements the preference UseCase interface
type Service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new preference service
func NewService(
	readDB *ent.Client,
	writeDB *ent.Client,
	logger logging.Logger,
) UseCase {
	return &Service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// GetPreferences gets user notification preferences
func (s *Service) GetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", userID).Debug("Getting user preferences")

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get preferences from database
	pref, err := s.readDB.NotificationPreference.
		Query().
		Where(notificationpreference.UserID(userUUID)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			// Return default preferences if not found
			s.logger.WithField("user_id", userID).Info("User preferences not found, returning defaults")
			return s.getDefaultPreferencesForUser(userID), nil
		}
		s.logger.WithError(err).Error("Failed to get user preferences")
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}

	return s.convertToPreferencesResponse(pref), nil
}

// UpdatePreferences updates user notification preferences
func (s *Service) UpdatePreferences(ctx context.Context, req *UpdatePreferencesRequest) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Updating user preferences")

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if preferences exist
	existing, err := s.readDB.NotificationPreference.
		Query().
		Where(notificationpreference.UserID(userUUID)).
		Only(ctx)

	var updatedPref *ent.NotificationPreference

	if err != nil {
		if ent.IsNotFound(err) {
			// Create new preferences
			updatedPref, err = s.createPreferences(ctx, userUUID, req)
		} else {
			s.logger.WithError(err).Error("Failed to check existing preferences")
			return nil, fmt.Errorf("failed to check existing preferences: %w", err)
		}
	} else {
		// Update existing preferences
		updatedPref, err = s.updateExistingPreferences(ctx, existing, req)
	}

	if err != nil {
		return nil, err
	}

	return s.convertToPreferencesResponse(updatedPref), nil
}

// ResetPreferences resets user preferences to defaults
func (s *Service) ResetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", userID).Info("Resetting user preferences to defaults")

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Delete existing preferences
	_, err = s.writeDB.NotificationPreference.
		Delete().
		Where(notificationpreference.UserID(userUUID)).
		Exec(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete existing preferences")
		return nil, fmt.Errorf("failed to delete existing preferences: %w", err)
	}

	// Create default preferences
	defaultEventPrefs := s.getDefaultEventPreferences()
	defaultQuietHours := s.getDefaultQuietHours()

	eventPrefsJSON, _ := json.Marshal(defaultEventPrefs)

	// Convert quiet hours to map[string]interface{}
	var quietHoursMap map[string]interface{}
	quietHoursBytes, _ := json.Marshal(defaultQuietHours)
	json.Unmarshal(quietHoursBytes, &quietHoursMap)

	pref, err := s.writeDB.NotificationPreference.
		Create().
		SetUserID(userUUID).
		SetEmailEnabled(true).
		SetPushEnabled(true).
		SetInAppEnabled(true).
		SetSmsEnabled(false).
		SetNotificationTypes(map[string]interface{}{}).
		SetQuietHours(quietHoursMap).
		SetTimezone("UTC").
		SetLanguage("en").
		SetEmailSettings(map[string]interface{}{}).
		SetPushSettings(map[string]interface{}{}).
		SetFrequencySettings(map[string]interface{}{}).
		SetMarketingEmails(false).
		SetProductUpdates(true).
		SetSecurityAlerts(true).
		SetMetadata(map[string]interface{}{
			"event_preferences": eventPrefsJSON,
		}).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to create default preferences")
		return nil, fmt.Errorf("failed to create default preferences: %w", err)
	}

	return s.convertToPreferencesResponse(pref), nil
}

// GetDefaultPreferences gets default notification preferences
func (s *Service) GetDefaultPreferences(ctx context.Context) (*PreferencesResponse, error) {
	s.logger.Debug("Getting default preferences")

	return s.getDefaultPreferencesForUser(""), nil
}

// Helper methods

// createPreferences creates new preferences for a user
func (s *Service) createPreferences(ctx context.Context, userUUID uuid.UUID, req *UpdatePreferencesRequest) (*ent.NotificationPreference, error) {
	s.logger.WithField("user_id", userUUID).Debug("Creating new preferences")

	// Set defaults
	emailEnabled := true
	if req.EmailEnabled != nil {
		emailEnabled = *req.EmailEnabled
	}
	smsEnabled := false
	if req.SMSEnabled != nil {
		smsEnabled = *req.SMSEnabled
	}
	pushEnabled := true
	if req.PushEnabled != nil {
		pushEnabled = *req.PushEnabled
	}
	inAppEnabled := true
	if req.WebSocketEnabled != nil {
		inAppEnabled = *req.WebSocketEnabled
	}
	if req.InAppEnabled != nil {
		inAppEnabled = *req.InAppEnabled
	}

	timezone := "UTC"
	if req.Timezone != "" {
		timezone = req.Timezone
	}
	language := "en"
	if req.Language != "" {
		language = req.Language
	}

	// Prepare event preferences
	eventPrefs := req.EventPreferences
	if eventPrefs == nil {
		eventPrefs = s.getDefaultEventPreferences()
	}

	// Prepare quiet hours
	quietHours := req.QuietHours
	if quietHours == nil {
		quietHours = s.getDefaultQuietHours()
	}

	eventPrefsJSON, _ := json.Marshal(eventPrefs)

	// Convert quiet hours to map[string]interface{}
	var quietHoursMap map[string]interface{}
	quietHoursBytes, _ := json.Marshal(quietHours)
	json.Unmarshal(quietHoursBytes, &quietHoursMap)

	pref, err := s.writeDB.NotificationPreference.
		Create().
		SetUserID(userUUID).
		SetEmailEnabled(emailEnabled).
		SetPushEnabled(pushEnabled).
		SetInAppEnabled(inAppEnabled).
		SetSmsEnabled(smsEnabled).
		SetNotificationTypes(map[string]interface{}{}).
		SetQuietHours(quietHoursMap).
		SetTimezone(timezone).
		SetLanguage(language).
		SetEmailSettings(map[string]interface{}{}).
		SetPushSettings(map[string]interface{}{}).
		SetFrequencySettings(map[string]interface{}{}).
		SetMarketingEmails(false).
		SetProductUpdates(true).
		SetSecurityAlerts(true).
		SetMetadata(map[string]interface{}{
			"event_preferences": eventPrefsJSON,
		}).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to create preferences")
		return nil, fmt.Errorf("failed to create preferences: %w", err)
	}

	return pref, nil
}

// updateExistingPreferences updates existing preferences
func (s *Service) updateExistingPreferences(ctx context.Context, existing *ent.NotificationPreference, req *UpdatePreferencesRequest) (*ent.NotificationPreference, error) {
	s.logger.WithField("user_id", existing.UserID).Debug("Updating existing preferences")

	update := s.writeDB.NotificationPreference.UpdateOne(existing)

	// Update fields if provided
	if req.EmailEnabled != nil {
		update = update.SetEmailEnabled(*req.EmailEnabled)
	}
	if req.SMSEnabled != nil {
		update = update.SetSmsEnabled(*req.SMSEnabled)
	}
	if req.PushEnabled != nil {
		update = update.SetPushEnabled(*req.PushEnabled)
	}
	if req.WebSocketEnabled != nil {
		// Note: WebSocket is stored as in_app in database
		update = update.SetInAppEnabled(*req.WebSocketEnabled)
	}
	if req.InAppEnabled != nil {
		update = update.SetInAppEnabled(*req.InAppEnabled)
	}
	if req.Timezone != "" {
		update = update.SetTimezone(req.Timezone)
	}
	if req.Language != "" {
		update = update.SetLanguage(req.Language)
	}

	// Update event preferences if provided
	if req.EventPreferences != nil {
		eventPrefsJSON, _ := json.Marshal(req.EventPreferences)
		metadata := existing.Metadata
		if metadata == nil {
			metadata = make(map[string]interface{})
		}
		metadata["event_preferences"] = eventPrefsJSON
		update = update.SetMetadata(metadata)
	}

	// Update quiet hours if provided
	if req.QuietHours != nil {
		var quietHoursMap map[string]interface{}
		quietHoursBytes, _ := json.Marshal(req.QuietHours)
		json.Unmarshal(quietHoursBytes, &quietHoursMap)
		update = update.SetQuietHours(quietHoursMap)
	}

	pref, err := update.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update preferences")
		return nil, fmt.Errorf("failed to update preferences: %w", err)
	}

	return pref, nil
}

// convertToPreferencesResponse converts ent entity to response
func (s *Service) convertToPreferencesResponse(pref *ent.NotificationPreference) *PreferencesResponse {
	// Parse event preferences from metadata
	eventPrefs := make(map[string]EventPreference)
	if pref.Metadata != nil {
		if eventPrefsData, exists := pref.Metadata["event_preferences"]; exists {
			if eventPrefsBytes, ok := eventPrefsData.([]byte); ok {
				json.Unmarshal(eventPrefsBytes, &eventPrefs)
			} else if eventPrefsStr, ok := eventPrefsData.(string); ok {
				json.Unmarshal([]byte(eventPrefsStr), &eventPrefs)
			}
		}
	}

	// Parse quiet hours
	var quietHours *QuietHours
	if pref.QuietHours != nil {
		quietHoursBytes, _ := json.Marshal(pref.QuietHours)
		quietHours = &QuietHours{}
		json.Unmarshal(quietHoursBytes, quietHours)
	}

	return &PreferencesResponse{
		UserID:           pref.UserID.String(),
		EmailEnabled:     pref.EmailEnabled,
		SMSEnabled:       pref.SmsEnabled,
		PushEnabled:      pref.PushEnabled,
		WebSocketEnabled: pref.InAppEnabled, // WebSocket is stored as in_app
		InAppEnabled:     pref.InAppEnabled,
		EventPreferences: eventPrefs,
		QuietHours:       quietHours,
		Timezone:         pref.Timezone,
		Language:         pref.Language,
		CreatedAt:        pref.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        pref.UpdatedAt.Format(time.RFC3339),
	}
}

// getDefaultPreferencesForUser returns default preferences for a user
func (s *Service) getDefaultPreferencesForUser(userID string) *PreferencesResponse {
	return &PreferencesResponse{
		UserID:           userID,
		EmailEnabled:     true,
		SMSEnabled:       false,
		PushEnabled:      true,
		WebSocketEnabled: true,
		InAppEnabled:     true,
		EventPreferences: s.getDefaultEventPreferences(),
		QuietHours:       s.getDefaultQuietHours(),
		Timezone:         "UTC",
		Language:         "en",
		CreatedAt:        time.Now().Format(time.RFC3339),
		UpdatedAt:        time.Now().Format(time.RFC3339),
	}
}

// getDefaultEventPreferences returns default event preferences
func (s *Service) getDefaultEventPreferences() map[string]EventPreference {
	return map[string]EventPreference{
		"user_registered": {
			Email:     true,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"password_reset": {
			Email:     true,
			SMS:       true,
			Push:      false,
			WebSocket: true,
			InApp:     true,
		},
		"email_verification": {
			Email:     true,
			SMS:       false,
			Push:      false,
			WebSocket: true,
			InApp:     true,
		},
		"content_generated": {
			Email:     false,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"post_published": {
			Email:     false,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"post_scheduled": {
			Email:     false,
			SMS:       false,
			Push:      false,
			WebSocket: true,
			InApp:     true,
		},
		"credit_low": {
			Email:     true,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"credit_purchased": {
			Email:     true,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"subscription_renewed": {
			Email:     true,
			SMS:       false,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"subscription_expired": {
			Email:     true,
			SMS:       true,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"security_alert": {
			Email:     true,
			SMS:       true,
			Push:      true,
			WebSocket: true,
			InApp:     true,
		},
		"marketing": {
			Email:     false,
			SMS:       false,
			Push:      false,
			WebSocket: false,
			InApp:     false,
		},
	}
}

// getDefaultQuietHours returns default quiet hours
func (s *Service) getDefaultQuietHours() *QuietHours {
	return &QuietHours{
		Enabled:   false,
		StartHour: 22,
		EndHour:   8,
	}
}

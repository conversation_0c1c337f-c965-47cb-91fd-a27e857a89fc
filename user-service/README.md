# User Service

User Service là một microservice quản lý người dùng, <PERSON><PERSON><PERSON> thực và phân quyền trong hệ thống Social Content AI. Service hỗ trợ cả PostgreSQL và SQLite với cấu hình linh hoạt.

## 🚀 Tính năng chính

- **Qu<PERSON>n lý người dùng**: <PERSON><PERSON><PERSON> ký, đăng nhập, cập nhật thông tin
- **X<PERSON><PERSON> thực**: JWT tokens, session management
- **Two-Factor Authentication (2FA)**: TOTP và backup codes
- **Avatar management**: Upload và quản lý ảnh đại diện
- **Admin functions**: Quản lý người dùng, khóa/mở khóa tài khoản
- **Database flexibility**: Hỗ trợ cả PostgreSQL và SQLite
- **Auto cleanup**: Tự động xóa tài khoản chưa verify sau 24h

## 🗄️ Database Support

Service hỗ trợ cả PostgreSQL và SQLite:

- **SQLite**: <PERSON><PERSON><PERSON><PERSON><PERSON> nghị cho development, testing
- **PostgreSQL**: <PERSON><PERSON><PERSON><PERSON><PERSON> nghị cho production

## 🚀 Quick Start

### Option 1: SQLite (Recommended for Development)

```bash
# Clone repository
git clone <repository-url>
cd user-service

# Quick setup with SQLite
make sqlite-setup

# Run the service
make run
```

### Option 2: PostgreSQL (Production-ready)

```bash
# Clone repository
git clone <repository-url>
cd user-service

# Start development dependencies (PostgreSQL, Redis, MailHog)
make dev-up

# Setup with PostgreSQL
make dev-setup

# Run the service
make run
```

### Manual Setup

```bash
# Install dependencies
go mod download

# Choose your database
make db-sqlite    # For SQLite (easier setup)
make db-postgres  # For PostgreSQL (production-ready)

# Build and run
make build-local
./bin/user-service
```

## 🛠️ Available Commands

### Development Environment
```bash
make dev-up       # Start development dependencies (PostgreSQL, Redis, MailHog)
make dev-down     # Stop development dependencies
make dev-logs     # Show development logs
make dev-setup    # Quick setup with PostgreSQL
make sqlite-setup # Quick setup with SQLite
```

### Database Management
```bash
make db-sqlite    # Switch to SQLite database
make db-postgres  # Switch to PostgreSQL database
make db-status    # Show current database configuration
make db-backup    # Backup current database
```

### Build & Run
```bash
make build        # Build for production
make build-local  # Build for local development
make run          # Build and run the service
make dev          # Run with hot reload (requires air)
```

### Testing
```bash
make test         # Run tests
make test-coverage # Run tests with coverage report
make bench        # Run benchmarks
```

### Code Quality
```bash
make lint         # Lint code
make fmt          # Format code
make vet          # Vet code
make security     # Run security scan
```

## 🔧 Configuration

### Database Configuration

Service tự động load cấu hình từ:
- `config/config.yaml` (default)
- `config/development.yaml` (development)
- `config/production.yaml` (production)

#### SQLite Configuration
```yaml
database:
  type: "sqlite"
  sqlite_path: "./data/user_service_dev.db"
  max_open_conns: 10
  max_idle_conns: 2
```

#### PostgreSQL Configuration
```yaml
database:
  type: "postgres"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "user_service_dev"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
```

### Environment Variables

```bash
# Database type
export DATABASE_TYPE=sqlite

# PostgreSQL
export DATABASE_HOST=localhost
export DATABASE_PORT=5432
export DATABASE_USER=postgres
export DATABASE_PASSWORD=postgres
export DATABASE_NAME=user_service_dev

# SQLite
export DATABASE_SQLITE_PATH=./data/user_service.db

# Environment
export ENV=development
export GIN_MODE=debug
```

## 🐳 Docker Development

### Start Development Environment
```bash
make dev-up
```

Điều này sẽ khởi động:
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379  
- **MailHog**: http://localhost:8025 (Email testing)
- **pgAdmin**: http://localhost:5050 (Database management)

### Stop Development Environment
```bash
make dev-down
```

## 📊 Health Check

```bash
curl http://localhost:8080/health
```

Response:
```json
{
  "service": "user-service",
  "status": "healthy"
}
```

## 🔗 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Đăng ký người dùng
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/logout` - Đăng xuất
- `POST /api/v1/auth/refresh` - Refresh token
- `POST /api/v1/auth/forgot-password` - Quên mật khẩu
- `POST /api/v1/auth/reset-password` - Reset mật khẩu
- `POST /api/v1/auth/verify-email` - Verify email

### Two-Factor Authentication
- `POST /api/v1/auth/2fa/setup` - Setup 2FA
- `POST /api/v1/auth/2fa/enable` - Enable 2FA
- `POST /api/v1/auth/2fa/disable` - Disable 2FA
- `POST /api/v1/auth/2fa/verify` - Verify 2FA token
- `POST /api/v1/auth/2fa/backup-codes` - Generate backup codes
- `GET /api/v1/auth/2fa/status` - Get 2FA status

### User Management
- `GET /api/v1/users/me` - Get current user
- `PUT /api/v1/users/me` - Update current user
- `DELETE /api/v1/users/me` - Delete current user
- `POST /api/v1/users/me/change-password` - Change password
- `POST /api/v1/users/me/avatar/upload-url` - Request avatar upload URL
- `POST /api/v1/users/me/avatar/confirm` - Confirm avatar upload

### Admin Functions
- `GET /api/v1/users` - List users (admin)
- `GET /api/v1/users/:id` - Get user by ID (admin)
- `PUT /api/v1/users/:id` - Update user (admin)
- `DELETE /api/v1/users/:id` - Delete user (admin)
- `POST /api/v1/users/:id/lock` - Lock user (admin)
- `POST /api/v1/users/:id/unlock` - Unlock user (admin)

### Session Management
- `GET /api/v1/sessions` - List user sessions
- `DELETE /api/v1/sessions/:id` - Revoke session
- `DELETE /api/v1/sessions` - Revoke all sessions

## 🔧 Development Tools

### Install Development Tools
```bash
make install-tools
```

### Check Tools Installation
```bash
make check-tools
```

### Generate Code
```bash
make proto        # Generate protobuf code
make ent          # Generate Ent ORM code
make mocks        # Generate mocks
```

## 📚 Documentation

- [Database Configuration Guide](docs/DATABASE_CONFIGURATION.md)
- [API Documentation](http://localhost:8080/docs) (when service is running)

## 🧪 Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run benchmarks
make bench
```

## 🔒 Security Features

- Password hashing với bcrypt
- JWT token authentication
- Two-Factor Authentication (TOTP)
- Rate limiting
- Input validation
- SQL injection protection
- CORS configuration

## 📈 Monitoring

Service cung cấp:
- Health check endpoint
- Metrics endpoint (Prometheus compatible)
- Structured logging
- Database connection monitoring

## 🚀 Production Deployment

1. **Build production image**
   ```bash
   make docker
   ```

2. **Set environment variables**
   ```bash
   export ENV=production
   export DATABASE_TYPE=postgres
   export DATABASE_HOST=your-postgres-host
   # ... other production configs
   ```

3. **Run with docker-compose**
   ```bash
   docker-compose up -d
   ```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Run tests: `make test`
5. Run linting: `make lint`
6. Submit pull request

## 📄 License

[License information]

package credit

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the credit management business logic interface
type UseCase interface {
	// Basic credit operations
	GetCreditBalance(ctx context.Context, userID string) (*models.CreditBalanceResponse, error)
	GetCreditHistory(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error)
	ValidateCredits(ctx context.Context, req *models.ValidateCreditsRequest) (*models.CreditValidationResult, error)
	ReserveCredits(ctx context.Context, req *models.ReserveCreditsRequest) (*models.CreditReservation, error)
	ConsumeCredits(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditBalanceResponse, error)
	RefundCredits(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditBalanceResponse, error)

	// Credit management
	TransferCredits(ctx context.Context, req *models.TransferCreditsRequest) (*models.CreditBalanceResponse, error)
	AdjustCredits(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditBalanceResponse, error)
	AddCredits(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditBalanceResponse, error)
	DeductCredits(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditBalanceResponse, error)

	// Monthly usage and limits
	GetMonthlyUsage(ctx context.Context, userID string) (*models.MonthlyUsageResponse, error)
	ResetMonthlyUsage(ctx context.Context, req *models.ResetMonthlyUsageRequest) (*models.MonthlyUsageResponse, error)

	// Statistics and analytics
	GetCreditStats(ctx context.Context, req *models.CreditStatsRequest) (*models.CreditStats, error)
	GetUsageReport(ctx context.Context, req *models.UsageReportRequest) (*models.UsageReport, error)
	GetCreditAnalytics(ctx context.Context, userID, period string) (*models.CreditAnalyticsResponse, error)
	GetUsageAnalytics(ctx context.Context, userID, period string) (*models.UsageReport, error)

	// Configuration
	GetCreditConfig(ctx context.Context, userID string) (*models.ModelCreditConfig, error)
	GetModelCreditConfig(ctx context.Context) (*models.ModelCreditConfig, error)

	// Admin operations
	AdminListUserCredits(ctx context.Context, page, limit int) (*models.CreditHistoryResponse, error)
	AdminGetUserCredit(ctx context.Context, userID string) (*models.CreditBalanceResponse, error)
	AdminAddCredits(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditBalanceResponse, error)
	AdminDeductCredits(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditBalanceResponse, error)
	AdminAdjustCredits(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditBalanceResponse, error)
	GetAdminAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error)
	GetAdminCreditAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error)
	GetAdminUserAnalytics(ctx context.Context, period string) (*models.CreditAnalyticsResponse, error)
	AdminGetCreditConfig(ctx context.Context) (*models.ModelCreditConfig, error)
	AdminUpdateCreditConfig(ctx context.Context, config *models.ModelCreditConfig) (*models.ModelCreditConfig, error)

	// AI Credit Validation methods
	ValidateCreditsForAI(ctx context.Context, req *models.ValidateAICreditsRequest) (*models.ValidateAICreditsResponse, error)
	ReserveCreditsForAI(ctx context.Context, req *models.ReserveAICreditsRequest) (*models.ReserveAICreditsResponse, error)
	ConfirmAIOperation(ctx context.Context, req *models.ConfirmAIOperationRequest) (*models.ConfirmAIOperationResponse, error)
	GetAIOperationQuota(ctx context.Context, req *models.GetAIOperationQuotaRequest) (*models.GetAIOperationQuotaResponse, error)
	UpdateAIOperationQuota(ctx context.Context, req *models.UpdateAIOperationQuotaRequest) error

	// Internal operations (for service-to-service communication)
	InternalValidateCredits(ctx context.Context, req *models.ValidateCreditsRequest) (*models.CreditValidationResult, error)
	InternalReserveCredits(ctx context.Context, req *models.ReserveCreditsRequest) (*models.CreditReservation, error)
	InternalConsumeCredits(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditBalanceResponse, error)
	InternalRefundCredits(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditBalanceResponse, error)
	InternalGetCreditBalance(ctx context.Context, userID string) (*models.CreditBalanceResponse, error)
}

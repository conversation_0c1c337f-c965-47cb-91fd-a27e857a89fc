// Code generated by ent, DO NOT EDIT.

package user

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the user type in the database.
	Label = "user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldFullName holds the string denoting the full_name field in the database.
	FieldFullName = "full_name"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldAvatarURL holds the string denoting the avatar_url field in the database.
	FieldAvatarURL = "avatar_url"
	// FieldBio holds the string denoting the bio field in the database.
	FieldBio = "bio"
	// FieldCompany holds the string denoting the company field in the database.
	FieldCompany = "company"
	// FieldIndustry holds the string denoting the industry field in the database.
	FieldIndustry = "industry"
	// FieldRole holds the string denoting the role field in the database.
	FieldRole = "role"
	// FieldPasswordHash holds the string denoting the password_hash field in the database.
	FieldPasswordHash = "password_hash"
	// FieldIsVerified holds the string denoting the is_verified field in the database.
	FieldIsVerified = "is_verified"
	// FieldTwoFactorEnabled holds the string denoting the two_factor_enabled field in the database.
	FieldTwoFactorEnabled = "two_factor_enabled"
	// FieldTwoFactorSecret holds the string denoting the two_factor_secret field in the database.
	FieldTwoFactorSecret = "two_factor_secret"
	// FieldTwoFactorBackupCodes holds the string denoting the two_factor_backup_codes field in the database.
	FieldTwoFactorBackupCodes = "two_factor_backup_codes"
	// FieldTwoFactorEnabledAt holds the string denoting the two_factor_enabled_at field in the database.
	FieldTwoFactorEnabledAt = "two_factor_enabled_at"
	// FieldNotificationSettings holds the string denoting the notification_settings field in the database.
	FieldNotificationSettings = "notification_settings"
	// FieldInviteCode holds the string denoting the invite_code field in the database.
	FieldInviteCode = "invite_code"
	// FieldAffCode holds the string denoting the aff_code field in the database.
	FieldAffCode = "aff_code"
	// FieldAffJoinedAt holds the string denoting the aff_joined_at field in the database.
	FieldAffJoinedAt = "aff_joined_at"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// EdgeSessions holds the string denoting the sessions edge name in mutations.
	EdgeSessions = "sessions"
	// Table holds the table name of the user in the database.
	Table = "users"
	// SessionsTable is the table that holds the sessions relation/edge.
	SessionsTable = "sessions"
	// SessionsInverseTable is the table name for the Session entity.
	// It exists in this package in order to avoid circular dependency with the "session" package.
	SessionsInverseTable = "sessions"
	// SessionsColumn is the table column denoting the sessions relation/edge.
	SessionsColumn = "user_id"
)

// Columns holds all SQL columns for user fields.
var Columns = []string{
	FieldID,
	FieldFullName,
	FieldEmail,
	FieldPhone,
	FieldAvatarURL,
	FieldBio,
	FieldCompany,
	FieldIndustry,
	FieldRole,
	FieldPasswordHash,
	FieldIsVerified,
	FieldTwoFactorEnabled,
	FieldTwoFactorSecret,
	FieldTwoFactorBackupCodes,
	FieldTwoFactorEnabledAt,
	FieldNotificationSettings,
	FieldInviteCode,
	FieldAffCode,
	FieldAffJoinedAt,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// FullNameValidator is a validator for the "full_name" field. It is called by the builders before save.
	FullNameValidator func(string) error
	// EmailValidator is a validator for the "email" field. It is called by the builders before save.
	EmailValidator func(string) error
	// PhoneValidator is a validator for the "phone" field. It is called by the builders before save.
	PhoneValidator func(string) error
	// CompanyValidator is a validator for the "company" field. It is called by the builders before save.
	CompanyValidator func(string) error
	// IndustryValidator is a validator for the "industry" field. It is called by the builders before save.
	IndustryValidator func(string) error
	// DefaultIsVerified holds the default value on creation for the "is_verified" field.
	DefaultIsVerified bool
	// DefaultTwoFactorEnabled holds the default value on creation for the "two_factor_enabled" field.
	DefaultTwoFactorEnabled bool
	// InviteCodeValidator is a validator for the "invite_code" field. It is called by the builders before save.
	InviteCodeValidator func(string) error
	// AffCodeValidator is a validator for the "aff_code" field. It is called by the builders before save.
	AffCodeValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Role defines the type for the "role" enum field.
type Role string

// RoleUser is the default value of the Role enum.
const DefaultRole = RoleUser

// Role values.
const (
	RoleUser  Role = "user"
	RoleAdmin Role = "admin"
)

func (r Role) String() string {
	return string(r)
}

// RoleValidator is a validator for the "role" field enum values. It is called by the builders before save.
func RoleValidator(r Role) error {
	switch r {
	case RoleUser, RoleAdmin:
		return nil
	default:
		return fmt.Errorf("user: invalid enum value for role field: %q", r)
	}
}

// OrderOption defines the ordering options for the User queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByFullName orders the results by the full_name field.
func ByFullName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFullName, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// ByAvatarURL orders the results by the avatar_url field.
func ByAvatarURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAvatarURL, opts...).ToFunc()
}

// ByBio orders the results by the bio field.
func ByBio(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBio, opts...).ToFunc()
}

// ByCompany orders the results by the company field.
func ByCompany(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCompany, opts...).ToFunc()
}

// ByIndustry orders the results by the industry field.
func ByIndustry(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIndustry, opts...).ToFunc()
}

// ByRole orders the results by the role field.
func ByRole(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRole, opts...).ToFunc()
}

// ByPasswordHash orders the results by the password_hash field.
func ByPasswordHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPasswordHash, opts...).ToFunc()
}

// ByIsVerified orders the results by the is_verified field.
func ByIsVerified(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsVerified, opts...).ToFunc()
}

// ByTwoFactorEnabled orders the results by the two_factor_enabled field.
func ByTwoFactorEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTwoFactorEnabled, opts...).ToFunc()
}

// ByTwoFactorSecret orders the results by the two_factor_secret field.
func ByTwoFactorSecret(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTwoFactorSecret, opts...).ToFunc()
}

// ByTwoFactorEnabledAt orders the results by the two_factor_enabled_at field.
func ByTwoFactorEnabledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTwoFactorEnabledAt, opts...).ToFunc()
}

// ByInviteCode orders the results by the invite_code field.
func ByInviteCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInviteCode, opts...).ToFunc()
}

// ByAffCode orders the results by the aff_code field.
func ByAffCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAffCode, opts...).ToFunc()
}

// ByAffJoinedAt orders the results by the aff_joined_at field.
func ByAffJoinedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAffJoinedAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// BySessionsCount orders the results by sessions count.
func BySessionsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSessionsStep(), opts...)
	}
}

// BySessions orders the results by sessions terms.
func BySessions(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSessionsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newSessionsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SessionsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SessionsTable, SessionsColumn),
	)
}

package models

import "time"

// FolderResponse represents a folder response
type FolderResponse struct {
	BaseModel
	UserID      string           `json:"user_id"`
	ParentID    string           `json:"parent_id,omitempty"`
	Name        string           `json:"name"`
	Description string           `json:"description,omitempty"`
	Path        string           `json:"path"`
	Level       int              `json:"level"`
	IsShared    bool             `json:"is_shared"`
	SharedWith  []string         `json:"shared_with,omitempty"`
	Permissions []string         `json:"permissions,omitempty"`
	AssetCount  int64            `json:"asset_count"`
	TotalSize   int64            `json:"total_size"`
	Children    []FolderResponse `json:"children,omitempty"`
	Assets      []AssetResponse  `json:"assets,omitempty"`
}

// CreateFolderRequest represents create folder request
type CreateFolderRequest struct {
	UserID      string `json:"user_id"`
	ParentID    string `json:"parent_id,omitempty"`
	Name        string `json:"name" validate:"required,min=1,max=100"`
	Description string `json:"description,omitempty"`
}

// UpdateFolderRequest represents update folder request
type UpdateFolderRequest struct {
	FolderID    string `json:"folder_id"`
	UserID      string `json:"user_id"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	ParentID    string `json:"parent_id,omitempty"`
}

// ListFoldersRequest represents list folders request
type ListFoldersRequest struct {
	UserID    string `json:"user_id"`
	ParentID  string `json:"parent_id,omitempty"`
	Search    string `json:"search,omitempty"`
	Page      int    `json:"page" validate:"min=1"`
	Limit     int    `json:"limit" validate:"min=1,max=100"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
	Recursive bool   `json:"recursive"`
}

// ListFoldersResponse represents list folders response
type ListFoldersResponse struct {
	Folders    []FolderResponse `json:"folders"`
	Pagination PaginationMeta   `json:"pagination"`
}

// MoveFolderRequest represents move folder request
type MoveFolderRequest struct {
	FolderID       string `json:"folder_id"`
	UserID         string `json:"user_id"`
	NewParentID    string `json:"new_parent_id,omitempty"`
	NewName        string `json:"new_name,omitempty"`
}

// CopyFolderRequest represents copy folder request
type CopyFolderRequest struct {
	FolderID       string `json:"folder_id"`
	UserID         string `json:"user_id"`
	NewParentID    string `json:"new_parent_id,omitempty"`
	NewName        string `json:"new_name,omitempty"`
	CopyAssets     bool   `json:"copy_assets"`
}

// ShareFolderRequest represents share folder request
type ShareFolderRequest struct {
	FolderID    string   `json:"folder_id"`
	UserID      string   `json:"user_id"`
	ShareWith   []string `json:"share_with" validate:"required,min=1"`
	Permissions []string `json:"permissions" validate:"required,min=1"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
	Message     string   `json:"message,omitempty"`
}

// FolderPermission represents folder permission
type FolderPermission struct {
	UserID      string     `json:"user_id"`
	Email       string     `json:"email"`
	Permissions []string   `json:"permissions"`
	GrantedAt   time.Time  `json:"granted_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// FolderTree represents folder tree structure
type FolderTree struct {
	Folder   FolderResponse `json:"folder"`
	Children []FolderTree   `json:"children,omitempty"`
}

// FolderBreadcrumb represents folder breadcrumb
type FolderBreadcrumb struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Path string `json:"path"`
}

package processor

import (
	"fmt"
	"strings"
)

// DocumentProcessor handles document processing operations
type DocumentProcessor struct {
	maxFileSize int64
}

// NewDocumentProcessor creates a new document processor
func NewDocumentProcessor() *DocumentProcessor {
	return &DocumentProcessor{
		maxFileSize: 100 * 1024 * 1024, // 100MB
	}
}

// ProcessedDocument represents a processed document
type ProcessedDocument struct {
	Content     string            `json:"content"`
	Metadata    map[string]string `json:"metadata"`
	PageCount   int               `json:"page_count"`
	WordCount   int               `json:"word_count"`
	ContentType string            `json:"content_type"`
	Size        int64             `json:"size"`
}

// DocumentInfo represents document metadata
type DocumentInfo struct {
	Title       string            `json:"title"`
	Author      string            `json:"author"`
	Subject     string            `json:"subject"`
	Creator     string            `json:"creator"`
	Producer    string            `json:"producer"`
	CreatedAt   string            `json:"created_at"`
	ModifiedAt  string            `json:"modified_at"`
	PageCount   int               `json:"page_count"`
	WordCount   int               `json:"word_count"`
	ContentType string            `json:"content_type"`
	Size        int64             `json:"size"`
	Metadata    map[string]string `json:"metadata"`
}

// ProcessDocument processes a document and extracts text content
func (p *DocumentProcessor) ProcessDocument(data []byte, filename, contentType string) (*ProcessedDocument, error) {
	if int64(len(data)) > p.maxFileSize {
		return nil, fmt.Errorf("file size %d exceeds maximum allowed size %d", len(data), p.maxFileSize)
	}

	// For now, only support text files
	if contentType == "text/plain" || strings.HasSuffix(filename, ".txt") {
		text := string(data)
		return &ProcessedDocument{
			Content:     text,
			Metadata:    make(map[string]string),
			PageCount:   1,
			WordCount:   len(strings.Fields(text)),
			ContentType: contentType,
			Size:        int64(len(data)),
		}, nil
	}

	// For other document types, return placeholder
	return &ProcessedDocument{
		Content:     "Document processing not implemented for type: " + contentType,
		Metadata:    make(map[string]string),
		PageCount:   1,
		WordCount:   0,
		ContentType: contentType,
		Size:        int64(len(data)),
	}, nil
}

// ExtractText extracts text from document
func (p *DocumentProcessor) ExtractText(data []byte, contentType string) (string, error) {
	if contentType == "text/plain" {
		return string(data), nil
	}
	
	// TODO: Implement text extraction for other document types
	return "", fmt.Errorf("text extraction not implemented for type: %s", contentType)
}

// ExtractMetadata extracts metadata from document
func (p *DocumentProcessor) ExtractMetadata(data []byte, contentType string) (map[string]string, error) {
	metadata := make(map[string]string)
	metadata["content_type"] = contentType
	metadata["size"] = fmt.Sprintf("%d", len(data))
	
	// TODO: Implement metadata extraction for different document types
	return metadata, nil
}

// GetDocumentInfo gets document information
func (p *DocumentProcessor) GetDocumentInfo(data []byte, filename, contentType string) (*DocumentInfo, error) {
	processed, err := p.ProcessDocument(data, filename, contentType)
	if err != nil {
		return nil, err
	}

	return &DocumentInfo{
		Title:       filename,
		Author:      "",
		Subject:     "",
		Creator:     "",
		Producer:    "",
		CreatedAt:   "",
		ModifiedAt:  "",
		PageCount:   processed.PageCount,
		WordCount:   processed.WordCount,
		ContentType: processed.ContentType,
		Size:        processed.Size,
		Metadata:    processed.Metadata,
	}, nil
}

// ValidateDocument validates document format and content
func (p *DocumentProcessor) ValidateDocument(data []byte, contentType string) error {
	if int64(len(data)) > p.maxFileSize {
		return fmt.Errorf("file size %d exceeds maximum allowed size %d", len(data), p.maxFileSize)
	}

	if len(data) == 0 {
		return fmt.Errorf("document is empty")
	}

	// TODO: Add more validation logic for different document types
	return nil
}

// SupportedFormats returns list of supported document formats
func (p *DocumentProcessor) SupportedFormats() []string {
	return []string{
		"text/plain",
		// TODO: Add more supported formats
		// "application/pdf",
		// "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		// "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		// "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	}
}

// IsSupported checks if document format is supported
func (p *DocumentProcessor) IsSupported(contentType string) bool {
	supported := p.SupportedFormats()
	for _, format := range supported {
		if format == contentType {
			return true
		}
	}
	return false
}

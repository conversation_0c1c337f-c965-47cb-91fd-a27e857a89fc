// Code generated by ent, DO NOT EDIT.

package asset

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the asset type in the database.
	Label = "asset"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldWorkspaceID holds the string denoting the workspace_id field in the database.
	FieldWorkspaceID = "workspace_id"
	// FieldFolderID holds the string denoting the folder_id field in the database.
	FieldFolderID = "folder_id"
	// FieldFileName holds the string denoting the file_name field in the database.
	FieldFileName = "file_name"
	// FieldOriginalName holds the string denoting the original_name field in the database.
	FieldOriginalName = "original_name"
	// FieldFileType holds the string denoting the file_type field in the database.
	FieldFileType = "file_type"
	// FieldFileExtension holds the string denoting the file_extension field in the database.
	FieldFileExtension = "file_extension"
	// FieldS3Key holds the string denoting the s3_key field in the database.
	FieldS3Key = "s3_key"
	// FieldS3Bucket holds the string denoting the s3_bucket field in the database.
	FieldS3Bucket = "s3_bucket"
	// FieldContentHash holds the string denoting the content_hash field in the database.
	FieldContentHash = "content_hash"
	// FieldPurpose holds the string denoting the purpose field in the database.
	FieldPurpose = "purpose"
	// FieldFileSize holds the string denoting the file_size field in the database.
	FieldFileSize = "file_size"
	// FieldThumbnailURL holds the string denoting the thumbnail_url field in the database.
	FieldThumbnailURL = "thumbnail_url"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldIsPublic holds the string denoting the is_public field in the database.
	FieldIsPublic = "is_public"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// EdgeFolder holds the string denoting the folder edge name in mutations.
	EdgeFolder = "folder"
	// Table holds the table name of the asset in the database.
	Table = "assets"
	// FolderTable is the table that holds the folder relation/edge.
	FolderTable = "assets"
	// FolderInverseTable is the table name for the Folder entity.
	// It exists in this package in order to avoid circular dependency with the "folder" package.
	FolderInverseTable = "folders"
	// FolderColumn is the table column denoting the folder relation/edge.
	FolderColumn = "folder_id"
)

// Columns holds all SQL columns for asset fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldWorkspaceID,
	FieldFolderID,
	FieldFileName,
	FieldOriginalName,
	FieldFileType,
	FieldFileExtension,
	FieldS3Key,
	FieldS3Bucket,
	FieldContentHash,
	FieldPurpose,
	FieldFileSize,
	FieldThumbnailURL,
	FieldMetadata,
	FieldStatus,
	FieldIsPublic,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	WorkspaceIDValidator func(string) error
	// FileNameValidator is a validator for the "file_name" field. It is called by the builders before save.
	FileNameValidator func(string) error
	// OriginalNameValidator is a validator for the "original_name" field. It is called by the builders before save.
	OriginalNameValidator func(string) error
	// FileTypeValidator is a validator for the "file_type" field. It is called by the builders before save.
	FileTypeValidator func(string) error
	// FileExtensionValidator is a validator for the "file_extension" field. It is called by the builders before save.
	FileExtensionValidator func(string) error
	// S3KeyValidator is a validator for the "s3_key" field. It is called by the builders before save.
	S3KeyValidator func(string) error
	// S3BucketValidator is a validator for the "s3_bucket" field. It is called by the builders before save.
	S3BucketValidator func(string) error
	// ContentHashValidator is a validator for the "content_hash" field. It is called by the builders before save.
	ContentHashValidator func(string) error
	// DefaultPurpose holds the default value on creation for the "purpose" field.
	DefaultPurpose string
	// PurposeValidator is a validator for the "purpose" field. It is called by the builders before save.
	PurposeValidator func(string) error
	// DefaultFileSize holds the default value on creation for the "file_size" field.
	DefaultFileSize int64
	// ThumbnailURLValidator is a validator for the "thumbnail_url" field. It is called by the builders before save.
	ThumbnailURLValidator func(string) error
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// StatusValidator is a validator for the "status" field. It is called by the builders before save.
	StatusValidator func(string) error
	// DefaultIsPublic holds the default value on creation for the "is_public" field.
	DefaultIsPublic bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the Asset queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByWorkspaceID orders the results by the workspace_id field.
func ByWorkspaceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkspaceID, opts...).ToFunc()
}

// ByFolderID orders the results by the folder_id field.
func ByFolderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFolderID, opts...).ToFunc()
}

// ByFileName orders the results by the file_name field.
func ByFileName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileName, opts...).ToFunc()
}

// ByOriginalName orders the results by the original_name field.
func ByOriginalName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOriginalName, opts...).ToFunc()
}

// ByFileType orders the results by the file_type field.
func ByFileType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileType, opts...).ToFunc()
}

// ByFileExtension orders the results by the file_extension field.
func ByFileExtension(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileExtension, opts...).ToFunc()
}

// ByS3Key orders the results by the s3_key field.
func ByS3Key(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldS3Key, opts...).ToFunc()
}

// ByS3Bucket orders the results by the s3_bucket field.
func ByS3Bucket(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldS3Bucket, opts...).ToFunc()
}

// ByContentHash orders the results by the content_hash field.
func ByContentHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContentHash, opts...).ToFunc()
}

// ByPurpose orders the results by the purpose field.
func ByPurpose(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPurpose, opts...).ToFunc()
}

// ByFileSize orders the results by the file_size field.
func ByFileSize(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileSize, opts...).ToFunc()
}

// ByThumbnailURL orders the results by the thumbnail_url field.
func ByThumbnailURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldThumbnailURL, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByIsPublic orders the results by the is_public field.
func ByIsPublic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPublic, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByFolderField orders the results by folder field.
func ByFolderField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFolderStep(), sql.OrderByField(field, opts...))
	}
}
func newFolderStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FolderInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, FolderTable, FolderColumn),
	)
}

package session

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/ent/session"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new session service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreateSession creates a new user session
func (s *service) CreateSession(ctx context.Context, req *CreateSessionRequest) (*ent.Session, error) {
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Calculate expiration time
	expiresAt := time.Now().Add(time.Duration(req.DurationSeconds) * time.Second)

	// Create session
	sessionBuilder := s.writeDB.Session.Create().
		SetUserID(userID).
		SetStatus(session.StatusActive).
		SetLoginTime(time.Now()).
		SetExpiresAt(expiresAt)

	if req.DeviceInfo != "" {
		sessionBuilder.SetDeviceInfo(req.DeviceInfo)
	}
	if req.IPAddress != "" {
		sessionBuilder.SetIPAddress(req.IPAddress)
	}

	newSession, err := sessionBuilder.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to create session")
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"session_id": newSession.ID,
		"user_id":    req.UserID,
		"expires_at": expiresAt,
	}).Info("Session created successfully")

	return newSession, nil
}

// GetSession retrieves a session by ID
func (s *service) GetSession(ctx context.Context, id string) (*ent.Session, error) {
	sessionID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid session ID: %w", err)
	}

	sessionEntity, err := s.readDB.Session.Get(ctx, sessionID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("session not found")
		}
		s.logger.WithError(err).WithField("session_id", id).Error("Failed to get session")
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return sessionEntity, nil
}

// ListSessions lists user sessions with pagination and filters
func (s *service) ListSessions(ctx context.Context, req *ListSessionsRequest) ([]*ent.Session, int, error) {
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.readDB.Session.Query().Where(session.UserID(userID))

	// Apply filters
	if req.Status != "" {
		query = query.Where(session.StatusEQ(session.Status(req.Status)))
	}

	// Count total
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to count sessions")
		return nil, 0, fmt.Errorf("failed to count sessions: %w", err)
	}

	// Apply sorting
	switch req.SortBy {
	case "login_time":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(session.FieldLoginTime))
		} else {
			query = query.Order(ent.Asc(session.FieldLoginTime))
		}
	case "expires_at":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(session.FieldExpiresAt))
		} else {
			query = query.Order(ent.Asc(session.FieldExpiresAt))
		}
	case "created_at":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(session.FieldCreatedAt))
		} else {
			query = query.Order(ent.Asc(session.FieldCreatedAt))
		}
	default:
		query = query.Order(ent.Desc(session.FieldCreatedAt))
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	sessions, err := query.Offset(offset).Limit(req.Limit).All(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserID).Error("Failed to list sessions")
		return nil, 0, fmt.Errorf("failed to list sessions: %w", err)
	}

	return sessions, total, nil
}

// RevokeSession revokes a session
func (s *service) RevokeSession(ctx context.Context, id string, reason string) error {
	sessionID, err := uuid.Parse(id)
	if err != nil {
		return fmt.Errorf("invalid session ID: %w", err)
	}

	err = s.writeDB.Session.UpdateOneID(sessionID).
		SetStatus(session.StatusRevoked).
		SetLogoutTime(time.Now()).
		Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("session not found")
		}
		s.logger.WithError(err).WithField("session_id", id).Error("Failed to revoke session")
		return fmt.Errorf("failed to revoke session: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"session_id": id,
		"reason":     reason,
	}).Info("Session revoked successfully")

	return nil
}

// RevokeAllSessions revokes all user sessions except the specified one
func (s *service) RevokeAllSessions(ctx context.Context, userID string, exceptSessionID string) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.writeDB.Session.Update().
		Where(session.UserID(userUUID)).
		Where(session.StatusEQ(session.StatusActive)).
		SetStatus(session.StatusRevoked).
		SetLogoutTime(time.Now())

	// Exclude specific session if provided
	if exceptSessionID != "" {
		exceptID, err := uuid.Parse(exceptSessionID)
		if err == nil {
			query = query.Where(session.IDNEQ(exceptID))
		}
	}

	affected, err := query.Save(ctx)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to revoke all sessions")
		return fmt.Errorf("failed to revoke sessions: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id":           userID,
		"except_session_id": exceptSessionID,
		"affected_sessions": affected,
	}).Info("All sessions revoked successfully")

	return nil
}

// IsSessionActive checks if a session is active and not expired
func (s *service) IsSessionActive(ctx context.Context, id string) (bool, error) {
	sessionID, err := uuid.Parse(id)
	if err != nil {
		return false, fmt.Errorf("invalid session ID: %w", err)
	}

	sessionEntity, err := s.readDB.Session.Get(ctx, sessionID)
	if err != nil {
		if ent.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is active
	if sessionEntity.Status != session.StatusActive {
		return false, nil
	}

	// Check if session is expired
	if !sessionEntity.ExpiresAt.IsZero() && time.Now().After(sessionEntity.ExpiresAt) {
		// Mark session as expired
		_ = s.writeDB.Session.UpdateOneID(sessionID).
			SetStatus(session.StatusExpired).
			Exec(ctx)
		return false, nil
	}

	return true, nil
}

// RefreshSession extends session expiration time
func (s *service) RefreshSession(ctx context.Context, id string) (*ent.Session, error) {
	sessionID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid session ID: %w", err)
	}

	// Check if session is active
	isActive, err := s.IsSessionActive(ctx, id)
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, fmt.Errorf("session is not active")
	}

	// Extend expiration by 24 hours
	newExpiresAt := time.Now().Add(24 * time.Hour)

	updatedSession, err := s.writeDB.Session.UpdateOneID(sessionID).
		SetExpiresAt(newExpiresAt).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("session not found")
		}
		s.logger.WithError(err).WithField("session_id", id).Error("Failed to refresh session")
		return nil, fmt.Errorf("failed to refresh session: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"session_id":     id,
		"new_expires_at": newExpiresAt,
	}).Info("Session refreshed successfully")

	return updatedSession, nil
}

// CleanupExpiredSessions removes expired sessions
func (s *service) CleanupExpiredSessions(ctx context.Context) (int, error) {
	// Mark expired sessions
	affected, err := s.writeDB.Session.Update().
		Where(session.StatusEQ(session.StatusActive)).
		Where(session.ExpiresAtLT(time.Now())).
		SetStatus(session.StatusExpired).
		Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to mark expired sessions")
		return 0, fmt.Errorf("failed to mark expired sessions: %w", err)
	}

	// Delete sessions older than 30 days
	cutoffTime := time.Now().AddDate(0, 0, -30)
	deleted, err := s.writeDB.Session.Delete().
		Where(session.CreatedAtLT(cutoffTime)).
		Exec(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to delete old sessions")
		return affected, fmt.Errorf("failed to delete old sessions: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"expired_sessions": affected,
		"deleted_sessions": deleted,
	}).Info("Session cleanup completed")

	return affected, nil
}

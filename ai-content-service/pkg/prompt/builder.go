package prompt

import (
	"fmt"
	"strings"
	"time"
)

// ContentType represents the type of content to generate
type ContentType string

const (
	ContentTypeSocialPost ContentType = "social_post"
	ContentTypeBlogPost   ContentType = "blog_post"
	ContentTypeEmail      ContentType = "email"
	ContentTypeAd         ContentType = "advertisement"
	ContentTypeCaption    ContentType = "caption"
	ContentTypeHashtags   ContentType = "hashtags"
)

// Platform represents social media platforms
type Platform string

const (
	PlatformFacebook   Platform = "facebook"
	PlatformInstagram  Platform = "instagram"
	PlatformTwitter    Platform = "twitter"
	PlatformLinkedIn   Platform = "linkedin"
	PlatformTikTok     Platform = "tiktok"
	PlatformYouTube    Platform = "youtube"
	PlatformPinterest  Platform = "pinterest"
)

// Tone represents content tone
type Tone string

const (
	ToneProfessional  Tone = "professional"
	ToneFriendly      Tone = "friendly"
	ToneHumorous      Tone = "humorous"
	ToneFormal        Tone = "formal"
	ToneCasual        Tone = "casual"
	ToneEnthusiastic  Tone = "enthusiastic"
	ToneAuthoritative Tone = "authoritative"
	ToneInspirational Tone = "inspirational"
)

// ContentRequest represents a content generation request
type ContentRequest struct {
	ContentType   ContentType       `json:"content_type"`
	Platform      Platform          `json:"platform,omitempty"`
	Tone          Tone              `json:"tone"`
	Topic         string            `json:"topic"`
	Keywords      []string          `json:"keywords,omitempty"`
	TargetLength  int               `json:"target_length,omitempty"`
	Audience      string            `json:"audience,omitempty"`
	CallToAction  string            `json:"call_to_action,omitempty"`
	BrandVoice    string            `json:"brand_voice,omitempty"`
	Context       string            `json:"context,omitempty"`
	Variables     map[string]string `json:"variables,omitempty"`
	Constraints   []string          `json:"constraints,omitempty"`
	Examples      []string          `json:"examples,omitempty"`
}

// Builder helps build optimized prompts for AI content generation
type Builder struct{}

// NewBuilder creates a new prompt builder
func NewBuilder() *Builder {
	return &Builder{}
}

// BuildContentPrompt builds a comprehensive prompt for content generation
func (b *Builder) BuildContentPrompt(req *ContentRequest) string {
	var prompt strings.Builder

	// System role and expertise
	prompt.WriteString(b.buildSystemRole(req.ContentType, req.Platform))
	prompt.WriteString("\n\n")

	// Content specifications
	prompt.WriteString(b.buildContentSpecs(req))
	prompt.WriteString("\n\n")

	// Platform-specific guidelines
	if req.Platform != "" {
		prompt.WriteString(b.buildPlatformGuidelines(req.Platform))
		prompt.WriteString("\n\n")
	}

	// Tone and style guidelines
	prompt.WriteString(b.buildToneGuidelines(req.Tone, req.BrandVoice))
	prompt.WriteString("\n\n")

	// Content requirements
	prompt.WriteString(b.buildContentRequirements(req))
	prompt.WriteString("\n\n")

	// Examples if provided
	if len(req.Examples) > 0 {
		prompt.WriteString(b.buildExamples(req.Examples))
		prompt.WriteString("\n\n")
	}

	// Final instructions
	prompt.WriteString(b.buildFinalInstructions(req))

	return prompt.String()
}

// buildSystemRole creates the system role based on content type and platform
func (b *Builder) buildSystemRole(contentType ContentType, platform Platform) string {
	roles := map[ContentType]string{
		ContentTypeSocialPost: "You are an expert social media content creator and strategist",
		ContentTypeBlogPost:   "You are a professional blog writer and content strategist",
		ContentTypeEmail:      "You are an expert email marketing copywriter",
		ContentTypeAd:         "You are a creative advertising copywriter",
		ContentTypeCaption:    "You are a social media caption specialist",
		ContentTypeHashtags:   "You are a social media hashtag expert",
	}

	role := roles[contentType]
	if role == "" {
		role = "You are an expert content creator"
	}

	if platform != "" {
		role += fmt.Sprintf(" specializing in %s content", platform)
	}

	role += ". You understand audience psychology, engagement strategies, and platform algorithms."

	return role
}

// buildContentSpecs creates content specifications
func (b *Builder) buildContentSpecs(req *ContentRequest) string {
	specs := fmt.Sprintf("Create %s content about: %s", req.ContentType, req.Topic)

	if req.Audience != "" {
		specs += fmt.Sprintf("\nTarget Audience: %s", req.Audience)
	}

	if len(req.Keywords) > 0 {
		specs += fmt.Sprintf("\nKey Topics/Keywords: %s", strings.Join(req.Keywords, ", "))
	}

	if req.Context != "" {
		specs += fmt.Sprintf("\nContext: %s", req.Context)
	}

	return specs
}

// buildPlatformGuidelines creates platform-specific guidelines
func (b *Builder) buildPlatformGuidelines(platform Platform) string {
	guidelines := map[Platform]string{
		PlatformFacebook: `Facebook Guidelines:
- Optimal length: 40-80 characters for highest engagement
- Use emojis to increase engagement by 57%
- Ask questions to encourage comments
- Include clear call-to-action
- Use 1-2 hashtags maximum`,

		PlatformInstagram: `Instagram Guidelines:
- Optimal caption length: 125-150 characters
- Use 5-10 relevant hashtags
- Include emojis and line breaks for readability
- Tell a story or share behind-the-scenes content
- Use strong visual language`,

		PlatformTwitter: `Twitter/X Guidelines:
- Keep under 280 characters
- Use 1-2 hashtags maximum
- Include mentions when relevant
- Create urgency or curiosity
- Use threads for longer content`,

		PlatformLinkedIn: `LinkedIn Guidelines:
- Professional tone with personal touch
- Optimal length: 150-300 words
- Use industry-specific keywords
- Include actionable insights
- End with thought-provoking questions`,

		PlatformTikTok: `TikTok Guidelines:
- Hook viewers in first 3 seconds
- Use trending sounds and hashtags
- Keep captions short and punchy
- Include clear call-to-action
- Use popular TikTok phrases and slang`,

		PlatformYouTube: `YouTube Guidelines:
- Compelling titles with keywords
- Detailed descriptions with timestamps
- Use relevant tags and categories
- Include clear call-to-action
- Optimize for search discovery`,
	}

	return guidelines[platform]
}

// buildToneGuidelines creates tone and style guidelines
func (b *Builder) buildToneGuidelines(tone Tone, brandVoice string) string {
	toneDescriptions := map[Tone]string{
		ToneProfessional:  "Use professional, polished language. Be authoritative yet approachable.",
		ToneFriendly:      "Use warm, conversational language. Be personable and relatable.",
		ToneHumorous:      "Use wit and humor appropriately. Be entertaining while staying on-topic.",
		ToneFormal:        "Use formal, structured language. Be respectful and traditional.",
		ToneCasual:        "Use relaxed, everyday language. Be informal and conversational.",
		ToneEnthusiastic:  "Use energetic, excited language. Show passion and excitement.",
		ToneAuthoritative: "Use confident, expert language. Demonstrate knowledge and credibility.",
		ToneInspirational: "Use uplifting, motivational language. Inspire and encourage action.",
	}

	guidelines := fmt.Sprintf("Tone: %s - %s", tone, toneDescriptions[tone])

	if brandVoice != "" {
		guidelines += fmt.Sprintf("\nBrand Voice: %s", brandVoice)
	}

	return guidelines
}

// buildContentRequirements creates content requirements
func (b *Builder) buildContentRequirements(req *ContentRequest) string {
	requirements := "Content Requirements:"

	if req.TargetLength > 0 {
		requirements += fmt.Sprintf("\n- Target length: approximately %d characters/words", req.TargetLength)
	}

	if req.CallToAction != "" {
		requirements += fmt.Sprintf("\n- Include this call-to-action: %s", req.CallToAction)
	}

	if len(req.Constraints) > 0 {
		requirements += "\n- Constraints:"
		for _, constraint := range req.Constraints {
			requirements += fmt.Sprintf("\n  • %s", constraint)
		}
	}

	// Add variables if provided
	if len(req.Variables) > 0 {
		requirements += "\n- Use these variables in the content:"
		for key, value := range req.Variables {
			requirements += fmt.Sprintf("\n  • %s: %s", key, value)
		}
	}

	return requirements
}

// buildExamples creates examples section
func (b *Builder) buildExamples(examples []string) string {
	result := "Examples for reference:"
	for i, example := range examples {
		result += fmt.Sprintf("\n\nExample %d:\n%s", i+1, example)
	}
	return result
}

// buildFinalInstructions creates final instructions
func (b *Builder) buildFinalInstructions(req *ContentRequest) string {
	instructions := "Instructions:\n"
	instructions += "- Create engaging, original content that resonates with the target audience\n"
	instructions += "- Ensure the content is optimized for the specified platform\n"
	instructions += "- Maintain consistency with the requested tone and brand voice\n"
	instructions += "- Include relevant keywords naturally\n"
	instructions += "- Make the content actionable and valuable\n"

	if req.Platform != "" {
		instructions += fmt.Sprintf("- Follow %s best practices and algorithm preferences\n", req.Platform)
	}

	instructions += "- Return only the final content without explanations or meta-commentary"

	return instructions
}

// BuildImprovementPrompt builds a prompt for content improvement
func (b *Builder) BuildImprovementPrompt(originalContent, improvements string, platform Platform) string {
	var prompt strings.Builder

	prompt.WriteString("You are an expert content editor and optimization specialist.\n\n")

	prompt.WriteString("Your task is to improve the following content based on the specified improvements:\n\n")

	prompt.WriteString(fmt.Sprintf("Original Content:\n%s\n\n", originalContent))

	prompt.WriteString(fmt.Sprintf("Requested Improvements:\n%s\n\n", improvements))

	if platform != "" {
		prompt.WriteString(b.buildPlatformGuidelines(platform))
		prompt.WriteString("\n\n")
	}

	prompt.WriteString("Improvement Guidelines:\n")
	prompt.WriteString("- Maintain the original intent and core message\n")
	prompt.WriteString("- Enhance clarity, engagement, and readability\n")
	prompt.WriteString("- Optimize for the target platform if specified\n")
	prompt.WriteString("- Improve grammar, style, and flow\n")
	prompt.WriteString("- Make the content more compelling and actionable\n")
	prompt.WriteString("- Return only the improved content without explanations\n")

	return prompt.String()
}

// BuildHashtagPrompt builds a prompt for hashtag generation
func (b *Builder) BuildHashtagPrompt(content string, platform Platform, count int) string {
	var prompt strings.Builder

	prompt.WriteString("You are a social media hashtag expert with deep knowledge of trending topics and platform algorithms.\n\n")

	prompt.WriteString(fmt.Sprintf("Generate %d relevant hashtags for the following content:\n\n", count))
	prompt.WriteString(fmt.Sprintf("Content: %s\n\n", content))

	if platform != "" {
		prompt.WriteString(fmt.Sprintf("Platform: %s\n\n", platform))
		
		// Platform-specific hashtag guidelines
		platformGuidelines := map[Platform]string{
			PlatformInstagram: "Instagram hashtag strategy: Mix popular (#1M+ posts), medium (#100K-1M posts), and niche (#10K-100K posts) hashtags",
			PlatformTwitter:    "Twitter hashtag strategy: Use 1-2 hashtags maximum, focus on trending and relevant tags",
			PlatformLinkedIn:   "LinkedIn hashtag strategy: Use professional, industry-specific hashtags",
			PlatformTikTok:     "TikTok hashtag strategy: Include trending hashtags and niche community tags",
		}
		
		if guideline, exists := platformGuidelines[platform]; exists {
			prompt.WriteString(fmt.Sprintf("%s\n\n", guideline))
		}
	}

	prompt.WriteString("Hashtag Requirements:\n")
	prompt.WriteString("- Generate relevant, trending hashtags\n")
	prompt.WriteString("- Mix popular and niche hashtags\n")
	prompt.WriteString("- Avoid banned or problematic hashtags\n")
	prompt.WriteString("- Consider current trends and seasonality\n")
	prompt.WriteString("- Return hashtags without the # symbol\n")
	prompt.WriteString("- One hashtag per line\n")
	prompt.WriteString("- No explanations or additional text\n")

	return prompt.String()
}

// GetCurrentTimeContext returns current time context for time-sensitive content
func (b *Builder) GetCurrentTimeContext() string {
	now := time.Now()
	return fmt.Sprintf("Current date: %s, Current time: %s", 
		now.Format("January 2, 2006"), 
		now.Format("3:04 PM MST"))
}

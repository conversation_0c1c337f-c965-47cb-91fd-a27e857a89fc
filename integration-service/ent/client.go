// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// PlatformIntegration is the client for interacting with the PlatformIntegration builders.
	PlatformIntegration *PlatformIntegrationClient
	// PublishedPost is the client for interacting with the PublishedPost builders.
	PublishedPost *PublishedPostClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.PlatformIntegration = NewPlatformIntegrationClient(c.config)
	c.PublishedPost = NewPublishedPostClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		PlatformIntegration: NewPlatformIntegrationClient(cfg),
		PublishedPost:       NewPublishedPostClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		PlatformIntegration: NewPlatformIntegrationClient(cfg),
		PublishedPost:       NewPublishedPostClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		PlatformIntegration.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.PlatformIntegration.Use(hooks...)
	c.PublishedPost.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.PlatformIntegration.Intercept(interceptors...)
	c.PublishedPost.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *PlatformIntegrationMutation:
		return c.PlatformIntegration.mutate(ctx, m)
	case *PublishedPostMutation:
		return c.PublishedPost.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// PlatformIntegrationClient is a client for the PlatformIntegration schema.
type PlatformIntegrationClient struct {
	config
}

// NewPlatformIntegrationClient returns a client for the PlatformIntegration from the given config.
func NewPlatformIntegrationClient(c config) *PlatformIntegrationClient {
	return &PlatformIntegrationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `platformintegration.Hooks(f(g(h())))`.
func (c *PlatformIntegrationClient) Use(hooks ...Hook) {
	c.hooks.PlatformIntegration = append(c.hooks.PlatformIntegration, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `platformintegration.Intercept(f(g(h())))`.
func (c *PlatformIntegrationClient) Intercept(interceptors ...Interceptor) {
	c.inters.PlatformIntegration = append(c.inters.PlatformIntegration, interceptors...)
}

// Create returns a builder for creating a PlatformIntegration entity.
func (c *PlatformIntegrationClient) Create() *PlatformIntegrationCreate {
	mutation := newPlatformIntegrationMutation(c.config, OpCreate)
	return &PlatformIntegrationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PlatformIntegration entities.
func (c *PlatformIntegrationClient) CreateBulk(builders ...*PlatformIntegrationCreate) *PlatformIntegrationCreateBulk {
	return &PlatformIntegrationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PlatformIntegrationClient) MapCreateBulk(slice any, setFunc func(*PlatformIntegrationCreate, int)) *PlatformIntegrationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PlatformIntegrationCreateBulk{err: fmt.Errorf("calling to PlatformIntegrationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PlatformIntegrationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PlatformIntegrationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PlatformIntegration.
func (c *PlatformIntegrationClient) Update() *PlatformIntegrationUpdate {
	mutation := newPlatformIntegrationMutation(c.config, OpUpdate)
	return &PlatformIntegrationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PlatformIntegrationClient) UpdateOne(pi *PlatformIntegration) *PlatformIntegrationUpdateOne {
	mutation := newPlatformIntegrationMutation(c.config, OpUpdateOne, withPlatformIntegration(pi))
	return &PlatformIntegrationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PlatformIntegrationClient) UpdateOneID(id uuid.UUID) *PlatformIntegrationUpdateOne {
	mutation := newPlatformIntegrationMutation(c.config, OpUpdateOne, withPlatformIntegrationID(id))
	return &PlatformIntegrationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PlatformIntegration.
func (c *PlatformIntegrationClient) Delete() *PlatformIntegrationDelete {
	mutation := newPlatformIntegrationMutation(c.config, OpDelete)
	return &PlatformIntegrationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PlatformIntegrationClient) DeleteOne(pi *PlatformIntegration) *PlatformIntegrationDeleteOne {
	return c.DeleteOneID(pi.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PlatformIntegrationClient) DeleteOneID(id uuid.UUID) *PlatformIntegrationDeleteOne {
	builder := c.Delete().Where(platformintegration.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PlatformIntegrationDeleteOne{builder}
}

// Query returns a query builder for PlatformIntegration.
func (c *PlatformIntegrationClient) Query() *PlatformIntegrationQuery {
	return &PlatformIntegrationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePlatformIntegration},
		inters: c.Interceptors(),
	}
}

// Get returns a PlatformIntegration entity by its id.
func (c *PlatformIntegrationClient) Get(ctx context.Context, id uuid.UUID) (*PlatformIntegration, error) {
	return c.Query().Where(platformintegration.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PlatformIntegrationClient) GetX(ctx context.Context, id uuid.UUID) *PlatformIntegration {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PlatformIntegrationClient) Hooks() []Hook {
	return c.hooks.PlatformIntegration
}

// Interceptors returns the client interceptors.
func (c *PlatformIntegrationClient) Interceptors() []Interceptor {
	return c.inters.PlatformIntegration
}

func (c *PlatformIntegrationClient) mutate(ctx context.Context, m *PlatformIntegrationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PlatformIntegrationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PlatformIntegrationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PlatformIntegrationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PlatformIntegrationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PlatformIntegration mutation op: %q", m.Op())
	}
}

// PublishedPostClient is a client for the PublishedPost schema.
type PublishedPostClient struct {
	config
}

// NewPublishedPostClient returns a client for the PublishedPost from the given config.
func NewPublishedPostClient(c config) *PublishedPostClient {
	return &PublishedPostClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `publishedpost.Hooks(f(g(h())))`.
func (c *PublishedPostClient) Use(hooks ...Hook) {
	c.hooks.PublishedPost = append(c.hooks.PublishedPost, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `publishedpost.Intercept(f(g(h())))`.
func (c *PublishedPostClient) Intercept(interceptors ...Interceptor) {
	c.inters.PublishedPost = append(c.inters.PublishedPost, interceptors...)
}

// Create returns a builder for creating a PublishedPost entity.
func (c *PublishedPostClient) Create() *PublishedPostCreate {
	mutation := newPublishedPostMutation(c.config, OpCreate)
	return &PublishedPostCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PublishedPost entities.
func (c *PublishedPostClient) CreateBulk(builders ...*PublishedPostCreate) *PublishedPostCreateBulk {
	return &PublishedPostCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PublishedPostClient) MapCreateBulk(slice any, setFunc func(*PublishedPostCreate, int)) *PublishedPostCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PublishedPostCreateBulk{err: fmt.Errorf("calling to PublishedPostClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PublishedPostCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PublishedPostCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PublishedPost.
func (c *PublishedPostClient) Update() *PublishedPostUpdate {
	mutation := newPublishedPostMutation(c.config, OpUpdate)
	return &PublishedPostUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PublishedPostClient) UpdateOne(pp *PublishedPost) *PublishedPostUpdateOne {
	mutation := newPublishedPostMutation(c.config, OpUpdateOne, withPublishedPost(pp))
	return &PublishedPostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PublishedPostClient) UpdateOneID(id uuid.UUID) *PublishedPostUpdateOne {
	mutation := newPublishedPostMutation(c.config, OpUpdateOne, withPublishedPostID(id))
	return &PublishedPostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PublishedPost.
func (c *PublishedPostClient) Delete() *PublishedPostDelete {
	mutation := newPublishedPostMutation(c.config, OpDelete)
	return &PublishedPostDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PublishedPostClient) DeleteOne(pp *PublishedPost) *PublishedPostDeleteOne {
	return c.DeleteOneID(pp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PublishedPostClient) DeleteOneID(id uuid.UUID) *PublishedPostDeleteOne {
	builder := c.Delete().Where(publishedpost.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PublishedPostDeleteOne{builder}
}

// Query returns a query builder for PublishedPost.
func (c *PublishedPostClient) Query() *PublishedPostQuery {
	return &PublishedPostQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePublishedPost},
		inters: c.Interceptors(),
	}
}

// Get returns a PublishedPost entity by its id.
func (c *PublishedPostClient) Get(ctx context.Context, id uuid.UUID) (*PublishedPost, error) {
	return c.Query().Where(publishedpost.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PublishedPostClient) GetX(ctx context.Context, id uuid.UUID) *PublishedPost {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PublishedPostClient) Hooks() []Hook {
	return c.hooks.PublishedPost
}

// Interceptors returns the client interceptors.
func (c *PublishedPostClient) Interceptors() []Interceptor {
	return c.inters.PublishedPost
}

func (c *PublishedPostClient) mutate(ctx context.Context, m *PublishedPostMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PublishedPostCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PublishedPostUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PublishedPostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PublishedPostDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown PublishedPost mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		PlatformIntegration, PublishedPost []ent.Hook
	}
	inters struct {
		PlatformIntegration, PublishedPost []ent.Interceptor
	}
)

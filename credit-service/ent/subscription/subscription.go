// Code generated by ent, DO NOT EDIT.

package subscription

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the subscription type in the database.
	Label = "subscription"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldPlanID holds the string denoting the plan_id field in the database.
	FieldPlanID = "plan_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldCurrentPeriodStart holds the string denoting the current_period_start field in the database.
	FieldCurrentPeriodStart = "current_period_start"
	// FieldCurrentPeriodEnd holds the string denoting the current_period_end field in the database.
	FieldCurrentPeriodEnd = "current_period_end"
	// FieldTrialStart holds the string denoting the trial_start field in the database.
	FieldTrialStart = "trial_start"
	// FieldTrialEnd holds the string denoting the trial_end field in the database.
	FieldTrialEnd = "trial_end"
	// FieldCancelledAt holds the string denoting the cancelled_at field in the database.
	FieldCancelledAt = "cancelled_at"
	// FieldCancelAtPeriodEnd holds the string denoting the cancel_at_period_end field in the database.
	FieldCancelAtPeriodEnd = "cancel_at_period_end"
	// FieldPaymentMethod holds the string denoting the payment_method field in the database.
	FieldPaymentMethod = "payment_method"
	// FieldBillingAddress holds the string denoting the billing_address field in the database.
	FieldBillingAddress = "billing_address"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldLastBilledAt holds the string denoting the last_billed_at field in the database.
	FieldLastBilledAt = "last_billed_at"
	// FieldNextBillingAt holds the string denoting the next_billing_at field in the database.
	FieldNextBillingAt = "next_billing_at"
	// FieldExternalSubscriptionID holds the string denoting the external_subscription_id field in the database.
	FieldExternalSubscriptionID = "external_subscription_id"
	// FieldPaymentID holds the string denoting the payment_id field in the database.
	FieldPaymentID = "payment_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the subscription in the database.
	Table = "subscriptions"
)

// Columns holds all SQL columns for subscription fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldPlanID,
	FieldStatus,
	FieldCurrentPeriodStart,
	FieldCurrentPeriodEnd,
	FieldTrialStart,
	FieldTrialEnd,
	FieldCancelledAt,
	FieldCancelAtPeriodEnd,
	FieldPaymentMethod,
	FieldBillingAddress,
	FieldMetadata,
	FieldLastBilledAt,
	FieldNextBillingAt,
	FieldExternalSubscriptionID,
	FieldPaymentID,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCancelAtPeriodEnd holds the default value on creation for the "cancel_at_period_end" field.
	DefaultCancelAtPeriodEnd bool
	// PaymentMethodValidator is a validator for the "payment_method" field. It is called by the builders before save.
	PaymentMethodValidator func(string) error
	// ExternalSubscriptionIDValidator is a validator for the "external_subscription_id" field. It is called by the builders before save.
	ExternalSubscriptionIDValidator func(string) error
	// PaymentIDValidator is a validator for the "payment_id" field. It is called by the builders before save.
	PaymentIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusTrial is the default value of the Status enum.
const DefaultStatus = StatusTrial

// Status values.
const (
	StatusActive    Status = "active"
	StatusCancelled Status = "cancelled"
	StatusExpired   Status = "expired"
	StatusTrial     Status = "trial"
	StatusPastDue   Status = "past_due"
	StatusPending   Status = "pending"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusActive, StatusCancelled, StatusExpired, StatusTrial, StatusPastDue, StatusPending:
		return nil
	default:
		return fmt.Errorf("subscription: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the Subscription queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByPlanID orders the results by the plan_id field.
func ByPlanID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlanID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByCurrentPeriodStart orders the results by the current_period_start field.
func ByCurrentPeriodStart(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrentPeriodStart, opts...).ToFunc()
}

// ByCurrentPeriodEnd orders the results by the current_period_end field.
func ByCurrentPeriodEnd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrentPeriodEnd, opts...).ToFunc()
}

// ByTrialStart orders the results by the trial_start field.
func ByTrialStart(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTrialStart, opts...).ToFunc()
}

// ByTrialEnd orders the results by the trial_end field.
func ByTrialEnd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTrialEnd, opts...).ToFunc()
}

// ByCancelledAt orders the results by the cancelled_at field.
func ByCancelledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCancelledAt, opts...).ToFunc()
}

// ByCancelAtPeriodEnd orders the results by the cancel_at_period_end field.
func ByCancelAtPeriodEnd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCancelAtPeriodEnd, opts...).ToFunc()
}

// ByPaymentMethod orders the results by the payment_method field.
func ByPaymentMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentMethod, opts...).ToFunc()
}

// ByLastBilledAt orders the results by the last_billed_at field.
func ByLastBilledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastBilledAt, opts...).ToFunc()
}

// ByNextBillingAt orders the results by the next_billing_at field.
func ByNextBillingAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNextBillingAt, opts...).ToFunc()
}

// ByExternalSubscriptionID orders the results by the external_subscription_id field.
func ByExternalSubscriptionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExternalSubscriptionID, opts...).ToFunc()
}

// ByPaymentID orders the results by the payment_id field.
func ByPaymentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

// WorkspaceCreate is the builder for creating a Workspace entity.
type WorkspaceCreate struct {
	config
	mutation *WorkspaceMutation
	hooks    []Hook
}

// SetOwnerID sets the "owner_id" field.
func (wc *WorkspaceCreate) SetOwnerID(u uuid.UUID) *WorkspaceCreate {
	wc.mutation.SetOwnerID(u)
	return wc
}

// SetName sets the "name" field.
func (wc *WorkspaceCreate) SetName(s string) *WorkspaceCreate {
	wc.mutation.SetName(s)
	return wc
}

// SetDescription sets the "description" field.
func (wc *WorkspaceCreate) SetDescription(s string) *WorkspaceCreate {
	wc.mutation.SetDescription(s)
	return wc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableDescription(s *string) *WorkspaceCreate {
	if s != nil {
		wc.SetDescription(*s)
	}
	return wc
}

// SetColor sets the "color" field.
func (wc *WorkspaceCreate) SetColor(s string) *WorkspaceCreate {
	wc.mutation.SetColor(s)
	return wc
}

// SetNillableColor sets the "color" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableColor(s *string) *WorkspaceCreate {
	if s != nil {
		wc.SetColor(*s)
	}
	return wc
}

// SetIcon sets the "icon" field.
func (wc *WorkspaceCreate) SetIcon(s string) *WorkspaceCreate {
	wc.mutation.SetIcon(s)
	return wc
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableIcon(s *string) *WorkspaceCreate {
	if s != nil {
		wc.SetIcon(*s)
	}
	return wc
}

// SetSettings sets the "settings" field.
func (wc *WorkspaceCreate) SetSettings(m map[string]interface{}) *WorkspaceCreate {
	wc.mutation.SetSettings(m)
	return wc
}

// SetDefaultPlatforms sets the "default_platforms" field.
func (wc *WorkspaceCreate) SetDefaultPlatforms(s []string) *WorkspaceCreate {
	wc.mutation.SetDefaultPlatforms(s)
	return wc
}

// SetBrandGuidelines sets the "brand_guidelines" field.
func (wc *WorkspaceCreate) SetBrandGuidelines(m map[string]interface{}) *WorkspaceCreate {
	wc.mutation.SetBrandGuidelines(m)
	return wc
}

// SetIsPersonal sets the "is_personal" field.
func (wc *WorkspaceCreate) SetIsPersonal(b bool) *WorkspaceCreate {
	wc.mutation.SetIsPersonal(b)
	return wc
}

// SetNillableIsPersonal sets the "is_personal" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableIsPersonal(b *bool) *WorkspaceCreate {
	if b != nil {
		wc.SetIsPersonal(*b)
	}
	return wc
}

// SetIsActive sets the "is_active" field.
func (wc *WorkspaceCreate) SetIsActive(b bool) *WorkspaceCreate {
	wc.mutation.SetIsActive(b)
	return wc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableIsActive(b *bool) *WorkspaceCreate {
	if b != nil {
		wc.SetIsActive(*b)
	}
	return wc
}

// SetPostCount sets the "post_count" field.
func (wc *WorkspaceCreate) SetPostCount(i int) *WorkspaceCreate {
	wc.mutation.SetPostCount(i)
	return wc
}

// SetNillablePostCount sets the "post_count" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillablePostCount(i *int) *WorkspaceCreate {
	if i != nil {
		wc.SetPostCount(*i)
	}
	return wc
}

// SetTemplateCount sets the "template_count" field.
func (wc *WorkspaceCreate) SetTemplateCount(i int) *WorkspaceCreate {
	wc.mutation.SetTemplateCount(i)
	return wc
}

// SetNillableTemplateCount sets the "template_count" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableTemplateCount(i *int) *WorkspaceCreate {
	if i != nil {
		wc.SetTemplateCount(*i)
	}
	return wc
}

// SetLastActivityAt sets the "last_activity_at" field.
func (wc *WorkspaceCreate) SetLastActivityAt(t time.Time) *WorkspaceCreate {
	wc.mutation.SetLastActivityAt(t)
	return wc
}

// SetNillableLastActivityAt sets the "last_activity_at" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableLastActivityAt(t *time.Time) *WorkspaceCreate {
	if t != nil {
		wc.SetLastActivityAt(*t)
	}
	return wc
}

// SetMetadata sets the "metadata" field.
func (wc *WorkspaceCreate) SetMetadata(m map[string]interface{}) *WorkspaceCreate {
	wc.mutation.SetMetadata(m)
	return wc
}

// SetCreatedAt sets the "created_at" field.
func (wc *WorkspaceCreate) SetCreatedAt(t time.Time) *WorkspaceCreate {
	wc.mutation.SetCreatedAt(t)
	return wc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableCreatedAt(t *time.Time) *WorkspaceCreate {
	if t != nil {
		wc.SetCreatedAt(*t)
	}
	return wc
}

// SetUpdatedAt sets the "updated_at" field.
func (wc *WorkspaceCreate) SetUpdatedAt(t time.Time) *WorkspaceCreate {
	wc.mutation.SetUpdatedAt(t)
	return wc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableUpdatedAt(t *time.Time) *WorkspaceCreate {
	if t != nil {
		wc.SetUpdatedAt(*t)
	}
	return wc
}

// SetDeletedAt sets the "deleted_at" field.
func (wc *WorkspaceCreate) SetDeletedAt(t time.Time) *WorkspaceCreate {
	wc.mutation.SetDeletedAt(t)
	return wc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableDeletedAt(t *time.Time) *WorkspaceCreate {
	if t != nil {
		wc.SetDeletedAt(*t)
	}
	return wc
}

// SetID sets the "id" field.
func (wc *WorkspaceCreate) SetID(u uuid.UUID) *WorkspaceCreate {
	wc.mutation.SetID(u)
	return wc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wc *WorkspaceCreate) SetNillableID(u *uuid.UUID) *WorkspaceCreate {
	if u != nil {
		wc.SetID(*u)
	}
	return wc
}

// Mutation returns the WorkspaceMutation object of the builder.
func (wc *WorkspaceCreate) Mutation() *WorkspaceMutation {
	return wc.mutation
}

// Save creates the Workspace in the database.
func (wc *WorkspaceCreate) Save(ctx context.Context) (*Workspace, error) {
	wc.defaults()
	return withHooks(ctx, wc.sqlSave, wc.mutation, wc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wc *WorkspaceCreate) SaveX(ctx context.Context) *Workspace {
	v, err := wc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wc *WorkspaceCreate) Exec(ctx context.Context) error {
	_, err := wc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wc *WorkspaceCreate) ExecX(ctx context.Context) {
	if err := wc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wc *WorkspaceCreate) defaults() {
	if _, ok := wc.mutation.Color(); !ok {
		v := workspace.DefaultColor
		wc.mutation.SetColor(v)
	}
	if _, ok := wc.mutation.IsPersonal(); !ok {
		v := workspace.DefaultIsPersonal
		wc.mutation.SetIsPersonal(v)
	}
	if _, ok := wc.mutation.IsActive(); !ok {
		v := workspace.DefaultIsActive
		wc.mutation.SetIsActive(v)
	}
	if _, ok := wc.mutation.PostCount(); !ok {
		v := workspace.DefaultPostCount
		wc.mutation.SetPostCount(v)
	}
	if _, ok := wc.mutation.TemplateCount(); !ok {
		v := workspace.DefaultTemplateCount
		wc.mutation.SetTemplateCount(v)
	}
	if _, ok := wc.mutation.CreatedAt(); !ok {
		v := workspace.DefaultCreatedAt()
		wc.mutation.SetCreatedAt(v)
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		v := workspace.DefaultUpdatedAt()
		wc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wc.mutation.ID(); !ok {
		v := workspace.DefaultID()
		wc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wc *WorkspaceCreate) check() error {
	if _, ok := wc.mutation.OwnerID(); !ok {
		return &ValidationError{Name: "owner_id", err: errors.New(`ent: missing required field "Workspace.owner_id"`)}
	}
	if _, ok := wc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Workspace.name"`)}
	}
	if v, ok := wc.mutation.Name(); ok {
		if err := workspace.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Workspace.name": %w`, err)}
		}
	}
	if _, ok := wc.mutation.Color(); !ok {
		return &ValidationError{Name: "color", err: errors.New(`ent: missing required field "Workspace.color"`)}
	}
	if v, ok := wc.mutation.Color(); ok {
		if err := workspace.ColorValidator(v); err != nil {
			return &ValidationError{Name: "color", err: fmt.Errorf(`ent: validator failed for field "Workspace.color": %w`, err)}
		}
	}
	if v, ok := wc.mutation.Icon(); ok {
		if err := workspace.IconValidator(v); err != nil {
			return &ValidationError{Name: "icon", err: fmt.Errorf(`ent: validator failed for field "Workspace.icon": %w`, err)}
		}
	}
	if _, ok := wc.mutation.IsPersonal(); !ok {
		return &ValidationError{Name: "is_personal", err: errors.New(`ent: missing required field "Workspace.is_personal"`)}
	}
	if _, ok := wc.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "Workspace.is_active"`)}
	}
	if _, ok := wc.mutation.PostCount(); !ok {
		return &ValidationError{Name: "post_count", err: errors.New(`ent: missing required field "Workspace.post_count"`)}
	}
	if _, ok := wc.mutation.TemplateCount(); !ok {
		return &ValidationError{Name: "template_count", err: errors.New(`ent: missing required field "Workspace.template_count"`)}
	}
	if _, ok := wc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Workspace.created_at"`)}
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Workspace.updated_at"`)}
	}
	return nil
}

func (wc *WorkspaceCreate) sqlSave(ctx context.Context) (*Workspace, error) {
	if err := wc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	wc.mutation.id = &_node.ID
	wc.mutation.done = true
	return _node, nil
}

func (wc *WorkspaceCreate) createSpec() (*Workspace, *sqlgraph.CreateSpec) {
	var (
		_node = &Workspace{config: wc.config}
		_spec = sqlgraph.NewCreateSpec(workspace.Table, sqlgraph.NewFieldSpec(workspace.FieldID, field.TypeUUID))
	)
	if id, ok := wc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := wc.mutation.OwnerID(); ok {
		_spec.SetField(workspace.FieldOwnerID, field.TypeUUID, value)
		_node.OwnerID = value
	}
	if value, ok := wc.mutation.Name(); ok {
		_spec.SetField(workspace.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := wc.mutation.Description(); ok {
		_spec.SetField(workspace.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := wc.mutation.Color(); ok {
		_spec.SetField(workspace.FieldColor, field.TypeString, value)
		_node.Color = value
	}
	if value, ok := wc.mutation.Icon(); ok {
		_spec.SetField(workspace.FieldIcon, field.TypeString, value)
		_node.Icon = value
	}
	if value, ok := wc.mutation.Settings(); ok {
		_spec.SetField(workspace.FieldSettings, field.TypeJSON, value)
		_node.Settings = value
	}
	if value, ok := wc.mutation.DefaultPlatforms(); ok {
		_spec.SetField(workspace.FieldDefaultPlatforms, field.TypeJSON, value)
		_node.DefaultPlatforms = value
	}
	if value, ok := wc.mutation.BrandGuidelines(); ok {
		_spec.SetField(workspace.FieldBrandGuidelines, field.TypeJSON, value)
		_node.BrandGuidelines = value
	}
	if value, ok := wc.mutation.IsPersonal(); ok {
		_spec.SetField(workspace.FieldIsPersonal, field.TypeBool, value)
		_node.IsPersonal = value
	}
	if value, ok := wc.mutation.IsActive(); ok {
		_spec.SetField(workspace.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := wc.mutation.PostCount(); ok {
		_spec.SetField(workspace.FieldPostCount, field.TypeInt, value)
		_node.PostCount = value
	}
	if value, ok := wc.mutation.TemplateCount(); ok {
		_spec.SetField(workspace.FieldTemplateCount, field.TypeInt, value)
		_node.TemplateCount = value
	}
	if value, ok := wc.mutation.LastActivityAt(); ok {
		_spec.SetField(workspace.FieldLastActivityAt, field.TypeTime, value)
		_node.LastActivityAt = value
	}
	if value, ok := wc.mutation.Metadata(); ok {
		_spec.SetField(workspace.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := wc.mutation.CreatedAt(); ok {
		_spec.SetField(workspace.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wc.mutation.UpdatedAt(); ok {
		_spec.SetField(workspace.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wc.mutation.DeletedAt(); ok {
		_spec.SetField(workspace.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// WorkspaceCreateBulk is the builder for creating many Workspace entities in bulk.
type WorkspaceCreateBulk struct {
	config
	err      error
	builders []*WorkspaceCreate
}

// Save creates the Workspace entities in the database.
func (wcb *WorkspaceCreateBulk) Save(ctx context.Context) ([]*Workspace, error) {
	if wcb.err != nil {
		return nil, wcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wcb.builders))
	nodes := make([]*Workspace, len(wcb.builders))
	mutators := make([]Mutator, len(wcb.builders))
	for i := range wcb.builders {
		func(i int, root context.Context) {
			builder := wcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WorkspaceMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wcb *WorkspaceCreateBulk) SaveX(ctx context.Context) []*Workspace {
	v, err := wcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wcb *WorkspaceCreateBulk) Exec(ctx context.Context) error {
	_, err := wcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wcb *WorkspaceCreateBulk) ExecX(ctx context.Context) {
	if err := wcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notification"
)

// NotificationCreate is the builder for creating a Notification entity.
type NotificationCreate struct {
	config
	mutation *NotificationMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (nc *NotificationCreate) SetUserID(u uuid.UUID) *NotificationCreate {
	nc.mutation.SetUserID(u)
	return nc
}

// SetType sets the "type" field.
func (nc *NotificationCreate) SetType(n notification.Type) *NotificationCreate {
	nc.mutation.SetType(n)
	return nc
}

// SetTitle sets the "title" field.
func (nc *NotificationCreate) SetTitle(s string) *NotificationCreate {
	nc.mutation.SetTitle(s)
	return nc
}

// SetMessage sets the "message" field.
func (nc *NotificationCreate) SetMessage(s string) *NotificationCreate {
	nc.mutation.SetMessage(s)
	return nc
}

// SetData sets the "data" field.
func (nc *NotificationCreate) SetData(m map[string]interface{}) *NotificationCreate {
	nc.mutation.SetData(m)
	return nc
}

// SetPriority sets the "priority" field.
func (nc *NotificationCreate) SetPriority(n notification.Priority) *NotificationCreate {
	nc.mutation.SetPriority(n)
	return nc
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (nc *NotificationCreate) SetNillablePriority(n *notification.Priority) *NotificationCreate {
	if n != nil {
		nc.SetPriority(*n)
	}
	return nc
}

// SetChannels sets the "channels" field.
func (nc *NotificationCreate) SetChannels(s []string) *NotificationCreate {
	nc.mutation.SetChannels(s)
	return nc
}

// SetStatus sets the "status" field.
func (nc *NotificationCreate) SetStatus(n notification.Status) *NotificationCreate {
	nc.mutation.SetStatus(n)
	return nc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableStatus(n *notification.Status) *NotificationCreate {
	if n != nil {
		nc.SetStatus(*n)
	}
	return nc
}

// SetReferenceID sets the "reference_id" field.
func (nc *NotificationCreate) SetReferenceID(s string) *NotificationCreate {
	nc.mutation.SetReferenceID(s)
	return nc
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableReferenceID(s *string) *NotificationCreate {
	if s != nil {
		nc.SetReferenceID(*s)
	}
	return nc
}

// SetReferenceType sets the "reference_type" field.
func (nc *NotificationCreate) SetReferenceType(s string) *NotificationCreate {
	nc.mutation.SetReferenceType(s)
	return nc
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableReferenceType(s *string) *NotificationCreate {
	if s != nil {
		nc.SetReferenceType(*s)
	}
	return nc
}

// SetDeliveryStatus sets the "delivery_status" field.
func (nc *NotificationCreate) SetDeliveryStatus(m map[string]interface{}) *NotificationCreate {
	nc.mutation.SetDeliveryStatus(m)
	return nc
}

// SetScheduledAt sets the "scheduled_at" field.
func (nc *NotificationCreate) SetScheduledAt(t time.Time) *NotificationCreate {
	nc.mutation.SetScheduledAt(t)
	return nc
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableScheduledAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetScheduledAt(*t)
	}
	return nc
}

// SetSentAt sets the "sent_at" field.
func (nc *NotificationCreate) SetSentAt(t time.Time) *NotificationCreate {
	nc.mutation.SetSentAt(t)
	return nc
}

// SetNillableSentAt sets the "sent_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableSentAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetSentAt(*t)
	}
	return nc
}

// SetDeliveredAt sets the "delivered_at" field.
func (nc *NotificationCreate) SetDeliveredAt(t time.Time) *NotificationCreate {
	nc.mutation.SetDeliveredAt(t)
	return nc
}

// SetNillableDeliveredAt sets the "delivered_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableDeliveredAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetDeliveredAt(*t)
	}
	return nc
}

// SetReadAt sets the "read_at" field.
func (nc *NotificationCreate) SetReadAt(t time.Time) *NotificationCreate {
	nc.mutation.SetReadAt(t)
	return nc
}

// SetNillableReadAt sets the "read_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableReadAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetReadAt(*t)
	}
	return nc
}

// SetExpiresAt sets the "expires_at" field.
func (nc *NotificationCreate) SetExpiresAt(t time.Time) *NotificationCreate {
	nc.mutation.SetExpiresAt(t)
	return nc
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableExpiresAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetExpiresAt(*t)
	}
	return nc
}

// SetActionURL sets the "action_url" field.
func (nc *NotificationCreate) SetActionURL(s string) *NotificationCreate {
	nc.mutation.SetActionURL(s)
	return nc
}

// SetNillableActionURL sets the "action_url" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableActionURL(s *string) *NotificationCreate {
	if s != nil {
		nc.SetActionURL(*s)
	}
	return nc
}

// SetActionData sets the "action_data" field.
func (nc *NotificationCreate) SetActionData(m map[string]interface{}) *NotificationCreate {
	nc.mutation.SetActionData(m)
	return nc
}

// SetRetryCount sets the "retry_count" field.
func (nc *NotificationCreate) SetRetryCount(i int) *NotificationCreate {
	nc.mutation.SetRetryCount(i)
	return nc
}

// SetNillableRetryCount sets the "retry_count" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableRetryCount(i *int) *NotificationCreate {
	if i != nil {
		nc.SetRetryCount(*i)
	}
	return nc
}

// SetNextRetryAt sets the "next_retry_at" field.
func (nc *NotificationCreate) SetNextRetryAt(t time.Time) *NotificationCreate {
	nc.mutation.SetNextRetryAt(t)
	return nc
}

// SetNillableNextRetryAt sets the "next_retry_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableNextRetryAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetNextRetryAt(*t)
	}
	return nc
}

// SetErrorMessage sets the "error_message" field.
func (nc *NotificationCreate) SetErrorMessage(s string) *NotificationCreate {
	nc.mutation.SetErrorMessage(s)
	return nc
}

// SetNillableErrorMessage sets the "error_message" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableErrorMessage(s *string) *NotificationCreate {
	if s != nil {
		nc.SetErrorMessage(*s)
	}
	return nc
}

// SetMetadata sets the "metadata" field.
func (nc *NotificationCreate) SetMetadata(m map[string]interface{}) *NotificationCreate {
	nc.mutation.SetMetadata(m)
	return nc
}

// SetCreatedAt sets the "created_at" field.
func (nc *NotificationCreate) SetCreatedAt(t time.Time) *NotificationCreate {
	nc.mutation.SetCreatedAt(t)
	return nc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableCreatedAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetCreatedAt(*t)
	}
	return nc
}

// SetUpdatedAt sets the "updated_at" field.
func (nc *NotificationCreate) SetUpdatedAt(t time.Time) *NotificationCreate {
	nc.mutation.SetUpdatedAt(t)
	return nc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableUpdatedAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetUpdatedAt(*t)
	}
	return nc
}

// SetDeletedAt sets the "deleted_at" field.
func (nc *NotificationCreate) SetDeletedAt(t time.Time) *NotificationCreate {
	nc.mutation.SetDeletedAt(t)
	return nc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableDeletedAt(t *time.Time) *NotificationCreate {
	if t != nil {
		nc.SetDeletedAt(*t)
	}
	return nc
}

// SetID sets the "id" field.
func (nc *NotificationCreate) SetID(u uuid.UUID) *NotificationCreate {
	nc.mutation.SetID(u)
	return nc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (nc *NotificationCreate) SetNillableID(u *uuid.UUID) *NotificationCreate {
	if u != nil {
		nc.SetID(*u)
	}
	return nc
}

// Mutation returns the NotificationMutation object of the builder.
func (nc *NotificationCreate) Mutation() *NotificationMutation {
	return nc.mutation
}

// Save creates the Notification in the database.
func (nc *NotificationCreate) Save(ctx context.Context) (*Notification, error) {
	nc.defaults()
	return withHooks(ctx, nc.sqlSave, nc.mutation, nc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (nc *NotificationCreate) SaveX(ctx context.Context) *Notification {
	v, err := nc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nc *NotificationCreate) Exec(ctx context.Context) error {
	_, err := nc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nc *NotificationCreate) ExecX(ctx context.Context) {
	if err := nc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nc *NotificationCreate) defaults() {
	if _, ok := nc.mutation.Priority(); !ok {
		v := notification.DefaultPriority
		nc.mutation.SetPriority(v)
	}
	if _, ok := nc.mutation.Status(); !ok {
		v := notification.DefaultStatus
		nc.mutation.SetStatus(v)
	}
	if _, ok := nc.mutation.RetryCount(); !ok {
		v := notification.DefaultRetryCount
		nc.mutation.SetRetryCount(v)
	}
	if _, ok := nc.mutation.CreatedAt(); !ok {
		v := notification.DefaultCreatedAt()
		nc.mutation.SetCreatedAt(v)
	}
	if _, ok := nc.mutation.UpdatedAt(); !ok {
		v := notification.DefaultUpdatedAt()
		nc.mutation.SetUpdatedAt(v)
	}
	if _, ok := nc.mutation.ID(); !ok {
		v := notification.DefaultID()
		nc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (nc *NotificationCreate) check() error {
	if _, ok := nc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Notification.user_id"`)}
	}
	if _, ok := nc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Notification.type"`)}
	}
	if v, ok := nc.mutation.GetType(); ok {
		if err := notification.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Notification.type": %w`, err)}
		}
	}
	if _, ok := nc.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "Notification.title"`)}
	}
	if v, ok := nc.mutation.Title(); ok {
		if err := notification.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Notification.title": %w`, err)}
		}
	}
	if _, ok := nc.mutation.Message(); !ok {
		return &ValidationError{Name: "message", err: errors.New(`ent: missing required field "Notification.message"`)}
	}
	if _, ok := nc.mutation.Priority(); !ok {
		return &ValidationError{Name: "priority", err: errors.New(`ent: missing required field "Notification.priority"`)}
	}
	if v, ok := nc.mutation.Priority(); ok {
		if err := notification.PriorityValidator(v); err != nil {
			return &ValidationError{Name: "priority", err: fmt.Errorf(`ent: validator failed for field "Notification.priority": %w`, err)}
		}
	}
	if _, ok := nc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Notification.status"`)}
	}
	if v, ok := nc.mutation.Status(); ok {
		if err := notification.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Notification.status": %w`, err)}
		}
	}
	if v, ok := nc.mutation.ReferenceID(); ok {
		if err := notification.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_id": %w`, err)}
		}
	}
	if v, ok := nc.mutation.ReferenceType(); ok {
		if err := notification.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "Notification.reference_type": %w`, err)}
		}
	}
	if v, ok := nc.mutation.ActionURL(); ok {
		if err := notification.ActionURLValidator(v); err != nil {
			return &ValidationError{Name: "action_url", err: fmt.Errorf(`ent: validator failed for field "Notification.action_url": %w`, err)}
		}
	}
	if _, ok := nc.mutation.RetryCount(); !ok {
		return &ValidationError{Name: "retry_count", err: errors.New(`ent: missing required field "Notification.retry_count"`)}
	}
	if v, ok := nc.mutation.ErrorMessage(); ok {
		if err := notification.ErrorMessageValidator(v); err != nil {
			return &ValidationError{Name: "error_message", err: fmt.Errorf(`ent: validator failed for field "Notification.error_message": %w`, err)}
		}
	}
	if _, ok := nc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Notification.created_at"`)}
	}
	if _, ok := nc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Notification.updated_at"`)}
	}
	return nil
}

func (nc *NotificationCreate) sqlSave(ctx context.Context) (*Notification, error) {
	if err := nc.check(); err != nil {
		return nil, err
	}
	_node, _spec := nc.createSpec()
	if err := sqlgraph.CreateNode(ctx, nc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	nc.mutation.id = &_node.ID
	nc.mutation.done = true
	return _node, nil
}

func (nc *NotificationCreate) createSpec() (*Notification, *sqlgraph.CreateSpec) {
	var (
		_node = &Notification{config: nc.config}
		_spec = sqlgraph.NewCreateSpec(notification.Table, sqlgraph.NewFieldSpec(notification.FieldID, field.TypeUUID))
	)
	if id, ok := nc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := nc.mutation.UserID(); ok {
		_spec.SetField(notification.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := nc.mutation.GetType(); ok {
		_spec.SetField(notification.FieldType, field.TypeEnum, value)
		_node.Type = value
	}
	if value, ok := nc.mutation.Title(); ok {
		_spec.SetField(notification.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := nc.mutation.Message(); ok {
		_spec.SetField(notification.FieldMessage, field.TypeString, value)
		_node.Message = value
	}
	if value, ok := nc.mutation.Data(); ok {
		_spec.SetField(notification.FieldData, field.TypeJSON, value)
		_node.Data = value
	}
	if value, ok := nc.mutation.Priority(); ok {
		_spec.SetField(notification.FieldPriority, field.TypeEnum, value)
		_node.Priority = value
	}
	if value, ok := nc.mutation.Channels(); ok {
		_spec.SetField(notification.FieldChannels, field.TypeJSON, value)
		_node.Channels = value
	}
	if value, ok := nc.mutation.Status(); ok {
		_spec.SetField(notification.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := nc.mutation.ReferenceID(); ok {
		_spec.SetField(notification.FieldReferenceID, field.TypeString, value)
		_node.ReferenceID = value
	}
	if value, ok := nc.mutation.ReferenceType(); ok {
		_spec.SetField(notification.FieldReferenceType, field.TypeString, value)
		_node.ReferenceType = value
	}
	if value, ok := nc.mutation.DeliveryStatus(); ok {
		_spec.SetField(notification.FieldDeliveryStatus, field.TypeJSON, value)
		_node.DeliveryStatus = value
	}
	if value, ok := nc.mutation.ScheduledAt(); ok {
		_spec.SetField(notification.FieldScheduledAt, field.TypeTime, value)
		_node.ScheduledAt = value
	}
	if value, ok := nc.mutation.SentAt(); ok {
		_spec.SetField(notification.FieldSentAt, field.TypeTime, value)
		_node.SentAt = value
	}
	if value, ok := nc.mutation.DeliveredAt(); ok {
		_spec.SetField(notification.FieldDeliveredAt, field.TypeTime, value)
		_node.DeliveredAt = value
	}
	if value, ok := nc.mutation.ReadAt(); ok {
		_spec.SetField(notification.FieldReadAt, field.TypeTime, value)
		_node.ReadAt = value
	}
	if value, ok := nc.mutation.ExpiresAt(); ok {
		_spec.SetField(notification.FieldExpiresAt, field.TypeTime, value)
		_node.ExpiresAt = value
	}
	if value, ok := nc.mutation.ActionURL(); ok {
		_spec.SetField(notification.FieldActionURL, field.TypeString, value)
		_node.ActionURL = value
	}
	if value, ok := nc.mutation.ActionData(); ok {
		_spec.SetField(notification.FieldActionData, field.TypeJSON, value)
		_node.ActionData = value
	}
	if value, ok := nc.mutation.RetryCount(); ok {
		_spec.SetField(notification.FieldRetryCount, field.TypeInt, value)
		_node.RetryCount = value
	}
	if value, ok := nc.mutation.NextRetryAt(); ok {
		_spec.SetField(notification.FieldNextRetryAt, field.TypeTime, value)
		_node.NextRetryAt = value
	}
	if value, ok := nc.mutation.ErrorMessage(); ok {
		_spec.SetField(notification.FieldErrorMessage, field.TypeString, value)
		_node.ErrorMessage = value
	}
	if value, ok := nc.mutation.Metadata(); ok {
		_spec.SetField(notification.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := nc.mutation.CreatedAt(); ok {
		_spec.SetField(notification.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := nc.mutation.UpdatedAt(); ok {
		_spec.SetField(notification.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := nc.mutation.DeletedAt(); ok {
		_spec.SetField(notification.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	return _node, _spec
}

// NotificationCreateBulk is the builder for creating many Notification entities in bulk.
type NotificationCreateBulk struct {
	config
	err      error
	builders []*NotificationCreate
}

// Save creates the Notification entities in the database.
func (ncb *NotificationCreateBulk) Save(ctx context.Context) ([]*Notification, error) {
	if ncb.err != nil {
		return nil, ncb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ncb.builders))
	nodes := make([]*Notification, len(ncb.builders))
	mutators := make([]Mutator, len(ncb.builders))
	for i := range ncb.builders {
		func(i int, root context.Context) {
			builder := ncb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotificationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ncb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ncb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ncb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ncb *NotificationCreateBulk) SaveX(ctx context.Context) []*Notification {
	v, err := ncb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ncb *NotificationCreateBulk) Exec(ctx context.Context) error {
	_, err := ncb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ncb *NotificationCreateBulk) ExecX(ctx context.Context) {
	if err := ncb.Exec(ctx); err != nil {
		panic(err)
	}
}

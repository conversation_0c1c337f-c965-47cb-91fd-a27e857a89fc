# OAuth Integration Flow

## Overview

Integration Service cung cấp OAuth flow hoàn chỉnh để kết nối các tài khoản mạng xã hội. Flow này bao gồm:

1. **OAuth URL Generation** - Tạo URL để redirect user đến platform OAuth
2. **OAuth Callback Handling** - Xử lý callback từ platform và kết nối tài khoản
3. **State Parameter Management** - Quản lý state để bảo mật và truyền thông tin

## Supported Platforms

- Facebook
- Twitter/X
- Instagram
- LinkedIn
- YouTube
- TikTok

## OAuth Flow Steps

### 1. Generate OAuth URL

Frontend gọi gRPC method `GetOAuthURL` để lấy OAuth URL:

```protobuf
message GetOAuthURLRequest {
  string user_id = 1;
  string platform = 2;
  string redirect_uri = 3;
  repeated string requested_permissions = 4;
  string state = 5;
}
```

**State Parameter Format:**
State parameter phải là base64 encoded JSON với format:
```json
{
  "user_id": "user-uuid",
  "workspace_id": "workspace-uuid", // optional
  "redirect_url": "https://app.com/success" // optional
}
```

**Example:**
```javascript
// Frontend JavaScript
const state = {
  user_id: "123e4567-e89b-12d3-a456-************",
  workspace_id: "456e7890-e89b-12d3-a456-************",
  redirect_url: "https://myapp.com/oauth/success"
};

const encodedState = btoa(JSON.stringify(state));

// Call gRPC service
const response = await integrationService.getOAuthURL({
  user_id: state.user_id,
  platform: "facebook",
  redirect_uri: "http://localhost:8085/oauth/facebook/callback",
  requested_permissions: ["pages_manage_posts", "pages_read_engagement"],
  state: encodedState
});

// Redirect user to OAuth URL
window.location.href = response.auth_url;
```

### 2. OAuth Callback Handling

Khi user hoàn thành OAuth flow, platform sẽ redirect về callback URL với authorization code.

**Callback URLs:**
- Facebook: `GET /oauth/facebook/callback`
- Twitter: `GET /oauth/twitter/callback`
- Instagram: `GET /oauth/instagram/callback`
- LinkedIn: `GET /oauth/linkedin/callback`
- YouTube: `GET /oauth/youtube/callback`
- TikTok: `GET /oauth/tiktok/callback`

**Callback Flow:**
1. Parse `code` và `state` parameters
2. Decode và validate state parameter
3. Gọi integration service để connect account
4. Redirect user về success/error page

### 3. Response Handling

**Success Response:**
- **JSON Response** (nếu `Accept: application/json` hoặc `?format=json`):
```json
{
  "success": true,
  "platform": "facebook",
  "account_id": "account-uuid",
  "message": "Account connected successfully"
}
```

- **Redirect Response** (default):
```
Redirect to: https://myapp.com/oauth/success?platform=facebook&account_id=account-uuid&status=success
```

**Error Response:**
- **JSON Response**:
```json
{
  "error": "connection_failed",
  "message": "Failed to connect account"
}
```

- **Redirect Response**:
```
Redirect to: http://localhost:3000/oauth/error?error=connection_failed&message=Failed%20to%20connect%20account
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| `oauth_error` | Platform OAuth error (user denied, etc.) |
| `missing_code` | Authorization code not provided |
| `invalid_state` | State parameter invalid or missing |
| `connection_failed` | Failed to connect account to platform |

## Frontend Integration Example

### React Example

```jsx
import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

function OAuthCallback() {
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    const error = searchParams.get('error');
    const platform = searchParams.get('platform');
    const accountId = searchParams.get('account_id');
    const status = searchParams.get('status');
    
    if (error) {
      // Handle error
      console.error('OAuth Error:', error, searchParams.get('message'));
      // Show error message to user
    } else if (status === 'success') {
      // Handle success
      console.log('Account connected:', { platform, accountId });
      // Update UI, refresh account list, etc.
    }
  }, [searchParams]);
  
  return (
    <div>
      {searchParams.get('error') ? (
        <div className="error">
          <h2>Connection Failed</h2>
          <p>{searchParams.get('message')}</p>
        </div>
      ) : (
        <div className="success">
          <h2>Account Connected Successfully!</h2>
          <p>Platform: {searchParams.get('platform')}</p>
        </div>
      )}
    </div>
  );
}
```

### Vue.js Example

```vue
<template>
  <div>
    <div v-if="error" class="error">
      <h2>Connection Failed</h2>
      <p>{{ errorMessage }}</p>
    </div>
    <div v-else-if="success" class="success">
      <h2>Account Connected Successfully!</h2>
      <p>Platform: {{ platform }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      error: null,
      errorMessage: '',
      success: false,
      platform: '',
      accountId: ''
    };
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search);
    
    this.error = urlParams.get('error');
    this.errorMessage = urlParams.get('message');
    this.platform = urlParams.get('platform');
    this.accountId = urlParams.get('account_id');
    this.success = urlParams.get('status') === 'success';
    
    if (this.success) {
      // Handle successful connection
      this.$emit('account-connected', {
        platform: this.platform,
        accountId: this.accountId
      });
    }
  }
};
</script>
```

## Security Considerations

1. **State Parameter**: Luôn sử dụng state parameter để prevent CSRF attacks
2. **HTTPS**: Sử dụng HTTPS cho tất cả OAuth URLs trong production
3. **Redirect URI Validation**: Đảm bảo redirect URIs được whitelist
4. **Token Storage**: Access tokens được lưu trữ an toàn trong database
5. **Signature Verification**: Webhook signatures được verify để đảm bảo authenticity

## Configuration

Cấu hình OAuth credentials trong config file:

```yaml
oauth:
  facebook:
    client_id: "${FACEBOOK_CLIENT_ID}"
    client_secret: "${FACEBOOK_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/facebook/callback"
  
  twitter:
    client_id: "${TWITTER_CLIENT_ID}"
    client_secret: "${TWITTER_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/twitter/callback"
  
  # ... other platforms
```

## Testing

Để test OAuth flow trong development:

1. Cấu hình OAuth apps trên các platforms với callback URLs pointing đến localhost
2. Set environment variables cho client IDs và secrets
3. Start integration service: `go run main.go`
4. Test OAuth flow bằng cách navigate đến OAuth URLs

## Troubleshooting

**Common Issues:**

1. **Invalid redirect URI**: Đảm bảo callback URL match exactly với configured redirect URI
2. **State mismatch**: Check state parameter encoding/decoding
3. **Token exchange failed**: Verify client credentials và network connectivity
4. **Permission denied**: Check requested permissions có valid không

**Debug Logs:**
Enable debug logging để xem detailed OAuth flow:
```yaml
logging:
  level: "debug"
```

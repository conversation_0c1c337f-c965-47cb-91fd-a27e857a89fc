// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notification"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeNotification           = "Notification"
	TypeNotificationPreference = "NotificationPreference"
	TypeNotificationTemplate   = "NotificationTemplate"
)

// NotificationMutation represents an operation that mutates the Notification nodes in the graph.
type NotificationMutation struct {
	config
	op              Op
	typ             string
	id              *uuid.UUID
	user_id         *uuid.UUID
	_type           *notification.Type
	title           *string
	message         *string
	data            *map[string]interface{}
	priority        *notification.Priority
	channels        *[]string
	appendchannels  []string
	status          *notification.Status
	reference_id    *string
	reference_type  *string
	delivery_status *map[string]interface{}
	scheduled_at    *time.Time
	sent_at         *time.Time
	delivered_at    *time.Time
	read_at         *time.Time
	expires_at      *time.Time
	action_url      *string
	action_data     *map[string]interface{}
	retry_count     *int
	addretry_count  *int
	next_retry_at   *time.Time
	error_message   *string
	metadata        *map[string]interface{}
	created_at      *time.Time
	updated_at      *time.Time
	deleted_at      *time.Time
	clearedFields   map[string]struct{}
	done            bool
	oldValue        func(context.Context) (*Notification, error)
	predicates      []predicate.Notification
}

var _ ent.Mutation = (*NotificationMutation)(nil)

// notificationOption allows management of the mutation configuration using functional options.
type notificationOption func(*NotificationMutation)

// newNotificationMutation creates new mutation for the Notification entity.
func newNotificationMutation(c config, op Op, opts ...notificationOption) *NotificationMutation {
	m := &NotificationMutation{
		config:        c,
		op:            op,
		typ:           TypeNotification,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withNotificationID sets the ID field of the mutation.
func withNotificationID(id uuid.UUID) notificationOption {
	return func(m *NotificationMutation) {
		var (
			err   error
			once  sync.Once
			value *Notification
		)
		m.oldValue = func(ctx context.Context) (*Notification, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Notification.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withNotification sets the old Notification of the mutation.
func withNotification(node *Notification) notificationOption {
	return func(m *NotificationMutation) {
		m.oldValue = func(context.Context) (*Notification, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m NotificationMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m NotificationMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Notification entities.
func (m *NotificationMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *NotificationMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *NotificationMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Notification.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *NotificationMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *NotificationMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *NotificationMutation) ResetUserID() {
	m.user_id = nil
}

// SetType sets the "type" field.
func (m *NotificationMutation) SetType(n notification.Type) {
	m._type = &n
}

// GetType returns the value of the "type" field in the mutation.
func (m *NotificationMutation) GetType() (r notification.Type, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldType(ctx context.Context) (v notification.Type, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *NotificationMutation) ResetType() {
	m._type = nil
}

// SetTitle sets the "title" field.
func (m *NotificationMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *NotificationMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *NotificationMutation) ResetTitle() {
	m.title = nil
}

// SetMessage sets the "message" field.
func (m *NotificationMutation) SetMessage(s string) {
	m.message = &s
}

// Message returns the value of the "message" field in the mutation.
func (m *NotificationMutation) Message() (r string, exists bool) {
	v := m.message
	if v == nil {
		return
	}
	return *v, true
}

// OldMessage returns the old "message" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMessage: %w", err)
	}
	return oldValue.Message, nil
}

// ResetMessage resets all changes to the "message" field.
func (m *NotificationMutation) ResetMessage() {
	m.message = nil
}

// SetData sets the "data" field.
func (m *NotificationMutation) SetData(value map[string]interface{}) {
	m.data = &value
}

// Data returns the value of the "data" field in the mutation.
func (m *NotificationMutation) Data() (r map[string]interface{}, exists bool) {
	v := m.data
	if v == nil {
		return
	}
	return *v, true
}

// OldData returns the old "data" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldData(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldData: %w", err)
	}
	return oldValue.Data, nil
}

// ClearData clears the value of the "data" field.
func (m *NotificationMutation) ClearData() {
	m.data = nil
	m.clearedFields[notification.FieldData] = struct{}{}
}

// DataCleared returns if the "data" field was cleared in this mutation.
func (m *NotificationMutation) DataCleared() bool {
	_, ok := m.clearedFields[notification.FieldData]
	return ok
}

// ResetData resets all changes to the "data" field.
func (m *NotificationMutation) ResetData() {
	m.data = nil
	delete(m.clearedFields, notification.FieldData)
}

// SetPriority sets the "priority" field.
func (m *NotificationMutation) SetPriority(n notification.Priority) {
	m.priority = &n
}

// Priority returns the value of the "priority" field in the mutation.
func (m *NotificationMutation) Priority() (r notification.Priority, exists bool) {
	v := m.priority
	if v == nil {
		return
	}
	return *v, true
}

// OldPriority returns the old "priority" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldPriority(ctx context.Context) (v notification.Priority, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPriority: %w", err)
	}
	return oldValue.Priority, nil
}

// ResetPriority resets all changes to the "priority" field.
func (m *NotificationMutation) ResetPriority() {
	m.priority = nil
}

// SetChannels sets the "channels" field.
func (m *NotificationMutation) SetChannels(s []string) {
	m.channels = &s
	m.appendchannels = nil
}

// Channels returns the value of the "channels" field in the mutation.
func (m *NotificationMutation) Channels() (r []string, exists bool) {
	v := m.channels
	if v == nil {
		return
	}
	return *v, true
}

// OldChannels returns the old "channels" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldChannels(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldChannels is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldChannels requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldChannels: %w", err)
	}
	return oldValue.Channels, nil
}

// AppendChannels adds s to the "channels" field.
func (m *NotificationMutation) AppendChannels(s []string) {
	m.appendchannels = append(m.appendchannels, s...)
}

// AppendedChannels returns the list of values that were appended to the "channels" field in this mutation.
func (m *NotificationMutation) AppendedChannels() ([]string, bool) {
	if len(m.appendchannels) == 0 {
		return nil, false
	}
	return m.appendchannels, true
}

// ClearChannels clears the value of the "channels" field.
func (m *NotificationMutation) ClearChannels() {
	m.channels = nil
	m.appendchannels = nil
	m.clearedFields[notification.FieldChannels] = struct{}{}
}

// ChannelsCleared returns if the "channels" field was cleared in this mutation.
func (m *NotificationMutation) ChannelsCleared() bool {
	_, ok := m.clearedFields[notification.FieldChannels]
	return ok
}

// ResetChannels resets all changes to the "channels" field.
func (m *NotificationMutation) ResetChannels() {
	m.channels = nil
	m.appendchannels = nil
	delete(m.clearedFields, notification.FieldChannels)
}

// SetStatus sets the "status" field.
func (m *NotificationMutation) SetStatus(n notification.Status) {
	m.status = &n
}

// Status returns the value of the "status" field in the mutation.
func (m *NotificationMutation) Status() (r notification.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldStatus(ctx context.Context) (v notification.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *NotificationMutation) ResetStatus() {
	m.status = nil
}

// SetReferenceID sets the "reference_id" field.
func (m *NotificationMutation) SetReferenceID(s string) {
	m.reference_id = &s
}

// ReferenceID returns the value of the "reference_id" field in the mutation.
func (m *NotificationMutation) ReferenceID() (r string, exists bool) {
	v := m.reference_id
	if v == nil {
		return
	}
	return *v, true
}

// OldReferenceID returns the old "reference_id" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldReferenceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReferenceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReferenceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReferenceID: %w", err)
	}
	return oldValue.ReferenceID, nil
}

// ClearReferenceID clears the value of the "reference_id" field.
func (m *NotificationMutation) ClearReferenceID() {
	m.reference_id = nil
	m.clearedFields[notification.FieldReferenceID] = struct{}{}
}

// ReferenceIDCleared returns if the "reference_id" field was cleared in this mutation.
func (m *NotificationMutation) ReferenceIDCleared() bool {
	_, ok := m.clearedFields[notification.FieldReferenceID]
	return ok
}

// ResetReferenceID resets all changes to the "reference_id" field.
func (m *NotificationMutation) ResetReferenceID() {
	m.reference_id = nil
	delete(m.clearedFields, notification.FieldReferenceID)
}

// SetReferenceType sets the "reference_type" field.
func (m *NotificationMutation) SetReferenceType(s string) {
	m.reference_type = &s
}

// ReferenceType returns the value of the "reference_type" field in the mutation.
func (m *NotificationMutation) ReferenceType() (r string, exists bool) {
	v := m.reference_type
	if v == nil {
		return
	}
	return *v, true
}

// OldReferenceType returns the old "reference_type" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldReferenceType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReferenceType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReferenceType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReferenceType: %w", err)
	}
	return oldValue.ReferenceType, nil
}

// ClearReferenceType clears the value of the "reference_type" field.
func (m *NotificationMutation) ClearReferenceType() {
	m.reference_type = nil
	m.clearedFields[notification.FieldReferenceType] = struct{}{}
}

// ReferenceTypeCleared returns if the "reference_type" field was cleared in this mutation.
func (m *NotificationMutation) ReferenceTypeCleared() bool {
	_, ok := m.clearedFields[notification.FieldReferenceType]
	return ok
}

// ResetReferenceType resets all changes to the "reference_type" field.
func (m *NotificationMutation) ResetReferenceType() {
	m.reference_type = nil
	delete(m.clearedFields, notification.FieldReferenceType)
}

// SetDeliveryStatus sets the "delivery_status" field.
func (m *NotificationMutation) SetDeliveryStatus(value map[string]interface{}) {
	m.delivery_status = &value
}

// DeliveryStatus returns the value of the "delivery_status" field in the mutation.
func (m *NotificationMutation) DeliveryStatus() (r map[string]interface{}, exists bool) {
	v := m.delivery_status
	if v == nil {
		return
	}
	return *v, true
}

// OldDeliveryStatus returns the old "delivery_status" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldDeliveryStatus(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeliveryStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeliveryStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeliveryStatus: %w", err)
	}
	return oldValue.DeliveryStatus, nil
}

// ClearDeliveryStatus clears the value of the "delivery_status" field.
func (m *NotificationMutation) ClearDeliveryStatus() {
	m.delivery_status = nil
	m.clearedFields[notification.FieldDeliveryStatus] = struct{}{}
}

// DeliveryStatusCleared returns if the "delivery_status" field was cleared in this mutation.
func (m *NotificationMutation) DeliveryStatusCleared() bool {
	_, ok := m.clearedFields[notification.FieldDeliveryStatus]
	return ok
}

// ResetDeliveryStatus resets all changes to the "delivery_status" field.
func (m *NotificationMutation) ResetDeliveryStatus() {
	m.delivery_status = nil
	delete(m.clearedFields, notification.FieldDeliveryStatus)
}

// SetScheduledAt sets the "scheduled_at" field.
func (m *NotificationMutation) SetScheduledAt(t time.Time) {
	m.scheduled_at = &t
}

// ScheduledAt returns the value of the "scheduled_at" field in the mutation.
func (m *NotificationMutation) ScheduledAt() (r time.Time, exists bool) {
	v := m.scheduled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldScheduledAt returns the old "scheduled_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldScheduledAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldScheduledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldScheduledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldScheduledAt: %w", err)
	}
	return oldValue.ScheduledAt, nil
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (m *NotificationMutation) ClearScheduledAt() {
	m.scheduled_at = nil
	m.clearedFields[notification.FieldScheduledAt] = struct{}{}
}

// ScheduledAtCleared returns if the "scheduled_at" field was cleared in this mutation.
func (m *NotificationMutation) ScheduledAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldScheduledAt]
	return ok
}

// ResetScheduledAt resets all changes to the "scheduled_at" field.
func (m *NotificationMutation) ResetScheduledAt() {
	m.scheduled_at = nil
	delete(m.clearedFields, notification.FieldScheduledAt)
}

// SetSentAt sets the "sent_at" field.
func (m *NotificationMutation) SetSentAt(t time.Time) {
	m.sent_at = &t
}

// SentAt returns the value of the "sent_at" field in the mutation.
func (m *NotificationMutation) SentAt() (r time.Time, exists bool) {
	v := m.sent_at
	if v == nil {
		return
	}
	return *v, true
}

// OldSentAt returns the old "sent_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldSentAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSentAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSentAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSentAt: %w", err)
	}
	return oldValue.SentAt, nil
}

// ClearSentAt clears the value of the "sent_at" field.
func (m *NotificationMutation) ClearSentAt() {
	m.sent_at = nil
	m.clearedFields[notification.FieldSentAt] = struct{}{}
}

// SentAtCleared returns if the "sent_at" field was cleared in this mutation.
func (m *NotificationMutation) SentAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldSentAt]
	return ok
}

// ResetSentAt resets all changes to the "sent_at" field.
func (m *NotificationMutation) ResetSentAt() {
	m.sent_at = nil
	delete(m.clearedFields, notification.FieldSentAt)
}

// SetDeliveredAt sets the "delivered_at" field.
func (m *NotificationMutation) SetDeliveredAt(t time.Time) {
	m.delivered_at = &t
}

// DeliveredAt returns the value of the "delivered_at" field in the mutation.
func (m *NotificationMutation) DeliveredAt() (r time.Time, exists bool) {
	v := m.delivered_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeliveredAt returns the old "delivered_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldDeliveredAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeliveredAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeliveredAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeliveredAt: %w", err)
	}
	return oldValue.DeliveredAt, nil
}

// ClearDeliveredAt clears the value of the "delivered_at" field.
func (m *NotificationMutation) ClearDeliveredAt() {
	m.delivered_at = nil
	m.clearedFields[notification.FieldDeliveredAt] = struct{}{}
}

// DeliveredAtCleared returns if the "delivered_at" field was cleared in this mutation.
func (m *NotificationMutation) DeliveredAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldDeliveredAt]
	return ok
}

// ResetDeliveredAt resets all changes to the "delivered_at" field.
func (m *NotificationMutation) ResetDeliveredAt() {
	m.delivered_at = nil
	delete(m.clearedFields, notification.FieldDeliveredAt)
}

// SetReadAt sets the "read_at" field.
func (m *NotificationMutation) SetReadAt(t time.Time) {
	m.read_at = &t
}

// ReadAt returns the value of the "read_at" field in the mutation.
func (m *NotificationMutation) ReadAt() (r time.Time, exists bool) {
	v := m.read_at
	if v == nil {
		return
	}
	return *v, true
}

// OldReadAt returns the old "read_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldReadAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReadAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReadAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReadAt: %w", err)
	}
	return oldValue.ReadAt, nil
}

// ClearReadAt clears the value of the "read_at" field.
func (m *NotificationMutation) ClearReadAt() {
	m.read_at = nil
	m.clearedFields[notification.FieldReadAt] = struct{}{}
}

// ReadAtCleared returns if the "read_at" field was cleared in this mutation.
func (m *NotificationMutation) ReadAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldReadAt]
	return ok
}

// ResetReadAt resets all changes to the "read_at" field.
func (m *NotificationMutation) ResetReadAt() {
	m.read_at = nil
	delete(m.clearedFields, notification.FieldReadAt)
}

// SetExpiresAt sets the "expires_at" field.
func (m *NotificationMutation) SetExpiresAt(t time.Time) {
	m.expires_at = &t
}

// ExpiresAt returns the value of the "expires_at" field in the mutation.
func (m *NotificationMutation) ExpiresAt() (r time.Time, exists bool) {
	v := m.expires_at
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiresAt returns the old "expires_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldExpiresAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiresAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiresAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiresAt: %w", err)
	}
	return oldValue.ExpiresAt, nil
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (m *NotificationMutation) ClearExpiresAt() {
	m.expires_at = nil
	m.clearedFields[notification.FieldExpiresAt] = struct{}{}
}

// ExpiresAtCleared returns if the "expires_at" field was cleared in this mutation.
func (m *NotificationMutation) ExpiresAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldExpiresAt]
	return ok
}

// ResetExpiresAt resets all changes to the "expires_at" field.
func (m *NotificationMutation) ResetExpiresAt() {
	m.expires_at = nil
	delete(m.clearedFields, notification.FieldExpiresAt)
}

// SetActionURL sets the "action_url" field.
func (m *NotificationMutation) SetActionURL(s string) {
	m.action_url = &s
}

// ActionURL returns the value of the "action_url" field in the mutation.
func (m *NotificationMutation) ActionURL() (r string, exists bool) {
	v := m.action_url
	if v == nil {
		return
	}
	return *v, true
}

// OldActionURL returns the old "action_url" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldActionURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActionURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActionURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActionURL: %w", err)
	}
	return oldValue.ActionURL, nil
}

// ClearActionURL clears the value of the "action_url" field.
func (m *NotificationMutation) ClearActionURL() {
	m.action_url = nil
	m.clearedFields[notification.FieldActionURL] = struct{}{}
}

// ActionURLCleared returns if the "action_url" field was cleared in this mutation.
func (m *NotificationMutation) ActionURLCleared() bool {
	_, ok := m.clearedFields[notification.FieldActionURL]
	return ok
}

// ResetActionURL resets all changes to the "action_url" field.
func (m *NotificationMutation) ResetActionURL() {
	m.action_url = nil
	delete(m.clearedFields, notification.FieldActionURL)
}

// SetActionData sets the "action_data" field.
func (m *NotificationMutation) SetActionData(value map[string]interface{}) {
	m.action_data = &value
}

// ActionData returns the value of the "action_data" field in the mutation.
func (m *NotificationMutation) ActionData() (r map[string]interface{}, exists bool) {
	v := m.action_data
	if v == nil {
		return
	}
	return *v, true
}

// OldActionData returns the old "action_data" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldActionData(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldActionData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldActionData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldActionData: %w", err)
	}
	return oldValue.ActionData, nil
}

// ClearActionData clears the value of the "action_data" field.
func (m *NotificationMutation) ClearActionData() {
	m.action_data = nil
	m.clearedFields[notification.FieldActionData] = struct{}{}
}

// ActionDataCleared returns if the "action_data" field was cleared in this mutation.
func (m *NotificationMutation) ActionDataCleared() bool {
	_, ok := m.clearedFields[notification.FieldActionData]
	return ok
}

// ResetActionData resets all changes to the "action_data" field.
func (m *NotificationMutation) ResetActionData() {
	m.action_data = nil
	delete(m.clearedFields, notification.FieldActionData)
}

// SetRetryCount sets the "retry_count" field.
func (m *NotificationMutation) SetRetryCount(i int) {
	m.retry_count = &i
	m.addretry_count = nil
}

// RetryCount returns the value of the "retry_count" field in the mutation.
func (m *NotificationMutation) RetryCount() (r int, exists bool) {
	v := m.retry_count
	if v == nil {
		return
	}
	return *v, true
}

// OldRetryCount returns the old "retry_count" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldRetryCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRetryCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRetryCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRetryCount: %w", err)
	}
	return oldValue.RetryCount, nil
}

// AddRetryCount adds i to the "retry_count" field.
func (m *NotificationMutation) AddRetryCount(i int) {
	if m.addretry_count != nil {
		*m.addretry_count += i
	} else {
		m.addretry_count = &i
	}
}

// AddedRetryCount returns the value that was added to the "retry_count" field in this mutation.
func (m *NotificationMutation) AddedRetryCount() (r int, exists bool) {
	v := m.addretry_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetRetryCount resets all changes to the "retry_count" field.
func (m *NotificationMutation) ResetRetryCount() {
	m.retry_count = nil
	m.addretry_count = nil
}

// SetNextRetryAt sets the "next_retry_at" field.
func (m *NotificationMutation) SetNextRetryAt(t time.Time) {
	m.next_retry_at = &t
}

// NextRetryAt returns the value of the "next_retry_at" field in the mutation.
func (m *NotificationMutation) NextRetryAt() (r time.Time, exists bool) {
	v := m.next_retry_at
	if v == nil {
		return
	}
	return *v, true
}

// OldNextRetryAt returns the old "next_retry_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldNextRetryAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNextRetryAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNextRetryAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNextRetryAt: %w", err)
	}
	return oldValue.NextRetryAt, nil
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (m *NotificationMutation) ClearNextRetryAt() {
	m.next_retry_at = nil
	m.clearedFields[notification.FieldNextRetryAt] = struct{}{}
}

// NextRetryAtCleared returns if the "next_retry_at" field was cleared in this mutation.
func (m *NotificationMutation) NextRetryAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldNextRetryAt]
	return ok
}

// ResetNextRetryAt resets all changes to the "next_retry_at" field.
func (m *NotificationMutation) ResetNextRetryAt() {
	m.next_retry_at = nil
	delete(m.clearedFields, notification.FieldNextRetryAt)
}

// SetErrorMessage sets the "error_message" field.
func (m *NotificationMutation) SetErrorMessage(s string) {
	m.error_message = &s
}

// ErrorMessage returns the value of the "error_message" field in the mutation.
func (m *NotificationMutation) ErrorMessage() (r string, exists bool) {
	v := m.error_message
	if v == nil {
		return
	}
	return *v, true
}

// OldErrorMessage returns the old "error_message" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldErrorMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldErrorMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldErrorMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldErrorMessage: %w", err)
	}
	return oldValue.ErrorMessage, nil
}

// ClearErrorMessage clears the value of the "error_message" field.
func (m *NotificationMutation) ClearErrorMessage() {
	m.error_message = nil
	m.clearedFields[notification.FieldErrorMessage] = struct{}{}
}

// ErrorMessageCleared returns if the "error_message" field was cleared in this mutation.
func (m *NotificationMutation) ErrorMessageCleared() bool {
	_, ok := m.clearedFields[notification.FieldErrorMessage]
	return ok
}

// ResetErrorMessage resets all changes to the "error_message" field.
func (m *NotificationMutation) ResetErrorMessage() {
	m.error_message = nil
	delete(m.clearedFields, notification.FieldErrorMessage)
}

// SetMetadata sets the "metadata" field.
func (m *NotificationMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *NotificationMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *NotificationMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[notification.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *NotificationMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[notification.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *NotificationMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, notification.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *NotificationMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *NotificationMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *NotificationMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *NotificationMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *NotificationMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *NotificationMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *NotificationMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *NotificationMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Notification entity.
// If the Notification object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *NotificationMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[notification.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *NotificationMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[notification.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *NotificationMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, notification.FieldDeletedAt)
}

// Where appends a list predicates to the NotificationMutation builder.
func (m *NotificationMutation) Where(ps ...predicate.Notification) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the NotificationMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *NotificationMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Notification, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *NotificationMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *NotificationMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Notification).
func (m *NotificationMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *NotificationMutation) Fields() []string {
	fields := make([]string, 0, 25)
	if m.user_id != nil {
		fields = append(fields, notification.FieldUserID)
	}
	if m._type != nil {
		fields = append(fields, notification.FieldType)
	}
	if m.title != nil {
		fields = append(fields, notification.FieldTitle)
	}
	if m.message != nil {
		fields = append(fields, notification.FieldMessage)
	}
	if m.data != nil {
		fields = append(fields, notification.FieldData)
	}
	if m.priority != nil {
		fields = append(fields, notification.FieldPriority)
	}
	if m.channels != nil {
		fields = append(fields, notification.FieldChannels)
	}
	if m.status != nil {
		fields = append(fields, notification.FieldStatus)
	}
	if m.reference_id != nil {
		fields = append(fields, notification.FieldReferenceID)
	}
	if m.reference_type != nil {
		fields = append(fields, notification.FieldReferenceType)
	}
	if m.delivery_status != nil {
		fields = append(fields, notification.FieldDeliveryStatus)
	}
	if m.scheduled_at != nil {
		fields = append(fields, notification.FieldScheduledAt)
	}
	if m.sent_at != nil {
		fields = append(fields, notification.FieldSentAt)
	}
	if m.delivered_at != nil {
		fields = append(fields, notification.FieldDeliveredAt)
	}
	if m.read_at != nil {
		fields = append(fields, notification.FieldReadAt)
	}
	if m.expires_at != nil {
		fields = append(fields, notification.FieldExpiresAt)
	}
	if m.action_url != nil {
		fields = append(fields, notification.FieldActionURL)
	}
	if m.action_data != nil {
		fields = append(fields, notification.FieldActionData)
	}
	if m.retry_count != nil {
		fields = append(fields, notification.FieldRetryCount)
	}
	if m.next_retry_at != nil {
		fields = append(fields, notification.FieldNextRetryAt)
	}
	if m.error_message != nil {
		fields = append(fields, notification.FieldErrorMessage)
	}
	if m.metadata != nil {
		fields = append(fields, notification.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, notification.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, notification.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, notification.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *NotificationMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case notification.FieldUserID:
		return m.UserID()
	case notification.FieldType:
		return m.GetType()
	case notification.FieldTitle:
		return m.Title()
	case notification.FieldMessage:
		return m.Message()
	case notification.FieldData:
		return m.Data()
	case notification.FieldPriority:
		return m.Priority()
	case notification.FieldChannels:
		return m.Channels()
	case notification.FieldStatus:
		return m.Status()
	case notification.FieldReferenceID:
		return m.ReferenceID()
	case notification.FieldReferenceType:
		return m.ReferenceType()
	case notification.FieldDeliveryStatus:
		return m.DeliveryStatus()
	case notification.FieldScheduledAt:
		return m.ScheduledAt()
	case notification.FieldSentAt:
		return m.SentAt()
	case notification.FieldDeliveredAt:
		return m.DeliveredAt()
	case notification.FieldReadAt:
		return m.ReadAt()
	case notification.FieldExpiresAt:
		return m.ExpiresAt()
	case notification.FieldActionURL:
		return m.ActionURL()
	case notification.FieldActionData:
		return m.ActionData()
	case notification.FieldRetryCount:
		return m.RetryCount()
	case notification.FieldNextRetryAt:
		return m.NextRetryAt()
	case notification.FieldErrorMessage:
		return m.ErrorMessage()
	case notification.FieldMetadata:
		return m.Metadata()
	case notification.FieldCreatedAt:
		return m.CreatedAt()
	case notification.FieldUpdatedAt:
		return m.UpdatedAt()
	case notification.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *NotificationMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case notification.FieldUserID:
		return m.OldUserID(ctx)
	case notification.FieldType:
		return m.OldType(ctx)
	case notification.FieldTitle:
		return m.OldTitle(ctx)
	case notification.FieldMessage:
		return m.OldMessage(ctx)
	case notification.FieldData:
		return m.OldData(ctx)
	case notification.FieldPriority:
		return m.OldPriority(ctx)
	case notification.FieldChannels:
		return m.OldChannels(ctx)
	case notification.FieldStatus:
		return m.OldStatus(ctx)
	case notification.FieldReferenceID:
		return m.OldReferenceID(ctx)
	case notification.FieldReferenceType:
		return m.OldReferenceType(ctx)
	case notification.FieldDeliveryStatus:
		return m.OldDeliveryStatus(ctx)
	case notification.FieldScheduledAt:
		return m.OldScheduledAt(ctx)
	case notification.FieldSentAt:
		return m.OldSentAt(ctx)
	case notification.FieldDeliveredAt:
		return m.OldDeliveredAt(ctx)
	case notification.FieldReadAt:
		return m.OldReadAt(ctx)
	case notification.FieldExpiresAt:
		return m.OldExpiresAt(ctx)
	case notification.FieldActionURL:
		return m.OldActionURL(ctx)
	case notification.FieldActionData:
		return m.OldActionData(ctx)
	case notification.FieldRetryCount:
		return m.OldRetryCount(ctx)
	case notification.FieldNextRetryAt:
		return m.OldNextRetryAt(ctx)
	case notification.FieldErrorMessage:
		return m.OldErrorMessage(ctx)
	case notification.FieldMetadata:
		return m.OldMetadata(ctx)
	case notification.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case notification.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case notification.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Notification field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationMutation) SetField(name string, value ent.Value) error {
	switch name {
	case notification.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case notification.FieldType:
		v, ok := value.(notification.Type)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case notification.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case notification.FieldMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMessage(v)
		return nil
	case notification.FieldData:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetData(v)
		return nil
	case notification.FieldPriority:
		v, ok := value.(notification.Priority)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPriority(v)
		return nil
	case notification.FieldChannels:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetChannels(v)
		return nil
	case notification.FieldStatus:
		v, ok := value.(notification.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case notification.FieldReferenceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReferenceID(v)
		return nil
	case notification.FieldReferenceType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReferenceType(v)
		return nil
	case notification.FieldDeliveryStatus:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeliveryStatus(v)
		return nil
	case notification.FieldScheduledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetScheduledAt(v)
		return nil
	case notification.FieldSentAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSentAt(v)
		return nil
	case notification.FieldDeliveredAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeliveredAt(v)
		return nil
	case notification.FieldReadAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReadAt(v)
		return nil
	case notification.FieldExpiresAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiresAt(v)
		return nil
	case notification.FieldActionURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActionURL(v)
		return nil
	case notification.FieldActionData:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetActionData(v)
		return nil
	case notification.FieldRetryCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRetryCount(v)
		return nil
	case notification.FieldNextRetryAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNextRetryAt(v)
		return nil
	case notification.FieldErrorMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetErrorMessage(v)
		return nil
	case notification.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case notification.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case notification.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case notification.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Notification field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *NotificationMutation) AddedFields() []string {
	var fields []string
	if m.addretry_count != nil {
		fields = append(fields, notification.FieldRetryCount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *NotificationMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case notification.FieldRetryCount:
		return m.AddedRetryCount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationMutation) AddField(name string, value ent.Value) error {
	switch name {
	case notification.FieldRetryCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRetryCount(v)
		return nil
	}
	return fmt.Errorf("unknown Notification numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *NotificationMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(notification.FieldData) {
		fields = append(fields, notification.FieldData)
	}
	if m.FieldCleared(notification.FieldChannels) {
		fields = append(fields, notification.FieldChannels)
	}
	if m.FieldCleared(notification.FieldReferenceID) {
		fields = append(fields, notification.FieldReferenceID)
	}
	if m.FieldCleared(notification.FieldReferenceType) {
		fields = append(fields, notification.FieldReferenceType)
	}
	if m.FieldCleared(notification.FieldDeliveryStatus) {
		fields = append(fields, notification.FieldDeliveryStatus)
	}
	if m.FieldCleared(notification.FieldScheduledAt) {
		fields = append(fields, notification.FieldScheduledAt)
	}
	if m.FieldCleared(notification.FieldSentAt) {
		fields = append(fields, notification.FieldSentAt)
	}
	if m.FieldCleared(notification.FieldDeliveredAt) {
		fields = append(fields, notification.FieldDeliveredAt)
	}
	if m.FieldCleared(notification.FieldReadAt) {
		fields = append(fields, notification.FieldReadAt)
	}
	if m.FieldCleared(notification.FieldExpiresAt) {
		fields = append(fields, notification.FieldExpiresAt)
	}
	if m.FieldCleared(notification.FieldActionURL) {
		fields = append(fields, notification.FieldActionURL)
	}
	if m.FieldCleared(notification.FieldActionData) {
		fields = append(fields, notification.FieldActionData)
	}
	if m.FieldCleared(notification.FieldNextRetryAt) {
		fields = append(fields, notification.FieldNextRetryAt)
	}
	if m.FieldCleared(notification.FieldErrorMessage) {
		fields = append(fields, notification.FieldErrorMessage)
	}
	if m.FieldCleared(notification.FieldMetadata) {
		fields = append(fields, notification.FieldMetadata)
	}
	if m.FieldCleared(notification.FieldDeletedAt) {
		fields = append(fields, notification.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *NotificationMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *NotificationMutation) ClearField(name string) error {
	switch name {
	case notification.FieldData:
		m.ClearData()
		return nil
	case notification.FieldChannels:
		m.ClearChannels()
		return nil
	case notification.FieldReferenceID:
		m.ClearReferenceID()
		return nil
	case notification.FieldReferenceType:
		m.ClearReferenceType()
		return nil
	case notification.FieldDeliveryStatus:
		m.ClearDeliveryStatus()
		return nil
	case notification.FieldScheduledAt:
		m.ClearScheduledAt()
		return nil
	case notification.FieldSentAt:
		m.ClearSentAt()
		return nil
	case notification.FieldDeliveredAt:
		m.ClearDeliveredAt()
		return nil
	case notification.FieldReadAt:
		m.ClearReadAt()
		return nil
	case notification.FieldExpiresAt:
		m.ClearExpiresAt()
		return nil
	case notification.FieldActionURL:
		m.ClearActionURL()
		return nil
	case notification.FieldActionData:
		m.ClearActionData()
		return nil
	case notification.FieldNextRetryAt:
		m.ClearNextRetryAt()
		return nil
	case notification.FieldErrorMessage:
		m.ClearErrorMessage()
		return nil
	case notification.FieldMetadata:
		m.ClearMetadata()
		return nil
	case notification.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Notification nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *NotificationMutation) ResetField(name string) error {
	switch name {
	case notification.FieldUserID:
		m.ResetUserID()
		return nil
	case notification.FieldType:
		m.ResetType()
		return nil
	case notification.FieldTitle:
		m.ResetTitle()
		return nil
	case notification.FieldMessage:
		m.ResetMessage()
		return nil
	case notification.FieldData:
		m.ResetData()
		return nil
	case notification.FieldPriority:
		m.ResetPriority()
		return nil
	case notification.FieldChannels:
		m.ResetChannels()
		return nil
	case notification.FieldStatus:
		m.ResetStatus()
		return nil
	case notification.FieldReferenceID:
		m.ResetReferenceID()
		return nil
	case notification.FieldReferenceType:
		m.ResetReferenceType()
		return nil
	case notification.FieldDeliveryStatus:
		m.ResetDeliveryStatus()
		return nil
	case notification.FieldScheduledAt:
		m.ResetScheduledAt()
		return nil
	case notification.FieldSentAt:
		m.ResetSentAt()
		return nil
	case notification.FieldDeliveredAt:
		m.ResetDeliveredAt()
		return nil
	case notification.FieldReadAt:
		m.ResetReadAt()
		return nil
	case notification.FieldExpiresAt:
		m.ResetExpiresAt()
		return nil
	case notification.FieldActionURL:
		m.ResetActionURL()
		return nil
	case notification.FieldActionData:
		m.ResetActionData()
		return nil
	case notification.FieldRetryCount:
		m.ResetRetryCount()
		return nil
	case notification.FieldNextRetryAt:
		m.ResetNextRetryAt()
		return nil
	case notification.FieldErrorMessage:
		m.ResetErrorMessage()
		return nil
	case notification.FieldMetadata:
		m.ResetMetadata()
		return nil
	case notification.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case notification.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case notification.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Notification field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *NotificationMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *NotificationMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *NotificationMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *NotificationMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *NotificationMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *NotificationMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *NotificationMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Notification unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *NotificationMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Notification edge %s", name)
}

// NotificationPreferenceMutation represents an operation that mutates the NotificationPreference nodes in the graph.
type NotificationPreferenceMutation struct {
	config
	op                 Op
	typ                string
	id                 *uuid.UUID
	user_id            *uuid.UUID
	email_enabled      *bool
	push_enabled       *bool
	in_app_enabled     *bool
	sms_enabled        *bool
	notification_types *map[string]interface{}
	quiet_hours        *map[string]interface{}
	timezone           *string
	language           *string
	email_settings     *map[string]interface{}
	push_settings      *map[string]interface{}
	frequency_settings *map[string]interface{}
	marketing_emails   *bool
	product_updates    *bool
	security_alerts    *bool
	metadata           *map[string]interface{}
	created_at         *time.Time
	updated_at         *time.Time
	clearedFields      map[string]struct{}
	done               bool
	oldValue           func(context.Context) (*NotificationPreference, error)
	predicates         []predicate.NotificationPreference
}

var _ ent.Mutation = (*NotificationPreferenceMutation)(nil)

// notificationpreferenceOption allows management of the mutation configuration using functional options.
type notificationpreferenceOption func(*NotificationPreferenceMutation)

// newNotificationPreferenceMutation creates new mutation for the NotificationPreference entity.
func newNotificationPreferenceMutation(c config, op Op, opts ...notificationpreferenceOption) *NotificationPreferenceMutation {
	m := &NotificationPreferenceMutation{
		config:        c,
		op:            op,
		typ:           TypeNotificationPreference,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withNotificationPreferenceID sets the ID field of the mutation.
func withNotificationPreferenceID(id uuid.UUID) notificationpreferenceOption {
	return func(m *NotificationPreferenceMutation) {
		var (
			err   error
			once  sync.Once
			value *NotificationPreference
		)
		m.oldValue = func(ctx context.Context) (*NotificationPreference, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().NotificationPreference.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withNotificationPreference sets the old NotificationPreference of the mutation.
func withNotificationPreference(node *NotificationPreference) notificationpreferenceOption {
	return func(m *NotificationPreferenceMutation) {
		m.oldValue = func(context.Context) (*NotificationPreference, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m NotificationPreferenceMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m NotificationPreferenceMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of NotificationPreference entities.
func (m *NotificationPreferenceMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *NotificationPreferenceMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *NotificationPreferenceMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().NotificationPreference.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *NotificationPreferenceMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *NotificationPreferenceMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *NotificationPreferenceMutation) ResetUserID() {
	m.user_id = nil
}

// SetEmailEnabled sets the "email_enabled" field.
func (m *NotificationPreferenceMutation) SetEmailEnabled(b bool) {
	m.email_enabled = &b
}

// EmailEnabled returns the value of the "email_enabled" field in the mutation.
func (m *NotificationPreferenceMutation) EmailEnabled() (r bool, exists bool) {
	v := m.email_enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEmailEnabled returns the old "email_enabled" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldEmailEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmailEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmailEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmailEnabled: %w", err)
	}
	return oldValue.EmailEnabled, nil
}

// ResetEmailEnabled resets all changes to the "email_enabled" field.
func (m *NotificationPreferenceMutation) ResetEmailEnabled() {
	m.email_enabled = nil
}

// SetPushEnabled sets the "push_enabled" field.
func (m *NotificationPreferenceMutation) SetPushEnabled(b bool) {
	m.push_enabled = &b
}

// PushEnabled returns the value of the "push_enabled" field in the mutation.
func (m *NotificationPreferenceMutation) PushEnabled() (r bool, exists bool) {
	v := m.push_enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldPushEnabled returns the old "push_enabled" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldPushEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPushEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPushEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPushEnabled: %w", err)
	}
	return oldValue.PushEnabled, nil
}

// ResetPushEnabled resets all changes to the "push_enabled" field.
func (m *NotificationPreferenceMutation) ResetPushEnabled() {
	m.push_enabled = nil
}

// SetInAppEnabled sets the "in_app_enabled" field.
func (m *NotificationPreferenceMutation) SetInAppEnabled(b bool) {
	m.in_app_enabled = &b
}

// InAppEnabled returns the value of the "in_app_enabled" field in the mutation.
func (m *NotificationPreferenceMutation) InAppEnabled() (r bool, exists bool) {
	v := m.in_app_enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldInAppEnabled returns the old "in_app_enabled" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldInAppEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInAppEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInAppEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInAppEnabled: %w", err)
	}
	return oldValue.InAppEnabled, nil
}

// ResetInAppEnabled resets all changes to the "in_app_enabled" field.
func (m *NotificationPreferenceMutation) ResetInAppEnabled() {
	m.in_app_enabled = nil
}

// SetSmsEnabled sets the "sms_enabled" field.
func (m *NotificationPreferenceMutation) SetSmsEnabled(b bool) {
	m.sms_enabled = &b
}

// SmsEnabled returns the value of the "sms_enabled" field in the mutation.
func (m *NotificationPreferenceMutation) SmsEnabled() (r bool, exists bool) {
	v := m.sms_enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldSmsEnabled returns the old "sms_enabled" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldSmsEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSmsEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSmsEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSmsEnabled: %w", err)
	}
	return oldValue.SmsEnabled, nil
}

// ResetSmsEnabled resets all changes to the "sms_enabled" field.
func (m *NotificationPreferenceMutation) ResetSmsEnabled() {
	m.sms_enabled = nil
}

// SetNotificationTypes sets the "notification_types" field.
func (m *NotificationPreferenceMutation) SetNotificationTypes(value map[string]interface{}) {
	m.notification_types = &value
}

// NotificationTypes returns the value of the "notification_types" field in the mutation.
func (m *NotificationPreferenceMutation) NotificationTypes() (r map[string]interface{}, exists bool) {
	v := m.notification_types
	if v == nil {
		return
	}
	return *v, true
}

// OldNotificationTypes returns the old "notification_types" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldNotificationTypes(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNotificationTypes is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNotificationTypes requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNotificationTypes: %w", err)
	}
	return oldValue.NotificationTypes, nil
}

// ClearNotificationTypes clears the value of the "notification_types" field.
func (m *NotificationPreferenceMutation) ClearNotificationTypes() {
	m.notification_types = nil
	m.clearedFields[notificationpreference.FieldNotificationTypes] = struct{}{}
}

// NotificationTypesCleared returns if the "notification_types" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) NotificationTypesCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldNotificationTypes]
	return ok
}

// ResetNotificationTypes resets all changes to the "notification_types" field.
func (m *NotificationPreferenceMutation) ResetNotificationTypes() {
	m.notification_types = nil
	delete(m.clearedFields, notificationpreference.FieldNotificationTypes)
}

// SetQuietHours sets the "quiet_hours" field.
func (m *NotificationPreferenceMutation) SetQuietHours(value map[string]interface{}) {
	m.quiet_hours = &value
}

// QuietHours returns the value of the "quiet_hours" field in the mutation.
func (m *NotificationPreferenceMutation) QuietHours() (r map[string]interface{}, exists bool) {
	v := m.quiet_hours
	if v == nil {
		return
	}
	return *v, true
}

// OldQuietHours returns the old "quiet_hours" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldQuietHours(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldQuietHours is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldQuietHours requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldQuietHours: %w", err)
	}
	return oldValue.QuietHours, nil
}

// ClearQuietHours clears the value of the "quiet_hours" field.
func (m *NotificationPreferenceMutation) ClearQuietHours() {
	m.quiet_hours = nil
	m.clearedFields[notificationpreference.FieldQuietHours] = struct{}{}
}

// QuietHoursCleared returns if the "quiet_hours" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) QuietHoursCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldQuietHours]
	return ok
}

// ResetQuietHours resets all changes to the "quiet_hours" field.
func (m *NotificationPreferenceMutation) ResetQuietHours() {
	m.quiet_hours = nil
	delete(m.clearedFields, notificationpreference.FieldQuietHours)
}

// SetTimezone sets the "timezone" field.
func (m *NotificationPreferenceMutation) SetTimezone(s string) {
	m.timezone = &s
}

// Timezone returns the value of the "timezone" field in the mutation.
func (m *NotificationPreferenceMutation) Timezone() (r string, exists bool) {
	v := m.timezone
	if v == nil {
		return
	}
	return *v, true
}

// OldTimezone returns the old "timezone" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldTimezone(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTimezone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTimezone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTimezone: %w", err)
	}
	return oldValue.Timezone, nil
}

// ResetTimezone resets all changes to the "timezone" field.
func (m *NotificationPreferenceMutation) ResetTimezone() {
	m.timezone = nil
}

// SetLanguage sets the "language" field.
func (m *NotificationPreferenceMutation) SetLanguage(s string) {
	m.language = &s
}

// Language returns the value of the "language" field in the mutation.
func (m *NotificationPreferenceMutation) Language() (r string, exists bool) {
	v := m.language
	if v == nil {
		return
	}
	return *v, true
}

// OldLanguage returns the old "language" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldLanguage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLanguage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLanguage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLanguage: %w", err)
	}
	return oldValue.Language, nil
}

// ResetLanguage resets all changes to the "language" field.
func (m *NotificationPreferenceMutation) ResetLanguage() {
	m.language = nil
}

// SetEmailSettings sets the "email_settings" field.
func (m *NotificationPreferenceMutation) SetEmailSettings(value map[string]interface{}) {
	m.email_settings = &value
}

// EmailSettings returns the value of the "email_settings" field in the mutation.
func (m *NotificationPreferenceMutation) EmailSettings() (r map[string]interface{}, exists bool) {
	v := m.email_settings
	if v == nil {
		return
	}
	return *v, true
}

// OldEmailSettings returns the old "email_settings" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldEmailSettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmailSettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmailSettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmailSettings: %w", err)
	}
	return oldValue.EmailSettings, nil
}

// ClearEmailSettings clears the value of the "email_settings" field.
func (m *NotificationPreferenceMutation) ClearEmailSettings() {
	m.email_settings = nil
	m.clearedFields[notificationpreference.FieldEmailSettings] = struct{}{}
}

// EmailSettingsCleared returns if the "email_settings" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) EmailSettingsCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldEmailSettings]
	return ok
}

// ResetEmailSettings resets all changes to the "email_settings" field.
func (m *NotificationPreferenceMutation) ResetEmailSettings() {
	m.email_settings = nil
	delete(m.clearedFields, notificationpreference.FieldEmailSettings)
}

// SetPushSettings sets the "push_settings" field.
func (m *NotificationPreferenceMutation) SetPushSettings(value map[string]interface{}) {
	m.push_settings = &value
}

// PushSettings returns the value of the "push_settings" field in the mutation.
func (m *NotificationPreferenceMutation) PushSettings() (r map[string]interface{}, exists bool) {
	v := m.push_settings
	if v == nil {
		return
	}
	return *v, true
}

// OldPushSettings returns the old "push_settings" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldPushSettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPushSettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPushSettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPushSettings: %w", err)
	}
	return oldValue.PushSettings, nil
}

// ClearPushSettings clears the value of the "push_settings" field.
func (m *NotificationPreferenceMutation) ClearPushSettings() {
	m.push_settings = nil
	m.clearedFields[notificationpreference.FieldPushSettings] = struct{}{}
}

// PushSettingsCleared returns if the "push_settings" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) PushSettingsCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldPushSettings]
	return ok
}

// ResetPushSettings resets all changes to the "push_settings" field.
func (m *NotificationPreferenceMutation) ResetPushSettings() {
	m.push_settings = nil
	delete(m.clearedFields, notificationpreference.FieldPushSettings)
}

// SetFrequencySettings sets the "frequency_settings" field.
func (m *NotificationPreferenceMutation) SetFrequencySettings(value map[string]interface{}) {
	m.frequency_settings = &value
}

// FrequencySettings returns the value of the "frequency_settings" field in the mutation.
func (m *NotificationPreferenceMutation) FrequencySettings() (r map[string]interface{}, exists bool) {
	v := m.frequency_settings
	if v == nil {
		return
	}
	return *v, true
}

// OldFrequencySettings returns the old "frequency_settings" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldFrequencySettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrequencySettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrequencySettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrequencySettings: %w", err)
	}
	return oldValue.FrequencySettings, nil
}

// ClearFrequencySettings clears the value of the "frequency_settings" field.
func (m *NotificationPreferenceMutation) ClearFrequencySettings() {
	m.frequency_settings = nil
	m.clearedFields[notificationpreference.FieldFrequencySettings] = struct{}{}
}

// FrequencySettingsCleared returns if the "frequency_settings" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) FrequencySettingsCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldFrequencySettings]
	return ok
}

// ResetFrequencySettings resets all changes to the "frequency_settings" field.
func (m *NotificationPreferenceMutation) ResetFrequencySettings() {
	m.frequency_settings = nil
	delete(m.clearedFields, notificationpreference.FieldFrequencySettings)
}

// SetMarketingEmails sets the "marketing_emails" field.
func (m *NotificationPreferenceMutation) SetMarketingEmails(b bool) {
	m.marketing_emails = &b
}

// MarketingEmails returns the value of the "marketing_emails" field in the mutation.
func (m *NotificationPreferenceMutation) MarketingEmails() (r bool, exists bool) {
	v := m.marketing_emails
	if v == nil {
		return
	}
	return *v, true
}

// OldMarketingEmails returns the old "marketing_emails" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldMarketingEmails(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMarketingEmails is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMarketingEmails requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMarketingEmails: %w", err)
	}
	return oldValue.MarketingEmails, nil
}

// ResetMarketingEmails resets all changes to the "marketing_emails" field.
func (m *NotificationPreferenceMutation) ResetMarketingEmails() {
	m.marketing_emails = nil
}

// SetProductUpdates sets the "product_updates" field.
func (m *NotificationPreferenceMutation) SetProductUpdates(b bool) {
	m.product_updates = &b
}

// ProductUpdates returns the value of the "product_updates" field in the mutation.
func (m *NotificationPreferenceMutation) ProductUpdates() (r bool, exists bool) {
	v := m.product_updates
	if v == nil {
		return
	}
	return *v, true
}

// OldProductUpdates returns the old "product_updates" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldProductUpdates(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProductUpdates is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProductUpdates requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProductUpdates: %w", err)
	}
	return oldValue.ProductUpdates, nil
}

// ResetProductUpdates resets all changes to the "product_updates" field.
func (m *NotificationPreferenceMutation) ResetProductUpdates() {
	m.product_updates = nil
}

// SetSecurityAlerts sets the "security_alerts" field.
func (m *NotificationPreferenceMutation) SetSecurityAlerts(b bool) {
	m.security_alerts = &b
}

// SecurityAlerts returns the value of the "security_alerts" field in the mutation.
func (m *NotificationPreferenceMutation) SecurityAlerts() (r bool, exists bool) {
	v := m.security_alerts
	if v == nil {
		return
	}
	return *v, true
}

// OldSecurityAlerts returns the old "security_alerts" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldSecurityAlerts(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSecurityAlerts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSecurityAlerts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSecurityAlerts: %w", err)
	}
	return oldValue.SecurityAlerts, nil
}

// ResetSecurityAlerts resets all changes to the "security_alerts" field.
func (m *NotificationPreferenceMutation) ResetSecurityAlerts() {
	m.security_alerts = nil
}

// SetMetadata sets the "metadata" field.
func (m *NotificationPreferenceMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *NotificationPreferenceMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *NotificationPreferenceMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[notificationpreference.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *NotificationPreferenceMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[notificationpreference.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *NotificationPreferenceMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, notificationpreference.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *NotificationPreferenceMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *NotificationPreferenceMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *NotificationPreferenceMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *NotificationPreferenceMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *NotificationPreferenceMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the NotificationPreference entity.
// If the NotificationPreference object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationPreferenceMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *NotificationPreferenceMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// Where appends a list predicates to the NotificationPreferenceMutation builder.
func (m *NotificationPreferenceMutation) Where(ps ...predicate.NotificationPreference) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the NotificationPreferenceMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *NotificationPreferenceMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.NotificationPreference, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *NotificationPreferenceMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *NotificationPreferenceMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (NotificationPreference).
func (m *NotificationPreferenceMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *NotificationPreferenceMutation) Fields() []string {
	fields := make([]string, 0, 18)
	if m.user_id != nil {
		fields = append(fields, notificationpreference.FieldUserID)
	}
	if m.email_enabled != nil {
		fields = append(fields, notificationpreference.FieldEmailEnabled)
	}
	if m.push_enabled != nil {
		fields = append(fields, notificationpreference.FieldPushEnabled)
	}
	if m.in_app_enabled != nil {
		fields = append(fields, notificationpreference.FieldInAppEnabled)
	}
	if m.sms_enabled != nil {
		fields = append(fields, notificationpreference.FieldSmsEnabled)
	}
	if m.notification_types != nil {
		fields = append(fields, notificationpreference.FieldNotificationTypes)
	}
	if m.quiet_hours != nil {
		fields = append(fields, notificationpreference.FieldQuietHours)
	}
	if m.timezone != nil {
		fields = append(fields, notificationpreference.FieldTimezone)
	}
	if m.language != nil {
		fields = append(fields, notificationpreference.FieldLanguage)
	}
	if m.email_settings != nil {
		fields = append(fields, notificationpreference.FieldEmailSettings)
	}
	if m.push_settings != nil {
		fields = append(fields, notificationpreference.FieldPushSettings)
	}
	if m.frequency_settings != nil {
		fields = append(fields, notificationpreference.FieldFrequencySettings)
	}
	if m.marketing_emails != nil {
		fields = append(fields, notificationpreference.FieldMarketingEmails)
	}
	if m.product_updates != nil {
		fields = append(fields, notificationpreference.FieldProductUpdates)
	}
	if m.security_alerts != nil {
		fields = append(fields, notificationpreference.FieldSecurityAlerts)
	}
	if m.metadata != nil {
		fields = append(fields, notificationpreference.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, notificationpreference.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, notificationpreference.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *NotificationPreferenceMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case notificationpreference.FieldUserID:
		return m.UserID()
	case notificationpreference.FieldEmailEnabled:
		return m.EmailEnabled()
	case notificationpreference.FieldPushEnabled:
		return m.PushEnabled()
	case notificationpreference.FieldInAppEnabled:
		return m.InAppEnabled()
	case notificationpreference.FieldSmsEnabled:
		return m.SmsEnabled()
	case notificationpreference.FieldNotificationTypes:
		return m.NotificationTypes()
	case notificationpreference.FieldQuietHours:
		return m.QuietHours()
	case notificationpreference.FieldTimezone:
		return m.Timezone()
	case notificationpreference.FieldLanguage:
		return m.Language()
	case notificationpreference.FieldEmailSettings:
		return m.EmailSettings()
	case notificationpreference.FieldPushSettings:
		return m.PushSettings()
	case notificationpreference.FieldFrequencySettings:
		return m.FrequencySettings()
	case notificationpreference.FieldMarketingEmails:
		return m.MarketingEmails()
	case notificationpreference.FieldProductUpdates:
		return m.ProductUpdates()
	case notificationpreference.FieldSecurityAlerts:
		return m.SecurityAlerts()
	case notificationpreference.FieldMetadata:
		return m.Metadata()
	case notificationpreference.FieldCreatedAt:
		return m.CreatedAt()
	case notificationpreference.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *NotificationPreferenceMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case notificationpreference.FieldUserID:
		return m.OldUserID(ctx)
	case notificationpreference.FieldEmailEnabled:
		return m.OldEmailEnabled(ctx)
	case notificationpreference.FieldPushEnabled:
		return m.OldPushEnabled(ctx)
	case notificationpreference.FieldInAppEnabled:
		return m.OldInAppEnabled(ctx)
	case notificationpreference.FieldSmsEnabled:
		return m.OldSmsEnabled(ctx)
	case notificationpreference.FieldNotificationTypes:
		return m.OldNotificationTypes(ctx)
	case notificationpreference.FieldQuietHours:
		return m.OldQuietHours(ctx)
	case notificationpreference.FieldTimezone:
		return m.OldTimezone(ctx)
	case notificationpreference.FieldLanguage:
		return m.OldLanguage(ctx)
	case notificationpreference.FieldEmailSettings:
		return m.OldEmailSettings(ctx)
	case notificationpreference.FieldPushSettings:
		return m.OldPushSettings(ctx)
	case notificationpreference.FieldFrequencySettings:
		return m.OldFrequencySettings(ctx)
	case notificationpreference.FieldMarketingEmails:
		return m.OldMarketingEmails(ctx)
	case notificationpreference.FieldProductUpdates:
		return m.OldProductUpdates(ctx)
	case notificationpreference.FieldSecurityAlerts:
		return m.OldSecurityAlerts(ctx)
	case notificationpreference.FieldMetadata:
		return m.OldMetadata(ctx)
	case notificationpreference.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case notificationpreference.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown NotificationPreference field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationPreferenceMutation) SetField(name string, value ent.Value) error {
	switch name {
	case notificationpreference.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case notificationpreference.FieldEmailEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmailEnabled(v)
		return nil
	case notificationpreference.FieldPushEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPushEnabled(v)
		return nil
	case notificationpreference.FieldInAppEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInAppEnabled(v)
		return nil
	case notificationpreference.FieldSmsEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSmsEnabled(v)
		return nil
	case notificationpreference.FieldNotificationTypes:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNotificationTypes(v)
		return nil
	case notificationpreference.FieldQuietHours:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetQuietHours(v)
		return nil
	case notificationpreference.FieldTimezone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTimezone(v)
		return nil
	case notificationpreference.FieldLanguage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLanguage(v)
		return nil
	case notificationpreference.FieldEmailSettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmailSettings(v)
		return nil
	case notificationpreference.FieldPushSettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPushSettings(v)
		return nil
	case notificationpreference.FieldFrequencySettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrequencySettings(v)
		return nil
	case notificationpreference.FieldMarketingEmails:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMarketingEmails(v)
		return nil
	case notificationpreference.FieldProductUpdates:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProductUpdates(v)
		return nil
	case notificationpreference.FieldSecurityAlerts:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSecurityAlerts(v)
		return nil
	case notificationpreference.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case notificationpreference.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case notificationpreference.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown NotificationPreference field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *NotificationPreferenceMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *NotificationPreferenceMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationPreferenceMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown NotificationPreference numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *NotificationPreferenceMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(notificationpreference.FieldNotificationTypes) {
		fields = append(fields, notificationpreference.FieldNotificationTypes)
	}
	if m.FieldCleared(notificationpreference.FieldQuietHours) {
		fields = append(fields, notificationpreference.FieldQuietHours)
	}
	if m.FieldCleared(notificationpreference.FieldEmailSettings) {
		fields = append(fields, notificationpreference.FieldEmailSettings)
	}
	if m.FieldCleared(notificationpreference.FieldPushSettings) {
		fields = append(fields, notificationpreference.FieldPushSettings)
	}
	if m.FieldCleared(notificationpreference.FieldFrequencySettings) {
		fields = append(fields, notificationpreference.FieldFrequencySettings)
	}
	if m.FieldCleared(notificationpreference.FieldMetadata) {
		fields = append(fields, notificationpreference.FieldMetadata)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *NotificationPreferenceMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *NotificationPreferenceMutation) ClearField(name string) error {
	switch name {
	case notificationpreference.FieldNotificationTypes:
		m.ClearNotificationTypes()
		return nil
	case notificationpreference.FieldQuietHours:
		m.ClearQuietHours()
		return nil
	case notificationpreference.FieldEmailSettings:
		m.ClearEmailSettings()
		return nil
	case notificationpreference.FieldPushSettings:
		m.ClearPushSettings()
		return nil
	case notificationpreference.FieldFrequencySettings:
		m.ClearFrequencySettings()
		return nil
	case notificationpreference.FieldMetadata:
		m.ClearMetadata()
		return nil
	}
	return fmt.Errorf("unknown NotificationPreference nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *NotificationPreferenceMutation) ResetField(name string) error {
	switch name {
	case notificationpreference.FieldUserID:
		m.ResetUserID()
		return nil
	case notificationpreference.FieldEmailEnabled:
		m.ResetEmailEnabled()
		return nil
	case notificationpreference.FieldPushEnabled:
		m.ResetPushEnabled()
		return nil
	case notificationpreference.FieldInAppEnabled:
		m.ResetInAppEnabled()
		return nil
	case notificationpreference.FieldSmsEnabled:
		m.ResetSmsEnabled()
		return nil
	case notificationpreference.FieldNotificationTypes:
		m.ResetNotificationTypes()
		return nil
	case notificationpreference.FieldQuietHours:
		m.ResetQuietHours()
		return nil
	case notificationpreference.FieldTimezone:
		m.ResetTimezone()
		return nil
	case notificationpreference.FieldLanguage:
		m.ResetLanguage()
		return nil
	case notificationpreference.FieldEmailSettings:
		m.ResetEmailSettings()
		return nil
	case notificationpreference.FieldPushSettings:
		m.ResetPushSettings()
		return nil
	case notificationpreference.FieldFrequencySettings:
		m.ResetFrequencySettings()
		return nil
	case notificationpreference.FieldMarketingEmails:
		m.ResetMarketingEmails()
		return nil
	case notificationpreference.FieldProductUpdates:
		m.ResetProductUpdates()
		return nil
	case notificationpreference.FieldSecurityAlerts:
		m.ResetSecurityAlerts()
		return nil
	case notificationpreference.FieldMetadata:
		m.ResetMetadata()
		return nil
	case notificationpreference.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case notificationpreference.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown NotificationPreference field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *NotificationPreferenceMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *NotificationPreferenceMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *NotificationPreferenceMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *NotificationPreferenceMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *NotificationPreferenceMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *NotificationPreferenceMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *NotificationPreferenceMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown NotificationPreference unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *NotificationPreferenceMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown NotificationPreference edge %s", name)
}

// NotificationTemplateMutation represents an operation that mutates the NotificationTemplate nodes in the graph.
type NotificationTemplateMutation struct {
	config
	op                     Op
	typ                    string
	id                     *uuid.UUID
	event_type             *string
	name                   *string
	description            *string
	templates              *map[string]interface{}
	is_active              *bool
	variables              *[]string
	appendvariables        []string
	default_channels       *[]string
	appenddefault_channels []string
	default_priority       *notificationtemplate.DefaultPriority
	language               *string
	version                *string
	metadata               *map[string]interface{}
	created_by             *string
	created_at             *time.Time
	updated_at             *time.Time
	deleted_at             *time.Time
	clearedFields          map[string]struct{}
	done                   bool
	oldValue               func(context.Context) (*NotificationTemplate, error)
	predicates             []predicate.NotificationTemplate
}

var _ ent.Mutation = (*NotificationTemplateMutation)(nil)

// notificationtemplateOption allows management of the mutation configuration using functional options.
type notificationtemplateOption func(*NotificationTemplateMutation)

// newNotificationTemplateMutation creates new mutation for the NotificationTemplate entity.
func newNotificationTemplateMutation(c config, op Op, opts ...notificationtemplateOption) *NotificationTemplateMutation {
	m := &NotificationTemplateMutation{
		config:        c,
		op:            op,
		typ:           TypeNotificationTemplate,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withNotificationTemplateID sets the ID field of the mutation.
func withNotificationTemplateID(id uuid.UUID) notificationtemplateOption {
	return func(m *NotificationTemplateMutation) {
		var (
			err   error
			once  sync.Once
			value *NotificationTemplate
		)
		m.oldValue = func(ctx context.Context) (*NotificationTemplate, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().NotificationTemplate.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withNotificationTemplate sets the old NotificationTemplate of the mutation.
func withNotificationTemplate(node *NotificationTemplate) notificationtemplateOption {
	return func(m *NotificationTemplateMutation) {
		m.oldValue = func(context.Context) (*NotificationTemplate, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m NotificationTemplateMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m NotificationTemplateMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of NotificationTemplate entities.
func (m *NotificationTemplateMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *NotificationTemplateMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *NotificationTemplateMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().NotificationTemplate.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetEventType sets the "event_type" field.
func (m *NotificationTemplateMutation) SetEventType(s string) {
	m.event_type = &s
}

// EventType returns the value of the "event_type" field in the mutation.
func (m *NotificationTemplateMutation) EventType() (r string, exists bool) {
	v := m.event_type
	if v == nil {
		return
	}
	return *v, true
}

// OldEventType returns the old "event_type" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldEventType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEventType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEventType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEventType: %w", err)
	}
	return oldValue.EventType, nil
}

// ResetEventType resets all changes to the "event_type" field.
func (m *NotificationTemplateMutation) ResetEventType() {
	m.event_type = nil
}

// SetName sets the "name" field.
func (m *NotificationTemplateMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *NotificationTemplateMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *NotificationTemplateMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *NotificationTemplateMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *NotificationTemplateMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *NotificationTemplateMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[notificationtemplate.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *NotificationTemplateMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *NotificationTemplateMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, notificationtemplate.FieldDescription)
}

// SetTemplates sets the "templates" field.
func (m *NotificationTemplateMutation) SetTemplates(value map[string]interface{}) {
	m.templates = &value
}

// Templates returns the value of the "templates" field in the mutation.
func (m *NotificationTemplateMutation) Templates() (r map[string]interface{}, exists bool) {
	v := m.templates
	if v == nil {
		return
	}
	return *v, true
}

// OldTemplates returns the old "templates" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldTemplates(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTemplates is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTemplates requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTemplates: %w", err)
	}
	return oldValue.Templates, nil
}

// ResetTemplates resets all changes to the "templates" field.
func (m *NotificationTemplateMutation) ResetTemplates() {
	m.templates = nil
}

// SetIsActive sets the "is_active" field.
func (m *NotificationTemplateMutation) SetIsActive(b bool) {
	m.is_active = &b
}

// IsActive returns the value of the "is_active" field in the mutation.
func (m *NotificationTemplateMutation) IsActive() (r bool, exists bool) {
	v := m.is_active
	if v == nil {
		return
	}
	return *v, true
}

// OldIsActive returns the old "is_active" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldIsActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsActive: %w", err)
	}
	return oldValue.IsActive, nil
}

// ResetIsActive resets all changes to the "is_active" field.
func (m *NotificationTemplateMutation) ResetIsActive() {
	m.is_active = nil
}

// SetVariables sets the "variables" field.
func (m *NotificationTemplateMutation) SetVariables(s []string) {
	m.variables = &s
	m.appendvariables = nil
}

// Variables returns the value of the "variables" field in the mutation.
func (m *NotificationTemplateMutation) Variables() (r []string, exists bool) {
	v := m.variables
	if v == nil {
		return
	}
	return *v, true
}

// OldVariables returns the old "variables" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldVariables(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVariables is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVariables requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVariables: %w", err)
	}
	return oldValue.Variables, nil
}

// AppendVariables adds s to the "variables" field.
func (m *NotificationTemplateMutation) AppendVariables(s []string) {
	m.appendvariables = append(m.appendvariables, s...)
}

// AppendedVariables returns the list of values that were appended to the "variables" field in this mutation.
func (m *NotificationTemplateMutation) AppendedVariables() ([]string, bool) {
	if len(m.appendvariables) == 0 {
		return nil, false
	}
	return m.appendvariables, true
}

// ClearVariables clears the value of the "variables" field.
func (m *NotificationTemplateMutation) ClearVariables() {
	m.variables = nil
	m.appendvariables = nil
	m.clearedFields[notificationtemplate.FieldVariables] = struct{}{}
}

// VariablesCleared returns if the "variables" field was cleared in this mutation.
func (m *NotificationTemplateMutation) VariablesCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldVariables]
	return ok
}

// ResetVariables resets all changes to the "variables" field.
func (m *NotificationTemplateMutation) ResetVariables() {
	m.variables = nil
	m.appendvariables = nil
	delete(m.clearedFields, notificationtemplate.FieldVariables)
}

// SetDefaultChannels sets the "default_channels" field.
func (m *NotificationTemplateMutation) SetDefaultChannels(s []string) {
	m.default_channels = &s
	m.appenddefault_channels = nil
}

// DefaultChannels returns the value of the "default_channels" field in the mutation.
func (m *NotificationTemplateMutation) DefaultChannels() (r []string, exists bool) {
	v := m.default_channels
	if v == nil {
		return
	}
	return *v, true
}

// OldDefaultChannels returns the old "default_channels" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldDefaultChannels(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDefaultChannels is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDefaultChannels requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDefaultChannels: %w", err)
	}
	return oldValue.DefaultChannels, nil
}

// AppendDefaultChannels adds s to the "default_channels" field.
func (m *NotificationTemplateMutation) AppendDefaultChannels(s []string) {
	m.appenddefault_channels = append(m.appenddefault_channels, s...)
}

// AppendedDefaultChannels returns the list of values that were appended to the "default_channels" field in this mutation.
func (m *NotificationTemplateMutation) AppendedDefaultChannels() ([]string, bool) {
	if len(m.appenddefault_channels) == 0 {
		return nil, false
	}
	return m.appenddefault_channels, true
}

// ClearDefaultChannels clears the value of the "default_channels" field.
func (m *NotificationTemplateMutation) ClearDefaultChannels() {
	m.default_channels = nil
	m.appenddefault_channels = nil
	m.clearedFields[notificationtemplate.FieldDefaultChannels] = struct{}{}
}

// DefaultChannelsCleared returns if the "default_channels" field was cleared in this mutation.
func (m *NotificationTemplateMutation) DefaultChannelsCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldDefaultChannels]
	return ok
}

// ResetDefaultChannels resets all changes to the "default_channels" field.
func (m *NotificationTemplateMutation) ResetDefaultChannels() {
	m.default_channels = nil
	m.appenddefault_channels = nil
	delete(m.clearedFields, notificationtemplate.FieldDefaultChannels)
}

// SetDefaultPriority sets the "default_priority" field.
func (m *NotificationTemplateMutation) SetDefaultPriority(np notificationtemplate.DefaultPriority) {
	m.default_priority = &np
}

// DefaultPriority returns the value of the "default_priority" field in the mutation.
func (m *NotificationTemplateMutation) DefaultPriority() (r notificationtemplate.DefaultPriority, exists bool) {
	v := m.default_priority
	if v == nil {
		return
	}
	return *v, true
}

// OldDefaultPriority returns the old "default_priority" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldDefaultPriority(ctx context.Context) (v notificationtemplate.DefaultPriority, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDefaultPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDefaultPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDefaultPriority: %w", err)
	}
	return oldValue.DefaultPriority, nil
}

// ResetDefaultPriority resets all changes to the "default_priority" field.
func (m *NotificationTemplateMutation) ResetDefaultPriority() {
	m.default_priority = nil
}

// SetLanguage sets the "language" field.
func (m *NotificationTemplateMutation) SetLanguage(s string) {
	m.language = &s
}

// Language returns the value of the "language" field in the mutation.
func (m *NotificationTemplateMutation) Language() (r string, exists bool) {
	v := m.language
	if v == nil {
		return
	}
	return *v, true
}

// OldLanguage returns the old "language" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldLanguage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLanguage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLanguage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLanguage: %w", err)
	}
	return oldValue.Language, nil
}

// ResetLanguage resets all changes to the "language" field.
func (m *NotificationTemplateMutation) ResetLanguage() {
	m.language = nil
}

// SetVersion sets the "version" field.
func (m *NotificationTemplateMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *NotificationTemplateMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *NotificationTemplateMutation) ResetVersion() {
	m.version = nil
}

// SetMetadata sets the "metadata" field.
func (m *NotificationTemplateMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *NotificationTemplateMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *NotificationTemplateMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[notificationtemplate.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *NotificationTemplateMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *NotificationTemplateMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, notificationtemplate.FieldMetadata)
}

// SetCreatedBy sets the "created_by" field.
func (m *NotificationTemplateMutation) SetCreatedBy(s string) {
	m.created_by = &s
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *NotificationTemplateMutation) CreatedBy() (r string, exists bool) {
	v := m.created_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldCreatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ClearCreatedBy clears the value of the "created_by" field.
func (m *NotificationTemplateMutation) ClearCreatedBy() {
	m.created_by = nil
	m.clearedFields[notificationtemplate.FieldCreatedBy] = struct{}{}
}

// CreatedByCleared returns if the "created_by" field was cleared in this mutation.
func (m *NotificationTemplateMutation) CreatedByCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldCreatedBy]
	return ok
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *NotificationTemplateMutation) ResetCreatedBy() {
	m.created_by = nil
	delete(m.clearedFields, notificationtemplate.FieldCreatedBy)
}

// SetCreatedAt sets the "created_at" field.
func (m *NotificationTemplateMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *NotificationTemplateMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *NotificationTemplateMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *NotificationTemplateMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *NotificationTemplateMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *NotificationTemplateMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *NotificationTemplateMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *NotificationTemplateMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the NotificationTemplate entity.
// If the NotificationTemplate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NotificationTemplateMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *NotificationTemplateMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[notificationtemplate.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *NotificationTemplateMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[notificationtemplate.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *NotificationTemplateMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, notificationtemplate.FieldDeletedAt)
}

// Where appends a list predicates to the NotificationTemplateMutation builder.
func (m *NotificationTemplateMutation) Where(ps ...predicate.NotificationTemplate) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the NotificationTemplateMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *NotificationTemplateMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.NotificationTemplate, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *NotificationTemplateMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *NotificationTemplateMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (NotificationTemplate).
func (m *NotificationTemplateMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *NotificationTemplateMutation) Fields() []string {
	fields := make([]string, 0, 15)
	if m.event_type != nil {
		fields = append(fields, notificationtemplate.FieldEventType)
	}
	if m.name != nil {
		fields = append(fields, notificationtemplate.FieldName)
	}
	if m.description != nil {
		fields = append(fields, notificationtemplate.FieldDescription)
	}
	if m.templates != nil {
		fields = append(fields, notificationtemplate.FieldTemplates)
	}
	if m.is_active != nil {
		fields = append(fields, notificationtemplate.FieldIsActive)
	}
	if m.variables != nil {
		fields = append(fields, notificationtemplate.FieldVariables)
	}
	if m.default_channels != nil {
		fields = append(fields, notificationtemplate.FieldDefaultChannels)
	}
	if m.default_priority != nil {
		fields = append(fields, notificationtemplate.FieldDefaultPriority)
	}
	if m.language != nil {
		fields = append(fields, notificationtemplate.FieldLanguage)
	}
	if m.version != nil {
		fields = append(fields, notificationtemplate.FieldVersion)
	}
	if m.metadata != nil {
		fields = append(fields, notificationtemplate.FieldMetadata)
	}
	if m.created_by != nil {
		fields = append(fields, notificationtemplate.FieldCreatedBy)
	}
	if m.created_at != nil {
		fields = append(fields, notificationtemplate.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, notificationtemplate.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, notificationtemplate.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *NotificationTemplateMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case notificationtemplate.FieldEventType:
		return m.EventType()
	case notificationtemplate.FieldName:
		return m.Name()
	case notificationtemplate.FieldDescription:
		return m.Description()
	case notificationtemplate.FieldTemplates:
		return m.Templates()
	case notificationtemplate.FieldIsActive:
		return m.IsActive()
	case notificationtemplate.FieldVariables:
		return m.Variables()
	case notificationtemplate.FieldDefaultChannels:
		return m.DefaultChannels()
	case notificationtemplate.FieldDefaultPriority:
		return m.DefaultPriority()
	case notificationtemplate.FieldLanguage:
		return m.Language()
	case notificationtemplate.FieldVersion:
		return m.Version()
	case notificationtemplate.FieldMetadata:
		return m.Metadata()
	case notificationtemplate.FieldCreatedBy:
		return m.CreatedBy()
	case notificationtemplate.FieldCreatedAt:
		return m.CreatedAt()
	case notificationtemplate.FieldUpdatedAt:
		return m.UpdatedAt()
	case notificationtemplate.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *NotificationTemplateMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case notificationtemplate.FieldEventType:
		return m.OldEventType(ctx)
	case notificationtemplate.FieldName:
		return m.OldName(ctx)
	case notificationtemplate.FieldDescription:
		return m.OldDescription(ctx)
	case notificationtemplate.FieldTemplates:
		return m.OldTemplates(ctx)
	case notificationtemplate.FieldIsActive:
		return m.OldIsActive(ctx)
	case notificationtemplate.FieldVariables:
		return m.OldVariables(ctx)
	case notificationtemplate.FieldDefaultChannels:
		return m.OldDefaultChannels(ctx)
	case notificationtemplate.FieldDefaultPriority:
		return m.OldDefaultPriority(ctx)
	case notificationtemplate.FieldLanguage:
		return m.OldLanguage(ctx)
	case notificationtemplate.FieldVersion:
		return m.OldVersion(ctx)
	case notificationtemplate.FieldMetadata:
		return m.OldMetadata(ctx)
	case notificationtemplate.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case notificationtemplate.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case notificationtemplate.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case notificationtemplate.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown NotificationTemplate field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationTemplateMutation) SetField(name string, value ent.Value) error {
	switch name {
	case notificationtemplate.FieldEventType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEventType(v)
		return nil
	case notificationtemplate.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case notificationtemplate.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case notificationtemplate.FieldTemplates:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTemplates(v)
		return nil
	case notificationtemplate.FieldIsActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsActive(v)
		return nil
	case notificationtemplate.FieldVariables:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVariables(v)
		return nil
	case notificationtemplate.FieldDefaultChannels:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDefaultChannels(v)
		return nil
	case notificationtemplate.FieldDefaultPriority:
		v, ok := value.(notificationtemplate.DefaultPriority)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDefaultPriority(v)
		return nil
	case notificationtemplate.FieldLanguage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLanguage(v)
		return nil
	case notificationtemplate.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case notificationtemplate.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case notificationtemplate.FieldCreatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case notificationtemplate.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case notificationtemplate.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case notificationtemplate.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown NotificationTemplate field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *NotificationTemplateMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *NotificationTemplateMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NotificationTemplateMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown NotificationTemplate numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *NotificationTemplateMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(notificationtemplate.FieldDescription) {
		fields = append(fields, notificationtemplate.FieldDescription)
	}
	if m.FieldCleared(notificationtemplate.FieldVariables) {
		fields = append(fields, notificationtemplate.FieldVariables)
	}
	if m.FieldCleared(notificationtemplate.FieldDefaultChannels) {
		fields = append(fields, notificationtemplate.FieldDefaultChannels)
	}
	if m.FieldCleared(notificationtemplate.FieldMetadata) {
		fields = append(fields, notificationtemplate.FieldMetadata)
	}
	if m.FieldCleared(notificationtemplate.FieldCreatedBy) {
		fields = append(fields, notificationtemplate.FieldCreatedBy)
	}
	if m.FieldCleared(notificationtemplate.FieldDeletedAt) {
		fields = append(fields, notificationtemplate.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *NotificationTemplateMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *NotificationTemplateMutation) ClearField(name string) error {
	switch name {
	case notificationtemplate.FieldDescription:
		m.ClearDescription()
		return nil
	case notificationtemplate.FieldVariables:
		m.ClearVariables()
		return nil
	case notificationtemplate.FieldDefaultChannels:
		m.ClearDefaultChannels()
		return nil
	case notificationtemplate.FieldMetadata:
		m.ClearMetadata()
		return nil
	case notificationtemplate.FieldCreatedBy:
		m.ClearCreatedBy()
		return nil
	case notificationtemplate.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown NotificationTemplate nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *NotificationTemplateMutation) ResetField(name string) error {
	switch name {
	case notificationtemplate.FieldEventType:
		m.ResetEventType()
		return nil
	case notificationtemplate.FieldName:
		m.ResetName()
		return nil
	case notificationtemplate.FieldDescription:
		m.ResetDescription()
		return nil
	case notificationtemplate.FieldTemplates:
		m.ResetTemplates()
		return nil
	case notificationtemplate.FieldIsActive:
		m.ResetIsActive()
		return nil
	case notificationtemplate.FieldVariables:
		m.ResetVariables()
		return nil
	case notificationtemplate.FieldDefaultChannels:
		m.ResetDefaultChannels()
		return nil
	case notificationtemplate.FieldDefaultPriority:
		m.ResetDefaultPriority()
		return nil
	case notificationtemplate.FieldLanguage:
		m.ResetLanguage()
		return nil
	case notificationtemplate.FieldVersion:
		m.ResetVersion()
		return nil
	case notificationtemplate.FieldMetadata:
		m.ResetMetadata()
		return nil
	case notificationtemplate.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case notificationtemplate.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case notificationtemplate.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case notificationtemplate.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown NotificationTemplate field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *NotificationTemplateMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *NotificationTemplateMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *NotificationTemplateMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *NotificationTemplateMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *NotificationTemplateMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *NotificationTemplateMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *NotificationTemplateMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown NotificationTemplate unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *NotificationTemplateMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown NotificationTemplate edge %s", name)
}

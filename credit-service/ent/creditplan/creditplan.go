// Code generated by ent, DO NOT EDIT.

package creditplan

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the creditplan type in the database.
	Label = "credit_plan"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDisplayName holds the string denoting the display_name field in the database.
	FieldDisplayName = "display_name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldCredits holds the string denoting the credits field in the database.
	FieldCredits = "credits"
	// FieldPrice holds the string denoting the price field in the database.
	FieldPrice = "price"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldDiscountPercentage holds the string denoting the discount_percentage field in the database.
	FieldDiscountPercentage = "discount_percentage"
	// FieldIsPopular holds the string denoting the is_popular field in the database.
	FieldIsPopular = "is_popular"
	// FieldIsActive holds the string denoting the is_active field in the database.
	FieldIsActive = "is_active"
	// FieldFeatures holds the string denoting the features field in the database.
	FieldFeatures = "features"
	// FieldValidUntil holds the string denoting the valid_until field in the database.
	FieldValidUntil = "valid_until"
	// FieldSortOrder holds the string denoting the sort_order field in the database.
	FieldSortOrder = "sort_order"
	// FieldStripePriceID holds the string denoting the stripe_price_id field in the database.
	FieldStripePriceID = "stripe_price_id"
	// FieldStripeProductID holds the string denoting the stripe_product_id field in the database.
	FieldStripeProductID = "stripe_product_id"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the creditplan in the database.
	Table = "credit_plans"
)

// Columns holds all SQL columns for creditplan fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldDisplayName,
	FieldDescription,
	FieldCredits,
	FieldPrice,
	FieldCurrency,
	FieldDiscountPercentage,
	FieldIsPopular,
	FieldIsActive,
	FieldFeatures,
	FieldValidUntil,
	FieldSortOrder,
	FieldStripePriceID,
	FieldStripeProductID,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DisplayNameValidator is a validator for the "display_name" field. It is called by the builders before save.
	DisplayNameValidator func(string) error
	// CreditsValidator is a validator for the "credits" field. It is called by the builders before save.
	CreditsValidator func(int) error
	// PriceValidator is a validator for the "price" field. It is called by the builders before save.
	PriceValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// DefaultDiscountPercentage holds the default value on creation for the "discount_percentage" field.
	DefaultDiscountPercentage int
	// DiscountPercentageValidator is a validator for the "discount_percentage" field. It is called by the builders before save.
	DiscountPercentageValidator func(int) error
	// DefaultIsPopular holds the default value on creation for the "is_popular" field.
	DefaultIsPopular bool
	// DefaultIsActive holds the default value on creation for the "is_active" field.
	DefaultIsActive bool
	// DefaultSortOrder holds the default value on creation for the "sort_order" field.
	DefaultSortOrder int
	// StripePriceIDValidator is a validator for the "stripe_price_id" field. It is called by the builders before save.
	StripePriceIDValidator func(string) error
	// StripeProductIDValidator is a validator for the "stripe_product_id" field. It is called by the builders before save.
	StripeProductIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the CreditPlan queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDisplayName orders the results by the display_name field.
func ByDisplayName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDisplayName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByCredits orders the results by the credits field.
func ByCredits(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCredits, opts...).ToFunc()
}

// ByPrice orders the results by the price field.
func ByPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrice, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByDiscountPercentage orders the results by the discount_percentage field.
func ByDiscountPercentage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDiscountPercentage, opts...).ToFunc()
}

// ByIsPopular orders the results by the is_popular field.
func ByIsPopular(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPopular, opts...).ToFunc()
}

// ByIsActive orders the results by the is_active field.
func ByIsActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsActive, opts...).ToFunc()
}

// ByValidUntil orders the results by the valid_until field.
func ByValidUntil(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldValidUntil, opts...).ToFunc()
}

// BySortOrder orders the results by the sort_order field.
func BySortOrder(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSortOrder, opts...).ToFunc()
}

// ByStripePriceID orders the results by the stripe_price_id field.
func ByStripePriceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStripePriceID, opts...).ToFunc()
}

// ByStripeProductID orders the results by the stripe_product_id field.
func ByStripeProductID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStripeProductID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

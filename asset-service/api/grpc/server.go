package grpc

import (
	"context"
	"fmt"
	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/asset-service/api/grpc/handlers"
	"github.com/social-content-ai/asset-service/usecase/asset"
	"github.com/social-content-ai/pkg-shared/logging"
	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
)

// Server represents the gRPC server
type Server struct {
	server       *grpc.Server
	assetUseCase asset.UseCase
	logger       logging.Logger
	port         int
}

// NewServer creates a new gRPC server
func NewServer(assetUseCase asset.UseCase, logger logging.Logger, port int) *Server {
	return &Server{
		assetUseCase: assetUseCase,
		logger:       logger,
		port:         port,
	}
}

// Start starts the gRPC server
func (s *Server) Start() error {
	// Create listener
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", s.port))
	if err != nil {
		return fmt.Errorf("failed to listen on port %d: %w", s.port, err)
	}

	// Create gRPC server with options
	opts := []grpc.ServerOption{
		grpc.UnaryInterceptor(s.loggingInterceptor),
	}

	s.server = grpc.NewServer(opts...)

	// Register services
	assetHandler := handlers.NewAssetHandler(s.assetUseCase, s.logger)
	assetv1.RegisterAssetServiceServer(s.server, assetHandler)

	// Enable reflection for development
	reflection.Register(s.server)

	s.logger.WithField("port", s.port).Info("Starting gRPC server")

	// Start serving
	if err := s.server.Serve(lis); err != nil {
		return fmt.Errorf("failed to serve gRPC server: %w", err)
	}

	return nil
}

// Stop stops the gRPC server gracefully
func (s *Server) Stop() {
	if s.server != nil {
		s.logger.Info("Stopping gRPC server")
		s.server.GracefulStop()
	}
}

// loggingInterceptor logs gRPC requests
func (s *Server) loggingInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	s.logger.WithFields(map[string]any{
		"method": info.FullMethod,
	}).Debug("gRPC request received")

	resp, err := handler(ctx, req)

	if err != nil {
		s.logger.WithFields(map[string]any{
			"method": info.FullMethod,
			"error":  err.Error(),
		}).Error("gRPC request failed")
	} else {
		s.logger.WithFields(map[string]any{
			"method": info.FullMethod,
		}).Debug("gRPC request completed")
	}

	return resp, err
}

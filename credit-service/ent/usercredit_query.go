// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
	"github.com/social-content-ai/credit-service/ent/usercredit"
)

// UserCreditQuery is the builder for querying UserCredit entities.
type UserCreditQuery struct {
	config
	ctx        *QueryContext
	order      []usercredit.OrderOption
	inters     []Interceptor
	predicates []predicate.UserCredit
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserCreditQuery builder.
func (ucq *UserCreditQuery) Where(ps ...predicate.UserCredit) *UserCreditQuery {
	ucq.predicates = append(ucq.predicates, ps...)
	return ucq
}

// Limit the number of records to be returned by this query.
func (ucq *UserCreditQuery) Limit(limit int) *UserCreditQuery {
	ucq.ctx.Limit = &limit
	return ucq
}

// Offset to start from.
func (ucq *UserCreditQuery) Offset(offset int) *UserCreditQuery {
	ucq.ctx.Offset = &offset
	return ucq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ucq *UserCreditQuery) Unique(unique bool) *UserCreditQuery {
	ucq.ctx.Unique = &unique
	return ucq
}

// Order specifies how the records should be ordered.
func (ucq *UserCreditQuery) Order(o ...usercredit.OrderOption) *UserCreditQuery {
	ucq.order = append(ucq.order, o...)
	return ucq
}

// First returns the first UserCredit entity from the query.
// Returns a *NotFoundError when no UserCredit was found.
func (ucq *UserCreditQuery) First(ctx context.Context) (*UserCredit, error) {
	nodes, err := ucq.Limit(1).All(setContextOp(ctx, ucq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{usercredit.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ucq *UserCreditQuery) FirstX(ctx context.Context) *UserCredit {
	node, err := ucq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UserCredit ID from the query.
// Returns a *NotFoundError when no UserCredit ID was found.
func (ucq *UserCreditQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ucq.Limit(1).IDs(setContextOp(ctx, ucq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{usercredit.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ucq *UserCreditQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ucq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UserCredit entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UserCredit entity is found.
// Returns a *NotFoundError when no UserCredit entities are found.
func (ucq *UserCreditQuery) Only(ctx context.Context) (*UserCredit, error) {
	nodes, err := ucq.Limit(2).All(setContextOp(ctx, ucq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{usercredit.Label}
	default:
		return nil, &NotSingularError{usercredit.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ucq *UserCreditQuery) OnlyX(ctx context.Context) *UserCredit {
	node, err := ucq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UserCredit ID in the query.
// Returns a *NotSingularError when more than one UserCredit ID is found.
// Returns a *NotFoundError when no entities are found.
func (ucq *UserCreditQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ucq.Limit(2).IDs(setContextOp(ctx, ucq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{usercredit.Label}
	default:
		err = &NotSingularError{usercredit.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ucq *UserCreditQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ucq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UserCredits.
func (ucq *UserCreditQuery) All(ctx context.Context) ([]*UserCredit, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryAll)
	if err := ucq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UserCredit, *UserCreditQuery]()
	return withInterceptors[[]*UserCredit](ctx, ucq, qr, ucq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ucq *UserCreditQuery) AllX(ctx context.Context) []*UserCredit {
	nodes, err := ucq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UserCredit IDs.
func (ucq *UserCreditQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ucq.ctx.Unique == nil && ucq.path != nil {
		ucq.Unique(true)
	}
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryIDs)
	if err = ucq.Select(usercredit.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ucq *UserCreditQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ucq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ucq *UserCreditQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryCount)
	if err := ucq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ucq, querierCount[*UserCreditQuery](), ucq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ucq *UserCreditQuery) CountX(ctx context.Context) int {
	count, err := ucq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ucq *UserCreditQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ucq.ctx, ent.OpQueryExist)
	switch _, err := ucq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ucq *UserCreditQuery) ExistX(ctx context.Context) bool {
	exist, err := ucq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserCreditQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ucq *UserCreditQuery) Clone() *UserCreditQuery {
	if ucq == nil {
		return nil
	}
	return &UserCreditQuery{
		config:     ucq.config,
		ctx:        ucq.ctx.Clone(),
		order:      append([]usercredit.OrderOption{}, ucq.order...),
		inters:     append([]Interceptor{}, ucq.inters...),
		predicates: append([]predicate.UserCredit{}, ucq.predicates...),
		// clone intermediate query.
		sql:  ucq.sql.Clone(),
		path: ucq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UserCredit.Query().
//		GroupBy(usercredit.FieldUserID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ucq *UserCreditQuery) GroupBy(field string, fields ...string) *UserCreditGroupBy {
	ucq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserCreditGroupBy{build: ucq}
	grbuild.flds = &ucq.ctx.Fields
	grbuild.label = usercredit.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.UserCredit.Query().
//		Select(usercredit.FieldUserID).
//		Scan(ctx, &v)
func (ucq *UserCreditQuery) Select(fields ...string) *UserCreditSelect {
	ucq.ctx.Fields = append(ucq.ctx.Fields, fields...)
	sbuild := &UserCreditSelect{UserCreditQuery: ucq}
	sbuild.label = usercredit.Label
	sbuild.flds, sbuild.scan = &ucq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserCreditSelect configured with the given aggregations.
func (ucq *UserCreditQuery) Aggregate(fns ...AggregateFunc) *UserCreditSelect {
	return ucq.Select().Aggregate(fns...)
}

func (ucq *UserCreditQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ucq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ucq); err != nil {
				return err
			}
		}
	}
	for _, f := range ucq.ctx.Fields {
		if !usercredit.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ucq.path != nil {
		prev, err := ucq.path(ctx)
		if err != nil {
			return err
		}
		ucq.sql = prev
	}
	return nil
}

func (ucq *UserCreditQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UserCredit, error) {
	var (
		nodes = []*UserCredit{}
		_spec = ucq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UserCredit).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UserCredit{config: ucq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ucq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ucq *UserCreditQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ucq.querySpec()
	_spec.Node.Columns = ucq.ctx.Fields
	if len(ucq.ctx.Fields) > 0 {
		_spec.Unique = ucq.ctx.Unique != nil && *ucq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ucq.driver, _spec)
}

func (ucq *UserCreditQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(usercredit.Table, usercredit.Columns, sqlgraph.NewFieldSpec(usercredit.FieldID, field.TypeUUID))
	_spec.From = ucq.sql
	if unique := ucq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ucq.path != nil {
		_spec.Unique = true
	}
	if fields := ucq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, usercredit.FieldID)
		for i := range fields {
			if fields[i] != usercredit.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ucq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ucq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ucq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ucq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ucq *UserCreditQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ucq.driver.Dialect())
	t1 := builder.Table(usercredit.Table)
	columns := ucq.ctx.Fields
	if len(columns) == 0 {
		columns = usercredit.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ucq.sql != nil {
		selector = ucq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ucq.ctx.Unique != nil && *ucq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ucq.predicates {
		p(selector)
	}
	for _, p := range ucq.order {
		p(selector)
	}
	if offset := ucq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ucq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// UserCreditGroupBy is the group-by builder for UserCredit entities.
type UserCreditGroupBy struct {
	selector
	build *UserCreditQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ucgb *UserCreditGroupBy) Aggregate(fns ...AggregateFunc) *UserCreditGroupBy {
	ucgb.fns = append(ucgb.fns, fns...)
	return ucgb
}

// Scan applies the selector query and scans the result into the given value.
func (ucgb *UserCreditGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ucgb.build.ctx, ent.OpQueryGroupBy)
	if err := ucgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserCreditQuery, *UserCreditGroupBy](ctx, ucgb.build, ucgb, ucgb.build.inters, v)
}

func (ucgb *UserCreditGroupBy) sqlScan(ctx context.Context, root *UserCreditQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ucgb.fns))
	for _, fn := range ucgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ucgb.flds)+len(ucgb.fns))
		for _, f := range *ucgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ucgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ucgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserCreditSelect is the builder for selecting fields of UserCredit entities.
type UserCreditSelect struct {
	*UserCreditQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ucs *UserCreditSelect) Aggregate(fns ...AggregateFunc) *UserCreditSelect {
	ucs.fns = append(ucs.fns, fns...)
	return ucs
}

// Scan applies the selector query and scans the result into the given value.
func (ucs *UserCreditSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ucs.ctx, ent.OpQuerySelect)
	if err := ucs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserCreditQuery, *UserCreditSelect](ctx, ucs.UserCreditQuery, ucs, ucs.inters, v)
}

func (ucs *UserCreditSelect) sqlScan(ctx context.Context, root *UserCreditQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ucs.fns))
	for _, fn := range ucs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ucs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ucs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

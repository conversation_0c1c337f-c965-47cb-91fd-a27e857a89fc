package template

import (
	"context"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new template service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreateTemplate creates a new template
func (s *service) CreateTemplate(ctx context.Context, req *models.CreateTemplateRequest) (*models.TemplateResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Creating template")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Create template in database
	templateCreate := s.writeDB.Template.
		Create().
		SetName(req.Name).
		SetDescription(req.Description).
		SetContent(req.Content).
		SetUserID(userUUID).
		SetCategory(template.Category(req.Category)).
		SetIsPublic(req.IsPublic).
		SetPlatforms(req.Platforms)

	if len(req.RAGFiles) > 0 {
		templateCreate = templateCreate.SetTrainingFiles(req.RAGFiles)
	}

	if len(req.VariablePlaceholders) > 0 {
		templateCreate = templateCreate.SetVariables(req.VariablePlaceholders)
	}

	if len(req.Tags) > 0 {
		templateCreate = templateCreate.SetTags(req.Tags)
	}

	template, err := templateCreate.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create template")
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	// Convert to response model
	response := &models.TemplateResponse{
		BaseModel: models.BaseModel{
			ID:        template.ID.String(),
			CreatedAt: template.CreatedAt,
			UpdatedAt: template.UpdatedAt,
		},
		Name:                 template.Name,
		Description:          template.Description,
		Content:              template.Content,
		UserID:               template.UserID.String(),
		Category:             string(template.Category),
		Platforms:            template.Platforms,
		IsPublic:             template.IsPublic,
		Type:                 "my",
		IsVerified:           false,
		IsFeatured:           template.IsFeatured,
		IsPremium:            false,
		CreditCost:           0,
		Rating:               template.Rating,
		RatingCount:          template.RatingCount,
		DownloadCount:        0,
		UsageCount:           template.UsageCount,
		Tags:                 template.Tags,
		VariablePlaceholders: template.Variables,
	}

	// Set RAG files if present
	if len(template.TrainingFiles) > 0 {
		response.RAGFiles = template.TrainingFiles
	}

	s.logger.WithField("template_id", template.ID).Info("Template created successfully")
	return response, nil
}

// GetTemplate gets a template by ID
func (s *service) GetTemplate(ctx context.Context, templateID, userID string) (*models.TemplateResponse, error) {
	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Getting template")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(templateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return nil, fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get template from database
	template, err := s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.DeletedAtIsNil(),
			template.Or(
				template.UserID(userUUID), // User's own template
				template.IsPublic(true),   // Public template
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Convert to response model
	response := s.convertTemplateToResponse(template)
	return response, nil
}

// UpdateTemplate updates a template
func (s *service) UpdateTemplate(ctx context.Context, req *models.UpdateTemplateRequest) (*models.TemplateResponse, error) {
	s.logger.WithFields(map[string]any{
		"template_id": req.ID,
		"user_id":     req.UserID,
	}).Info("Updating template")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(req.ID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return nil, fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build update query
	updateQuery := s.writeDB.Template.
		Update().
		Where(
			template.ID(templateUUID),
			template.UserID(userUUID),
			template.DeletedAtIsNil(),
		)

	// Apply updates only for non-empty fields
	if req.Name != "" {
		updateQuery = updateQuery.SetName(req.Name)
	}

	if req.Description != "" {
		updateQuery = updateQuery.SetDescription(req.Description)
	}

	if req.Content != "" {
		updateQuery = updateQuery.SetContent(req.Content)
	}

	if req.Category != "" {
		updateQuery = updateQuery.SetCategory(template.Category(req.Category))
	}

	if len(req.Platforms) > 0 {
		updateQuery = updateQuery.SetPlatforms(req.Platforms)
	}

	if len(req.Tags) > 0 {
		updateQuery = updateQuery.SetTags(req.Tags)
	}

	if len(req.VariablePlaceholders) > 0 {
		updateQuery = updateQuery.SetVariables(req.VariablePlaceholders)
	}

	if len(req.RAGFiles) > 0 {
		updateQuery = updateQuery.SetTrainingFiles(req.RAGFiles)
	}

	// IsPublic is always set since it's a bool, so we update it
	updateQuery = updateQuery.SetIsPublic(req.IsPublic)

	// Execute update
	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update template")
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("template not found or no changes made")
	}

	// Get updated template
	updatedTemplate, err := s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.UserID(userUUID),
			template.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated template")
		return nil, fmt.Errorf("failed to get updated template: %w", err)
	}

	// Convert to response model
	response := s.convertTemplateToResponse(updatedTemplate)
	s.logger.WithField("template_id", req.ID).Info("Template updated successfully")
	return response, nil
}

// DeleteTemplate deletes a template
func (s *service) DeleteTemplate(ctx context.Context, templateID, userID string) error {
	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Deleting template")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(templateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Soft delete the template by setting deleted_at
	affected, err := s.writeDB.Template.
		Update().
		Where(
			template.ID(templateUUID),
			template.UserID(userUUID),
			template.DeletedAtIsNil(),
		).
		SetDeletedAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete template")
		return fmt.Errorf("failed to delete template: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("template not found or already deleted")
	}

	s.logger.WithField("template_id", templateID).Info("Template deleted successfully")
	return nil
}

// ListTemplates lists templates with pagination
func (s *service) ListTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing templates")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query based on type
	query := s.readDB.Template.
		Query().
		Where(template.DeletedAtIsNil())

	switch req.Type {
	case "my":
		query = query.Where(template.UserID(userUUID))
	case "marketplace", "public":
		query = query.Where(template.IsPublic(true))
	case "premium":
		query = query.Where(
			template.IsPublic(true),
			// TODO: Add premium filter when implemented
		)
	case "favorites":
		// TODO: Implement favorites when user-template relationship is added
		query = query.Where(template.UserID(userUUID))
	default:
		// Default to user's templates and public templates
		query = query.Where(
			template.Or(
				template.UserID(userUUID),
				template.IsPublic(true),
			),
		)
	}

	// Apply category filter
	if req.Category != "" {
		query = query.Where(template.CategoryEQ(template.Category(req.Category)))
	}

	// Apply platform filter
	if req.Platform != "" {
		// For JSON array fields, we need to use a custom predicate
		query = query.Where(func(s *sql.Selector) {
			s.Where(sql.Contains(s.C(template.FieldPlatforms), req.Platform))
		})
	}

	// Apply search filter
	if req.Search != "" {
		query = query.Where(
			template.Or(
				template.NameContains(req.Search),
				template.DescriptionContains(req.Search),
				template.ContentContains(req.Search),
			),
		)
	}

	// Apply filters
	if req.IsVerified != nil {
		// TODO: Add verified filter when implemented
	}

	if req.IsFeatured != nil {
		query = query.Where(template.IsFeatured(*req.IsFeatured))
	}

	if req.MinRating > 0 {
		query = query.Where(template.RatingGTE(req.MinRating))
	}

	// Apply sorting
	switch req.SortBy {
	case "name":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldName))
		} else {
			query = query.Order(ent.Asc(template.FieldName))
		}
	case "rating":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldRating))
		} else {
			query = query.Order(ent.Asc(template.FieldRating))
		}
	case "uses":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldUsageCount))
		} else {
			query = query.Order(ent.Asc(template.FieldUsageCount))
		}
	default:
		query = query.Order(ent.Desc(template.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count templates")
		return nil, fmt.Errorf("failed to count templates: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	templates, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list templates")
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	// Convert to response models
	templateResponses := make([]models.TemplateResponse, len(templates))
	for i, t := range templates {
		templateResponses[i] = *s.convertTemplateToResponse(t)
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	response := &models.ListTemplatesResponse{
		Templates: templateResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
		Filters: models.TemplateFilters{
			Categories: []string{"social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business"},
			Platforms:  []string{"facebook", "twitter", "instagram", "linkedin", "youtube", "tiktok"},
			Tags:       []string{}, // TODO: Get from database
		},
	}

	return response, nil
}

// UseTemplate uses a template to generate content
func (s *service) UseTemplate(ctx context.Context, req *models.UseTemplateRequest) (*models.UseTemplateResponse, error) {
	s.logger.WithFields(map[string]any{
		"template_id": req.TemplateID,
		"user_id":     req.UserID,
	}).Info("Using template")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(req.TemplateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return nil, fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get template from database
	templateEntity, err := s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.DeletedAtIsNil(),
			template.Or(
				template.UserID(userUUID), // User's own template
				template.IsPublic(true),   // Public template
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Process template content with variables
	processedContent := templateEntity.Content

	// Replace variables in content
	if len(req.Variables) > 0 {
		for key, value := range req.Variables {
			// Simple variable replacement - in production you might want more sophisticated templating
			placeholder := "{{" + key + "}}"
			if strValue, ok := value.(string); ok {
				processedContent = strings.ReplaceAll(processedContent, placeholder, strValue)
			} else {
				// Convert to string if not already
				processedContent = strings.ReplaceAll(processedContent, placeholder, fmt.Sprintf("%v", value))
			}
		}
	}

	// Increment usage count
	_, err = s.writeDB.Template.
		Update().
		Where(template.ID(templateUUID)).
		AddUsageCount(1).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Warn("Failed to increment template usage count")
		// Don't fail the request for this
	}

	// Create response
	response := &models.UseTemplateResponse{
		TemplateID:       req.TemplateID,
		GeneratedContent: processedContent,
		CreditCost:       0,                   // TODO: Calculate credit cost if premium template
		UsageID:          uuid.New().String(), // Generate usage ID for tracking
	}

	s.logger.WithField("template_id", req.TemplateID).Info("Template used successfully")
	return response, nil
}

// RateTemplate rates a template
func (s *service) RateTemplate(ctx context.Context, req *models.RateTemplateRequest) error {
	s.logger.WithFields(map[string]any{
		"template_id": req.TemplateID,
		"user_id":     req.UserID,
		"rating":      req.Rating,
	}).Info("Rating template")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(req.TemplateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Validate rating range
	if req.Rating < 1 || req.Rating > 5 {
		return fmt.Errorf("rating must be between 1 and 5")
	}

	// Check if template exists and is accessible
	templateEntity, err := s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.DeletedAtIsNil(),
			template.Or(
				template.UserID(userUUID), // User's own template
				template.IsPublic(true),   // Public template
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return fmt.Errorf("failed to get template: %w", err)
	}

	// Calculate new rating
	// This is a simplified approach - in production you might want to store individual ratings
	currentRating := templateEntity.Rating
	currentCount := templateEntity.RatingCount

	newCount := currentCount + 1
	newRating := ((currentRating * float64(currentCount)) + float64(req.Rating)) / float64(newCount)

	// Update template rating
	_, err = s.writeDB.Template.
		Update().
		Where(template.ID(templateUUID)).
		SetRating(newRating).
		SetRatingCount(newCount).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to update template rating")
		return fmt.Errorf("failed to update template rating: %w", err)
	}

	s.logger.WithFields(map[string]any{
		"template_id": req.TemplateID,
		"new_rating":  newRating,
		"new_count":   newCount,
	}).Info("Template rated successfully")

	return nil
}

// FavoriteTemplate adds template to favorites
func (s *service) FavoriteTemplate(ctx context.Context, templateID, userID string) error {
	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Adding template to favorites")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(templateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if template exists and is accessible
	_, err = s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.DeletedAtIsNil(),
			template.Or(
				template.UserID(userUUID), // User's own template
				template.IsPublic(true),   // Public template
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return fmt.Errorf("failed to get template: %w", err)
	}

	// TODO: In a real implementation, you would store user-template favorites in a separate table
	// For now, we'll just log the action since we don't have a favorites table in the schema

	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Template added to favorites (stored in user preferences)")

	// In production, you would:
	// 1. Create a user_template_favorites table with (user_id, template_id, created_at)
	// 2. Insert a record if not exists
	// 3. Handle duplicate key errors gracefully

	return nil
}

// UnfavoriteTemplate removes template from favorites
func (s *service) UnfavoriteTemplate(ctx context.Context, templateID, userID string) error {
	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Removing template from favorites")

	// Parse template ID to UUID
	templateUUID, err := uuid.Parse(templateID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid template ID format")
		return fmt.Errorf("invalid template ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if template exists and is accessible
	_, err = s.readDB.Template.
		Query().
		Where(
			template.ID(templateUUID),
			template.DeletedAtIsNil(),
			template.Or(
				template.UserID(userUUID), // User's own template
				template.IsPublic(true),   // Public template
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("template not found")
		}
		s.logger.WithError(err).Error("Failed to get template")
		return fmt.Errorf("failed to get template: %w", err)
	}

	// TODO: In a real implementation, you would remove from user-template favorites table
	// For now, we'll just log the action since we don't have a favorites table in the schema

	s.logger.WithFields(map[string]any{
		"template_id": templateID,
		"user_id":     userID,
	}).Info("Template removed from favorites (removed from user preferences)")

	// In production, you would:
	// 1. Delete record from user_template_favorites table
	// 2. WHERE user_id = ? AND template_id = ?
	// 3. Handle case where record doesn't exist gracefully

	return nil
}

// ListMarketplaceTemplates lists marketplace templates
func (s *service) ListMarketplaceTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing marketplace templates")

	// Parse user ID to UUID for access control
	_, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query for marketplace templates (public templates only)
	query := s.readDB.Template.
		Query().
		Where(
			template.IsPublic(true),
			template.DeletedAtIsNil(),
		)

	// Apply category filter
	if req.Category != "" {
		query = query.Where(template.CategoryEQ(template.Category(req.Category)))
	}

	// Apply platform filter
	if req.Platform != "" {
		query = query.Where(func(s *sql.Selector) {
			s.Where(sql.Contains(s.C(template.FieldPlatforms), req.Platform))
		})
	}

	// Apply search filter
	if req.Search != "" {
		query = query.Where(
			template.Or(
				template.NameContains(req.Search),
				template.DescriptionContains(req.Search),
				template.ContentContains(req.Search),
			),
		)
	}

	// Apply filters
	if req.IsVerified != nil && *req.IsVerified {
		// TODO: Add verified filter when implemented
		// For now, we'll use featured as a proxy for verified
		query = query.Where(template.IsFeatured(true))
	}

	if req.IsFeatured != nil {
		query = query.Where(template.IsFeatured(*req.IsFeatured))
	}

	if req.MinRating > 0 {
		query = query.Where(template.RatingGTE(req.MinRating))
	}

	// Apply sorting for marketplace (prioritize quality)
	switch req.SortBy {
	case "name":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldName))
		} else {
			query = query.Order(ent.Asc(template.FieldName))
		}
	case "rating":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldRating))
		} else {
			query = query.Order(ent.Asc(template.FieldRating))
		}
	case "uses":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(template.FieldUsageCount))
		} else {
			query = query.Order(ent.Asc(template.FieldUsageCount))
		}
	case "popular":
		// Sort by combination of rating and usage
		query = query.Order(
			ent.Desc(template.FieldRating),
			ent.Desc(template.FieldUsageCount),
		)
	default:
		// Default marketplace sorting: featured first, then by rating and usage
		query = query.Order(
			ent.Desc(template.FieldIsFeatured),
			ent.Desc(template.FieldRating),
			ent.Desc(template.FieldUsageCount),
		)
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count marketplace templates")
		return nil, fmt.Errorf("failed to count marketplace templates: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	templates, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list marketplace templates")
		return nil, fmt.Errorf("failed to list marketplace templates: %w", err)
	}

	// Convert to response models
	templateResponses := make([]models.TemplateResponse, len(templates))
	for i, t := range templates {
		response := s.convertTemplateToResponse(t)
		// Mark as marketplace type
		response.Type = "marketplace"
		templateResponses[i] = *response
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	// Create filters for marketplace
	filters := models.TemplateFilters{
		Categories: []string{"social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business"},
		Platforms:  []string{"facebook", "twitter", "instagram", "linkedin", "youtube", "tiktok"},
		Tags:       []string{}, // TODO: Get popular tags from database
	}

	response := &models.ListTemplatesResponse{
		Templates: templateResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
		Filters: filters,
	}

	s.logger.WithField("marketplace_count", len(templates)).Info("Marketplace templates listed successfully")
	return response, nil
}

// ListFeaturedTemplates lists featured templates
func (s *service) ListFeaturedTemplates(ctx context.Context, userID string, limit int) (*models.ListTemplatesResponse, error) {
	s.logger.WithFields(map[string]any{
		"user_id": userID,
		"limit":   limit,
	}).Info("Listing featured templates")

	// Validate user ID format (for logging purposes)
	_, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query for featured templates
	query := s.readDB.Template.
		Query().
		Where(
			template.IsFeatured(true),
			template.IsPublic(true),
			template.DeletedAtIsNil(),
		).
		Order(ent.Desc(template.FieldRating), ent.Desc(template.FieldUsageCount)).
		Limit(limit)

	// Get featured templates
	templates, err := query.All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list featured templates")
		return nil, fmt.Errorf("failed to list featured templates: %w", err)
	}

	// Convert to response models
	templateResponses := make([]models.TemplateResponse, len(templates))
	for i, t := range templates {
		response := s.convertTemplateToResponse(t)
		// Mark as featured type
		response.Type = "featured"
		templateResponses[i] = *response
	}

	// Create pagination metadata (simplified for featured list)
	pagination := models.PaginationMeta{
		Page:       1,
		Limit:      limit,
		Total:      len(templates),
		TotalPages: 1,
	}

	// Create filters for featured templates
	filters := models.TemplateFilters{
		Categories: []string{"social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business"},
		Platforms:  []string{"facebook", "twitter", "instagram", "linkedin", "youtube", "tiktok"},
		Tags:       []string{}, // TODO: Get popular tags from database
	}

	response := &models.ListTemplatesResponse{
		Templates:  templateResponses,
		Pagination: pagination,
		Filters:    filters,
	}

	s.logger.WithField("featured_count", len(templates)).Info("Featured templates listed successfully")
	return response, nil
}

// GetTemplateCategories gets template categories
func (s *service) GetTemplateCategories(ctx context.Context) (*models.TemplateCategoriesResponse, error) {
	s.logger.Info("Getting template categories")

	// Get category statistics from database
	categoryStats := make(map[string]int)

	// Query templates grouped by category
	categories := []string{"social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business"}

	for _, category := range categories {
		count, err := s.readDB.Template.
			Query().
			Where(
				template.CategoryEQ(template.Category(category)),
				template.DeletedAtIsNil(),
				template.IsPublic(true), // Only count public templates
			).
			Count(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("category", category).Warn("Failed to count templates for category")
			count = 0 // Continue with 0 count
		}

		categoryStats[category] = count
	}

	// Create category details
	categoryDetails := []models.TemplateCategory{
		{
			ID:          "social_media",
			Name:        "Social Media",
			Description: "Templates for social media posts across various platforms",
			Icon:        "social-media",
			Count:       categoryStats["social_media"],
			Featured:    categoryStats["social_media"] > 10, // Mark as featured if has many templates
		},
		{
			ID:          "blog",
			Name:        "Blog",
			Description: "Templates for blog posts and articles",
			Icon:        "blog",
			Count:       categoryStats["blog"],
			Featured:    categoryStats["blog"] > 10,
		},
		{
			ID:          "email",
			Name:        "Email",
			Description: "Templates for email marketing and newsletters",
			Icon:        "email",
			Count:       categoryStats["email"],
			Featured:    categoryStats["email"] > 10,
		},
		{
			ID:          "marketing",
			Name:        "Marketing",
			Description: "Templates for marketing campaigns and promotions",
			Icon:        "marketing",
			Count:       categoryStats["marketing"],
			Featured:    categoryStats["marketing"] > 10,
		},
		{
			ID:          "educational",
			Name:        "Educational",
			Description: "Templates for educational content and tutorials",
			Icon:        "education",
			Count:       categoryStats["educational"],
			Featured:    categoryStats["educational"] > 10,
		},
		{
			ID:          "promotional",
			Name:        "Promotional",
			Description: "Templates for promotional content and advertisements",
			Icon:        "promotion",
			Count:       categoryStats["promotional"],
			Featured:    categoryStats["promotional"] > 10,
		},
		{
			ID:          "personal",
			Name:        "Personal",
			Description: "Templates for personal use and communication",
			Icon:        "personal",
			Count:       categoryStats["personal"],
			Featured:    categoryStats["personal"] > 10,
		},
		{
			ID:          "business",
			Name:        "Business",
			Description: "Templates for business communication and content",
			Icon:        "business",
			Count:       categoryStats["business"],
			Featured:    categoryStats["business"] > 10,
		},
	}

	// Calculate total templates
	totalTemplates := 0
	for _, count := range categoryStats {
		totalTemplates += count
	}

	response := &models.TemplateCategoriesResponse{
		Categories: categoryDetails,
	}

	s.logger.WithField("total_categories", len(categoryDetails)).Info("Template categories retrieved successfully")
	return response, nil
}

// SearchTemplates searches templates
func (s *service) SearchTemplates(ctx context.Context, req *models.SearchTemplatesRequest) (*models.SearchTemplatesResponse, error) {
	// TODO: Implement search templates
	return nil, fmt.Errorf("not implemented")
}

// GetTemplateAnalytics gets template analytics
func (s *service) GetTemplateAnalytics(ctx context.Context, req *models.TemplateAnalyticsRequest) (*models.TemplateAnalyticsResponse, error) {
	// TODO: Implement get template analytics
	return nil, fmt.Errorf("not implemented")
}

// GetTemplateUsageStats gets template usage stats
func (s *service) GetTemplateUsageStats(ctx context.Context, req *models.TemplateUsageStatsRequest) (*models.TemplateUsageStatsResponse, error) {
	// TODO: Implement get template usage stats
	return nil, fmt.Errorf("not implemented")
}

// GetTemplatesAnalytics gets templates analytics
func (s *service) GetTemplatesAnalytics(ctx context.Context, userID string, period string) (*models.TemplateAnalyticsResponse, error) {
	// TODO: Implement get templates analytics
	return nil, fmt.Errorf("not implemented")
}

// AdminListTemplates lists templates (admin)
func (s *service) AdminListTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error) {
	// TODO: Implement admin list templates
	return nil, fmt.Errorf("not implemented")
}

// AdminVerifyTemplate verifies template (admin)
func (s *service) AdminVerifyTemplate(ctx context.Context, templateID string, verified bool) (*models.TemplateResponse, error) {
	// TODO: Implement admin verify template
	return nil, fmt.Errorf("not implemented")
}

// AdminFeatureTemplate features template (admin)
func (s *service) AdminFeatureTemplate(ctx context.Context, templateID string, featured bool) (*models.TemplateResponse, error) {
	// TODO: Implement admin feature template
	return nil, fmt.Errorf("not implemented")
}

// AdminDeleteTemplate deletes template (admin)
func (s *service) AdminDeleteTemplate(ctx context.Context, templateID string) error {
	// TODO: Implement admin delete template
	return fmt.Errorf("not implemented")
}

// GetAdminTemplateAnalytics gets admin template analytics
func (s *service) GetAdminTemplateAnalytics(ctx context.Context, period string) (*models.TemplateAnalyticsResponse, error) {
	// TODO: Implement get admin template analytics
	return nil, fmt.Errorf("not implemented")
}

// convertTemplateToResponse converts an ent Template to TemplateResponse model
func (s *service) convertTemplateToResponse(template *ent.Template) *models.TemplateResponse {
	response := &models.TemplateResponse{
		BaseModel: models.BaseModel{
			ID:        template.ID.String(),
			CreatedAt: template.CreatedAt,
			UpdatedAt: template.UpdatedAt,
		},
		Name:                 template.Name,
		Description:          template.Description,
		Content:              template.Content,
		UserID:               template.UserID.String(),
		Category:             string(template.Category),
		Platforms:            template.Platforms,
		IsPublic:             template.IsPublic,
		Type:                 "my",
		IsVerified:           false,
		IsFeatured:           template.IsFeatured,
		IsPremium:            false,
		CreditCost:           0,
		Rating:               template.Rating,
		RatingCount:          template.RatingCount,
		DownloadCount:        0,
		UsageCount:           template.UsageCount,
		Tags:                 template.Tags,
		VariablePlaceholders: template.Variables,
	}

	// Set RAG files if present
	if len(template.TrainingFiles) > 0 {
		response.RAGFiles = template.TrainingFiles
	}

	// Set hashtags if present
	if len(template.Hashtags) > 0 {
		// Convert hashtags to tags for compatibility
		if response.Tags == nil {
			response.Tags = template.Hashtags
		} else {
			response.Tags = append(response.Tags, template.Hashtags...)
		}
	}

	return response
}

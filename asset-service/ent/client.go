// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Asset is the client for interacting with the Asset builders.
	Asset *AssetClient
	// Folder is the client for interacting with the Folder builders.
	Folder *FolderClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Asset = NewAssetClient(c.config)
	c.Folder = NewFolderClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:    ctx,
		config: cfg,
		Asset:  NewAssetClient(cfg),
		Folder: NewFolderClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:    ctx,
		config: cfg,
		Asset:  NewAssetClient(cfg),
		Folder: NewFolderClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Asset.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Asset.Use(hooks...)
	c.Folder.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Asset.Intercept(interceptors...)
	c.Folder.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AssetMutation:
		return c.Asset.mutate(ctx, m)
	case *FolderMutation:
		return c.Folder.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AssetClient is a client for the Asset schema.
type AssetClient struct {
	config
}

// NewAssetClient returns a client for the Asset from the given config.
func NewAssetClient(c config) *AssetClient {
	return &AssetClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `asset.Hooks(f(g(h())))`.
func (c *AssetClient) Use(hooks ...Hook) {
	c.hooks.Asset = append(c.hooks.Asset, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `asset.Intercept(f(g(h())))`.
func (c *AssetClient) Intercept(interceptors ...Interceptor) {
	c.inters.Asset = append(c.inters.Asset, interceptors...)
}

// Create returns a builder for creating a Asset entity.
func (c *AssetClient) Create() *AssetCreate {
	mutation := newAssetMutation(c.config, OpCreate)
	return &AssetCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Asset entities.
func (c *AssetClient) CreateBulk(builders ...*AssetCreate) *AssetCreateBulk {
	return &AssetCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AssetClient) MapCreateBulk(slice any, setFunc func(*AssetCreate, int)) *AssetCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AssetCreateBulk{err: fmt.Errorf("calling to AssetClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AssetCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AssetCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Asset.
func (c *AssetClient) Update() *AssetUpdate {
	mutation := newAssetMutation(c.config, OpUpdate)
	return &AssetUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AssetClient) UpdateOne(a *Asset) *AssetUpdateOne {
	mutation := newAssetMutation(c.config, OpUpdateOne, withAsset(a))
	return &AssetUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AssetClient) UpdateOneID(id uuid.UUID) *AssetUpdateOne {
	mutation := newAssetMutation(c.config, OpUpdateOne, withAssetID(id))
	return &AssetUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Asset.
func (c *AssetClient) Delete() *AssetDelete {
	mutation := newAssetMutation(c.config, OpDelete)
	return &AssetDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AssetClient) DeleteOne(a *Asset) *AssetDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AssetClient) DeleteOneID(id uuid.UUID) *AssetDeleteOne {
	builder := c.Delete().Where(asset.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AssetDeleteOne{builder}
}

// Query returns a query builder for Asset.
func (c *AssetClient) Query() *AssetQuery {
	return &AssetQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAsset},
		inters: c.Interceptors(),
	}
}

// Get returns a Asset entity by its id.
func (c *AssetClient) Get(ctx context.Context, id uuid.UUID) (*Asset, error) {
	return c.Query().Where(asset.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AssetClient) GetX(ctx context.Context, id uuid.UUID) *Asset {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFolder queries the folder edge of a Asset.
func (c *AssetClient) QueryFolder(a *Asset) *FolderQuery {
	query := (&FolderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(asset.Table, asset.FieldID, id),
			sqlgraph.To(folder.Table, folder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, asset.FolderTable, asset.FolderColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AssetClient) Hooks() []Hook {
	return c.hooks.Asset
}

// Interceptors returns the client interceptors.
func (c *AssetClient) Interceptors() []Interceptor {
	return c.inters.Asset
}

func (c *AssetClient) mutate(ctx context.Context, m *AssetMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AssetCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AssetUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AssetUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AssetDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Asset mutation op: %q", m.Op())
	}
}

// FolderClient is a client for the Folder schema.
type FolderClient struct {
	config
}

// NewFolderClient returns a client for the Folder from the given config.
func NewFolderClient(c config) *FolderClient {
	return &FolderClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `folder.Hooks(f(g(h())))`.
func (c *FolderClient) Use(hooks ...Hook) {
	c.hooks.Folder = append(c.hooks.Folder, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `folder.Intercept(f(g(h())))`.
func (c *FolderClient) Intercept(interceptors ...Interceptor) {
	c.inters.Folder = append(c.inters.Folder, interceptors...)
}

// Create returns a builder for creating a Folder entity.
func (c *FolderClient) Create() *FolderCreate {
	mutation := newFolderMutation(c.config, OpCreate)
	return &FolderCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Folder entities.
func (c *FolderClient) CreateBulk(builders ...*FolderCreate) *FolderCreateBulk {
	return &FolderCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FolderClient) MapCreateBulk(slice any, setFunc func(*FolderCreate, int)) *FolderCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FolderCreateBulk{err: fmt.Errorf("calling to FolderClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FolderCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FolderCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Folder.
func (c *FolderClient) Update() *FolderUpdate {
	mutation := newFolderMutation(c.config, OpUpdate)
	return &FolderUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FolderClient) UpdateOne(f *Folder) *FolderUpdateOne {
	mutation := newFolderMutation(c.config, OpUpdateOne, withFolder(f))
	return &FolderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FolderClient) UpdateOneID(id uuid.UUID) *FolderUpdateOne {
	mutation := newFolderMutation(c.config, OpUpdateOne, withFolderID(id))
	return &FolderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Folder.
func (c *FolderClient) Delete() *FolderDelete {
	mutation := newFolderMutation(c.config, OpDelete)
	return &FolderDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FolderClient) DeleteOne(f *Folder) *FolderDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FolderClient) DeleteOneID(id uuid.UUID) *FolderDeleteOne {
	builder := c.Delete().Where(folder.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FolderDeleteOne{builder}
}

// Query returns a query builder for Folder.
func (c *FolderClient) Query() *FolderQuery {
	return &FolderQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFolder},
		inters: c.Interceptors(),
	}
}

// Get returns a Folder entity by its id.
func (c *FolderClient) Get(ctx context.Context, id uuid.UUID) (*Folder, error) {
	return c.Query().Where(folder.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FolderClient) GetX(ctx context.Context, id uuid.UUID) *Folder {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryParent queries the parent edge of a Folder.
func (c *FolderClient) QueryParent(f *Folder) *FolderQuery {
	query := (&FolderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, id),
			sqlgraph.To(folder.Table, folder.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, folder.ParentTable, folder.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Folder.
func (c *FolderClient) QueryChildren(f *Folder) *FolderQuery {
	query := (&FolderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, id),
			sqlgraph.To(folder.Table, folder.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, folder.ChildrenTable, folder.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAssets queries the assets edge of a Folder.
func (c *FolderClient) QueryAssets(f *Folder) *AssetQuery {
	query := (&AssetClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(folder.Table, folder.FieldID, id),
			sqlgraph.To(asset.Table, asset.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, folder.AssetsTable, folder.AssetsColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FolderClient) Hooks() []Hook {
	return c.hooks.Folder
}

// Interceptors returns the client interceptors.
func (c *FolderClient) Interceptors() []Interceptor {
	return c.inters.Folder
}

func (c *FolderClient) mutate(ctx context.Context, m *FolderMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FolderCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FolderUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FolderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FolderDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Folder mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Asset, Folder []ent.Hook
	}
	inters struct {
		Asset, Folder []ent.Interceptor
	}
)

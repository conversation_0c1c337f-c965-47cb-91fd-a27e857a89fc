syntax = "proto3";

package user.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";
import "common/v1/timestamp.proto";

option go_package = "github.com/social-content-ai/proto-shared/user/v1;userv1";

// User represents a user in the system
message User {
  string id = 1;
  string full_name = 2;
  string email = 3;
  string phone = 4;
  string avatar_url = 5;
  string bio = 6;
  string company = 7;
  string industry = 8;
  string role = 9;           // user, admin
  bool is_verified = 10;
  bool two_factor_enabled = 11;
  map<string, string> notification_settings = 12;
  string invite_code = 13;
  string aff_code = 14;
  google.protobuf.Timestamp aff_joined_at = 15;
  common.v1.TimestampInfo timestamps = 16;
}

// CreateUserRequest for user registration
message CreateUserRequest {
  string full_name = 1;
  string email = 2;
  string password = 3;
  string phone = 4;
  string company = 5;
  string industry = 6;
  string aff_code = 7;       // Referral code used during registration
}

// UpdateUserRequest for updating user profile
message UpdateUserRequest {
  string id = 1;
  optional string full_name = 2;
  optional string phone = 3;
  optional string avatar_url = 4;
  optional string bio = 5;
  optional string company = 6;
  optional string industry = 7;
  map<string, string> notification_settings = 8;
}

// GetUserRequest for retrieving a user
message GetUserRequest {
  string id = 1;
}

// ListUsersRequest for listing users with pagination
message ListUsersRequest {
  common.v1.PaginationRequest pagination = 1;
  string role = 2;           // Filter by role
  bool verified_only = 3;    // Filter verified users only
}

// ListUsersResponse with paginated users
message ListUsersResponse {
  repeated User users = 1;
  common.v1.PaginationResponse pagination = 2;
}

// DeleteUserRequest for soft deleting a user
message DeleteUserRequest {
  string id = 1;
  string reason = 2;         // Reason for deletion
}

// Empty response
message Empty {}

// UserService provides user management operations
service UserService {
  rpc CreateUser(CreateUserRequest) returns (User);
  rpc GetUser(GetUserRequest) returns (User);
  rpc UpdateUser(UpdateUserRequest) returns (User);
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse);
  rpc DeleteUser(DeleteUserRequest) returns (Empty);
  rpc VerifyUser(GetUserRequest) returns (User);
}

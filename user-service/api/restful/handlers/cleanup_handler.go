package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/pkg/cleanup"
	"github.com/social-content-ai/user-service/pkg/models"
)

// CleanupHandler handles cleanup-related HTTP requests
type CleanupHandler struct {
	scheduler *cleanup.Scheduler
	logger    logging.Logger
}

// NewCleanupHandler creates a new cleanup handler
func NewCleanupHandler(scheduler *cleanup.Scheduler, logger logging.Logger) *CleanupHandler {
	return &CleanupHandler{
		scheduler: scheduler,
		logger:    logger,
	}
}

// GetCleanupStats godoc
// @Summary Get cleanup statistics
// @Description Get statistics about unverified user cleanup process including counts and next cleanup time.
// @Tags admin,cleanup
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CleanupStatsResponse "Cleanup statistics retrieved successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/admin/cleanup/stats [get]
func (h *CleanupHandler) GetCleanupStats(c *gin.Context) {
	stats, err := h.scheduler.GetStats()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get cleanup stats")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_CLEANUP_STATS_FAILED",
			Message: "Failed to get cleanup statistics",
		})
		return
	}

	if stats == nil {
		c.JSON(http.StatusOK, models.CleanupStatsResponse{
			TotalUnverified:   0,
			ExpiredCount:      0,
			WarningCount:      0,
			NextCleanupIn:     "N/A",
			UnverifiedUserTTL: "N/A",
			ServiceRunning:    h.scheduler.IsRunning(),
		})
		return
	}

	c.JSON(http.StatusOK, models.CleanupStatsResponse{
		TotalUnverified:   stats.TotalUnverified,
		ExpiredCount:      stats.ExpiredCount,
		WarningCount:      stats.WarningCount,
		NextCleanupIn:     stats.NextCleanupIn.String(),
		UnverifiedUserTTL: stats.UnverifiedUserTTL.String(),
		ServiceRunning:    h.scheduler.IsRunning(),
	})
}

// TriggerCleanup godoc
// @Summary Trigger manual cleanup
// @Description Manually trigger a cleanup cycle to delete expired unverified users. Admin access required.
// @Tags admin,cleanup
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "Cleanup triggered successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/admin/cleanup/trigger [post]
func (h *CleanupHandler) TriggerCleanup(c *gin.Context) {
	if !h.scheduler.IsRunning() {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
			Error:   "CLEANUP_SERVICE_NOT_RUNNING",
			Message: "Cleanup service is not running",
		})
		return
	}

	err := h.scheduler.TriggerCleanup()
	if err != nil {
		h.logger.WithError(err).Error("Failed to trigger cleanup")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "TRIGGER_CLEANUP_FAILED",
			Message: "Failed to trigger cleanup",
		})
		return
	}

	h.logger.Info("Manual cleanup triggered")
	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Cleanup triggered successfully",
	})
}

// GetCleanupStatus godoc
// @Summary Get cleanup service status
// @Description Get the current status of the cleanup service including whether it's running.
// @Tags admin,cleanup
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CleanupStatusResponse "Cleanup service status retrieved successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/admin/cleanup/status [get]
func (h *CleanupHandler) GetCleanupStatus(c *gin.Context) {
	status := models.CleanupStatusResponse{
		ServiceRunning: h.scheduler.IsRunning(),
		ServiceName:    "user-cleanup-service",
	}

	if h.scheduler.IsRunning() {
		status.Status = "running"
		status.Message = "Cleanup service is running normally"
	} else {
		status.Status = "stopped"
		status.Message = "Cleanup service is not running"
	}

	c.JSON(http.StatusOK, status)
}

// StartCleanupService godoc
// @Summary Start cleanup service
// @Description Start the cleanup service for unverified users. Admin access required.
// @Tags admin,cleanup
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "Cleanup service started successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 409 {object} models.ErrorResponse "Service already running"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/admin/cleanup/start [post]
func (h *CleanupHandler) StartCleanupService(c *gin.Context) {
	if h.scheduler.IsRunning() {
		c.JSON(http.StatusConflict, models.ErrorResponse{
			Error:   "CLEANUP_SERVICE_ALREADY_RUNNING",
			Message: "Cleanup service is already running",
		})
		return
	}

	err := h.scheduler.Start()
	if err != nil {
		h.logger.WithError(err).Error("Failed to start cleanup service")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "START_CLEANUP_SERVICE_FAILED",
			Message: "Failed to start cleanup service",
		})
		return
	}

	h.logger.Info("Cleanup service started via API")
	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Cleanup service started successfully",
	})
}

// StopCleanupService godoc
// @Summary Stop cleanup service
// @Description Stop the cleanup service for unverified users. Admin access required.
// @Tags admin,cleanup
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SuccessResponse "Cleanup service stopped successfully"
// @Failure 401 {object} models.ErrorResponse "User not authenticated"
// @Failure 403 {object} models.ErrorResponse "Admin access required"
// @Failure 409 {object} models.ErrorResponse "Service not running"
// @Failure 500 {object} models.ErrorResponse "Internal server error"
// @Router /api/v1/admin/cleanup/stop [post]
func (h *CleanupHandler) StopCleanupService(c *gin.Context) {
	if !h.scheduler.IsRunning() {
		c.JSON(http.StatusConflict, models.ErrorResponse{
			Error:   "CLEANUP_SERVICE_NOT_RUNNING",
			Message: "Cleanup service is not running",
		})
		return
	}

	err := h.scheduler.Stop()
	if err != nil {
		h.logger.WithError(err).Error("Failed to stop cleanup service")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "STOP_CLEANUP_SERVICE_FAILED",
			Message: "Failed to stop cleanup service",
		})
		return
	}

	h.logger.Info("Cleanup service stopped via API")
	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Cleanup service stopped successfully",
	})
}

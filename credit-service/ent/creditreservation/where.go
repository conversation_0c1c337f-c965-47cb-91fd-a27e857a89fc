// Code generated by ent, DO NOT EDIT.

package creditreservation

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldUserID, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldAmount, v))
}

// Purpose applies equality check predicate on the "purpose" field. It's identical to PurposeEQ.
func Purpose(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldPurpose, v))
}

// ReferenceID applies equality check predicate on the "reference_id" field. It's identical to ReferenceIDEQ.
func ReferenceID(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceType applies equality check predicate on the "reference_type" field. It's identical to ReferenceTypeEQ.
func ReferenceType(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldReferenceType, v))
}

// ExpiresAt applies equality check predicate on the "expires_at" field. It's identical to ExpiresAtEQ.
func ExpiresAt(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldExpiresAt, v))
}

// ConsumedAt applies equality check predicate on the "consumed_at" field. It's identical to ConsumedAtEQ.
func ConsumedAt(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldConsumedAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldUserID, v))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v int) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldAmount, v))
}

// PurposeEQ applies the EQ predicate on the "purpose" field.
func PurposeEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldPurpose, v))
}

// PurposeNEQ applies the NEQ predicate on the "purpose" field.
func PurposeNEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldPurpose, v))
}

// PurposeIn applies the In predicate on the "purpose" field.
func PurposeIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldPurpose, vs...))
}

// PurposeNotIn applies the NotIn predicate on the "purpose" field.
func PurposeNotIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldPurpose, vs...))
}

// PurposeGT applies the GT predicate on the "purpose" field.
func PurposeGT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldPurpose, v))
}

// PurposeGTE applies the GTE predicate on the "purpose" field.
func PurposeGTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldPurpose, v))
}

// PurposeLT applies the LT predicate on the "purpose" field.
func PurposeLT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldPurpose, v))
}

// PurposeLTE applies the LTE predicate on the "purpose" field.
func PurposeLTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldPurpose, v))
}

// PurposeContains applies the Contains predicate on the "purpose" field.
func PurposeContains(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContains(FieldPurpose, v))
}

// PurposeHasPrefix applies the HasPrefix predicate on the "purpose" field.
func PurposeHasPrefix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasPrefix(FieldPurpose, v))
}

// PurposeHasSuffix applies the HasSuffix predicate on the "purpose" field.
func PurposeHasSuffix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasSuffix(FieldPurpose, v))
}

// PurposeEqualFold applies the EqualFold predicate on the "purpose" field.
func PurposeEqualFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEqualFold(FieldPurpose, v))
}

// PurposeContainsFold applies the ContainsFold predicate on the "purpose" field.
func PurposeContainsFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContainsFold(FieldPurpose, v))
}

// ReferenceIDEQ applies the EQ predicate on the "reference_id" field.
func ReferenceIDEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceIDNEQ applies the NEQ predicate on the "reference_id" field.
func ReferenceIDNEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldReferenceID, v))
}

// ReferenceIDIn applies the In predicate on the "reference_id" field.
func ReferenceIDIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldReferenceID, vs...))
}

// ReferenceIDNotIn applies the NotIn predicate on the "reference_id" field.
func ReferenceIDNotIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldReferenceID, vs...))
}

// ReferenceIDGT applies the GT predicate on the "reference_id" field.
func ReferenceIDGT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldReferenceID, v))
}

// ReferenceIDGTE applies the GTE predicate on the "reference_id" field.
func ReferenceIDGTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldReferenceID, v))
}

// ReferenceIDLT applies the LT predicate on the "reference_id" field.
func ReferenceIDLT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldReferenceID, v))
}

// ReferenceIDLTE applies the LTE predicate on the "reference_id" field.
func ReferenceIDLTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldReferenceID, v))
}

// ReferenceIDContains applies the Contains predicate on the "reference_id" field.
func ReferenceIDContains(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContains(FieldReferenceID, v))
}

// ReferenceIDHasPrefix applies the HasPrefix predicate on the "reference_id" field.
func ReferenceIDHasPrefix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasPrefix(FieldReferenceID, v))
}

// ReferenceIDHasSuffix applies the HasSuffix predicate on the "reference_id" field.
func ReferenceIDHasSuffix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasSuffix(FieldReferenceID, v))
}

// ReferenceIDIsNil applies the IsNil predicate on the "reference_id" field.
func ReferenceIDIsNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIsNull(FieldReferenceID))
}

// ReferenceIDNotNil applies the NotNil predicate on the "reference_id" field.
func ReferenceIDNotNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotNull(FieldReferenceID))
}

// ReferenceIDEqualFold applies the EqualFold predicate on the "reference_id" field.
func ReferenceIDEqualFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEqualFold(FieldReferenceID, v))
}

// ReferenceIDContainsFold applies the ContainsFold predicate on the "reference_id" field.
func ReferenceIDContainsFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContainsFold(FieldReferenceID, v))
}

// ReferenceTypeEQ applies the EQ predicate on the "reference_type" field.
func ReferenceTypeEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldReferenceType, v))
}

// ReferenceTypeNEQ applies the NEQ predicate on the "reference_type" field.
func ReferenceTypeNEQ(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldReferenceType, v))
}

// ReferenceTypeIn applies the In predicate on the "reference_type" field.
func ReferenceTypeIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldReferenceType, vs...))
}

// ReferenceTypeNotIn applies the NotIn predicate on the "reference_type" field.
func ReferenceTypeNotIn(vs ...string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldReferenceType, vs...))
}

// ReferenceTypeGT applies the GT predicate on the "reference_type" field.
func ReferenceTypeGT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldReferenceType, v))
}

// ReferenceTypeGTE applies the GTE predicate on the "reference_type" field.
func ReferenceTypeGTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldReferenceType, v))
}

// ReferenceTypeLT applies the LT predicate on the "reference_type" field.
func ReferenceTypeLT(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldReferenceType, v))
}

// ReferenceTypeLTE applies the LTE predicate on the "reference_type" field.
func ReferenceTypeLTE(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldReferenceType, v))
}

// ReferenceTypeContains applies the Contains predicate on the "reference_type" field.
func ReferenceTypeContains(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContains(FieldReferenceType, v))
}

// ReferenceTypeHasPrefix applies the HasPrefix predicate on the "reference_type" field.
func ReferenceTypeHasPrefix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasPrefix(FieldReferenceType, v))
}

// ReferenceTypeHasSuffix applies the HasSuffix predicate on the "reference_type" field.
func ReferenceTypeHasSuffix(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldHasSuffix(FieldReferenceType, v))
}

// ReferenceTypeIsNil applies the IsNil predicate on the "reference_type" field.
func ReferenceTypeIsNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIsNull(FieldReferenceType))
}

// ReferenceTypeNotNil applies the NotNil predicate on the "reference_type" field.
func ReferenceTypeNotNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotNull(FieldReferenceType))
}

// ReferenceTypeEqualFold applies the EqualFold predicate on the "reference_type" field.
func ReferenceTypeEqualFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEqualFold(FieldReferenceType, v))
}

// ReferenceTypeContainsFold applies the ContainsFold predicate on the "reference_type" field.
func ReferenceTypeContainsFold(v string) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldContainsFold(FieldReferenceType, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldStatus, vs...))
}

// ExpiresAtEQ applies the EQ predicate on the "expires_at" field.
func ExpiresAtEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldExpiresAt, v))
}

// ExpiresAtNEQ applies the NEQ predicate on the "expires_at" field.
func ExpiresAtNEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldExpiresAt, v))
}

// ExpiresAtIn applies the In predicate on the "expires_at" field.
func ExpiresAtIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldExpiresAt, vs...))
}

// ExpiresAtNotIn applies the NotIn predicate on the "expires_at" field.
func ExpiresAtNotIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldExpiresAt, vs...))
}

// ExpiresAtGT applies the GT predicate on the "expires_at" field.
func ExpiresAtGT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldExpiresAt, v))
}

// ExpiresAtGTE applies the GTE predicate on the "expires_at" field.
func ExpiresAtGTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldExpiresAt, v))
}

// ExpiresAtLT applies the LT predicate on the "expires_at" field.
func ExpiresAtLT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldExpiresAt, v))
}

// ExpiresAtLTE applies the LTE predicate on the "expires_at" field.
func ExpiresAtLTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldExpiresAt, v))
}

// ConsumedAtEQ applies the EQ predicate on the "consumed_at" field.
func ConsumedAtEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldConsumedAt, v))
}

// ConsumedAtNEQ applies the NEQ predicate on the "consumed_at" field.
func ConsumedAtNEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldConsumedAt, v))
}

// ConsumedAtIn applies the In predicate on the "consumed_at" field.
func ConsumedAtIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldConsumedAt, vs...))
}

// ConsumedAtNotIn applies the NotIn predicate on the "consumed_at" field.
func ConsumedAtNotIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldConsumedAt, vs...))
}

// ConsumedAtGT applies the GT predicate on the "consumed_at" field.
func ConsumedAtGT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldConsumedAt, v))
}

// ConsumedAtGTE applies the GTE predicate on the "consumed_at" field.
func ConsumedAtGTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldConsumedAt, v))
}

// ConsumedAtLT applies the LT predicate on the "consumed_at" field.
func ConsumedAtLT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldConsumedAt, v))
}

// ConsumedAtLTE applies the LTE predicate on the "consumed_at" field.
func ConsumedAtLTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldConsumedAt, v))
}

// ConsumedAtIsNil applies the IsNil predicate on the "consumed_at" field.
func ConsumedAtIsNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIsNull(FieldConsumedAt))
}

// ConsumedAtNotNil applies the NotNil predicate on the "consumed_at" field.
func ConsumedAtNotNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotNull(FieldConsumedAt))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CreditReservation {
	return predicate.CreditReservation(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CreditReservation) predicate.CreditReservation {
	return predicate.CreditReservation(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CreditReservation) predicate.CreditReservation {
	return predicate.CreditReservation(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CreditReservation) predicate.CreditReservation {
	return predicate.CreditReservation(sql.NotPredicates(p))
}

package handlers

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/integration-service/usecase/integration"
)

// OAuthHandler handles OAuth callback endpoints
type OAuthHandler struct {
	*BaseHandler
}

// OAuthState represents the state parameter in OAuth flow
type OAuthState struct {
	UserID      string `json:"user_id"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	RedirectURL string `json:"redirect_url,omitempty"`
}

// NewOAuthHandler creates a new OAuth handler
func NewOAuthHandler(base *BaseHandler) *OAuthHandler {
	return &OAuthHandler{
		BaseHandler: base,
	}
}

// FacebookCallback handles Facebook OAuth callback
func (h *OAuthHandler) FacebookCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "facebook")
}

// TwitterCallback handles Twitter OAuth callback
func (h *OAuthHandler) TwitterCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "twitter")
}

// InstagramCallback handles Instagram OAuth callback
func (h *OAuthHandler) InstagramCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "instagram")
}

// LinkedInCallback handles LinkedIn OAuth callback
func (h *OAuthHandler) LinkedInCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "linkedin")
}

// YouTubeCallback handles YouTube OAuth callback
func (h *OAuthHandler) YouTubeCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "youtube")
}

// TikTokCallback handles TikTok OAuth callback
func (h *OAuthHandler) TikTokCallback(c *gin.Context) {
	h.handleOAuthCallback(c, "tiktok")
}

// parseOAuthState parses the base64 encoded state parameter
func (h *OAuthHandler) parseOAuthState(state string) (*OAuthState, error) {
	if state == "" {
		return nil, fmt.Errorf("state parameter is empty")
	}

	// Decode base64
	decoded, err := base64.URLEncoding.DecodeString(state)
	if err != nil {
		return nil, fmt.Errorf("failed to decode state: %w", err)
	}

	// Parse JSON
	var oauthState OAuthState
	if err := json.Unmarshal(decoded, &oauthState); err != nil {
		return nil, fmt.Errorf("failed to parse state JSON: %w", err)
	}

	if oauthState.UserID == "" {
		return nil, fmt.Errorf("user_id is required in state")
	}

	return &oauthState, nil
}

// redirectWithError redirects user to error page or returns JSON error
func (h *OAuthHandler) redirectWithError(c *gin.Context, errorCode, errorMessage string) {
	// Check if this is an API request (Accept header contains application/json)
	if c.GetHeader("Accept") == "application/json" || c.Query("format") == "json" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   errorCode,
			"message": errorMessage,
		})
		return
	}

	// Redirect to frontend error page
	redirectURL := fmt.Sprintf("http://localhost:3000/oauth/error?error=%s&message=%s",
		url.QueryEscape(errorCode),
		url.QueryEscape(errorMessage))

	c.Redirect(http.StatusFound, redirectURL)
}

// redirectWithSuccess redirects user to success page or returns JSON success
func (h *OAuthHandler) redirectWithSuccess(c *gin.Context, customRedirectURL, platform, accountID string) {
	// Check if this is an API request
	if c.GetHeader("Accept") == "application/json" || c.Query("format") == "json" {
		c.JSON(http.StatusOK, gin.H{
			"success":    true,
			"platform":   platform,
			"account_id": accountID,
			"message":    "Account connected successfully",
		})
		return
	}

	// Use custom redirect URL if provided, otherwise use default
	redirectURL := customRedirectURL
	if redirectURL == "" {
		redirectURL = "http://localhost:3000/oauth/success"
	}

	// Add query parameters
	if redirectURL != "" {
		separator := "?"
		if contains(redirectURL, "?") {
			separator = "&"
		}
		redirectURL = fmt.Sprintf("%s%splatform=%s&account_id=%s&status=success",
			redirectURL, separator, platform, accountID)
	}

	c.Redirect(http.StatusFound, redirectURL)
}

// contains checks if string contains substring
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// handleOAuthCallback is a generic OAuth callback handler
func (h *OAuthHandler) handleOAuthCallback(c *gin.Context, platform string) {
	h.logger.WithField("platform", platform).Info("OAuth callback received")

	code := c.Query("code")
	state := c.Query("state")
	errorParam := c.Query("error")

	if errorParam != "" {
		h.logger.WithFields(map[string]interface{}{
			"platform": platform,
			"error":    errorParam,
		}).Error("OAuth error")
		h.redirectWithError(c, "oauth_error", c.Query("error_description"))
		return
	}

	if code == "" {
		h.logger.WithField("platform", platform).Error("Missing authorization code")
		h.redirectWithError(c, "missing_code", "Authorization code not provided")
		return
	}

	// Parse state to get user information
	oauthState, err := h.parseOAuthState(state)
	if err != nil {
		h.logger.WithError(err).WithField("platform", platform).Error("Failed to parse OAuth state")
		h.redirectWithError(c, "invalid_state", "Invalid state parameter")
		return
	}

	// Connect account using integration service
	req := &integration.ConnectAccountRequest{
		UserID:      oauthState.UserID,
		WorkspaceID: oauthState.WorkspaceID,
		Platform:    platform,
		OAuthCode:   code,
		RedirectURI: c.Request.URL.String(), // Current callback URL
	}

	response, err := h.integrationUseCase.ConnectAccount(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).WithField("platform", platform).Error("Failed to connect account")
		h.redirectWithError(c, "connection_failed", "Failed to connect account")
		return
	}

	if !response.Success {
		h.logger.WithFields(map[string]interface{}{
			"platform": platform,
			"error":    response.ErrorMessage,
		}).Error("Account connection failed")
		h.redirectWithError(c, "connection_failed", response.ErrorMessage)
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"user_id":    oauthState.UserID,
		"account_id": response.AccountID,
		"platform":   platform,
	}).Info("Account connected successfully")

	// Redirect to success page
	h.redirectWithSuccess(c, oauthState.RedirectURL, platform, response.AccountID)
}

// GenerateOAuthState generates a base64 encoded state parameter
// This is a utility function that can be used by frontend applications
func GenerateOAuthState(userID, workspaceID, redirectURL string) (string, error) {
	state := OAuthState{
		UserID:      userID,
		WorkspaceID: workspaceID,
		RedirectURL: redirectURL,
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(state)
	if err != nil {
		return "", fmt.Errorf("failed to marshal state: %w", err)
	}

	// Encode to base64
	encoded := base64.URLEncoding.EncodeToString(jsonData)
	return encoded, nil
}

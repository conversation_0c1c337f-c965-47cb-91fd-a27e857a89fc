package payment

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// Processor defines the payment processing interface
type Processor interface {
	// Payment processing
	ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)
	RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error)
	CancelPayment(ctx context.Context, paymentID string) error
	GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error)

	// Payment methods
	CreatePaymentMethod(ctx context.Context, req *CreatePaymentMethodRequest) (*PaymentMethodResponse, error)
	UpdatePaymentMethod(ctx context.Context, req *UpdatePaymentMethodRequest) (*PaymentMethodResponse, error)
	DeletePaymentMethod(ctx context.Context, paymentMethodID string) error
	ListPaymentMethods(ctx context.Context, userID string) ([]*PaymentMethodResponse, error)

	// Subscriptions
	CreateSubscription(ctx context.Context, req *CreateSubscriptionRequest) (*SubscriptionResponse, error)
	UpdateSubscription(ctx context.Context, req *UpdateSubscriptionRequest) (*SubscriptionResponse, error)
	CancelSubscription(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error)
	GetSubscription(ctx context.Context, subscriptionID string) (*SubscriptionResponse, error)

	// Webhooks
	ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error)
	ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

// PaymentRequest represents a payment request
type PaymentRequest struct {
	UserID        string                 `json:"user_id"`
	Amount        int64                  `json:"amount"` // in cents
	Currency      string                 `json:"currency"`
	PaymentMethod string                 `json:"payment_method"`
	Description   string                 `json:"description"`
	OrderID       string                 `json:"order_id"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentResponse represents a payment response
type PaymentResponse struct {
	PaymentID   string                 `json:"payment_id"`
	Status      string                 `json:"status"`
	Amount      int64                  `json:"amount"`
	Currency    string                 `json:"currency"`
	PaymentURL  string                 `json:"payment_url,omitempty"`
	PaymentInfo map[string]interface{} `json:"payment_info,omitempty"`
	ExpiresAt   string                 `json:"expires_at,omitempty"`
}

// RefundRequest represents a refund request
type RefundRequest struct {
	PaymentID string `json:"payment_id"`
	Amount    int64  `json:"amount,omitempty"` // partial refund amount, omit for full refund
	Reason    string `json:"reason"`
}

// RefundResponse represents a refund response
type RefundResponse struct {
	RefundID  string `json:"refund_id"`
	PaymentID string `json:"payment_id"`
	Amount    int64  `json:"amount"`
	Status    string `json:"status"`
	Reason    string `json:"reason"`
}

// PaymentStatus represents payment status
type PaymentStatus struct {
	PaymentID     string `json:"payment_id"`
	Status        string `json:"status"`
	Amount        int64  `json:"amount"`
	Currency      string `json:"currency"`
	FailureReason string `json:"failure_reason,omitempty"`
	ProcessedAt   string `json:"processed_at,omitempty"`
}

// CreatePaymentMethodRequest represents create payment method request
type CreatePaymentMethodRequest struct {
	UserID    string                 `json:"user_id"`
	Type      string                 `json:"type"` // card, bank_account, etc.
	Details   map[string]interface{} `json:"details"`
	IsDefault bool                   `json:"is_default"`
}

// UpdatePaymentMethodRequest represents update payment method request
type UpdatePaymentMethodRequest struct {
	PaymentMethodID string                 `json:"payment_method_id"`
	Details         map[string]interface{} `json:"details,omitempty"`
	IsDefault       *bool                  `json:"is_default,omitempty"`
}

// PaymentMethodResponse represents payment method response
type PaymentMethodResponse struct {
	PaymentMethodID string                 `json:"payment_method_id"`
	UserID          string                 `json:"user_id"`
	Type            string                 `json:"type"`
	Details         map[string]interface{} `json:"details"`
	IsDefault       bool                   `json:"is_default"`
	CreatedAt       string                 `json:"created_at"`
	UpdatedAt       string                 `json:"updated_at"`
}

// CreateSubscriptionRequest represents create subscription request
type CreateSubscriptionRequest struct {
	UserID          string                 `json:"user_id"`
	PlanID          string                 `json:"plan_id"`
	PaymentMethodID string                 `json:"payment_method_id"`
	TrialDays       int                    `json:"trial_days,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateSubscriptionRequest represents update subscription request
type UpdateSubscriptionRequest struct {
	SubscriptionID  string                 `json:"subscription_id"`
	PlanID          string                 `json:"plan_id,omitempty"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionResponse represents subscription response
type SubscriptionResponse struct {
	SubscriptionID     string                 `json:"subscription_id"`
	UserID             string                 `json:"user_id"`
	PlanID             string                 `json:"plan_id"`
	Status             string                 `json:"status"`
	CurrentPeriodStart string                 `json:"current_period_start"`
	CurrentPeriodEnd   string                 `json:"current_period_end"`
	TrialStart         string                 `json:"trial_start,omitempty"`
	TrialEnd           string                 `json:"trial_end,omitempty"`
	CancelledAt        string                 `json:"cancelled_at,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
}

// WebhookEvent represents a webhook event
type WebhookEvent struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt string                 `json:"created_at"`
}

// Provider-specific processors
type StripeProcessor interface {
	Processor
	CreateStripePaymentIntent(ctx context.Context, req *PaymentRequest) (*models.StripePaymentInfo, error)
	HandleStripeWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error)
}

type MomoProcessor interface {
	Processor
	CreateMomoPayment(ctx context.Context, req *PaymentRequest) (*models.MomoPaymentInfo, error)
	HandleMomoWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

type ZaloPayProcessor interface {
	Processor
	CreateZaloPayPayment(ctx context.Context, req *PaymentRequest) (*models.ZaloPayPaymentInfo, error)
	HandleZaloPayWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

type PayPalProcessor interface {
	Processor
	CreatePayPalPayment(ctx context.Context, req *PaymentRequest) (*models.PayPalPaymentInfo, error)
	HandlePayPalWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error)
	ExecutePayPalPayment(ctx context.Context, paymentID, payerID string) (*PaymentResponse, error)
}

type BankTransferProcessor interface {
	Processor
	CreateBankTransfer(ctx context.Context, req *PaymentRequest) (*models.BankTransferInfo, error)
	VerifyBankTransfer(ctx context.Context, transferCode string) (*PaymentStatus, error)
}

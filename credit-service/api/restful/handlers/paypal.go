package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PayPalHandler handles PayPal payment REST API endpoints
type PayPalHandler struct {
	paypalUseCase paypal.UseCase
	logger        logging.Logger
	validator     *validator.Validate
}

// NewPayPalHandler creates a new PayPal handler
func NewPayPalHandler(paypalUseCase paypal.UseCase, logger logging.Logger) *PayPalHandler {
	return &PayPalHandler{
		paypalUseCase: paypalUseCase,
		logger:        logger,
		validator:     validator.New(),
	}
}

// InitiatePayment initiates a PayPal payment
// @Summary Initiate PayPal payment
// @Description Initiate a new PayPal payment for subscription or credit topup
// @Tags PayPal
// @Accept json
// @Produce json
// @Param request body models.InitiatePayPalPaymentRequest true "Payment initiation request"
// @Success 200 {object} models.InitiatePayPalPaymentResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/initiate [post]
func (h *PayPalHandler) InitiatePayment(c *gin.Context) {
	var req models.InitiatePayPalPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	// Get user ID from JWT token in context
	userID, err := getUserIDFromContext(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user ID from context")
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User authentication required",
		})
		return
	}

	req.UserID = userID

	// Validate request
	if err := h.validateStruct(req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
		})
		return
	}

	// Call use case
	response, err := h.paypalUseCase.InitiatePayment(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to initiate PayPal payment")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to initiate PayPal payment",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ExecutePayment executes a PayPal payment after user approval
// @Summary Execute PayPal payment
// @Description Execute a PayPal payment after user approval
// @Tags PayPal
// @Accept json
// @Produce json
// @Param request body models.ExecutePayPalPaymentRequest true "Payment execution request"
// @Success 200 {object} models.ExecutePayPalPaymentResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/execute [post]
func (h *PayPalHandler) ExecutePayment(c *gin.Context) {
	var req models.ExecutePayPalPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	// Get user ID from JWT token in context
	userID, err := getUserIDFromContext(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user ID from context")
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User authentication required",
		})
		return
	}

	req.UserID = userID

	// Validate request
	if err := h.validateStruct(req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
		})
		return
	}

	// Call use case
	response, err := h.paypalUseCase.ExecutePayment(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to execute PayPal payment")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to execute PayPal payment",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPaymentStatus gets the status of a PayPal payment
// @Summary Get PayPal payment status
// @Description Get the status of a PayPal payment
// @Tags PayPal
// @Produce json
// @Param payment_id path string true "Payment ID"
// @Success 200 {object} models.PayPalPaymentInfo
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/payments/{payment_id} [get]
func (h *PayPalHandler) GetPaymentStatus(c *gin.Context) {
	paymentID := c.Param("payment_id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: "Payment ID is required",
		})
		return
	}

	// Get user ID from JWT token in context
	userID, err := getUserIDFromContext(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user ID from context")
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User authentication required",
		})
		return
	}

	// Call use case
	paymentInfo, err := h.paypalUseCase.GetPaymentStatus(c.Request.Context(), paymentID, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get PayPal payment status")
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Not found",
				Message: "Payment not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to get PayPal payment status",
		})
		return
	}

	c.JSON(http.StatusOK, paymentInfo)
}

// CancelPayment cancels a PayPal payment
// @Summary Cancel PayPal payment
// @Description Cancel a PayPal payment
// @Tags PayPal
// @Accept json
// @Produce json
// @Param payment_id path string true "Payment ID"
// @Param request body models.CancelPayPalPaymentRequest true "Cancel request"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/payments/{payment_id}/cancel [post]
func (h *PayPalHandler) CancelPayment(c *gin.Context) {
	paymentID := c.Param("payment_id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: "Payment ID is required",
		})
		return
	}

	var req models.CancelPayPalPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	// Get user ID from JWT token in context
	userID, err := getUserIDFromContext(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user ID from context")
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User authentication required",
		})
		return
	}

	req.PaymentID = paymentID
	req.UserID = userID

	// Call use case
	err = h.paypalUseCase.CancelPayment(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel PayPal payment")
		if err.Error() == "payment not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Not found",
				Message: "Payment not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to cancel PayPal payment",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "Payment cancelled successfully",
	})
}

// ListPayments lists PayPal payments for a user
// @Summary List PayPal payments
// @Description List PayPal payments for the authenticated user
// @Tags PayPal
// @Produce json
// @Param status query string false "Payment status filter"
// @Param purpose query string false "Payment purpose filter"
// @Param date_from query string false "Date from filter (YYYY-MM-DD)"
// @Param date_to query string false "Date to filter (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.ListPayPalPaymentsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/payments [get]
func (h *PayPalHandler) ListPayments(c *gin.Context) {
	// Get user ID from JWT token in context
	userID, err := getUserIDFromContext(c)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user ID from context")
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User authentication required",
		})
		return
	}

	// Parse query parameters
	req := models.ListPayPalPaymentsRequest{
		UserID:  userID,
		Status:  c.Query("status"),
		Purpose: c.Query("purpose"),
		Page:    1,
		Limit:   20,
	}

	// Parse pagination
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			req.Limit = limit
		}
	}

	// Parse date filters
	if dateFromStr := c.Query("date_from"); dateFromStr != "" {
		if dateFrom, err := time.Parse("2006-01-02", dateFromStr); err == nil {
			req.DateFrom = &dateFrom
		}
	}

	if dateToStr := c.Query("date_to"); dateToStr != "" {
		if dateTo, err := time.Parse("2006-01-02", dateToStr); err == nil {
			req.DateTo = &dateTo
		}
	}

	// Call use case
	response, err := h.paypalUseCase.ListPayments(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list PayPal payments")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to list PayPal payments",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// HandleWebhook handles PayPal webhook events
// @Summary Handle PayPal webhook
// @Description Handle PayPal webhook events
// @Tags PayPal
// @Accept json
// @Produce json
// @Param request body models.PayPalWebhookRequest true "Webhook request"
// @Success 200 {object} models.PayPalWebhookResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/paypal/webhook [post]
func (h *PayPalHandler) HandleWebhook(c *gin.Context) {
	var req models.PayPalWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid webhook request body")
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	// TODO: Validate webhook signature
	// headers := make(map[string]string)
	// for key, values := range c.Request.Header {
	//     if len(values) > 0 {
	//         headers[key] = values[0]
	//     }
	// }

	// Call use case
	response, err := h.paypalUseCase.HandleWebhook(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to handle PayPal webhook")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Internal server error",
			Message: "Failed to handle PayPal webhook",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Response types for consistent API responses
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

type SuccessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// Helper functions

// validateStruct validates a struct using the validator
func (h *PayPalHandler) validateStruct(s interface{}) error {
	return h.validator.Struct(s)
}

// getUserIDFromContext extracts user ID from JWT token in context
func getUserIDFromContext(c *gin.Context) (string, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", fmt.Errorf("user ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return "", fmt.Errorf("invalid user ID type")
	}

	return userIDStr, nil
}

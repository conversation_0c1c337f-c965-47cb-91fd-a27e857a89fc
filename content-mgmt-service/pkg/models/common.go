package models

import "time"

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// BaseModel represents common fields for all models
type BaseModel struct {
	ID        string     `json:"id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
}

// Platform represents social media platform
type Platform struct {
	Name     string `json:"name"`
	Enabled  bool   `json:"enabled"`
	Settings map[string]interface{} `json:"settings,omitempty"`
}

// MediaAsset represents a media asset (image, video, etc.)
type MediaAsset struct {
	ID       string `json:"id"`
	URL      string `json:"url"`
	Type     string `json:"type"` // image, video, document
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	Duration int    `json:"duration,omitempty"` // for videos
}

// Analytics represents analytics data
type Analytics struct {
	Views       int64   `json:"views"`
	Likes       int64   `json:"likes"`
	Comments    int64   `json:"comments"`
	Shares      int64   `json:"shares"`
	Engagement  int64   `json:"engagement"`
	Reach       int64   `json:"reach"`
	Impressions int64   `json:"impressions"`
	CTR         float64 `json:"ctr"` // Click-through rate
}

// ScheduleSettings represents scheduling settings
type ScheduleSettings struct {
	Timezone     string    `json:"timezone"`
	ScheduledAt  time.Time `json:"scheduled_at"`
	AutoPublish  bool      `json:"auto_publish"`
	OptimalTime  bool      `json:"optimal_time"`
	Recurring    bool      `json:"recurring"`
	RecurringPattern string `json:"recurring_pattern,omitempty"`
}

// PublishStatus represents publishing status
type PublishStatus struct {
	Platform  string    `json:"platform"`
	Status    string    `json:"status"` // pending, published, failed
	PostID    string    `json:"post_id,omitempty"`
	URL       string    `json:"url,omitempty"`
	Error     string    `json:"error,omitempty"`
	PublishedAt *time.Time `json:"published_at,omitempty"`
}

// ContentVariation represents content variation for different platforms
type ContentVariation struct {
	Platform string `json:"platform"`
	Content  string `json:"content"`
	Title    string `json:"title,omitempty"`
	Tags     []string `json:"tags,omitempty"`
	Hashtags []string `json:"hashtags,omitempty"`
}

// AIGenerationInfo represents AI generation metadata
type AIGenerationInfo struct {
	Model       string                 `json:"model"`
	Prompt      string                 `json:"prompt"`
	Temperature float64                `json:"temperature"`
	MaxTokens   int                    `json:"max_tokens"`
	CreditCost  int                    `json:"credit_cost"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// BulkOperationResult represents result of bulk operations
type BulkOperationResult struct {
	Total     int                    `json:"total"`
	Success   int                    `json:"success"`
	Failed    int                    `json:"failed"`
	Errors    []BulkOperationError   `json:"errors,omitempty"`
	Results   []interface{}          `json:"results,omitempty"`
}

// BulkOperationError represents an error in bulk operations
type BulkOperationError struct {
	ID     string `json:"id,omitempty"`
	Index  int    `json:"index"`
	Error  string `json:"error"`
	Reason string `json:"reason"`
}

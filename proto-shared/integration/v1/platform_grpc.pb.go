// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: integration/v1/platform.proto

package integrationv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PlatformIntegrationServiceClient is the client API for PlatformIntegrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PlatformIntegrationServiceClient interface {
	// Account connection operations
	GetOAuthUrl(ctx context.Context, in *GetOAuthUrlRequest, opts ...grpc.CallOption) (*GetOAuthUrlResponse, error)
	ConnectAccount(ctx context.Context, in *ConnectAccountRequest, opts ...grpc.CallOption) (*ConnectAccountResponse, error)
	RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*RefreshTokenResponse, error)
	DisconnectAccount(ctx context.Context, in *DisconnectAccountRequest, opts ...grpc.CallOption) (*Empty, error)
	ListAccounts(ctx context.Context, in *ListAccountsRequest, opts ...grpc.CallOption) (*ListAccountsResponse, error)
	ValidateAccount(ctx context.Context, in *ValidateAccountRequest, opts ...grpc.CallOption) (*ValidateAccountResponse, error)
	// Publishing operations
	PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error)
	// Analytics operations
	GetPostAnalytics(ctx context.Context, in *GetPostAnalyticsRequest, opts ...grpc.CallOption) (*PostAnalytics, error)
	GetAccountAnalytics(ctx context.Context, in *GetAccountAnalyticsRequest, opts ...grpc.CallOption) (*AccountAnalytics, error)
	// Platform information
	GetPlatformLimits(ctx context.Context, in *GetPlatformLimitsRequest, opts ...grpc.CallOption) (*PlatformLimits, error)
	// Webhook operations
	ProcessWebhook(ctx context.Context, in *ProcessWebhookRequest, opts ...grpc.CallOption) (*ProcessWebhookResponse, error)
}

type platformIntegrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlatformIntegrationServiceClient(cc grpc.ClientConnInterface) PlatformIntegrationServiceClient {
	return &platformIntegrationServiceClient{cc}
}

func (c *platformIntegrationServiceClient) GetOAuthUrl(ctx context.Context, in *GetOAuthUrlRequest, opts ...grpc.CallOption) (*GetOAuthUrlResponse, error) {
	out := new(GetOAuthUrlResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/GetOAuthUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) ConnectAccount(ctx context.Context, in *ConnectAccountRequest, opts ...grpc.CallOption) (*ConnectAccountResponse, error) {
	out := new(ConnectAccountResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/ConnectAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*RefreshTokenResponse, error) {
	out := new(RefreshTokenResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/RefreshToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) DisconnectAccount(ctx context.Context, in *DisconnectAccountRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/DisconnectAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) ListAccounts(ctx context.Context, in *ListAccountsRequest, opts ...grpc.CallOption) (*ListAccountsResponse, error) {
	out := new(ListAccountsResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/ListAccounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) ValidateAccount(ctx context.Context, in *ValidateAccountRequest, opts ...grpc.CallOption) (*ValidateAccountResponse, error) {
	out := new(ValidateAccountResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/ValidateAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error) {
	out := new(PublishPostResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/PublishPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) GetPostAnalytics(ctx context.Context, in *GetPostAnalyticsRequest, opts ...grpc.CallOption) (*PostAnalytics, error) {
	out := new(PostAnalytics)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/GetPostAnalytics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) GetAccountAnalytics(ctx context.Context, in *GetAccountAnalyticsRequest, opts ...grpc.CallOption) (*AccountAnalytics, error) {
	out := new(AccountAnalytics)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/GetAccountAnalytics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) GetPlatformLimits(ctx context.Context, in *GetPlatformLimitsRequest, opts ...grpc.CallOption) (*PlatformLimits, error) {
	out := new(PlatformLimits)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/GetPlatformLimits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformIntegrationServiceClient) ProcessWebhook(ctx context.Context, in *ProcessWebhookRequest, opts ...grpc.CallOption) (*ProcessWebhookResponse, error) {
	out := new(ProcessWebhookResponse)
	err := c.cc.Invoke(ctx, "/integration.v1.PlatformIntegrationService/ProcessWebhook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlatformIntegrationServiceServer is the server API for PlatformIntegrationService service.
// All implementations must embed UnimplementedPlatformIntegrationServiceServer
// for forward compatibility
type PlatformIntegrationServiceServer interface {
	// Account connection operations
	GetOAuthUrl(context.Context, *GetOAuthUrlRequest) (*GetOAuthUrlResponse, error)
	ConnectAccount(context.Context, *ConnectAccountRequest) (*ConnectAccountResponse, error)
	RefreshToken(context.Context, *RefreshTokenRequest) (*RefreshTokenResponse, error)
	DisconnectAccount(context.Context, *DisconnectAccountRequest) (*Empty, error)
	ListAccounts(context.Context, *ListAccountsRequest) (*ListAccountsResponse, error)
	ValidateAccount(context.Context, *ValidateAccountRequest) (*ValidateAccountResponse, error)
	// Publishing operations
	PublishPost(context.Context, *PublishPostRequest) (*PublishPostResponse, error)
	// Analytics operations
	GetPostAnalytics(context.Context, *GetPostAnalyticsRequest) (*PostAnalytics, error)
	GetAccountAnalytics(context.Context, *GetAccountAnalyticsRequest) (*AccountAnalytics, error)
	// Platform information
	GetPlatformLimits(context.Context, *GetPlatformLimitsRequest) (*PlatformLimits, error)
	// Webhook operations
	ProcessWebhook(context.Context, *ProcessWebhookRequest) (*ProcessWebhookResponse, error)
	mustEmbedUnimplementedPlatformIntegrationServiceServer()
}

// UnimplementedPlatformIntegrationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPlatformIntegrationServiceServer struct {
}

func (UnimplementedPlatformIntegrationServiceServer) GetOAuthUrl(context.Context, *GetOAuthUrlRequest) (*GetOAuthUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOAuthUrl not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) ConnectAccount(context.Context, *ConnectAccountRequest) (*ConnectAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConnectAccount not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) RefreshToken(context.Context, *RefreshTokenRequest) (*RefreshTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) DisconnectAccount(context.Context, *DisconnectAccountRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisconnectAccount not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) ListAccounts(context.Context, *ListAccountsRequest) (*ListAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccounts not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) ValidateAccount(context.Context, *ValidateAccountRequest) (*ValidateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAccount not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) PublishPost(context.Context, *PublishPostRequest) (*PublishPostResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishPost not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) GetPostAnalytics(context.Context, *GetPostAnalyticsRequest) (*PostAnalytics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostAnalytics not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) GetAccountAnalytics(context.Context, *GetAccountAnalyticsRequest) (*AccountAnalytics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountAnalytics not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) GetPlatformLimits(context.Context, *GetPlatformLimitsRequest) (*PlatformLimits, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatformLimits not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) ProcessWebhook(context.Context, *ProcessWebhookRequest) (*ProcessWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessWebhook not implemented")
}
func (UnimplementedPlatformIntegrationServiceServer) mustEmbedUnimplementedPlatformIntegrationServiceServer() {
}

// UnsafePlatformIntegrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlatformIntegrationServiceServer will
// result in compilation errors.
type UnsafePlatformIntegrationServiceServer interface {
	mustEmbedUnimplementedPlatformIntegrationServiceServer()
}

func RegisterPlatformIntegrationServiceServer(s grpc.ServiceRegistrar, srv PlatformIntegrationServiceServer) {
	s.RegisterService(&PlatformIntegrationService_ServiceDesc, srv)
}

func _PlatformIntegrationService_GetOAuthUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOAuthUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).GetOAuthUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/GetOAuthUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).GetOAuthUrl(ctx, req.(*GetOAuthUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_ConnectAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).ConnectAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/ConnectAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).ConnectAccount(ctx, req.(*ConnectAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/RefreshToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).RefreshToken(ctx, req.(*RefreshTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_DisconnectAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisconnectAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).DisconnectAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/DisconnectAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).DisconnectAccount(ctx, req.(*DisconnectAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_ListAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).ListAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/ListAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).ListAccounts(ctx, req.(*ListAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_ValidateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).ValidateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/ValidateAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).ValidateAccount(ctx, req.(*ValidateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_PublishPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).PublishPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/PublishPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).PublishPost(ctx, req.(*PublishPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_GetPostAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).GetPostAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/GetPostAnalytics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).GetPostAnalytics(ctx, req.(*GetPostAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_GetAccountAnalytics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountAnalyticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).GetAccountAnalytics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/GetAccountAnalytics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).GetAccountAnalytics(ctx, req.(*GetAccountAnalyticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_GetPlatformLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatformLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).GetPlatformLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/GetPlatformLimits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).GetPlatformLimits(ctx, req.(*GetPlatformLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformIntegrationService_ProcessWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformIntegrationServiceServer).ProcessWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/integration.v1.PlatformIntegrationService/ProcessWebhook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformIntegrationServiceServer).ProcessWebhook(ctx, req.(*ProcessWebhookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlatformIntegrationService_ServiceDesc is the grpc.ServiceDesc for PlatformIntegrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlatformIntegrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "integration.v1.PlatformIntegrationService",
	HandlerType: (*PlatformIntegrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOAuthUrl",
			Handler:    _PlatformIntegrationService_GetOAuthUrl_Handler,
		},
		{
			MethodName: "ConnectAccount",
			Handler:    _PlatformIntegrationService_ConnectAccount_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _PlatformIntegrationService_RefreshToken_Handler,
		},
		{
			MethodName: "DisconnectAccount",
			Handler:    _PlatformIntegrationService_DisconnectAccount_Handler,
		},
		{
			MethodName: "ListAccounts",
			Handler:    _PlatformIntegrationService_ListAccounts_Handler,
		},
		{
			MethodName: "ValidateAccount",
			Handler:    _PlatformIntegrationService_ValidateAccount_Handler,
		},
		{
			MethodName: "PublishPost",
			Handler:    _PlatformIntegrationService_PublishPost_Handler,
		},
		{
			MethodName: "GetPostAnalytics",
			Handler:    _PlatformIntegrationService_GetPostAnalytics_Handler,
		},
		{
			MethodName: "GetAccountAnalytics",
			Handler:    _PlatformIntegrationService_GetAccountAnalytics_Handler,
		},
		{
			MethodName: "GetPlatformLimits",
			Handler:    _PlatformIntegrationService_GetPlatformLimits_Handler,
		},
		{
			MethodName: "ProcessWebhook",
			Handler:    _PlatformIntegrationService_ProcessWebhook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "integration/v1/platform.proto",
}

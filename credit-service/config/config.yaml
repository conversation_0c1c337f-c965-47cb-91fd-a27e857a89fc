# Credit Service Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50053
  http_port: 8083
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"

  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "credit_service_db"
  ssl_mode: "disable"

  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/credit_service.db"

  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# Payment configuration
payment:
  stripe:
    secret_key: "${STRIPE_SECRET_KEY}"
    publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
    webhook_secret: "${STRIPE_WEBHOOK_SECRET}"

# External services configuration
services:
  user_service: "localhost:50051"

# Metrics configuration
metrics:
  port: 9093

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# JWT configuration
jwt:
  secret_key: "${JWT_SECRET_KEY:your-secret-key}"
  token_duration: "24h"
  issuer: "credit-service"

# Kafka configuration for Event-Driven Architecture
kafka:
  brokers:
    - "localhost:9092"
  group_id: "credit-service"

  # Topic configurations
  topics:
    credit_events: "credit.events"
    subscription_events: "subscription.events"
    payment_events: "payment.events"
    notification_events: "notification.events"

  # Producer settings
  producer:
    batch_size: 100
    batch_timeout: "10ms"
    compression: "snappy"

  # Consumer settings
  consumer:
    min_bytes: 10240      # 10KB
    max_bytes: 10485760   # 10MB
    commit_interval: "1s"

# PayPal configuration
paypal:
  client_id: "${PAYPAL_CLIENT_ID}"
  client_secret: "${PAYPAL_CLIENT_SECRET}"
  mode: "sandbox"  # "sandbox" or "live"
  webhook_id: "${PAYPAL_WEBHOOK_ID}"

# Credit system configuration
credit:
  # Default credit amounts for different actions
  defaults:
    welcome_bonus: 100
    referral_bonus: 50
    daily_login_bonus: 5

  # Credit limits
  limits:
    max_daily_earn: 500
    max_transfer_amount: 1000
    min_transfer_amount: 10

  # Credit expiration settings
  expiration:
    enabled: false
    days: 365

# Subscription configuration
subscription:
  # Trial settings
  trial:
    enabled: true
    default_days: 7

  # Grace period for failed payments
  grace_period_days: 3

  # Automatic renewal settings
  auto_renewal:
    enabled: true
    retry_attempts: 3
    retry_interval: "24h"

# Feature flags
features:
  paypal_enabled: false
  bank_transfer_enabled: false
  referral_system_enabled: true
  credit_expiration_enabled: false
  auto_subscription_renewal: true

# Security configuration
security:
  # CORS settings
  cors:
    enabled: true
    allowed_origins:
      - "http://localhost:3000"
      - "https://app.socialcontentai.com"
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:
      - "Authorization"
      - "Content-Type"
      - "X-Requested-With"

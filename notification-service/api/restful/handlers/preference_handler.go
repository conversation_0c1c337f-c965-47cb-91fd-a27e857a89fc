package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/notification-service/usecase/preference"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PreferenceHandler handles preference-related HTTP requests
type PreferenceHandler struct {
	preferenceUC preference.UseCase
	logger       logging.Logger
}

// NewPreferenceHandler creates a new preference handler
func NewPreferenceHandler(preferenceUC preference.UseCase, logger logging.Logger) *PreferenceHandler {
	return &PreferenceHandler{
		preferenceUC: preferenceUC,
		logger:       logger,
	}
}

// GetPreferences handles GET /api/v1/preferences
func (h *PreferenceHandler) GetPreferences(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.preferenceUC.GetPreferences(c.Request.Context(), userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get preferences")
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdatePreferences handles PUT /api/v1/preferences
func (h *PreferenceHandler) UpdatePreferences(c *gin.Context) {
	var req preference.UpdatePreferencesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Override user ID from token
	req.UserID = userID.(string)

	response, err := h.preferenceUC.UpdatePreferences(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update preferences")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ResetPreferences handles POST /api/v1/preferences/reset
func (h *PreferenceHandler) ResetPreferences(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.preferenceUC.ResetPreferences(c.Request.Context(), userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to reset preferences")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetDefaultPreferences handles GET /api/v1/preferences/defaults
func (h *PreferenceHandler) GetDefaultPreferences(c *gin.Context) {
	response, err := h.preferenceUC.GetDefaultPreferences(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get default preferences")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

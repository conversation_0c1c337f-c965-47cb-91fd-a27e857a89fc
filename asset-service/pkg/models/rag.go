package models

import "time"

// RAGDocumentResponse represents a RAG document response
type RAGDocumentResponse struct {
	BaseModel
	UserID          string                 `json:"user_id"`
	AssetID         string                 `json:"asset_id,omitempty"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description,omitempty"`
	Category        string                 `json:"category,omitempty"`
	Language        string                 `json:"language,omitempty"`
	Content         string                 `json:"content,omitempty"`
	ExtractedText   string                 `json:"extracted_text,omitempty"`
	ProcessingStatus RAGProcessingStatus   `json:"processing_status"`
	Embeddings      []RAGEmbedding         `json:"embeddings,omitempty"`
	Chunks          []RAGChunk             `json:"chunks,omitempty"`
	Keywords        []string               `json:"keywords,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	IndexedAt       *time.Time             `json:"indexed_at,omitempty"`
	LastTrainedAt   *time.Time             `json:"last_trained_at,omitempty"`
	UsageCount      int64                  `json:"usage_count"`
	FileInfo        *FileInfo              `json:"file_info,omitempty"`
}

// RAGProcessingStatus represents RAG processing status
type RAGProcessingStatus struct {
	Status        string    `json:"status"` // pending, processing, indexed, failed
	Progress      int       `json:"progress"` // 0-100
	StartedAt     time.Time `json:"started_at"`
	CompletedAt   *time.Time `json:"completed_at,omitempty"`
	Error         string    `json:"error,omitempty"`
	Steps         []RAGProcessingStep `json:"steps,omitempty"`
	ChunkCount    int       `json:"chunk_count"`
	EmbeddingCount int      `json:"embedding_count"`
}

// RAGProcessingStep represents a RAG processing step
type RAGProcessingStep struct {
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	Progress    int       `json:"progress"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Error       string    `json:"error,omitempty"`
	Details     map[string]interface{} `json:"details,omitempty"`
}

// RAGEmbedding represents a RAG embedding
type RAGEmbedding struct {
	ID        string    `json:"id"`
	ChunkID   string    `json:"chunk_id"`
	Vector    []float64 `json:"vector,omitempty"`
	Model     string    `json:"model"`
	Dimension int       `json:"dimension"`
	CreatedAt time.Time `json:"created_at"`
}

// RAGChunk represents a RAG text chunk
type RAGChunk struct {
	ID        string                 `json:"id"`
	Content   string                 `json:"content"`
	StartPos  int                    `json:"start_pos"`
	EndPos    int                    `json:"end_pos"`
	TokenCount int                   `json:"token_count"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Embedding *RAGEmbedding          `json:"embedding,omitempty"`
}

// UploadRAGDocumentRequest represents upload RAG document request
type UploadRAGDocumentRequest struct {
	UserID      string                 `json:"user_id"`
	AssetID     string                 `json:"asset_id,omitempty"`
	Name        string                 `json:"name" validate:"required"`
	Description string                 `json:"description,omitempty"`
	Category    string                 `json:"category,omitempty"`
	Language    string                 `json:"language,omitempty"`
	Content     string                 `json:"content,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	AutoProcess bool                   `json:"auto_process"`
}

// UpdateRAGDocumentRequest represents update RAG document request
type UpdateRAGDocumentRequest struct {
	DocumentID  string                 `json:"document_id"`
	UserID      string                 `json:"user_id"`
	Name        string                 `json:"name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Category    string                 `json:"category,omitempty"`
	Language    string                 `json:"language,omitempty"`
	Content     string                 `json:"content,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Reprocess   bool                   `json:"reprocess"`
}

// ListRAGDocumentsRequest represents list RAG documents request
type ListRAGDocumentsRequest struct {
	UserID    string   `json:"user_id"`
	Category  string   `json:"category,omitempty"`
	Language  string   `json:"language,omitempty"`
	Status    string   `json:"status,omitempty"`
	Search    string   `json:"search,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	Page      int      `json:"page" validate:"min=1"`
	Limit     int      `json:"limit" validate:"min=1,max=100"`
	SortBy    string   `json:"sort_by,omitempty"`
	SortOrder string   `json:"sort_order,omitempty"`
}

// ListRAGDocumentsResponse represents list RAG documents response
type ListRAGDocumentsResponse struct {
	Documents  []RAGDocumentResponse `json:"documents"`
	Pagination PaginationMeta        `json:"pagination"`
	Filters    RAGFilters            `json:"filters"`
}

// RAGFilters represents available RAG filters
type RAGFilters struct {
	Categories []string `json:"categories"`
	Languages  []string `json:"languages"`
	Tags       []string `json:"tags"`
	Statuses   []string `json:"statuses"`
}

// SearchRAGDocumentsRequest represents search RAG documents request
type SearchRAGDocumentsRequest struct {
	UserID     string   `json:"user_id"`
	Query      string   `json:"query" validate:"required,min=1"`
	Category   string   `json:"category,omitempty"`
	Language   string   `json:"language,omitempty"`
	Tags       []string `json:"tags,omitempty"`
	Limit      int      `json:"limit" validate:"min=1,max=100"`
	Threshold  float64  `json:"threshold,omitempty" validate:"min=0,max=1"`
	Method     string   `json:"method,omitempty"` // keyword, semantic, hybrid
}

// SearchRAGDocumentsResponse represents search RAG documents response
type SearchRAGDocumentsResponse struct {
	Query     string              `json:"query"`
	Method    string              `json:"method"`
	Results   []RAGSearchResult   `json:"results"`
	Total     int                 `json:"total"`
	TimeTaken float64             `json:"time_taken"`
}

// RAGSearchResult represents a RAG search result
type RAGSearchResult struct {
	Document   RAGDocumentResponse `json:"document"`
	Chunk      *RAGChunk           `json:"chunk,omitempty"`
	Score      float64             `json:"score"`
	Highlights []string            `json:"highlights,omitempty"`
	Context    string              `json:"context,omitempty"`
}

// SemanticSearchRequest represents semantic search request
type SemanticSearchRequest struct {
	UserID     string   `json:"user_id"`
	Query      string   `json:"query" validate:"required,min=1"`
	Category   string   `json:"category,omitempty"`
	Language   string   `json:"language,omitempty"`
	Tags       []string `json:"tags,omitempty"`
	Limit      int      `json:"limit" validate:"min=1,max=100"`
	Threshold  float64  `json:"threshold,omitempty" validate:"min=0,max=1"`
	Model      string   `json:"model,omitempty"`
}

// ProcessRAGDocumentRequest represents process RAG document request
type ProcessRAGDocumentRequest struct {
	DocumentID string                 `json:"document_id"`
	UserID     string                 `json:"user_id"`
	Options    RAGProcessingOptions   `json:"options,omitempty"`
}

// RAGProcessingOptions represents RAG processing options
type RAGProcessingOptions struct {
	ChunkSize     int                    `json:"chunk_size,omitempty"`
	ChunkOverlap  int                    `json:"chunk_overlap,omitempty"`
	Model         string                 `json:"model,omitempty"`
	Language      string                 `json:"language,omitempty"`
	ExtractImages bool                   `json:"extract_images"`
	ExtractTables bool                   `json:"extract_tables"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// RAGCategoriesResponse represents RAG categories response
type RAGCategoriesResponse struct {
	Categories []RAGCategory `json:"categories"`
}

// RAGCategory represents a RAG category
type RAGCategory struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Count       int64  `json:"count"`
	Icon        string `json:"icon,omitempty"`
}

// RAGAnalyticsResponse represents RAG analytics
type RAGAnalyticsResponse struct {
	Period            string                     `json:"period"`
	TotalDocuments    int64                      `json:"total_documents"`
	TotalChunks       int64                      `json:"total_chunks"`
	TotalEmbeddings   int64                      `json:"total_embeddings"`
	ProcessingStats   RAGProcessingStats         `json:"processing_stats"`
	UsageStats        RAGUsageStats              `json:"usage_stats"`
	Timeline          []RAGTimelinePoint         `json:"timeline"`
	ByCategory        map[string]int64           `json:"by_category"`
	ByLanguage        map[string]int64           `json:"by_language"`
	TopDocuments      []RAGDocumentUsageStats    `json:"top_documents"`
}

// RAGProcessingStats represents RAG processing statistics
type RAGProcessingStats struct {
	AverageProcessTime float64 `json:"average_process_time"`
	SuccessRate        float64 `json:"success_rate"`
	ErrorRate          float64 `json:"error_rate"`
	QueueSize          int64   `json:"queue_size"`
}

// RAGUsageStats represents RAG usage statistics
type RAGUsageStats struct {
	TotalSearches      int64   `json:"total_searches"`
	AverageSearchTime  float64 `json:"average_search_time"`
	TopQueries         []string `json:"top_queries"`
	SearchSuccessRate  float64 `json:"search_success_rate"`
}

// RAGTimelinePoint represents a point in RAG timeline
type RAGTimelinePoint struct {
	Date            string `json:"date"`
	DocumentsAdded  int64  `json:"documents_added"`
	DocumentsProcessed int64 `json:"documents_processed"`
	Searches        int64  `json:"searches"`
	ProcessingTime  float64 `json:"processing_time"`
}

// RAGDocumentUsageStats represents RAG document usage statistics
type RAGDocumentUsageStats struct {
	Document    RAGDocumentResponse `json:"document"`
	SearchCount int64               `json:"search_count"`
	LastUsed    time.Time           `json:"last_used"`
}

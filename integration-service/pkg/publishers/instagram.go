package publishers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// InstagramPublisher handles Instagram content publishing
type InstagramPublisher struct {
	httpClient *http.Client
	logger     logging.Logger
}

// NewInstagramPublisher creates a new Instagram publisher
func NewInstagramPublisher(logger logging.Logger) Publisher {
	return &InstagramPublisher{
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// InstagramMediaRequest represents an Instagram media creation request
type InstagramMediaRequest struct {
	ImageURL  string `json:"image_url,omitempty"`
	VideoURL  string `json:"video_url,omitempty"`
	Caption   string `json:"caption,omitempty"`
	MediaType string `json:"media_type,omitempty"`
}

// InstagramMediaResponse represents Instagram media creation response
type InstagramMediaResponse struct {
	ID string `json:"id"`
}

// InstagramPublishResponse represents Instagram publish response
type InstagramPublishResponse struct {
	ID string `json:"id"`
}

// InstagramInsightsResponse represents Instagram insights response
type InstagramInsightsResponse struct {
	Data []struct {
		Name   string `json:"name"`
		Period string `json:"period"`
		Values []struct {
			Value int64 `json:"value"`
		} `json:"values"`
	} `json:"data"`
}

// PublishPost publishes content to Instagram
func (i *InstagramPublisher) PublishPost(ctx context.Context, req *PublishRequest) (*PublishResponse, error) {
	i.logger.Info("Publishing post to Instagram")

	// Instagram requires at least one image or video
	if len(req.ImageURLs) == 0 && req.VideoURL == "" {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: "Instagram posts require at least one image or video",
		}, nil
	}

	// Prepare caption with hashtags
	caption := req.Content
	if len(req.Hashtags) > 0 {
		hashtags := make([]string, len(req.Hashtags))
		for j, tag := range req.Hashtags {
			if !strings.HasPrefix(tag, "#") {
				hashtags[j] = "#" + tag
			} else {
				hashtags[j] = tag
			}
		}
		caption += "\n\n" + strings.Join(hashtags, " ")
	}

	// Step 1: Create media container
	var mediaID string
	var err error

	if req.VideoURL != "" {
		mediaID, err = i.createVideoMedia(ctx, req.AccessToken, req.VideoURL, caption)
	} else {
		mediaID, err = i.createImageMedia(ctx, req.AccessToken, req.ImageURLs[0], caption)
	}

	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create media: %v", err),
		}, nil
	}

	// Step 2: Publish media
	publishedID, err := i.publishMedia(ctx, req.AccessToken, mediaID)
	if err != nil {
		return &PublishResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to publish media: %v", err),
		}, nil
	}

	platformURL := fmt.Sprintf("https://www.instagram.com/p/%s/", publishedID)

	return &PublishResponse{
		PlatformPostID: publishedID,
		PlatformURL:    platformURL,
		Success:        true,
		PublishedAt:    time.Now().Unix(),
		Metadata: map[string]string{
			"media_id": mediaID,
			"caption":  caption,
		},
	}, nil
}

// createImageMedia creates an Instagram image media container
func (i *InstagramPublisher) createImageMedia(ctx context.Context, accessToken, imageURL, caption string) (string, error) {
	mediaURL := "https://graph.instagram.com/me/media"

	data := url.Values{}
	data.Set("image_url", imageURL)
	data.Set("caption", caption)
	data.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "POST", mediaURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to create media: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("media creation failed with status: %d", resp.StatusCode)
	}

	var mediaResp InstagramMediaResponse
	if err := json.NewDecoder(resp.Body).Decode(&mediaResp); err != nil {
		return "", fmt.Errorf("failed to decode media response: %w", err)
	}

	return mediaResp.ID, nil
}

// createVideoMedia creates an Instagram video media container
func (i *InstagramPublisher) createVideoMedia(ctx context.Context, accessToken, videoURL, caption string) (string, error) {
	mediaURL := "https://graph.instagram.com/me/media"

	data := url.Values{}
	data.Set("video_url", videoURL)
	data.Set("caption", caption)
	data.Set("media_type", "VIDEO")
	data.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "POST", mediaURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to create video media: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("video media creation failed with status: %d", resp.StatusCode)
	}

	var mediaResp InstagramMediaResponse
	if err := json.NewDecoder(resp.Body).Decode(&mediaResp); err != nil {
		return "", fmt.Errorf("failed to decode video media response: %w", err)
	}

	return mediaResp.ID, nil
}

// publishMedia publishes the media container
func (i *InstagramPublisher) publishMedia(ctx context.Context, accessToken, mediaID string) (string, error) {
	publishURL := "https://graph.instagram.com/me/media_publish"

	data := url.Values{}
	data.Set("creation_id", mediaID)
	data.Set("access_token", accessToken)

	req, err := http.NewRequestWithContext(ctx, "POST", publishURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create publish request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := i.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to publish media: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("media publish failed with status: %d", resp.StatusCode)
	}

	var publishResp InstagramPublishResponse
	if err := json.NewDecoder(resp.Body).Decode(&publishResp); err != nil {
		return "", fmt.Errorf("failed to decode publish response: %w", err)
	}

	return publishResp.ID, nil
}

// GetPostAnalytics retrieves analytics for an Instagram post
func (i *InstagramPublisher) GetPostAnalytics(ctx context.Context, req *AnalyticsRequest) (*AnalyticsResponse, error) {
	i.logger.WithField("media_id", req.PlatformPostID).Info("Getting Instagram post analytics")

	insightsURL := fmt.Sprintf("https://graph.instagram.com/%s/insights", req.PlatformPostID)

	params := url.Values{}
	params.Set("metric", "impressions,reach,likes,comments,saves,shares")
	params.Set("access_token", req.AccessToken)

	httpReq, err := http.NewRequestWithContext(ctx, "GET", fmt.Sprintf("%s?%s", insightsURL, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := i.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get insights: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("insights request failed with status: %d", resp.StatusCode)
	}

	var insightsResp InstagramInsightsResponse
	if err := json.NewDecoder(resp.Body).Decode(&insightsResp); err != nil {
		return nil, fmt.Errorf("failed to decode insights response: %w", err)
	}

	metrics := make(map[string]int64)
	for _, insight := range insightsResp.Data {
		if len(insight.Values) > 0 {
			metrics[insight.Name] = insight.Values[0].Value
		}
	}

	// Calculate engagement
	likes := metrics["likes"]
	comments := metrics["comments"]
	saves := metrics["saves"]
	shares := metrics["shares"]
	metrics["engagement"] = likes + comments + saves + shares

	return &AnalyticsResponse{
		PlatformPostID: req.PlatformPostID,
		Metrics:        metrics,
		CollectedAt:    time.Now().Unix(),
	}, nil
}

// DeletePost deletes an Instagram post
func (i *InstagramPublisher) DeletePost(ctx context.Context, req *DeleteRequest) error {
	i.logger.WithField("media_id", req.PlatformPostID).Info("Deleting Instagram post")

	deleteURL := fmt.Sprintf("https://graph.instagram.com/%s", req.PlatformPostID)

	params := url.Values{}
	params.Set("access_token", req.AccessToken)

	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", fmt.Sprintf("%s?%s", deleteURL, params.Encode()), nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := i.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("delete request failed with status: %d", resp.StatusCode)
	}

	return nil
}

// GetPlatformLimits gets Instagram rate limits
func (i *InstagramPublisher) GetPlatformLimits(ctx context.Context) (*PlatformLimits, error) {
	// Instagram rate limits (approximate)
	return &PlatformLimits{
		RateLimits: map[string]int32{
			"posts":     25,  // 25 posts per hour
			"api_calls": 200, // 200 API calls per hour
		},
		CurrentUsage: map[string]int32{
			"posts":     0,
			"api_calls": 0,
		},
		ResetTimes: map[string]int64{
			"posts":     time.Now().Add(1 * time.Hour).Unix(),
			"api_calls": time.Now().Add(1 * time.Hour).Unix(),
		},
	}, nil
}

package models

import "time"

// Session represents a user session
type Session struct {
	BaseModel
	UserID    uint64    `json:"user_id"`
	SessionID string    `json:"session_id"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	ExpiresAt time.Time `json:"expires_at"`
	IsActive  bool      `json:"is_active"`
	LastUsed  time.Time `json:"last_used"`
}

// ListSessionsResponse represents list sessions response
type ListSessionsResponse struct {
	Sessions interface{} `json:"sessions"` // Can be []Session or []*SessionResponse
	Total    int         `json:"total"`
}

// SessionAnalyticsRequest represents session analytics request
type SessionAnalyticsRequest struct {
	Period string `json:"period"`
}

// SessionAnalyticsResponse represents session analytics response
type SessionAnalyticsResponse struct {
	Period                 string              `json:"period"`
	TotalSessions          int64               `json:"total_sessions"`
	ActiveSessions         int64               `json:"active_sessions"`
	ExpiredSessions        int64               `json:"expired_sessions"`
	AverageSessionDuration float64             `json:"average_session_duration"`
	DailyStats             []DailySessionStats `json:"daily_stats"`
	DeviceStats            []DeviceStats       `json:"device_stats"`
	LocationStats          []LocationStats     `json:"location_stats"`
}

// DailySessionStats represents daily session statistics
type DailySessionStats struct {
	Date           string  `json:"date"`
	NewSessions    int64   `json:"new_sessions"`
	ActiveSessions int64   `json:"active_sessions"`
	AvgDuration    float64 `json:"avg_duration"`
}

// LocationStats represents location-based statistics
type LocationStats struct {
	Country string `json:"country"`
	City    string `json:"city,omitempty"`
	Count   int64  `json:"count"`
}

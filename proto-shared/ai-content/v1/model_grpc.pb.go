// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: ai-content/v1/model.proto

package aicontentv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AIModelServiceClient is the client API for AIModelService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AIModelServiceClient interface {
	GetModels(ctx context.Context, in *GetModelsRequest, opts ...grpc.CallOption) (*GetModelsResponse, error)
	GetModel(ctx context.Context, in *GetModelRequest, opts ...grpc.CallOption) (*AIModel, error)
	GetModelStats(ctx context.Context, in *GetModelStatsRequest, opts ...grpc.CallOption) (*GetModelStatsResponse, error)
}

type aIModelServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAIModelServiceClient(cc grpc.ClientConnInterface) AIModelServiceClient {
	return &aIModelServiceClient{cc}
}

func (c *aIModelServiceClient) GetModels(ctx context.Context, in *GetModelsRequest, opts ...grpc.CallOption) (*GetModelsResponse, error) {
	out := new(GetModelsResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.AIModelService/GetModels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIModelServiceClient) GetModel(ctx context.Context, in *GetModelRequest, opts ...grpc.CallOption) (*AIModel, error) {
	out := new(AIModel)
	err := c.cc.Invoke(ctx, "/ai_content.v1.AIModelService/GetModel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIModelServiceClient) GetModelStats(ctx context.Context, in *GetModelStatsRequest, opts ...grpc.CallOption) (*GetModelStatsResponse, error) {
	out := new(GetModelStatsResponse)
	err := c.cc.Invoke(ctx, "/ai_content.v1.AIModelService/GetModelStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AIModelServiceServer is the server API for AIModelService service.
// All implementations must embed UnimplementedAIModelServiceServer
// for forward compatibility
type AIModelServiceServer interface {
	GetModels(context.Context, *GetModelsRequest) (*GetModelsResponse, error)
	GetModel(context.Context, *GetModelRequest) (*AIModel, error)
	GetModelStats(context.Context, *GetModelStatsRequest) (*GetModelStatsResponse, error)
	mustEmbedUnimplementedAIModelServiceServer()
}

// UnimplementedAIModelServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAIModelServiceServer struct {
}

func (UnimplementedAIModelServiceServer) GetModels(context.Context, *GetModelsRequest) (*GetModelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModels not implemented")
}
func (UnimplementedAIModelServiceServer) GetModel(context.Context, *GetModelRequest) (*AIModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModel not implemented")
}
func (UnimplementedAIModelServiceServer) GetModelStats(context.Context, *GetModelStatsRequest) (*GetModelStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelStats not implemented")
}
func (UnimplementedAIModelServiceServer) mustEmbedUnimplementedAIModelServiceServer() {}

// UnsafeAIModelServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AIModelServiceServer will
// result in compilation errors.
type UnsafeAIModelServiceServer interface {
	mustEmbedUnimplementedAIModelServiceServer()
}

func RegisterAIModelServiceServer(s grpc.ServiceRegistrar, srv AIModelServiceServer) {
	s.RegisterService(&AIModelService_ServiceDesc, srv)
}

func _AIModelService_GetModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIModelServiceServer).GetModels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.AIModelService/GetModels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIModelServiceServer).GetModels(ctx, req.(*GetModelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIModelService_GetModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIModelServiceServer).GetModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.AIModelService/GetModel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIModelServiceServer).GetModel(ctx, req.(*GetModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIModelService_GetModelStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIModelServiceServer).GetModelStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ai_content.v1.AIModelService/GetModelStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIModelServiceServer).GetModelStats(ctx, req.(*GetModelStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AIModelService_ServiceDesc is the grpc.ServiceDesc for AIModelService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AIModelService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai_content.v1.AIModelService",
	HandlerType: (*AIModelServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetModels",
			Handler:    _AIModelService_GetModels_Handler,
		},
		{
			MethodName: "GetModel",
			Handler:    _AIModelService_GetModel_Handler,
		},
		{
			MethodName: "GetModelStats",
			Handler:    _AIModelService_GetModelStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai-content/v1/model.proto",
}

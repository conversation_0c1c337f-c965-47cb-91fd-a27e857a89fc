package paypal

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/pkg/paypal"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// mockPayPalOrder represents a mock PayPal order entity
// TODO: Replace with actual ent.PayPalOrder when schema is created
type mockPayPalOrder struct {
	ID            uuid.UUID
	UserID        uuid.UUID
	PlanID        *uuid.UUID
	PaypalOrderID string
	Amount        int64
	Currency      string
	Purpose       string
	Status        string
	Description   string
	ReturnURL     string
	CancelURL     string
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// service implements the UseCase interface
type service struct {
	readDB         *ent.Client
	writeDB        *ent.Client
	paypalClient   paypal.Client
	creditUC       credit.UseCase
	subscriptionUC subscription.UseCase
	eventPublisher sharedKafka.Producer
	logger         logging.Logger
}

// NewService creates a new PayPal service
func NewService(
	readDB, writeDB *ent.Client,
	paypalClient paypal.Client,
	creditUC credit.UseCase,
	subscriptionUC subscription.UseCase,
	eventPublisher sharedKafka.Producer,
	logger logging.Logger,
) UseCase {
	return &service{
		readDB:         readDB,
		writeDB:        writeDB,
		paypalClient:   paypalClient,
		creditUC:       creditUC,
		subscriptionUC: subscriptionUC,
		eventPublisher: eventPublisher,
		logger:         logger,
	}
}

// InitiatePayment initiates a PayPal payment
func (s *service) InitiatePayment(ctx context.Context, req *models.InitiatePayPalPaymentRequest) (*models.InitiatePayPalPaymentResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Initiating PayPal payment")

	// Validate request
	if err := s.validatePaymentRequest(req); err != nil {
		return nil, fmt.Errorf("invalid payment request: %w", err)
	}

	// Create PayPal order
	orderReq := &paypal.CreateOrderRequest{
		Intent: "CAPTURE",
		PurchaseUnits: []paypal.PurchaseUnit{
			{
				Amount: paypal.Amount{
					CurrencyCode: req.Currency,
					Value:        fmt.Sprintf("%.2f", float64(req.Amount)/100), // Convert cents to dollars
				},
				Description: req.Description,
				CustomID:    req.UserID,
				InvoiceID:   uuid.New().String(),
			},
		},
		ApplicationContext: &paypal.ApplicationContext{
			ReturnURL: req.ReturnURL,
			CancelURL: req.CancelURL,
		},
	}

	order, err := s.paypalClient.CreateOrder(ctx, orderReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal order: %w", err)
	}

	// Save order to database
	orderEntity, err := s.savePayPalOrder(ctx, req, order)
	if err != nil {
		return nil, fmt.Errorf("failed to save PayPal order: %w", err)
	}

	// Find approval URL
	var approvalURL string
	for _, link := range order.Links {
		if link.Rel == "approve" {
			approvalURL = link.Href
			break
		}
	}

	return &models.InitiatePayPalPaymentResponse{
		Success:       true,
		PaymentID:     orderEntity.ID.String(),
		PayPalOrderID: order.ID,
		ApprovalURL:   approvalURL,
	}, nil
}

// ExecutePayment executes a PayPal payment after user approval
func (s *service) ExecutePayment(ctx context.Context, req *models.ExecutePayPalPaymentRequest) (*models.ExecutePayPalPaymentResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"payer_id":   req.PayerID,
	}).Info("Executing PayPal payment")

	// Get order from database
	orderUUID, err := uuid.Parse(req.PaymentID)
	if err != nil {
		return nil, fmt.Errorf("invalid payment ID: %w", err)
	}

	// TODO: Replace with actual PayPalOrder entity when schema is created
	// For now, create a mock order entity
	orderEntity := &mockPayPalOrder{
		ID:            orderUUID,
		UserID:        uuid.MustParse(req.UserID),
		PaypalOrderID: "MOCK_ORDER_ID",
		Amount:        1000,
		Currency:      "USD",
		Purpose:       "credit_topup",
		Status:        "APPROVED",
	}

	// Capture the order
	captureResp, err := s.paypalClient.CaptureOrder(ctx, orderEntity.PaypalOrderID)
	if err != nil {
		// Update order status to failed (mock implementation)
		s.logger.WithError(err).Error("Failed to capture PayPal order")
		return &models.ExecutePayPalPaymentResponse{
			Success:      false,
			PaymentID:    req.PaymentID,
			ErrorMessage: "Failed to capture payment",
		}, nil
	}

	// Update order status
	err = s.updateOrderStatus(ctx, orderEntity.ID, captureResp.Status)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update order status")
	}

	// Process the payment based on purpose
	var response *models.ExecutePayPalPaymentResponse
	switch orderEntity.Purpose {
	case "credit_topup":
		response, err = s.processCreditTopup(ctx, orderEntity, captureResp)
	case "subscription":
		response, err = s.processSubscriptionPayment(ctx, orderEntity, captureResp)
	default:
		return nil, fmt.Errorf("unsupported payment purpose: %s", orderEntity.Purpose)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to process payment: %w", err)
	}

	// Publish payment completed event
	s.publishPaymentEvent(ctx, "payment.completed", orderEntity, captureResp)

	return response, nil
}

// CancelPayment cancels a PayPal payment
func (s *service) CancelPayment(ctx context.Context, req *models.CancelPayPalPaymentRequest) (*models.CancelPayPalPaymentResponse, error) {
	s.logger.WithField("payment_id", req.PaymentID).Info("Cancelling PayPal payment")

	// Mock implementation - in production, get order from database and cancel
	orderUUID, err := uuid.Parse(req.PaymentID)
	if err != nil {
		return &models.CancelPayPalPaymentResponse{
			Success:   false,
			PaymentID: req.PaymentID,
			Status:    "failed",
			Message:   "Invalid payment ID",
		}, nil
	}

	// Mock order entity
	orderEntity := &mockPayPalOrder{
		ID:            orderUUID,
		UserID:        uuid.MustParse(req.UserID),
		PaypalOrderID: "MOCK_ORDER_ID",
		Status:        "CANCELLED",
	}

	// Publish payment cancelled event
	s.publishPaymentEvent(ctx, "payment.cancelled", orderEntity, nil)

	return &models.CancelPayPalPaymentResponse{
		Success:   true,
		PaymentID: req.PaymentID,
		Status:    "cancelled",
		Message:   "Payment cancelled successfully",
	}, nil
}

// GetPaymentStatus gets the status of a PayPal payment
func (s *service) GetPaymentStatus(ctx context.Context, req *models.GetPayPalPaymentStatusRequest) (*models.GetPayPalPaymentStatusResponse, error) {
	s.logger.WithField("payment_id", req.PaymentID).Info("Getting PayPal payment status")

	// Mock implementation - in production, get from database
	orderUUID, err := uuid.Parse(req.PaymentID)
	if err != nil {
		return nil, fmt.Errorf("invalid payment ID: %w", err)
	}

	// Mock order entity
	now := time.Now()
	return &models.GetPayPalPaymentStatusResponse{
		PaymentID: req.PaymentID,
		OrderID:   "MOCK_ORDER_ID",
		Status:    "COMPLETED",
		Amount:    1000,
		Currency:  "USD",
		Purpose:   "credit_topup",
		CreatedAt: now.Add(-time.Hour),
		UpdatedAt: now,
	}, nil
}

// validatePaymentRequest validates the payment request
func (s *service) validatePaymentRequest(req *models.InitiatePayPalPaymentRequest) error {
	if req.UserID == "" {
		return fmt.Errorf("user ID is required")
	}
	if req.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}
	if req.Currency == "" {
		return fmt.Errorf("currency is required")
	}
	if req.Purpose == "" {
		return fmt.Errorf("purpose is required")
	}
	if req.ReturnURL == "" {
		return fmt.Errorf("return URL is required")
	}
	if req.CancelURL == "" {
		return fmt.Errorf("cancel URL is required")
	}
	return nil
}

// savePayPalOrder saves PayPal order to database (mock implementation)
func (s *service) savePayPalOrder(ctx context.Context, req *models.InitiatePayPalPaymentRequest, order *paypal.Order) (*mockPayPalOrder, error) {
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	var planUUID *uuid.UUID
	if req.PlanID != "" {
		planID, err := uuid.Parse(req.PlanID)
		if err != nil {
			return nil, fmt.Errorf("invalid plan ID: %w", err)
		}
		planUUID = &planID
	}

	// Mock implementation - in production, save to database
	orderEntity := &mockPayPalOrder{
		ID:            uuid.New(),
		UserID:        userUUID,
		PlanID:        planUUID,
		PaypalOrderID: order.ID,
		Amount:        req.Amount,
		Currency:      req.Currency,
		Purpose:       req.Purpose,
		Description:   req.Description,
		Status:        order.Status,
		ReturnURL:     req.ReturnURL,
		CancelURL:     req.CancelURL,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	s.logger.WithFields(map[string]interface{}{
		"order_id":        orderEntity.ID,
		"paypal_order_id": orderEntity.PaypalOrderID,
		"amount":          orderEntity.Amount,
		"currency":        orderEntity.Currency,
	}).Info("Saved PayPal order (mock)")

	return orderEntity, nil
}

// updateOrderStatus updates PayPal order status (mock implementation)
func (s *service) updateOrderStatus(ctx context.Context, orderID uuid.UUID, status string) error {
	// Mock implementation - in production, update database
	s.logger.WithFields(map[string]interface{}{
		"order_id": orderID,
		"status":   status,
	}).Info("Updated PayPal order status (mock)")
	return nil
}

// processCreditTopup processes credit topup payment
func (s *service) processCreditTopup(ctx context.Context, order *mockPayPalOrder, captureResp *paypal.CaptureResponse) (*models.ExecutePayPalPaymentResponse, error) {
	if captureResp.Status != "COMPLETED" {
		return &models.ExecutePayPalPaymentResponse{
			PaymentID: order.ID.String(),
			Status:    captureResp.Status,
			Success:   false,
			Message:   "Payment not completed",
		}, nil
	}

	// Calculate credits to add (assuming 1 USD = 100 credits)
	creditsToAdd := int(order.Amount) // Amount is already in cents, so direct conversion

	// Add credits to user account
	addReq := &models.AddCreditsRequest{
		UserID:        order.UserID.String(),
		Amount:        creditsToAdd,
		Source:        "paypal_purchase",
		Description:   fmt.Sprintf("Credit purchase via PayPal - Order: %s", order.PaypalOrderID),
		ReferenceID:   order.ID.String(),
		ReferenceType: "paypal_order",
	}

	_, err := s.creditUC.AddCredits(ctx, addReq)
	if err != nil {
		return nil, fmt.Errorf("failed to add credits: %w", err)
	}

	return &models.ExecutePayPalPaymentResponse{
		PaymentID:    order.ID.String(),
		Status:       captureResp.Status,
		Success:      true,
		Message:      "Credit topup completed successfully",
		CreditsAdded: int32(creditsToAdd),
		Amount:       order.Amount,
		Currency:     order.Currency,
	}, nil
}

// processSubscriptionPayment processes subscription payment
func (s *service) processSubscriptionPayment(ctx context.Context, order *mockPayPalOrder, captureResp *paypal.CaptureResponse) (*models.ExecutePayPalPaymentResponse, error) {
	if captureResp.Status != "COMPLETED" {
		return &models.ExecutePayPalPaymentResponse{
			PaymentID: order.ID.String(),
			Status:    captureResp.Status,
			Success:   false,
			Message:   "Payment not completed",
		}, nil
	}

	if order.PlanID == nil {
		return nil, fmt.Errorf("plan ID is required for subscription payment")
	}

	// Create or update subscription
	subReq := &models.CreateSubscriptionRequest{
		UserID:          order.UserID.String(),
		PlanID:          order.PlanID.String(),
		PaymentMethodID: "paypal",
		Metadata: map[string]interface{}{
			"paypal_order_id": order.PaypalOrderID,
			"payment_id":      order.ID.String(),
		},
	}

	subResp, err := s.subscriptionUC.CreateSubscription(ctx, subReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription: %w", err)
	}

	return &models.ExecutePayPalPaymentResponse{
		PaymentID:      order.ID.String(),
		Status:         captureResp.Status,
		Success:        true,
		Message:        "Subscription payment completed successfully",
		SubscriptionID: subResp.ID,
		Amount:         order.Amount,
		Currency:       order.Currency,
	}, nil
}

// publishPaymentEvent publishes payment event to Kafka
func (s *service) publishPaymentEvent(ctx context.Context, eventType string, order *mockPayPalOrder, captureResp *paypal.CaptureResponse) {
	event := map[string]interface{}{
		"event_type":      eventType,
		"payment_id":      order.ID.String(),
		"user_id":         order.UserID.String(),
		"paypal_order_id": order.PaypalOrderID,
		"amount":          order.Amount,
		"currency":        order.Currency,
		"purpose":         order.Purpose,
		"status":          order.Status,
		"timestamp":       time.Now().Unix(),
	}

	if order.PlanID != nil {
		event["plan_id"] = order.PlanID.String()
	}

	if captureResp != nil {
		event["capture_id"] = captureResp.ID
		event["capture_status"] = captureResp.Status
	}

	eventData, _ := json.Marshal(event)

	err := s.eventPublisher.Publish(ctx, "payment.events", string(eventData))
	if err != nil {
		s.logger.WithError(err).Error("Failed to publish payment event")
	}
}

// ListPayments lists PayPal payments for a user
func (s *service) ListPayments(ctx context.Context, req *models.ListPayPalPaymentsRequest) (*models.ListPayPalPaymentsResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing PayPal payments")

	// Mock implementation - in production, query from database
	payments := []models.PayPalPaymentInfo{
		{
			PaymentID:   "payment_1",
			UserID:      req.UserID,
			Amount:      1000,
			Currency:    "USD",
			Status:      "completed",
			Purpose:     "credit_topup",
			ApprovalURL: "",
			ReturnURL:   "",
			CancelURL:   "",
		},
	}

	return &models.ListPayPalPaymentsResponse{
		Payments:   payments,
		TotalCount: len(payments),
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: 1,
	}, nil
}

// RefundPayment refunds a PayPal payment
func (s *service) RefundPayment(ctx context.Context, req *models.RefundPayPalPaymentRequest) (*models.RefundPayPalPaymentResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentID,
		"amount":     req.Amount,
		"reason":     req.Reason,
	}).Info("Refunding PayPal payment")

	// Mock implementation - in production, call PayPal refund API
	refundID := uuid.New().String()

	return &models.RefundPayPalPaymentResponse{
		Success:   true,
		RefundID:  refundID,
		PaymentID: req.PaymentID,
		Amount:    req.Amount,
		Status:    "completed",
		Message:   "Refund processed successfully",
	}, nil
}

// CreateSubscription creates a PayPal subscription
func (s *service) CreateSubscription(ctx context.Context, req *models.CreatePayPalSubscriptionRequest) (*models.CreatePayPalSubscriptionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
	}).Info("Creating PayPal subscription")

	// Mock implementation - in production, call PayPal subscription API
	subscriptionID := uuid.New().String()
	approvalURL := fmt.Sprintf("https://www.sandbox.paypal.com/webapps/billing/subscriptions?ba_token=%s", subscriptionID)

	return &models.CreatePayPalSubscriptionResponse{
		Success:        true,
		SubscriptionID: subscriptionID,
		ApprovalURL:    approvalURL,
	}, nil
}

// CancelSubscription cancels a PayPal subscription
func (s *service) CancelSubscription(ctx context.Context, req *models.CancelPayPalSubscriptionRequest) (*models.CancelPayPalSubscriptionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionID,
		"reason":          req.Reason,
	}).Info("Cancelling PayPal subscription")

	// Call PayPal API to cancel subscription
	err := s.paypalClient.CancelSubscription(ctx, req.SubscriptionID, req.Reason)
	if err != nil {
		return &models.CancelPayPalSubscriptionResponse{
			Success:        false,
			SubscriptionID: req.SubscriptionID,
			Status:         "failed",
			Message:        fmt.Sprintf("Failed to cancel subscription: %v", err),
		}, nil
	}

	return &models.CancelPayPalSubscriptionResponse{
		Success:        true,
		SubscriptionID: req.SubscriptionID,
		Status:         "cancelled",
		Message:        "Subscription cancelled successfully",
	}, nil
}

// GetSubscription gets a PayPal subscription
func (s *service) GetSubscription(ctx context.Context, req *models.GetPayPalSubscriptionRequest) (*models.GetPayPalSubscriptionResponse, error) {
	s.logger.WithField("subscription_id", req.SubscriptionID).Info("Getting PayPal subscription")

	// Call PayPal API to get subscription
	subscription, err := s.paypalClient.GetSubscription(ctx, req.SubscriptionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	return &models.GetPayPalSubscriptionResponse{
		SubscriptionID:  subscription.ID,
		PlanID:          subscription.PlanID,
		Status:          subscription.Status,
		StartTime:       subscription.StartTime,
		NextBillingTime: subscription.BillingInfo.NextBillingTime,
		CreatedAt:       subscription.CreateTime,
		UpdatedAt:       subscription.UpdateTime,
	}, nil
}

// HandleWebhook handles PayPal webhook events
func (s *service) HandleWebhook(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"event_type": req.EventType,
		"event_id":   req.ID,
	}).Info("Handling PayPal webhook")

	// Process different webhook events
	switch req.EventType {
	case "PAYMENT.CAPTURE.COMPLETED":
		return s.handlePaymentCompleted(ctx, req)
	case "PAYMENT.CAPTURE.DENIED":
		return s.handlePaymentDenied(ctx, req)
	case "BILLING.SUBSCRIPTION.ACTIVATED":
		return s.handleSubscriptionActivated(ctx, req)
	case "BILLING.SUBSCRIPTION.CANCELLED":
		return s.handleSubscriptionCancelled(ctx, req)
	default:
		s.logger.WithField("event_type", req.EventType).Warn("Unhandled webhook event type")
		return &models.PayPalWebhookResponse{
			Success: true,
			Message: "Event received but not processed",
		}, nil
	}
}

// ValidateWebhook validates PayPal webhook signature
func (s *service) ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error) {
	s.logger.Info("Validating PayPal webhook signature")

	// Use PayPal client to verify webhook signature
	return s.paypalClient.VerifyWebhookSignature(ctx, payload, signature)
}

// GetPayPalConfig gets PayPal configuration
func (s *service) GetPayPalConfig(ctx context.Context) (*models.PayPalConfig, error) {
	s.logger.Info("Getting PayPal configuration")

	// Mock implementation - in production, get from database or config
	return &models.PayPalConfig{
		ClientID:    "mock_client_id",
		Environment: "sandbox",
		WebhookID:   "mock_webhook_id",
	}, nil
}

// UpdatePayPalConfig updates PayPal configuration
func (s *service) UpdatePayPalConfig(ctx context.Context, config *models.PayPalConfig) error {
	s.logger.Info("Updating PayPal configuration")

	// Mock implementation - in production, save to database
	return nil
}

// handlePaymentCompleted handles payment completed webhook
func (s *service) handlePaymentCompleted(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	s.logger.WithField("event_id", req.ID).Info("Handling payment completed webhook")

	// Extract payment information from webhook resource
	resource := req.Resource
	if captureID, ok := resource["id"].(string); ok {
		s.logger.WithField("capture_id", captureID).Info("Payment capture completed")

		// TODO: Update payment status in database
		// TODO: Process credit addition or subscription activation
	}

	return &models.PayPalWebhookResponse{
		Success: true,
		Message: "Payment completed webhook processed",
	}, nil
}

// handlePaymentDenied handles payment denied webhook
func (s *service) handlePaymentDenied(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	s.logger.WithField("event_id", req.ID).Info("Handling payment denied webhook")

	// Extract payment information from webhook resource
	resource := req.Resource
	if captureID, ok := resource["id"].(string); ok {
		s.logger.WithField("capture_id", captureID).Info("Payment capture denied")

		// TODO: Update payment status in database
		// TODO: Send notification to user
	}

	return &models.PayPalWebhookResponse{
		Success: true,
		Message: "Payment denied webhook processed",
	}, nil
}

// handleSubscriptionActivated handles subscription activated webhook
func (s *service) handleSubscriptionActivated(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	s.logger.WithField("event_id", req.ID).Info("Handling subscription activated webhook")

	// Extract subscription information from webhook resource
	resource := req.Resource
	if subscriptionID, ok := resource["id"].(string); ok {
		s.logger.WithField("subscription_id", subscriptionID).Info("Subscription activated")

		// TODO: Update subscription status in database
		// TODO: Activate user plan
	}

	return &models.PayPalWebhookResponse{
		Success: true,
		Message: "Subscription activated webhook processed",
	}, nil
}

// handleSubscriptionCancelled handles subscription cancelled webhook
func (s *service) handleSubscriptionCancelled(ctx context.Context, req *models.PayPalWebhookRequest) (*models.PayPalWebhookResponse, error) {
	s.logger.WithField("event_id", req.ID).Info("Handling subscription cancelled webhook")

	// Extract subscription information from webhook resource
	resource := req.Resource
	if subscriptionID, ok := resource["id"].(string); ok {
		s.logger.WithField("subscription_id", subscriptionID).Info("Subscription cancelled")

		// TODO: Update subscription status in database
		// TODO: Downgrade user plan
	}

	return &models.PayPalWebhookResponse{
		Success: true,
		Message: "Subscription cancelled webhook processed",
	}, nil
}

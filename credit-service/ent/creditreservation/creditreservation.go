// Code generated by ent, DO NOT EDIT.

package creditreservation

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the creditreservation type in the database.
	Label = "credit_reservation"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldPurpose holds the string denoting the purpose field in the database.
	FieldPurpose = "purpose"
	// FieldReferenceID holds the string denoting the reference_id field in the database.
	FieldReferenceID = "reference_id"
	// FieldReferenceType holds the string denoting the reference_type field in the database.
	FieldReferenceType = "reference_type"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldExpiresAt holds the string denoting the expires_at field in the database.
	FieldExpiresAt = "expires_at"
	// FieldConsumedAt holds the string denoting the consumed_at field in the database.
	FieldConsumedAt = "consumed_at"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the creditreservation in the database.
	Table = "credit_reservations"
)

// Columns holds all SQL columns for creditreservation fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldAmount,
	FieldPurpose,
	FieldReferenceID,
	FieldReferenceType,
	FieldStatus,
	FieldExpiresAt,
	FieldConsumedAt,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// AmountValidator is a validator for the "amount" field. It is called by the builders before save.
	AmountValidator func(int) error
	// PurposeValidator is a validator for the "purpose" field. It is called by the builders before save.
	PurposeValidator func(string) error
	// ReferenceIDValidator is a validator for the "reference_id" field. It is called by the builders before save.
	ReferenceIDValidator func(string) error
	// ReferenceTypeValidator is a validator for the "reference_type" field. It is called by the builders before save.
	ReferenceTypeValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusReserved is the default value of the Status enum.
const DefaultStatus = StatusReserved

// Status values.
const (
	StatusReserved  Status = "reserved"
	StatusConsumed  Status = "consumed"
	StatusExpired   Status = "expired"
	StatusCancelled Status = "cancelled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusReserved, StatusConsumed, StatusExpired, StatusCancelled:
		return nil
	default:
		return fmt.Errorf("creditreservation: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the CreditReservation queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByPurpose orders the results by the purpose field.
func ByPurpose(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPurpose, opts...).ToFunc()
}

// ByReferenceID orders the results by the reference_id field.
func ByReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceID, opts...).ToFunc()
}

// ByReferenceType orders the results by the reference_type field.
func ByReferenceType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceType, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByExpiresAt orders the results by the expires_at field.
func ByExpiresAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresAt, opts...).ToFunc()
}

// ByConsumedAt orders the results by the consumed_at field.
func ByConsumedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConsumedAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

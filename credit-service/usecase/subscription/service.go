package subscription

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent"
	"github.com/social-content-ai/credit-service/ent/subscription"
	"github.com/social-content-ai/credit-service/ent/subscriptionplan"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/payment"
	sharedKafka "github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB           *ent.Client
	writeDB          *ent.Client
	paymentProcessor payment.Processor
	eventPublisher   sharedKafka.Producer
	logger           logging.Logger
}

// NewService creates a new subscription service
func NewService(readDB, writeDB *ent.Client, paymentProcessor payment.Processor, eventPublisher sharedKafka.Producer, logger logging.Logger) UseCase {
	return &service{
		readDB:           readDB,
		writeDB:          writeDB,
		paymentProcessor: paymentProcessor,
		eventPublisher:   eventPublisher,
		logger:           logger,
	}
}

// Placeholder implementations for all interface methods
// TODO: Implement all methods according to interface requirements

func (s *service) CreateSubscription(ctx context.Context, req *models.CreateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"plan_id": req.PlanID,
	}).Info("Creating subscription")

	// Use payment flow if payment processor is available
	if s.paymentProcessor != nil {
		return s.CreateSubscriptionWithPayment(ctx, req)
	}

	// Fallback to direct activation (for free plans or when payment processor is not available)
	activationReq := &models.ActivateSubscriptionRequest{
		UserID:   req.UserID,
		PlanID:   req.PlanID,
		Source:   "direct",
		Metadata: req.Metadata,
	}

	activationInfo, err := s.ActivateSubscription(ctx, activationReq)
	if err != nil {
		return nil, fmt.Errorf("failed to activate subscription: %w", err)
	}

	// Get the created subscription
	return s.GetSubscriptionByID(ctx, activationInfo.SubscriptionID)
}

func (s *service) GetSubscription(ctx context.Context, userID string) (*models.SubscriptionResponse, error) {
	s.logger.WithField("user_id", userID).Info("Getting user subscription")

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get active subscription for user
	sub, err := s.readDB.Subscription.Query().
		Where(
			subscription.UserID(userUUID),
			subscription.StatusIn(subscription.StatusActive, subscription.StatusTrial),
		).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("no active subscription found")
		}
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// Get plan separately
	plan, err := s.readDB.SubscriptionPlan.Query().
		Where(subscriptionplan.ID(sub.PlanID)).
		First(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get subscription plan")
		// Continue without plan info
		plan = nil
	}

	return s.convertSubscriptionToModel(sub, plan), nil
}

func (s *service) GetSubscriptionByID(ctx context.Context, subscriptionID string) (*models.SubscriptionResponse, error) {
	s.logger.WithField("subscription_id", subscriptionID).Info("Getting subscription by ID")

	// Parse subscription ID
	subscriptionUUID, err := uuid.Parse(subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("invalid subscription ID format: %w", err)
	}

	// Get subscription
	sub, err := s.readDB.Subscription.Query().
		Where(subscription.ID(subscriptionUUID)).
		First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("subscription not found")
		}
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// Get plan separately
	plan, err := s.readDB.SubscriptionPlan.Query().
		Where(subscriptionplan.ID(sub.PlanID)).
		First(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get subscription plan")
		// Continue without plan info
		plan = nil
	}

	return s.convertSubscriptionToModel(sub, plan), nil
}

func (s *service) UpdateSubscription(ctx context.Context, req *models.UpdateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) CancelSubscription(ctx context.Context, req *models.CancelSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ReactivateSubscription(ctx context.Context, req *models.ReactivateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionPlans(ctx context.Context) ([]*models.SubscriptionPlanResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionPlan(ctx context.Context, planID string) (*models.SubscriptionPlanResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionInvoices(ctx context.Context, req *models.GetInvoicesRequest) (*models.InvoicesResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetUpcomingInvoice(ctx context.Context, userID string) (*models.InvoiceResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ProcessSubscriptionPayment(ctx context.Context, req *models.ProcessPaymentRequest) (*models.PaymentResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionUsage(ctx context.Context, userID string) (*models.SubscriptionUsageResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) UpdateSubscriptionUsage(ctx context.Context, req *models.UpdateUsageRequest) (*models.SubscriptionUsageResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) ResetSubscriptionUsage(ctx context.Context, userID string) (*models.SubscriptionUsageResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// RenewSubscription is implemented in activation.go

func (s *service) UpgradeSubscription(ctx context.Context, req *models.UpgradeSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) DowngradeSubscription(ctx context.Context, req *models.DowngradeSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionAnalytics(ctx context.Context, userID string, period string) (*models.SubscriptionAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) GetSubscriptionHistory(ctx context.Context, req *models.SubscriptionHistoryRequest) (*models.SubscriptionHistoryResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminListSubscriptions(ctx context.Context, req *models.AdminListSubscriptionsRequest) (*models.AdminSubscriptionsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminGetSubscription(ctx context.Context, subscriptionID string) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminUpdateSubscription(ctx context.Context, req *models.AdminUpdateSubscriptionRequest) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminCancelSubscription(ctx context.Context, subscriptionID string, reason string) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminCreateSubscriptionPlan(ctx context.Context, req *models.CreateSubscriptionPlanRequest) (*models.SubscriptionPlanResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminUpdateSubscriptionPlan(ctx context.Context, req *models.UpdateSubscriptionPlanRequest) (*models.SubscriptionPlanResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) AdminDeleteSubscriptionPlan(ctx context.Context, planID string) error {
	return fmt.Errorf("not implemented")
}

func (s *service) AdminGetSubscriptionAnalytics(ctx context.Context, period string) (*models.AdminSubscriptionAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) HandlePaymentWebhook(ctx context.Context, event *models.PaymentWebhookEvent) error {
	return fmt.Errorf("not implemented")
}

func (s *service) InternalGetSubscription(ctx context.Context, userID string) (*models.SubscriptionResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) InternalUpdateSubscriptionUsage(ctx context.Context, req *models.UpdateUsageRequest) (*models.SubscriptionUsageResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (s *service) InternalCheckSubscriptionLimits(ctx context.Context, userID string, usage int) (*models.SubscriptionLimitCheckResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: ai-content/v1/generation.proto

package aicontentv1

import (
	v1 "github.com/social-content-ai/proto-shared/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ContentGenerationRequest for generating content with AI
type ContentGenerationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId            string            `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Topic             string            `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	ContentType       string            `protobuf:"bytes,3,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`                                                                  // educational, promotional, entertainment, etc.
	Tone              string            `protobuf:"bytes,4,opt,name=tone,proto3" json:"tone,omitempty"`                                                                                                   // professional, friendly, humorous, etc.
	Length            string            `protobuf:"bytes,5,opt,name=length,proto3" json:"length,omitempty"`                                                                                               // short, medium, long
	Platforms         []string          `protobuf:"bytes,6,rep,name=platforms,proto3" json:"platforms,omitempty"`                                                                                         // Target platforms
	Model             string            `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`                                                                                                 // AI model to use (gpt-4, claude, gemini)
	TemplateId        string            `protobuf:"bytes,8,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`                                                                     // Optional template ID
	Variables         map[string]string `protobuf:"bytes,9,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Template variables
	WorkspaceId       string            `protobuf:"bytes,10,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`                                                                 // Workspace context
	UseRag            bool              `protobuf:"varint,11,opt,name=use_rag,json=useRag,proto3" json:"use_rag,omitempty"`                                                                               // Whether to use RAG for context
	RagDocumentIds    []string          `protobuf:"bytes,12,rep,name=rag_document_ids,json=ragDocumentIds,proto3" json:"rag_document_ids,omitempty"`                                                      // Specific documents for RAG context
	RagQuery          string            `protobuf:"bytes,13,opt,name=rag_query,json=ragQuery,proto3" json:"rag_query,omitempty"`                                                                          // Custom RAG query (if different from topic)
	MaxRagChunks      int32             `protobuf:"varint,14,opt,name=max_rag_chunks,json=maxRagChunks,proto3" json:"max_rag_chunks,omitempty"`                                                           // Maximum RAG chunks to use
	ValidateCredits   bool              `protobuf:"varint,15,opt,name=validate_credits,json=validateCredits,proto3" json:"validate_credits,omitempty"`                                                    // Whether to validate credits before generation
	GenerationPurpose string            `protobuf:"bytes,16,opt,name=generation_purpose,json=generationPurpose,proto3" json:"generation_purpose,omitempty"`                                               // content_creation, improvement, template_usage
}

func (x *ContentGenerationRequest) Reset() {
	*x = ContentGenerationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentGenerationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentGenerationRequest) ProtoMessage() {}

func (x *ContentGenerationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentGenerationRequest.ProtoReflect.Descriptor instead.
func (*ContentGenerationRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{0}
}

func (x *ContentGenerationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ContentGenerationRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *ContentGenerationRequest) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ContentGenerationRequest) GetTone() string {
	if x != nil {
		return x.Tone
	}
	return ""
}

func (x *ContentGenerationRequest) GetLength() string {
	if x != nil {
		return x.Length
	}
	return ""
}

func (x *ContentGenerationRequest) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

func (x *ContentGenerationRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ContentGenerationRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *ContentGenerationRequest) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *ContentGenerationRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ContentGenerationRequest) GetUseRag() bool {
	if x != nil {
		return x.UseRag
	}
	return false
}

func (x *ContentGenerationRequest) GetRagDocumentIds() []string {
	if x != nil {
		return x.RagDocumentIds
	}
	return nil
}

func (x *ContentGenerationRequest) GetRagQuery() string {
	if x != nil {
		return x.RagQuery
	}
	return ""
}

func (x *ContentGenerationRequest) GetMaxRagChunks() int32 {
	if x != nil {
		return x.MaxRagChunks
	}
	return 0
}

func (x *ContentGenerationRequest) GetValidateCredits() bool {
	if x != nil {
		return x.ValidateCredits
	}
	return false
}

func (x *ContentGenerationRequest) GetGenerationPurpose() string {
	if x != nil {
		return x.GenerationPurpose
	}
	return ""
}

// ContentGenerationResponse with generated content
type ContentGenerationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content             string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Hashtags            []string               `protobuf:"bytes,3,rep,name=hashtags,proto3" json:"hashtags,omitempty"`
	SuggestedImages     []string               `protobuf:"bytes,4,rep,name=suggested_images,json=suggestedImages,proto3" json:"suggested_images,omitempty"`
	CreditsUsed         int32                  `protobuf:"varint,5,opt,name=credits_used,json=creditsUsed,proto3" json:"credits_used,omitempty"`
	GenerationTime      float32                `protobuf:"fixed32,6,opt,name=generation_time,json=generationTime,proto3" json:"generation_time,omitempty"`
	ModelUsed           string                 `protobuf:"bytes,7,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	RagUsed             bool                   `protobuf:"varint,9,opt,name=rag_used,json=ragUsed,proto3" json:"rag_used,omitempty"`                                       // Whether RAG was used
	RagChunksUsed       int32                  `protobuf:"varint,10,opt,name=rag_chunks_used,json=ragChunksUsed,proto3" json:"rag_chunks_used,omitempty"`                  // Number of RAG chunks used
	RagSources          []string               `protobuf:"bytes,11,rep,name=rag_sources,json=ragSources,proto3" json:"rag_sources,omitempty"`                              // Source documents used for RAG
	CreditTransactionId string                 `protobuf:"bytes,12,opt,name=credit_transaction_id,json=creditTransactionId,proto3" json:"credit_transaction_id,omitempty"` // Credit transaction reference
	CreditValidation    *CreditValidation      `protobuf:"bytes,13,opt,name=credit_validation,json=creditValidation,proto3" json:"credit_validation,omitempty"`            // Credit validation result
}

func (x *ContentGenerationResponse) Reset() {
	*x = ContentGenerationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentGenerationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentGenerationResponse) ProtoMessage() {}

func (x *ContentGenerationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentGenerationResponse.ProtoReflect.Descriptor instead.
func (*ContentGenerationResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{1}
}

func (x *ContentGenerationResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContentGenerationResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ContentGenerationResponse) GetHashtags() []string {
	if x != nil {
		return x.Hashtags
	}
	return nil
}

func (x *ContentGenerationResponse) GetSuggestedImages() []string {
	if x != nil {
		return x.SuggestedImages
	}
	return nil
}

func (x *ContentGenerationResponse) GetCreditsUsed() int32 {
	if x != nil {
		return x.CreditsUsed
	}
	return 0
}

func (x *ContentGenerationResponse) GetGenerationTime() float32 {
	if x != nil {
		return x.GenerationTime
	}
	return 0
}

func (x *ContentGenerationResponse) GetModelUsed() string {
	if x != nil {
		return x.ModelUsed
	}
	return ""
}

func (x *ContentGenerationResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ContentGenerationResponse) GetRagUsed() bool {
	if x != nil {
		return x.RagUsed
	}
	return false
}

func (x *ContentGenerationResponse) GetRagChunksUsed() int32 {
	if x != nil {
		return x.RagChunksUsed
	}
	return 0
}

func (x *ContentGenerationResponse) GetRagSources() []string {
	if x != nil {
		return x.RagSources
	}
	return nil
}

func (x *ContentGenerationResponse) GetCreditTransactionId() string {
	if x != nil {
		return x.CreditTransactionId
	}
	return ""
}

func (x *ContentGenerationResponse) GetCreditValidation() *CreditValidation {
	if x != nil {
		return x.CreditValidation
	}
	return nil
}

// GetGenerationRequest for retrieving a generation
type GetGenerationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetGenerationRequest) Reset() {
	*x = GetGenerationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGenerationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGenerationRequest) ProtoMessage() {}

func (x *GetGenerationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGenerationRequest.ProtoReflect.Descriptor instead.
func (*GetGenerationRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{2}
}

func (x *GetGenerationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ListGenerationsRequest for listing user generations
type ListGenerationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pagination  *v1.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Model       string                `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`                                // Filter by model
	ContentType string                `protobuf:"bytes,4,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"` // Filter by content type
}

func (x *ListGenerationsRequest) Reset() {
	*x = ListGenerationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGenerationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGenerationsRequest) ProtoMessage() {}

func (x *ListGenerationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGenerationsRequest.ProtoReflect.Descriptor instead.
func (*ListGenerationsRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{3}
}

func (x *ListGenerationsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListGenerationsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListGenerationsRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ListGenerationsRequest) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

// ListGenerationsResponse with paginated generations
type ListGenerationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Generations []*ContentGenerationResponse `protobuf:"bytes,1,rep,name=generations,proto3" json:"generations,omitempty"`
	Pagination  *v1.PaginationResponse       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListGenerationsResponse) Reset() {
	*x = ListGenerationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGenerationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGenerationsResponse) ProtoMessage() {}

func (x *ListGenerationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGenerationsResponse.ProtoReflect.Descriptor instead.
func (*ListGenerationsResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{4}
}

func (x *ListGenerationsResponse) GetGenerations() []*ContentGenerationResponse {
	if x != nil {
		return x.Generations
	}
	return nil
}

func (x *ListGenerationsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// RegenerateContentRequest for regenerating content
type RegenerateContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenerationId string `protobuf:"bytes,1,opt,name=generation_id,json=generationId,proto3" json:"generation_id,omitempty"`
	NewTopic     string `protobuf:"bytes,2,opt,name=new_topic,json=newTopic,proto3" json:"new_topic,omitempty"`    // Optional new topic
	NewTone      string `protobuf:"bytes,3,opt,name=new_tone,json=newTone,proto3" json:"new_tone,omitempty"`       // Optional new tone
	NewLength    string `protobuf:"bytes,4,opt,name=new_length,json=newLength,proto3" json:"new_length,omitempty"` // Optional new length
}

func (x *RegenerateContentRequest) Reset() {
	*x = RegenerateContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegenerateContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegenerateContentRequest) ProtoMessage() {}

func (x *RegenerateContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegenerateContentRequest.ProtoReflect.Descriptor instead.
func (*RegenerateContentRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{5}
}

func (x *RegenerateContentRequest) GetGenerationId() string {
	if x != nil {
		return x.GenerationId
	}
	return ""
}

func (x *RegenerateContentRequest) GetNewTopic() string {
	if x != nil {
		return x.NewTopic
	}
	return ""
}

func (x *RegenerateContentRequest) GetNewTone() string {
	if x != nil {
		return x.NewTone
	}
	return ""
}

func (x *RegenerateContentRequest) GetNewLength() string {
	if x != nil {
		return x.NewLength
	}
	return ""
}

// CreditValidation contains credit validation information
type CreditValidation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasSufficientCredits bool   `protobuf:"varint,1,opt,name=has_sufficient_credits,json=hasSufficientCredits,proto3" json:"has_sufficient_credits,omitempty"`
	RequiredCredits      int32  `protobuf:"varint,2,opt,name=required_credits,json=requiredCredits,proto3" json:"required_credits,omitempty"`
	CurrentCredits       int32  `protobuf:"varint,3,opt,name=current_credits,json=currentCredits,proto3" json:"current_credits,omitempty"`
	CreditsAfter         int32  `protobuf:"varint,4,opt,name=credits_after,json=creditsAfter,proto3" json:"credits_after,omitempty"` // Credits remaining after operation
	ValidationId         string `protobuf:"bytes,5,opt,name=validation_id,json=validationId,proto3" json:"validation_id,omitempty"`  // Credit validation transaction ID
}

func (x *CreditValidation) Reset() {
	*x = CreditValidation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditValidation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditValidation) ProtoMessage() {}

func (x *CreditValidation) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditValidation.ProtoReflect.Descriptor instead.
func (*CreditValidation) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{6}
}

func (x *CreditValidation) GetHasSufficientCredits() bool {
	if x != nil {
		return x.HasSufficientCredits
	}
	return false
}

func (x *CreditValidation) GetRequiredCredits() int32 {
	if x != nil {
		return x.RequiredCredits
	}
	return 0
}

func (x *CreditValidation) GetCurrentCredits() int32 {
	if x != nil {
		return x.CurrentCredits
	}
	return 0
}

func (x *CreditValidation) GetCreditsAfter() int32 {
	if x != nil {
		return x.CreditsAfter
	}
	return 0
}

func (x *CreditValidation) GetValidationId() string {
	if x != nil {
		return x.ValidationId
	}
	return ""
}

// ValidateCreditsRequest for checking credits before generation
type ValidateCreditsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Model           string `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`                                             // AI model to use
	GenerationType  string `protobuf:"bytes,3,opt,name=generation_type,json=generationType,proto3" json:"generation_type,omitempty"`     // content, image, improvement
	EstimatedTokens int32  `protobuf:"varint,4,opt,name=estimated_tokens,json=estimatedTokens,proto3" json:"estimated_tokens,omitempty"` // Estimated token usage
	WorkspaceId     string `protobuf:"bytes,5,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`              // Workspace context
}

func (x *ValidateCreditsRequest) Reset() {
	*x = ValidateCreditsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateCreditsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCreditsRequest) ProtoMessage() {}

func (x *ValidateCreditsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCreditsRequest.ProtoReflect.Descriptor instead.
func (*ValidateCreditsRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{7}
}

func (x *ValidateCreditsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateCreditsRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ValidateCreditsRequest) GetGenerationType() string {
	if x != nil {
		return x.GenerationType
	}
	return ""
}

func (x *ValidateCreditsRequest) GetEstimatedTokens() int32 {
	if x != nil {
		return x.EstimatedTokens
	}
	return 0
}

func (x *ValidateCreditsRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

// ValidateCreditsResponse with validation result
type ValidateCreditsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Validation   *CreditValidation `protobuf:"bytes,1,opt,name=validation,proto3" json:"validation,omitempty"`
	ErrorMessage string            `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"` // If validation failed
}

func (x *ValidateCreditsResponse) Reset() {
	*x = ValidateCreditsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateCreditsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCreditsResponse) ProtoMessage() {}

func (x *ValidateCreditsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCreditsResponse.ProtoReflect.Descriptor instead.
func (*ValidateCreditsResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{8}
}

func (x *ValidateCreditsResponse) GetValidation() *CreditValidation {
	if x != nil {
		return x.Validation
	}
	return nil
}

func (x *ValidateCreditsResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// RAGContextRequest for retrieving RAG context
type RAGContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId string   `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	TemplateId  string   `protobuf:"bytes,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`    // Optional template filter
	Query       string   `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`                                // RAG query
	MaxChunks   int32    `protobuf:"varint,5,opt,name=max_chunks,json=maxChunks,proto3" json:"max_chunks,omitempty"`      // Maximum chunks to retrieve
	DocumentIds []string `protobuf:"bytes,6,rep,name=document_ids,json=documentIds,proto3" json:"document_ids,omitempty"` // Specific documents to search
}

func (x *RAGContextRequest) Reset() {
	*x = RAGContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAGContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAGContextRequest) ProtoMessage() {}

func (x *RAGContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAGContextRequest.ProtoReflect.Descriptor instead.
func (*RAGContextRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{9}
}

func (x *RAGContextRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RAGContextRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *RAGContextRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *RAGContextRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *RAGContextRequest) GetMaxChunks() int32 {
	if x != nil {
		return x.MaxChunks
	}
	return 0
}

func (x *RAGContextRequest) GetDocumentIds() []string {
	if x != nil {
		return x.DocumentIds
	}
	return nil
}

// RAGContextResponse with retrieved context
type RAGContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunks         []*RAGChunk  `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
	Entities       []*RAGEntity `protobuf:"bytes,2,rep,name=entities,proto3" json:"entities,omitempty"`
	ReasoningPath  string       `protobuf:"bytes,3,opt,name=reasoning_path,json=reasoningPath,proto3" json:"reasoning_path,omitempty"`      // Graph reasoning path
	RelevanceScore float32      `protobuf:"fixed32,4,opt,name=relevance_score,json=relevanceScore,proto3" json:"relevance_score,omitempty"` // Overall relevance score
}

func (x *RAGContextResponse) Reset() {
	*x = RAGContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAGContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAGContextResponse) ProtoMessage() {}

func (x *RAGContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAGContextResponse.ProtoReflect.Descriptor instead.
func (*RAGContextResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{10}
}

func (x *RAGContextResponse) GetChunks() []*RAGChunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *RAGContextResponse) GetEntities() []*RAGEntity {
	if x != nil {
		return x.Entities
	}
	return nil
}

func (x *RAGContextResponse) GetReasoningPath() string {
	if x != nil {
		return x.ReasoningPath
	}
	return ""
}

func (x *RAGContextResponse) GetRelevanceScore() float32 {
	if x != nil {
		return x.RelevanceScore
	}
	return 0
}

// RAGChunk represents a relevant context chunk
type RAGChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChunkId         string  `protobuf:"bytes,1,opt,name=chunk_id,json=chunkId,proto3" json:"chunk_id,omitempty"`
	DocumentId      string  `protobuf:"bytes,2,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	Content         string  `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	SimilarityScore float32 `protobuf:"fixed32,4,opt,name=similarity_score,json=similarityScore,proto3" json:"similarity_score,omitempty"`
	SourceFile      string  `protobuf:"bytes,5,opt,name=source_file,json=sourceFile,proto3" json:"source_file,omitempty"` // Original filename
}

func (x *RAGChunk) Reset() {
	*x = RAGChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAGChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAGChunk) ProtoMessage() {}

func (x *RAGChunk) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAGChunk.ProtoReflect.Descriptor instead.
func (*RAGChunk) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{11}
}

func (x *RAGChunk) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

func (x *RAGChunk) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *RAGChunk) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *RAGChunk) GetSimilarityScore() float32 {
	if x != nil {
		return x.SimilarityScore
	}
	return 0
}

func (x *RAGChunk) GetSourceFile() string {
	if x != nil {
		return x.SourceFile
	}
	return ""
}

// RAGEntity represents an extracted entity
type RAGEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityId    string  `protobuf:"bytes,1,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	Name        string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type        string  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"` // PERSON, ORG, CONCEPT, etc.
	Confidence  float32 `protobuf:"fixed32,4,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Description string  `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *RAGEntity) Reset() {
	*x = RAGEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAGEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAGEntity) ProtoMessage() {}

func (x *RAGEntity) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAGEntity.ProtoReflect.Descriptor instead.
func (*RAGEntity) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{12}
}

func (x *RAGEntity) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *RAGEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RAGEntity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RAGEntity) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *RAGEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// GenerateWithRAGRequest for RAG-enhanced generation
type GenerateWithRAGRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenerationRequest    *ContentGenerationRequest `protobuf:"bytes,1,opt,name=generation_request,json=generationRequest,proto3" json:"generation_request,omitempty"`
	RagRequest           *RAGContextRequest        `protobuf:"bytes,2,opt,name=rag_request,json=ragRequest,proto3" json:"rag_request,omitempty"`
	ValidateCreditsFirst bool                      `protobuf:"varint,3,opt,name=validate_credits_first,json=validateCreditsFirst,proto3" json:"validate_credits_first,omitempty"`
}

func (x *GenerateWithRAGRequest) Reset() {
	*x = GenerateWithRAGRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateWithRAGRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateWithRAGRequest) ProtoMessage() {}

func (x *GenerateWithRAGRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateWithRAGRequest.ProtoReflect.Descriptor instead.
func (*GenerateWithRAGRequest) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{13}
}

func (x *GenerateWithRAGRequest) GetGenerationRequest() *ContentGenerationRequest {
	if x != nil {
		return x.GenerationRequest
	}
	return nil
}

func (x *GenerateWithRAGRequest) GetRagRequest() *RAGContextRequest {
	if x != nil {
		return x.RagRequest
	}
	return nil
}

func (x *GenerateWithRAGRequest) GetValidateCreditsFirst() bool {
	if x != nil {
		return x.ValidateCreditsFirst
	}
	return false
}

// GenerateWithRAGResponse with RAG-enhanced content
type GenerateWithRAGResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenerationResponse *ContentGenerationResponse `protobuf:"bytes,1,opt,name=generation_response,json=generationResponse,proto3" json:"generation_response,omitempty"`
	RagContext         *RAGContextResponse        `protobuf:"bytes,2,opt,name=rag_context,json=ragContext,proto3" json:"rag_context,omitempty"`
	CombinedPrompt     string                     `protobuf:"bytes,3,opt,name=combined_prompt,json=combinedPrompt,proto3" json:"combined_prompt,omitempty"` // Final prompt used for generation
}

func (x *GenerateWithRAGResponse) Reset() {
	*x = GenerateWithRAGResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateWithRAGResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateWithRAGResponse) ProtoMessage() {}

func (x *GenerateWithRAGResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateWithRAGResponse.ProtoReflect.Descriptor instead.
func (*GenerateWithRAGResponse) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{14}
}

func (x *GenerateWithRAGResponse) GetGenerationResponse() *ContentGenerationResponse {
	if x != nil {
		return x.GenerationResponse
	}
	return nil
}

func (x *GenerateWithRAGResponse) GetRagContext() *RAGContextResponse {
	if x != nil {
		return x.RagContext
	}
	return nil
}

func (x *GenerateWithRAGResponse) GetCombinedPrompt() string {
	if x != nil {
		return x.CombinedPrompt
	}
	return ""
}

// Empty response
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_content_v1_generation_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_ai_content_v1_generation_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_ai_content_v1_generation_proto_rawDescGZIP(), []int{15}
}

var File_ai_content_v1_generation_proto protoreflect.FileDescriptor

var file_ai_content_v1_generation_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0d, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x84, 0x05, 0x0a,
	0x18, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x6f, 0x6e, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x09,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x75, 0x73, 0x65, 0x52, 0x61, 0x67, 0x12, 0x28,
	0x0a, 0x10, 0x72, 0x61, 0x67, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x61, 0x67, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x67, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x67,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x67,
	0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d,
	0x61, 0x78, 0x52, 0x61, 0x67, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x1a, 0x3c, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x98, 0x04, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x61, 0x73, 0x68, 0x74, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x61, 0x73, 0x68, 0x74, 0x61, 0x67, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x73,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x64, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x67, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x61, 0x67, 0x55,
	0x73, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x61, 0x67, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x61,
	0x67, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x61, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x61, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x15,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x4c, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x26,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xa4, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a,
	0x0b, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x96, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65,
	0x77, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x65, 0x77, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x5f, 0x74,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x77, 0x54, 0x6f,
	0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x22, 0xe6, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x75,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x53, 0x75, 0x66, 0x66, 0x69,
	0x63, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xbe, 0x01, 0x0a, 0x16, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x17, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x69, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc8, 0x01, 0x0a,
	0x11, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xcb, 0x01, 0x0a, 0x12, 0x52, 0x41, 0x47, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x41, 0x47, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x12,
	0x34, 0x0a, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x41, 0x47, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x74, 0x68, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x72, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x08, 0x52, 0x41, 0x47, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x69, 0x6d, 0x69,
	0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x22, 0x92, 0x01, 0x0a, 0x09, 0x52, 0x41, 0x47, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe9, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x41, 0x47, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x12, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0b,
	0x72, 0x61, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x72, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x22, 0xe1, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x41, 0x47, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x59, 0x0a, 0x13, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b,
	0x72, 0x61, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x72, 0x61, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x32, 0xc4, 0x05, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x64, 0x0a, 0x0f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x27, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x11, 0x52, 0x65, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x61,
	0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x60, 0x0a, 0x0f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x69, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x54, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x41, 0x47, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x0f, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x41, 0x47, 0x12, 0x25, 0x2e, 0x61, 0x69, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x41, 0x47, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x41,
	0x47, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x2d, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x61, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x61, 0x69, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ai_content_v1_generation_proto_rawDescOnce sync.Once
	file_ai_content_v1_generation_proto_rawDescData = file_ai_content_v1_generation_proto_rawDesc
)

func file_ai_content_v1_generation_proto_rawDescGZIP() []byte {
	file_ai_content_v1_generation_proto_rawDescOnce.Do(func() {
		file_ai_content_v1_generation_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_content_v1_generation_proto_rawDescData)
	})
	return file_ai_content_v1_generation_proto_rawDescData
}

var file_ai_content_v1_generation_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_ai_content_v1_generation_proto_goTypes = []interface{}{
	(*ContentGenerationRequest)(nil),  // 0: ai_content.v1.ContentGenerationRequest
	(*ContentGenerationResponse)(nil), // 1: ai_content.v1.ContentGenerationResponse
	(*GetGenerationRequest)(nil),      // 2: ai_content.v1.GetGenerationRequest
	(*ListGenerationsRequest)(nil),    // 3: ai_content.v1.ListGenerationsRequest
	(*ListGenerationsResponse)(nil),   // 4: ai_content.v1.ListGenerationsResponse
	(*RegenerateContentRequest)(nil),  // 5: ai_content.v1.RegenerateContentRequest
	(*CreditValidation)(nil),          // 6: ai_content.v1.CreditValidation
	(*ValidateCreditsRequest)(nil),    // 7: ai_content.v1.ValidateCreditsRequest
	(*ValidateCreditsResponse)(nil),   // 8: ai_content.v1.ValidateCreditsResponse
	(*RAGContextRequest)(nil),         // 9: ai_content.v1.RAGContextRequest
	(*RAGContextResponse)(nil),        // 10: ai_content.v1.RAGContextResponse
	(*RAGChunk)(nil),                  // 11: ai_content.v1.RAGChunk
	(*RAGEntity)(nil),                 // 12: ai_content.v1.RAGEntity
	(*GenerateWithRAGRequest)(nil),    // 13: ai_content.v1.GenerateWithRAGRequest
	(*GenerateWithRAGResponse)(nil),   // 14: ai_content.v1.GenerateWithRAGResponse
	(*Empty)(nil),                     // 15: ai_content.v1.Empty
	nil,                               // 16: ai_content.v1.ContentGenerationRequest.VariablesEntry
	(*timestamppb.Timestamp)(nil),     // 17: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),      // 18: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),     // 19: common.v1.PaginationResponse
}
var file_ai_content_v1_generation_proto_depIdxs = []int32{
	16, // 0: ai_content.v1.ContentGenerationRequest.variables:type_name -> ai_content.v1.ContentGenerationRequest.VariablesEntry
	17, // 1: ai_content.v1.ContentGenerationResponse.created_at:type_name -> google.protobuf.Timestamp
	6,  // 2: ai_content.v1.ContentGenerationResponse.credit_validation:type_name -> ai_content.v1.CreditValidation
	18, // 3: ai_content.v1.ListGenerationsRequest.pagination:type_name -> common.v1.PaginationRequest
	1,  // 4: ai_content.v1.ListGenerationsResponse.generations:type_name -> ai_content.v1.ContentGenerationResponse
	19, // 5: ai_content.v1.ListGenerationsResponse.pagination:type_name -> common.v1.PaginationResponse
	6,  // 6: ai_content.v1.ValidateCreditsResponse.validation:type_name -> ai_content.v1.CreditValidation
	11, // 7: ai_content.v1.RAGContextResponse.chunks:type_name -> ai_content.v1.RAGChunk
	12, // 8: ai_content.v1.RAGContextResponse.entities:type_name -> ai_content.v1.RAGEntity
	0,  // 9: ai_content.v1.GenerateWithRAGRequest.generation_request:type_name -> ai_content.v1.ContentGenerationRequest
	9,  // 10: ai_content.v1.GenerateWithRAGRequest.rag_request:type_name -> ai_content.v1.RAGContextRequest
	1,  // 11: ai_content.v1.GenerateWithRAGResponse.generation_response:type_name -> ai_content.v1.ContentGenerationResponse
	10, // 12: ai_content.v1.GenerateWithRAGResponse.rag_context:type_name -> ai_content.v1.RAGContextResponse
	0,  // 13: ai_content.v1.ContentGenerationService.GenerateContent:input_type -> ai_content.v1.ContentGenerationRequest
	2,  // 14: ai_content.v1.ContentGenerationService.GetGeneration:input_type -> ai_content.v1.GetGenerationRequest
	3,  // 15: ai_content.v1.ContentGenerationService.ListGenerations:input_type -> ai_content.v1.ListGenerationsRequest
	5,  // 16: ai_content.v1.ContentGenerationService.RegenerateContent:input_type -> ai_content.v1.RegenerateContentRequest
	7,  // 17: ai_content.v1.ContentGenerationService.ValidateCredits:input_type -> ai_content.v1.ValidateCreditsRequest
	9,  // 18: ai_content.v1.ContentGenerationService.GetRAGContext:input_type -> ai_content.v1.RAGContextRequest
	13, // 19: ai_content.v1.ContentGenerationService.GenerateWithRAG:input_type -> ai_content.v1.GenerateWithRAGRequest
	1,  // 20: ai_content.v1.ContentGenerationService.GenerateContent:output_type -> ai_content.v1.ContentGenerationResponse
	1,  // 21: ai_content.v1.ContentGenerationService.GetGeneration:output_type -> ai_content.v1.ContentGenerationResponse
	4,  // 22: ai_content.v1.ContentGenerationService.ListGenerations:output_type -> ai_content.v1.ListGenerationsResponse
	1,  // 23: ai_content.v1.ContentGenerationService.RegenerateContent:output_type -> ai_content.v1.ContentGenerationResponse
	8,  // 24: ai_content.v1.ContentGenerationService.ValidateCredits:output_type -> ai_content.v1.ValidateCreditsResponse
	10, // 25: ai_content.v1.ContentGenerationService.GetRAGContext:output_type -> ai_content.v1.RAGContextResponse
	14, // 26: ai_content.v1.ContentGenerationService.GenerateWithRAG:output_type -> ai_content.v1.GenerateWithRAGResponse
	20, // [20:27] is the sub-list for method output_type
	13, // [13:20] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_ai_content_v1_generation_proto_init() }
func file_ai_content_v1_generation_proto_init() {
	if File_ai_content_v1_generation_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ai_content_v1_generation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentGenerationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentGenerationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGenerationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGenerationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGenerationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegenerateContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditValidation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateCreditsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateCreditsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAGContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAGContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAGChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAGEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateWithRAGRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateWithRAGResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_content_v1_generation_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_content_v1_generation_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_content_v1_generation_proto_goTypes,
		DependencyIndexes: file_ai_content_v1_generation_proto_depIdxs,
		MessageInfos:      file_ai_content_v1_generation_proto_msgTypes,
	}.Build()
	File_ai_content_v1_generation_proto = out.File
	file_ai_content_v1_generation_proto_rawDesc = nil
	file_ai_content_v1_generation_proto_goTypes = nil
	file_ai_content_v1_generation_proto_depIdxs = nil
}

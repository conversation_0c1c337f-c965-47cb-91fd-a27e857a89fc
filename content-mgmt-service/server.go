package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/content-mgmt-service/api/grpc/handlers"
	"github.com/social-content-ai/content-mgmt-service/api/restful"
	"github.com/social-content-ai/content-mgmt-service/config"
	"github.com/social-content-ai/content-mgmt-service/ent"
	"github.com/social-content-ai/content-mgmt-service/usecase/post"
	"github.com/social-content-ai/content-mgmt-service/usecase/schedule"
	"github.com/social-content-ai/content-mgmt-service/usecase/template"
	"github.com/social-content-ai/content-mgmt-service/usecase/workspace"
	"github.com/social-content-ai/pkg-shared/logging"
	contentmgmtv1 "github.com/social-content-ai/proto-shared/content-mgmt/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"

	_ "github.com/jackc/pgx/v5/stdlib"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
	})

	logger.Info("Starting content management service")

	// Initialize database connections
	writeDB, readDB, err := initDatabases(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize databases")
	}
	defer writeDB.Close()
	defer readDB.Close()

	// Run database migrations
	logger.Info("Running database migrations")
	if err := writeDB.Schema.Create(context.Background()); err != nil {
		logger.WithError(err).Fatal("Failed to create schema")
	}
	logger.Info("Database migrations completed")

	// Initialize use cases
	postUseCase := post.NewService(writeDB, readDB, logger)
	templateUseCase := template.NewService(writeDB, readDB, logger)
	workspaceUseCase := workspace.NewService(writeDB, readDB, logger)
	scheduleUseCase := schedule.NewService(writeDB, readDB, logger)

	// Start servers
	if err := startServers(cfg, logger, writeDB, readDB, postUseCase, templateUseCase, workspaceUseCase, scheduleUseCase); err != nil {
		logger.WithError(err).Fatal("Failed to start servers")
	}
}

// initDatabases initializes read and write database connections
func initDatabases(cfg *config.Config, logger logging.Logger) (*ent.Client, *ent.Client, error) {
	// Get database driver and DSN based on configuration
	driverName := cfg.Database.GetDriverName()
	dsn := cfg.Database.GetDSN()

	logger.WithFields(map[string]interface{}{
		"driver": driverName,
		"type":   cfg.Database.Type,
	}).Info("Initializing database connection")

	// Create data directory for SQLite if needed
	if cfg.Database.Type == "sqlite" {
		if err := os.MkdirAll("./data", 0755); err != nil {
			logger.WithError(err).Error("Failed to create data directory")
			return nil, nil, fmt.Errorf("failed to create data directory: %w", err)
		}
	}

	// Create Ent client
	client, err := ent.Open(driverName, dsn)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"driver": driverName,
			"type":   cfg.Database.Type,
		}).Error("Failed opening database connection")
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// For simplicity, use the same client for read and write
	// In production, you might want separate read replicas
	logger.WithField("type", cfg.Database.Type).Info("Database connections established")
	return client, client, nil
}

// startServers starts both HTTP and gRPC servers
func startServers(
	cfg *config.Config,
	logger logging.Logger,
	writeDB, readDB *ent.Client,
	postUseCase post.UseCase,
	templateUseCase template.UseCase,
	workspaceUseCase workspace.UseCase,
	scheduleUseCase schedule.UseCase,
) error {
	// Initialize gRPC server
	grpcServer := grpc.NewServer()

	// Register gRPC services
	postHandler := handlers.NewPostHandler(postUseCase, logger)
	templateHandler := handlers.NewTemplateHandler(templateUseCase, logger)
	workspaceHandler := handlers.NewWorkspaceHandler(workspaceUseCase, logger)

	contentmgmtv1.RegisterPostServiceServer(grpcServer, postHandler)
	contentmgmtv1.RegisterTemplateServiceServer(grpcServer, templateHandler)
	contentmgmtv1.RegisterWorkspaceServiceServer(grpcServer, workspaceHandler)

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())

	// Setup RESTful API routes
	// For now, we'll use nil for userClient since we don't have user service connection
	// In production, you should establish gRPC connection to user service
	var userClient userv1.AuthServiceClient = nil
	restful.SetupRoutes(httpRouter, postUseCase, templateUseCase, workspaceUseCase, scheduleUseCase, userClient, logger)

	// Start HTTP server
	httpServer := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler:      httpRouter,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")

	// Start HTTP server in a goroutine
	go func() {
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
		return err
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Servers stopped")
	return nil
}

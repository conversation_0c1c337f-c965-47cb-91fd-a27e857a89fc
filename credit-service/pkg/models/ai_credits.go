package models

// ValidateAICreditsRequest represents AI credit validation request
type ValidateAICreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Operation     string                 `json:"operation" validate:"required"` // "text_generation", "image_generation", etc.
	ServiceName   string                 `json:"service_name" validate:"required"` // "openai", "anthropic", etc.
	EstimatedCost int                    `json:"estimated_cost" validate:"required,min=1"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"` // Operation-specific parameters
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// ValidateAICreditsResponse represents AI credit validation response
type ValidateAICreditsResponse struct {
	Valid               bool           `json:"valid"`
	UserID              string         `json:"user_id"`
	Operation           string         `json:"operation"`
	EstimatedCost       int            `json:"estimated_cost"`
	ActualCost          int            `json:"actual_cost"`
	CurrentCredits      int            `json:"current_credits"`
	RequiredCredits     int            `json:"required_credits"`
	Shortage            int            `json:"shortage"`
	CostBreakdown       map[string]int `json:"cost_breakdown"`
	RecommendedPlan     string         `json:"recommended_plan,omitempty"`
	ValidationTimestamp int64          `json:"validation_timestamp"`
}

// ReserveAICreditsRequest represents AI credit reservation request
type ReserveAICreditsRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	Operation   string                 `json:"operation" validate:"required"`
	Credits     int                    `json:"credits" validate:"required,min=1"`
	ReferenceID string                 `json:"reference_id" validate:"required"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ReserveAICreditsResponse represents AI credit reservation response
type ReserveAICreditsResponse struct {
	Success         bool   `json:"success"`
	ReservedCredits int    `json:"reserved_credits"`
	CurrentCredits  int    `json:"current_credits"`
	RequiredCredits int    `json:"required_credits"`
	ReservationID   string `json:"reservation_id,omitempty"`
	ErrorMessage    string `json:"error_message,omitempty"`
}

// ConfirmAIOperationRequest represents AI operation confirmation request
type ConfirmAIOperationRequest struct {
	UserID          string                 `json:"user_id" validate:"required"`
	Operation       string                 `json:"operation" validate:"required"`
	ReservationID   string                 `json:"reservation_id" validate:"required"`
	ReservedCredits int                    `json:"reserved_credits" validate:"required"`
	ActualCost      int                    `json:"actual_cost" validate:"required"`
	Success         bool                   `json:"success"`
	Result          map[string]interface{} `json:"result,omitempty"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// ConfirmAIOperationResponse represents AI operation confirmation response
type ConfirmAIOperationResponse struct {
	Success     bool   `json:"success"`
	ActualCost  int    `json:"actual_cost"`
	Adjustment  int    `json:"adjustment"` // Positive if credits refunded, negative if additional charged
	OperationID string `json:"operation_id"`
}

// AIOperationCostConfig represents cost configuration for AI operations
type AIOperationCostConfig struct {
	Operation   string                 `json:"operation"`
	ServiceName string                 `json:"service_name"`
	BaseCost    int                    `json:"base_cost"`
	CostModel   string                 `json:"cost_model"` // "fixed", "per_token", "per_second", "per_image"
	Parameters  map[string]interface{} `json:"parameters"`
}

// AIServiceUsageStats represents AI service usage statistics
type AIServiceUsageStats struct {
	UserID              string                    `json:"user_id"`
	Period              string                    `json:"period"`
	TotalOperations     int                       `json:"total_operations"`
	TotalCreditsUsed    int                       `json:"total_credits_used"`
	OperationBreakdown  map[string]int            `json:"operation_breakdown"`
	ServiceBreakdown    map[string]int            `json:"service_breakdown"`
	CostBreakdown       map[string]int            `json:"cost_breakdown"`
	Timeline            []AIUsageTimelinePoint    `json:"timeline"`
	TopOperations       []AIOperationUsageSummary `json:"top_operations"`
}

// AIUsageTimelinePoint represents a point in AI usage timeline
type AIUsageTimelinePoint struct {
	Date       string `json:"date"`
	Operations int    `json:"operations"`
	Credits    int    `json:"credits"`
}

// AIOperationUsageSummary represents usage summary for an operation
type AIOperationUsageSummary struct {
	Operation string `json:"operation"`
	Count     int    `json:"count"`
	Credits   int    `json:"credits"`
}

// AIOperationLimits represents limits for AI operations
type AIOperationLimits struct {
	UserID           string                 `json:"user_id"`
	PlanID           string                 `json:"plan_id,omitempty"`
	DailyLimit       int                    `json:"daily_limit"`
	MonthlyLimit     int                    `json:"monthly_limit"`
	OperationLimits  map[string]int         `json:"operation_limits"` // Per operation type
	CurrentUsage     AICurrentUsage         `json:"current_usage"`
	ResetDate        string                 `json:"reset_date"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

// AICurrentUsage represents current AI usage
type AICurrentUsage struct {
	DailyUsage     int            `json:"daily_usage"`
	MonthlyUsage   int            `json:"monthly_usage"`
	OperationUsage map[string]int `json:"operation_usage"`
	LastResetDate  string         `json:"last_reset_date"`
}

// AIOperationHistory represents AI operation history
type AIOperationHistory struct {
	BaseModel
	UserID        string                 `json:"user_id"`
	Operation     string                 `json:"operation"`
	ServiceName   string                 `json:"service_name"`
	CreditsCost   int                    `json:"credits_cost"`
	Status        string                 `json:"status"` // "success", "failed", "cancelled"
	ReferenceID   string                 `json:"reference_id"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"`
	Result        map[string]interface{} `json:"result,omitempty"`
	ErrorMessage  string                 `json:"error_message,omitempty"`
	Duration      int64                  `json:"duration"` // Duration in milliseconds
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// AIOperationQuota represents AI operation quota
type AIOperationQuota struct {
	UserID          string `json:"user_id"`
	Operation       string `json:"operation"`
	DailyQuota      int    `json:"daily_quota"`
	MonthlyQuota    int    `json:"monthly_quota"`
	DailyUsed       int    `json:"daily_used"`
	MonthlyUsed     int    `json:"monthly_used"`
	DailyRemaining  int    `json:"daily_remaining"`
	MonthlyRemaining int   `json:"monthly_remaining"`
	QuotaResetDate  string `json:"quota_reset_date"`
}

// GetAIOperationQuotaRequest represents get AI operation quota request
type GetAIOperationQuotaRequest struct {
	UserID    string `json:"user_id" validate:"required"`
	Operation string `json:"operation,omitempty"`
}

// GetAIOperationQuotaResponse represents get AI operation quota response
type GetAIOperationQuotaResponse struct {
	UserID string              `json:"user_id"`
	Quotas []AIOperationQuota  `json:"quotas"`
	Limits AIOperationLimits   `json:"limits"`
}

// UpdateAIOperationQuotaRequest represents update AI operation quota request
type UpdateAIOperationQuotaRequest struct {
	UserID       string `json:"user_id" validate:"required"`
	Operation    string `json:"operation" validate:"required"`
	CreditsUsed  int    `json:"credits_used" validate:"required,min=1"`
	ReferenceID  string `json:"reference_id" validate:"required"`
}

// AIOperationPricing represents pricing for AI operations
type AIOperationPricing struct {
	Operation   string                 `json:"operation"`
	ServiceName string                 `json:"service_name"`
	PricingTier string                 `json:"pricing_tier"` // "free", "basic", "pro", "enterprise"
	BaseCost    int                    `json:"base_cost"`
	UnitCost    float64                `json:"unit_cost"`
	Unit        string                 `json:"unit"` // "token", "second", "image", "character"
	MinCost     int                    `json:"min_cost"`
	MaxCost     int                    `json:"max_cost,omitempty"`
	Discounts   []AIOperationDiscount  `json:"discounts,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AIOperationDiscount represents discount for AI operations
type AIOperationDiscount struct {
	Type        string  `json:"type"` // "volume", "subscription", "promotional"
	Threshold   int     `json:"threshold"`
	Discount    float64 `json:"discount"` // Percentage discount (0.1 = 10%)
	Description string  `json:"description"`
}

// AIServiceIntegration represents AI service integration configuration
type AIServiceIntegration struct {
	ServiceName string                 `json:"service_name"`
	APIKey      string                 `json:"api_key"`
	BaseURL     string                 `json:"base_url"`
	Enabled     bool                   `json:"enabled"`
	RateLimit   AIServiceRateLimit     `json:"rate_limit"`
	Operations  []string               `json:"operations"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AIServiceRateLimit represents rate limit for AI service
type AIServiceRateLimit struct {
	RequestsPerMinute int `json:"requests_per_minute"`
	RequestsPerHour   int `json:"requests_per_hour"`
	RequestsPerDay    int `json:"requests_per_day"`
	TokensPerMinute   int `json:"tokens_per_minute,omitempty"`
	TokensPerHour     int `json:"tokens_per_hour,omitempty"`
	TokensPerDay      int `json:"tokens_per_day,omitempty"`
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Template holds the schema definition for the Template entity.
type Template struct {
	ent.Schema
}

// Fields of the Template.
func (Template) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who created this template"),
		field.String("workspace_id").
			MaxLen(36).
			Optional().
			Comment("Workspace this template belongs to"),
		field.String("name").
			MaxLen(200),
		field.Text("description").
			Optional(),
		field.Text("content").
			Comment("Template content with variables"),
		field.JSON("variables", []string{}).
			Optional().
			Comment("List of variable names used in template"),
		field.JSON("variable_descriptions", map[string]string{}).
			Optional().
			Comment("Descriptions for each variable"),
		field.Enum("category").
			Values("social_media", "blog", "email", "marketing", "educational", "promotional", "personal", "business").
			Default("social_media"),
		field.JSON("platforms", []string{}).
			Optional().
			Comment("Recommended platforms for this template"),
		field.Enum("tone").
			Values("professional", "friendly", "humorous", "formal", "casual", "enthusiastic", "authoritative").
			Default("professional"),
		field.Enum("content_type").
			Values("educational", "promotional", "entertainment", "informational", "news", "tutorial").
			Default("informational"),
		field.JSON("hashtags", []string{}).
			Optional().
			Comment("Default hashtags for this template"),
		field.Bool("is_public").
			Default(false).
			Comment("Whether this template is publicly available"),
		field.Bool("is_featured").
			Default(false).
			Comment("Whether this template is featured"),
		field.Int("usage_count").
			Default(0).
			Comment("Number of times this template has been used"),
		field.Float("rating").
			Default(0.0).
			Range(0.0, 5.0).
			Comment("Average user rating"),
		field.Int("rating_count").
			Default(0).
			Comment("Number of ratings"),
		field.JSON("tags", []string{}).
			Optional().
			Comment("Tags for categorization and search"),
		field.String("language").
			MaxLen(5).
			Default("en").
			Comment("Template language code"),
		field.JSON("training_files", []string{}).
			Optional().
			Comment("File URLs used for RAG training"),
		field.Bool("rag_trained").
			Default(false).
			Comment("Whether RAG training has been completed"),
		field.Time("rag_trained_at").
			Optional().
			Comment("When RAG training was last completed"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the Template.
func (Template) Edges() []ent.Edge {
	return nil
}

// Indexes of the Template.
func (Template) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("workspace_id"),
		index.Fields("category"),
		index.Fields("tone"),
		index.Fields("content_type"),
		index.Fields("is_public"),
		index.Fields("is_featured"),
		index.Fields("language"),
		index.Fields("rag_trained"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "category"),
		index.Fields("is_public", "is_featured"),
		index.Fields("rating", "rating_count"),
	}
}

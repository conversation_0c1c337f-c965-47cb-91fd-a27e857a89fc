package handlers

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TransactionServiceHandler handles transaction-related gRPC operations
type TransactionServiceHandler struct {
	transactionUseCase transaction.UseCase
	logger             logging.Logger
}

// NewTransactionServiceHandler creates a new transaction service handler
func NewTransactionServiceHandler(
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *TransactionServiceHandler {
	return &TransactionServiceHandler{
		transactionUseCase: transactionUseCase,
		logger:             logger,
	}
}

// ListTransactions lists credit transactions for a user
func (h *TransactionServiceHandler) ListTransactions(ctx context.Context, req *creditv1.ListTransactionsRequest) (*creditv1.ListTransactionsResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
	}).Info("Listing transactions")

	// Convert pagination
	page := int(req.Pagination.Page)
	limit := int(req.Pagination.Limit)
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	// Convert date filters
	var dateFrom, dateTo *time.Time
	if req.DateFrom != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			dateFrom = &parsed
		}
	}
	if req.DateTo != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			dateTo = &parsed
		}
	}

	// Create internal request
	historyReq := &models.CreditHistoryRequest{
		UserID:    req.UserId,
		Type:      req.Type,
		DateFrom:  dateFrom,
		DateTo:    dateTo,
		Page:      page,
		Limit:     limit,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// Get transactions
	history, err := h.transactionUseCase.ListTransactions(ctx, historyReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list transactions")
		return nil, status.Errorf(codes.Internal, "failed to list transactions: %v", err)
	}

	// Convert transactions to proto format
	var protoTransactions []*creditv1.CreditTransaction
	for _, tx := range history.Transactions {
		protoTx := &creditv1.CreditTransaction{
			Id:          tx.ID,
			UserId:      tx.UserID,
			Type:        tx.Type,
			Amount:      int32(tx.Amount),
			Description: tx.Description,
			ReferenceId: tx.ReferenceID,
			CreatedAt:   timestamppb.New(tx.CreatedAt),
		}
		protoTransactions = append(protoTransactions, protoTx)
	}

	// Convert pagination
	protoPagination := &commonv1.PaginationResponse{
		Page:       int32(history.Pagination.Page),
		Limit:      int32(history.Pagination.Limit),
		TotalItems: int32(history.Pagination.Total),
		TotalPages: int32(history.Pagination.TotalPages),
		HasNext:    history.Pagination.Page < history.Pagination.TotalPages,
		HasPrev:    history.Pagination.Page > 1,
	}

	return &creditv1.ListTransactionsResponse{
		Transactions:       protoTransactions,
		Pagination:         protoPagination,
		TotalCreditsEarned: int32(history.Summary.TotalCreditsEarned),
		TotalCreditsSpent:  int32(history.Summary.TotalCreditsUsed),
	}, nil
}

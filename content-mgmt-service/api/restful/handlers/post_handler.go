package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/content-mgmt-service/usecase/post"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PostHandler handles post-related HTTP requests
type Post<PERSON>andler struct {
	postUseCase post.UseCase
	logger      logging.Logger
}

// NewPostHandler creates a new post handler
func NewPostHandler(postUseCase post.UseCase, logger logging.Logger) *PostHandler {
	return &PostHandler{
		postUseCase: postUseCase,
		logger:      logger,
	}
}

// CreatePost godoc
// @Summary Create a new post
// @Description Create a new social media post
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.CreatePostRequest true "Create post request"
// @Success 201 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/posts [post]
func (h *PostHandler) CreatePost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid create post request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	post, err := h.postUseCase.CreatePost(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create post")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CREATE_FAILED",
			Message: "Failed to create post",
		})
		return
	}

	c.JSON(http.StatusCreated, post)
}

// ListPosts godoc
// @Summary List posts
// @Description List posts with pagination and filters
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Post status filter"
// @Param workspace_id query string false "Workspace ID filter"
// @Success 200 {object} models.ListPostsResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/posts [get]
func (h *PostHandler) ListPosts(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")
	workspaceID := c.Query("workspace_id")

	req := &models.ListPostsRequest{
		UserID:      userID.(string),
		Page:        page,
		Limit:       limit,
		Status:      status,
		WorkspaceID: workspaceID,
	}

	resp, err := h.postUseCase.ListPosts(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list posts")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list posts",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetPost godoc
// @Summary Get post by ID
// @Description Get post information by ID
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id} [get]
func (h *PostHandler) GetPost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	post, err := h.postUseCase.GetPost(c.Request.Context(), postID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to this post",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_POST_FAILED",
				Message: "Failed to get post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, post)
}

// UpdatePost godoc
// @Summary Update post
// @Description Update post information
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body models.UpdatePostRequest true "Update post request"
// @Success 200 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id} [put]
func (h *PostHandler) UpdatePost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	var req models.UpdatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update post request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = postID
	req.UserID = userID.(string)

	post, err := h.postUseCase.UpdatePost(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to update this post",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UPDATE_FAILED",
				Message: "Failed to update post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, post)
}

// DeletePost godoc
// @Summary Delete post
// @Description Delete a post
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id} [delete]
func (h *PostHandler) DeletePost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	err := h.postUseCase.DeletePost(c.Request.Context(), postID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to delete this post",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DELETE_FAILED",
				Message: "Failed to delete post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Post deleted successfully",
	})
}

// SchedulePost godoc
// @Summary Schedule a post
// @Description Schedule a post for future publishing
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body models.SchedulePostRequest true "Schedule post request"
// @Success 200 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/schedule [post]
func (h *PostHandler) SchedulePost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	var req models.SchedulePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid schedule post request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.PostID = postID
	req.UserID = userID.(string)

	post, err := h.postUseCase.SchedulePost(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to schedule post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "INVALID_SCHEDULE_TIME":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_SCHEDULE_TIME",
				Message: "Schedule time must be in the future",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "SCHEDULE_FAILED",
				Message: "Failed to schedule post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, post)
}

// PublishPost godoc
// @Summary Publish a post immediately
// @Description Publish a post to social media platforms immediately
// @Tags posts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param request body models.PublishPostRequest true "Publish post request"
// @Success 200 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/publish [post]
func (h *PostHandler) PublishPost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	var req models.PublishPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid publish post request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.PostID = postID
	req.UserID = userID.(string)

	post, err := h.postUseCase.PublishPost(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "INTEGRATION_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INTEGRATION_NOT_FOUND",
				Message: "Social media integration not configured",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "PUBLISH_FAILED",
				Message: "Failed to publish post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, post)
}

// CancelScheduledPost godoc
// @Summary Cancel a scheduled post
// @Description Cancel a scheduled post
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} models.PostResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/cancel [post]
func (h *PostHandler) CancelScheduledPost(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	post, err := h.postUseCase.CancelScheduledPost(c.Request.Context(), postID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel scheduled post")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		case "POST_NOT_SCHEDULED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "POST_NOT_SCHEDULED",
				Message: "Post is not scheduled",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "CANCEL_FAILED",
				Message: "Failed to cancel scheduled post",
			})
		}
		return
	}

	c.JSON(http.StatusOK, post)
}

// GetPostStatus godoc
// @Summary Get post status
// @Description Get post publishing status
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Success 200 {object} models.PostStatusResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/status [get]
func (h *PostHandler) GetPostStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	status, err := h.postUseCase.GetPostStatus(c.Request.Context(), postID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post status")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_STATUS_FAILED",
				Message: "Failed to get post status",
			})
		}
		return
	}

	c.JSON(http.StatusOK, status)
}

// GetPostAnalytics godoc
// @Summary Get post analytics
// @Description Get detailed analytics for a post
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param platform query string false "Platform filter"
// @Param date_from query string false "Start date (YYYY-MM-DD)"
// @Param date_to query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} models.PostAnalyticsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/analytics [get]
func (h *PostHandler) GetPostAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	req := &models.PostAnalyticsRequest{
		PostID:   postID,
		UserID:   userID.(string),
		Platform: c.Query("platform"),
		DateFrom: c.Query("date_from"),
		DateTo:   c.Query("date_to"),
	}

	analytics, err := h.postUseCase.GetPostAnalytics(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post analytics")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "ANALYTICS_FAILED",
				Message: "Failed to get post analytics",
			})
		}
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetPostEngagement godoc
// @Summary Get post engagement
// @Description Get engagement metrics for a post
// @Tags posts
// @Produce json
// @Security BearerAuth
// @Param id path string true "Post ID"
// @Param platform query string false "Platform filter"
// @Param period query string false "Time period (hour, day, week, month)"
// @Success 200 {object} models.PostEngagementResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/posts/{id}/engagement [get]
func (h *PostHandler) GetPostEngagement(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_POST_ID",
			Message: "Post ID is required",
		})
		return
	}

	req := &models.PostEngagementRequest{
		PostID:   postID,
		UserID:   userID.(string),
		Platform: c.Query("platform"),
		Period:   c.Query("period"),
	}

	engagement, err := h.postUseCase.GetPostEngagement(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post engagement")

		switch err.Error() {
		case "POST_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "POST_NOT_FOUND",
				Message: "Post not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "ENGAGEMENT_FAILED",
				Message: "Failed to get post engagement",
			})
		}
		return
	}

	c.JSON(http.StatusOK, engagement)
}

// GetPostsAnalytics gets analytics for multiple posts
func (h *PostHandler) GetPostsAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	workspaceID := c.Query("workspace_id")

	analytics, err := h.postUseCase.GetPostsAnalytics(c.Request.Context(), userID.(string), workspaceID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get posts analytics")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "ANALYTICS_FAILED",
			Message: "Failed to get posts analytics",
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

package storage

import (
	"context"
	"fmt"
	"io"
	"time"
)

// Storage defines the storage interface for asset management
type Storage interface {
	// Upload operations
	GeneratePresignedUploadURL(ctx context.Context, key string, contentType string, expiresIn time.Duration) (*PresignedURL, error)
	UploadFile(ctx context.Context, key string, data io.Reader, contentType string, metadata map[string]string) (*UploadResult, error)

	// Download operations
	GeneratePresignedDownloadURL(ctx context.Context, key string, expiresIn time.Duration) (string, error)
	DownloadFile(ctx context.Context, key string) (io.ReadCloser, error)

	// File operations
	DeleteFile(ctx context.Context, key string) error
	CopyFile(ctx context.Context, sourceKey, destKey string) error
	FileExists(ctx context.Context, key string) (bool, error)
	GetFileInfo(ctx context.Context, key string) (*FileInfo, error)

	// Batch operations
	DeleteFiles(ctx context.Context, keys []string) error
	ListFiles(ctx context.Context, prefix string, maxKeys int) ([]*FileInfo, error)

	// Health check
	HealthCheck(ctx context.Context) error
}

// PresignedURL represents a presigned URL for upload
type PresignedURL struct {
	URL       string            `json:"url"`
	FormData  map[string]string `json:"form_data,omitempty"`
	ExpiresAt time.Time         `json:"expires_at"`
}

// UploadResult represents the result of a file upload
type UploadResult struct {
	Key         string            `json:"key"`
	ETag        string            `json:"etag"`
	Size        int64             `json:"size"`
	ContentType string            `json:"content_type"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	PublicURL   string            `json:"public_url,omitempty"`
}

// FileInfo represents file information
type FileInfo struct {
	Key          string            `json:"key"`
	Size         int64             `json:"size"`
	ContentType  string            `json:"content_type"`
	ETag         string            `json:"etag"`
	LastModified time.Time         `json:"last_modified"`
	Metadata     map[string]string `json:"metadata,omitempty"`
	PublicURL    string            `json:"public_url,omitempty"`
}

// Config represents storage configuration
type Config struct {
	Provider        string   `json:"provider"` // s3, minio, gcs, azure
	Endpoint        string   `json:"endpoint"` // For MinIO or custom S3 endpoint
	Region          string   `json:"region"`
	Bucket          string   `json:"bucket"`
	AccessKey       string   `json:"access_key"`
	SecretKey       string   `json:"secret_key"`
	UseSSL          bool     `json:"use_ssl"`
	PublicURL       string   `json:"public_url"`       // Base URL for public access
	MaxFileSize     int64    `json:"max_file_size"`    // Maximum file size in bytes
	AllowedTypes    []string `json:"allowed_types"`    // Allowed MIME types
	UploadTimeout   int      `json:"upload_timeout"`   // Upload timeout in seconds
	DownloadTimeout int      `json:"download_timeout"` // Download timeout in seconds
}

// Error types
type StorageError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *StorageError) Error() string {
	return e.Message
}

// Common error codes
const (
	ErrCodeFileNotFound     = "FILE_NOT_FOUND"
	ErrCodeFileExists       = "FILE_EXISTS"
	ErrCodeInvalidKey       = "INVALID_KEY"
	ErrCodeUploadFailed     = "UPLOAD_FAILED"
	ErrCodeDownloadFailed   = "DOWNLOAD_FAILED"
	ErrCodeDeleteFailed     = "DELETE_FAILED"
	ErrCodePermissionDenied = "PERMISSION_DENIED"
	ErrCodeQuotaExceeded    = "QUOTA_EXCEEDED"
	ErrCodeInvalidConfig    = "INVALID_CONFIG"
	ErrCodeConnectionFailed = "CONNECTION_FAILED"
	ErrCodeTimeout          = "TIMEOUT"
	ErrCodeUnknown          = "UNKNOWN"
)

// Utility functions for key generation
func GenerateAssetKey(userID, assetID, filename string) string {
	return fmt.Sprintf("assets/%s/%s/%s", userID, assetID, filename)
}

func GenerateThumbnailKey(userID, assetID, filename string) string {
	return fmt.Sprintf("thumbnails/%s/%s/thumb_%s", userID, assetID, filename)
}

func GenerateTemporaryKey(sessionID, filename string) string {
	return fmt.Sprintf("temp/%s/%s", sessionID, filename)
}

// File type validation
func IsValidImageType(contentType string) bool {
	validTypes := []string{
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/svg+xml",
		"image/bmp",
		"image/tiff",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

func IsValidVideoType(contentType string) bool {
	validTypes := []string{
		"video/mp4",
		"video/mpeg",
		"video/quicktime",
		"video/x-msvideo",
		"video/webm",
		"video/ogg",
		"video/3gpp",
		"video/x-flv",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

func IsValidAudioType(contentType string) bool {
	validTypes := []string{
		"audio/mpeg",
		"audio/mp4",
		"audio/wav",
		"audio/ogg",
		"audio/webm",
		"audio/aac",
		"audio/flac",
		"audio/x-wav",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

func IsValidDocumentType(contentType string) bool {
	validTypes := []string{
		"application/pdf",
		"application/msword",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		"application/vnd.ms-excel",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		"application/vnd.ms-powerpoint",
		"application/vnd.openxmlformats-officedocument.presentationml.presentation",
		"text/plain",
		"text/csv",
		"application/json",
		"application/xml",
		"text/xml",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return true
		}
	}
	return false
}

// File size limits (in bytes)
const (
	MaxImageSize    = 10 * 1024 * 1024  // 10MB
	MaxVideoSize    = 100 * 1024 * 1024 // 100MB
	MaxAudioSize    = 50 * 1024 * 1024  // 50MB
	MaxDocumentSize = 25 * 1024 * 1024  // 25MB
	MaxOtherSize    = 10 * 1024 * 1024  // 10MB
)

func GetMaxFileSizeForType(assetType string) int64 {
	switch assetType {
	case "image":
		return MaxImageSize
	case "video":
		return MaxVideoSize
	case "audio":
		return MaxAudioSize
	case "document":
		return MaxDocumentSize
	default:
		return MaxOtherSize
	}
}

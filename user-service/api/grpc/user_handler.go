package grpc

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/social-content-ai/pkg-shared/logging"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/user-service/pkg/mapping"
	"github.com/social-content-ai/user-service/usecase/user"
)

// UserHandler implements the UserService gRPC server
type UserHandler struct {
	userv1.UnimplementedUserServiceServer
	userUC user.UseCase
	logger logging.Logger
}

// NewUserHandler creates a new user gRPC handler
func NewUser<PERSON>andler(userUC user.UseCase, logger logging.Logger) *UserHandler {
	return &UserHandler{
		userUC: userUC,
		logger: logger,
	}
}

// Create<PERSON>ser creates a new user
func (h *UserHandler) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.User, error) {
	// Validate request
	if req.Email == "" {
		return nil, status.Error(codes.InvalidArgument, "email is required")
	}
	if req.Password == "" {
		return nil, status.Error(codes.InvalidArgument, "password is required")
	}
	if req.FullName == "" {
		return nil, status.Error(codes.InvalidArgument, "full_name is required")
	}

	// Convert to usecase request using mapping
	createReq := mapping.ToCreateUserRequest(req)
	if createReq == nil {
		return nil, status.Error(codes.InvalidArgument, "invalid request")
	}

	// Create user
	userResp, err := h.userUC.CreateUser(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		return nil, status.Error(codes.Internal, "failed to create user")
	}

	// Convert to proto response
	return mapping.ToProtoUserFromResponse(userResp), nil
}

// GetUser retrieves a user by ID
func (h *UserHandler) GetUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.User, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	// Convert string ID to uint64
	userID, err := mapping.StringToUint64(req.Id)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid user ID")
	}

	userResp, err := h.userUC.GetUserByID(ctx, userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.Id).Error("Failed to get user")
		return nil, status.Error(codes.NotFound, "user not found")
	}

	return mapping.ToProtoUserFromResponse(userResp), nil
}

// UpdateUser updates user information
func (h *UserHandler) UpdateUser(ctx context.Context, req *userv1.UpdateUserRequest) (*userv1.User, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	// Convert to usecase request using mapping
	updateReq := mapping.ToUpdateUserRequest(req)
	if updateReq == nil {
		return nil, status.Error(codes.InvalidArgument, "invalid request")
	}

	// Update user
	userResp, err := h.userUC.UpdateUser(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.Id).Error("Failed to update user")
		return nil, status.Error(codes.Internal, "failed to update user")
	}

	return mapping.ToProtoUserFromResponse(userResp), nil
}

// ListUsers lists users with pagination and filters
func (h *UserHandler) ListUsers(ctx context.Context, req *userv1.ListUsersRequest) (*userv1.ListUsersResponse, error) {
	// Convert to usecase request using mapping
	listReq := mapping.ToListUsersRequest(req)
	if listReq == nil {
		return nil, status.Error(codes.InvalidArgument, "invalid request")
	}

	// List users
	usersResp, err := h.userUC.ListUsers(ctx, listReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list users")
		return nil, status.Error(codes.Internal, "failed to list users")
	}

	// Convert to proto response
	protoUsers := make([]*userv1.User, len(usersResp.Users))
	for i, u := range usersResp.Users {
		protoUsers[i] = mapping.ToProtoUserFromResponse(&u)
	}

	// Calculate pagination info
	hasNext := usersResp.Pagination.Page < usersResp.Pagination.TotalPages
	hasPrev := usersResp.Pagination.Page > 1

	return &userv1.ListUsersResponse{
		Users: protoUsers,
		Pagination: &commonv1.PaginationResponse{
			Page:       int32(usersResp.Pagination.Page),
			Limit:      int32(usersResp.Pagination.Limit),
			TotalItems: int32(usersResp.Pagination.Total),
			TotalPages: int32(usersResp.Pagination.TotalPages),
			HasNext:    hasNext,
			HasPrev:    hasPrev,
		},
	}, nil
}

// DeleteUser soft deletes a user
func (h *UserHandler) DeleteUser(ctx context.Context, req *userv1.DeleteUserRequest) (*userv1.Empty, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	// Convert string ID to uint64
	userID, err := mapping.StringToUint64(req.Id)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid user ID")
	}

	err = h.userUC.DeleteUser(ctx, userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.Id).Error("Failed to delete user")
		return nil, status.Error(codes.Internal, "failed to delete user")
	}

	return &userv1.Empty{}, nil
}

// VerifyUser marks a user as verified
func (h *UserHandler) VerifyUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.User, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "id is required")
	}

	// For now, just get the user since VerifyUser is not in the interface
	// This should be implemented in the usecase interface
	userID, err := mapping.StringToUint64(req.Id)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid user ID")
	}

	userResp, err := h.userUC.GetUserByID(ctx, userID)
	if err != nil {
		h.logger.WithError(err).WithField("user_id", req.Id).Error("Failed to get user")
		return nil, status.Error(codes.NotFound, "user not found")
	}

	return mapping.ToProtoUserFromResponse(userResp), nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditTransactionDelete is the builder for deleting a CreditTransaction entity.
type CreditTransactionDelete struct {
	config
	hooks    []Hook
	mutation *CreditTransactionMutation
}

// Where appends a list predicates to the CreditTransactionDelete builder.
func (ctd *CreditTransactionDelete) Where(ps ...predicate.CreditTransaction) *CreditTransactionDelete {
	ctd.mutation.Where(ps...)
	return ctd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ctd *CreditTransactionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ctd.sqlExec, ctd.mutation, ctd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ctd *CreditTransactionDelete) ExecX(ctx context.Context) int {
	n, err := ctd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ctd *CreditTransactionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(credittransaction.Table, sqlgraph.NewFieldSpec(credittransaction.FieldID, field.TypeUUID))
	if ps := ctd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ctd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ctd.mutation.done = true
	return affected, err
}

// CreditTransactionDeleteOne is the builder for deleting a single CreditTransaction entity.
type CreditTransactionDeleteOne struct {
	ctd *CreditTransactionDelete
}

// Where appends a list predicates to the CreditTransactionDelete builder.
func (ctdo *CreditTransactionDeleteOne) Where(ps ...predicate.CreditTransaction) *CreditTransactionDeleteOne {
	ctdo.ctd.mutation.Where(ps...)
	return ctdo
}

// Exec executes the deletion query.
func (ctdo *CreditTransactionDeleteOne) Exec(ctx context.Context) error {
	n, err := ctdo.ctd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{credittransaction.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ctdo *CreditTransactionDeleteOne) ExecX(ctx context.Context) {
	if err := ctdo.Exec(ctx); err != nil {
		panic(err)
	}
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// CreditPlan holds the schema definition for the CreditPlan entity.
// This represents one-time credit purchase plans (different from subscription plans)
type CreditPlan struct {
	ent.Schema
}

// Fields of the CreditPlan.
func (CreditPlan) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.String("name").
			MaxLen(100),
		field.String("display_name").
			MaxLen(100),
		field.Text("description").
			Optional(),
		field.Int("credits").
			Min(1), // Number of credits in this plan
		field.Int64("price"). // Price in cents
			Min(0),
		field.String("currency").
			MaxLen(3).
			Default("USD"),
		field.Int("discount_percentage"). // Discount percentage
			Default(0).
			Min(0).
			Max(100),
		field.Bool("is_popular").
			Default(false),
		field.Bool("is_active").
			Default(true),
		field.JSON("features", []string{}).
			Optional(),
		field.Time("valid_until").
			Optional(), // Plan expiration date
		field.Int("sort_order").
			Default(0),
		field.String("stripe_price_id").
			MaxLen(100).
			Optional(),
		field.String("stripe_product_id").
			MaxLen(100).
			Optional(),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the CreditPlan.
func (CreditPlan) Edges() []ent.Edge {
	return nil
}

// Indexes of the CreditPlan.
func (CreditPlan) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("is_active"),
		index.Fields("is_popular"),
		index.Fields("sort_order"),
		index.Fields("valid_until"),
		index.Fields("stripe_price_id"),
		index.Fields("created_at"),
	}
}

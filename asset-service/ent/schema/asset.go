package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Asset holds the schema definition for the Asset entity.
type Asset struct {
	ent.Schema
}

// Fields of the Asset.
func (Asset) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who uploaded this asset"),
		field.String("workspace_id").
			MaxLen(36).
			Optional().
			Comment("Workspace this asset belongs to"),
		field.UUID("folder_id", uuid.UUID{}).
			Optional().
			Nillable().
			Comment("Folder this asset belongs to"),
		field.String("file_name").
			MaxLen(255).
			Comment("Current filename"),
		field.String("original_name").
			MaxLen(255).
			Comment("Original filename"),
		field.String("file_type").
			MaxLen(100).
			Comment("MIME type of the file"),
		field.String("file_extension").
			MaxLen(10).
			Optional().
			Comment("File extension"),
		field.String("s3_key").
			MaxLen(500).
			Comment("S3 storage key/path"),
		field.String("s3_bucket").
			MaxLen(100).
			Comment("S3 bucket name"),
		field.String("content_hash").
			MaxLen(64).
			Optional().
			Comment("Content hash for deduplication"),
		field.String("purpose").
			MaxLen(50).
			Default("general").
			Comment("Purpose of the asset"),
		field.Int64("file_size").
			Default(0).
			Comment("File size in bytes"),
		field.String("thumbnail_url").
			MaxLen(1000).
			Optional().
			Comment("Thumbnail URL for images/videos"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("File metadata (dimensions, duration, etc.)"),
		field.String("status").
			MaxLen(20).
			Default("uploaded").
			Comment("Asset status"),
		field.Bool("is_public").
			Default(false).
			Comment("Whether the asset is publicly accessible"),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional().
			Nillable(),
	}
}

// Edges of the Asset.
func (Asset) Edges() []ent.Edge {
	return []ent.Edge{
		// Edge to folder
		edge.From("folder", Folder.Type).
			Ref("assets").
			Unique().
			Field("folder_id"),
	}
}

// Indexes of the Asset.
func (Asset) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("folder_id"),
		index.Fields("workspace_id"),
		index.Fields("status"),
		index.Fields("file_type"),
		index.Fields("content_hash"),
		index.Fields("is_public"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "folder_id"),
		index.Fields("user_id", "created_at"),
		index.Fields("user_id", "status"),
	}
}

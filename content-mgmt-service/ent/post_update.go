// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
)

// PostUpdate is the builder for updating Post entities.
type PostUpdate struct {
	config
	hooks    []Hook
	mutation *PostMutation
}

// Where appends a list predicates to the PostUpdate builder.
func (pu *PostUpdate) Where(ps ...predicate.Post) *PostUpdate {
	pu.mutation.Where(ps...)
	return pu
}

// SetUserID sets the "user_id" field.
func (pu *PostUpdate) SetUserID(u uuid.UUID) *PostUpdate {
	pu.mutation.SetUserID(u)
	return pu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (pu *PostUpdate) SetNillableUserID(u *uuid.UUID) *PostUpdate {
	if u != nil {
		pu.SetUserID(*u)
	}
	return pu
}

// SetWorkspaceID sets the "workspace_id" field.
func (pu *PostUpdate) SetWorkspaceID(s string) *PostUpdate {
	pu.mutation.SetWorkspaceID(s)
	return pu
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (pu *PostUpdate) SetNillableWorkspaceID(s *string) *PostUpdate {
	if s != nil {
		pu.SetWorkspaceID(*s)
	}
	return pu
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (pu *PostUpdate) ClearWorkspaceID() *PostUpdate {
	pu.mutation.ClearWorkspaceID()
	return pu
}

// SetTemplateID sets the "template_id" field.
func (pu *PostUpdate) SetTemplateID(s string) *PostUpdate {
	pu.mutation.SetTemplateID(s)
	return pu
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (pu *PostUpdate) SetNillableTemplateID(s *string) *PostUpdate {
	if s != nil {
		pu.SetTemplateID(*s)
	}
	return pu
}

// ClearTemplateID clears the value of the "template_id" field.
func (pu *PostUpdate) ClearTemplateID() *PostUpdate {
	pu.mutation.ClearTemplateID()
	return pu
}

// SetGenerationID sets the "generation_id" field.
func (pu *PostUpdate) SetGenerationID(s string) *PostUpdate {
	pu.mutation.SetGenerationID(s)
	return pu
}

// SetNillableGenerationID sets the "generation_id" field if the given value is not nil.
func (pu *PostUpdate) SetNillableGenerationID(s *string) *PostUpdate {
	if s != nil {
		pu.SetGenerationID(*s)
	}
	return pu
}

// ClearGenerationID clears the value of the "generation_id" field.
func (pu *PostUpdate) ClearGenerationID() *PostUpdate {
	pu.mutation.ClearGenerationID()
	return pu
}

// SetTitle sets the "title" field.
func (pu *PostUpdate) SetTitle(s string) *PostUpdate {
	pu.mutation.SetTitle(s)
	return pu
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (pu *PostUpdate) SetNillableTitle(s *string) *PostUpdate {
	if s != nil {
		pu.SetTitle(*s)
	}
	return pu
}

// SetContent sets the "content" field.
func (pu *PostUpdate) SetContent(s string) *PostUpdate {
	pu.mutation.SetContent(s)
	return pu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (pu *PostUpdate) SetNillableContent(s *string) *PostUpdate {
	if s != nil {
		pu.SetContent(*s)
	}
	return pu
}

// SetOriginalContent sets the "original_content" field.
func (pu *PostUpdate) SetOriginalContent(s string) *PostUpdate {
	pu.mutation.SetOriginalContent(s)
	return pu
}

// SetNillableOriginalContent sets the "original_content" field if the given value is not nil.
func (pu *PostUpdate) SetNillableOriginalContent(s *string) *PostUpdate {
	if s != nil {
		pu.SetOriginalContent(*s)
	}
	return pu
}

// ClearOriginalContent clears the value of the "original_content" field.
func (pu *PostUpdate) ClearOriginalContent() *PostUpdate {
	pu.mutation.ClearOriginalContent()
	return pu
}

// SetPlatforms sets the "platforms" field.
func (pu *PostUpdate) SetPlatforms(s []string) *PostUpdate {
	pu.mutation.SetPlatforms(s)
	return pu
}

// AppendPlatforms appends s to the "platforms" field.
func (pu *PostUpdate) AppendPlatforms(s []string) *PostUpdate {
	pu.mutation.AppendPlatforms(s)
	return pu
}

// ClearPlatforms clears the value of the "platforms" field.
func (pu *PostUpdate) ClearPlatforms() *PostUpdate {
	pu.mutation.ClearPlatforms()
	return pu
}

// SetStatus sets the "status" field.
func (pu *PostUpdate) SetStatus(po post.Status) *PostUpdate {
	pu.mutation.SetStatus(po)
	return pu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pu *PostUpdate) SetNillableStatus(po *post.Status) *PostUpdate {
	if po != nil {
		pu.SetStatus(*po)
	}
	return pu
}

// SetScheduledAt sets the "scheduled_at" field.
func (pu *PostUpdate) SetScheduledAt(t time.Time) *PostUpdate {
	pu.mutation.SetScheduledAt(t)
	return pu
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (pu *PostUpdate) SetNillableScheduledAt(t *time.Time) *PostUpdate {
	if t != nil {
		pu.SetScheduledAt(*t)
	}
	return pu
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (pu *PostUpdate) ClearScheduledAt() *PostUpdate {
	pu.mutation.ClearScheduledAt()
	return pu
}

// SetPublishedAt sets the "published_at" field.
func (pu *PostUpdate) SetPublishedAt(t time.Time) *PostUpdate {
	pu.mutation.SetPublishedAt(t)
	return pu
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (pu *PostUpdate) SetNillablePublishedAt(t *time.Time) *PostUpdate {
	if t != nil {
		pu.SetPublishedAt(*t)
	}
	return pu
}

// ClearPublishedAt clears the value of the "published_at" field.
func (pu *PostUpdate) ClearPublishedAt() *PostUpdate {
	pu.mutation.ClearPublishedAt()
	return pu
}

// SetPlatformPosts sets the "platform_posts" field.
func (pu *PostUpdate) SetPlatformPosts(m map[string]interface{}) *PostUpdate {
	pu.mutation.SetPlatformPosts(m)
	return pu
}

// ClearPlatformPosts clears the value of the "platform_posts" field.
func (pu *PostUpdate) ClearPlatformPosts() *PostUpdate {
	pu.mutation.ClearPlatformPosts()
	return pu
}

// SetHashtags sets the "hashtags" field.
func (pu *PostUpdate) SetHashtags(s []string) *PostUpdate {
	pu.mutation.SetHashtags(s)
	return pu
}

// AppendHashtags appends s to the "hashtags" field.
func (pu *PostUpdate) AppendHashtags(s []string) *PostUpdate {
	pu.mutation.AppendHashtags(s)
	return pu
}

// ClearHashtags clears the value of the "hashtags" field.
func (pu *PostUpdate) ClearHashtags() *PostUpdate {
	pu.mutation.ClearHashtags()
	return pu
}

// SetMentions sets the "mentions" field.
func (pu *PostUpdate) SetMentions(s []string) *PostUpdate {
	pu.mutation.SetMentions(s)
	return pu
}

// AppendMentions appends s to the "mentions" field.
func (pu *PostUpdate) AppendMentions(s []string) *PostUpdate {
	pu.mutation.AppendMentions(s)
	return pu
}

// ClearMentions clears the value of the "mentions" field.
func (pu *PostUpdate) ClearMentions() *PostUpdate {
	pu.mutation.ClearMentions()
	return pu
}

// SetMediaUrls sets the "media_urls" field.
func (pu *PostUpdate) SetMediaUrls(s []string) *PostUpdate {
	pu.mutation.SetMediaUrls(s)
	return pu
}

// AppendMediaUrls appends s to the "media_urls" field.
func (pu *PostUpdate) AppendMediaUrls(s []string) *PostUpdate {
	pu.mutation.AppendMediaUrls(s)
	return pu
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (pu *PostUpdate) ClearMediaUrls() *PostUpdate {
	pu.mutation.ClearMediaUrls()
	return pu
}

// SetVariables sets the "variables" field.
func (pu *PostUpdate) SetVariables(m map[string]string) *PostUpdate {
	pu.mutation.SetVariables(m)
	return pu
}

// ClearVariables clears the value of the "variables" field.
func (pu *PostUpdate) ClearVariables() *PostUpdate {
	pu.mutation.ClearVariables()
	return pu
}

// SetVisibility sets the "visibility" field.
func (pu *PostUpdate) SetVisibility(po post.Visibility) *PostUpdate {
	pu.mutation.SetVisibility(po)
	return pu
}

// SetNillableVisibility sets the "visibility" field if the given value is not nil.
func (pu *PostUpdate) SetNillableVisibility(po *post.Visibility) *PostUpdate {
	if po != nil {
		pu.SetVisibility(*po)
	}
	return pu
}

// SetLanguage sets the "language" field.
func (pu *PostUpdate) SetLanguage(s string) *PostUpdate {
	pu.mutation.SetLanguage(s)
	return pu
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (pu *PostUpdate) SetNillableLanguage(s *string) *PostUpdate {
	if s != nil {
		pu.SetLanguage(*s)
	}
	return pu
}

// SetAnalytics sets the "analytics" field.
func (pu *PostUpdate) SetAnalytics(m map[string]interface{}) *PostUpdate {
	pu.mutation.SetAnalytics(m)
	return pu
}

// ClearAnalytics clears the value of the "analytics" field.
func (pu *PostUpdate) ClearAnalytics() *PostUpdate {
	pu.mutation.ClearAnalytics()
	return pu
}

// SetMetadata sets the "metadata" field.
func (pu *PostUpdate) SetMetadata(m map[string]interface{}) *PostUpdate {
	pu.mutation.SetMetadata(m)
	return pu
}

// ClearMetadata clears the value of the "metadata" field.
func (pu *PostUpdate) ClearMetadata() *PostUpdate {
	pu.mutation.ClearMetadata()
	return pu
}

// SetUpdatedAt sets the "updated_at" field.
func (pu *PostUpdate) SetUpdatedAt(t time.Time) *PostUpdate {
	pu.mutation.SetUpdatedAt(t)
	return pu
}

// SetDeletedAt sets the "deleted_at" field.
func (pu *PostUpdate) SetDeletedAt(t time.Time) *PostUpdate {
	pu.mutation.SetDeletedAt(t)
	return pu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pu *PostUpdate) SetNillableDeletedAt(t *time.Time) *PostUpdate {
	if t != nil {
		pu.SetDeletedAt(*t)
	}
	return pu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (pu *PostUpdate) ClearDeletedAt() *PostUpdate {
	pu.mutation.ClearDeletedAt()
	return pu
}

// Mutation returns the PostMutation object of the builder.
func (pu *PostUpdate) Mutation() *PostMutation {
	return pu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pu *PostUpdate) Save(ctx context.Context) (int, error) {
	pu.defaults()
	return withHooks(ctx, pu.sqlSave, pu.mutation, pu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pu *PostUpdate) SaveX(ctx context.Context) int {
	affected, err := pu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pu *PostUpdate) Exec(ctx context.Context) error {
	_, err := pu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pu *PostUpdate) ExecX(ctx context.Context) {
	if err := pu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pu *PostUpdate) defaults() {
	if _, ok := pu.mutation.UpdatedAt(); !ok {
		v := post.UpdateDefaultUpdatedAt()
		pu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pu *PostUpdate) check() error {
	if v, ok := pu.mutation.WorkspaceID(); ok {
		if err := post.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Post.workspace_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.TemplateID(); ok {
		if err := post.TemplateIDValidator(v); err != nil {
			return &ValidationError{Name: "template_id", err: fmt.Errorf(`ent: validator failed for field "Post.template_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.GenerationID(); ok {
		if err := post.GenerationIDValidator(v); err != nil {
			return &ValidationError{Name: "generation_id", err: fmt.Errorf(`ent: validator failed for field "Post.generation_id": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Title(); ok {
		if err := post.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Post.title": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Status(); ok {
		if err := post.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Post.status": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Visibility(); ok {
		if err := post.VisibilityValidator(v); err != nil {
			return &ValidationError{Name: "visibility", err: fmt.Errorf(`ent: validator failed for field "Post.visibility": %w`, err)}
		}
	}
	if v, ok := pu.mutation.Language(); ok {
		if err := post.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Post.language": %w`, err)}
		}
	}
	return nil
}

func (pu *PostUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(post.Table, post.Columns, sqlgraph.NewFieldSpec(post.FieldID, field.TypeUUID))
	if ps := pu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pu.mutation.UserID(); ok {
		_spec.SetField(post.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := pu.mutation.WorkspaceID(); ok {
		_spec.SetField(post.FieldWorkspaceID, field.TypeString, value)
	}
	if pu.mutation.WorkspaceIDCleared() {
		_spec.ClearField(post.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := pu.mutation.TemplateID(); ok {
		_spec.SetField(post.FieldTemplateID, field.TypeString, value)
	}
	if pu.mutation.TemplateIDCleared() {
		_spec.ClearField(post.FieldTemplateID, field.TypeString)
	}
	if value, ok := pu.mutation.GenerationID(); ok {
		_spec.SetField(post.FieldGenerationID, field.TypeString, value)
	}
	if pu.mutation.GenerationIDCleared() {
		_spec.ClearField(post.FieldGenerationID, field.TypeString)
	}
	if value, ok := pu.mutation.Title(); ok {
		_spec.SetField(post.FieldTitle, field.TypeString, value)
	}
	if value, ok := pu.mutation.Content(); ok {
		_spec.SetField(post.FieldContent, field.TypeString, value)
	}
	if value, ok := pu.mutation.OriginalContent(); ok {
		_spec.SetField(post.FieldOriginalContent, field.TypeString, value)
	}
	if pu.mutation.OriginalContentCleared() {
		_spec.ClearField(post.FieldOriginalContent, field.TypeString)
	}
	if value, ok := pu.mutation.Platforms(); ok {
		_spec.SetField(post.FieldPlatforms, field.TypeJSON, value)
	}
	if value, ok := pu.mutation.AppendedPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldPlatforms, value)
		})
	}
	if pu.mutation.PlatformsCleared() {
		_spec.ClearField(post.FieldPlatforms, field.TypeJSON)
	}
	if value, ok := pu.mutation.Status(); ok {
		_spec.SetField(post.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := pu.mutation.ScheduledAt(); ok {
		_spec.SetField(post.FieldScheduledAt, field.TypeTime, value)
	}
	if pu.mutation.ScheduledAtCleared() {
		_spec.ClearField(post.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := pu.mutation.PublishedAt(); ok {
		_spec.SetField(post.FieldPublishedAt, field.TypeTime, value)
	}
	if pu.mutation.PublishedAtCleared() {
		_spec.ClearField(post.FieldPublishedAt, field.TypeTime)
	}
	if value, ok := pu.mutation.PlatformPosts(); ok {
		_spec.SetField(post.FieldPlatformPosts, field.TypeJSON, value)
	}
	if pu.mutation.PlatformPostsCleared() {
		_spec.ClearField(post.FieldPlatformPosts, field.TypeJSON)
	}
	if value, ok := pu.mutation.Hashtags(); ok {
		_spec.SetField(post.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := pu.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldHashtags, value)
		})
	}
	if pu.mutation.HashtagsCleared() {
		_spec.ClearField(post.FieldHashtags, field.TypeJSON)
	}
	if value, ok := pu.mutation.Mentions(); ok {
		_spec.SetField(post.FieldMentions, field.TypeJSON, value)
	}
	if value, ok := pu.mutation.AppendedMentions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldMentions, value)
		})
	}
	if pu.mutation.MentionsCleared() {
		_spec.ClearField(post.FieldMentions, field.TypeJSON)
	}
	if value, ok := pu.mutation.MediaUrls(); ok {
		_spec.SetField(post.FieldMediaUrls, field.TypeJSON, value)
	}
	if value, ok := pu.mutation.AppendedMediaUrls(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldMediaUrls, value)
		})
	}
	if pu.mutation.MediaUrlsCleared() {
		_spec.ClearField(post.FieldMediaUrls, field.TypeJSON)
	}
	if value, ok := pu.mutation.Variables(); ok {
		_spec.SetField(post.FieldVariables, field.TypeJSON, value)
	}
	if pu.mutation.VariablesCleared() {
		_spec.ClearField(post.FieldVariables, field.TypeJSON)
	}
	if value, ok := pu.mutation.Visibility(); ok {
		_spec.SetField(post.FieldVisibility, field.TypeEnum, value)
	}
	if value, ok := pu.mutation.Language(); ok {
		_spec.SetField(post.FieldLanguage, field.TypeString, value)
	}
	if value, ok := pu.mutation.Analytics(); ok {
		_spec.SetField(post.FieldAnalytics, field.TypeJSON, value)
	}
	if pu.mutation.AnalyticsCleared() {
		_spec.ClearField(post.FieldAnalytics, field.TypeJSON)
	}
	if value, ok := pu.mutation.Metadata(); ok {
		_spec.SetField(post.FieldMetadata, field.TypeJSON, value)
	}
	if pu.mutation.MetadataCleared() {
		_spec.ClearField(post.FieldMetadata, field.TypeJSON)
	}
	if value, ok := pu.mutation.UpdatedAt(); ok {
		_spec.SetField(post.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pu.mutation.DeletedAt(); ok {
		_spec.SetField(post.FieldDeletedAt, field.TypeTime, value)
	}
	if pu.mutation.DeletedAtCleared() {
		_spec.ClearField(post.FieldDeletedAt, field.TypeTime)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, pu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{post.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pu.mutation.done = true
	return n, nil
}

// PostUpdateOne is the builder for updating a single Post entity.
type PostUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PostMutation
}

// SetUserID sets the "user_id" field.
func (puo *PostUpdateOne) SetUserID(u uuid.UUID) *PostUpdateOne {
	puo.mutation.SetUserID(u)
	return puo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableUserID(u *uuid.UUID) *PostUpdateOne {
	if u != nil {
		puo.SetUserID(*u)
	}
	return puo
}

// SetWorkspaceID sets the "workspace_id" field.
func (puo *PostUpdateOne) SetWorkspaceID(s string) *PostUpdateOne {
	puo.mutation.SetWorkspaceID(s)
	return puo
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableWorkspaceID(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetWorkspaceID(*s)
	}
	return puo
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (puo *PostUpdateOne) ClearWorkspaceID() *PostUpdateOne {
	puo.mutation.ClearWorkspaceID()
	return puo
}

// SetTemplateID sets the "template_id" field.
func (puo *PostUpdateOne) SetTemplateID(s string) *PostUpdateOne {
	puo.mutation.SetTemplateID(s)
	return puo
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableTemplateID(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetTemplateID(*s)
	}
	return puo
}

// ClearTemplateID clears the value of the "template_id" field.
func (puo *PostUpdateOne) ClearTemplateID() *PostUpdateOne {
	puo.mutation.ClearTemplateID()
	return puo
}

// SetGenerationID sets the "generation_id" field.
func (puo *PostUpdateOne) SetGenerationID(s string) *PostUpdateOne {
	puo.mutation.SetGenerationID(s)
	return puo
}

// SetNillableGenerationID sets the "generation_id" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableGenerationID(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetGenerationID(*s)
	}
	return puo
}

// ClearGenerationID clears the value of the "generation_id" field.
func (puo *PostUpdateOne) ClearGenerationID() *PostUpdateOne {
	puo.mutation.ClearGenerationID()
	return puo
}

// SetTitle sets the "title" field.
func (puo *PostUpdateOne) SetTitle(s string) *PostUpdateOne {
	puo.mutation.SetTitle(s)
	return puo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableTitle(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetTitle(*s)
	}
	return puo
}

// SetContent sets the "content" field.
func (puo *PostUpdateOne) SetContent(s string) *PostUpdateOne {
	puo.mutation.SetContent(s)
	return puo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableContent(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetContent(*s)
	}
	return puo
}

// SetOriginalContent sets the "original_content" field.
func (puo *PostUpdateOne) SetOriginalContent(s string) *PostUpdateOne {
	puo.mutation.SetOriginalContent(s)
	return puo
}

// SetNillableOriginalContent sets the "original_content" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableOriginalContent(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetOriginalContent(*s)
	}
	return puo
}

// ClearOriginalContent clears the value of the "original_content" field.
func (puo *PostUpdateOne) ClearOriginalContent() *PostUpdateOne {
	puo.mutation.ClearOriginalContent()
	return puo
}

// SetPlatforms sets the "platforms" field.
func (puo *PostUpdateOne) SetPlatforms(s []string) *PostUpdateOne {
	puo.mutation.SetPlatforms(s)
	return puo
}

// AppendPlatforms appends s to the "platforms" field.
func (puo *PostUpdateOne) AppendPlatforms(s []string) *PostUpdateOne {
	puo.mutation.AppendPlatforms(s)
	return puo
}

// ClearPlatforms clears the value of the "platforms" field.
func (puo *PostUpdateOne) ClearPlatforms() *PostUpdateOne {
	puo.mutation.ClearPlatforms()
	return puo
}

// SetStatus sets the "status" field.
func (puo *PostUpdateOne) SetStatus(po post.Status) *PostUpdateOne {
	puo.mutation.SetStatus(po)
	return puo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableStatus(po *post.Status) *PostUpdateOne {
	if po != nil {
		puo.SetStatus(*po)
	}
	return puo
}

// SetScheduledAt sets the "scheduled_at" field.
func (puo *PostUpdateOne) SetScheduledAt(t time.Time) *PostUpdateOne {
	puo.mutation.SetScheduledAt(t)
	return puo
}

// SetNillableScheduledAt sets the "scheduled_at" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableScheduledAt(t *time.Time) *PostUpdateOne {
	if t != nil {
		puo.SetScheduledAt(*t)
	}
	return puo
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (puo *PostUpdateOne) ClearScheduledAt() *PostUpdateOne {
	puo.mutation.ClearScheduledAt()
	return puo
}

// SetPublishedAt sets the "published_at" field.
func (puo *PostUpdateOne) SetPublishedAt(t time.Time) *PostUpdateOne {
	puo.mutation.SetPublishedAt(t)
	return puo
}

// SetNillablePublishedAt sets the "published_at" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillablePublishedAt(t *time.Time) *PostUpdateOne {
	if t != nil {
		puo.SetPublishedAt(*t)
	}
	return puo
}

// ClearPublishedAt clears the value of the "published_at" field.
func (puo *PostUpdateOne) ClearPublishedAt() *PostUpdateOne {
	puo.mutation.ClearPublishedAt()
	return puo
}

// SetPlatformPosts sets the "platform_posts" field.
func (puo *PostUpdateOne) SetPlatformPosts(m map[string]interface{}) *PostUpdateOne {
	puo.mutation.SetPlatformPosts(m)
	return puo
}

// ClearPlatformPosts clears the value of the "platform_posts" field.
func (puo *PostUpdateOne) ClearPlatformPosts() *PostUpdateOne {
	puo.mutation.ClearPlatformPosts()
	return puo
}

// SetHashtags sets the "hashtags" field.
func (puo *PostUpdateOne) SetHashtags(s []string) *PostUpdateOne {
	puo.mutation.SetHashtags(s)
	return puo
}

// AppendHashtags appends s to the "hashtags" field.
func (puo *PostUpdateOne) AppendHashtags(s []string) *PostUpdateOne {
	puo.mutation.AppendHashtags(s)
	return puo
}

// ClearHashtags clears the value of the "hashtags" field.
func (puo *PostUpdateOne) ClearHashtags() *PostUpdateOne {
	puo.mutation.ClearHashtags()
	return puo
}

// SetMentions sets the "mentions" field.
func (puo *PostUpdateOne) SetMentions(s []string) *PostUpdateOne {
	puo.mutation.SetMentions(s)
	return puo
}

// AppendMentions appends s to the "mentions" field.
func (puo *PostUpdateOne) AppendMentions(s []string) *PostUpdateOne {
	puo.mutation.AppendMentions(s)
	return puo
}

// ClearMentions clears the value of the "mentions" field.
func (puo *PostUpdateOne) ClearMentions() *PostUpdateOne {
	puo.mutation.ClearMentions()
	return puo
}

// SetMediaUrls sets the "media_urls" field.
func (puo *PostUpdateOne) SetMediaUrls(s []string) *PostUpdateOne {
	puo.mutation.SetMediaUrls(s)
	return puo
}

// AppendMediaUrls appends s to the "media_urls" field.
func (puo *PostUpdateOne) AppendMediaUrls(s []string) *PostUpdateOne {
	puo.mutation.AppendMediaUrls(s)
	return puo
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (puo *PostUpdateOne) ClearMediaUrls() *PostUpdateOne {
	puo.mutation.ClearMediaUrls()
	return puo
}

// SetVariables sets the "variables" field.
func (puo *PostUpdateOne) SetVariables(m map[string]string) *PostUpdateOne {
	puo.mutation.SetVariables(m)
	return puo
}

// ClearVariables clears the value of the "variables" field.
func (puo *PostUpdateOne) ClearVariables() *PostUpdateOne {
	puo.mutation.ClearVariables()
	return puo
}

// SetVisibility sets the "visibility" field.
func (puo *PostUpdateOne) SetVisibility(po post.Visibility) *PostUpdateOne {
	puo.mutation.SetVisibility(po)
	return puo
}

// SetNillableVisibility sets the "visibility" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableVisibility(po *post.Visibility) *PostUpdateOne {
	if po != nil {
		puo.SetVisibility(*po)
	}
	return puo
}

// SetLanguage sets the "language" field.
func (puo *PostUpdateOne) SetLanguage(s string) *PostUpdateOne {
	puo.mutation.SetLanguage(s)
	return puo
}

// SetNillableLanguage sets the "language" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableLanguage(s *string) *PostUpdateOne {
	if s != nil {
		puo.SetLanguage(*s)
	}
	return puo
}

// SetAnalytics sets the "analytics" field.
func (puo *PostUpdateOne) SetAnalytics(m map[string]interface{}) *PostUpdateOne {
	puo.mutation.SetAnalytics(m)
	return puo
}

// ClearAnalytics clears the value of the "analytics" field.
func (puo *PostUpdateOne) ClearAnalytics() *PostUpdateOne {
	puo.mutation.ClearAnalytics()
	return puo
}

// SetMetadata sets the "metadata" field.
func (puo *PostUpdateOne) SetMetadata(m map[string]interface{}) *PostUpdateOne {
	puo.mutation.SetMetadata(m)
	return puo
}

// ClearMetadata clears the value of the "metadata" field.
func (puo *PostUpdateOne) ClearMetadata() *PostUpdateOne {
	puo.mutation.ClearMetadata()
	return puo
}

// SetUpdatedAt sets the "updated_at" field.
func (puo *PostUpdateOne) SetUpdatedAt(t time.Time) *PostUpdateOne {
	puo.mutation.SetUpdatedAt(t)
	return puo
}

// SetDeletedAt sets the "deleted_at" field.
func (puo *PostUpdateOne) SetDeletedAt(t time.Time) *PostUpdateOne {
	puo.mutation.SetDeletedAt(t)
	return puo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (puo *PostUpdateOne) SetNillableDeletedAt(t *time.Time) *PostUpdateOne {
	if t != nil {
		puo.SetDeletedAt(*t)
	}
	return puo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (puo *PostUpdateOne) ClearDeletedAt() *PostUpdateOne {
	puo.mutation.ClearDeletedAt()
	return puo
}

// Mutation returns the PostMutation object of the builder.
func (puo *PostUpdateOne) Mutation() *PostMutation {
	return puo.mutation
}

// Where appends a list predicates to the PostUpdate builder.
func (puo *PostUpdateOne) Where(ps ...predicate.Post) *PostUpdateOne {
	puo.mutation.Where(ps...)
	return puo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (puo *PostUpdateOne) Select(field string, fields ...string) *PostUpdateOne {
	puo.fields = append([]string{field}, fields...)
	return puo
}

// Save executes the query and returns the updated Post entity.
func (puo *PostUpdateOne) Save(ctx context.Context) (*Post, error) {
	puo.defaults()
	return withHooks(ctx, puo.sqlSave, puo.mutation, puo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (puo *PostUpdateOne) SaveX(ctx context.Context) *Post {
	node, err := puo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (puo *PostUpdateOne) Exec(ctx context.Context) error {
	_, err := puo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (puo *PostUpdateOne) ExecX(ctx context.Context) {
	if err := puo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (puo *PostUpdateOne) defaults() {
	if _, ok := puo.mutation.UpdatedAt(); !ok {
		v := post.UpdateDefaultUpdatedAt()
		puo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (puo *PostUpdateOne) check() error {
	if v, ok := puo.mutation.WorkspaceID(); ok {
		if err := post.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Post.workspace_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.TemplateID(); ok {
		if err := post.TemplateIDValidator(v); err != nil {
			return &ValidationError{Name: "template_id", err: fmt.Errorf(`ent: validator failed for field "Post.template_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.GenerationID(); ok {
		if err := post.GenerationIDValidator(v); err != nil {
			return &ValidationError{Name: "generation_id", err: fmt.Errorf(`ent: validator failed for field "Post.generation_id": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Title(); ok {
		if err := post.TitleValidator(v); err != nil {
			return &ValidationError{Name: "title", err: fmt.Errorf(`ent: validator failed for field "Post.title": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Status(); ok {
		if err := post.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Post.status": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Visibility(); ok {
		if err := post.VisibilityValidator(v); err != nil {
			return &ValidationError{Name: "visibility", err: fmt.Errorf(`ent: validator failed for field "Post.visibility": %w`, err)}
		}
	}
	if v, ok := puo.mutation.Language(); ok {
		if err := post.LanguageValidator(v); err != nil {
			return &ValidationError{Name: "language", err: fmt.Errorf(`ent: validator failed for field "Post.language": %w`, err)}
		}
	}
	return nil
}

func (puo *PostUpdateOne) sqlSave(ctx context.Context) (_node *Post, err error) {
	if err := puo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(post.Table, post.Columns, sqlgraph.NewFieldSpec(post.FieldID, field.TypeUUID))
	id, ok := puo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Post.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := puo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, post.FieldID)
		for _, f := range fields {
			if !post.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != post.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := puo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := puo.mutation.UserID(); ok {
		_spec.SetField(post.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := puo.mutation.WorkspaceID(); ok {
		_spec.SetField(post.FieldWorkspaceID, field.TypeString, value)
	}
	if puo.mutation.WorkspaceIDCleared() {
		_spec.ClearField(post.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := puo.mutation.TemplateID(); ok {
		_spec.SetField(post.FieldTemplateID, field.TypeString, value)
	}
	if puo.mutation.TemplateIDCleared() {
		_spec.ClearField(post.FieldTemplateID, field.TypeString)
	}
	if value, ok := puo.mutation.GenerationID(); ok {
		_spec.SetField(post.FieldGenerationID, field.TypeString, value)
	}
	if puo.mutation.GenerationIDCleared() {
		_spec.ClearField(post.FieldGenerationID, field.TypeString)
	}
	if value, ok := puo.mutation.Title(); ok {
		_spec.SetField(post.FieldTitle, field.TypeString, value)
	}
	if value, ok := puo.mutation.Content(); ok {
		_spec.SetField(post.FieldContent, field.TypeString, value)
	}
	if value, ok := puo.mutation.OriginalContent(); ok {
		_spec.SetField(post.FieldOriginalContent, field.TypeString, value)
	}
	if puo.mutation.OriginalContentCleared() {
		_spec.ClearField(post.FieldOriginalContent, field.TypeString)
	}
	if value, ok := puo.mutation.Platforms(); ok {
		_spec.SetField(post.FieldPlatforms, field.TypeJSON, value)
	}
	if value, ok := puo.mutation.AppendedPlatforms(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldPlatforms, value)
		})
	}
	if puo.mutation.PlatformsCleared() {
		_spec.ClearField(post.FieldPlatforms, field.TypeJSON)
	}
	if value, ok := puo.mutation.Status(); ok {
		_spec.SetField(post.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := puo.mutation.ScheduledAt(); ok {
		_spec.SetField(post.FieldScheduledAt, field.TypeTime, value)
	}
	if puo.mutation.ScheduledAtCleared() {
		_spec.ClearField(post.FieldScheduledAt, field.TypeTime)
	}
	if value, ok := puo.mutation.PublishedAt(); ok {
		_spec.SetField(post.FieldPublishedAt, field.TypeTime, value)
	}
	if puo.mutation.PublishedAtCleared() {
		_spec.ClearField(post.FieldPublishedAt, field.TypeTime)
	}
	if value, ok := puo.mutation.PlatformPosts(); ok {
		_spec.SetField(post.FieldPlatformPosts, field.TypeJSON, value)
	}
	if puo.mutation.PlatformPostsCleared() {
		_spec.ClearField(post.FieldPlatformPosts, field.TypeJSON)
	}
	if value, ok := puo.mutation.Hashtags(); ok {
		_spec.SetField(post.FieldHashtags, field.TypeJSON, value)
	}
	if value, ok := puo.mutation.AppendedHashtags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldHashtags, value)
		})
	}
	if puo.mutation.HashtagsCleared() {
		_spec.ClearField(post.FieldHashtags, field.TypeJSON)
	}
	if value, ok := puo.mutation.Mentions(); ok {
		_spec.SetField(post.FieldMentions, field.TypeJSON, value)
	}
	if value, ok := puo.mutation.AppendedMentions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldMentions, value)
		})
	}
	if puo.mutation.MentionsCleared() {
		_spec.ClearField(post.FieldMentions, field.TypeJSON)
	}
	if value, ok := puo.mutation.MediaUrls(); ok {
		_spec.SetField(post.FieldMediaUrls, field.TypeJSON, value)
	}
	if value, ok := puo.mutation.AppendedMediaUrls(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, post.FieldMediaUrls, value)
		})
	}
	if puo.mutation.MediaUrlsCleared() {
		_spec.ClearField(post.FieldMediaUrls, field.TypeJSON)
	}
	if value, ok := puo.mutation.Variables(); ok {
		_spec.SetField(post.FieldVariables, field.TypeJSON, value)
	}
	if puo.mutation.VariablesCleared() {
		_spec.ClearField(post.FieldVariables, field.TypeJSON)
	}
	if value, ok := puo.mutation.Visibility(); ok {
		_spec.SetField(post.FieldVisibility, field.TypeEnum, value)
	}
	if value, ok := puo.mutation.Language(); ok {
		_spec.SetField(post.FieldLanguage, field.TypeString, value)
	}
	if value, ok := puo.mutation.Analytics(); ok {
		_spec.SetField(post.FieldAnalytics, field.TypeJSON, value)
	}
	if puo.mutation.AnalyticsCleared() {
		_spec.ClearField(post.FieldAnalytics, field.TypeJSON)
	}
	if value, ok := puo.mutation.Metadata(); ok {
		_spec.SetField(post.FieldMetadata, field.TypeJSON, value)
	}
	if puo.mutation.MetadataCleared() {
		_spec.ClearField(post.FieldMetadata, field.TypeJSON)
	}
	if value, ok := puo.mutation.UpdatedAt(); ok {
		_spec.SetField(post.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := puo.mutation.DeletedAt(); ok {
		_spec.SetField(post.FieldDeletedAt, field.TypeTime, value)
	}
	if puo.mutation.DeletedAtCleared() {
		_spec.ClearField(post.FieldDeletedAt, field.TypeTime)
	}
	_node = &Post{config: puo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, puo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{post.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	puo.mutation.done = true
	return _node, nil
}

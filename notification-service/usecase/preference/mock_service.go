package preference

import (
	"context"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// MockService implements the preference UseCase interface with mock implementations
type MockService struct {
	logger logging.Logger
}

// NewMockService creates a new mock preference service
func NewMockService(logger logging.Logger) UseCase {
	return &MockService{
		logger: logger,
	}
}

// GetPreferences gets user notification preferences
func (s *MockService) GetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", userID).Debug("Getting preferences (mock)")

	return &PreferencesResponse{
		UserID:           userID,
		EmailEnabled:     true,
		SMSEnabled:       false,
		PushEnabled:      true,
		WebSocketEnabled: true,
		InAppEnabled:     true,
		EventPreferences: map[string]EventPreference{
			"user_registered": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"password_reset": {
				Email:     true,
				SMS:       true,
				Push:      false,
				WebSocket: true,
				InApp:     true,
			},
			"content_generated": {
				Email:     false,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"payment_received": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"marketing": {
				Email:     false,
				SMS:       false,
				Push:      false,
				WebSocket: false,
				InApp:     false,
			},
		},
		QuietHours: &QuietHours{
			Enabled:   true,
			StartHour: 22,
			EndHour:   8,
		},
		Timezone:  "UTC",
		Language:  "en",
		CreatedAt: time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
		UpdatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// UpdatePreferences updates user notification preferences
func (s *MockService) UpdatePreferences(ctx context.Context, req *UpdatePreferencesRequest) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Updating preferences (mock)")

	// Handle pointer fields
	emailEnabled := true
	if req.EmailEnabled != nil {
		emailEnabled = *req.EmailEnabled
	}
	smsEnabled := false
	if req.SMSEnabled != nil {
		smsEnabled = *req.SMSEnabled
	}
	pushEnabled := true
	if req.PushEnabled != nil {
		pushEnabled = *req.PushEnabled
	}
	webSocketEnabled := true
	if req.WebSocketEnabled != nil {
		webSocketEnabled = *req.WebSocketEnabled
	}
	inAppEnabled := true
	if req.InAppEnabled != nil {
		inAppEnabled = *req.InAppEnabled
	}

	// Return updated preferences (mock)
	return &PreferencesResponse{
		UserID:           req.UserID,
		EmailEnabled:     emailEnabled,
		SMSEnabled:       smsEnabled,
		PushEnabled:      pushEnabled,
		WebSocketEnabled: webSocketEnabled,
		InAppEnabled:     inAppEnabled,
		EventPreferences: req.EventPreferences,
		QuietHours:       req.QuietHours,
		Timezone:         req.Timezone,
		Language:         req.Language,
		CreatedAt:        time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
		UpdatedAt:        time.Now().Format(time.RFC3339),
	}, nil
}

// ResetPreferences resets user preferences to defaults
func (s *MockService) ResetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error) {
	s.logger.WithField("user_id", userID).Info("Resetting preferences (mock)")

	return &PreferencesResponse{
		UserID:           userID,
		EmailEnabled:     true,
		SMSEnabled:       false,
		PushEnabled:      true,
		WebSocketEnabled: true,
		InAppEnabled:     true,
		EventPreferences: map[string]EventPreference{
			"user_registered": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"password_reset": {
				Email:     true,
				SMS:       true,
				Push:      false,
				WebSocket: true,
				InApp:     true,
			},
			"content_generated": {
				Email:     false,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"payment_received": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"marketing": {
				Email:     false,
				SMS:       false,
				Push:      false,
				WebSocket: false,
				InApp:     false,
			},
		},
		QuietHours: &QuietHours{
			Enabled:   false,
			StartHour: 22,
			EndHour:   8,
		},
		Timezone:  "UTC",
		Language:  "en",
		CreatedAt: time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
		UpdatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

// GetDefaultPreferences gets default notification preferences
func (s *MockService) GetDefaultPreferences(ctx context.Context) (*PreferencesResponse, error) {
	s.logger.Debug("Getting default preferences (mock)")

	return &PreferencesResponse{
		UserID:           "",
		EmailEnabled:     true,
		SMSEnabled:       false,
		PushEnabled:      true,
		WebSocketEnabled: true,
		InAppEnabled:     true,
		EventPreferences: map[string]EventPreference{
			"user_registered": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"password_reset": {
				Email:     true,
				SMS:       true,
				Push:      false,
				WebSocket: true,
				InApp:     true,
			},
			"content_generated": {
				Email:     false,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"payment_received": {
				Email:     true,
				SMS:       false,
				Push:      true,
				WebSocket: true,
				InApp:     true,
			},
			"marketing": {
				Email:     false,
				SMS:       false,
				Push:      false,
				WebSocket: false,
				InApp:     false,
			},
		},
		QuietHours: &QuietHours{
			Enabled:   false,
			StartHour: 22,
			EndHour:   8,
		},
		Timezone:  "UTC",
		Language:  "en",
		CreatedAt: time.Now().Format(time.RFC3339),
		UpdatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

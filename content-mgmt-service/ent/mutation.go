// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypePost      = "Post"
	TypeTemplate  = "Template"
	TypeWorkspace = "Workspace"
)

// PostMutation represents an operation that mutates the Post nodes in the graph.
type PostMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	user_id          *uuid.UUID
	workspace_id     *string
	template_id      *string
	generation_id    *string
	title            *string
	content          *string
	original_content *string
	platforms        *[]string
	appendplatforms  []string
	status           *post.Status
	scheduled_at     *time.Time
	published_at     *time.Time
	platform_posts   *map[string]interface{}
	hashtags         *[]string
	appendhashtags   []string
	mentions         *[]string
	appendmentions   []string
	media_urls       *[]string
	appendmedia_urls []string
	variables        *map[string]string
	visibility       *post.Visibility
	language         *string
	analytics        *map[string]interface{}
	metadata         *map[string]interface{}
	created_at       *time.Time
	updated_at       *time.Time
	deleted_at       *time.Time
	clearedFields    map[string]struct{}
	done             bool
	oldValue         func(context.Context) (*Post, error)
	predicates       []predicate.Post
}

var _ ent.Mutation = (*PostMutation)(nil)

// postOption allows management of the mutation configuration using functional options.
type postOption func(*PostMutation)

// newPostMutation creates new mutation for the Post entity.
func newPostMutation(c config, op Op, opts ...postOption) *PostMutation {
	m := &PostMutation{
		config:        c,
		op:            op,
		typ:           TypePost,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPostID sets the ID field of the mutation.
func withPostID(id uuid.UUID) postOption {
	return func(m *PostMutation) {
		var (
			err   error
			once  sync.Once
			value *Post
		)
		m.oldValue = func(ctx context.Context) (*Post, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Post.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPost sets the old Post of the mutation.
func withPost(node *Post) postOption {
	return func(m *PostMutation) {
		m.oldValue = func(context.Context) (*Post, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PostMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PostMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Post entities.
func (m *PostMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PostMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PostMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Post.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *PostMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *PostMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *PostMutation) ResetUserID() {
	m.user_id = nil
}

// SetWorkspaceID sets the "workspace_id" field.
func (m *PostMutation) SetWorkspaceID(s string) {
	m.workspace_id = &s
}

// WorkspaceID returns the value of the "workspace_id" field in the mutation.
func (m *PostMutation) WorkspaceID() (r string, exists bool) {
	v := m.workspace_id
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkspaceID returns the old "workspace_id" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldWorkspaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkspaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkspaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkspaceID: %w", err)
	}
	return oldValue.WorkspaceID, nil
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (m *PostMutation) ClearWorkspaceID() {
	m.workspace_id = nil
	m.clearedFields[post.FieldWorkspaceID] = struct{}{}
}

// WorkspaceIDCleared returns if the "workspace_id" field was cleared in this mutation.
func (m *PostMutation) WorkspaceIDCleared() bool {
	_, ok := m.clearedFields[post.FieldWorkspaceID]
	return ok
}

// ResetWorkspaceID resets all changes to the "workspace_id" field.
func (m *PostMutation) ResetWorkspaceID() {
	m.workspace_id = nil
	delete(m.clearedFields, post.FieldWorkspaceID)
}

// SetTemplateID sets the "template_id" field.
func (m *PostMutation) SetTemplateID(s string) {
	m.template_id = &s
}

// TemplateID returns the value of the "template_id" field in the mutation.
func (m *PostMutation) TemplateID() (r string, exists bool) {
	v := m.template_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTemplateID returns the old "template_id" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldTemplateID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTemplateID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTemplateID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTemplateID: %w", err)
	}
	return oldValue.TemplateID, nil
}

// ClearTemplateID clears the value of the "template_id" field.
func (m *PostMutation) ClearTemplateID() {
	m.template_id = nil
	m.clearedFields[post.FieldTemplateID] = struct{}{}
}

// TemplateIDCleared returns if the "template_id" field was cleared in this mutation.
func (m *PostMutation) TemplateIDCleared() bool {
	_, ok := m.clearedFields[post.FieldTemplateID]
	return ok
}

// ResetTemplateID resets all changes to the "template_id" field.
func (m *PostMutation) ResetTemplateID() {
	m.template_id = nil
	delete(m.clearedFields, post.FieldTemplateID)
}

// SetGenerationID sets the "generation_id" field.
func (m *PostMutation) SetGenerationID(s string) {
	m.generation_id = &s
}

// GenerationID returns the value of the "generation_id" field in the mutation.
func (m *PostMutation) GenerationID() (r string, exists bool) {
	v := m.generation_id
	if v == nil {
		return
	}
	return *v, true
}

// OldGenerationID returns the old "generation_id" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldGenerationID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGenerationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGenerationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGenerationID: %w", err)
	}
	return oldValue.GenerationID, nil
}

// ClearGenerationID clears the value of the "generation_id" field.
func (m *PostMutation) ClearGenerationID() {
	m.generation_id = nil
	m.clearedFields[post.FieldGenerationID] = struct{}{}
}

// GenerationIDCleared returns if the "generation_id" field was cleared in this mutation.
func (m *PostMutation) GenerationIDCleared() bool {
	_, ok := m.clearedFields[post.FieldGenerationID]
	return ok
}

// ResetGenerationID resets all changes to the "generation_id" field.
func (m *PostMutation) ResetGenerationID() {
	m.generation_id = nil
	delete(m.clearedFields, post.FieldGenerationID)
}

// SetTitle sets the "title" field.
func (m *PostMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *PostMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *PostMutation) ResetTitle() {
	m.title = nil
}

// SetContent sets the "content" field.
func (m *PostMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *PostMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *PostMutation) ResetContent() {
	m.content = nil
}

// SetOriginalContent sets the "original_content" field.
func (m *PostMutation) SetOriginalContent(s string) {
	m.original_content = &s
}

// OriginalContent returns the value of the "original_content" field in the mutation.
func (m *PostMutation) OriginalContent() (r string, exists bool) {
	v := m.original_content
	if v == nil {
		return
	}
	return *v, true
}

// OldOriginalContent returns the old "original_content" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldOriginalContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOriginalContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOriginalContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOriginalContent: %w", err)
	}
	return oldValue.OriginalContent, nil
}

// ClearOriginalContent clears the value of the "original_content" field.
func (m *PostMutation) ClearOriginalContent() {
	m.original_content = nil
	m.clearedFields[post.FieldOriginalContent] = struct{}{}
}

// OriginalContentCleared returns if the "original_content" field was cleared in this mutation.
func (m *PostMutation) OriginalContentCleared() bool {
	_, ok := m.clearedFields[post.FieldOriginalContent]
	return ok
}

// ResetOriginalContent resets all changes to the "original_content" field.
func (m *PostMutation) ResetOriginalContent() {
	m.original_content = nil
	delete(m.clearedFields, post.FieldOriginalContent)
}

// SetPlatforms sets the "platforms" field.
func (m *PostMutation) SetPlatforms(s []string) {
	m.platforms = &s
	m.appendplatforms = nil
}

// Platforms returns the value of the "platforms" field in the mutation.
func (m *PostMutation) Platforms() (r []string, exists bool) {
	v := m.platforms
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatforms returns the old "platforms" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldPlatforms(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatforms is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatforms requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatforms: %w", err)
	}
	return oldValue.Platforms, nil
}

// AppendPlatforms adds s to the "platforms" field.
func (m *PostMutation) AppendPlatforms(s []string) {
	m.appendplatforms = append(m.appendplatforms, s...)
}

// AppendedPlatforms returns the list of values that were appended to the "platforms" field in this mutation.
func (m *PostMutation) AppendedPlatforms() ([]string, bool) {
	if len(m.appendplatforms) == 0 {
		return nil, false
	}
	return m.appendplatforms, true
}

// ClearPlatforms clears the value of the "platforms" field.
func (m *PostMutation) ClearPlatforms() {
	m.platforms = nil
	m.appendplatforms = nil
	m.clearedFields[post.FieldPlatforms] = struct{}{}
}

// PlatformsCleared returns if the "platforms" field was cleared in this mutation.
func (m *PostMutation) PlatformsCleared() bool {
	_, ok := m.clearedFields[post.FieldPlatforms]
	return ok
}

// ResetPlatforms resets all changes to the "platforms" field.
func (m *PostMutation) ResetPlatforms() {
	m.platforms = nil
	m.appendplatforms = nil
	delete(m.clearedFields, post.FieldPlatforms)
}

// SetStatus sets the "status" field.
func (m *PostMutation) SetStatus(po post.Status) {
	m.status = &po
}

// Status returns the value of the "status" field in the mutation.
func (m *PostMutation) Status() (r post.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldStatus(ctx context.Context) (v post.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *PostMutation) ResetStatus() {
	m.status = nil
}

// SetScheduledAt sets the "scheduled_at" field.
func (m *PostMutation) SetScheduledAt(t time.Time) {
	m.scheduled_at = &t
}

// ScheduledAt returns the value of the "scheduled_at" field in the mutation.
func (m *PostMutation) ScheduledAt() (r time.Time, exists bool) {
	v := m.scheduled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldScheduledAt returns the old "scheduled_at" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldScheduledAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldScheduledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldScheduledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldScheduledAt: %w", err)
	}
	return oldValue.ScheduledAt, nil
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (m *PostMutation) ClearScheduledAt() {
	m.scheduled_at = nil
	m.clearedFields[post.FieldScheduledAt] = struct{}{}
}

// ScheduledAtCleared returns if the "scheduled_at" field was cleared in this mutation.
func (m *PostMutation) ScheduledAtCleared() bool {
	_, ok := m.clearedFields[post.FieldScheduledAt]
	return ok
}

// ResetScheduledAt resets all changes to the "scheduled_at" field.
func (m *PostMutation) ResetScheduledAt() {
	m.scheduled_at = nil
	delete(m.clearedFields, post.FieldScheduledAt)
}

// SetPublishedAt sets the "published_at" field.
func (m *PostMutation) SetPublishedAt(t time.Time) {
	m.published_at = &t
}

// PublishedAt returns the value of the "published_at" field in the mutation.
func (m *PostMutation) PublishedAt() (r time.Time, exists bool) {
	v := m.published_at
	if v == nil {
		return
	}
	return *v, true
}

// OldPublishedAt returns the old "published_at" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldPublishedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPublishedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPublishedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPublishedAt: %w", err)
	}
	return oldValue.PublishedAt, nil
}

// ClearPublishedAt clears the value of the "published_at" field.
func (m *PostMutation) ClearPublishedAt() {
	m.published_at = nil
	m.clearedFields[post.FieldPublishedAt] = struct{}{}
}

// PublishedAtCleared returns if the "published_at" field was cleared in this mutation.
func (m *PostMutation) PublishedAtCleared() bool {
	_, ok := m.clearedFields[post.FieldPublishedAt]
	return ok
}

// ResetPublishedAt resets all changes to the "published_at" field.
func (m *PostMutation) ResetPublishedAt() {
	m.published_at = nil
	delete(m.clearedFields, post.FieldPublishedAt)
}

// SetPlatformPosts sets the "platform_posts" field.
func (m *PostMutation) SetPlatformPosts(value map[string]interface{}) {
	m.platform_posts = &value
}

// PlatformPosts returns the value of the "platform_posts" field in the mutation.
func (m *PostMutation) PlatformPosts() (r map[string]interface{}, exists bool) {
	v := m.platform_posts
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformPosts returns the old "platform_posts" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldPlatformPosts(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformPosts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformPosts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformPosts: %w", err)
	}
	return oldValue.PlatformPosts, nil
}

// ClearPlatformPosts clears the value of the "platform_posts" field.
func (m *PostMutation) ClearPlatformPosts() {
	m.platform_posts = nil
	m.clearedFields[post.FieldPlatformPosts] = struct{}{}
}

// PlatformPostsCleared returns if the "platform_posts" field was cleared in this mutation.
func (m *PostMutation) PlatformPostsCleared() bool {
	_, ok := m.clearedFields[post.FieldPlatformPosts]
	return ok
}

// ResetPlatformPosts resets all changes to the "platform_posts" field.
func (m *PostMutation) ResetPlatformPosts() {
	m.platform_posts = nil
	delete(m.clearedFields, post.FieldPlatformPosts)
}

// SetHashtags sets the "hashtags" field.
func (m *PostMutation) SetHashtags(s []string) {
	m.hashtags = &s
	m.appendhashtags = nil
}

// Hashtags returns the value of the "hashtags" field in the mutation.
func (m *PostMutation) Hashtags() (r []string, exists bool) {
	v := m.hashtags
	if v == nil {
		return
	}
	return *v, true
}

// OldHashtags returns the old "hashtags" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldHashtags(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHashtags is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHashtags requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHashtags: %w", err)
	}
	return oldValue.Hashtags, nil
}

// AppendHashtags adds s to the "hashtags" field.
func (m *PostMutation) AppendHashtags(s []string) {
	m.appendhashtags = append(m.appendhashtags, s...)
}

// AppendedHashtags returns the list of values that were appended to the "hashtags" field in this mutation.
func (m *PostMutation) AppendedHashtags() ([]string, bool) {
	if len(m.appendhashtags) == 0 {
		return nil, false
	}
	return m.appendhashtags, true
}

// ClearHashtags clears the value of the "hashtags" field.
func (m *PostMutation) ClearHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	m.clearedFields[post.FieldHashtags] = struct{}{}
}

// HashtagsCleared returns if the "hashtags" field was cleared in this mutation.
func (m *PostMutation) HashtagsCleared() bool {
	_, ok := m.clearedFields[post.FieldHashtags]
	return ok
}

// ResetHashtags resets all changes to the "hashtags" field.
func (m *PostMutation) ResetHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	delete(m.clearedFields, post.FieldHashtags)
}

// SetMentions sets the "mentions" field.
func (m *PostMutation) SetMentions(s []string) {
	m.mentions = &s
	m.appendmentions = nil
}

// Mentions returns the value of the "mentions" field in the mutation.
func (m *PostMutation) Mentions() (r []string, exists bool) {
	v := m.mentions
	if v == nil {
		return
	}
	return *v, true
}

// OldMentions returns the old "mentions" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldMentions(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMentions is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMentions requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMentions: %w", err)
	}
	return oldValue.Mentions, nil
}

// AppendMentions adds s to the "mentions" field.
func (m *PostMutation) AppendMentions(s []string) {
	m.appendmentions = append(m.appendmentions, s...)
}

// AppendedMentions returns the list of values that were appended to the "mentions" field in this mutation.
func (m *PostMutation) AppendedMentions() ([]string, bool) {
	if len(m.appendmentions) == 0 {
		return nil, false
	}
	return m.appendmentions, true
}

// ClearMentions clears the value of the "mentions" field.
func (m *PostMutation) ClearMentions() {
	m.mentions = nil
	m.appendmentions = nil
	m.clearedFields[post.FieldMentions] = struct{}{}
}

// MentionsCleared returns if the "mentions" field was cleared in this mutation.
func (m *PostMutation) MentionsCleared() bool {
	_, ok := m.clearedFields[post.FieldMentions]
	return ok
}

// ResetMentions resets all changes to the "mentions" field.
func (m *PostMutation) ResetMentions() {
	m.mentions = nil
	m.appendmentions = nil
	delete(m.clearedFields, post.FieldMentions)
}

// SetMediaUrls sets the "media_urls" field.
func (m *PostMutation) SetMediaUrls(s []string) {
	m.media_urls = &s
	m.appendmedia_urls = nil
}

// MediaUrls returns the value of the "media_urls" field in the mutation.
func (m *PostMutation) MediaUrls() (r []string, exists bool) {
	v := m.media_urls
	if v == nil {
		return
	}
	return *v, true
}

// OldMediaUrls returns the old "media_urls" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldMediaUrls(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMediaUrls is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMediaUrls requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMediaUrls: %w", err)
	}
	return oldValue.MediaUrls, nil
}

// AppendMediaUrls adds s to the "media_urls" field.
func (m *PostMutation) AppendMediaUrls(s []string) {
	m.appendmedia_urls = append(m.appendmedia_urls, s...)
}

// AppendedMediaUrls returns the list of values that were appended to the "media_urls" field in this mutation.
func (m *PostMutation) AppendedMediaUrls() ([]string, bool) {
	if len(m.appendmedia_urls) == 0 {
		return nil, false
	}
	return m.appendmedia_urls, true
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (m *PostMutation) ClearMediaUrls() {
	m.media_urls = nil
	m.appendmedia_urls = nil
	m.clearedFields[post.FieldMediaUrls] = struct{}{}
}

// MediaUrlsCleared returns if the "media_urls" field was cleared in this mutation.
func (m *PostMutation) MediaUrlsCleared() bool {
	_, ok := m.clearedFields[post.FieldMediaUrls]
	return ok
}

// ResetMediaUrls resets all changes to the "media_urls" field.
func (m *PostMutation) ResetMediaUrls() {
	m.media_urls = nil
	m.appendmedia_urls = nil
	delete(m.clearedFields, post.FieldMediaUrls)
}

// SetVariables sets the "variables" field.
func (m *PostMutation) SetVariables(value map[string]string) {
	m.variables = &value
}

// Variables returns the value of the "variables" field in the mutation.
func (m *PostMutation) Variables() (r map[string]string, exists bool) {
	v := m.variables
	if v == nil {
		return
	}
	return *v, true
}

// OldVariables returns the old "variables" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldVariables(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVariables is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVariables requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVariables: %w", err)
	}
	return oldValue.Variables, nil
}

// ClearVariables clears the value of the "variables" field.
func (m *PostMutation) ClearVariables() {
	m.variables = nil
	m.clearedFields[post.FieldVariables] = struct{}{}
}

// VariablesCleared returns if the "variables" field was cleared in this mutation.
func (m *PostMutation) VariablesCleared() bool {
	_, ok := m.clearedFields[post.FieldVariables]
	return ok
}

// ResetVariables resets all changes to the "variables" field.
func (m *PostMutation) ResetVariables() {
	m.variables = nil
	delete(m.clearedFields, post.FieldVariables)
}

// SetVisibility sets the "visibility" field.
func (m *PostMutation) SetVisibility(po post.Visibility) {
	m.visibility = &po
}

// Visibility returns the value of the "visibility" field in the mutation.
func (m *PostMutation) Visibility() (r post.Visibility, exists bool) {
	v := m.visibility
	if v == nil {
		return
	}
	return *v, true
}

// OldVisibility returns the old "visibility" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldVisibility(ctx context.Context) (v post.Visibility, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVisibility is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVisibility requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVisibility: %w", err)
	}
	return oldValue.Visibility, nil
}

// ResetVisibility resets all changes to the "visibility" field.
func (m *PostMutation) ResetVisibility() {
	m.visibility = nil
}

// SetLanguage sets the "language" field.
func (m *PostMutation) SetLanguage(s string) {
	m.language = &s
}

// Language returns the value of the "language" field in the mutation.
func (m *PostMutation) Language() (r string, exists bool) {
	v := m.language
	if v == nil {
		return
	}
	return *v, true
}

// OldLanguage returns the old "language" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldLanguage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLanguage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLanguage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLanguage: %w", err)
	}
	return oldValue.Language, nil
}

// ResetLanguage resets all changes to the "language" field.
func (m *PostMutation) ResetLanguage() {
	m.language = nil
}

// SetAnalytics sets the "analytics" field.
func (m *PostMutation) SetAnalytics(value map[string]interface{}) {
	m.analytics = &value
}

// Analytics returns the value of the "analytics" field in the mutation.
func (m *PostMutation) Analytics() (r map[string]interface{}, exists bool) {
	v := m.analytics
	if v == nil {
		return
	}
	return *v, true
}

// OldAnalytics returns the old "analytics" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldAnalytics(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAnalytics is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAnalytics requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAnalytics: %w", err)
	}
	return oldValue.Analytics, nil
}

// ClearAnalytics clears the value of the "analytics" field.
func (m *PostMutation) ClearAnalytics() {
	m.analytics = nil
	m.clearedFields[post.FieldAnalytics] = struct{}{}
}

// AnalyticsCleared returns if the "analytics" field was cleared in this mutation.
func (m *PostMutation) AnalyticsCleared() bool {
	_, ok := m.clearedFields[post.FieldAnalytics]
	return ok
}

// ResetAnalytics resets all changes to the "analytics" field.
func (m *PostMutation) ResetAnalytics() {
	m.analytics = nil
	delete(m.clearedFields, post.FieldAnalytics)
}

// SetMetadata sets the "metadata" field.
func (m *PostMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *PostMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *PostMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[post.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *PostMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[post.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *PostMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, post.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *PostMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PostMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PostMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PostMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PostMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PostMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *PostMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *PostMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Post entity.
// If the Post object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PostMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *PostMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[post.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *PostMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[post.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *PostMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, post.FieldDeletedAt)
}

// Where appends a list predicates to the PostMutation builder.
func (m *PostMutation) Where(ps ...predicate.Post) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PostMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PostMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Post, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PostMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PostMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Post).
func (m *PostMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PostMutation) Fields() []string {
	fields := make([]string, 0, 23)
	if m.user_id != nil {
		fields = append(fields, post.FieldUserID)
	}
	if m.workspace_id != nil {
		fields = append(fields, post.FieldWorkspaceID)
	}
	if m.template_id != nil {
		fields = append(fields, post.FieldTemplateID)
	}
	if m.generation_id != nil {
		fields = append(fields, post.FieldGenerationID)
	}
	if m.title != nil {
		fields = append(fields, post.FieldTitle)
	}
	if m.content != nil {
		fields = append(fields, post.FieldContent)
	}
	if m.original_content != nil {
		fields = append(fields, post.FieldOriginalContent)
	}
	if m.platforms != nil {
		fields = append(fields, post.FieldPlatforms)
	}
	if m.status != nil {
		fields = append(fields, post.FieldStatus)
	}
	if m.scheduled_at != nil {
		fields = append(fields, post.FieldScheduledAt)
	}
	if m.published_at != nil {
		fields = append(fields, post.FieldPublishedAt)
	}
	if m.platform_posts != nil {
		fields = append(fields, post.FieldPlatformPosts)
	}
	if m.hashtags != nil {
		fields = append(fields, post.FieldHashtags)
	}
	if m.mentions != nil {
		fields = append(fields, post.FieldMentions)
	}
	if m.media_urls != nil {
		fields = append(fields, post.FieldMediaUrls)
	}
	if m.variables != nil {
		fields = append(fields, post.FieldVariables)
	}
	if m.visibility != nil {
		fields = append(fields, post.FieldVisibility)
	}
	if m.language != nil {
		fields = append(fields, post.FieldLanguage)
	}
	if m.analytics != nil {
		fields = append(fields, post.FieldAnalytics)
	}
	if m.metadata != nil {
		fields = append(fields, post.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, post.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, post.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, post.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PostMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case post.FieldUserID:
		return m.UserID()
	case post.FieldWorkspaceID:
		return m.WorkspaceID()
	case post.FieldTemplateID:
		return m.TemplateID()
	case post.FieldGenerationID:
		return m.GenerationID()
	case post.FieldTitle:
		return m.Title()
	case post.FieldContent:
		return m.Content()
	case post.FieldOriginalContent:
		return m.OriginalContent()
	case post.FieldPlatforms:
		return m.Platforms()
	case post.FieldStatus:
		return m.Status()
	case post.FieldScheduledAt:
		return m.ScheduledAt()
	case post.FieldPublishedAt:
		return m.PublishedAt()
	case post.FieldPlatformPosts:
		return m.PlatformPosts()
	case post.FieldHashtags:
		return m.Hashtags()
	case post.FieldMentions:
		return m.Mentions()
	case post.FieldMediaUrls:
		return m.MediaUrls()
	case post.FieldVariables:
		return m.Variables()
	case post.FieldVisibility:
		return m.Visibility()
	case post.FieldLanguage:
		return m.Language()
	case post.FieldAnalytics:
		return m.Analytics()
	case post.FieldMetadata:
		return m.Metadata()
	case post.FieldCreatedAt:
		return m.CreatedAt()
	case post.FieldUpdatedAt:
		return m.UpdatedAt()
	case post.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PostMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case post.FieldUserID:
		return m.OldUserID(ctx)
	case post.FieldWorkspaceID:
		return m.OldWorkspaceID(ctx)
	case post.FieldTemplateID:
		return m.OldTemplateID(ctx)
	case post.FieldGenerationID:
		return m.OldGenerationID(ctx)
	case post.FieldTitle:
		return m.OldTitle(ctx)
	case post.FieldContent:
		return m.OldContent(ctx)
	case post.FieldOriginalContent:
		return m.OldOriginalContent(ctx)
	case post.FieldPlatforms:
		return m.OldPlatforms(ctx)
	case post.FieldStatus:
		return m.OldStatus(ctx)
	case post.FieldScheduledAt:
		return m.OldScheduledAt(ctx)
	case post.FieldPublishedAt:
		return m.OldPublishedAt(ctx)
	case post.FieldPlatformPosts:
		return m.OldPlatformPosts(ctx)
	case post.FieldHashtags:
		return m.OldHashtags(ctx)
	case post.FieldMentions:
		return m.OldMentions(ctx)
	case post.FieldMediaUrls:
		return m.OldMediaUrls(ctx)
	case post.FieldVariables:
		return m.OldVariables(ctx)
	case post.FieldVisibility:
		return m.OldVisibility(ctx)
	case post.FieldLanguage:
		return m.OldLanguage(ctx)
	case post.FieldAnalytics:
		return m.OldAnalytics(ctx)
	case post.FieldMetadata:
		return m.OldMetadata(ctx)
	case post.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case post.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case post.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Post field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PostMutation) SetField(name string, value ent.Value) error {
	switch name {
	case post.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case post.FieldWorkspaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkspaceID(v)
		return nil
	case post.FieldTemplateID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTemplateID(v)
		return nil
	case post.FieldGenerationID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGenerationID(v)
		return nil
	case post.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case post.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case post.FieldOriginalContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOriginalContent(v)
		return nil
	case post.FieldPlatforms:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatforms(v)
		return nil
	case post.FieldStatus:
		v, ok := value.(post.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case post.FieldScheduledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetScheduledAt(v)
		return nil
	case post.FieldPublishedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPublishedAt(v)
		return nil
	case post.FieldPlatformPosts:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformPosts(v)
		return nil
	case post.FieldHashtags:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHashtags(v)
		return nil
	case post.FieldMentions:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMentions(v)
		return nil
	case post.FieldMediaUrls:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMediaUrls(v)
		return nil
	case post.FieldVariables:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVariables(v)
		return nil
	case post.FieldVisibility:
		v, ok := value.(post.Visibility)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVisibility(v)
		return nil
	case post.FieldLanguage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLanguage(v)
		return nil
	case post.FieldAnalytics:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAnalytics(v)
		return nil
	case post.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case post.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case post.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case post.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Post field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PostMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PostMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PostMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Post numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PostMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(post.FieldWorkspaceID) {
		fields = append(fields, post.FieldWorkspaceID)
	}
	if m.FieldCleared(post.FieldTemplateID) {
		fields = append(fields, post.FieldTemplateID)
	}
	if m.FieldCleared(post.FieldGenerationID) {
		fields = append(fields, post.FieldGenerationID)
	}
	if m.FieldCleared(post.FieldOriginalContent) {
		fields = append(fields, post.FieldOriginalContent)
	}
	if m.FieldCleared(post.FieldPlatforms) {
		fields = append(fields, post.FieldPlatforms)
	}
	if m.FieldCleared(post.FieldScheduledAt) {
		fields = append(fields, post.FieldScheduledAt)
	}
	if m.FieldCleared(post.FieldPublishedAt) {
		fields = append(fields, post.FieldPublishedAt)
	}
	if m.FieldCleared(post.FieldPlatformPosts) {
		fields = append(fields, post.FieldPlatformPosts)
	}
	if m.FieldCleared(post.FieldHashtags) {
		fields = append(fields, post.FieldHashtags)
	}
	if m.FieldCleared(post.FieldMentions) {
		fields = append(fields, post.FieldMentions)
	}
	if m.FieldCleared(post.FieldMediaUrls) {
		fields = append(fields, post.FieldMediaUrls)
	}
	if m.FieldCleared(post.FieldVariables) {
		fields = append(fields, post.FieldVariables)
	}
	if m.FieldCleared(post.FieldAnalytics) {
		fields = append(fields, post.FieldAnalytics)
	}
	if m.FieldCleared(post.FieldMetadata) {
		fields = append(fields, post.FieldMetadata)
	}
	if m.FieldCleared(post.FieldDeletedAt) {
		fields = append(fields, post.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PostMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PostMutation) ClearField(name string) error {
	switch name {
	case post.FieldWorkspaceID:
		m.ClearWorkspaceID()
		return nil
	case post.FieldTemplateID:
		m.ClearTemplateID()
		return nil
	case post.FieldGenerationID:
		m.ClearGenerationID()
		return nil
	case post.FieldOriginalContent:
		m.ClearOriginalContent()
		return nil
	case post.FieldPlatforms:
		m.ClearPlatforms()
		return nil
	case post.FieldScheduledAt:
		m.ClearScheduledAt()
		return nil
	case post.FieldPublishedAt:
		m.ClearPublishedAt()
		return nil
	case post.FieldPlatformPosts:
		m.ClearPlatformPosts()
		return nil
	case post.FieldHashtags:
		m.ClearHashtags()
		return nil
	case post.FieldMentions:
		m.ClearMentions()
		return nil
	case post.FieldMediaUrls:
		m.ClearMediaUrls()
		return nil
	case post.FieldVariables:
		m.ClearVariables()
		return nil
	case post.FieldAnalytics:
		m.ClearAnalytics()
		return nil
	case post.FieldMetadata:
		m.ClearMetadata()
		return nil
	case post.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Post nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PostMutation) ResetField(name string) error {
	switch name {
	case post.FieldUserID:
		m.ResetUserID()
		return nil
	case post.FieldWorkspaceID:
		m.ResetWorkspaceID()
		return nil
	case post.FieldTemplateID:
		m.ResetTemplateID()
		return nil
	case post.FieldGenerationID:
		m.ResetGenerationID()
		return nil
	case post.FieldTitle:
		m.ResetTitle()
		return nil
	case post.FieldContent:
		m.ResetContent()
		return nil
	case post.FieldOriginalContent:
		m.ResetOriginalContent()
		return nil
	case post.FieldPlatforms:
		m.ResetPlatforms()
		return nil
	case post.FieldStatus:
		m.ResetStatus()
		return nil
	case post.FieldScheduledAt:
		m.ResetScheduledAt()
		return nil
	case post.FieldPublishedAt:
		m.ResetPublishedAt()
		return nil
	case post.FieldPlatformPosts:
		m.ResetPlatformPosts()
		return nil
	case post.FieldHashtags:
		m.ResetHashtags()
		return nil
	case post.FieldMentions:
		m.ResetMentions()
		return nil
	case post.FieldMediaUrls:
		m.ResetMediaUrls()
		return nil
	case post.FieldVariables:
		m.ResetVariables()
		return nil
	case post.FieldVisibility:
		m.ResetVisibility()
		return nil
	case post.FieldLanguage:
		m.ResetLanguage()
		return nil
	case post.FieldAnalytics:
		m.ResetAnalytics()
		return nil
	case post.FieldMetadata:
		m.ResetMetadata()
		return nil
	case post.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case post.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case post.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Post field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PostMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PostMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PostMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PostMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PostMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PostMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PostMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Post unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PostMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Post edge %s", name)
}

// TemplateMutation represents an operation that mutates the Template nodes in the graph.
type TemplateMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	user_id               *uuid.UUID
	workspace_id          *string
	name                  *string
	description           *string
	content               *string
	variables             *[]string
	appendvariables       []string
	variable_descriptions *map[string]string
	category              *template.Category
	platforms             *[]string
	appendplatforms       []string
	tone                  *template.Tone
	content_type          *template.ContentType
	hashtags              *[]string
	appendhashtags        []string
	is_public             *bool
	is_featured           *bool
	usage_count           *int
	addusage_count        *int
	rating                *float64
	addrating             *float64
	rating_count          *int
	addrating_count       *int
	tags                  *[]string
	appendtags            []string
	language              *string
	training_files        *[]string
	appendtraining_files  []string
	rag_trained           *bool
	rag_trained_at        *time.Time
	metadata              *map[string]interface{}
	created_at            *time.Time
	updated_at            *time.Time
	deleted_at            *time.Time
	clearedFields         map[string]struct{}
	done                  bool
	oldValue              func(context.Context) (*Template, error)
	predicates            []predicate.Template
}

var _ ent.Mutation = (*TemplateMutation)(nil)

// templateOption allows management of the mutation configuration using functional options.
type templateOption func(*TemplateMutation)

// newTemplateMutation creates new mutation for the Template entity.
func newTemplateMutation(c config, op Op, opts ...templateOption) *TemplateMutation {
	m := &TemplateMutation{
		config:        c,
		op:            op,
		typ:           TypeTemplate,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTemplateID sets the ID field of the mutation.
func withTemplateID(id uuid.UUID) templateOption {
	return func(m *TemplateMutation) {
		var (
			err   error
			once  sync.Once
			value *Template
		)
		m.oldValue = func(ctx context.Context) (*Template, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Template.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTemplate sets the old Template of the mutation.
func withTemplate(node *Template) templateOption {
	return func(m *TemplateMutation) {
		m.oldValue = func(context.Context) (*Template, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TemplateMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TemplateMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Template entities.
func (m *TemplateMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TemplateMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TemplateMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Template.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *TemplateMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *TemplateMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *TemplateMutation) ResetUserID() {
	m.user_id = nil
}

// SetWorkspaceID sets the "workspace_id" field.
func (m *TemplateMutation) SetWorkspaceID(s string) {
	m.workspace_id = &s
}

// WorkspaceID returns the value of the "workspace_id" field in the mutation.
func (m *TemplateMutation) WorkspaceID() (r string, exists bool) {
	v := m.workspace_id
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkspaceID returns the old "workspace_id" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldWorkspaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkspaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkspaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkspaceID: %w", err)
	}
	return oldValue.WorkspaceID, nil
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (m *TemplateMutation) ClearWorkspaceID() {
	m.workspace_id = nil
	m.clearedFields[template.FieldWorkspaceID] = struct{}{}
}

// WorkspaceIDCleared returns if the "workspace_id" field was cleared in this mutation.
func (m *TemplateMutation) WorkspaceIDCleared() bool {
	_, ok := m.clearedFields[template.FieldWorkspaceID]
	return ok
}

// ResetWorkspaceID resets all changes to the "workspace_id" field.
func (m *TemplateMutation) ResetWorkspaceID() {
	m.workspace_id = nil
	delete(m.clearedFields, template.FieldWorkspaceID)
}

// SetName sets the "name" field.
func (m *TemplateMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *TemplateMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *TemplateMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *TemplateMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *TemplateMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *TemplateMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[template.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *TemplateMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[template.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *TemplateMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, template.FieldDescription)
}

// SetContent sets the "content" field.
func (m *TemplateMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *TemplateMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *TemplateMutation) ResetContent() {
	m.content = nil
}

// SetVariables sets the "variables" field.
func (m *TemplateMutation) SetVariables(s []string) {
	m.variables = &s
	m.appendvariables = nil
}

// Variables returns the value of the "variables" field in the mutation.
func (m *TemplateMutation) Variables() (r []string, exists bool) {
	v := m.variables
	if v == nil {
		return
	}
	return *v, true
}

// OldVariables returns the old "variables" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldVariables(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVariables is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVariables requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVariables: %w", err)
	}
	return oldValue.Variables, nil
}

// AppendVariables adds s to the "variables" field.
func (m *TemplateMutation) AppendVariables(s []string) {
	m.appendvariables = append(m.appendvariables, s...)
}

// AppendedVariables returns the list of values that were appended to the "variables" field in this mutation.
func (m *TemplateMutation) AppendedVariables() ([]string, bool) {
	if len(m.appendvariables) == 0 {
		return nil, false
	}
	return m.appendvariables, true
}

// ClearVariables clears the value of the "variables" field.
func (m *TemplateMutation) ClearVariables() {
	m.variables = nil
	m.appendvariables = nil
	m.clearedFields[template.FieldVariables] = struct{}{}
}

// VariablesCleared returns if the "variables" field was cleared in this mutation.
func (m *TemplateMutation) VariablesCleared() bool {
	_, ok := m.clearedFields[template.FieldVariables]
	return ok
}

// ResetVariables resets all changes to the "variables" field.
func (m *TemplateMutation) ResetVariables() {
	m.variables = nil
	m.appendvariables = nil
	delete(m.clearedFields, template.FieldVariables)
}

// SetVariableDescriptions sets the "variable_descriptions" field.
func (m *TemplateMutation) SetVariableDescriptions(value map[string]string) {
	m.variable_descriptions = &value
}

// VariableDescriptions returns the value of the "variable_descriptions" field in the mutation.
func (m *TemplateMutation) VariableDescriptions() (r map[string]string, exists bool) {
	v := m.variable_descriptions
	if v == nil {
		return
	}
	return *v, true
}

// OldVariableDescriptions returns the old "variable_descriptions" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldVariableDescriptions(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVariableDescriptions is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVariableDescriptions requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVariableDescriptions: %w", err)
	}
	return oldValue.VariableDescriptions, nil
}

// ClearVariableDescriptions clears the value of the "variable_descriptions" field.
func (m *TemplateMutation) ClearVariableDescriptions() {
	m.variable_descriptions = nil
	m.clearedFields[template.FieldVariableDescriptions] = struct{}{}
}

// VariableDescriptionsCleared returns if the "variable_descriptions" field was cleared in this mutation.
func (m *TemplateMutation) VariableDescriptionsCleared() bool {
	_, ok := m.clearedFields[template.FieldVariableDescriptions]
	return ok
}

// ResetVariableDescriptions resets all changes to the "variable_descriptions" field.
func (m *TemplateMutation) ResetVariableDescriptions() {
	m.variable_descriptions = nil
	delete(m.clearedFields, template.FieldVariableDescriptions)
}

// SetCategory sets the "category" field.
func (m *TemplateMutation) SetCategory(t template.Category) {
	m.category = &t
}

// Category returns the value of the "category" field in the mutation.
func (m *TemplateMutation) Category() (r template.Category, exists bool) {
	v := m.category
	if v == nil {
		return
	}
	return *v, true
}

// OldCategory returns the old "category" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldCategory(ctx context.Context) (v template.Category, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCategory is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCategory requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCategory: %w", err)
	}
	return oldValue.Category, nil
}

// ResetCategory resets all changes to the "category" field.
func (m *TemplateMutation) ResetCategory() {
	m.category = nil
}

// SetPlatforms sets the "platforms" field.
func (m *TemplateMutation) SetPlatforms(s []string) {
	m.platforms = &s
	m.appendplatforms = nil
}

// Platforms returns the value of the "platforms" field in the mutation.
func (m *TemplateMutation) Platforms() (r []string, exists bool) {
	v := m.platforms
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatforms returns the old "platforms" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldPlatforms(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatforms is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatforms requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatforms: %w", err)
	}
	return oldValue.Platforms, nil
}

// AppendPlatforms adds s to the "platforms" field.
func (m *TemplateMutation) AppendPlatforms(s []string) {
	m.appendplatforms = append(m.appendplatforms, s...)
}

// AppendedPlatforms returns the list of values that were appended to the "platforms" field in this mutation.
func (m *TemplateMutation) AppendedPlatforms() ([]string, bool) {
	if len(m.appendplatforms) == 0 {
		return nil, false
	}
	return m.appendplatforms, true
}

// ClearPlatforms clears the value of the "platforms" field.
func (m *TemplateMutation) ClearPlatforms() {
	m.platforms = nil
	m.appendplatforms = nil
	m.clearedFields[template.FieldPlatforms] = struct{}{}
}

// PlatformsCleared returns if the "platforms" field was cleared in this mutation.
func (m *TemplateMutation) PlatformsCleared() bool {
	_, ok := m.clearedFields[template.FieldPlatforms]
	return ok
}

// ResetPlatforms resets all changes to the "platforms" field.
func (m *TemplateMutation) ResetPlatforms() {
	m.platforms = nil
	m.appendplatforms = nil
	delete(m.clearedFields, template.FieldPlatforms)
}

// SetTone sets the "tone" field.
func (m *TemplateMutation) SetTone(t template.Tone) {
	m.tone = &t
}

// Tone returns the value of the "tone" field in the mutation.
func (m *TemplateMutation) Tone() (r template.Tone, exists bool) {
	v := m.tone
	if v == nil {
		return
	}
	return *v, true
}

// OldTone returns the old "tone" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldTone(ctx context.Context) (v template.Tone, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTone: %w", err)
	}
	return oldValue.Tone, nil
}

// ResetTone resets all changes to the "tone" field.
func (m *TemplateMutation) ResetTone() {
	m.tone = nil
}

// SetContentType sets the "content_type" field.
func (m *TemplateMutation) SetContentType(tt template.ContentType) {
	m.content_type = &tt
}

// ContentType returns the value of the "content_type" field in the mutation.
func (m *TemplateMutation) ContentType() (r template.ContentType, exists bool) {
	v := m.content_type
	if v == nil {
		return
	}
	return *v, true
}

// OldContentType returns the old "content_type" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldContentType(ctx context.Context) (v template.ContentType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContentType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContentType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContentType: %w", err)
	}
	return oldValue.ContentType, nil
}

// ResetContentType resets all changes to the "content_type" field.
func (m *TemplateMutation) ResetContentType() {
	m.content_type = nil
}

// SetHashtags sets the "hashtags" field.
func (m *TemplateMutation) SetHashtags(s []string) {
	m.hashtags = &s
	m.appendhashtags = nil
}

// Hashtags returns the value of the "hashtags" field in the mutation.
func (m *TemplateMutation) Hashtags() (r []string, exists bool) {
	v := m.hashtags
	if v == nil {
		return
	}
	return *v, true
}

// OldHashtags returns the old "hashtags" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldHashtags(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHashtags is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHashtags requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHashtags: %w", err)
	}
	return oldValue.Hashtags, nil
}

// AppendHashtags adds s to the "hashtags" field.
func (m *TemplateMutation) AppendHashtags(s []string) {
	m.appendhashtags = append(m.appendhashtags, s...)
}

// AppendedHashtags returns the list of values that were appended to the "hashtags" field in this mutation.
func (m *TemplateMutation) AppendedHashtags() ([]string, bool) {
	if len(m.appendhashtags) == 0 {
		return nil, false
	}
	return m.appendhashtags, true
}

// ClearHashtags clears the value of the "hashtags" field.
func (m *TemplateMutation) ClearHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	m.clearedFields[template.FieldHashtags] = struct{}{}
}

// HashtagsCleared returns if the "hashtags" field was cleared in this mutation.
func (m *TemplateMutation) HashtagsCleared() bool {
	_, ok := m.clearedFields[template.FieldHashtags]
	return ok
}

// ResetHashtags resets all changes to the "hashtags" field.
func (m *TemplateMutation) ResetHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	delete(m.clearedFields, template.FieldHashtags)
}

// SetIsPublic sets the "is_public" field.
func (m *TemplateMutation) SetIsPublic(b bool) {
	m.is_public = &b
}

// IsPublic returns the value of the "is_public" field in the mutation.
func (m *TemplateMutation) IsPublic() (r bool, exists bool) {
	v := m.is_public
	if v == nil {
		return
	}
	return *v, true
}

// OldIsPublic returns the old "is_public" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldIsPublic(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsPublic is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsPublic requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsPublic: %w", err)
	}
	return oldValue.IsPublic, nil
}

// ResetIsPublic resets all changes to the "is_public" field.
func (m *TemplateMutation) ResetIsPublic() {
	m.is_public = nil
}

// SetIsFeatured sets the "is_featured" field.
func (m *TemplateMutation) SetIsFeatured(b bool) {
	m.is_featured = &b
}

// IsFeatured returns the value of the "is_featured" field in the mutation.
func (m *TemplateMutation) IsFeatured() (r bool, exists bool) {
	v := m.is_featured
	if v == nil {
		return
	}
	return *v, true
}

// OldIsFeatured returns the old "is_featured" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldIsFeatured(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsFeatured is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsFeatured requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsFeatured: %w", err)
	}
	return oldValue.IsFeatured, nil
}

// ResetIsFeatured resets all changes to the "is_featured" field.
func (m *TemplateMutation) ResetIsFeatured() {
	m.is_featured = nil
}

// SetUsageCount sets the "usage_count" field.
func (m *TemplateMutation) SetUsageCount(i int) {
	m.usage_count = &i
	m.addusage_count = nil
}

// UsageCount returns the value of the "usage_count" field in the mutation.
func (m *TemplateMutation) UsageCount() (r int, exists bool) {
	v := m.usage_count
	if v == nil {
		return
	}
	return *v, true
}

// OldUsageCount returns the old "usage_count" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldUsageCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsageCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsageCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsageCount: %w", err)
	}
	return oldValue.UsageCount, nil
}

// AddUsageCount adds i to the "usage_count" field.
func (m *TemplateMutation) AddUsageCount(i int) {
	if m.addusage_count != nil {
		*m.addusage_count += i
	} else {
		m.addusage_count = &i
	}
}

// AddedUsageCount returns the value that was added to the "usage_count" field in this mutation.
func (m *TemplateMutation) AddedUsageCount() (r int, exists bool) {
	v := m.addusage_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetUsageCount resets all changes to the "usage_count" field.
func (m *TemplateMutation) ResetUsageCount() {
	m.usage_count = nil
	m.addusage_count = nil
}

// SetRating sets the "rating" field.
func (m *TemplateMutation) SetRating(f float64) {
	m.rating = &f
	m.addrating = nil
}

// Rating returns the value of the "rating" field in the mutation.
func (m *TemplateMutation) Rating() (r float64, exists bool) {
	v := m.rating
	if v == nil {
		return
	}
	return *v, true
}

// OldRating returns the old "rating" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldRating(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRating is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRating requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRating: %w", err)
	}
	return oldValue.Rating, nil
}

// AddRating adds f to the "rating" field.
func (m *TemplateMutation) AddRating(f float64) {
	if m.addrating != nil {
		*m.addrating += f
	} else {
		m.addrating = &f
	}
}

// AddedRating returns the value that was added to the "rating" field in this mutation.
func (m *TemplateMutation) AddedRating() (r float64, exists bool) {
	v := m.addrating
	if v == nil {
		return
	}
	return *v, true
}

// ResetRating resets all changes to the "rating" field.
func (m *TemplateMutation) ResetRating() {
	m.rating = nil
	m.addrating = nil
}

// SetRatingCount sets the "rating_count" field.
func (m *TemplateMutation) SetRatingCount(i int) {
	m.rating_count = &i
	m.addrating_count = nil
}

// RatingCount returns the value of the "rating_count" field in the mutation.
func (m *TemplateMutation) RatingCount() (r int, exists bool) {
	v := m.rating_count
	if v == nil {
		return
	}
	return *v, true
}

// OldRatingCount returns the old "rating_count" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldRatingCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRatingCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRatingCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRatingCount: %w", err)
	}
	return oldValue.RatingCount, nil
}

// AddRatingCount adds i to the "rating_count" field.
func (m *TemplateMutation) AddRatingCount(i int) {
	if m.addrating_count != nil {
		*m.addrating_count += i
	} else {
		m.addrating_count = &i
	}
}

// AddedRatingCount returns the value that was added to the "rating_count" field in this mutation.
func (m *TemplateMutation) AddedRatingCount() (r int, exists bool) {
	v := m.addrating_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetRatingCount resets all changes to the "rating_count" field.
func (m *TemplateMutation) ResetRatingCount() {
	m.rating_count = nil
	m.addrating_count = nil
}

// SetTags sets the "tags" field.
func (m *TemplateMutation) SetTags(s []string) {
	m.tags = &s
	m.appendtags = nil
}

// Tags returns the value of the "tags" field in the mutation.
func (m *TemplateMutation) Tags() (r []string, exists bool) {
	v := m.tags
	if v == nil {
		return
	}
	return *v, true
}

// OldTags returns the old "tags" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldTags(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTags is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTags requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTags: %w", err)
	}
	return oldValue.Tags, nil
}

// AppendTags adds s to the "tags" field.
func (m *TemplateMutation) AppendTags(s []string) {
	m.appendtags = append(m.appendtags, s...)
}

// AppendedTags returns the list of values that were appended to the "tags" field in this mutation.
func (m *TemplateMutation) AppendedTags() ([]string, bool) {
	if len(m.appendtags) == 0 {
		return nil, false
	}
	return m.appendtags, true
}

// ClearTags clears the value of the "tags" field.
func (m *TemplateMutation) ClearTags() {
	m.tags = nil
	m.appendtags = nil
	m.clearedFields[template.FieldTags] = struct{}{}
}

// TagsCleared returns if the "tags" field was cleared in this mutation.
func (m *TemplateMutation) TagsCleared() bool {
	_, ok := m.clearedFields[template.FieldTags]
	return ok
}

// ResetTags resets all changes to the "tags" field.
func (m *TemplateMutation) ResetTags() {
	m.tags = nil
	m.appendtags = nil
	delete(m.clearedFields, template.FieldTags)
}

// SetLanguage sets the "language" field.
func (m *TemplateMutation) SetLanguage(s string) {
	m.language = &s
}

// Language returns the value of the "language" field in the mutation.
func (m *TemplateMutation) Language() (r string, exists bool) {
	v := m.language
	if v == nil {
		return
	}
	return *v, true
}

// OldLanguage returns the old "language" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldLanguage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLanguage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLanguage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLanguage: %w", err)
	}
	return oldValue.Language, nil
}

// ResetLanguage resets all changes to the "language" field.
func (m *TemplateMutation) ResetLanguage() {
	m.language = nil
}

// SetTrainingFiles sets the "training_files" field.
func (m *TemplateMutation) SetTrainingFiles(s []string) {
	m.training_files = &s
	m.appendtraining_files = nil
}

// TrainingFiles returns the value of the "training_files" field in the mutation.
func (m *TemplateMutation) TrainingFiles() (r []string, exists bool) {
	v := m.training_files
	if v == nil {
		return
	}
	return *v, true
}

// OldTrainingFiles returns the old "training_files" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldTrainingFiles(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTrainingFiles is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTrainingFiles requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTrainingFiles: %w", err)
	}
	return oldValue.TrainingFiles, nil
}

// AppendTrainingFiles adds s to the "training_files" field.
func (m *TemplateMutation) AppendTrainingFiles(s []string) {
	m.appendtraining_files = append(m.appendtraining_files, s...)
}

// AppendedTrainingFiles returns the list of values that were appended to the "training_files" field in this mutation.
func (m *TemplateMutation) AppendedTrainingFiles() ([]string, bool) {
	if len(m.appendtraining_files) == 0 {
		return nil, false
	}
	return m.appendtraining_files, true
}

// ClearTrainingFiles clears the value of the "training_files" field.
func (m *TemplateMutation) ClearTrainingFiles() {
	m.training_files = nil
	m.appendtraining_files = nil
	m.clearedFields[template.FieldTrainingFiles] = struct{}{}
}

// TrainingFilesCleared returns if the "training_files" field was cleared in this mutation.
func (m *TemplateMutation) TrainingFilesCleared() bool {
	_, ok := m.clearedFields[template.FieldTrainingFiles]
	return ok
}

// ResetTrainingFiles resets all changes to the "training_files" field.
func (m *TemplateMutation) ResetTrainingFiles() {
	m.training_files = nil
	m.appendtraining_files = nil
	delete(m.clearedFields, template.FieldTrainingFiles)
}

// SetRagTrained sets the "rag_trained" field.
func (m *TemplateMutation) SetRagTrained(b bool) {
	m.rag_trained = &b
}

// RagTrained returns the value of the "rag_trained" field in the mutation.
func (m *TemplateMutation) RagTrained() (r bool, exists bool) {
	v := m.rag_trained
	if v == nil {
		return
	}
	return *v, true
}

// OldRagTrained returns the old "rag_trained" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldRagTrained(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRagTrained is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRagTrained requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRagTrained: %w", err)
	}
	return oldValue.RagTrained, nil
}

// ResetRagTrained resets all changes to the "rag_trained" field.
func (m *TemplateMutation) ResetRagTrained() {
	m.rag_trained = nil
}

// SetRagTrainedAt sets the "rag_trained_at" field.
func (m *TemplateMutation) SetRagTrainedAt(t time.Time) {
	m.rag_trained_at = &t
}

// RagTrainedAt returns the value of the "rag_trained_at" field in the mutation.
func (m *TemplateMutation) RagTrainedAt() (r time.Time, exists bool) {
	v := m.rag_trained_at
	if v == nil {
		return
	}
	return *v, true
}

// OldRagTrainedAt returns the old "rag_trained_at" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldRagTrainedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRagTrainedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRagTrainedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRagTrainedAt: %w", err)
	}
	return oldValue.RagTrainedAt, nil
}

// ClearRagTrainedAt clears the value of the "rag_trained_at" field.
func (m *TemplateMutation) ClearRagTrainedAt() {
	m.rag_trained_at = nil
	m.clearedFields[template.FieldRagTrainedAt] = struct{}{}
}

// RagTrainedAtCleared returns if the "rag_trained_at" field was cleared in this mutation.
func (m *TemplateMutation) RagTrainedAtCleared() bool {
	_, ok := m.clearedFields[template.FieldRagTrainedAt]
	return ok
}

// ResetRagTrainedAt resets all changes to the "rag_trained_at" field.
func (m *TemplateMutation) ResetRagTrainedAt() {
	m.rag_trained_at = nil
	delete(m.clearedFields, template.FieldRagTrainedAt)
}

// SetMetadata sets the "metadata" field.
func (m *TemplateMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *TemplateMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *TemplateMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[template.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *TemplateMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[template.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *TemplateMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, template.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *TemplateMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *TemplateMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *TemplateMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *TemplateMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *TemplateMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *TemplateMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *TemplateMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *TemplateMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Template entity.
// If the Template object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TemplateMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *TemplateMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[template.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *TemplateMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[template.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *TemplateMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, template.FieldDeletedAt)
}

// Where appends a list predicates to the TemplateMutation builder.
func (m *TemplateMutation) Where(ps ...predicate.Template) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TemplateMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TemplateMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Template, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TemplateMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TemplateMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Template).
func (m *TemplateMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TemplateMutation) Fields() []string {
	fields := make([]string, 0, 26)
	if m.user_id != nil {
		fields = append(fields, template.FieldUserID)
	}
	if m.workspace_id != nil {
		fields = append(fields, template.FieldWorkspaceID)
	}
	if m.name != nil {
		fields = append(fields, template.FieldName)
	}
	if m.description != nil {
		fields = append(fields, template.FieldDescription)
	}
	if m.content != nil {
		fields = append(fields, template.FieldContent)
	}
	if m.variables != nil {
		fields = append(fields, template.FieldVariables)
	}
	if m.variable_descriptions != nil {
		fields = append(fields, template.FieldVariableDescriptions)
	}
	if m.category != nil {
		fields = append(fields, template.FieldCategory)
	}
	if m.platforms != nil {
		fields = append(fields, template.FieldPlatforms)
	}
	if m.tone != nil {
		fields = append(fields, template.FieldTone)
	}
	if m.content_type != nil {
		fields = append(fields, template.FieldContentType)
	}
	if m.hashtags != nil {
		fields = append(fields, template.FieldHashtags)
	}
	if m.is_public != nil {
		fields = append(fields, template.FieldIsPublic)
	}
	if m.is_featured != nil {
		fields = append(fields, template.FieldIsFeatured)
	}
	if m.usage_count != nil {
		fields = append(fields, template.FieldUsageCount)
	}
	if m.rating != nil {
		fields = append(fields, template.FieldRating)
	}
	if m.rating_count != nil {
		fields = append(fields, template.FieldRatingCount)
	}
	if m.tags != nil {
		fields = append(fields, template.FieldTags)
	}
	if m.language != nil {
		fields = append(fields, template.FieldLanguage)
	}
	if m.training_files != nil {
		fields = append(fields, template.FieldTrainingFiles)
	}
	if m.rag_trained != nil {
		fields = append(fields, template.FieldRagTrained)
	}
	if m.rag_trained_at != nil {
		fields = append(fields, template.FieldRagTrainedAt)
	}
	if m.metadata != nil {
		fields = append(fields, template.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, template.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, template.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, template.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TemplateMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case template.FieldUserID:
		return m.UserID()
	case template.FieldWorkspaceID:
		return m.WorkspaceID()
	case template.FieldName:
		return m.Name()
	case template.FieldDescription:
		return m.Description()
	case template.FieldContent:
		return m.Content()
	case template.FieldVariables:
		return m.Variables()
	case template.FieldVariableDescriptions:
		return m.VariableDescriptions()
	case template.FieldCategory:
		return m.Category()
	case template.FieldPlatforms:
		return m.Platforms()
	case template.FieldTone:
		return m.Tone()
	case template.FieldContentType:
		return m.ContentType()
	case template.FieldHashtags:
		return m.Hashtags()
	case template.FieldIsPublic:
		return m.IsPublic()
	case template.FieldIsFeatured:
		return m.IsFeatured()
	case template.FieldUsageCount:
		return m.UsageCount()
	case template.FieldRating:
		return m.Rating()
	case template.FieldRatingCount:
		return m.RatingCount()
	case template.FieldTags:
		return m.Tags()
	case template.FieldLanguage:
		return m.Language()
	case template.FieldTrainingFiles:
		return m.TrainingFiles()
	case template.FieldRagTrained:
		return m.RagTrained()
	case template.FieldRagTrainedAt:
		return m.RagTrainedAt()
	case template.FieldMetadata:
		return m.Metadata()
	case template.FieldCreatedAt:
		return m.CreatedAt()
	case template.FieldUpdatedAt:
		return m.UpdatedAt()
	case template.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TemplateMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case template.FieldUserID:
		return m.OldUserID(ctx)
	case template.FieldWorkspaceID:
		return m.OldWorkspaceID(ctx)
	case template.FieldName:
		return m.OldName(ctx)
	case template.FieldDescription:
		return m.OldDescription(ctx)
	case template.FieldContent:
		return m.OldContent(ctx)
	case template.FieldVariables:
		return m.OldVariables(ctx)
	case template.FieldVariableDescriptions:
		return m.OldVariableDescriptions(ctx)
	case template.FieldCategory:
		return m.OldCategory(ctx)
	case template.FieldPlatforms:
		return m.OldPlatforms(ctx)
	case template.FieldTone:
		return m.OldTone(ctx)
	case template.FieldContentType:
		return m.OldContentType(ctx)
	case template.FieldHashtags:
		return m.OldHashtags(ctx)
	case template.FieldIsPublic:
		return m.OldIsPublic(ctx)
	case template.FieldIsFeatured:
		return m.OldIsFeatured(ctx)
	case template.FieldUsageCount:
		return m.OldUsageCount(ctx)
	case template.FieldRating:
		return m.OldRating(ctx)
	case template.FieldRatingCount:
		return m.OldRatingCount(ctx)
	case template.FieldTags:
		return m.OldTags(ctx)
	case template.FieldLanguage:
		return m.OldLanguage(ctx)
	case template.FieldTrainingFiles:
		return m.OldTrainingFiles(ctx)
	case template.FieldRagTrained:
		return m.OldRagTrained(ctx)
	case template.FieldRagTrainedAt:
		return m.OldRagTrainedAt(ctx)
	case template.FieldMetadata:
		return m.OldMetadata(ctx)
	case template.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case template.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case template.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Template field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TemplateMutation) SetField(name string, value ent.Value) error {
	switch name {
	case template.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case template.FieldWorkspaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkspaceID(v)
		return nil
	case template.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case template.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case template.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case template.FieldVariables:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVariables(v)
		return nil
	case template.FieldVariableDescriptions:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVariableDescriptions(v)
		return nil
	case template.FieldCategory:
		v, ok := value.(template.Category)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCategory(v)
		return nil
	case template.FieldPlatforms:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatforms(v)
		return nil
	case template.FieldTone:
		v, ok := value.(template.Tone)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTone(v)
		return nil
	case template.FieldContentType:
		v, ok := value.(template.ContentType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContentType(v)
		return nil
	case template.FieldHashtags:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHashtags(v)
		return nil
	case template.FieldIsPublic:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsPublic(v)
		return nil
	case template.FieldIsFeatured:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsFeatured(v)
		return nil
	case template.FieldUsageCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsageCount(v)
		return nil
	case template.FieldRating:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRating(v)
		return nil
	case template.FieldRatingCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRatingCount(v)
		return nil
	case template.FieldTags:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTags(v)
		return nil
	case template.FieldLanguage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLanguage(v)
		return nil
	case template.FieldTrainingFiles:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTrainingFiles(v)
		return nil
	case template.FieldRagTrained:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRagTrained(v)
		return nil
	case template.FieldRagTrainedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRagTrainedAt(v)
		return nil
	case template.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case template.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case template.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case template.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Template field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TemplateMutation) AddedFields() []string {
	var fields []string
	if m.addusage_count != nil {
		fields = append(fields, template.FieldUsageCount)
	}
	if m.addrating != nil {
		fields = append(fields, template.FieldRating)
	}
	if m.addrating_count != nil {
		fields = append(fields, template.FieldRatingCount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TemplateMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case template.FieldUsageCount:
		return m.AddedUsageCount()
	case template.FieldRating:
		return m.AddedRating()
	case template.FieldRatingCount:
		return m.AddedRatingCount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TemplateMutation) AddField(name string, value ent.Value) error {
	switch name {
	case template.FieldUsageCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUsageCount(v)
		return nil
	case template.FieldRating:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRating(v)
		return nil
	case template.FieldRatingCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRatingCount(v)
		return nil
	}
	return fmt.Errorf("unknown Template numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TemplateMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(template.FieldWorkspaceID) {
		fields = append(fields, template.FieldWorkspaceID)
	}
	if m.FieldCleared(template.FieldDescription) {
		fields = append(fields, template.FieldDescription)
	}
	if m.FieldCleared(template.FieldVariables) {
		fields = append(fields, template.FieldVariables)
	}
	if m.FieldCleared(template.FieldVariableDescriptions) {
		fields = append(fields, template.FieldVariableDescriptions)
	}
	if m.FieldCleared(template.FieldPlatforms) {
		fields = append(fields, template.FieldPlatforms)
	}
	if m.FieldCleared(template.FieldHashtags) {
		fields = append(fields, template.FieldHashtags)
	}
	if m.FieldCleared(template.FieldTags) {
		fields = append(fields, template.FieldTags)
	}
	if m.FieldCleared(template.FieldTrainingFiles) {
		fields = append(fields, template.FieldTrainingFiles)
	}
	if m.FieldCleared(template.FieldRagTrainedAt) {
		fields = append(fields, template.FieldRagTrainedAt)
	}
	if m.FieldCleared(template.FieldMetadata) {
		fields = append(fields, template.FieldMetadata)
	}
	if m.FieldCleared(template.FieldDeletedAt) {
		fields = append(fields, template.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TemplateMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TemplateMutation) ClearField(name string) error {
	switch name {
	case template.FieldWorkspaceID:
		m.ClearWorkspaceID()
		return nil
	case template.FieldDescription:
		m.ClearDescription()
		return nil
	case template.FieldVariables:
		m.ClearVariables()
		return nil
	case template.FieldVariableDescriptions:
		m.ClearVariableDescriptions()
		return nil
	case template.FieldPlatforms:
		m.ClearPlatforms()
		return nil
	case template.FieldHashtags:
		m.ClearHashtags()
		return nil
	case template.FieldTags:
		m.ClearTags()
		return nil
	case template.FieldTrainingFiles:
		m.ClearTrainingFiles()
		return nil
	case template.FieldRagTrainedAt:
		m.ClearRagTrainedAt()
		return nil
	case template.FieldMetadata:
		m.ClearMetadata()
		return nil
	case template.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Template nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TemplateMutation) ResetField(name string) error {
	switch name {
	case template.FieldUserID:
		m.ResetUserID()
		return nil
	case template.FieldWorkspaceID:
		m.ResetWorkspaceID()
		return nil
	case template.FieldName:
		m.ResetName()
		return nil
	case template.FieldDescription:
		m.ResetDescription()
		return nil
	case template.FieldContent:
		m.ResetContent()
		return nil
	case template.FieldVariables:
		m.ResetVariables()
		return nil
	case template.FieldVariableDescriptions:
		m.ResetVariableDescriptions()
		return nil
	case template.FieldCategory:
		m.ResetCategory()
		return nil
	case template.FieldPlatforms:
		m.ResetPlatforms()
		return nil
	case template.FieldTone:
		m.ResetTone()
		return nil
	case template.FieldContentType:
		m.ResetContentType()
		return nil
	case template.FieldHashtags:
		m.ResetHashtags()
		return nil
	case template.FieldIsPublic:
		m.ResetIsPublic()
		return nil
	case template.FieldIsFeatured:
		m.ResetIsFeatured()
		return nil
	case template.FieldUsageCount:
		m.ResetUsageCount()
		return nil
	case template.FieldRating:
		m.ResetRating()
		return nil
	case template.FieldRatingCount:
		m.ResetRatingCount()
		return nil
	case template.FieldTags:
		m.ResetTags()
		return nil
	case template.FieldLanguage:
		m.ResetLanguage()
		return nil
	case template.FieldTrainingFiles:
		m.ResetTrainingFiles()
		return nil
	case template.FieldRagTrained:
		m.ResetRagTrained()
		return nil
	case template.FieldRagTrainedAt:
		m.ResetRagTrainedAt()
		return nil
	case template.FieldMetadata:
		m.ResetMetadata()
		return nil
	case template.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case template.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case template.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Template field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TemplateMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TemplateMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TemplateMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TemplateMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TemplateMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TemplateMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TemplateMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Template unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TemplateMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Template edge %s", name)
}

// WorkspaceMutation represents an operation that mutates the Workspace nodes in the graph.
type WorkspaceMutation struct {
	config
	op                      Op
	typ                     string
	id                      *uuid.UUID
	owner_id                *uuid.UUID
	name                    *string
	description             *string
	color                   *string
	icon                    *string
	settings                *map[string]interface{}
	default_platforms       *[]string
	appenddefault_platforms []string
	brand_guidelines        *map[string]interface{}
	is_personal             *bool
	is_active               *bool
	post_count              *int
	addpost_count           *int
	template_count          *int
	addtemplate_count       *int
	last_activity_at        *time.Time
	metadata                *map[string]interface{}
	created_at              *time.Time
	updated_at              *time.Time
	deleted_at              *time.Time
	clearedFields           map[string]struct{}
	done                    bool
	oldValue                func(context.Context) (*Workspace, error)
	predicates              []predicate.Workspace
}

var _ ent.Mutation = (*WorkspaceMutation)(nil)

// workspaceOption allows management of the mutation configuration using functional options.
type workspaceOption func(*WorkspaceMutation)

// newWorkspaceMutation creates new mutation for the Workspace entity.
func newWorkspaceMutation(c config, op Op, opts ...workspaceOption) *WorkspaceMutation {
	m := &WorkspaceMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkspace,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkspaceID sets the ID field of the mutation.
func withWorkspaceID(id uuid.UUID) workspaceOption {
	return func(m *WorkspaceMutation) {
		var (
			err   error
			once  sync.Once
			value *Workspace
		)
		m.oldValue = func(ctx context.Context) (*Workspace, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Workspace.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkspace sets the old Workspace of the mutation.
func withWorkspace(node *Workspace) workspaceOption {
	return func(m *WorkspaceMutation) {
		m.oldValue = func(context.Context) (*Workspace, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkspaceMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkspaceMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Workspace entities.
func (m *WorkspaceMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkspaceMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkspaceMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Workspace.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetOwnerID sets the "owner_id" field.
func (m *WorkspaceMutation) SetOwnerID(u uuid.UUID) {
	m.owner_id = &u
}

// OwnerID returns the value of the "owner_id" field in the mutation.
func (m *WorkspaceMutation) OwnerID() (r uuid.UUID, exists bool) {
	v := m.owner_id
	if v == nil {
		return
	}
	return *v, true
}

// OldOwnerID returns the old "owner_id" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldOwnerID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOwnerID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOwnerID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOwnerID: %w", err)
	}
	return oldValue.OwnerID, nil
}

// ResetOwnerID resets all changes to the "owner_id" field.
func (m *WorkspaceMutation) ResetOwnerID() {
	m.owner_id = nil
}

// SetName sets the "name" field.
func (m *WorkspaceMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *WorkspaceMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *WorkspaceMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *WorkspaceMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *WorkspaceMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *WorkspaceMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[workspace.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *WorkspaceMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[workspace.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *WorkspaceMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, workspace.FieldDescription)
}

// SetColor sets the "color" field.
func (m *WorkspaceMutation) SetColor(s string) {
	m.color = &s
}

// Color returns the value of the "color" field in the mutation.
func (m *WorkspaceMutation) Color() (r string, exists bool) {
	v := m.color
	if v == nil {
		return
	}
	return *v, true
}

// OldColor returns the old "color" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldColor(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldColor is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldColor requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldColor: %w", err)
	}
	return oldValue.Color, nil
}

// ResetColor resets all changes to the "color" field.
func (m *WorkspaceMutation) ResetColor() {
	m.color = nil
}

// SetIcon sets the "icon" field.
func (m *WorkspaceMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *WorkspaceMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ClearIcon clears the value of the "icon" field.
func (m *WorkspaceMutation) ClearIcon() {
	m.icon = nil
	m.clearedFields[workspace.FieldIcon] = struct{}{}
}

// IconCleared returns if the "icon" field was cleared in this mutation.
func (m *WorkspaceMutation) IconCleared() bool {
	_, ok := m.clearedFields[workspace.FieldIcon]
	return ok
}

// ResetIcon resets all changes to the "icon" field.
func (m *WorkspaceMutation) ResetIcon() {
	m.icon = nil
	delete(m.clearedFields, workspace.FieldIcon)
}

// SetSettings sets the "settings" field.
func (m *WorkspaceMutation) SetSettings(value map[string]interface{}) {
	m.settings = &value
}

// Settings returns the value of the "settings" field in the mutation.
func (m *WorkspaceMutation) Settings() (r map[string]interface{}, exists bool) {
	v := m.settings
	if v == nil {
		return
	}
	return *v, true
}

// OldSettings returns the old "settings" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldSettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSettings: %w", err)
	}
	return oldValue.Settings, nil
}

// ClearSettings clears the value of the "settings" field.
func (m *WorkspaceMutation) ClearSettings() {
	m.settings = nil
	m.clearedFields[workspace.FieldSettings] = struct{}{}
}

// SettingsCleared returns if the "settings" field was cleared in this mutation.
func (m *WorkspaceMutation) SettingsCleared() bool {
	_, ok := m.clearedFields[workspace.FieldSettings]
	return ok
}

// ResetSettings resets all changes to the "settings" field.
func (m *WorkspaceMutation) ResetSettings() {
	m.settings = nil
	delete(m.clearedFields, workspace.FieldSettings)
}

// SetDefaultPlatforms sets the "default_platforms" field.
func (m *WorkspaceMutation) SetDefaultPlatforms(s []string) {
	m.default_platforms = &s
	m.appenddefault_platforms = nil
}

// DefaultPlatforms returns the value of the "default_platforms" field in the mutation.
func (m *WorkspaceMutation) DefaultPlatforms() (r []string, exists bool) {
	v := m.default_platforms
	if v == nil {
		return
	}
	return *v, true
}

// OldDefaultPlatforms returns the old "default_platforms" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldDefaultPlatforms(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDefaultPlatforms is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDefaultPlatforms requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDefaultPlatforms: %w", err)
	}
	return oldValue.DefaultPlatforms, nil
}

// AppendDefaultPlatforms adds s to the "default_platforms" field.
func (m *WorkspaceMutation) AppendDefaultPlatforms(s []string) {
	m.appenddefault_platforms = append(m.appenddefault_platforms, s...)
}

// AppendedDefaultPlatforms returns the list of values that were appended to the "default_platforms" field in this mutation.
func (m *WorkspaceMutation) AppendedDefaultPlatforms() ([]string, bool) {
	if len(m.appenddefault_platforms) == 0 {
		return nil, false
	}
	return m.appenddefault_platforms, true
}

// ClearDefaultPlatforms clears the value of the "default_platforms" field.
func (m *WorkspaceMutation) ClearDefaultPlatforms() {
	m.default_platforms = nil
	m.appenddefault_platforms = nil
	m.clearedFields[workspace.FieldDefaultPlatforms] = struct{}{}
}

// DefaultPlatformsCleared returns if the "default_platforms" field was cleared in this mutation.
func (m *WorkspaceMutation) DefaultPlatformsCleared() bool {
	_, ok := m.clearedFields[workspace.FieldDefaultPlatforms]
	return ok
}

// ResetDefaultPlatforms resets all changes to the "default_platforms" field.
func (m *WorkspaceMutation) ResetDefaultPlatforms() {
	m.default_platforms = nil
	m.appenddefault_platforms = nil
	delete(m.clearedFields, workspace.FieldDefaultPlatforms)
}

// SetBrandGuidelines sets the "brand_guidelines" field.
func (m *WorkspaceMutation) SetBrandGuidelines(value map[string]interface{}) {
	m.brand_guidelines = &value
}

// BrandGuidelines returns the value of the "brand_guidelines" field in the mutation.
func (m *WorkspaceMutation) BrandGuidelines() (r map[string]interface{}, exists bool) {
	v := m.brand_guidelines
	if v == nil {
		return
	}
	return *v, true
}

// OldBrandGuidelines returns the old "brand_guidelines" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldBrandGuidelines(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBrandGuidelines is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBrandGuidelines requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBrandGuidelines: %w", err)
	}
	return oldValue.BrandGuidelines, nil
}

// ClearBrandGuidelines clears the value of the "brand_guidelines" field.
func (m *WorkspaceMutation) ClearBrandGuidelines() {
	m.brand_guidelines = nil
	m.clearedFields[workspace.FieldBrandGuidelines] = struct{}{}
}

// BrandGuidelinesCleared returns if the "brand_guidelines" field was cleared in this mutation.
func (m *WorkspaceMutation) BrandGuidelinesCleared() bool {
	_, ok := m.clearedFields[workspace.FieldBrandGuidelines]
	return ok
}

// ResetBrandGuidelines resets all changes to the "brand_guidelines" field.
func (m *WorkspaceMutation) ResetBrandGuidelines() {
	m.brand_guidelines = nil
	delete(m.clearedFields, workspace.FieldBrandGuidelines)
}

// SetIsPersonal sets the "is_personal" field.
func (m *WorkspaceMutation) SetIsPersonal(b bool) {
	m.is_personal = &b
}

// IsPersonal returns the value of the "is_personal" field in the mutation.
func (m *WorkspaceMutation) IsPersonal() (r bool, exists bool) {
	v := m.is_personal
	if v == nil {
		return
	}
	return *v, true
}

// OldIsPersonal returns the old "is_personal" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldIsPersonal(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsPersonal is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsPersonal requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsPersonal: %w", err)
	}
	return oldValue.IsPersonal, nil
}

// ResetIsPersonal resets all changes to the "is_personal" field.
func (m *WorkspaceMutation) ResetIsPersonal() {
	m.is_personal = nil
}

// SetIsActive sets the "is_active" field.
func (m *WorkspaceMutation) SetIsActive(b bool) {
	m.is_active = &b
}

// IsActive returns the value of the "is_active" field in the mutation.
func (m *WorkspaceMutation) IsActive() (r bool, exists bool) {
	v := m.is_active
	if v == nil {
		return
	}
	return *v, true
}

// OldIsActive returns the old "is_active" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldIsActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsActive: %w", err)
	}
	return oldValue.IsActive, nil
}

// ResetIsActive resets all changes to the "is_active" field.
func (m *WorkspaceMutation) ResetIsActive() {
	m.is_active = nil
}

// SetPostCount sets the "post_count" field.
func (m *WorkspaceMutation) SetPostCount(i int) {
	m.post_count = &i
	m.addpost_count = nil
}

// PostCount returns the value of the "post_count" field in the mutation.
func (m *WorkspaceMutation) PostCount() (r int, exists bool) {
	v := m.post_count
	if v == nil {
		return
	}
	return *v, true
}

// OldPostCount returns the old "post_count" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldPostCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPostCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPostCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPostCount: %w", err)
	}
	return oldValue.PostCount, nil
}

// AddPostCount adds i to the "post_count" field.
func (m *WorkspaceMutation) AddPostCount(i int) {
	if m.addpost_count != nil {
		*m.addpost_count += i
	} else {
		m.addpost_count = &i
	}
}

// AddedPostCount returns the value that was added to the "post_count" field in this mutation.
func (m *WorkspaceMutation) AddedPostCount() (r int, exists bool) {
	v := m.addpost_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetPostCount resets all changes to the "post_count" field.
func (m *WorkspaceMutation) ResetPostCount() {
	m.post_count = nil
	m.addpost_count = nil
}

// SetTemplateCount sets the "template_count" field.
func (m *WorkspaceMutation) SetTemplateCount(i int) {
	m.template_count = &i
	m.addtemplate_count = nil
}

// TemplateCount returns the value of the "template_count" field in the mutation.
func (m *WorkspaceMutation) TemplateCount() (r int, exists bool) {
	v := m.template_count
	if v == nil {
		return
	}
	return *v, true
}

// OldTemplateCount returns the old "template_count" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldTemplateCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTemplateCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTemplateCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTemplateCount: %w", err)
	}
	return oldValue.TemplateCount, nil
}

// AddTemplateCount adds i to the "template_count" field.
func (m *WorkspaceMutation) AddTemplateCount(i int) {
	if m.addtemplate_count != nil {
		*m.addtemplate_count += i
	} else {
		m.addtemplate_count = &i
	}
}

// AddedTemplateCount returns the value that was added to the "template_count" field in this mutation.
func (m *WorkspaceMutation) AddedTemplateCount() (r int, exists bool) {
	v := m.addtemplate_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetTemplateCount resets all changes to the "template_count" field.
func (m *WorkspaceMutation) ResetTemplateCount() {
	m.template_count = nil
	m.addtemplate_count = nil
}

// SetLastActivityAt sets the "last_activity_at" field.
func (m *WorkspaceMutation) SetLastActivityAt(t time.Time) {
	m.last_activity_at = &t
}

// LastActivityAt returns the value of the "last_activity_at" field in the mutation.
func (m *WorkspaceMutation) LastActivityAt() (r time.Time, exists bool) {
	v := m.last_activity_at
	if v == nil {
		return
	}
	return *v, true
}

// OldLastActivityAt returns the old "last_activity_at" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldLastActivityAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastActivityAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastActivityAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastActivityAt: %w", err)
	}
	return oldValue.LastActivityAt, nil
}

// ClearLastActivityAt clears the value of the "last_activity_at" field.
func (m *WorkspaceMutation) ClearLastActivityAt() {
	m.last_activity_at = nil
	m.clearedFields[workspace.FieldLastActivityAt] = struct{}{}
}

// LastActivityAtCleared returns if the "last_activity_at" field was cleared in this mutation.
func (m *WorkspaceMutation) LastActivityAtCleared() bool {
	_, ok := m.clearedFields[workspace.FieldLastActivityAt]
	return ok
}

// ResetLastActivityAt resets all changes to the "last_activity_at" field.
func (m *WorkspaceMutation) ResetLastActivityAt() {
	m.last_activity_at = nil
	delete(m.clearedFields, workspace.FieldLastActivityAt)
}

// SetMetadata sets the "metadata" field.
func (m *WorkspaceMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *WorkspaceMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *WorkspaceMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[workspace.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *WorkspaceMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[workspace.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *WorkspaceMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, workspace.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkspaceMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkspaceMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkspaceMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkspaceMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkspaceMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkspaceMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *WorkspaceMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *WorkspaceMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Workspace entity.
// If the Workspace object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkspaceMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *WorkspaceMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[workspace.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *WorkspaceMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[workspace.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *WorkspaceMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, workspace.FieldDeletedAt)
}

// Where appends a list predicates to the WorkspaceMutation builder.
func (m *WorkspaceMutation) Where(ps ...predicate.Workspace) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkspaceMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkspaceMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Workspace, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkspaceMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkspaceMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Workspace).
func (m *WorkspaceMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkspaceMutation) Fields() []string {
	fields := make([]string, 0, 17)
	if m.owner_id != nil {
		fields = append(fields, workspace.FieldOwnerID)
	}
	if m.name != nil {
		fields = append(fields, workspace.FieldName)
	}
	if m.description != nil {
		fields = append(fields, workspace.FieldDescription)
	}
	if m.color != nil {
		fields = append(fields, workspace.FieldColor)
	}
	if m.icon != nil {
		fields = append(fields, workspace.FieldIcon)
	}
	if m.settings != nil {
		fields = append(fields, workspace.FieldSettings)
	}
	if m.default_platforms != nil {
		fields = append(fields, workspace.FieldDefaultPlatforms)
	}
	if m.brand_guidelines != nil {
		fields = append(fields, workspace.FieldBrandGuidelines)
	}
	if m.is_personal != nil {
		fields = append(fields, workspace.FieldIsPersonal)
	}
	if m.is_active != nil {
		fields = append(fields, workspace.FieldIsActive)
	}
	if m.post_count != nil {
		fields = append(fields, workspace.FieldPostCount)
	}
	if m.template_count != nil {
		fields = append(fields, workspace.FieldTemplateCount)
	}
	if m.last_activity_at != nil {
		fields = append(fields, workspace.FieldLastActivityAt)
	}
	if m.metadata != nil {
		fields = append(fields, workspace.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, workspace.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workspace.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, workspace.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkspaceMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workspace.FieldOwnerID:
		return m.OwnerID()
	case workspace.FieldName:
		return m.Name()
	case workspace.FieldDescription:
		return m.Description()
	case workspace.FieldColor:
		return m.Color()
	case workspace.FieldIcon:
		return m.Icon()
	case workspace.FieldSettings:
		return m.Settings()
	case workspace.FieldDefaultPlatforms:
		return m.DefaultPlatforms()
	case workspace.FieldBrandGuidelines:
		return m.BrandGuidelines()
	case workspace.FieldIsPersonal:
		return m.IsPersonal()
	case workspace.FieldIsActive:
		return m.IsActive()
	case workspace.FieldPostCount:
		return m.PostCount()
	case workspace.FieldTemplateCount:
		return m.TemplateCount()
	case workspace.FieldLastActivityAt:
		return m.LastActivityAt()
	case workspace.FieldMetadata:
		return m.Metadata()
	case workspace.FieldCreatedAt:
		return m.CreatedAt()
	case workspace.FieldUpdatedAt:
		return m.UpdatedAt()
	case workspace.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkspaceMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workspace.FieldOwnerID:
		return m.OldOwnerID(ctx)
	case workspace.FieldName:
		return m.OldName(ctx)
	case workspace.FieldDescription:
		return m.OldDescription(ctx)
	case workspace.FieldColor:
		return m.OldColor(ctx)
	case workspace.FieldIcon:
		return m.OldIcon(ctx)
	case workspace.FieldSettings:
		return m.OldSettings(ctx)
	case workspace.FieldDefaultPlatforms:
		return m.OldDefaultPlatforms(ctx)
	case workspace.FieldBrandGuidelines:
		return m.OldBrandGuidelines(ctx)
	case workspace.FieldIsPersonal:
		return m.OldIsPersonal(ctx)
	case workspace.FieldIsActive:
		return m.OldIsActive(ctx)
	case workspace.FieldPostCount:
		return m.OldPostCount(ctx)
	case workspace.FieldTemplateCount:
		return m.OldTemplateCount(ctx)
	case workspace.FieldLastActivityAt:
		return m.OldLastActivityAt(ctx)
	case workspace.FieldMetadata:
		return m.OldMetadata(ctx)
	case workspace.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workspace.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case workspace.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Workspace field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkspaceMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workspace.FieldOwnerID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOwnerID(v)
		return nil
	case workspace.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case workspace.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case workspace.FieldColor:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetColor(v)
		return nil
	case workspace.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case workspace.FieldSettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSettings(v)
		return nil
	case workspace.FieldDefaultPlatforms:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDefaultPlatforms(v)
		return nil
	case workspace.FieldBrandGuidelines:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBrandGuidelines(v)
		return nil
	case workspace.FieldIsPersonal:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsPersonal(v)
		return nil
	case workspace.FieldIsActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsActive(v)
		return nil
	case workspace.FieldPostCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPostCount(v)
		return nil
	case workspace.FieldTemplateCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTemplateCount(v)
		return nil
	case workspace.FieldLastActivityAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastActivityAt(v)
		return nil
	case workspace.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case workspace.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workspace.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case workspace.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Workspace field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkspaceMutation) AddedFields() []string {
	var fields []string
	if m.addpost_count != nil {
		fields = append(fields, workspace.FieldPostCount)
	}
	if m.addtemplate_count != nil {
		fields = append(fields, workspace.FieldTemplateCount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkspaceMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case workspace.FieldPostCount:
		return m.AddedPostCount()
	case workspace.FieldTemplateCount:
		return m.AddedTemplateCount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkspaceMutation) AddField(name string, value ent.Value) error {
	switch name {
	case workspace.FieldPostCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPostCount(v)
		return nil
	case workspace.FieldTemplateCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTemplateCount(v)
		return nil
	}
	return fmt.Errorf("unknown Workspace numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkspaceMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(workspace.FieldDescription) {
		fields = append(fields, workspace.FieldDescription)
	}
	if m.FieldCleared(workspace.FieldIcon) {
		fields = append(fields, workspace.FieldIcon)
	}
	if m.FieldCleared(workspace.FieldSettings) {
		fields = append(fields, workspace.FieldSettings)
	}
	if m.FieldCleared(workspace.FieldDefaultPlatforms) {
		fields = append(fields, workspace.FieldDefaultPlatforms)
	}
	if m.FieldCleared(workspace.FieldBrandGuidelines) {
		fields = append(fields, workspace.FieldBrandGuidelines)
	}
	if m.FieldCleared(workspace.FieldLastActivityAt) {
		fields = append(fields, workspace.FieldLastActivityAt)
	}
	if m.FieldCleared(workspace.FieldMetadata) {
		fields = append(fields, workspace.FieldMetadata)
	}
	if m.FieldCleared(workspace.FieldDeletedAt) {
		fields = append(fields, workspace.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkspaceMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkspaceMutation) ClearField(name string) error {
	switch name {
	case workspace.FieldDescription:
		m.ClearDescription()
		return nil
	case workspace.FieldIcon:
		m.ClearIcon()
		return nil
	case workspace.FieldSettings:
		m.ClearSettings()
		return nil
	case workspace.FieldDefaultPlatforms:
		m.ClearDefaultPlatforms()
		return nil
	case workspace.FieldBrandGuidelines:
		m.ClearBrandGuidelines()
		return nil
	case workspace.FieldLastActivityAt:
		m.ClearLastActivityAt()
		return nil
	case workspace.FieldMetadata:
		m.ClearMetadata()
		return nil
	case workspace.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Workspace nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkspaceMutation) ResetField(name string) error {
	switch name {
	case workspace.FieldOwnerID:
		m.ResetOwnerID()
		return nil
	case workspace.FieldName:
		m.ResetName()
		return nil
	case workspace.FieldDescription:
		m.ResetDescription()
		return nil
	case workspace.FieldColor:
		m.ResetColor()
		return nil
	case workspace.FieldIcon:
		m.ResetIcon()
		return nil
	case workspace.FieldSettings:
		m.ResetSettings()
		return nil
	case workspace.FieldDefaultPlatforms:
		m.ResetDefaultPlatforms()
		return nil
	case workspace.FieldBrandGuidelines:
		m.ResetBrandGuidelines()
		return nil
	case workspace.FieldIsPersonal:
		m.ResetIsPersonal()
		return nil
	case workspace.FieldIsActive:
		m.ResetIsActive()
		return nil
	case workspace.FieldPostCount:
		m.ResetPostCount()
		return nil
	case workspace.FieldTemplateCount:
		m.ResetTemplateCount()
		return nil
	case workspace.FieldLastActivityAt:
		m.ResetLastActivityAt()
		return nil
	case workspace.FieldMetadata:
		m.ResetMetadata()
		return nil
	case workspace.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workspace.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case workspace.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Workspace field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkspaceMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkspaceMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkspaceMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkspaceMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkspaceMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkspaceMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkspaceMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Workspace unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkspaceMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Workspace edge %s", name)
}

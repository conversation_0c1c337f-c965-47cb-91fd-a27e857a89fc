# Integration Service Configuration

# Server configuration
server:
  env: "development"
  grpc_port: 50055
  http_port: 8085
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"

# Database configuration
database:
  # Database type: "postgres" or "sqlite"
  type: "postgres"

  # PostgreSQL configuration (used when type = "postgres")
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "integration_service_db"
  ssl_mode: "disable"

  # SQLite configuration (used when type = "sqlite")
  sqlite_path: "./data/integration_service.db"

  # Connection pool settings (applies to both databases)
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"

# OAuth provider configurations
oauth:
  facebook:
    client_id: "${FACEBOOK_CLIENT_ID}"
    client_secret: "${FACEBOOK_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/facebook/callback"
  
  twitter:
    client_id: "${TWITTER_CLIENT_ID}"
    client_secret: "${TWITTER_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/twitter/callback"
  
  instagram:
    client_id: "${INSTAGRAM_CLIENT_ID}"
    client_secret: "${INSTAGRAM_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/instagram/callback"
  
  linkedin:
    client_id: "${LINKEDIN_CLIENT_ID}"
    client_secret: "${LINKEDIN_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/linkedin/callback"
  
  youtube:
    client_id: "${YOUTUBE_CLIENT_ID}"
    client_secret: "${YOUTUBE_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/youtube/callback"
  
  tiktok:
    client_id: "${TIKTOK_CLIENT_ID}"
    client_secret: "${TIKTOK_CLIENT_SECRET}"
    redirect_url: "http://localhost:8085/oauth/tiktok/callback"

# Publishers configuration
publishers:
  default_timeout: "60s"
  retry_attempts: 3
  retry_delay: "5s"

# Rate limiting
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# Feature flags
features:
  scheduled_posts: true
  analytics: true
  webhooks: false
  bulk_operations: true

# CORS configuration
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:3001"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true
  max_age: 86400

# Cleanup configuration
cleanup:
  enabled: true
  interval: "24h"
  retain_failed_posts_days: 30
  retain_success_posts_days: 90

# Monitoring configuration
monitoring:
  enabled: true
  metrics_port: "9095"
  health_endpoint: "/health"

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# JWT configuration
jwt:
  secret_key: "${JWT_SECRET_KEY:integration-service-secret}"
  token_duration: "1h"
  refresh_duration: "24h"

# Services configuration
services:
  user_service: "localhost:50051"
  asset_service: "localhost:50052"
  ai_content_service: "localhost:50053"
  credit_billing_service: "localhost:50054"
  notification_service: "localhost:50056"

# Kafka configuration
kafka:
  brokers:
    - "localhost:9092"
  consumer_group: "integration-service"
  topics:
    notification_events: "notification.events"
  producer:
    retry_max: 3
    retry_backoff: "100ms"
    flush_timeout: "10s"
  consumer:
    session_timeout: "10s"
    heartbeat_interval: "3s"

# Metrics configuration
metrics:
  enabled: true
  port: "9095"
  path: "/metrics"

# Tracing configuration
tracing:
  enabled: true
  service_name: "integration-service"
  jaeger_endpoint: "http://localhost:14268/api/traces"
  sample_rate: 0.1

// Code generated by ent, DO NOT EDIT.

package subscriptionplan

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the subscriptionplan type in the database.
	Label = "subscription_plan"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDisplayName holds the string denoting the display_name field in the database.
	FieldDisplayName = "display_name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldPrice holds the string denoting the price field in the database.
	FieldPrice = "price"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldBillingInterval holds the string denoting the billing_interval field in the database.
	FieldBillingInterval = "billing_interval"
	// FieldCreditsIncluded holds the string denoting the credits_included field in the database.
	FieldCreditsIncluded = "credits_included"
	// FieldMonthlyCredits holds the string denoting the monthly_credits field in the database.
	FieldMonthlyCredits = "monthly_credits"
	// FieldIsUnlimited holds the string denoting the is_unlimited field in the database.
	FieldIsUnlimited = "is_unlimited"
	// FieldFeatures holds the string denoting the features field in the database.
	FieldFeatures = "features"
	// FieldLimits holds the string denoting the limits field in the database.
	FieldLimits = "limits"
	// FieldMaxUsers holds the string denoting the max_users field in the database.
	FieldMaxUsers = "max_users"
	// FieldMaxWorkspaces holds the string denoting the max_workspaces field in the database.
	FieldMaxWorkspaces = "max_workspaces"
	// FieldIsActive holds the string denoting the is_active field in the database.
	FieldIsActive = "is_active"
	// FieldIsFeatured holds the string denoting the is_featured field in the database.
	FieldIsFeatured = "is_featured"
	// FieldSortOrder holds the string denoting the sort_order field in the database.
	FieldSortOrder = "sort_order"
	// FieldStripePriceID holds the string denoting the stripe_price_id field in the database.
	FieldStripePriceID = "stripe_price_id"
	// FieldStripeProductID holds the string denoting the stripe_product_id field in the database.
	FieldStripeProductID = "stripe_product_id"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the subscriptionplan in the database.
	Table = "subscription_plans"
)

// Columns holds all SQL columns for subscriptionplan fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldDisplayName,
	FieldDescription,
	FieldPrice,
	FieldCurrency,
	FieldBillingInterval,
	FieldCreditsIncluded,
	FieldMonthlyCredits,
	FieldIsUnlimited,
	FieldFeatures,
	FieldLimits,
	FieldMaxUsers,
	FieldMaxWorkspaces,
	FieldIsActive,
	FieldIsFeatured,
	FieldSortOrder,
	FieldStripePriceID,
	FieldStripeProductID,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DisplayNameValidator is a validator for the "display_name" field. It is called by the builders before save.
	DisplayNameValidator func(string) error
	// PriceValidator is a validator for the "price" field. It is called by the builders before save.
	PriceValidator func(int64) error
	// DefaultCurrency holds the default value on creation for the "currency" field.
	DefaultCurrency string
	// CurrencyValidator is a validator for the "currency" field. It is called by the builders before save.
	CurrencyValidator func(string) error
	// DefaultCreditsIncluded holds the default value on creation for the "credits_included" field.
	DefaultCreditsIncluded int
	// CreditsIncludedValidator is a validator for the "credits_included" field. It is called by the builders before save.
	CreditsIncludedValidator func(int) error
	// DefaultMonthlyCredits holds the default value on creation for the "monthly_credits" field.
	DefaultMonthlyCredits int
	// MonthlyCreditsValidator is a validator for the "monthly_credits" field. It is called by the builders before save.
	MonthlyCreditsValidator func(int) error
	// DefaultIsUnlimited holds the default value on creation for the "is_unlimited" field.
	DefaultIsUnlimited bool
	// DefaultMaxUsers holds the default value on creation for the "max_users" field.
	DefaultMaxUsers int
	// MaxUsersValidator is a validator for the "max_users" field. It is called by the builders before save.
	MaxUsersValidator func(int) error
	// DefaultMaxWorkspaces holds the default value on creation for the "max_workspaces" field.
	DefaultMaxWorkspaces int
	// MaxWorkspacesValidator is a validator for the "max_workspaces" field. It is called by the builders before save.
	MaxWorkspacesValidator func(int) error
	// DefaultIsActive holds the default value on creation for the "is_active" field.
	DefaultIsActive bool
	// DefaultIsFeatured holds the default value on creation for the "is_featured" field.
	DefaultIsFeatured bool
	// DefaultSortOrder holds the default value on creation for the "sort_order" field.
	DefaultSortOrder int
	// StripePriceIDValidator is a validator for the "stripe_price_id" field. It is called by the builders before save.
	StripePriceIDValidator func(string) error
	// StripeProductIDValidator is a validator for the "stripe_product_id" field. It is called by the builders before save.
	StripeProductIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// BillingInterval defines the type for the "billing_interval" enum field.
type BillingInterval string

// BillingIntervalMonthly is the default value of the BillingInterval enum.
const DefaultBillingInterval = BillingIntervalMonthly

// BillingInterval values.
const (
	BillingIntervalMonthly BillingInterval = "monthly"
	BillingIntervalYearly  BillingInterval = "yearly"
)

func (bi BillingInterval) String() string {
	return string(bi)
}

// BillingIntervalValidator is a validator for the "billing_interval" field enum values. It is called by the builders before save.
func BillingIntervalValidator(bi BillingInterval) error {
	switch bi {
	case BillingIntervalMonthly, BillingIntervalYearly:
		return nil
	default:
		return fmt.Errorf("subscriptionplan: invalid enum value for billing_interval field: %q", bi)
	}
}

// OrderOption defines the ordering options for the SubscriptionPlan queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDisplayName orders the results by the display_name field.
func ByDisplayName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDisplayName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByPrice orders the results by the price field.
func ByPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrice, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByBillingInterval orders the results by the billing_interval field.
func ByBillingInterval(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBillingInterval, opts...).ToFunc()
}

// ByCreditsIncluded orders the results by the credits_included field.
func ByCreditsIncluded(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreditsIncluded, opts...).ToFunc()
}

// ByMonthlyCredits orders the results by the monthly_credits field.
func ByMonthlyCredits(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonthlyCredits, opts...).ToFunc()
}

// ByIsUnlimited orders the results by the is_unlimited field.
func ByIsUnlimited(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsUnlimited, opts...).ToFunc()
}

// ByMaxUsers orders the results by the max_users field.
func ByMaxUsers(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxUsers, opts...).ToFunc()
}

// ByMaxWorkspaces orders the results by the max_workspaces field.
func ByMaxWorkspaces(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxWorkspaces, opts...).ToFunc()
}

// ByIsActive orders the results by the is_active field.
func ByIsActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsActive, opts...).ToFunc()
}

// ByIsFeatured orders the results by the is_featured field.
func ByIsFeatured(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsFeatured, opts...).ToFunc()
}

// BySortOrder orders the results by the sort_order field.
func BySortOrder(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSortOrder, opts...).ToFunc()
}

// ByStripePriceID orders the results by the stripe_price_id field.
func ByStripePriceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStripePriceID, opts...).ToFunc()
}

// ByStripeProductID orders the results by the stripe_product_id field.
func ByStripeProductID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStripeProductID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

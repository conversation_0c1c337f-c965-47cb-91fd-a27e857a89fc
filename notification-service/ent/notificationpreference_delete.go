// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/notification-service/ent/notificationpreference"
	"github.com/social-content-ai/notification-service/ent/predicate"
)

// NotificationPreferenceDelete is the builder for deleting a NotificationPreference entity.
type NotificationPreferenceDelete struct {
	config
	hooks    []Hook
	mutation *NotificationPreferenceMutation
}

// Where appends a list predicates to the NotificationPreferenceDelete builder.
func (npd *NotificationPreferenceDelete) Where(ps ...predicate.NotificationPreference) *NotificationPreferenceDelete {
	npd.mutation.Where(ps...)
	return npd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (npd *NotificationPreferenceDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, npd.sqlExec, npd.mutation, npd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (npd *NotificationPreferenceDelete) ExecX(ctx context.Context) int {
	n, err := npd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (npd *NotificationPreferenceDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(notificationpreference.Table, sqlgraph.NewFieldSpec(notificationpreference.FieldID, field.TypeUUID))
	if ps := npd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, npd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	npd.mutation.done = true
	return affected, err
}

// NotificationPreferenceDeleteOne is the builder for deleting a single NotificationPreference entity.
type NotificationPreferenceDeleteOne struct {
	npd *NotificationPreferenceDelete
}

// Where appends a list predicates to the NotificationPreferenceDelete builder.
func (npdo *NotificationPreferenceDeleteOne) Where(ps ...predicate.NotificationPreference) *NotificationPreferenceDeleteOne {
	npdo.npd.mutation.Where(ps...)
	return npdo
}

// Exec executes the deletion query.
func (npdo *NotificationPreferenceDeleteOne) Exec(ctx context.Context) error {
	n, err := npdo.npd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{notificationpreference.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (npdo *NotificationPreferenceDeleteOne) ExecX(ctx context.Context) {
	if err := npdo.Exec(ctx); err != nil {
		panic(err)
	}
}

# 🧠 RAG Processing Service - Detailed Flows

## Tổng quan
RAG Processing Service là một Python service chuyên xử lý training data cho RAG (Retrieval-Augmented Generation) và Graph RAG, sử dụng **LangChain framework** để orchestrate toàn bộ pipeline, lưu trữ vào Neo4j (graph database) và Qdrant (vector database).

## 🏗️ Service Architecture

### Core Components với LangChain Integration
```
RAG Processing Service (LangChain-powered)
├── 📄 LangChain Document Loaders
│   ├── PyPDFLoader (PDF files)
│   ├── Docx2txtLoader (DOCX files)
│   ├── TextLoader (TXT, MD files)
│   ├── UnstructuredHTMLLoader (HTML files)
│   ├── UnstructuredPowerPointLoader (PPTX files)
│   └── Custom Document Loaders
├── ✂️ LangChain Text Splitters
│   ├── RecursiveCharacterTextSplitter
│   ├── TokenTextSplitter
│   ├── MarkdownHeaderTextSplitter
│   └── SemanticChunker
├── 🔤 LangChain Embeddings
│   ├── OpenAIEmbeddings
│   ├── HuggingFaceEmbeddings
│   ├── SentenceTransformerEmbeddings
│   └── Custom Embedding Models
├── 🏷️ LangChain Entity Extraction
│   ├── LLM-based Entity Extraction
│   ├── spaCy Integration
│   ├── Custom Entity Chains
│   └── Confidence Scoring
├── 🕸️ LangChain Graph Construction
│   ├── Neo4j Vector Integration
│   ├── GraphCypherQAChain
│   ├── Relationship Extraction Chains
│   └── Graph Optimization
├── 🔍 LangChain Vector Stores
│   ├── Qdrant Vector Store
│   ├── Neo4j Vector Index
│   ├── Similarity Search Chains
│   └── Retrieval QA Chains
└── 🧠 LangChain RAG Chains
    ├── RetrievalQA Chain
    ├── ConversationalRetrievalChain
    ├── GraphCypherQAChain
    └── Custom RAG Pipelines
```

## 🔄 Processing Flows

### 1. Document Upload và Processing Trigger

#### **Scenario A: Template Creation với RAG Training**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant CMS as Content Management Service
    participant AS as Asset Service
    participant US as User Service
    participant CS as Credit Service
    participant S3 as S3/MinIO
    participant K as Kafka
    participant RPS as RAG Processing Service
    participant R as Redis

    Note over U,S3: Step 1: Request Upload cho Training Document
    U->>FE: Select training document file
    FE->>AG: POST /assets/request-upload
    AG->>AS: RequestUpload(filename, filesize, filetype, purpose=rag_training)

    AS->>US: ValidateToken() + CheckPermission()
    US->>AS: Return user context

    AS->>CS: CheckStorageQuota(user_id, filesize)
    CS->>AS: Return quota status

    AS->>AS: Create pending_upload record
    AS->>S3: GeneratePresignedURL(bucket, key, expiry=15min)
    S3->>AS: Return presigned URL

    AS->>AG: UploadResponse(presigned_url, upload_id, s3_key)
    AG->>FE: Return upload URL + upload_id
    FE->>U: Show upload progress UI

    Note over U,S3: Step 2: Direct Upload to S3
    U->>S3: Direct upload using presigned URL
    S3->>U: Upload completed

    Note over U,AS: Step 3: Confirm Upload và Validate File
    FE->>AG: POST /assets/confirm-upload/{upload_id}
    AG->>AS: ConfirmUpload(upload_id)

    AS->>S3: Verify file exists and get metadata
    S3->>AS: Return file info

    AS->>AS: Download file for validation
    AS->>AS: Validate file (type, size, format)
    AS->>AS: Scan for malware/corruption
    AS->>AS: Calculate file hash
    AS->>AS: Check file integrity
    AS->>AS: Verify file is not garbage/corrupted
    AS->>AS: Extract content preview

    alt File validation successful
        AS->>AS: Update upload status to 'completed'
        AS->>AS: Create asset record with validation status
        AS->>AG: Return asset_id + validation results
        AG->>FE: File validated successfully
        FE->>U: Show file ready + metadata preview

        Note over U,CMS: Step 4: Create Template với Validated File
        U->>FE: Create template (with asset_id reference)
        FE->>AG: POST /templates {asset_id, template_data}
        AG->>CMS: Route template creation

        CMS->>AS: Verify asset exists and is valid
        AS->>CMS: Confirm asset validity + file info

        CMS->>CMS: Create template record
        CMS->>CMS: Link template to asset_id
        CMS->>K: Publish TemplateCreated event
        CMS->>K: Publish RAGTrainingRequested event
        CMS->>AG: Return template creation success
        AG->>FE: Template created successfully
        FE->>U: Show success + RAG processing status

        K->>RPS: Consume RAGTrainingRequested event
        RPS->>AS: Get verified file metadata and path
        AS->>RPS: Return validated file info
        RPS->>R: Queue processing job
        RPS->>RPS: Start async processing

        Note over RPS: RAG training begins for template
    else File validation failed
        AS->>AS: Update upload status to 'failed'
        AS->>S3: Delete invalid file
        AS->>AG: Return validation errors
        AG->>FE: File validation failed
        FE->>U: Show validation errors + retry option
    end
```

#### **Scenario B: Template Update với File Change**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant CMS as Content Management Service
    participant AS as Asset Service
    participant US as User Service
    participant CS as Credit Service
    participant S3 as S3/MinIO
    participant K as Kafka
    participant RPS as RAG Processing Service
    participant R as Redis
    participant Neo4j as Neo4j Graph DB
    participant Qdrant as Qdrant Vector DB

    Note over U,S3: Step 1: Request Upload cho New Training File
    U->>FE: Select new training file for template
    FE->>AG: POST /assets/request-upload
    AG->>AS: RequestUpload(filename, filesize, filetype, purpose=rag_training, template_id={id})

    AS->>US: ValidateToken() + CheckPermission()
    US->>AS: Return user context

    AS->>CS: CheckStorageQuota(user_id, filesize)
    CS->>AS: Return quota status

    AS->>AS: Get existing template file hash
    AS->>AS: Create pending_upload record
    AS->>S3: GeneratePresignedURL(bucket, key, expiry=15min)

    AS->>AG: UploadResponse(presigned_url, upload_id, s3_key)
    AG->>FE: Return upload URL + upload_id

    Note over U,S3: Step 2: Direct Upload to S3
    U->>S3: Direct upload using presigned URL
    S3->>U: Upload completed

    Note over U,AS: Step 3: Confirm Upload và Compare Files
    FE->>AG: POST /assets/confirm-upload/{upload_id}
    AG->>AS: ConfirmUpload(upload_id)

    AS->>S3: Verify file exists and get metadata
    AS->>AS: Download file for validation
    AS->>AS: Validate new file (type, size, format)
    AS->>AS: Calculate new file hash
    AS->>AS: Compare with existing template file hash

    alt File content changed and valid
        AS->>AS: Complete file validation
        AS->>AS: Create new asset record
        AS->>AG: Return new asset_id + change detected
        AG->>FE: New file validated + change detected
        FE->>U: Show new file ready + change detected

        Note over U,CMS: Step 4: Update Template với New File Reference
        U->>FE: Update template (with new asset_id)
        FE->>AG: PUT /templates/{id} {training_asset_id: new_asset_id}
        AG->>CMS: Route template update

        CMS->>AS: Verify new asset exists and is valid
        AS->>CMS: Confirm new asset validity

        CMS->>CMS: Get existing template training asset
        CMS->>CMS: Compare old vs new asset_id
        CMS->>CMS: Update template record

        alt Training asset changed
            CMS->>CMS: Mark old RAG data for cleanup
            CMS->>K: Publish TemplateUpdated event
            CMS->>K: Publish RAGRetrainingRequested event
            CMS->>AG: Return update success + retraining_triggered: true
            AG->>FE: Template updated successfully
            FE->>U: Show success + "Training data updated, reprocessing..."
        else Training asset unchanged or no training asset
            CMS->>K: Publish TemplateUpdated event (no RAG event)
            CMS->>AG: Return update success + retraining_triggered: false
            AG->>FE: Template updated successfully
            FE->>U: Show success (no training changes)
        end

        K->>RPS: Consume RAGRetrainingRequested event
        RPS->>AS: Get new verified file info
        AS->>RPS: Return validated file metadata

        Note over RPS: Cleanup old RAG data
        RPS->>Neo4j: Delete old template graph data
        RPS->>Qdrant: Delete old template embeddings
        RPS->>R: Clear related cache

        RPS->>R: Queue reprocessing job
        RPS->>RPS: Start async reprocessing

        Note over RPS: RAG retraining begins
    else File unchanged
        AS->>AS: Delete uploaded file (duplicate)
        AS->>AS: Update upload status to 'duplicate'
        AS->>AG: Return file unchanged status
        AG->>FE: No changes detected
        FE->>U: File unchanged - no retraining needed
    else File invalid
        AS->>S3: Delete invalid file
        AS->>AS: Update upload status to 'failed'
        AS->>AG: Return validation errors
        AG->>FE: File validation failed
        FE->>U: Show validation errors + retry option
    end
```

#### **Scenario C: Direct Document Upload for RAG**
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant AG as API Gateway
    participant AS as Asset Service
    participant K as Kafka
    participant RPS as RAG Processing Service
    participant R as Redis

    U->>FE: Upload document for RAG training
    FE->>AG: POST /assets/upload?purpose=rag_training
    AG->>AS: Route upload request

    AS->>AS: Validate file (type, size, format)
    AS->>AS: Store file in S3/MinIO
    AS->>AS: Create asset record with RAG purpose

    AS->>K: Publish DocumentUploaded event
    AS->>AG: Return upload success
    AG->>FE: Upload completed
    FE->>U: Show upload success + processing status

    K->>RPS: Consume DocumentUploaded event
    RPS->>RPS: Check if RAG processing required
    RPS->>R: Queue processing job
    RPS->>RPS: Start async processing

    Note over RPS: Document processing begins
```

### 2. LangChain RAG Document Processing Pipeline

```mermaid
sequenceDiagram
    participant RPS as RAG Processing Service
    participant LC as LangChain Framework
    participant S3 as S3/MinIO Storage
    participant Neo4j as Neo4j Graph DB
    participant Qdrant as Qdrant Vector DB
    participant LLM as Language Model
    participant K as Kafka
    participant NS as Notification Service

    RPS->>S3: Download document file
    S3->>RPS: Return file content

    Note over RPS,LC: LangChain Document Loading Phase
    RPS->>LC: Initialize appropriate DocumentLoader
    LC->>LC: PyPDFLoader/Docx2txtLoader/TextLoader
    LC->>RPS: Return Document objects với metadata

    Note over RPS,LC: LangChain Text Splitting Phase
    RPS->>LC: RecursiveCharacterTextSplitter(chunk_size=1000, overlap=200)
    LC->>LC: Split documents into semantic chunks
    LC->>LC: Preserve metadata và source references
    LC->>RPS: Return Document chunks với metadata

    Note over RPS,LC: LangChain Embedding Generation Phase
    RPS->>LC: Initialize embedding model (OpenAI/HuggingFace)
    LC->>LC: Generate embeddings for document chunks
    LC->>LC: Generate document-level embeddings
    LC->>RPS: Return embedding vectors

    Note over RPS,LC: LangChain Entity Extraction Phase
    RPS->>LC: Create LLM-based entity extraction chain
    LC->>LLM: Extract named entities với prompts
    LLM->>LC: Return entities với confidence scores
    LC->>LC: Integrate với spaCy NER for validation
    LC->>RPS: Return structured entity data

    Note over RPS,LC: LangChain Relationship Extraction Phase
    RPS->>LC: Create relationship extraction chain
    LC->>LLM: Identify entity relationships
    LLM->>LC: Return relationship triplets
    LC->>LC: Build knowledge graph structure
    LC->>RPS: Return graph relationships

    Note over RPS,LC: LangChain Vector Store Integration
    par Store in Neo4j với LangChain
        RPS->>LC: Neo4jVector.from_documents()
        LC->>Neo4j: Create Document nodes với embeddings
        LC->>Neo4j: Create Entity nodes
        LC->>Neo4j: Create relationships
        LC->>Neo4j: Create vector indexes
    and Store in Qdrant với LangChain
        RPS->>LC: Qdrant.from_documents()
        LC->>Qdrant: Store chunk embeddings
        LC->>Qdrant: Store entity embeddings
        LC->>Qdrant: Create collection indexes
        LC->>Qdrant: Setup similarity search
    end

    Note over RPS,LC: LangChain RAG Chain Setup
    RPS->>LC: Create RetrievalQA chain
    LC->>LC: Setup retriever với Qdrant + Neo4j
    LC->>LC: Configure prompt templates
    LC->>RPS: Return configured RAG chain

    RPS->>K: Publish RAGProcessingCompleted event
    K->>NS: Send processing completion notification

    Note over RPS: LangChain-powered processing completed
```

### 3. LangChain RAG Retrieval Flow

```mermaid
sequenceDiagram
    participant ACS as AI Content Service
    participant RPS as RAG Processing Service
    participant LC as LangChain Framework
    participant Qdrant as Qdrant Vector DB
    participant Neo4j as Neo4j Graph DB
    participant LLM as Language Model
    participant R as Redis

    ACS->>RPS: gRPC RetrieveContext(query, user_id)

    Note over RPS,LC: LangChain Retrieval Chain Execution
    RPS->>LC: Initialize RetrievalQA chain
    LC->>LC: Load pre-configured retrievers

    Note over LC: Multi-Retriever Strategy
    par Vector Retrieval
        LC->>Qdrant: Similarity search với query embedding
        Qdrant->>LC: Return top-k similar chunks
    and Graph Retrieval
        LC->>Neo4j: Cypher query for entity relationships
        Neo4j->>LC: Return connected entities và paths
    end

    Note over LC: LangChain Context Fusion
    LC->>LC: Combine vector và graph results
    LC->>LC: Rank documents by relevance
    LC->>LC: Apply retrieval filters
    LC->>LC: Deduplicate overlapping content

    Note over LC: LangChain Prompt Construction
    LC->>LC: Format retrieved context
    LC->>LC: Apply prompt template
    LC->>LC: Add system instructions
    LC->>LC: Include entity relationships

    Note over RPS,LC: Optional LLM Enhancement
    alt Enhanced Context Mode
        LC->>LLM: Summarize và enhance context
        LLM->>LC: Return refined context
        LC->>LC: Merge original và enhanced context
    end

    LC->>R: Cache retrieval results (TTL: 1h)
    LC->>RPS: Return enriched context với metadata
    RPS->>ACS: Return LangChain-processed context

    Note over ACS: Use enhanced context for AI generation
```

### 4. Knowledge Graph Construction Flow

```mermaid
sequenceDiagram
    participant RPS as RAG Processing Service
    participant NLP as NLP Pipeline
    participant ML as ML Models
    participant Neo4j as Neo4j Graph DB

    RPS->>NLP: Process document chunks
    
    Note over NLP: Named Entity Recognition
    NLP->>ML: spaCy NER model
    ML->>NLP: Return entities with confidence
    
    Note over NLP: Concept Extraction
    NLP->>ML: Topic modeling (LDA/BERT)
    ML->>NLP: Return topics and concepts
    
    Note over NLP: Relationship Extraction
    NLP->>ML: Dependency parsing
    ML->>NLP: Return syntactic relationships
    
    NLP->>ML: Semantic relationship detection
    ML->>NLP: Return semantic relationships
    
    NLP->>RPS: Return extraction results
    
    Note over RPS: Graph Construction
    RPS->>RPS: Merge duplicate entities
    RPS->>RPS: Resolve entity references
    RPS->>RPS: Filter low-confidence relationships
    RPS->>RPS: Create graph structure
    
    RPS->>Neo4j: Batch insert nodes
    RPS->>Neo4j: Batch insert relationships
    RPS->>Neo4j: Update graph statistics
    
    Note over Neo4j: Knowledge graph ready for queries
```

### 5. LangChain Graph RAG Query Flow

```mermaid
sequenceDiagram
    participant ACS as AI Content Service
    participant RPS as RAG Processing Service
    participant LC as LangChain Framework
    participant Neo4j as Neo4j Graph DB
    participant Qdrant as Qdrant Vector DB
    participant LLM as Language Model

    ACS->>RPS: gRPC GraphRAGQuery(complex_question)

    Note over RPS,LC: LangChain GraphCypherQAChain Setup
    RPS->>LC: Initialize GraphCypherQAChain
    LC->>LC: Load Neo4j graph schema
    LC->>LC: Setup Cypher generation chain

    Note over LC: LangChain Entity Resolution
    LC->>LC: Extract entities from question
    LC->>Qdrant: Vector search for entity disambiguation
    Qdrant->>LC: Return entity candidates
    LC->>LC: Resolve entity references

    Note over LC: LangChain Cypher Generation
    LC->>LLM: Generate Cypher query from question
    LLM->>LC: Return Cypher query
    LC->>LC: Validate và optimize Cypher

    Note over LC: LangChain Graph Traversal
    LC->>Neo4j: Execute Cypher query
    Neo4j->>LC: Return graph results
    LC->>LC: Extract reasoning paths
    LC->>LC: Collect connected entities

    Note over LC: LangChain Context Enhancement
    par Vector Context
        LC->>Qdrant: Search related documents
        Qdrant->>LC: Return supporting documents
    and Graph Context
        LC->>Neo4j: Get relationship explanations
        Neo4j->>LC: Return relationship metadata
    end

    Note over LC: LangChain Answer Generation
    LC->>LC: Combine graph và vector context
    LC->>LC: Format reasoning chain
    LC->>LC: Apply answer generation prompt
    LC->>LLM: Generate final answer với reasoning
    LLM->>LC: Return structured answer

    LC->>RPS: Return answer với reasoning paths
    RPS->>ACS: Return LangChain Graph RAG result

    Note over ACS: Answer includes graph-based reasoning
```

### 6. File Validation và Verification Process

#### **Asset Service File Validation**
```python
async def validate_rag_training_file(file_data: bytes, filename: str, file_type: str) -> ValidationResult:
    """Comprehensive file validation for RAG training documents."""

    validation_result = ValidationResult()

    # 1. Basic file validation
    if len(file_data) == 0:
        validation_result.add_error("File is empty")
        return validation_result

    if len(file_data) > settings.max_file_size:
        validation_result.add_error(f"File too large: {len(file_data)} bytes")
        return validation_result

    # 2. File type validation
    detected_type = detect_file_type(file_data)
    if detected_type != file_type:
        validation_result.add_error(f"File type mismatch: expected {file_type}, got {detected_type}")
        return validation_result

    # 3. File integrity check
    try:
        content = extract_text_content(file_data, file_type)
        if not content or len(content.strip()) < 100:
            validation_result.add_error("File appears to be empty or has insufficient content")
            return validation_result
    except Exception as e:
        validation_result.add_error(f"File corruption detected: {str(e)}")
        return validation_result

    # 4. Content quality check
    content_quality = assess_content_quality(content)
    if content_quality.score < 0.5:
        validation_result.add_warning(f"Low content quality score: {content_quality.score}")
        validation_result.quality_issues = content_quality.issues

    # 5. Malware scan (if enabled)
    if settings.enable_malware_scan:
        scan_result = await scan_for_malware(file_data)
        if scan_result.is_malicious:
            validation_result.add_error(f"Malware detected: {scan_result.threat_type}")
            return validation_result

    # 6. Duplicate detection
    file_hash = calculate_sha256_hash(file_data)
    existing_file = await check_duplicate_file(file_hash)
    if existing_file:
        validation_result.add_info(f"Duplicate file detected: {existing_file.id}")
        validation_result.duplicate_file_id = existing_file.id

    # 7. Language detection
    detected_language = detect_language(content)
    if detected_language not in settings.supported_languages:
        validation_result.add_warning(f"Unsupported language: {detected_language}")

    validation_result.file_hash = file_hash
    validation_result.content_preview = content[:500]  # First 500 chars
    validation_result.word_count = len(content.split())
    validation_result.detected_language = detected_language
    validation_result.is_valid = len(validation_result.errors) == 0

    return validation_result

def assess_content_quality(content: str) -> ContentQuality:
    """Assess the quality of document content for RAG training."""

    quality = ContentQuality()
    issues = []

    # Check for minimum content length
    if len(content) < 1000:
        issues.append("Content too short for effective training")
        quality.score -= 0.3

    # Check for repetitive content
    sentences = content.split('.')
    unique_sentences = set(sentences)
    if len(unique_sentences) / len(sentences) < 0.7:
        issues.append("High repetitive content detected")
        quality.score -= 0.2

    # Check for structured content
    if '\n' not in content and len(content) > 5000:
        issues.append("Lacks paragraph structure")
        quality.score -= 0.1

    # Check for special characters ratio
    special_char_ratio = sum(1 for c in content if not c.isalnum() and c not in ' \n\t.,!?') / len(content)
    if special_char_ratio > 0.1:
        issues.append("High special character ratio - possible encoding issues")
        quality.score -= 0.2

    # Check for meaningful content
    words = content.split()
    avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
    if avg_word_length < 3:
        issues.append("Average word length too short - possible garbage content")
        quality.score -= 0.3

    quality.score = max(0.0, min(1.0, quality.score))  # Clamp between 0 and 1
    quality.issues = issues

    return quality
```

### 7. Server-side Training Decision Logic

#### **Content Management Service - Template Update Logic**
```python
async def update_template(template_id: str, update_data: dict, user_id: str) -> TemplateUpdateResult:
    """Update template và tự động xác định có cần RAG training hay không."""

    # 1. Get existing template
    existing_template = await get_template(template_id)
    if not existing_template:
        raise TemplateNotFoundError(f"Template {template_id} not found")

    # 2. Check user permission
    if existing_template.user_id != user_id:
        raise PermissionDeniedError("User not authorized to update this template")

    # 3. Extract training asset info from update data
    new_training_asset_id = update_data.get('training_asset_id')
    old_training_asset_id = existing_template.training_asset_id

    # 4. Determine if RAG retraining is needed
    retraining_needed = False
    retraining_reason = None

    if new_training_asset_id != old_training_asset_id:
        if new_training_asset_id and old_training_asset_id:
            # Both exist - check if files are different
            old_asset = await get_asset(old_training_asset_id)
            new_asset = await get_asset(new_training_asset_id)

            if old_asset.file_hash != new_asset.file_hash:
                retraining_needed = True
                retraining_reason = "training_file_changed"
            else:
                retraining_reason = "same_file_reused"

        elif new_training_asset_id and not old_training_asset_id:
            # Adding training file for first time
            retraining_needed = True
            retraining_reason = "training_file_added"

        elif not new_training_asset_id and old_training_asset_id:
            # Removing training file
            retraining_needed = True
            retraining_reason = "training_file_removed"

    # 5. Update template record
    updated_template = await update_template_record(
        template_id=template_id,
        update_data=update_data,
        old_training_asset_id=old_training_asset_id,
        new_training_asset_id=new_training_asset_id
    )

    # 6. Publish events based on training decision
    await publish_event("TemplateUpdated", {
        "template_id": template_id,
        "user_id": user_id,
        "changes": update_data.keys(),
        "training_asset_changed": new_training_asset_id != old_training_asset_id,
        "retraining_needed": retraining_needed,
        "retraining_reason": retraining_reason
    })

    if retraining_needed:
        if retraining_reason == "training_file_removed":
            # Cleanup RAG data
            await publish_event("RAGCleanupRequested", {
                "template_id": template_id,
                "old_asset_id": old_training_asset_id,
                "user_id": user_id,
                "cleanup_reason": "training_file_removed"
            })
        else:
            # Trigger retraining
            await publish_event("RAGRetrainingRequested", {
                "template_id": template_id,
                "old_asset_id": old_training_asset_id,
                "new_asset_id": new_training_asset_id,
                "user_id": user_id,
                "workspace_id": updated_template.workspace_id,
                "change_reason": retraining_reason,
                "cleanup_required": old_training_asset_id is not None
            })

    return TemplateUpdateResult(
        template=updated_template,
        retraining_triggered=retraining_needed,
        retraining_reason=retraining_reason
    )

class TemplateUpdateResult:
    def __init__(self, template, retraining_triggered: bool, retraining_reason: str = None):
        self.template = template
        self.retraining_triggered = retraining_triggered
        self.retraining_reason = retraining_reason
```

### 8. RAG Processing Logic cho Template Scenarios

#### **Template Creation Processing**
```python
async def process_template_creation(event: RAGTrainingRequested):
    """Process RAG training when template is created."""

    # 1. Validate template and document
    template = await get_template(event.template_id)
    document = await get_document(event.document_id)

    # 2. Check if document already processed
    existing_rag_data = await check_existing_rag_data(
        document_id=event.document_id,
        user_id=event.user_id
    )

    if existing_rag_data:
        # Link existing RAG data to template
        await link_template_to_rag_data(
            template_id=event.template_id,
            rag_data_id=existing_rag_data.id
        )
        await publish_event("ProcessingCompleted", {
            "template_id": event.template_id,
            "document_id": event.document_id,
            "reused_existing_data": True
        })
        return

    # 3. Process document for RAG
    processing_result = await process_document_for_rag(
        document_path=event.file_path,
        document_type=event.document_type,
        user_id=event.user_id,
        workspace_id=event.workspace_id,
        template_id=event.template_id
    )

    # 4. Store template-specific metadata
    await store_template_rag_metadata(
        template_id=event.template_id,
        document_id=event.document_id,
        processing_result=processing_result
    )

    # 5. Publish completion event
    await publish_event("ProcessingCompleted", {
        "template_id": event.template_id,
        "document_id": event.document_id,
        "chunks_count": processing_result.chunks_count,
        "entities_count": processing_result.entities_count,
        "training_purpose": "template_creation"
    })
```

#### **Asset Service File Comparison Logic**
```python
async def confirm_upload_and_validate(upload_id: str, template_id: Optional[str] = None) -> UploadConfirmationResult:
    """Confirm upload and validate file, with optional template comparison."""

    # 1. Get upload record
    upload_record = await get_pending_upload(upload_id)
    if not upload_record:
        raise UploadNotFoundError(f"Upload {upload_id} not found")

    # 2. Verify file exists in S3
    file_exists = await s3_client.object_exists(upload_record.s3_key)
    if not file_exists:
        raise FileNotFoundError(f"File not found in S3: {upload_record.s3_key}")

    # 3. Download file for validation
    file_data = await s3_client.download_object(upload_record.s3_key)

    # 4. Validate file integrity and content
    validation_result = await validate_rag_training_file(
        file_data=file_data,
        filename=upload_record.filename,
        file_type=upload_record.filetype
    )

    if not validation_result.is_valid:
        # Delete invalid file from S3
        await s3_client.delete_object(upload_record.s3_key)
        await update_upload_status(upload_id, "failed")
        return UploadConfirmationResult(
            success=False,
            validation_result=validation_result
        )

    # 5. Check for template file comparison (if template_id provided)
    file_changed = True
    existing_file_hash = None

    if template_id:
        existing_template = await get_template(template_id)
        if existing_template and existing_template.training_asset_id:
            existing_asset = await get_asset(existing_template.training_asset_id)
            existing_file_hash = existing_asset.file_hash

            if existing_file_hash == validation_result.file_hash:
                # File unchanged - delete duplicate and return
                await s3_client.delete_object(upload_record.s3_key)
                await update_upload_status(upload_id, "duplicate")
                return UploadConfirmationResult(
                    success=True,
                    file_changed=False,
                    existing_file_hash=existing_file_hash,
                    validation_result=validation_result
                )

    # 6. Create asset record for valid file
    asset = await create_asset_record(
        filename=upload_record.filename,
        file_size=len(file_data),
        file_hash=validation_result.file_hash,
        content_type=upload_record.filetype,
        s3_key=upload_record.s3_key,
        user_id=upload_record.user_id,
        workspace_id=upload_record.workspace_id,
        purpose="rag_training",
        validation_result=validation_result
    )

    # 7. Update upload status
    await update_upload_status(upload_id, "completed")

    return UploadConfirmationResult(
        success=True,
        asset_id=asset.id,
        file_changed=file_changed,
        existing_file_hash=existing_file_hash,
        validation_result=validation_result
    )

class UploadConfirmationResult:
    def __init__(self, success: bool, **kwargs):
        self.success = success
        self.asset_id = kwargs.get('asset_id')
        self.file_changed = kwargs.get('file_changed', True)
        self.existing_file_hash = kwargs.get('existing_file_hash')
        self.validation_result = kwargs.get('validation_result')
```

#### **Template Update Processing**
```python
async def process_template_update(event: RAGRetrainingRequested):
    """Process RAG retraining when template is updated."""

    # File comparison already done in Asset Service
    # This event only triggered when file actually changed

    # 1. Validate template and assets
    template = await get_template(event.template_id)
    old_asset = await get_asset(event.old_asset_id)
    new_asset = await get_asset(event.new_asset_id)

    # 2. Cleanup old RAG data if required
    if event.cleanup_required:
        await cleanup_old_rag_data(
            template_id=event.template_id,
            old_asset_id=event.old_asset_id,
            user_id=event.user_id
        )

    # 3. Process new document
    processing_result = await process_document_for_rag(
        asset_id=event.new_asset_id,
        user_id=event.user_id,
        workspace_id=event.workspace_id,
        template_id=event.template_id
    )

    # 4. Update template RAG metadata
    await update_template_rag_metadata(
        template_id=event.template_id,
        old_asset_id=event.old_asset_id,
        new_asset_id=event.new_asset_id,
        processing_result=processing_result
    )

    # 5. Publish completion event
    await publish_event("RetrainingCompleted", {
        "template_id": event.template_id,
        "old_asset_id": event.old_asset_id,
        "new_asset_id": event.new_asset_id,
        "cleanup_completed": event.cleanup_required,
        "chunks_count": processing_result.chunks_count,
        "entities_count": processing_result.entities_count
    })
```

#### **RAG Data Cleanup Logic**
```python
async def cleanup_old_rag_data(
    template_id: str,
    old_document_id: str,
    user_id: str
):
    """Clean up old RAG data when template is updated."""

    # 1. Remove from Neo4j
    await neo4j_client.run_query("""
        MATCH (d:Document {id: $document_id, user_id: $user_id})
        OPTIONAL MATCH (d)-[r]-()
        DELETE r, d
    """, {
        "document_id": old_document_id,
        "user_id": user_id
    })

    # 2. Remove from Qdrant
    await qdrant_client.delete(
        collection_name="documents",
        points_selector={
            "filter": {
                "must": [
                    {"key": "document_id", "match": {"value": old_document_id}},
                    {"key": "user_id", "match": {"value": user_id}}
                ]
            }
        }
    )

    # 3. Clear Redis cache
    cache_keys = [
        f"chunks:{old_document_id}",
        f"entities:{old_document_id}",
        f"embeddings:{old_document_id}:*"
    ]
    await redis_client.delete(*cache_keys)

    # 4. Update processing status
    await update_processing_status(
        document_id=old_document_id,
        status="cleaned_up",
        template_id=template_id
    )

    logger.info(f"Cleaned up RAG data for document {old_document_id}")
```

## 🛠️ LangChain Implementation Details

### LangChain Dependencies
```python
# requirements.txt
langchain==0.1.0
langchain-community==0.0.13
langchain-openai==0.0.5
langchain-huggingface==0.0.1
neo4j==5.15.0
qdrant-client==1.7.0
sentence-transformers==2.2.2
spacy==3.7.2
unstructured==0.11.8
pypdf==3.17.4
python-docx==1.1.0
```

### LangChain Document Processing Implementation
```python
from langchain.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    UnstructuredHTMLLoader,
    UnstructuredPowerPointLoader
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings, HuggingFaceEmbeddings
from langchain.vectorstores import Qdrant
from langchain.graphs import Neo4jGraph
from langchain.chains import RetrievalQA, GraphCypherQAChain
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate

class LangChainRAGProcessor:
    def __init__(self):
        # Initialize LangChain components
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-ada-002",
            openai_api_key=settings.OPENAI_API_KEY
        )

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", " ", ""]
        )

        self.neo4j_graph = Neo4jGraph(
            url=settings.NEO4J_URL,
            username=settings.NEO4J_USERNAME,
            password=settings.NEO4J_PASSWORD
        )

        self.llm = OpenAI(
            temperature=0,
            openai_api_key=settings.OPENAI_API_KEY
        )

    async def process_document(self, file_path: str, document_type: str, user_id: str) -> ProcessingResult:
        """Process document using LangChain pipeline."""

        # 1. Load document với appropriate LangChain loader
        documents = await self._load_document(file_path, document_type)

        # 2. Split documents into chunks
        chunks = self.text_splitter.split_documents(documents)

        # 3. Generate embeddings và store in Qdrant
        vector_store = await self._create_vector_store(chunks, user_id)

        # 4. Extract entities và relationships
        entities, relationships = await self._extract_graph_data(chunks)

        # 5. Store in Neo4j graph
        await self._store_graph_data(entities, relationships, user_id)

        # 6. Setup RAG chains
        rag_chain = await self._setup_rag_chain(vector_store, user_id)
        graph_chain = await self._setup_graph_chain(user_id)

        return ProcessingResult(
            chunks_count=len(chunks),
            entities_count=len(entities),
            relationships_count=len(relationships),
            vector_store=vector_store,
            rag_chain=rag_chain,
            graph_chain=graph_chain
        )

    async def _load_document(self, file_path: str, document_type: str) -> List[Document]:
        """Load document using appropriate LangChain loader."""

        loaders = {
            "pdf": PyPDFLoader,
            "docx": Docx2txtLoader,
            "txt": TextLoader,
            "md": TextLoader,
            "html": UnstructuredHTMLLoader,
            "pptx": UnstructuredPowerPointLoader
        }

        loader_class = loaders.get(document_type.lower())
        if not loader_class:
            raise ValueError(f"Unsupported document type: {document_type}")

        loader = loader_class(file_path)
        documents = loader.load()

        # Add metadata
        for doc in documents:
            doc.metadata.update({
                "source": file_path,
                "document_type": document_type,
                "processed_at": datetime.utcnow().isoformat()
            })

        return documents

    async def _create_vector_store(self, chunks: List[Document], user_id: str) -> Qdrant:
        """Create Qdrant vector store using LangChain."""

        collection_name = f"user_{user_id}_documents"

        # Add user-specific metadata
        for chunk in chunks:
            chunk.metadata.update({
                "user_id": user_id,
                "chunk_id": str(uuid.uuid4())
            })

        vector_store = Qdrant.from_documents(
            documents=chunks,
            embedding=self.embeddings,
            url=settings.QDRANT_URL,
            api_key=settings.QDRANT_API_KEY,
            collection_name=collection_name,
            force_recreate=False
        )

        return vector_store

    async def _extract_graph_data(self, chunks: List[Document]) -> Tuple[List[Entity], List[Relationship]]:
        """Extract entities và relationships using LangChain + LLM."""

        entity_extraction_prompt = PromptTemplate(
            input_variables=["text"],
            template="""
            Extract named entities from the following text.
            Return entities in JSON format with name, type, and confidence score.

            Text: {text}

            Entities:
            """
        )

        relationship_extraction_prompt = PromptTemplate(
            input_variables=["text", "entities"],
            template="""
            Given the text and entities, extract relationships between entities.
            Return relationships in JSON format with source, target, relationship_type, and confidence.

            Text: {text}
            Entities: {entities}

            Relationships:
            """
        )

        entities = []
        relationships = []

        for chunk in chunks:
            # Extract entities
            entity_chain = LLMChain(llm=self.llm, prompt=entity_extraction_prompt)
            entity_result = await entity_chain.arun(text=chunk.page_content)
            chunk_entities = self._parse_entity_result(entity_result)
            entities.extend(chunk_entities)

            # Extract relationships
            if len(chunk_entities) > 1:
                relationship_chain = LLMChain(llm=self.llm, prompt=relationship_extraction_prompt)
                relationship_result = await relationship_chain.arun(
                    text=chunk.page_content,
                    entities=json.dumps([e.dict() for e in chunk_entities])
                )
                chunk_relationships = self._parse_relationship_result(relationship_result)
                relationships.extend(chunk_relationships)

        return entities, relationships

    async def _store_graph_data(self, entities: List[Entity], relationships: List[Relationship], user_id: str):
        """Store entities và relationships in Neo4j using LangChain."""

        # Create entity nodes
        for entity in entities:
            query = """
            MERGE (e:Entity {name: $name, type: $type, user_id: $user_id})
            SET e.confidence = $confidence,
                e.created_at = datetime(),
                e.description = $description
            """
            await self.neo4j_graph.query(query, {
                "name": entity.name,
                "type": entity.type,
                "user_id": user_id,
                "confidence": entity.confidence,
                "description": entity.description
            })

        # Create relationships
        for rel in relationships:
            query = """
            MATCH (source:Entity {name: $source_name, user_id: $user_id})
            MATCH (target:Entity {name: $target_name, user_id: $user_id})
            MERGE (source)-[r:RELATES_TO {type: $rel_type}]->(target)
            SET r.confidence = $confidence,
                r.created_at = datetime()
            """
            await self.neo4j_graph.query(query, {
                "source_name": rel.source,
                "target_name": rel.target,
                "rel_type": rel.relationship_type,
                "user_id": user_id,
                "confidence": rel.confidence
            })

    async def _setup_rag_chain(self, vector_store: Qdrant, user_id: str) -> RetrievalQA:
        """Setup LangChain RetrievalQA chain."""

        retriever = vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5, "filter": {"user_id": user_id}}
        )

        prompt_template = """
        Use the following pieces of context to answer the question at the end.
        If you don't know the answer, just say that you don't know, don't try to make up an answer.

        Context:
        {context}

        Question: {question}
        Answer:
        """

        prompt = PromptTemplate(
            template=prompt_template,
            input_variables=["context", "question"]
        )

        rag_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=retriever,
            chain_type_kwargs={"prompt": prompt},
            return_source_documents=True
        )

        return rag_chain

    async def _setup_graph_chain(self, user_id: str) -> GraphCypherQAChain:
        """Setup LangChain GraphCypherQAChain."""

        # Filter graph to user's data only
        self.neo4j_graph.refresh_schema()

        graph_chain = GraphCypherQAChain.from_llm(
            llm=self.llm,
            graph=self.neo4j_graph,
            verbose=True,
            return_intermediate_steps=True,
            cypher_prompt=PromptTemplate(
                input_variables=["schema", "question"],
                template="""
                Task: Generate Cypher statement to query a graph database.
                Instructions:
                Use only the provided relationship types and properties in the schema.
                Do not use any other relationship types or properties that are not provided.
                Always filter by user_id = '{user_id}' in your queries.

                Schema:
                {{schema}}

                Note: Do not include any explanations or apologies in your responses.
                Do not respond to any questions that might ask anything else than for you to construct a Cypher statement.
                Do not include any text except the generated Cypher statement.

                Question: {{question}}
                """.format(user_id=user_id)
            )
        )

        return graph_chain
```

### LangChain Processing Configuration
```yaml
langchain_config:
  # Document Loaders
  document_loaders:
    pdf:
      loader_class: "PyPDFLoader"
      extract_images: false

    docx:
      loader_class: "Docx2txtLoader"

    html:
      loader_class: "UnstructuredHTMLLoader"
      mode: "elements"

    pptx:
      loader_class: "UnstructuredPowerPointLoader"
      mode: "elements"

  # Text Splitters
  text_splitters:
    recursive_character:
      chunk_size: 1000
      chunk_overlap: 200
      separators: ["\n\n", "\n", " ", ""]
      keep_separator: true

    semantic_chunker:
      buffer_size: 1
      breakpoint_threshold_type: "percentile"

    markdown_header:
      headers_to_split_on: [
        ("#", "Header 1"),
        ("##", "Header 2"),
        ("###", "Header 3")
      ]

  # Embeddings
  embeddings:
    openai:
      model: "text-embedding-ada-002"
      dimensions: 1536

    huggingface:
      model_name: "sentence-transformers/all-MiniLM-L6-v2"
      model_kwargs: {"device": "cpu"}
      encode_kwargs: {"normalize_embeddings": true}

    sentence_transformers:
      model_name: "all-mpnet-base-v2"
      dimensions: 768

  # Vector Stores
  vector_stores:
    qdrant:
      url: "${QDRANT_URL}"
      api_key: "${QDRANT_API_KEY}"
      collection_config:
        vectors:
          size: 1536
          distance: "Cosine"
        optimizers_config:
          default_segment_number: 2
          max_segment_size: 20000

    neo4j_vector:
      url: "${NEO4J_URL}"
      username: "${NEO4J_USERNAME}"
      password: "${NEO4J_PASSWORD}"
      index_name: "vector_index"
      node_label: "Document"
      text_node_property: "content"
      embedding_node_property: "embedding"

  # Graph Database
  neo4j_graph:
    url: "${NEO4J_URL}"
    username: "${NEO4J_USERNAME}"
    password: "${NEO4J_PASSWORD}"
    database: "neo4j"

  # LLM Configuration
  llms:
    openai:
      model_name: "gpt-3.5-turbo"
      temperature: 0
      max_tokens: 1000

    openai_gpt4:
      model_name: "gpt-4"
      temperature: 0
      max_tokens: 2000

  # Chain Configuration
  chains:
    retrieval_qa:
      chain_type: "stuff"
      return_source_documents: true

    graph_cypher_qa:
      return_intermediate_steps: true
      top_k: 10

    conversational_retrieval:
      return_source_documents: true
      max_tokens_limit: 3000

  # Entity Extraction
  entity_extraction:
    llm_based:
      enabled: true
      model: "gpt-3.5-turbo"
      confidence_threshold: 0.8
      max_entities_per_chunk: 50

    spacy_integration:
      enabled: true
      model: "en_core_web_sm"
      entity_types: ["PERSON", "ORG", "GPE", "PRODUCT", "EVENT"]

    custom_prompts:
      entity_prompt: |
        Extract named entities from the following text.
        Focus on: people, organizations, locations, products, concepts.
        Return as JSON array with name, type, confidence (0-1).

        Text: {text}

        Entities:

      relationship_prompt: |
        Given entities and text, extract relationships.
        Return as JSON array with source, target, type, confidence.

        Text: {text}
        Entities: {entities}

        Relationships:

  # Graph Construction
  graph_construction:
    max_depth: 3
    min_relationship_strength: 0.6
    entity_merge_threshold: 0.9
    batch_size: 1000

    node_types:
      - "Document"
      - "Entity"
      - "Concept"
      - "Topic"

    relationship_types:
      - "MENTIONS"
      - "RELATES_TO"
      - "PART_OF"
      - "SIMILAR_TO"
      - "CONTAINS"
```

### LangChain RAG Retrieval Implementation
```python
from langchain.retrievers import EnsembleRetriever
from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain.retrievers.contextual_compression import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor

class LangChainRAGRetriever:
    def __init__(self, vector_store: Qdrant, graph: Neo4jGraph, llm):
        self.vector_store = vector_store
        self.graph = graph
        self.llm = llm
        self.setup_retrievers()

    def setup_retrievers(self):
        """Setup multiple LangChain retrievers for ensemble retrieval."""

        # 1. Vector similarity retriever
        self.vector_retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5}
        )

        # 2. Multi-query retriever (generates multiple queries)
        self.multi_query_retriever = MultiQueryRetriever.from_llm(
            retriever=self.vector_retriever,
            llm=self.llm
        )

        # 3. Contextual compression retriever
        compressor = LLMChainExtractor.from_llm(self.llm)
        self.compression_retriever = ContextualCompressionRetriever(
            base_compressor=compressor,
            base_retriever=self.vector_retriever
        )

        # 4. Ensemble retriever (combines multiple retrievers)
        self.ensemble_retriever = EnsembleRetriever(
            retrievers=[self.vector_retriever, self.multi_query_retriever],
            weights=[0.6, 0.4]
        )

    async def retrieve_context(self, query: str, user_id: str, retrieval_strategy: str = "ensemble") -> Dict:
        """Retrieve context using specified LangChain strategy."""

        # Add user filter to query
        filtered_query = f"user_id:{user_id} {query}"

        if retrieval_strategy == "vector":
            docs = await self.vector_retriever.aget_relevant_documents(filtered_query)
        elif retrieval_strategy == "multi_query":
            docs = await self.multi_query_retriever.aget_relevant_documents(filtered_query)
        elif retrieval_strategy == "compression":
            docs = await self.compression_retriever.aget_relevant_documents(filtered_query)
        elif retrieval_strategy == "ensemble":
            docs = await self.ensemble_retriever.aget_relevant_documents(filtered_query)
        else:
            docs = await self.vector_retriever.aget_relevant_documents(filtered_query)

        # Enhance with graph context
        graph_context = await self._get_graph_context(query, user_id)

        return {
            "documents": docs,
            "graph_context": graph_context,
            "retrieval_strategy": retrieval_strategy,
            "total_docs": len(docs)
        }

    async def _get_graph_context(self, query: str, user_id: str) -> Dict:
        """Get additional context from Neo4j graph."""

        # Extract entities from query
        entity_extraction_query = f"""
        CALL apoc.nlp.gcp.entities.stream([{{text: '{query}'}}], {{
            key: $api_key,
            nodeProperty: 'entities'
        }}) YIELD value
        RETURN value.entities as entities
        """

        # Find related entities in user's graph
        related_entities_query = f"""
        MATCH (e:Entity {{user_id: '{user_id}'}})
        WHERE e.name CONTAINS $query_term
        MATCH (e)-[r:RELATES_TO*1..2]-(related:Entity)
        RETURN e, r, related
        LIMIT 20
        """

        try:
            results = await self.graph.query(related_entities_query, {"query_term": query})
            return {
                "related_entities": results,
                "entity_count": len(results)
            }
        except Exception as e:
            logger.error(f"Graph context retrieval failed: {e}")
            return {"related_entities": [], "entity_count": 0}

class LangChainGraphRAGChain:
    def __init__(self, graph: Neo4jGraph, vector_store: Qdrant, llm):
        self.graph = graph
        self.vector_store = vector_store
        self.llm = llm
        self.setup_chains()

    def setup_chains(self):
        """Setup LangChain Graph RAG chains."""

        # 1. Graph Cypher QA Chain
        self.graph_qa_chain = GraphCypherQAChain.from_llm(
            llm=self.llm,
            graph=self.graph,
            verbose=True,
            return_intermediate_steps=True
        )

        # 2. Custom Graph + Vector hybrid chain
        self.hybrid_chain = self._create_hybrid_chain()

    def _create_hybrid_chain(self):
        """Create custom hybrid chain combining graph và vector retrieval."""

        from langchain.chains.base import Chain
        from langchain.schema import BaseRetriever

        class GraphVectorHybridChain(Chain):
            def __init__(self, graph_chain, vector_retriever, llm):
                super().__init__()
                self.graph_chain = graph_chain
                self.vector_retriever = vector_retriever
                self.llm = llm

            @property
            def input_keys(self):
                return ["question", "user_id"]

            @property
            def output_keys(self):
                return ["answer", "graph_context", "vector_context", "reasoning"]

            def _call(self, inputs):
                question = inputs["question"]
                user_id = inputs["user_id"]

                # Get graph context
                graph_result = self.graph_chain.run(question)

                # Get vector context
                vector_docs = self.vector_retriever.get_relevant_documents(question)

                # Combine contexts
                combined_context = self._combine_contexts(graph_result, vector_docs)

                # Generate final answer
                final_prompt = f"""
                Based on the following graph and vector contexts, answer the question.

                Graph Context: {graph_result}
                Vector Context: {[doc.page_content for doc in vector_docs]}

                Question: {question}

                Provide a comprehensive answer with reasoning:
                """

                answer = self.llm.predict(final_prompt)

                return {
                    "answer": answer,
                    "graph_context": graph_result,
                    "vector_context": vector_docs,
                    "reasoning": "Combined graph and vector retrieval"
                }

            def _combine_contexts(self, graph_result, vector_docs):
                # Logic to intelligently combine graph và vector contexts
                return {
                    "graph": graph_result,
                    "vector": [doc.page_content for doc in vector_docs]
                }

        return GraphVectorHybridChain(
            graph_chain=self.graph_qa_chain,
            vector_retriever=self.vector_store.as_retriever(),
            llm=self.llm
        )

    async def query(self, question: str, user_id: str, chain_type: str = "hybrid") -> Dict:
        """Execute Graph RAG query using specified chain."""

        if chain_type == "graph_only":
            result = await self.graph_qa_chain.arun(question)
            return {"answer": result, "chain_type": "graph_only"}

        elif chain_type == "hybrid":
            result = await self.hybrid_chain.acall({
                "question": question,
                "user_id": user_id
            })
            return {**result, "chain_type": "hybrid"}

        else:
            raise ValueError(f"Unknown chain type: {chain_type}")
```

### Database Schemas

#### Neo4j Cypher Schemas với LangChain Integration
```cypher
// Create constraints
CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE;
CREATE CONSTRAINT entity_name_type IF NOT EXISTS FOR (e:Entity) REQUIRE (e.name, e.type) IS UNIQUE;
CREATE CONSTRAINT concept_name IF NOT EXISTS FOR (c:Concept) REQUIRE c.name IS UNIQUE;

// Create indexes
CREATE INDEX document_user IF NOT EXISTS FOR (d:Document) ON (d.user_id);
CREATE INDEX document_workspace IF NOT EXISTS FOR (d:Document) ON (d.workspace_id);
CREATE INDEX entity_type IF NOT EXISTS FOR (e:Entity) ON (e.type);
CREATE FULLTEXT INDEX document_content IF NOT EXISTS FOR (d:Document) ON EACH [d.title, d.content];
CREATE FULLTEXT INDEX entity_search IF NOT EXISTS FOR (e:Entity) ON EACH [e.name, e.description];

// Sample queries
MATCH (d:Document)-[:MENTIONS]->(e:Entity)-[:RELATES_TO]->(e2:Entity)
WHERE d.user_id = $user_id
RETURN d, e, e2 LIMIT 100;

MATCH path = (e1:Entity)-[:RELATES_TO*1..3]-(e2:Entity)
WHERE e1.name = $entity_name
RETURN path ORDER BY length(path) LIMIT 50;
```

#### Qdrant Collection Configurations
```python
collections_config = {
    "documents": {
        "vectors": {
            "size": 384,
            "distance": "Cosine"
        },
        "payload_schema": {
            "document_id": "keyword",
            "chunk_id": "keyword",
            "user_id": "keyword",
            "workspace_id": "keyword",
            "content": "text",
            "metadata": "object"
        },
        "optimizers_config": {
            "default_segment_number": 2,
            "max_segment_size": 20000
        }
    },
    "entities": {
        "vectors": {
            "size": 384,
            "distance": "Cosine"
        },
        "payload_schema": {
            "entity_name": "keyword",
            "entity_type": "keyword",
            "confidence": "float",
            "graph_id": "keyword"
        }
    }
}
```

### 8. Asset Service API Endpoints cho RAG Files

#### **Request Upload (Step 1)**
```yaml
POST /assets/request-upload
Content-Type: application/json

Request:
  filename: string
  filesize: integer
  filetype: string
  purpose: "rag_training"
  user_id: string
  workspace_id: string (optional)
  template_id: string (optional, for updates)

Response:
  upload_id: string
  presigned_url: string
  s3_key: string
  expires_at: datetime
  max_file_size: integer
  allowed_types: array
```

#### **Confirm Upload và Validate (Step 2)**
```yaml
POST /assets/confirm-upload/{upload_id}
Content-Type: application/json

Request:
  upload_id: string

Response:
  asset_id: string
  filename: string
  file_size: integer
  file_hash: string
  content_type: string
  validation_status: "valid" | "invalid" | "warning" | "duplicate"
  validation_errors: array
  validation_warnings: array
  content_preview: string
  word_count: integer
  detected_language: string
  duplicate_file_id: string (optional)
  quality_score: float
  created_at: datetime

  # For template updates
  file_changed: boolean (if template_id provided)
  existing_file_hash: string (if template_id provided)
```

#### **File Validation Status Check**
```yaml
GET /assets/{asset_id}/validation

Response:
  asset_id: string
  validation_status: "valid" | "invalid" | "warning"
  validation_errors: array
  validation_warnings: array
  quality_score: float
  content_metrics:
    word_count: integer
    character_count: integer
    paragraph_count: integer
    detected_language: string
    readability_score: float
  file_info:
    size: integer
    hash: string
    content_type: string
    last_modified: datetime
```

#### **Internal Template File Management (Server-side only)**
```yaml
# User không có quyền truy cập trực tiếp vào training file
# Server tự động xác định có training hay không khi template update

# Internal API cho Content Management Service
GET /internal/templates/{template_id}/training-asset
Authorization: Internal-Service-Token

Response:
  asset_id: string
  file_hash: string
  validation_status: string
  last_processed: datetime
  processing_status: string

# Template update với asset reference (user chỉ cung cấp asset_id)
PUT /templates/{template_id}
Request:
  name: string
  description: string
  content: object
  training_asset_id: string  # Optional - reference to pre-uploaded file

Response:
  template_id: string
  success: boolean
  # Server không expose training file details cho user
  retraining_triggered: boolean  # Chỉ thông báo có trigger retraining hay không
```

## 🔄 Integration với Other Services

### LangChain gRPC Service Definitions
```protobuf
// LangChain RAG Processing Service
service LangChainRAGService {
  rpc ProcessDocument(ProcessDocumentRequest) returns (ProcessDocumentResponse);
  rpc GetProcessingStatus(ProcessingStatusRequest) returns (ProcessingStatusResponse);
  rpc RetrieveContext(ContextRetrievalRequest) returns (ContextRetrievalResponse);
  rpc GraphRAGQuery(GraphQueryRequest) returns (GraphQueryResponse);
  rpc HybridRAGQuery(HybridQueryRequest) returns (HybridQueryResponse);
  rpc SearchSimilarDocuments(SimilaritySearchRequest) returns (SimilaritySearchResponse);
  rpc GetAvailableChains(GetChainsRequest) returns (GetChainsResponse);
  rpc ExecuteCustomChain(CustomChainRequest) returns (CustomChainResponse);
}

message ProcessDocumentRequest {
  string document_id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string file_path = 4;
  string document_type = 5;
  LangChainProcessingOptions processing_options = 6;
}

message LangChainProcessingOptions {
  string text_splitter_type = 1; // "recursive_character", "semantic", "markdown_header"
  int32 chunk_size = 2;
  int32 chunk_overlap = 3;
  string embedding_model = 4; // "openai", "huggingface", "sentence_transformers"
  string llm_model = 5; // "gpt-3.5-turbo", "gpt-4"
  bool enable_graph_construction = 6;
  bool enable_entity_extraction = 7;
  repeated string entity_types = 8;
  map<string, string> custom_options = 9;
}

message ContextRetrievalRequest {
  string query = 1;
  string user_id = 2;
  string workspace_id = 3;
  string retrieval_strategy = 4; // "vector", "multi_query", "compression", "ensemble"
  int32 max_chunks = 5;
  float similarity_threshold = 6;
  repeated string entity_types = 7;
  bool include_graph_context = 8;
  map<string, string> retrieval_filters = 9;
}

message ContextRetrievalResponse {
  bool success = 1;
  repeated DocumentChunk chunks = 2;
  GraphContext graph_context = 3;
  RetrievalMetadata metadata = 4;
  string error_message = 5;
}

message DocumentChunk {
  string chunk_id = 1;
  string content = 2;
  map<string, string> metadata = 3;
  float similarity_score = 4;
  string source_document = 5;
}

message GraphContext {
  repeated EntityNode entities = 2;
  repeated RelationshipEdge relationships = 3;
  repeated string reasoning_paths = 4;
  int32 total_entities = 5;
}

message EntityNode {
  string entity_id = 1;
  string name = 2;
  string type = 3;
  float confidence = 4;
  map<string, string> properties = 5;
}

message RelationshipEdge {
  string source_entity = 1;
  string target_entity = 2;
  string relationship_type = 3;
  float confidence = 4;
  map<string, string> properties = 5;
}

message GraphQueryRequest {
  string question = 1;
  string user_id = 2;
  string chain_type = 3; // "graph_only", "hybrid"
  repeated string focus_entities = 4;
  int32 max_hops = 5;
  bool include_reasoning_path = 6;
  bool return_cypher_query = 7;
  map<string, string> chain_options = 8;
}

message GraphQueryResponse {
  bool success = 1;
  string answer = 2;
  GraphContext graph_context = 3;
  repeated DocumentChunk supporting_documents = 4;
  string cypher_query = 5; // If requested
  repeated string reasoning_steps = 6;
  QueryMetadata metadata = 7;
  string error_message = 8;
}

message HybridQueryRequest {
  string question = 1;
  string user_id = 2;
  string workspace_id = 3;
  float graph_weight = 4; // 0.0 to 1.0, weight for graph vs vector
  string retrieval_strategy = 5;
  int32 max_context_length = 6;
  bool include_source_attribution = 7;
  map<string, string> hybrid_options = 8;
}

message HybridQueryResponse {
  bool success = 1;
  string answer = 2;
  GraphContext graph_context = 3;
  repeated DocumentChunk vector_context = 4;
  string reasoning_explanation = 5;
  repeated SourceAttribution sources = 6;
  QueryMetadata metadata = 7;
  string error_message = 8;
}

message SourceAttribution {
  string source_type = 1; // "graph", "vector", "hybrid"
  string source_id = 2;
  string content_snippet = 3;
  float contribution_score = 4;
}

message RetrievalMetadata {
  string retrieval_strategy = 1;
  int32 total_chunks_found = 2;
  float avg_similarity_score = 3;
  int32 processing_time_ms = 4;
  bool used_cache = 5;
  map<string, string> debug_info = 6;
}

message QueryMetadata {
  string chain_type = 1;
  int32 total_processing_time_ms = 2;
  int32 graph_query_time_ms = 3;
  int32 vector_search_time_ms = 4;
  int32 llm_generation_time_ms = 5;
  bool used_cache = 6;
  map<string, string> performance_metrics = 7;
}

message GetChainsRequest {
  string user_id = 1;
  string workspace_id = 2;
}

message GetChainsResponse {
  bool success = 1;
  repeated ChainInfo available_chains = 2;
  string error_message = 3;
}

message ChainInfo {
  string chain_id = 1;
  string chain_name = 2;
  string chain_type = 3; // "retrieval_qa", "graph_cypher_qa", "hybrid"
  string description = 4;
  repeated string supported_features = 5;
  map<string, string> configuration = 6;
}

message CustomChainRequest {
  string chain_id = 1;
  string user_id = 2;
  string input_text = 3;
  map<string, string> chain_parameters = 4;
}

message CustomChainResponse {
  bool success = 1;
  string output = 2;
  map<string, string> intermediate_steps = 3;
  QueryMetadata metadata = 4;
  string error_message = 5;
}
```

### Kafka Event Integration
```yaml
kafka_events:
  consumed_topics:
    - topic: "asset.events"
      events:
        - DocumentUploaded
        - DocumentDeleted
        - DocumentUpdated
    - topic: "content.events"
      events:
        - TemplateCreated
        - TemplateUpdated
        - RAGTrainingRequested
        - RAGRetrainingRequested

  produced_topics:
    - topic: "rag.events"
      events:
        - ProcessingStarted
        - ProcessingCompleted
        - ProcessingFailed
        - RetrainingStarted
        - RetrainingCompleted
        - KnowledgeGraphUpdated
        - EmbeddingsGenerated
        - OldDataCleanedUp

event_schemas:
  RAGTrainingRequested:
    template_id: string
    asset_id: string  # Reference to pre-uploaded and validated file
    user_id: string
    workspace_id: string
    file_path: string
    document_type: string
    training_purpose: string  # "template_creation", "template_update", "direct_upload"
    priority: integer
    file_validation:
      validation_status: string  # "valid", "invalid", "warning"
      file_hash: string
      content_preview: string
      word_count: integer
      quality_score: float
    metadata: object

  RAGRetrainingRequested:
    template_id: string
    old_asset_id: string
    new_asset_id: string
    user_id: string
    workspace_id: string
    old_file_path: string
    new_file_path: string
    change_reason: string  # "file_content_changed", "file_replaced"
    cleanup_required: boolean
    file_validation:
      validation_status: string
      file_hash: string
      content_preview: string
      word_count: integer
      quality_score: float
    metadata: object

  ProcessingCompleted:
    document_id: string
    template_id: string  # optional
    user_id: string
    workspace_id: string
    chunks_count: integer
    entities_count: integer
    relationships_count: integer
    processing_time: float
    training_purpose: string
    metadata: object

  RetrainingCompleted:
    template_id: string
    old_document_id: string
    new_document_id: string
    user_id: string
    workspace_id: string
    cleanup_completed: boolean
    chunks_count: integer
    entities_count: integer
    relationships_count: integer
    processing_time: float
    metadata: object
```

## 📈 Performance Optimization

### Caching Strategy
```yaml
caching:
  redis_layers:
    embeddings_cache:
      ttl: 86400  # 24 hours
      key_pattern: "emb:{model}:{hash}"
      
    query_results_cache:
      ttl: 3600   # 1 hour
      key_pattern: "query:{user_id}:{query_hash}"
      
    entity_cache:
      ttl: 43200  # 12 hours
      key_pattern: "entity:{name}:{type}"
      
  processing_cache:
    document_chunks:
      ttl: 7200   # 2 hours
      key_pattern: "chunks:{doc_id}"
```

### Batch Processing Optimization
```yaml
batch_processing:
  embedding_generation:
    batch_size: 32
    max_concurrent_batches: 4
    timeout_per_batch: 30
    
  neo4j_operations:
    batch_size: 1000
    transaction_timeout: 60
    retry_attempts: 3
    
  qdrant_operations:
    batch_size: 100
    parallel_uploads: 8
    connection_pool_size: 10
```

### 9. Security và Permission Management

#### **User Permission Restrictions**
```yaml
# User KHÔNG có quyền:
forbidden_actions:
  - Direct access to training files via API
  - Manual trigger RAG retraining
  - Access to RAG processing status details
  - Modify RAG data directly
  - Access internal training file metadata

# User CHỈ có quyền:
allowed_actions:
  - Upload files for training purpose
  - Create/update templates with training_asset_id reference
  - View template basic info (không bao gồm training file details)
  - Receive notification về training completion
```

#### **Server-side Decision Making**
```python
# Content Management Service tự động quyết định:
automatic_decisions = {
    "training_needed": "Server compare asset IDs và file hashes",
    "cleanup_required": "Server xác định khi nào cần cleanup old data",
    "retraining_trigger": "Server tự động trigger based on file changes",
    "permission_check": "Server verify user ownership của template",
    "asset_validation": "Server verify asset exists và user có quyền access"
}

# User chỉ cung cấp:
user_input = {
    "template_data": "Template content và metadata",
    "training_asset_id": "Reference to pre-uploaded file (optional)"
}

# Server response chỉ bao gồm:
server_response = {
    "template_id": "Created/updated template ID",
    "success": "Operation success status",
    "retraining_triggered": "Boolean - có trigger training hay không",
    "message": "User-friendly message về training status"
}
```

#### **API Security Patterns**
```yaml
# Public APIs (User accessible)
POST /templates
PUT /templates/{id}
GET /templates/{id}
DELETE /templates/{id}

# Internal APIs (Service-to-service only)
GET /internal/templates/{id}/training-asset
POST /internal/rag/trigger-training
DELETE /internal/rag/cleanup-data

# Asset APIs (Controlled access)
POST /assets/request-upload  # User can upload
POST /assets/confirm-upload/{id}  # User can confirm
GET /assets/{id}/metadata  # User can view own assets only
```

## 🚀 Deployment và Monitoring

### Docker Configuration
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY . .

# Expose ports
EXPOSE 8000 50051

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["python", "-m", "app.main"]
```

### Monitoring Metrics
```yaml
prometheus_metrics:
  processing_metrics:
    - documents_processed_total
    - processing_duration_seconds
    - processing_errors_total
    - chunks_generated_total
    - entities_extracted_total
    - relationships_created_total
    
  database_metrics:
    - neo4j_query_duration_seconds
    - qdrant_search_duration_seconds
    - database_connection_pool_size
    - cache_hit_rate
    
  system_metrics:
    - cpu_usage_percent
    - memory_usage_bytes
    - disk_usage_bytes
    - active_processing_jobs
```

## 🎯 Benefits của RAG Processing Service

### **Enhanced AI Generation:**
- **Contextual Knowledge:** AI có access đến knowledge graph và vector embeddings
- **Multi-hop Reasoning:** Graph RAG cho phép reasoning phức tạp qua relationships
- **Semantic Search:** Vector search cho relevant context retrieval
- **Domain Expertise:** Training data được structured thành knowledge base

### **Scalable Architecture:**
- **Independent Processing:** Async document processing không block user interactions
- **Distributed Storage:** Neo4j và Qdrant scale independently
- **Batch Optimization:** Efficient batch processing cho large documents
- **Caching Strategy:** Multi-layer caching cho performance

### **Flexible Integration:**
- **gRPC APIs:** High-performance integration với AI Content Service
- **Event-driven:** Kafka events cho loose coupling
- **Multi-format Support:** PDF, DOCX, TXT, MD, HTML, PPTX
- **User Isolation:** Per-user và per-workspace data separation

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// PublishedPost holds the schema definition for the PublishedPost entity.
type PublishedPost struct {
	ent.Schema
}

// Fields of the PublishedPost.
func (PublishedPost) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who published the post"),
		field.String("post_id").
			MaxLen(36).
			Comment("Original post ID from content management service"),
		field.String("integration_id").
			MaxLen(36).
			Comment("Platform integration ID used for publishing"),
		field.Enum("platform").
			Values("facebook", "instagram", "twitter", "linkedin", "tiktok", "youtube", "pinterest", "snapchat").
			Comment("Platform where the post was published"),
		field.String("platform_post_id").
			MaxLen(200).
			Comment("Post ID on the platform"),
		field.String("platform_post_url").
			MaxLen(1000).
			Optional().
			Comment("URL of the post on the platform"),
		field.Text("content").
			Comment("Content that was published"),
		field.JSON("media_urls", []string{}).
			Optional().
			Comment("Media URLs that were published"),
		field.JSON("hashtags", []string{}).
			Optional().
			Comment("Hashtags used in the post"),
		field.Enum("status").
			Values("pending", "published", "failed", "deleted", "scheduled").
			Default("pending").
			Comment("Publishing status"),
		field.String("error_message").
			MaxLen(1000).
			Optional().
			Comment("Error message if publishing failed"),
		field.Time("scheduled_at").
			Optional().
			Comment("When the post was scheduled to be published"),
		field.Time("published_at").
			Optional().
			Comment("When the post was actually published"),
		field.JSON("platform_response", map[string]interface{}{}).
			Optional().
			Comment("Response from the platform API"),
		field.JSON("analytics", map[string]interface{}{}).
			Optional().
			Comment("Post analytics data"),
		field.Time("last_analytics_sync").
			Optional().
			Comment("Last time analytics were synced"),
		field.Int("retry_count").
			Default(0).
			Comment("Number of retry attempts"),
		field.Time("next_retry_at").
			Optional().
			Comment("When to retry publishing if failed"),
		field.JSON("publish_options", map[string]interface{}{}).
			Optional().
			Comment("Platform-specific publishing options"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
		field.Time("deleted_at").
			Optional(),
	}
}

// Edges of the PublishedPost.
func (PublishedPost) Edges() []ent.Edge {
	return nil
}

// Indexes of the PublishedPost.
func (PublishedPost) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("post_id"),
		index.Fields("integration_id"),
		index.Fields("platform"),
		index.Fields("platform_post_id"),
		index.Fields("status"),
		index.Fields("scheduled_at"),
		index.Fields("published_at"),
		index.Fields("next_retry_at"),
		index.Fields("created_at"),
		index.Fields("deleted_at"),
		index.Fields("user_id", "platform"),
		index.Fields("post_id", "platform"),
		index.Fields("platform", "platform_post_id").Unique(),
	}
}

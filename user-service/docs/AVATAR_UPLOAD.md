# Avatar Upload via Asset Service

## Overview

User avatars are now uploaded through the Asset Service using a two-step process with presigned URLs. This ensures secure, scalable file uploads to S3 with proper validation and processing.

## Architecture

```mermaid
sequenceDiagram
    participant Frontend
    participant UserService
    participant AssetService
    participant S3
    participant Database

    Frontend->>UserService: POST /api/v1/users/me/avatar/upload-url
    UserService->>AssetService: RequestUpload (gRPC)
    AssetService->>S3: Generate presigned URL
    S3-->>AssetService: Presigned URL + metadata
    AssetService-->>UserService: Upload response
    UserService-->>Frontend: Upload URL + instructions

    Frontend->>S3: PUT file to presigned URL
    S3-->>Frontend: Upload success + ETag

    Frontend->>UserService: POST /api/v1/users/me/avatar/confirm
    UserService->>AssetService: ConfirmUpload (gRPC)
    AssetService->>AssetService: Validate file
    AssetService->>Database: Save asset metadata
    AssetService-->>UserService: Asset info + URLs
    UserService->>Database: Update user.avatar_url
    UserService-->>Frontend: Avatar URLs
```

## API Endpoints

### 1. Request Avatar Upload URL

**Endpoint:** `POST /api/v1/users/me/avatar/upload-url`

**Request:**
```json
{
  "file_name": "avatar.jpg",
  "file_type": "image/jpeg",
  "file_size": 1048576
}
```

**Response:**
```json
{
  "upload_id": "upload_123456",
  "presigned_url": "https://s3.amazonaws.com/bucket/key?signature=...",
  "s3_key": "avatars/user_123/avatar_456.jpg",
  "s3_bucket": "social-content-ai-assets",
  "expires_at": "2023-12-01T11:00:00Z",
  "instructions": {
    "method": "PUT",
    "url": "https://s3.amazonaws.com/bucket/key?signature=...",
    "max_file_size": 5242880,
    "allowed_types": ["image/jpeg", "image/png", "image/gif", "image/webp"]
  }
}
```

### 2. Upload File to S3

**Frontend uploads directly to S3 using presigned URL:**

```javascript
const response = await fetch(presignedURL, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': file.type
  }
});

const etag = response.headers.get('ETag');
```

### 3. Confirm Avatar Upload

**Endpoint:** `POST /api/v1/users/me/avatar/confirm`

**Request:**
```json
{
  "upload_id": "upload_123456",
  "etag": "d41d8cd98f00b204e9800998ecf8427e"
}
```

**Response:**
```json
{
  "asset_id": "asset_123456",
  "status": "validated",
  "avatar_url": "https://cdn.social-content-ai.com/avatars/user_123/avatar_456.jpg",
  "thumbnail_url": "https://cdn.social-content-ai.com/avatars/user_123/thumb_456.jpg",
  "message": "Avatar uploaded and validated successfully"
}
```

### 4. Get Current Avatar

**Endpoint:** `GET /api/v1/users/me/avatar`

**Response:**
```json
{
  "asset_id": "asset_123456",
  "avatar_url": "https://cdn.social-content-ai.com/avatars/user_123/avatar_456.jpg",
  "thumbnail_url": "https://cdn.social-content-ai.com/avatars/user_123/thumb_456.jpg",
  "file_size": 1048576,
  "file_type": "image/jpeg",
  "status": "ready",
  "is_valid": true,
  "uploaded_at": "2023-12-01T10:00:00Z"
}
```

### 5. Delete Current Avatar

**Endpoint:** `DELETE /api/v1/users/me/avatar`

**Response:**
```json
{
  "message": "Avatar deleted successfully"
}
```

## Frontend Implementation

### React/JavaScript Example

```javascript
class AvatarUpload {
  async uploadAvatar(file) {
    try {
      // Step 1: Request upload URL
      const uploadRequest = {
        file_name: file.name,
        file_type: file.type,
        file_size: file.size
      };

      const uploadResponse = await fetch('/api/v1/users/me/avatar/upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(uploadRequest)
      });

      const uploadData = await uploadResponse.json();

      // Step 2: Upload to S3
      const s3Response = await fetch(uploadData.presigned_url, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      });

      if (!s3Response.ok) {
        throw new Error('Failed to upload to S3');
      }

      const etag = s3Response.headers.get('ETag');

      // Step 3: Confirm upload
      const confirmRequest = {
        upload_id: uploadData.upload_id,
        etag: etag.replace(/"/g, '') // Remove quotes from ETag
      };

      const confirmResponse = await fetch('/api/v1/users/me/avatar/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(confirmRequest)
      });

      const confirmData = await confirmResponse.json();
      return confirmData;

    } catch (error) {
      console.error('Avatar upload failed:', error);
      throw error;
    }
  }
}
```

## File Validation

### Supported Formats
- JPEG (`image/jpeg`)
- PNG (`image/png`)
- GIF (`image/gif`)
- WebP (`image/webp`)

### Size Limits
- **Minimum:** 1KB
- **Maximum:** 5MB

### Validation Process
1. **Client-side validation** (file type, size)
2. **Server-side validation** via Asset Service
3. **Content validation** (malware scan, corruption check)
4. **Image processing** (thumbnail generation, optimization)

## Error Handling

### Common Error Codes

| Error Code | Description | Solution |
|------------|-------------|----------|
| `INVALID_FILE_TYPE` | Unsupported file format | Use JPEG, PNG, GIF, or WebP |
| `FILE_TOO_LARGE` | File exceeds 5MB limit | Compress or resize image |
| `UPLOAD_NOT_FOUND` | Upload ID not found/expired | Request new upload URL |
| `VALIDATION_FAILED` | File failed validation | Check file integrity |
| `AVATAR_NOT_FOUND` | User has no avatar | Upload avatar first |

### Error Response Format
```json
{
  "error": "INVALID_FILE_TYPE",
  "message": "Only image files are allowed",
  "details": "Supported formats: JPEG, PNG, GIF, WebP"
}
```

## Security Features

### Upload Security
- **Presigned URLs** with expiration (1 hour)
- **File type validation** on both client and server
- **Size limits** to prevent abuse
- **Malware scanning** via Asset Service
- **Content validation** to prevent corrupted files

### Access Control
- **Authentication required** for all endpoints
- **User isolation** - users can only manage their own avatars
- **Rate limiting** on upload requests

## Performance Considerations

### CDN Integration
- Avatar URLs point to CDN for fast delivery
- Automatic thumbnail generation for different sizes
- Image optimization for web delivery

### Caching
- Avatar URLs are cached in user records
- CDN caching for static assets
- Browser caching with appropriate headers

## Monitoring & Analytics

### Metrics to Track
- Upload success/failure rates
- Average upload time
- File size distribution
- Popular image formats
- Storage usage per user

### Logging
- All upload attempts logged
- Validation failures tracked
- Performance metrics collected

## Migration from Direct Upload

### For Existing Systems
1. **Dual support** - support both old and new endpoints temporarily
2. **Gradual migration** - migrate users over time
3. **Data migration** - move existing avatars to Asset Service
4. **Cleanup** - remove old upload endpoints after migration

### Backward Compatibility
- Existing avatar URLs continue to work
- Old API endpoints return deprecation warnings
- Gradual sunset of legacy endpoints

## Troubleshooting

### Common Issues

#### Upload Fails at S3
- Check presigned URL expiration
- Verify file type matches request
- Ensure file size is within limits

#### Confirmation Fails
- Verify ETag format (remove quotes)
- Check upload_id validity
- Ensure file was actually uploaded to S3

#### Avatar Not Displaying
- Check CDN propagation
- Verify avatar_url in user record
- Check browser cache

### Debug Steps
1. Check network requests in browser dev tools
2. Verify API responses and error messages
3. Check server logs for detailed errors
4. Test with different file types/sizes

## Best Practices

### Frontend
- Show upload progress to users
- Validate files before upload
- Handle errors gracefully
- Provide clear feedback

### Backend
- Log all upload attempts
- Monitor storage usage
- Implement proper cleanup
- Use appropriate timeouts

### Security
- Validate all inputs
- Scan uploaded files
- Monitor for abuse
- Implement rate limiting

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// PostAnalytics holds the schema definition for the PostAnalytics entity.
type PostAnalytics struct {
	ent.Schema
}

// Fields of the PostAnalytics.
func (PostAnalytics) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}).
			Comment("User who owns the post"),
		field.String("post_id").
			MaxLen(36).
			Comment("Post ID from content management service"),
		field.String("published_post_id").
			MaxLen(36).
			Optional().
			Comment("Published post ID from integration service"),
		field.Enum("platform").
			Values("facebook", "instagram", "twitter", "linkedin", "tiktok", "youtube", "pinterest", "snapchat").
			Comment("Platform where analytics are tracked"),
		field.String("platform_post_id").
			MaxLen(200).
			Optional().
			Comment("Post ID on the platform"),
		field.Time("date").
			Comment("Date of the analytics data"),
		field.Int("impressions").
			Default(0).
			Comment("Number of times the post was displayed"),
		field.Int("reach").
			Default(0).
			Comment("Number of unique users who saw the post"),
		field.Int("likes").
			Default(0).
			Comment("Number of likes/reactions"),
		field.Int("comments").
			Default(0).
			Comment("Number of comments"),
		field.Int("shares").
			Default(0).
			Comment("Number of shares/retweets"),
		field.Int("saves").
			Default(0).
			Comment("Number of saves/bookmarks"),
		field.Int("clicks").
			Default(0).
			Comment("Number of link clicks"),
		field.Int("video_views").
			Default(0).
			Comment("Number of video views (if applicable)"),
		field.Other("engagement_rate", decimal.Decimal{}).
			SchemaType(map[string]string{
				"postgres": "decimal(5,4)",
			}).
			Default(decimal.Zero).
			Comment("Engagement rate as a decimal"),
		field.Other("click_through_rate", decimal.Decimal{}).
			SchemaType(map[string]string{
				"postgres": "decimal(5,4)",
			}).
			Default(decimal.Zero).
			Comment("Click-through rate as a decimal"),
		field.JSON("demographics", map[string]interface{}{}).
			Optional().
			Comment("Audience demographics data"),
		field.JSON("geographic_data", map[string]interface{}{}).
			Optional().
			Comment("Geographic distribution of audience"),
		field.JSON("time_data", map[string]interface{}{}).
			Optional().
			Comment("Time-based analytics (hourly, daily patterns)"),
		field.JSON("platform_specific", map[string]interface{}{}).
			Optional().
			Comment("Platform-specific metrics"),
		field.JSON("raw_data", map[string]interface{}{}).
			Optional().
			Comment("Raw analytics data from platform API"),
		field.Time("last_updated").
			Default(time.Now).
			Comment("When this data was last updated"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the PostAnalytics.
func (PostAnalytics) Edges() []ent.Edge {
	return nil
}

// Indexes of the PostAnalytics.
func (PostAnalytics) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("post_id"),
		index.Fields("published_post_id"),
		index.Fields("platform"),
		index.Fields("platform_post_id"),
		index.Fields("date"),
		index.Fields("last_updated"),
		index.Fields("created_at"),
		index.Fields("user_id", "platform"),
		index.Fields("post_id", "platform"),
		index.Fields("platform", "date"),
		index.Fields("post_id", "platform", "date").Unique(),
	}
}

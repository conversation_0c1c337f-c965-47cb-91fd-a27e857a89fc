package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TransactionHandler handles transaction-related HTTP requests
type TransactionHandler struct {
	transactionUseCase transaction.UseCase
	logger             logging.Logger
}

// NewTransactionHandler creates a new transaction handler
func NewTransactionHandler(transactionUseCase transaction.UseCase, logger logging.Logger) *TransactionHandler {
	return &TransactionHandler{
		transactionUseCase: transactionUseCase,
		logger:             logger,
	}
}

// ListTransactions godoc
// @Summary List transactions
// @Description Get paginated transaction history with filters
// @Tags transactions
// @Produce json
// @Security BearerAuth
// @Param type query string false "Transaction type filter"
// @Param status query string false "Transaction status filter"
// @Param date_from query string false "Start date (YYYY-MM-DD)"
// @Param date_to query string false "End date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.CreditHistoryResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/transactions [get]
func (h *TransactionHandler) ListTransactions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	req := &models.CreditHistoryRequest{
		UserID:        userID.(string),
		Type:          c.Query("type"),
		Page:          page,
		Limit:         limit,
		SortBy:        c.DefaultQuery("sort_by", "created_at"),
		SortOrder:     c.DefaultQuery("sort_order", "desc"),
	}

	// Parse date filters if provided
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		// TODO: Parse date_from
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		// TODO: Parse date_to
	}

	transactions, err := h.transactionUseCase.ListTransactions(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list transactions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_TRANSACTIONS_FAILED",
			Message: "Failed to list transactions",
		})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// GetTransaction godoc
// @Summary Get transaction by ID
// @Description Get transaction details by ID
// @Tags transactions
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} models.CreditTransactionResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/transactions/{id} [get]
func (h *TransactionHandler) GetTransaction(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	transactionID := c.Param("id")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TRANSACTION_ID",
			Message: "Transaction ID is required",
		})
		return
	}

	transaction, err := h.transactionUseCase.GetTransaction(c.Request.Context(), transactionID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get transaction")
		
		switch err.Error() {
		case "TRANSACTION_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_FOUND",
				Message: "Transaction not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to this transaction",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_TRANSACTION_FAILED",
				Message: "Failed to get transaction",
			})
		}
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// ExportTransactions godoc
// @Summary Export transactions
// @Description Export transactions to file (CSV, Excel, PDF)
// @Tags transactions
// @Produce json
// @Security BearerAuth
// @Param format query string true "Export format (csv, xlsx, pdf)"
// @Param date_from query string false "Start date (YYYY-MM-DD)"
// @Param date_to query string false "End date (YYYY-MM-DD)"
// @Param type query string false "Transaction type filter"
// @Param status query string false "Transaction status filter"
// @Success 200 {object} models.ExportTransactionsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/transactions/export [get]
func (h *TransactionHandler) ExportTransactions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	format := c.Query("format")
	if format == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "MISSING_FORMAT",
			Message: "Export format is required",
		})
		return
	}

	req := &models.ExportTransactionsRequest{
		UserID: userID.(string),
		Format: format,
		Type:   c.Query("type"),
		Status: c.Query("status"),
	}

	// Parse date filters if provided
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		// TODO: Parse date_from
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		// TODO: Parse date_to
	}

	export, err := h.transactionUseCase.ExportTransactions(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to export transactions")
		
		switch err.Error() {
		case "INVALID_FORMAT":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_FORMAT",
				Message: "Invalid export format",
			})
		case "NO_DATA":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "NO_DATA",
				Message: "No transactions found for export",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "EXPORT_FAILED",
				Message: "Failed to export transactions",
			})
		}
		return
	}

	c.JSON(http.StatusOK, export)
}

// GetTransactionSummary godoc
// @Summary Get transaction summary
// @Description Get transaction summary for a period
// @Tags transactions
// @Produce json
// @Security BearerAuth
// @Param period query string false "Period (day, week, month, year)" default("month")
// @Success 200 {object} models.CreditHistorySummary
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/transactions/summary [get]
func (h *TransactionHandler) GetTransactionSummary(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	period := c.DefaultQuery("period", "month")

	summary, err := h.transactionUseCase.GetTransactionSummary(c.Request.Context(), userID.(string), period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get transaction summary")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_SUMMARY_FAILED",
			Message: "Failed to get transaction summary",
		})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// AdminListTransactions godoc
// @Summary Admin list transactions
// @Description List all transactions (admin only)
// @Tags admin
// @Produce json
// @Security BearerAuth
// @Param user_id query string false "User ID filter"
// @Param type query string false "Transaction type filter"
// @Param status query string false "Transaction status filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.CreditHistoryResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Router /admin/v1/transactions [get]
func (h *TransactionHandler) AdminListTransactions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	req := &models.CreditHistoryRequest{
		UserID:    c.Query("user_id"),
		Type:      c.Query("type"),
		Page:      page,
		Limit:     limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "desc"),
	}

	transactions, err := h.transactionUseCase.AdminListTransactions(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to admin list transactions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "ADMIN_LIST_TRANSACTIONS_FAILED",
			Message: "Failed to list transactions",
		})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// AdminGetTransaction godoc
// @Summary Admin get transaction
// @Description Get transaction details (admin only)
// @Tags admin
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} models.CreditTransactionResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /admin/v1/transactions/{id} [get]
func (h *TransactionHandler) AdminGetTransaction(c *gin.Context) {
	transactionID := c.Param("id")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TRANSACTION_ID",
			Message: "Transaction ID is required",
		})
		return
	}

	transaction, err := h.transactionUseCase.AdminGetTransaction(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to admin get transaction")
		
		switch err.Error() {
		case "TRANSACTION_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_FOUND",
				Message: "Transaction not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "ADMIN_GET_TRANSACTION_FAILED",
				Message: "Failed to get transaction",
			})
		}
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// AdminApproveTransaction godoc
// @Summary Admin approve transaction
// @Description Approve a pending transaction (admin only)
// @Tags admin
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} models.CreditTransactionResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /admin/v1/transactions/{id}/approve [post]
func (h *TransactionHandler) AdminApproveTransaction(c *gin.Context) {
	transactionID := c.Param("id")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TRANSACTION_ID",
			Message: "Transaction ID is required",
		})
		return
	}

	transaction, err := h.transactionUseCase.AdminApproveTransaction(c.Request.Context(), transactionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to approve transaction")
		
		switch err.Error() {
		case "TRANSACTION_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_FOUND",
				Message: "Transaction not found",
			})
		case "TRANSACTION_NOT_PENDING":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_PENDING",
				Message: "Transaction is not in pending status",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "APPROVE_TRANSACTION_FAILED",
				Message: "Failed to approve transaction",
			})
		}
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// AdminRejectTransaction godoc
// @Summary Admin reject transaction
// @Description Reject a pending transaction (admin only)
// @Tags admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Param request body object{reason=string} true "Rejection reason"
// @Success 200 {object} models.CreditTransactionResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /admin/v1/transactions/{id}/reject [post]
func (h *TransactionHandler) AdminRejectTransaction(c *gin.Context) {
	transactionID := c.Param("id")
	if transactionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TRANSACTION_ID",
			Message: "Transaction ID is required",
		})
		return
	}

	var req struct {
		Reason string `json:"reason" validate:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid reject transaction request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	transaction, err := h.transactionUseCase.AdminRejectTransaction(c.Request.Context(), transactionID, req.Reason)
	if err != nil {
		h.logger.WithError(err).Error("Failed to reject transaction")
		
		switch err.Error() {
		case "TRANSACTION_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_FOUND",
				Message: "Transaction not found",
			})
		case "TRANSACTION_NOT_PENDING":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "TRANSACTION_NOT_PENDING",
				Message: "Transaction is not in pending status",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "REJECT_TRANSACTION_FAILED",
				Message: "Failed to reject transaction",
			})
		}
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// InternalGetUserTransactions godoc
// @Summary Internal get user transactions
// @Description Get user transactions for internal service communication
// @Tags internal
// @Produce json
// @Param user_id path string true "User ID"
// @Param limit query int false "Limit" default(10)
// @Success 200 {object} models.CreditHistoryResponse
// @Failure 400 {object} models.ErrorResponse
// @Router /internal/v1/transactions/user/{user_id} [get]
func (h *TransactionHandler) InternalGetUserTransactions(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_USER_ID",
			Message: "User ID is required",
		})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	transactions, err := h.transactionUseCase.InternalGetUserTransactions(c.Request.Context(), userID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user transactions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_USER_TRANSACTIONS_FAILED",
			Message: "Failed to get user transactions",
		})
		return
	}

	c.JSON(http.StatusOK, transactions)
}

// InternalCreateTransaction godoc
// @Summary Internal create transaction
// @Description Create transaction for internal service communication
// @Tags internal
// @Accept json
// @Produce json
// @Param request body models.ConsumeCreditsRequest true "Create transaction request"
// @Success 201 {object} models.CreditTransactionResponse
// @Failure 400 {object} models.ErrorResponse
// @Router /internal/v1/transactions [post]
func (h *TransactionHandler) InternalCreateTransaction(c *gin.Context) {
	var req models.ConsumeCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid create transaction request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	transaction, err := h.transactionUseCase.InternalCreateTransaction(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create transaction")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CREATE_TRANSACTION_FAILED",
			Message: "Failed to create transaction",
		})
		return
	}

	c.JSON(http.StatusCreated, transaction)
}

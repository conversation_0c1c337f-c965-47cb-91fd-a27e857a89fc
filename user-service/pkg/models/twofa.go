package models

// SetupTwoFAResponse represents the response from setting up 2FA
type SetupTwoFAResponse struct {
	Secret      string   `json:"secret" example:"JBSWY3DPEHPK3PXP"`
	QRCodeURL   string   `json:"qr_code_url" example:"otpauth://totp/Social%20Content%20AI:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Social%20Content%20AI"`
	QRCode      []byte   `json:"qr_code,omitempty" swaggertype:"string" format:"byte" example:"iVBORw0KGgoAAAANSUhEUgAAAQAAAAEA..."`
	BackupCodes []string `json:"backup_codes" example:"ABCD-1234,EFGH-5678,IJKL-9012"`
}

// EnableTwoFARequest represents a request to enable 2FA
type EnableTwoFARequest struct {
	Token string `json:"token" binding:"required,len=6" example:"123456"`
}

// EnableTwoFAResponse represents the response from enabling 2FA
type EnableTwoFAResponse struct {
	Enabled     bool     `json:"enabled" example:"true"`
	BackupCodes []string `json:"backup_codes" example:"ABCD-1234,EFGH-5678,IJKL-9012"`
	EnabledAt   string   `json:"enabled_at" example:"2023-12-01T10:00:00Z"`
}

// DisableTwoFARequest represents a request to disable 2FA
type DisableTwoFARequest struct {
	Password string `json:"password" binding:"required" example:"currentpassword123"`
	Token    string `json:"token,omitempty" example:"123456"`
}

// VerifyTwoFARequest represents a request to verify 2FA
type VerifyTwoFARequest struct {
	Token string `json:"token" binding:"required" example:"123456"`
}

// VerifyTwoFAResponse represents the response from 2FA verification
type VerifyTwoFAResponse struct {
	Valid          bool   `json:"valid" example:"true"`
	BackupCodeUsed bool   `json:"backup_code_used,omitempty" example:"false"`
	RemainingCodes int    `json:"remaining_codes,omitempty" example:"9"`
	Message        string `json:"message,omitempty" example:"Token verified successfully"`
}

// GenerateBackupCodesRequest represents a request to generate new backup codes
type GenerateBackupCodesRequest struct {
	Password string `json:"password" binding:"required" example:"currentpassword123"`
}

// GenerateBackupCodesResponse represents the response with new backup codes
type GenerateBackupCodesResponse struct {
	BackupCodes []string `json:"backup_codes" example:"ABCD-1234,EFGH-5678,IJKL-9012"`
	GeneratedAt string   `json:"generated_at" example:"2023-12-01T10:00:00Z"`
}

// GetTwoFAStatusResponse represents the 2FA status for a user
type GetTwoFAStatusResponse struct {
	UserID              uint64 `json:"user_id" example:"1"`
	TwoFAEnabled        bool   `json:"two_fa_enabled" example:"true"`
	EnabledAt           string `json:"enabled_at,omitempty" example:"2023-12-01T10:00:00Z"`
	BackupCodesCount    int    `json:"backup_codes_count" example:"10"`
	HasBackupCodes      bool   `json:"has_backup_codes" example:"true"`
	LastVerificationAt  string `json:"last_verification_at,omitempty" example:"2023-12-01T15:30:00Z"`
}

// TwoFAStatsResponse represents 2FA usage statistics
type TwoFAStatsResponse struct {
	TotalUsers           int     `json:"total_users" example:"1000"`
	UsersWithTwoFA       int     `json:"users_with_2fa" example:"250"`
	TwoFAEnabledPercent  float64 `json:"2fa_enabled_percent" example:"25.0"`
	RecentVerifications  int     `json:"recent_verifications" example:"150"`
	FailedVerifications  int     `json:"failed_verifications" example:"5"`
}

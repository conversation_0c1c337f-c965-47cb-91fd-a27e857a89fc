package events

import (
	"time"

	"github.com/google/uuid"
)

// NewSubscriptionPaymentCompletedEvent creates a new subscription payment completed event
func NewSubscriptionPaymentCompletedEvent(userID, subscriptionID, paymentID string, amount int64, currency string) *SubscriptionPaymentCompletedEvent {
	return &SubscriptionPaymentCompletedEvent{
		EventID:        generateEventID(),
		EventType:      EventTypeSubscriptionPaymentCompleted,
		UserID:         userID,
		SubscriptionID: subscriptionID,
		PaymentID:      paymentID,
		Timestamp:      time.Now(),
		Source:         SourceCreditService,
		Version:        EventVersion,
		Amount:         amount,
		Currency:       currency,
		PaymentMethod:  "paypal",
		EventData:      make(map[string]interface{}),
		Metadata:       make(map[string]interface{}),
	}
}

// NewSubscriptionPaymentFailedEvent creates a new subscription payment failed event
func NewSubscriptionPaymentFailedEvent(userID, subscriptionID, paymentID string, amount int64, currency, failureReason string) *SubscriptionPaymentFailedEvent {
	return &SubscriptionPaymentFailedEvent{
		EventID:        generateEventID(),
		EventType:      EventTypeSubscriptionPaymentFailed,
		UserID:         userID,
		SubscriptionID: subscriptionID,
		PaymentID:      paymentID,
		Timestamp:      time.Now(),
		Source:         SourceCreditService,
		Version:        EventVersion,
		Amount:         amount,
		Currency:       currency,
		PaymentMethod:  "paypal",
		FailureReason:  failureReason,
		EventData:      make(map[string]interface{}),
		Metadata:       make(map[string]interface{}),
	}
}

// NewSubscriptionActivatedEventKafka creates a new subscription activated event for Kafka
func NewSubscriptionActivatedEventKafka(userID, subscriptionID, planID, planName string) *SubscriptionActivatedEventKafka {
	now := time.Now()
	return &SubscriptionActivatedEventKafka{
		EventID:        generateEventID(),
		EventType:      EventTypeSubscriptionActivatedKafka,
		UserID:         userID,
		SubscriptionID: subscriptionID,
		Timestamp:      now,
		Source:         SourceCreditService,
		Version:        EventVersion,
		PlanID:         planID,
		PlanName:       planName,
		Status:         "active",
		StartsAt:       now,
		EndsAt:         now.AddDate(0, 1, 0), // Default to 1 month
		EventData:      make(map[string]interface{}),
		Metadata:       make(map[string]interface{}),
	}
}

// NewSubscriptionCancelledEventKafka creates a new subscription cancelled event for Kafka
func NewSubscriptionCancelledEventKafka(userID, subscriptionID, planID, planName, reason string) *SubscriptionCancelledEventKafka {
	now := time.Now()
	return &SubscriptionCancelledEventKafka{
		EventID:           generateEventID(),
		EventType:         EventTypeSubscriptionCancelledKafka,
		UserID:            userID,
		SubscriptionID:    subscriptionID,
		Timestamp:         now,
		Source:            SourceCreditService,
		Version:           EventVersion,
		PlanID:            planID,
		PlanName:          planName,
		Status:            "cancelled",
		CancelledAt:       now,
		Reason:            reason,
		CancelAtPeriodEnd: false,
		EventData:         make(map[string]interface{}),
		Metadata:          make(map[string]interface{}),
	}
}

// NewGeneralSubscriptionEvent creates a new general subscription event
func NewGeneralSubscriptionEvent(eventType, userID, subscriptionID, status string) *GeneralSubscriptionEvent {
	return &GeneralSubscriptionEvent{
		EventID:        generateEventID(),
		EventType:      eventType,
		UserID:         userID,
		SubscriptionID: subscriptionID,
		Timestamp:      time.Now(),
		Source:         SourceCreditService,
		Version:        EventVersion,
		Status:         status,
		EventData:      make(map[string]interface{}),
		Metadata:       make(map[string]interface{}),
	}
}

// Utility Functions

// generateEventID generates a unique event ID
func generateEventID() string {
	return "evt_" + uuid.New().String()
}

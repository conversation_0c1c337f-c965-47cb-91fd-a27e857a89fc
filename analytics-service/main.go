package main

import (
	"context"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/social-content-ai/analytics-service/api/grpc/handlers"
	"github.com/social-content-ai/analytics-service/api/restful"
	"github.com/social-content-ai/analytics-service/ent"
	"github.com/social-content-ai/analytics-service/usecase/analytics"
	"github.com/social-content-ai/analytics-service/usecase/metrics"
	"github.com/social-content-ai/analytics-service/usecase/report"
	
	"github.com/social-content-ai/pkg-shared/config"
	"github.com/social-content-ai/pkg-shared/database"
	"github.com/social-content-ai/pkg-shared/logging"
	pkgmetrics "github.com/social-content-ai/pkg-shared/metrics"
	"github.com/social-content-ai/pkg-shared/tracing"
	
	analyticsv1 "github.com/social-content-ai/proto-shared/analytics/v1"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		GRPCPort string `env:"GRPC_PORT" envDefault:"50057"`
		HTTPPort string `env:"HTTP_PORT" envDefault:"8087"`
	}
	Database struct {
		URL string `env:"DATABASE_URL" envDefault:"postgres://user:password@localhost/analytics_db?sslmode=disable"`
	}
	Services struct {
		UserService string `env:"USER_SERVICE_URL" envDefault:"localhost:50050"`
	}
	Metrics struct {
		Port string `env:"METRICS_PORT" envDefault:"9097"`
	}
}

func main() {
	// Load configuration
	cfg := &Config{}
	if err := config.Load(cfg); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  "info",
		Format: "json",
	})

	// Initialize tracing
	tracer, err := tracing.NewTracer("analytics-service")
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize tracer")
	}
	defer tracer.Close()

	// Initialize metrics
	metricsServer := pkgmetrics.NewServer(cfg.Metrics.Port)
	go func() {
		if err := metricsServer.Start(); err != nil {
			logger.WithError(err).Error("Failed to start metrics server")
		}
	}()

	// Initialize database
	db, err := database.NewPostgresConnection(cfg.Database.URL)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to database")
	}
	defer db.Close()

	// Initialize Ent client
	entClient := ent.NewClient(ent.Driver(db))
	defer entClient.Close()

	// Run database migrations
	if err := entClient.Schema.Create(context.Background()); err != nil {
		logger.WithError(err).Fatal("Failed to create schema")
	}

	// Initialize external service clients
	userConn, err := grpc.Dial(cfg.Services.UserService, grpc.WithInsecure())
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to user service")
	}
	defer userConn.Close()
	userClient := userv1.NewAuthServiceClient(userConn)

	// Initialize use cases
	analyticsUseCase := analytics.NewService(entClient, entClient, logger)
	metricsUseCase := metrics.NewService(entClient, entClient, logger)
	reportUseCase := report.NewService(entClient, entClient, logger)

	// Initialize gRPC server
	grpcServer := grpc.NewServer()
	analyticsv1.RegisterAnalyticsServiceServer(grpcServer, handlers.NewAnalyticsHandler(
		analyticsUseCase,
		metricsUseCase,
		reportUseCase,
		logger,
	))

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Initialize HTTP server
	gin.SetMode(gin.ReleaseMode)
	httpRouter := gin.New()
	httpRouter.Use(gin.Recovery())
	
	restful.SetupRoutes(httpRouter, analyticsUseCase, metricsUseCase, reportUseCase, userClient, logger)

	// Start gRPC server
	grpcListener, err := net.Listen("tcp", ":"+cfg.Server.GRPCPort)
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen on gRPC port")
	}

	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(grpcListener); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start HTTP server
	httpServer := &http.Server{
		Addr:    ":" + cfg.Server.HTTPPort,
		Handler: httpRouter,
	}

	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down servers...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Failed to shutdown HTTP server")
	}

	// Shutdown gRPC server
	grpcServer.GracefulStop()

	logger.Info("Servers stopped")
}

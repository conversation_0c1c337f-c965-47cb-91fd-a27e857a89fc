package handlers

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/paypal"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PayPalHandler implements PayPal-related gRPC methods
type PayPalHandler struct {
	paypalUseCase paypal.UseCase
	logger        logging.Logger
}

// NewPayPalHandler creates a new PayPal handler
func NewPayPalHandler(paypalUseCase paypal.UseCase, logger logging.Logger) *PayPalHandler {
	return &PayPalHandler{
		paypalUseCase: paypalUseCase,
		logger:        logger,
	}
}

// InitiatePayPalPayment initiates a PayPal payment
func (h *PayPalHandler) InitiatePayPalPayment(ctx context.Context, req *creditv1.InitiatePayPalPaymentRequest) (*creditv1.InitiatePayPalPaymentResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"amount":  req.Amount,
		"purpose": req.Purpose,
	}).Info("Initiating PayPal payment")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	if req.Amount <= 0 {
		return nil, status.Error(codes.InvalidArgument, "amount must be positive")
	}

	if req.Currency == "" {
		return nil, status.Error(codes.InvalidArgument, "currency is required")
	}

	if req.Purpose == "" {
		return nil, status.Error(codes.InvalidArgument, "purpose is required")
	}

	if req.ReturnUrl == "" {
		return nil, status.Error(codes.InvalidArgument, "return_url is required")
	}

	if req.CancelUrl == "" {
		return nil, status.Error(codes.InvalidArgument, "cancel_url is required")
	}

	// Convert to internal request
	paypalReq := &models.InitiatePayPalPaymentRequest{
		UserID:      req.UserId,
		PlanID:      req.PlanId,
		Amount:      req.Amount,
		Currency:    req.Currency,
		Purpose:     req.Purpose,
		Description: req.Description,
		ReturnURL:   req.ReturnUrl,
		CancelURL:   req.CancelUrl,
		Metadata:    convertMetadata(req.Metadata),
	}

	// Call use case
	response, err := h.paypalUseCase.InitiatePayment(ctx, paypalReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to initiate PayPal payment")
		return nil, status.Error(codes.Internal, "failed to initiate PayPal payment")
	}

	if !response.Success {
		return &creditv1.InitiatePayPalPaymentResponse{
			Success:      false,
			ErrorMessage: response.ErrorMessage,
		}, nil
	}

	return &creditv1.InitiatePayPalPaymentResponse{
		Success:       true,
		PaymentId:     response.PaymentID,
		PaypalOrderId: response.PayPalOrderID,
		ApprovalUrl:   response.ApprovalURL,
	}, nil
}

// ExecutePayPalPayment executes a PayPal payment after user approval
func (h *PayPalHandler) ExecutePayPalPayment(ctx context.Context, req *creditv1.ExecutePayPalPaymentRequest) (*creditv1.ExecutePayPalPaymentResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentId,
		"payer_id":   req.PayerId,
		"user_id":    req.UserId,
	}).Info("Executing PayPal payment")

	if req.PaymentId == "" {
		return nil, status.Error(codes.InvalidArgument, "payment_id is required")
	}

	if req.PayerId == "" {
		return nil, status.Error(codes.InvalidArgument, "payer_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Convert to internal request
	paypalReq := &models.ExecutePayPalPaymentRequest{
		PaymentID: req.PaymentId,
		PayerID:   req.PayerId,
		UserID:    req.UserId,
	}

	// Call use case
	response, err := h.paypalUseCase.ExecutePayment(ctx, paypalReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to execute PayPal payment")
		return nil, status.Error(codes.Internal, "failed to execute PayPal payment")
	}

	if !response.Success {
		return &creditv1.ExecutePayPalPaymentResponse{
			Success:      false,
			ErrorMessage: response.ErrorMessage,
		}, nil
	}

	grpcResponse := &creditv1.ExecutePayPalPaymentResponse{
		Success:       true,
		PaymentId:     response.PaymentID,
		TransactionId: response.TransactionID,
		CreditsAdded:  response.CreditsAdded,
	}

	if response.SubscriptionID != "" {
		grpcResponse.SubscriptionId = response.SubscriptionID
	}

	if response.SubscriptionInfo != nil {
		grpcResponse.SubscriptionInfo = &creditv1.SubscriptionActivationInfo{
			SubscriptionId: response.SubscriptionInfo.SubscriptionID,
			PlanName:       response.SubscriptionInfo.PlanName,
			StartsAt:       response.SubscriptionInfo.StartsAt.Unix(),
			EndsAt:         response.SubscriptionInfo.EndsAt.Unix(),
			CreditsAdded:   response.SubscriptionInfo.CreditsAdded,
		}
	}

	return grpcResponse, nil
}

// GetPayPalPaymentStatus gets the status of a PayPal payment
func (h *PayPalHandler) GetPayPalPaymentStatus(ctx context.Context, req *creditv1.GetPayPalPaymentStatusRequest) (*creditv1.PayPalPaymentInfo, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentId,
		"user_id":    req.UserId,
	}).Info("Getting PayPal payment status")

	if req.PaymentId == "" {
		return nil, status.Error(codes.InvalidArgument, "payment_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Call use case
	paymentInfo, err := h.paypalUseCase.GetPaymentStatus(ctx, req.PaymentId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get PayPal payment status")
		return nil, status.Error(codes.Internal, "failed to get PayPal payment status")
	}

	// Convert to gRPC response
	grpcResponse := &creditv1.PayPalPaymentInfo{
		PaymentId:     paymentInfo.PaymentID,
		UserId:        paymentInfo.UserID,
		PlanId:        paymentInfo.PlanID,
		Amount:        paymentInfo.Amount,
		Currency:      paymentInfo.Currency,
		Status:        paymentInfo.Status,
		PaypalOrderId: paymentInfo.PayPalOrderID,
		Purpose:       paymentInfo.Purpose,
		Description:   paymentInfo.Description,
		CreatedAt:     paymentInfo.CreatedAt.Unix(),
		UpdatedAt:     paymentInfo.UpdatedAt.Unix(),
	}

	if paymentInfo.CompletedAt != nil {
		grpcResponse.CompletedAt = paymentInfo.CompletedAt.Unix()
	}

	return grpcResponse, nil
}

// CancelPayPalPayment cancels a PayPal payment
func (h *PayPalHandler) CancelPayPalPayment(ctx context.Context, req *creditv1.CancelPayPalPaymentRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_id": req.PaymentId,
		"user_id":    req.UserId,
		"reason":     req.Reason,
	}).Info("Cancelling PayPal payment")

	if req.PaymentId == "" {
		return nil, status.Error(codes.InvalidArgument, "payment_id is required")
	}

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Convert to internal request
	paypalReq := &models.CancelPayPalPaymentRequest{
		PaymentID: req.PaymentId,
		UserID:    req.UserId,
		Reason:    req.Reason,
	}

	// Call use case
	err := h.paypalUseCase.CancelPayment(ctx, paypalReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel PayPal payment")
		return nil, status.Error(codes.Internal, "failed to cancel PayPal payment")
	}

	return &creditv1.Empty{}, nil
}

// ListPayPalPayments lists PayPal payments for a user
func (h *PayPalHandler) ListPayPalPayments(ctx context.Context, req *creditv1.ListPayPalPaymentsRequest) (*creditv1.ListPayPalPaymentsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Listing PayPal payments")

	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}

	// Parse pagination
	page := 1
	limit := 20
	if req.Pagination != nil {
		if req.Pagination.Page > 0 {
			page = int(req.Pagination.Page)
		}
		if req.Pagination.Limit > 0 {
			limit = int(req.Pagination.Limit)
		}
	}

	// Convert to internal request
	paypalReq := &models.ListPayPalPaymentsRequest{
		UserID:  req.UserId,
		Status:  req.Status,
		Purpose: req.Purpose,
		Page:    page,
		Limit:   limit,
	}

	// Parse date filters
	if req.DateFrom != "" {
		// TODO: Parse date from string
	}
	if req.DateTo != "" {
		// TODO: Parse date from string
	}

	// Call use case
	response, err := h.paypalUseCase.ListPayments(ctx, paypalReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list PayPal payments")
		return nil, status.Error(codes.Internal, "failed to list PayPal payments")
	}

	// Convert payments to gRPC format
	var grpcPayments []*creditv1.PayPalPaymentInfo
	for _, payment := range response.Payments {
		grpcPayment := &creditv1.PayPalPaymentInfo{
			PaymentId:     payment.PaymentID,
			UserId:        payment.UserID,
			PlanId:        payment.PlanID,
			Amount:        payment.Amount,
			Currency:      payment.Currency,
			Status:        payment.Status,
			PaypalOrderId: payment.PayPalOrderID,
			Purpose:       payment.Purpose,
			Description:   payment.Description,
			CreatedAt:     payment.CreatedAt.Unix(),
			UpdatedAt:     payment.UpdatedAt.Unix(),
		}

		if payment.CompletedAt != nil {
			grpcPayment.CompletedAt = payment.CompletedAt.Unix()
		}

		grpcPayments = append(grpcPayments, grpcPayment)
	}

	return &creditv1.ListPayPalPaymentsResponse{
		Payments: grpcPayments,
		Summary: &creditv1.PayPalPaymentSummary{
			TotalPayments:     int32(response.Summary.TotalPayments),
			CompletedPayments: int32(response.Summary.CompletedPayments),
			PendingPayments:   int32(response.Summary.PendingPayments),
			FailedPayments:    int32(response.Summary.FailedPayments),
			TotalAmount:       response.Summary.TotalAmount,
			CompletedAmount:   response.Summary.CompletedAmount,
		},
		// TODO: Add pagination response
	}, nil
}

// convertMetadata converts gRPC metadata to internal format
func convertMetadata(metadata map[string]string) map[string]interface{} {
	if metadata == nil {
		return nil
	}

	result := make(map[string]interface{})
	for k, v := range metadata {
		result[k] = v
	}
	return result
}

package transaction

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// UseCase defines the transaction management business logic interface
type UseCase interface {
	// Basic transaction operations
	ListTransactions(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error)
	GetTransaction(ctx context.Context, transactionID, userID string) (*models.CreditTransactionResponse, error)
	ExportTransactions(ctx context.Context, req *models.ExportTransactionsRequest) (*models.ExportTransactionsResponse, error)
	GetTransactionSummary(ctx context.Context, userID, period string) (*models.CreditHistorySummary, error)

	// Transaction creation and management
	CreateTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error)
	UpdateTransactionStatus(ctx context.Context, transactionID, status string) (*models.CreditTransactionResponse, error)
	CancelTransaction(ctx context.Context, transactionID, userID string) (*models.CreditTransactionResponse, error)

	// Admin operations
	AdminListTransactions(ctx context.Context, req *models.CreditHistoryRequest) (*models.CreditHistoryResponse, error)
	AdminGetTransaction(ctx context.Context, transactionID string) (*models.CreditTransactionResponse, error)
	AdminApproveTransaction(ctx context.Context, transactionID string) (*models.CreditTransactionResponse, error)
	AdminRejectTransaction(ctx context.Context, transactionID, reason string) (*models.CreditTransactionResponse, error)

	// Internal operations (for service-to-service communication)
	InternalGetUserTransactions(ctx context.Context, userID string, limit int) (*models.CreditHistoryResponse, error)
	InternalCreateTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error)

	// Transaction analytics and statistics
	GetTransactionAnalytics(ctx context.Context, userID, period string) (*models.CreditAnalyticsResponse, error)
	GetTransactionTrends(ctx context.Context, userID, period string) (*models.UsageTrends, error)
	GetCreditStats(ctx context.Context, req *models.CreditStatsRequest) (*models.CreditStats, error)
	GetUsageReport(ctx context.Context, req *models.UsageReportRequest) (*models.UsageReport, error)
	GetCreditAnalytics(ctx context.Context, userID, period string) (*models.CreditAnalyticsResponse, error)
	GetUsageAnalytics(ctx context.Context, userID, period string) (*models.UsageReport, error)

	// Monthly usage management
	GetMonthlyUsage(ctx context.Context, userID string) (*models.MonthlyUsageResponse, error)
	ResetMonthlyUsage(ctx context.Context, req *models.ResetMonthlyUsageRequest) (*models.MonthlyUsageResponse, error)

	// Bulk operations
	BulkProcessTransactions(ctx context.Context, transactionIDs []string, action string) (*models.BulkOperationResult, error)
	BulkExportTransactions(ctx context.Context, userIDs []string, req *models.ExportTransactionsRequest) (*models.BulkOperationResult, error)

	// Credit operations through transaction flow
	AddCreditsWithTransaction(ctx context.Context, req *models.AddCreditsRequest) (*models.CreditTransactionResponse, error)
	DeductCreditsWithTransaction(ctx context.Context, req *models.DeductCreditsRequest) (*models.CreditTransactionResponse, error)
	TransferCreditsWithTransaction(ctx context.Context, req *models.TransferCreditsRequest) (*models.CreditTransactionResponse, error)
	AdjustCreditsWithTransaction(ctx context.Context, req *models.AdjustCreditsRequest) (*models.CreditTransactionResponse, error)
	ConsumeCreditsWithTransaction(ctx context.Context, req *models.ConsumeCreditsRequest) (*models.CreditTransactionResponse, error)
	RefundCreditsWithTransaction(ctx context.Context, req *models.RefundCreditsRequest) (*models.CreditTransactionResponse, error)

	// Purchase and payment related transactions
	ProcessCreditPurchase(ctx context.Context, req *models.ProcessCreditPurchaseRequest) (*models.CreditTransactionResponse, error)
	ProcessSubscriptionPayment(ctx context.Context, req *models.ProcessSubscriptionPaymentRequest) (*models.CreditTransactionResponse, error)
}

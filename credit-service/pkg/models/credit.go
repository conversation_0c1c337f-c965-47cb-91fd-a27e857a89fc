package models

import "time"

// CreditBalanceResponse represents user credit balance
type CreditBalanceResponse struct {
	UserID           string     `json:"user_id"`
	CurrentCredits   int        `json:"current_credits"`
	TotalCredits     int        `json:"total_credits"`
	MonthlyUsed      int        `json:"monthly_used"`
	MonthlyLimit     int        `json:"monthly_limit"`
	MonthlyRemaining int        `json:"monthly_remaining"`
	PlanID           *string    `json:"plan_id,omitempty"`
	PlanName         *string    `json:"plan_name,omitempty"`
	PlanExpiresAt    *time.Time `json:"plan_expires_at,omitempty"`
	Status           string     `json:"status"`
	MonthlyResetAt   *time.Time `json:"monthly_reset_at,omitempty"`
	LastUpdated      time.Time  `json:"last_updated"`
}

// ValidateCreditsRequest represents credit validation request
type ValidateCreditsRequest struct {
	UserID          string `json:"user_id" validate:"required"`
	RequiredCredits int    `json:"required_credits" validate:"required,min=1"`
	Purpose         string `json:"purpose,omitempty"`
	ReferenceID     string `json:"reference_id,omitempty"`
	ReferenceType   string `json:"reference_type,omitempty"`
}

// ConsumeCreditsRequest represents credit consumption request
type ConsumeCreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required,min=1"`
	Purpose       string                 `json:"purpose" validate:"required"`
	Description   string                 `json:"description,omitempty"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// RefundCreditsRequest represents credit refund request
type RefundCreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required,min=1"`
	Reason        string                 `json:"reason" validate:"required"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	TransactionID string                 `json:"transaction_id,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// TransferCreditsRequest represents credit transfer request
type TransferCreditsRequest struct {
	FromUserID    string                 `json:"from_user_id" validate:"required"`
	ToUserID      string                 `json:"to_user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required,min=1"`
	Reason        string                 `json:"reason" validate:"required"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// AdjustCreditsRequest represents credit adjustment request
type AdjustCreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required"` // can be positive or negative
	Reason        string                 `json:"reason" validate:"required"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// AddCreditsRequest represents add credits request
type AddCreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required,min=1"`
	Source        string                 `json:"source" validate:"required"` // purchase, bonus, referral, etc.
	Description   string                 `json:"description" validate:"required"`
	PaymentID     string                 `json:"payment_id,omitempty"`
	InvoiceID     string                 `json:"invoice_id,omitempty"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// DeductCreditsRequest represents deduct credits request
type DeductCreditsRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        int                    `json:"amount" validate:"required,min=1"`
	Purpose       string                 `json:"purpose" validate:"required"`
	Description   string                 `json:"description" validate:"required"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// CreditHistoryRequest represents credit history request
type CreditHistoryRequest struct {
	UserID        string     `json:"user_id"`
	Type          string     `json:"type,omitempty"` // purchase, usage, bonus, refund, etc.
	ReferenceType string     `json:"reference_type,omitempty"`
	DateFrom      *time.Time `json:"date_from,omitempty"`
	DateTo        *time.Time `json:"date_to,omitempty"`
	Page          int        `json:"page" validate:"min=1"`
	Limit         int        `json:"limit" validate:"min=1,max=100"`
	SortBy        string     `json:"sort_by,omitempty"`
	SortOrder     string     `json:"sort_order,omitempty"`
}

// CreditHistoryResponse represents credit history response
type CreditHistoryResponse struct {
	Transactions []CreditTransactionResponse `json:"transactions"`
	Pagination   PaginationMeta              `json:"pagination"`
	Summary      CreditHistorySummary        `json:"summary"`
}

// CreditTransactionResponse represents credit transaction
type CreditTransactionResponse struct {
	BaseModel
	UserID        string                 `json:"user_id"`
	Type          string                 `json:"type"`
	Amount        int                    `json:"amount"`
	Description   string                 `json:"description"`
	ReferenceID   string                 `json:"reference_id,omitempty"`
	ReferenceType string                 `json:"reference_type,omitempty"`
	PaymentID     string                 `json:"payment_id,omitempty"`
	InvoiceID     string                 `json:"invoice_id,omitempty"`
	Status        string                 `json:"status"`
	ProcessedAt   *time.Time             `json:"processed_at,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	BalanceAfter  int                    `json:"balance_after,omitempty"`
}

// CreditHistorySummary represents credit history summary
type CreditHistorySummary struct {
	TotalTransactions  int            `json:"total_transactions"`
	TotalCreditsEarned int            `json:"total_credits_earned"`
	TotalCreditsUsed   int            `json:"total_credits_used"`
	NetChange          int            `json:"net_change"`
	ByType             map[string]int `json:"by_type"`
	ByCategory         map[string]int `json:"by_category"`
}

// MonthlyUsageResponse represents monthly usage response
type MonthlyUsageResponse struct {
	UserID           string         `json:"user_id"`
	Month            string         `json:"month"` // YYYY-MM
	MonthlyUsed      int            `json:"monthly_used"`
	MonthlyLimit     int            `json:"monthly_limit"`
	MonthlyRemaining int            `json:"monthly_remaining"`
	DailyUsage       []DailyUsage   `json:"daily_usage"`
	UsageByCategory  map[string]int `json:"usage_by_category"`
	UsageByModel     map[string]int `json:"usage_by_model"`
	ResetDate        time.Time      `json:"reset_date"`
}

// CreditStatsRequest represents credit stats request
type CreditStatsRequest struct {
	UserID   string     `json:"user_id"`
	Period   string     `json:"period,omitempty"` // day, week, month, year
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	GroupBy  string     `json:"group_by,omitempty"` // day, week, month
}

// UsageReportRequest represents usage report request
type UsageReportRequest struct {
	UserID   string     `json:"user_id"`
	Period   string     `json:"period" validate:"required"` // day, week, month, year
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Category string     `json:"category,omitempty"`
	Model    string     `json:"model,omitempty"`
}

// CreditAnalyticsResponse represents credit analytics
type CreditAnalyticsResponse struct {
	Period                string                 `json:"period"`
	TotalUsers            int                    `json:"total_users"`
	ActiveUsers           int                    `json:"active_users"`
	TotalCreditsIssued    int64                  `json:"total_credits_issued"`
	TotalCreditsUsed      int64                  `json:"total_credits_used"`
	AverageCreditsPerUser float64                `json:"average_credits_per_user"`
	Timeline              []CreditAnalyticsPoint `json:"timeline"`
	UsageByCategory       map[string]int64       `json:"usage_by_category"`
	UsageByModel          map[string]int64       `json:"usage_by_model"`
	TopUsers              []UserCreditUsage      `json:"top_users"`
	CreditDistribution    CreditDistribution     `json:"credit_distribution"`
}

// CreditAnalyticsPoint represents a point in credit analytics timeline
type CreditAnalyticsPoint struct {
	Date          string `json:"date"`
	CreditsIssued int64  `json:"credits_issued"`
	CreditsUsed   int64  `json:"credits_used"`
	ActiveUsers   int    `json:"active_users"`
}

// UserCreditUsage represents user credit usage
type UserCreditUsage struct {
	UserID       string `json:"user_id"`
	Email        string `json:"email,omitempty"`
	CreditsUsed  int    `json:"credits_used"`
	Transactions int    `json:"transactions"`
}

// CreditDistribution represents credit distribution
type CreditDistribution struct {
	Ranges []CreditRange `json:"ranges"`
}

// CreditRange represents a credit range
type CreditRange struct {
	Min   int `json:"min"`
	Max   int `json:"max"`
	Count int `json:"count"`
}

// ResetMonthlyUsageRequest represents reset monthly usage request
type ResetMonthlyUsageRequest struct {
	UserID string `json:"user_id" validate:"required"`
	Reason string `json:"reason,omitempty"`
}

// ProcessCreditPurchaseRequest represents credit purchase processing request
type ProcessCreditPurchaseRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	PaymentID   string                 `json:"payment_id" validate:"required"`
	Amount      int                    `json:"amount" validate:"required,min=1"`
	PackageID   string                 `json:"package_id,omitempty"`
	PackageName string                 `json:"package_name,omitempty"`
	Currency    string                 `json:"currency" validate:"required"`
	Price       int64                  `json:"price" validate:"required,min=1"` // in cents
	Source      string                 `json:"source" validate:"required"`      // paypal, stripe, etc.
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ProcessSubscriptionPaymentRequest represents subscription payment processing request
type ProcessSubscriptionPaymentRequest struct {
	UserID         string                 `json:"user_id" validate:"required"`
	SubscriptionID string                 `json:"subscription_id" validate:"required"`
	PaymentID      string                 `json:"payment_id" validate:"required"`
	PlanID         string                 `json:"plan_id" validate:"required"`
	PlanName       string                 `json:"plan_name,omitempty"`
	Credits        int                    `json:"credits" validate:"required,min=0"`
	Currency       string                 `json:"currency" validate:"required"`
	Amount         int64                  `json:"amount" validate:"required,min=0"` // in cents
	Source         string                 `json:"source" validate:"required"`       // paypal, stripe, etc.
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

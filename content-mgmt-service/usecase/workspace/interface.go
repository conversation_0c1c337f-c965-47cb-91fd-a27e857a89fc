package workspace

import (
	"context"

	"github.com/social-content-ai/content-mgmt-service/pkg/models"
)

// UseCase defines the workspace business logic interface
type UseCase interface {
	// Basic CRUD operations
	CreateWorkspace(ctx context.Context, req *models.CreateWorkspaceRequest) (*models.WorkspaceResponse, error)
	GetWorkspace(ctx context.Context, workspaceID, userID string) (*models.WorkspaceResponse, error)
	UpdateWorkspace(ctx context.Context, req *models.UpdateWorkspaceRequest) (*models.WorkspaceResponse, error)
	DeleteWorkspace(ctx context.Context, workspaceID, userID string) error
	ListWorkspaces(ctx context.Context, req *models.ListWorkspacesRequest) (*models.ListWorkspacesResponse, error)

	// Member management
	AddMember(ctx context.Context, req *models.AddMemberRequest) (*models.WorkspaceMember, error)
	ListMembers(ctx context.Context, req *models.ListMembersRequest) (*models.ListMembersResponse, error)
	UpdateMemberRole(ctx context.Context, req *models.UpdateMemberRoleRequest) (*models.WorkspaceMember, error)
	RemoveMember(ctx context.Context, workspaceID, userID, memberID string) error

	// Workspace content
	GetWorkspacePosts(ctx context.Context, workspaceID, userID string, page, limit int) (*models.ListPostsResponse, error)
	GetWorkspaceTemplates(ctx context.Context, workspaceID, userID string, page, limit int) (*models.ListTemplatesResponse, error)

	// Settings
	UpdateWorkspaceSettings(ctx context.Context, workspaceID, userID string, settings models.WorkspaceSettings) (*models.WorkspaceResponse, error)
	GetWorkspaceSettings(ctx context.Context, workspaceID, userID string) (*models.WorkspaceSettings, error)

	// Analytics
	GetWorkspaceAnalytics(ctx context.Context, req *models.WorkspaceAnalyticsRequest) (*models.WorkspaceAnalyticsResponse, error)
	GetWorkspacesAnalytics(ctx context.Context, userID string, period string) (*models.WorkspaceAnalyticsResponse, error)

	// Admin operations
	AdminListWorkspaces(ctx context.Context, req *models.ListWorkspacesRequest) (*models.ListWorkspacesResponse, error)
	AdminGetWorkspace(ctx context.Context, workspaceID string) (*models.WorkspaceResponse, error)
	AdminDeleteWorkspace(ctx context.Context, workspaceID string) error
	GetAdminWorkspaceAnalytics(ctx context.Context, period string) (*models.WorkspaceAnalyticsResponse, error)
}

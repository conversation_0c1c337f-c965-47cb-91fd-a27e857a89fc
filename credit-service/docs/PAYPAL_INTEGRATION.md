# PayPal Integration Guide

This document explains how to integrate PayPal payment processing with the credit service using the unified payment processor architecture.

## Overview

The PayPal integration consists of two main components:

1. **PayPal UseCase** (`usecase/paypal/`) - Core business logic for PayPal payment operations
2. **PayPal Processor** (`pkg/payment/paypal_processor.go`) - Payment processor adapter implementation

The PayPal processor acts as an adapter between the generic payment interface and the PayPal-specific usecase, providing a unified API for essential payment processing operations.

## Core Features

The integration focuses on essential payment operations:
- **Payment Processing**: Initiate and execute PayPal payments
- **Payment Status**: Check payment status and details
- **Refund Processing**: Handle payment refunds
- **Webhook Handling**: Process PayPal webhook notifications

## Architecture

```
Payment Interface
       ↓
PayPal Processor (Adapter)
       ↓
PayPal UseCase (Business Logic)
       ↓
PayPal Client (API Integration)
```

## Configuration

### PayPal Client Configuration

```go
paypalConfig := paypal.Config{
    ClientID:     "your-paypal-client-id",
    ClientSecret: "your-paypal-client-secret", 
    Environment:  "sandbox", // or "live" for production
    Timeout:      30,
}
```

### Payment Processor Configuration

```go
paymentConfig := payment.Config{
    DefaultProcessor: payment.ProcessorTypePayPal,
    PayPal: payment.PayPalConfig{
        ClientID:     paypalConfig.ClientID,
        ClientSecret: paypalConfig.ClientSecret,
        Environment:  paypalConfig.Environment,
        BaseURL:      "https://api.sandbox.paypal.com",
        ReturnURL:    "http://localhost:8080/payment/success",
        CancelURL:    "http://localhost:8080/payment/cancel",
    },
}
```

## Setup and Initialization

### 1. Create Dependencies

```go
// Initialize logger
logger := logging.NewLogger("paypal-service")

// Create PayPal client
paypalClient := paypal.NewClient(paypalConfig, logger)

// Create PayPal usecase with dependencies
paypalUseCase := paypalUC.NewService(
    readDB,
    writeDB,
    paypalClient,
    creditUC,
    subscriptionUC,
    eventPublisher,
    logger,
)
```

### 2. Create Payment Factory with Dependencies

```go
// Create factory with PayPal dependencies
factory := payment.NewFactoryWithDependencies(
    paymentConfig,
    logger,
    paypalUseCase,
    paypalClient,
)

// Create PayPal processor
processor, err := factory.CreateProcessor(payment.ProcessorTypePayPal)
if err != nil {
    log.Fatalf("Failed to create PayPal processor: %v", err)
}
```

## Usage Examples

### Credit Topup Payment

```go
// Create payment request for credit topup
paymentReq := &payment.PaymentRequest{
    UserID:        "user-123",
    Amount:        1000, // $10.00 in cents
    Currency:      "USD",
    PaymentMethod: "paypal",
    Description:   "Credit topup",
    OrderID:       "order-123",
    Metadata: map[string]interface{}{
        "purpose": "credit_topup",
    },
}

// Process payment
paymentResp, err := processor.ProcessPayment(ctx, paymentReq)
if err != nil {
    return fmt.Errorf("failed to process payment: %w", err)
}

// Redirect user to PayPal approval URL
fmt.Printf("Redirect user to: %s\n", paymentResp.PaymentURL)
```

### Subscription Payment

```go
// Create payment request for subscription
paymentReq := &payment.PaymentRequest{
    UserID:        "user-123",
    Amount:        2999, // $29.99 in cents
    Currency:      "USD",
    PaymentMethod: "paypal",
    Description:   "Monthly subscription",
    OrderID:       "order-456",
    Metadata: map[string]interface{}{
        "purpose": "subscription",
        "plan_id": "plan-premium",
    },
}

// Process payment
paymentResp, err := processor.ProcessPayment(ctx, paymentReq)
if err != nil {
    return fmt.Errorf("failed to process subscription payment: %w", err)
}
```

### Payment Status Check

```go
// Get payment status
status, err := processor.GetPaymentStatus(ctx, paymentID)
if err != nil {
    return fmt.Errorf("failed to get payment status: %w", err)
}

fmt.Printf("Payment Status: %s\n", status.Status)
```

### Refund Payment

```go
// Create refund request
refundReq := &payment.RefundRequest{
    PaymentID: paymentID,
    Amount:    500, // Partial refund of $5.00
    Reason:    "Customer requested refund",
}

// Process refund
refundResp, err := processor.RefundPayment(ctx, refundReq)
if err != nil {
    return fmt.Errorf("failed to refund payment: %w", err)
}

fmt.Printf("Refund ID: %s\n", refundResp.RefundID)
```

## Payment Flow

### 1. Credit Topup Flow

1. **Initiate Payment**: Create PayPal order via usecase
2. **User Approval**: Redirect user to PayPal for approval
3. **Execute Payment**: Capture payment after user approval
4. **Add Credits**: Add credits to user account
5. **Publish Event**: Publish payment completed event

### 2. Subscription Flow

1. **Initiate Payment**: Create PayPal order for subscription
2. **User Approval**: Redirect user to PayPal for approval
3. **Execute Payment**: Capture initial subscription payment
4. **Create Subscription**: Create subscription record via subscription service
5. **Activate Plan**: Activate user's subscription plan

### 3. Refund Flow

1. **Request Refund**: Submit refund request with payment ID
2. **Process Refund**: Call PayPal API to process refund
3. **Update Status**: Update payment status in database
4. **Publish Event**: Publish refund completed event

## Error Handling

The PayPal processor includes comprehensive error handling:

```go
// Payment processing with error handling
paymentResp, err := processor.ProcessPayment(ctx, paymentReq)
if err != nil {
    // Handle different error types
    switch {
    case strings.Contains(err.Error(), "PayPal payment initiation failed"):
        // Handle PayPal API errors
        log.Printf("PayPal API error: %v", err)
    case strings.Contains(err.Error(), "failed to create PayPal payment"):
        // Handle payment creation errors
        log.Printf("Payment creation error: %v", err)
    default:
        // Handle general errors
        log.Printf("General payment error: %v", err)
    }
    return err
}
```

## Fallback Behavior

The PayPal processor includes fallback behavior when dependencies are not available:

- **With Dependencies**: Uses real PayPal usecase and client
- **Without Dependencies**: Falls back to mock implementation
- **Graceful Degradation**: Logs warnings and continues with mock behavior

## Event-Driven Architecture

The PayPal integration publishes events for:

- `payment.completed` - When payment is successfully processed
- `payment.failed` - When payment processing fails
- `payment.cancelled` - When payment is cancelled
- `payment.refunded` - When payment is refunded

## Testing

Run the integration example:

```bash
go run examples/paypal_integration.go
```

This will demonstrate all PayPal processor features with mock implementations.

## Production Considerations

1. **Environment Configuration**: Use `live` environment for production
2. **Webhook Handling**: Implement webhook endpoints for payment notifications
3. **Error Monitoring**: Monitor PayPal API errors and failures
4. **Security**: Secure PayPal credentials and webhook signatures
5. **Logging**: Comprehensive logging for payment operations
6. **Database Integration**: Implement real database operations instead of mocks

## Dependencies

- PayPal UseCase: Business logic implementation
- PayPal Client: API integration
- Credit UseCase: For credit operations
- Subscription UseCase: For subscription management
- Event Publisher: For publishing payment events
- Database: For storing payment records

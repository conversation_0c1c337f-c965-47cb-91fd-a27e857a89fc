package models

import "time"

// InitiateBankTransferRequest represents initiate bank transfer request
type InitiateBankTransferRequest struct {
	UserID   string                 `json:"user_id" validate:"required"`
	PlanID   string                 `json:"plan_id,omitempty"` // Optional: if purchasing a plan
	Amount   int64                  `json:"amount" validate:"required,min=1"`
	Currency string                 `json:"currency" validate:"required"`
	Purpose  string                 `json:"purpose" validate:"required"` // "subscription", "credit_topup"
	UserInfo map[string]string      `json:"user_info" validate:"required"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// InitiateBankTransferResponse represents initiate bank transfer response
type InitiateBankTransferResponse struct {
	Success       bool                     `json:"success"`
	TransferID    string                   `json:"transfer_id"`
	ReferenceCode string                   `json:"reference_code"`
	Instructions  BankTransferInstructions `json:"instructions"`
	ExpiresAt     time.Time                `json:"expires_at"`
	ErrorMessage  string                   `json:"error_message,omitempty"`
}

// BankTransferInstructions represents bank transfer instructions
type BankTransferInstructions struct {
	BankName        string   `json:"bank_name"`
	AccountNumber   string   `json:"account_number"`
	AccountName     string   `json:"account_name"`
	Amount          int64    `json:"amount"`
	TransferContent string   `json:"transfer_content"`
	ReferenceCode   string   `json:"reference_code"`
	QRCodeURL       string   `json:"qr_code_url,omitempty"`
	Instructions    []string `json:"instructions"`
}

// BankTransferInfo represents bank transfer information
type BankTransferInfo struct {
	BaseModel
	TransferID           string                   `json:"transfer_id"`
	UserID               string                   `json:"user_id"`
	PlanID               string                   `json:"plan_id,omitempty"`
	Amount               int64                    `json:"amount"`
	Currency             string                   `json:"currency"`
	Status               string                   `json:"status"` // "pending", "confirmed", "expired", "cancelled"
	ReferenceCode        string                   `json:"reference_code"`
	Instructions         BankTransferInstructions `json:"instructions"`
	ExpiresAt            time.Time                `json:"expires_at"`
	ConfirmedAt          *time.Time               `json:"confirmed_at,omitempty"`
	BankTransactionID    string                   `json:"bank_transaction_id,omitempty"`
	ActualAmountReceived int64                    `json:"actual_amount_received,omitempty"`
	ConfirmationMethod   string                   `json:"confirmation_method,omitempty"` // "manual", "auto_webhook"
	ConfirmedBy          string                   `json:"confirmed_by,omitempty"`
	Purpose              string                   `json:"purpose"`
	UserInfo             map[string]string        `json:"user_info"`
	Metadata             map[string]interface{}   `json:"metadata,omitempty"`
}

// ConfirmBankTransferRequest represents confirm bank transfer request
type ConfirmBankTransferRequest struct {
	TransferID         string   `json:"transfer_id" validate:"required"`
	ReferenceCode      string   `json:"reference_code" validate:"required"`
	ActualAmount       int64    `json:"actual_amount" validate:"required,min=1"`
	BankTransactionID  string   `json:"bank_transaction_id" validate:"required"`
	ConfirmedBy        string   `json:"confirmed_by" validate:"required"`
	ConfirmationNotes  string   `json:"confirmation_notes,omitempty"`
	ProofImages        []string `json:"proof_images,omitempty"`
	ConfirmationMethod string   `json:"confirmation_method,omitempty"` // "manual", "auto_webhook"
}

// ConfirmBankTransferResponse represents confirm bank transfer response
type ConfirmBankTransferResponse struct {
	Success          bool                        `json:"success"`
	SubscriptionID   string                      `json:"subscription_id,omitempty"`
	CreditsAdded     int32                       `json:"credits_added"`
	ErrorMessage     string                      `json:"error_message,omitempty"`
	TransactionID    string                      `json:"transaction_id,omitempty"`
	SubscriptionInfo *SubscriptionActivationInfo `json:"subscription_info,omitempty"`
}

// SubscriptionActivationInfo represents subscription activation information
type SubscriptionActivationInfo struct {
	SubscriptionID string    `json:"subscription_id"`
	PlanName       string    `json:"plan_name"`
	StartsAt       time.Time `json:"starts_at"`
	EndsAt         time.Time `json:"ends_at"`
	CreditsAdded   int32     `json:"credits_added"`
}

// ListBankTransfersRequest represents list bank transfers request
type ListBankTransfersRequest struct {
	UserID   string     `json:"user_id" validate:"required"`
	Status   string     `json:"status,omitempty"`
	Purpose  string     `json:"purpose,omitempty"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Page     int        `json:"page" validate:"min=1"`
	Limit    int        `json:"limit" validate:"min=1,max=100"`
}

// ListBankTransfersResponse represents list bank transfers response
type ListBankTransfersResponse struct {
	Transfers  []BankTransferInfo  `json:"transfers"`
	Pagination PaginationMeta      `json:"pagination"`
	Summary    BankTransferSummary `json:"summary"`
}

// BankTransferSummary represents bank transfer summary
type BankTransferSummary struct {
	TotalTransfers     int   `json:"total_transfers"`
	PendingTransfers   int   `json:"pending_transfers"`
	ConfirmedTransfers int   `json:"confirmed_transfers"`
	TotalAmount        int64 `json:"total_amount"`
	ConfirmedAmount    int64 `json:"confirmed_amount"`
}

// BankTransferValidationResult represents bank transfer validation result
type BankTransferValidationResult struct {
	Valid          bool   `json:"valid"`
	TransferID     string `json:"transfer_id,omitempty"`
	ExpectedAmount int64  `json:"expected_amount"`
	ActualAmount   int64  `json:"actual_amount"`
	AmountMatch    bool   `json:"amount_match"`
	Status         string `json:"status"`
	ErrorMessage   string `json:"error_message,omitempty"`
}

// BankTransferStatsRequest represents bank transfer statistics request
type BankTransferStatsRequest struct {
	UserID   string     `json:"user_id,omitempty"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Period   string     `json:"period,omitempty"`   // "day", "week", "month", "year"
	GroupBy  string     `json:"group_by,omitempty"` // "day", "week", "month"
}

// BankTransferStatsResponse represents bank transfer statistics response
type BankTransferStatsResponse struct {
	Period             string                      `json:"period"`
	TotalTransfers     int                         `json:"total_transfers"`
	ConfirmedTransfers int                         `json:"confirmed_transfers"`
	PendingTransfers   int                         `json:"pending_transfers"`
	ExpiredTransfers   int                         `json:"expired_transfers"`
	TotalAmount        int64                       `json:"total_amount"`
	ConfirmedAmount    int64                       `json:"confirmed_amount"`
	AverageAmount      int64                       `json:"average_amount"`
	ConversionRate     float64                     `json:"conversion_rate"`
	Timeline           []BankTransferTimelinePoint `json:"timeline"`
	StatusBreakdown    map[string]int              `json:"status_breakdown"`
	PurposeBreakdown   map[string]int              `json:"purpose_breakdown"`
}

// BankTransferTimelinePoint represents a point in bank transfer timeline
type BankTransferTimelinePoint struct {
	Date               string `json:"date"`
	TotalTransfers     int    `json:"total_transfers"`
	ConfirmedTransfers int    `json:"confirmed_transfers"`
	TotalAmount        int64  `json:"total_amount"`
	ConfirmedAmount    int64  `json:"confirmed_amount"`
}

// AdminListBankTransfersRequest represents admin list bank transfers request
type AdminListBankTransfersRequest struct {
	Status    string     `json:"status,omitempty"`
	Purpose   string     `json:"purpose,omitempty"`
	UserID    string     `json:"user_id,omitempty"`
	DateFrom  *time.Time `json:"date_from,omitempty"`
	DateTo    *time.Time `json:"date_to,omitempty"`
	MinAmount *int64     `json:"min_amount,omitempty"`
	MaxAmount *int64     `json:"max_amount,omitempty"`
	Page      int        `json:"page" validate:"min=1"`
	Limit     int        `json:"limit" validate:"min=1,max=100"`
	SortBy    string     `json:"sort_by,omitempty"`
	SortOrder string     `json:"sort_order,omitempty"`
}

// AdminBankTransfersResponse represents admin bank transfers response
type AdminBankTransfersResponse struct {
	Transfers  []BankTransferInfo        `json:"transfers"`
	Pagination PaginationMeta            `json:"pagination"`
	Summary    BankTransferSummary       `json:"summary"`
	Statistics BankTransferStatsResponse `json:"statistics"`
}

// AdminConfirmBankTransferRequest represents admin confirm bank transfer request
type AdminConfirmBankTransferRequest struct {
	TransferID        string                 `json:"transfer_id" validate:"required"`
	ActualAmount      int64                  `json:"actual_amount" validate:"required,min=1"`
	BankTransactionID string                 `json:"bank_transaction_id" validate:"required"`
	ConfirmationNotes string                 `json:"confirmation_notes,omitempty"`
	ProofImages       []string               `json:"proof_images,omitempty"`
	AdminUserID       string                 `json:"admin_user_id" validate:"required"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// BankWebhookRequest represents bank webhook request
type BankWebhookRequest struct {
	Provider  string                 `json:"provider" validate:"required"`
	EventType string                 `json:"event_type" validate:"required"`
	Data      map[string]interface{} `json:"data" validate:"required"`
	Signature string                 `json:"signature,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// BankWebhookResponse represents bank webhook response
type BankWebhookResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	TransferID   string `json:"transfer_id,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// BankWebhookEvent represents bank webhook event
type BankWebhookEvent struct {
	Provider          string                 `json:"provider"`
	EventType         string                 `json:"event_type"`
	ReferenceCode     string                 `json:"reference_code"`
	BankTransactionID string                 `json:"bank_transaction_id"`
	Amount            int64                  `json:"amount"`
	Currency          string                 `json:"currency"`
	TransferDate      time.Time              `json:"transfer_date"`
	SenderInfo        map[string]string      `json:"sender_info,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// Bank configuration for different banks
type BankConfig struct {
	BankName      string `json:"bank_name"`
	AccountNumber string `json:"account_number"`
	AccountName   string `json:"account_name"`
	BankCode      string `json:"bank_code"`
	BranchName    string `json:"branch_name,omitempty"`
	SwiftCode     string `json:"swift_code,omitempty"`
}

// Default bank configurations for Vietnam
var DefaultBankConfigs = map[string]BankConfig{
	"vietcombank": {
		BankName:      "Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)",
		AccountNumber: "**********",
		AccountName:   "CONG TY TNHH SOCIAL AI",
		BankCode:      "VCB",
		BranchName:    "Chi nhánh Hà Nội",
		SwiftCode:     "BFTVVNVX",
	},
	"techcombank": {
		BankName:      "Ngân hàng TMCP Kỹ thương Việt Nam (Techcombank)",
		AccountNumber: "**********",
		AccountName:   "CONG TY TNHH SOCIAL AI",
		BankCode:      "TCB",
		BranchName:    "Chi nhánh Hà Nội",
		SwiftCode:     "VTCBVNVX",
	},
	"bidv": {
		BankName:      "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)",
		AccountNumber: "**********",
		AccountName:   "CONG TY TNHH SOCIAL AI",
		BankCode:      "BIDV",
		BranchName:    "Chi nhánh Hà Nội",
		SwiftCode:     "BIDVVNVX",
	},
}

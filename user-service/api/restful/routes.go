package restful

import (
	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/api/restful/handlers"
	"github.com/social-content-ai/user-service/api/restful/middleware"
	"github.com/social-content-ai/user-service/usecase/auth"
	"github.com/social-content-ai/user-service/usecase/session"
	"github.com/social-content-ai/user-service/usecase/twofa"
	"github.com/social-content-ai/user-service/usecase/user"
)

// SetupRoutes configures all API routes for the user service
func SetupRoutes(
	router *gin.Engine,
	authUseCase auth.UseCase,
	userUseCase user.UseCase,
	sessionUseCase session.UseCase,
	twofaUseCase twofa.UseCase, // twofa.UseCase - using interface{} to avoid import issues
	cleanupScheduler interface{}, // *cleanup.Scheduler - using interface{} to avoid import issues
	logger logging.Logger,
) {
	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authUseCase, logger)
	userHandler := handlers.NewUserHandler(userUseCase, logger)
	sessionHandler := handlers.NewSessionHandler(sessionUseCase, logger)

	// Initialize 2FA handler if usecase is provided
	var twofaHandler *handlers.TwoFAHandler
	if twofaUseCase != nil {
		// Type assertion to get the actual twofa usecase
		// twofaHandler = handlers.NewTwoFAHandler(twofaUseCase.(twofa.UseCase), logger)
	}

	// Initialize cleanup handler if scheduler is provided
	var cleanupHandler *handlers.CleanupHandler
	if cleanupScheduler != nil {
		// Type assertion to get the actual cleanup scheduler
		// cleanupHandler = handlers.NewCleanupHandler(cleanupScheduler.(*cleanup.Scheduler), logger)
	}

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(authUseCase, logger)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "user-service",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", authHandler.Logout)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/forgot-password", authHandler.ForgotPassword)
			auth.POST("/reset-password", authHandler.ResetPassword)
			auth.POST("/verify-email", authHandler.VerifyEmail)
			auth.POST("/resend-verification", authHandler.ResendVerification)

			// Two-Factor Authentication routes (protected)
			twofa := auth.Group("/2fa")
			twofa.Use(authMiddleware.RequireAuth())
			{
				twofa.POST("/setup", twofaHandler.SetupTwoFA)
				twofa.POST("/enable", twofaHandler.EnableTwoFA)
				twofa.POST("/disable", twofaHandler.DisableTwoFA)
				twofa.POST("/verify", twofaHandler.VerifyTwoFA)
				twofa.POST("/backup-codes", twofaHandler.GenerateBackupCodes)
				twofa.GET("/status", twofaHandler.GetTwoFAStatus)
			}
		}

		// User routes (protected)
		users := v1.Group("/users")
		users.Use(authMiddleware.RequireAuth())
		{
			// Current user operations
			users.GET("/me", userHandler.GetCurrentUser)
			users.PUT("/me", userHandler.UpdateCurrentUser)
			users.DELETE("/me", userHandler.DeleteCurrentUser)
			users.POST("/me/change-password", userHandler.ChangePassword)
			users.POST("/me/avatar/upload-url", userHandler.RequestAvatarUpload)
			users.POST("/me/avatar/confirm", userHandler.ConfirmAvatarUpload)
			users.GET("/me/avatar", userHandler.GetCurrentAvatar)
			users.DELETE("/me/avatar", userHandler.DeleteCurrentAvatar)

			// User management (admin only)
			admin := users.Group("")
			admin.Use(authMiddleware.RequireRole("admin"))
			{
				admin.GET("", userHandler.ListUsers)
				admin.GET("/:id", userHandler.GetUser)
				admin.PUT("/:id", userHandler.UpdateUser)
				admin.DELETE("/:id", userHandler.DeleteUser)
				admin.POST("/:id/lock", userHandler.LockUser)
				admin.POST("/:id/unlock", userHandler.UnlockUser)
				admin.POST("/:id/reset-password", userHandler.AdminResetPassword)

				// Cleanup management routes
				if cleanupHandler != nil {
					cleanup := admin.Group("/cleanup")
					{
						cleanup.GET("/stats", cleanupHandler.GetCleanupStats)
						cleanup.GET("/status", cleanupHandler.GetCleanupStatus)
						cleanup.POST("/trigger", cleanupHandler.TriggerCleanup)
						cleanup.POST("/start", cleanupHandler.StartCleanupService)
						cleanup.POST("/stop", cleanupHandler.StopCleanupService)
					}
				}
			}
		}

		// Session routes (protected)
		sessions := v1.Group("/sessions")
		sessions.Use(authMiddleware.RequireAuth())
		{
			sessions.GET("", sessionHandler.ListSessions)
			sessions.DELETE("/:id", sessionHandler.RevokeSession)
			sessions.DELETE("", sessionHandler.RevokeAllSessions)
		}
	}
}

# Makefile for AI Content Service

.PHONY: all build test clean run docker proto ent deps lint fmt vet

# Variables
SERVICE_NAME := ai-content-service
VERSION ?= latest
DOCKER_IMAGE := $(SERVICE_NAME):$(VERSION)
GO_VERSION := 1.21

# Build info
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS := -ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Default target
all: deps proto ent build test

# Install dependencies
deps:
	@echo "📦 Installing dependencies..."
	go mod download
	go mod tidy

# Generate protocol buffers
proto:
	@echo "🔄 Generating protocol buffers..."
	cd ../proto-shared && make ai-content

# Generate Ent code
ent:
	@echo "🔄 Generating Ent code..."
	go generate ./ent

# Build the service
build:
	@echo "🔨 Building $(SERVICE_NAME)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(SERVICE_NAME) ./server.go

# Build for local development
build-local:
	@echo "🔨 Building $(SERVICE_NAME) for local development..."
	go build $(LDFLAGS) -o bin/$(SERVICE_NAME) ./server.go

# Run tests
test:
	@echo "🧪 Running tests..."
	go test -v -race -coverprofile=coverage.out ./...

# Run tests with coverage report
test-coverage:
	@echo "🧪 Running tests with coverage..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "📊 Coverage report generated: coverage.html"

# Run benchmarks
bench:
	@echo "⚡ Running benchmarks..."
	go test -bench=. -benchmem ./...

# Run the service locally
run: build-local
	@echo "🚀 Running $(SERVICE_NAME)..."
	./bin/$(SERVICE_NAME)

# Run with hot reload (requires air)
dev:
	@echo "🔥 Running $(SERVICE_NAME) with hot reload..."
	air

# Docker build
docker:
	@echo "🐳 Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

# Docker run
docker-run:
	@echo "🐳 Running Docker container..."
	docker run --rm -p 50052:50052 -p 8081:8081 $(DOCKER_IMAGE)

# Docker compose up
compose-up:
	@echo "🐳 Starting services with docker-compose..."
	docker-compose up -d

# Docker compose down
compose-down:
	@echo "🐳 Stopping services with docker-compose..."
	docker-compose down

# Lint code
lint:
	@echo "🔍 Linting code..."
	golangci-lint run

# Format code
fmt:
	@echo "✨ Formatting code..."
	go fmt ./...
	goimports -w .

# Vet code
vet:
	@echo "🔍 Vetting code..."
	go vet ./...

# Security scan
security:
	@echo "🔒 Running security scan..."
	gosec ./...

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	go clean -cache

# Database migrations
migrate-up:
	@echo "⬆️ Running database migrations..."
	go run ./cmd/migrate up

migrate-down:
	@echo "⬇️ Rolling back database migrations..."
	go run ./cmd/migrate down

migrate-create:
	@echo "📝 Creating new migration..."
	@read -p "Enter migration name: " name; \
	go run ./cmd/migrate create $$name

# Generate mocks
mocks:
	@echo "🎭 Generating mocks..."
	mockgen -source=usecase/generation/interface.go -destination=mocks/generation_usecase.go
	mockgen -source=usecase/improvement/interface.go -destination=mocks/improvement_usecase.go
	mockgen -source=pkg/ai/interface.go -destination=mocks/ai_service.go

# Install development tools
install-tools:
	@echo "🛠️ Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	go install github.com/golang/mock/mockgen@latest
	go install github.com/cosmtrek/air@latest
	go install entgo.io/ent/cmd/ent@latest

# Check if tools are installed
check-tools:
	@echo "🔍 Checking development tools..."
	@which golangci-lint > /dev/null || echo "❌ golangci-lint not found"
	@which goimports > /dev/null || echo "❌ goimports not found"
	@which gosec > /dev/null || echo "❌ gosec not found"
	@which mockgen > /dev/null || echo "❌ mockgen not found"
	@which air > /dev/null || echo "❌ air not found"
	@which ent > /dev/null || echo "❌ ent not found"

# Test AI providers
test-openai:
	@echo "🤖 Testing OpenAI integration..."
	go test -v ./pkg/ai/providers/openai/...

test-anthropic:
	@echo "🤖 Testing Anthropic integration..."
	go test -v ./pkg/ai/providers/anthropic/...

test-google:
	@echo "🤖 Testing Google AI integration..."
	go test -v ./pkg/ai/providers/google/...

# Load test
load-test:
	@echo "⚡ Running load tests..."
	go test -v ./tests/load/...

# Integration tests
integration-test:
	@echo "🔗 Running integration tests..."
	go test -v -tags=integration ./tests/integration/...

# Full CI pipeline
ci: deps proto ent fmt vet lint security test

# Release build
release: clean ci build docker
	@echo "🎉 Release build completed!"

# Start development environment
dev-env:
	@echo "🚀 Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d

# Stop development environment
dev-env-down:
	@echo "🛑 Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

# Monitor logs
logs:
	@echo "📋 Monitoring service logs..."
	docker-compose logs -f $(SERVICE_NAME)

# Health check
health:
	@echo "🏥 Checking service health..."
	curl -f http://localhost:8081/health || echo "Service is not healthy"

# Help
help:
	@echo "Available targets:"
	@echo "  all              - Run deps, proto, ent, build, and test"
	@echo "  deps             - Install Go dependencies"
	@echo "  proto            - Generate protocol buffer code"
	@echo "  ent              - Generate Ent ORM code"
	@echo "  build            - Build the service binary"
	@echo "  build-local      - Build for local development"
	@echo "  test             - Run tests"
	@echo "  test-coverage    - Run tests with coverage report"
	@echo "  bench            - Run benchmarks"
	@echo "  run              - Build and run the service"
	@echo "  dev              - Run with hot reload (requires air)"
	@echo "  docker           - Build Docker image"
	@echo "  docker-run       - Run Docker container"
	@echo "  compose-up       - Start with docker-compose"
	@echo "  compose-down     - Stop docker-compose services"
	@echo "  lint             - Lint code with golangci-lint"
	@echo "  fmt              - Format code"
	@echo "  vet              - Vet code"
	@echo "  security         - Run security scan"
	@echo "  clean            - Clean build artifacts"
	@echo "  migrate-up       - Run database migrations"
	@echo "  migrate-down     - Rollback database migrations"
	@echo "  migrate-create   - Create new migration"
	@echo "  mocks            - Generate mocks"
	@echo "  install-tools    - Install development tools"
	@echo "  check-tools      - Check if tools are installed"
	@echo "  test-openai      - Test OpenAI integration"
	@echo "  test-anthropic   - Test Anthropic integration"
	@echo "  test-google      - Test Google AI integration"
	@echo "  load-test        - Run load tests"
	@echo "  integration-test - Run integration tests"
	@echo "  ci               - Run full CI pipeline"
	@echo "  release          - Build release version"
	@echo "  dev-env          - Start development environment"
	@echo "  dev-env-down     - Stop development environment"
	@echo "  logs             - Monitor service logs"
	@echo "  health           - Check service health"
	@echo "  help             - Show this help message"

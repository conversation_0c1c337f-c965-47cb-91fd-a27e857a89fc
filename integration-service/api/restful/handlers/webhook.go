package handlers

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/integration-service/usecase/platform"
)

// WebhookHandler handles webhook endpoints
type WebhookHandler struct {
	*BaseHandler
}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler(base *BaseHandler) *WebhookHandler {
	return &WebhookHandler{
		BaseHandler: base,
	}
}

// FacebookWebhook handles Facebook webhook events
func (h *WebhookHandler) FacebookWebhook(c *gin.Context) {
	h.logger.Info("Facebook webhook received")

	// Verify webhook (Facebook sends GET request for verification)
	if c.Request.Method == "GET" {
		mode := c.Query("hub.mode")
		token := c.Query("hub.verify_token")
		challenge := c.Query("hub.challenge")

		// TODO: Verify token against configured webhook token
		if mode == "subscribe" && token == "facebook_webhook_token" {
			h.logger.Info("Facebook webhook verified")
			c.String(http.StatusOK, challenge)
			return
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "verification_failed"})
		return
	}

	// Process webhook payload
	signature := c.GetHeader("X-Hub-Signature-256")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyFacebookSignature(signature, payload) {
		h.logger.Error("Invalid Facebook webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "facebook",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process Facebook webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("Facebook webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("Facebook webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// TwitterWebhook handles Twitter webhook events
func (h *WebhookHandler) TwitterWebhook(c *gin.Context) {
	h.logger.Info("Twitter webhook received")

	signature := c.GetHeader("X-Twitter-Webhooks-Signature")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyTwitterSignature(signature, payload) {
		h.logger.Error("Invalid Twitter webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "twitter",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process Twitter webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("Twitter webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("Twitter webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// InstagramWebhook handles Instagram webhook events
func (h *WebhookHandler) InstagramWebhook(c *gin.Context) {
	h.logger.Info("Instagram webhook received")

	// Verify webhook (Instagram sends GET request for verification)
	if c.Request.Method == "GET" {
		mode := c.Query("hub.mode")
		token := c.Query("hub.verify_token")
		challenge := c.Query("hub.challenge")

		// TODO: Verify token against configured webhook token
		if mode == "subscribe" && token == "instagram_webhook_token" {
			h.logger.Info("Instagram webhook verified")
			c.String(http.StatusOK, challenge)
			return
		}

		c.JSON(http.StatusForbidden, gin.H{"error": "verification_failed"})
		return
	}

	signature := c.GetHeader("X-Hub-Signature-256")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyInstagramSignature(signature, payload) {
		h.logger.Error("Invalid Instagram webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "instagram",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process Instagram webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("Instagram webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("Instagram webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// LinkedInWebhook handles LinkedIn webhook events
func (h *WebhookHandler) LinkedInWebhook(c *gin.Context) {
	h.logger.Info("LinkedIn webhook received")

	signature := c.GetHeader("X-LinkedIn-Signature")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyLinkedInSignature(signature, payload) {
		h.logger.Error("Invalid LinkedIn webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "linkedin",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process LinkedIn webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("LinkedIn webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("LinkedIn webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// YouTubeWebhook handles YouTube webhook events
func (h *WebhookHandler) YouTubeWebhook(c *gin.Context) {
	h.logger.Info("YouTube webhook received")

	signature := c.GetHeader("X-YouTube-Signature")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyYouTubeSignature(signature, payload) {
		h.logger.Error("Invalid YouTube webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "youtube",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process YouTube webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("YouTube webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("YouTube webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// TikTokWebhook handles TikTok webhook events
func (h *WebhookHandler) TikTokWebhook(c *gin.Context) {
	h.logger.Info("TikTok webhook received")

	signature := c.GetHeader("X-TikTok-Signature")
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid_payload"})
		return
	}

	// Verify signature
	if !h.verifyTikTokSignature(signature, payload) {
		h.logger.Error("Invalid TikTok webhook signature")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_signature"})
		return
	}

	// Process webhook event through platform service
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	req := &platform.ProcessWebhookRequest{
		Platform:  "tiktok",
		Signature: signature,
		Payload:   payload,
		Headers:   headers,
	}

	response, err := h.platformUseCase.ProcessWebhook(context.Background(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process TikTok webhook")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "processing_failed"})
		return
	}

	if !response.Success {
		h.logger.WithField("error", response.ErrorMessage).Error("TikTok webhook processing failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": response.ErrorMessage})
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"event_id":     response.EventID,
		"event_type":   response.EventType,
		"payload_size": len(payload),
	}).Info("TikTok webhook processed successfully")
	c.JSON(http.StatusOK, gin.H{"status": "received"})
}

// verifyFacebookSignature verifies Facebook webhook signature
func (h *WebhookHandler) verifyFacebookSignature(signature string, payload []byte) bool {
	// TODO: Get app secret from config
	appSecret := "your_facebook_app_secret"

	if !strings.HasPrefix(signature, "sha256=") {
		return false
	}

	expectedSignature := signature[7:] // Remove "sha256=" prefix
	mac := hmac.New(sha256.New, []byte(appSecret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(expectedSignature), []byte(computedSignature))
}

// verifyTwitterSignature verifies Twitter webhook signature
func (h *WebhookHandler) verifyTwitterSignature(signature string, payload []byte) bool {
	// TODO: Get consumer secret from config
	consumerSecret := "your_twitter_consumer_secret"

	if !strings.HasPrefix(signature, "sha256=") {
		return false
	}

	expectedSignature := signature[7:] // Remove "sha256=" prefix
	mac := hmac.New(sha256.New, []byte(consumerSecret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(expectedSignature), []byte(computedSignature))
}

// verifyInstagramSignature verifies Instagram webhook signature (same as Facebook)
func (h *WebhookHandler) verifyInstagramSignature(signature string, payload []byte) bool {
	return h.verifyFacebookSignature(signature, payload)
}

// verifyLinkedInSignature verifies LinkedIn webhook signature
func (h *WebhookHandler) verifyLinkedInSignature(signature string, payload []byte) bool {
	// TODO: Get client secret from config
	clientSecret := "your_linkedin_client_secret"

	mac := hmac.New(sha256.New, []byte(clientSecret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(computedSignature))
}

// verifyYouTubeSignature verifies YouTube webhook signature
func (h *WebhookHandler) verifyYouTubeSignature(signature string, payload []byte) bool {
	// YouTube uses different verification method
	// TODO: Implement YouTube-specific signature verification
	return true // Placeholder
}

// verifyTikTokSignature verifies TikTok webhook signature
func (h *WebhookHandler) verifyTikTokSignature(signature string, payload []byte) bool {
	// TODO: Get client secret from config
	clientSecret := "your_tiktok_client_secret"

	mac := hmac.New(sha256.New, []byte(clientSecret))
	mac.Write(payload)
	computedSignature := hex.EncodeToString(mac.Sum(nil))

	return hmac.Equal([]byte(signature), []byte(computedSignature))
}

package restful

import (
	"github.com/gin-gonic/gin"

	"github.com/social-content-ai/integration-service/api/restful/handlers"
	"github.com/social-content-ai/integration-service/api/restful/middleware"
	"github.com/social-content-ai/integration-service/usecase/integration"
	"github.com/social-content-ai/integration-service/usecase/platform"
	"github.com/social-content-ai/integration-service/usecase/publishing"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// SetupRoutes sets up all RESTful API routes
func SetupRoutes(
	router *gin.Engine,
	integrationUseCase integration.UseCase,
	platformUseCase platform.UseCase,
	publishingUseCase publishing.UseCase,
	userClient userv1.AuthServiceClient,
	logger logging.Logger,
) {
	// Create auth middleware
	authMiddleware := middleware.NewAuthMiddleware(userClient, logger)

	// Create base handler with all dependencies
	baseHandler := handlers.NewBaseHandler(
		integrationUseCase,
		platformUseCase,
		publishingUseCase,
		userClient,
		logger,
	)

	// Create specific handlers
	oauthHandler := handlers.NewOAuthHandler(baseHandler)
	webhookHandler := handlers.NewWebhookHandler(baseHandler)
	healthHandler := handlers.NewHealthHandler(baseHandler)
	integrationHandler := handlers.NewIntegrationHandler(baseHandler)
	publishingHandler := handlers.NewPublishingHandler(baseHandler)
	platformHandler := handlers.NewPlatformHandler(baseHandler)

	// OAuth callback routes
	oauth := router.Group("/oauth")
	{
		oauth.GET("/facebook/callback", oauthHandler.FacebookCallback)
		oauth.GET("/twitter/callback", oauthHandler.TwitterCallback)
		oauth.GET("/instagram/callback", oauthHandler.InstagramCallback)
		oauth.GET("/linkedin/callback", oauthHandler.LinkedInCallback)
		oauth.GET("/youtube/callback", oauthHandler.YouTubeCallback)
		oauth.GET("/tiktok/callback", oauthHandler.TikTokCallback)
	}

	// Webhook routes
	webhooks := router.Group("/webhooks")
	{
		webhooks.POST("/facebook", webhookHandler.FacebookWebhook)
		webhooks.POST("/twitter", webhookHandler.TwitterWebhook)
		webhooks.POST("/instagram", webhookHandler.InstagramWebhook)
		webhooks.POST("/linkedin", webhookHandler.LinkedInWebhook)
		webhooks.POST("/youtube", webhookHandler.YouTubeWebhook)
		webhooks.POST("/tiktok", webhookHandler.TikTokWebhook)
	}

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Integration management routes (protected)
		integrations := v1.Group("/integrations")
		integrations.Use(authMiddleware.RequireAuth())
		{
			integrations.POST("/oauth/url", integrationHandler.GetOAuthURL)
			integrations.GET("", integrationHandler.ListIntegrations)
			integrations.GET("/:id", integrationHandler.GetIntegration)
			integrations.DELETE("/:id", integrationHandler.DeleteIntegration)
			integrations.POST("/:id/refresh", integrationHandler.RefreshIntegration)
		}

		// Publishing routes (protected)
		publishing := v1.Group("/publishing")
		publishing.Use(authMiddleware.RequireAuth())
		{
			publishing.POST("/posts", publishingHandler.PublishPost)
			publishing.POST("/posts/analytics", publishingHandler.GetPostAnalytics)
			publishing.POST("/accounts/analytics", publishingHandler.GetAccountAnalytics)
		}

		// Platform routes (public info, protected limits)
		platform := v1.Group("/platform")
		{
			platform.GET("/info", platformHandler.GetPlatformInfo)                                    // Public
			platform.POST("/limits", authMiddleware.RequireAuth(), platformHandler.GetPlatformLimits) // Protected
		}
	}

	// Health check
	router.GET("/health", healthHandler.HealthCheck)
}

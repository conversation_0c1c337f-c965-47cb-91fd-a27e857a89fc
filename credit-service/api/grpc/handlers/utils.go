package handlers

// convertProtoMapToInterface converts proto map to interface map
func convertProtoMapToInterface(protoMap map[string]string) map[string]interface{} {
	if protoMap == nil {
		return nil
	}
	result := make(map[string]interface{})
	for k, v := range protoMap {
		result[k] = v
	}
	return result
}

// convertInterfaceMapToProto converts interface map to proto map
func convertInterfaceMapToProto(interfaceMap map[string]interface{}) map[string]string {
	if interfaceMap == nil {
		return nil
	}
	result := make(map[string]string)
	for k, v := range interfaceMap {
		if str, ok := v.(string); ok {
			result[k] = str
		}
	}
	return result
}

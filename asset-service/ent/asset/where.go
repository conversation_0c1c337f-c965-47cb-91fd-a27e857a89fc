// Code generated by ent, DO NOT EDIT.

package asset

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldUserID, v))
}

// WorkspaceID applies equality check predicate on the "workspace_id" field. It's identical to WorkspaceIDEQ.
func WorkspaceID(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldWorkspaceID, v))
}

// FolderID applies equality check predicate on the "folder_id" field. It's identical to FolderIDEQ.
func FolderID(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFolderID, v))
}

// FileName applies equality check predicate on the "file_name" field. It's identical to FileNameEQ.
func FileName(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileName, v))
}

// OriginalName applies equality check predicate on the "original_name" field. It's identical to OriginalNameEQ.
func OriginalName(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldOriginalName, v))
}

// FileType applies equality check predicate on the "file_type" field. It's identical to FileTypeEQ.
func FileType(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileType, v))
}

// FileExtension applies equality check predicate on the "file_extension" field. It's identical to FileExtensionEQ.
func FileExtension(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileExtension, v))
}

// S3Key applies equality check predicate on the "s3_key" field. It's identical to S3KeyEQ.
func S3Key(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldS3Key, v))
}

// S3Bucket applies equality check predicate on the "s3_bucket" field. It's identical to S3BucketEQ.
func S3Bucket(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldS3Bucket, v))
}

// ContentHash applies equality check predicate on the "content_hash" field. It's identical to ContentHashEQ.
func ContentHash(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldContentHash, v))
}

// Purpose applies equality check predicate on the "purpose" field. It's identical to PurposeEQ.
func Purpose(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldPurpose, v))
}

// FileSize applies equality check predicate on the "file_size" field. It's identical to FileSizeEQ.
func FileSize(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileSize, v))
}

// ThumbnailURL applies equality check predicate on the "thumbnail_url" field. It's identical to ThumbnailURLEQ.
func ThumbnailURL(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldThumbnailURL, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldStatus, v))
}

// IsPublic applies equality check predicate on the "is_public" field. It's identical to IsPublicEQ.
func IsPublic(v bool) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldIsPublic, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldUserID, v))
}

// WorkspaceIDEQ applies the EQ predicate on the "workspace_id" field.
func WorkspaceIDEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldWorkspaceID, v))
}

// WorkspaceIDNEQ applies the NEQ predicate on the "workspace_id" field.
func WorkspaceIDNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldWorkspaceID, v))
}

// WorkspaceIDIn applies the In predicate on the "workspace_id" field.
func WorkspaceIDIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDNotIn applies the NotIn predicate on the "workspace_id" field.
func WorkspaceIDNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDGT applies the GT predicate on the "workspace_id" field.
func WorkspaceIDGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldWorkspaceID, v))
}

// WorkspaceIDGTE applies the GTE predicate on the "workspace_id" field.
func WorkspaceIDGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldWorkspaceID, v))
}

// WorkspaceIDLT applies the LT predicate on the "workspace_id" field.
func WorkspaceIDLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldWorkspaceID, v))
}

// WorkspaceIDLTE applies the LTE predicate on the "workspace_id" field.
func WorkspaceIDLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldWorkspaceID, v))
}

// WorkspaceIDContains applies the Contains predicate on the "workspace_id" field.
func WorkspaceIDContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldWorkspaceID, v))
}

// WorkspaceIDHasPrefix applies the HasPrefix predicate on the "workspace_id" field.
func WorkspaceIDHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldWorkspaceID, v))
}

// WorkspaceIDHasSuffix applies the HasSuffix predicate on the "workspace_id" field.
func WorkspaceIDHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldWorkspaceID, v))
}

// WorkspaceIDIsNil applies the IsNil predicate on the "workspace_id" field.
func WorkspaceIDIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldWorkspaceID))
}

// WorkspaceIDNotNil applies the NotNil predicate on the "workspace_id" field.
func WorkspaceIDNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldWorkspaceID))
}

// WorkspaceIDEqualFold applies the EqualFold predicate on the "workspace_id" field.
func WorkspaceIDEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldWorkspaceID, v))
}

// WorkspaceIDContainsFold applies the ContainsFold predicate on the "workspace_id" field.
func WorkspaceIDContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldWorkspaceID, v))
}

// FolderIDEQ applies the EQ predicate on the "folder_id" field.
func FolderIDEQ(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFolderID, v))
}

// FolderIDNEQ applies the NEQ predicate on the "folder_id" field.
func FolderIDNEQ(v uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldFolderID, v))
}

// FolderIDIn applies the In predicate on the "folder_id" field.
func FolderIDIn(vs ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldFolderID, vs...))
}

// FolderIDNotIn applies the NotIn predicate on the "folder_id" field.
func FolderIDNotIn(vs ...uuid.UUID) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldFolderID, vs...))
}

// FolderIDIsNil applies the IsNil predicate on the "folder_id" field.
func FolderIDIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldFolderID))
}

// FolderIDNotNil applies the NotNil predicate on the "folder_id" field.
func FolderIDNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldFolderID))
}

// FileNameEQ applies the EQ predicate on the "file_name" field.
func FileNameEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileName, v))
}

// FileNameNEQ applies the NEQ predicate on the "file_name" field.
func FileNameNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldFileName, v))
}

// FileNameIn applies the In predicate on the "file_name" field.
func FileNameIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldFileName, vs...))
}

// FileNameNotIn applies the NotIn predicate on the "file_name" field.
func FileNameNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldFileName, vs...))
}

// FileNameGT applies the GT predicate on the "file_name" field.
func FileNameGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldFileName, v))
}

// FileNameGTE applies the GTE predicate on the "file_name" field.
func FileNameGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldFileName, v))
}

// FileNameLT applies the LT predicate on the "file_name" field.
func FileNameLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldFileName, v))
}

// FileNameLTE applies the LTE predicate on the "file_name" field.
func FileNameLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldFileName, v))
}

// FileNameContains applies the Contains predicate on the "file_name" field.
func FileNameContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldFileName, v))
}

// FileNameHasPrefix applies the HasPrefix predicate on the "file_name" field.
func FileNameHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldFileName, v))
}

// FileNameHasSuffix applies the HasSuffix predicate on the "file_name" field.
func FileNameHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldFileName, v))
}

// FileNameEqualFold applies the EqualFold predicate on the "file_name" field.
func FileNameEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldFileName, v))
}

// FileNameContainsFold applies the ContainsFold predicate on the "file_name" field.
func FileNameContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldFileName, v))
}

// OriginalNameEQ applies the EQ predicate on the "original_name" field.
func OriginalNameEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldOriginalName, v))
}

// OriginalNameNEQ applies the NEQ predicate on the "original_name" field.
func OriginalNameNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldOriginalName, v))
}

// OriginalNameIn applies the In predicate on the "original_name" field.
func OriginalNameIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldOriginalName, vs...))
}

// OriginalNameNotIn applies the NotIn predicate on the "original_name" field.
func OriginalNameNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldOriginalName, vs...))
}

// OriginalNameGT applies the GT predicate on the "original_name" field.
func OriginalNameGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldOriginalName, v))
}

// OriginalNameGTE applies the GTE predicate on the "original_name" field.
func OriginalNameGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldOriginalName, v))
}

// OriginalNameLT applies the LT predicate on the "original_name" field.
func OriginalNameLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldOriginalName, v))
}

// OriginalNameLTE applies the LTE predicate on the "original_name" field.
func OriginalNameLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldOriginalName, v))
}

// OriginalNameContains applies the Contains predicate on the "original_name" field.
func OriginalNameContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldOriginalName, v))
}

// OriginalNameHasPrefix applies the HasPrefix predicate on the "original_name" field.
func OriginalNameHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldOriginalName, v))
}

// OriginalNameHasSuffix applies the HasSuffix predicate on the "original_name" field.
func OriginalNameHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldOriginalName, v))
}

// OriginalNameEqualFold applies the EqualFold predicate on the "original_name" field.
func OriginalNameEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldOriginalName, v))
}

// OriginalNameContainsFold applies the ContainsFold predicate on the "original_name" field.
func OriginalNameContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldOriginalName, v))
}

// FileTypeEQ applies the EQ predicate on the "file_type" field.
func FileTypeEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileType, v))
}

// FileTypeNEQ applies the NEQ predicate on the "file_type" field.
func FileTypeNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldFileType, v))
}

// FileTypeIn applies the In predicate on the "file_type" field.
func FileTypeIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldFileType, vs...))
}

// FileTypeNotIn applies the NotIn predicate on the "file_type" field.
func FileTypeNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldFileType, vs...))
}

// FileTypeGT applies the GT predicate on the "file_type" field.
func FileTypeGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldFileType, v))
}

// FileTypeGTE applies the GTE predicate on the "file_type" field.
func FileTypeGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldFileType, v))
}

// FileTypeLT applies the LT predicate on the "file_type" field.
func FileTypeLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldFileType, v))
}

// FileTypeLTE applies the LTE predicate on the "file_type" field.
func FileTypeLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldFileType, v))
}

// FileTypeContains applies the Contains predicate on the "file_type" field.
func FileTypeContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldFileType, v))
}

// FileTypeHasPrefix applies the HasPrefix predicate on the "file_type" field.
func FileTypeHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldFileType, v))
}

// FileTypeHasSuffix applies the HasSuffix predicate on the "file_type" field.
func FileTypeHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldFileType, v))
}

// FileTypeEqualFold applies the EqualFold predicate on the "file_type" field.
func FileTypeEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldFileType, v))
}

// FileTypeContainsFold applies the ContainsFold predicate on the "file_type" field.
func FileTypeContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldFileType, v))
}

// FileExtensionEQ applies the EQ predicate on the "file_extension" field.
func FileExtensionEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileExtension, v))
}

// FileExtensionNEQ applies the NEQ predicate on the "file_extension" field.
func FileExtensionNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldFileExtension, v))
}

// FileExtensionIn applies the In predicate on the "file_extension" field.
func FileExtensionIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldFileExtension, vs...))
}

// FileExtensionNotIn applies the NotIn predicate on the "file_extension" field.
func FileExtensionNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldFileExtension, vs...))
}

// FileExtensionGT applies the GT predicate on the "file_extension" field.
func FileExtensionGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldFileExtension, v))
}

// FileExtensionGTE applies the GTE predicate on the "file_extension" field.
func FileExtensionGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldFileExtension, v))
}

// FileExtensionLT applies the LT predicate on the "file_extension" field.
func FileExtensionLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldFileExtension, v))
}

// FileExtensionLTE applies the LTE predicate on the "file_extension" field.
func FileExtensionLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldFileExtension, v))
}

// FileExtensionContains applies the Contains predicate on the "file_extension" field.
func FileExtensionContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldFileExtension, v))
}

// FileExtensionHasPrefix applies the HasPrefix predicate on the "file_extension" field.
func FileExtensionHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldFileExtension, v))
}

// FileExtensionHasSuffix applies the HasSuffix predicate on the "file_extension" field.
func FileExtensionHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldFileExtension, v))
}

// FileExtensionIsNil applies the IsNil predicate on the "file_extension" field.
func FileExtensionIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldFileExtension))
}

// FileExtensionNotNil applies the NotNil predicate on the "file_extension" field.
func FileExtensionNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldFileExtension))
}

// FileExtensionEqualFold applies the EqualFold predicate on the "file_extension" field.
func FileExtensionEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldFileExtension, v))
}

// FileExtensionContainsFold applies the ContainsFold predicate on the "file_extension" field.
func FileExtensionContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldFileExtension, v))
}

// S3KeyEQ applies the EQ predicate on the "s3_key" field.
func S3KeyEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldS3Key, v))
}

// S3KeyNEQ applies the NEQ predicate on the "s3_key" field.
func S3KeyNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldS3Key, v))
}

// S3KeyIn applies the In predicate on the "s3_key" field.
func S3KeyIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldS3Key, vs...))
}

// S3KeyNotIn applies the NotIn predicate on the "s3_key" field.
func S3KeyNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldS3Key, vs...))
}

// S3KeyGT applies the GT predicate on the "s3_key" field.
func S3KeyGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldS3Key, v))
}

// S3KeyGTE applies the GTE predicate on the "s3_key" field.
func S3KeyGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldS3Key, v))
}

// S3KeyLT applies the LT predicate on the "s3_key" field.
func S3KeyLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldS3Key, v))
}

// S3KeyLTE applies the LTE predicate on the "s3_key" field.
func S3KeyLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldS3Key, v))
}

// S3KeyContains applies the Contains predicate on the "s3_key" field.
func S3KeyContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldS3Key, v))
}

// S3KeyHasPrefix applies the HasPrefix predicate on the "s3_key" field.
func S3KeyHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldS3Key, v))
}

// S3KeyHasSuffix applies the HasSuffix predicate on the "s3_key" field.
func S3KeyHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldS3Key, v))
}

// S3KeyEqualFold applies the EqualFold predicate on the "s3_key" field.
func S3KeyEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldS3Key, v))
}

// S3KeyContainsFold applies the ContainsFold predicate on the "s3_key" field.
func S3KeyContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldS3Key, v))
}

// S3BucketEQ applies the EQ predicate on the "s3_bucket" field.
func S3BucketEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldS3Bucket, v))
}

// S3BucketNEQ applies the NEQ predicate on the "s3_bucket" field.
func S3BucketNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldS3Bucket, v))
}

// S3BucketIn applies the In predicate on the "s3_bucket" field.
func S3BucketIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldS3Bucket, vs...))
}

// S3BucketNotIn applies the NotIn predicate on the "s3_bucket" field.
func S3BucketNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldS3Bucket, vs...))
}

// S3BucketGT applies the GT predicate on the "s3_bucket" field.
func S3BucketGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldS3Bucket, v))
}

// S3BucketGTE applies the GTE predicate on the "s3_bucket" field.
func S3BucketGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldS3Bucket, v))
}

// S3BucketLT applies the LT predicate on the "s3_bucket" field.
func S3BucketLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldS3Bucket, v))
}

// S3BucketLTE applies the LTE predicate on the "s3_bucket" field.
func S3BucketLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldS3Bucket, v))
}

// S3BucketContains applies the Contains predicate on the "s3_bucket" field.
func S3BucketContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldS3Bucket, v))
}

// S3BucketHasPrefix applies the HasPrefix predicate on the "s3_bucket" field.
func S3BucketHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldS3Bucket, v))
}

// S3BucketHasSuffix applies the HasSuffix predicate on the "s3_bucket" field.
func S3BucketHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldS3Bucket, v))
}

// S3BucketEqualFold applies the EqualFold predicate on the "s3_bucket" field.
func S3BucketEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldS3Bucket, v))
}

// S3BucketContainsFold applies the ContainsFold predicate on the "s3_bucket" field.
func S3BucketContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldS3Bucket, v))
}

// ContentHashEQ applies the EQ predicate on the "content_hash" field.
func ContentHashEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldContentHash, v))
}

// ContentHashNEQ applies the NEQ predicate on the "content_hash" field.
func ContentHashNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldContentHash, v))
}

// ContentHashIn applies the In predicate on the "content_hash" field.
func ContentHashIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldContentHash, vs...))
}

// ContentHashNotIn applies the NotIn predicate on the "content_hash" field.
func ContentHashNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldContentHash, vs...))
}

// ContentHashGT applies the GT predicate on the "content_hash" field.
func ContentHashGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldContentHash, v))
}

// ContentHashGTE applies the GTE predicate on the "content_hash" field.
func ContentHashGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldContentHash, v))
}

// ContentHashLT applies the LT predicate on the "content_hash" field.
func ContentHashLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldContentHash, v))
}

// ContentHashLTE applies the LTE predicate on the "content_hash" field.
func ContentHashLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldContentHash, v))
}

// ContentHashContains applies the Contains predicate on the "content_hash" field.
func ContentHashContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldContentHash, v))
}

// ContentHashHasPrefix applies the HasPrefix predicate on the "content_hash" field.
func ContentHashHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldContentHash, v))
}

// ContentHashHasSuffix applies the HasSuffix predicate on the "content_hash" field.
func ContentHashHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldContentHash, v))
}

// ContentHashIsNil applies the IsNil predicate on the "content_hash" field.
func ContentHashIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldContentHash))
}

// ContentHashNotNil applies the NotNil predicate on the "content_hash" field.
func ContentHashNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldContentHash))
}

// ContentHashEqualFold applies the EqualFold predicate on the "content_hash" field.
func ContentHashEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldContentHash, v))
}

// ContentHashContainsFold applies the ContainsFold predicate on the "content_hash" field.
func ContentHashContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldContentHash, v))
}

// PurposeEQ applies the EQ predicate on the "purpose" field.
func PurposeEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldPurpose, v))
}

// PurposeNEQ applies the NEQ predicate on the "purpose" field.
func PurposeNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldPurpose, v))
}

// PurposeIn applies the In predicate on the "purpose" field.
func PurposeIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldPurpose, vs...))
}

// PurposeNotIn applies the NotIn predicate on the "purpose" field.
func PurposeNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldPurpose, vs...))
}

// PurposeGT applies the GT predicate on the "purpose" field.
func PurposeGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldPurpose, v))
}

// PurposeGTE applies the GTE predicate on the "purpose" field.
func PurposeGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldPurpose, v))
}

// PurposeLT applies the LT predicate on the "purpose" field.
func PurposeLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldPurpose, v))
}

// PurposeLTE applies the LTE predicate on the "purpose" field.
func PurposeLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldPurpose, v))
}

// PurposeContains applies the Contains predicate on the "purpose" field.
func PurposeContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldPurpose, v))
}

// PurposeHasPrefix applies the HasPrefix predicate on the "purpose" field.
func PurposeHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldPurpose, v))
}

// PurposeHasSuffix applies the HasSuffix predicate on the "purpose" field.
func PurposeHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldPurpose, v))
}

// PurposeEqualFold applies the EqualFold predicate on the "purpose" field.
func PurposeEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldPurpose, v))
}

// PurposeContainsFold applies the ContainsFold predicate on the "purpose" field.
func PurposeContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldPurpose, v))
}

// FileSizeEQ applies the EQ predicate on the "file_size" field.
func FileSizeEQ(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldFileSize, v))
}

// FileSizeNEQ applies the NEQ predicate on the "file_size" field.
func FileSizeNEQ(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldFileSize, v))
}

// FileSizeIn applies the In predicate on the "file_size" field.
func FileSizeIn(vs ...int64) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldFileSize, vs...))
}

// FileSizeNotIn applies the NotIn predicate on the "file_size" field.
func FileSizeNotIn(vs ...int64) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldFileSize, vs...))
}

// FileSizeGT applies the GT predicate on the "file_size" field.
func FileSizeGT(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldFileSize, v))
}

// FileSizeGTE applies the GTE predicate on the "file_size" field.
func FileSizeGTE(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldFileSize, v))
}

// FileSizeLT applies the LT predicate on the "file_size" field.
func FileSizeLT(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldFileSize, v))
}

// FileSizeLTE applies the LTE predicate on the "file_size" field.
func FileSizeLTE(v int64) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldFileSize, v))
}

// ThumbnailURLEQ applies the EQ predicate on the "thumbnail_url" field.
func ThumbnailURLEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldThumbnailURL, v))
}

// ThumbnailURLNEQ applies the NEQ predicate on the "thumbnail_url" field.
func ThumbnailURLNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldThumbnailURL, v))
}

// ThumbnailURLIn applies the In predicate on the "thumbnail_url" field.
func ThumbnailURLIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldThumbnailURL, vs...))
}

// ThumbnailURLNotIn applies the NotIn predicate on the "thumbnail_url" field.
func ThumbnailURLNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldThumbnailURL, vs...))
}

// ThumbnailURLGT applies the GT predicate on the "thumbnail_url" field.
func ThumbnailURLGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldThumbnailURL, v))
}

// ThumbnailURLGTE applies the GTE predicate on the "thumbnail_url" field.
func ThumbnailURLGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldThumbnailURL, v))
}

// ThumbnailURLLT applies the LT predicate on the "thumbnail_url" field.
func ThumbnailURLLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldThumbnailURL, v))
}

// ThumbnailURLLTE applies the LTE predicate on the "thumbnail_url" field.
func ThumbnailURLLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldThumbnailURL, v))
}

// ThumbnailURLContains applies the Contains predicate on the "thumbnail_url" field.
func ThumbnailURLContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldThumbnailURL, v))
}

// ThumbnailURLHasPrefix applies the HasPrefix predicate on the "thumbnail_url" field.
func ThumbnailURLHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldThumbnailURL, v))
}

// ThumbnailURLHasSuffix applies the HasSuffix predicate on the "thumbnail_url" field.
func ThumbnailURLHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldThumbnailURL, v))
}

// ThumbnailURLIsNil applies the IsNil predicate on the "thumbnail_url" field.
func ThumbnailURLIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldThumbnailURL))
}

// ThumbnailURLNotNil applies the NotNil predicate on the "thumbnail_url" field.
func ThumbnailURLNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldThumbnailURL))
}

// ThumbnailURLEqualFold applies the EqualFold predicate on the "thumbnail_url" field.
func ThumbnailURLEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldThumbnailURL, v))
}

// ThumbnailURLContainsFold applies the ContainsFold predicate on the "thumbnail_url" field.
func ThumbnailURLContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldThumbnailURL, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldMetadata))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Asset {
	return predicate.Asset(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Asset {
	return predicate.Asset(sql.FieldContainsFold(FieldStatus, v))
}

// IsPublicEQ applies the EQ predicate on the "is_public" field.
func IsPublicEQ(v bool) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldIsPublic, v))
}

// IsPublicNEQ applies the NEQ predicate on the "is_public" field.
func IsPublicNEQ(v bool) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldIsPublic, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Asset {
	return predicate.Asset(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Asset {
	return predicate.Asset(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Asset {
	return predicate.Asset(sql.FieldNotNull(FieldDeletedAt))
}

// HasFolder applies the HasEdge predicate on the "folder" edge.
func HasFolder() predicate.Asset {
	return predicate.Asset(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, FolderTable, FolderColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFolderWith applies the HasEdge predicate on the "folder" edge with a given conditions (other predicates).
func HasFolderWith(preds ...predicate.Folder) predicate.Asset {
	return predicate.Asset(func(s *sql.Selector) {
		step := newFolderStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Asset) predicate.Asset {
	return predicate.Asset(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Asset) predicate.Asset {
	return predicate.Asset(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Asset) predicate.Asset {
	return predicate.Asset(sql.NotPredicates(p))
}

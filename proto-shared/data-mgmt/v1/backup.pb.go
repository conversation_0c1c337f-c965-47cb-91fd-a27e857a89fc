// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: data-mgmt/v1/backup.proto

package datamgmtv1

import (
	v1 "github.com/social-content-ai/proto-shared/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BackupJob represents a data backup job
type BackupJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId       string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId  string                 `protobuf:"bytes,3,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"` // Optional workspace filter
	BackupType   string                 `protobuf:"bytes,4,opt,name=backup_type,json=backupType,proto3" json:"backup_type,omitempty"`    // full, incremental, posts_only, images_only
	Status       string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                              // pending, processing, completed, failed
	Format       string                 `protobuf:"bytes,6,opt,name=format,proto3" json:"format,omitempty"`                              // html_zip, json, csv
	TotalSize    int64                  `protobuf:"varint,7,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`      // Total backup size in bytes
	S3Path       string                 `protobuf:"bytes,8,opt,name=s3_path,json=s3Path,proto3" json:"s3_path,omitempty"`                // S3 backup file path
	DownloadUrl  string                 `protobuf:"bytes,9,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"` // Presigned download URL (24h expiry)
	ExpiresAt    *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`      // Download URL expiry
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CompletedAt  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	ErrorMessage string                 `protobuf:"bytes,13,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"` // If failed
	Stats        *BackupStats           `protobuf:"bytes,14,opt,name=stats,proto3" json:"stats,omitempty"`
}

func (x *BackupJob) Reset() {
	*x = BackupJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackupJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupJob) ProtoMessage() {}

func (x *BackupJob) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupJob.ProtoReflect.Descriptor instead.
func (*BackupJob) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{0}
}

func (x *BackupJob) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BackupJob) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BackupJob) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *BackupJob) GetBackupType() string {
	if x != nil {
		return x.BackupType
	}
	return ""
}

func (x *BackupJob) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BackupJob) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *BackupJob) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

func (x *BackupJob) GetS3Path() string {
	if x != nil {
		return x.S3Path
	}
	return ""
}

func (x *BackupJob) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BackupJob) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *BackupJob) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BackupJob) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *BackupJob) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *BackupJob) GetStats() *BackupStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

// BackupStats with backup metrics
type BackupStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsCount      int32   `protobuf:"varint,1,opt,name=posts_count,json=postsCount,proto3" json:"posts_count,omitempty"`
	ImagesCount     int32   `protobuf:"varint,2,opt,name=images_count,json=imagesCount,proto3" json:"images_count,omitempty"`
	TemplatesCount  int32   `protobuf:"varint,3,opt,name=templates_count,json=templatesCount,proto3" json:"templates_count,omitempty"`
	WorkspacesCount int32   `protobuf:"varint,4,opt,name=workspaces_count,json=workspacesCount,proto3" json:"workspaces_count,omitempty"`
	FilesCount      int32   `protobuf:"varint,5,opt,name=files_count,json=filesCount,proto3" json:"files_count,omitempty"`
	TotalSize       int64   `protobuf:"varint,6,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`                 // Total size in bytes
	ProcessingTime  float32 `protobuf:"fixed32,7,opt,name=processing_time,json=processingTime,proto3" json:"processing_time,omitempty"` // Processing time in seconds
}

func (x *BackupStats) Reset() {
	*x = BackupStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackupStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupStats) ProtoMessage() {}

func (x *BackupStats) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupStats.ProtoReflect.Descriptor instead.
func (*BackupStats) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{1}
}

func (x *BackupStats) GetPostsCount() int32 {
	if x != nil {
		return x.PostsCount
	}
	return 0
}

func (x *BackupStats) GetImagesCount() int32 {
	if x != nil {
		return x.ImagesCount
	}
	return 0
}

func (x *BackupStats) GetTemplatesCount() int32 {
	if x != nil {
		return x.TemplatesCount
	}
	return 0
}

func (x *BackupStats) GetWorkspacesCount() int32 {
	if x != nil {
		return x.WorkspacesCount
	}
	return 0
}

func (x *BackupStats) GetFilesCount() int32 {
	if x != nil {
		return x.FilesCount
	}
	return 0
}

func (x *BackupStats) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

func (x *BackupStats) GetProcessingTime() float32 {
	if x != nil {
		return x.ProcessingTime
	}
	return 0
}

// CreateBackupRequest for creating a new backup
type CreateBackupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId    string   `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`           // Optional workspace filter
	BackupType     string   `protobuf:"bytes,3,opt,name=backup_type,json=backupType,proto3" json:"backup_type,omitempty"`              // full, incremental, posts_only, images_only
	Format         string   `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"`                                        // html_zip, json, csv
	IncludeData    []string `protobuf:"bytes,5,rep,name=include_data,json=includeData,proto3" json:"include_data,omitempty"`           // posts, images, templates, workspaces, analytics
	DateFrom       string   `protobuf:"bytes,6,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`                    // Format: YYYY-MM-DD (for incremental)
	DateTo         string   `protobuf:"bytes,7,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`                          // Format: YYYY-MM-DD (for incremental)
	IncludeDeleted bool     `protobuf:"varint,8,opt,name=include_deleted,json=includeDeleted,proto3" json:"include_deleted,omitempty"` // Include soft-deleted items
}

func (x *CreateBackupRequest) Reset() {
	*x = CreateBackupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBackupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBackupRequest) ProtoMessage() {}

func (x *CreateBackupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBackupRequest.ProtoReflect.Descriptor instead.
func (*CreateBackupRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{2}
}

func (x *CreateBackupRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateBackupRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *CreateBackupRequest) GetBackupType() string {
	if x != nil {
		return x.BackupType
	}
	return ""
}

func (x *CreateBackupRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *CreateBackupRequest) GetIncludeData() []string {
	if x != nil {
		return x.IncludeData
	}
	return nil
}

func (x *CreateBackupRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *CreateBackupRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *CreateBackupRequest) GetIncludeDeleted() bool {
	if x != nil {
		return x.IncludeDeleted
	}
	return false
}

// CreateBackupResponse with backup job info
type CreateBackupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackupId          string                 `protobuf:"bytes,1,opt,name=backup_id,json=backupId,proto3" json:"backup_id,omitempty"`
	Status            string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	EstimatedDuration int32                  `protobuf:"varint,4,opt,name=estimated_duration,json=estimatedDuration,proto3" json:"estimated_duration,omitempty"` // Estimated processing time in seconds
}

func (x *CreateBackupResponse) Reset() {
	*x = CreateBackupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBackupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBackupResponse) ProtoMessage() {}

func (x *CreateBackupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBackupResponse.ProtoReflect.Descriptor instead.
func (*CreateBackupResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{3}
}

func (x *CreateBackupResponse) GetBackupId() string {
	if x != nil {
		return x.BackupId
	}
	return ""
}

func (x *CreateBackupResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateBackupResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CreateBackupResponse) GetEstimatedDuration() int32 {
	if x != nil {
		return x.EstimatedDuration
	}
	return 0
}

// GetBackupRequest for retrieving backup info
type GetBackupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackupId string `protobuf:"bytes,1,opt,name=backup_id,json=backupId,proto3" json:"backup_id,omitempty"`
	UserId   string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetBackupRequest) Reset() {
	*x = GetBackupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBackupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBackupRequest) ProtoMessage() {}

func (x *GetBackupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBackupRequest.ProtoReflect.Descriptor instead.
func (*GetBackupRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{4}
}

func (x *GetBackupRequest) GetBackupId() string {
	if x != nil {
		return x.BackupId
	}
	return ""
}

func (x *GetBackupRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// ListBackupsRequest for listing user backups
type ListBackupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId string                `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"` // Optional workspace filter
	Pagination  *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Status      string                `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`                           // Filter by status
	BackupType  string                `protobuf:"bytes,5,opt,name=backup_type,json=backupType,proto3" json:"backup_type,omitempty"` // Filter by backup type
	DateFrom    string                `protobuf:"bytes,6,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`       // Filter by creation date
	DateTo      string                `protobuf:"bytes,7,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`             // Filter by creation date
}

func (x *ListBackupsRequest) Reset() {
	*x = ListBackupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBackupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBackupsRequest) ProtoMessage() {}

func (x *ListBackupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBackupsRequest.ProtoReflect.Descriptor instead.
func (*ListBackupsRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{5}
}

func (x *ListBackupsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListBackupsRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListBackupsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListBackupsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListBackupsRequest) GetBackupType() string {
	if x != nil {
		return x.BackupType
	}
	return ""
}

func (x *ListBackupsRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *ListBackupsRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

// ListBackupsResponse with paginated backups
type ListBackupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Backups    []*BackupJob           `protobuf:"bytes,1,rep,name=backups,proto3" json:"backups,omitempty"`
	Pagination *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListBackupsResponse) Reset() {
	*x = ListBackupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBackupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBackupsResponse) ProtoMessage() {}

func (x *ListBackupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBackupsResponse.ProtoReflect.Descriptor instead.
func (*ListBackupsResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{6}
}

func (x *ListBackupsResponse) GetBackups() []*BackupJob {
	if x != nil {
		return x.Backups
	}
	return nil
}

func (x *ListBackupsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// DeleteBackupRequest for deleting a backup
type DeleteBackupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackupId    string `protobuf:"bytes,1,opt,name=backup_id,json=backupId,proto3" json:"backup_id,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ForceDelete bool   `protobuf:"varint,3,opt,name=force_delete,json=forceDelete,proto3" json:"force_delete,omitempty"` // Force delete even if not expired
}

func (x *DeleteBackupRequest) Reset() {
	*x = DeleteBackupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBackupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBackupRequest) ProtoMessage() {}

func (x *DeleteBackupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBackupRequest.ProtoReflect.Descriptor instead.
func (*DeleteBackupRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteBackupRequest) GetBackupId() string {
	if x != nil {
		return x.BackupId
	}
	return ""
}

func (x *DeleteBackupRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeleteBackupRequest) GetForceDelete() bool {
	if x != nil {
		return x.ForceDelete
	}
	return false
}

// RefreshDownloadUrlRequest for refreshing download URL
type RefreshDownloadUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackupId    string `protobuf:"bytes,1,opt,name=backup_id,json=backupId,proto3" json:"backup_id,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ExpiryHours int32  `protobuf:"varint,3,opt,name=expiry_hours,json=expiryHours,proto3" json:"expiry_hours,omitempty"` // URL expiry in hours (max 24)
}

func (x *RefreshDownloadUrlRequest) Reset() {
	*x = RefreshDownloadUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDownloadUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDownloadUrlRequest) ProtoMessage() {}

func (x *RefreshDownloadUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDownloadUrlRequest.ProtoReflect.Descriptor instead.
func (*RefreshDownloadUrlRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{8}
}

func (x *RefreshDownloadUrlRequest) GetBackupId() string {
	if x != nil {
		return x.BackupId
	}
	return ""
}

func (x *RefreshDownloadUrlRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RefreshDownloadUrlRequest) GetExpiryHours() int32 {
	if x != nil {
		return x.ExpiryHours
	}
	return 0
}

// RefreshDownloadUrlResponse with new URL
type RefreshDownloadUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DownloadUrl string                 `protobuf:"bytes,1,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	ExpiresAt   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *RefreshDownloadUrlResponse) Reset() {
	*x = RefreshDownloadUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDownloadUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDownloadUrlResponse) ProtoMessage() {}

func (x *RefreshDownloadUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDownloadUrlResponse.ProtoReflect.Descriptor instead.
func (*RefreshDownloadUrlResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{9}
}

func (x *RefreshDownloadUrlResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *RefreshDownloadUrlResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

// AccountDeletionRequest for GDPR account deletion
type AccountDeletionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Reason       string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`                                  // user_request, gdpr_compliance, admin_action
	CreateBackup bool                   `protobuf:"varint,3,opt,name=create_backup,json=createBackup,proto3" json:"create_backup,omitempty"` // Create backup before deletion
	BackupFormat string                 `protobuf:"bytes,4,opt,name=backup_format,json=backupFormat,proto3" json:"backup_format,omitempty"`  // html_zip, json (if create_backup=true)
	Immediate    bool                   `protobuf:"varint,5,opt,name=immediate,proto3" json:"immediate,omitempty"`                           // Immediate deletion or scheduled
	ScheduledAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`     // If not immediate
}

func (x *AccountDeletionRequest) Reset() {
	*x = AccountDeletionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDeletionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeletionRequest) ProtoMessage() {}

func (x *AccountDeletionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeletionRequest.ProtoReflect.Descriptor instead.
func (*AccountDeletionRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{10}
}

func (x *AccountDeletionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AccountDeletionRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AccountDeletionRequest) GetCreateBackup() bool {
	if x != nil {
		return x.CreateBackup
	}
	return false
}

func (x *AccountDeletionRequest) GetBackupFormat() string {
	if x != nil {
		return x.BackupFormat
	}
	return ""
}

func (x *AccountDeletionRequest) GetImmediate() bool {
	if x != nil {
		return x.Immediate
	}
	return false
}

func (x *AccountDeletionRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

// AccountDeletionResponse with deletion job info
type AccountDeletionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeletionId  string                 `protobuf:"bytes,1,opt,name=deletion_id,json=deletionId,proto3" json:"deletion_id,omitempty"`
	Status      string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`                     // scheduled, processing, completed
	BackupId    string                 `protobuf:"bytes,3,opt,name=backup_id,json=backupId,proto3" json:"backup_id,omitempty"` // If backup was created
	ScheduledAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
}

func (x *AccountDeletionResponse) Reset() {
	*x = AccountDeletionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDeletionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeletionResponse) ProtoMessage() {}

func (x *AccountDeletionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeletionResponse.ProtoReflect.Descriptor instead.
func (*AccountDeletionResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{11}
}

func (x *AccountDeletionResponse) GetDeletionId() string {
	if x != nil {
		return x.DeletionId
	}
	return ""
}

func (x *AccountDeletionResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountDeletionResponse) GetBackupId() string {
	if x != nil {
		return x.BackupId
	}
	return ""
}

func (x *AccountDeletionResponse) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *AccountDeletionResponse) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

// GetDeletionStatusRequest for checking deletion status
type GetDeletionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeletionId string `protobuf:"bytes,1,opt,name=deletion_id,json=deletionId,proto3" json:"deletion_id,omitempty"`
	UserId     string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Admin can check any user
}

func (x *GetDeletionStatusRequest) Reset() {
	*x = GetDeletionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeletionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeletionStatusRequest) ProtoMessage() {}

func (x *GetDeletionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeletionStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDeletionStatusRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{12}
}

func (x *GetDeletionStatusRequest) GetDeletionId() string {
	if x != nil {
		return x.DeletionId
	}
	return ""
}

func (x *GetDeletionStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// DeletionStatus with current deletion status
type DeletionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeletionId   string                 `protobuf:"bytes,1,opt,name=deletion_id,json=deletionId,proto3" json:"deletion_id,omitempty"`
	UserId       string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status       string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                                 // scheduled, processing, completed, failed
	Progress     float32                `protobuf:"fixed32,4,opt,name=progress,proto3" json:"progress,omitempty"`                           // Progress percentage (0-100)
	CurrentStage string                 `protobuf:"bytes,5,opt,name=current_stage,json=currentStage,proto3" json:"current_stage,omitempty"` // backup_creation, data_anonymization, file_deletion
	ScheduledAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	StartedAt    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	CompletedAt  *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	ErrorMessage string                 `protobuf:"bytes,9,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"` // If failed
	Stats        *DeletionStats         `protobuf:"bytes,10,opt,name=stats,proto3" json:"stats,omitempty"`
}

func (x *DeletionStatus) Reset() {
	*x = DeletionStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletionStatus) ProtoMessage() {}

func (x *DeletionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletionStatus.ProtoReflect.Descriptor instead.
func (*DeletionStatus) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{13}
}

func (x *DeletionStatus) GetDeletionId() string {
	if x != nil {
		return x.DeletionId
	}
	return ""
}

func (x *DeletionStatus) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DeletionStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeletionStatus) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *DeletionStatus) GetCurrentStage() string {
	if x != nil {
		return x.CurrentStage
	}
	return ""
}

func (x *DeletionStatus) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *DeletionStatus) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *DeletionStatus) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *DeletionStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *DeletionStatus) GetStats() *DeletionStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

// DeletionStats with deletion metrics
type DeletionStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsDeleted      int32   `protobuf:"varint,1,opt,name=posts_deleted,json=postsDeleted,proto3" json:"posts_deleted,omitempty"`
	ImagesDeleted     int32   `protobuf:"varint,2,opt,name=images_deleted,json=imagesDeleted,proto3" json:"images_deleted,omitempty"`
	TemplatesDeleted  int32   `protobuf:"varint,3,opt,name=templates_deleted,json=templatesDeleted,proto3" json:"templates_deleted,omitempty"`
	WorkspacesDeleted int32   `protobuf:"varint,4,opt,name=workspaces_deleted,json=workspacesDeleted,proto3" json:"workspaces_deleted,omitempty"`
	FilesDeleted      int32   `protobuf:"varint,5,opt,name=files_deleted,json=filesDeleted,proto3" json:"files_deleted,omitempty"`
	StorageFreed      int64   `protobuf:"varint,6,opt,name=storage_freed,json=storageFreed,proto3" json:"storage_freed,omitempty"`        // Storage freed in bytes
	ProcessingTime    float32 `protobuf:"fixed32,7,opt,name=processing_time,json=processingTime,proto3" json:"processing_time,omitempty"` // Processing time in seconds
}

func (x *DeletionStats) Reset() {
	*x = DeletionStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletionStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletionStats) ProtoMessage() {}

func (x *DeletionStats) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletionStats.ProtoReflect.Descriptor instead.
func (*DeletionStats) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{14}
}

func (x *DeletionStats) GetPostsDeleted() int32 {
	if x != nil {
		return x.PostsDeleted
	}
	return 0
}

func (x *DeletionStats) GetImagesDeleted() int32 {
	if x != nil {
		return x.ImagesDeleted
	}
	return 0
}

func (x *DeletionStats) GetTemplatesDeleted() int32 {
	if x != nil {
		return x.TemplatesDeleted
	}
	return 0
}

func (x *DeletionStats) GetWorkspacesDeleted() int32 {
	if x != nil {
		return x.WorkspacesDeleted
	}
	return 0
}

func (x *DeletionStats) GetFilesDeleted() int32 {
	if x != nil {
		return x.FilesDeleted
	}
	return 0
}

func (x *DeletionStats) GetStorageFreed() int64 {
	if x != nil {
		return x.StorageFreed
	}
	return 0
}

func (x *DeletionStats) GetProcessingTime() float32 {
	if x != nil {
		return x.ProcessingTime
	}
	return 0
}

// DataExportRequest for exporting specific data
type DataExportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string            `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId string            `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`                                                              // Optional workspace filter
	ExportType  string            `protobuf:"bytes,3,opt,name=export_type,json=exportType,proto3" json:"export_type,omitempty"`                                                                 // posts, templates, analytics, all
	Format      string            `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"`                                                                                           // json, csv, html
	Fields      []string          `protobuf:"bytes,5,rep,name=fields,proto3" json:"fields,omitempty"`                                                                                           // Specific fields to export
	DateFrom    string            `protobuf:"bytes,6,opt,name=date_from,json=dateFrom,proto3" json:"date_from,omitempty"`                                                                       // Format: YYYY-MM-DD
	DateTo      string            `protobuf:"bytes,7,opt,name=date_to,json=dateTo,proto3" json:"date_to,omitempty"`                                                                             // Format: YYYY-MM-DD
	Filters     map[string]string `protobuf:"bytes,8,rep,name=filters,proto3" json:"filters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Additional filters
}

func (x *DataExportRequest) Reset() {
	*x = DataExportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataExportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataExportRequest) ProtoMessage() {}

func (x *DataExportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataExportRequest.ProtoReflect.Descriptor instead.
func (*DataExportRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{15}
}

func (x *DataExportRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DataExportRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *DataExportRequest) GetExportType() string {
	if x != nil {
		return x.ExportType
	}
	return ""
}

func (x *DataExportRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *DataExportRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *DataExportRequest) GetDateFrom() string {
	if x != nil {
		return x.DateFrom
	}
	return ""
}

func (x *DataExportRequest) GetDateTo() string {
	if x != nil {
		return x.DateTo
	}
	return ""
}

func (x *DataExportRequest) GetFilters() map[string]string {
	if x != nil {
		return x.Filters
	}
	return nil
}

// DataExportResponse with export job info
type DataExportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExportId    string                 `protobuf:"bytes,1,opt,name=export_id,json=exportId,proto3" json:"export_id,omitempty"`
	Status      string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	DownloadUrl string                 `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"` // Presigned download URL
	ExpiresAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	FileSize    int64                  `protobuf:"varint,5,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"` // Export file size in bytes
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *DataExportResponse) Reset() {
	*x = DataExportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataExportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataExportResponse) ProtoMessage() {}

func (x *DataExportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataExportResponse.ProtoReflect.Descriptor instead.
func (*DataExportResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{16}
}

func (x *DataExportResponse) GetExportId() string {
	if x != nil {
		return x.ExportId
	}
	return ""
}

func (x *DataExportResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DataExportResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *DataExportResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *DataExportResponse) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *DataExportResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// GetExportRequest for retrieving export info
type GetExportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExportId string `protobuf:"bytes,1,opt,name=export_id,json=exportId,proto3" json:"export_id,omitempty"`
	UserId   string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetExportRequest) Reset() {
	*x = GetExportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExportRequest) ProtoMessage() {}

func (x *GetExportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExportRequest.ProtoReflect.Descriptor instead.
func (*GetExportRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{17}
}

func (x *GetExportRequest) GetExportId() string {
	if x != nil {
		return x.ExportId
	}
	return ""
}

func (x *GetExportRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// ListExportsRequest for listing user exports
type ListExportsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WorkspaceId string                `protobuf:"bytes,2,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"` // Optional workspace filter
	Pagination  *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ExportType  string                `protobuf:"bytes,4,opt,name=export_type,json=exportType,proto3" json:"export_type,omitempty"` // Filter by export type
	Status      string                `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                           // Filter by status
}

func (x *ListExportsRequest) Reset() {
	*x = ListExportsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExportsRequest) ProtoMessage() {}

func (x *ListExportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExportsRequest.ProtoReflect.Descriptor instead.
func (*ListExportsRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{18}
}

func (x *ListExportsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListExportsRequest) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

func (x *ListExportsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListExportsRequest) GetExportType() string {
	if x != nil {
		return x.ExportType
	}
	return ""
}

func (x *ListExportsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// ListExportsResponse with paginated exports
type ListExportsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exports    []*DataExportResponse  `protobuf:"bytes,1,rep,name=exports,proto3" json:"exports,omitempty"`
	Pagination *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListExportsResponse) Reset() {
	*x = ListExportsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExportsResponse) ProtoMessage() {}

func (x *ListExportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExportsResponse.ProtoReflect.Descriptor instead.
func (*ListExportsResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{19}
}

func (x *ListExportsResponse) GetExports() []*DataExportResponse {
	if x != nil {
		return x.Exports
	}
	return nil
}

func (x *ListExportsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// CleanupExpiredRequest for cleaning up expired backups/exports
type CleanupExpiredRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CleanupType string `protobuf:"bytes,1,opt,name=cleanup_type,json=cleanupType,proto3" json:"cleanup_type,omitempty"` // backups, exports, all
	DaysOld     int32  `protobuf:"varint,2,opt,name=days_old,json=daysOld,proto3" json:"days_old,omitempty"`            // Clean items older than X days
	DryRun      bool   `protobuf:"varint,3,opt,name=dry_run,json=dryRun,proto3" json:"dry_run,omitempty"`               // Preview what would be cleaned
}

func (x *CleanupExpiredRequest) Reset() {
	*x = CleanupExpiredRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupExpiredRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupExpiredRequest) ProtoMessage() {}

func (x *CleanupExpiredRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupExpiredRequest.ProtoReflect.Descriptor instead.
func (*CleanupExpiredRequest) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{20}
}

func (x *CleanupExpiredRequest) GetCleanupType() string {
	if x != nil {
		return x.CleanupType
	}
	return ""
}

func (x *CleanupExpiredRequest) GetDaysOld() int32 {
	if x != nil {
		return x.DaysOld
	}
	return 0
}

func (x *CleanupExpiredRequest) GetDryRun() bool {
	if x != nil {
		return x.DryRun
	}
	return false
}

// CleanupExpiredResponse with cleanup results
type CleanupExpiredResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemsCleaned int32    `protobuf:"varint,1,opt,name=items_cleaned,json=itemsCleaned,proto3" json:"items_cleaned,omitempty"`
	StorageFreed int64    `protobuf:"varint,2,opt,name=storage_freed,json=storageFreed,proto3" json:"storage_freed,omitempty"` // Storage freed in bytes
	CleanedItems []string `protobuf:"bytes,3,rep,name=cleaned_items,json=cleanedItems,proto3" json:"cleaned_items,omitempty"`  // List of cleaned item IDs
}

func (x *CleanupExpiredResponse) Reset() {
	*x = CleanupExpiredResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CleanupExpiredResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupExpiredResponse) ProtoMessage() {}

func (x *CleanupExpiredResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupExpiredResponse.ProtoReflect.Descriptor instead.
func (*CleanupExpiredResponse) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{21}
}

func (x *CleanupExpiredResponse) GetItemsCleaned() int32 {
	if x != nil {
		return x.ItemsCleaned
	}
	return 0
}

func (x *CleanupExpiredResponse) GetStorageFreed() int64 {
	if x != nil {
		return x.StorageFreed
	}
	return 0
}

func (x *CleanupExpiredResponse) GetCleanedItems() []string {
	if x != nil {
		return x.CleanedItems
	}
	return nil
}

// Empty response
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_mgmt_v1_backup_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_data_mgmt_v1_backup_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_data_mgmt_v1_backup_proto_rawDescGZIP(), []int{22}
}

var File_data_mgmt_v1_backup_proto protoreflect.FileDescriptor

var file_data_mgmt_v1_backup_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x04, 0x0a, 0x09, 0x42, 0x61, 0x63, 0x6b, 0x75,
	0x70, 0x4a, 0x6f, 0x62, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x22, 0x8e, 0x02, 0x0a, 0x0b, 0x42, 0x61, 0x63, 0x6b,
	0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x73,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8c, 0x02, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x12, 0x27,
	0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x2d, 0x0a, 0x12, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x48, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xfd, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x22, 0x87, 0x01, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x31, 0x0a, 0x07, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x52, 0x07, 0x62, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x6e, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61,
	0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x22, 0x74, 0x0a, 0x19, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x22, 0x7a, 0x0a, 0x1a, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x41, 0x74, 0x22, 0xf0, 0x01, 0x0a, 0x16, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b,
	0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69,
	0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x22, 0xed, 0x01, 0x0a, 0x17, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x54, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb4,
	0x03, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d,
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x73, 0x22, 0xaa, 0x02, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x74, 0x73,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x12, 0x2d, 0x0a, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f,
	0x66, 0x72, 0x65, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x46, 0x72, 0x65, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xda, 0x02, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x46, 0x72,
	0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x12, 0x46, 0x0a, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xff, 0x01, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x61, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x39,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x48, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc7, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3c,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6e, 0x0a, 0x15, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x75, 0x70, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x6f, 0x6c, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x61, 0x79, 0x73, 0x4f, 0x6c, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x72, 0x79, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x64, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x16, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x75, 0x70, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x5f, 0x63, 0x6c, 0x65,
	0x61, 0x6e, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x46, 0x72, 0x65, 0x65, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x32, 0xc4, 0x07, 0x0a, 0x0d,
	0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x21, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75,
	0x70, 0x12, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x17, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x4a, 0x6f, 0x62, 0x12, 0x52, 0x0a, 0x0b, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73, 0x12, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46,
	0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x21,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x13, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x67, 0x0a, 0x12, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4f, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x52, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x20,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x0e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x75, 0x70, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d,
	0x61, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x2d, 0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x61, 0x74,
	0x61, 0x6d, 0x67, 0x6d, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_mgmt_v1_backup_proto_rawDescOnce sync.Once
	file_data_mgmt_v1_backup_proto_rawDescData = file_data_mgmt_v1_backup_proto_rawDesc
)

func file_data_mgmt_v1_backup_proto_rawDescGZIP() []byte {
	file_data_mgmt_v1_backup_proto_rawDescOnce.Do(func() {
		file_data_mgmt_v1_backup_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_mgmt_v1_backup_proto_rawDescData)
	})
	return file_data_mgmt_v1_backup_proto_rawDescData
}

var file_data_mgmt_v1_backup_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_data_mgmt_v1_backup_proto_goTypes = []interface{}{
	(*BackupJob)(nil),                  // 0: data_mgmt.v1.BackupJob
	(*BackupStats)(nil),                // 1: data_mgmt.v1.BackupStats
	(*CreateBackupRequest)(nil),        // 2: data_mgmt.v1.CreateBackupRequest
	(*CreateBackupResponse)(nil),       // 3: data_mgmt.v1.CreateBackupResponse
	(*GetBackupRequest)(nil),           // 4: data_mgmt.v1.GetBackupRequest
	(*ListBackupsRequest)(nil),         // 5: data_mgmt.v1.ListBackupsRequest
	(*ListBackupsResponse)(nil),        // 6: data_mgmt.v1.ListBackupsResponse
	(*DeleteBackupRequest)(nil),        // 7: data_mgmt.v1.DeleteBackupRequest
	(*RefreshDownloadUrlRequest)(nil),  // 8: data_mgmt.v1.RefreshDownloadUrlRequest
	(*RefreshDownloadUrlResponse)(nil), // 9: data_mgmt.v1.RefreshDownloadUrlResponse
	(*AccountDeletionRequest)(nil),     // 10: data_mgmt.v1.AccountDeletionRequest
	(*AccountDeletionResponse)(nil),    // 11: data_mgmt.v1.AccountDeletionResponse
	(*GetDeletionStatusRequest)(nil),   // 12: data_mgmt.v1.GetDeletionStatusRequest
	(*DeletionStatus)(nil),             // 13: data_mgmt.v1.DeletionStatus
	(*DeletionStats)(nil),              // 14: data_mgmt.v1.DeletionStats
	(*DataExportRequest)(nil),          // 15: data_mgmt.v1.DataExportRequest
	(*DataExportResponse)(nil),         // 16: data_mgmt.v1.DataExportResponse
	(*GetExportRequest)(nil),           // 17: data_mgmt.v1.GetExportRequest
	(*ListExportsRequest)(nil),         // 18: data_mgmt.v1.ListExportsRequest
	(*ListExportsResponse)(nil),        // 19: data_mgmt.v1.ListExportsResponse
	(*CleanupExpiredRequest)(nil),      // 20: data_mgmt.v1.CleanupExpiredRequest
	(*CleanupExpiredResponse)(nil),     // 21: data_mgmt.v1.CleanupExpiredResponse
	(*Empty)(nil),                      // 22: data_mgmt.v1.Empty
	nil,                                // 23: data_mgmt.v1.DataExportRequest.FiltersEntry
	(*timestamppb.Timestamp)(nil),      // 24: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),       // 25: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),      // 26: common.v1.PaginationResponse
}
var file_data_mgmt_v1_backup_proto_depIdxs = []int32{
	24, // 0: data_mgmt.v1.BackupJob.expires_at:type_name -> google.protobuf.Timestamp
	24, // 1: data_mgmt.v1.BackupJob.created_at:type_name -> google.protobuf.Timestamp
	24, // 2: data_mgmt.v1.BackupJob.completed_at:type_name -> google.protobuf.Timestamp
	1,  // 3: data_mgmt.v1.BackupJob.stats:type_name -> data_mgmt.v1.BackupStats
	24, // 4: data_mgmt.v1.CreateBackupResponse.created_at:type_name -> google.protobuf.Timestamp
	25, // 5: data_mgmt.v1.ListBackupsRequest.pagination:type_name -> common.v1.PaginationRequest
	0,  // 6: data_mgmt.v1.ListBackupsResponse.backups:type_name -> data_mgmt.v1.BackupJob
	26, // 7: data_mgmt.v1.ListBackupsResponse.pagination:type_name -> common.v1.PaginationResponse
	24, // 8: data_mgmt.v1.RefreshDownloadUrlResponse.expires_at:type_name -> google.protobuf.Timestamp
	24, // 9: data_mgmt.v1.AccountDeletionRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	24, // 10: data_mgmt.v1.AccountDeletionResponse.scheduled_at:type_name -> google.protobuf.Timestamp
	24, // 11: data_mgmt.v1.AccountDeletionResponse.completed_at:type_name -> google.protobuf.Timestamp
	24, // 12: data_mgmt.v1.DeletionStatus.scheduled_at:type_name -> google.protobuf.Timestamp
	24, // 13: data_mgmt.v1.DeletionStatus.started_at:type_name -> google.protobuf.Timestamp
	24, // 14: data_mgmt.v1.DeletionStatus.completed_at:type_name -> google.protobuf.Timestamp
	14, // 15: data_mgmt.v1.DeletionStatus.stats:type_name -> data_mgmt.v1.DeletionStats
	23, // 16: data_mgmt.v1.DataExportRequest.filters:type_name -> data_mgmt.v1.DataExportRequest.FiltersEntry
	24, // 17: data_mgmt.v1.DataExportResponse.expires_at:type_name -> google.protobuf.Timestamp
	24, // 18: data_mgmt.v1.DataExportResponse.created_at:type_name -> google.protobuf.Timestamp
	25, // 19: data_mgmt.v1.ListExportsRequest.pagination:type_name -> common.v1.PaginationRequest
	16, // 20: data_mgmt.v1.ListExportsResponse.exports:type_name -> data_mgmt.v1.DataExportResponse
	26, // 21: data_mgmt.v1.ListExportsResponse.pagination:type_name -> common.v1.PaginationResponse
	2,  // 22: data_mgmt.v1.BackupService.CreateBackup:input_type -> data_mgmt.v1.CreateBackupRequest
	4,  // 23: data_mgmt.v1.BackupService.GetBackup:input_type -> data_mgmt.v1.GetBackupRequest
	5,  // 24: data_mgmt.v1.BackupService.ListBackups:input_type -> data_mgmt.v1.ListBackupsRequest
	7,  // 25: data_mgmt.v1.BackupService.DeleteBackup:input_type -> data_mgmt.v1.DeleteBackupRequest
	8,  // 26: data_mgmt.v1.BackupService.RefreshDownloadUrl:input_type -> data_mgmt.v1.RefreshDownloadUrlRequest
	15, // 27: data_mgmt.v1.BackupService.ExportData:input_type -> data_mgmt.v1.DataExportRequest
	17, // 28: data_mgmt.v1.BackupService.GetExport:input_type -> data_mgmt.v1.GetExportRequest
	18, // 29: data_mgmt.v1.BackupService.ListExports:input_type -> data_mgmt.v1.ListExportsRequest
	10, // 30: data_mgmt.v1.BackupService.RequestAccountDeletion:input_type -> data_mgmt.v1.AccountDeletionRequest
	12, // 31: data_mgmt.v1.BackupService.GetDeletionStatus:input_type -> data_mgmt.v1.GetDeletionStatusRequest
	20, // 32: data_mgmt.v1.BackupService.CleanupExpired:input_type -> data_mgmt.v1.CleanupExpiredRequest
	3,  // 33: data_mgmt.v1.BackupService.CreateBackup:output_type -> data_mgmt.v1.CreateBackupResponse
	0,  // 34: data_mgmt.v1.BackupService.GetBackup:output_type -> data_mgmt.v1.BackupJob
	6,  // 35: data_mgmt.v1.BackupService.ListBackups:output_type -> data_mgmt.v1.ListBackupsResponse
	22, // 36: data_mgmt.v1.BackupService.DeleteBackup:output_type -> data_mgmt.v1.Empty
	9,  // 37: data_mgmt.v1.BackupService.RefreshDownloadUrl:output_type -> data_mgmt.v1.RefreshDownloadUrlResponse
	16, // 38: data_mgmt.v1.BackupService.ExportData:output_type -> data_mgmt.v1.DataExportResponse
	16, // 39: data_mgmt.v1.BackupService.GetExport:output_type -> data_mgmt.v1.DataExportResponse
	19, // 40: data_mgmt.v1.BackupService.ListExports:output_type -> data_mgmt.v1.ListExportsResponse
	11, // 41: data_mgmt.v1.BackupService.RequestAccountDeletion:output_type -> data_mgmt.v1.AccountDeletionResponse
	13, // 42: data_mgmt.v1.BackupService.GetDeletionStatus:output_type -> data_mgmt.v1.DeletionStatus
	21, // 43: data_mgmt.v1.BackupService.CleanupExpired:output_type -> data_mgmt.v1.CleanupExpiredResponse
	33, // [33:44] is the sub-list for method output_type
	22, // [22:33] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_data_mgmt_v1_backup_proto_init() }
func file_data_mgmt_v1_backup_proto_init() {
	if File_data_mgmt_v1_backup_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_mgmt_v1_backup_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BackupJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BackupStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBackupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBackupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBackupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBackupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBackupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBackupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDownloadUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDownloadUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDeletionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDeletionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeletionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletionStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletionStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataExportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataExportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExportsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExportsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupExpiredRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CleanupExpiredResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_mgmt_v1_backup_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_mgmt_v1_backup_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_mgmt_v1_backup_proto_goTypes,
		DependencyIndexes: file_data_mgmt_v1_backup_proto_depIdxs,
		MessageInfos:      file_data_mgmt_v1_backup_proto_msgTypes,
	}.Build()
	File_data_mgmt_v1_backup_proto = out.File
	file_data_mgmt_v1_backup_proto_rawDesc = nil
	file_data_mgmt_v1_backup_proto_goTypes = nil
	file_data_mgmt_v1_backup_proto_depIdxs = nil
}

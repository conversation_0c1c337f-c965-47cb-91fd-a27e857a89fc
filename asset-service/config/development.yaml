# Development Environment Configuration

# Override server settings for development
server:
  env: "development"
  grpc_port: 50054
  http_port: 8084

# Development database settings
database:
  # Use SQLite for development for easier setup
  type: "sqlite"
  sqlite_path: "./data/asset_service_dev.db"

  # PostgreSQL fallback configuration
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  name: "asset_service_dev"
  ssl_mode: "disable"

# Development storage settings
storage:
  provider: "minio"
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  bucket: "assets-dev"
  use_ssl: false
  public_url: "http://localhost:9000"
  region: "us-east-1"

# Development cleanup settings (more frequent for testing)
cleanup:
  enabled: true
  pending_upload_ttl: "30m"     # Shorter TTL for development
  orphaned_file_ttl: "2h"       # Shorter TTL for development
  deleted_asset_ttl: "24h"      # Shorter TTL for development
  cleanup_interval: "10m"       # More frequent cleanup
  batch_size: 50                # Smaller batches
  enable_storage_cleanup: true
  dry_run: false

# Development security settings (more permissive)
security:
  max_file_size: 52428800       # 50MB for development
  allowed_mime_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"
    - "application/json"
    - "text/markdown"
    - "text/csv"
    - "application/octet-stream"  # Allow binary files in dev
  enable_virus_scanning: false   # Disable for faster development
  quarantine_ttl: "24h"

# Development feature flags
features:
  enable_image_processing: true
  enable_rag: true
  enable_folders: true
  enable_versioning: true        # Enable for testing

# Development logging (more verbose)
logging:
  level: "debug"
  format: "text"
  output: "stdout"

# Development JWT settings (less secure for easier testing)
jwt:
  secret_key: "dev-secret-key-not-for-production"
  token_duration: "24h"
  refresh_duration: "168h"

# Development Kafka settings
kafka:
  enabled: false                 # Disable Kafka in development
  brokers:
    - "localhost:9092"

# Development services configuration
services:
  user_service:
    host: "localhost"
    port: 50051
    timeout: "30s"
    retries: 3
  
  ai_service:
    host: "localhost"
    port: 50055
    timeout: "60s"
    retries: 2

# Development monitoring
monitoring:
  enabled: true
  metrics_port: 9094             # Different port to avoid conflicts
  health_check_interval: "10s"   # More frequent health checks

# Development rate limiting (more permissive)
rate_limit:
  enabled: false                 # Disable rate limiting in development
  requests_per_minute: 1000
  burst: 100
  upload_requests_per_minute: 100
  upload_burst: 20

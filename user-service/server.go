package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/pkg-shared/auth"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	grpchandlers "github.com/social-content-ai/user-service/api/grpc"
	"github.com/social-content-ai/user-service/api/restful"
	"github.com/social-content-ai/user-service/config"
	"github.com/social-content-ai/user-service/ent"
	authuc "github.com/social-content-ai/user-service/usecase/auth"
	"github.com/social-content-ai/user-service/usecase/session"
	"github.com/social-content-ai/user-service/usecase/twofa"
	"github.com/social-content-ai/user-service/usecase/user"

	twofapkg "github.com/social-content-ai/user-service/pkg/twofa"

	_ "github.com/jackc/pgx/v5/stdlib"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

const (
	serviceName = "user-service"
	version     = "1.0.0"
)

// StartServer starts the user service server
func StartServer() error {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(&logging.Config{
		Level:  cfg.Logging.Level,
		Format: cfg.Logging.Format,
		Output: cfg.Logging.Output,
	})

	logger.WithField("service", serviceName).Info("Starting user service")

	// Initialize database connections
	readDB, writeDB, err := initDatabases(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize databases")
	}
	defer readDB.Close()
	defer writeDB.Close()

	// Run database migrations
	if err := runMigrations(writeDB, logger); err != nil {
		logger.WithError(err).Fatal("Failed to run migrations")
	}

	// Initialize JWT manager
	jwtManager := auth.NewJWTManager(
		cfg.JWT.SecretKey,
		cfg.JWT.TokenDuration,
		cfg.JWT.Issuer,
	)

	// Initialize cleanup service
	// cleanupConfig := cleanup.DefaultConfig()
	// cleanupService := cleanup.NewService(readDB, writeDB, nil, logger, cleanupConfig)
	// cleanupScheduler := cleanup.NewScheduler(cleanupService, logger)

	// Initialize use cases
	sessionUC := session.NewService(readDB, writeDB, logger)
	userUC := user.NewService(readDB, writeDB, logger)

	// For now, pass nil for optional dependencies
	// TODO: Initialize these properly when needed
	authUC := authuc.NewService(readDB, writeDB, jwtManager, sessionUC, logger, nil, nil, nil)

	// Initialize 2FA service - temporarily skip for now
	twofaSvc := twofapkg.NewService(logger, "Social Content AI")
	twofaUC := twofa.NewService(readDB, writeDB, twofaSvc, logger) // nil for now

	// Initialize gRPC handlers
	userHandler := grpchandlers.NewUserHandler(userUC, logger)
	authHandler := grpchandlers.NewAuthHandler(authUC, twofaUC, logger)
	sessionHandler := grpchandlers.NewSessionHandler(sessionUC, logger)

	// Create gRPC server
	grpcServer := grpc.NewServer(
		grpc.UnaryInterceptor(loggingInterceptor(logger)),
	)

	// Register services
	userv1.RegisterUserServiceServer(grpcServer, userHandler)
	userv1.RegisterAuthServiceServer(grpcServer, authHandler)
	userv1.RegisterSessionServiceServer(grpcServer, sessionHandler)

	// Enable reflection for development
	if cfg.Server.Env == "development" {
		reflection.Register(grpcServer)
	}

	// Setup RESTful API server
	gin.SetMode(gin.ReleaseMode)
	if cfg.Server.Env == "development" {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()
	router.Use(gin.Logger(), gin.Recovery())

	// Setup RESTful routes
	restful.SetupRoutes(router, authUC, userUC, sessionUC, twofaUC, nil, logger)

	// Setup Swagger documentation endpoint
	if cfg.Server.Env == "development" {
		// Import swagger docs
		// router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		router.GET("/docs/*any", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "Swagger documentation will be available here",
				"note":    "Run 'swag init' to generate docs",
			})
		})
	}

	// Start gRPC server
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		logger.WithError(err).Fatal("Failed to listen")
	}

	// Start gRPC server in goroutine
	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Serve(lis); err != nil {
			logger.WithError(err).Fatal("Failed to serve gRPC")
		}
	}()

	// Start RESTful API server in goroutine
	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := router.Run(fmt.Sprintf(":%d", cfg.Server.HTTPPort)); err != nil {
			logger.WithError(err).Fatal("Failed to serve HTTP")
		}
	}()

	// Wait for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	logger.Info("Shutting down user service")

	// Graceful shutdown
	_, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Stop gRPC server
	grpcServer.GracefulStop()

	logger.Info("User service stopped")
	return nil
}

// initDatabases initializes read and write database connections
func initDatabases(cfg *config.Config, logger logging.Logger) (*ent.Client, *ent.Client, error) {
	// Get database driver and DSN based on configuration
	driverName := cfg.Database.GetDriverName()
	dsn := cfg.Database.GetDSN()

	logger.WithFields(map[string]interface{}{
		"driver": driverName,
		"type":   cfg.Database.Type,
	}).Info("Initializing database connection")

	// Create data directory for SQLite if needed
	if cfg.Database.Type == "sqlite" {
		if err := os.MkdirAll("./data", 0755); err != nil {
			logger.WithError(err).Error("Failed to create data directory")
			return nil, nil, fmt.Errorf("failed to create data directory: %w", err)
		}
	}

	// Create Ent client
	client, err := ent.Open(driverName, dsn)
	if err != nil {
		logger.WithError(err).WithFields(map[string]interface{}{
			"driver": driverName,
			"type":   cfg.Database.Type,
		}).Error("Failed opening database connection")
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// For simplicity, use the same client for read and write
	// In production, you might want separate read replicas
	logger.WithField("type", cfg.Database.Type).Info("Database connections established")
	return client, client, nil
}

// runMigrations runs database migrations
func runMigrations(client *ent.Client, logger logging.Logger) error {
	logger.Info("Running database migrations")

	ctx := context.Background()
	if err := client.Schema.Create(ctx); err != nil {
		return fmt.Errorf("failed to create schema: %w", err)
	}

	logger.Info("Database migrations completed")
	return nil
}

// loggingInterceptor provides request logging for gRPC
func loggingInterceptor(logger logging.Logger) grpc.UnaryServerInterceptor {
	return func(
		ctx context.Context,
		req interface{},
		info *grpc.UnaryServerInfo,
		handler grpc.UnaryHandler,
	) (interface{}, error) {
		start := time.Now()

		// Call the handler
		resp, err := handler(ctx, req)

		// Log the request
		duration := time.Since(start)
		fields := map[string]interface{}{
			"method":   info.FullMethod,
			"duration": duration.String(),
		}

		if err != nil {
			fields["error"] = err.Error()
			logger.WithFields(fields).Error("gRPC request failed")
		} else {
			logger.WithFields(fields).Info("gRPC request completed")
		}

		return resp, err
	}
}

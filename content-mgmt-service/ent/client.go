// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
	"github.com/social-content-ai/content-mgmt-service/ent/template"
	"github.com/social-content-ai/content-mgmt-service/ent/workspace"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Post is the client for interacting with the Post builders.
	Post *PostClient
	// Template is the client for interacting with the Template builders.
	Template *TemplateClient
	// Workspace is the client for interacting with the Workspace builders.
	Workspace *WorkspaceClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Post = NewPostClient(c.config)
	c.Template = NewTemplateClient(c.config)
	c.Workspace = NewWorkspaceClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:       ctx,
		config:    cfg,
		Post:      NewPostClient(cfg),
		Template:  NewTemplateClient(cfg),
		Workspace: NewWorkspaceClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:       ctx,
		config:    cfg,
		Post:      NewPostClient(cfg),
		Template:  NewTemplateClient(cfg),
		Workspace: NewWorkspaceClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Post.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Post.Use(hooks...)
	c.Template.Use(hooks...)
	c.Workspace.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Post.Intercept(interceptors...)
	c.Template.Intercept(interceptors...)
	c.Workspace.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *PostMutation:
		return c.Post.mutate(ctx, m)
	case *TemplateMutation:
		return c.Template.mutate(ctx, m)
	case *WorkspaceMutation:
		return c.Workspace.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// PostClient is a client for the Post schema.
type PostClient struct {
	config
}

// NewPostClient returns a client for the Post from the given config.
func NewPostClient(c config) *PostClient {
	return &PostClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `post.Hooks(f(g(h())))`.
func (c *PostClient) Use(hooks ...Hook) {
	c.hooks.Post = append(c.hooks.Post, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `post.Intercept(f(g(h())))`.
func (c *PostClient) Intercept(interceptors ...Interceptor) {
	c.inters.Post = append(c.inters.Post, interceptors...)
}

// Create returns a builder for creating a Post entity.
func (c *PostClient) Create() *PostCreate {
	mutation := newPostMutation(c.config, OpCreate)
	return &PostCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Post entities.
func (c *PostClient) CreateBulk(builders ...*PostCreate) *PostCreateBulk {
	return &PostCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PostClient) MapCreateBulk(slice any, setFunc func(*PostCreate, int)) *PostCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PostCreateBulk{err: fmt.Errorf("calling to PostClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PostCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PostCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Post.
func (c *PostClient) Update() *PostUpdate {
	mutation := newPostMutation(c.config, OpUpdate)
	return &PostUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PostClient) UpdateOne(po *Post) *PostUpdateOne {
	mutation := newPostMutation(c.config, OpUpdateOne, withPost(po))
	return &PostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PostClient) UpdateOneID(id uuid.UUID) *PostUpdateOne {
	mutation := newPostMutation(c.config, OpUpdateOne, withPostID(id))
	return &PostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Post.
func (c *PostClient) Delete() *PostDelete {
	mutation := newPostMutation(c.config, OpDelete)
	return &PostDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PostClient) DeleteOne(po *Post) *PostDeleteOne {
	return c.DeleteOneID(po.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PostClient) DeleteOneID(id uuid.UUID) *PostDeleteOne {
	builder := c.Delete().Where(post.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PostDeleteOne{builder}
}

// Query returns a query builder for Post.
func (c *PostClient) Query() *PostQuery {
	return &PostQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePost},
		inters: c.Interceptors(),
	}
}

// Get returns a Post entity by its id.
func (c *PostClient) Get(ctx context.Context, id uuid.UUID) (*Post, error) {
	return c.Query().Where(post.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PostClient) GetX(ctx context.Context, id uuid.UUID) *Post {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PostClient) Hooks() []Hook {
	return c.hooks.Post
}

// Interceptors returns the client interceptors.
func (c *PostClient) Interceptors() []Interceptor {
	return c.inters.Post
}

func (c *PostClient) mutate(ctx context.Context, m *PostMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PostCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PostUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PostUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PostDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Post mutation op: %q", m.Op())
	}
}

// TemplateClient is a client for the Template schema.
type TemplateClient struct {
	config
}

// NewTemplateClient returns a client for the Template from the given config.
func NewTemplateClient(c config) *TemplateClient {
	return &TemplateClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `template.Hooks(f(g(h())))`.
func (c *TemplateClient) Use(hooks ...Hook) {
	c.hooks.Template = append(c.hooks.Template, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `template.Intercept(f(g(h())))`.
func (c *TemplateClient) Intercept(interceptors ...Interceptor) {
	c.inters.Template = append(c.inters.Template, interceptors...)
}

// Create returns a builder for creating a Template entity.
func (c *TemplateClient) Create() *TemplateCreate {
	mutation := newTemplateMutation(c.config, OpCreate)
	return &TemplateCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Template entities.
func (c *TemplateClient) CreateBulk(builders ...*TemplateCreate) *TemplateCreateBulk {
	return &TemplateCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TemplateClient) MapCreateBulk(slice any, setFunc func(*TemplateCreate, int)) *TemplateCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TemplateCreateBulk{err: fmt.Errorf("calling to TemplateClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TemplateCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TemplateCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Template.
func (c *TemplateClient) Update() *TemplateUpdate {
	mutation := newTemplateMutation(c.config, OpUpdate)
	return &TemplateUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TemplateClient) UpdateOne(t *Template) *TemplateUpdateOne {
	mutation := newTemplateMutation(c.config, OpUpdateOne, withTemplate(t))
	return &TemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TemplateClient) UpdateOneID(id uuid.UUID) *TemplateUpdateOne {
	mutation := newTemplateMutation(c.config, OpUpdateOne, withTemplateID(id))
	return &TemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Template.
func (c *TemplateClient) Delete() *TemplateDelete {
	mutation := newTemplateMutation(c.config, OpDelete)
	return &TemplateDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TemplateClient) DeleteOne(t *Template) *TemplateDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TemplateClient) DeleteOneID(id uuid.UUID) *TemplateDeleteOne {
	builder := c.Delete().Where(template.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TemplateDeleteOne{builder}
}

// Query returns a query builder for Template.
func (c *TemplateClient) Query() *TemplateQuery {
	return &TemplateQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTemplate},
		inters: c.Interceptors(),
	}
}

// Get returns a Template entity by its id.
func (c *TemplateClient) Get(ctx context.Context, id uuid.UUID) (*Template, error) {
	return c.Query().Where(template.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TemplateClient) GetX(ctx context.Context, id uuid.UUID) *Template {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TemplateClient) Hooks() []Hook {
	return c.hooks.Template
}

// Interceptors returns the client interceptors.
func (c *TemplateClient) Interceptors() []Interceptor {
	return c.inters.Template
}

func (c *TemplateClient) mutate(ctx context.Context, m *TemplateMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TemplateCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TemplateUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TemplateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TemplateDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Template mutation op: %q", m.Op())
	}
}

// WorkspaceClient is a client for the Workspace schema.
type WorkspaceClient struct {
	config
}

// NewWorkspaceClient returns a client for the Workspace from the given config.
func NewWorkspaceClient(c config) *WorkspaceClient {
	return &WorkspaceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `workspace.Hooks(f(g(h())))`.
func (c *WorkspaceClient) Use(hooks ...Hook) {
	c.hooks.Workspace = append(c.hooks.Workspace, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `workspace.Intercept(f(g(h())))`.
func (c *WorkspaceClient) Intercept(interceptors ...Interceptor) {
	c.inters.Workspace = append(c.inters.Workspace, interceptors...)
}

// Create returns a builder for creating a Workspace entity.
func (c *WorkspaceClient) Create() *WorkspaceCreate {
	mutation := newWorkspaceMutation(c.config, OpCreate)
	return &WorkspaceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Workspace entities.
func (c *WorkspaceClient) CreateBulk(builders ...*WorkspaceCreate) *WorkspaceCreateBulk {
	return &WorkspaceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WorkspaceClient) MapCreateBulk(slice any, setFunc func(*WorkspaceCreate, int)) *WorkspaceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WorkspaceCreateBulk{err: fmt.Errorf("calling to WorkspaceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WorkspaceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WorkspaceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Workspace.
func (c *WorkspaceClient) Update() *WorkspaceUpdate {
	mutation := newWorkspaceMutation(c.config, OpUpdate)
	return &WorkspaceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WorkspaceClient) UpdateOne(w *Workspace) *WorkspaceUpdateOne {
	mutation := newWorkspaceMutation(c.config, OpUpdateOne, withWorkspace(w))
	return &WorkspaceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WorkspaceClient) UpdateOneID(id uuid.UUID) *WorkspaceUpdateOne {
	mutation := newWorkspaceMutation(c.config, OpUpdateOne, withWorkspaceID(id))
	return &WorkspaceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Workspace.
func (c *WorkspaceClient) Delete() *WorkspaceDelete {
	mutation := newWorkspaceMutation(c.config, OpDelete)
	return &WorkspaceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WorkspaceClient) DeleteOne(w *Workspace) *WorkspaceDeleteOne {
	return c.DeleteOneID(w.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WorkspaceClient) DeleteOneID(id uuid.UUID) *WorkspaceDeleteOne {
	builder := c.Delete().Where(workspace.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WorkspaceDeleteOne{builder}
}

// Query returns a query builder for Workspace.
func (c *WorkspaceClient) Query() *WorkspaceQuery {
	return &WorkspaceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWorkspace},
		inters: c.Interceptors(),
	}
}

// Get returns a Workspace entity by its id.
func (c *WorkspaceClient) Get(ctx context.Context, id uuid.UUID) (*Workspace, error) {
	return c.Query().Where(workspace.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WorkspaceClient) GetX(ctx context.Context, id uuid.UUID) *Workspace {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *WorkspaceClient) Hooks() []Hook {
	return c.hooks.Workspace
}

// Interceptors returns the client interceptors.
func (c *WorkspaceClient) Interceptors() []Interceptor {
	return c.inters.Workspace
}

func (c *WorkspaceClient) mutate(ctx context.Context, m *WorkspaceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WorkspaceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WorkspaceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WorkspaceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WorkspaceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Workspace mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Post, Template, Workspace []ent.Hook
	}
	inters struct {
		Post, Template, Workspace []ent.Interceptor
	}
)

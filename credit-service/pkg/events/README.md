# Credit Service Events - Simplified Architecture

## Overview

Hệ thống event đã được refactor để đơn giản hóa và sử dụng trực tiếp `pkg-shared/kafka` làm base, loại bỏ các layer phức tạp và duplicate code.

## Key Improvements

### 1. **Unified Topic Structure**
- **Before**: Nhiều topics khác nhau (`subscription.events`, `subscription.payment.completed`, etc.)
- **After**: Sử dụng single topic `billing.events` từ pkg-shared/kafka
- **Benefit**: Đơn giản hóa routing, dễ maintain

### 2. **Single Event Handler**
- **Before**: Nhiều handlers riêng biệt cho từng event type
- **After**: `SubscriptionEventHandler` xử lý tất cả subscription events
- **Benefit**: Centralized logic, dễ debug và maintain

### 3. **Direct pkg-shared Integration**
- **Before**: Custom adapter layers và wrapper classes
- **After**: Sử dụng trực tiếp `sharedKafka.Consumer` và `sharedKafka.Producer`
- **Benefit**: Ít code hơn, performance tốt hơn, consistency với other services

### 4. **Simplified Event Types**
```go
const (
    EventTypePaymentCompleted      = "subscription.payment.completed"
    EventTypePaymentFailed         = "subscription.payment.failed"
    EventTypeSubscriptionActivated = "subscription.activated"
    EventTypeSubscriptionCancelled = "subscription.cancelled"
)
```

## Architecture

```
SimpleEventService
├── sharedKafka.Consumer (pkg-shared/kafka)
├── sharedKafka.Producer (pkg-shared/kafka)
└── SubscriptionEventHandler
    ├── HandleBillingEvent() -> routes to specific handlers
    ├── handlePaymentCompleted()
    ├── handlePaymentFailed()
    ├── handleSubscriptionActivated()
    └── handleSubscriptionCancelled()
```

## Usage

### 1. **Basic Setup**

```go
// Create configuration
config := events.ProductionSimpleEventServiceConfig(
    []string{"localhost:9092"},
    "credit-service-subscription-events",
)

// Create service
eventService, err := events.NewSimpleEventService(
    config,
    logger,
    subscriptionUseCase,
    transactionUseCase,
)

// Start service
ctx := context.Background()
err = eventService.Start(ctx)
```

### 2. **Publishing Events**

```go
// Payment completed
err := eventService.PublishPaymentCompleted(
    ctx,
    "user_123",
    "sub_456", 
    "pay_789",
    2999, // amount in cents
    "USD",
    1000, // credits
)

// Payment failed
err := eventService.PublishPaymentFailed(
    ctx,
    "user_123",
    "sub_456",
    "pay_789", 
    "Card declined",
)

// Custom event
customEvent := &events.SubscriptionEvent{
    Type:           events.EventTypeSubscriptionActivated,
    UserID:         "user_123",
    SubscriptionID: "sub_456",
    Credits:        2000,
    // ... other fields
}
err := eventService.PublishSubscriptionEvent(ctx, customEvent)
```

### 3. **Event Processing Flow**

1. **Event Published** → `billing.events` topic (pkg-shared/kafka)
2. **Consumer Receives** → `SubscriptionEventHandler.HandleBillingEvent()`
3. **Event Routing** → Based on event type, routes to specific handler
4. **Business Logic** → Calls subscription/transaction use cases
5. **Credits Processing** → Through transaction flow (unified pattern)

## Event Flow Examples

### Payment Completed Flow
```
PublishPaymentCompleted()
  ↓
billing.events topic
  ↓
HandleBillingEvent()
  ↓
handlePaymentCompleted()
  ↓
1. subscriptionUC.HandlePaymentSuccess()
2. transactionUC.ProcessSubscriptionPayment() // Add credits
```

### Subscription Activated Flow
```
SubscriptionActivated Event
  ↓
billing.events topic
  ↓
HandleBillingEvent()
  ↓
handleSubscriptionActivated()
  ↓
transactionUC.ProcessSubscriptionPayment() // Add plan credits
```

## Files Structure

### Core Files (Keep)
- `subscription_events.go` - Unified event definitions and handler
- `simple_event_service.go` - Simplified service using pkg-shared/kafka directly

### Legacy Files (Can be removed)
- `event_service.go` - Complex event service with adapters
- `kafka_adapter.go` - Custom adapter (not needed)
- `kafka_consumer.go` - Custom consumer interface
- `subscription_event_handlers.go` - Old complex handlers
- `event_adapter.go` - Additional adapter layer
- `consumer.go` - Legacy consumer
- `publisher.go` - Legacy publisher

## Configuration

### Development
```go
config := events.DefaultSimpleEventServiceConfig()
// Uses localhost:9092, mock transaction service
```

### Production
```go
config := events.ProductionSimpleEventServiceConfig(
    []string{"kafka-broker-1:9092", "kafka-broker-2:9092"},
    "credit-service-prod",
)
```

## Benefits of Simplified Architecture

1. **Reduced Complexity**: 70% less code, easier to understand
2. **Better Performance**: Direct pkg-shared usage, no adapter overhead
3. **Consistency**: Same patterns as other services using pkg-shared/kafka
4. **Maintainability**: Single handler, unified event structure
5. **Debugging**: Centralized logging, clear event flow
6. **Testing**: Simpler mocking, focused test cases

## Migration Guide

### From Old EventService to SimpleEventService

```go
// Old way
eventService, err := events.NewEventServiceBuilder().
    WithConfig(config).
    WithLogger(logger).
    WithSubscriptionUseCase(subscriptionUC).
    WithCreditUseCase(creditUC).
    WithTransactionUseCase(transactionUC).
    Build()

// New way
eventService, err := events.NewSimpleEventService(
    config,
    logger,
    subscriptionUC,
    transactionUC,
)
```

### Topic Migration
- All subscription events now use `billing.events` topic
- Event types differentiate the specific event
- No need for separate topics per event type

## Testing

```bash
# Run simple example
go run examples/simple_subscription_events.go

# Build and test
go build -o bin/credit-service .
```

## Next Steps

1. Remove legacy files after confirming new system works
2. Update other services to use similar simplified patterns
3. Add monitoring and metrics to SimpleEventService
4. Consider adding event replay capabilities for debugging

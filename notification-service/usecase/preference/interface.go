package preference

import (
	"context"
)

// UseCase defines the preference business logic interface
type UseCase interface {
	// Get user notification preferences
	GetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error)
	
	// Update user notification preferences
	UpdatePreferences(ctx context.Context, req *UpdatePreferencesRequest) (*PreferencesResponse, error)
	
	// Reset preferences to default
	ResetPreferences(ctx context.Context, userID string) (*PreferencesResponse, error)
	
	// Get default preferences
	GetDefaultPreferences(ctx context.Context) (*PreferencesResponse, error)
}

// PreferencesResponse represents user notification preferences
type PreferencesResponse struct {
	UserID           string                    `json:"user_id"`
	EmailEnabled     bool                      `json:"email_enabled"`
	SMSEnabled       bool                      `json:"sms_enabled"`
	PushEnabled      bool                      `json:"push_enabled"`
	WebSocketEnabled bool                      `json:"websocket_enabled"`
	InAppEnabled     bool                      `json:"in_app_enabled"`
	EventPreferences map[string]EventPreference `json:"event_preferences"`
	QuietHours       *QuietHours               `json:"quiet_hours,omitempty"`
	Timezone         string                    `json:"timezone"`
	Language         string                    `json:"language"`
	CreatedAt        string                    `json:"created_at"`
	UpdatedAt        string                    `json:"updated_at"`
}

// UpdatePreferencesRequest represents a request to update preferences
type UpdatePreferencesRequest struct {
	UserID           string                    `json:"user_id" validate:"required"`
	EmailEnabled     *bool                     `json:"email_enabled,omitempty"`
	SMSEnabled       *bool                     `json:"sms_enabled,omitempty"`
	PushEnabled      *bool                     `json:"push_enabled,omitempty"`
	WebSocketEnabled *bool                     `json:"websocket_enabled,omitempty"`
	InAppEnabled     *bool                     `json:"in_app_enabled,omitempty"`
	EventPreferences map[string]EventPreference `json:"event_preferences,omitempty"`
	QuietHours       *QuietHours               `json:"quiet_hours,omitempty"`
	Timezone         string                    `json:"timezone,omitempty"`
	Language         string                    `json:"language,omitempty"`
}

// EventPreference represents preferences for a specific event type
type EventPreference struct {
	Email     bool `json:"email"`
	SMS       bool `json:"sms"`
	Push      bool `json:"push"`
	WebSocket bool `json:"websocket"`
	InApp     bool `json:"in_app"`
}

// QuietHours represents user's quiet hours
type QuietHours struct {
	Enabled   bool `json:"enabled"`
	StartHour int  `json:"start_hour"` // 0-23
	EndHour   int  `json:"end_hour"`   // 0-23
}

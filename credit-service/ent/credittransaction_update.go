// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/credittransaction"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditTransactionUpdate is the builder for updating CreditTransaction entities.
type CreditTransactionUpdate struct {
	config
	hooks    []Hook
	mutation *CreditTransactionMutation
}

// Where appends a list predicates to the CreditTransactionUpdate builder.
func (ctu *CreditTransactionUpdate) Where(ps ...predicate.CreditTransaction) *CreditTransactionUpdate {
	ctu.mutation.Where(ps...)
	return ctu
}

// SetUserID sets the "user_id" field.
func (ctu *CreditTransactionUpdate) SetUserID(u uuid.UUID) *CreditTransactionUpdate {
	ctu.mutation.SetUserID(u)
	return ctu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableUserID(u *uuid.UUID) *CreditTransactionUpdate {
	if u != nil {
		ctu.SetUserID(*u)
	}
	return ctu
}

// SetType sets the "type" field.
func (ctu *CreditTransactionUpdate) SetType(c credittransaction.Type) *CreditTransactionUpdate {
	ctu.mutation.SetType(c)
	return ctu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableType(c *credittransaction.Type) *CreditTransactionUpdate {
	if c != nil {
		ctu.SetType(*c)
	}
	return ctu
}

// SetAmount sets the "amount" field.
func (ctu *CreditTransactionUpdate) SetAmount(i int) *CreditTransactionUpdate {
	ctu.mutation.ResetAmount()
	ctu.mutation.SetAmount(i)
	return ctu
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableAmount(i *int) *CreditTransactionUpdate {
	if i != nil {
		ctu.SetAmount(*i)
	}
	return ctu
}

// AddAmount adds i to the "amount" field.
func (ctu *CreditTransactionUpdate) AddAmount(i int) *CreditTransactionUpdate {
	ctu.mutation.AddAmount(i)
	return ctu
}

// SetDescription sets the "description" field.
func (ctu *CreditTransactionUpdate) SetDescription(s string) *CreditTransactionUpdate {
	ctu.mutation.SetDescription(s)
	return ctu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableDescription(s *string) *CreditTransactionUpdate {
	if s != nil {
		ctu.SetDescription(*s)
	}
	return ctu
}

// SetReferenceID sets the "reference_id" field.
func (ctu *CreditTransactionUpdate) SetReferenceID(s string) *CreditTransactionUpdate {
	ctu.mutation.SetReferenceID(s)
	return ctu
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableReferenceID(s *string) *CreditTransactionUpdate {
	if s != nil {
		ctu.SetReferenceID(*s)
	}
	return ctu
}

// ClearReferenceID clears the value of the "reference_id" field.
func (ctu *CreditTransactionUpdate) ClearReferenceID() *CreditTransactionUpdate {
	ctu.mutation.ClearReferenceID()
	return ctu
}

// SetReferenceType sets the "reference_type" field.
func (ctu *CreditTransactionUpdate) SetReferenceType(s string) *CreditTransactionUpdate {
	ctu.mutation.SetReferenceType(s)
	return ctu
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableReferenceType(s *string) *CreditTransactionUpdate {
	if s != nil {
		ctu.SetReferenceType(*s)
	}
	return ctu
}

// ClearReferenceType clears the value of the "reference_type" field.
func (ctu *CreditTransactionUpdate) ClearReferenceType() *CreditTransactionUpdate {
	ctu.mutation.ClearReferenceType()
	return ctu
}

// SetPaymentID sets the "payment_id" field.
func (ctu *CreditTransactionUpdate) SetPaymentID(s string) *CreditTransactionUpdate {
	ctu.mutation.SetPaymentID(s)
	return ctu
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillablePaymentID(s *string) *CreditTransactionUpdate {
	if s != nil {
		ctu.SetPaymentID(*s)
	}
	return ctu
}

// ClearPaymentID clears the value of the "payment_id" field.
func (ctu *CreditTransactionUpdate) ClearPaymentID() *CreditTransactionUpdate {
	ctu.mutation.ClearPaymentID()
	return ctu
}

// SetInvoiceID sets the "invoice_id" field.
func (ctu *CreditTransactionUpdate) SetInvoiceID(s string) *CreditTransactionUpdate {
	ctu.mutation.SetInvoiceID(s)
	return ctu
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableInvoiceID(s *string) *CreditTransactionUpdate {
	if s != nil {
		ctu.SetInvoiceID(*s)
	}
	return ctu
}

// ClearInvoiceID clears the value of the "invoice_id" field.
func (ctu *CreditTransactionUpdate) ClearInvoiceID() *CreditTransactionUpdate {
	ctu.mutation.ClearInvoiceID()
	return ctu
}

// SetStatus sets the "status" field.
func (ctu *CreditTransactionUpdate) SetStatus(c credittransaction.Status) *CreditTransactionUpdate {
	ctu.mutation.SetStatus(c)
	return ctu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableStatus(c *credittransaction.Status) *CreditTransactionUpdate {
	if c != nil {
		ctu.SetStatus(*c)
	}
	return ctu
}

// SetMetadata sets the "metadata" field.
func (ctu *CreditTransactionUpdate) SetMetadata(m map[string]interface{}) *CreditTransactionUpdate {
	ctu.mutation.SetMetadata(m)
	return ctu
}

// ClearMetadata clears the value of the "metadata" field.
func (ctu *CreditTransactionUpdate) ClearMetadata() *CreditTransactionUpdate {
	ctu.mutation.ClearMetadata()
	return ctu
}

// SetProcessedAt sets the "processed_at" field.
func (ctu *CreditTransactionUpdate) SetProcessedAt(t time.Time) *CreditTransactionUpdate {
	ctu.mutation.SetProcessedAt(t)
	return ctu
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ctu *CreditTransactionUpdate) SetNillableProcessedAt(t *time.Time) *CreditTransactionUpdate {
	if t != nil {
		ctu.SetProcessedAt(*t)
	}
	return ctu
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ctu *CreditTransactionUpdate) ClearProcessedAt() *CreditTransactionUpdate {
	ctu.mutation.ClearProcessedAt()
	return ctu
}

// SetUpdatedAt sets the "updated_at" field.
func (ctu *CreditTransactionUpdate) SetUpdatedAt(t time.Time) *CreditTransactionUpdate {
	ctu.mutation.SetUpdatedAt(t)
	return ctu
}

// Mutation returns the CreditTransactionMutation object of the builder.
func (ctu *CreditTransactionUpdate) Mutation() *CreditTransactionMutation {
	return ctu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ctu *CreditTransactionUpdate) Save(ctx context.Context) (int, error) {
	ctu.defaults()
	return withHooks(ctx, ctu.sqlSave, ctu.mutation, ctu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ctu *CreditTransactionUpdate) SaveX(ctx context.Context) int {
	affected, err := ctu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ctu *CreditTransactionUpdate) Exec(ctx context.Context) error {
	_, err := ctu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ctu *CreditTransactionUpdate) ExecX(ctx context.Context) {
	if err := ctu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ctu *CreditTransactionUpdate) defaults() {
	if _, ok := ctu.mutation.UpdatedAt(); !ok {
		v := credittransaction.UpdateDefaultUpdatedAt()
		ctu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ctu *CreditTransactionUpdate) check() error {
	if v, ok := ctu.mutation.GetType(); ok {
		if err := credittransaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.type": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.Description(); ok {
		if err := credittransaction.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.description": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.ReferenceID(); ok {
		if err := credittransaction.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_id": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.ReferenceType(); ok {
		if err := credittransaction.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_type": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.PaymentID(); ok {
		if err := credittransaction.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.payment_id": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.InvoiceID(); ok {
		if err := credittransaction.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.invoice_id": %w`, err)}
		}
	}
	if v, ok := ctu.mutation.Status(); ok {
		if err := credittransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.status": %w`, err)}
		}
	}
	return nil
}

func (ctu *CreditTransactionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ctu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(credittransaction.Table, credittransaction.Columns, sqlgraph.NewFieldSpec(credittransaction.FieldID, field.TypeUUID))
	if ps := ctu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ctu.mutation.UserID(); ok {
		_spec.SetField(credittransaction.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ctu.mutation.GetType(); ok {
		_spec.SetField(credittransaction.FieldType, field.TypeEnum, value)
	}
	if value, ok := ctu.mutation.Amount(); ok {
		_spec.SetField(credittransaction.FieldAmount, field.TypeInt, value)
	}
	if value, ok := ctu.mutation.AddedAmount(); ok {
		_spec.AddField(credittransaction.FieldAmount, field.TypeInt, value)
	}
	if value, ok := ctu.mutation.Description(); ok {
		_spec.SetField(credittransaction.FieldDescription, field.TypeString, value)
	}
	if value, ok := ctu.mutation.ReferenceID(); ok {
		_spec.SetField(credittransaction.FieldReferenceID, field.TypeString, value)
	}
	if ctu.mutation.ReferenceIDCleared() {
		_spec.ClearField(credittransaction.FieldReferenceID, field.TypeString)
	}
	if value, ok := ctu.mutation.ReferenceType(); ok {
		_spec.SetField(credittransaction.FieldReferenceType, field.TypeString, value)
	}
	if ctu.mutation.ReferenceTypeCleared() {
		_spec.ClearField(credittransaction.FieldReferenceType, field.TypeString)
	}
	if value, ok := ctu.mutation.PaymentID(); ok {
		_spec.SetField(credittransaction.FieldPaymentID, field.TypeString, value)
	}
	if ctu.mutation.PaymentIDCleared() {
		_spec.ClearField(credittransaction.FieldPaymentID, field.TypeString)
	}
	if value, ok := ctu.mutation.InvoiceID(); ok {
		_spec.SetField(credittransaction.FieldInvoiceID, field.TypeString, value)
	}
	if ctu.mutation.InvoiceIDCleared() {
		_spec.ClearField(credittransaction.FieldInvoiceID, field.TypeString)
	}
	if value, ok := ctu.mutation.Status(); ok {
		_spec.SetField(credittransaction.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ctu.mutation.Metadata(); ok {
		_spec.SetField(credittransaction.FieldMetadata, field.TypeJSON, value)
	}
	if ctu.mutation.MetadataCleared() {
		_spec.ClearField(credittransaction.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ctu.mutation.ProcessedAt(); ok {
		_spec.SetField(credittransaction.FieldProcessedAt, field.TypeTime, value)
	}
	if ctu.mutation.ProcessedAtCleared() {
		_spec.ClearField(credittransaction.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ctu.mutation.UpdatedAt(); ok {
		_spec.SetField(credittransaction.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ctu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{credittransaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ctu.mutation.done = true
	return n, nil
}

// CreditTransactionUpdateOne is the builder for updating a single CreditTransaction entity.
type CreditTransactionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CreditTransactionMutation
}

// SetUserID sets the "user_id" field.
func (ctuo *CreditTransactionUpdateOne) SetUserID(u uuid.UUID) *CreditTransactionUpdateOne {
	ctuo.mutation.SetUserID(u)
	return ctuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableUserID(u *uuid.UUID) *CreditTransactionUpdateOne {
	if u != nil {
		ctuo.SetUserID(*u)
	}
	return ctuo
}

// SetType sets the "type" field.
func (ctuo *CreditTransactionUpdateOne) SetType(c credittransaction.Type) *CreditTransactionUpdateOne {
	ctuo.mutation.SetType(c)
	return ctuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableType(c *credittransaction.Type) *CreditTransactionUpdateOne {
	if c != nil {
		ctuo.SetType(*c)
	}
	return ctuo
}

// SetAmount sets the "amount" field.
func (ctuo *CreditTransactionUpdateOne) SetAmount(i int) *CreditTransactionUpdateOne {
	ctuo.mutation.ResetAmount()
	ctuo.mutation.SetAmount(i)
	return ctuo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableAmount(i *int) *CreditTransactionUpdateOne {
	if i != nil {
		ctuo.SetAmount(*i)
	}
	return ctuo
}

// AddAmount adds i to the "amount" field.
func (ctuo *CreditTransactionUpdateOne) AddAmount(i int) *CreditTransactionUpdateOne {
	ctuo.mutation.AddAmount(i)
	return ctuo
}

// SetDescription sets the "description" field.
func (ctuo *CreditTransactionUpdateOne) SetDescription(s string) *CreditTransactionUpdateOne {
	ctuo.mutation.SetDescription(s)
	return ctuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableDescription(s *string) *CreditTransactionUpdateOne {
	if s != nil {
		ctuo.SetDescription(*s)
	}
	return ctuo
}

// SetReferenceID sets the "reference_id" field.
func (ctuo *CreditTransactionUpdateOne) SetReferenceID(s string) *CreditTransactionUpdateOne {
	ctuo.mutation.SetReferenceID(s)
	return ctuo
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableReferenceID(s *string) *CreditTransactionUpdateOne {
	if s != nil {
		ctuo.SetReferenceID(*s)
	}
	return ctuo
}

// ClearReferenceID clears the value of the "reference_id" field.
func (ctuo *CreditTransactionUpdateOne) ClearReferenceID() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearReferenceID()
	return ctuo
}

// SetReferenceType sets the "reference_type" field.
func (ctuo *CreditTransactionUpdateOne) SetReferenceType(s string) *CreditTransactionUpdateOne {
	ctuo.mutation.SetReferenceType(s)
	return ctuo
}

// SetNillableReferenceType sets the "reference_type" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableReferenceType(s *string) *CreditTransactionUpdateOne {
	if s != nil {
		ctuo.SetReferenceType(*s)
	}
	return ctuo
}

// ClearReferenceType clears the value of the "reference_type" field.
func (ctuo *CreditTransactionUpdateOne) ClearReferenceType() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearReferenceType()
	return ctuo
}

// SetPaymentID sets the "payment_id" field.
func (ctuo *CreditTransactionUpdateOne) SetPaymentID(s string) *CreditTransactionUpdateOne {
	ctuo.mutation.SetPaymentID(s)
	return ctuo
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillablePaymentID(s *string) *CreditTransactionUpdateOne {
	if s != nil {
		ctuo.SetPaymentID(*s)
	}
	return ctuo
}

// ClearPaymentID clears the value of the "payment_id" field.
func (ctuo *CreditTransactionUpdateOne) ClearPaymentID() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearPaymentID()
	return ctuo
}

// SetInvoiceID sets the "invoice_id" field.
func (ctuo *CreditTransactionUpdateOne) SetInvoiceID(s string) *CreditTransactionUpdateOne {
	ctuo.mutation.SetInvoiceID(s)
	return ctuo
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableInvoiceID(s *string) *CreditTransactionUpdateOne {
	if s != nil {
		ctuo.SetInvoiceID(*s)
	}
	return ctuo
}

// ClearInvoiceID clears the value of the "invoice_id" field.
func (ctuo *CreditTransactionUpdateOne) ClearInvoiceID() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearInvoiceID()
	return ctuo
}

// SetStatus sets the "status" field.
func (ctuo *CreditTransactionUpdateOne) SetStatus(c credittransaction.Status) *CreditTransactionUpdateOne {
	ctuo.mutation.SetStatus(c)
	return ctuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableStatus(c *credittransaction.Status) *CreditTransactionUpdateOne {
	if c != nil {
		ctuo.SetStatus(*c)
	}
	return ctuo
}

// SetMetadata sets the "metadata" field.
func (ctuo *CreditTransactionUpdateOne) SetMetadata(m map[string]interface{}) *CreditTransactionUpdateOne {
	ctuo.mutation.SetMetadata(m)
	return ctuo
}

// ClearMetadata clears the value of the "metadata" field.
func (ctuo *CreditTransactionUpdateOne) ClearMetadata() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearMetadata()
	return ctuo
}

// SetProcessedAt sets the "processed_at" field.
func (ctuo *CreditTransactionUpdateOne) SetProcessedAt(t time.Time) *CreditTransactionUpdateOne {
	ctuo.mutation.SetProcessedAt(t)
	return ctuo
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ctuo *CreditTransactionUpdateOne) SetNillableProcessedAt(t *time.Time) *CreditTransactionUpdateOne {
	if t != nil {
		ctuo.SetProcessedAt(*t)
	}
	return ctuo
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ctuo *CreditTransactionUpdateOne) ClearProcessedAt() *CreditTransactionUpdateOne {
	ctuo.mutation.ClearProcessedAt()
	return ctuo
}

// SetUpdatedAt sets the "updated_at" field.
func (ctuo *CreditTransactionUpdateOne) SetUpdatedAt(t time.Time) *CreditTransactionUpdateOne {
	ctuo.mutation.SetUpdatedAt(t)
	return ctuo
}

// Mutation returns the CreditTransactionMutation object of the builder.
func (ctuo *CreditTransactionUpdateOne) Mutation() *CreditTransactionMutation {
	return ctuo.mutation
}

// Where appends a list predicates to the CreditTransactionUpdate builder.
func (ctuo *CreditTransactionUpdateOne) Where(ps ...predicate.CreditTransaction) *CreditTransactionUpdateOne {
	ctuo.mutation.Where(ps...)
	return ctuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ctuo *CreditTransactionUpdateOne) Select(field string, fields ...string) *CreditTransactionUpdateOne {
	ctuo.fields = append([]string{field}, fields...)
	return ctuo
}

// Save executes the query and returns the updated CreditTransaction entity.
func (ctuo *CreditTransactionUpdateOne) Save(ctx context.Context) (*CreditTransaction, error) {
	ctuo.defaults()
	return withHooks(ctx, ctuo.sqlSave, ctuo.mutation, ctuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ctuo *CreditTransactionUpdateOne) SaveX(ctx context.Context) *CreditTransaction {
	node, err := ctuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ctuo *CreditTransactionUpdateOne) Exec(ctx context.Context) error {
	_, err := ctuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ctuo *CreditTransactionUpdateOne) ExecX(ctx context.Context) {
	if err := ctuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ctuo *CreditTransactionUpdateOne) defaults() {
	if _, ok := ctuo.mutation.UpdatedAt(); !ok {
		v := credittransaction.UpdateDefaultUpdatedAt()
		ctuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ctuo *CreditTransactionUpdateOne) check() error {
	if v, ok := ctuo.mutation.GetType(); ok {
		if err := credittransaction.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.type": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.Description(); ok {
		if err := credittransaction.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.description": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.ReferenceID(); ok {
		if err := credittransaction.ReferenceIDValidator(v); err != nil {
			return &ValidationError{Name: "reference_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_id": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.ReferenceType(); ok {
		if err := credittransaction.ReferenceTypeValidator(v); err != nil {
			return &ValidationError{Name: "reference_type", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.reference_type": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.PaymentID(); ok {
		if err := credittransaction.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.payment_id": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.InvoiceID(); ok {
		if err := credittransaction.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.invoice_id": %w`, err)}
		}
	}
	if v, ok := ctuo.mutation.Status(); ok {
		if err := credittransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "CreditTransaction.status": %w`, err)}
		}
	}
	return nil
}

func (ctuo *CreditTransactionUpdateOne) sqlSave(ctx context.Context) (_node *CreditTransaction, err error) {
	if err := ctuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(credittransaction.Table, credittransaction.Columns, sqlgraph.NewFieldSpec(credittransaction.FieldID, field.TypeUUID))
	id, ok := ctuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CreditTransaction.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ctuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, credittransaction.FieldID)
		for _, f := range fields {
			if !credittransaction.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != credittransaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ctuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ctuo.mutation.UserID(); ok {
		_spec.SetField(credittransaction.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ctuo.mutation.GetType(); ok {
		_spec.SetField(credittransaction.FieldType, field.TypeEnum, value)
	}
	if value, ok := ctuo.mutation.Amount(); ok {
		_spec.SetField(credittransaction.FieldAmount, field.TypeInt, value)
	}
	if value, ok := ctuo.mutation.AddedAmount(); ok {
		_spec.AddField(credittransaction.FieldAmount, field.TypeInt, value)
	}
	if value, ok := ctuo.mutation.Description(); ok {
		_spec.SetField(credittransaction.FieldDescription, field.TypeString, value)
	}
	if value, ok := ctuo.mutation.ReferenceID(); ok {
		_spec.SetField(credittransaction.FieldReferenceID, field.TypeString, value)
	}
	if ctuo.mutation.ReferenceIDCleared() {
		_spec.ClearField(credittransaction.FieldReferenceID, field.TypeString)
	}
	if value, ok := ctuo.mutation.ReferenceType(); ok {
		_spec.SetField(credittransaction.FieldReferenceType, field.TypeString, value)
	}
	if ctuo.mutation.ReferenceTypeCleared() {
		_spec.ClearField(credittransaction.FieldReferenceType, field.TypeString)
	}
	if value, ok := ctuo.mutation.PaymentID(); ok {
		_spec.SetField(credittransaction.FieldPaymentID, field.TypeString, value)
	}
	if ctuo.mutation.PaymentIDCleared() {
		_spec.ClearField(credittransaction.FieldPaymentID, field.TypeString)
	}
	if value, ok := ctuo.mutation.InvoiceID(); ok {
		_spec.SetField(credittransaction.FieldInvoiceID, field.TypeString, value)
	}
	if ctuo.mutation.InvoiceIDCleared() {
		_spec.ClearField(credittransaction.FieldInvoiceID, field.TypeString)
	}
	if value, ok := ctuo.mutation.Status(); ok {
		_spec.SetField(credittransaction.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ctuo.mutation.Metadata(); ok {
		_spec.SetField(credittransaction.FieldMetadata, field.TypeJSON, value)
	}
	if ctuo.mutation.MetadataCleared() {
		_spec.ClearField(credittransaction.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ctuo.mutation.ProcessedAt(); ok {
		_spec.SetField(credittransaction.FieldProcessedAt, field.TypeTime, value)
	}
	if ctuo.mutation.ProcessedAtCleared() {
		_spec.ClearField(credittransaction.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ctuo.mutation.UpdatedAt(); ok {
		_spec.SetField(credittransaction.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &CreditTransaction{config: ctuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ctuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{credittransaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ctuo.mutation.done = true
	return _node, nil
}

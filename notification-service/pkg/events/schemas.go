package events

// TODO: Fix all type redeclaration and undefined type errors
// This file is temporarily commented out to resolve compilation issues

/*
// KafkaMessage represents the wrapper for all Kafka messages
type KafkaMessage struct {
	Topic     string            `json:"topic" validate:"required"`
	Key       string            `json:"key" validate:"required"`
	Value     json.RawMessage   `json:"value" validate:"required"`
	Headers   map[string]string `json:"headers,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
	Partition int32             `json:"partition,omitempty"`
	Offset    int64             `json:"offset,omitempty"`
}

// NotificationEventMessage represents the standardized message format for notification.events topic
type NotificationEventMessage struct {
	MessageID   string                 `json:"message_id" validate:"required,uuid"`
	EventType   EventType              `json:"event_type" validate:"required"`
	UserID      string                 `json:"user_id" validate:"required"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Priority    NotificationPriority   `json:"priority" validate:"required,oneof=low normal high critical"`
	Channels    []NotificationChannel  `json:"channels" validate:"required,min=1,dive,oneof=email sms push websocket in_app"`
	EventData   map[string]interface{} `json:"event_data" validate:"required"`
	Metadata    EventMetadata          `json:"metadata" validate:"required"`
}



// UserEventMessage represents messages from user.events topic
type UserEventMessage struct {
	MessageID string                 `json:"message_id" validate:"required,uuid"`
	EventType string                 `json:"event_type" validate:"required"`
	UserID    string                 `json:"user_id" validate:"required"`
	EventData map[string]interface{} `json:"event_data" validate:"required"`
	Metadata  EventMetadata          `json:"metadata" validate:"required"`
}

// ContentEventMessage represents messages from content.events topic
type ContentEventMessage struct {
	MessageID   string                 `json:"message_id" validate:"required,uuid"`
	EventType   string                 `json:"event_type" validate:"required"`
	UserID      string                 `json:"user_id" validate:"required"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	ContentID   string                 `json:"content_id,omitempty"`
	EventData   map[string]interface{} `json:"event_data" validate:"required"`
	Metadata    EventMetadata          `json:"metadata" validate:"required"`
}

// BillingEventMessage represents messages from billing.events topic
type BillingEventMessage struct {
	MessageID      string                 `json:"message_id" validate:"required,uuid"`
	EventType      string                 `json:"event_type" validate:"required"`
	UserID         string                 `json:"user_id" validate:"required"`
	SubscriptionID string                 `json:"subscription_id,omitempty"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	EventData      map[string]interface{} `json:"event_data" validate:"required"`
	Metadata       EventMetadata          `json:"metadata" validate:"required"`
}

// EventValidator provides validation for event messages
type EventValidator struct {
	validator *validator.Validate
}

// NewEventValidator creates a new event validator
func NewEventValidator() *EventValidator {
	v := validator.New()

	// Register custom validations
	v.RegisterValidation("event_type", validateEventType)
	v.RegisterValidation("notification_priority", validateNotificationPriority)
	v.RegisterValidation("notification_channel", validateNotificationChannel)

	return &EventValidator{
		validator: v,
	}
}

// ValidateNotificationEvent validates a notification event message
func (ev *EventValidator) ValidateNotificationEvent(msg *NotificationEventMessage) error {
	if err := ev.validator.Struct(msg); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Additional business logic validations
	if err := ev.validateEventData(msg.EventType, msg.EventData); err != nil {
		return fmt.Errorf("event data validation failed: %w", err)
	}

	return nil
}

// ValidateUserEvent validates a user event message
func (ev *EventValidator) ValidateUserEvent(msg *UserEventMessage) error {
	return ev.validator.Struct(msg)
}

// ValidateContentEvent validates a content event message
func (ev *EventValidator) ValidateContentEvent(msg *ContentEventMessage) error {
	return ev.validator.Struct(msg)
}

// ValidateBillingEvent validates a billing event message
func (ev *EventValidator) ValidateBillingEvent(msg *BillingEventMessage) error {
	return ev.validator.Struct(msg)
}

// validateEventData validates event-specific data based on event type
func (ev *EventValidator) validateEventData(eventType EventType, data map[string]interface{}) error {
	switch eventType {
	case EventTypeUserWelcome:
		return ev.validateUserWelcomeData(data)
	case EventTypeContentGenerated:
		return ev.validateContentGeneratedData(data)
	case EventTypePostPublished:
		return ev.validatePostPublishedData(data)
	case EventTypeCreditLow:
		return ev.validateCreditLowData(data)
	case EventTypeSubscriptionExpired:
		return ev.validateSubscriptionExpiredData(data)
	case EventTypeIntegrationConnected:
		return ev.validateIntegrationConnectedData(data)
	case EventTypeSystemMaintenance:
		return ev.validateSystemMaintenanceData(data)
	default:
		// For unknown event types, just check that data is not empty
		if len(data) == 0 {
			return fmt.Errorf("event data cannot be empty for event type: %s", eventType)
		}
	}
	return nil
}

// validateUserWelcomeData validates user welcome event data
func (ev *EventValidator) validateUserWelcomeData(data map[string]interface{}) error {
	requiredFields := []string{"user_email", "user_first_name"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateContentGeneratedData validates content generated event data
func (ev *EventValidator) validateContentGeneratedData(data map[string]interface{}) error {
	requiredFields := []string{"content_id", "content_type", "content_title"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validatePostPublishedData validates post published event data
func (ev *EventValidator) validatePostPublishedData(data map[string]interface{}) error {
	requiredFields := []string{"post_id", "post_title", "platform"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateCreditLowData validates credit low event data
func (ev *EventValidator) validateCreditLowData(data map[string]interface{}) error {
	requiredFields := []string{"current_credits", "threshold_credits"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateSubscriptionExpiredData validates subscription expired event data
func (ev *EventValidator) validateSubscriptionExpiredData(data map[string]interface{}) error {
	requiredFields := []string{"subscription_id", "plan_name", "expiration_date"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateIntegrationConnectedData validates integration connected event data
func (ev *EventValidator) validateIntegrationConnectedData(data map[string]interface{}) error {
	requiredFields := []string{"integration_id", "integration_type", "platform_name"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// validateSystemMaintenanceData validates system maintenance event data
func (ev *EventValidator) validateSystemMaintenanceData(data map[string]interface{}) error {
	requiredFields := []string{"title", "description", "start_time", "end_time"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

// Custom validation functions
func validateEventType(fl validator.FieldLevel) bool {
	eventType := EventType(fl.Field().String())
	validTypes := []EventType{
		EventTypeUserWelcome, EventTypeUserUpgraded, EventTypeUserProfileUpdated,
		EventTypeUserPasswordReset, EventTypeUserEmailVerified, EventTypeUserSecurityAlert,
		EventTypeContentGenerated, EventTypeContentImproved, EventTypePostPublished,
		EventTypePostScheduled, EventTypePostFailed, EventTypeTemplateCreated, EventTypeTemplateUpdated,
		EventTypeCreditLow, EventTypeCreditPurchased, EventTypeSubscriptionRenewed,
		EventTypeSubscriptionExpired, EventTypePaymentFailed, EventTypePaymentSucceeded,
		EventTypeIntegrationConnected, EventTypeIntegrationFailed, EventTypeIntegrationDisconnected,
		EventTypeAnalyticsReport, EventTypeAnalyticsInsight,
		EventTypeSystemMaintenance, EventTypeSystemUpdate, EventTypeSystemAlert,
	}

	for _, validType := range validTypes {
		if eventType == validType {
			return true
		}
	}
	return false
}

func validateNotificationPriority(fl validator.FieldLevel) bool {
	priority := NotificationPriority(fl.Field().String())
	return priority == PriorityLow || priority == PriorityNormal ||
		priority == PriorityHigh || priority == PriorityCritical
}

func validateNotificationChannel(fl validator.FieldLevel) bool {
	channel := NotificationChannel(fl.Field().String())
	return channel == ChannelEmail || channel == ChannelSMS ||
		channel == ChannelPush || channel == ChannelWebSocket || channel == ChannelInApp
}

// MessageBuilder helps build standardized Kafka messages
type MessageBuilder struct {
	validator *EventValidator
}

// NewMessageBuilder creates a new message builder
func NewMessageBuilder() *MessageBuilder {
	return &MessageBuilder{
		validator: NewEventValidator(),
	}
}

// BuildNotificationEventMessage builds a notification event message from a base event
func (mb *MessageBuilder) BuildNotificationEventMessage(event *BaseEvent) (*NotificationEventMessage, error) {
	msg := &NotificationEventMessage{
		MessageID:   event.ID,
		EventType:   event.Type,
		UserID:      event.UserID,
		WorkspaceID: event.WorkspaceID,
		Priority:    event.Priority,
		Channels:    event.Channels,
		EventData:   event.Data,
		Timestamp:   event.Timestamp,
	}

	if err := mb.validator.ValidateNotificationEvent(msg); err != nil {
		return nil, fmt.Errorf("failed to validate notification event message: %w", err)
	}

	return msg, nil
}

// BuildKafkaMessage builds a Kafka message from a notification event message
func (mb *MessageBuilder) BuildKafkaMessage(topic string, msg *NotificationEventMessage) (*KafkaMessage, error) {
	value, err := json.Marshal(msg)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message: %w", err)
	}

	return &KafkaMessage{
		Topic: topic,
		Key:   msg.UserID, // Use user ID as partition key for ordering
		Value: value,
		Headers: map[string]string{
			"event_type": string(msg.EventType),
			"source":     msg.Source,
			"version":    msg.Version,
		},
		Timestamp: msg.Timestamp,
	}, nil
}
*/

// TODO: EventMetadata is already defined in pkg-shared
// EventMetadata contains metadata about the event
// // type EventMetadata struct {
// 	Source        string            `json:"source" validate:"required"`
// 	Version       string            `json:"version" validate:"required"`
// 	Timestamp     time.Time         `json:"timestamp" validate:"required"`
// 	CorrelationID string            `json:"correlation_id,omitempty"`
// 	TraceID       string            `json:"trace_id,omitempty"`
// 	RetryCount    int               `json:"retry_count,omitempty"`
// 	Tags          map[string]string `json:"tags,omitempty"`
// }

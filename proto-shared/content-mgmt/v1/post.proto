syntax = "proto3";

package content_mgmt.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";
import "common/v1/timestamp.proto";

option go_package = "github.com/social-content-ai/proto-shared/content-mgmt/v1;contentmgmtv1";

// Post represents a social media post
message Post {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;
  string title = 4;
  string content = 5;
  string type = 6;           // manual, ai-generated
  string status = 7;         // draft, scheduled, published, failed
  google.protobuf.Timestamp scheduled_at = 8;
  repeated string platforms = 9;
  repeated string hashtags = 10;
  repeated string images = 11;
  string emoji = 12;
  repeated string tags = 13;
  bool is_immediate = 14;
  repeated string platform_accounts = 15;
  string ai_model_used = 16;
  string ai_generation_id = 17;
  string template_id = 18;
  common.v1.TimestampInfo timestamps = 19;
}

// CreatePostRequest for creating a new post
message CreatePostRequest {
  string user_id = 1;
  string workspace_id = 2;
  string title = 3;
  string content = 4;
  string type = 5;
  repeated string platforms = 6;
  repeated string hashtags = 7;
  repeated string images = 8;
  string emoji = 9;
  repeated string tags = 10;
  bool is_immediate = 11;
  google.protobuf.Timestamp scheduled_at = 12;
  repeated string platform_accounts = 13;
  string template_id = 14;
}

// UpdatePostRequest for updating a post
message UpdatePostRequest {
  string id = 1;
  optional string title = 2;
  optional string content = 3;
  repeated string platforms = 4;
  repeated string hashtags = 5;
  repeated string images = 6;
  optional string emoji = 7;
  repeated string tags = 8;
  optional google.protobuf.Timestamp scheduled_at = 9;
}

// GetPostRequest for retrieving a post
message GetPostRequest {
  string id = 1;
}

// ListPostsRequest for listing posts with filters
message ListPostsRequest {
  string user_id = 1;
  string workspace_id = 2;
  common.v1.PaginationRequest pagination = 3;
  string status = 4;         // Filter by status
  string type = 5;           // Filter by type
  repeated string platforms = 6; // Filter by platforms
  string date_from = 7;      // Format: YYYY-MM-DD
  string date_to = 8;        // Format: YYYY-MM-DD
}

// ListPostsResponse with paginated posts
message ListPostsResponse {
  repeated Post posts = 1;
  common.v1.PaginationResponse pagination = 2;
}

// DeletePostRequest for deleting a post
message DeletePostRequest {
  string id = 1;
}

// PublishPostRequest for publishing a post immediately
message PublishPostRequest {
  string id = 1;
  repeated string platforms = 2; // Specific platforms to publish to
}

// SchedulePostRequest for scheduling a post
message SchedulePostRequest {
  string id = 1;
  google.protobuf.Timestamp scheduled_at = 2;
  repeated string platforms = 3;
}

// Empty response
message Empty {}

// PostService provides post management operations
service PostService {
  rpc CreatePost(CreatePostRequest) returns (Post);
  rpc GetPost(GetPostRequest) returns (Post);
  rpc UpdatePost(UpdatePostRequest) returns (Post);
  rpc ListPosts(ListPostsRequest) returns (ListPostsResponse);
  rpc DeletePost(DeletePostRequest) returns (Empty);
  rpc PublishPost(PublishPostRequest) returns (Post);
  rpc SchedulePost(SchedulePostRequest) returns (Post);
}

package models

import "time"

// TemplateResponse represents a template response
type TemplateResponse struct {
	BaseModel
	UserID               string                 `json:"user_id"`
	Name                 string                 `json:"name"`
	Description          string                 `json:"description"`
	Content              string                 `json:"content"`
	Category             string                 `json:"category"`
	Platforms            []string               `json:"platforms"`
	Prompt               string                 `json:"prompt,omitempty"`
	IsPublic             bool                   `json:"is_public"`
	Type                 string                 `json:"type"` // my, marketplace, premium
	ThumbnailURL         string                 `json:"thumbnail_url,omitempty"`
	RAGFiles             []string               `json:"rag_files,omitempty"`
	VariablePlaceholders []string               `json:"variable_placeholders,omitempty"`
	IsVerified           bool                   `json:"is_verified"`
	IsFeatured           bool                   `json:"is_featured"`
	IsPremium            bool                   `json:"is_premium"`
	CreditCost           int                    `json:"credit_cost"`
	Rating               float64                `json:"rating"`
	RatingCount          int                    `json:"rating_count"`
	DownloadCount        int                    `json:"download_count"`
	UsageCount           int                    `json:"usage_count"`
	Tags                 []string               `json:"tags,omitempty"`
	Author               *TemplateAuthor        `json:"author,omitempty"`
	Analytics            *TemplateAnalytics     `json:"analytics,omitempty"`
	IsFavorited          bool                   `json:"is_favorited,omitempty"`
	LastUsed             *time.Time             `json:"last_used,omitempty"`
}

// TemplateAuthor represents template author information
type TemplateAuthor struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Avatar   string `json:"avatar,omitempty"`
	Verified bool   `json:"verified"`
}

// TemplateAnalytics represents template analytics
type TemplateAnalytics struct {
	Views         int64   `json:"views"`
	Downloads     int64   `json:"downloads"`
	Uses          int64   `json:"uses"`
	Revenue       float64 `json:"revenue,omitempty"`
	ConversionRate float64 `json:"conversion_rate"`
}

// CreateTemplateRequest represents create template request
type CreateTemplateRequest struct {
	UserID               string   `json:"user_id"`
	Name                 string   `json:"name" validate:"required,min=1,max=100"`
	Description          string   `json:"description" validate:"required,min=1,max=500"`
	Content              string   `json:"content" validate:"required,min=1,max=10000"`
	Category             string   `json:"category" validate:"required"`
	Platforms            []string `json:"platforms" validate:"required,min=1"`
	Prompt               string   `json:"prompt,omitempty"`
	IsPublic             bool     `json:"is_public"`
	ThumbnailURL         string   `json:"thumbnail_url,omitempty"`
	RAGFiles             []string `json:"rag_files,omitempty"`
	VariablePlaceholders []string `json:"variable_placeholders,omitempty"`
	IsPremium            bool     `json:"is_premium"`
	CreditCost           int      `json:"credit_cost,omitempty"`
	Tags                 []string `json:"tags,omitempty"`
}

// UpdateTemplateRequest represents update template request
type UpdateTemplateRequest struct {
	ID                   string   `json:"id"`
	UserID               string   `json:"user_id"`
	Name                 string   `json:"name,omitempty"`
	Description          string   `json:"description,omitempty"`
	Content              string   `json:"content,omitempty"`
	Category             string   `json:"category,omitempty"`
	Platforms            []string `json:"platforms,omitempty"`
	Prompt               string   `json:"prompt,omitempty"`
	IsPublic             bool     `json:"is_public,omitempty"`
	ThumbnailURL         string   `json:"thumbnail_url,omitempty"`
	RAGFiles             []string `json:"rag_files,omitempty"`
	VariablePlaceholders []string `json:"variable_placeholders,omitempty"`
	IsPremium            bool     `json:"is_premium,omitempty"`
	CreditCost           int      `json:"credit_cost,omitempty"`
	Tags                 []string `json:"tags,omitempty"`
}

// ListTemplatesRequest represents list templates request
type ListTemplatesRequest struct {
	UserID     string `json:"user_id"`
	Page       int    `json:"page" validate:"min=1"`
	Limit      int    `json:"limit" validate:"min=1,max=100"`
	Category   string `json:"category,omitempty"`
	Type       string `json:"type,omitempty"` // my, marketplace, premium, favorites
	Platform   string `json:"platform,omitempty"`
	Search     string `json:"search,omitempty"`
	SortBy     string `json:"sort_by,omitempty"` // name, created_at, rating, downloads, uses
	SortOrder  string `json:"sort_order,omitempty"` // asc, desc
	IsPremium  *bool  `json:"is_premium,omitempty"`
	IsVerified *bool  `json:"is_verified,omitempty"`
	IsFeatured *bool  `json:"is_featured,omitempty"`
	MinRating  float64 `json:"min_rating,omitempty"`
	MaxCost    int    `json:"max_cost,omitempty"`
}

// ListTemplatesResponse represents list templates response
type ListTemplatesResponse struct {
	Templates  []TemplateResponse `json:"templates"`
	Pagination PaginationMeta     `json:"pagination"`
	Filters    TemplateFilters    `json:"filters"`
}

// TemplateFilters represents available template filters
type TemplateFilters struct {
	Categories []string `json:"categories"`
	Platforms  []string `json:"platforms"`
	Tags       []string `json:"tags"`
	PriceRange struct {
		Min int `json:"min"`
		Max int `json:"max"`
	} `json:"price_range"`
}

// UseTemplateRequest represents use template request
type UseTemplateRequest struct {
	TemplateID string                 `json:"template_id" validate:"required"`
	UserID     string                 `json:"user_id"`
	Variables  map[string]interface{} `json:"variables,omitempty"`
	Platform   string                 `json:"platform,omitempty"`
}

// UseTemplateResponse represents use template response
type UseTemplateResponse struct {
	TemplateID      string `json:"template_id"`
	GeneratedContent string `json:"generated_content"`
	CreditCost      int    `json:"credit_cost"`
	UsageID         string `json:"usage_id"`
}

// RateTemplateRequest represents rate template request
type RateTemplateRequest struct {
	TemplateID string `json:"template_id" validate:"required"`
	UserID     string `json:"user_id"`
	Rating     int    `json:"rating" validate:"required,min=1,max=5"`
	Review     string `json:"review,omitempty"`
}

// FavoriteTemplateRequest represents favorite template request
type FavoriteTemplateRequest struct {
	TemplateID string `json:"template_id" validate:"required"`
	UserID     string `json:"user_id"`
}

// SearchTemplatesRequest represents search templates request
type SearchTemplatesRequest struct {
	UserID    string   `json:"user_id"`
	Query     string   `json:"query" validate:"required,min=1"`
	Category  string   `json:"category,omitempty"`
	Platforms []string `json:"platforms,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	Page      int      `json:"page" validate:"min=1"`
	Limit     int      `json:"limit" validate:"min=1,max=100"`
	SortBy    string   `json:"sort_by,omitempty"`
	SortOrder string   `json:"sort_order,omitempty"`
}

// SearchTemplatesResponse represents search templates response
type SearchTemplatesResponse struct {
	Query       string             `json:"query"`
	Templates   []TemplateResponse `json:"templates"`
	Pagination  PaginationMeta     `json:"pagination"`
	Suggestions []string           `json:"suggestions,omitempty"`
	RelatedTags []string           `json:"related_tags,omitempty"`
}

// TemplateCategoriesResponse represents template categories response
type TemplateCategoriesResponse struct {
	Categories []TemplateCategory `json:"categories"`
}

// TemplateCategory represents a template category
type TemplateCategory struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Icon        string `json:"icon,omitempty"`
	Count       int    `json:"count"`
	Featured    bool   `json:"featured"`
}

// TemplateAnalyticsRequest represents template analytics request
type TemplateAnalyticsRequest struct {
	TemplateID string `json:"template_id"`
	UserID     string `json:"user_id"`
	Period     string `json:"period,omitempty"` // day, week, month, year
	DateFrom   string `json:"date_from,omitempty"`
	DateTo     string `json:"date_to,omitempty"`
}

// TemplateAnalyticsResponse represents template analytics response
type TemplateAnalyticsResponse struct {
	TemplateID  string                      `json:"template_id"`
	Period      string                      `json:"period"`
	Overview    TemplateAnalytics           `json:"overview"`
	Timeline    []TemplateTimelineAnalytics `json:"timeline"`
	TopUsers    []TemplateUserAnalytics     `json:"top_users"`
	Platforms   map[string]int              `json:"platforms"`
	Revenue     TemplateRevenueAnalytics    `json:"revenue,omitempty"`
}

// TemplateTimelineAnalytics represents template analytics over time
type TemplateTimelineAnalytics struct {
	Date      string            `json:"date"`
	Views     int64             `json:"views"`
	Downloads int64             `json:"downloads"`
	Uses      int64             `json:"uses"`
	Revenue   float64           `json:"revenue,omitempty"`
}

// TemplateUserAnalytics represents template user analytics
type TemplateUserAnalytics struct {
	UserID    string `json:"user_id"`
	UserName  string `json:"user_name"`
	Uses      int64  `json:"uses"`
	LastUsed  time.Time `json:"last_used"`
}

// TemplateRevenueAnalytics represents template revenue analytics
type TemplateRevenueAnalytics struct {
	TotalRevenue    float64 `json:"total_revenue"`
	MonthlyRevenue  float64 `json:"monthly_revenue"`
	AveragePerUse   float64 `json:"average_per_use"`
	PayoutEligible  float64 `json:"payout_eligible"`
	NextPayoutDate  string  `json:"next_payout_date,omitempty"`
}

// TemplateUsageStatsRequest represents template usage stats request
type TemplateUsageStatsRequest struct {
	TemplateID string `json:"template_id"`
	UserID     string `json:"user_id"`
	Period     string `json:"period,omitempty"`
}

// TemplateUsageStatsResponse represents template usage stats response
type TemplateUsageStatsResponse struct {
	TemplateID    string                    `json:"template_id"`
	TotalUses     int64                     `json:"total_uses"`
	UniqueUsers   int64                     `json:"unique_users"`
	SuccessRate   float64                   `json:"success_rate"`
	AverageRating float64                   `json:"average_rating"`
	RecentUses    []TemplateUsage           `json:"recent_uses"`
	PopularTimes  []TemplatePopularTime     `json:"popular_times"`
}

// TemplateUsage represents a template usage
type TemplateUsage struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	UserName  string    `json:"user_name"`
	Platform  string    `json:"platform"`
	Success   bool      `json:"success"`
	CreatedAt time.Time `json:"created_at"`
}

// TemplatePopularTime represents popular usage times
type TemplatePopularTime struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}

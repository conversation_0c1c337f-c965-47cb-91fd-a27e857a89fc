package cleanup

import (
	"context"
	"fmt"
	"time"

	"github.com/social-content-ai/pkg-shared/kafka"
	"github.com/social-content-ai/pkg-shared/logging"
	"github.com/social-content-ai/user-service/ent"
	"github.com/social-content-ai/user-service/ent/user"
)

// Service handles cleanup operations for unverified users
type Service struct {
	readDB        *ent.Client
	writeDB       *ent.Client
	kafkaProducer *kafka.Producer
	logger        logging.Logger
	config        *Config
}

// Config holds cleanup service configuration
type Config struct {
	UnverifiedUserTTL    time.Duration // Time to live for unverified users (default: 24h)
	CleanupInterval      time.Duration // How often to run cleanup (default: 1h)
	BatchSize           int           // Number of users to process in one batch (default: 100)
	EnableNotifications bool          // Whether to send notifications before deletion
	WarningTime         time.Duration // Time before deletion to send warning (default: 2h)
}

// DefaultConfig returns default cleanup configuration
func DefaultConfig() *Config {
	return &Config{
		UnverifiedUserTTL:    24 * time.Hour,
		CleanupInterval:      1 * time.Hour,
		BatchSize:           100,
		EnableNotifications: true,
		WarningTime:         2 * time.Hour,
	}
}

// NewService creates a new cleanup service
func NewService(
	readDB, writeDB *ent.Client,
	kafkaProducer *kafka.Producer,
	logger logging.Logger,
	config *Config,
) *Service {
	if config == nil {
		config = DefaultConfig()
	}

	return &Service{
		readDB:        readDB,
		writeDB:       writeDB,
		kafkaProducer: kafkaProducer,
		logger:        logger,
		config:        config,
	}
}

// Start starts the cleanup service background worker
func (s *Service) Start(ctx context.Context) error {
	s.logger.WithFields(map[string]interface{}{
		"unverified_ttl":    s.config.UnverifiedUserTTL,
		"cleanup_interval":  s.config.CleanupInterval,
		"batch_size":        s.config.BatchSize,
		"warning_time":      s.config.WarningTime,
	}).Info("Starting user cleanup service")

	ticker := time.NewTicker(s.config.CleanupInterval)
	defer ticker.Stop()

	// Run initial cleanup
	if err := s.runCleanup(ctx); err != nil {
		s.logger.WithError(err).Error("Initial cleanup failed")
	}

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Cleanup service stopped")
			return ctx.Err()
		case <-ticker.C:
			if err := s.runCleanup(ctx); err != nil {
				s.logger.WithError(err).Error("Cleanup cycle failed")
			}
		}
	}
}

// runCleanup performs the cleanup operation
func (s *Service) runCleanup(ctx context.Context) error {
	s.logger.Debug("Starting cleanup cycle")

	// Send warnings first
	if s.config.EnableNotifications {
		if err := s.sendWarnings(ctx); err != nil {
			s.logger.WithError(err).Error("Failed to send warnings")
		}
	}

	// Delete expired unverified users
	deletedCount, err := s.deleteExpiredUsers(ctx)
	if err != nil {
		return fmt.Errorf("failed to delete expired users: %w", err)
	}

	if deletedCount > 0 {
		s.logger.WithField("deleted_count", deletedCount).Info("Cleanup cycle completed")
	} else {
		s.logger.Debug("Cleanup cycle completed - no users to delete")
	}

	return nil
}

// sendWarnings sends warning notifications to users who will be deleted soon
func (s *Service) sendWarnings(ctx context.Context) error {
	warningCutoff := time.Now().Add(-s.config.UnverifiedUserTTL + s.config.WarningTime)

	users, err := s.readDB.User.Query().
		Where(
			user.IsVerified(false),
			user.CreatedAtLT(warningCutoff),
			user.DeletedAtIsNil(),
		).
		Limit(s.config.BatchSize).
		All(ctx)
	if err != nil {
		return fmt.Errorf("failed to query users for warnings: %w", err)
	}

	for _, userEntity := range users {
		// Check if we already sent a warning (using notification_settings as flag)
		if userEntity.NotificationSettings != nil {
			if warned, exists := userEntity.NotificationSettings["deletion_warning_sent"]; exists && warned == true {
				continue // Already warned
			}
		}

		// Send warning event
		if s.kafkaProducer != nil {
			userEvent := kafka.NewUserEvent("user.deletion_warning", userEntity.ID.String())
			userEvent.BaseEvent.WithData("email", userEntity.Email).
				WithData("full_name", userEntity.FullName).
				WithData("deletion_time", time.Now().Add(s.config.WarningTime).Format(time.RFC3339)).
				WithData("verification_url", s.buildVerificationURL(userEntity.Email))

			userEvent.Email = userEntity.Email

			err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
			if err != nil {
				s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to publish deletion warning event")
				continue
			}
		}

		// Mark as warned
		settings := userEntity.NotificationSettings
		if settings == nil {
			settings = make(map[string]interface{})
		}
		settings["deletion_warning_sent"] = true
		settings["warning_sent_at"] = time.Now().Format(time.RFC3339)

		err = s.writeDB.User.UpdateOneID(userEntity.ID).
			SetNotificationSettings(settings).
			Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to mark user as warned")
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id": userEntity.ID,
			"email":   userEntity.Email,
		}).Info("Deletion warning sent")
	}

	return nil
}

// deleteExpiredUsers deletes users who have exceeded the TTL without verification
func (s *Service) deleteExpiredUsers(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-s.config.UnverifiedUserTTL)

	// Find expired unverified users
	users, err := s.readDB.User.Query().
		Where(
			user.IsVerified(false),
			user.CreatedAtLT(cutoffTime),
			user.DeletedAtIsNil(),
		).
		Limit(s.config.BatchSize).
		All(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to query expired users: %w", err)
	}

	if len(users) == 0 {
		return 0, nil
	}

	deletedCount := 0
	for _, userEntity := range users {
		// Soft delete the user
		err = s.writeDB.User.UpdateOneID(userEntity.ID).
			SetDeletedAt(time.Now()).
			Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to delete expired user")
			continue
		}

		// Send deletion event
		if s.kafkaProducer != nil {
			userEvent := kafka.NewUserEvent("user.deleted_unverified", userEntity.ID.String())
			userEvent.BaseEvent.WithData("email", userEntity.Email).
				WithData("full_name", userEntity.FullName).
				WithData("created_at", userEntity.CreatedAt.Format(time.RFC3339)).
				WithData("reason", "email_not_verified").
				WithData("ttl_hours", fmt.Sprintf("%.0f", s.config.UnverifiedUserTTL.Hours()))

			userEvent.Email = userEntity.Email

			err = s.kafkaProducer.PublishUserEvent(ctx, userEvent)
			if err != nil {
				s.logger.WithError(err).WithField("user_id", userEntity.ID).Error("Failed to publish user deletion event")
			}
		}

		s.logger.WithFields(map[string]interface{}{
			"user_id":    userEntity.ID,
			"email":      userEntity.Email,
			"created_at": userEntity.CreatedAt,
			"age_hours":  time.Since(userEntity.CreatedAt).Hours(),
		}).Info("Deleted unverified user")

		deletedCount++
	}

	return deletedCount, nil
}

// buildVerificationURL builds verification URL for warning emails
func (s *Service) buildVerificationURL(email string) string {
	// This should be configurable based on environment
	baseURL := "https://app.social-content-ai.com" // TODO: Make configurable
	return fmt.Sprintf("%s/auth/resend-verification?email=%s", baseURL, email)
}

// GetStats returns cleanup service statistics
func (s *Service) GetStats(ctx context.Context) (*Stats, error) {
	// Count unverified users by age
	now := time.Now()
	cutoffTime := now.Add(-s.config.UnverifiedUserTTL)
	warningTime := now.Add(-s.config.UnverifiedUserTTL + s.config.WarningTime)

	totalUnverified, err := s.readDB.User.Query().
		Where(
			user.IsVerified(false),
			user.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count unverified users: %w", err)
	}

	expiredCount, err := s.readDB.User.Query().
		Where(
			user.IsVerified(false),
			user.CreatedAtLT(cutoffTime),
			user.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count expired users: %w", err)
	}

	warningCount, err := s.readDB.User.Query().
		Where(
			user.IsVerified(false),
			user.CreatedAtLT(warningTime),
			user.CreatedAtGTE(cutoffTime),
			user.DeletedAtIsNil(),
		).
		Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count users in warning period: %w", err)
	}

	return &Stats{
		TotalUnverified:    totalUnverified,
		ExpiredCount:       expiredCount,
		WarningCount:       warningCount,
		NextCleanupIn:      s.config.CleanupInterval,
		UnverifiedUserTTL:  s.config.UnverifiedUserTTL,
	}, nil
}

// Stats represents cleanup service statistics
type Stats struct {
	TotalUnverified   int           `json:"total_unverified"`
	ExpiredCount      int           `json:"expired_count"`
	WarningCount      int           `json:"warning_count"`
	NextCleanupIn     time.Duration `json:"next_cleanup_in"`
	UnverifiedUserTTL time.Duration `json:"unverified_user_ttl"`
}

// Stop gracefully stops the cleanup service
func (s *Service) Stop() {
	s.logger.Info("Cleanup service stopping...")
}

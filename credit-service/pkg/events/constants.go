package events

// Event type constants - Unified naming
const (
	// Credit events
	EventTypeCreditAdded          = "credit.added"
	EventTypeCreditDeducted       = "credit.deducted"
	EventTypeCreditTransferred    = "credit.transferred"
	EventTypeCreditBalanceUpdated = "credit.balance_updated"

	// Subscription events - Unified with subscription_events.go
	// Note: These are defined in subscription_events.go to avoid duplication
	// EventTypePaymentCompleted      = "subscription.payment.completed"
	// EventTypePaymentFailed         = "subscription.payment.failed"
	// EventTypeSubscriptionActivated = "subscription.activated"
	// EventTypeSubscriptionCancelled = "subscription.cancelled"

	// Legacy subscription events (deprecated)
	EventTypeSubscriptionRenewed    = "subscription.renewed"
	EventTypeSubscriptionExpired    = "subscription.expired"
	EventTypeSubscriptionUpgraded   = "subscription.upgraded"
	EventTypeSubscriptionDowngraded = "subscription.downgraded"

	// Legacy payment events (deprecated - use subscription events instead)
	EventTypePaymentSucceeded = "payment.succeeded"
	EventTypePaymentRefunded  = "payment.refunded"

	// Notification events
	EventTypeNotificationSend = "notification.send"
)

// Event source constants
const (
	SourceCreditService       = "credit-service"
	SourcePaymentService      = "payment-service"
	SourceSubscriptionService = "subscription-service"
)

// Event version
const (
	EventVersion = "1.0"
)

// Kafka Topic Constants
const (
	TopicSubscriptionEvents           = "subscription.events"
	TopicSubscriptionPaymentCompleted = "subscription.payment.completed"
	TopicSubscriptionPaymentFailed    = "subscription.payment.failed"
	TopicSubscriptionActivated        = "subscription.activated"
	TopicSubscriptionCancelled        = "subscription.cancelled"
)

package grpc

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/subscription"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	"github.com/social-content-ai/pkg-shared/logging"
	commonv1 "github.com/social-content-ai/proto-shared/common/v1"
	creditv1 "github.com/social-content-ai/proto-shared/credit/v1"
)

// CreditServiceHandler implements the CreditService gRPC interface
type CreditServiceHandler struct {
	creditv1.UnimplementedCreditServiceServer
	creditUseCase       credit.UseCase
	planUseCase         plan.UseCase
	subscriptionUseCase subscription.UseCase
	transactionUseCase  transaction.UseCase
	logger              logging.Logger
}

// Handlers contains all gRPC handlers
type Handlers struct {
	creditService *CreditServiceHandler
	logger        logging.Logger
}

// NewHandlers creates a new handlers instance
func NewHandlers(
	creditUseCase credit.UseCase,
	planUseCase plan.UseCase,
	subscriptionUseCase subscription.UseCase,
	transactionUseCase transaction.UseCase,
	logger logging.Logger,
) *Handlers {
	creditService := &CreditServiceHandler{
		creditUseCase:       creditUseCase,
		planUseCase:         planUseCase,
		subscriptionUseCase: subscriptionUseCase,
		transactionUseCase:  transactionUseCase,
		logger:              logger,
	}

	return &Handlers{
		creditService: creditService,
		logger:        logger,
	}
}

// RegisterServices registers all gRPC services
func (h *Handlers) RegisterServices(server *grpc.Server) {
	creditv1.RegisterCreditServiceServer(server, h.creditService)
	h.logger.Info("gRPC services registered successfully")
}

// GetUserCredit retrieves user credit information
func (h *CreditServiceHandler) GetUserCredit(ctx context.Context, req *creditv1.GetUserCreditRequest) (*creditv1.UserCredit, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting user credit")

	// Get credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user credit")
		return nil, status.Errorf(codes.Internal, "failed to get user credit: %v", err)
	}

	// Get user subscription for plan info
	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Warn("Failed to get user subscription")
		// Continue without subscription info
	}

	userCredit := &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}

	if subscription != nil {
		userCredit.PlanId = subscription.PlanID
		if !subscription.CurrentPeriodEnd.IsZero() {
			userCredit.RenewalDate = subscription.CurrentPeriodEnd.Format("2006-01-02")
		}
	}

	return userCredit, nil
}

// ValidateCredit checks if user has sufficient credits
func (h *CreditServiceHandler) ValidateCredit(ctx context.Context, req *creditv1.ValidateCreditRequest) (*creditv1.ValidateCreditResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":          req.UserId,
		"required_credits": req.RequiredCredits,
	}).Info("Validating credit")

	// Get current balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get credit balance for validation")
		return nil, status.Errorf(codes.Internal, "failed to get credit balance: %v", err)
	}

	hasSufficient := balance.CurrentCredits >= int(req.RequiredCredits)
	shortage := int32(0)
	if !hasSufficient {
		shortage = req.RequiredCredits - int32(balance.CurrentCredits)
	}

	return &creditv1.ValidateCreditResponse{
		HasSufficientCredits: hasSufficient,
		CurrentCredits:       int32(balance.CurrentCredits),
		RequiredCredits:      req.RequiredCredits,
		Shortage:             shortage,
	}, nil
}

// AddCredits adds credits to user account
func (h *CreditServiceHandler) AddCredits(ctx context.Context, req *creditv1.AddCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"amount":  req.Amount,
		"source":  req.Source,
	}).Info("Adding credits")

	// Convert proto request to internal model
	addReq := &models.AddCreditsRequest{
		UserID:      req.UserId,
		Amount:      int(req.Amount),
		Source:      req.Source,
		Description: req.Description,
		PaymentID:   req.PaymentId,
	}

	// Use transaction usecase for credit addition
	_, err := h.transactionUseCase.AddCreditsWithTransaction(ctx, addReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add credits")
		return nil, status.Errorf(codes.Internal, "failed to add credits: %v", err)
	}

	// Get updated credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get updated credit balance")
		return nil, status.Errorf(codes.Internal, "failed to get updated balance: %v", err)
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}, nil
}

// DeductCredits deducts credits from user account
func (h *CreditServiceHandler) DeductCredits(ctx context.Context, req *creditv1.DeductCreditsRequest) (*creditv1.UserCredit, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserId,
		"amount":       req.Amount,
		"purpose":      req.Purpose,
		"reference_id": req.ReferenceId,
	}).Info("Deducting credits")

	// Convert proto request to internal model
	deductReq := &models.DeductCreditsRequest{
		UserID:        req.UserId,
		Amount:        int(req.Amount),
		Purpose:       req.Purpose,
		Description:   req.Description,
		ReferenceID:   req.ReferenceId,
		ReferenceType: "grpc_request",
	}

	// Use transaction usecase for credit deduction
	_, err := h.transactionUseCase.DeductCreditsWithTransaction(ctx, deductReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to deduct credits")
		return nil, status.Errorf(codes.Internal, "failed to deduct credits: %v", err)
	}

	// Get updated credit balance
	balance, err := h.creditUseCase.GetCreditBalance(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get updated credit balance")
		return nil, status.Errorf(codes.Internal, "failed to get updated balance: %v", err)
	}

	return &creditv1.UserCredit{
		UserId:         req.UserId,
		CurrentCredits: int32(balance.CurrentCredits),
		TotalCredits:   int32(balance.TotalCredits),
	}, nil
}

// ListTransactions lists credit transactions for a user
func (h *CreditServiceHandler) ListTransactions(ctx context.Context, req *creditv1.ListTransactionsRequest) (*creditv1.ListTransactionsResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
	}).Info("Listing transactions")

	// Convert pagination
	page := int(req.Pagination.Page)
	limit := int(req.Pagination.Limit)
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	// Convert date filters
	var dateFrom, dateTo *time.Time
	if req.DateFrom != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			dateFrom = &parsed
		}
	}
	if req.DateTo != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			dateTo = &parsed
		}
	}

	// Create internal request
	historyReq := &models.CreditHistoryRequest{
		UserID:    req.UserId,
		Type:      req.Type,
		DateFrom:  dateFrom,
		DateTo:    dateTo,
		Page:      page,
		Limit:     limit,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// Get transactions
	history, err := h.transactionUseCase.ListTransactions(ctx, historyReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list transactions")
		return nil, status.Errorf(codes.Internal, "failed to list transactions: %v", err)
	}

	// Convert transactions to proto format
	var protoTransactions []*creditv1.CreditTransaction
	for _, tx := range history.Transactions {
		protoTx := &creditv1.CreditTransaction{
			Id:          tx.ID,
			UserId:      tx.UserID,
			Type:        tx.Type,
			Amount:      int32(tx.Amount),
			Description: tx.Description,
			ReferenceId: tx.ReferenceID,
			CreatedAt:   timestamppb.New(tx.CreatedAt),
		}
		protoTransactions = append(protoTransactions, protoTx)
	}

	// Convert pagination
	protoPagination := &commonv1.PaginationResponse{
		Page:       int32(history.Pagination.Page),
		Limit:      int32(history.Pagination.Limit),
		TotalItems: int32(history.Pagination.Total),
		TotalPages: int32(history.Pagination.TotalPages),
		HasNext:    history.Pagination.Page < history.Pagination.TotalPages,
		HasPrev:    history.Pagination.Page > 1,
	}

	return &creditv1.ListTransactionsResponse{
		Transactions:       protoTransactions,
		Pagination:         protoPagination,
		TotalCreditsEarned: int32(history.Summary.TotalCreditsEarned),
		TotalCreditsSpent:  int32(history.Summary.TotalCreditsUsed),
	}, nil
}

// CreateSubscription creates a new subscription
func (h *CreditServiceHandler) CreateSubscription(ctx context.Context, req *creditv1.CreateSubscriptionRequest) (*creditv1.CreateSubscriptionResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":      req.UserId,
		"plan_id":      req.PlanId,
		"payment_type": req.PaymentType,
	}).Info("Creating subscription")

	// Convert proto request to internal model
	createReq := &models.CreateSubscriptionRequest{
		UserID:          req.UserId,
		PlanID:          req.PlanId,
		PaymentMethodID: req.PaymentMethodId,
		Metadata:        convertProtoMapToInterface(req.Metadata),
	}

	// Create subscription
	subscription, err := h.subscriptionUseCase.CreateSubscription(ctx, createReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create subscription")
		return nil, status.Errorf(codes.Internal, "failed to create subscription: %v", err)
	}

	// Convert to proto response
	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
		PaymentMethod:      req.PaymentType,
		Metadata:           req.Metadata,
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	response := &creditv1.CreateSubscriptionResponse{
		Subscription: protoSubscription,
	}

	// Handle bank transfer payment type
	if req.PaymentType == "bank_transfer" {
		// TODO: Create bank transfer payment record
		response.RequiresConfirmation = true
	}

	return response, nil
}

// GetSubscription retrieves a user's subscription
func (h *CreditServiceHandler) GetSubscription(ctx context.Context, req *creditv1.GetSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting subscription")

	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get subscription")
		return nil, status.Errorf(codes.Internal, "failed to get subscription: %v", err)
	}

	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	return protoSubscription, nil
}

// ListSubscriptions lists user subscriptions
func (h *CreditServiceHandler) ListSubscriptions(ctx context.Context, req *creditv1.ListSubscriptionsRequest) (*creditv1.ListSubscriptionsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Listing subscriptions")

	// For now, return single subscription since we typically have one active subscription per user
	subscription, err := h.subscriptionUseCase.GetSubscription(ctx, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get subscription")
		return nil, status.Errorf(codes.Internal, "failed to get subscription: %v", err)
	}

	var subscriptions []*creditv1.Subscription
	if subscription != nil {
		protoSubscription := &creditv1.Subscription{
			Id:                 subscription.ID,
			UserId:             subscription.UserID,
			PlanId:             subscription.PlanID,
			PlanName:           subscription.PlanName,
			Status:             subscription.Status,
			CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
			PricePerMonth:      int32(subscription.Amount),
			Currency:           subscription.Currency,
			CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
			CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
			CreatedAt:          timestamppb.New(subscription.CreatedAt),
		}

		if subscription.CanceledAt != nil {
			protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
		}

		subscriptions = append(subscriptions, protoSubscription)
	}

	pagination := &commonv1.PaginationResponse{
		Page:       1,
		Limit:      10,
		TotalItems: int32(len(subscriptions)),
		TotalPages: 1,
		HasNext:    false,
		HasPrev:    false,
	}

	return &creditv1.ListSubscriptionsResponse{
		Subscriptions: subscriptions,
		Pagination:    pagination,
	}, nil
}

// UpdateSubscription updates a subscription
func (h *CreditServiceHandler) UpdateSubscription(ctx context.Context, req *creditv1.UpdateSubscriptionRequest) (*creditv1.Subscription, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id":     req.UserId,
		"new_plan_id": req.NewPlanId,
	}).Info("Updating subscription")

	// Convert to internal request
	updateReq := &models.UpdateSubscriptionRequest{
		SubscriptionID:  req.UserId, // Using user_id to find subscription
		PlanID:          req.NewPlanId,
		PaymentMethodID: req.PaymentMethodId,
	}

	subscription, err := h.subscriptionUseCase.UpdateSubscription(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update subscription")
		return nil, status.Errorf(codes.Internal, "failed to update subscription: %v", err)
	}

	protoSubscription := &creditv1.Subscription{
		Id:                 subscription.ID,
		UserId:             subscription.UserID,
		PlanId:             subscription.PlanID,
		PlanName:           subscription.PlanName,
		Status:             subscription.Status,
		CreditsPerMonth:    int32(subscription.Usage.CreditsLimit),
		PricePerMonth:      int32(subscription.Amount),
		Currency:           subscription.Currency,
		CurrentPeriodStart: timestamppb.New(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:   timestamppb.New(subscription.CurrentPeriodEnd),
		CreatedAt:          timestamppb.New(subscription.CreatedAt),
	}

	if subscription.CanceledAt != nil {
		protoSubscription.CancelledAt = timestamppb.New(*subscription.CanceledAt)
	}

	return protoSubscription, nil
}

// CancelSubscription cancels a subscription
func (h *CreditServiceHandler) CancelSubscription(ctx context.Context, req *creditv1.CancelSubscriptionRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"user_id":         req.UserId,
		"immediate":       req.Immediate,
		"reason":          req.Reason,
	}).Info("Cancelling subscription")

	cancelReq := &models.CancelSubscriptionRequest{
		SubscriptionID: req.SubscriptionId,
		Reason:         req.Reason,
		Immediate:      req.Immediate,
	}

	_, err := h.subscriptionUseCase.CancelSubscription(ctx, cancelReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel subscription")
		return nil, status.Errorf(codes.Internal, "failed to cancel subscription: %v", err)
	}

	return &creditv1.Empty{}, nil
}

// GetPaymentMethods retrieves user payment methods
func (h *CreditServiceHandler) GetPaymentMethods(ctx context.Context, req *creditv1.GetPaymentMethodsRequest) (*creditv1.GetPaymentMethodsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("Getting payment methods")

	// TODO: Implement payment method retrieval
	// For now, return empty response
	return &creditv1.GetPaymentMethodsResponse{
		PaymentMethods: []*creditv1.PaymentMethod{},
	}, nil
}

// AddPaymentMethod adds a payment method
func (h *CreditServiceHandler) AddPaymentMethod(ctx context.Context, req *creditv1.AddPaymentMethodRequest) (*creditv1.PaymentMethod, error) {
	h.logger.WithFields(map[string]interface{}{
		"user_id": req.UserId,
		"type":    req.Type,
	}).Info("Adding payment method")

	// TODO: Implement payment method addition
	return &creditv1.PaymentMethod{
		Id:        fmt.Sprintf("pm_%d", time.Now().Unix()),
		UserId:    req.UserId,
		Type:      req.Type,
		Status:    "active",
		Details:   req.Details,
		IsDefault: req.SetAsDefault,
		CreatedAt: timestamppb.Now(),
	}, nil
}

// RemovePaymentMethod removes a payment method
func (h *CreditServiceHandler) RemovePaymentMethod(ctx context.Context, req *creditv1.RemovePaymentMethodRequest) (*creditv1.Empty, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_method_id": req.PaymentMethodId,
		"user_id":           req.UserId,
	}).Info("Removing payment method")

	// TODO: Implement payment method removal
	return &creditv1.Empty{}, nil
}

// ConfirmBankTransfer confirms a bank transfer payment
func (h *CreditServiceHandler) ConfirmBankTransfer(ctx context.Context, req *creditv1.ConfirmBankTransferRequest) (*creditv1.ConfirmBankTransferResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"payment_id":        req.PaymentId,
		"user_id":           req.UserId,
		"confirmation_code": req.ConfirmationCode,
	}).Info("Confirming bank transfer")

	// TODO: Implement bank transfer confirmation
	return &creditv1.ConfirmBankTransferResponse{
		Success:      true,
		CreditsAdded: 100, // Placeholder
	}, nil
}

// ProcessRecurringPayment processes recurring subscription payments
func (h *CreditServiceHandler) ProcessRecurringPayment(ctx context.Context, req *creditv1.ProcessRecurringPaymentRequest) (*creditv1.ProcessRecurringPaymentResponse, error) {
	h.logger.WithFields(map[string]interface{}{
		"subscription_id": req.SubscriptionId,
		"force_process":   req.ForceProcess,
	}).Info("Processing recurring payment")

	// TODO: Implement recurring payment processing
	return &creditv1.ProcessRecurringPaymentResponse{
		Success:         true,
		PaymentId:       fmt.Sprintf("pay_%d", time.Now().Unix()),
		CreditsAdded:    100, // Placeholder
		NextPaymentDate: timestamppb.New(time.Now().AddDate(0, 1, 0)),
	}, nil
}

// Helper function to convert proto map to interface map
func convertProtoMapToInterface(protoMap map[string]string) map[string]interface{} {
	if protoMap == nil {
		return nil
	}
	result := make(map[string]interface{})
	for k, v := range protoMap {
		result[k] = v
	}
	return result
}

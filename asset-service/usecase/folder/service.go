package folder

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new folder service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreateFolder creates a new folder
func (s *service) CreateFolder(ctx context.Context, req *models.CreateFolderRequest) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"user_id":   req.UserID,
		"parent_id": req.ParentID,
		"name":      req.Name,
	}).Info("Creating folder")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Generate folder path
	folderPath := s.generateFolderPath(ctx, req.ParentID, req.Name)
	level := s.calculateFolderLevel(ctx, req.ParentID)

	// Create folder
	folderID := uuid.New()
	createQuery := s.writeDB.Folder.
		Create().
		SetID(folderID).
		SetUserID(userUUID).
		SetName(req.Name).
		SetDescription(req.Description).
		SetPath(folderPath).
		SetLevel(level)

	// Set parent if provided
	if req.ParentID != "" {
		parentUUID, err := uuid.Parse(req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("invalid parent ID format: %w", err)
		}
		createQuery = createQuery.SetParentID(parentUUID)
	}

	createdFolder, err := createQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create folder")
		return nil, fmt.Errorf("failed to create folder: %w", err)
	}

	// Convert to response model
	response := s.convertFolderToResponse(createdFolder)
	s.logger.WithField("folder_id", folderID).Info("Folder created successfully")
	return response, nil
}

// GetFolder gets a folder by ID
func (s *service) GetFolder(ctx context.Context, folderID, userID string) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
	}).Info("Getting folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get folder from database
	folderEntity, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("folder not found")
		}
		s.logger.WithError(err).Error("Failed to get folder")
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Convert to response model
	response := s.convertFolderToResponse(folderEntity)
	return response, nil
}

// UpdateFolder updates folder information
func (s *service) UpdateFolder(ctx context.Context, req *models.UpdateFolderRequest) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": req.FolderID,
		"user_id":   req.UserID,
	}).Info("Updating folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(req.FolderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build update query
	updateQuery := s.writeDB.Folder.
		Update().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		)

	// Apply updates only for non-empty fields
	if req.Name != "" {
		updateQuery = updateQuery.SetName(req.Name)
		// TODO: Update path if name changes
	}

	if req.Description != "" {
		updateQuery = updateQuery.SetDescription(req.Description)
	}

	// TODO: Handle parent ID change (move folder)

	// Execute update
	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update folder")
		return nil, fmt.Errorf("failed to update folder: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("folder not found or no changes made")
	}

	// Get updated folder
	updatedFolder, err := s.readDB.Folder.Get(ctx, folderUUID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated folder")
		return nil, fmt.Errorf("failed to get updated folder: %w", err)
	}

	// Convert to response model
	response := s.convertFolderToResponse(updatedFolder)
	s.logger.WithField("folder_id", req.FolderID).Info("Folder updated successfully")
	return response, nil
}

// DeleteFolder deletes a folder
func (s *service) DeleteFolder(ctx context.Context, folderID, userID string, force bool) error {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
		"force":     force,
	}).Info("Deleting folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if folder has children or assets
	if !force {
		hasChildren, err := s.readDB.Folder.
			Query().
			Where(
				folder.ParentID(folderUUID),
				folder.UserID(userUUID),
				folder.DeletedAtIsNil(),
			).
			Exist(ctx)

		if err != nil {
			return fmt.Errorf("failed to check folder children: %w", err)
		}

		if hasChildren {
			return fmt.Errorf("folder has children, use force=true to delete")
		}

		// TODO: Check if folder has assets
	}

	// Soft delete the folder
	affected, err := s.writeDB.Folder.
		Update().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		SetDeletedAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete folder")
		return fmt.Errorf("failed to delete folder: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("folder not found or already deleted")
	}

	s.logger.WithField("folder_id", folderID).Info("Folder deleted successfully")
	return nil
}

// ListFolders lists folders with pagination and filters
func (s *service) ListFolders(ctx context.Context, req *models.ListFoldersRequest) (*models.ListFoldersResponse, error) {
	s.logger.WithFields(map[string]any{
		"user_id":   req.UserID,
		"parent_id": req.ParentID,
		"page":      req.Page,
		"limit":     req.Limit,
	}).Info("Listing folders")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query
	query := s.readDB.Folder.
		Query().
		Where(
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		)

	// Apply parent filter
	if req.ParentID != "" {
		parentUUID, err := uuid.Parse(req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("invalid parent ID format: %w", err)
		}
		query = query.Where(folder.ParentID(parentUUID))
	} else {
		// Root folders (no parent)
		query = query.Where(folder.ParentIDIsNil())
	}

	// Apply search filter
	if req.Search != "" {
		query = query.Where(folder.NameContains(req.Search))
	}

	// Apply sorting
	switch req.SortBy {
	case "name":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(folder.FieldName))
		} else {
			query = query.Order(ent.Asc(folder.FieldName))
		}
	default:
		// Default sort by created_at desc
		query = query.Order(ent.Desc(folder.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count folders")
		return nil, fmt.Errorf("failed to count folders: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	folders, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list folders")
		return nil, fmt.Errorf("failed to list folders: %w", err)
	}

	// Convert to response models
	folderResponses := make([]models.FolderResponse, len(folders))
	for i, f := range folders {
		folderResponses[i] = *s.convertFolderToResponse(f)
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	response := &models.ListFoldersResponse{
		Folders: folderResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	s.logger.WithField("folder_count", len(folders)).Info("Folders listed successfully")
	return response, nil
}

// Helper methods

// generateFolderPath generates full path for folder
func (s *service) generateFolderPath(ctx context.Context, parentID, name string) string {
	if parentID == "" {
		return "/" + name
	}

	// Get parent folder path
	parentUUID, err := uuid.Parse(parentID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid parent ID in generateFolderPath")
		return "/" + name
	}

	parentFolder, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(parentUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get parent folder path")
		return "/" + name
	}

	return parentFolder.Path + "/" + name
}

// calculateFolderLevel calculates folder depth level
func (s *service) calculateFolderLevel(ctx context.Context, parentID string) int {
	if parentID == "" {
		return 0
	}

	// Get parent folder level
	parentUUID, err := uuid.Parse(parentID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid parent ID in calculateFolderLevel")
		return 1
	}

	parentFolder, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(parentUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get parent folder level")
		return 1
	}

	return parentFolder.Level + 1
}

// convertFolderToResponse converts ent Folder to FolderResponse model
func (s *service) convertFolderToResponse(folder *ent.Folder) *models.FolderResponse {
	response := &models.FolderResponse{
		BaseModel: models.BaseModel{
			ID:        folder.ID.String(),
			CreatedAt: folder.CreatedAt,
			UpdatedAt: folder.UpdatedAt,
		},
		UserID:      folder.UserID.String(),
		Name:        folder.Name,
		Description: folder.Description,
		Path:        folder.Path,
		Level:       folder.Level,
		IsShared:    folder.IsShared,
		SharedWith:  folder.SharedWith,
		Permissions: folder.Permissions,
		AssetCount:  folder.AssetCount,
		TotalSize:   folder.TotalSize,
	}

	// Set parent ID if exists
	if folder.ParentID != nil {
		response.ParentID = folder.ParentID.String()
	}

	return response
}

// CheckFolderAccess checks if user has access to folder
func (s *service) CheckFolderAccess(ctx context.Context, folderID, userID string, permission string) (bool, error) {
	s.logger.WithFields(map[string]any{
		"folder_id":  folderID,
		"user_id":    userID,
		"permission": permission,
	}).Info("Checking folder access")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return false, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return false, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if user owns the folder
	exists, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Exist(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to check folder ownership")
		return false, fmt.Errorf("failed to check folder ownership: %w", err)
	}

	if exists {
		// Owner has all permissions
		return true, nil
	}

	// TODO: Check shared permissions
	// For now, return false for non-owners
	return false, nil
}

// MoveFolder moves a folder to a new parent
func (s *service) MoveFolder(ctx context.Context, req *models.MoveFolderRequest) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id":     req.FolderID,
		"new_parent_id": req.NewParentID,
		"user_id":       req.UserID,
	}).Info("Moving folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(req.FolderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the folder to move
	folderEntity, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("folder not found")
		}
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Validate new parent
	var newParentUUID *uuid.UUID
	if req.NewParentID != "" {
		parentUUID, err := uuid.Parse(req.NewParentID)
		if err != nil {
			return nil, fmt.Errorf("invalid new parent ID format: %w", err)
		}

		// Check if new parent exists and belongs to user
		exists, err := s.readDB.Folder.
			Query().
			Where(
				folder.ID(parentUUID),
				folder.UserID(userUUID),
				folder.DeletedAtIsNil(),
			).
			Exist(ctx)

		if err != nil {
			return nil, fmt.Errorf("failed to check new parent: %w", err)
		}

		if !exists {
			return nil, fmt.Errorf("new parent folder not found")
		}

		// Prevent moving folder into itself or its descendants
		if req.NewParentID == req.FolderID {
			return nil, fmt.Errorf("cannot move folder into itself")
		}

		newParentUUID = &parentUUID
	}

	// Calculate new path and level
	newPath := s.generateFolderPath(ctx, req.NewParentID, folderEntity.Name)
	newLevel := s.calculateFolderLevel(ctx, req.NewParentID)

	// Update folder
	update := s.writeDB.Folder.
		UpdateOneID(folderUUID).
		SetPath(newPath).
		SetLevel(newLevel).
		SetUpdatedAt(time.Now())

	if newParentUUID != nil {
		update = update.SetParentID(*newParentUUID)
	} else {
		update = update.ClearParentID()
	}

	updatedFolder, err := update.Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to move folder: %w", err)
	}

	s.logger.WithField("folder_id", req.FolderID).Info("Folder moved successfully")
	return s.convertFolderToResponse(updatedFolder), nil
}

func (s *service) CopyFolder(ctx context.Context, req *models.CopyFolderRequest) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id":     req.FolderID,
		"new_parent_id": req.NewParentID,
		"new_name":      req.NewName,
		"user_id":       req.UserID,
	}).Info("Copying folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(req.FolderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the folder to copy
	sourceFolder, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("source folder not found")
		}
		return nil, fmt.Errorf("failed to get source folder: %w", err)
	}

	// Validate new parent if specified
	var newParentUUID *uuid.UUID
	if req.NewParentID != "" {
		parentUUID, err := uuid.Parse(req.NewParentID)
		if err != nil {
			return nil, fmt.Errorf("invalid new parent ID format: %w", err)
		}

		// Check if new parent exists and belongs to user
		exists, err := s.readDB.Folder.
			Query().
			Where(
				folder.ID(parentUUID),
				folder.UserID(userUUID),
				folder.DeletedAtIsNil(),
			).
			Exist(ctx)

		if err != nil {
			return nil, fmt.Errorf("failed to check new parent: %w", err)
		}

		if !exists {
			return nil, fmt.Errorf("new parent folder not found")
		}

		newParentUUID = &parentUUID
	}

	// Determine new name
	newName := req.NewName
	if newName == "" {
		newName = sourceFolder.Name + " (Copy)"
	}

	// Check for name conflicts
	exists, err := s.checkFolderNameExists(ctx, req.UserID, req.NewParentID, newName)
	if err != nil {
		return nil, fmt.Errorf("failed to check name conflicts: %w", err)
	}

	if exists {
		return nil, fmt.Errorf("folder with name '%s' already exists in target location", newName)
	}

	// Calculate new path and level
	newPath := s.generateFolderPath(ctx, req.NewParentID, newName)
	newLevel := s.calculateFolderLevel(ctx, req.NewParentID)

	// Create new folder
	create := s.writeDB.Folder.
		Create().
		SetUserID(userUUID).
		SetName(newName).
		SetDescription(sourceFolder.Description).
		SetPath(newPath).
		SetLevel(newLevel).
		SetIsShared(false). // Copy is not shared by default
		SetSharedWith([]string{}).
		SetPermissions([]string{}).
		SetAssetCount(0). // Copy starts with 0 assets
		SetTotalSize(0)

	if newParentUUID != nil {
		create = create.SetParentID(*newParentUUID)
	}

	copiedFolder, err := create.Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to copy folder: %w", err)
	}

	s.logger.WithField("new_folder_id", copiedFolder.ID.String()).Info("Folder copied successfully")
	return s.convertFolderToResponse(copiedFolder), nil
}

// checkFolderNameExists checks if a folder name already exists in the given parent
func (s *service) checkFolderNameExists(ctx context.Context, userID, parentID, name string) (bool, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return false, fmt.Errorf("invalid user ID format: %w", err)
	}

	query := s.readDB.Folder.
		Query().
		Where(
			folder.UserID(userUUID),
			folder.Name(name),
			folder.DeletedAtIsNil(),
		)

	if parentID != "" {
		parentUUID, err := uuid.Parse(parentID)
		if err != nil {
			return false, fmt.Errorf("invalid parent ID format: %w", err)
		}
		query = query.Where(folder.ParentID(parentUUID))
	} else {
		query = query.Where(folder.ParentIDIsNil())
	}

	return query.Exist(ctx)
}

// buildFolderTree builds a hierarchical tree structure from flat folder list
func (s *service) buildFolderTree(folders []*ent.Folder, rootFolderID string) *models.FolderTree {
	// Create a map for quick lookup
	folderMap := make(map[string]*models.FolderTree)

	// First pass: create all nodes
	for _, f := range folders {
		node := &models.FolderTree{
			Folder:   *s.convertFolderToResponse(f),
			Children: []models.FolderTree{},
		}
		folderMap[f.ID.String()] = node
	}

	// Find root node or create virtual root
	var rootNode *models.FolderTree

	// If specific root folder requested
	if rootFolderID != "" {
		if node, exists := folderMap[rootFolderID]; exists {
			rootNode = node
		} else {
			// Root folder not found, return empty tree
			return &models.FolderTree{
				Folder: models.FolderResponse{
					BaseModel: models.BaseModel{ID: "not-found"},
					Name:      "Not Found",
					Path:      "/",
					Level:     0,
				},
				Children: []models.FolderTree{},
			}
		}
	} else {
		// Create virtual root for all top-level folders
		rootNode = &models.FolderTree{
			Folder: models.FolderResponse{
				BaseModel: models.BaseModel{ID: "root"},
				Name:      "Root",
				Path:      "/",
				Level:     -1,
			},
			Children: []models.FolderTree{},
		}
	}

	// Second pass: build parent-child relationships
	for _, f := range folders {
		node := folderMap[f.ID.String()]

		if f.ParentID != nil {
			parentID := f.ParentID.String()
			if parent, exists := folderMap[parentID]; exists {
				parent.Children = append(parent.Children, *node)
			}
		} else if rootFolderID == "" {
			// Add to virtual root
			rootNode.Children = append(rootNode.Children, *node)
		}
	}

	return rootNode
}

func (s *service) GetFolderAssets(ctx context.Context, folderID, userID string, page, limit int) (*models.ListAssetsResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
		"page":      page,
		"limit":     limit,
	}).Info("Getting folder assets")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if folder exists and belongs to user
	exists, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Exist(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to check folder: %w", err)
	}

	if !exists {
		return nil, fmt.Errorf("folder not found")
	}

	// TODO: This would require access to asset service
	// For now, return empty response
	return &models.ListAssetsResponse{
		Assets: []models.AssetResponse{},
		Pagination: models.PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      0,
			TotalPages: 0,
		},
	}, nil
}

func (s *service) ShareFolder(ctx context.Context, req *models.ShareFolderRequest) (*models.FolderResponse, error) {
	s.logger.WithFields(map[string]any{
		"folder_id":   req.FolderID,
		"user_id":     req.UserID,
		"share_with":  req.ShareWith,
		"permissions": req.Permissions,
	}).Info("Sharing folder")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(req.FolderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if folder exists and belongs to user
	exists, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Exist(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to check folder: %w", err)
	}

	if !exists {
		return nil, fmt.Errorf("folder not found")
	}

	// Update sharing settings
	updatedFolder, err := s.writeDB.Folder.
		UpdateOneID(folderUUID).
		SetIsShared(true).
		SetSharedWith(req.ShareWith).
		SetPermissions(req.Permissions).
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to share folder: %w", err)
	}

	s.logger.WithField("folder_id", req.FolderID).Info("Folder shared successfully")
	return s.convertFolderToResponse(updatedFolder), nil
}

func (s *service) GetFolderTree(ctx context.Context, userID string, rootFolderID string) (*models.FolderTree, error) {
	s.logger.WithFields(map[string]any{
		"user_id":        userID,
		"root_folder_id": rootFolderID,
	}).Info("Getting folder tree")

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get all folders for the user
	query := s.readDB.Folder.
		Query().
		Where(
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Order(ent.Asc(folder.FieldLevel), ent.Asc(folder.FieldName))

	// If root folder specified, get only that subtree
	if rootFolderID != "" {
		rootUUID, err := uuid.Parse(rootFolderID)
		if err != nil {
			return nil, fmt.Errorf("invalid root folder ID format: %w", err)
		}

		// Get root folder path
		rootFolder, err := s.readDB.Folder.
			Query().
			Where(
				folder.ID(rootUUID),
				folder.UserID(userUUID),
				folder.DeletedAtIsNil(),
			).
			Only(ctx)

		if err != nil {
			if ent.IsNotFound(err) {
				return nil, fmt.Errorf("root folder not found")
			}
			return nil, fmt.Errorf("failed to get root folder: %w", err)
		}

		// Filter folders that are under this root
		query = query.Where(
			folder.Or(
				folder.ID(rootUUID),
				folder.PathHasPrefix(rootFolder.Path+"/"),
			),
		)
	}

	folders, err := query.All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get folders: %w", err)
	}

	// Build tree structure
	tree := s.buildFolderTree(folders, rootFolderID)

	return tree, nil
}

func (s *service) GetFolderBreadcrumb(ctx context.Context, folderID, userID string) ([]models.FolderBreadcrumb, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
	}).Info("Getting folder breadcrumb")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the target folder
	targetFolder, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("folder not found")
		}
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Build breadcrumb by traversing up the hierarchy
	var breadcrumbs []models.FolderBreadcrumb
	currentFolder := targetFolder

	for currentFolder != nil {
		breadcrumb := models.FolderBreadcrumb{
			ID:   currentFolder.ID.String(),
			Name: currentFolder.Name,
			Path: currentFolder.Path,
		}

		// Prepend to get correct order (root -> ... -> current)
		breadcrumbs = append([]models.FolderBreadcrumb{breadcrumb}, breadcrumbs...)

		// Get parent folder if exists
		if currentFolder.ParentID != nil {
			parentFolder, err := s.readDB.Folder.
				Query().
				Where(
					folder.ID(*currentFolder.ParentID),
					folder.UserID(userUUID),
					folder.DeletedAtIsNil(),
				).
				Only(ctx)

			if err != nil {
				if ent.IsNotFound(err) {
					break // Parent not found, stop here
				}
				s.logger.WithError(err).Warn("Failed to get parent folder for breadcrumb")
				break
			}

			currentFolder = parentFolder
		} else {
			break // Reached root
		}
	}

	// Add root breadcrumb at the beginning
	if len(breadcrumbs) > 0 && breadcrumbs[0].Path != "/" {
		rootBreadcrumb := models.FolderBreadcrumb{
			ID:   "root",
			Name: "Root",
			Path: "/",
		}
		breadcrumbs = append([]models.FolderBreadcrumb{rootBreadcrumb}, breadcrumbs...)
	}

	return breadcrumbs, nil
}

func (s *service) GetFolderPath(ctx context.Context, folderID, userID string) (string, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
	}).Info("Getting folder path")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return "", fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the folder
	folderEntity, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return "", fmt.Errorf("folder not found")
		}
		return "", fmt.Errorf("failed to get folder: %w", err)
	}

	return folderEntity.Path, nil
}

func (s *service) GetFolderPermissions(ctx context.Context, folderID, userID string) ([]models.FolderPermission, error) {
	s.logger.WithFields(map[string]any{
		"folder_id": folderID,
		"user_id":   userID,
	}).Info("Getting folder permissions")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return nil, fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the folder
	folderEntity, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("folder not found")
		}
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Convert stored permissions to response format
	var permissions []models.FolderPermission

	// Owner always has full permissions
	ownerPermission := models.FolderPermission{
		UserID:      folderEntity.UserID.String(),
		Permissions: []string{"read", "write", "delete", "share"},
		GrantedAt:   folderEntity.CreatedAt,
	}
	permissions = append(permissions, ownerPermission)

	// TODO: Parse shared permissions from SharedWith and Permissions fields
	// For now, return only owner permissions

	return permissions, nil
}

func (s *service) UpdateFolderPermissions(ctx context.Context, folderID, userID string, permissions []models.FolderPermission) error {
	s.logger.WithFields(map[string]any{
		"folder_id":   folderID,
		"user_id":     userID,
		"permissions": len(permissions),
	}).Info("Updating folder permissions")

	// Parse IDs to UUID
	folderUUID, err := uuid.Parse(folderID)
	if err != nil {
		return fmt.Errorf("invalid folder ID format: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if folder exists and belongs to user
	exists, err := s.readDB.Folder.
		Query().
		Where(
			folder.ID(folderUUID),
			folder.UserID(userUUID),
			folder.DeletedAtIsNil(),
		).
		Exist(ctx)

	if err != nil {
		return fmt.Errorf("failed to check folder: %w", err)
	}

	if !exists {
		return fmt.Errorf("folder not found")
	}

	// Extract shared users and permissions
	var sharedWith []string
	var permissionsList []string

	for _, perm := range permissions {
		if perm.UserID != userID { // Skip owner
			sharedWith = append(sharedWith, perm.UserID)
			for _, p := range perm.Permissions {
				permissionsList = append(permissionsList, p)
			}
		}
	}

	// Update folder permissions
	_, err = s.writeDB.Folder.
		UpdateOneID(folderUUID).
		SetSharedWith(sharedWith).
		SetPermissions(permissionsList).
		SetIsShared(len(sharedWith) > 0).
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		return fmt.Errorf("failed to update folder permissions: %w", err)
	}

	s.logger.WithField("folder_id", folderID).Info("Folder permissions updated successfully")
	return nil
}

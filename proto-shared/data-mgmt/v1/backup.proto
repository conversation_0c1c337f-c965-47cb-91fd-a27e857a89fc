syntax = "proto3";

package data_mgmt.v1;

import "google/protobuf/timestamp.proto";
import "common/v1/pagination.proto";

option go_package = "github.com/social-content-ai/proto-shared/data-mgmt/v1;datamgmtv1";

// BackupJob represents a data backup job
message BackupJob {
  string id = 1;
  string user_id = 2;
  string workspace_id = 3;       // Optional workspace filter
  string backup_type = 4;        // full, incremental, posts_only, images_only
  string status = 5;             // pending, processing, completed, failed
  string format = 6;             // html_zip, json, csv
  int64 total_size = 7;          // Total backup size in bytes
  string s3_path = 8;            // S3 backup file path
  string download_url = 9;       // Presigned download URL (24h expiry)
  google.protobuf.Timestamp expires_at = 10; // Download URL expiry
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp completed_at = 12;
  string error_message = 13;     // If failed
  BackupStats stats = 14;
}

// BackupStats with backup metrics
message BackupStats {
  int32 posts_count = 1;
  int32 images_count = 2;
  int32 templates_count = 3;
  int32 workspaces_count = 4;
  int32 files_count = 5;
  int64 total_size = 6;          // Total size in bytes
  float processing_time = 7;     // Processing time in seconds
}

// CreateBackupRequest for creating a new backup
message CreateBackupRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  string backup_type = 3;        // full, incremental, posts_only, images_only
  string format = 4;             // html_zip, json, csv
  repeated string include_data = 5; // posts, images, templates, workspaces, analytics
  string date_from = 6;          // Format: YYYY-MM-DD (for incremental)
  string date_to = 7;            // Format: YYYY-MM-DD (for incremental)
  bool include_deleted = 8;      // Include soft-deleted items
}

// CreateBackupResponse with backup job info
message CreateBackupResponse {
  string backup_id = 1;
  string status = 2;
  google.protobuf.Timestamp created_at = 3;
  int32 estimated_duration = 4;  // Estimated processing time in seconds
}

// GetBackupRequest for retrieving backup info
message GetBackupRequest {
  string backup_id = 1;
  string user_id = 2;
}

// ListBackupsRequest for listing user backups
message ListBackupsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  common.v1.PaginationRequest pagination = 3;
  string status = 4;             // Filter by status
  string backup_type = 5;        // Filter by backup type
  string date_from = 6;          // Filter by creation date
  string date_to = 7;            // Filter by creation date
}

// ListBackupsResponse with paginated backups
message ListBackupsResponse {
  repeated BackupJob backups = 1;
  common.v1.PaginationResponse pagination = 2;
}

// DeleteBackupRequest for deleting a backup
message DeleteBackupRequest {
  string backup_id = 1;
  string user_id = 2;
  bool force_delete = 3;         // Force delete even if not expired
}

// RefreshDownloadUrlRequest for refreshing download URL
message RefreshDownloadUrlRequest {
  string backup_id = 1;
  string user_id = 2;
  int32 expiry_hours = 3;        // URL expiry in hours (max 24)
}

// RefreshDownloadUrlResponse with new URL
message RefreshDownloadUrlResponse {
  string download_url = 1;
  google.protobuf.Timestamp expires_at = 2;
}

// AccountDeletionRequest for GDPR account deletion
message AccountDeletionRequest {
  string user_id = 1;
  string reason = 2;             // user_request, gdpr_compliance, admin_action
  bool create_backup = 3;        // Create backup before deletion
  string backup_format = 4;      // html_zip, json (if create_backup=true)
  bool immediate = 5;            // Immediate deletion or scheduled
  google.protobuf.Timestamp scheduled_at = 6; // If not immediate
}

// AccountDeletionResponse with deletion job info
message AccountDeletionResponse {
  string deletion_id = 1;
  string status = 2;             // scheduled, processing, completed
  string backup_id = 3;          // If backup was created
  google.protobuf.Timestamp scheduled_at = 4;
  google.protobuf.Timestamp completed_at = 5;
}

// GetDeletionStatusRequest for checking deletion status
message GetDeletionStatusRequest {
  string deletion_id = 1;
  string user_id = 2;            // Admin can check any user
}

// DeletionStatus with current deletion status
message DeletionStatus {
  string deletion_id = 1;
  string user_id = 2;
  string status = 3;             // scheduled, processing, completed, failed
  float progress = 4;            // Progress percentage (0-100)
  string current_stage = 5;      // backup_creation, data_anonymization, file_deletion
  google.protobuf.Timestamp scheduled_at = 6;
  google.protobuf.Timestamp started_at = 7;
  google.protobuf.Timestamp completed_at = 8;
  string error_message = 9;      // If failed
  DeletionStats stats = 10;
}

// DeletionStats with deletion metrics
message DeletionStats {
  int32 posts_deleted = 1;
  int32 images_deleted = 2;
  int32 templates_deleted = 3;
  int32 workspaces_deleted = 4;
  int32 files_deleted = 5;
  int64 storage_freed = 6;       // Storage freed in bytes
  float processing_time = 7;     // Processing time in seconds
}

// DataExportRequest for exporting specific data
message DataExportRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  string export_type = 3;        // posts, templates, analytics, all
  string format = 4;             // json, csv, html
  repeated string fields = 5;    // Specific fields to export
  string date_from = 6;          // Format: YYYY-MM-DD
  string date_to = 7;            // Format: YYYY-MM-DD
  map<string, string> filters = 8; // Additional filters
}

// DataExportResponse with export job info
message DataExportResponse {
  string export_id = 1;
  string status = 2;
  string download_url = 3;       // Presigned download URL
  google.protobuf.Timestamp expires_at = 4;
  int64 file_size = 5;           // Export file size in bytes
  google.protobuf.Timestamp created_at = 6;
}

// GetExportRequest for retrieving export info
message GetExportRequest {
  string export_id = 1;
  string user_id = 2;
}

// ListExportsRequest for listing user exports
message ListExportsRequest {
  string user_id = 1;
  string workspace_id = 2;       // Optional workspace filter
  common.v1.PaginationRequest pagination = 3;
  string export_type = 4;        // Filter by export type
  string status = 5;             // Filter by status
}

// ListExportsResponse with paginated exports
message ListExportsResponse {
  repeated DataExportResponse exports = 1;
  common.v1.PaginationResponse pagination = 2;
}

// CleanupExpiredRequest for cleaning up expired backups/exports
message CleanupExpiredRequest {
  string cleanup_type = 1;       // backups, exports, all
  int32 days_old = 2;            // Clean items older than X days
  bool dry_run = 3;              // Preview what would be cleaned
}

// CleanupExpiredResponse with cleanup results
message CleanupExpiredResponse {
  int32 items_cleaned = 1;
  int64 storage_freed = 2;       // Storage freed in bytes
  repeated string cleaned_items = 3; // List of cleaned item IDs
}

// Empty response
message Empty {}

// BackupService provides data backup and export operations
service BackupService {
  // Backup operations
  rpc CreateBackup(CreateBackupRequest) returns (CreateBackupResponse);
  rpc GetBackup(GetBackupRequest) returns (BackupJob);
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse);
  rpc DeleteBackup(DeleteBackupRequest) returns (Empty);
  rpc RefreshDownloadUrl(RefreshDownloadUrlRequest) returns (RefreshDownloadUrlResponse);
  
  // Data export operations
  rpc ExportData(DataExportRequest) returns (DataExportResponse);
  rpc GetExport(GetExportRequest) returns (DataExportResponse);
  rpc ListExports(ListExportsRequest) returns (ListExportsResponse);
  
  // GDPR compliance operations
  rpc RequestAccountDeletion(AccountDeletionRequest) returns (AccountDeletionResponse);
  rpc GetDeletionStatus(GetDeletionStatusRequest) returns (DeletionStatus);
  
  // Maintenance operations
  rpc CleanupExpired(CleanupExpiredRequest) returns (CleanupExpiredResponse);
}

// Code generated by ent, DO NOT EDIT.

package template

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the template type in the database.
	Label = "template"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldWorkspaceID holds the string denoting the workspace_id field in the database.
	FieldWorkspaceID = "workspace_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldVariables holds the string denoting the variables field in the database.
	FieldVariables = "variables"
	// FieldVariableDescriptions holds the string denoting the variable_descriptions field in the database.
	FieldVariableDescriptions = "variable_descriptions"
	// FieldCategory holds the string denoting the category field in the database.
	FieldCategory = "category"
	// FieldPlatforms holds the string denoting the platforms field in the database.
	FieldPlatforms = "platforms"
	// FieldTone holds the string denoting the tone field in the database.
	FieldTone = "tone"
	// FieldContentType holds the string denoting the content_type field in the database.
	FieldContentType = "content_type"
	// FieldHashtags holds the string denoting the hashtags field in the database.
	FieldHashtags = "hashtags"
	// FieldIsPublic holds the string denoting the is_public field in the database.
	FieldIsPublic = "is_public"
	// FieldIsFeatured holds the string denoting the is_featured field in the database.
	FieldIsFeatured = "is_featured"
	// FieldUsageCount holds the string denoting the usage_count field in the database.
	FieldUsageCount = "usage_count"
	// FieldRating holds the string denoting the rating field in the database.
	FieldRating = "rating"
	// FieldRatingCount holds the string denoting the rating_count field in the database.
	FieldRatingCount = "rating_count"
	// FieldTags holds the string denoting the tags field in the database.
	FieldTags = "tags"
	// FieldLanguage holds the string denoting the language field in the database.
	FieldLanguage = "language"
	// FieldTrainingFiles holds the string denoting the training_files field in the database.
	FieldTrainingFiles = "training_files"
	// FieldRagTrained holds the string denoting the rag_trained field in the database.
	FieldRagTrained = "rag_trained"
	// FieldRagTrainedAt holds the string denoting the rag_trained_at field in the database.
	FieldRagTrainedAt = "rag_trained_at"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the template in the database.
	Table = "templates"
)

// Columns holds all SQL columns for template fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldWorkspaceID,
	FieldName,
	FieldDescription,
	FieldContent,
	FieldVariables,
	FieldVariableDescriptions,
	FieldCategory,
	FieldPlatforms,
	FieldTone,
	FieldContentType,
	FieldHashtags,
	FieldIsPublic,
	FieldIsFeatured,
	FieldUsageCount,
	FieldRating,
	FieldRatingCount,
	FieldTags,
	FieldLanguage,
	FieldTrainingFiles,
	FieldRagTrained,
	FieldRagTrainedAt,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// WorkspaceIDValidator is a validator for the "workspace_id" field. It is called by the builders before save.
	WorkspaceIDValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultIsPublic holds the default value on creation for the "is_public" field.
	DefaultIsPublic bool
	// DefaultIsFeatured holds the default value on creation for the "is_featured" field.
	DefaultIsFeatured bool
	// DefaultUsageCount holds the default value on creation for the "usage_count" field.
	DefaultUsageCount int
	// DefaultRating holds the default value on creation for the "rating" field.
	DefaultRating float64
	// RatingValidator is a validator for the "rating" field. It is called by the builders before save.
	RatingValidator func(float64) error
	// DefaultRatingCount holds the default value on creation for the "rating_count" field.
	DefaultRatingCount int
	// DefaultLanguage holds the default value on creation for the "language" field.
	DefaultLanguage string
	// LanguageValidator is a validator for the "language" field. It is called by the builders before save.
	LanguageValidator func(string) error
	// DefaultRagTrained holds the default value on creation for the "rag_trained" field.
	DefaultRagTrained bool
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Category defines the type for the "category" enum field.
type Category string

// CategorySocialMedia is the default value of the Category enum.
const DefaultCategory = CategorySocialMedia

// Category values.
const (
	CategorySocialMedia Category = "social_media"
	CategoryBlog        Category = "blog"
	CategoryEmail       Category = "email"
	CategoryMarketing   Category = "marketing"
	CategoryEducational Category = "educational"
	CategoryPromotional Category = "promotional"
	CategoryPersonal    Category = "personal"
	CategoryBusiness    Category = "business"
)

func (c Category) String() string {
	return string(c)
}

// CategoryValidator is a validator for the "category" field enum values. It is called by the builders before save.
func CategoryValidator(c Category) error {
	switch c {
	case CategorySocialMedia, CategoryBlog, CategoryEmail, CategoryMarketing, CategoryEducational, CategoryPromotional, CategoryPersonal, CategoryBusiness:
		return nil
	default:
		return fmt.Errorf("template: invalid enum value for category field: %q", c)
	}
}

// Tone defines the type for the "tone" enum field.
type Tone string

// ToneProfessional is the default value of the Tone enum.
const DefaultTone = ToneProfessional

// Tone values.
const (
	ToneProfessional  Tone = "professional"
	ToneFriendly      Tone = "friendly"
	ToneHumorous      Tone = "humorous"
	ToneFormal        Tone = "formal"
	ToneCasual        Tone = "casual"
	ToneEnthusiastic  Tone = "enthusiastic"
	ToneAuthoritative Tone = "authoritative"
)

func (t Tone) String() string {
	return string(t)
}

// ToneValidator is a validator for the "tone" field enum values. It is called by the builders before save.
func ToneValidator(t Tone) error {
	switch t {
	case ToneProfessional, ToneFriendly, ToneHumorous, ToneFormal, ToneCasual, ToneEnthusiastic, ToneAuthoritative:
		return nil
	default:
		return fmt.Errorf("template: invalid enum value for tone field: %q", t)
	}
}

// ContentType defines the type for the "content_type" enum field.
type ContentType string

// ContentTypeInformational is the default value of the ContentType enum.
const DefaultContentType = ContentTypeInformational

// ContentType values.
const (
	ContentTypeEducational   ContentType = "educational"
	ContentTypePromotional   ContentType = "promotional"
	ContentTypeEntertainment ContentType = "entertainment"
	ContentTypeInformational ContentType = "informational"
	ContentTypeNews          ContentType = "news"
	ContentTypeTutorial      ContentType = "tutorial"
)

func (ct ContentType) String() string {
	return string(ct)
}

// ContentTypeValidator is a validator for the "content_type" field enum values. It is called by the builders before save.
func ContentTypeValidator(ct ContentType) error {
	switch ct {
	case ContentTypeEducational, ContentTypePromotional, ContentTypeEntertainment, ContentTypeInformational, ContentTypeNews, ContentTypeTutorial:
		return nil
	default:
		return fmt.Errorf("template: invalid enum value for content_type field: %q", ct)
	}
}

// OrderOption defines the ordering options for the Template queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByWorkspaceID orders the results by the workspace_id field.
func ByWorkspaceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkspaceID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByCategory orders the results by the category field.
func ByCategory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCategory, opts...).ToFunc()
}

// ByTone orders the results by the tone field.
func ByTone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTone, opts...).ToFunc()
}

// ByContentType orders the results by the content_type field.
func ByContentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContentType, opts...).ToFunc()
}

// ByIsPublic orders the results by the is_public field.
func ByIsPublic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPublic, opts...).ToFunc()
}

// ByIsFeatured orders the results by the is_featured field.
func ByIsFeatured(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsFeatured, opts...).ToFunc()
}

// ByUsageCount orders the results by the usage_count field.
func ByUsageCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsageCount, opts...).ToFunc()
}

// ByRating orders the results by the rating field.
func ByRating(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRating, opts...).ToFunc()
}

// ByRatingCount orders the results by the rating_count field.
func ByRatingCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRatingCount, opts...).ToFunc()
}

// ByLanguage orders the results by the language field.
func ByLanguage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLanguage, opts...).ToFunc()
}

// ByRagTrained orders the results by the rag_trained field.
func ByRagTrained(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRagTrained, opts...).ToFunc()
}

// ByRagTrainedAt orders the results by the rag_trained_at field.
func ByRagTrainedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRagTrainedAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

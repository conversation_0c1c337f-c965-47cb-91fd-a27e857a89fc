package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/content-mgmt-service/usecase/schedule"
	"github.com/social-content-ai/pkg-shared/logging"
)

// ScheduleHandler handles schedule-related HTTP requests
type Schedule<PERSON>andler struct {
	scheduleUseCase schedule.UseCase
	logger          logging.Logger
}

// NewScheduleHandler creates a new schedule handler
func NewScheduleHandler(scheduleUseCase schedule.UseCase, logger logging.Logger) *ScheduleHandler {
	return &ScheduleHandler{
		scheduleUseCase: scheduleUseCase,
		logger:          logger,
	}
}

// CreateSchedule creates a new schedule
func (h *ScheduleHandler) CreateSchedule(c *gin.Context) {
	var req models.CreateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind create schedule request")
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	req.UserID = userID.(string)

	schedule, err := h.scheduleUseCase.CreateSchedule(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create schedule"})
		return
	}

	c.JSON(http.StatusCreated, schedule)
}

// GetSchedule retrieves a schedule by ID
func (h *ScheduleHandler) GetSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	schedule, err := h.scheduleUseCase.GetSchedule(c.Request.Context(), scheduleID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// UpdateSchedule updates an existing schedule
func (h *ScheduleHandler) UpdateSchedule(c *gin.Context) {
	var req models.UpdateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind update schedule request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	req.ScheduleID = c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	req.UserID = userID.(string)

	schedule, err := h.scheduleUseCase.UpdateSchedule(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// DeleteSchedule deletes a schedule
func (h *ScheduleHandler) DeleteSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.scheduleUseCase.DeleteSchedule(c.Request.Context(), scheduleID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete schedule"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// ListSchedules lists schedules for a user
func (h *ScheduleHandler) ListSchedules(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	workspaceID := c.Query("workspace_id")

	req := &models.ListSchedulesRequest{
		UserID:      userID.(string),
		Page:        page,
		Limit:       limit,
		Status:      status,
		WorkspaceID: workspaceID,
	}

	schedules, err := h.scheduleUseCase.ListSchedules(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list schedules")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list schedules"})
		return
	}

	c.JSON(http.StatusOK, schedules)
}

// ExecuteSchedule executes a schedule
func (h *ScheduleHandler) ExecuteSchedule(c *gin.Context) {
	scheduleID := c.Param("id")

	execution, err := h.scheduleUseCase.ExecuteSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to execute schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute schedule"})
		return
	}

	c.JSON(http.StatusOK, execution)
}

// EnableSchedule enables a schedule
func (h *ScheduleHandler) EnableSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	schedule, err := h.scheduleUseCase.EnableSchedule(c.Request.Context(), scheduleID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to enable schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to enable schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// DisableSchedule disables a schedule
func (h *ScheduleHandler) DisableSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	schedule, err := h.scheduleUseCase.DisableSchedule(c.Request.Context(), scheduleID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to disable schedule")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to disable schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// GetScheduleCalendar gets schedule calendar
func (h *ScheduleHandler) GetScheduleCalendar(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	year, _ := strconv.Atoi(c.DefaultQuery("year", "2024"))
	month, _ := strconv.Atoi(c.DefaultQuery("month", "1"))
	workspaceID := c.Query("workspace_id")
	platform := c.Query("platform")

	req := &models.ScheduleCalendarRequest{
		UserID:      userID.(string),
		WorkspaceID: workspaceID,
		Year:        year,
		Month:       month,
		Platform:    platform,
	}

	calendar, err := h.scheduleUseCase.GetScheduleCalendar(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get schedule calendar")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule calendar"})
		return
	}

	c.JSON(http.StatusOK, calendar)
}

// GetUpcomingSchedules gets upcoming schedules
func (h *ScheduleHandler) GetUpcomingSchedules(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	schedules, err := h.scheduleUseCase.GetUpcomingSchedules(c.Request.Context(), userID.(string), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get upcoming schedules")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get upcoming schedules"})
		return
	}

	c.JSON(http.StatusOK, schedules)
}

// GetScheduleAnalytics gets schedule analytics
func (h *ScheduleHandler) GetScheduleAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	scheduleID := c.Query("schedule_id")
	workspaceID := c.Query("workspace_id")

	req := &models.ScheduleAnalyticsRequest{
		UserID:      userID.(string),
		Period:      period,
		ScheduleID:  scheduleID,
		WorkspaceID: workspaceID,
	}

	analytics, err := h.scheduleUseCase.GetScheduleAnalytics(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get schedule analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule analytics"})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// BulkOperations handles bulk operations on schedules
func (h *ScheduleHandler) BulkOperations(c *gin.Context) {
	var req struct {
		ScheduleIDs []string `json:"schedule_ids" binding:"required"`
		Operation   string   `json:"operation" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind bulk operation request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var result *models.BulkOperationResult
	var err error

	switch req.Operation {
	case "enable":
		result, err = h.scheduleUseCase.BulkEnableSchedules(c.Request.Context(), req.ScheduleIDs, userID.(string))
	case "disable":
		result, err = h.scheduleUseCase.BulkDisableSchedules(c.Request.Context(), req.ScheduleIDs, userID.(string))
	case "delete":
		result, err = h.scheduleUseCase.BulkDeleteSchedules(c.Request.Context(), req.ScheduleIDs, userID.(string))
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid operation"})
		return
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to perform bulk operation")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to perform bulk operation"})
		return
	}

	c.JSON(http.StatusOK, result)
}

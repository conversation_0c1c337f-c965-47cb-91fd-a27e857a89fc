// Code generated by ent, DO NOT EDIT.

package workspace

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the workspace type in the database.
	Label = "workspace"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldOwnerID holds the string denoting the owner_id field in the database.
	FieldOwnerID = "owner_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldColor holds the string denoting the color field in the database.
	FieldColor = "color"
	// FieldIcon holds the string denoting the icon field in the database.
	FieldIcon = "icon"
	// FieldSettings holds the string denoting the settings field in the database.
	FieldSettings = "settings"
	// FieldDefaultPlatforms holds the string denoting the default_platforms field in the database.
	FieldDefaultPlatforms = "default_platforms"
	// FieldBrandGuidelines holds the string denoting the brand_guidelines field in the database.
	FieldBrandGuidelines = "brand_guidelines"
	// FieldIsPersonal holds the string denoting the is_personal field in the database.
	FieldIsPersonal = "is_personal"
	// FieldIsActive holds the string denoting the is_active field in the database.
	FieldIsActive = "is_active"
	// FieldPostCount holds the string denoting the post_count field in the database.
	FieldPostCount = "post_count"
	// FieldTemplateCount holds the string denoting the template_count field in the database.
	FieldTemplateCount = "template_count"
	// FieldLastActivityAt holds the string denoting the last_activity_at field in the database.
	FieldLastActivityAt = "last_activity_at"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the workspace in the database.
	Table = "workspaces"
)

// Columns holds all SQL columns for workspace fields.
var Columns = []string{
	FieldID,
	FieldOwnerID,
	FieldName,
	FieldDescription,
	FieldColor,
	FieldIcon,
	FieldSettings,
	FieldDefaultPlatforms,
	FieldBrandGuidelines,
	FieldIsPersonal,
	FieldIsActive,
	FieldPostCount,
	FieldTemplateCount,
	FieldLastActivityAt,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultColor holds the default value on creation for the "color" field.
	DefaultColor string
	// ColorValidator is a validator for the "color" field. It is called by the builders before save.
	ColorValidator func(string) error
	// IconValidator is a validator for the "icon" field. It is called by the builders before save.
	IconValidator func(string) error
	// DefaultIsPersonal holds the default value on creation for the "is_personal" field.
	DefaultIsPersonal bool
	// DefaultIsActive holds the default value on creation for the "is_active" field.
	DefaultIsActive bool
	// DefaultPostCount holds the default value on creation for the "post_count" field.
	DefaultPostCount int
	// DefaultTemplateCount holds the default value on creation for the "template_count" field.
	DefaultTemplateCount int
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the Workspace queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByOwnerID orders the results by the owner_id field.
func ByOwnerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOwnerID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByColor orders the results by the color field.
func ByColor(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldColor, opts...).ToFunc()
}

// ByIcon orders the results by the icon field.
func ByIcon(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcon, opts...).ToFunc()
}

// ByIsPersonal orders the results by the is_personal field.
func ByIsPersonal(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPersonal, opts...).ToFunc()
}

// ByIsActive orders the results by the is_active field.
func ByIsActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsActive, opts...).ToFunc()
}

// ByPostCount orders the results by the post_count field.
func ByPostCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPostCount, opts...).ToFunc()
}

// ByTemplateCount orders the results by the template_count field.
func ByTemplateCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTemplateCount, opts...).ToFunc()
}

// ByLastActivityAt orders the results by the last_activity_at field.
func ByLastActivityAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastActivityAt, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditplan"
)

// CreditPlan is the model entity for the CreditPlan schema.
type CreditPlan struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// DisplayName holds the value of the "display_name" field.
	DisplayName string `json:"display_name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Credits holds the value of the "credits" field.
	Credits int `json:"credits,omitempty"`
	// Price holds the value of the "price" field.
	Price int64 `json:"price,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// DiscountPercentage holds the value of the "discount_percentage" field.
	DiscountPercentage int `json:"discount_percentage,omitempty"`
	// IsPopular holds the value of the "is_popular" field.
	IsPopular bool `json:"is_popular,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// Features holds the value of the "features" field.
	Features []string `json:"features,omitempty"`
	// ValidUntil holds the value of the "valid_until" field.
	ValidUntil time.Time `json:"valid_until,omitempty"`
	// SortOrder holds the value of the "sort_order" field.
	SortOrder int `json:"sort_order,omitempty"`
	// StripePriceID holds the value of the "stripe_price_id" field.
	StripePriceID string `json:"stripe_price_id,omitempty"`
	// StripeProductID holds the value of the "stripe_product_id" field.
	StripeProductID string `json:"stripe_product_id,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CreditPlan) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case creditplan.FieldFeatures, creditplan.FieldMetadata:
			values[i] = new([]byte)
		case creditplan.FieldIsPopular, creditplan.FieldIsActive:
			values[i] = new(sql.NullBool)
		case creditplan.FieldCredits, creditplan.FieldPrice, creditplan.FieldDiscountPercentage, creditplan.FieldSortOrder:
			values[i] = new(sql.NullInt64)
		case creditplan.FieldName, creditplan.FieldDisplayName, creditplan.FieldDescription, creditplan.FieldCurrency, creditplan.FieldStripePriceID, creditplan.FieldStripeProductID:
			values[i] = new(sql.NullString)
		case creditplan.FieldValidUntil, creditplan.FieldCreatedAt, creditplan.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case creditplan.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CreditPlan fields.
func (cp *CreditPlan) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case creditplan.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				cp.ID = *value
			}
		case creditplan.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				cp.Name = value.String
			}
		case creditplan.FieldDisplayName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field display_name", values[i])
			} else if value.Valid {
				cp.DisplayName = value.String
			}
		case creditplan.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				cp.Description = value.String
			}
		case creditplan.FieldCredits:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field credits", values[i])
			} else if value.Valid {
				cp.Credits = int(value.Int64)
			}
		case creditplan.FieldPrice:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field price", values[i])
			} else if value.Valid {
				cp.Price = value.Int64
			}
		case creditplan.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				cp.Currency = value.String
			}
		case creditplan.FieldDiscountPercentage:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field discount_percentage", values[i])
			} else if value.Valid {
				cp.DiscountPercentage = int(value.Int64)
			}
		case creditplan.FieldIsPopular:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_popular", values[i])
			} else if value.Valid {
				cp.IsPopular = value.Bool
			}
		case creditplan.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				cp.IsActive = value.Bool
			}
		case creditplan.FieldFeatures:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field features", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &cp.Features); err != nil {
					return fmt.Errorf("unmarshal field features: %w", err)
				}
			}
		case creditplan.FieldValidUntil:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field valid_until", values[i])
			} else if value.Valid {
				cp.ValidUntil = value.Time
			}
		case creditplan.FieldSortOrder:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort_order", values[i])
			} else if value.Valid {
				cp.SortOrder = int(value.Int64)
			}
		case creditplan.FieldStripePriceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field stripe_price_id", values[i])
			} else if value.Valid {
				cp.StripePriceID = value.String
			}
		case creditplan.FieldStripeProductID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field stripe_product_id", values[i])
			} else if value.Valid {
				cp.StripeProductID = value.String
			}
		case creditplan.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &cp.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case creditplan.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cp.CreatedAt = value.Time
			}
		case creditplan.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				cp.UpdatedAt = value.Time
			}
		default:
			cp.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CreditPlan.
// This includes values selected through modifiers, order, etc.
func (cp *CreditPlan) Value(name string) (ent.Value, error) {
	return cp.selectValues.Get(name)
}

// Update returns a builder for updating this CreditPlan.
// Note that you need to call CreditPlan.Unwrap() before calling this method if this CreditPlan
// was returned from a transaction, and the transaction was committed or rolled back.
func (cp *CreditPlan) Update() *CreditPlanUpdateOne {
	return NewCreditPlanClient(cp.config).UpdateOne(cp)
}

// Unwrap unwraps the CreditPlan entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cp *CreditPlan) Unwrap() *CreditPlan {
	_tx, ok := cp.config.driver.(*txDriver)
	if !ok {
		panic("ent: CreditPlan is not a transactional entity")
	}
	cp.config.driver = _tx.drv
	return cp
}

// String implements the fmt.Stringer.
func (cp *CreditPlan) String() string {
	var builder strings.Builder
	builder.WriteString("CreditPlan(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cp.ID))
	builder.WriteString("name=")
	builder.WriteString(cp.Name)
	builder.WriteString(", ")
	builder.WriteString("display_name=")
	builder.WriteString(cp.DisplayName)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(cp.Description)
	builder.WriteString(", ")
	builder.WriteString("credits=")
	builder.WriteString(fmt.Sprintf("%v", cp.Credits))
	builder.WriteString(", ")
	builder.WriteString("price=")
	builder.WriteString(fmt.Sprintf("%v", cp.Price))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(cp.Currency)
	builder.WriteString(", ")
	builder.WriteString("discount_percentage=")
	builder.WriteString(fmt.Sprintf("%v", cp.DiscountPercentage))
	builder.WriteString(", ")
	builder.WriteString("is_popular=")
	builder.WriteString(fmt.Sprintf("%v", cp.IsPopular))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", cp.IsActive))
	builder.WriteString(", ")
	builder.WriteString("features=")
	builder.WriteString(fmt.Sprintf("%v", cp.Features))
	builder.WriteString(", ")
	builder.WriteString("valid_until=")
	builder.WriteString(cp.ValidUntil.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sort_order=")
	builder.WriteString(fmt.Sprintf("%v", cp.SortOrder))
	builder.WriteString(", ")
	builder.WriteString("stripe_price_id=")
	builder.WriteString(cp.StripePriceID)
	builder.WriteString(", ")
	builder.WriteString("stripe_product_id=")
	builder.WriteString(cp.StripeProductID)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", cp.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(cp.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(cp.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CreditPlans is a parsable slice of CreditPlan.
type CreditPlans []*CreditPlan

package google

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/social-content-ai/pkg-shared/logging"
)

// Client represents Google AI API client
type Client struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     logging.Logger
}

// NewClient creates a new Google AI client
func NewClient(apiKey, baseURL string, logger logging.Logger) *Client {
	if baseURL == "" {
		baseURL = "https://generativelanguage.googleapis.com"
	}

	return &Client{
		apiKey:  apiKey,
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// GenerateContentRequest represents content generation request
type GenerateContentRequest struct {
	Contents         []Content         `json:"contents"`
	GenerationConfig *GenerationConfig `json:"generationConfig,omitempty"`
	SafetySettings   []SafetySetting   `json:"safetySettings,omitempty"`
}

// Content represents request content
type Content struct {
	Parts []Part `json:"parts"`
	Role  string `json:"role,omitempty"`
}

// Part represents content part
type Part struct {
	Text string `json:"text"`
}

// GenerationConfig represents generation configuration
type GenerationConfig struct {
	Temperature     float64  `json:"temperature,omitempty"`
	TopP            float64  `json:"topP,omitempty"`
	TopK            int      `json:"topK,omitempty"`
	MaxOutputTokens int      `json:"maxOutputTokens,omitempty"`
	StopSequences   []string `json:"stopSequences,omitempty"`
}

// SafetySetting represents safety configuration
type SafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// GenerateContentResponse represents content generation response
type GenerateContentResponse struct {
	Candidates     []Candidate     `json:"candidates"`
	PromptFeedback *PromptFeedback `json:"promptFeedback,omitempty"`
	UsageMetadata  *UsageMetadata  `json:"usageMetadata,omitempty"`
}

// Candidate represents response candidate
type Candidate struct {
	Content       Content        `json:"content"`
	FinishReason  string         `json:"finishReason"`
	Index         int            `json:"index"`
	SafetyRatings []SafetyRating `json:"safetyRatings,omitempty"`
}

// SafetyRating represents safety rating
type SafetyRating struct {
	Category    string `json:"category"`
	Probability string `json:"probability"`
}

// PromptFeedback represents prompt feedback
type PromptFeedback struct {
	SafetyRatings []SafetyRating `json:"safetyRatings,omitempty"`
	BlockReason   string         `json:"blockReason,omitempty"`
}

// UsageMetadata represents usage metadata
type UsageMetadata struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// ErrorResponse represents API error response
type ErrorResponse struct {
	Error struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Status  string `json:"status"`
	} `json:"error"`
}

// GenerateContent generates content using Google AI API
func (c *Client) GenerateContent(ctx context.Context, req *GenerateContentRequest, model string) (*GenerateContentResponse, error) {
	// Set default model if not specified
	if model == "" {
		model = "gemini-1.5-flash"
	}

	// Set default generation config if not specified
	if req.GenerationConfig == nil {
		req.GenerationConfig = &GenerationConfig{
			Temperature:     0.7,
			TopP:            0.8,
			TopK:            40,
			MaxOutputTokens: 1000,
		}
	}

	// Set default safety settings if not specified
	if req.SafetySettings == nil {
		req.SafetySettings = []SafetySetting{
			{
				Category:  "HARM_CATEGORY_HARASSMENT",
				Threshold: "BLOCK_MEDIUM_AND_ABOVE",
			},
			{
				Category:  "HARM_CATEGORY_HATE_SPEECH",
				Threshold: "BLOCK_MEDIUM_AND_ABOVE",
			},
			{
				Category:  "HARM_CATEGORY_SEXUALLY_EXPLICIT",
				Threshold: "BLOCK_MEDIUM_AND_ABOVE",
			},
			{
				Category:  "HARM_CATEGORY_DANGEROUS_CONTENT",
				Threshold: "BLOCK_MEDIUM_AND_ABOVE",
			},
		}
	}

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/v1beta/models/%s:generateContent?key=%s", c.baseURL, model, c.apiKey)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")

	// Log request
	c.logger.WithFields(map[string]interface{}{
		"model":            model,
		"contents":         len(req.Contents),
		"max_tokens":       req.GenerationConfig.MaxOutputTokens,
		"temperature":      req.GenerationConfig.Temperature,
	}).Debug("Making Google AI API request")

	// Make request
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(respBody))
		}
		return nil, fmt.Errorf("Google AI API error: %s", errResp.Error.Message)
	}

	// Parse successful response
	var result GenerateContentResponse
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Log response
	if result.UsageMetadata != nil {
		c.logger.WithFields(map[string]interface{}{
			"model":              model,
			"candidates":         len(result.Candidates),
			"prompt_tokens":      result.UsageMetadata.PromptTokenCount,
			"candidates_tokens":  result.UsageMetadata.CandidatesTokenCount,
			"total_tokens":       result.UsageMetadata.TotalTokenCount,
		}).Debug("Received Google AI API response")
	}

	return &result, nil
}

// ImproveContent improves existing content
func (c *Client) ImproveContent(ctx context.Context, content, instructions string) (*GenerateContentResponse, error) {
	prompt := fmt.Sprintf(`You are an expert content writer and editor. Your task is to improve the given content based on the provided instructions.

Focus on:
- Clarity and readability
- Engagement and appeal
- Grammar and style
- SEO optimization when relevant
- Platform-specific best practices

Maintain the original tone and intent while making improvements. Return only the improved content without explanations.

Content to improve:
%s

Instructions: %s`, content, instructions)

	req := &GenerateContentRequest{
		Contents: []Content{
			{
				Parts: []Part{
					{Text: prompt},
				},
			},
		},
		GenerationConfig: &GenerationConfig{
			Temperature:     0.7,
			MaxOutputTokens: 1500,
		},
	}

	return c.GenerateContent(ctx, req, "gemini-1.5-flash")
}

// GenerateHashtags generates hashtags for content
func (c *Client) GenerateHashtags(ctx context.Context, content string, platform string, count int) ([]string, error) {
	if count <= 0 {
		count = 10
	}

	prompt := fmt.Sprintf(`You are a social media expert. Generate %d relevant hashtags for the given content and platform.

Rules:
- Generate hashtags that are relevant and trending
- Consider platform-specific best practices
- Mix popular and niche hashtags
- Avoid banned or problematic hashtags
- Return only the hashtags, one per line, without the # symbol
- No explanations or additional text

Platform: %s
Content: %s`, count, platform, content)

	req := &GenerateContentRequest{
		Contents: []Content{
			{
				Parts: []Part{
					{Text: prompt},
				},
			},
		},
		GenerationConfig: &GenerationConfig{
			Temperature:     0.8,
			MaxOutputTokens: 300,
		},
	}

	resp, err := c.GenerateContent(ctx, req, "gemini-1.5-flash")
	if err != nil {
		return nil, err
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no hashtags generated")
	}

	// Parse hashtags from response
	hashtagsText := resp.Candidates[0].Content.Parts[0].Text
	hashtags := parseHashtags(hashtagsText)

	return hashtags, nil
}

// GenerateCaption generates social media caption
func (c *Client) GenerateCaption(ctx context.Context, content, platform, tone string) (string, error) {
	prompt := fmt.Sprintf(`You are a social media expert specializing in %s content creation.

Create an engaging caption that:
- Matches the %s tone
- Is optimized for %s platform
- Includes relevant emojis where appropriate
- Encourages engagement
- Follows platform best practices

Return only the caption without explanations.

Content: %s`, platform, tone, platform, content)

	req := &GenerateContentRequest{
		Contents: []Content{
			{
				Parts: []Part{
					{Text: prompt},
				},
			},
		},
		GenerationConfig: &GenerationConfig{
			Temperature:     0.8,
			MaxOutputTokens: 500,
		},
	}

	resp, err := c.GenerateContent(ctx, req, "gemini-1.5-flash")
	if err != nil {
		return "", err
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no caption generated")
	}

	return strings.TrimSpace(resp.Candidates[0].Content.Parts[0].Text), nil
}

// parseHashtags parses hashtags from text response
func parseHashtags(text string) []string {
	lines := strings.Split(strings.TrimSpace(text), "\n")
	var hashtags []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Remove # if present
			line = strings.TrimPrefix(line, "#")
			// Remove numbers and dots from beginning
			line = strings.TrimLeft(line, "**********. ")
			if line != "" {
				hashtags = append(hashtags, line)
			}
		}
	}

	return hashtags
}

// HealthCheck checks if the Google AI API is accessible
func (c *Client) HealthCheck(ctx context.Context) error {
	// Simple test request
	req := &GenerateContentRequest{
		Contents: []Content{
			{
				Parts: []Part{
					{Text: "Hello"},
				},
			},
		},
		GenerationConfig: &GenerationConfig{
			MaxOutputTokens: 10,
		},
	}

	_, err := c.GenerateContent(ctx, req, "gemini-1.5-flash")
	return err
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

// AssetUpdate is the builder for updating Asset entities.
type AssetUpdate struct {
	config
	hooks    []Hook
	mutation *AssetMutation
}

// Where appends a list predicates to the AssetUpdate builder.
func (au *AssetUpdate) Where(ps ...predicate.Asset) *AssetUpdate {
	au.mutation.Where(ps...)
	return au
}

// SetUserID sets the "user_id" field.
func (au *AssetUpdate) SetUserID(u uuid.UUID) *AssetUpdate {
	au.mutation.SetUserID(u)
	return au
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (au *AssetUpdate) SetNillableUserID(u *uuid.UUID) *AssetUpdate {
	if u != nil {
		au.SetUserID(*u)
	}
	return au
}

// SetWorkspaceID sets the "workspace_id" field.
func (au *AssetUpdate) SetWorkspaceID(s string) *AssetUpdate {
	au.mutation.SetWorkspaceID(s)
	return au
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (au *AssetUpdate) SetNillableWorkspaceID(s *string) *AssetUpdate {
	if s != nil {
		au.SetWorkspaceID(*s)
	}
	return au
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (au *AssetUpdate) ClearWorkspaceID() *AssetUpdate {
	au.mutation.ClearWorkspaceID()
	return au
}

// SetFolderID sets the "folder_id" field.
func (au *AssetUpdate) SetFolderID(u uuid.UUID) *AssetUpdate {
	au.mutation.SetFolderID(u)
	return au
}

// SetNillableFolderID sets the "folder_id" field if the given value is not nil.
func (au *AssetUpdate) SetNillableFolderID(u *uuid.UUID) *AssetUpdate {
	if u != nil {
		au.SetFolderID(*u)
	}
	return au
}

// ClearFolderID clears the value of the "folder_id" field.
func (au *AssetUpdate) ClearFolderID() *AssetUpdate {
	au.mutation.ClearFolderID()
	return au
}

// SetFileName sets the "file_name" field.
func (au *AssetUpdate) SetFileName(s string) *AssetUpdate {
	au.mutation.SetFileName(s)
	return au
}

// SetNillableFileName sets the "file_name" field if the given value is not nil.
func (au *AssetUpdate) SetNillableFileName(s *string) *AssetUpdate {
	if s != nil {
		au.SetFileName(*s)
	}
	return au
}

// SetOriginalName sets the "original_name" field.
func (au *AssetUpdate) SetOriginalName(s string) *AssetUpdate {
	au.mutation.SetOriginalName(s)
	return au
}

// SetNillableOriginalName sets the "original_name" field if the given value is not nil.
func (au *AssetUpdate) SetNillableOriginalName(s *string) *AssetUpdate {
	if s != nil {
		au.SetOriginalName(*s)
	}
	return au
}

// SetFileType sets the "file_type" field.
func (au *AssetUpdate) SetFileType(s string) *AssetUpdate {
	au.mutation.SetFileType(s)
	return au
}

// SetNillableFileType sets the "file_type" field if the given value is not nil.
func (au *AssetUpdate) SetNillableFileType(s *string) *AssetUpdate {
	if s != nil {
		au.SetFileType(*s)
	}
	return au
}

// SetFileExtension sets the "file_extension" field.
func (au *AssetUpdate) SetFileExtension(s string) *AssetUpdate {
	au.mutation.SetFileExtension(s)
	return au
}

// SetNillableFileExtension sets the "file_extension" field if the given value is not nil.
func (au *AssetUpdate) SetNillableFileExtension(s *string) *AssetUpdate {
	if s != nil {
		au.SetFileExtension(*s)
	}
	return au
}

// ClearFileExtension clears the value of the "file_extension" field.
func (au *AssetUpdate) ClearFileExtension() *AssetUpdate {
	au.mutation.ClearFileExtension()
	return au
}

// SetS3Key sets the "s3_key" field.
func (au *AssetUpdate) SetS3Key(s string) *AssetUpdate {
	au.mutation.SetS3Key(s)
	return au
}

// SetNillableS3Key sets the "s3_key" field if the given value is not nil.
func (au *AssetUpdate) SetNillableS3Key(s *string) *AssetUpdate {
	if s != nil {
		au.SetS3Key(*s)
	}
	return au
}

// SetS3Bucket sets the "s3_bucket" field.
func (au *AssetUpdate) SetS3Bucket(s string) *AssetUpdate {
	au.mutation.SetS3Bucket(s)
	return au
}

// SetNillableS3Bucket sets the "s3_bucket" field if the given value is not nil.
func (au *AssetUpdate) SetNillableS3Bucket(s *string) *AssetUpdate {
	if s != nil {
		au.SetS3Bucket(*s)
	}
	return au
}

// SetContentHash sets the "content_hash" field.
func (au *AssetUpdate) SetContentHash(s string) *AssetUpdate {
	au.mutation.SetContentHash(s)
	return au
}

// SetNillableContentHash sets the "content_hash" field if the given value is not nil.
func (au *AssetUpdate) SetNillableContentHash(s *string) *AssetUpdate {
	if s != nil {
		au.SetContentHash(*s)
	}
	return au
}

// ClearContentHash clears the value of the "content_hash" field.
func (au *AssetUpdate) ClearContentHash() *AssetUpdate {
	au.mutation.ClearContentHash()
	return au
}

// SetPurpose sets the "purpose" field.
func (au *AssetUpdate) SetPurpose(s string) *AssetUpdate {
	au.mutation.SetPurpose(s)
	return au
}

// SetNillablePurpose sets the "purpose" field if the given value is not nil.
func (au *AssetUpdate) SetNillablePurpose(s *string) *AssetUpdate {
	if s != nil {
		au.SetPurpose(*s)
	}
	return au
}

// SetFileSize sets the "file_size" field.
func (au *AssetUpdate) SetFileSize(i int64) *AssetUpdate {
	au.mutation.ResetFileSize()
	au.mutation.SetFileSize(i)
	return au
}

// SetNillableFileSize sets the "file_size" field if the given value is not nil.
func (au *AssetUpdate) SetNillableFileSize(i *int64) *AssetUpdate {
	if i != nil {
		au.SetFileSize(*i)
	}
	return au
}

// AddFileSize adds i to the "file_size" field.
func (au *AssetUpdate) AddFileSize(i int64) *AssetUpdate {
	au.mutation.AddFileSize(i)
	return au
}

// SetThumbnailURL sets the "thumbnail_url" field.
func (au *AssetUpdate) SetThumbnailURL(s string) *AssetUpdate {
	au.mutation.SetThumbnailURL(s)
	return au
}

// SetNillableThumbnailURL sets the "thumbnail_url" field if the given value is not nil.
func (au *AssetUpdate) SetNillableThumbnailURL(s *string) *AssetUpdate {
	if s != nil {
		au.SetThumbnailURL(*s)
	}
	return au
}

// ClearThumbnailURL clears the value of the "thumbnail_url" field.
func (au *AssetUpdate) ClearThumbnailURL() *AssetUpdate {
	au.mutation.ClearThumbnailURL()
	return au
}

// SetMetadata sets the "metadata" field.
func (au *AssetUpdate) SetMetadata(m map[string]interface{}) *AssetUpdate {
	au.mutation.SetMetadata(m)
	return au
}

// ClearMetadata clears the value of the "metadata" field.
func (au *AssetUpdate) ClearMetadata() *AssetUpdate {
	au.mutation.ClearMetadata()
	return au
}

// SetStatus sets the "status" field.
func (au *AssetUpdate) SetStatus(s string) *AssetUpdate {
	au.mutation.SetStatus(s)
	return au
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (au *AssetUpdate) SetNillableStatus(s *string) *AssetUpdate {
	if s != nil {
		au.SetStatus(*s)
	}
	return au
}

// SetIsPublic sets the "is_public" field.
func (au *AssetUpdate) SetIsPublic(b bool) *AssetUpdate {
	au.mutation.SetIsPublic(b)
	return au
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (au *AssetUpdate) SetNillableIsPublic(b *bool) *AssetUpdate {
	if b != nil {
		au.SetIsPublic(*b)
	}
	return au
}

// SetUpdatedAt sets the "updated_at" field.
func (au *AssetUpdate) SetUpdatedAt(t time.Time) *AssetUpdate {
	au.mutation.SetUpdatedAt(t)
	return au
}

// SetDeletedAt sets the "deleted_at" field.
func (au *AssetUpdate) SetDeletedAt(t time.Time) *AssetUpdate {
	au.mutation.SetDeletedAt(t)
	return au
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (au *AssetUpdate) SetNillableDeletedAt(t *time.Time) *AssetUpdate {
	if t != nil {
		au.SetDeletedAt(*t)
	}
	return au
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (au *AssetUpdate) ClearDeletedAt() *AssetUpdate {
	au.mutation.ClearDeletedAt()
	return au
}

// SetFolder sets the "folder" edge to the Folder entity.
func (au *AssetUpdate) SetFolder(f *Folder) *AssetUpdate {
	return au.SetFolderID(f.ID)
}

// Mutation returns the AssetMutation object of the builder.
func (au *AssetUpdate) Mutation() *AssetMutation {
	return au.mutation
}

// ClearFolder clears the "folder" edge to the Folder entity.
func (au *AssetUpdate) ClearFolder() *AssetUpdate {
	au.mutation.ClearFolder()
	return au
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (au *AssetUpdate) Save(ctx context.Context) (int, error) {
	au.defaults()
	return withHooks(ctx, au.sqlSave, au.mutation, au.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (au *AssetUpdate) SaveX(ctx context.Context) int {
	affected, err := au.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (au *AssetUpdate) Exec(ctx context.Context) error {
	_, err := au.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (au *AssetUpdate) ExecX(ctx context.Context) {
	if err := au.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (au *AssetUpdate) defaults() {
	if _, ok := au.mutation.UpdatedAt(); !ok {
		v := asset.UpdateDefaultUpdatedAt()
		au.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (au *AssetUpdate) check() error {
	if v, ok := au.mutation.WorkspaceID(); ok {
		if err := asset.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Asset.workspace_id": %w`, err)}
		}
	}
	if v, ok := au.mutation.FileName(); ok {
		if err := asset.FileNameValidator(v); err != nil {
			return &ValidationError{Name: "file_name", err: fmt.Errorf(`ent: validator failed for field "Asset.file_name": %w`, err)}
		}
	}
	if v, ok := au.mutation.OriginalName(); ok {
		if err := asset.OriginalNameValidator(v); err != nil {
			return &ValidationError{Name: "original_name", err: fmt.Errorf(`ent: validator failed for field "Asset.original_name": %w`, err)}
		}
	}
	if v, ok := au.mutation.FileType(); ok {
		if err := asset.FileTypeValidator(v); err != nil {
			return &ValidationError{Name: "file_type", err: fmt.Errorf(`ent: validator failed for field "Asset.file_type": %w`, err)}
		}
	}
	if v, ok := au.mutation.FileExtension(); ok {
		if err := asset.FileExtensionValidator(v); err != nil {
			return &ValidationError{Name: "file_extension", err: fmt.Errorf(`ent: validator failed for field "Asset.file_extension": %w`, err)}
		}
	}
	if v, ok := au.mutation.S3Key(); ok {
		if err := asset.S3KeyValidator(v); err != nil {
			return &ValidationError{Name: "s3_key", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_key": %w`, err)}
		}
	}
	if v, ok := au.mutation.S3Bucket(); ok {
		if err := asset.S3BucketValidator(v); err != nil {
			return &ValidationError{Name: "s3_bucket", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_bucket": %w`, err)}
		}
	}
	if v, ok := au.mutation.ContentHash(); ok {
		if err := asset.ContentHashValidator(v); err != nil {
			return &ValidationError{Name: "content_hash", err: fmt.Errorf(`ent: validator failed for field "Asset.content_hash": %w`, err)}
		}
	}
	if v, ok := au.mutation.Purpose(); ok {
		if err := asset.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "Asset.purpose": %w`, err)}
		}
	}
	if v, ok := au.mutation.ThumbnailURL(); ok {
		if err := asset.ThumbnailURLValidator(v); err != nil {
			return &ValidationError{Name: "thumbnail_url", err: fmt.Errorf(`ent: validator failed for field "Asset.thumbnail_url": %w`, err)}
		}
	}
	if v, ok := au.mutation.Status(); ok {
		if err := asset.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Asset.status": %w`, err)}
		}
	}
	return nil
}

func (au *AssetUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := au.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(asset.Table, asset.Columns, sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID))
	if ps := au.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := au.mutation.UserID(); ok {
		_spec.SetField(asset.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := au.mutation.WorkspaceID(); ok {
		_spec.SetField(asset.FieldWorkspaceID, field.TypeString, value)
	}
	if au.mutation.WorkspaceIDCleared() {
		_spec.ClearField(asset.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := au.mutation.FileName(); ok {
		_spec.SetField(asset.FieldFileName, field.TypeString, value)
	}
	if value, ok := au.mutation.OriginalName(); ok {
		_spec.SetField(asset.FieldOriginalName, field.TypeString, value)
	}
	if value, ok := au.mutation.FileType(); ok {
		_spec.SetField(asset.FieldFileType, field.TypeString, value)
	}
	if value, ok := au.mutation.FileExtension(); ok {
		_spec.SetField(asset.FieldFileExtension, field.TypeString, value)
	}
	if au.mutation.FileExtensionCleared() {
		_spec.ClearField(asset.FieldFileExtension, field.TypeString)
	}
	if value, ok := au.mutation.S3Key(); ok {
		_spec.SetField(asset.FieldS3Key, field.TypeString, value)
	}
	if value, ok := au.mutation.S3Bucket(); ok {
		_spec.SetField(asset.FieldS3Bucket, field.TypeString, value)
	}
	if value, ok := au.mutation.ContentHash(); ok {
		_spec.SetField(asset.FieldContentHash, field.TypeString, value)
	}
	if au.mutation.ContentHashCleared() {
		_spec.ClearField(asset.FieldContentHash, field.TypeString)
	}
	if value, ok := au.mutation.Purpose(); ok {
		_spec.SetField(asset.FieldPurpose, field.TypeString, value)
	}
	if value, ok := au.mutation.FileSize(); ok {
		_spec.SetField(asset.FieldFileSize, field.TypeInt64, value)
	}
	if value, ok := au.mutation.AddedFileSize(); ok {
		_spec.AddField(asset.FieldFileSize, field.TypeInt64, value)
	}
	if value, ok := au.mutation.ThumbnailURL(); ok {
		_spec.SetField(asset.FieldThumbnailURL, field.TypeString, value)
	}
	if au.mutation.ThumbnailURLCleared() {
		_spec.ClearField(asset.FieldThumbnailURL, field.TypeString)
	}
	if value, ok := au.mutation.Metadata(); ok {
		_spec.SetField(asset.FieldMetadata, field.TypeJSON, value)
	}
	if au.mutation.MetadataCleared() {
		_spec.ClearField(asset.FieldMetadata, field.TypeJSON)
	}
	if value, ok := au.mutation.Status(); ok {
		_spec.SetField(asset.FieldStatus, field.TypeString, value)
	}
	if value, ok := au.mutation.IsPublic(); ok {
		_spec.SetField(asset.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := au.mutation.UpdatedAt(); ok {
		_spec.SetField(asset.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := au.mutation.DeletedAt(); ok {
		_spec.SetField(asset.FieldDeletedAt, field.TypeTime, value)
	}
	if au.mutation.DeletedAtCleared() {
		_spec.ClearField(asset.FieldDeletedAt, field.TypeTime)
	}
	if au.mutation.FolderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   asset.FolderTable,
			Columns: []string{asset.FolderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.FolderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   asset.FolderTable,
			Columns: []string{asset.FolderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, au.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{asset.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	au.mutation.done = true
	return n, nil
}

// AssetUpdateOne is the builder for updating a single Asset entity.
type AssetUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AssetMutation
}

// SetUserID sets the "user_id" field.
func (auo *AssetUpdateOne) SetUserID(u uuid.UUID) *AssetUpdateOne {
	auo.mutation.SetUserID(u)
	return auo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableUserID(u *uuid.UUID) *AssetUpdateOne {
	if u != nil {
		auo.SetUserID(*u)
	}
	return auo
}

// SetWorkspaceID sets the "workspace_id" field.
func (auo *AssetUpdateOne) SetWorkspaceID(s string) *AssetUpdateOne {
	auo.mutation.SetWorkspaceID(s)
	return auo
}

// SetNillableWorkspaceID sets the "workspace_id" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableWorkspaceID(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetWorkspaceID(*s)
	}
	return auo
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (auo *AssetUpdateOne) ClearWorkspaceID() *AssetUpdateOne {
	auo.mutation.ClearWorkspaceID()
	return auo
}

// SetFolderID sets the "folder_id" field.
func (auo *AssetUpdateOne) SetFolderID(u uuid.UUID) *AssetUpdateOne {
	auo.mutation.SetFolderID(u)
	return auo
}

// SetNillableFolderID sets the "folder_id" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableFolderID(u *uuid.UUID) *AssetUpdateOne {
	if u != nil {
		auo.SetFolderID(*u)
	}
	return auo
}

// ClearFolderID clears the value of the "folder_id" field.
func (auo *AssetUpdateOne) ClearFolderID() *AssetUpdateOne {
	auo.mutation.ClearFolderID()
	return auo
}

// SetFileName sets the "file_name" field.
func (auo *AssetUpdateOne) SetFileName(s string) *AssetUpdateOne {
	auo.mutation.SetFileName(s)
	return auo
}

// SetNillableFileName sets the "file_name" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableFileName(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetFileName(*s)
	}
	return auo
}

// SetOriginalName sets the "original_name" field.
func (auo *AssetUpdateOne) SetOriginalName(s string) *AssetUpdateOne {
	auo.mutation.SetOriginalName(s)
	return auo
}

// SetNillableOriginalName sets the "original_name" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableOriginalName(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetOriginalName(*s)
	}
	return auo
}

// SetFileType sets the "file_type" field.
func (auo *AssetUpdateOne) SetFileType(s string) *AssetUpdateOne {
	auo.mutation.SetFileType(s)
	return auo
}

// SetNillableFileType sets the "file_type" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableFileType(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetFileType(*s)
	}
	return auo
}

// SetFileExtension sets the "file_extension" field.
func (auo *AssetUpdateOne) SetFileExtension(s string) *AssetUpdateOne {
	auo.mutation.SetFileExtension(s)
	return auo
}

// SetNillableFileExtension sets the "file_extension" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableFileExtension(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetFileExtension(*s)
	}
	return auo
}

// ClearFileExtension clears the value of the "file_extension" field.
func (auo *AssetUpdateOne) ClearFileExtension() *AssetUpdateOne {
	auo.mutation.ClearFileExtension()
	return auo
}

// SetS3Key sets the "s3_key" field.
func (auo *AssetUpdateOne) SetS3Key(s string) *AssetUpdateOne {
	auo.mutation.SetS3Key(s)
	return auo
}

// SetNillableS3Key sets the "s3_key" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableS3Key(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetS3Key(*s)
	}
	return auo
}

// SetS3Bucket sets the "s3_bucket" field.
func (auo *AssetUpdateOne) SetS3Bucket(s string) *AssetUpdateOne {
	auo.mutation.SetS3Bucket(s)
	return auo
}

// SetNillableS3Bucket sets the "s3_bucket" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableS3Bucket(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetS3Bucket(*s)
	}
	return auo
}

// SetContentHash sets the "content_hash" field.
func (auo *AssetUpdateOne) SetContentHash(s string) *AssetUpdateOne {
	auo.mutation.SetContentHash(s)
	return auo
}

// SetNillableContentHash sets the "content_hash" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableContentHash(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetContentHash(*s)
	}
	return auo
}

// ClearContentHash clears the value of the "content_hash" field.
func (auo *AssetUpdateOne) ClearContentHash() *AssetUpdateOne {
	auo.mutation.ClearContentHash()
	return auo
}

// SetPurpose sets the "purpose" field.
func (auo *AssetUpdateOne) SetPurpose(s string) *AssetUpdateOne {
	auo.mutation.SetPurpose(s)
	return auo
}

// SetNillablePurpose sets the "purpose" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillablePurpose(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetPurpose(*s)
	}
	return auo
}

// SetFileSize sets the "file_size" field.
func (auo *AssetUpdateOne) SetFileSize(i int64) *AssetUpdateOne {
	auo.mutation.ResetFileSize()
	auo.mutation.SetFileSize(i)
	return auo
}

// SetNillableFileSize sets the "file_size" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableFileSize(i *int64) *AssetUpdateOne {
	if i != nil {
		auo.SetFileSize(*i)
	}
	return auo
}

// AddFileSize adds i to the "file_size" field.
func (auo *AssetUpdateOne) AddFileSize(i int64) *AssetUpdateOne {
	auo.mutation.AddFileSize(i)
	return auo
}

// SetThumbnailURL sets the "thumbnail_url" field.
func (auo *AssetUpdateOne) SetThumbnailURL(s string) *AssetUpdateOne {
	auo.mutation.SetThumbnailURL(s)
	return auo
}

// SetNillableThumbnailURL sets the "thumbnail_url" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableThumbnailURL(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetThumbnailURL(*s)
	}
	return auo
}

// ClearThumbnailURL clears the value of the "thumbnail_url" field.
func (auo *AssetUpdateOne) ClearThumbnailURL() *AssetUpdateOne {
	auo.mutation.ClearThumbnailURL()
	return auo
}

// SetMetadata sets the "metadata" field.
func (auo *AssetUpdateOne) SetMetadata(m map[string]interface{}) *AssetUpdateOne {
	auo.mutation.SetMetadata(m)
	return auo
}

// ClearMetadata clears the value of the "metadata" field.
func (auo *AssetUpdateOne) ClearMetadata() *AssetUpdateOne {
	auo.mutation.ClearMetadata()
	return auo
}

// SetStatus sets the "status" field.
func (auo *AssetUpdateOne) SetStatus(s string) *AssetUpdateOne {
	auo.mutation.SetStatus(s)
	return auo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableStatus(s *string) *AssetUpdateOne {
	if s != nil {
		auo.SetStatus(*s)
	}
	return auo
}

// SetIsPublic sets the "is_public" field.
func (auo *AssetUpdateOne) SetIsPublic(b bool) *AssetUpdateOne {
	auo.mutation.SetIsPublic(b)
	return auo
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableIsPublic(b *bool) *AssetUpdateOne {
	if b != nil {
		auo.SetIsPublic(*b)
	}
	return auo
}

// SetUpdatedAt sets the "updated_at" field.
func (auo *AssetUpdateOne) SetUpdatedAt(t time.Time) *AssetUpdateOne {
	auo.mutation.SetUpdatedAt(t)
	return auo
}

// SetDeletedAt sets the "deleted_at" field.
func (auo *AssetUpdateOne) SetDeletedAt(t time.Time) *AssetUpdateOne {
	auo.mutation.SetDeletedAt(t)
	return auo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (auo *AssetUpdateOne) SetNillableDeletedAt(t *time.Time) *AssetUpdateOne {
	if t != nil {
		auo.SetDeletedAt(*t)
	}
	return auo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (auo *AssetUpdateOne) ClearDeletedAt() *AssetUpdateOne {
	auo.mutation.ClearDeletedAt()
	return auo
}

// SetFolder sets the "folder" edge to the Folder entity.
func (auo *AssetUpdateOne) SetFolder(f *Folder) *AssetUpdateOne {
	return auo.SetFolderID(f.ID)
}

// Mutation returns the AssetMutation object of the builder.
func (auo *AssetUpdateOne) Mutation() *AssetMutation {
	return auo.mutation
}

// ClearFolder clears the "folder" edge to the Folder entity.
func (auo *AssetUpdateOne) ClearFolder() *AssetUpdateOne {
	auo.mutation.ClearFolder()
	return auo
}

// Where appends a list predicates to the AssetUpdate builder.
func (auo *AssetUpdateOne) Where(ps ...predicate.Asset) *AssetUpdateOne {
	auo.mutation.Where(ps...)
	return auo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (auo *AssetUpdateOne) Select(field string, fields ...string) *AssetUpdateOne {
	auo.fields = append([]string{field}, fields...)
	return auo
}

// Save executes the query and returns the updated Asset entity.
func (auo *AssetUpdateOne) Save(ctx context.Context) (*Asset, error) {
	auo.defaults()
	return withHooks(ctx, auo.sqlSave, auo.mutation, auo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (auo *AssetUpdateOne) SaveX(ctx context.Context) *Asset {
	node, err := auo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (auo *AssetUpdateOne) Exec(ctx context.Context) error {
	_, err := auo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (auo *AssetUpdateOne) ExecX(ctx context.Context) {
	if err := auo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (auo *AssetUpdateOne) defaults() {
	if _, ok := auo.mutation.UpdatedAt(); !ok {
		v := asset.UpdateDefaultUpdatedAt()
		auo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (auo *AssetUpdateOne) check() error {
	if v, ok := auo.mutation.WorkspaceID(); ok {
		if err := asset.WorkspaceIDValidator(v); err != nil {
			return &ValidationError{Name: "workspace_id", err: fmt.Errorf(`ent: validator failed for field "Asset.workspace_id": %w`, err)}
		}
	}
	if v, ok := auo.mutation.FileName(); ok {
		if err := asset.FileNameValidator(v); err != nil {
			return &ValidationError{Name: "file_name", err: fmt.Errorf(`ent: validator failed for field "Asset.file_name": %w`, err)}
		}
	}
	if v, ok := auo.mutation.OriginalName(); ok {
		if err := asset.OriginalNameValidator(v); err != nil {
			return &ValidationError{Name: "original_name", err: fmt.Errorf(`ent: validator failed for field "Asset.original_name": %w`, err)}
		}
	}
	if v, ok := auo.mutation.FileType(); ok {
		if err := asset.FileTypeValidator(v); err != nil {
			return &ValidationError{Name: "file_type", err: fmt.Errorf(`ent: validator failed for field "Asset.file_type": %w`, err)}
		}
	}
	if v, ok := auo.mutation.FileExtension(); ok {
		if err := asset.FileExtensionValidator(v); err != nil {
			return &ValidationError{Name: "file_extension", err: fmt.Errorf(`ent: validator failed for field "Asset.file_extension": %w`, err)}
		}
	}
	if v, ok := auo.mutation.S3Key(); ok {
		if err := asset.S3KeyValidator(v); err != nil {
			return &ValidationError{Name: "s3_key", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_key": %w`, err)}
		}
	}
	if v, ok := auo.mutation.S3Bucket(); ok {
		if err := asset.S3BucketValidator(v); err != nil {
			return &ValidationError{Name: "s3_bucket", err: fmt.Errorf(`ent: validator failed for field "Asset.s3_bucket": %w`, err)}
		}
	}
	if v, ok := auo.mutation.ContentHash(); ok {
		if err := asset.ContentHashValidator(v); err != nil {
			return &ValidationError{Name: "content_hash", err: fmt.Errorf(`ent: validator failed for field "Asset.content_hash": %w`, err)}
		}
	}
	if v, ok := auo.mutation.Purpose(); ok {
		if err := asset.PurposeValidator(v); err != nil {
			return &ValidationError{Name: "purpose", err: fmt.Errorf(`ent: validator failed for field "Asset.purpose": %w`, err)}
		}
	}
	if v, ok := auo.mutation.ThumbnailURL(); ok {
		if err := asset.ThumbnailURLValidator(v); err != nil {
			return &ValidationError{Name: "thumbnail_url", err: fmt.Errorf(`ent: validator failed for field "Asset.thumbnail_url": %w`, err)}
		}
	}
	if v, ok := auo.mutation.Status(); ok {
		if err := asset.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Asset.status": %w`, err)}
		}
	}
	return nil
}

func (auo *AssetUpdateOne) sqlSave(ctx context.Context) (_node *Asset, err error) {
	if err := auo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(asset.Table, asset.Columns, sqlgraph.NewFieldSpec(asset.FieldID, field.TypeUUID))
	id, ok := auo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Asset.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := auo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, asset.FieldID)
		for _, f := range fields {
			if !asset.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != asset.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := auo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := auo.mutation.UserID(); ok {
		_spec.SetField(asset.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := auo.mutation.WorkspaceID(); ok {
		_spec.SetField(asset.FieldWorkspaceID, field.TypeString, value)
	}
	if auo.mutation.WorkspaceIDCleared() {
		_spec.ClearField(asset.FieldWorkspaceID, field.TypeString)
	}
	if value, ok := auo.mutation.FileName(); ok {
		_spec.SetField(asset.FieldFileName, field.TypeString, value)
	}
	if value, ok := auo.mutation.OriginalName(); ok {
		_spec.SetField(asset.FieldOriginalName, field.TypeString, value)
	}
	if value, ok := auo.mutation.FileType(); ok {
		_spec.SetField(asset.FieldFileType, field.TypeString, value)
	}
	if value, ok := auo.mutation.FileExtension(); ok {
		_spec.SetField(asset.FieldFileExtension, field.TypeString, value)
	}
	if auo.mutation.FileExtensionCleared() {
		_spec.ClearField(asset.FieldFileExtension, field.TypeString)
	}
	if value, ok := auo.mutation.S3Key(); ok {
		_spec.SetField(asset.FieldS3Key, field.TypeString, value)
	}
	if value, ok := auo.mutation.S3Bucket(); ok {
		_spec.SetField(asset.FieldS3Bucket, field.TypeString, value)
	}
	if value, ok := auo.mutation.ContentHash(); ok {
		_spec.SetField(asset.FieldContentHash, field.TypeString, value)
	}
	if auo.mutation.ContentHashCleared() {
		_spec.ClearField(asset.FieldContentHash, field.TypeString)
	}
	if value, ok := auo.mutation.Purpose(); ok {
		_spec.SetField(asset.FieldPurpose, field.TypeString, value)
	}
	if value, ok := auo.mutation.FileSize(); ok {
		_spec.SetField(asset.FieldFileSize, field.TypeInt64, value)
	}
	if value, ok := auo.mutation.AddedFileSize(); ok {
		_spec.AddField(asset.FieldFileSize, field.TypeInt64, value)
	}
	if value, ok := auo.mutation.ThumbnailURL(); ok {
		_spec.SetField(asset.FieldThumbnailURL, field.TypeString, value)
	}
	if auo.mutation.ThumbnailURLCleared() {
		_spec.ClearField(asset.FieldThumbnailURL, field.TypeString)
	}
	if value, ok := auo.mutation.Metadata(); ok {
		_spec.SetField(asset.FieldMetadata, field.TypeJSON, value)
	}
	if auo.mutation.MetadataCleared() {
		_spec.ClearField(asset.FieldMetadata, field.TypeJSON)
	}
	if value, ok := auo.mutation.Status(); ok {
		_spec.SetField(asset.FieldStatus, field.TypeString, value)
	}
	if value, ok := auo.mutation.IsPublic(); ok {
		_spec.SetField(asset.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := auo.mutation.UpdatedAt(); ok {
		_spec.SetField(asset.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := auo.mutation.DeletedAt(); ok {
		_spec.SetField(asset.FieldDeletedAt, field.TypeTime, value)
	}
	if auo.mutation.DeletedAtCleared() {
		_spec.ClearField(asset.FieldDeletedAt, field.TypeTime)
	}
	if auo.mutation.FolderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   asset.FolderTable,
			Columns: []string{asset.FolderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.FolderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   asset.FolderTable,
			Columns: []string{asset.FolderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(folder.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Asset{config: auo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, auo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{asset.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	auo.mutation.done = true
	return _node, nil
}

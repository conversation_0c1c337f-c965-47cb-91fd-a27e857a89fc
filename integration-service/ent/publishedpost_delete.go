// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/social-content-ai/integration-service/ent/predicate"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

// PublishedPostDelete is the builder for deleting a PublishedPost entity.
type PublishedPostDelete struct {
	config
	hooks    []Hook
	mutation *PublishedPostMutation
}

// Where appends a list predicates to the PublishedPostDelete builder.
func (ppd *PublishedPostDelete) Where(ps ...predicate.PublishedPost) *PublishedPostDelete {
	ppd.mutation.Where(ps...)
	return ppd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ppd *PublishedPostDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ppd.sqlExec, ppd.mutation, ppd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ppd *PublishedPostDelete) ExecX(ctx context.Context) int {
	n, err := ppd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ppd *PublishedPostDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(publishedpost.Table, sqlgraph.NewFieldSpec(publishedpost.FieldID, field.TypeUUID))
	if ps := ppd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ppd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ppd.mutation.done = true
	return affected, err
}

// PublishedPostDeleteOne is the builder for deleting a single PublishedPost entity.
type PublishedPostDeleteOne struct {
	ppd *PublishedPostDelete
}

// Where appends a list predicates to the PublishedPostDelete builder.
func (ppdo *PublishedPostDeleteOne) Where(ps ...predicate.PublishedPost) *PublishedPostDeleteOne {
	ppdo.ppd.mutation.Where(ps...)
	return ppdo
}

// Exec executes the deletion query.
func (ppdo *PublishedPostDeleteOne) Exec(ctx context.Context) error {
	n, err := ppdo.ppd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{publishedpost.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ppdo *PublishedPostDeleteOne) ExecX(ctx context.Context) {
	if err := ppdo.Exec(ctx); err != nil {
		panic(err)
	}
}

package template

import (
	"context"
)

// UseCase defines the template business logic interface
type UseCase interface {
	// Create a new notification template
	CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*TemplateResponse, error)

	// Get template by ID
	GetTemplate(ctx context.Context, id string) (*TemplateResponse, error)

	// Get template by event type
	GetTemplateByEventType(ctx context.Context, eventType string) (*TemplateResponse, error)

	// Update template
	UpdateTemplate(ctx context.Context, req *UpdateTemplateRequest) (*TemplateResponse, error)

	// Delete template
	DeleteTemplate(ctx context.Context, id string) error

	// List templates
	ListTemplates(ctx context.Context, req *ListTemplatesRequest) (*ListTemplatesResponse, error)

	// Render template with data
	RenderTemplate(ctx context.Context, req *RenderTemplateRequest) (*RenderTemplateResponse, error)
}

// CreateTemplateRequest represents a request to create a template
type CreateTemplateRequest struct {
	EventType   string                     `json:"event_type" validate:"required"`
	Name        string                     `json:"name" validate:"required"`
	Description string                     `json:"description,omitempty"`
	Templates   map[string]ChannelTemplate `json:"templates" validate:"required,min=1"`
	IsActive    bool                       `json:"is_active"`
	Variables   []string                   `json:"variables,omitempty"`
	CreatedBy   string                     `json:"created_by,omitempty"`
}

// UpdateTemplateRequest represents a request to update a template
type UpdateTemplateRequest struct {
	ID          string                     `json:"id" validate:"required"`
	Name        string                     `json:"name,omitempty"`
	Description string                     `json:"description,omitempty"`
	Templates   map[string]ChannelTemplate `json:"templates,omitempty"`
	IsActive    *bool                      `json:"is_active,omitempty"`
	Variables   []string                   `json:"variables,omitempty"`
}

// TemplateResponse represents a notification template
type TemplateResponse struct {
	ID          string                     `json:"id"`
	EventType   string                     `json:"event_type"`
	Name        string                     `json:"name"`
	Description string                     `json:"description"`
	Templates   map[string]ChannelTemplate `json:"templates"`
	IsActive    bool                       `json:"is_active"`
	Variables   []string                   `json:"variables"`
	CreatedAt   string                     `json:"created_at"`
	UpdatedAt   string                     `json:"updated_at"`
}

// ChannelTemplate represents a template for a specific channel
type ChannelTemplate struct {
	Subject   string            `json:"subject,omitempty"` // For email
	Body      string            `json:"body" validate:"required"`
	HTMLBody  string            `json:"html_body,omitempty"` // For email
	Variables []string          `json:"variables,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
}

// ListTemplatesRequest represents a request to list templates
type ListTemplatesRequest struct {
	EventType string `json:"event_type,omitempty"`
	IsActive  *bool  `json:"is_active,omitempty"`
	Page      int    `json:"page" validate:"min=1"`
	Limit     int    `json:"limit" validate:"min=1,max=100"`
	SortBy    string `json:"sort_by,omitempty"`
	Order     string `json:"order,omitempty"`
}

// ListTemplatesResponse represents the response from listing templates
type ListTemplatesResponse struct {
	Templates  []TemplateResponse `json:"templates"`
	Pagination PaginationMeta     `json:"pagination"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// RenderTemplateRequest represents a request to render a template
type RenderTemplateRequest struct {
	TemplateID string                 `json:"template_id" validate:"required"`
	Channel    string                 `json:"channel" validate:"required"`
	Data       map[string]interface{} `json:"data" validate:"required"`
}

// RenderTemplateResponse represents the response from rendering a template
type RenderTemplateResponse struct {
	Subject  string            `json:"subject,omitempty"`
	Body     string            `json:"body"`
	HTMLBody string            `json:"html_body,omitempty"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

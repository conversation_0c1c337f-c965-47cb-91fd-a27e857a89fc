# Makefile for generating Go code from Protocol Buffers

.PHONY: all clean generate user ai-content content-mgmt integration asset credit analytics notification rag-processing data-mgmt common

# Default target
all: generate

# Generate all proto files
generate: common user ai-content content-mgmt integration asset credit analytics notification rag-processing data-mgmt
	@echo "✅ All proto files generated successfully"

# Generate common proto files
common:
	@echo "🔄 Generating common proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		common/v1/*.proto

# Generate user service proto files
user:
	@echo "🔄 Generating user service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		user/v1/*.proto

# Generate AI content service proto files
ai-content:
	@echo "🔄 Generating AI content service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		ai-content/v1/*.proto

# Generate content management service proto files
content-mgmt:
	@echo "🔄 Generating content management service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		content-mgmt/v1/*.proto

# Generate integration service proto files
integration:
	@echo "🔄 Generating integration service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		integration/v1/*.proto

# Generate asset service proto files
asset:
	@echo "🔄 Generating asset service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		asset/v1/*.proto

# Generate credit service proto files
credit:
	@echo "🔄 Generating credit service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		credit/v1/*.proto

# Generate analytics service proto files
analytics:
	@echo "🔄 Generating analytics service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		analytics/v1/*.proto

# Generate notification service proto files
notification:
	@echo "🔄 Generating notification service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		notification/v1/*.proto

# Generate RAG processing service proto files
rag-processing:
	@echo "🔄 Generating RAG processing service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		rag-processing/v1/*.proto

# Generate data management service proto files
data-mgmt:
	@echo "🔄 Generating data management service proto files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		-I . \
		data-mgmt/v1/*.proto

# Clean generated files
clean:
	@echo "🧹 Cleaning generated files..."
	find . -name "*.pb.go" -delete
	find . -name "*_grpc.pb.go" -delete
	@echo "✅ Cleaned generated files"

# Install required tools
install-tools:
	@echo "📦 Installing protoc plugins..."
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	@echo "✅ Tools installed"

# Check if protoc is installed
check-protoc:
	@which protoc > /dev/null || (echo "❌ protoc not found. Please install Protocol Buffers compiler" && exit 1)
	@echo "✅ protoc found"

# Validate proto files
validate:
	@echo "🔍 Validating proto files..."
	@find . -name "*.proto" -exec protoc --descriptor_set_out=/dev/null {} \;
	@echo "✅ All proto files are valid"

# Help target
help:
	@echo "Available targets:"
	@echo "  all            - Generate all proto files (default)"
	@echo "  generate       - Generate all proto files"
	@echo "  common         - Generate common proto files"
	@echo "  user           - Generate user service proto files"
	@echo "  ai-content     - Generate AI content service proto files"
	@echo "  content-mgmt   - Generate content management service proto files"
	@echo "  integration    - Generate integration service proto files"
	@echo "  asset          - Generate asset service proto files"
	@echo "  credit         - Generate credit service proto files"
	@echo "  analytics      - Generate analytics service proto files"
	@echo "  notification   - Generate notification service proto files"
	@echo "  rag-processing - Generate RAG processing service proto files"
	@echo "  data-mgmt      - Generate data management service proto files"
	@echo "  clean        - Clean generated files"
	@echo "  install-tools- Install required protoc plugins"
	@echo "  check-protoc - Check if protoc is installed"
	@echo "  validate     - Validate proto files"
	@echo "  help         - Show this help message"

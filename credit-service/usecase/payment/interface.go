package payment

import (
	"context"

	"github.com/social-content-ai/credit-service/pkg/models"
)

// Processor defines the payment processing interface
// Only essential methods for the new flow
type Processor interface {
	// Core payment processing - ESSENTIAL
	ProcessPayment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)
	RefundPayment(ctx context.Context, req *RefundRequest) (*RefundResponse, error)
	GetPaymentStatus(ctx context.Context, paymentID string) (*PaymentStatus, error)

	// Webhooks - ESSENTIAL for payment flow completion
	ValidateWebhook(ctx context.Context, payload []byte, signature string) (bool, error)
	ProcessWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

// PaymentRequest represents a payment request
type PaymentRequest struct {
	UserID        string                 `json:"user_id"`
	Amount        int64                  `json:"amount"` // in cents
	Currency      string                 `json:"currency"`
	PaymentMethod string                 `json:"payment_method"`
	Description   string                 `json:"description"`
	OrderID       string                 `json:"order_id"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentResponse represents a payment response
type PaymentResponse struct {
	PaymentID   string                 `json:"payment_id"`
	Status      string                 `json:"status"`
	Amount      int64                  `json:"amount"`
	Currency    string                 `json:"currency"`
	PaymentURL  string                 `json:"payment_url,omitempty"`
	PaymentInfo map[string]interface{} `json:"payment_info,omitempty"`
	ExpiresAt   string                 `json:"expires_at,omitempty"`
}

// RefundRequest represents a refund request
type RefundRequest struct {
	PaymentID string `json:"payment_id"`
	Amount    int64  `json:"amount,omitempty"` // partial refund amount, omit for full refund
	Reason    string `json:"reason"`
}

// RefundResponse represents a refund response
type RefundResponse struct {
	RefundID  string `json:"refund_id"`
	PaymentID string `json:"payment_id"`
	Amount    int64  `json:"amount"`
	Status    string `json:"status"`
	Reason    string `json:"reason"`
}

// PaymentStatus represents payment status
type PaymentStatus struct {
	PaymentID     string `json:"payment_id"`
	Status        string `json:"status"`
	Amount        int64  `json:"amount"`
	Currency      string `json:"currency"`
	FailureReason string `json:"failure_reason,omitempty"`
	ProcessedAt   string `json:"processed_at,omitempty"`
}

// WebhookEvent represents a webhook event
type WebhookEvent struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt string                 `json:"created_at"`
}

// WebhookProcessResult represents the result of webhook processing
type WebhookProcessResult struct {
	Success     bool                   `json:"success"`
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	EventData   map[string]interface{} `json:"event_data"`
	ProcessedAt string                 `json:"processed_at"`
	Message     string                 `json:"message,omitempty"`
}

// SubscriptionUpdateEvent represents a subscription update event for Kafka
type SubscriptionUpdateEvent struct {
	EventID        string                 `json:"event_id"`
	EventType      string                 `json:"event_type"`
	EventData      map[string]interface{} `json:"event_data"`
	ProcessedAt    string                 `json:"processed_at"`
	SubscriptionID string                 `json:"subscription_id,omitempty"`
	UserID         string                 `json:"user_id,omitempty"`
	PaymentID      string                 `json:"payment_id,omitempty"`
	Status         string                 `json:"status,omitempty"`
	Amount         int64                  `json:"amount,omitempty"`
	Currency       string                 `json:"currency,omitempty"`
}

// Provider-specific processors
type StripeProcessor interface {
	Processor
	CreateStripePaymentIntent(ctx context.Context, req *PaymentRequest) (*models.StripePaymentInfo, error)
	HandleStripeWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error)
}

type MomoProcessor interface {
	Processor
	CreateMomoPayment(ctx context.Context, req *PaymentRequest) (*models.MomoPaymentInfo, error)
	HandleMomoWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

type ZaloPayProcessor interface {
	Processor
	CreateZaloPayPayment(ctx context.Context, req *PaymentRequest) (*models.ZaloPayPaymentInfo, error)
	HandleZaloPayWebhook(ctx context.Context, payload []byte) (*WebhookEvent, error)
}

type PayPalProcessor interface {
	Processor
	CreatePayPalPayment(ctx context.Context, req *PaymentRequest) (*models.PayPalPaymentInfo, error)
	HandlePayPalWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error)
	ExecutePayPalPayment(ctx context.Context, paymentID, payerID string) (*PaymentResponse, error)
}

type BankTransferProcessor interface {
	Processor
	CreateBankTransfer(ctx context.Context, req *PaymentRequest) (*models.BankTransferInfo, error)
	VerifyBankTransfer(ctx context.Context, transferCode string) (*PaymentStatus, error)
}

// CaptureResponse represents payment capture response
type CaptureResponse struct {
	CaptureID string `json:"capture_id"`
	OrderID   string `json:"order_id"`
	Status    string `json:"status"`
	Amount    int64  `json:"amount"`
	Currency  string `json:"currency"`
}

// SubscriptionRequest represents subscription creation request
type SubscriptionRequest struct {
	UserID    string `json:"user_id"`
	UserEmail string `json:"user_email"`
	PlanID    string `json:"plan_id"`
}

// SubscriptionResponse represents subscription creation response
type SubscriptionResponse struct {
	SubscriptionID string `json:"subscription_id"`
	Status         string `json:"status"`
	ApprovalURL    string `json:"approval_url"`
	PlanID         string `json:"plan_id"`
}

// SubscriptionDetails represents subscription details
type SubscriptionDetails struct {
	SubscriptionID string `json:"subscription_id"`
	PlanID         string `json:"plan_id"`
	Status         string `json:"status"`
	StartTime      string `json:"start_time"`
	CreateTime     string `json:"create_time"`
	UpdateTime     string `json:"update_time"`
}

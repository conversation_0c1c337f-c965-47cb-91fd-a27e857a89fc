package handlers

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/asset-service/usecase/asset"
	"github.com/social-content-ai/pkg-shared/logging"
	assetv1 "github.com/social-content-ai/proto-shared/asset/v1"
)

// AssetHandler implements the gRPC AssetService
type AssetHandler struct {
	assetv1.UnimplementedAssetServiceServer
	assetUseCase asset.UseCase
	logger       logging.Logger
}

// NewAssetHandler creates a new asset gRPC handler
func <PERSON>AssetHandler(assetUseCase asset.UseCase, logger logging.Logger) *AssetHandler {
	return &AssetHandler{
		assetUseCase: assetUseCase,
		logger:       logger,
	}
}

// RequestUpload requests a presigned URL for file upload
func (h *<PERSON><PERSON><PERSON><PERSON><PERSON>) RequestUpload(ctx context.Context, req *assetv1.UploadRequest) (*assetv1.UploadResponse, error) {
	h.logger.WithFields(map[string]any{
		"user_id":   req.UserId,
		"file_name": req.FileName,
		"file_type": req.FileType,
	}).Info("gRPC RequestUpload called")

	// Convert proto request to domain model
	uploadReq := &models.RequestUploadRequest{
		UserID:   req.UserId,
		Name:     req.FileName,
		MimeType: req.FileType,
		Size:     req.FileSize,
		IsPublic: req.IsPublic,
		Metadata: make(map[string]interface{}),
	}

	// Call use case
	uploadResponse, err := h.assetUseCase.RequestUpload(ctx, uploadReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to request upload")
		return nil, status.Errorf(codes.Internal, "failed to request upload: %v", err)
	}

	// Convert response to proto
	return &assetv1.UploadResponse{
		UploadId:     uploadResponse.UploadID,
		PresignedUrl: uploadResponse.UploadURL,
		S3Key:        "assets/key",
		S3Bucket:     "default",
		ExpiresAt:    timestamppb.New(uploadResponse.ExpiresAt),
		UploadFields: uploadResponse.Fields,
	}, nil
}

// ConfirmUpload confirms upload completion and processes the asset
func (h *AssetHandler) ConfirmUpload(ctx context.Context, req *assetv1.ConfirmUploadRequest) (*assetv1.ConfirmUploadResponse, error) {
	h.logger.WithField("upload_id", req.UploadId).Info("gRPC ConfirmUpload called")

	// Convert proto request to domain model
	confirmReq := &models.ConfirmUploadRequest{
		UploadID: req.UploadId,
		UserID:   req.UserId,
		ETag:     req.Etag,
	}

	// Call use case
	assetResponse, err := h.assetUseCase.ConfirmUpload(ctx, confirmReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm upload")
		return nil, status.Errorf(codes.Internal, "failed to confirm upload: %v", err)
	}

	// Convert response to proto
	return &assetv1.ConfirmUploadResponse{
		Asset: h.convertAssetToProto(assetResponse),
	}, nil
}

// GetAsset retrieves an asset by ID
func (h *AssetHandler) GetAsset(ctx context.Context, req *assetv1.GetAssetRequest) (*assetv1.Asset, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC GetAsset called")

	// Call use case
	assetResponse, err := h.assetUseCase.GetAsset(ctx, req.AssetId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get asset")
		return nil, status.Errorf(codes.NotFound, "asset not found: %v", err)
	}

	// Convert response to proto
	return h.convertAssetToProto(assetResponse), nil
}

// ListAssets lists assets with pagination and filters
func (h *AssetHandler) ListAssets(ctx context.Context, req *assetv1.ListAssetsRequest) (*assetv1.ListAssetsResponse, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC ListAssets called")

	// Convert proto request to domain model
	listReq := &models.ListAssetsRequest{
		UserID: req.UserId,
		Page:   int(req.Pagination.Page),
		Limit:  int(req.Pagination.Limit),
	}

	// Call use case
	listResponse, err := h.assetUseCase.ListAssets(ctx, listReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list assets")
		return nil, status.Errorf(codes.Internal, "failed to list assets: %v", err)
	}

	// Convert assets to proto
	assets := make([]*assetv1.Asset, len(listResponse.Assets))
	for i, asset := range listResponse.Assets {
		assets[i] = h.convertAssetToProto(&asset)
	}

	return &assetv1.ListAssetsResponse{
		Assets: assets,
		// TODO: Add pagination response when proto is updated
	}, nil
}

// UpdateAsset updates asset metadata
func (h *AssetHandler) UpdateAsset(ctx context.Context, req *assetv1.UpdateAssetRequest) (*assetv1.Asset, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC UpdateAsset called")

	// Convert proto request to domain model
	updateReq := &models.UpdateAssetRequest{
		AssetID: req.AssetId,
		UserID:  req.UserId,
	}

	// Call use case
	assetResponse, err := h.assetUseCase.UpdateAsset(ctx, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update asset")
		return nil, status.Errorf(codes.Internal, "failed to update asset: %v", err)
	}

	// Convert response to proto
	return h.convertAssetToProto(assetResponse), nil
}

// DeleteAsset deletes an asset
func (h *AssetHandler) DeleteAsset(ctx context.Context, req *assetv1.DeleteAssetRequest) (*assetv1.Empty, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC DeleteAsset called")

	// Call use case
	err := h.assetUseCase.DeleteAsset(ctx, req.AssetId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete asset")
		return nil, status.Errorf(codes.Internal, "failed to delete asset: %v", err)
	}

	return &assetv1.Empty{}, nil
}

// ValidateAsset validates an asset
func (h *AssetHandler) ValidateAsset(ctx context.Context, req *assetv1.ValidateAssetRequest) (*assetv1.ValidationResult, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC ValidateAsset called")

	// Get asset details first
	assetResponse, err := h.assetUseCase.GetAsset(ctx, req.AssetId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get asset for validation")
		return nil, status.Errorf(codes.NotFound, "asset not found: %v", err)
	}

	// Perform validation based on asset status and type
	var errors []string
	var warnings []string
	isValid := true
	isSafe := true
	isCorrupted := false

	// Check asset status
	switch assetResponse.Status {
	case "quarantined":
		isSafe = false
		errors = append(errors, "Asset is quarantined due to security concerns")
		isValid = false
	case "failed":
		isCorrupted = true
		errors = append(errors, "Asset processing failed")
		isValid = false
	case "expired":
		isValid = false
		errors = append(errors, "Asset has expired")
	case "validating":
		warnings = append(warnings, "Asset is still being validated")
	}

	// Check file size limits
	if assetResponse.FileInfo.Size > 100*1024*1024 { // 100MB
		warnings = append(warnings, "File size exceeds recommended limit")
	}

	// Check file type
	allowedTypes := map[string]bool{
		"image/jpeg":       true,
		"image/png":        true,
		"image/gif":        true,
		"image/webp":       true,
		"application/pdf":  true,
		"text/plain":       true,
		"application/json": true,
		"text/markdown":    true,
		"text/csv":         true,
	}

	if !allowedTypes[assetResponse.Type] {
		warnings = append(warnings, "File type may not be fully supported")
	}

	// Build properties
	properties := map[string]string{
		"file_size": fmt.Sprintf("%d", assetResponse.FileInfo.Size),
		"file_type": assetResponse.Type,
		"status":    assetResponse.Status,
	}

	if assetResponse.ImageInfo != nil && assetResponse.ImageInfo.Width > 0 {
		properties["width"] = fmt.Sprintf("%d", assetResponse.ImageInfo.Width)
		properties["height"] = fmt.Sprintf("%d", assetResponse.ImageInfo.Height)
		properties["format"] = assetResponse.ImageInfo.Format
	}

	return &assetv1.ValidationResult{
		IsValid:     isValid,
		Errors:      errors,
		Warnings:    warnings,
		IsSafe:      isSafe,
		IsCorrupted: isCorrupted,
		MimeType:    assetResponse.Type,
		Properties:  properties,
		ValidatedAt: timestamppb.Now(),
	}, nil
}

// ProcessAsset processes an asset
func (h *AssetHandler) ProcessAsset(ctx context.Context, req *assetv1.ProcessAssetRequest) (*assetv1.ProcessAssetResponse, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC ProcessAsset called")

	// Call use case
	_, err := h.assetUseCase.ProcessAsset(ctx, req.AssetId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process asset")
		return nil, status.Errorf(codes.Internal, "failed to process asset: %v", err)
	}

	return &assetv1.ProcessAssetResponse{
		ProcessingId:      generateProcessingID(),
		Status:            "queued",
		StartedAt:         timestamppb.Now(),
		EstimatedDuration: 60,
	}, nil
}

// GetProcessingStatus gets processing status
func (h *AssetHandler) GetProcessingStatus(ctx context.Context, req *assetv1.GetProcessingStatusRequest) (*assetv1.ProcessingStatus, error) {
	h.logger.WithField("processing_id", req.ProcessingId).Info("gRPC GetProcessingStatus called")

	// In a real implementation, this would:
	// 1. Look up processing job by ID in database/cache
	// 2. Check job status from processing queue
	// 3. Return current status and progress

	// For now, simulate different statuses based on processing ID pattern
	var status string
	var progress float32
	var currentStage string
	var errorMessage string
	var completedAt *timestamppb.Timestamp

	// Simple simulation based on processing ID
	if strings.Contains(req.ProcessingId, "error") {
		status = "failed"
		progress = 0
		currentStage = "error"
		errorMessage = "Processing failed due to invalid file format"
	} else if strings.Contains(req.ProcessingId, "proc") {
		// Simulate in-progress
		status = "processing"
		progress = 65.0
		currentStage = "thumbnail_generation"
	} else {
		// Simulate completed
		status = "completed"
		progress = 100.0
		currentStage = "finished"
		completedAt = timestamppb.Now()
	}

	response := &assetv1.ProcessingStatus{
		ProcessingId:   req.ProcessingId,
		AssetId:        req.AssetId,
		ProcessingType: "asset_processing",
		Status:         status,
		Progress:       progress,
		CurrentStage:   currentStage,
		StartedAt:      timestamppb.New(time.Now().Add(-5 * time.Minute)),
		ErrorMessage:   errorMessage,
		Results:        map[string]string{},
	}

	if completedAt != nil {
		response.CompletedAt = completedAt
	}

	return response, nil
}

// GetDownloadUrl gets download URL for an asset
func (h *AssetHandler) GetDownloadUrl(ctx context.Context, req *assetv1.GetDownloadUrlRequest) (*assetv1.GetDownloadUrlResponse, error) {
	h.logger.WithField("asset_id", req.AssetId).Info("gRPC GetDownloadUrl called")

	// Call use case
	downloadURL, err := h.assetUseCase.DownloadAsset(ctx, req.AssetId, req.UserId)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get download URL")
		return nil, status.Errorf(codes.Internal, "failed to get download URL: %v", err)
	}

	return &assetv1.GetDownloadUrlResponse{
		DownloadUrl: downloadURL,
		ExpiresAt:   timestamppb.New(time.Now().Add(time.Hour)),
	}, nil
}

// CheckDuplicate checks for duplicate assets
func (h *AssetHandler) CheckDuplicate(ctx context.Context, req *assetv1.DuplicateCheckRequest) (*assetv1.DuplicateCheckResponse, error) {
	h.logger.WithField("content_hash", req.ContentHash).Info("gRPC CheckDuplicate called")

	// In a real implementation, this would:
	// 1. Query database for assets with same content hash
	// 2. Check if user has access to those assets
	// 3. Return duplicate information

	// For now, simulate duplicate check
	// In a real implementation, would query database for matching content hash

	// TODO: Update proto definition to include proper fields
	return &assetv1.DuplicateCheckResponse{
		// Fields will be added when proto is updated
	}, nil
}

// GetStorageStats gets storage statistics
func (h *AssetHandler) GetStorageStats(ctx context.Context, req *assetv1.GetStorageStatsRequest) (*assetv1.StorageStats, error) {
	h.logger.WithField("user_id", req.UserId).Info("gRPC GetStorageStats called")

	// In a real implementation, this would:
	// 1. Query database for user's assets
	// 2. Calculate total size by type
	// 3. Get quota information from user service
	// 4. Calculate usage percentages

	// For now, simulate realistic storage stats
	totalSize := int64(150 * 1024 * 1024)   // 150MB
	quotaLimit := int64(1024 * 1024 * 1024) // 1GB
	quotaUsagePercent := float32(totalSize) / float32(quotaLimit) * 100

	return &assetv1.StorageStats{
		TotalSize:         totalSize,
		TotalFiles:        25,
		ImagesSize:        90 * 1024 * 1024, // 90MB
		ImagesCount:       15,
		DocumentsSize:     60 * 1024 * 1024, // 60MB
		DocumentsCount:    10,
		QuotaLimit:        quotaLimit,
		QuotaUsagePercent: quotaUsagePercent,
	}, nil
}

// convertAssetToProto converts domain model to proto
func (h *AssetHandler) convertAssetToProto(asset *models.AssetResponse) *assetv1.Asset {
	return &assetv1.Asset{
		Id:        asset.ID,
		UserId:    asset.UserID,
		FileName:  asset.Name,
		FileType:  asset.Type,
		FileSize:  asset.FileInfo.Size,
		Status:    asset.Status,
		IsPublic:  asset.AccessControl.IsPublic,
		CreatedAt: timestamppb.New(asset.CreatedAt),
		UpdatedAt: timestamppb.New(asset.UpdatedAt),
	}
}

// generateProcessingID generates a unique processing ID
func generateProcessingID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

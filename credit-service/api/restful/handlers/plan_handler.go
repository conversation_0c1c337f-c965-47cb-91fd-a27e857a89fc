package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/pkg/models"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/pkg-shared/logging"
)

// PlanHandler handles plan-related HTTP requests
type PlanHandler struct {
	planUseCase plan.UseCase
	logger      logging.Logger
}

// NewPlanHandler creates a new plan handler
func NewPlanHandler(planUseCase plan.UseCase, logger logging.Logger) *PlanHandler {
	return &PlanHandler{
		planUseCase: planUseCase,
		logger:      logger,
	}
}

// ListPlans godoc
// @Summary List subscription plans
// @Description Get available subscription plans
// @Tags plans
// @Produce json
// @Security BearerAuth
// @Param active query bool false "Filter by active status"
// @Param billing_interval query string false "Filter by billing interval (monthly, yearly)"
// @Param currency query string false "Filter by currency"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.ListPlansResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans [get]
func (h *PlanHandler) ListPlans(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	var active *bool
	if activeStr := c.Query("active"); activeStr != "" {
		if activeBool, err := strconv.ParseBool(activeStr); err == nil {
			active = &activeBool
		}
	}

	req := &models.ListPlansRequest{
		Active:          active,
		BillingInterval: c.Query("billing_interval"),
		Currency:        c.Query("currency"),
		Page:            page,
		Limit:           limit,
		SortBy:          c.DefaultQuery("sort_by", "sort_order"),
		SortOrder:       c.DefaultQuery("sort_order", "asc"),
	}

	plans, err := h.planUseCase.ListPlans(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list plans")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_PLANS_FAILED",
			Message: "Failed to list plans",
		})
		return
	}

	c.JSON(http.StatusOK, plans)
}

// GetPlan godoc
// @Summary Get plan by ID
// @Description Get subscription plan details by ID
// @Tags plans
// @Produce json
// @Security BearerAuth
// @Param id path string true "Plan ID"
// @Success 200 {object} models.SubscriptionPlan
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/plans/{id} [get]
func (h *PlanHandler) GetPlan(c *gin.Context) {
	planID := c.Param("id")
	if planID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_PLAN_ID",
			Message: "Plan ID is required",
		})
		return
	}

	plan, err := h.planUseCase.GetPlan(c.Request.Context(), planID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get plan")

		switch err.Error() {
		case "PLAN_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "PLAN_NOT_FOUND",
				Message: "Plan not found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_PLAN_FAILED",
				Message: "Failed to get plan",
			})
		}
		return
	}

	c.JSON(http.StatusOK, plan)
}

// Subscribe godoc
// @Summary Subscribe to a plan
// @Description Subscribe to a subscription plan
// @Tags plans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.SubscribeRequest true "Subscribe request"
// @Success 200 {object} models.SubscribeResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans/subscribe [post]
func (h *PlanHandler) Subscribe(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.SubscribeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid subscribe request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	subscription, err := h.planUseCase.Subscribe(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to subscribe")

		switch err.Error() {
		case "PLAN_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "PLAN_NOT_FOUND",
				Message: "Plan not found",
			})
		case "PLAN_NOT_ACTIVE":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "PLAN_NOT_ACTIVE",
				Message: "Plan is not active",
			})
		case "ALREADY_SUBSCRIBED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "ALREADY_SUBSCRIBED",
				Message: "User already has an active subscription",
			})
		case "PAYMENT_FAILED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "PAYMENT_FAILED",
				Message: "Payment processing failed",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "SUBSCRIBE_FAILED",
				Message: "Failed to subscribe to plan",
			})
		}
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// UpgradePlan godoc
// @Summary Upgrade subscription plan
// @Description Upgrade to a higher tier plan
// @Tags plans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UpgradePlanRequest true "Upgrade plan request"
// @Success 200 {object} models.Subscription
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans/upgrade [post]
func (h *PlanHandler) UpgradePlan(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.UpgradePlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid upgrade plan request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	subscription, err := h.planUseCase.UpgradePlan(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade plan")

		switch err.Error() {
		case "NO_ACTIVE_SUBSCRIPTION":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "NO_ACTIVE_SUBSCRIPTION",
				Message: "No active subscription found",
			})
		case "PLAN_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "PLAN_NOT_FOUND",
				Message: "Target plan not found",
			})
		case "INVALID_UPGRADE":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_UPGRADE",
				Message: "Cannot upgrade to this plan",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UPGRADE_FAILED",
				Message: "Failed to upgrade plan",
			})
		}
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// DowngradePlan godoc
// @Summary Downgrade subscription plan
// @Description Downgrade to a lower tier plan
// @Tags plans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.DowngradePlanRequest true "Downgrade plan request"
// @Success 200 {object} models.Subscription
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans/downgrade [post]
func (h *PlanHandler) DowngradePlan(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.DowngradePlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid downgrade plan request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	subscription, err := h.planUseCase.DowngradePlan(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to downgrade plan")

		switch err.Error() {
		case "NO_ACTIVE_SUBSCRIPTION":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "NO_ACTIVE_SUBSCRIPTION",
				Message: "No active subscription found",
			})
		case "PLAN_NOT_FOUND":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "PLAN_NOT_FOUND",
				Message: "Target plan not found",
			})
		case "INVALID_DOWNGRADE":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "INVALID_DOWNGRADE",
				Message: "Cannot downgrade to this plan",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DOWNGRADE_FAILED",
				Message: "Failed to downgrade plan",
			})
		}
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// CancelSubscription godoc
// @Summary Cancel subscription
// @Description Cancel current subscription
// @Tags plans
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.CancelSubscriptionRequest true "Cancel subscription request"
// @Success 200 {object} models.Subscription
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans/cancel [post]
func (h *PlanHandler) CancelSubscription(c *gin.Context) {
	_, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.CancelSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid cancel subscription request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	// req.UserID = userID.(string) // TODO: Add UserID field to CancelSubscriptionRequest model

	subscription, err := h.planUseCase.CancelSubscription(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel subscription")

		switch err.Error() {
		case "NO_ACTIVE_SUBSCRIPTION":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "NO_ACTIVE_SUBSCRIPTION",
				Message: "No active subscription found",
			})
		case "ALREADY_CANCELLED":
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "ALREADY_CANCELLED",
				Message: "Subscription is already cancelled",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "CANCEL_FAILED",
				Message: "Failed to cancel subscription",
			})
		}
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// GetCurrentSubscription godoc
// @Summary Get current subscription
// @Description Get user's current active subscription
// @Tags plans
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.Subscription
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/plans/current [get]
func (h *PlanHandler) GetCurrentSubscription(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	subscription, err := h.planUseCase.GetCurrentSubscription(c.Request.Context(), userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get current subscription")

		switch err.Error() {
		case "NO_ACTIVE_SUBSCRIPTION":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "NO_ACTIVE_SUBSCRIPTION",
				Message: "No active subscription found",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_SUBSCRIPTION_FAILED",
				Message: "Failed to get current subscription",
			})
		}
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// GetSubscriptionHistory godoc
// @Summary Get subscription history
// @Description Get user's subscription history
// @Tags plans
// @Produce json
// @Security BearerAuth
// @Param status query string false "Filter by status"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.SubscriptionHistoryResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/plans/history [get]
func (h *PlanHandler) GetSubscriptionHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	req := &models.SubscriptionHistoryRequest{
		UserID: userID.(string),
		Status: c.Query("status"),
		Page:   page,
		Limit:  limit,
		// TODO: Add SortBy and SortOrder fields to SubscriptionHistoryRequest model
		// SortBy:    c.DefaultQuery("sort_by", "created_at"),
		// SortOrder: c.DefaultQuery("sort_order", "desc"),
	}

	history, err := h.planUseCase.GetSubscriptionHistory(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get subscription history")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_HISTORY_FAILED",
			Message: "Failed to get subscription history",
		})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetPlanAnalytics godoc
// @Summary Get plan analytics
// @Description Get plan analytics for user
// @Tags plans
// @Produce json
// @Security BearerAuth
// @Param period query string false "Period (day, week, month, year)" default("month")
// @Success 200 {object} models.PlanAnalyticsResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/analytics/plans [get]
func (h *PlanHandler) GetPlanAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	period := c.DefaultQuery("period", "month")

	analytics, err := h.planUseCase.GetPlanAnalytics(c.Request.Context(), userID.(string), period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get plan analytics")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_ANALYTICS_FAILED",
			Message: "Failed to get plan analytics",
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

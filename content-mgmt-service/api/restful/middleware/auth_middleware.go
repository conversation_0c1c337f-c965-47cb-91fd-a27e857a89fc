package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// AuthMiddleware handles authentication middleware
type AuthMiddleware struct {
	userClient userv1.AuthServiceClient
	logger     logging.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(userClient userv1.AuthServiceClient, logger logging.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		userClient: userClient,
		logger:     logger,
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "MISSING_TOKEN",
				Message: "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_TOKEN_FORMAT",
				Message: "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil {
			m.logger.WithError(err).Error("Token validation failed")
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Invalid or expired token",
			})
			c.Abort()
			return
		}

		if !resp.Valid {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "INVALID_TOKEN",
				Message: "Token is not valid",
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		c.Set("email", resp.Claims["email"])
		c.Set("role", resp.Claims["role"])

		c.Next()
	}
}

// RequireRole middleware that requires specific role
func (m *AuthMiddleware) RequireRole(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		if userRole != requiredRole && userRole != "admin" { // admin can access everything
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "INSUFFICIENT_PERMISSIONS",
				Message: "Insufficient permissions to access this resource",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRoles middleware that requires one of multiple roles
func (m *AuthMiddleware) RequireRoles(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := role.(string)

		// Admin can access everything
		if userRole == "admin" {
			c.Next()
			return
		}

		// Check if user has one of the required roles
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error:   "INSUFFICIENT_PERMISSIONS",
			Message: "Insufficient permissions to access this resource",
		})
		c.Abort()
	}
}

// OptionalAuth middleware that optionally authenticates user
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if token has Bearer prefix
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		token := tokenParts[1]

		// Validate token with user service
		resp, err := m.userClient.ValidateToken(context.Background(), &userv1.ValidateTokenRequest{
			Token: token,
		})
		if err != nil || !resp.Valid {
			// Don't abort, just continue without authentication
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", resp.UserId)
		c.Set("email", resp.Claims["email"])
		c.Set("role", resp.Claims["role"])

		c.Next()
	}
}

// CORS middleware
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestID middleware adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)

		c.Next()
	})
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production, use UUID or similar
	return "req_" + strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(
		"2006-01-02T15:04:05.000Z", "-", ""), ":", ""), ".", "")
}

package asset

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/pkg/models"
	"github.com/social-content-ai/asset-service/pkg/processor"
	"github.com/social-content-ai/asset-service/pkg/storage"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB            *ent.Client
	writeDB           *ent.Client
	storageClient     storage.Storage
	imageProcessor    *processor.ImageProcessor
	documentProcessor *processor.DocumentProcessor
	logger            logging.Logger
}

// NewService creates a new asset service
func NewService(
	readDB, writeDB *ent.Client,
	storageClient storage.Storage,
	imageProcessor *processor.ImageProcessor,
	documentProcessor *processor.DocumentProcessor,
	logger logging.Logger,
) UseCase {
	return &service{
		readDB:            readDB,
		writeDB:           writeDB,
		storageClient:     storageClient,
		imageProcessor:    imageProcessor,
		documentProcessor: documentProcessor,
		logger:            logger,
	}
}

// RequestUpload requests a presigned URL for file upload
func (s *service) RequestUpload(ctx context.Context, req *models.RequestUploadRequest) (*models.UploadURLResponse, error) {
	s.logger.WithFields(map[string]any{
		"user_id":   req.UserID,
		"file_name": req.Name,
		"file_type": req.MimeType,
		"file_size": req.Size,
	}).Info("Requesting upload URL")

	// Validate file type and size
	if err := s.validateUploadRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Generate storage key
	storageKey := s.generateStorageKey(req.UserID, req.Name)

	// Generate presigned URL
	presignedURL, err := s.storageClient.GeneratePresignedUploadURL(
		ctx,
		storageKey,
		req.MimeType,
		time.Hour,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return &models.UploadURLResponse{
		UploadID:  uuid.New().String(),
		UploadURL: presignedURL.URL,
		Method:    "PUT",
		ExpiresAt: presignedURL.ExpiresAt,
		MaxSize:   req.Size,
	}, nil
}

// ConfirmUpload confirms upload completion and processes the asset
func (s *service) ConfirmUpload(ctx context.Context, req *models.ConfirmUploadRequest) (*models.AssetResponse, error) {
	s.logger.WithField("upload_id", req.UploadID).Info("Confirming upload")

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Step 1: Verify file exists in S3
	storageKey := s.generateStorageKey(req.UserID, req.UploadID)
	exists, err := s.storageClient.FileExists(ctx, storageKey)
	if err != nil {
		return nil, fmt.Errorf("failed to check file existence: %w", err)
	}
	if !exists {
		return nil, fmt.Errorf("uploaded file not found in storage")
	}

	// Step 2: Download and validate file
	fileReader, err := s.storageClient.DownloadFile(ctx, storageKey)
	if err != nil {
		return nil, fmt.Errorf("failed to download file for validation: %w", err)
	}
	defer fileReader.Close()

	// Read file data
	fileData, err := io.ReadAll(fileReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read file data: %w", err)
	}

	// Step 3: Validate file integrity and content
	validationResult, err := s.validateFileContent(ctx, fileData, req.UploadID)
	if err != nil {
		// Delete invalid file from S3
		s.logger.WithError(err).Warn("File validation failed, cleaning up")
		if deleteErr := s.storageClient.DeleteFile(ctx, storageKey); deleteErr != nil {
			s.logger.WithError(deleteErr).Error("Failed to cleanup invalid file")
		}
		return nil, fmt.Errorf("file validation failed: %w", err)
	}

	// Step 4: Create asset record with validated information
	assetEntity, err := s.writeDB.Asset.
		Create().
		SetUserID(userUUID).
		SetFileName(validationResult.FileName).
		SetOriginalName(validationResult.OriginalName).
		SetFileType(validationResult.MimeType).
		SetFileSize(validationResult.FileSize).
		SetFileExtension(validationResult.Extension).
		SetContentHash(validationResult.ContentHash).
		SetS3Key(storageKey).
		SetS3Bucket("assets").
		SetStatus("validating"). // Start with validating status
		Save(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to create asset: %w", err)
	}

	// Step 5: Trigger async virus scanning
	go s.triggerVirusScanning(context.Background(), assetEntity.ID.String(), storageKey)

	return s.convertAssetToResponse(assetEntity), nil
}

// GetAsset retrieves an asset by ID
func (s *service) GetAsset(ctx context.Context, assetID, userID string) (*models.AssetResponse, error) {
	s.logger.WithFields(map[string]any{
		"asset_id": assetID,
		"user_id":  userID,
	}).Info("Getting asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("asset not found")
		}
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// ListAssets lists assets with pagination and filters
func (s *service) ListAssets(ctx context.Context, req *models.ListAssetsRequest) (*models.ListAssetsResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing assets")

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		)

	// Apply filters
	if req.Type != "" {
		query = query.Where(asset.FileTypeContains(req.Type))
	}
	if req.Status != "" {
		query = query.Where(asset.StatusEQ(req.Status))
	}
	if req.FolderID != "" {
		folderUUID, err := uuid.Parse(req.FolderID)
		if err != nil {
			return nil, fmt.Errorf("invalid folder ID: %w", err)
		}
		query = query.Where(asset.FolderID(folderUUID))
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	assets, err := query.
		Offset(offset).
		Limit(req.Limit).
		Order(asset.ByCreatedAt()).
		All(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to list assets: %w", err)
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count assets: %w", err)
	}

	// Convert to response
	assetResponses := make([]models.AssetResponse, len(assets))
	for i, assetEntity := range assets {
		assetResponses[i] = *s.convertAssetToResponse(assetEntity)
	}

	return &models.ListAssetsResponse{
		Assets: assetResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + req.Limit - 1) / req.Limit,
		},
	}, nil
}

// UpdateAsset updates asset metadata
func (s *service) UpdateAsset(ctx context.Context, req *models.UpdateAssetRequest) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", req.AssetID).Info("Updating asset")

	assetUUID, err := uuid.Parse(req.AssetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	update := s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetUpdatedAt(time.Now())

	if req.Name != "" {
		update = update.SetFileName(req.Name)
	}
	if req.FolderID != "" {
		folderUUID, err := uuid.Parse(req.FolderID)
		if err != nil {
			return nil, fmt.Errorf("invalid folder ID: %w", err)
		}
		update = update.SetFolderID(folderUUID)
	}

	assetEntity, err := update.Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("asset not found")
		}
		return nil, fmt.Errorf("failed to update asset: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// DeleteAsset deletes an asset
func (s *service) DeleteAsset(ctx context.Context, assetID, userID string) error {
	s.logger.WithFields(map[string]any{
		"asset_id": assetID,
		"user_id":  userID,
	}).Info("Deleting asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	// Soft delete
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetDeletedAt(time.Now()).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to delete asset: %w", err)
	}

	// TODO: Schedule storage cleanup
	return nil
}

// ProcessAsset processes an asset
func (s *service) ProcessAsset(ctx context.Context, assetID, userID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Processing asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("asset not found")
		}
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}

	// Update status to processing
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus("processing").
		Save(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to update asset status: %w", err)
	}

	// TODO: Implement actual processing logic
	// For now, just mark as ready
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus("ready").
		Save(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to update asset status: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// GenerateThumbnail generates thumbnail for an asset
func (s *service) GenerateThumbnail(ctx context.Context, assetID, userID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Generating thumbnail")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("asset not found")
		}
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}

	if !strings.HasPrefix(assetEntity.FileType, "image/") {
		return nil, fmt.Errorf("thumbnails can only be generated for images")
	}

	// TODO: Generate thumbnail using image processor
	// For now, return the original asset
	return s.convertAssetToResponse(assetEntity), nil
}

// DownloadAsset gets download URL for an asset
func (s *service) DownloadAsset(ctx context.Context, assetID, userID string) (string, error) {
	s.logger.WithField("asset_id", assetID).Info("Getting download URL")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return "", fmt.Errorf("invalid asset ID: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return "", fmt.Errorf("asset not found")
		}
		return "", fmt.Errorf("failed to get asset: %w", err)
	}

	// Generate download URL
	downloadURL, err := s.storageClient.GeneratePresignedDownloadURL(
		ctx,
		assetEntity.S3Key,
		time.Hour,
	)
	if err != nil {
		return "", fmt.Errorf("failed to generate download URL: %w", err)
	}

	return downloadURL, nil
}

// GetPublicURL gets public URL for an asset
func (s *service) GetPublicURL(ctx context.Context, assetID, userID string) (string, error) {
	s.logger.WithField("asset_id", assetID).Info("Getting public URL")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return "", fmt.Errorf("invalid asset ID: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
				asset.IsPublic(true), // Only public assets
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return "", fmt.Errorf("asset not found or not public")
		}
		return "", fmt.Errorf("failed to get asset: %w", err)
	}

	// Generate public URL with long expiration (24 hours for public assets)
	publicURL, err := s.storageClient.GeneratePresignedDownloadURL(
		ctx,
		assetEntity.S3Key,
		24*time.Hour,
	)
	if err != nil {
		return "", fmt.Errorf("failed to generate public URL: %w", err)
	}

	return publicURL, nil
}

// BulkUpload handles bulk upload requests
func (s *service) BulkUpload(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Processing bulk upload")

	uploads := make([]models.UploadURLResponse, 0, len(req.Assets))
	errors := make([]models.BulkOperationError, 0)

	for i, assetReq := range req.Assets {
		assetReq.UserID = req.UserID
		assetReq.FolderID = req.FolderID

		uploadResp, err := s.RequestUpload(ctx, &assetReq)
		if err != nil {
			errors = append(errors, models.BulkOperationError{
				Index:  i,
				Error:  err.Error(),
				Reason: "Failed to generate upload URL",
			})
			continue
		}

		uploads = append(uploads, *uploadResp)
	}

	return &models.BulkUploadResponse{
		BulkOperationResult: models.BulkOperationResult{
			Total:   len(req.Assets),
			Success: len(uploads),
			Failed:  len(errors),
			Errors:  errors,
		},
		Uploads: uploads,
	}, nil
}

// BulkDelete handles bulk delete requests
func (s *service) BulkDelete(ctx context.Context, req *models.BulkDeleteRequest) (*models.BulkOperationResult, error) {
	s.logger.WithField("user_id", req.UserID).Info("Processing bulk delete")

	errors := make([]models.BulkOperationError, 0)
	successCount := 0

	for i, assetID := range req.AssetIDs {
		err := s.DeleteAsset(ctx, assetID, req.UserID)
		if err != nil {
			errors = append(errors, models.BulkOperationError{
				ID:     assetID,
				Index:  i,
				Error:  err.Error(),
				Reason: "Failed to delete asset",
			})
			continue
		}
		successCount++
	}

	return &models.BulkOperationResult{
		Total:   len(req.AssetIDs),
		Success: successCount,
		Failed:  len(errors),
		Errors:  errors,
	}, nil
}

// BulkMove handles bulk move requests
func (s *service) BulkMove(ctx context.Context, req *models.BulkMoveRequest) (*models.BulkOperationResult, error) {
	s.logger.WithField("user_id", req.UserID).Info("Processing bulk move")

	errors := make([]models.BulkOperationError, 0)
	successCount := 0

	// Validate target folder if specified
	var targetFolderUUID *uuid.UUID
	if req.FolderID != "" {
		folderUUID, err := uuid.Parse(req.FolderID)
		if err != nil {
			return &models.BulkOperationResult{
				Total:   len(req.AssetIDs),
				Success: 0,
				Failed:  len(req.AssetIDs),
				Errors: []models.BulkOperationError{{
					Error:  "Invalid target folder ID",
					Reason: "Failed to parse folder UUID",
				}},
			}, nil
		}
		targetFolderUUID = &folderUUID
	}

	for i, assetID := range req.AssetIDs {
		assetUUID, err := uuid.Parse(assetID)
		if err != nil {
			errors = append(errors, models.BulkOperationError{
				ID:     assetID,
				Index:  i,
				Error:  err.Error(),
				Reason: "Invalid asset ID",
			})
			continue
		}

		// Update asset folder
		update := s.writeDB.Asset.UpdateOneID(assetUUID)
		if targetFolderUUID != nil {
			update = update.SetFolderID(*targetFolderUUID)
		} else {
			update = update.ClearFolderID()
		}

		_, err = update.Save(ctx)
		if err != nil {
			errors = append(errors, models.BulkOperationError{
				ID:     assetID,
				Index:  i,
				Error:  err.Error(),
				Reason: "Failed to move asset",
			})
			continue
		}
		successCount++
	}

	return &models.BulkOperationResult{
		Total:   len(req.AssetIDs),
		Success: successCount,
		Failed:  len(errors),
		Errors:  errors,
	}, nil
}

// BulkTag handles bulk tag requests
func (s *service) BulkTag(ctx context.Context, req *models.BulkTagRequest) (*models.BulkOperationResult, error) {
	s.logger.WithField("user_id", req.UserID).Info("Processing bulk tag")

	// TODO: Implement tagging when tags field is added to database schema
	// For now, return success but don't actually tag
	return &models.BulkOperationResult{
		Total:   len(req.AssetIDs),
		Success: len(req.AssetIDs),
		Failed:  0,
		Errors:  []models.BulkOperationError{},
	}, nil
}

// ListImages lists image assets
func (s *service) ListImages(ctx context.Context, userID string, page, limit int) (*models.ListAssetsResponse, error) {
	req := &models.ListAssetsRequest{
		UserID: userID,
		Type:   "image",
		Page:   page,
		Limit:  limit,
	}
	return s.ListAssets(ctx, req)
}

// UploadImages handles image upload requests
func (s *service) UploadImages(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error) {
	// Filter to only image types
	imageAssets := make([]models.RequestUploadRequest, 0)
	for _, assetReq := range req.Assets {
		if strings.HasPrefix(assetReq.MimeType, "image/") {
			imageAssets = append(imageAssets, assetReq)
		}
	}

	req.Assets = imageAssets
	return s.BulkUpload(ctx, req)
}

// ResizeImage resizes an image
func (s *service) ResizeImage(ctx context.Context, req *models.ResizeImageRequest) (*models.AssetResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// OptimizeImage optimizes an image
func (s *service) OptimizeImage(ctx context.Context, req *models.OptimizeImageRequest) (*models.AssetResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// ConvertImageFormat converts image format
func (s *service) ConvertImageFormat(ctx context.Context, req *models.ConvertImageFormatRequest) (*models.AssetResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetImageVariants gets image variants
func (s *service) GetImageVariants(ctx context.Context, assetID, userID string) ([]models.AssetVariant, error) {
	return nil, fmt.Errorf("not implemented")
}

// ListDocuments lists document assets
func (s *service) ListDocuments(ctx context.Context, userID string, page, limit int) (*models.ListAssetsResponse, error) {
	req := &models.ListAssetsRequest{
		UserID: userID,
		Type:   "document",
		Page:   page,
		Limit:  limit,
	}
	return s.ListAssets(ctx, req)
}

// UploadDocuments handles document upload requests
func (s *service) UploadDocuments(ctx context.Context, req *models.BulkUploadRequest) (*models.BulkUploadResponse, error) {
	// Filter to only document types
	docAssets := make([]models.RequestUploadRequest, 0)
	for _, assetReq := range req.Assets {
		if strings.HasPrefix(assetReq.MimeType, "application/pdf") ||
			strings.HasPrefix(assetReq.MimeType, "text/") ||
			strings.HasPrefix(assetReq.MimeType, "application/msword") {
			docAssets = append(docAssets, assetReq)
		}
	}

	req.Assets = docAssets
	return s.BulkUpload(ctx, req)
}

// GetDocumentPreview gets document preview
func (s *service) GetDocumentPreview(ctx context.Context, assetID, userID string) (string, error) {
	return "", fmt.Errorf("not implemented")
}

// ExtractDocumentText extracts text from document
func (s *service) ExtractDocumentText(ctx context.Context, assetID, userID string) (string, error) {
	return "", fmt.Errorf("not implemented")
}

// GetDocumentMetadata gets document metadata
func (s *service) GetDocumentMetadata(ctx context.Context, assetID, userID string) (*models.DocumentInfo, error) {
	return nil, fmt.Errorf("not implemented")
}

// SearchAssets searches assets
func (s *service) SearchAssets(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error) {
	s.logger.WithField("query", req.Query).Info("Searching assets")

	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	query := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
				asset.FileNameContains(req.Query),
			),
		)

	// Apply filters
	if req.Type != "" {
		query = query.Where(asset.FileTypeContains(req.Type))
	}
	if req.FolderID != "" {
		folderUUID, err := uuid.Parse(req.FolderID)
		if err != nil {
			return nil, fmt.Errorf("invalid folder ID: %w", err)
		}
		query = query.Where(asset.FolderID(folderUUID))
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	assets, err := query.
		Offset(offset).
		Limit(req.Limit).
		Order(asset.ByCreatedAt()).
		All(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to search assets: %w", err)
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count search results: %w", err)
	}

	// Convert to response
	assetResponses := make([]models.AssetResponse, len(assets))
	for i, assetEntity := range assets {
		assetResponses[i] = *s.convertAssetToResponse(assetEntity)
	}

	return &models.ListAssetsResponse{
		Assets: assetResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + req.Limit - 1) / req.Limit,
		},
	}, nil
}

// SearchImages searches images
func (s *service) SearchImages(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error) {
	req.Type = "image"
	return s.SearchAssets(ctx, req)
}

// SearchDocuments searches documents
func (s *service) SearchDocuments(ctx context.Context, req *models.SearchAssetsRequest) (*models.ListAssetsResponse, error) {
	req.Type = "document"
	return s.SearchAssets(ctx, req)
}

// VisualSearch performs visual search
func (s *service) VisualSearch(ctx context.Context, req *models.VisualSearchRequest) (*models.VisualSearchResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetStorageAnalytics gets storage analytics
func (s *service) GetStorageAnalytics(ctx context.Context, userID string) (*models.StorageAnalyticsResponse, error) {
	s.logger.WithField("user_id", userID).Info("Getting storage analytics")

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Get total assets count
	totalAssets, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count assets: %w", err)
	}

	// Get total storage used
	assets, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.UserID(userUUID),
				asset.DeletedAtIsNil(),
			),
		).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get assets: %w", err)
	}

	var totalSize int64
	typeStats := make(map[string]models.StorageStats)
	for _, assetEntity := range assets {
		totalSize += assetEntity.FileSize
		assetType := s.detectAssetType(assetEntity.FileType)

		stats := typeStats[assetType]
		stats.Count++
		stats.Size += assetEntity.FileSize
		typeStats[assetType] = stats
	}

	return &models.StorageAnalyticsResponse{
		TotalAssets:     int64(totalAssets),
		TotalSize:       totalSize,
		ByType:          typeStats,
		ByFolder:        make(map[string]models.StorageStats), // TODO: Implement folder stats
		RecentUploads:   []models.AssetResponse{},             // TODO: Get recent uploads
		LargestAssets:   []models.AssetResponse{},             // TODO: Get largest assets
		QuotaUsed:       totalSize,
		QuotaLimit:      100 * 1024 * 1024 * 1024, // 100GB default
		QuotaPercentage: float64(totalSize) / float64(100*1024*1024*1024) * 100,
	}, nil
}

// File validation and processing methods

// FileValidationResult contains the result of file validation
type FileValidationResult struct {
	FileName     string
	OriginalName string
	MimeType     string
	FileSize     int64
	Extension    string
	ContentHash  string
	IsValid      bool
	Errors       []string
}

// validateFileContent validates file content and extracts metadata
func (s *service) validateFileContent(ctx context.Context, fileData []byte, uploadID string) (*FileValidationResult, error) {
	s.logger.WithField("upload_id", uploadID).Info("Validating file content")

	result := &FileValidationResult{
		FileName:     uploadID,
		OriginalName: uploadID,
		FileSize:     int64(len(fileData)),
		IsValid:      true,
		Errors:       make([]string, 0),
	}

	// Step 1: Check file size
	if result.FileSize == 0 {
		return nil, fmt.Errorf("file is empty")
	}
	if result.FileSize > 100*1024*1024 { // 100MB limit
		return nil, fmt.Errorf("file size %d exceeds maximum allowed size", result.FileSize)
	}

	// Step 2: Detect MIME type using magic numbers
	mimeType, err := s.detectMimeTypeFromContent(fileData)
	if err != nil {
		return nil, fmt.Errorf("failed to detect file type: %w", err)
	}
	result.MimeType = mimeType

	// Step 3: Validate file type is allowed
	if !s.isAllowedFileType(mimeType) {
		return nil, fmt.Errorf("file type %s is not allowed", mimeType)
	}

	// Step 4: Check for file corruption
	if err := s.checkFileIntegrity(fileData, mimeType); err != nil {
		return nil, fmt.Errorf("file appears to be corrupted: %w", err)
	}

	// Step 5: Generate content hash
	result.ContentHash = s.generateContentHash(fileData)

	// Step 6: Extract file extension
	result.Extension = s.getFileExtension(mimeType)

	s.logger.WithFields(map[string]any{
		"upload_id":    uploadID,
		"mime_type":    result.MimeType,
		"file_size":    result.FileSize,
		"content_hash": result.ContentHash,
	}).Info("File validation completed successfully")

	return result, nil
}

// detectMimeTypeFromContent detects MIME type using magic numbers
func (s *service) detectMimeTypeFromContent(data []byte) (string, error) {
	if len(data) == 0 {
		return "", fmt.Errorf("empty file")
	}

	// Use http.DetectContentType for basic detection
	mimeType := http.DetectContentType(data)

	// Additional magic number checks for better accuracy
	if len(data) >= 4 {
		// PDF
		if string(data[:4]) == "%PDF" {
			return "application/pdf", nil
		}
		// PNG
		if len(data) >= 8 && string(data[:8]) == "\x89PNG\r\n\x1a\n" {
			return "image/png", nil
		}
		// JPEG
		if len(data) >= 3 && string(data[:3]) == "\xff\xd8\xff" {
			return "image/jpeg", nil
		}
		// GIF
		if string(data[:4]) == "GIF8" {
			return "image/gif", nil
		}
	}

	return mimeType, nil
}

// isAllowedFileType checks if the MIME type is allowed
func (s *service) isAllowedFileType(mimeType string) bool {
	allowedTypes := []string{
		"image/jpeg", "image/png", "image/gif", "image/webp",
		"application/pdf", "text/plain", "application/json",
		"text/markdown", "text/csv",
		"application/msword",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	}

	for _, allowedType := range allowedTypes {
		if mimeType == allowedType {
			return true
		}
	}

	return false
}

// checkFileIntegrity checks if file is corrupted based on content
func (s *service) checkFileIntegrity(data []byte, mimeType string) error {
	if len(data) == 0 {
		return fmt.Errorf("file is empty")
	}

	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return s.checkImageIntegrity(data, mimeType)
	case mimeType == "application/pdf":
		return s.checkPDFIntegrity(data)
	case strings.HasPrefix(mimeType, "text/"):
		return s.checkTextIntegrity(data)
	default:
		// Basic check for binary files
		return s.checkBinaryIntegrity(data)
	}
}

// checkImageIntegrity validates image file integrity
func (s *service) checkImageIntegrity(data []byte, mimeType string) error {
	// Use image processor for validation
	if s.imageProcessor != nil {
		return s.imageProcessor.ValidateImageSize(data, 100*1024*1024) // 100MB max
	}

	// Basic checks
	if len(data) < 10 {
		return fmt.Errorf("image file too small")
	}

	return nil
}

// checkPDFIntegrity validates PDF file integrity
func (s *service) checkPDFIntegrity(data []byte) error {
	if len(data) < 4 || string(data[:4]) != "%PDF" {
		return fmt.Errorf("invalid PDF header")
	}

	// Check for PDF trailer
	content := string(data)
	if !strings.Contains(content, "%%EOF") {
		return fmt.Errorf("PDF missing EOF marker")
	}

	return nil
}

// checkTextIntegrity validates text file integrity
func (s *service) checkTextIntegrity(data []byte) error {
	// Check for null bytes in text files
	for _, b := range data {
		if b == 0 {
			return fmt.Errorf("text file contains null bytes")
		}
	}

	return nil
}

// checkBinaryIntegrity basic binary file validation
func (s *service) checkBinaryIntegrity(data []byte) error {
	// Basic size check
	if len(data) < 1 {
		return fmt.Errorf("binary file is empty")
	}

	return nil
}

// generateContentHash generates SHA256 hash of file content
func (s *service) generateContentHash(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// getFileExtension returns file extension based on MIME type
func (s *service) getFileExtension(mimeType string) string {
	extensions := map[string]string{
		"image/jpeg":         ".jpg",
		"image/png":          ".png",
		"image/gif":          ".gif",
		"image/webp":         ".webp",
		"application/pdf":    ".pdf",
		"text/plain":         ".txt",
		"application/json":   ".json",
		"text/markdown":      ".md",
		"text/csv":           ".csv",
		"application/msword": ".doc",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
	}

	if ext, exists := extensions[mimeType]; exists {
		return ext
	}

	return ""
}

// triggerVirusScanning triggers async virus scanning
func (s *service) triggerVirusScanning(ctx context.Context, assetID, storageKey string) {
	s.logger.WithFields(map[string]any{
		"asset_id":    assetID,
		"storage_key": storageKey,
	}).Info("Triggering virus scanning")

	// TODO: Integrate with actual virus scanning service (ClamAV/VirusTotal)
	// For now, simulate scanning and mark as safe
	go func() {
		time.Sleep(5 * time.Second) // Simulate scanning time

		// Simulate successful scan
		err := s.VirusScanCompleteWebhook(ctx, assetID, true, []string{})
		if err != nil {
			s.logger.WithError(err).Error("Failed to process virus scan result")
		}
	}()
}

// GetUsageAnalytics gets usage analytics
func (s *service) GetUsageAnalytics(ctx context.Context, userID, period string) (*models.UsageAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetPerformanceAnalytics gets performance analytics
func (s *service) GetPerformanceAnalytics(ctx context.Context, userID, period string) (*models.PerformanceAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// AdminListAssets lists assets (admin)
func (s *service) AdminListAssets(ctx context.Context, req *models.ListAssetsRequest) (*models.ListAssetsResponse, error) {
	s.logger.Info("Admin listing assets")

	query := s.readDB.Asset.Query()

	// Apply filters (admin can see all assets, including deleted)
	if req.UserID != "" {
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		query = query.Where(asset.UserID(userUUID))
	}
	if req.Type != "" {
		query = query.Where(asset.FileTypeContains(req.Type))
	}
	if req.Status != "" {
		query = query.Where(asset.StatusEQ(req.Status))
	}
	if req.FolderID != "" {
		folderUUID, err := uuid.Parse(req.FolderID)
		if err != nil {
			return nil, fmt.Errorf("invalid folder ID: %w", err)
		}
		query = query.Where(asset.FolderID(folderUUID))
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	assets, err := query.
		Offset(offset).
		Limit(req.Limit).
		Order(asset.ByCreatedAt()).
		All(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to list assets: %w", err)
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count assets: %w", err)
	}

	// Convert to response
	assetResponses := make([]models.AssetResponse, len(assets))
	for i, assetEntity := range assets {
		assetResponses[i] = *s.convertAssetToResponse(assetEntity)
	}

	return &models.ListAssetsResponse{
		Assets: assetResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + req.Limit - 1) / req.Limit,
		},
	}, nil
}

// AdminGetAsset gets asset (admin)
func (s *service) AdminGetAsset(ctx context.Context, assetID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Admin getting asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(asset.ID(assetUUID)).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("asset not found")
		}
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// AdminDeleteAsset deletes asset (admin)
func (s *service) AdminDeleteAsset(ctx context.Context, assetID string) error {
	s.logger.WithField("asset_id", assetID).Info("Admin deleting asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	// Hard delete for admin
	err = s.writeDB.Asset.DeleteOneID(assetUUID).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to delete asset: %w", err)
	}

	// TODO: Schedule storage cleanup
	return nil
}

// QuarantineAsset quarantines an asset
func (s *service) QuarantineAsset(ctx context.Context, assetID string) error {
	s.logger.WithField("asset_id", assetID).Info("Quarantining asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	// Update status to quarantined
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus("quarantined").
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to quarantine asset: %w", err)
	}

	return nil
}

// RestoreAsset restores a quarantined asset
func (s *service) RestoreAsset(ctx context.Context, assetID string) error {
	s.logger.WithField("asset_id", assetID).Info("Restoring asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	// Update status to ready
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus("ready").
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to restore asset: %w", err)
	}

	return nil
}

// GetStorageStats gets storage statistics
func (s *service) GetStorageStats(ctx context.Context) (*models.StorageAnalyticsResponse, error) {
	s.logger.Info("Getting storage stats")

	// Get all assets (admin view)
	assets, err := s.readDB.Asset.Query().All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get assets: %w", err)
	}

	var totalSize int64
	typeStats := make(map[string]models.StorageStats)
	userStats := make(map[string]models.StorageStats)

	for _, assetEntity := range assets {
		totalSize += assetEntity.FileSize

		// By type
		assetType := s.detectAssetType(assetEntity.FileType)
		stats := typeStats[assetType]
		stats.Count++
		stats.Size += assetEntity.FileSize
		typeStats[assetType] = stats

		// By user
		userID := assetEntity.UserID.String()
		userStat := userStats[userID]
		userStat.Count++
		userStat.Size += assetEntity.FileSize
		userStats[userID] = userStat
	}

	return &models.StorageAnalyticsResponse{
		TotalAssets:     int64(len(assets)),
		TotalSize:       totalSize,
		ByType:          typeStats,
		ByFolder:        userStats, // Using ByFolder for user stats
		RecentUploads:   []models.AssetResponse{},
		LargestAssets:   []models.AssetResponse{},
		QuotaUsed:       totalSize,
		QuotaLimit:      1024 * 1024 * 1024 * 1024, // 1TB for system
		QuotaPercentage: float64(totalSize) / float64(1024*1024*1024*1024) * 100,
	}, nil
}

// CleanupStorage cleans up storage
func (s *service) CleanupStorage(ctx context.Context) (*models.BulkOperationResult, error) {
	s.logger.Info("Starting storage cleanup")

	var totalCleaned int
	var errors []models.BulkOperationError

	// 1. Cleanup pending uploads older than 1 hour
	pendingCleaned, err := s.cleanupPendingUploads(ctx)
	if err != nil {
		errors = append(errors, models.BulkOperationError{
			Error:  err.Error(),
			Reason: "Failed to cleanup pending uploads",
		})
	} else {
		totalCleaned += pendingCleaned
	}

	// 2. Cleanup deleted assets older than 30 days
	deletedCleaned, err := s.cleanupDeletedAssets(ctx)
	if err != nil {
		errors = append(errors, models.BulkOperationError{
			Error:  err.Error(),
			Reason: "Failed to cleanup deleted assets",
		})
	} else {
		totalCleaned += deletedCleaned
	}

	// 3. Cleanup quarantined assets older than 30 days
	quarantinedCleaned, err := s.cleanupQuarantinedAssets(ctx)
	if err != nil {
		errors = append(errors, models.BulkOperationError{
			Error:  err.Error(),
			Reason: "Failed to cleanup quarantined assets",
		})
	} else {
		totalCleaned += quarantinedCleaned
	}

	s.logger.WithField("total_cleaned", totalCleaned).Info("Storage cleanup completed")

	return &models.BulkOperationResult{
		Total:   totalCleaned + len(errors),
		Success: totalCleaned,
		Failed:  len(errors),
		Errors:  errors,
	}, nil
}

// OptimizeStorage optimizes storage
func (s *service) OptimizeStorage(ctx context.Context) (*models.BulkOperationResult, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetAdminAnalytics gets admin analytics
func (s *service) GetAdminAnalytics(ctx context.Context, period string) (*models.StorageAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetAdminStorageAnalytics gets admin storage analytics
func (s *service) GetAdminStorageAnalytics(ctx context.Context, period string) (*models.StorageAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetAdminUsageAnalytics gets admin usage analytics
func (s *service) GetAdminUsageAnalytics(ctx context.Context, period string) (*models.UsageAnalyticsResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

// GetPublicAsset gets public asset
func (s *service) GetPublicAsset(ctx context.Context, assetID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Getting public asset")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.IsPublic(true),
				asset.DeletedAtIsNil(),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("public asset not found")
		}
		return nil, fmt.Errorf("failed to get public asset: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// GetPublicImage gets public image
func (s *service) GetPublicImage(ctx context.Context, assetID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Getting public image")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.IsPublic(true),
				asset.DeletedAtIsNil(),
				asset.FileTypeHasPrefix("image/"),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("public image not found")
		}
		return nil, fmt.Errorf("failed to get public image: %w", err)
	}

	return s.convertAssetToResponse(assetEntity), nil
}

// GetPublicThumbnail gets public thumbnail
func (s *service) GetPublicThumbnail(ctx context.Context, assetID string) (*models.AssetResponse, error) {
	s.logger.WithField("asset_id", assetID).Info("Getting public thumbnail")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return nil, fmt.Errorf("invalid asset ID: %w", err)
	}

	assetEntity, err := s.readDB.Asset.
		Query().
		Where(
			asset.And(
				asset.ID(assetUUID),
				asset.IsPublic(true),
				asset.DeletedAtIsNil(),
				asset.FileTypeHasPrefix("image/"),
			),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("public thumbnail not found")
		}
		return nil, fmt.Errorf("failed to get public thumbnail: %w", err)
	}

	// TODO: Return actual thumbnail asset instead of original
	// For now, return the original image
	return s.convertAssetToResponse(assetEntity), nil
}

// ProcessingCompleteWebhook handles processing complete webhook
func (s *service) ProcessingCompleteWebhook(ctx context.Context, assetID string, status string) error {
	s.logger.WithFields(map[string]any{
		"asset_id": assetID,
		"status":   status,
	}).Info("Processing complete webhook")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	// Update asset status
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus(status).
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to update asset status: %w", err)
	}

	return nil
}

// VirusScanCompleteWebhook handles virus scan complete webhook
func (s *service) VirusScanCompleteWebhook(ctx context.Context, assetID string, safe bool, threats []string) error {
	s.logger.WithFields(map[string]any{
		"asset_id": assetID,
		"safe":     safe,
		"threats":  threats,
	}).Info("Virus scan complete webhook")

	assetUUID, err := uuid.Parse(assetID)
	if err != nil {
		return fmt.Errorf("invalid asset ID: %w", err)
	}

	var status string
	if safe {
		status = "ready"
	} else {
		status = "quarantined"
	}

	// Update asset status based on scan result
	_, err = s.writeDB.Asset.
		UpdateOneID(assetUUID).
		SetStatus(status).
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("asset not found")
		}
		return fmt.Errorf("failed to update asset status: %w", err)
	}

	// TODO: Store threat information if any
	if !safe && len(threats) > 0 {
		s.logger.WithFields(map[string]any{
			"asset_id": assetID,
			"threats":  threats,
		}).Warn("Asset quarantined due to threats")
	}

	return nil
}

// Helper functions
func (s *service) validateUploadRequest(req *models.RequestUploadRequest) error {
	// Validate file size (max 100MB)
	if req.Size > 100*1024*1024 {
		return fmt.Errorf("file size too large: %d bytes", req.Size)
	}

	// Validate MIME type
	allowedTypes := []string{
		"image/jpeg", "image/png", "image/gif", "image/webp",
		"application/pdf", "text/plain", "application/json",
	}

	for _, allowedType := range allowedTypes {
		if req.MimeType == allowedType {
			return nil
		}
	}

	return fmt.Errorf("unsupported file type: %s", req.MimeType)
}

func (s *service) generateStorageKey(userID, filename string) string {
	return fmt.Sprintf("assets/%s/%s/%s", userID, time.Now().Format("2006/01/02"), filename)
}

func (s *service) detectAssetType(mimeType string) string {
	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return "image"
	case strings.HasPrefix(mimeType, "video/"):
		return "video"
	case strings.HasPrefix(mimeType, "audio/"):
		return "audio"
	case mimeType == "application/pdf":
		return "document"
	case strings.HasPrefix(mimeType, "text/"):
		return "document"
	default:
		return "other"
	}
}

func (s *service) convertAssetToResponse(assetEntity *ent.Asset) *models.AssetResponse {
	var folderID string
	if assetEntity.FolderID != nil {
		folderID = assetEntity.FolderID.String()
	}

	return &models.AssetResponse{
		BaseModel: models.BaseModel{
			ID:        assetEntity.ID.String(),
			CreatedAt: assetEntity.CreatedAt,
			UpdatedAt: assetEntity.UpdatedAt,
			DeletedAt: assetEntity.DeletedAt,
		},
		UserID:   assetEntity.UserID.String(),
		FolderID: folderID,
		Name:     assetEntity.FileName,
		Type:     s.detectAssetType(assetEntity.FileType),
		Status:   assetEntity.Status,
		FileInfo: models.FileInfo{
			Name:      assetEntity.FileName,
			Size:      assetEntity.FileSize,
			MimeType:  assetEntity.FileType,
			Extension: assetEntity.FileExtension,
			Hash:      assetEntity.ContentHash,
		},
		StorageInfo: models.StorageInfo{
			Key:    assetEntity.S3Key,
			Bucket: assetEntity.S3Bucket,
		},
		AccessControl: models.AccessControl{
			IsPublic: assetEntity.IsPublic,
		},
	}
}

// Cleanup helper methods

// cleanupPendingUploads cleans up uploads that are stuck in pending/validating state
func (s *service) cleanupPendingUploads(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-1 * time.Hour) // 1 hour TTL

	// Find assets stuck in pending/validating state
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.Or(
				asset.StatusEQ("pending"),
				asset.StatusEQ("validating"),
				asset.StatusEQ("uploaded"),
			),
			asset.CreatedAtLT(cutoffTime),
			asset.DeletedAtIsNil(),
		).
		Limit(100). // Batch size
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query pending uploads: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		// Check if file exists in storage
		if s.storageClient != nil {
			exists, err := s.storageClient.FileExists(ctx, assetEntity.S3Key)
			if err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Warn("Failed to check file existence")
			} else if exists {
				// Delete file from storage
				if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
					s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete file from storage")
					continue
				}
			}
		}

		// Mark asset as expired
		_, err = s.writeDB.Asset.
			UpdateOneID(assetEntity.ID).
			SetStatus("expired").
			SetUpdatedAt(time.Now()).
			Save(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to mark asset as expired")
			continue
		}

		s.logger.WithFields(map[string]any{
			"asset_id": assetEntity.ID.String(),
			"status":   assetEntity.Status,
			"age":      time.Since(assetEntity.CreatedAt),
		}).Info("Cleaned up pending upload")

		cleanedCount++
	}

	return cleanedCount, nil
}

// cleanupDeletedAssets permanently removes assets that have been soft-deleted for 30 days
func (s *service) cleanupDeletedAssets(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-30 * 24 * time.Hour) // 30 days TTL

	// Find soft-deleted assets older than TTL
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.DeletedAtNotNil(),
			asset.DeletedAtLT(cutoffTime),
		).
		Limit(100). // Batch size
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query deleted assets: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		// Delete file from storage
		if s.storageClient != nil {
			if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete file from storage")
			}
		}

		// Hard delete from database
		err = s.writeDB.Asset.DeleteOneID(assetEntity.ID).Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to hard delete asset")
			continue
		}

		s.logger.WithFields(map[string]any{
			"asset_id":   assetEntity.ID.String(),
			"deleted_at": assetEntity.DeletedAt,
			"age":        time.Since(*assetEntity.DeletedAt),
		}).Info("Permanently deleted asset")

		cleanedCount++
	}

	return cleanedCount, nil
}

// cleanupQuarantinedAssets cleans up quarantined assets older than 30 days
func (s *service) cleanupQuarantinedAssets(ctx context.Context) (int, error) {
	cutoffTime := time.Now().Add(-30 * 24 * time.Hour) // 30 days TTL

	// Find quarantined assets older than TTL
	assets, err := s.readDB.Asset.Query().
		Where(
			asset.StatusEQ("quarantined"),
			asset.CreatedAtLT(cutoffTime),
			asset.DeletedAtIsNil(),
		).
		Limit(100). // Batch size
		All(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to query quarantined assets: %w", err)
	}

	if len(assets) == 0 {
		return 0, nil
	}

	cleanedCount := 0
	for _, assetEntity := range assets {
		// Delete file from storage
		if s.storageClient != nil {
			if err := s.storageClient.DeleteFile(ctx, assetEntity.S3Key); err != nil {
				s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete quarantined file from storage")
			}
		}

		// Hard delete quarantined asset
		err = s.writeDB.Asset.DeleteOneID(assetEntity.ID).Exec(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("asset_id", assetEntity.ID).Error("Failed to delete quarantined asset")
			continue
		}

		s.logger.WithFields(map[string]any{
			"asset_id": assetEntity.ID.String(),
			"age":      time.Since(assetEntity.CreatedAt),
		}).Info("Deleted quarantined asset")

		cleanedCount++
	}

	return cleanedCount, nil
}

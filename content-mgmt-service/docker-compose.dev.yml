version: '3.8'

services:
  # PostgreSQL Database for development
  postgres:
    image: postgres:15-alpine
    container_name: content-mgmt-postgres
    environment:
      POSTGRES_DB: content_mgmt_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5433:5432"  # Different port to avoid conflict with user service
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d content_mgmt_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - content-mgmt-network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: content-mgmt-redis
    ports:
      - "6380:6379"  # Different port to avoid conflict
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - content-mgmt-network

  # MinIO for S3-compatible object storage
  minio:
    image: minio/minio:latest
    container_name: content-mgmt-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"   # API port
      - "9001:9001"   # Console port
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - content-mgmt-network

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: content-mgmt-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5051:80"  # Different port to avoid conflict
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - content-mgmt-network

  # Elasticsearch for search functionality (optional)
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: content-mgmt-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - content-mgmt-network

  # Kibana for Elasticsearch visualization (optional)
  kibana:
    image: kibana:8.8.0
    container_name: content-mgmt-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - content-mgmt-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  pgadmin_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  content-mgmt-network:
    driver: bridge

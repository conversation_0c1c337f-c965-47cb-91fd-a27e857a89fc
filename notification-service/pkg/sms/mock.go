package sms

import (
	"context"
	"fmt"

	"github.com/social-content-ai/notification-service/pkg/channels"
	"github.com/social-content-ai/pkg-shared/logging"
)

// MockSender implements SMSSender for testing and development
type MockSender struct {
	logger logging.Logger
}

// NewMockSender creates a new mock SMS sender
func NewMockSender(logger logging.Logger) channels.SMSSender {
	return &MockSender{
		logger: logger,
	}
}

// SendSMS simulates sending an SMS message
func (m *MockSender) SendSMS(ctx context.Context, message *channels.SMSMessage) error {
	m.logger.WithFields(map[string]interface{}{
		"to":   message.To,
		"body": message.Body,
	}).Info("Mock SMS sent")

	// Validate message
	if message.To == "" {
		return fmt.Errorf("recipient phone number is required")
	}
	if message.Body == "" {
		return fmt.Errorf("message body is required")
	}

	// Simulate successful sending
	m.logger.WithField("to", message.To).Info("Mock SMS sent successfully")
	return nil
}

package handlers

import (
	"github.com/social-content-ai/integration-service/usecase/integration"
	"github.com/social-content-ai/integration-service/usecase/platform"
	"github.com/social-content-ai/integration-service/usecase/publishing"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// BaseHandler contains common dependencies for all handlers
type BaseHandler struct {
	integrationUseCase integration.UseCase
	platformUseCase    platform.UseCase
	publishingUseCase  publishing.UseCase
	userClient         userv1.AuthServiceClient
	logger             logging.Logger
}

// NewBaseHandler creates a new base handler with all dependencies
func NewBaseHandler(
	integrationUseCase integration.UseCase,
	platformUseCase platform.UseCase,
	publishingUseCase publishing.UseCase,
	userClient userv1.AuthServiceClient,
	logger logging.Logger,
) *BaseHandler {
	return &BaseHandler{
		integrationUseCase: integrationUseCase,
		platformUseCase:    platformUseCase,
		publishingUseCase:  publishingUseCase,
		userClient:         userClient,
		logger:             logger,
	}
}

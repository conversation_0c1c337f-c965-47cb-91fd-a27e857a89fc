// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/asset-service/ent/asset"
	"github.com/social-content-ai/asset-service/ent/folder"
	"github.com/social-content-ai/asset-service/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAsset  = "Asset"
	TypeFolder = "Folder"
)

// AssetMutation represents an operation that mutates the Asset nodes in the graph.
type AssetMutation struct {
	config
	op             Op
	typ            string
	id             *uuid.UUID
	user_id        *uuid.UUID
	workspace_id   *string
	file_name      *string
	original_name  *string
	file_type      *string
	file_extension *string
	s3_key         *string
	s3_bucket      *string
	content_hash   *string
	purpose        *string
	file_size      *int64
	addfile_size   *int64
	thumbnail_url  *string
	metadata       *map[string]interface{}
	status         *string
	is_public      *bool
	created_at     *time.Time
	updated_at     *time.Time
	deleted_at     *time.Time
	clearedFields  map[string]struct{}
	folder         *uuid.UUID
	clearedfolder  bool
	done           bool
	oldValue       func(context.Context) (*Asset, error)
	predicates     []predicate.Asset
}

var _ ent.Mutation = (*AssetMutation)(nil)

// assetOption allows management of the mutation configuration using functional options.
type assetOption func(*AssetMutation)

// newAssetMutation creates new mutation for the Asset entity.
func newAssetMutation(c config, op Op, opts ...assetOption) *AssetMutation {
	m := &AssetMutation{
		config:        c,
		op:            op,
		typ:           TypeAsset,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAssetID sets the ID field of the mutation.
func withAssetID(id uuid.UUID) assetOption {
	return func(m *AssetMutation) {
		var (
			err   error
			once  sync.Once
			value *Asset
		)
		m.oldValue = func(ctx context.Context) (*Asset, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Asset.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAsset sets the old Asset of the mutation.
func withAsset(node *Asset) assetOption {
	return func(m *AssetMutation) {
		m.oldValue = func(context.Context) (*Asset, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AssetMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AssetMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Asset entities.
func (m *AssetMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AssetMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AssetMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Asset.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *AssetMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *AssetMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *AssetMutation) ResetUserID() {
	m.user_id = nil
}

// SetWorkspaceID sets the "workspace_id" field.
func (m *AssetMutation) SetWorkspaceID(s string) {
	m.workspace_id = &s
}

// WorkspaceID returns the value of the "workspace_id" field in the mutation.
func (m *AssetMutation) WorkspaceID() (r string, exists bool) {
	v := m.workspace_id
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkspaceID returns the old "workspace_id" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldWorkspaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkspaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkspaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkspaceID: %w", err)
	}
	return oldValue.WorkspaceID, nil
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (m *AssetMutation) ClearWorkspaceID() {
	m.workspace_id = nil
	m.clearedFields[asset.FieldWorkspaceID] = struct{}{}
}

// WorkspaceIDCleared returns if the "workspace_id" field was cleared in this mutation.
func (m *AssetMutation) WorkspaceIDCleared() bool {
	_, ok := m.clearedFields[asset.FieldWorkspaceID]
	return ok
}

// ResetWorkspaceID resets all changes to the "workspace_id" field.
func (m *AssetMutation) ResetWorkspaceID() {
	m.workspace_id = nil
	delete(m.clearedFields, asset.FieldWorkspaceID)
}

// SetFolderID sets the "folder_id" field.
func (m *AssetMutation) SetFolderID(u uuid.UUID) {
	m.folder = &u
}

// FolderID returns the value of the "folder_id" field in the mutation.
func (m *AssetMutation) FolderID() (r uuid.UUID, exists bool) {
	v := m.folder
	if v == nil {
		return
	}
	return *v, true
}

// OldFolderID returns the old "folder_id" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldFolderID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFolderID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFolderID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFolderID: %w", err)
	}
	return oldValue.FolderID, nil
}

// ClearFolderID clears the value of the "folder_id" field.
func (m *AssetMutation) ClearFolderID() {
	m.folder = nil
	m.clearedFields[asset.FieldFolderID] = struct{}{}
}

// FolderIDCleared returns if the "folder_id" field was cleared in this mutation.
func (m *AssetMutation) FolderIDCleared() bool {
	_, ok := m.clearedFields[asset.FieldFolderID]
	return ok
}

// ResetFolderID resets all changes to the "folder_id" field.
func (m *AssetMutation) ResetFolderID() {
	m.folder = nil
	delete(m.clearedFields, asset.FieldFolderID)
}

// SetFileName sets the "file_name" field.
func (m *AssetMutation) SetFileName(s string) {
	m.file_name = &s
}

// FileName returns the value of the "file_name" field in the mutation.
func (m *AssetMutation) FileName() (r string, exists bool) {
	v := m.file_name
	if v == nil {
		return
	}
	return *v, true
}

// OldFileName returns the old "file_name" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldFileName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileName: %w", err)
	}
	return oldValue.FileName, nil
}

// ResetFileName resets all changes to the "file_name" field.
func (m *AssetMutation) ResetFileName() {
	m.file_name = nil
}

// SetOriginalName sets the "original_name" field.
func (m *AssetMutation) SetOriginalName(s string) {
	m.original_name = &s
}

// OriginalName returns the value of the "original_name" field in the mutation.
func (m *AssetMutation) OriginalName() (r string, exists bool) {
	v := m.original_name
	if v == nil {
		return
	}
	return *v, true
}

// OldOriginalName returns the old "original_name" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldOriginalName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOriginalName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOriginalName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOriginalName: %w", err)
	}
	return oldValue.OriginalName, nil
}

// ResetOriginalName resets all changes to the "original_name" field.
func (m *AssetMutation) ResetOriginalName() {
	m.original_name = nil
}

// SetFileType sets the "file_type" field.
func (m *AssetMutation) SetFileType(s string) {
	m.file_type = &s
}

// FileType returns the value of the "file_type" field in the mutation.
func (m *AssetMutation) FileType() (r string, exists bool) {
	v := m.file_type
	if v == nil {
		return
	}
	return *v, true
}

// OldFileType returns the old "file_type" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldFileType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileType: %w", err)
	}
	return oldValue.FileType, nil
}

// ResetFileType resets all changes to the "file_type" field.
func (m *AssetMutation) ResetFileType() {
	m.file_type = nil
}

// SetFileExtension sets the "file_extension" field.
func (m *AssetMutation) SetFileExtension(s string) {
	m.file_extension = &s
}

// FileExtension returns the value of the "file_extension" field in the mutation.
func (m *AssetMutation) FileExtension() (r string, exists bool) {
	v := m.file_extension
	if v == nil {
		return
	}
	return *v, true
}

// OldFileExtension returns the old "file_extension" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldFileExtension(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileExtension is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileExtension requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileExtension: %w", err)
	}
	return oldValue.FileExtension, nil
}

// ClearFileExtension clears the value of the "file_extension" field.
func (m *AssetMutation) ClearFileExtension() {
	m.file_extension = nil
	m.clearedFields[asset.FieldFileExtension] = struct{}{}
}

// FileExtensionCleared returns if the "file_extension" field was cleared in this mutation.
func (m *AssetMutation) FileExtensionCleared() bool {
	_, ok := m.clearedFields[asset.FieldFileExtension]
	return ok
}

// ResetFileExtension resets all changes to the "file_extension" field.
func (m *AssetMutation) ResetFileExtension() {
	m.file_extension = nil
	delete(m.clearedFields, asset.FieldFileExtension)
}

// SetS3Key sets the "s3_key" field.
func (m *AssetMutation) SetS3Key(s string) {
	m.s3_key = &s
}

// S3Key returns the value of the "s3_key" field in the mutation.
func (m *AssetMutation) S3Key() (r string, exists bool) {
	v := m.s3_key
	if v == nil {
		return
	}
	return *v, true
}

// OldS3Key returns the old "s3_key" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldS3Key(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldS3Key is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldS3Key requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldS3Key: %w", err)
	}
	return oldValue.S3Key, nil
}

// ResetS3Key resets all changes to the "s3_key" field.
func (m *AssetMutation) ResetS3Key() {
	m.s3_key = nil
}

// SetS3Bucket sets the "s3_bucket" field.
func (m *AssetMutation) SetS3Bucket(s string) {
	m.s3_bucket = &s
}

// S3Bucket returns the value of the "s3_bucket" field in the mutation.
func (m *AssetMutation) S3Bucket() (r string, exists bool) {
	v := m.s3_bucket
	if v == nil {
		return
	}
	return *v, true
}

// OldS3Bucket returns the old "s3_bucket" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldS3Bucket(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldS3Bucket is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldS3Bucket requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldS3Bucket: %w", err)
	}
	return oldValue.S3Bucket, nil
}

// ResetS3Bucket resets all changes to the "s3_bucket" field.
func (m *AssetMutation) ResetS3Bucket() {
	m.s3_bucket = nil
}

// SetContentHash sets the "content_hash" field.
func (m *AssetMutation) SetContentHash(s string) {
	m.content_hash = &s
}

// ContentHash returns the value of the "content_hash" field in the mutation.
func (m *AssetMutation) ContentHash() (r string, exists bool) {
	v := m.content_hash
	if v == nil {
		return
	}
	return *v, true
}

// OldContentHash returns the old "content_hash" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldContentHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContentHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContentHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContentHash: %w", err)
	}
	return oldValue.ContentHash, nil
}

// ClearContentHash clears the value of the "content_hash" field.
func (m *AssetMutation) ClearContentHash() {
	m.content_hash = nil
	m.clearedFields[asset.FieldContentHash] = struct{}{}
}

// ContentHashCleared returns if the "content_hash" field was cleared in this mutation.
func (m *AssetMutation) ContentHashCleared() bool {
	_, ok := m.clearedFields[asset.FieldContentHash]
	return ok
}

// ResetContentHash resets all changes to the "content_hash" field.
func (m *AssetMutation) ResetContentHash() {
	m.content_hash = nil
	delete(m.clearedFields, asset.FieldContentHash)
}

// SetPurpose sets the "purpose" field.
func (m *AssetMutation) SetPurpose(s string) {
	m.purpose = &s
}

// Purpose returns the value of the "purpose" field in the mutation.
func (m *AssetMutation) Purpose() (r string, exists bool) {
	v := m.purpose
	if v == nil {
		return
	}
	return *v, true
}

// OldPurpose returns the old "purpose" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldPurpose(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPurpose is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPurpose requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPurpose: %w", err)
	}
	return oldValue.Purpose, nil
}

// ResetPurpose resets all changes to the "purpose" field.
func (m *AssetMutation) ResetPurpose() {
	m.purpose = nil
}

// SetFileSize sets the "file_size" field.
func (m *AssetMutation) SetFileSize(i int64) {
	m.file_size = &i
	m.addfile_size = nil
}

// FileSize returns the value of the "file_size" field in the mutation.
func (m *AssetMutation) FileSize() (r int64, exists bool) {
	v := m.file_size
	if v == nil {
		return
	}
	return *v, true
}

// OldFileSize returns the old "file_size" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldFileSize(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileSize is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileSize requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileSize: %w", err)
	}
	return oldValue.FileSize, nil
}

// AddFileSize adds i to the "file_size" field.
func (m *AssetMutation) AddFileSize(i int64) {
	if m.addfile_size != nil {
		*m.addfile_size += i
	} else {
		m.addfile_size = &i
	}
}

// AddedFileSize returns the value that was added to the "file_size" field in this mutation.
func (m *AssetMutation) AddedFileSize() (r int64, exists bool) {
	v := m.addfile_size
	if v == nil {
		return
	}
	return *v, true
}

// ResetFileSize resets all changes to the "file_size" field.
func (m *AssetMutation) ResetFileSize() {
	m.file_size = nil
	m.addfile_size = nil
}

// SetThumbnailURL sets the "thumbnail_url" field.
func (m *AssetMutation) SetThumbnailURL(s string) {
	m.thumbnail_url = &s
}

// ThumbnailURL returns the value of the "thumbnail_url" field in the mutation.
func (m *AssetMutation) ThumbnailURL() (r string, exists bool) {
	v := m.thumbnail_url
	if v == nil {
		return
	}
	return *v, true
}

// OldThumbnailURL returns the old "thumbnail_url" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldThumbnailURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldThumbnailURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldThumbnailURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldThumbnailURL: %w", err)
	}
	return oldValue.ThumbnailURL, nil
}

// ClearThumbnailURL clears the value of the "thumbnail_url" field.
func (m *AssetMutation) ClearThumbnailURL() {
	m.thumbnail_url = nil
	m.clearedFields[asset.FieldThumbnailURL] = struct{}{}
}

// ThumbnailURLCleared returns if the "thumbnail_url" field was cleared in this mutation.
func (m *AssetMutation) ThumbnailURLCleared() bool {
	_, ok := m.clearedFields[asset.FieldThumbnailURL]
	return ok
}

// ResetThumbnailURL resets all changes to the "thumbnail_url" field.
func (m *AssetMutation) ResetThumbnailURL() {
	m.thumbnail_url = nil
	delete(m.clearedFields, asset.FieldThumbnailURL)
}

// SetMetadata sets the "metadata" field.
func (m *AssetMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *AssetMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *AssetMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[asset.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *AssetMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[asset.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *AssetMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, asset.FieldMetadata)
}

// SetStatus sets the "status" field.
func (m *AssetMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *AssetMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *AssetMutation) ResetStatus() {
	m.status = nil
}

// SetIsPublic sets the "is_public" field.
func (m *AssetMutation) SetIsPublic(b bool) {
	m.is_public = &b
}

// IsPublic returns the value of the "is_public" field in the mutation.
func (m *AssetMutation) IsPublic() (r bool, exists bool) {
	v := m.is_public
	if v == nil {
		return
	}
	return *v, true
}

// OldIsPublic returns the old "is_public" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldIsPublic(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsPublic is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsPublic requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsPublic: %w", err)
	}
	return oldValue.IsPublic, nil
}

// ResetIsPublic resets all changes to the "is_public" field.
func (m *AssetMutation) ResetIsPublic() {
	m.is_public = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *AssetMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AssetMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AssetMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AssetMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AssetMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AssetMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *AssetMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *AssetMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Asset entity.
// If the Asset object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssetMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *AssetMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[asset.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *AssetMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[asset.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *AssetMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, asset.FieldDeletedAt)
}

// ClearFolder clears the "folder" edge to the Folder entity.
func (m *AssetMutation) ClearFolder() {
	m.clearedfolder = true
	m.clearedFields[asset.FieldFolderID] = struct{}{}
}

// FolderCleared reports if the "folder" edge to the Folder entity was cleared.
func (m *AssetMutation) FolderCleared() bool {
	return m.FolderIDCleared() || m.clearedfolder
}

// FolderIDs returns the "folder" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// FolderID instead. It exists only for internal usage by the builders.
func (m *AssetMutation) FolderIDs() (ids []uuid.UUID) {
	if id := m.folder; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetFolder resets all changes to the "folder" edge.
func (m *AssetMutation) ResetFolder() {
	m.folder = nil
	m.clearedfolder = false
}

// Where appends a list predicates to the AssetMutation builder.
func (m *AssetMutation) Where(ps ...predicate.Asset) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AssetMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AssetMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Asset, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AssetMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AssetMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Asset).
func (m *AssetMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AssetMutation) Fields() []string {
	fields := make([]string, 0, 19)
	if m.user_id != nil {
		fields = append(fields, asset.FieldUserID)
	}
	if m.workspace_id != nil {
		fields = append(fields, asset.FieldWorkspaceID)
	}
	if m.folder != nil {
		fields = append(fields, asset.FieldFolderID)
	}
	if m.file_name != nil {
		fields = append(fields, asset.FieldFileName)
	}
	if m.original_name != nil {
		fields = append(fields, asset.FieldOriginalName)
	}
	if m.file_type != nil {
		fields = append(fields, asset.FieldFileType)
	}
	if m.file_extension != nil {
		fields = append(fields, asset.FieldFileExtension)
	}
	if m.s3_key != nil {
		fields = append(fields, asset.FieldS3Key)
	}
	if m.s3_bucket != nil {
		fields = append(fields, asset.FieldS3Bucket)
	}
	if m.content_hash != nil {
		fields = append(fields, asset.FieldContentHash)
	}
	if m.purpose != nil {
		fields = append(fields, asset.FieldPurpose)
	}
	if m.file_size != nil {
		fields = append(fields, asset.FieldFileSize)
	}
	if m.thumbnail_url != nil {
		fields = append(fields, asset.FieldThumbnailURL)
	}
	if m.metadata != nil {
		fields = append(fields, asset.FieldMetadata)
	}
	if m.status != nil {
		fields = append(fields, asset.FieldStatus)
	}
	if m.is_public != nil {
		fields = append(fields, asset.FieldIsPublic)
	}
	if m.created_at != nil {
		fields = append(fields, asset.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, asset.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, asset.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AssetMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case asset.FieldUserID:
		return m.UserID()
	case asset.FieldWorkspaceID:
		return m.WorkspaceID()
	case asset.FieldFolderID:
		return m.FolderID()
	case asset.FieldFileName:
		return m.FileName()
	case asset.FieldOriginalName:
		return m.OriginalName()
	case asset.FieldFileType:
		return m.FileType()
	case asset.FieldFileExtension:
		return m.FileExtension()
	case asset.FieldS3Key:
		return m.S3Key()
	case asset.FieldS3Bucket:
		return m.S3Bucket()
	case asset.FieldContentHash:
		return m.ContentHash()
	case asset.FieldPurpose:
		return m.Purpose()
	case asset.FieldFileSize:
		return m.FileSize()
	case asset.FieldThumbnailURL:
		return m.ThumbnailURL()
	case asset.FieldMetadata:
		return m.Metadata()
	case asset.FieldStatus:
		return m.Status()
	case asset.FieldIsPublic:
		return m.IsPublic()
	case asset.FieldCreatedAt:
		return m.CreatedAt()
	case asset.FieldUpdatedAt:
		return m.UpdatedAt()
	case asset.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AssetMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case asset.FieldUserID:
		return m.OldUserID(ctx)
	case asset.FieldWorkspaceID:
		return m.OldWorkspaceID(ctx)
	case asset.FieldFolderID:
		return m.OldFolderID(ctx)
	case asset.FieldFileName:
		return m.OldFileName(ctx)
	case asset.FieldOriginalName:
		return m.OldOriginalName(ctx)
	case asset.FieldFileType:
		return m.OldFileType(ctx)
	case asset.FieldFileExtension:
		return m.OldFileExtension(ctx)
	case asset.FieldS3Key:
		return m.OldS3Key(ctx)
	case asset.FieldS3Bucket:
		return m.OldS3Bucket(ctx)
	case asset.FieldContentHash:
		return m.OldContentHash(ctx)
	case asset.FieldPurpose:
		return m.OldPurpose(ctx)
	case asset.FieldFileSize:
		return m.OldFileSize(ctx)
	case asset.FieldThumbnailURL:
		return m.OldThumbnailURL(ctx)
	case asset.FieldMetadata:
		return m.OldMetadata(ctx)
	case asset.FieldStatus:
		return m.OldStatus(ctx)
	case asset.FieldIsPublic:
		return m.OldIsPublic(ctx)
	case asset.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case asset.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case asset.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Asset field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AssetMutation) SetField(name string, value ent.Value) error {
	switch name {
	case asset.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case asset.FieldWorkspaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkspaceID(v)
		return nil
	case asset.FieldFolderID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFolderID(v)
		return nil
	case asset.FieldFileName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileName(v)
		return nil
	case asset.FieldOriginalName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOriginalName(v)
		return nil
	case asset.FieldFileType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileType(v)
		return nil
	case asset.FieldFileExtension:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileExtension(v)
		return nil
	case asset.FieldS3Key:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetS3Key(v)
		return nil
	case asset.FieldS3Bucket:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetS3Bucket(v)
		return nil
	case asset.FieldContentHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContentHash(v)
		return nil
	case asset.FieldPurpose:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPurpose(v)
		return nil
	case asset.FieldFileSize:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileSize(v)
		return nil
	case asset.FieldThumbnailURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetThumbnailURL(v)
		return nil
	case asset.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case asset.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case asset.FieldIsPublic:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsPublic(v)
		return nil
	case asset.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case asset.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case asset.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Asset field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AssetMutation) AddedFields() []string {
	var fields []string
	if m.addfile_size != nil {
		fields = append(fields, asset.FieldFileSize)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AssetMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case asset.FieldFileSize:
		return m.AddedFileSize()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AssetMutation) AddField(name string, value ent.Value) error {
	switch name {
	case asset.FieldFileSize:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddFileSize(v)
		return nil
	}
	return fmt.Errorf("unknown Asset numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AssetMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(asset.FieldWorkspaceID) {
		fields = append(fields, asset.FieldWorkspaceID)
	}
	if m.FieldCleared(asset.FieldFolderID) {
		fields = append(fields, asset.FieldFolderID)
	}
	if m.FieldCleared(asset.FieldFileExtension) {
		fields = append(fields, asset.FieldFileExtension)
	}
	if m.FieldCleared(asset.FieldContentHash) {
		fields = append(fields, asset.FieldContentHash)
	}
	if m.FieldCleared(asset.FieldThumbnailURL) {
		fields = append(fields, asset.FieldThumbnailURL)
	}
	if m.FieldCleared(asset.FieldMetadata) {
		fields = append(fields, asset.FieldMetadata)
	}
	if m.FieldCleared(asset.FieldDeletedAt) {
		fields = append(fields, asset.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AssetMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AssetMutation) ClearField(name string) error {
	switch name {
	case asset.FieldWorkspaceID:
		m.ClearWorkspaceID()
		return nil
	case asset.FieldFolderID:
		m.ClearFolderID()
		return nil
	case asset.FieldFileExtension:
		m.ClearFileExtension()
		return nil
	case asset.FieldContentHash:
		m.ClearContentHash()
		return nil
	case asset.FieldThumbnailURL:
		m.ClearThumbnailURL()
		return nil
	case asset.FieldMetadata:
		m.ClearMetadata()
		return nil
	case asset.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Asset nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AssetMutation) ResetField(name string) error {
	switch name {
	case asset.FieldUserID:
		m.ResetUserID()
		return nil
	case asset.FieldWorkspaceID:
		m.ResetWorkspaceID()
		return nil
	case asset.FieldFolderID:
		m.ResetFolderID()
		return nil
	case asset.FieldFileName:
		m.ResetFileName()
		return nil
	case asset.FieldOriginalName:
		m.ResetOriginalName()
		return nil
	case asset.FieldFileType:
		m.ResetFileType()
		return nil
	case asset.FieldFileExtension:
		m.ResetFileExtension()
		return nil
	case asset.FieldS3Key:
		m.ResetS3Key()
		return nil
	case asset.FieldS3Bucket:
		m.ResetS3Bucket()
		return nil
	case asset.FieldContentHash:
		m.ResetContentHash()
		return nil
	case asset.FieldPurpose:
		m.ResetPurpose()
		return nil
	case asset.FieldFileSize:
		m.ResetFileSize()
		return nil
	case asset.FieldThumbnailURL:
		m.ResetThumbnailURL()
		return nil
	case asset.FieldMetadata:
		m.ResetMetadata()
		return nil
	case asset.FieldStatus:
		m.ResetStatus()
		return nil
	case asset.FieldIsPublic:
		m.ResetIsPublic()
		return nil
	case asset.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case asset.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case asset.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Asset field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AssetMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.folder != nil {
		edges = append(edges, asset.EdgeFolder)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AssetMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case asset.EdgeFolder:
		if id := m.folder; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AssetMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AssetMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AssetMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedfolder {
		edges = append(edges, asset.EdgeFolder)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AssetMutation) EdgeCleared(name string) bool {
	switch name {
	case asset.EdgeFolder:
		return m.clearedfolder
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AssetMutation) ClearEdge(name string) error {
	switch name {
	case asset.EdgeFolder:
		m.ClearFolder()
		return nil
	}
	return fmt.Errorf("unknown Asset unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AssetMutation) ResetEdge(name string) error {
	switch name {
	case asset.EdgeFolder:
		m.ResetFolder()
		return nil
	}
	return fmt.Errorf("unknown Asset edge %s", name)
}

// FolderMutation represents an operation that mutates the Folder nodes in the graph.
type FolderMutation struct {
	config
	op                Op
	typ               string
	id                *uuid.UUID
	user_id           *uuid.UUID
	name              *string
	description       *string
	_path             *string
	level             *int
	addlevel          *int
	is_shared         *bool
	shared_with       *[]string
	appendshared_with []string
	permissions       *[]string
	appendpermissions []string
	asset_count       *int64
	addasset_count    *int64
	total_size        *int64
	addtotal_size     *int64
	created_at        *time.Time
	updated_at        *time.Time
	deleted_at        *time.Time
	clearedFields     map[string]struct{}
	parent            *uuid.UUID
	clearedparent     bool
	children          map[uuid.UUID]struct{}
	removedchildren   map[uuid.UUID]struct{}
	clearedchildren   bool
	assets            map[uuid.UUID]struct{}
	removedassets     map[uuid.UUID]struct{}
	clearedassets     bool
	done              bool
	oldValue          func(context.Context) (*Folder, error)
	predicates        []predicate.Folder
}

var _ ent.Mutation = (*FolderMutation)(nil)

// folderOption allows management of the mutation configuration using functional options.
type folderOption func(*FolderMutation)

// newFolderMutation creates new mutation for the Folder entity.
func newFolderMutation(c config, op Op, opts ...folderOption) *FolderMutation {
	m := &FolderMutation{
		config:        c,
		op:            op,
		typ:           TypeFolder,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFolderID sets the ID field of the mutation.
func withFolderID(id uuid.UUID) folderOption {
	return func(m *FolderMutation) {
		var (
			err   error
			once  sync.Once
			value *Folder
		)
		m.oldValue = func(ctx context.Context) (*Folder, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Folder.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFolder sets the old Folder of the mutation.
func withFolder(node *Folder) folderOption {
	return func(m *FolderMutation) {
		m.oldValue = func(context.Context) (*Folder, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FolderMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FolderMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Folder entities.
func (m *FolderMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FolderMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FolderMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Folder.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *FolderMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *FolderMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *FolderMutation) ResetUserID() {
	m.user_id = nil
}

// SetParentID sets the "parent_id" field.
func (m *FolderMutation) SetParentID(u uuid.UUID) {
	m.parent = &u
}

// ParentID returns the value of the "parent_id" field in the mutation.
func (m *FolderMutation) ParentID() (r uuid.UUID, exists bool) {
	v := m.parent
	if v == nil {
		return
	}
	return *v, true
}

// OldParentID returns the old "parent_id" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldParentID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldParentID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldParentID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldParentID: %w", err)
	}
	return oldValue.ParentID, nil
}

// ClearParentID clears the value of the "parent_id" field.
func (m *FolderMutation) ClearParentID() {
	m.parent = nil
	m.clearedFields[folder.FieldParentID] = struct{}{}
}

// ParentIDCleared returns if the "parent_id" field was cleared in this mutation.
func (m *FolderMutation) ParentIDCleared() bool {
	_, ok := m.clearedFields[folder.FieldParentID]
	return ok
}

// ResetParentID resets all changes to the "parent_id" field.
func (m *FolderMutation) ResetParentID() {
	m.parent = nil
	delete(m.clearedFields, folder.FieldParentID)
}

// SetName sets the "name" field.
func (m *FolderMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *FolderMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *FolderMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *FolderMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *FolderMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *FolderMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[folder.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *FolderMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[folder.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *FolderMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, folder.FieldDescription)
}

// SetPath sets the "path" field.
func (m *FolderMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *FolderMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *FolderMutation) ResetPath() {
	m._path = nil
}

// SetLevel sets the "level" field.
func (m *FolderMutation) SetLevel(i int) {
	m.level = &i
	m.addlevel = nil
}

// Level returns the value of the "level" field in the mutation.
func (m *FolderMutation) Level() (r int, exists bool) {
	v := m.level
	if v == nil {
		return
	}
	return *v, true
}

// OldLevel returns the old "level" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldLevel(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLevel is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLevel requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLevel: %w", err)
	}
	return oldValue.Level, nil
}

// AddLevel adds i to the "level" field.
func (m *FolderMutation) AddLevel(i int) {
	if m.addlevel != nil {
		*m.addlevel += i
	} else {
		m.addlevel = &i
	}
}

// AddedLevel returns the value that was added to the "level" field in this mutation.
func (m *FolderMutation) AddedLevel() (r int, exists bool) {
	v := m.addlevel
	if v == nil {
		return
	}
	return *v, true
}

// ResetLevel resets all changes to the "level" field.
func (m *FolderMutation) ResetLevel() {
	m.level = nil
	m.addlevel = nil
}

// SetIsShared sets the "is_shared" field.
func (m *FolderMutation) SetIsShared(b bool) {
	m.is_shared = &b
}

// IsShared returns the value of the "is_shared" field in the mutation.
func (m *FolderMutation) IsShared() (r bool, exists bool) {
	v := m.is_shared
	if v == nil {
		return
	}
	return *v, true
}

// OldIsShared returns the old "is_shared" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldIsShared(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsShared is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsShared requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsShared: %w", err)
	}
	return oldValue.IsShared, nil
}

// ResetIsShared resets all changes to the "is_shared" field.
func (m *FolderMutation) ResetIsShared() {
	m.is_shared = nil
}

// SetSharedWith sets the "shared_with" field.
func (m *FolderMutation) SetSharedWith(s []string) {
	m.shared_with = &s
	m.appendshared_with = nil
}

// SharedWith returns the value of the "shared_with" field in the mutation.
func (m *FolderMutation) SharedWith() (r []string, exists bool) {
	v := m.shared_with
	if v == nil {
		return
	}
	return *v, true
}

// OldSharedWith returns the old "shared_with" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldSharedWith(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSharedWith is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSharedWith requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSharedWith: %w", err)
	}
	return oldValue.SharedWith, nil
}

// AppendSharedWith adds s to the "shared_with" field.
func (m *FolderMutation) AppendSharedWith(s []string) {
	m.appendshared_with = append(m.appendshared_with, s...)
}

// AppendedSharedWith returns the list of values that were appended to the "shared_with" field in this mutation.
func (m *FolderMutation) AppendedSharedWith() ([]string, bool) {
	if len(m.appendshared_with) == 0 {
		return nil, false
	}
	return m.appendshared_with, true
}

// ClearSharedWith clears the value of the "shared_with" field.
func (m *FolderMutation) ClearSharedWith() {
	m.shared_with = nil
	m.appendshared_with = nil
	m.clearedFields[folder.FieldSharedWith] = struct{}{}
}

// SharedWithCleared returns if the "shared_with" field was cleared in this mutation.
func (m *FolderMutation) SharedWithCleared() bool {
	_, ok := m.clearedFields[folder.FieldSharedWith]
	return ok
}

// ResetSharedWith resets all changes to the "shared_with" field.
func (m *FolderMutation) ResetSharedWith() {
	m.shared_with = nil
	m.appendshared_with = nil
	delete(m.clearedFields, folder.FieldSharedWith)
}

// SetPermissions sets the "permissions" field.
func (m *FolderMutation) SetPermissions(s []string) {
	m.permissions = &s
	m.appendpermissions = nil
}

// Permissions returns the value of the "permissions" field in the mutation.
func (m *FolderMutation) Permissions() (r []string, exists bool) {
	v := m.permissions
	if v == nil {
		return
	}
	return *v, true
}

// OldPermissions returns the old "permissions" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldPermissions(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPermissions is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPermissions requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPermissions: %w", err)
	}
	return oldValue.Permissions, nil
}

// AppendPermissions adds s to the "permissions" field.
func (m *FolderMutation) AppendPermissions(s []string) {
	m.appendpermissions = append(m.appendpermissions, s...)
}

// AppendedPermissions returns the list of values that were appended to the "permissions" field in this mutation.
func (m *FolderMutation) AppendedPermissions() ([]string, bool) {
	if len(m.appendpermissions) == 0 {
		return nil, false
	}
	return m.appendpermissions, true
}

// ClearPermissions clears the value of the "permissions" field.
func (m *FolderMutation) ClearPermissions() {
	m.permissions = nil
	m.appendpermissions = nil
	m.clearedFields[folder.FieldPermissions] = struct{}{}
}

// PermissionsCleared returns if the "permissions" field was cleared in this mutation.
func (m *FolderMutation) PermissionsCleared() bool {
	_, ok := m.clearedFields[folder.FieldPermissions]
	return ok
}

// ResetPermissions resets all changes to the "permissions" field.
func (m *FolderMutation) ResetPermissions() {
	m.permissions = nil
	m.appendpermissions = nil
	delete(m.clearedFields, folder.FieldPermissions)
}

// SetAssetCount sets the "asset_count" field.
func (m *FolderMutation) SetAssetCount(i int64) {
	m.asset_count = &i
	m.addasset_count = nil
}

// AssetCount returns the value of the "asset_count" field in the mutation.
func (m *FolderMutation) AssetCount() (r int64, exists bool) {
	v := m.asset_count
	if v == nil {
		return
	}
	return *v, true
}

// OldAssetCount returns the old "asset_count" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldAssetCount(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAssetCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAssetCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAssetCount: %w", err)
	}
	return oldValue.AssetCount, nil
}

// AddAssetCount adds i to the "asset_count" field.
func (m *FolderMutation) AddAssetCount(i int64) {
	if m.addasset_count != nil {
		*m.addasset_count += i
	} else {
		m.addasset_count = &i
	}
}

// AddedAssetCount returns the value that was added to the "asset_count" field in this mutation.
func (m *FolderMutation) AddedAssetCount() (r int64, exists bool) {
	v := m.addasset_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetAssetCount resets all changes to the "asset_count" field.
func (m *FolderMutation) ResetAssetCount() {
	m.asset_count = nil
	m.addasset_count = nil
}

// SetTotalSize sets the "total_size" field.
func (m *FolderMutation) SetTotalSize(i int64) {
	m.total_size = &i
	m.addtotal_size = nil
}

// TotalSize returns the value of the "total_size" field in the mutation.
func (m *FolderMutation) TotalSize() (r int64, exists bool) {
	v := m.total_size
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalSize returns the old "total_size" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldTotalSize(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalSize is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalSize requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalSize: %w", err)
	}
	return oldValue.TotalSize, nil
}

// AddTotalSize adds i to the "total_size" field.
func (m *FolderMutation) AddTotalSize(i int64) {
	if m.addtotal_size != nil {
		*m.addtotal_size += i
	} else {
		m.addtotal_size = &i
	}
}

// AddedTotalSize returns the value that was added to the "total_size" field in this mutation.
func (m *FolderMutation) AddedTotalSize() (r int64, exists bool) {
	v := m.addtotal_size
	if v == nil {
		return
	}
	return *v, true
}

// ResetTotalSize resets all changes to the "total_size" field.
func (m *FolderMutation) ResetTotalSize() {
	m.total_size = nil
	m.addtotal_size = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *FolderMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *FolderMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *FolderMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *FolderMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *FolderMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *FolderMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *FolderMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *FolderMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Folder entity.
// If the Folder object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FolderMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *FolderMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[folder.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *FolderMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[folder.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *FolderMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, folder.FieldDeletedAt)
}

// ClearParent clears the "parent" edge to the Folder entity.
func (m *FolderMutation) ClearParent() {
	m.clearedparent = true
	m.clearedFields[folder.FieldParentID] = struct{}{}
}

// ParentCleared reports if the "parent" edge to the Folder entity was cleared.
func (m *FolderMutation) ParentCleared() bool {
	return m.ParentIDCleared() || m.clearedparent
}

// ParentIDs returns the "parent" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ParentID instead. It exists only for internal usage by the builders.
func (m *FolderMutation) ParentIDs() (ids []uuid.UUID) {
	if id := m.parent; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetParent resets all changes to the "parent" edge.
func (m *FolderMutation) ResetParent() {
	m.parent = nil
	m.clearedparent = false
}

// AddChildIDs adds the "children" edge to the Folder entity by ids.
func (m *FolderMutation) AddChildIDs(ids ...uuid.UUID) {
	if m.children == nil {
		m.children = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.children[ids[i]] = struct{}{}
	}
}

// ClearChildren clears the "children" edge to the Folder entity.
func (m *FolderMutation) ClearChildren() {
	m.clearedchildren = true
}

// ChildrenCleared reports if the "children" edge to the Folder entity was cleared.
func (m *FolderMutation) ChildrenCleared() bool {
	return m.clearedchildren
}

// RemoveChildIDs removes the "children" edge to the Folder entity by IDs.
func (m *FolderMutation) RemoveChildIDs(ids ...uuid.UUID) {
	if m.removedchildren == nil {
		m.removedchildren = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.children, ids[i])
		m.removedchildren[ids[i]] = struct{}{}
	}
}

// RemovedChildren returns the removed IDs of the "children" edge to the Folder entity.
func (m *FolderMutation) RemovedChildrenIDs() (ids []uuid.UUID) {
	for id := range m.removedchildren {
		ids = append(ids, id)
	}
	return
}

// ChildrenIDs returns the "children" edge IDs in the mutation.
func (m *FolderMutation) ChildrenIDs() (ids []uuid.UUID) {
	for id := range m.children {
		ids = append(ids, id)
	}
	return
}

// ResetChildren resets all changes to the "children" edge.
func (m *FolderMutation) ResetChildren() {
	m.children = nil
	m.clearedchildren = false
	m.removedchildren = nil
}

// AddAssetIDs adds the "assets" edge to the Asset entity by ids.
func (m *FolderMutation) AddAssetIDs(ids ...uuid.UUID) {
	if m.assets == nil {
		m.assets = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.assets[ids[i]] = struct{}{}
	}
}

// ClearAssets clears the "assets" edge to the Asset entity.
func (m *FolderMutation) ClearAssets() {
	m.clearedassets = true
}

// AssetsCleared reports if the "assets" edge to the Asset entity was cleared.
func (m *FolderMutation) AssetsCleared() bool {
	return m.clearedassets
}

// RemoveAssetIDs removes the "assets" edge to the Asset entity by IDs.
func (m *FolderMutation) RemoveAssetIDs(ids ...uuid.UUID) {
	if m.removedassets == nil {
		m.removedassets = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.assets, ids[i])
		m.removedassets[ids[i]] = struct{}{}
	}
}

// RemovedAssets returns the removed IDs of the "assets" edge to the Asset entity.
func (m *FolderMutation) RemovedAssetsIDs() (ids []uuid.UUID) {
	for id := range m.removedassets {
		ids = append(ids, id)
	}
	return
}

// AssetsIDs returns the "assets" edge IDs in the mutation.
func (m *FolderMutation) AssetsIDs() (ids []uuid.UUID) {
	for id := range m.assets {
		ids = append(ids, id)
	}
	return
}

// ResetAssets resets all changes to the "assets" edge.
func (m *FolderMutation) ResetAssets() {
	m.assets = nil
	m.clearedassets = false
	m.removedassets = nil
}

// Where appends a list predicates to the FolderMutation builder.
func (m *FolderMutation) Where(ps ...predicate.Folder) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FolderMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FolderMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Folder, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FolderMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FolderMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Folder).
func (m *FolderMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FolderMutation) Fields() []string {
	fields := make([]string, 0, 14)
	if m.user_id != nil {
		fields = append(fields, folder.FieldUserID)
	}
	if m.parent != nil {
		fields = append(fields, folder.FieldParentID)
	}
	if m.name != nil {
		fields = append(fields, folder.FieldName)
	}
	if m.description != nil {
		fields = append(fields, folder.FieldDescription)
	}
	if m._path != nil {
		fields = append(fields, folder.FieldPath)
	}
	if m.level != nil {
		fields = append(fields, folder.FieldLevel)
	}
	if m.is_shared != nil {
		fields = append(fields, folder.FieldIsShared)
	}
	if m.shared_with != nil {
		fields = append(fields, folder.FieldSharedWith)
	}
	if m.permissions != nil {
		fields = append(fields, folder.FieldPermissions)
	}
	if m.asset_count != nil {
		fields = append(fields, folder.FieldAssetCount)
	}
	if m.total_size != nil {
		fields = append(fields, folder.FieldTotalSize)
	}
	if m.created_at != nil {
		fields = append(fields, folder.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, folder.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, folder.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FolderMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case folder.FieldUserID:
		return m.UserID()
	case folder.FieldParentID:
		return m.ParentID()
	case folder.FieldName:
		return m.Name()
	case folder.FieldDescription:
		return m.Description()
	case folder.FieldPath:
		return m.Path()
	case folder.FieldLevel:
		return m.Level()
	case folder.FieldIsShared:
		return m.IsShared()
	case folder.FieldSharedWith:
		return m.SharedWith()
	case folder.FieldPermissions:
		return m.Permissions()
	case folder.FieldAssetCount:
		return m.AssetCount()
	case folder.FieldTotalSize:
		return m.TotalSize()
	case folder.FieldCreatedAt:
		return m.CreatedAt()
	case folder.FieldUpdatedAt:
		return m.UpdatedAt()
	case folder.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FolderMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case folder.FieldUserID:
		return m.OldUserID(ctx)
	case folder.FieldParentID:
		return m.OldParentID(ctx)
	case folder.FieldName:
		return m.OldName(ctx)
	case folder.FieldDescription:
		return m.OldDescription(ctx)
	case folder.FieldPath:
		return m.OldPath(ctx)
	case folder.FieldLevel:
		return m.OldLevel(ctx)
	case folder.FieldIsShared:
		return m.OldIsShared(ctx)
	case folder.FieldSharedWith:
		return m.OldSharedWith(ctx)
	case folder.FieldPermissions:
		return m.OldPermissions(ctx)
	case folder.FieldAssetCount:
		return m.OldAssetCount(ctx)
	case folder.FieldTotalSize:
		return m.OldTotalSize(ctx)
	case folder.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case folder.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case folder.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Folder field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FolderMutation) SetField(name string, value ent.Value) error {
	switch name {
	case folder.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case folder.FieldParentID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetParentID(v)
		return nil
	case folder.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case folder.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case folder.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	case folder.FieldLevel:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLevel(v)
		return nil
	case folder.FieldIsShared:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsShared(v)
		return nil
	case folder.FieldSharedWith:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSharedWith(v)
		return nil
	case folder.FieldPermissions:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPermissions(v)
		return nil
	case folder.FieldAssetCount:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAssetCount(v)
		return nil
	case folder.FieldTotalSize:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalSize(v)
		return nil
	case folder.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case folder.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case folder.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Folder field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FolderMutation) AddedFields() []string {
	var fields []string
	if m.addlevel != nil {
		fields = append(fields, folder.FieldLevel)
	}
	if m.addasset_count != nil {
		fields = append(fields, folder.FieldAssetCount)
	}
	if m.addtotal_size != nil {
		fields = append(fields, folder.FieldTotalSize)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FolderMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case folder.FieldLevel:
		return m.AddedLevel()
	case folder.FieldAssetCount:
		return m.AddedAssetCount()
	case folder.FieldTotalSize:
		return m.AddedTotalSize()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FolderMutation) AddField(name string, value ent.Value) error {
	switch name {
	case folder.FieldLevel:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddLevel(v)
		return nil
	case folder.FieldAssetCount:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAssetCount(v)
		return nil
	case folder.FieldTotalSize:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalSize(v)
		return nil
	}
	return fmt.Errorf("unknown Folder numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FolderMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(folder.FieldParentID) {
		fields = append(fields, folder.FieldParentID)
	}
	if m.FieldCleared(folder.FieldDescription) {
		fields = append(fields, folder.FieldDescription)
	}
	if m.FieldCleared(folder.FieldSharedWith) {
		fields = append(fields, folder.FieldSharedWith)
	}
	if m.FieldCleared(folder.FieldPermissions) {
		fields = append(fields, folder.FieldPermissions)
	}
	if m.FieldCleared(folder.FieldDeletedAt) {
		fields = append(fields, folder.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FolderMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FolderMutation) ClearField(name string) error {
	switch name {
	case folder.FieldParentID:
		m.ClearParentID()
		return nil
	case folder.FieldDescription:
		m.ClearDescription()
		return nil
	case folder.FieldSharedWith:
		m.ClearSharedWith()
		return nil
	case folder.FieldPermissions:
		m.ClearPermissions()
		return nil
	case folder.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Folder nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FolderMutation) ResetField(name string) error {
	switch name {
	case folder.FieldUserID:
		m.ResetUserID()
		return nil
	case folder.FieldParentID:
		m.ResetParentID()
		return nil
	case folder.FieldName:
		m.ResetName()
		return nil
	case folder.FieldDescription:
		m.ResetDescription()
		return nil
	case folder.FieldPath:
		m.ResetPath()
		return nil
	case folder.FieldLevel:
		m.ResetLevel()
		return nil
	case folder.FieldIsShared:
		m.ResetIsShared()
		return nil
	case folder.FieldSharedWith:
		m.ResetSharedWith()
		return nil
	case folder.FieldPermissions:
		m.ResetPermissions()
		return nil
	case folder.FieldAssetCount:
		m.ResetAssetCount()
		return nil
	case folder.FieldTotalSize:
		m.ResetTotalSize()
		return nil
	case folder.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case folder.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case folder.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Folder field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FolderMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.parent != nil {
		edges = append(edges, folder.EdgeParent)
	}
	if m.children != nil {
		edges = append(edges, folder.EdgeChildren)
	}
	if m.assets != nil {
		edges = append(edges, folder.EdgeAssets)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FolderMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case folder.EdgeParent:
		if id := m.parent; id != nil {
			return []ent.Value{*id}
		}
	case folder.EdgeChildren:
		ids := make([]ent.Value, 0, len(m.children))
		for id := range m.children {
			ids = append(ids, id)
		}
		return ids
	case folder.EdgeAssets:
		ids := make([]ent.Value, 0, len(m.assets))
		for id := range m.assets {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FolderMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removedchildren != nil {
		edges = append(edges, folder.EdgeChildren)
	}
	if m.removedassets != nil {
		edges = append(edges, folder.EdgeAssets)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FolderMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case folder.EdgeChildren:
		ids := make([]ent.Value, 0, len(m.removedchildren))
		for id := range m.removedchildren {
			ids = append(ids, id)
		}
		return ids
	case folder.EdgeAssets:
		ids := make([]ent.Value, 0, len(m.removedassets))
		for id := range m.removedassets {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FolderMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.clearedparent {
		edges = append(edges, folder.EdgeParent)
	}
	if m.clearedchildren {
		edges = append(edges, folder.EdgeChildren)
	}
	if m.clearedassets {
		edges = append(edges, folder.EdgeAssets)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FolderMutation) EdgeCleared(name string) bool {
	switch name {
	case folder.EdgeParent:
		return m.clearedparent
	case folder.EdgeChildren:
		return m.clearedchildren
	case folder.EdgeAssets:
		return m.clearedassets
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FolderMutation) ClearEdge(name string) error {
	switch name {
	case folder.EdgeParent:
		m.ClearParent()
		return nil
	}
	return fmt.Errorf("unknown Folder unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FolderMutation) ResetEdge(name string) error {
	switch name {
	case folder.EdgeParent:
		m.ResetParent()
		return nil
	case folder.EdgeChildren:
		m.ResetChildren()
		return nil
	case folder.EdgeAssets:
		m.ResetAssets()
		return nil
	}
	return fmt.Errorf("unknown Folder edge %s", name)
}

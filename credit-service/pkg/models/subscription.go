package models

import "time"

// CreateSubscriptionRequest represents create subscription request
type CreateSubscriptionRequest struct {
	UserID          string                 `json:"user_id" validate:"required"`
	PlanID          string                 `json:"plan_id" validate:"required"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	BillingAddress  *BillingAddress        `json:"billing_address,omitempty"`
	PromoCode       string                 `json:"promo_code,omitempty"`
	TrialDays       int                    `json:"trial_days,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateSubscriptionRequest represents update subscription request
type UpdateSubscriptionRequest struct {
	SubscriptionID  string                 `json:"subscription_id" validate:"required"`
	PlanID          string                 `json:"plan_id,omitempty"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	BillingAddress  *BillingAddress        `json:"billing_address,omitempty"`
	PromoCode       string                 `json:"promo_code,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// CancelSubscriptionRequest represents cancel subscription request
type CancelSubscriptionRequest struct {
	SubscriptionID string `json:"subscription_id" validate:"required"`
	Reason         string `json:"reason,omitempty"`
	CancelAtEnd    bool   `json:"cancel_at_end"` // Cancel at end of billing period
	Immediate      bool   `json:"immediate"`     // Cancel immediately
}

// ReactivateSubscriptionRequest represents reactivate subscription request
type ReactivateSubscriptionRequest struct {
	SubscriptionID  string                 `json:"subscription_id" validate:"required"`
	PaymentMethodID string                 `json:"payment_method_id,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpgradeSubscriptionRequest represents upgrade subscription request
type UpgradeSubscriptionRequest struct {
	SubscriptionID string                 `json:"subscription_id" validate:"required"`
	NewPlanID      string                 `json:"new_plan_id" validate:"required"`
	ProrationMode  string                 `json:"proration_mode,omitempty"` // immediate, next_cycle
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// DowngradeSubscriptionRequest represents downgrade subscription request
type DowngradeSubscriptionRequest struct {
	SubscriptionID string                 `json:"subscription_id" validate:"required"`
	NewPlanID      string                 `json:"new_plan_id" validate:"required"`
	EffectiveDate  string                 `json:"effective_date,omitempty"` // immediate, next_cycle
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionResponse represents subscription response
type SubscriptionResponse struct {
	BaseModel
	UserID               string                 `json:"user_id"`
	PlanID               string                 `json:"plan_id"`
	PlanName             string                 `json:"plan_name"`
	Status               string                 `json:"status"`           // active, canceled, past_due, unpaid, trialing
	BillingInterval      string                 `json:"billing_interval"` // monthly, yearly
	Amount               int64                  `json:"amount"`           // in cents
	Currency             string                 `json:"currency"`
	TrialStart           *time.Time             `json:"trial_start,omitempty"`
	TrialEnd             *time.Time             `json:"trial_end,omitempty"`
	CurrentPeriodStart   time.Time              `json:"current_period_start"`
	CurrentPeriodEnd     time.Time              `json:"current_period_end"`
	CancelAtPeriodEnd    bool                   `json:"cancel_at_period_end"`
	CanceledAt           *time.Time             `json:"canceled_at,omitempty"`
	EndedAt              *time.Time             `json:"ended_at,omitempty"`
	StripeSubscriptionID string                 `json:"stripe_subscription_id,omitempty"`
	PaymentMethodID      string                 `json:"payment_method_id,omitempty"`
	BillingAddress       *BillingAddress        `json:"billing_address,omitempty"`
	Usage                *SubscriptionUsage     `json:"usage,omitempty"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionPlanResponse represents subscription plan response
type SubscriptionPlanResponse struct {
	BaseModel
	Name            string                 `json:"name"`
	DisplayName     string                 `json:"display_name"`
	Description     string                 `json:"description"`
	BillingInterval string                 `json:"billing_interval"` // monthly, yearly
	Amount          int64                  `json:"amount"`           // in cents
	Currency        string                 `json:"currency"`
	TrialDays       int                    `json:"trial_days"`
	CreditLimit     int                    `json:"credit_limit"`
	Features        []string               `json:"features"`
	IsActive        bool                   `json:"is_active"`
	IsPopular       bool                   `json:"is_popular"`
	SortOrder       int                    `json:"sort_order"`
	StripePriceID   string                 `json:"stripe_price_id,omitempty"`
	StripeProductID string                 `json:"stripe_product_id,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionUsage represents subscription usage
type SubscriptionUsage struct {
	CreditsUsed      int       `json:"credits_used"`
	CreditsLimit     int       `json:"credits_limit"`
	CreditsRemaining int       `json:"credits_remaining"`
	UsagePercentage  float64   `json:"usage_percentage"`
	ResetDate        time.Time `json:"reset_date"`
}

// SubscriptionUsageResponse represents subscription usage response
type SubscriptionUsageResponse struct {
	UserID          string                   `json:"user_id"`
	SubscriptionID  string                   `json:"subscription_id"`
	BillingPeriod   string                   `json:"billing_period"` // current period
	Usage           *SubscriptionUsage       `json:"usage"`
	DailyUsage      []DailyUsage             `json:"daily_usage"`
	UsageByCategory map[string]int           `json:"usage_by_category"`
	UsageByModel    map[string]int           `json:"usage_by_model"`
	UsageHistory    []SubscriptionUsagePoint `json:"usage_history"`
}

// SubscriptionUsagePoint represents a point in usage history
type SubscriptionUsagePoint struct {
	Date         string `json:"date"`
	CreditsUsed  int    `json:"credits_used"`
	Transactions int    `json:"transactions"`
}

// UpdateUsageRequest represents update usage request
type UpdateUsageRequest struct {
	UserID         string                 `json:"user_id" validate:"required"`
	SubscriptionID string                 `json:"subscription_id,omitempty"`
	CreditsUsed    int                    `json:"credits_used" validate:"required,min=1"`
	Category       string                 `json:"category,omitempty"`
	Model          string                 `json:"model,omitempty"`
	Description    string                 `json:"description,omitempty"`
	ReferenceID    string                 `json:"reference_id,omitempty"`
	ReferenceType  string                 `json:"reference_type,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// GetInvoicesRequest represents get invoices request
type GetInvoicesRequest struct {
	UserID         string     `json:"user_id" validate:"required"`
	SubscriptionID string     `json:"subscription_id,omitempty"`
	Status         string     `json:"status,omitempty"` // paid, open, void, uncollectible
	StartDate      *time.Time `json:"start_date,omitempty"`
	EndDate        *time.Time `json:"end_date,omitempty"`
	Limit          int        `json:"limit,omitempty"`
	Offset         int        `json:"offset,omitempty"`
}

// InvoicesResponse represents invoices response
type InvoicesResponse struct {
	Invoices   []InvoiceResponse `json:"invoices"`
	Pagination PaginationMeta    `json:"pagination"`
}

// InvoiceResponse represents invoice response
type InvoiceResponse struct {
	BaseModel
	InvoiceNumber   string                 `json:"invoice_number"`
	UserID          string                 `json:"user_id"`
	SubscriptionID  string                 `json:"subscription_id,omitempty"`
	Amount          int64                  `json:"amount"` // in cents
	Currency        string                 `json:"currency"`
	Status          string                 `json:"status"` // draft, open, paid, void, uncollectible
	DueDate         time.Time              `json:"due_date"`
	PaidAt          *time.Time             `json:"paid_at,omitempty"`
	Items           []InvoiceItem          `json:"items"`
	BillingAddress  BillingAddress         `json:"billing_address"`
	StripeInvoiceID string                 `json:"stripe_invoice_id,omitempty"`
	DownloadURL     string                 `json:"download_url,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// ProcessPaymentRequest represents process payment request
type ProcessPaymentRequest struct {
	UserID          string                 `json:"user_id" validate:"required"`
	SubscriptionID  string                 `json:"subscription_id,omitempty"`
	InvoiceID       string                 `json:"invoice_id,omitempty"`
	PaymentMethodID string                 `json:"payment_method_id" validate:"required"`
	Amount          int64                  `json:"amount,omitempty"` // in cents, optional for subscription payments
	Currency        string                 `json:"currency,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentResponse represents payment response
type PaymentResponse struct {
	BaseModel
	UserID          string                 `json:"user_id"`
	SubscriptionID  string                 `json:"subscription_id,omitempty"`
	InvoiceID       string                 `json:"invoice_id,omitempty"`
	Amount          int64                  `json:"amount"` // in cents
	Currency        string                 `json:"currency"`
	Status          string                 `json:"status"` // succeeded, pending, failed
	PaymentMethodID string                 `json:"payment_method_id"`
	StripePaymentID string                 `json:"stripe_payment_id,omitempty"`
	FailureCode     string                 `json:"failure_code,omitempty"`
	FailureMessage  string                 `json:"failure_message,omitempty"`
	ProcessedAt     *time.Time             `json:"processed_at,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// Additional models for subscription service

// SubscriptionAnalyticsResponse represents subscription analytics
type SubscriptionAnalyticsResponse struct {
	UserID              string                      `json:"user_id"`
	Period              string                      `json:"period"`
	TotalSubscriptions  int                         `json:"total_subscriptions"`
	ActiveSubscriptions int                         `json:"active_subscriptions"`
	TotalRevenue        int64                       `json:"total_revenue"`
	Timeline            []SubscriptionTimelinePoint `json:"timeline"`
}

// SubscriptionTimelinePoint represents a point in subscription timeline
type SubscriptionTimelinePoint struct {
	Date          string `json:"date"`
	Subscriptions int    `json:"subscriptions"`
	Revenue       int64  `json:"revenue"`
}

// SubscriptionHistoryRequest represents subscription history request
type SubscriptionHistoryRequest struct {
	UserID   string     `json:"user_id"`
	Status   string     `json:"status,omitempty"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Page     int        `json:"page" validate:"min=1"`
	Limit    int        `json:"limit" validate:"min=1,max=100"`
}

// SubscriptionHistoryResponse represents subscription history response
type SubscriptionHistoryResponse struct {
	Subscriptions []SubscriptionResponse `json:"subscriptions"`
	Pagination    PaginationMeta         `json:"pagination"`
}

// AdminListSubscriptionsRequest represents admin list subscriptions request
type AdminListSubscriptionsRequest struct {
	Status   string     `json:"status,omitempty"`
	PlanID   string     `json:"plan_id,omitempty"`
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`
	Page     int        `json:"page" validate:"min=1"`
	Limit    int        `json:"limit" validate:"min=1,max=100"`
}

// AdminSubscriptionsResponse represents admin subscriptions response
type AdminSubscriptionsResponse struct {
	Subscriptions []SubscriptionResponse `json:"subscriptions"`
	Pagination    PaginationMeta         `json:"pagination"`
	Summary       SubscriptionSummary    `json:"summary"`
}

// SubscriptionSummary represents subscription summary
type SubscriptionSummary struct {
	TotalSubscriptions  int   `json:"total_subscriptions"`
	ActiveSubscriptions int   `json:"active_subscriptions"`
	TotalRevenue        int64 `json:"total_revenue"`
	MonthlyRevenue      int64 `json:"monthly_revenue"`
}

// AdminUpdateSubscriptionRequest represents admin update subscription request
type AdminUpdateSubscriptionRequest struct {
	SubscriptionID string                 `json:"subscription_id" validate:"required"`
	Status         string                 `json:"status,omitempty"`
	PlanID         string                 `json:"plan_id,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// CreateSubscriptionPlanRequest represents create subscription plan request
type CreateSubscriptionPlanRequest struct {
	Name            string                 `json:"name" validate:"required"`
	DisplayName     string                 `json:"display_name" validate:"required"`
	Description     string                 `json:"description"`
	BillingInterval string                 `json:"billing_interval" validate:"required"`
	Amount          int64                  `json:"amount" validate:"required,min=0"`
	Currency        string                 `json:"currency" validate:"required"`
	TrialDays       int                    `json:"trial_days"`
	CreditLimit     int                    `json:"credit_limit" validate:"required,min=1"`
	Features        []string               `json:"features"`
	IsActive        bool                   `json:"is_active"`
	IsPopular       bool                   `json:"is_popular"`
	SortOrder       int                    `json:"sort_order"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateSubscriptionPlanRequest represents update subscription plan request
type UpdateSubscriptionPlanRequest struct {
	PlanID      string                 `json:"plan_id" validate:"required"`
	DisplayName string                 `json:"display_name,omitempty"`
	Description string                 `json:"description,omitempty"`
	Amount      *int64                 `json:"amount,omitempty"`
	TrialDays   *int                   `json:"trial_days,omitempty"`
	CreditLimit *int                   `json:"credit_limit,omitempty"`
	Features    []string               `json:"features,omitempty"`
	IsActive    *bool                  `json:"is_active,omitempty"`
	IsPopular   *bool                  `json:"is_popular,omitempty"`
	SortOrder   *int                   `json:"sort_order,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AdminSubscriptionAnalyticsResponse represents admin subscription analytics
type AdminSubscriptionAnalyticsResponse struct {
	Period              string                      `json:"period"`
	TotalSubscriptions  int                         `json:"total_subscriptions"`
	ActiveSubscriptions int                         `json:"active_subscriptions"`
	TotalRevenue        int64                       `json:"total_revenue"`
	Timeline            []SubscriptionTimelinePoint `json:"timeline"`
	PlanBreakdown       []PlanAnalytics             `json:"plan_breakdown"`
}

// PlanAnalytics represents analytics for a specific plan
type PlanAnalytics struct {
	PlanID        string `json:"plan_id"`
	PlanName      string `json:"plan_name"`
	Subscriptions int    `json:"subscriptions"`
	Revenue       int64  `json:"revenue"`
}

// StripeWebhookEvent represents Stripe webhook event
type StripeWebhookEvent struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Data     map[string]interface{} `json:"data"`
	Created  time.Time              `json:"created"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// PaymentWebhookEvent represents payment webhook event
type PaymentWebhookEvent struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Provider string                 `json:"provider"`
	Data     map[string]interface{} `json:"data"`
	Created  time.Time              `json:"created"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionLimitCheckResponse represents subscription limit check response
type SubscriptionLimitCheckResponse struct {
	WithinLimits     bool `json:"within_limits"`
	CurrentUsage     int  `json:"current_usage"`
	UsageLimit       int  `json:"usage_limit"`
	RemainingCredits int  `json:"remaining_credits"`
}

// ActivateSubscriptionRequest represents activate subscription request
type ActivateSubscriptionRequest struct {
	UserID    string                 `json:"user_id" validate:"required"`
	PlanID    string                 `json:"plan_id" validate:"required"`
	PaymentID string                 `json:"payment_id,omitempty"`
	Source    string                 `json:"source,omitempty"` // "payment", "trial", "admin"
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// SubscriptionActivationInfo represents subscription activation information
type SubscriptionActivationInfo struct {
	SubscriptionID string    `json:"subscription_id"`
	PlanName       string    `json:"plan_name"`
	StartsAt       time.Time `json:"starts_at"`
	EndsAt         time.Time `json:"ends_at"`
	CreditsAdded   int32     `json:"credits_added"`
}

package oauth

import "context"

// Provider defines the OAuth provider interface
type Provider interface {
	// GetAuthURL generates OAuth authorization URL
	GetAuthURL(state string, scopes []string) string
	
	// ExchangeCodeForToken exchanges authorization code for access token
	ExchangeCodeForToken(ctx context.Context, code string) (*TokenResponse, error)
	
	// RefreshToken refreshes access token using refresh token
	RefreshToken(ctx context.Context, accessToken string) (*TokenResponse, error)
	
	// RevokeToken revokes access token
	RevokeToken(ctx context.Context, accessToken string) error
	
	// GetUserProfile gets user profile information
	GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error)
	
	// GetPermissions gets current permissions for the token
	GetPermissions(ctx context.Context, accessToken string) ([]string, error)
}

// TokenResponse represents OAuth token response
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
}

// UserProfile represents user profile information from OAuth provider
type UserProfile struct {
	ID          string            `json:"id"`
	Username    string            `json:"username"`
	DisplayName string            `json:"display_name"`
	Email       string            `json:"email"`
	AvatarURL   string            `json:"avatar_url"`
	Metadata    map[string]string `json:"metadata"`
}

// ErrorResponse represents OAuth error response
type ErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	ErrorURI         string `json:"error_uri"`
}

package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/content-mgmt-service/usecase/template"
	"github.com/social-content-ai/pkg-shared/logging"
)

// TemplateHandler handles template-related HTTP requests
type TemplateHandler struct {
	templateUseCase template.UseCase
	logger          logging.Logger
}

// NewTemplateHandler creates a new template handler
func NewTemplateHandler(templateUseCase template.UseCase, logger logging.Logger) *TemplateHandler {
	return &TemplateHandler{
		templateUseCase: templateUseCase,
		logger:          logger,
	}
}

// CreateTemplate godoc
// @Summary Create a new template
// @Description Create a new content template
// @Tags templates
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.CreateTemplateRequest true "Create template request"
// @Success 201 {object} models.TemplateResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/templates [post]
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	var req models.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid create template request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.UserID = userID.(string)

	template, err := h.templateUseCase.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create template")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "CREATE_FAILED",
			Message: "Failed to create template",
		})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// ListTemplates godoc
// @Summary List templates
// @Description List templates with pagination and filters
// @Tags templates
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param category query string false "Template category filter"
// @Param type query string false "Template type filter (my, marketplace, premium)"
// @Param search query string false "Search term"
// @Success 200 {object} models.ListTemplatesResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /api/v1/templates [get]
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	category := c.Query("category")
	templateType := c.Query("type")
	search := c.Query("search")

	req := &models.ListTemplatesRequest{
		UserID:   userID.(string),
		Page:     page,
		Limit:    limit,
		Category: category,
		Type:     templateType,
		Search:   search,
	}

	resp, err := h.templateUseCase.ListTemplates(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list templates")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list templates",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetTemplate godoc
// @Summary Get template by ID
// @Description Get template information by ID
// @Tags templates
// @Produce json
// @Security BearerAuth
// @Param id path string true "Template ID"
// @Success 200 {object} models.TemplateResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/templates/{id} [get]
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	template, err := h.templateUseCase.GetTemplate(c.Request.Context(), templateID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template")

		switch err.Error() {
		case "TEMPLATE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TEMPLATE_NOT_FOUND",
				Message: "Template not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to this template",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "GET_TEMPLATE_FAILED",
				Message: "Failed to get template",
			})
		}
		return
	}

	c.JSON(http.StatusOK, template)
}

// UpdateTemplate godoc
// @Summary Update template
// @Description Update template information
// @Tags templates
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Template ID"
// @Param request body models.UpdateTemplateRequest true "Update template request"
// @Success 200 {object} models.TemplateResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/templates/{id} [put]
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	var req models.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid update template request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.ID = templateID
	req.UserID = userID.(string)

	template, err := h.templateUseCase.UpdateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update template")

		switch err.Error() {
		case "TEMPLATE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TEMPLATE_NOT_FOUND",
				Message: "Template not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to update this template",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "UPDATE_FAILED",
				Message: "Failed to update template",
			})
		}
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate godoc
// @Summary Delete template
// @Description Delete a template
// @Tags templates
// @Produce json
// @Security BearerAuth
// @Param id path string true "Template ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/templates/{id} [delete]
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	err := h.templateUseCase.DeleteTemplate(c.Request.Context(), templateID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete template")

		switch err.Error() {
		case "TEMPLATE_NOT_FOUND":
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "TEMPLATE_NOT_FOUND",
				Message: "Template not found",
			})
		case "ACCESS_DENIED":
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "ACCESS_DENIED",
				Message: "Access denied to delete this template",
			})
		default:
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "DELETE_FAILED",
				Message: "Failed to delete template",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Template deleted successfully",
	})
}

// UseTemplate uses a template to generate content
func (h *TemplateHandler) UseTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	var req models.UseTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid use template request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.TemplateID = templateID
	req.UserID = userID.(string)

	result, err := h.templateUseCase.UseTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to use template")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "USE_TEMPLATE_FAILED",
			Message: "Failed to use template",
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// RateTemplate rates a template
func (h *TemplateHandler) RateTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	var req models.RateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid rate template request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	req.TemplateID = templateID
	req.UserID = userID.(string)

	err := h.templateUseCase.RateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to rate template")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "RATE_TEMPLATE_FAILED",
			Message: "Failed to rate template",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Template rated successfully",
	})
}

// FavoriteTemplate adds template to favorites
func (h *TemplateHandler) FavoriteTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	err := h.templateUseCase.FavoriteTemplate(c.Request.Context(), templateID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to favorite template")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "FAVORITE_FAILED",
			Message: "Failed to favorite template",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Template favorited successfully",
	})
}

// UnfavoriteTemplate removes template from favorites
func (h *TemplateHandler) UnfavoriteTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	err := h.templateUseCase.UnfavoriteTemplate(c.Request.Context(), templateID, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to unfavorite template")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "UNFAVORITE_FAILED",
			Message: "Failed to unfavorite template",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Message: "Template unfavorited successfully",
	})
}

// ListMarketplaceTemplates lists marketplace templates
func (h *TemplateHandler) ListMarketplaceTemplates(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	category := c.Query("category")
	platform := c.Query("platform")
	search := c.Query("search")

	req := &models.ListTemplatesRequest{
		UserID:   userID.(string),
		Page:     page,
		Limit:    limit,
		Category: category,
		Platform: platform,
		Search:   search,
		Type:     "marketplace",
	}

	resp, err := h.templateUseCase.ListMarketplaceTemplates(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list marketplace templates")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list marketplace templates",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ListFeaturedTemplates lists featured templates
func (h *TemplateHandler) ListFeaturedTemplates(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	resp, err := h.templateUseCase.ListFeaturedTemplates(c.Request.Context(), userID.(string), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list featured templates")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "LIST_FAILED",
			Message: "Failed to list featured templates",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetTemplateCategories gets template categories
func (h *TemplateHandler) GetTemplateCategories(c *gin.Context) {
	categories, err := h.templateUseCase.GetTemplateCategories(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template categories")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "GET_CATEGORIES_FAILED",
			Message: "Failed to get template categories",
		})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// SearchTemplates searches templates
func (h *TemplateHandler) SearchTemplates(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_QUERY",
			Message: "Search query is required",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	req := &models.SearchTemplatesRequest{
		UserID: userID.(string),
		Query:  query,
		Page:   page,
		Limit:  limit,
	}

	resp, err := h.templateUseCase.SearchTemplates(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search templates")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "SEARCH_FAILED",
			Message: "Failed to search templates",
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetTemplateAnalytics gets template analytics
func (h *TemplateHandler) GetTemplateAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	req := &models.TemplateAnalyticsRequest{
		TemplateID: templateID,
		UserID:     userID.(string),
		DateFrom:   c.Query("date_from"),
		DateTo:     c.Query("date_to"),
	}

	analytics, err := h.templateUseCase.GetTemplateAnalytics(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template analytics")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "ANALYTICS_FAILED",
			Message: "Failed to get template analytics",
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetTemplateUsageStats gets template usage stats
func (h *TemplateHandler) GetTemplateUsageStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "UNAUTHORIZED",
			Message: "User not authenticated",
		})
		return
	}

	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "INVALID_TEMPLATE_ID",
			Message: "Template ID is required",
		})
		return
	}

	req := &models.TemplateUsageStatsRequest{
		TemplateID: templateID,
		UserID:     userID.(string),
	}

	stats, err := h.templateUseCase.GetTemplateUsageStats(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get template usage stats")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "STATS_FAILED",
			Message: "Failed to get template usage stats",
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetTemplatesAnalytics gets analytics for multiple templates
func (h *TemplateHandler) GetTemplatesAnalytics(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Get templates analytics functionality not implemented yet",
	})
}

// AdminListTemplates lists templates for admin
func (h *TemplateHandler) AdminListTemplates(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Admin list templates functionality not implemented yet",
	})
}

// AdminVerifyTemplate verifies a template
func (h *TemplateHandler) AdminVerifyTemplate(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Admin verify template functionality not implemented yet",
	})
}

// AdminFeatureTemplate features a template
func (h *TemplateHandler) AdminFeatureTemplate(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, models.ErrorResponse{
		Error:   "NOT_IMPLEMENTED",
		Message: "Admin feature template functionality not implemented yet",
	})
}

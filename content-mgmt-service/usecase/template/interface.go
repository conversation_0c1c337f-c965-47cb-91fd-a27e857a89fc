package template

import (
	"context"

	"github.com/social-content-ai/content-mgmt-service/pkg/models"
)

// UseCase defines the template business logic interface
type UseCase interface {
	// Basic CRUD operations
	CreateTemplate(ctx context.Context, req *models.CreateTemplateRequest) (*models.TemplateResponse, error)
	GetTemplate(ctx context.Context, templateID, userID string) (*models.TemplateResponse, error)
	UpdateTemplate(ctx context.Context, req *models.UpdateTemplateRequest) (*models.TemplateResponse, error)
	DeleteTemplate(ctx context.Context, templateID, userID string) error
	ListTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error)

	// Template usage
	UseTemplate(ctx context.Context, req *models.UseTemplateRequest) (*models.UseTemplateResponse, error)
	RateTemplate(ctx context.Context, req *models.RateTemplateRequest) error
	FavoriteTemplate(ctx context.Context, templateID, userID string) error
	UnfavoriteTemplate(ctx context.Context, templateID, userID string) error

	// TODO: Implement these methods after creating the required models:
	// GetTemplateRatings(ctx context.Context, req *models.GetTemplateRatingsRequest) (*models.GetTemplateRatingsResponse, error)
	// PurchaseTemplate(ctx context.Context, req *models.PurchaseTemplateRequest) (*models.PurchaseTemplateResponse, error)
	// GetPurchasedTemplates(ctx context.Context, req *models.GetPurchasedTemplatesRequest) (*models.GetPurchasedTemplatesResponse, error)
	// GetTemplateRevenue(ctx context.Context, req *models.GetTemplateRevenueRequest) (*models.GetTemplateRevenueResponse, error)

	// Marketplace
	ListMarketplaceTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error)
	ListFeaturedTemplates(ctx context.Context, userID string, limit int) (*models.ListTemplatesResponse, error)
	GetTemplateCategories(ctx context.Context) (*models.TemplateCategoriesResponse, error)
	SearchTemplates(ctx context.Context, req *models.SearchTemplatesRequest) (*models.SearchTemplatesResponse, error)

	// Analytics
	GetTemplateAnalytics(ctx context.Context, req *models.TemplateAnalyticsRequest) (*models.TemplateAnalyticsResponse, error)
	GetTemplateUsageStats(ctx context.Context, req *models.TemplateUsageStatsRequest) (*models.TemplateUsageStatsResponse, error)
	GetTemplatesAnalytics(ctx context.Context, userID string, period string) (*models.TemplateAnalyticsResponse, error)

	// Admin operations
	AdminListTemplates(ctx context.Context, req *models.ListTemplatesRequest) (*models.ListTemplatesResponse, error)
	AdminVerifyTemplate(ctx context.Context, templateID string, verified bool) (*models.TemplateResponse, error)
	AdminFeatureTemplate(ctx context.Context, templateID string, featured bool) (*models.TemplateResponse, error)
	AdminDeleteTemplate(ctx context.Context, templateID string) error
	GetAdminTemplateAnalytics(ctx context.Context, period string) (*models.TemplateAnalyticsResponse, error)
}

package storage

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinIOStorage implements Storage interface using MinIO
type MinIOStorage struct {
	client    *minio.Client
	bucket    string
	publicURL string
	config    *Config
}

// NewMinIOStorage creates a new MinIO storage instance
func NewMinIOStorage(config *Config) (*MinIOStorage, error) {
	// Initialize MinIO client
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKey, config.SecretKey, ""),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	storage := &MinIOStorage{
		client:    client,
		bucket:    config.Bucket,
		publicURL: config.PublicURL,
		config:    config,
	}

	return storage, nil
}

// Initialize sets up the storage (creates bucket if needed)
func (s *MinIOStorage) Initialize(ctx context.Context) error {
	// Check if bucket exists
	exists, err := s.client.BucketExists(ctx, s.bucket)
	if err != nil {
		return fmt.Errorf("failed to check bucket existence: %w", err)
	}

	// Create bucket if it doesn't exist
	if !exists {
		err = s.client.MakeBucket(ctx, s.bucket, minio.MakeBucketOptions{
			Region: s.config.Region,
		})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
	}

	return nil
}

// GeneratePresignedUploadURL generates a presigned URL for file upload
func (s *MinIOStorage) GeneratePresignedUploadURL(ctx context.Context, key string, contentType string, expiresIn time.Duration) (*PresignedURL, error) {
	// Set default expiration if not provided
	if expiresIn == 0 {
		expiresIn = 15 * time.Minute
	}

	// Generate presigned URL for PUT operation
	presignedURL, err := s.client.PresignedPutObject(ctx, s.bucket, key, expiresIn)
	if err != nil {
		return nil, &StorageError{
			Code:    ErrCodeUploadFailed,
			Message: "Failed to generate presigned upload URL",
			Details: err.Error(),
		}
	}

	return &PresignedURL{
		URL:       presignedURL.String(),
		ExpiresAt: time.Now().Add(expiresIn),
	}, nil
}

// UploadFile uploads a file directly to storage
func (s *MinIOStorage) UploadFile(ctx context.Context, key string, data io.Reader, contentType string, metadata map[string]string) (*UploadResult, error) {
	// Prepare upload options
	options := minio.PutObjectOptions{
		ContentType:  contentType,
		UserMetadata: metadata,
	}

	// Upload file
	info, err := s.client.PutObject(ctx, s.bucket, key, data, -1, options)
	if err != nil {
		return nil, &StorageError{
			Code:    ErrCodeUploadFailed,
			Message: "Failed to upload file",
			Details: err.Error(),
		}
	}

	// Generate public URL if configured
	publicURL := ""
	if s.publicURL != "" {
		publicURL = fmt.Sprintf("%s/%s/%s", s.publicURL, s.bucket, key)
	}

	return &UploadResult{
		Key:         key,
		ETag:        info.ETag,
		Size:        info.Size,
		ContentType: contentType,
		Metadata:    metadata,
		PublicURL:   publicURL,
	}, nil
}

// GeneratePresignedDownloadURL generates a presigned URL for file download
func (s *MinIOStorage) GeneratePresignedDownloadURL(ctx context.Context, key string, expiresIn time.Duration) (string, error) {
	// Set default expiration if not provided
	if expiresIn == 0 {
		expiresIn = 1 * time.Hour
	}

	// Generate presigned URL for GET operation
	presignedURL, err := s.client.PresignedGetObject(ctx, s.bucket, key, expiresIn, nil)
	if err != nil {
		return "", &StorageError{
			Code:    ErrCodeDownloadFailed,
			Message: "Failed to generate presigned download URL",
			Details: err.Error(),
		}
	}

	return presignedURL.String(), nil
}

// DownloadFile downloads a file from storage
func (s *MinIOStorage) DownloadFile(ctx context.Context, key string) (io.ReadCloser, error) {
	object, err := s.client.GetObject(ctx, s.bucket, key, minio.GetObjectOptions{})
	if err != nil {
		return nil, &StorageError{
			Code:    ErrCodeDownloadFailed,
			Message: "Failed to download file",
			Details: err.Error(),
		}
	}

	return object, nil
}

// DeleteFile deletes a file from storage
func (s *MinIOStorage) DeleteFile(ctx context.Context, key string) error {
	err := s.client.RemoveObject(ctx, s.bucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return &StorageError{
			Code:    ErrCodeDeleteFailed,
			Message: "Failed to delete file",
			Details: err.Error(),
		}
	}

	return nil
}

// CopyFile copies a file within storage
func (s *MinIOStorage) CopyFile(ctx context.Context, sourceKey, destKey string) error {
	// Source object
	src := minio.CopySrcOptions{
		Bucket: s.bucket,
		Object: sourceKey,
	}

	// Destination object
	dst := minio.CopyDestOptions{
		Bucket: s.bucket,
		Object: destKey,
	}

	_, err := s.client.CopyObject(ctx, dst, src)
	if err != nil {
		return &StorageError{
			Code:    ErrCodeUploadFailed,
			Message: "Failed to copy file",
			Details: err.Error(),
		}
	}

	return nil
}

// FileExists checks if a file exists in storage
func (s *MinIOStorage) FileExists(ctx context.Context, key string) (bool, error) {
	_, err := s.client.StatObject(ctx, s.bucket, key, minio.StatObjectOptions{})
	if err != nil {
		// Check if error is "not found"
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return false, nil
		}
		return false, &StorageError{
			Code:    ErrCodeUnknown,
			Message: "Failed to check file existence",
			Details: err.Error(),
		}
	}

	return true, nil
}

// GetFileInfo gets file information
func (s *MinIOStorage) GetFileInfo(ctx context.Context, key string) (*FileInfo, error) {
	stat, err := s.client.StatObject(ctx, s.bucket, key, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return nil, &StorageError{
				Code:    ErrCodeFileNotFound,
				Message: "File not found",
				Details: key,
			}
		}
		return nil, &StorageError{
			Code:    ErrCodeUnknown,
			Message: "Failed to get file info",
			Details: err.Error(),
		}
	}

	// Generate public URL if configured
	publicURL := ""
	if s.publicURL != "" {
		publicURL = fmt.Sprintf("%s/%s/%s", s.publicURL, s.bucket, key)
	}

	return &FileInfo{
		Key:          key,
		Size:         stat.Size,
		ContentType:  stat.ContentType,
		ETag:         stat.ETag,
		LastModified: stat.LastModified,
		Metadata:     stat.UserMetadata,
		PublicURL:    publicURL,
	}, nil
}

// DeleteFiles deletes multiple files from storage
func (s *MinIOStorage) DeleteFiles(ctx context.Context, keys []string) error {
	// Create channel for object names
	objectsCh := make(chan minio.ObjectInfo)

	// Send object names to channel
	go func() {
		defer close(objectsCh)
		for _, key := range keys {
			objectsCh <- minio.ObjectInfo{Key: key}
		}
	}()

	// Remove objects
	opts := minio.RemoveObjectsOptions{
		GovernanceBypass: true,
	}

	errorCh := s.client.RemoveObjects(ctx, s.bucket, objectsCh, opts)

	// Check for errors
	var errors []string
	for err := range errorCh {
		if err.Err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", err.ObjectName, err.Err))
		}
	}

	if len(errors) > 0 {
		return &StorageError{
			Code:    ErrCodeDeleteFailed,
			Message: "Failed to delete some files",
			Details: strings.Join(errors, "; "),
		}
	}

	return nil
}

// ListFiles lists files with a given prefix
func (s *MinIOStorage) ListFiles(ctx context.Context, prefix string, maxKeys int) ([]*FileInfo, error) {
	opts := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
		MaxKeys:   maxKeys,
	}

	var files []*FileInfo
	for object := range s.client.ListObjects(ctx, s.bucket, opts) {
		if object.Err != nil {
			return nil, &StorageError{
				Code:    ErrCodeUnknown,
				Message: "Failed to list files",
				Details: object.Err.Error(),
			}
		}

		// Generate public URL if configured
		publicURL := ""
		if s.publicURL != "" {
			publicURL = fmt.Sprintf("%s/%s/%s", s.publicURL, s.bucket, object.Key)
		}

		files = append(files, &FileInfo{
			Key:          object.Key,
			Size:         object.Size,
			ContentType:  object.ContentType,
			ETag:         object.ETag,
			LastModified: object.LastModified,
			PublicURL:    publicURL,
		})
	}

	return files, nil
}

// HealthCheck checks if the storage is accessible
func (s *MinIOStorage) HealthCheck(ctx context.Context) error {
	// Try to list buckets to check connectivity
	_, err := s.client.ListBuckets(ctx)
	if err != nil {
		return &StorageError{
			Code:    ErrCodeConnectionFailed,
			Message: "Storage health check failed",
			Details: err.Error(),
		}
	}

	// Check if our bucket exists
	exists, err := s.client.BucketExists(ctx, s.bucket)
	if err != nil {
		return &StorageError{
			Code:    ErrCodeConnectionFailed,
			Message: "Failed to check bucket existence",
			Details: err.Error(),
		}
	}

	if !exists {
		return &StorageError{
			Code:    ErrCodeInvalidConfig,
			Message: "Configured bucket does not exist",
			Details: s.bucket,
		}
	}

	return nil
}

// GetPublicURL returns the public URL for a file
func (s *MinIOStorage) GetPublicURL(key string) string {
	if s.publicURL == "" {
		return ""
	}
	return fmt.Sprintf("%s/%s/%s", s.publicURL, s.bucket, key)
}

// GenerateSignedURL generates a signed URL with custom parameters
func (s *MinIOStorage) GenerateSignedURL(ctx context.Context, key string, method string, expiresIn time.Duration, params url.Values) (string, error) {
	switch strings.ToUpper(method) {
	case "GET":
		presignedURL, err := s.client.PresignedGetObject(ctx, s.bucket, key, expiresIn, params)
		if err != nil {
			return "", err
		}
		return presignedURL.String(), nil
	case "PUT":
		presignedURL, err := s.client.PresignedPutObject(ctx, s.bucket, key, expiresIn)
		if err != nil {
			return "", err
		}
		return presignedURL.String(), nil
	case "DELETE":
		// MinIO doesn't support presigned DELETE, return error
		return "", &StorageError{
			Code:    ErrCodeInvalidConfig,
			Message: "Presigned DELETE not supported",
		}
	default:
		return "", &StorageError{
			Code:    ErrCodeInvalidConfig,
			Message: "Unsupported HTTP method",
			Details: method,
		}
	}
}

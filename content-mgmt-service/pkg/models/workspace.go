package models

import "time"

// WorkspaceResponse represents a workspace response
type WorkspaceResponse struct {
	BaseModel
	Name        string              `json:"name"`
	Description string              `json:"description"`
	OwnerID     string              `json:"owner_id"`
	Type        string              `json:"type"` // personal, team, enterprise
	Status      string              `json:"status"` // active, suspended, archived
	Settings    WorkspaceSettings   `json:"settings"`
	Members     []WorkspaceMember   `json:"members,omitempty"`
	Stats       WorkspaceStats      `json:"stats,omitempty"`
	Permissions WorkspacePermissions `json:"permissions,omitempty"`
	Plan        WorkspacePlan       `json:"plan,omitempty"`
}

// WorkspaceSettings represents workspace settings
type WorkspaceSettings struct {
	Timezone            string                 `json:"timezone"`
	DefaultPlatforms    []string               `json:"default_platforms"`
	AutoApproval        bool                   `json:"auto_approval"`
	ContentModeration   bool                   `json:"content_moderation"`
	BrandGuidelines     map[string]interface{} `json:"brand_guidelines,omitempty"`
	PostingSchedule     PostingSchedule        `json:"posting_schedule,omitempty"`
	NotificationSettings NotificationSettings  `json:"notification_settings"`
	CreditPooling       bool                   `json:"credit_pooling"`
	SharedAssets        bool                   `json:"shared_assets"`
}

// PostingSchedule represents posting schedule settings
type PostingSchedule struct {
	Enabled      bool                    `json:"enabled"`
	OptimalTimes map[string][]TimeSlot   `json:"optimal_times"` // platform -> time slots
	Blackouts    []BlackoutPeriod        `json:"blackouts,omitempty"`
}

// TimeSlot represents a time slot
type TimeSlot struct {
	Hour   int `json:"hour"`
	Minute int `json:"minute"`
}

// BlackoutPeriod represents a blackout period
type BlackoutPeriod struct {
	Name      string    `json:"name"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	Recurring bool      `json:"recurring"`
}

// NotificationSettings represents notification settings
type NotificationSettings struct {
	Email    EmailNotifications    `json:"email"`
	InApp    InAppNotifications    `json:"in_app"`
	Webhook  WebhookNotifications  `json:"webhook,omitempty"`
}

// EmailNotifications represents email notification settings
type EmailNotifications struct {
	Enabled           bool `json:"enabled"`
	PostPublished     bool `json:"post_published"`
	PostFailed        bool `json:"post_failed"`
	MemberJoined      bool `json:"member_joined"`
	CreditLow         bool `json:"credit_low"`
	WeeklyReport      bool `json:"weekly_report"`
	MonthlyReport     bool `json:"monthly_report"`
}

// InAppNotifications represents in-app notification settings
type InAppNotifications struct {
	Enabled           bool `json:"enabled"`
	PostPublished     bool `json:"post_published"`
	PostFailed        bool `json:"post_failed"`
	MemberJoined      bool `json:"member_joined"`
	CreditLow         bool `json:"credit_low"`
	TemplateShared    bool `json:"template_shared"`
}

// WebhookNotifications represents webhook notification settings
type WebhookNotifications struct {
	Enabled bool   `json:"enabled"`
	URL     string `json:"url,omitempty"`
	Secret  string `json:"secret,omitempty"`
	Events  []string `json:"events,omitempty"`
}

// WorkspaceMember represents a workspace member
type WorkspaceMember struct {
	UserID      string                 `json:"user_id"`
	Email       string                 `json:"email"`
	Name        string                 `json:"name"`
	Avatar      string                 `json:"avatar,omitempty"`
	Role        string                 `json:"role"` // owner, admin, editor, viewer
	Status      string                 `json:"status"` // active, pending, suspended
	Permissions []string               `json:"permissions"`
	JoinedAt    time.Time              `json:"joined_at"`
	LastActive  *time.Time             `json:"last_active,omitempty"`
	Stats       WorkspaceMemberStats   `json:"stats,omitempty"`
}

// WorkspaceMemberStats represents member statistics
type WorkspaceMemberStats struct {
	PostsCreated    int64 `json:"posts_created"`
	PostsPublished  int64 `json:"posts_published"`
	TemplatesCreated int64 `json:"templates_created"`
	CreditsUsed     int64 `json:"credits_used"`
}

// WorkspaceStats represents workspace statistics
type WorkspaceStats struct {
	TotalMembers     int64 `json:"total_members"`
	ActiveMembers    int64 `json:"active_members"`
	TotalPosts       int64 `json:"total_posts"`
	PublishedPosts   int64 `json:"published_posts"`
	ScheduledPosts   int64 `json:"scheduled_posts"`
	TotalTemplates   int64 `json:"total_templates"`
	SharedTemplates  int64 `json:"shared_templates"`
	CreditsUsed      int64 `json:"credits_used"`
	CreditsRemaining int64 `json:"credits_remaining"`
}

// WorkspacePermissions represents user permissions in workspace
type WorkspacePermissions struct {
	CanCreatePosts     bool `json:"can_create_posts"`
	CanEditPosts       bool `json:"can_edit_posts"`
	CanDeletePosts     bool `json:"can_delete_posts"`
	CanPublishPosts    bool `json:"can_publish_posts"`
	CanCreateTemplates bool `json:"can_create_templates"`
	CanEditTemplates   bool `json:"can_edit_templates"`
	CanDeleteTemplates bool `json:"can_delete_templates"`
	CanManageMembers   bool `json:"can_manage_members"`
	CanManageSettings  bool `json:"can_manage_settings"`
	CanViewAnalytics   bool `json:"can_view_analytics"`
	CanUseCredits      bool `json:"can_use_credits"`
}

// WorkspacePlan represents workspace plan information
type WorkspacePlan struct {
	Name            string    `json:"name"`
	Type            string    `json:"type"` // free, pro, enterprise
	MaxMembers      int       `json:"max_members"`
	MaxPosts        int       `json:"max_posts"`
	MaxTemplates    int       `json:"max_templates"`
	CreditsPerMonth int       `json:"credits_per_month"`
	Features        []string  `json:"features"`
	ExpiresAt       *time.Time `json:"expires_at,omitempty"`
}

// CreateWorkspaceRequest represents create workspace request
type CreateWorkspaceRequest struct {
	OwnerID     string            `json:"owner_id"`
	Name        string            `json:"name" validate:"required,min=1,max=100"`
	Description string            `json:"description,omitempty"`
	Type        string            `json:"type" validate:"required,oneof=personal team enterprise"`
	Settings    WorkspaceSettings `json:"settings,omitempty"`
}

// UpdateWorkspaceRequest represents update workspace request
type UpdateWorkspaceRequest struct {
	ID          string            `json:"id"`
	UserID      string            `json:"user_id"`
	Name        string            `json:"name,omitempty"`
	Description string            `json:"description,omitempty"`
	Settings    WorkspaceSettings `json:"settings,omitempty"`
}

// ListWorkspacesRequest represents list workspaces request
type ListWorkspacesRequest struct {
	UserID    string `json:"user_id"`
	Page      int    `json:"page" validate:"min=1"`
	Limit     int    `json:"limit" validate:"min=1,max=100"`
	Type      string `json:"type,omitempty"`
	Status    string `json:"status,omitempty"`
	Search    string `json:"search,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
}

// ListWorkspacesResponse represents list workspaces response
type ListWorkspacesResponse struct {
	Workspaces []WorkspaceResponse `json:"workspaces"`
	Pagination PaginationMeta      `json:"pagination"`
}

// AddMemberRequest represents add member request
type AddMemberRequest struct {
	WorkspaceID string   `json:"workspace_id"`
	UserID      string   `json:"user_id"`
	Email       string   `json:"email,omitempty"`
	Role        string   `json:"role" validate:"required,oneof=admin editor viewer"`
	Permissions []string `json:"permissions,omitempty"`
	Message     string   `json:"message,omitempty"`
}

// UpdateMemberRoleRequest represents update member role request
type UpdateMemberRoleRequest struct {
	WorkspaceID string   `json:"workspace_id"`
	UserID      string   `json:"user_id"`
	MemberID    string   `json:"member_id"`
	Role        string   `json:"role" validate:"required,oneof=admin editor viewer"`
	Permissions []string `json:"permissions,omitempty"`
}

// ListMembersRequest represents list members request
type ListMembersRequest struct {
	WorkspaceID string `json:"workspace_id"`
	UserID      string `json:"user_id"`
	Page        int    `json:"page" validate:"min=1"`
	Limit       int    `json:"limit" validate:"min=1,max=100"`
	Role        string `json:"role,omitempty"`
	Status      string `json:"status,omitempty"`
	Search      string `json:"search,omitempty"`
}

// ListMembersResponse represents list members response
type ListMembersResponse struct {
	Members    []WorkspaceMember `json:"members"`
	Pagination PaginationMeta    `json:"pagination"`
}

// WorkspaceAnalyticsRequest represents workspace analytics request
type WorkspaceAnalyticsRequest struct {
	WorkspaceID string `json:"workspace_id"`
	UserID      string `json:"user_id"`
	Period      string `json:"period,omitempty"` // day, week, month, year
	DateFrom    string `json:"date_from,omitempty"`
	DateTo      string `json:"date_to,omitempty"`
}

// WorkspaceAnalyticsResponse represents workspace analytics response
type WorkspaceAnalyticsResponse struct {
	WorkspaceID     string                        `json:"workspace_id"`
	Period          string                        `json:"period"`
	Overview        WorkspaceAnalyticsOverview    `json:"overview"`
	Timeline        []WorkspaceTimelineAnalytics  `json:"timeline"`
	Members         []WorkspaceMemberAnalytics    `json:"members"`
	Platforms       map[string]int64              `json:"platforms"`
	ContentTypes    map[string]int64              `json:"content_types"`
	Performance     WorkspacePerformanceAnalytics `json:"performance"`
}

// WorkspaceAnalyticsOverview represents workspace analytics overview
type WorkspaceAnalyticsOverview struct {
	TotalPosts       int64   `json:"total_posts"`
	PublishedPosts   int64   `json:"published_posts"`
	ScheduledPosts   int64   `json:"scheduled_posts"`
	TotalEngagement  int64   `json:"total_engagement"`
	EngagementRate   float64 `json:"engagement_rate"`
	TotalReach       int64   `json:"total_reach"`
	CreditsUsed      int64   `json:"credits_used"`
	ActiveMembers    int64   `json:"active_members"`
}

// WorkspaceTimelineAnalytics represents workspace analytics over time
type WorkspaceTimelineAnalytics struct {
	Date            string  `json:"date"`
	PostsCreated    int64   `json:"posts_created"`
	PostsPublished  int64   `json:"posts_published"`
	Engagement      int64   `json:"engagement"`
	Reach           int64   `json:"reach"`
	CreditsUsed     int64   `json:"credits_used"`
	ActiveMembers   int64   `json:"active_members"`
}

// WorkspaceMemberAnalytics represents member analytics
type WorkspaceMemberAnalytics struct {
	UserID          string  `json:"user_id"`
	Name            string  `json:"name"`
	PostsCreated    int64   `json:"posts_created"`
	PostsPublished  int64   `json:"posts_published"`
	Engagement      int64   `json:"engagement"`
	CreditsUsed     int64   `json:"credits_used"`
	EngagementRate  float64 `json:"engagement_rate"`
}

// WorkspacePerformanceAnalytics represents workspace performance analytics
type WorkspacePerformanceAnalytics struct {
	TopPerformingPosts    []PostPerformance    `json:"top_performing_posts"`
	TopPerformingTemplates []TemplatePerformance `json:"top_performing_templates"`
	BestPostingTimes      []OptimalTime        `json:"best_posting_times"`
	PlatformPerformance   []PlatformPerformance `json:"platform_performance"`
}

// PostPerformance represents post performance
type PostPerformance struct {
	PostID         string  `json:"post_id"`
	Title          string  `json:"title"`
	Platform       string  `json:"platform"`
	Engagement     int64   `json:"engagement"`
	EngagementRate float64 `json:"engagement_rate"`
	Reach          int64   `json:"reach"`
}

// TemplatePerformance represents template performance
type TemplatePerformance struct {
	TemplateID     string  `json:"template_id"`
	Name           string  `json:"name"`
	Uses           int64   `json:"uses"`
	SuccessRate    float64 `json:"success_rate"`
	AverageRating  float64 `json:"average_rating"`
}

// OptimalTime represents optimal posting time
type OptimalTime struct {
	Platform       string  `json:"platform"`
	DayOfWeek      int     `json:"day_of_week"` // 0 = Sunday
	Hour           int     `json:"hour"`
	EngagementRate float64 `json:"engagement_rate"`
}

// PlatformPerformance represents platform performance
type PlatformPerformance struct {
	Platform       string  `json:"platform"`
	Posts          int64   `json:"posts"`
	Engagement     int64   `json:"engagement"`
	EngagementRate float64 `json:"engagement_rate"`
	Reach          int64   `json:"reach"`
	BestTime       string  `json:"best_time"`
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/user-service/ent/session"
	"github.com/social-content-ai/user-service/ent/user"
)

// UserCreate is the builder for creating a User entity.
type UserCreate struct {
	config
	mutation *UserMutation
	hooks    []Hook
}

// SetFullName sets the "full_name" field.
func (uc *UserCreate) SetFullName(s string) *UserCreate {
	uc.mutation.SetFullName(s)
	return uc
}

// SetNillableFullName sets the "full_name" field if the given value is not nil.
func (uc *UserCreate) SetNillableFullName(s *string) *UserCreate {
	if s != nil {
		uc.SetFullName(*s)
	}
	return uc
}

// SetEmail sets the "email" field.
func (uc *UserCreate) SetEmail(s string) *UserCreate {
	uc.mutation.SetEmail(s)
	return uc
}

// SetPhone sets the "phone" field.
func (uc *UserCreate) SetPhone(s string) *UserCreate {
	uc.mutation.SetPhone(s)
	return uc
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (uc *UserCreate) SetNillablePhone(s *string) *UserCreate {
	if s != nil {
		uc.SetPhone(*s)
	}
	return uc
}

// SetAvatarURL sets the "avatar_url" field.
func (uc *UserCreate) SetAvatarURL(s string) *UserCreate {
	uc.mutation.SetAvatarURL(s)
	return uc
}

// SetNillableAvatarURL sets the "avatar_url" field if the given value is not nil.
func (uc *UserCreate) SetNillableAvatarURL(s *string) *UserCreate {
	if s != nil {
		uc.SetAvatarURL(*s)
	}
	return uc
}

// SetBio sets the "bio" field.
func (uc *UserCreate) SetBio(s string) *UserCreate {
	uc.mutation.SetBio(s)
	return uc
}

// SetNillableBio sets the "bio" field if the given value is not nil.
func (uc *UserCreate) SetNillableBio(s *string) *UserCreate {
	if s != nil {
		uc.SetBio(*s)
	}
	return uc
}

// SetCompany sets the "company" field.
func (uc *UserCreate) SetCompany(s string) *UserCreate {
	uc.mutation.SetCompany(s)
	return uc
}

// SetNillableCompany sets the "company" field if the given value is not nil.
func (uc *UserCreate) SetNillableCompany(s *string) *UserCreate {
	if s != nil {
		uc.SetCompany(*s)
	}
	return uc
}

// SetIndustry sets the "industry" field.
func (uc *UserCreate) SetIndustry(s string) *UserCreate {
	uc.mutation.SetIndustry(s)
	return uc
}

// SetNillableIndustry sets the "industry" field if the given value is not nil.
func (uc *UserCreate) SetNillableIndustry(s *string) *UserCreate {
	if s != nil {
		uc.SetIndustry(*s)
	}
	return uc
}

// SetRole sets the "role" field.
func (uc *UserCreate) SetRole(u user.Role) *UserCreate {
	uc.mutation.SetRole(u)
	return uc
}

// SetNillableRole sets the "role" field if the given value is not nil.
func (uc *UserCreate) SetNillableRole(u *user.Role) *UserCreate {
	if u != nil {
		uc.SetRole(*u)
	}
	return uc
}

// SetPasswordHash sets the "password_hash" field.
func (uc *UserCreate) SetPasswordHash(s string) *UserCreate {
	uc.mutation.SetPasswordHash(s)
	return uc
}

// SetIsVerified sets the "is_verified" field.
func (uc *UserCreate) SetIsVerified(b bool) *UserCreate {
	uc.mutation.SetIsVerified(b)
	return uc
}

// SetNillableIsVerified sets the "is_verified" field if the given value is not nil.
func (uc *UserCreate) SetNillableIsVerified(b *bool) *UserCreate {
	if b != nil {
		uc.SetIsVerified(*b)
	}
	return uc
}

// SetTwoFactorEnabled sets the "two_factor_enabled" field.
func (uc *UserCreate) SetTwoFactorEnabled(b bool) *UserCreate {
	uc.mutation.SetTwoFactorEnabled(b)
	return uc
}

// SetNillableTwoFactorEnabled sets the "two_factor_enabled" field if the given value is not nil.
func (uc *UserCreate) SetNillableTwoFactorEnabled(b *bool) *UserCreate {
	if b != nil {
		uc.SetTwoFactorEnabled(*b)
	}
	return uc
}

// SetTwoFactorSecret sets the "two_factor_secret" field.
func (uc *UserCreate) SetTwoFactorSecret(s string) *UserCreate {
	uc.mutation.SetTwoFactorSecret(s)
	return uc
}

// SetNillableTwoFactorSecret sets the "two_factor_secret" field if the given value is not nil.
func (uc *UserCreate) SetNillableTwoFactorSecret(s *string) *UserCreate {
	if s != nil {
		uc.SetTwoFactorSecret(*s)
	}
	return uc
}

// SetTwoFactorBackupCodes sets the "two_factor_backup_codes" field.
func (uc *UserCreate) SetTwoFactorBackupCodes(s []string) *UserCreate {
	uc.mutation.SetTwoFactorBackupCodes(s)
	return uc
}

// SetTwoFactorEnabledAt sets the "two_factor_enabled_at" field.
func (uc *UserCreate) SetTwoFactorEnabledAt(t time.Time) *UserCreate {
	uc.mutation.SetTwoFactorEnabledAt(t)
	return uc
}

// SetNillableTwoFactorEnabledAt sets the "two_factor_enabled_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableTwoFactorEnabledAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetTwoFactorEnabledAt(*t)
	}
	return uc
}

// SetNotificationSettings sets the "notification_settings" field.
func (uc *UserCreate) SetNotificationSettings(m map[string]interface{}) *UserCreate {
	uc.mutation.SetNotificationSettings(m)
	return uc
}

// SetInviteCode sets the "invite_code" field.
func (uc *UserCreate) SetInviteCode(s string) *UserCreate {
	uc.mutation.SetInviteCode(s)
	return uc
}

// SetNillableInviteCode sets the "invite_code" field if the given value is not nil.
func (uc *UserCreate) SetNillableInviteCode(s *string) *UserCreate {
	if s != nil {
		uc.SetInviteCode(*s)
	}
	return uc
}

// SetAffCode sets the "aff_code" field.
func (uc *UserCreate) SetAffCode(s string) *UserCreate {
	uc.mutation.SetAffCode(s)
	return uc
}

// SetNillableAffCode sets the "aff_code" field if the given value is not nil.
func (uc *UserCreate) SetNillableAffCode(s *string) *UserCreate {
	if s != nil {
		uc.SetAffCode(*s)
	}
	return uc
}

// SetAffJoinedAt sets the "aff_joined_at" field.
func (uc *UserCreate) SetAffJoinedAt(t time.Time) *UserCreate {
	uc.mutation.SetAffJoinedAt(t)
	return uc
}

// SetNillableAffJoinedAt sets the "aff_joined_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableAffJoinedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetAffJoinedAt(*t)
	}
	return uc
}

// SetCreatedAt sets the "created_at" field.
func (uc *UserCreate) SetCreatedAt(t time.Time) *UserCreate {
	uc.mutation.SetCreatedAt(t)
	return uc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableCreatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetCreatedAt(*t)
	}
	return uc
}

// SetUpdatedAt sets the "updated_at" field.
func (uc *UserCreate) SetUpdatedAt(t time.Time) *UserCreate {
	uc.mutation.SetUpdatedAt(t)
	return uc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableUpdatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetUpdatedAt(*t)
	}
	return uc
}

// SetDeletedAt sets the "deleted_at" field.
func (uc *UserCreate) SetDeletedAt(t time.Time) *UserCreate {
	uc.mutation.SetDeletedAt(t)
	return uc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableDeletedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetDeletedAt(*t)
	}
	return uc
}

// SetID sets the "id" field.
func (uc *UserCreate) SetID(u uuid.UUID) *UserCreate {
	uc.mutation.SetID(u)
	return uc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (uc *UserCreate) SetNillableID(u *uuid.UUID) *UserCreate {
	if u != nil {
		uc.SetID(*u)
	}
	return uc
}

// AddSessionIDs adds the "sessions" edge to the Session entity by IDs.
func (uc *UserCreate) AddSessionIDs(ids ...uuid.UUID) *UserCreate {
	uc.mutation.AddSessionIDs(ids...)
	return uc
}

// AddSessions adds the "sessions" edges to the Session entity.
func (uc *UserCreate) AddSessions(s ...*Session) *UserCreate {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return uc.AddSessionIDs(ids...)
}

// Mutation returns the UserMutation object of the builder.
func (uc *UserCreate) Mutation() *UserMutation {
	return uc.mutation
}

// Save creates the User in the database.
func (uc *UserCreate) Save(ctx context.Context) (*User, error) {
	uc.defaults()
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UserCreate) SaveX(ctx context.Context) *User {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UserCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UserCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UserCreate) defaults() {
	if _, ok := uc.mutation.Role(); !ok {
		v := user.DefaultRole
		uc.mutation.SetRole(v)
	}
	if _, ok := uc.mutation.IsVerified(); !ok {
		v := user.DefaultIsVerified
		uc.mutation.SetIsVerified(v)
	}
	if _, ok := uc.mutation.TwoFactorEnabled(); !ok {
		v := user.DefaultTwoFactorEnabled
		uc.mutation.SetTwoFactorEnabled(v)
	}
	if _, ok := uc.mutation.CreatedAt(); !ok {
		v := user.DefaultCreatedAt()
		uc.mutation.SetCreatedAt(v)
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		v := user.DefaultUpdatedAt()
		uc.mutation.SetUpdatedAt(v)
	}
	if _, ok := uc.mutation.ID(); !ok {
		v := user.DefaultID()
		uc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uc *UserCreate) check() error {
	if v, ok := uc.mutation.FullName(); ok {
		if err := user.FullNameValidator(v); err != nil {
			return &ValidationError{Name: "full_name", err: fmt.Errorf(`ent: validator failed for field "User.full_name": %w`, err)}
		}
	}
	if _, ok := uc.mutation.Email(); !ok {
		return &ValidationError{Name: "email", err: errors.New(`ent: missing required field "User.email"`)}
	}
	if v, ok := uc.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Phone(); ok {
		if err := user.PhoneValidator(v); err != nil {
			return &ValidationError{Name: "phone", err: fmt.Errorf(`ent: validator failed for field "User.phone": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Company(); ok {
		if err := user.CompanyValidator(v); err != nil {
			return &ValidationError{Name: "company", err: fmt.Errorf(`ent: validator failed for field "User.company": %w`, err)}
		}
	}
	if v, ok := uc.mutation.Industry(); ok {
		if err := user.IndustryValidator(v); err != nil {
			return &ValidationError{Name: "industry", err: fmt.Errorf(`ent: validator failed for field "User.industry": %w`, err)}
		}
	}
	if _, ok := uc.mutation.Role(); !ok {
		return &ValidationError{Name: "role", err: errors.New(`ent: missing required field "User.role"`)}
	}
	if v, ok := uc.mutation.Role(); ok {
		if err := user.RoleValidator(v); err != nil {
			return &ValidationError{Name: "role", err: fmt.Errorf(`ent: validator failed for field "User.role": %w`, err)}
		}
	}
	if _, ok := uc.mutation.PasswordHash(); !ok {
		return &ValidationError{Name: "password_hash", err: errors.New(`ent: missing required field "User.password_hash"`)}
	}
	if _, ok := uc.mutation.IsVerified(); !ok {
		return &ValidationError{Name: "is_verified", err: errors.New(`ent: missing required field "User.is_verified"`)}
	}
	if _, ok := uc.mutation.TwoFactorEnabled(); !ok {
		return &ValidationError{Name: "two_factor_enabled", err: errors.New(`ent: missing required field "User.two_factor_enabled"`)}
	}
	if v, ok := uc.mutation.InviteCode(); ok {
		if err := user.InviteCodeValidator(v); err != nil {
			return &ValidationError{Name: "invite_code", err: fmt.Errorf(`ent: validator failed for field "User.invite_code": %w`, err)}
		}
	}
	if v, ok := uc.mutation.AffCode(); ok {
		if err := user.AffCodeValidator(v); err != nil {
			return &ValidationError{Name: "aff_code", err: fmt.Errorf(`ent: validator failed for field "User.aff_code": %w`, err)}
		}
	}
	if _, ok := uc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "User.created_at"`)}
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "User.updated_at"`)}
	}
	return nil
}

func (uc *UserCreate) sqlSave(ctx context.Context) (*User, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UserCreate) createSpec() (*User, *sqlgraph.CreateSpec) {
	var (
		_node = &User{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(user.Table, sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID))
	)
	if id, ok := uc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := uc.mutation.FullName(); ok {
		_spec.SetField(user.FieldFullName, field.TypeString, value)
		_node.FullName = value
	}
	if value, ok := uc.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := uc.mutation.Phone(); ok {
		_spec.SetField(user.FieldPhone, field.TypeString, value)
		_node.Phone = value
	}
	if value, ok := uc.mutation.AvatarURL(); ok {
		_spec.SetField(user.FieldAvatarURL, field.TypeString, value)
		_node.AvatarURL = value
	}
	if value, ok := uc.mutation.Bio(); ok {
		_spec.SetField(user.FieldBio, field.TypeString, value)
		_node.Bio = value
	}
	if value, ok := uc.mutation.Company(); ok {
		_spec.SetField(user.FieldCompany, field.TypeString, value)
		_node.Company = value
	}
	if value, ok := uc.mutation.Industry(); ok {
		_spec.SetField(user.FieldIndustry, field.TypeString, value)
		_node.Industry = value
	}
	if value, ok := uc.mutation.Role(); ok {
		_spec.SetField(user.FieldRole, field.TypeEnum, value)
		_node.Role = value
	}
	if value, ok := uc.mutation.PasswordHash(); ok {
		_spec.SetField(user.FieldPasswordHash, field.TypeString, value)
		_node.PasswordHash = value
	}
	if value, ok := uc.mutation.IsVerified(); ok {
		_spec.SetField(user.FieldIsVerified, field.TypeBool, value)
		_node.IsVerified = value
	}
	if value, ok := uc.mutation.TwoFactorEnabled(); ok {
		_spec.SetField(user.FieldTwoFactorEnabled, field.TypeBool, value)
		_node.TwoFactorEnabled = value
	}
	if value, ok := uc.mutation.TwoFactorSecret(); ok {
		_spec.SetField(user.FieldTwoFactorSecret, field.TypeString, value)
		_node.TwoFactorSecret = value
	}
	if value, ok := uc.mutation.TwoFactorBackupCodes(); ok {
		_spec.SetField(user.FieldTwoFactorBackupCodes, field.TypeJSON, value)
		_node.TwoFactorBackupCodes = value
	}
	if value, ok := uc.mutation.TwoFactorEnabledAt(); ok {
		_spec.SetField(user.FieldTwoFactorEnabledAt, field.TypeTime, value)
		_node.TwoFactorEnabledAt = value
	}
	if value, ok := uc.mutation.NotificationSettings(); ok {
		_spec.SetField(user.FieldNotificationSettings, field.TypeJSON, value)
		_node.NotificationSettings = value
	}
	if value, ok := uc.mutation.InviteCode(); ok {
		_spec.SetField(user.FieldInviteCode, field.TypeString, value)
		_node.InviteCode = value
	}
	if value, ok := uc.mutation.AffCode(); ok {
		_spec.SetField(user.FieldAffCode, field.TypeString, value)
		_node.AffCode = value
	}
	if value, ok := uc.mutation.AffJoinedAt(); ok {
		_spec.SetField(user.FieldAffJoinedAt, field.TypeTime, value)
		_node.AffJoinedAt = value
	}
	if value, ok := uc.mutation.CreatedAt(); ok {
		_spec.SetField(user.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uc.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uc.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if nodes := uc.mutation.SessionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.SessionsTable,
			Columns: []string{user.SessionsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(session.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// UserCreateBulk is the builder for creating many User entities in bulk.
type UserCreateBulk struct {
	config
	err      error
	builders []*UserCreate
}

// Save creates the User entities in the database.
func (ucb *UserCreateBulk) Save(ctx context.Context) ([]*User, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*User, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UserCreateBulk) SaveX(ctx context.Context) []*User {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UserCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UserCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}

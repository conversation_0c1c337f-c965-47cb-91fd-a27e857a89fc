// Code generated by ent, DO NOT EDIT.

package credittransaction

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldUserID, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldAmount, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldDescription, v))
}

// ReferenceID applies equality check predicate on the "reference_id" field. It's identical to ReferenceIDEQ.
func ReferenceID(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceType applies equality check predicate on the "reference_type" field. It's identical to ReferenceTypeEQ.
func ReferenceType(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldReferenceType, v))
}

// PaymentID applies equality check predicate on the "payment_id" field. It's identical to PaymentIDEQ.
func PaymentID(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldPaymentID, v))
}

// InvoiceID applies equality check predicate on the "invoice_id" field. It's identical to InvoiceIDEQ.
func InvoiceID(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldInvoiceID, v))
}

// ProcessedAt applies equality check predicate on the "processed_at" field. It's identical to ProcessedAtEQ.
func ProcessedAt(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldProcessedAt, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldUserID, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldType, vs...))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v int) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldAmount, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContainsFold(FieldDescription, v))
}

// ReferenceIDEQ applies the EQ predicate on the "reference_id" field.
func ReferenceIDEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceIDNEQ applies the NEQ predicate on the "reference_id" field.
func ReferenceIDNEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldReferenceID, v))
}

// ReferenceIDIn applies the In predicate on the "reference_id" field.
func ReferenceIDIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldReferenceID, vs...))
}

// ReferenceIDNotIn applies the NotIn predicate on the "reference_id" field.
func ReferenceIDNotIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldReferenceID, vs...))
}

// ReferenceIDGT applies the GT predicate on the "reference_id" field.
func ReferenceIDGT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldReferenceID, v))
}

// ReferenceIDGTE applies the GTE predicate on the "reference_id" field.
func ReferenceIDGTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldReferenceID, v))
}

// ReferenceIDLT applies the LT predicate on the "reference_id" field.
func ReferenceIDLT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldReferenceID, v))
}

// ReferenceIDLTE applies the LTE predicate on the "reference_id" field.
func ReferenceIDLTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldReferenceID, v))
}

// ReferenceIDContains applies the Contains predicate on the "reference_id" field.
func ReferenceIDContains(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContains(FieldReferenceID, v))
}

// ReferenceIDHasPrefix applies the HasPrefix predicate on the "reference_id" field.
func ReferenceIDHasPrefix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasPrefix(FieldReferenceID, v))
}

// ReferenceIDHasSuffix applies the HasSuffix predicate on the "reference_id" field.
func ReferenceIDHasSuffix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasSuffix(FieldReferenceID, v))
}

// ReferenceIDIsNil applies the IsNil predicate on the "reference_id" field.
func ReferenceIDIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldReferenceID))
}

// ReferenceIDNotNil applies the NotNil predicate on the "reference_id" field.
func ReferenceIDNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldReferenceID))
}

// ReferenceIDEqualFold applies the EqualFold predicate on the "reference_id" field.
func ReferenceIDEqualFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEqualFold(FieldReferenceID, v))
}

// ReferenceIDContainsFold applies the ContainsFold predicate on the "reference_id" field.
func ReferenceIDContainsFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContainsFold(FieldReferenceID, v))
}

// ReferenceTypeEQ applies the EQ predicate on the "reference_type" field.
func ReferenceTypeEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldReferenceType, v))
}

// ReferenceTypeNEQ applies the NEQ predicate on the "reference_type" field.
func ReferenceTypeNEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldReferenceType, v))
}

// ReferenceTypeIn applies the In predicate on the "reference_type" field.
func ReferenceTypeIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldReferenceType, vs...))
}

// ReferenceTypeNotIn applies the NotIn predicate on the "reference_type" field.
func ReferenceTypeNotIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldReferenceType, vs...))
}

// ReferenceTypeGT applies the GT predicate on the "reference_type" field.
func ReferenceTypeGT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldReferenceType, v))
}

// ReferenceTypeGTE applies the GTE predicate on the "reference_type" field.
func ReferenceTypeGTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldReferenceType, v))
}

// ReferenceTypeLT applies the LT predicate on the "reference_type" field.
func ReferenceTypeLT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldReferenceType, v))
}

// ReferenceTypeLTE applies the LTE predicate on the "reference_type" field.
func ReferenceTypeLTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldReferenceType, v))
}

// ReferenceTypeContains applies the Contains predicate on the "reference_type" field.
func ReferenceTypeContains(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContains(FieldReferenceType, v))
}

// ReferenceTypeHasPrefix applies the HasPrefix predicate on the "reference_type" field.
func ReferenceTypeHasPrefix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasPrefix(FieldReferenceType, v))
}

// ReferenceTypeHasSuffix applies the HasSuffix predicate on the "reference_type" field.
func ReferenceTypeHasSuffix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasSuffix(FieldReferenceType, v))
}

// ReferenceTypeIsNil applies the IsNil predicate on the "reference_type" field.
func ReferenceTypeIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldReferenceType))
}

// ReferenceTypeNotNil applies the NotNil predicate on the "reference_type" field.
func ReferenceTypeNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldReferenceType))
}

// ReferenceTypeEqualFold applies the EqualFold predicate on the "reference_type" field.
func ReferenceTypeEqualFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEqualFold(FieldReferenceType, v))
}

// ReferenceTypeContainsFold applies the ContainsFold predicate on the "reference_type" field.
func ReferenceTypeContainsFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContainsFold(FieldReferenceType, v))
}

// PaymentIDEQ applies the EQ predicate on the "payment_id" field.
func PaymentIDEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldPaymentID, v))
}

// PaymentIDNEQ applies the NEQ predicate on the "payment_id" field.
func PaymentIDNEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldPaymentID, v))
}

// PaymentIDIn applies the In predicate on the "payment_id" field.
func PaymentIDIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldPaymentID, vs...))
}

// PaymentIDNotIn applies the NotIn predicate on the "payment_id" field.
func PaymentIDNotIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldPaymentID, vs...))
}

// PaymentIDGT applies the GT predicate on the "payment_id" field.
func PaymentIDGT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldPaymentID, v))
}

// PaymentIDGTE applies the GTE predicate on the "payment_id" field.
func PaymentIDGTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldPaymentID, v))
}

// PaymentIDLT applies the LT predicate on the "payment_id" field.
func PaymentIDLT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldPaymentID, v))
}

// PaymentIDLTE applies the LTE predicate on the "payment_id" field.
func PaymentIDLTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldPaymentID, v))
}

// PaymentIDContains applies the Contains predicate on the "payment_id" field.
func PaymentIDContains(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContains(FieldPaymentID, v))
}

// PaymentIDHasPrefix applies the HasPrefix predicate on the "payment_id" field.
func PaymentIDHasPrefix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasPrefix(FieldPaymentID, v))
}

// PaymentIDHasSuffix applies the HasSuffix predicate on the "payment_id" field.
func PaymentIDHasSuffix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasSuffix(FieldPaymentID, v))
}

// PaymentIDIsNil applies the IsNil predicate on the "payment_id" field.
func PaymentIDIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldPaymentID))
}

// PaymentIDNotNil applies the NotNil predicate on the "payment_id" field.
func PaymentIDNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldPaymentID))
}

// PaymentIDEqualFold applies the EqualFold predicate on the "payment_id" field.
func PaymentIDEqualFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEqualFold(FieldPaymentID, v))
}

// PaymentIDContainsFold applies the ContainsFold predicate on the "payment_id" field.
func PaymentIDContainsFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContainsFold(FieldPaymentID, v))
}

// InvoiceIDEQ applies the EQ predicate on the "invoice_id" field.
func InvoiceIDEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldInvoiceID, v))
}

// InvoiceIDNEQ applies the NEQ predicate on the "invoice_id" field.
func InvoiceIDNEQ(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldInvoiceID, v))
}

// InvoiceIDIn applies the In predicate on the "invoice_id" field.
func InvoiceIDIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldInvoiceID, vs...))
}

// InvoiceIDNotIn applies the NotIn predicate on the "invoice_id" field.
func InvoiceIDNotIn(vs ...string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldInvoiceID, vs...))
}

// InvoiceIDGT applies the GT predicate on the "invoice_id" field.
func InvoiceIDGT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldInvoiceID, v))
}

// InvoiceIDGTE applies the GTE predicate on the "invoice_id" field.
func InvoiceIDGTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldInvoiceID, v))
}

// InvoiceIDLT applies the LT predicate on the "invoice_id" field.
func InvoiceIDLT(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldInvoiceID, v))
}

// InvoiceIDLTE applies the LTE predicate on the "invoice_id" field.
func InvoiceIDLTE(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldInvoiceID, v))
}

// InvoiceIDContains applies the Contains predicate on the "invoice_id" field.
func InvoiceIDContains(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContains(FieldInvoiceID, v))
}

// InvoiceIDHasPrefix applies the HasPrefix predicate on the "invoice_id" field.
func InvoiceIDHasPrefix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasPrefix(FieldInvoiceID, v))
}

// InvoiceIDHasSuffix applies the HasSuffix predicate on the "invoice_id" field.
func InvoiceIDHasSuffix(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldHasSuffix(FieldInvoiceID, v))
}

// InvoiceIDIsNil applies the IsNil predicate on the "invoice_id" field.
func InvoiceIDIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldInvoiceID))
}

// InvoiceIDNotNil applies the NotNil predicate on the "invoice_id" field.
func InvoiceIDNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldInvoiceID))
}

// InvoiceIDEqualFold applies the EqualFold predicate on the "invoice_id" field.
func InvoiceIDEqualFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEqualFold(FieldInvoiceID, v))
}

// InvoiceIDContainsFold applies the ContainsFold predicate on the "invoice_id" field.
func InvoiceIDContainsFold(v string) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldContainsFold(FieldInvoiceID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldStatus, vs...))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldMetadata))
}

// ProcessedAtEQ applies the EQ predicate on the "processed_at" field.
func ProcessedAtEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldProcessedAt, v))
}

// ProcessedAtNEQ applies the NEQ predicate on the "processed_at" field.
func ProcessedAtNEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldProcessedAt, v))
}

// ProcessedAtIn applies the In predicate on the "processed_at" field.
func ProcessedAtIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldProcessedAt, vs...))
}

// ProcessedAtNotIn applies the NotIn predicate on the "processed_at" field.
func ProcessedAtNotIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldProcessedAt, vs...))
}

// ProcessedAtGT applies the GT predicate on the "processed_at" field.
func ProcessedAtGT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldProcessedAt, v))
}

// ProcessedAtGTE applies the GTE predicate on the "processed_at" field.
func ProcessedAtGTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldProcessedAt, v))
}

// ProcessedAtLT applies the LT predicate on the "processed_at" field.
func ProcessedAtLT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldProcessedAt, v))
}

// ProcessedAtLTE applies the LTE predicate on the "processed_at" field.
func ProcessedAtLTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldProcessedAt, v))
}

// ProcessedAtIsNil applies the IsNil predicate on the "processed_at" field.
func ProcessedAtIsNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIsNull(FieldProcessedAt))
}

// ProcessedAtNotNil applies the NotNil predicate on the "processed_at" field.
func ProcessedAtNotNil() predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotNull(FieldProcessedAt))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CreditTransaction) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CreditTransaction) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CreditTransaction) predicate.CreditTransaction {
	return predicate.CreditTransaction(sql.NotPredicates(p))
}

# Notification Service Development Configuration

server:
  grpc_port: 50056
  http_port: 8086
  env: development

database:
  type: sqlite
  sqlite_path: ./data/notification_service.db

email:
  smtp_host: localhost
  smtp_port: 1025  # MailHog for development
  username: ""
  password: ""
  from_email: <EMAIL>
  from_name: Social Content AI
  use_tls: false
  rate_limit: 100
  retry_attempts: 3

sms:
  provider: mock
  api_key: ""
  api_secret: ""
  from_number: "+**********"
  rate_limit: 50
  retry_attempts: 3

push:
  provider: mock
  api_key: ""
  rate_limit: 200
  retry_attempts: 3

websocket:
  enabled: true
  max_connections: 100
  ping_interval: 30s
  write_timeout: 10s
  read_timeout: 60s

kafka:
  brokers:
    - localhost:9092
  group_id: notification-service-dev
  topics:
    notification_events: notification.events
    user_events: user.events
    content_events: content.events
    billing_events: billing.events
    data_events: data.events
    platform_events: platform.events
    asset_events: asset.events
    dead_letter_queue: notification.dlq

templates:
  default_language: en
  supported_languages:
    - en
    - vi
  templates_path: ./templates
  cache_enabled: true
  cache_ttl: 1h

rate_limit:
  enabled: false  # Disabled for development
  requests_per_minute: 1000
  burst: 100

features:
  email_notifications: true
  sms_notifications: false
  push_notifications: false
  websocket_notifications: true
  event_driven_notifications: true   # Enabled for testing Kafka event processing
  template_system: true
  user_preferences: true
  analytics: true
  admin_panel: true

monitoring:
  enabled: true
  metrics_port: 9096
  health_check_path: /health
  metrics_path: /metrics

logging:
  level: debug
  format: text
  output: stdout

jwt:
  secret_key: dev-secret-key-notification-service
  token_duration: 24h
  issuer: social-content-ai-notification-dev

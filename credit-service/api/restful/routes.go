package restful

import (
	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/credit-service/api/restful/handlers"
	"github.com/social-content-ai/credit-service/api/restful/middleware"
	"github.com/social-content-ai/credit-service/pkg/payment"
	"github.com/social-content-ai/credit-service/usecase/credit"
	"github.com/social-content-ai/credit-service/usecase/plan"
	"github.com/social-content-ai/credit-service/usecase/transaction"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
	"github.com/social-content-ai/pkg-shared/logging"
)

// SetupRoutes configures all API routes for the credit service
func SetupRoutes(
	router *gin.Engine,
	creditUseCase credit.UseCase,
	planUseCase plan.UseCase,
	transactionUseCase transaction.UseCase,
	paymentProcessor payment.Processor,
	userClient userv1.AuthServiceClient,
	logger logging.Logger,
) {
	// Initialize handlers
	creditHandler := handlers.NewCreditHandler(creditUseCase, logger)
	planHandler := handlers.NewPlanHandler(planUseCase, logger)
	transactionHandler := handlers.NewTransactionHandler(transactionUseCase, logger)
	billingHandler := handlers.NewBillingHandler(creditUseCase, planUseCase, paymentProcessor, logger)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(userClient, logger)

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "credit-service",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Credit management routes
		credits := v1.Group("/credits")
		credits.Use(authMiddleware.RequireAuth())
		{
			// Basic credit operations
			credits.GET("/balance", creditHandler.GetCreditBalance)
			credits.GET("/history", creditHandler.GetCreditHistory)
			credits.POST("/validate", creditHandler.ValidateCredits)
			credits.POST("/reserve", creditHandler.ReserveCredits)
			credits.POST("/consume", creditHandler.ConsumeCredits)
			credits.POST("/refund", creditHandler.RefundCredits)
			
			// Credit management
			credits.POST("/transfer", creditHandler.TransferCredits)
			credits.POST("/adjust", creditHandler.AdjustCredits)
			credits.GET("/stats", creditHandler.GetCreditStats)
			credits.GET("/usage-report", creditHandler.GetUsageReport)
			
			// Monthly limits
			credits.GET("/monthly-usage", creditHandler.GetMonthlyUsage)
			credits.POST("/reset-monthly", creditHandler.ResetMonthlyUsage)
		}

		// Credit configuration routes
		config := v1.Group("/credits/config")
		config.Use(authMiddleware.RequireAuth())
		{
			config.GET("", creditHandler.GetCreditConfig)
			config.GET("/models", creditHandler.GetModelCreditConfig)
		}

		// Billing and purchase routes
		billing := v1.Group("/credits")
		billing.Use(authMiddleware.RequireAuth())
		{
			billing.POST("/purchase", billingHandler.PurchaseCredits)
			billing.GET("/packages", billingHandler.GetCreditPackages)
			billing.GET("/orders", billingHandler.GetOrders)
			billing.GET("/orders/:id", billingHandler.GetOrder)
			billing.POST("/orders/:id/cancel", billingHandler.CancelOrder)
		}

		// Subscription plan routes
		plans := v1.Group("/plans")
		plans.Use(authMiddleware.RequireAuth())
		{
			plans.GET("", planHandler.ListPlans)
			plans.GET("/:id", planHandler.GetPlan)
			plans.POST("/subscribe", planHandler.Subscribe)
			plans.POST("/upgrade", planHandler.UpgradePlan)
			plans.POST("/downgrade", planHandler.DowngradePlan)
			plans.POST("/cancel", planHandler.CancelSubscription)
			plans.GET("/current", planHandler.GetCurrentSubscription)
			plans.GET("/history", planHandler.GetSubscriptionHistory)
		}

		// Transaction routes
		transactions := v1.Group("/transactions")
		transactions.Use(authMiddleware.RequireAuth())
		{
			transactions.GET("", transactionHandler.ListTransactions)
			transactions.GET("/:id", transactionHandler.GetTransaction)
			transactions.GET("/export", transactionHandler.ExportTransactions)
			transactions.GET("/summary", transactionHandler.GetTransactionSummary)
		}

		// Analytics routes
		analytics := v1.Group("/analytics")
		analytics.Use(authMiddleware.RequireAuth())
		{
			analytics.GET("/credits", creditHandler.GetCreditAnalytics)
			analytics.GET("/usage", creditHandler.GetUsageAnalytics)
			analytics.GET("/revenue", billingHandler.GetRevenueAnalytics)
			analytics.GET("/plans", planHandler.GetPlanAnalytics)
		}
	}

	// Admin routes
	admin := router.Group("/admin/v1")
	admin.Use(authMiddleware.RequireAuth())
	admin.Use(authMiddleware.RequireRole("admin"))
	{
		// Admin credit management
		admin.GET("/credits/users", creditHandler.AdminListUserCredits)
		admin.GET("/credits/users/:user_id", creditHandler.AdminGetUserCredit)
		admin.POST("/credits/users/:user_id/add", creditHandler.AdminAddCredits)
		admin.POST("/credits/users/:user_id/deduct", creditHandler.AdminDeductCredits)
		admin.POST("/credits/users/:user_id/adjust", creditHandler.AdminAdjustCredits)
		
		// Admin plan management
		admin.GET("/plans", planHandler.AdminListPlans)
		admin.POST("/plans", planHandler.AdminCreatePlan)
		admin.PUT("/plans/:id", planHandler.AdminUpdatePlan)
		admin.DELETE("/plans/:id", planHandler.AdminDeletePlan)
		admin.POST("/plans/:id/activate", planHandler.AdminActivatePlan)
		admin.POST("/plans/:id/deactivate", planHandler.AdminDeactivatePlan)
		
		// Admin transaction management
		admin.GET("/transactions", transactionHandler.AdminListTransactions)
		admin.GET("/transactions/:id", transactionHandler.AdminGetTransaction)
		admin.POST("/transactions/:id/approve", transactionHandler.AdminApproveTransaction)
		admin.POST("/transactions/:id/reject", transactionHandler.AdminRejectTransaction)
		
		// Admin billing management
		admin.GET("/billing/orders", billingHandler.AdminListOrders)
		admin.GET("/billing/orders/:id", billingHandler.AdminGetOrder)
		admin.POST("/billing/orders/:id/refund", billingHandler.AdminRefundOrder)
		admin.GET("/billing/revenue", billingHandler.AdminGetRevenue)
		
		// Admin analytics
		admin.GET("/analytics/overview", creditHandler.GetAdminAnalytics)
		admin.GET("/analytics/credits", creditHandler.GetAdminCreditAnalytics)
		admin.GET("/analytics/revenue", billingHandler.GetAdminRevenueAnalytics)
		admin.GET("/analytics/plans", planHandler.GetAdminPlanAnalytics)
		admin.GET("/analytics/users", creditHandler.GetAdminUserAnalytics)
		
		// Admin configuration
		admin.GET("/config/credit-models", creditHandler.AdminGetCreditConfig)
		admin.PUT("/config/credit-models", creditHandler.AdminUpdateCreditConfig)
		admin.GET("/config/plans", planHandler.AdminGetPlanConfig)
		admin.PUT("/config/plans", planHandler.AdminUpdatePlanConfig)
	}

	// Webhook routes (no authentication required)
	webhooks := router.Group("/webhooks")
	{
		webhooks.POST("/stripe", billingHandler.StripeWebhook)
		webhooks.POST("/momo", billingHandler.MomoWebhook)
		webhooks.POST("/zalopay", billingHandler.ZaloPayWebhook)
		webhooks.POST("/bank-transfer", billingHandler.BankTransferWebhook)
		webhooks.POST("/payment-success", billingHandler.PaymentSuccessWebhook)
		webhooks.POST("/payment-failed", billingHandler.PaymentFailedWebhook)
	}

	// Public routes (no authentication required)
	public := router.Group("/public")
	{
		public.GET("/plans", planHandler.GetPublicPlans)
		public.GET("/plans/:id", planHandler.GetPublicPlan)
		public.GET("/pricing", planHandler.GetPricing)
		public.GET("/credit-packages", billingHandler.GetPublicCreditPackages)
	}

	// Internal routes (for service-to-service communication)
	internal := router.Group("/internal/v1")
	{
		// Credit validation for other services
		internal.POST("/credits/validate", creditHandler.InternalValidateCredits)
		internal.POST("/credits/reserve", creditHandler.InternalReserveCredits)
		internal.POST("/credits/consume", creditHandler.InternalConsumeCredits)
		internal.POST("/credits/refund", creditHandler.InternalRefundCredits)
		internal.GET("/credits/balance/:user_id", creditHandler.InternalGetCreditBalance)
		
		// Plan information for other services
		internal.GET("/plans/user/:user_id", planHandler.InternalGetUserPlan)
		internal.GET("/plans/:id", planHandler.InternalGetPlan)
		
		// Transaction information for other services
		internal.GET("/transactions/user/:user_id", transactionHandler.InternalGetUserTransactions)
		internal.POST("/transactions", transactionHandler.InternalCreateTransaction)
	}
}

package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/social-content-ai/pkg-shared/logging"
)

// EventHandler defines the interface for handling events
type EventHandler interface {
	HandleUserEvent(ctx context.Context, event *UserEvent) error
	HandleContentEvent(ctx context.Context, event *ContentEvent) error
	HandleBillingEvent(ctx context.Context, event *BillingEvent) error
	HandleIntegrationEvent(ctx context.Context, event *IntegrationEvent) error
	HandleAssetEvent(ctx context.Context, event *AssetEvent) error
	HandleRAGEvent(ctx context.Context, event *RAGEvent) error
	HandleSystemEvent(ctx context.Context, event *SystemEvent) error
}

// Consumer represents a Kafka consumer
type Consumer struct {
	config    *Config
	logger    logging.Logger
	handler   EventHandler
	validator *MessageValidator
	readers   map[string]*kafka.Reader
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewConsumer creates a new Kafka consumer
func NewConsumer(config *Config, handler EventHandler, logger logging.Logger) *Consumer {
	ctx, cancel := context.WithCancel(context.Background())

	return &Consumer{
		config:    config,
		logger:    logger,
		handler:   handler,
		validator: NewMessageValidator(),
		readers:   make(map[string]*kafka.Reader),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Start starts the Kafka consumer for specified topics
func (c *Consumer) Start(topics []string) error {
	c.logger.Info("Starting Kafka consumer")

	// Create readers for each topic
	for _, topic := range topics {
		reader := kafka.NewReader(c.getReaderConfig(topic))
		c.readers[topic] = reader

		// Start consuming from each topic in a separate goroutine
		go c.consumeTopic(topic, reader)
	}

	c.logger.WithField("topics", topics).Info("Kafka consumer started for all topics")
	return nil
}

// getReaderConfig returns kafka-go reader config for a topic
func (c *Consumer) getReaderConfig(topic string) kafka.ReaderConfig {
	config := kafka.ReaderConfig{
		Brokers:           c.config.Brokers,
		Topic:             topic,
		GroupID:           c.config.Consumer.GroupID,
		MinBytes:          c.config.Consumer.MinBytes,
		MaxBytes:          c.config.Consumer.MaxBytes,
		MaxWait:           c.config.Consumer.MaxWait,
		CommitInterval:    c.config.Consumer.CommitInterval,
		StartOffset:       c.config.Consumer.StartOffset,
		HeartbeatInterval: c.config.Consumer.HeartbeatInterval,
		SessionTimeout:    c.config.Consumer.SessionTimeout,
		RebalanceTimeout:  c.config.Consumer.RebalanceTimeout,
		RetentionTime:     c.config.Consumer.RetentionTime,
	}

	// Add security configuration if enabled
	if c.config.Security.Enabled {
		// TODO: Add SASL/TLS configuration
	}

	return config
}

// Stop stops the Kafka consumer
func (c *Consumer) Stop() error {
	c.logger.Info("Stopping Kafka consumer")

	// Cancel context to stop all consumers
	c.cancel()

	// Close all readers
	for topic, reader := range c.readers {
		if err := reader.Close(); err != nil {
			c.logger.WithError(err).WithField("topic", topic).Error("Failed to close reader")
		}
	}

	c.logger.Info("Kafka consumer stopped")
	return nil
}

// consumeTopic consumes messages from a specific topic
func (c *Consumer) consumeTopic(topic string, reader *kafka.Reader) {
	c.logger.WithField("topic", topic).Info("Starting topic consumer")

	for {
		select {
		case <-c.ctx.Done():
			c.logger.WithField("topic", topic).Info("Topic consumer stopped")
			return
		default:
			// Read message with timeout
			ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
			msg, err := reader.FetchMessage(ctx)
			cancel()

			if err != nil {
				if err == context.DeadlineExceeded {
					continue // Timeout is expected, continue polling
				}
				c.logger.WithError(err).WithField("topic", topic).Error("Failed to fetch message")
				time.Sleep(1 * time.Second) // Brief pause before retrying
				continue
			}

			// Process message
			if err := c.processMessage(topic, msg); err != nil {
				c.logger.WithError(err).WithFields(map[string]interface{}{
					"topic":     topic,
					"partition": msg.Partition,
					"offset":    msg.Offset,
				}).Error("Failed to process message")

				// Send to dead letter queue
				c.sendToDeadLetterQueue(msg, err)
			}

			// Commit message
			if err := reader.CommitMessages(c.ctx, msg); err != nil {
				c.logger.WithError(err).WithField("topic", topic).Error("Failed to commit message")
			}
		}
	}
}

// processMessage processes a Kafka message based on its topic
func (c *Consumer) processMessage(topic string, msg kafka.Message) error {
	c.logger.WithFields(map[string]interface{}{
		"topic":     topic,
		"partition": msg.Partition,
		"offset":    msg.Offset,
		"key":       string(msg.Key),
	}).Debug("Processing message")

	// Parse event message
	var eventMsg EventMessage
	if err := json.Unmarshal(msg.Value, &eventMsg); err != nil {
		return fmt.Errorf("failed to unmarshal event message: %w", err)
	}

	// Validate message
	if err := c.validator.ValidateEventMessage(&eventMsg); err != nil {
		return fmt.Errorf("event message validation failed: %w", err)
	}

	// Route to appropriate handler based on topic
	ctx := context.WithValue(c.ctx, "kafka_message", msg)
	return c.routeEvent(ctx, topic, &eventMsg)
}

// routeEvent routes an event to the appropriate handler
func (c *Consumer) routeEvent(ctx context.Context, topic string, eventMsg *EventMessage) error {
	switch topic {
	case c.config.Topics.UserEvents:
		return c.handleUserEvent(ctx, eventMsg)
	case c.config.Topics.ContentEvents:
		return c.handleContentEvent(ctx, eventMsg)
	case c.config.Topics.BillingEvents:
		return c.handleBillingEvent(ctx, eventMsg)
	case c.config.Topics.IntegrationEvents:
		return c.handleIntegrationEvent(ctx, eventMsg)
	case c.config.Topics.AssetEvents:
		return c.handleAssetEvent(ctx, eventMsg)
	case c.config.Topics.RAGEvents:
		return c.handleRAGEvent(ctx, eventMsg)
	case c.config.Topics.SystemEvents:
		return c.handleSystemEvent(ctx, eventMsg)
	default:
		return fmt.Errorf("unknown topic: %s", topic)
	}
}

// handleUserEvent handles user events
func (c *Consumer) handleUserEvent(ctx context.Context, eventMsg *EventMessage) error {
	userEvent := &UserEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract user-specific fields from event data
	if email, ok := eventMsg.EventData["email"].(string); ok {
		userEvent.Email = email
	}
	if firstName, ok := eventMsg.EventData["first_name"].(string); ok {
		userEvent.FirstName = firstName
	}
	if lastName, ok := eventMsg.EventData["last_name"].(string); ok {
		userEvent.LastName = lastName
	}
	if role, ok := eventMsg.EventData["role"].(string); ok {
		userEvent.Role = role
	}

	return c.handler.HandleUserEvent(ctx, userEvent)
}

// handleContentEvent handles content events
func (c *Consumer) handleContentEvent(ctx context.Context, eventMsg *EventMessage) error {
	contentEvent := &ContentEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract content-specific fields from event data
	if contentID, ok := eventMsg.EventData["content_id"].(string); ok {
		contentEvent.ContentID = contentID
	}
	if contentType, ok := eventMsg.EventData["content_type"].(string); ok {
		contentEvent.ContentType = contentType
	}
	if contentTitle, ok := eventMsg.EventData["content_title"].(string); ok {
		contentEvent.ContentTitle = contentTitle
	}
	if platform, ok := eventMsg.EventData["platform"].(string); ok {
		contentEvent.Platform = platform
	}
	if postID, ok := eventMsg.EventData["post_id"].(string); ok {
		contentEvent.PostID = postID
	}
	if templateID, ok := eventMsg.EventData["template_id"].(string); ok {
		contentEvent.TemplateID = templateID
	}

	return c.handler.HandleContentEvent(ctx, contentEvent)
}

// handleBillingEvent handles billing events
func (c *Consumer) handleBillingEvent(ctx context.Context, eventMsg *EventMessage) error {
	billingEvent := &BillingEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract billing-specific fields from event data
	if subscriptionID, ok := eventMsg.EventData["subscription_id"].(string); ok {
		billingEvent.SubscriptionID = subscriptionID
	}
	if paymentID, ok := eventMsg.EventData["payment_id"].(string); ok {
		billingEvent.PaymentID = paymentID
	}
	if amount, ok := eventMsg.EventData["amount"].(float64); ok {
		billingEvent.Amount = amount
	}
	if currency, ok := eventMsg.EventData["currency"].(string); ok {
		billingEvent.Currency = currency
	}
	if credits, ok := eventMsg.EventData["credits"].(float64); ok {
		billingEvent.Credits = int(credits)
	}
	if planName, ok := eventMsg.EventData["plan_name"].(string); ok {
		billingEvent.PlanName = planName
	}

	return c.handler.HandleBillingEvent(ctx, billingEvent)
}

// handleIntegrationEvent handles integration events
func (c *Consumer) handleIntegrationEvent(ctx context.Context, eventMsg *EventMessage) error {
	integrationEvent := &IntegrationEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract integration-specific fields from event data
	if integrationID, ok := eventMsg.EventData["integration_id"].(string); ok {
		integrationEvent.IntegrationID = integrationID
	}
	if integrationType, ok := eventMsg.EventData["integration_type"].(string); ok {
		integrationEvent.IntegrationType = integrationType
	}
	if platformName, ok := eventMsg.EventData["platform_name"].(string); ok {
		integrationEvent.PlatformName = platformName
	}
	if accountName, ok := eventMsg.EventData["account_name"].(string); ok {
		integrationEvent.AccountName = accountName
	}
	if errorMsg, ok := eventMsg.EventData["error"].(string); ok {
		integrationEvent.Error = errorMsg
	}

	return c.handler.HandleIntegrationEvent(ctx, integrationEvent)
}

// handleAssetEvent handles asset events
func (c *Consumer) handleAssetEvent(ctx context.Context, eventMsg *EventMessage) error {
	assetEvent := &AssetEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract asset-specific fields from event data
	if assetID, ok := eventMsg.EventData["asset_id"].(string); ok {
		assetEvent.AssetID = assetID
	}
	if assetType, ok := eventMsg.EventData["asset_type"].(string); ok {
		assetEvent.AssetType = assetType
	}
	if assetURL, ok := eventMsg.EventData["asset_url"].(string); ok {
		assetEvent.AssetURL = assetURL
	}
	if fileName, ok := eventMsg.EventData["file_name"].(string); ok {
		assetEvent.FileName = fileName
	}
	if fileSize, ok := eventMsg.EventData["file_size"].(float64); ok {
		assetEvent.FileSize = int64(fileSize)
	}
	if contentType, ok := eventMsg.EventData["content_type"].(string); ok {
		assetEvent.ContentType = contentType
	}
	if validationStatus, ok := eventMsg.EventData["validation_status"].(string); ok {
		assetEvent.ValidationStatus = validationStatus
	}

	return c.handler.HandleAssetEvent(ctx, assetEvent)
}

// handleRAGEvent handles RAG events
func (c *Consumer) handleRAGEvent(ctx context.Context, eventMsg *EventMessage) error {
	ragEvent := &RAGEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract RAG-specific fields from event data
	if processingID, ok := eventMsg.EventData["processing_id"].(string); ok {
		ragEvent.ProcessingID = processingID
	}
	if documentID, ok := eventMsg.EventData["document_id"].(string); ok {
		ragEvent.DocumentID = documentID
	}
	if templateID, ok := eventMsg.EventData["template_id"].(string); ok {
		ragEvent.TemplateID = templateID
	}
	if status, ok := eventMsg.EventData["status"].(string); ok {
		ragEvent.Status = status
	}
	if progress, ok := eventMsg.EventData["progress"].(float64); ok {
		ragEvent.Progress = int(progress)
	}
	if errorMsg, ok := eventMsg.EventData["error"].(string); ok {
		ragEvent.Error = errorMsg
	}

	return c.handler.HandleRAGEvent(ctx, ragEvent)
}

// handleSystemEvent handles system events
func (c *Consumer) handleSystemEvent(ctx context.Context, eventMsg *EventMessage) error {
	systemEvent := &SystemEvent{
		BaseEvent: &BaseEvent{
			ID:          eventMsg.MessageID,
			Type:        eventMsg.EventType,
			Source:      eventMsg.Source,
			Version:     eventMsg.Version,
			Timestamp:   eventMsg.Timestamp,
			UserID:      eventMsg.UserID,
			WorkspaceID: eventMsg.WorkspaceID,
			Priority:    eventMsg.Priority,
			Data:        eventMsg.EventData,
			Metadata:    eventMsg.Metadata,
		},
	}

	// Extract system-specific fields from event data
	if component, ok := eventMsg.EventData["component"].(string); ok {
		systemEvent.Component = component
	}
	if maintenanceID, ok := eventMsg.EventData["maintenance_id"].(string); ok {
		systemEvent.MaintenanceID = maintenanceID
	}
	if title, ok := eventMsg.EventData["title"].(string); ok {
		systemEvent.Title = title
	}
	if description, ok := eventMsg.EventData["description"].(string); ok {
		systemEvent.Description = description
	}
	if startTimeStr, ok := eventMsg.EventData["start_time"].(string); ok {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			systemEvent.StartTime = startTime
		}
	}
	if endTimeStr, ok := eventMsg.EventData["end_time"].(string); ok {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			systemEvent.EndTime = endTime
		}
	}
	if affectedServices, ok := eventMsg.EventData["affected_services"].([]interface{}); ok {
		for _, service := range affectedServices {
			if serviceStr, ok := service.(string); ok {
				systemEvent.AffectedServices = append(systemEvent.AffectedServices, serviceStr)
			}
		}
	}

	return c.handler.HandleSystemEvent(ctx, systemEvent)
}

// sendToDeadLetterQueue sends failed messages to dead letter queue
func (c *Consumer) sendToDeadLetterQueue(msg kafka.Message, processingError error) {
	// Create DLQ producer
	producer := NewProducer(c.config, c.logger)
	defer producer.Close()

	// Create DLQ event
	dlqEvent := NewBaseEvent(EventTypeSystemAlert, "kafka-consumer")
	dlqEvent.WithPriority(PriorityCritical)
	dlqEvent.WithData("original_topic", msg.Topic)
	dlqEvent.WithData("original_partition", msg.Partition)
	dlqEvent.WithData("original_offset", msg.Offset)
	dlqEvent.WithData("original_key", string(msg.Key))
	dlqEvent.WithData("original_value", string(msg.Value))
	dlqEvent.WithData("error", processingError.Error())
	dlqEvent.WithData("failed_at", time.Now().Format(time.RFC3339))

	// Add original headers
	for _, header := range msg.Headers {
		dlqEvent.WithData("original_header_"+header.Key, string(header.Value))
	}

	// Send to DLQ
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := producer.PublishToTopic(ctx, c.config.Topics.DeadLetterQueue, dlqEvent); err != nil {
		c.logger.WithError(err).Error("Failed to send message to dead letter queue")
	} else {
		c.logger.WithFields(map[string]interface{}{
			"original_topic":     msg.Topic,
			"original_partition": msg.Partition,
			"original_offset":    msg.Offset,
			"error":              processingError.Error(),
		}).Warn("Message sent to dead letter queue")
	}
}

// GetConsumerStats returns consumer statistics
func (c *Consumer) GetConsumerStats() map[string]interface{} {
	stats := make(map[string]interface{})

	for topic, reader := range c.readers {
		readerStats := reader.Stats()
		stats[topic] = map[string]interface{}{
			"messages":   readerStats.Messages,
			"bytes":      readerStats.Bytes,
			"rebalances": readerStats.Rebalances,
			"timeouts":   readerStats.Timeouts,
			"errors":     readerStats.Errors,
			"lag":        readerStats.Lag,
		}
	}

	return stats
}

// HealthCheck checks if the consumer is healthy
func (c *Consumer) HealthCheck() error {
	if c.ctx.Err() != nil {
		return fmt.Errorf("consumer context is cancelled")
	}

	// Check if all readers are still active
	for topic, reader := range c.readers {
		stats := reader.Stats()
		if stats.Errors > 0 {
			c.logger.WithFields(map[string]interface{}{
				"topic":  topic,
				"errors": stats.Errors,
			}).Warn("Consumer has errors")
		}
	}

	return nil
}

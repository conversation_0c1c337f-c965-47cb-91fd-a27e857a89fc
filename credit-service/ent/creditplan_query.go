// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/creditplan"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// CreditPlanQuery is the builder for querying CreditPlan entities.
type CreditPlanQuery struct {
	config
	ctx        *QueryContext
	order      []creditplan.OrderOption
	inters     []Interceptor
	predicates []predicate.CreditPlan
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CreditPlanQuery builder.
func (cpq *CreditPlanQuery) Where(ps ...predicate.CreditPlan) *CreditPlanQuery {
	cpq.predicates = append(cpq.predicates, ps...)
	return cpq
}

// Limit the number of records to be returned by this query.
func (cpq *CreditPlanQuery) Limit(limit int) *CreditPlanQuery {
	cpq.ctx.Limit = &limit
	return cpq
}

// Offset to start from.
func (cpq *CreditPlanQuery) Offset(offset int) *CreditPlanQuery {
	cpq.ctx.Offset = &offset
	return cpq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cpq *CreditPlanQuery) Unique(unique bool) *CreditPlanQuery {
	cpq.ctx.Unique = &unique
	return cpq
}

// Order specifies how the records should be ordered.
func (cpq *CreditPlanQuery) Order(o ...creditplan.OrderOption) *CreditPlanQuery {
	cpq.order = append(cpq.order, o...)
	return cpq
}

// First returns the first CreditPlan entity from the query.
// Returns a *NotFoundError when no CreditPlan was found.
func (cpq *CreditPlanQuery) First(ctx context.Context) (*CreditPlan, error) {
	nodes, err := cpq.Limit(1).All(setContextOp(ctx, cpq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{creditplan.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cpq *CreditPlanQuery) FirstX(ctx context.Context) *CreditPlan {
	node, err := cpq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CreditPlan ID from the query.
// Returns a *NotFoundError when no CreditPlan ID was found.
func (cpq *CreditPlanQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = cpq.Limit(1).IDs(setContextOp(ctx, cpq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{creditplan.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cpq *CreditPlanQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := cpq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CreditPlan entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CreditPlan entity is found.
// Returns a *NotFoundError when no CreditPlan entities are found.
func (cpq *CreditPlanQuery) Only(ctx context.Context) (*CreditPlan, error) {
	nodes, err := cpq.Limit(2).All(setContextOp(ctx, cpq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{creditplan.Label}
	default:
		return nil, &NotSingularError{creditplan.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cpq *CreditPlanQuery) OnlyX(ctx context.Context) *CreditPlan {
	node, err := cpq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CreditPlan ID in the query.
// Returns a *NotSingularError when more than one CreditPlan ID is found.
// Returns a *NotFoundError when no entities are found.
func (cpq *CreditPlanQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = cpq.Limit(2).IDs(setContextOp(ctx, cpq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{creditplan.Label}
	default:
		err = &NotSingularError{creditplan.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cpq *CreditPlanQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := cpq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CreditPlans.
func (cpq *CreditPlanQuery) All(ctx context.Context) ([]*CreditPlan, error) {
	ctx = setContextOp(ctx, cpq.ctx, ent.OpQueryAll)
	if err := cpq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CreditPlan, *CreditPlanQuery]()
	return withInterceptors[[]*CreditPlan](ctx, cpq, qr, cpq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cpq *CreditPlanQuery) AllX(ctx context.Context) []*CreditPlan {
	nodes, err := cpq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CreditPlan IDs.
func (cpq *CreditPlanQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if cpq.ctx.Unique == nil && cpq.path != nil {
		cpq.Unique(true)
	}
	ctx = setContextOp(ctx, cpq.ctx, ent.OpQueryIDs)
	if err = cpq.Select(creditplan.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cpq *CreditPlanQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := cpq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cpq *CreditPlanQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cpq.ctx, ent.OpQueryCount)
	if err := cpq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cpq, querierCount[*CreditPlanQuery](), cpq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cpq *CreditPlanQuery) CountX(ctx context.Context) int {
	count, err := cpq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cpq *CreditPlanQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cpq.ctx, ent.OpQueryExist)
	switch _, err := cpq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cpq *CreditPlanQuery) ExistX(ctx context.Context) bool {
	exist, err := cpq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CreditPlanQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cpq *CreditPlanQuery) Clone() *CreditPlanQuery {
	if cpq == nil {
		return nil
	}
	return &CreditPlanQuery{
		config:     cpq.config,
		ctx:        cpq.ctx.Clone(),
		order:      append([]creditplan.OrderOption{}, cpq.order...),
		inters:     append([]Interceptor{}, cpq.inters...),
		predicates: append([]predicate.CreditPlan{}, cpq.predicates...),
		// clone intermediate query.
		sql:  cpq.sql.Clone(),
		path: cpq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CreditPlan.Query().
//		GroupBy(creditplan.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cpq *CreditPlanQuery) GroupBy(field string, fields ...string) *CreditPlanGroupBy {
	cpq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CreditPlanGroupBy{build: cpq}
	grbuild.flds = &cpq.ctx.Fields
	grbuild.label = creditplan.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.CreditPlan.Query().
//		Select(creditplan.FieldName).
//		Scan(ctx, &v)
func (cpq *CreditPlanQuery) Select(fields ...string) *CreditPlanSelect {
	cpq.ctx.Fields = append(cpq.ctx.Fields, fields...)
	sbuild := &CreditPlanSelect{CreditPlanQuery: cpq}
	sbuild.label = creditplan.Label
	sbuild.flds, sbuild.scan = &cpq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CreditPlanSelect configured with the given aggregations.
func (cpq *CreditPlanQuery) Aggregate(fns ...AggregateFunc) *CreditPlanSelect {
	return cpq.Select().Aggregate(fns...)
}

func (cpq *CreditPlanQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cpq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cpq); err != nil {
				return err
			}
		}
	}
	for _, f := range cpq.ctx.Fields {
		if !creditplan.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cpq.path != nil {
		prev, err := cpq.path(ctx)
		if err != nil {
			return err
		}
		cpq.sql = prev
	}
	return nil
}

func (cpq *CreditPlanQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CreditPlan, error) {
	var (
		nodes = []*CreditPlan{}
		_spec = cpq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CreditPlan).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CreditPlan{config: cpq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cpq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (cpq *CreditPlanQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cpq.querySpec()
	_spec.Node.Columns = cpq.ctx.Fields
	if len(cpq.ctx.Fields) > 0 {
		_spec.Unique = cpq.ctx.Unique != nil && *cpq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cpq.driver, _spec)
}

func (cpq *CreditPlanQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(creditplan.Table, creditplan.Columns, sqlgraph.NewFieldSpec(creditplan.FieldID, field.TypeUUID))
	_spec.From = cpq.sql
	if unique := cpq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cpq.path != nil {
		_spec.Unique = true
	}
	if fields := cpq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, creditplan.FieldID)
		for i := range fields {
			if fields[i] != creditplan.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := cpq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cpq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cpq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cpq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cpq *CreditPlanQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cpq.driver.Dialect())
	t1 := builder.Table(creditplan.Table)
	columns := cpq.ctx.Fields
	if len(columns) == 0 {
		columns = creditplan.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cpq.sql != nil {
		selector = cpq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cpq.ctx.Unique != nil && *cpq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range cpq.predicates {
		p(selector)
	}
	for _, p := range cpq.order {
		p(selector)
	}
	if offset := cpq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cpq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CreditPlanGroupBy is the group-by builder for CreditPlan entities.
type CreditPlanGroupBy struct {
	selector
	build *CreditPlanQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cpgb *CreditPlanGroupBy) Aggregate(fns ...AggregateFunc) *CreditPlanGroupBy {
	cpgb.fns = append(cpgb.fns, fns...)
	return cpgb
}

// Scan applies the selector query and scans the result into the given value.
func (cpgb *CreditPlanGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cpgb.build.ctx, ent.OpQueryGroupBy)
	if err := cpgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditPlanQuery, *CreditPlanGroupBy](ctx, cpgb.build, cpgb, cpgb.build.inters, v)
}

func (cpgb *CreditPlanGroupBy) sqlScan(ctx context.Context, root *CreditPlanQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cpgb.fns))
	for _, fn := range cpgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cpgb.flds)+len(cpgb.fns))
		for _, f := range *cpgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cpgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cpgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CreditPlanSelect is the builder for selecting fields of CreditPlan entities.
type CreditPlanSelect struct {
	*CreditPlanQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cps *CreditPlanSelect) Aggregate(fns ...AggregateFunc) *CreditPlanSelect {
	cps.fns = append(cps.fns, fns...)
	return cps
}

// Scan applies the selector query and scans the result into the given value.
func (cps *CreditPlanSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cps.ctx, ent.OpQuerySelect)
	if err := cps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CreditPlanQuery, *CreditPlanSelect](ctx, cps.CreditPlanQuery, cps, cps.inters, v)
}

func (cps *CreditPlanSelect) sqlScan(ctx context.Context, root *CreditPlanQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cps.fns))
	for _, fn := range cps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

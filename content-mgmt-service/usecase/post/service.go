package post

import (
	"context"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent"
	"github.com/social-content-ai/content-mgmt-service/ent/post"
	"github.com/social-content-ai/content-mgmt-service/pkg/models"
	"github.com/social-content-ai/pkg-shared/logging"
)

// service implements the UseCase interface
type service struct {
	readDB  *ent.Client
	writeDB *ent.Client
	logger  logging.Logger
}

// NewService creates a new post service
func NewService(readDB, writeDB *ent.Client, logger logging.Logger) UseCase {
	return &service{
		readDB:  readDB,
		writeDB: writeDB,
		logger:  logger,
	}
}

// CreatePost creates a new post
func (s *service) CreatePost(ctx context.Context, req *models.CreatePostRequest) (*models.PostResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Creating post")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Parse workspace ID to UUID if provided
	var workspaceUUID *uuid.UUID
	if req.WorkspaceID != "" {
		parsed, err := uuid.Parse(req.WorkspaceID)
		if err != nil {
			s.logger.WithError(err).Error("Invalid workspace ID format")
			return nil, fmt.Errorf("invalid workspace ID format: %w", err)
		}
		workspaceUUID = &parsed
	}

	// Create post in database
	postCreate := s.writeDB.Post.
		Create().
		SetTitle(req.Title).
		SetContent(req.Content).
		SetUserID(userUUID).
		SetStatus("draft").
		SetPlatforms(req.Platforms)

	if workspaceUUID != nil {
		postCreate = postCreate.SetWorkspaceID(workspaceUUID.String())
	}

	if len(req.Hashtags) > 0 {
		postCreate = postCreate.SetHashtags(req.Hashtags)
	}

	if req.TemplateID != "" {
		postCreate = postCreate.SetTemplateID(req.TemplateID)
	}

	// Set media URLs if provided
	if len(req.MediaAssets) > 0 {
		mediaURLs := make([]string, len(req.MediaAssets))
		for i, asset := range req.MediaAssets {
			mediaURLs[i] = asset.URL
		}
		postCreate = postCreate.SetMediaUrls(mediaURLs)
	}

	post, err := postCreate.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create post")
		return nil, fmt.Errorf("failed to create post: %w", err)
	}

	// Convert to response model
	response := &models.PostResponse{
		BaseModel: models.BaseModel{
			ID:        post.ID.String(),
			CreatedAt: post.CreatedAt,
			UpdatedAt: post.UpdatedAt,
		},
		Title:     post.Title,
		Content:   post.Content,
		UserID:    post.UserID.String(),
		Status:    string(post.Status),
		Platforms: post.Platforms,
		Type:      req.Type,
	}

	// Set workspace ID if present
	if post.WorkspaceID != "" {
		response.WorkspaceID = post.WorkspaceID
	}

	// Set template ID if present
	if post.TemplateID != "" {
		response.TemplateID = post.TemplateID
	}

	// Set scheduled time if present
	if !post.ScheduledAt.IsZero() {
		response.ScheduledAt = &post.ScheduledAt
	}

	// Convert media URLs to MediaAssets
	if len(post.MediaUrls) > 0 {
		mediaAssets := make([]models.MediaAsset, len(post.MediaUrls))
		for i, url := range post.MediaUrls {
			mediaAssets[i] = models.MediaAsset{
				URL:  url,
				Type: "image", // Default type, should be determined from URL
			}
		}
		response.MediaAssets = mediaAssets
	}

	// Set hashtags if present
	if len(post.Hashtags) > 0 {
		response.Hashtags = post.Hashtags
	}

	s.logger.WithField("post_id", post.ID).Info("Post created successfully")
	return response, nil
}

// GetPost gets a post by ID
func (s *service) GetPost(ctx context.Context, postID, userID string) (*models.PostResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	}).Info("Getting post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(postID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get post from database
	post, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("post not found")
		}
		s.logger.WithError(err).Error("Failed to get post")
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Convert to response model
	response := s.convertPostToResponse(post)
	return response, nil
}

// UpdatePost updates a post
func (s *service) UpdatePost(ctx context.Context, req *models.UpdatePostRequest) (*models.PostResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id": req.ID,
		"user_id": req.UserID,
	}).Info("Updating post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(req.ID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build update query
	updateQuery := s.writeDB.Post.
		Update().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		SetUpdatedAt(time.Now())

	// Apply updates only for non-empty fields
	if req.Title != "" {
		updateQuery = updateQuery.SetTitle(req.Title)
	}

	if req.Content != "" {
		updateQuery = updateQuery.SetContent(req.Content)
	}

	if len(req.Platforms) > 0 {
		updateQuery = updateQuery.SetPlatforms(req.Platforms)
	}

	if len(req.Hashtags) > 0 {
		updateQuery = updateQuery.SetHashtags(req.Hashtags)
	}

	// Set media URLs if provided
	if len(req.MediaAssets) > 0 {
		mediaURLs := make([]string, len(req.MediaAssets))
		for i, asset := range req.MediaAssets {
			mediaURLs[i] = asset.URL
		}
		updateQuery = updateQuery.SetMediaUrls(mediaURLs)
	}

	// Execute update
	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update post")
		return nil, fmt.Errorf("failed to update post: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("post not found or no changes made")
	}

	// Get updated post
	updatedPost, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated post")
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	// Convert to response model
	response := s.convertPostToResponse(updatedPost)
	s.logger.WithField("post_id", req.ID).Info("Post updated successfully")
	return response, nil
}

// DeletePost deletes a post
func (s *service) DeletePost(ctx context.Context, postID, userID string) error {
	s.logger.WithFields(map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	}).Info("Deleting post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(postID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Soft delete the post by setting deleted_at
	affected, err := s.writeDB.Post.
		Update().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		SetDeletedAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to delete post")
		return fmt.Errorf("failed to delete post: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("post not found or already deleted")
	}

	s.logger.WithField("post_id", postID).Info("Post deleted successfully")
	return nil
}

// ListPosts lists posts with pagination
func (s *service) ListPosts(ctx context.Context, req *models.ListPostsRequest) (*models.ListPostsResponse, error) {
	s.logger.WithField("user_id", req.UserID).Info("Listing posts")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query
	query := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		)

	// Apply workspace filter
	if req.WorkspaceID != "" {
		query = query.Where(post.WorkspaceID(req.WorkspaceID))
	}

	// Apply status filter
	if req.Status != "" {
		query = query.Where(post.StatusEQ(post.Status(req.Status)))
	}

	// Apply platform filter - check if platform exists in JSON array
	if req.Platform != "" {
		// For JSON array fields, we need to use a custom predicate
		// This is a simplified approach - in production you might want to use SQL functions
		query = query.Where(func(s *sql.Selector) {
			s.Where(sql.Contains(s.C(post.FieldPlatforms), req.Platform))
		})
	}

	// Apply search filter
	if req.Search != "" {
		query = query.Where(
			post.Or(
				post.TitleContains(req.Search),
				post.ContentContains(req.Search),
			),
		)
	}

	// Apply date filters
	if req.DateFrom != "" {
		if dateFrom, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			query = query.Where(post.CreatedAtGTE(dateFrom))
		}
	}
	if req.DateTo != "" {
		if dateTo, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			query = query.Where(post.CreatedAtLTE(dateTo.Add(24 * time.Hour)))
		}
	}

	// Apply sorting
	switch req.SortBy {
	case "title":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(post.FieldTitle))
		} else {
			query = query.Order(ent.Asc(post.FieldTitle))
		}
	case "status":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(post.FieldStatus))
		} else {
			query = query.Order(ent.Asc(post.FieldStatus))
		}
	case "scheduled_at":
		if req.SortOrder == "desc" {
			query = query.Order(ent.Desc(post.FieldScheduledAt))
		} else {
			query = query.Order(ent.Asc(post.FieldScheduledAt))
		}
	default:
		query = query.Order(ent.Desc(post.FieldCreatedAt))
	}

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count posts")
		return nil, fmt.Errorf("failed to count posts: %w", err)
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	posts, err := query.
		Offset(offset).
		Limit(req.Limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list posts")
		return nil, fmt.Errorf("failed to list posts: %w", err)
	}

	// Convert to response models
	postResponses := make([]models.PostResponse, len(posts))
	for i, p := range posts {
		postResponses[i] = *s.convertPostToResponse(p)
	}

	// Calculate pagination
	totalPages := (total + req.Limit - 1) / req.Limit

	response := &models.ListPostsResponse{
		Posts: postResponses,
		Pagination: models.PaginationMeta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	return response, nil
}

// SchedulePost schedules a post for future publishing
func (s *service) SchedulePost(ctx context.Context, req *models.SchedulePostRequest) (*models.PostResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id":      req.PostID,
		"user_id":      req.UserID,
		"scheduled_at": req.ScheduledAt,
	}).Info("Scheduling post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(req.PostID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Validate scheduled time is in the future
	if req.ScheduledAt.Before(time.Now()) {
		return nil, fmt.Errorf("scheduled time must be in the future")
	}

	// Update post with schedule information
	updateQuery := s.writeDB.Post.
		Update().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		SetScheduledAt(req.ScheduledAt).
		SetStatus("scheduled").
		SetUpdatedAt(time.Now())

	// Update platforms if specified
	if len(req.Platforms) > 0 {
		updateQuery = updateQuery.SetPlatforms(req.Platforms)
	}

	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to schedule post")
		return nil, fmt.Errorf("failed to schedule post: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("post not found")
	}

	// Get updated post
	scheduledPost, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get scheduled post")
		return nil, fmt.Errorf("failed to get scheduled post: %w", err)
	}

	// Convert to response model
	response := s.convertPostToResponse(scheduledPost)
	s.logger.WithField("post_id", req.PostID).Info("Post scheduled successfully")
	return response, nil
}

// PublishPost publishes a post immediately
func (s *service) PublishPost(ctx context.Context, req *models.PublishPostRequest) (*models.PostResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id":   req.PostID,
		"user_id":   req.UserID,
		"immediate": req.Immediate,
	}).Info("Publishing post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(req.PostID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get the post first to validate it exists and can be published
	existingPost, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("post not found")
		}
		s.logger.WithError(err).Error("Failed to get post")
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Validate post can be published
	if existingPost.Status == "published" {
		return nil, fmt.Errorf("post is already published")
	}

	// Update post status to published
	updateQuery := s.writeDB.Post.
		Update().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		SetStatus("published").
		SetPublishedAt(time.Now()).
		SetUpdatedAt(time.Now())

	// Update platforms if specified
	if len(req.Platforms) > 0 {
		updateQuery = updateQuery.SetPlatforms(req.Platforms)
	}

	affected, err := updateQuery.Save(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to publish post")
		return nil, fmt.Errorf("failed to publish post: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("post not found or already published")
	}

	// Get updated post
	publishedPost, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get published post")
		return nil, fmt.Errorf("failed to get published post: %w", err)
	}

	// TODO: Here you would integrate with external publishing services
	// For now, we just mark it as published in the database

	// Convert to response model
	response := s.convertPostToResponse(publishedPost)
	s.logger.WithField("post_id", req.PostID).Info("Post published successfully")
	return response, nil
}

// CancelScheduledPost cancels a scheduled post
func (s *service) CancelScheduledPost(ctx context.Context, postID, userID string) (*models.PostResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	}).Info("Canceling scheduled post")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(postID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Update post status from scheduled to draft
	affected, err := s.writeDB.Post.
		Update().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.StatusEQ("scheduled"),
			post.DeletedAtIsNil(),
		).
		SetStatus("draft").
		ClearScheduledAt().
		SetUpdatedAt(time.Now()).
		Save(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to cancel scheduled post")
		return nil, fmt.Errorf("failed to cancel scheduled post: %w", err)
	}

	if affected == 0 {
		return nil, fmt.Errorf("scheduled post not found")
	}

	// Get updated post
	updatedPost, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated post")
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	// Convert to response model
	response := s.convertPostToResponse(updatedPost)
	s.logger.WithField("post_id", postID).Info("Scheduled post canceled successfully")
	return response, nil
}

// GetPostStatus gets post status
func (s *service) GetPostStatus(ctx context.Context, postID, userID string) (*models.PostStatusResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	}).Info("Getting post status")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(postID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get post from database
	post, err := s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("post not found")
		}
		s.logger.WithError(err).Error("Failed to get post")
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Create publish status for each platform
	publishStatus := make([]models.PublishStatus, len(post.Platforms))
	for i, platform := range post.Platforms {
		status := "pending"
		var publishedAt *time.Time
		var errorMsg string

		switch post.Status {
		case "published":
			status = "published"
			if !post.PublishedAt.IsZero() {
				publishedAt = &post.PublishedAt
			}
		case "scheduled":
			status = "pending"
		case "failed":
			status = "failed"
			errorMsg = "Publishing failed" // TODO: Store actual error message
		default:
			status = "pending"
		}

		publishStatus[i] = models.PublishStatus{
			Platform:    platform,
			Status:      status,
			PostID:      "", // TODO: Store platform-specific post ID
			URL:         "", // TODO: Store platform-specific URL
			Error:       errorMsg,
			PublishedAt: publishedAt,
		}
	}

	// Determine next action
	nextAction := ""
	var nextActionAt *time.Time

	switch post.Status {
	case "draft":
		nextAction = "schedule_or_publish"
	case "scheduled":
		nextAction = "publish"
		if !post.ScheduledAt.IsZero() {
			nextActionAt = &post.ScheduledAt
		}
	case "published":
		nextAction = "none"
	case "failed":
		nextAction = "retry_or_edit"
	}

	// Create status response
	response := &models.PostStatusResponse{
		PostID:        postID,
		Status:        string(post.Status),
		PublishStatus: publishStatus,
		LastUpdated:   post.UpdatedAt,
		NextAction:    nextAction,
		NextActionAt:  nextActionAt,
	}

	return response, nil
}

// BulkSchedulePosts schedules multiple posts
func (s *service) BulkSchedulePosts(ctx context.Context, req *models.BulkScheduleRequest) (*models.BulkScheduleResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"post_count": len(req.Posts),
	}).Info("Bulk scheduling posts")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	var successfulPosts []models.PostResponse
	var failedPosts []models.BulkOperationError
	var totalUpdated int

	// Process each post
	for i, bulkPost := range req.Posts {
		// Validate scheduled time is in the future
		if bulkPost.ScheduledAt.Before(time.Now()) {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Scheduled time must be in the future",
				Reason: "invalid_schedule_time",
			})
			continue
		}

		postUUID, err := uuid.Parse(bulkPost.PostID)
		if err != nil {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Invalid post ID format",
				Reason: "invalid_post_id",
			})
			continue
		}

		// Build update query
		updateQuery := s.writeDB.Post.
			Update().
			Where(
				post.ID(postUUID),
				post.UserID(userUUID),
				post.DeletedAtIsNil(),
				post.StatusEQ("draft"), // Only schedule draft posts
			).
			SetScheduledAt(bulkPost.ScheduledAt).
			SetStatus("scheduled").
			SetUpdatedAt(time.Now())

		// Update platforms if specified
		if len(bulkPost.Platforms) > 0 {
			updateQuery = updateQuery.SetPlatforms(bulkPost.Platforms)
		}

		affected, err := updateQuery.Save(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("post_id", bulkPost.PostID).Error("Failed to schedule post")
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  fmt.Sprintf("Failed to schedule post: %v", err),
				Reason: "database_error",
			})
			continue
		}

		if affected == 0 {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Post not found or not in draft status",
				Reason: "post_not_found",
			})
			continue
		}

		// Get updated post for response
		updatedPost, err := s.readDB.Post.
			Query().
			Where(
				post.ID(postUUID),
				post.UserID(userUUID),
				post.DeletedAtIsNil(),
			).
			Only(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("post_id", bulkPost.PostID).Error("Failed to get updated post")
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Failed to retrieve updated post",
				Reason: "database_error",
			})
			continue
		}

		successfulPosts = append(successfulPosts, *s.convertPostToResponse(updatedPost))
		totalUpdated++
	}

	response := &models.BulkScheduleResponse{
		BulkOperationResult: models.BulkOperationResult{
			Total:   len(req.Posts),
			Success: totalUpdated,
			Failed:  len(failedPosts),
			Errors:  failedPosts,
		},
		ScheduledPosts: successfulPosts,
	}

	s.logger.WithFields(map[string]interface{}{
		"total_requested": len(req.Posts),
		"total_updated":   totalUpdated,
		"total_failed":    len(failedPosts),
	}).Info("Bulk schedule posts completed")

	return response, nil
}

// BulkUpdateSchedule updates multiple scheduled posts
func (s *service) BulkUpdateSchedule(ctx context.Context, req *models.BulkScheduleRequest) (*models.BulkScheduleResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    req.UserID,
		"post_count": len(req.Posts),
	}).Info("Bulk updating scheduled posts")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	var successfulPosts []models.PostResponse
	var failedPosts []models.BulkOperationError
	var totalUpdated int

	// Process each post
	for i, bulkPost := range req.Posts {
		// Validate scheduled time is in the future
		if bulkPost.ScheduledAt.Before(time.Now()) {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Scheduled time must be in the future",
				Reason: "invalid_schedule_time",
			})
			continue
		}

		postUUID, err := uuid.Parse(bulkPost.PostID)
		if err != nil {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Invalid post ID format",
				Reason: "invalid_post_id",
			})
			continue
		}

		// Build update query for scheduled posts only
		updateQuery := s.writeDB.Post.
			Update().
			Where(
				post.ID(postUUID),
				post.UserID(userUUID),
				post.DeletedAtIsNil(),
				post.StatusEQ("scheduled"), // Only update already scheduled posts
			).
			SetScheduledAt(bulkPost.ScheduledAt).
			SetUpdatedAt(time.Now())

		// Update platforms if specified
		if len(bulkPost.Platforms) > 0 {
			updateQuery = updateQuery.SetPlatforms(bulkPost.Platforms)
		}

		affected, err := updateQuery.Save(ctx)
		if err != nil {
			s.logger.WithError(err).WithField("post_id", bulkPost.PostID).Error("Failed to update scheduled post")
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  fmt.Sprintf("Failed to update scheduled post: %v", err),
				Reason: "database_error",
			})
			continue
		}

		if affected == 0 {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Scheduled post not found",
				Reason: "post_not_found",
			})
			continue
		}

		// Get updated post for response
		updatedPost, err := s.readDB.Post.
			Query().
			Where(
				post.ID(postUUID),
				post.UserID(userUUID),
				post.DeletedAtIsNil(),
			).
			Only(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("post_id", bulkPost.PostID).Error("Failed to get updated post")
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     bulkPost.PostID,
				Index:  i,
				Error:  "Failed to retrieve updated post",
				Reason: "database_error",
			})
			continue
		}

		successfulPosts = append(successfulPosts, *s.convertPostToResponse(updatedPost))
		totalUpdated++
	}

	response := &models.BulkScheduleResponse{
		BulkOperationResult: models.BulkOperationResult{
			Total:   len(req.Posts),
			Success: totalUpdated,
			Failed:  len(failedPosts),
			Errors:  failedPosts,
		},
		ScheduledPosts: successfulPosts,
	}

	s.logger.WithFields(map[string]interface{}{
		"total_requested": len(req.Posts),
		"total_updated":   totalUpdated,
		"total_failed":    len(failedPosts),
	}).Info("Bulk update schedule completed")

	return response, nil
}

// BulkCancelSchedule cancels multiple scheduled posts
func (s *service) BulkCancelSchedule(ctx context.Context, postIDs []string, userID string) (*models.BulkOperationResult, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":    userID,
		"post_count": len(postIDs),
	}).Info("Bulk canceling scheduled posts")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	var failedPosts []models.BulkOperationError
	var totalUpdated int

	// Process each post ID
	for i, postID := range postIDs {
		postUUID, err := uuid.Parse(postID)
		if err != nil {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     postID,
				Index:  i,
				Error:  "Invalid post ID format",
				Reason: "invalid_post_id",
			})
			continue
		}

		// Update post status from scheduled to draft
		affected, err := s.writeDB.Post.
			Update().
			Where(
				post.ID(postUUID),
				post.UserID(userUUID),
				post.StatusEQ("scheduled"),
				post.DeletedAtIsNil(),
			).
			SetStatus("draft").
			ClearScheduledAt().
			SetUpdatedAt(time.Now()).
			Save(ctx)

		if err != nil {
			s.logger.WithError(err).WithField("post_id", postID).Error("Failed to cancel scheduled post")
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     postID,
				Index:  i,
				Error:  fmt.Sprintf("Failed to cancel scheduled post: %v", err),
				Reason: "database_error",
			})
			continue
		}

		if affected == 0 {
			failedPosts = append(failedPosts, models.BulkOperationError{
				ID:     postID,
				Index:  i,
				Error:  "Scheduled post not found",
				Reason: "post_not_found",
			})
			continue
		}

		totalUpdated++
	}

	response := &models.BulkOperationResult{
		Total:   len(postIDs),
		Success: totalUpdated,
		Failed:  len(failedPosts),
		Errors:  failedPosts,
	}

	s.logger.WithFields(map[string]interface{}{
		"total_requested": len(postIDs),
		"total_updated":   totalUpdated,
		"total_failed":    len(failedPosts),
	}).Info("Bulk cancel schedule completed")

	return response, nil
}

// ListScheduledPosts lists scheduled posts
func (s *service) ListScheduledPosts(ctx context.Context, userID string, page, limit int) (*models.ListPostsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"page":    page,
		"limit":   limit,
	}).Info("Listing scheduled posts")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Build query for scheduled posts
	query := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.StatusEQ("scheduled"),
			post.DeletedAtIsNil(),
		).
		Order(ent.Asc(post.FieldScheduledAt)) // Order by scheduled time

	// Get total count
	total, err := query.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count scheduled posts")
		return nil, fmt.Errorf("failed to count scheduled posts: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	posts, err := query.
		Offset(offset).
		Limit(limit).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to list scheduled posts")
		return nil, fmt.Errorf("failed to list scheduled posts: %w", err)
	}

	// Convert to response models
	postResponses := make([]models.PostResponse, len(posts))
	for i, p := range posts {
		postResponses[i] = *s.convertPostToResponse(p)
	}

	// Calculate pagination
	totalPages := (total + limit - 1) / limit

	response := &models.ListPostsResponse{
		Posts: postResponses,
		Pagination: models.PaginationMeta{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	s.logger.WithField("total_scheduled", total).Info("Scheduled posts listed successfully")
	return response, nil
}

// GetScheduleCalendar gets schedule calendar
func (s *service) GetScheduleCalendar(ctx context.Context, req *models.ScheduleCalendarRequest) (*models.ScheduleCalendarResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": req.UserID,
		"year":    req.Year,
		"month":   req.Month,
	}).Info("Getting schedule calendar")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate month range
	startDate := time.Date(req.Year, time.Month(req.Month), 1, 0, 0, 0, 0, time.UTC)
	endDate := startDate.AddDate(0, 1, 0) // First day of next month

	// Build query for scheduled posts in the month
	query := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.StatusEQ("scheduled"),
			post.DeletedAtIsNil(),
			post.ScheduledAtGTE(startDate),
			post.ScheduledAtLT(endDate),
		)

	// Apply workspace filter if specified
	if req.WorkspaceID != "" {
		query = query.Where(post.WorkspaceID(req.WorkspaceID))
	}

	// Apply platform filter if specified
	if req.Platform != "" {
		query = query.Where(func(s *sql.Selector) {
			s.Where(sql.Contains(s.C(post.FieldPlatforms), req.Platform))
		})
	}

	// Get scheduled posts
	posts, err := query.All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get scheduled posts")
		return nil, fmt.Errorf("failed to get scheduled posts: %w", err)
	}

	// Group posts by date
	calendarDays := make(map[string]models.ScheduleCalendarDay)

	for _, p := range posts {
		dateKey := p.ScheduledAt.Format("2006-01-02")

		if day, exists := calendarDays[dateKey]; exists {
			day.Count++
			day.Posts = append(day.Posts, *s.convertPostToResponse(p))
			calendarDays[dateKey] = day
		} else {
			calendarDays[dateKey] = models.ScheduleCalendarDay{
				Date:  dateKey,
				Count: 1,
				Posts: []models.PostResponse{*s.convertPostToResponse(p)},
			}
		}
	}

	// Convert map to slice
	calendar := make([]models.ScheduleCalendarDay, 0, len(calendarDays))
	for _, day := range calendarDays {
		calendar = append(calendar, day)
	}

	// Calculate statistics
	totalPosts := len(posts)
	platformStats := make(map[string]int)
	statusStats := make(map[string]int)

	var busiestDay string
	var busiestDayCount int

	for _, p := range posts {
		for _, platform := range p.Platforms {
			platformStats[platform]++
		}
		statusStats[string(p.Status)]++
	}

	// Find busiest day
	for _, day := range calendar {
		if day.Count > busiestDayCount {
			busiestDayCount = day.Count
			busiestDay = day.Date
		}
	}

	summary := models.ScheduleCalendarSummary{
		TotalPosts:      totalPosts,
		ByStatus:        statusStats,
		ByPlatform:      platformStats,
		BusiestDay:      busiestDay,
		BusiestDayCount: busiestDayCount,
	}

	response := &models.ScheduleCalendarResponse{
		Year:     req.Year,
		Month:    req.Month,
		Calendar: calendar,
		Summary:  summary,
	}

	s.logger.WithField("total_posts", totalPosts).Info("Schedule calendar retrieved successfully")
	return response, nil
}

// GetPostAnalytics gets post analytics
func (s *service) GetPostAnalytics(ctx context.Context, req *models.PostAnalyticsRequest) (*models.PostAnalyticsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id":   req.UserID,
		"post_id":   req.PostID,
		"date_from": req.DateFrom,
		"date_to":   req.DateTo,
	}).Info("Getting post analytics")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate date range
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30) // Default to 30 days

	if req.DateFrom != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateFrom); err == nil {
			startDate = parsed
		}
	}

	if req.DateTo != "" {
		if parsed, err := time.Parse("2006-01-02", req.DateTo); err == nil {
			endDate = parsed.Add(24 * time.Hour) // Include the entire day
		}
	}

	// Base query for user's posts
	baseQuery := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
			post.CreatedAtGTE(startDate),
			post.CreatedAtLTE(endDate),
		)

	// Apply post ID filter if specified (for single post analytics)
	if req.PostID != "" {
		postUUID, err := uuid.Parse(req.PostID)
		if err != nil {
			return nil, fmt.Errorf("invalid post ID format: %w", err)
		}
		baseQuery = baseQuery.Where(post.ID(postUUID))
	}

	// Apply platform filter if specified
	if req.Platform != "" {
		baseQuery = baseQuery.Where(func(s *sql.Selector) {
			s.Where(sql.Contains(s.C(post.FieldPlatforms), req.Platform))
		})
	}

	// Get total posts count
	totalPosts, err := baseQuery.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count total posts")
		return nil, fmt.Errorf("failed to count total posts: %w", err)
	}

	// Get posts by status
	draftPosts, _ := baseQuery.Clone().Where(post.StatusEQ("draft")).Count(ctx)
	scheduledPosts, _ := baseQuery.Clone().Where(post.StatusEQ("scheduled")).Count(ctx)
	publishedPosts, _ := baseQuery.Clone().Where(post.StatusEQ("published")).Count(ctx)
	failedPosts, _ := baseQuery.Clone().Where(post.StatusEQ("failed")).Count(ctx)

	// Calculate success rate
	var successRate float64
	if totalPosts > 0 {
		successRate = float64(publishedPosts) / float64(totalPosts) * 100
	}

	// Get platform statistics
	platformStats := make(map[string]int)
	posts, err := baseQuery.Clone().All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get posts for platform stats")
	} else {
		for _, p := range posts {
			for _, platform := range p.Platforms {
				platformStats[platform]++
			}
		}
	}

	// Create overall analytics
	overall := models.Analytics{
		Views:       int64(publishedPosts),
		Likes:       0, // TODO: Integrate with social media APIs
		Comments:    0, // TODO: Integrate with social media APIs
		Shares:      0, // TODO: Integrate with social media APIs
		Engagement:  0, // TODO: Calculate engagement
		Reach:       0, // TODO: Integrate with social media APIs
		Impressions: 0, // TODO: Integrate with social media APIs
		CTR:         successRate / 100,
	}

	// Create platform analytics
	byPlatform := make(map[string]models.Analytics)
	for platform, count := range platformStats {
		byPlatform[platform] = models.Analytics{
			Views:       int64(count),
			Likes:       0, // TODO: Integrate with platform APIs
			Comments:    0, // TODO: Integrate with platform APIs
			Shares:      0, // TODO: Integrate with platform APIs
			Engagement:  0, // TODO: Calculate engagement
			Reach:       0, // TODO: Integrate with platform APIs
			Impressions: 0, // TODO: Integrate with platform APIs
			CTR:         0, // TODO: Calculate CTR
		}
	}

	// Create timeline analytics (simplified)
	timeline := make([]models.TimelineAnalytics, 0)
	// TODO: Implement detailed timeline analytics

	response := &models.PostAnalyticsResponse{
		PostID:     req.PostID,
		Overall:    overall,
		ByPlatform: byPlatform,
		Timeline:   timeline,
		Demographics: map[string]interface{}{
			"total_posts":     totalPosts,
			"published_posts": publishedPosts,
			"scheduled_posts": scheduledPosts,
			"draft_posts":     draftPosts,
			"failed_posts":    failedPosts,
			"success_rate":    successRate,
		},
	}

	s.logger.WithField("total_posts", totalPosts).Info("Post analytics generated successfully")
	return response, nil
}

// GetPostEngagement gets post engagement
func (s *service) GetPostEngagement(ctx context.Context, req *models.PostEngagementRequest) (*models.PostEngagementResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"post_id": req.PostID,
		"user_id": req.UserID,
	}).Info("Getting post engagement")

	// Parse post ID to UUID
	postUUID, err := uuid.Parse(req.PostID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid post ID format")
		return nil, fmt.Errorf("invalid post ID format: %w", err)
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Get post from database to validate access
	_, err = s.readDB.Post.
		Query().
		Where(
			post.ID(postUUID),
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
		).
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("post not found")
		}
		s.logger.WithError(err).Error("Failed to get post")
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Create engagement data points (simplified - in production integrate with social media APIs)
	now := time.Now()
	engagementData := make([]models.EngagementDataPoint, 24) // Last 24 hours

	totalEngagement := int64(0)
	peakEngagement := int64(0)
	var peakEngagementAt time.Time

	for i := 0; i < 24; i++ {
		timestamp := now.Add(time.Duration(-i) * time.Hour)

		// Simulate engagement data (in production, get from social media APIs)
		views := int64(100 + i*10)
		likes := int64(10 + i*2)
		comments := int64(2 + i/3)
		shares := int64(1 + i/5)
		engagement := likes + comments + shares

		engagementData[23-i] = models.EngagementDataPoint{
			Timestamp:  timestamp,
			Views:      views,
			Likes:      likes,
			Comments:   comments,
			Shares:     shares,
			Engagement: engagement,
		}

		totalEngagement += engagement
		if engagement > peakEngagement {
			peakEngagement = engagement
			peakEngagementAt = timestamp
		}
	}

	// Calculate average engagement
	averageEngagement := float64(totalEngagement) / 24.0

	// Calculate engagement rate (simplified)
	totalViews := int64(0)
	for _, data := range engagementData {
		totalViews += data.Views
	}

	engagementRate := float64(0)
	if totalViews > 0 {
		engagementRate = (float64(totalEngagement) / float64(totalViews)) * 100
	}

	// Create engagement summary
	summary := models.EngagementSummary{
		TotalEngagement:   totalEngagement,
		EngagementRate:    engagementRate,
		PeakEngagement:    peakEngagement,
		PeakEngagementAt:  peakEngagementAt,
		AverageEngagement: averageEngagement,
	}

	// Create predictions (simplified)
	predictions := make([]models.EngagementPrediction, 3)
	for i := 0; i < 3; i++ {
		predictions[i] = models.EngagementPrediction{
			Timestamp:           now.Add(time.Duration(i+1) * time.Hour),
			PredictedEngagement: totalEngagement/24 + int64(i*5), // Simple prediction
			Confidence:          0.75 - float64(i)*0.1,           // Decreasing confidence
		}
	}

	// Determine period based on request or default to "24h"
	period := req.Period
	if period == "" {
		period = "24h"
	}

	response := &models.PostEngagementResponse{
		PostID:      req.PostID,
		Platform:    req.Platform,
		Period:      period,
		Engagement:  engagementData,
		Summary:     summary,
		Predictions: predictions,
	}

	s.logger.WithField("engagement_rate", engagementRate).Info("Post engagement retrieved successfully")
	return response, nil
}

// GetPostsAnalytics gets posts analytics
func (s *service) GetPostsAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"period":  period,
	}).Info("Getting posts analytics")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate date range based on period
	endDate := time.Now()
	var startDate time.Time

	switch period {
	case "7d":
		startDate = endDate.AddDate(0, 0, -7)
	case "30d":
		startDate = endDate.AddDate(0, 0, -30)
	case "90d":
		startDate = endDate.AddDate(0, 0, -90)
	case "1y":
		startDate = endDate.AddDate(-1, 0, 0)
	default:
		startDate = endDate.AddDate(0, 0, -30) // Default to 30 days
	}

	// Base query for user's posts
	baseQuery := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.DeletedAtIsNil(),
			post.CreatedAtGTE(startDate),
			post.CreatedAtLTE(endDate),
		)

	// Get all posts for analysis
	posts, err := baseQuery.All(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get posts for analytics")
		return nil, fmt.Errorf("failed to get posts for analytics: %w", err)
	}

	// Calculate overall metrics
	totalPosts := len(posts)
	publishedPosts := 0
	scheduledPosts := 0
	draftPosts := 0
	failedPosts := 0

	platformStats := make(map[string]int)

	for _, p := range posts {
		// Count by status
		switch p.Status {
		case "published":
			publishedPosts++
		case "scheduled":
			scheduledPosts++
		case "draft":
			draftPosts++
		case "failed":
			failedPosts++
		}

		// Count by platform
		for _, platform := range p.Platforms {
			platformStats[platform]++
		}
	}

	// Calculate success rate
	successRate := float64(0)
	if totalPosts > 0 {
		successRate = (float64(publishedPosts) / float64(totalPosts)) * 100
	}

	// Create overall analytics (reuse existing structure)
	overall := models.Analytics{
		Views:       int64(publishedPosts * 100), // Simplified
		Likes:       int64(publishedPosts * 15),  // Simplified
		Comments:    int64(publishedPosts * 3),   // Simplified
		Shares:      int64(publishedPosts * 2),   // Simplified
		Engagement:  int64(publishedPosts * 20),  // Simplified
		Reach:       int64(publishedPosts * 300), // Simplified
		Impressions: int64(publishedPosts * 500), // Simplified
		CTR:         successRate / 100,
	}

	// Create platform analytics
	byPlatform := make(map[string]models.Analytics)
	for platform, count := range platformStats {
		byPlatform[platform] = models.Analytics{
			Views:       int64(count * 100),
			Likes:       int64(count * 15),
			Comments:    int64(count * 3),
			Shares:      int64(count * 2),
			Engagement:  int64(count * 20),
			Reach:       int64(count * 300),
			Impressions: int64(count * 500),
			CTR:         0.05, // Simplified
		}
	}

	response := &models.PostAnalyticsResponse{
		PostID:     "", // Empty for aggregate analytics
		Overall:    overall,
		ByPlatform: byPlatform,
		Timeline:   []models.TimelineAnalytics{}, // TODO: Implement timeline
		Demographics: map[string]interface{}{
			"total_posts":     totalPosts,
			"published_posts": publishedPosts,
			"scheduled_posts": scheduledPosts,
			"draft_posts":     draftPosts,
			"failed_posts":    failedPosts,
			"success_rate":    successRate,
			"period":          period,
		},
	}

	s.logger.WithField("total_posts", totalPosts).Info("Posts analytics generated successfully")
	return response, nil
}

// GetPerformanceAnalytics gets performance analytics
func (s *service) GetPerformanceAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"period":  period,
	}).Info("Getting performance analytics")

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		s.logger.WithError(err).Error("Invalid user ID format")
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Calculate date range based on period
	endDate := time.Now()
	var startDate time.Time

	switch period {
	case "7d":
		startDate = endDate.AddDate(0, 0, -7)
	case "30d":
		startDate = endDate.AddDate(0, 0, -30)
	case "90d":
		startDate = endDate.AddDate(0, 0, -90)
	case "1y":
		startDate = endDate.AddDate(-1, 0, 0)
	default:
		startDate = endDate.AddDate(0, 0, -30) // Default to 30 days
	}

	// Get published posts only for performance analysis
	posts, err := s.readDB.Post.
		Query().
		Where(
			post.UserID(userUUID),
			post.StatusEQ("published"),
			post.DeletedAtIsNil(),
			post.PublishedAtGTE(startDate),
			post.PublishedAtLTE(endDate),
		).
		Order(ent.Desc(post.FieldPublishedAt)).
		All(ctx)

	if err != nil {
		s.logger.WithError(err).Error("Failed to get published posts for performance analytics")
		return nil, fmt.Errorf("failed to get published posts: %w", err)
	}

	totalPosts := len(posts)
	if totalPosts == 0 {
		// Return empty analytics if no published posts
		return &models.PostAnalyticsResponse{
			PostID:       "",
			Overall:      models.Analytics{},
			ByPlatform:   make(map[string]models.Analytics),
			Timeline:     []models.TimelineAnalytics{},
			Demographics: map[string]interface{}{"message": "No published posts in the specified period"},
		}, nil
	}

	// Calculate performance metrics (simplified - in production integrate with social media APIs)
	totalViews := int64(0)
	totalLikes := int64(0)
	totalComments := int64(0)
	totalShares := int64(0)
	totalReach := int64(0)
	totalImpressions := int64(0)

	platformStats := make(map[string]models.Analytics)
	platformCounts := make(map[string]int)

	// Simulate performance data for each post
	for i, p := range posts {
		// Simulate decreasing performance for older posts
		multiplier := float64(totalPosts-i) / float64(totalPosts)

		views := int64(float64(500) * multiplier)
		likes := int64(float64(75) * multiplier)
		comments := int64(float64(15) * multiplier)
		shares := int64(float64(10) * multiplier)
		reach := int64(float64(1500) * multiplier)
		impressions := int64(float64(2500) * multiplier)

		totalViews += views
		totalLikes += likes
		totalComments += comments
		totalShares += shares
		totalReach += reach
		totalImpressions += impressions

		// Aggregate by platform
		for _, platform := range p.Platforms {
			platformCounts[platform]++

			if existing, exists := platformStats[platform]; exists {
				existing.Views += views / int64(len(p.Platforms))
				existing.Likes += likes / int64(len(p.Platforms))
				existing.Comments += comments / int64(len(p.Platforms))
				existing.Shares += shares / int64(len(p.Platforms))
				existing.Reach += reach / int64(len(p.Platforms))
				existing.Impressions += impressions / int64(len(p.Platforms))
				platformStats[platform] = existing
			} else {
				platformStats[platform] = models.Analytics{
					Views:       views / int64(len(p.Platforms)),
					Likes:       likes / int64(len(p.Platforms)),
					Comments:    comments / int64(len(p.Platforms)),
					Shares:      shares / int64(len(p.Platforms)),
					Engagement:  (likes + comments + shares) / int64(len(p.Platforms)),
					Reach:       reach / int64(len(p.Platforms)),
					Impressions: impressions / int64(len(p.Platforms)),
					CTR:         0.05, // Simplified
				}
			}
		}
	}

	// Calculate overall CTR
	overallCTR := float64(0)
	if totalImpressions > 0 {
		overallCTR = float64(totalViews) / float64(totalImpressions)
	}

	// Create overall performance analytics
	overall := models.Analytics{
		Views:       totalViews,
		Likes:       totalLikes,
		Comments:    totalComments,
		Shares:      totalShares,
		Engagement:  totalLikes + totalComments + totalShares,
		Reach:       totalReach,
		Impressions: totalImpressions,
		CTR:         overallCTR,
	}

	// Calculate CTR for each platform
	for platform, stats := range platformStats {
		if stats.Impressions > 0 {
			stats.CTR = float64(stats.Views) / float64(stats.Impressions)
			platformStats[platform] = stats
		}
	}

	// Create timeline analytics (simplified daily breakdown)
	timeline := make([]models.TimelineAnalytics, 0)
	currentDate := startDate
	for currentDate.Before(endDate) {
		dayStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), 0, 0, 0, 0, currentDate.Location())
		dayEnd := dayStart.Add(24 * time.Hour)

		// Count posts published on this day
		dayPosts := 0
		for _, p := range posts {
			if p.PublishedAt.After(dayStart) && p.PublishedAt.Before(dayEnd) {
				dayPosts++
			}
		}

		if dayPosts > 0 {
			timeline = append(timeline, models.TimelineAnalytics{
				Date: currentDate.Format("2006-01-02"),
				Analytics: models.Analytics{
					Views:       int64(dayPosts * 500),  // Simplified
					Likes:       int64(dayPosts * 75),   // Simplified
					Comments:    int64(dayPosts * 15),   // Simplified
					Shares:      int64(dayPosts * 10),   // Simplified
					Engagement:  int64(dayPosts * 100),  // Simplified
					Reach:       int64(dayPosts * 1500), // Simplified
					Impressions: int64(dayPosts * 2500), // Simplified
					CTR:         0.2,                    // Simplified
				},
			})
		}

		currentDate = currentDate.AddDate(0, 0, 1)
	}

	response := &models.PostAnalyticsResponse{
		PostID:     "", // Empty for aggregate performance analytics
		Overall:    overall,
		ByPlatform: platformStats,
		Timeline:   timeline,
		Demographics: map[string]interface{}{
			"total_published_posts":       totalPosts,
			"average_views_per_post":      totalViews / int64(totalPosts),
			"average_engagement_per_post": (totalLikes + totalComments + totalShares) / int64(totalPosts),
			"period":                      period,
			"performance_summary":         "Based on published posts only",
		},
	}

	s.logger.WithFields(map[string]interface{}{
		"total_posts":      totalPosts,
		"total_views":      totalViews,
		"total_engagement": totalLikes + totalComments + totalShares,
	}).Info("Performance analytics generated successfully")

	return response, nil
}

// GetEngagementAnalytics gets engagement analytics
func (s *service) GetEngagementAnalytics(ctx context.Context, userID string, period string) (*models.PostAnalyticsResponse, error) {
	// TODO: Implement get engagement analytics
	return nil, fmt.Errorf("not implemented")
}

// AdminListPosts lists posts (admin)
func (s *service) AdminListPosts(ctx context.Context, req *models.ListPostsRequest) (*models.ListPostsResponse, error) {
	// TODO: Implement admin list posts
	return nil, fmt.Errorf("not implemented")
}

// AdminGetPost gets post (admin)
func (s *service) AdminGetPost(ctx context.Context, postID string) (*models.PostResponse, error) {
	// TODO: Implement admin get post
	return nil, fmt.Errorf("not implemented")
}

// AdminDeletePost deletes post (admin)
func (s *service) AdminDeletePost(ctx context.Context, postID string) error {
	// TODO: Implement admin delete post
	return fmt.Errorf("not implemented")
}

// GetAdminAnalytics gets admin analytics
func (s *service) GetAdminAnalytics(ctx context.Context, period string) (*models.PostAnalyticsResponse, error) {
	// TODO: Implement get admin analytics
	return nil, fmt.Errorf("not implemented")
}

// convertPostToResponse converts an ent Post to PostResponse model
func (s *service) convertPostToResponse(post *ent.Post) *models.PostResponse {
	response := &models.PostResponse{
		BaseModel: models.BaseModel{
			ID:        post.ID.String(),
			CreatedAt: post.CreatedAt,
			UpdatedAt: post.UpdatedAt,
		},
		Title:     post.Title,
		Content:   post.Content,
		UserID:    post.UserID.String(),
		Status:    string(post.Status),
		Platforms: post.Platforms,
		Type:      "manual", // Default type, should be determined from post data
	}

	// Set workspace ID if present
	if post.WorkspaceID != "" {
		response.WorkspaceID = post.WorkspaceID
	}

	// Set template ID if present
	if post.TemplateID != "" {
		response.TemplateID = post.TemplateID
	}

	// Set scheduled time if present
	if !post.ScheduledAt.IsZero() {
		response.ScheduledAt = &post.ScheduledAt
	}

	// Set published time if present
	if !post.PublishedAt.IsZero() {
		response.PublishedAt = &post.PublishedAt
	}

	// Convert media URLs to MediaAssets
	if len(post.MediaUrls) > 0 {
		mediaAssets := make([]models.MediaAsset, len(post.MediaUrls))
		for i, url := range post.MediaUrls {
			mediaAssets[i] = models.MediaAsset{
				URL:  url,
				Type: "image", // Default type, should be determined from URL
			}
		}
		response.MediaAssets = mediaAssets
	}

	// Set hashtags if present
	if len(post.Hashtags) > 0 {
		response.Hashtags = post.Hashtags
	}

	// Set mentions if present
	if len(post.Mentions) > 0 {
		// Convert mentions to tags for now
		response.Tags = post.Mentions
	}

	return response
}

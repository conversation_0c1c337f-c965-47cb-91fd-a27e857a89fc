// Code generated by ent, DO NOT EDIT.

package post

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/content-mgmt-service/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldUserID, v))
}

// WorkspaceID applies equality check predicate on the "workspace_id" field. It's identical to WorkspaceIDEQ.
func WorkspaceID(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldWorkspaceID, v))
}

// TemplateID applies equality check predicate on the "template_id" field. It's identical to TemplateIDEQ.
func TemplateID(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldTemplateID, v))
}

// GenerationID applies equality check predicate on the "generation_id" field. It's identical to GenerationIDEQ.
func GenerationID(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldGenerationID, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldTitle, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldContent, v))
}

// OriginalContent applies equality check predicate on the "original_content" field. It's identical to OriginalContentEQ.
func OriginalContent(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldOriginalContent, v))
}

// ScheduledAt applies equality check predicate on the "scheduled_at" field. It's identical to ScheduledAtEQ.
func ScheduledAt(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldScheduledAt, v))
}

// PublishedAt applies equality check predicate on the "published_at" field. It's identical to PublishedAtEQ.
func PublishedAt(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldPublishedAt, v))
}

// Language applies equality check predicate on the "language" field. It's identical to LanguageEQ.
func Language(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldLanguage, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldDeletedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldUserID, v))
}

// WorkspaceIDEQ applies the EQ predicate on the "workspace_id" field.
func WorkspaceIDEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldWorkspaceID, v))
}

// WorkspaceIDNEQ applies the NEQ predicate on the "workspace_id" field.
func WorkspaceIDNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldWorkspaceID, v))
}

// WorkspaceIDIn applies the In predicate on the "workspace_id" field.
func WorkspaceIDIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDNotIn applies the NotIn predicate on the "workspace_id" field.
func WorkspaceIDNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldWorkspaceID, vs...))
}

// WorkspaceIDGT applies the GT predicate on the "workspace_id" field.
func WorkspaceIDGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldWorkspaceID, v))
}

// WorkspaceIDGTE applies the GTE predicate on the "workspace_id" field.
func WorkspaceIDGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldWorkspaceID, v))
}

// WorkspaceIDLT applies the LT predicate on the "workspace_id" field.
func WorkspaceIDLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldWorkspaceID, v))
}

// WorkspaceIDLTE applies the LTE predicate on the "workspace_id" field.
func WorkspaceIDLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldWorkspaceID, v))
}

// WorkspaceIDContains applies the Contains predicate on the "workspace_id" field.
func WorkspaceIDContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldWorkspaceID, v))
}

// WorkspaceIDHasPrefix applies the HasPrefix predicate on the "workspace_id" field.
func WorkspaceIDHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldWorkspaceID, v))
}

// WorkspaceIDHasSuffix applies the HasSuffix predicate on the "workspace_id" field.
func WorkspaceIDHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldWorkspaceID, v))
}

// WorkspaceIDIsNil applies the IsNil predicate on the "workspace_id" field.
func WorkspaceIDIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldWorkspaceID))
}

// WorkspaceIDNotNil applies the NotNil predicate on the "workspace_id" field.
func WorkspaceIDNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldWorkspaceID))
}

// WorkspaceIDEqualFold applies the EqualFold predicate on the "workspace_id" field.
func WorkspaceIDEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldWorkspaceID, v))
}

// WorkspaceIDContainsFold applies the ContainsFold predicate on the "workspace_id" field.
func WorkspaceIDContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldWorkspaceID, v))
}

// TemplateIDEQ applies the EQ predicate on the "template_id" field.
func TemplateIDEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldTemplateID, v))
}

// TemplateIDNEQ applies the NEQ predicate on the "template_id" field.
func TemplateIDNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldTemplateID, v))
}

// TemplateIDIn applies the In predicate on the "template_id" field.
func TemplateIDIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldTemplateID, vs...))
}

// TemplateIDNotIn applies the NotIn predicate on the "template_id" field.
func TemplateIDNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldTemplateID, vs...))
}

// TemplateIDGT applies the GT predicate on the "template_id" field.
func TemplateIDGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldTemplateID, v))
}

// TemplateIDGTE applies the GTE predicate on the "template_id" field.
func TemplateIDGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldTemplateID, v))
}

// TemplateIDLT applies the LT predicate on the "template_id" field.
func TemplateIDLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldTemplateID, v))
}

// TemplateIDLTE applies the LTE predicate on the "template_id" field.
func TemplateIDLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldTemplateID, v))
}

// TemplateIDContains applies the Contains predicate on the "template_id" field.
func TemplateIDContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldTemplateID, v))
}

// TemplateIDHasPrefix applies the HasPrefix predicate on the "template_id" field.
func TemplateIDHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldTemplateID, v))
}

// TemplateIDHasSuffix applies the HasSuffix predicate on the "template_id" field.
func TemplateIDHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldTemplateID, v))
}

// TemplateIDIsNil applies the IsNil predicate on the "template_id" field.
func TemplateIDIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldTemplateID))
}

// TemplateIDNotNil applies the NotNil predicate on the "template_id" field.
func TemplateIDNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldTemplateID))
}

// TemplateIDEqualFold applies the EqualFold predicate on the "template_id" field.
func TemplateIDEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldTemplateID, v))
}

// TemplateIDContainsFold applies the ContainsFold predicate on the "template_id" field.
func TemplateIDContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldTemplateID, v))
}

// GenerationIDEQ applies the EQ predicate on the "generation_id" field.
func GenerationIDEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldGenerationID, v))
}

// GenerationIDNEQ applies the NEQ predicate on the "generation_id" field.
func GenerationIDNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldGenerationID, v))
}

// GenerationIDIn applies the In predicate on the "generation_id" field.
func GenerationIDIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldGenerationID, vs...))
}

// GenerationIDNotIn applies the NotIn predicate on the "generation_id" field.
func GenerationIDNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldGenerationID, vs...))
}

// GenerationIDGT applies the GT predicate on the "generation_id" field.
func GenerationIDGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldGenerationID, v))
}

// GenerationIDGTE applies the GTE predicate on the "generation_id" field.
func GenerationIDGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldGenerationID, v))
}

// GenerationIDLT applies the LT predicate on the "generation_id" field.
func GenerationIDLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldGenerationID, v))
}

// GenerationIDLTE applies the LTE predicate on the "generation_id" field.
func GenerationIDLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldGenerationID, v))
}

// GenerationIDContains applies the Contains predicate on the "generation_id" field.
func GenerationIDContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldGenerationID, v))
}

// GenerationIDHasPrefix applies the HasPrefix predicate on the "generation_id" field.
func GenerationIDHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldGenerationID, v))
}

// GenerationIDHasSuffix applies the HasSuffix predicate on the "generation_id" field.
func GenerationIDHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldGenerationID, v))
}

// GenerationIDIsNil applies the IsNil predicate on the "generation_id" field.
func GenerationIDIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldGenerationID))
}

// GenerationIDNotNil applies the NotNil predicate on the "generation_id" field.
func GenerationIDNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldGenerationID))
}

// GenerationIDEqualFold applies the EqualFold predicate on the "generation_id" field.
func GenerationIDEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldGenerationID, v))
}

// GenerationIDContainsFold applies the ContainsFold predicate on the "generation_id" field.
func GenerationIDContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldGenerationID, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldTitle, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldContent, v))
}

// OriginalContentEQ applies the EQ predicate on the "original_content" field.
func OriginalContentEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldOriginalContent, v))
}

// OriginalContentNEQ applies the NEQ predicate on the "original_content" field.
func OriginalContentNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldOriginalContent, v))
}

// OriginalContentIn applies the In predicate on the "original_content" field.
func OriginalContentIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldOriginalContent, vs...))
}

// OriginalContentNotIn applies the NotIn predicate on the "original_content" field.
func OriginalContentNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldOriginalContent, vs...))
}

// OriginalContentGT applies the GT predicate on the "original_content" field.
func OriginalContentGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldOriginalContent, v))
}

// OriginalContentGTE applies the GTE predicate on the "original_content" field.
func OriginalContentGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldOriginalContent, v))
}

// OriginalContentLT applies the LT predicate on the "original_content" field.
func OriginalContentLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldOriginalContent, v))
}

// OriginalContentLTE applies the LTE predicate on the "original_content" field.
func OriginalContentLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldOriginalContent, v))
}

// OriginalContentContains applies the Contains predicate on the "original_content" field.
func OriginalContentContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldOriginalContent, v))
}

// OriginalContentHasPrefix applies the HasPrefix predicate on the "original_content" field.
func OriginalContentHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldOriginalContent, v))
}

// OriginalContentHasSuffix applies the HasSuffix predicate on the "original_content" field.
func OriginalContentHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldOriginalContent, v))
}

// OriginalContentIsNil applies the IsNil predicate on the "original_content" field.
func OriginalContentIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldOriginalContent))
}

// OriginalContentNotNil applies the NotNil predicate on the "original_content" field.
func OriginalContentNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldOriginalContent))
}

// OriginalContentEqualFold applies the EqualFold predicate on the "original_content" field.
func OriginalContentEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldOriginalContent, v))
}

// OriginalContentContainsFold applies the ContainsFold predicate on the "original_content" field.
func OriginalContentContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldOriginalContent, v))
}

// PlatformsIsNil applies the IsNil predicate on the "platforms" field.
func PlatformsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldPlatforms))
}

// PlatformsNotNil applies the NotNil predicate on the "platforms" field.
func PlatformsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldPlatforms))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldStatus, vs...))
}

// ScheduledAtEQ applies the EQ predicate on the "scheduled_at" field.
func ScheduledAtEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldScheduledAt, v))
}

// ScheduledAtNEQ applies the NEQ predicate on the "scheduled_at" field.
func ScheduledAtNEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldScheduledAt, v))
}

// ScheduledAtIn applies the In predicate on the "scheduled_at" field.
func ScheduledAtIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldScheduledAt, vs...))
}

// ScheduledAtNotIn applies the NotIn predicate on the "scheduled_at" field.
func ScheduledAtNotIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldScheduledAt, vs...))
}

// ScheduledAtGT applies the GT predicate on the "scheduled_at" field.
func ScheduledAtGT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldScheduledAt, v))
}

// ScheduledAtGTE applies the GTE predicate on the "scheduled_at" field.
func ScheduledAtGTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldScheduledAt, v))
}

// ScheduledAtLT applies the LT predicate on the "scheduled_at" field.
func ScheduledAtLT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldScheduledAt, v))
}

// ScheduledAtLTE applies the LTE predicate on the "scheduled_at" field.
func ScheduledAtLTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldScheduledAt, v))
}

// ScheduledAtIsNil applies the IsNil predicate on the "scheduled_at" field.
func ScheduledAtIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldScheduledAt))
}

// ScheduledAtNotNil applies the NotNil predicate on the "scheduled_at" field.
func ScheduledAtNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldScheduledAt))
}

// PublishedAtEQ applies the EQ predicate on the "published_at" field.
func PublishedAtEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldPublishedAt, v))
}

// PublishedAtNEQ applies the NEQ predicate on the "published_at" field.
func PublishedAtNEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldPublishedAt, v))
}

// PublishedAtIn applies the In predicate on the "published_at" field.
func PublishedAtIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldPublishedAt, vs...))
}

// PublishedAtNotIn applies the NotIn predicate on the "published_at" field.
func PublishedAtNotIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldPublishedAt, vs...))
}

// PublishedAtGT applies the GT predicate on the "published_at" field.
func PublishedAtGT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldPublishedAt, v))
}

// PublishedAtGTE applies the GTE predicate on the "published_at" field.
func PublishedAtGTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldPublishedAt, v))
}

// PublishedAtLT applies the LT predicate on the "published_at" field.
func PublishedAtLT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldPublishedAt, v))
}

// PublishedAtLTE applies the LTE predicate on the "published_at" field.
func PublishedAtLTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldPublishedAt, v))
}

// PublishedAtIsNil applies the IsNil predicate on the "published_at" field.
func PublishedAtIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldPublishedAt))
}

// PublishedAtNotNil applies the NotNil predicate on the "published_at" field.
func PublishedAtNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldPublishedAt))
}

// PlatformPostsIsNil applies the IsNil predicate on the "platform_posts" field.
func PlatformPostsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldPlatformPosts))
}

// PlatformPostsNotNil applies the NotNil predicate on the "platform_posts" field.
func PlatformPostsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldPlatformPosts))
}

// HashtagsIsNil applies the IsNil predicate on the "hashtags" field.
func HashtagsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldHashtags))
}

// HashtagsNotNil applies the NotNil predicate on the "hashtags" field.
func HashtagsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldHashtags))
}

// MentionsIsNil applies the IsNil predicate on the "mentions" field.
func MentionsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldMentions))
}

// MentionsNotNil applies the NotNil predicate on the "mentions" field.
func MentionsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldMentions))
}

// MediaUrlsIsNil applies the IsNil predicate on the "media_urls" field.
func MediaUrlsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldMediaUrls))
}

// MediaUrlsNotNil applies the NotNil predicate on the "media_urls" field.
func MediaUrlsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldMediaUrls))
}

// VariablesIsNil applies the IsNil predicate on the "variables" field.
func VariablesIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldVariables))
}

// VariablesNotNil applies the NotNil predicate on the "variables" field.
func VariablesNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldVariables))
}

// VisibilityEQ applies the EQ predicate on the "visibility" field.
func VisibilityEQ(v Visibility) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldVisibility, v))
}

// VisibilityNEQ applies the NEQ predicate on the "visibility" field.
func VisibilityNEQ(v Visibility) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldVisibility, v))
}

// VisibilityIn applies the In predicate on the "visibility" field.
func VisibilityIn(vs ...Visibility) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldVisibility, vs...))
}

// VisibilityNotIn applies the NotIn predicate on the "visibility" field.
func VisibilityNotIn(vs ...Visibility) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldVisibility, vs...))
}

// LanguageEQ applies the EQ predicate on the "language" field.
func LanguageEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldLanguage, v))
}

// LanguageNEQ applies the NEQ predicate on the "language" field.
func LanguageNEQ(v string) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldLanguage, v))
}

// LanguageIn applies the In predicate on the "language" field.
func LanguageIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldLanguage, vs...))
}

// LanguageNotIn applies the NotIn predicate on the "language" field.
func LanguageNotIn(vs ...string) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldLanguage, vs...))
}

// LanguageGT applies the GT predicate on the "language" field.
func LanguageGT(v string) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldLanguage, v))
}

// LanguageGTE applies the GTE predicate on the "language" field.
func LanguageGTE(v string) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldLanguage, v))
}

// LanguageLT applies the LT predicate on the "language" field.
func LanguageLT(v string) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldLanguage, v))
}

// LanguageLTE applies the LTE predicate on the "language" field.
func LanguageLTE(v string) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldLanguage, v))
}

// LanguageContains applies the Contains predicate on the "language" field.
func LanguageContains(v string) predicate.Post {
	return predicate.Post(sql.FieldContains(FieldLanguage, v))
}

// LanguageHasPrefix applies the HasPrefix predicate on the "language" field.
func LanguageHasPrefix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasPrefix(FieldLanguage, v))
}

// LanguageHasSuffix applies the HasSuffix predicate on the "language" field.
func LanguageHasSuffix(v string) predicate.Post {
	return predicate.Post(sql.FieldHasSuffix(FieldLanguage, v))
}

// LanguageEqualFold applies the EqualFold predicate on the "language" field.
func LanguageEqualFold(v string) predicate.Post {
	return predicate.Post(sql.FieldEqualFold(FieldLanguage, v))
}

// LanguageContainsFold applies the ContainsFold predicate on the "language" field.
func LanguageContainsFold(v string) predicate.Post {
	return predicate.Post(sql.FieldContainsFold(FieldLanguage, v))
}

// AnalyticsIsNil applies the IsNil predicate on the "analytics" field.
func AnalyticsIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldAnalytics))
}

// AnalyticsNotNil applies the NotNil predicate on the "analytics" field.
func AnalyticsNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldAnalytics))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Post {
	return predicate.Post(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Post {
	return predicate.Post(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Post {
	return predicate.Post(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Post {
	return predicate.Post(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Post) predicate.Post {
	return predicate.Post(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Post) predicate.Post {
	return predicate.Post(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Post) predicate.Post {
	return predicate.Post(sql.NotPredicates(p))
}

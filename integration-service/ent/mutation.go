// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/integration-service/ent/platformintegration"
	"github.com/social-content-ai/integration-service/ent/predicate"
	"github.com/social-content-ai/integration-service/ent/publishedpost"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypePlatformIntegration = "PlatformIntegration"
	TypePublishedPost       = "PublishedPost"
)

// PlatformIntegrationMutation represents an operation that mutates the PlatformIntegration nodes in the graph.
type PlatformIntegrationMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	user_id               *uuid.UUID
	workspace_id          *string
	platform              *platformintegration.Platform
	platform_user_id      *string
	platform_username     *string
	platform_display_name *string
	platform_avatar_url   *string
	access_token          *string
	refresh_token         *string
	token_expires_at      *time.Time
	scopes                *[]string
	appendscopes          []string
	platform_data         *map[string]interface{}
	status                *platformintegration.Status
	error_message         *string
	last_sync_at          *time.Time
	last_post_at          *time.Time
	capabilities          *[]string
	appendcapabilities    []string
	settings              *map[string]interface{}
	auto_publish          *bool
	is_primary            *bool
	metadata              *map[string]interface{}
	created_at            *time.Time
	updated_at            *time.Time
	deleted_at            *time.Time
	clearedFields         map[string]struct{}
	done                  bool
	oldValue              func(context.Context) (*PlatformIntegration, error)
	predicates            []predicate.PlatformIntegration
}

var _ ent.Mutation = (*PlatformIntegrationMutation)(nil)

// platformintegrationOption allows management of the mutation configuration using functional options.
type platformintegrationOption func(*PlatformIntegrationMutation)

// newPlatformIntegrationMutation creates new mutation for the PlatformIntegration entity.
func newPlatformIntegrationMutation(c config, op Op, opts ...platformintegrationOption) *PlatformIntegrationMutation {
	m := &PlatformIntegrationMutation{
		config:        c,
		op:            op,
		typ:           TypePlatformIntegration,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPlatformIntegrationID sets the ID field of the mutation.
func withPlatformIntegrationID(id uuid.UUID) platformintegrationOption {
	return func(m *PlatformIntegrationMutation) {
		var (
			err   error
			once  sync.Once
			value *PlatformIntegration
		)
		m.oldValue = func(ctx context.Context) (*PlatformIntegration, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().PlatformIntegration.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPlatformIntegration sets the old PlatformIntegration of the mutation.
func withPlatformIntegration(node *PlatformIntegration) platformintegrationOption {
	return func(m *PlatformIntegrationMutation) {
		m.oldValue = func(context.Context) (*PlatformIntegration, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PlatformIntegrationMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PlatformIntegrationMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of PlatformIntegration entities.
func (m *PlatformIntegrationMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PlatformIntegrationMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PlatformIntegrationMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().PlatformIntegration.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *PlatformIntegrationMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *PlatformIntegrationMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *PlatformIntegrationMutation) ResetUserID() {
	m.user_id = nil
}

// SetWorkspaceID sets the "workspace_id" field.
func (m *PlatformIntegrationMutation) SetWorkspaceID(s string) {
	m.workspace_id = &s
}

// WorkspaceID returns the value of the "workspace_id" field in the mutation.
func (m *PlatformIntegrationMutation) WorkspaceID() (r string, exists bool) {
	v := m.workspace_id
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkspaceID returns the old "workspace_id" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldWorkspaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkspaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkspaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkspaceID: %w", err)
	}
	return oldValue.WorkspaceID, nil
}

// ClearWorkspaceID clears the value of the "workspace_id" field.
func (m *PlatformIntegrationMutation) ClearWorkspaceID() {
	m.workspace_id = nil
	m.clearedFields[platformintegration.FieldWorkspaceID] = struct{}{}
}

// WorkspaceIDCleared returns if the "workspace_id" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) WorkspaceIDCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldWorkspaceID]
	return ok
}

// ResetWorkspaceID resets all changes to the "workspace_id" field.
func (m *PlatformIntegrationMutation) ResetWorkspaceID() {
	m.workspace_id = nil
	delete(m.clearedFields, platformintegration.FieldWorkspaceID)
}

// SetPlatform sets the "platform" field.
func (m *PlatformIntegrationMutation) SetPlatform(pl platformintegration.Platform) {
	m.platform = &pl
}

// Platform returns the value of the "platform" field in the mutation.
func (m *PlatformIntegrationMutation) Platform() (r platformintegration.Platform, exists bool) {
	v := m.platform
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatform returns the old "platform" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatform(ctx context.Context) (v platformintegration.Platform, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatform is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatform requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatform: %w", err)
	}
	return oldValue.Platform, nil
}

// ResetPlatform resets all changes to the "platform" field.
func (m *PlatformIntegrationMutation) ResetPlatform() {
	m.platform = nil
}

// SetPlatformUserID sets the "platform_user_id" field.
func (m *PlatformIntegrationMutation) SetPlatformUserID(s string) {
	m.platform_user_id = &s
}

// PlatformUserID returns the value of the "platform_user_id" field in the mutation.
func (m *PlatformIntegrationMutation) PlatformUserID() (r string, exists bool) {
	v := m.platform_user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformUserID returns the old "platform_user_id" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatformUserID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformUserID: %w", err)
	}
	return oldValue.PlatformUserID, nil
}

// ResetPlatformUserID resets all changes to the "platform_user_id" field.
func (m *PlatformIntegrationMutation) ResetPlatformUserID() {
	m.platform_user_id = nil
}

// SetPlatformUsername sets the "platform_username" field.
func (m *PlatformIntegrationMutation) SetPlatformUsername(s string) {
	m.platform_username = &s
}

// PlatformUsername returns the value of the "platform_username" field in the mutation.
func (m *PlatformIntegrationMutation) PlatformUsername() (r string, exists bool) {
	v := m.platform_username
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformUsername returns the old "platform_username" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatformUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformUsername: %w", err)
	}
	return oldValue.PlatformUsername, nil
}

// ClearPlatformUsername clears the value of the "platform_username" field.
func (m *PlatformIntegrationMutation) ClearPlatformUsername() {
	m.platform_username = nil
	m.clearedFields[platformintegration.FieldPlatformUsername] = struct{}{}
}

// PlatformUsernameCleared returns if the "platform_username" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) PlatformUsernameCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldPlatformUsername]
	return ok
}

// ResetPlatformUsername resets all changes to the "platform_username" field.
func (m *PlatformIntegrationMutation) ResetPlatformUsername() {
	m.platform_username = nil
	delete(m.clearedFields, platformintegration.FieldPlatformUsername)
}

// SetPlatformDisplayName sets the "platform_display_name" field.
func (m *PlatformIntegrationMutation) SetPlatformDisplayName(s string) {
	m.platform_display_name = &s
}

// PlatformDisplayName returns the value of the "platform_display_name" field in the mutation.
func (m *PlatformIntegrationMutation) PlatformDisplayName() (r string, exists bool) {
	v := m.platform_display_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformDisplayName returns the old "platform_display_name" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatformDisplayName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformDisplayName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformDisplayName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformDisplayName: %w", err)
	}
	return oldValue.PlatformDisplayName, nil
}

// ClearPlatformDisplayName clears the value of the "platform_display_name" field.
func (m *PlatformIntegrationMutation) ClearPlatformDisplayName() {
	m.platform_display_name = nil
	m.clearedFields[platformintegration.FieldPlatformDisplayName] = struct{}{}
}

// PlatformDisplayNameCleared returns if the "platform_display_name" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) PlatformDisplayNameCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldPlatformDisplayName]
	return ok
}

// ResetPlatformDisplayName resets all changes to the "platform_display_name" field.
func (m *PlatformIntegrationMutation) ResetPlatformDisplayName() {
	m.platform_display_name = nil
	delete(m.clearedFields, platformintegration.FieldPlatformDisplayName)
}

// SetPlatformAvatarURL sets the "platform_avatar_url" field.
func (m *PlatformIntegrationMutation) SetPlatformAvatarURL(s string) {
	m.platform_avatar_url = &s
}

// PlatformAvatarURL returns the value of the "platform_avatar_url" field in the mutation.
func (m *PlatformIntegrationMutation) PlatformAvatarURL() (r string, exists bool) {
	v := m.platform_avatar_url
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformAvatarURL returns the old "platform_avatar_url" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatformAvatarURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformAvatarURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformAvatarURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformAvatarURL: %w", err)
	}
	return oldValue.PlatformAvatarURL, nil
}

// ClearPlatformAvatarURL clears the value of the "platform_avatar_url" field.
func (m *PlatformIntegrationMutation) ClearPlatformAvatarURL() {
	m.platform_avatar_url = nil
	m.clearedFields[platformintegration.FieldPlatformAvatarURL] = struct{}{}
}

// PlatformAvatarURLCleared returns if the "platform_avatar_url" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) PlatformAvatarURLCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldPlatformAvatarURL]
	return ok
}

// ResetPlatformAvatarURL resets all changes to the "platform_avatar_url" field.
func (m *PlatformIntegrationMutation) ResetPlatformAvatarURL() {
	m.platform_avatar_url = nil
	delete(m.clearedFields, platformintegration.FieldPlatformAvatarURL)
}

// SetAccessToken sets the "access_token" field.
func (m *PlatformIntegrationMutation) SetAccessToken(s string) {
	m.access_token = &s
}

// AccessToken returns the value of the "access_token" field in the mutation.
func (m *PlatformIntegrationMutation) AccessToken() (r string, exists bool) {
	v := m.access_token
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessToken returns the old "access_token" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldAccessToken(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessToken is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessToken requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessToken: %w", err)
	}
	return oldValue.AccessToken, nil
}

// ResetAccessToken resets all changes to the "access_token" field.
func (m *PlatformIntegrationMutation) ResetAccessToken() {
	m.access_token = nil
}

// SetRefreshToken sets the "refresh_token" field.
func (m *PlatformIntegrationMutation) SetRefreshToken(s string) {
	m.refresh_token = &s
}

// RefreshToken returns the value of the "refresh_token" field in the mutation.
func (m *PlatformIntegrationMutation) RefreshToken() (r string, exists bool) {
	v := m.refresh_token
	if v == nil {
		return
	}
	return *v, true
}

// OldRefreshToken returns the old "refresh_token" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldRefreshToken(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRefreshToken is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRefreshToken requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRefreshToken: %w", err)
	}
	return oldValue.RefreshToken, nil
}

// ClearRefreshToken clears the value of the "refresh_token" field.
func (m *PlatformIntegrationMutation) ClearRefreshToken() {
	m.refresh_token = nil
	m.clearedFields[platformintegration.FieldRefreshToken] = struct{}{}
}

// RefreshTokenCleared returns if the "refresh_token" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) RefreshTokenCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldRefreshToken]
	return ok
}

// ResetRefreshToken resets all changes to the "refresh_token" field.
func (m *PlatformIntegrationMutation) ResetRefreshToken() {
	m.refresh_token = nil
	delete(m.clearedFields, platformintegration.FieldRefreshToken)
}

// SetTokenExpiresAt sets the "token_expires_at" field.
func (m *PlatformIntegrationMutation) SetTokenExpiresAt(t time.Time) {
	m.token_expires_at = &t
}

// TokenExpiresAt returns the value of the "token_expires_at" field in the mutation.
func (m *PlatformIntegrationMutation) TokenExpiresAt() (r time.Time, exists bool) {
	v := m.token_expires_at
	if v == nil {
		return
	}
	return *v, true
}

// OldTokenExpiresAt returns the old "token_expires_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldTokenExpiresAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTokenExpiresAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTokenExpiresAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTokenExpiresAt: %w", err)
	}
	return oldValue.TokenExpiresAt, nil
}

// ClearTokenExpiresAt clears the value of the "token_expires_at" field.
func (m *PlatformIntegrationMutation) ClearTokenExpiresAt() {
	m.token_expires_at = nil
	m.clearedFields[platformintegration.FieldTokenExpiresAt] = struct{}{}
}

// TokenExpiresAtCleared returns if the "token_expires_at" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) TokenExpiresAtCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldTokenExpiresAt]
	return ok
}

// ResetTokenExpiresAt resets all changes to the "token_expires_at" field.
func (m *PlatformIntegrationMutation) ResetTokenExpiresAt() {
	m.token_expires_at = nil
	delete(m.clearedFields, platformintegration.FieldTokenExpiresAt)
}

// SetScopes sets the "scopes" field.
func (m *PlatformIntegrationMutation) SetScopes(s []string) {
	m.scopes = &s
	m.appendscopes = nil
}

// Scopes returns the value of the "scopes" field in the mutation.
func (m *PlatformIntegrationMutation) Scopes() (r []string, exists bool) {
	v := m.scopes
	if v == nil {
		return
	}
	return *v, true
}

// OldScopes returns the old "scopes" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldScopes(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldScopes is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldScopes requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldScopes: %w", err)
	}
	return oldValue.Scopes, nil
}

// AppendScopes adds s to the "scopes" field.
func (m *PlatformIntegrationMutation) AppendScopes(s []string) {
	m.appendscopes = append(m.appendscopes, s...)
}

// AppendedScopes returns the list of values that were appended to the "scopes" field in this mutation.
func (m *PlatformIntegrationMutation) AppendedScopes() ([]string, bool) {
	if len(m.appendscopes) == 0 {
		return nil, false
	}
	return m.appendscopes, true
}

// ClearScopes clears the value of the "scopes" field.
func (m *PlatformIntegrationMutation) ClearScopes() {
	m.scopes = nil
	m.appendscopes = nil
	m.clearedFields[platformintegration.FieldScopes] = struct{}{}
}

// ScopesCleared returns if the "scopes" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) ScopesCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldScopes]
	return ok
}

// ResetScopes resets all changes to the "scopes" field.
func (m *PlatformIntegrationMutation) ResetScopes() {
	m.scopes = nil
	m.appendscopes = nil
	delete(m.clearedFields, platformintegration.FieldScopes)
}

// SetPlatformData sets the "platform_data" field.
func (m *PlatformIntegrationMutation) SetPlatformData(value map[string]interface{}) {
	m.platform_data = &value
}

// PlatformData returns the value of the "platform_data" field in the mutation.
func (m *PlatformIntegrationMutation) PlatformData() (r map[string]interface{}, exists bool) {
	v := m.platform_data
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformData returns the old "platform_data" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldPlatformData(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformData: %w", err)
	}
	return oldValue.PlatformData, nil
}

// ClearPlatformData clears the value of the "platform_data" field.
func (m *PlatformIntegrationMutation) ClearPlatformData() {
	m.platform_data = nil
	m.clearedFields[platformintegration.FieldPlatformData] = struct{}{}
}

// PlatformDataCleared returns if the "platform_data" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) PlatformDataCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldPlatformData]
	return ok
}

// ResetPlatformData resets all changes to the "platform_data" field.
func (m *PlatformIntegrationMutation) ResetPlatformData() {
	m.platform_data = nil
	delete(m.clearedFields, platformintegration.FieldPlatformData)
}

// SetStatus sets the "status" field.
func (m *PlatformIntegrationMutation) SetStatus(pl platformintegration.Status) {
	m.status = &pl
}

// Status returns the value of the "status" field in the mutation.
func (m *PlatformIntegrationMutation) Status() (r platformintegration.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldStatus(ctx context.Context) (v platformintegration.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *PlatformIntegrationMutation) ResetStatus() {
	m.status = nil
}

// SetErrorMessage sets the "error_message" field.
func (m *PlatformIntegrationMutation) SetErrorMessage(s string) {
	m.error_message = &s
}

// ErrorMessage returns the value of the "error_message" field in the mutation.
func (m *PlatformIntegrationMutation) ErrorMessage() (r string, exists bool) {
	v := m.error_message
	if v == nil {
		return
	}
	return *v, true
}

// OldErrorMessage returns the old "error_message" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldErrorMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldErrorMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldErrorMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldErrorMessage: %w", err)
	}
	return oldValue.ErrorMessage, nil
}

// ClearErrorMessage clears the value of the "error_message" field.
func (m *PlatformIntegrationMutation) ClearErrorMessage() {
	m.error_message = nil
	m.clearedFields[platformintegration.FieldErrorMessage] = struct{}{}
}

// ErrorMessageCleared returns if the "error_message" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) ErrorMessageCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldErrorMessage]
	return ok
}

// ResetErrorMessage resets all changes to the "error_message" field.
func (m *PlatformIntegrationMutation) ResetErrorMessage() {
	m.error_message = nil
	delete(m.clearedFields, platformintegration.FieldErrorMessage)
}

// SetLastSyncAt sets the "last_sync_at" field.
func (m *PlatformIntegrationMutation) SetLastSyncAt(t time.Time) {
	m.last_sync_at = &t
}

// LastSyncAt returns the value of the "last_sync_at" field in the mutation.
func (m *PlatformIntegrationMutation) LastSyncAt() (r time.Time, exists bool) {
	v := m.last_sync_at
	if v == nil {
		return
	}
	return *v, true
}

// OldLastSyncAt returns the old "last_sync_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldLastSyncAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastSyncAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastSyncAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastSyncAt: %w", err)
	}
	return oldValue.LastSyncAt, nil
}

// ClearLastSyncAt clears the value of the "last_sync_at" field.
func (m *PlatformIntegrationMutation) ClearLastSyncAt() {
	m.last_sync_at = nil
	m.clearedFields[platformintegration.FieldLastSyncAt] = struct{}{}
}

// LastSyncAtCleared returns if the "last_sync_at" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) LastSyncAtCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldLastSyncAt]
	return ok
}

// ResetLastSyncAt resets all changes to the "last_sync_at" field.
func (m *PlatformIntegrationMutation) ResetLastSyncAt() {
	m.last_sync_at = nil
	delete(m.clearedFields, platformintegration.FieldLastSyncAt)
}

// SetLastPostAt sets the "last_post_at" field.
func (m *PlatformIntegrationMutation) SetLastPostAt(t time.Time) {
	m.last_post_at = &t
}

// LastPostAt returns the value of the "last_post_at" field in the mutation.
func (m *PlatformIntegrationMutation) LastPostAt() (r time.Time, exists bool) {
	v := m.last_post_at
	if v == nil {
		return
	}
	return *v, true
}

// OldLastPostAt returns the old "last_post_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldLastPostAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastPostAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastPostAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastPostAt: %w", err)
	}
	return oldValue.LastPostAt, nil
}

// ClearLastPostAt clears the value of the "last_post_at" field.
func (m *PlatformIntegrationMutation) ClearLastPostAt() {
	m.last_post_at = nil
	m.clearedFields[platformintegration.FieldLastPostAt] = struct{}{}
}

// LastPostAtCleared returns if the "last_post_at" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) LastPostAtCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldLastPostAt]
	return ok
}

// ResetLastPostAt resets all changes to the "last_post_at" field.
func (m *PlatformIntegrationMutation) ResetLastPostAt() {
	m.last_post_at = nil
	delete(m.clearedFields, platformintegration.FieldLastPostAt)
}

// SetCapabilities sets the "capabilities" field.
func (m *PlatformIntegrationMutation) SetCapabilities(s []string) {
	m.capabilities = &s
	m.appendcapabilities = nil
}

// Capabilities returns the value of the "capabilities" field in the mutation.
func (m *PlatformIntegrationMutation) Capabilities() (r []string, exists bool) {
	v := m.capabilities
	if v == nil {
		return
	}
	return *v, true
}

// OldCapabilities returns the old "capabilities" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldCapabilities(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCapabilities is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCapabilities requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCapabilities: %w", err)
	}
	return oldValue.Capabilities, nil
}

// AppendCapabilities adds s to the "capabilities" field.
func (m *PlatformIntegrationMutation) AppendCapabilities(s []string) {
	m.appendcapabilities = append(m.appendcapabilities, s...)
}

// AppendedCapabilities returns the list of values that were appended to the "capabilities" field in this mutation.
func (m *PlatformIntegrationMutation) AppendedCapabilities() ([]string, bool) {
	if len(m.appendcapabilities) == 0 {
		return nil, false
	}
	return m.appendcapabilities, true
}

// ClearCapabilities clears the value of the "capabilities" field.
func (m *PlatformIntegrationMutation) ClearCapabilities() {
	m.capabilities = nil
	m.appendcapabilities = nil
	m.clearedFields[platformintegration.FieldCapabilities] = struct{}{}
}

// CapabilitiesCleared returns if the "capabilities" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) CapabilitiesCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldCapabilities]
	return ok
}

// ResetCapabilities resets all changes to the "capabilities" field.
func (m *PlatformIntegrationMutation) ResetCapabilities() {
	m.capabilities = nil
	m.appendcapabilities = nil
	delete(m.clearedFields, platformintegration.FieldCapabilities)
}

// SetSettings sets the "settings" field.
func (m *PlatformIntegrationMutation) SetSettings(value map[string]interface{}) {
	m.settings = &value
}

// Settings returns the value of the "settings" field in the mutation.
func (m *PlatformIntegrationMutation) Settings() (r map[string]interface{}, exists bool) {
	v := m.settings
	if v == nil {
		return
	}
	return *v, true
}

// OldSettings returns the old "settings" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldSettings(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSettings is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSettings requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSettings: %w", err)
	}
	return oldValue.Settings, nil
}

// ClearSettings clears the value of the "settings" field.
func (m *PlatformIntegrationMutation) ClearSettings() {
	m.settings = nil
	m.clearedFields[platformintegration.FieldSettings] = struct{}{}
}

// SettingsCleared returns if the "settings" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) SettingsCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldSettings]
	return ok
}

// ResetSettings resets all changes to the "settings" field.
func (m *PlatformIntegrationMutation) ResetSettings() {
	m.settings = nil
	delete(m.clearedFields, platformintegration.FieldSettings)
}

// SetAutoPublish sets the "auto_publish" field.
func (m *PlatformIntegrationMutation) SetAutoPublish(b bool) {
	m.auto_publish = &b
}

// AutoPublish returns the value of the "auto_publish" field in the mutation.
func (m *PlatformIntegrationMutation) AutoPublish() (r bool, exists bool) {
	v := m.auto_publish
	if v == nil {
		return
	}
	return *v, true
}

// OldAutoPublish returns the old "auto_publish" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldAutoPublish(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAutoPublish is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAutoPublish requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAutoPublish: %w", err)
	}
	return oldValue.AutoPublish, nil
}

// ResetAutoPublish resets all changes to the "auto_publish" field.
func (m *PlatformIntegrationMutation) ResetAutoPublish() {
	m.auto_publish = nil
}

// SetIsPrimary sets the "is_primary" field.
func (m *PlatformIntegrationMutation) SetIsPrimary(b bool) {
	m.is_primary = &b
}

// IsPrimary returns the value of the "is_primary" field in the mutation.
func (m *PlatformIntegrationMutation) IsPrimary() (r bool, exists bool) {
	v := m.is_primary
	if v == nil {
		return
	}
	return *v, true
}

// OldIsPrimary returns the old "is_primary" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldIsPrimary(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsPrimary is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsPrimary requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsPrimary: %w", err)
	}
	return oldValue.IsPrimary, nil
}

// ResetIsPrimary resets all changes to the "is_primary" field.
func (m *PlatformIntegrationMutation) ResetIsPrimary() {
	m.is_primary = nil
}

// SetMetadata sets the "metadata" field.
func (m *PlatformIntegrationMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *PlatformIntegrationMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *PlatformIntegrationMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[platformintegration.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *PlatformIntegrationMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, platformintegration.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *PlatformIntegrationMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PlatformIntegrationMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PlatformIntegrationMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PlatformIntegrationMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PlatformIntegrationMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PlatformIntegrationMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *PlatformIntegrationMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *PlatformIntegrationMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the PlatformIntegration entity.
// If the PlatformIntegration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PlatformIntegrationMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *PlatformIntegrationMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[platformintegration.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *PlatformIntegrationMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[platformintegration.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *PlatformIntegrationMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, platformintegration.FieldDeletedAt)
}

// Where appends a list predicates to the PlatformIntegrationMutation builder.
func (m *PlatformIntegrationMutation) Where(ps ...predicate.PlatformIntegration) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PlatformIntegrationMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PlatformIntegrationMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.PlatformIntegration, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PlatformIntegrationMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PlatformIntegrationMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (PlatformIntegration).
func (m *PlatformIntegrationMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PlatformIntegrationMutation) Fields() []string {
	fields := make([]string, 0, 24)
	if m.user_id != nil {
		fields = append(fields, platformintegration.FieldUserID)
	}
	if m.workspace_id != nil {
		fields = append(fields, platformintegration.FieldWorkspaceID)
	}
	if m.platform != nil {
		fields = append(fields, platformintegration.FieldPlatform)
	}
	if m.platform_user_id != nil {
		fields = append(fields, platformintegration.FieldPlatformUserID)
	}
	if m.platform_username != nil {
		fields = append(fields, platformintegration.FieldPlatformUsername)
	}
	if m.platform_display_name != nil {
		fields = append(fields, platformintegration.FieldPlatformDisplayName)
	}
	if m.platform_avatar_url != nil {
		fields = append(fields, platformintegration.FieldPlatformAvatarURL)
	}
	if m.access_token != nil {
		fields = append(fields, platformintegration.FieldAccessToken)
	}
	if m.refresh_token != nil {
		fields = append(fields, platformintegration.FieldRefreshToken)
	}
	if m.token_expires_at != nil {
		fields = append(fields, platformintegration.FieldTokenExpiresAt)
	}
	if m.scopes != nil {
		fields = append(fields, platformintegration.FieldScopes)
	}
	if m.platform_data != nil {
		fields = append(fields, platformintegration.FieldPlatformData)
	}
	if m.status != nil {
		fields = append(fields, platformintegration.FieldStatus)
	}
	if m.error_message != nil {
		fields = append(fields, platformintegration.FieldErrorMessage)
	}
	if m.last_sync_at != nil {
		fields = append(fields, platformintegration.FieldLastSyncAt)
	}
	if m.last_post_at != nil {
		fields = append(fields, platformintegration.FieldLastPostAt)
	}
	if m.capabilities != nil {
		fields = append(fields, platformintegration.FieldCapabilities)
	}
	if m.settings != nil {
		fields = append(fields, platformintegration.FieldSettings)
	}
	if m.auto_publish != nil {
		fields = append(fields, platformintegration.FieldAutoPublish)
	}
	if m.is_primary != nil {
		fields = append(fields, platformintegration.FieldIsPrimary)
	}
	if m.metadata != nil {
		fields = append(fields, platformintegration.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, platformintegration.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, platformintegration.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, platformintegration.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PlatformIntegrationMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case platformintegration.FieldUserID:
		return m.UserID()
	case platformintegration.FieldWorkspaceID:
		return m.WorkspaceID()
	case platformintegration.FieldPlatform:
		return m.Platform()
	case platformintegration.FieldPlatformUserID:
		return m.PlatformUserID()
	case platformintegration.FieldPlatformUsername:
		return m.PlatformUsername()
	case platformintegration.FieldPlatformDisplayName:
		return m.PlatformDisplayName()
	case platformintegration.FieldPlatformAvatarURL:
		return m.PlatformAvatarURL()
	case platformintegration.FieldAccessToken:
		return m.AccessToken()
	case platformintegration.FieldRefreshToken:
		return m.RefreshToken()
	case platformintegration.FieldTokenExpiresAt:
		return m.TokenExpiresAt()
	case platformintegration.FieldScopes:
		return m.Scopes()
	case platformintegration.FieldPlatformData:
		return m.PlatformData()
	case platformintegration.FieldStatus:
		return m.Status()
	case platformintegration.FieldErrorMessage:
		return m.ErrorMessage()
	case platformintegration.FieldLastSyncAt:
		return m.LastSyncAt()
	case platformintegration.FieldLastPostAt:
		return m.LastPostAt()
	case platformintegration.FieldCapabilities:
		return m.Capabilities()
	case platformintegration.FieldSettings:
		return m.Settings()
	case platformintegration.FieldAutoPublish:
		return m.AutoPublish()
	case platformintegration.FieldIsPrimary:
		return m.IsPrimary()
	case platformintegration.FieldMetadata:
		return m.Metadata()
	case platformintegration.FieldCreatedAt:
		return m.CreatedAt()
	case platformintegration.FieldUpdatedAt:
		return m.UpdatedAt()
	case platformintegration.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PlatformIntegrationMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case platformintegration.FieldUserID:
		return m.OldUserID(ctx)
	case platformintegration.FieldWorkspaceID:
		return m.OldWorkspaceID(ctx)
	case platformintegration.FieldPlatform:
		return m.OldPlatform(ctx)
	case platformintegration.FieldPlatformUserID:
		return m.OldPlatformUserID(ctx)
	case platformintegration.FieldPlatformUsername:
		return m.OldPlatformUsername(ctx)
	case platformintegration.FieldPlatformDisplayName:
		return m.OldPlatformDisplayName(ctx)
	case platformintegration.FieldPlatformAvatarURL:
		return m.OldPlatformAvatarURL(ctx)
	case platformintegration.FieldAccessToken:
		return m.OldAccessToken(ctx)
	case platformintegration.FieldRefreshToken:
		return m.OldRefreshToken(ctx)
	case platformintegration.FieldTokenExpiresAt:
		return m.OldTokenExpiresAt(ctx)
	case platformintegration.FieldScopes:
		return m.OldScopes(ctx)
	case platformintegration.FieldPlatformData:
		return m.OldPlatformData(ctx)
	case platformintegration.FieldStatus:
		return m.OldStatus(ctx)
	case platformintegration.FieldErrorMessage:
		return m.OldErrorMessage(ctx)
	case platformintegration.FieldLastSyncAt:
		return m.OldLastSyncAt(ctx)
	case platformintegration.FieldLastPostAt:
		return m.OldLastPostAt(ctx)
	case platformintegration.FieldCapabilities:
		return m.OldCapabilities(ctx)
	case platformintegration.FieldSettings:
		return m.OldSettings(ctx)
	case platformintegration.FieldAutoPublish:
		return m.OldAutoPublish(ctx)
	case platformintegration.FieldIsPrimary:
		return m.OldIsPrimary(ctx)
	case platformintegration.FieldMetadata:
		return m.OldMetadata(ctx)
	case platformintegration.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case platformintegration.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case platformintegration.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown PlatformIntegration field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PlatformIntegrationMutation) SetField(name string, value ent.Value) error {
	switch name {
	case platformintegration.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case platformintegration.FieldWorkspaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkspaceID(v)
		return nil
	case platformintegration.FieldPlatform:
		v, ok := value.(platformintegration.Platform)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatform(v)
		return nil
	case platformintegration.FieldPlatformUserID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformUserID(v)
		return nil
	case platformintegration.FieldPlatformUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformUsername(v)
		return nil
	case platformintegration.FieldPlatformDisplayName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformDisplayName(v)
		return nil
	case platformintegration.FieldPlatformAvatarURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformAvatarURL(v)
		return nil
	case platformintegration.FieldAccessToken:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessToken(v)
		return nil
	case platformintegration.FieldRefreshToken:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRefreshToken(v)
		return nil
	case platformintegration.FieldTokenExpiresAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTokenExpiresAt(v)
		return nil
	case platformintegration.FieldScopes:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetScopes(v)
		return nil
	case platformintegration.FieldPlatformData:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformData(v)
		return nil
	case platformintegration.FieldStatus:
		v, ok := value.(platformintegration.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case platformintegration.FieldErrorMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetErrorMessage(v)
		return nil
	case platformintegration.FieldLastSyncAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastSyncAt(v)
		return nil
	case platformintegration.FieldLastPostAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastPostAt(v)
		return nil
	case platformintegration.FieldCapabilities:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCapabilities(v)
		return nil
	case platformintegration.FieldSettings:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSettings(v)
		return nil
	case platformintegration.FieldAutoPublish:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAutoPublish(v)
		return nil
	case platformintegration.FieldIsPrimary:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsPrimary(v)
		return nil
	case platformintegration.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case platformintegration.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case platformintegration.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case platformintegration.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown PlatformIntegration field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PlatformIntegrationMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PlatformIntegrationMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PlatformIntegrationMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown PlatformIntegration numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PlatformIntegrationMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(platformintegration.FieldWorkspaceID) {
		fields = append(fields, platformintegration.FieldWorkspaceID)
	}
	if m.FieldCleared(platformintegration.FieldPlatformUsername) {
		fields = append(fields, platformintegration.FieldPlatformUsername)
	}
	if m.FieldCleared(platformintegration.FieldPlatformDisplayName) {
		fields = append(fields, platformintegration.FieldPlatformDisplayName)
	}
	if m.FieldCleared(platformintegration.FieldPlatformAvatarURL) {
		fields = append(fields, platformintegration.FieldPlatformAvatarURL)
	}
	if m.FieldCleared(platformintegration.FieldRefreshToken) {
		fields = append(fields, platformintegration.FieldRefreshToken)
	}
	if m.FieldCleared(platformintegration.FieldTokenExpiresAt) {
		fields = append(fields, platformintegration.FieldTokenExpiresAt)
	}
	if m.FieldCleared(platformintegration.FieldScopes) {
		fields = append(fields, platformintegration.FieldScopes)
	}
	if m.FieldCleared(platformintegration.FieldPlatformData) {
		fields = append(fields, platformintegration.FieldPlatformData)
	}
	if m.FieldCleared(platformintegration.FieldErrorMessage) {
		fields = append(fields, platformintegration.FieldErrorMessage)
	}
	if m.FieldCleared(platformintegration.FieldLastSyncAt) {
		fields = append(fields, platformintegration.FieldLastSyncAt)
	}
	if m.FieldCleared(platformintegration.FieldLastPostAt) {
		fields = append(fields, platformintegration.FieldLastPostAt)
	}
	if m.FieldCleared(platformintegration.FieldCapabilities) {
		fields = append(fields, platformintegration.FieldCapabilities)
	}
	if m.FieldCleared(platformintegration.FieldSettings) {
		fields = append(fields, platformintegration.FieldSettings)
	}
	if m.FieldCleared(platformintegration.FieldMetadata) {
		fields = append(fields, platformintegration.FieldMetadata)
	}
	if m.FieldCleared(platformintegration.FieldDeletedAt) {
		fields = append(fields, platformintegration.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PlatformIntegrationMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PlatformIntegrationMutation) ClearField(name string) error {
	switch name {
	case platformintegration.FieldWorkspaceID:
		m.ClearWorkspaceID()
		return nil
	case platformintegration.FieldPlatformUsername:
		m.ClearPlatformUsername()
		return nil
	case platformintegration.FieldPlatformDisplayName:
		m.ClearPlatformDisplayName()
		return nil
	case platformintegration.FieldPlatformAvatarURL:
		m.ClearPlatformAvatarURL()
		return nil
	case platformintegration.FieldRefreshToken:
		m.ClearRefreshToken()
		return nil
	case platformintegration.FieldTokenExpiresAt:
		m.ClearTokenExpiresAt()
		return nil
	case platformintegration.FieldScopes:
		m.ClearScopes()
		return nil
	case platformintegration.FieldPlatformData:
		m.ClearPlatformData()
		return nil
	case platformintegration.FieldErrorMessage:
		m.ClearErrorMessage()
		return nil
	case platformintegration.FieldLastSyncAt:
		m.ClearLastSyncAt()
		return nil
	case platformintegration.FieldLastPostAt:
		m.ClearLastPostAt()
		return nil
	case platformintegration.FieldCapabilities:
		m.ClearCapabilities()
		return nil
	case platformintegration.FieldSettings:
		m.ClearSettings()
		return nil
	case platformintegration.FieldMetadata:
		m.ClearMetadata()
		return nil
	case platformintegration.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown PlatformIntegration nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PlatformIntegrationMutation) ResetField(name string) error {
	switch name {
	case platformintegration.FieldUserID:
		m.ResetUserID()
		return nil
	case platformintegration.FieldWorkspaceID:
		m.ResetWorkspaceID()
		return nil
	case platformintegration.FieldPlatform:
		m.ResetPlatform()
		return nil
	case platformintegration.FieldPlatformUserID:
		m.ResetPlatformUserID()
		return nil
	case platformintegration.FieldPlatformUsername:
		m.ResetPlatformUsername()
		return nil
	case platformintegration.FieldPlatformDisplayName:
		m.ResetPlatformDisplayName()
		return nil
	case platformintegration.FieldPlatformAvatarURL:
		m.ResetPlatformAvatarURL()
		return nil
	case platformintegration.FieldAccessToken:
		m.ResetAccessToken()
		return nil
	case platformintegration.FieldRefreshToken:
		m.ResetRefreshToken()
		return nil
	case platformintegration.FieldTokenExpiresAt:
		m.ResetTokenExpiresAt()
		return nil
	case platformintegration.FieldScopes:
		m.ResetScopes()
		return nil
	case platformintegration.FieldPlatformData:
		m.ResetPlatformData()
		return nil
	case platformintegration.FieldStatus:
		m.ResetStatus()
		return nil
	case platformintegration.FieldErrorMessage:
		m.ResetErrorMessage()
		return nil
	case platformintegration.FieldLastSyncAt:
		m.ResetLastSyncAt()
		return nil
	case platformintegration.FieldLastPostAt:
		m.ResetLastPostAt()
		return nil
	case platformintegration.FieldCapabilities:
		m.ResetCapabilities()
		return nil
	case platformintegration.FieldSettings:
		m.ResetSettings()
		return nil
	case platformintegration.FieldAutoPublish:
		m.ResetAutoPublish()
		return nil
	case platformintegration.FieldIsPrimary:
		m.ResetIsPrimary()
		return nil
	case platformintegration.FieldMetadata:
		m.ResetMetadata()
		return nil
	case platformintegration.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case platformintegration.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case platformintegration.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown PlatformIntegration field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PlatformIntegrationMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PlatformIntegrationMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PlatformIntegrationMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PlatformIntegrationMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PlatformIntegrationMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PlatformIntegrationMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PlatformIntegrationMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown PlatformIntegration unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PlatformIntegrationMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown PlatformIntegration edge %s", name)
}

// PublishedPostMutation represents an operation that mutates the PublishedPost nodes in the graph.
type PublishedPostMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	user_id             *uuid.UUID
	post_id             *string
	integration_id      *string
	platform            *publishedpost.Platform
	platform_post_id    *string
	platform_post_url   *string
	content             *string
	media_urls          *[]string
	appendmedia_urls    []string
	hashtags            *[]string
	appendhashtags      []string
	status              *publishedpost.Status
	error_message       *string
	scheduled_at        *time.Time
	published_at        *time.Time
	platform_response   *map[string]interface{}
	analytics           *map[string]interface{}
	last_analytics_sync *time.Time
	retry_count         *int
	addretry_count      *int
	next_retry_at       *time.Time
	publish_options     *map[string]interface{}
	metadata            *map[string]interface{}
	created_at          *time.Time
	updated_at          *time.Time
	deleted_at          *time.Time
	clearedFields       map[string]struct{}
	done                bool
	oldValue            func(context.Context) (*PublishedPost, error)
	predicates          []predicate.PublishedPost
}

var _ ent.Mutation = (*PublishedPostMutation)(nil)

// publishedpostOption allows management of the mutation configuration using functional options.
type publishedpostOption func(*PublishedPostMutation)

// newPublishedPostMutation creates new mutation for the PublishedPost entity.
func newPublishedPostMutation(c config, op Op, opts ...publishedpostOption) *PublishedPostMutation {
	m := &PublishedPostMutation{
		config:        c,
		op:            op,
		typ:           TypePublishedPost,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPublishedPostID sets the ID field of the mutation.
func withPublishedPostID(id uuid.UUID) publishedpostOption {
	return func(m *PublishedPostMutation) {
		var (
			err   error
			once  sync.Once
			value *PublishedPost
		)
		m.oldValue = func(ctx context.Context) (*PublishedPost, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().PublishedPost.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPublishedPost sets the old PublishedPost of the mutation.
func withPublishedPost(node *PublishedPost) publishedpostOption {
	return func(m *PublishedPostMutation) {
		m.oldValue = func(context.Context) (*PublishedPost, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PublishedPostMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PublishedPostMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of PublishedPost entities.
func (m *PublishedPostMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PublishedPostMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PublishedPostMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().PublishedPost.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *PublishedPostMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *PublishedPostMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *PublishedPostMutation) ResetUserID() {
	m.user_id = nil
}

// SetPostID sets the "post_id" field.
func (m *PublishedPostMutation) SetPostID(s string) {
	m.post_id = &s
}

// PostID returns the value of the "post_id" field in the mutation.
func (m *PublishedPostMutation) PostID() (r string, exists bool) {
	v := m.post_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPostID returns the old "post_id" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPostID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPostID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPostID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPostID: %w", err)
	}
	return oldValue.PostID, nil
}

// ResetPostID resets all changes to the "post_id" field.
func (m *PublishedPostMutation) ResetPostID() {
	m.post_id = nil
}

// SetIntegrationID sets the "integration_id" field.
func (m *PublishedPostMutation) SetIntegrationID(s string) {
	m.integration_id = &s
}

// IntegrationID returns the value of the "integration_id" field in the mutation.
func (m *PublishedPostMutation) IntegrationID() (r string, exists bool) {
	v := m.integration_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIntegrationID returns the old "integration_id" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldIntegrationID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIntegrationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIntegrationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIntegrationID: %w", err)
	}
	return oldValue.IntegrationID, nil
}

// ResetIntegrationID resets all changes to the "integration_id" field.
func (m *PublishedPostMutation) ResetIntegrationID() {
	m.integration_id = nil
}

// SetPlatform sets the "platform" field.
func (m *PublishedPostMutation) SetPlatform(pu publishedpost.Platform) {
	m.platform = &pu
}

// Platform returns the value of the "platform" field in the mutation.
func (m *PublishedPostMutation) Platform() (r publishedpost.Platform, exists bool) {
	v := m.platform
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatform returns the old "platform" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPlatform(ctx context.Context) (v publishedpost.Platform, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatform is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatform requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatform: %w", err)
	}
	return oldValue.Platform, nil
}

// ResetPlatform resets all changes to the "platform" field.
func (m *PublishedPostMutation) ResetPlatform() {
	m.platform = nil
}

// SetPlatformPostID sets the "platform_post_id" field.
func (m *PublishedPostMutation) SetPlatformPostID(s string) {
	m.platform_post_id = &s
}

// PlatformPostID returns the value of the "platform_post_id" field in the mutation.
func (m *PublishedPostMutation) PlatformPostID() (r string, exists bool) {
	v := m.platform_post_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformPostID returns the old "platform_post_id" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPlatformPostID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformPostID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformPostID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformPostID: %w", err)
	}
	return oldValue.PlatformPostID, nil
}

// ResetPlatformPostID resets all changes to the "platform_post_id" field.
func (m *PublishedPostMutation) ResetPlatformPostID() {
	m.platform_post_id = nil
}

// SetPlatformPostURL sets the "platform_post_url" field.
func (m *PublishedPostMutation) SetPlatformPostURL(s string) {
	m.platform_post_url = &s
}

// PlatformPostURL returns the value of the "platform_post_url" field in the mutation.
func (m *PublishedPostMutation) PlatformPostURL() (r string, exists bool) {
	v := m.platform_post_url
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformPostURL returns the old "platform_post_url" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPlatformPostURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformPostURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformPostURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformPostURL: %w", err)
	}
	return oldValue.PlatformPostURL, nil
}

// ClearPlatformPostURL clears the value of the "platform_post_url" field.
func (m *PublishedPostMutation) ClearPlatformPostURL() {
	m.platform_post_url = nil
	m.clearedFields[publishedpost.FieldPlatformPostURL] = struct{}{}
}

// PlatformPostURLCleared returns if the "platform_post_url" field was cleared in this mutation.
func (m *PublishedPostMutation) PlatformPostURLCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldPlatformPostURL]
	return ok
}

// ResetPlatformPostURL resets all changes to the "platform_post_url" field.
func (m *PublishedPostMutation) ResetPlatformPostURL() {
	m.platform_post_url = nil
	delete(m.clearedFields, publishedpost.FieldPlatformPostURL)
}

// SetContent sets the "content" field.
func (m *PublishedPostMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *PublishedPostMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *PublishedPostMutation) ResetContent() {
	m.content = nil
}

// SetMediaUrls sets the "media_urls" field.
func (m *PublishedPostMutation) SetMediaUrls(s []string) {
	m.media_urls = &s
	m.appendmedia_urls = nil
}

// MediaUrls returns the value of the "media_urls" field in the mutation.
func (m *PublishedPostMutation) MediaUrls() (r []string, exists bool) {
	v := m.media_urls
	if v == nil {
		return
	}
	return *v, true
}

// OldMediaUrls returns the old "media_urls" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldMediaUrls(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMediaUrls is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMediaUrls requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMediaUrls: %w", err)
	}
	return oldValue.MediaUrls, nil
}

// AppendMediaUrls adds s to the "media_urls" field.
func (m *PublishedPostMutation) AppendMediaUrls(s []string) {
	m.appendmedia_urls = append(m.appendmedia_urls, s...)
}

// AppendedMediaUrls returns the list of values that were appended to the "media_urls" field in this mutation.
func (m *PublishedPostMutation) AppendedMediaUrls() ([]string, bool) {
	if len(m.appendmedia_urls) == 0 {
		return nil, false
	}
	return m.appendmedia_urls, true
}

// ClearMediaUrls clears the value of the "media_urls" field.
func (m *PublishedPostMutation) ClearMediaUrls() {
	m.media_urls = nil
	m.appendmedia_urls = nil
	m.clearedFields[publishedpost.FieldMediaUrls] = struct{}{}
}

// MediaUrlsCleared returns if the "media_urls" field was cleared in this mutation.
func (m *PublishedPostMutation) MediaUrlsCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldMediaUrls]
	return ok
}

// ResetMediaUrls resets all changes to the "media_urls" field.
func (m *PublishedPostMutation) ResetMediaUrls() {
	m.media_urls = nil
	m.appendmedia_urls = nil
	delete(m.clearedFields, publishedpost.FieldMediaUrls)
}

// SetHashtags sets the "hashtags" field.
func (m *PublishedPostMutation) SetHashtags(s []string) {
	m.hashtags = &s
	m.appendhashtags = nil
}

// Hashtags returns the value of the "hashtags" field in the mutation.
func (m *PublishedPostMutation) Hashtags() (r []string, exists bool) {
	v := m.hashtags
	if v == nil {
		return
	}
	return *v, true
}

// OldHashtags returns the old "hashtags" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldHashtags(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHashtags is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHashtags requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHashtags: %w", err)
	}
	return oldValue.Hashtags, nil
}

// AppendHashtags adds s to the "hashtags" field.
func (m *PublishedPostMutation) AppendHashtags(s []string) {
	m.appendhashtags = append(m.appendhashtags, s...)
}

// AppendedHashtags returns the list of values that were appended to the "hashtags" field in this mutation.
func (m *PublishedPostMutation) AppendedHashtags() ([]string, bool) {
	if len(m.appendhashtags) == 0 {
		return nil, false
	}
	return m.appendhashtags, true
}

// ClearHashtags clears the value of the "hashtags" field.
func (m *PublishedPostMutation) ClearHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	m.clearedFields[publishedpost.FieldHashtags] = struct{}{}
}

// HashtagsCleared returns if the "hashtags" field was cleared in this mutation.
func (m *PublishedPostMutation) HashtagsCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldHashtags]
	return ok
}

// ResetHashtags resets all changes to the "hashtags" field.
func (m *PublishedPostMutation) ResetHashtags() {
	m.hashtags = nil
	m.appendhashtags = nil
	delete(m.clearedFields, publishedpost.FieldHashtags)
}

// SetStatus sets the "status" field.
func (m *PublishedPostMutation) SetStatus(pu publishedpost.Status) {
	m.status = &pu
}

// Status returns the value of the "status" field in the mutation.
func (m *PublishedPostMutation) Status() (r publishedpost.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldStatus(ctx context.Context) (v publishedpost.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *PublishedPostMutation) ResetStatus() {
	m.status = nil
}

// SetErrorMessage sets the "error_message" field.
func (m *PublishedPostMutation) SetErrorMessage(s string) {
	m.error_message = &s
}

// ErrorMessage returns the value of the "error_message" field in the mutation.
func (m *PublishedPostMutation) ErrorMessage() (r string, exists bool) {
	v := m.error_message
	if v == nil {
		return
	}
	return *v, true
}

// OldErrorMessage returns the old "error_message" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldErrorMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldErrorMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldErrorMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldErrorMessage: %w", err)
	}
	return oldValue.ErrorMessage, nil
}

// ClearErrorMessage clears the value of the "error_message" field.
func (m *PublishedPostMutation) ClearErrorMessage() {
	m.error_message = nil
	m.clearedFields[publishedpost.FieldErrorMessage] = struct{}{}
}

// ErrorMessageCleared returns if the "error_message" field was cleared in this mutation.
func (m *PublishedPostMutation) ErrorMessageCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldErrorMessage]
	return ok
}

// ResetErrorMessage resets all changes to the "error_message" field.
func (m *PublishedPostMutation) ResetErrorMessage() {
	m.error_message = nil
	delete(m.clearedFields, publishedpost.FieldErrorMessage)
}

// SetScheduledAt sets the "scheduled_at" field.
func (m *PublishedPostMutation) SetScheduledAt(t time.Time) {
	m.scheduled_at = &t
}

// ScheduledAt returns the value of the "scheduled_at" field in the mutation.
func (m *PublishedPostMutation) ScheduledAt() (r time.Time, exists bool) {
	v := m.scheduled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldScheduledAt returns the old "scheduled_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldScheduledAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldScheduledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldScheduledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldScheduledAt: %w", err)
	}
	return oldValue.ScheduledAt, nil
}

// ClearScheduledAt clears the value of the "scheduled_at" field.
func (m *PublishedPostMutation) ClearScheduledAt() {
	m.scheduled_at = nil
	m.clearedFields[publishedpost.FieldScheduledAt] = struct{}{}
}

// ScheduledAtCleared returns if the "scheduled_at" field was cleared in this mutation.
func (m *PublishedPostMutation) ScheduledAtCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldScheduledAt]
	return ok
}

// ResetScheduledAt resets all changes to the "scheduled_at" field.
func (m *PublishedPostMutation) ResetScheduledAt() {
	m.scheduled_at = nil
	delete(m.clearedFields, publishedpost.FieldScheduledAt)
}

// SetPublishedAt sets the "published_at" field.
func (m *PublishedPostMutation) SetPublishedAt(t time.Time) {
	m.published_at = &t
}

// PublishedAt returns the value of the "published_at" field in the mutation.
func (m *PublishedPostMutation) PublishedAt() (r time.Time, exists bool) {
	v := m.published_at
	if v == nil {
		return
	}
	return *v, true
}

// OldPublishedAt returns the old "published_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPublishedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPublishedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPublishedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPublishedAt: %w", err)
	}
	return oldValue.PublishedAt, nil
}

// ClearPublishedAt clears the value of the "published_at" field.
func (m *PublishedPostMutation) ClearPublishedAt() {
	m.published_at = nil
	m.clearedFields[publishedpost.FieldPublishedAt] = struct{}{}
}

// PublishedAtCleared returns if the "published_at" field was cleared in this mutation.
func (m *PublishedPostMutation) PublishedAtCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldPublishedAt]
	return ok
}

// ResetPublishedAt resets all changes to the "published_at" field.
func (m *PublishedPostMutation) ResetPublishedAt() {
	m.published_at = nil
	delete(m.clearedFields, publishedpost.FieldPublishedAt)
}

// SetPlatformResponse sets the "platform_response" field.
func (m *PublishedPostMutation) SetPlatformResponse(value map[string]interface{}) {
	m.platform_response = &value
}

// PlatformResponse returns the value of the "platform_response" field in the mutation.
func (m *PublishedPostMutation) PlatformResponse() (r map[string]interface{}, exists bool) {
	v := m.platform_response
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatformResponse returns the old "platform_response" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPlatformResponse(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatformResponse is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatformResponse requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatformResponse: %w", err)
	}
	return oldValue.PlatformResponse, nil
}

// ClearPlatformResponse clears the value of the "platform_response" field.
func (m *PublishedPostMutation) ClearPlatformResponse() {
	m.platform_response = nil
	m.clearedFields[publishedpost.FieldPlatformResponse] = struct{}{}
}

// PlatformResponseCleared returns if the "platform_response" field was cleared in this mutation.
func (m *PublishedPostMutation) PlatformResponseCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldPlatformResponse]
	return ok
}

// ResetPlatformResponse resets all changes to the "platform_response" field.
func (m *PublishedPostMutation) ResetPlatformResponse() {
	m.platform_response = nil
	delete(m.clearedFields, publishedpost.FieldPlatformResponse)
}

// SetAnalytics sets the "analytics" field.
func (m *PublishedPostMutation) SetAnalytics(value map[string]interface{}) {
	m.analytics = &value
}

// Analytics returns the value of the "analytics" field in the mutation.
func (m *PublishedPostMutation) Analytics() (r map[string]interface{}, exists bool) {
	v := m.analytics
	if v == nil {
		return
	}
	return *v, true
}

// OldAnalytics returns the old "analytics" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldAnalytics(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAnalytics is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAnalytics requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAnalytics: %w", err)
	}
	return oldValue.Analytics, nil
}

// ClearAnalytics clears the value of the "analytics" field.
func (m *PublishedPostMutation) ClearAnalytics() {
	m.analytics = nil
	m.clearedFields[publishedpost.FieldAnalytics] = struct{}{}
}

// AnalyticsCleared returns if the "analytics" field was cleared in this mutation.
func (m *PublishedPostMutation) AnalyticsCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldAnalytics]
	return ok
}

// ResetAnalytics resets all changes to the "analytics" field.
func (m *PublishedPostMutation) ResetAnalytics() {
	m.analytics = nil
	delete(m.clearedFields, publishedpost.FieldAnalytics)
}

// SetLastAnalyticsSync sets the "last_analytics_sync" field.
func (m *PublishedPostMutation) SetLastAnalyticsSync(t time.Time) {
	m.last_analytics_sync = &t
}

// LastAnalyticsSync returns the value of the "last_analytics_sync" field in the mutation.
func (m *PublishedPostMutation) LastAnalyticsSync() (r time.Time, exists bool) {
	v := m.last_analytics_sync
	if v == nil {
		return
	}
	return *v, true
}

// OldLastAnalyticsSync returns the old "last_analytics_sync" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldLastAnalyticsSync(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastAnalyticsSync is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastAnalyticsSync requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastAnalyticsSync: %w", err)
	}
	return oldValue.LastAnalyticsSync, nil
}

// ClearLastAnalyticsSync clears the value of the "last_analytics_sync" field.
func (m *PublishedPostMutation) ClearLastAnalyticsSync() {
	m.last_analytics_sync = nil
	m.clearedFields[publishedpost.FieldLastAnalyticsSync] = struct{}{}
}

// LastAnalyticsSyncCleared returns if the "last_analytics_sync" field was cleared in this mutation.
func (m *PublishedPostMutation) LastAnalyticsSyncCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldLastAnalyticsSync]
	return ok
}

// ResetLastAnalyticsSync resets all changes to the "last_analytics_sync" field.
func (m *PublishedPostMutation) ResetLastAnalyticsSync() {
	m.last_analytics_sync = nil
	delete(m.clearedFields, publishedpost.FieldLastAnalyticsSync)
}

// SetRetryCount sets the "retry_count" field.
func (m *PublishedPostMutation) SetRetryCount(i int) {
	m.retry_count = &i
	m.addretry_count = nil
}

// RetryCount returns the value of the "retry_count" field in the mutation.
func (m *PublishedPostMutation) RetryCount() (r int, exists bool) {
	v := m.retry_count
	if v == nil {
		return
	}
	return *v, true
}

// OldRetryCount returns the old "retry_count" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldRetryCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRetryCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRetryCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRetryCount: %w", err)
	}
	return oldValue.RetryCount, nil
}

// AddRetryCount adds i to the "retry_count" field.
func (m *PublishedPostMutation) AddRetryCount(i int) {
	if m.addretry_count != nil {
		*m.addretry_count += i
	} else {
		m.addretry_count = &i
	}
}

// AddedRetryCount returns the value that was added to the "retry_count" field in this mutation.
func (m *PublishedPostMutation) AddedRetryCount() (r int, exists bool) {
	v := m.addretry_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetRetryCount resets all changes to the "retry_count" field.
func (m *PublishedPostMutation) ResetRetryCount() {
	m.retry_count = nil
	m.addretry_count = nil
}

// SetNextRetryAt sets the "next_retry_at" field.
func (m *PublishedPostMutation) SetNextRetryAt(t time.Time) {
	m.next_retry_at = &t
}

// NextRetryAt returns the value of the "next_retry_at" field in the mutation.
func (m *PublishedPostMutation) NextRetryAt() (r time.Time, exists bool) {
	v := m.next_retry_at
	if v == nil {
		return
	}
	return *v, true
}

// OldNextRetryAt returns the old "next_retry_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldNextRetryAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNextRetryAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNextRetryAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNextRetryAt: %w", err)
	}
	return oldValue.NextRetryAt, nil
}

// ClearNextRetryAt clears the value of the "next_retry_at" field.
func (m *PublishedPostMutation) ClearNextRetryAt() {
	m.next_retry_at = nil
	m.clearedFields[publishedpost.FieldNextRetryAt] = struct{}{}
}

// NextRetryAtCleared returns if the "next_retry_at" field was cleared in this mutation.
func (m *PublishedPostMutation) NextRetryAtCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldNextRetryAt]
	return ok
}

// ResetNextRetryAt resets all changes to the "next_retry_at" field.
func (m *PublishedPostMutation) ResetNextRetryAt() {
	m.next_retry_at = nil
	delete(m.clearedFields, publishedpost.FieldNextRetryAt)
}

// SetPublishOptions sets the "publish_options" field.
func (m *PublishedPostMutation) SetPublishOptions(value map[string]interface{}) {
	m.publish_options = &value
}

// PublishOptions returns the value of the "publish_options" field in the mutation.
func (m *PublishedPostMutation) PublishOptions() (r map[string]interface{}, exists bool) {
	v := m.publish_options
	if v == nil {
		return
	}
	return *v, true
}

// OldPublishOptions returns the old "publish_options" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldPublishOptions(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPublishOptions is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPublishOptions requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPublishOptions: %w", err)
	}
	return oldValue.PublishOptions, nil
}

// ClearPublishOptions clears the value of the "publish_options" field.
func (m *PublishedPostMutation) ClearPublishOptions() {
	m.publish_options = nil
	m.clearedFields[publishedpost.FieldPublishOptions] = struct{}{}
}

// PublishOptionsCleared returns if the "publish_options" field was cleared in this mutation.
func (m *PublishedPostMutation) PublishOptionsCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldPublishOptions]
	return ok
}

// ResetPublishOptions resets all changes to the "publish_options" field.
func (m *PublishedPostMutation) ResetPublishOptions() {
	m.publish_options = nil
	delete(m.clearedFields, publishedpost.FieldPublishOptions)
}

// SetMetadata sets the "metadata" field.
func (m *PublishedPostMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *PublishedPostMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *PublishedPostMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[publishedpost.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *PublishedPostMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *PublishedPostMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, publishedpost.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *PublishedPostMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PublishedPostMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PublishedPostMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PublishedPostMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PublishedPostMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PublishedPostMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *PublishedPostMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *PublishedPostMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the PublishedPost entity.
// If the PublishedPost object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PublishedPostMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *PublishedPostMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[publishedpost.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *PublishedPostMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[publishedpost.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *PublishedPostMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, publishedpost.FieldDeletedAt)
}

// Where appends a list predicates to the PublishedPostMutation builder.
func (m *PublishedPostMutation) Where(ps ...predicate.PublishedPost) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PublishedPostMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PublishedPostMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.PublishedPost, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PublishedPostMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PublishedPostMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (PublishedPost).
func (m *PublishedPostMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PublishedPostMutation) Fields() []string {
	fields := make([]string, 0, 23)
	if m.user_id != nil {
		fields = append(fields, publishedpost.FieldUserID)
	}
	if m.post_id != nil {
		fields = append(fields, publishedpost.FieldPostID)
	}
	if m.integration_id != nil {
		fields = append(fields, publishedpost.FieldIntegrationID)
	}
	if m.platform != nil {
		fields = append(fields, publishedpost.FieldPlatform)
	}
	if m.platform_post_id != nil {
		fields = append(fields, publishedpost.FieldPlatformPostID)
	}
	if m.platform_post_url != nil {
		fields = append(fields, publishedpost.FieldPlatformPostURL)
	}
	if m.content != nil {
		fields = append(fields, publishedpost.FieldContent)
	}
	if m.media_urls != nil {
		fields = append(fields, publishedpost.FieldMediaUrls)
	}
	if m.hashtags != nil {
		fields = append(fields, publishedpost.FieldHashtags)
	}
	if m.status != nil {
		fields = append(fields, publishedpost.FieldStatus)
	}
	if m.error_message != nil {
		fields = append(fields, publishedpost.FieldErrorMessage)
	}
	if m.scheduled_at != nil {
		fields = append(fields, publishedpost.FieldScheduledAt)
	}
	if m.published_at != nil {
		fields = append(fields, publishedpost.FieldPublishedAt)
	}
	if m.platform_response != nil {
		fields = append(fields, publishedpost.FieldPlatformResponse)
	}
	if m.analytics != nil {
		fields = append(fields, publishedpost.FieldAnalytics)
	}
	if m.last_analytics_sync != nil {
		fields = append(fields, publishedpost.FieldLastAnalyticsSync)
	}
	if m.retry_count != nil {
		fields = append(fields, publishedpost.FieldRetryCount)
	}
	if m.next_retry_at != nil {
		fields = append(fields, publishedpost.FieldNextRetryAt)
	}
	if m.publish_options != nil {
		fields = append(fields, publishedpost.FieldPublishOptions)
	}
	if m.metadata != nil {
		fields = append(fields, publishedpost.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, publishedpost.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, publishedpost.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, publishedpost.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PublishedPostMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case publishedpost.FieldUserID:
		return m.UserID()
	case publishedpost.FieldPostID:
		return m.PostID()
	case publishedpost.FieldIntegrationID:
		return m.IntegrationID()
	case publishedpost.FieldPlatform:
		return m.Platform()
	case publishedpost.FieldPlatformPostID:
		return m.PlatformPostID()
	case publishedpost.FieldPlatformPostURL:
		return m.PlatformPostURL()
	case publishedpost.FieldContent:
		return m.Content()
	case publishedpost.FieldMediaUrls:
		return m.MediaUrls()
	case publishedpost.FieldHashtags:
		return m.Hashtags()
	case publishedpost.FieldStatus:
		return m.Status()
	case publishedpost.FieldErrorMessage:
		return m.ErrorMessage()
	case publishedpost.FieldScheduledAt:
		return m.ScheduledAt()
	case publishedpost.FieldPublishedAt:
		return m.PublishedAt()
	case publishedpost.FieldPlatformResponse:
		return m.PlatformResponse()
	case publishedpost.FieldAnalytics:
		return m.Analytics()
	case publishedpost.FieldLastAnalyticsSync:
		return m.LastAnalyticsSync()
	case publishedpost.FieldRetryCount:
		return m.RetryCount()
	case publishedpost.FieldNextRetryAt:
		return m.NextRetryAt()
	case publishedpost.FieldPublishOptions:
		return m.PublishOptions()
	case publishedpost.FieldMetadata:
		return m.Metadata()
	case publishedpost.FieldCreatedAt:
		return m.CreatedAt()
	case publishedpost.FieldUpdatedAt:
		return m.UpdatedAt()
	case publishedpost.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PublishedPostMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case publishedpost.FieldUserID:
		return m.OldUserID(ctx)
	case publishedpost.FieldPostID:
		return m.OldPostID(ctx)
	case publishedpost.FieldIntegrationID:
		return m.OldIntegrationID(ctx)
	case publishedpost.FieldPlatform:
		return m.OldPlatform(ctx)
	case publishedpost.FieldPlatformPostID:
		return m.OldPlatformPostID(ctx)
	case publishedpost.FieldPlatformPostURL:
		return m.OldPlatformPostURL(ctx)
	case publishedpost.FieldContent:
		return m.OldContent(ctx)
	case publishedpost.FieldMediaUrls:
		return m.OldMediaUrls(ctx)
	case publishedpost.FieldHashtags:
		return m.OldHashtags(ctx)
	case publishedpost.FieldStatus:
		return m.OldStatus(ctx)
	case publishedpost.FieldErrorMessage:
		return m.OldErrorMessage(ctx)
	case publishedpost.FieldScheduledAt:
		return m.OldScheduledAt(ctx)
	case publishedpost.FieldPublishedAt:
		return m.OldPublishedAt(ctx)
	case publishedpost.FieldPlatformResponse:
		return m.OldPlatformResponse(ctx)
	case publishedpost.FieldAnalytics:
		return m.OldAnalytics(ctx)
	case publishedpost.FieldLastAnalyticsSync:
		return m.OldLastAnalyticsSync(ctx)
	case publishedpost.FieldRetryCount:
		return m.OldRetryCount(ctx)
	case publishedpost.FieldNextRetryAt:
		return m.OldNextRetryAt(ctx)
	case publishedpost.FieldPublishOptions:
		return m.OldPublishOptions(ctx)
	case publishedpost.FieldMetadata:
		return m.OldMetadata(ctx)
	case publishedpost.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case publishedpost.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case publishedpost.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown PublishedPost field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PublishedPostMutation) SetField(name string, value ent.Value) error {
	switch name {
	case publishedpost.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case publishedpost.FieldPostID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPostID(v)
		return nil
	case publishedpost.FieldIntegrationID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIntegrationID(v)
		return nil
	case publishedpost.FieldPlatform:
		v, ok := value.(publishedpost.Platform)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatform(v)
		return nil
	case publishedpost.FieldPlatformPostID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformPostID(v)
		return nil
	case publishedpost.FieldPlatformPostURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformPostURL(v)
		return nil
	case publishedpost.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case publishedpost.FieldMediaUrls:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMediaUrls(v)
		return nil
	case publishedpost.FieldHashtags:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHashtags(v)
		return nil
	case publishedpost.FieldStatus:
		v, ok := value.(publishedpost.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case publishedpost.FieldErrorMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetErrorMessage(v)
		return nil
	case publishedpost.FieldScheduledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetScheduledAt(v)
		return nil
	case publishedpost.FieldPublishedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPublishedAt(v)
		return nil
	case publishedpost.FieldPlatformResponse:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatformResponse(v)
		return nil
	case publishedpost.FieldAnalytics:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAnalytics(v)
		return nil
	case publishedpost.FieldLastAnalyticsSync:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastAnalyticsSync(v)
		return nil
	case publishedpost.FieldRetryCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRetryCount(v)
		return nil
	case publishedpost.FieldNextRetryAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNextRetryAt(v)
		return nil
	case publishedpost.FieldPublishOptions:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPublishOptions(v)
		return nil
	case publishedpost.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case publishedpost.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case publishedpost.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case publishedpost.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown PublishedPost field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PublishedPostMutation) AddedFields() []string {
	var fields []string
	if m.addretry_count != nil {
		fields = append(fields, publishedpost.FieldRetryCount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PublishedPostMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case publishedpost.FieldRetryCount:
		return m.AddedRetryCount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PublishedPostMutation) AddField(name string, value ent.Value) error {
	switch name {
	case publishedpost.FieldRetryCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRetryCount(v)
		return nil
	}
	return fmt.Errorf("unknown PublishedPost numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PublishedPostMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(publishedpost.FieldPlatformPostURL) {
		fields = append(fields, publishedpost.FieldPlatformPostURL)
	}
	if m.FieldCleared(publishedpost.FieldMediaUrls) {
		fields = append(fields, publishedpost.FieldMediaUrls)
	}
	if m.FieldCleared(publishedpost.FieldHashtags) {
		fields = append(fields, publishedpost.FieldHashtags)
	}
	if m.FieldCleared(publishedpost.FieldErrorMessage) {
		fields = append(fields, publishedpost.FieldErrorMessage)
	}
	if m.FieldCleared(publishedpost.FieldScheduledAt) {
		fields = append(fields, publishedpost.FieldScheduledAt)
	}
	if m.FieldCleared(publishedpost.FieldPublishedAt) {
		fields = append(fields, publishedpost.FieldPublishedAt)
	}
	if m.FieldCleared(publishedpost.FieldPlatformResponse) {
		fields = append(fields, publishedpost.FieldPlatformResponse)
	}
	if m.FieldCleared(publishedpost.FieldAnalytics) {
		fields = append(fields, publishedpost.FieldAnalytics)
	}
	if m.FieldCleared(publishedpost.FieldLastAnalyticsSync) {
		fields = append(fields, publishedpost.FieldLastAnalyticsSync)
	}
	if m.FieldCleared(publishedpost.FieldNextRetryAt) {
		fields = append(fields, publishedpost.FieldNextRetryAt)
	}
	if m.FieldCleared(publishedpost.FieldPublishOptions) {
		fields = append(fields, publishedpost.FieldPublishOptions)
	}
	if m.FieldCleared(publishedpost.FieldMetadata) {
		fields = append(fields, publishedpost.FieldMetadata)
	}
	if m.FieldCleared(publishedpost.FieldDeletedAt) {
		fields = append(fields, publishedpost.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PublishedPostMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PublishedPostMutation) ClearField(name string) error {
	switch name {
	case publishedpost.FieldPlatformPostURL:
		m.ClearPlatformPostURL()
		return nil
	case publishedpost.FieldMediaUrls:
		m.ClearMediaUrls()
		return nil
	case publishedpost.FieldHashtags:
		m.ClearHashtags()
		return nil
	case publishedpost.FieldErrorMessage:
		m.ClearErrorMessage()
		return nil
	case publishedpost.FieldScheduledAt:
		m.ClearScheduledAt()
		return nil
	case publishedpost.FieldPublishedAt:
		m.ClearPublishedAt()
		return nil
	case publishedpost.FieldPlatformResponse:
		m.ClearPlatformResponse()
		return nil
	case publishedpost.FieldAnalytics:
		m.ClearAnalytics()
		return nil
	case publishedpost.FieldLastAnalyticsSync:
		m.ClearLastAnalyticsSync()
		return nil
	case publishedpost.FieldNextRetryAt:
		m.ClearNextRetryAt()
		return nil
	case publishedpost.FieldPublishOptions:
		m.ClearPublishOptions()
		return nil
	case publishedpost.FieldMetadata:
		m.ClearMetadata()
		return nil
	case publishedpost.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown PublishedPost nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PublishedPostMutation) ResetField(name string) error {
	switch name {
	case publishedpost.FieldUserID:
		m.ResetUserID()
		return nil
	case publishedpost.FieldPostID:
		m.ResetPostID()
		return nil
	case publishedpost.FieldIntegrationID:
		m.ResetIntegrationID()
		return nil
	case publishedpost.FieldPlatform:
		m.ResetPlatform()
		return nil
	case publishedpost.FieldPlatformPostID:
		m.ResetPlatformPostID()
		return nil
	case publishedpost.FieldPlatformPostURL:
		m.ResetPlatformPostURL()
		return nil
	case publishedpost.FieldContent:
		m.ResetContent()
		return nil
	case publishedpost.FieldMediaUrls:
		m.ResetMediaUrls()
		return nil
	case publishedpost.FieldHashtags:
		m.ResetHashtags()
		return nil
	case publishedpost.FieldStatus:
		m.ResetStatus()
		return nil
	case publishedpost.FieldErrorMessage:
		m.ResetErrorMessage()
		return nil
	case publishedpost.FieldScheduledAt:
		m.ResetScheduledAt()
		return nil
	case publishedpost.FieldPublishedAt:
		m.ResetPublishedAt()
		return nil
	case publishedpost.FieldPlatformResponse:
		m.ResetPlatformResponse()
		return nil
	case publishedpost.FieldAnalytics:
		m.ResetAnalytics()
		return nil
	case publishedpost.FieldLastAnalyticsSync:
		m.ResetLastAnalyticsSync()
		return nil
	case publishedpost.FieldRetryCount:
		m.ResetRetryCount()
		return nil
	case publishedpost.FieldNextRetryAt:
		m.ResetNextRetryAt()
		return nil
	case publishedpost.FieldPublishOptions:
		m.ResetPublishOptions()
		return nil
	case publishedpost.FieldMetadata:
		m.ResetMetadata()
		return nil
	case publishedpost.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case publishedpost.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case publishedpost.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown PublishedPost field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PublishedPostMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PublishedPostMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PublishedPostMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PublishedPostMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PublishedPostMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PublishedPostMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PublishedPostMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown PublishedPost unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PublishedPostMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown PublishedPost edge %s", name)
}

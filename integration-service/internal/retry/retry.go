package retry

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/social-content-ai/integration-service/internal/errors"
	"github.com/social-content-ai/pkg-shared/logging"
)

// Strategy defines the retry strategy
type Strategy string

const (
	StrategyFixed       Strategy = "fixed"
	StrategyLinear      Strategy = "linear"
	StrategyExponential Strategy = "exponential"
	StrategyJittered    Strategy = "jittered"
)

// Config holds retry configuration
type Config struct {
	MaxAttempts   int           `yaml:"max_attempts" env:"RETRY_MAX_ATTEMPTS" env-default:"3"`
	InitialDelay  time.Duration `yaml:"initial_delay" env:"RETRY_INITIAL_DELAY" env-default:"1s"`
	MaxDelay      time.Duration `yaml:"max_delay" env:"RETRY_MAX_DELAY" env-default:"30s"`
	Multiplier    float64       `yaml:"multiplier" env:"RETRY_MULTIPLIER" env-default:"2.0"`
	Strategy      Strategy      `yaml:"strategy" env:"RETRY_STRATEGY" env-default:"exponential"`
	JitterPercent float64       `yaml:"jitter_percent" env:"RETRY_JITTER_PERCENT" env-default:"0.1"`
}

// DefaultConfig returns default retry configuration
func DefaultConfig() Config {
	return Config{
		MaxAttempts:   3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		Multiplier:    2.0,
		Strategy:      StrategyExponential,
		JitterPercent: 0.1,
	}
}

// Retrier handles retry logic
type Retrier struct {
	config Config
	logger logging.Logger
}

// NewRetrier creates a new retrier
func NewRetrier(config Config, logger logging.Logger) *Retrier {
	return &Retrier{
		config: config,
		logger: logger,
	}
}

// RetryableFunc is a function that can be retried
type RetryableFunc func(ctx context.Context, attempt int) error

// Do executes a function with retry logic
func (r *Retrier) Do(ctx context.Context, operation string, fn RetryableFunc) error {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute the function
		err := fn(ctx, attempt)
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(map[string]interface{}{
					"operation": operation,
					"attempt":   attempt,
				}).Info("Operation succeeded after retry")
			}
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if !r.isRetryable(err) {
			r.logger.WithFields(map[string]interface{}{
				"operation": operation,
				"attempt":   attempt,
				"error":     err.Error(),
			}).Info("Operation failed with non-retryable error")
			return err
		}

		// Don't sleep after the last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(map[string]interface{}{
			"operation":    operation,
			"attempt":      attempt,
			"max_attempts": r.config.MaxAttempts,
			"delay":        delay,
			"error":        err.Error(),
		}).Warn("Operation failed, retrying")

		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}
	}

	r.logger.WithFields(map[string]interface{}{
		"operation":    operation,
		"max_attempts": r.config.MaxAttempts,
		"final_error":  lastErr.Error(),
	}).Error("Operation failed after all retry attempts")

	return fmt.Errorf("operation failed after %d attempts: %w", r.config.MaxAttempts, lastErr)
}

// DoWithResult executes a function with retry logic and returns a result
// This is a generic function (not a method) to work around Go's limitation with generic methods
func DoWithResult[T any](r *Retrier, ctx context.Context, operation string, fn func(ctx context.Context, attempt int) (T, error)) (T, error) {
	var result T
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// Check if context is cancelled
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}

		// Execute the function
		res, err := fn(ctx, attempt)
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(map[string]interface{}{
					"operation": operation,
					"attempt":   attempt,
				}).Info("Operation succeeded after retry")
			}
			return res, nil
		}

		lastErr = err

		// Check if error is retryable
		if !r.isRetryable(err) {
			r.logger.WithFields(map[string]interface{}{
				"operation": operation,
				"attempt":   attempt,
				"error":     err.Error(),
			}).Info("Operation failed with non-retryable error")
			return result, err
		}

		// Don't sleep after the last attempt
		if attempt == r.config.MaxAttempts {
			break
		}

		// Calculate delay
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(map[string]interface{}{
			"operation":    operation,
			"attempt":      attempt,
			"max_attempts": r.config.MaxAttempts,
			"delay":        delay,
			"error":        err.Error(),
		}).Warn("Operation failed, retrying")

		// Wait before retry
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		case <-time.After(delay):
		}
	}

	r.logger.WithFields(map[string]interface{}{
		"operation":    operation,
		"max_attempts": r.config.MaxAttempts,
		"final_error":  lastErr.Error(),
	}).Error("Operation failed after all retry attempts")

	return result, fmt.Errorf("operation failed after %d attempts: %w", r.config.MaxAttempts, lastErr)
}

// isRetryable checks if an error is retryable
func (r *Retrier) isRetryable(err error) bool {
	// Check if it's an IntegrationError
	if integrationErr, ok := err.(*errors.IntegrationError); ok {
		return integrationErr.IsRetryable()
	}

	// Check for common retryable errors
	switch err {
	case context.DeadlineExceeded:
		return true
	case context.Canceled:
		return false
	default:
		// By default, assume network/temporary errors are retryable
		return true
	}
}

// calculateDelay calculates the delay for the next retry attempt
func (r *Retrier) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.Strategy {
	case StrategyFixed:
		delay = r.config.InitialDelay

	case StrategyLinear:
		delay = time.Duration(float64(r.config.InitialDelay) * float64(attempt))

	case StrategyExponential:
		delay = time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt-1)))

	case StrategyJittered:
		baseDelay := time.Duration(float64(r.config.InitialDelay) * math.Pow(r.config.Multiplier, float64(attempt-1)))
		jitter := time.Duration(float64(baseDelay) * r.config.JitterPercent * (rand.Float64()*2 - 1))
		delay = baseDelay + jitter

	default:
		delay = r.config.InitialDelay
	}

	// Cap the delay at max delay
	if delay > r.config.MaxDelay {
		delay = r.config.MaxDelay
	}

	// Ensure minimum delay
	if delay < 0 {
		delay = r.config.InitialDelay
	}

	return delay
}

// WithMaxAttempts creates a new retrier with different max attempts
func (r *Retrier) WithMaxAttempts(maxAttempts int) *Retrier {
	config := r.config
	config.MaxAttempts = maxAttempts
	return NewRetrier(config, r.logger)
}

// WithStrategy creates a new retrier with different strategy
func (r *Retrier) WithStrategy(strategy Strategy) *Retrier {
	config := r.config
	config.Strategy = strategy
	return NewRetrier(config, r.logger)
}

// WithInitialDelay creates a new retrier with different initial delay
func (r *Retrier) WithInitialDelay(delay time.Duration) *Retrier {
	config := r.config
	config.InitialDelay = delay
	return NewRetrier(config, r.logger)
}

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// BankTransfer holds the schema definition for the BankTransfer entity.
type BankTransfer struct {
	ent.Schema
}

// Fields of the BankTransfer.
func (BankTransfer) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			StorageKey("id"),
		field.UUID("user_id", uuid.UUID{}),
		field.UUID("plan_id", uuid.UUID{}).
			Optional(), // Optional: if purchasing a subscription
		field.Int64("amount"). // Amount in smallest currency unit (VND)
			Min(0),
		field.String("currency").
			MaxLen(3).
			Default("VND"),
		field.Enum("status").
			Values("pending", "confirmed", "failed", "expired").
			Default("pending"),
		field.String("bank_account").
			MaxLen(100), // Destination bank account
		field.String("reference_code").
			MaxLen(50).
			Unique(), // Unique reference code for transfer
		field.Text("transfer_instructions"), // Instructions for user
		field.Time("confirmed_at").
			Optional(),
		field.Time("expires_at"), // Payment deadline
		field.Int("credits_to_add"). // Credits to add when confirmed
			Default(0).
			Min(0),
		field.String("bank_transaction_id").
			MaxLen(100).
			Optional(), // Bank's transaction ID
		field.Int64("actual_amount_received").
			Optional().
			Min(0), // Actual amount received from bank
		field.String("confirmation_method").
			MaxLen(20).
			Default("manual"), // 'manual', 'auto_webhook'
		field.UUID("confirmed_by", uuid.UUID{}).
			Optional(), // Admin user ID (for manual confirmations)
		field.JSON("metadata", map[string]interface{}{}).
			Optional(),
		field.Time("created_at").
			Default(time.Now).
			Immutable(),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now),
	}
}

// Edges of the BankTransfer.
func (BankTransfer) Edges() []ent.Edge {
	return nil
}

// Indexes of the BankTransfer.
func (BankTransfer) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("plan_id"),
		index.Fields("status"),
		index.Fields("reference_code"),
		index.Fields("expires_at"),
		index.Fields("confirmed_at"),
		index.Fields("bank_transaction_id"),
		index.Fields("created_at"),
	}
}

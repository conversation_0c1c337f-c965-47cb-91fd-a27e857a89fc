package restful

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/social-content-ai/asset-service/api/restful/handlers"
	"github.com/social-content-ai/asset-service/api/restful/middleware"
	"github.com/social-content-ai/asset-service/usecase/asset"
	"github.com/social-content-ai/asset-service/usecase/folder"
	"github.com/social-content-ai/asset-service/usecase/rag"
	"github.com/social-content-ai/pkg-shared/logging"
	userv1 "github.com/social-content-ai/proto-shared/user/v1"
)

// SetupRoutes sets up all HTTP routes
func SetupRoutes(
	router *gin.Engine,
	assetUseCase asset.UseCase,
	folderUseCase folder.UseCase,
	ragUseCase rag.UseCase,
	userClient userv1.UserServiceClient,
	logger logging.Logger,
) {
	// Initialize handlers
	assetHandler := handlers.NewAssetHandler(assetUseCase, logger)
	folderHandler := handlers.NewFolderHandler(folderUseCase, logger)
	ragHandler := handlers.NewRAGHandler(ragUseCase, logger)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(logger)

	// Global middleware
	router.Use(middleware.CORS())
	router.Use(middleware.RequestLogger(logger))
	router.Use(middleware.ErrorHandler(logger))

	// Health check endpoint (no auth required)
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "asset-service",
			"status":  "ok",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Asset routes (require authentication)
		assets := v1.Group("/assets")
		assets.Use(authMiddleware.RequireAuth())
		{
			// Upload flow
			assets.POST("/upload/request", assetHandler.RequestUpload)
			assets.POST("/upload/confirm", assetHandler.ConfirmUpload)

			// Asset CRUD
			assets.GET("/", assetHandler.ListAssets)
			assets.GET("/:id", assetHandler.GetAsset)
			assets.PUT("/:id", assetHandler.UpdateAsset)
			assets.DELETE("/:id", assetHandler.DeleteAsset)

			// Asset operations
			assets.GET("/:id/download", assetHandler.GetAssetDownloadURL)
			assets.GET("/:id/metadata", assetHandler.GetAssetMetadata)
			assets.POST("/bulk-delete", assetHandler.BulkDeleteAssets)
		}

		// Admin-only asset routes
		assetsAdmin := v1.Group("/assets")
		assetsAdmin.Use(authMiddleware.AdminAuth())
		{
			assetsAdmin.POST("/cleanup", assetHandler.CleanupStorage)
		}

		// Folder routes (require authentication)
		folders := v1.Group("/folders")
		folders.Use(authMiddleware.RequireAuth())
		{
			// Folder CRUD
			folders.GET("/", folderHandler.ListFolders)
			folders.POST("/", folderHandler.CreateFolder)
			folders.GET("/:id", folderHandler.GetFolder)
			folders.PUT("/:id", folderHandler.UpdateFolder)
			folders.DELETE("/:id", folderHandler.DeleteFolder)

			// Folder operations
			folders.POST("/:id/move", folderHandler.MoveFolder)
			folders.POST("/:id/copy", folderHandler.CopyFolder)
			folders.GET("/:id/assets", folderHandler.GetFolderAssets)
			folders.POST("/:id/share", folderHandler.ShareFolder)
			folders.GET("/:id/breadcrumb", folderHandler.GetFolderBreadcrumb)

			// Folder tree
			folders.GET("/tree", folderHandler.GetFolderTree)
		}

		// RAG routes (require authentication)
		ragRoutes := v1.Group("/rag")
		ragRoutes.Use(authMiddleware.RequireAuth())
		{
			// RAG document CRUD
			ragRoutes.GET("/documents", ragHandler.ListRAGDocuments)
			ragRoutes.POST("/documents", ragHandler.UploadRAGDocument)
			ragRoutes.GET("/documents/:id", ragHandler.GetRAGDocument)
			ragRoutes.PUT("/documents/:id", ragHandler.UpdateRAGDocument)
			ragRoutes.DELETE("/documents/:id", ragHandler.DeleteRAGDocument)

			// RAG operations
			ragRoutes.POST("/documents/:id/process", ragHandler.ProcessRAGDocument)
			ragRoutes.GET("/documents/:id/chunks", ragHandler.GetRAGDocumentChunks)
			ragRoutes.POST("/search", ragHandler.SearchRAGDocuments)
			ragRoutes.GET("/stats", ragHandler.GetRAGStats)
		}
	}

	logger.Info("REST API routes setup completed")
	logger.WithFields(map[string]interface{}{
		"asset_endpoints":  "✓ Upload, CRUD, Download, Metadata, Bulk operations",
		"folder_endpoints": "✓ CRUD, Move, Copy, Share, Tree, Breadcrumb",
		"rag_endpoints":    "✓ Document CRUD, Processing, Search, Stats",
		"auth_required":    "✓ All endpoints except /health",
		"admin_endpoints":  "✓ Storage cleanup",
	}).Info("Available REST API endpoints")
}

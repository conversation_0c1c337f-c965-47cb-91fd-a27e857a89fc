package notification

import (
	"context"
)

// UseCase defines the notification business logic interface
type UseCase interface {
	// Send a single notification
	SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error)

	// Send templated notification
	SendTemplatedNotification(ctx context.Context, req *SendTemplatedNotificationRequest) (*SendNotificationResponse, error)

	// Send bulk notifications
	SendBulkNotification(ctx context.Context, req *BulkNotificationRequest) (*BulkNotificationResponse, error)

	// Get notification by ID
	GetNotification(ctx context.Context, id string) (*NotificationResponse, error)

	// List notifications for a user
	ListNotifications(ctx context.Context, req *ListNotificationsRequest) (*ListNotificationsResponse, error)

	// Mark notification as read
	MarkAsRead(ctx context.Context, req *MarkAsReadRequest) error

	// Mark all notifications as read for a user
	MarkAllAsRead(ctx context.Context, userID string) error

	// Delete notification
	DeleteNotification(ctx context.Context, notificationID, userID string) error

	// Get delivery status
	GetDeliveryStatus(ctx context.Context, req *GetDeliveryStatusRequest) (*DeliveryStatusResponse, error)
}

// SendNotificationRequest represents a request to send a notification
type SendNotificationRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Type        string                 `json:"type" validate:"required"`
	Title       string                 `json:"title" validate:"required"`
	Message     string                 `json:"message" validate:"required"`
	Channels    []string               `json:"channels" validate:"required,min=1"`
	Priority    string                 `json:"priority" validate:"required,oneof=low normal high critical"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// SendTemplatedNotificationRequest represents a request to send a templated notification
type SendTemplatedNotificationRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	TemplateID  string                 `json:"template_id" validate:"required"`
	Channels    []string               `json:"channels" validate:"required,min=1"`
	Priority    string                 `json:"priority" validate:"required,oneof=low normal high critical"`
	Data        map[string]interface{} `json:"data" validate:"required"`
}

// BulkNotificationRequest represents a request to send bulk notifications
type BulkNotificationRequest struct {
	UserIDs     []string               `json:"user_ids" validate:"required,min=1"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Type        string                 `json:"type" validate:"required"`
	Title       string                 `json:"title" validate:"required"`
	Message     string                 `json:"message" validate:"required"`
	Channels    []string               `json:"channels" validate:"required,min=1"`
	Priority    string                 `json:"priority" validate:"required,oneof=low normal high critical"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// SendNotificationResponse represents the response from sending a notification
type SendNotificationResponse struct {
	NotificationID string `json:"notification_id"`
	Status         string `json:"status"`
	Message        string `json:"message,omitempty"`
}

// BulkNotificationResponse represents the response from sending bulk notifications
type BulkNotificationResponse struct {
	TotalSent     int                        `json:"total_sent"`
	TotalFailed   int                        `json:"total_failed"`
	Results       []SendNotificationResponse `json:"results"`
	FailedUserIDs []string                   `json:"failed_user_ids,omitempty"`
}

// NotificationResponse represents a notification
type NotificationResponse struct {
	ID          string                 `json:"id"`
	UserID      string                 `json:"user_id"`
	WorkspaceID string                 `json:"workspace_id,omitempty"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Channels    []string               `json:"channels"`
	Priority    string                 `json:"priority"`
	Data        map[string]interface{} `json:"data,omitempty"`
	Status      string                 `json:"status"`
	CreatedAt   string                 `json:"created_at"`
	SentAt      string                 `json:"sent_at,omitempty"`
	DeliveredAt string                 `json:"delivered_at,omitempty"`
	ReadAt      string                 `json:"read_at,omitempty"`
}

// ListNotificationsRequest represents a request to list notifications
type ListNotificationsRequest struct {
	UserID      string `json:"user_id" validate:"required"`
	WorkspaceID string `json:"workspace_id,omitempty"`
	Type        string `json:"type,omitempty"`
	Status      string `json:"status,omitempty"`
	Page        int    `json:"page" validate:"min=1"`
	Limit       int    `json:"limit" validate:"min=1,max=100"`
	SortBy      string `json:"sort_by,omitempty"`
	Order       string `json:"order,omitempty"`
}

// ListNotificationsResponse represents the response from listing notifications
type ListNotificationsResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Pagination    PaginationMeta         `json:"pagination"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// MarkAsReadRequest represents a request to mark notifications as read
type MarkAsReadRequest struct {
	UserID          string   `json:"user_id" validate:"required"`
	NotificationIDs []string `json:"notification_ids" validate:"required,min=1"`
}

// GetDeliveryStatusRequest represents a request to get delivery status
type GetDeliveryStatusRequest struct {
	NotificationID string `json:"notification_id" validate:"required"`
}

// DeliveryStatusResponse represents delivery status information
type DeliveryStatusResponse struct {
	NotificationID string            `json:"notification_id"`
	Status         string            `json:"status"`
	Channels       []ChannelStatus   `json:"channels"`
	CreatedAt      string            `json:"created_at"`
	LastUpdatedAt  string            `json:"last_updated_at"`
	Attempts       []DeliveryAttempt `json:"attempts,omitempty"`
}

// ChannelStatus represents the status of a delivery channel
type ChannelStatus struct {
	Channel     string `json:"channel"`
	Status      string `json:"status"`
	SentAt      string `json:"sent_at,omitempty"`
	DeliveredAt string `json:"delivered_at,omitempty"`
	Error       string `json:"error,omitempty"`
}

// DeliveryAttempt represents a delivery attempt
type DeliveryAttempt struct {
	Channel     string `json:"channel"`
	AttemptedAt string `json:"attempted_at"`
	Status      string `json:"status"`
	Error       string `json:"error,omitempty"`
	RetryCount  int    `json:"retry_count"`
}

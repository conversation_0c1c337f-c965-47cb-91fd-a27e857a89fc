// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/social-content-ai/credit-service/ent/order"
	"github.com/social-content-ai/credit-service/ent/predicate"
)

// OrderUpdate is the builder for updating Order entities.
type OrderUpdate struct {
	config
	hooks    []Hook
	mutation *OrderMutation
}

// Where appends a list predicates to the OrderUpdate builder.
func (ou *OrderUpdate) Where(ps ...predicate.Order) *OrderUpdate {
	ou.mutation.Where(ps...)
	return ou
}

// SetOrderNumber sets the "order_number" field.
func (ou *OrderUpdate) SetOrderNumber(s string) *OrderUpdate {
	ou.mutation.SetOrderNumber(s)
	return ou
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableOrderNumber(s *string) *OrderUpdate {
	if s != nil {
		ou.SetOrderNumber(*s)
	}
	return ou
}

// SetUserID sets the "user_id" field.
func (ou *OrderUpdate) SetUserID(u uuid.UUID) *OrderUpdate {
	ou.mutation.SetUserID(u)
	return ou
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableUserID(u *uuid.UUID) *OrderUpdate {
	if u != nil {
		ou.SetUserID(*u)
	}
	return ou
}

// SetPackageID sets the "package_id" field.
func (ou *OrderUpdate) SetPackageID(u uuid.UUID) *OrderUpdate {
	ou.mutation.SetPackageID(u)
	return ou
}

// SetNillablePackageID sets the "package_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillablePackageID(u *uuid.UUID) *OrderUpdate {
	if u != nil {
		ou.SetPackageID(*u)
	}
	return ou
}

// ClearPackageID clears the value of the "package_id" field.
func (ou *OrderUpdate) ClearPackageID() *OrderUpdate {
	ou.mutation.ClearPackageID()
	return ou
}

// SetPlanID sets the "plan_id" field.
func (ou *OrderUpdate) SetPlanID(u uuid.UUID) *OrderUpdate {
	ou.mutation.SetPlanID(u)
	return ou
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillablePlanID(u *uuid.UUID) *OrderUpdate {
	if u != nil {
		ou.SetPlanID(*u)
	}
	return ou
}

// ClearPlanID clears the value of the "plan_id" field.
func (ou *OrderUpdate) ClearPlanID() *OrderUpdate {
	ou.mutation.ClearPlanID()
	return ou
}

// SetType sets the "type" field.
func (ou *OrderUpdate) SetType(o order.Type) *OrderUpdate {
	ou.mutation.SetType(o)
	return ou
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableType(o *order.Type) *OrderUpdate {
	if o != nil {
		ou.SetType(*o)
	}
	return ou
}

// SetAmount sets the "amount" field.
func (ou *OrderUpdate) SetAmount(i int64) *OrderUpdate {
	ou.mutation.ResetAmount()
	ou.mutation.SetAmount(i)
	return ou
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableAmount(i *int64) *OrderUpdate {
	if i != nil {
		ou.SetAmount(*i)
	}
	return ou
}

// AddAmount adds i to the "amount" field.
func (ou *OrderUpdate) AddAmount(i int64) *OrderUpdate {
	ou.mutation.AddAmount(i)
	return ou
}

// SetCurrency sets the "currency" field.
func (ou *OrderUpdate) SetCurrency(s string) *OrderUpdate {
	ou.mutation.SetCurrency(s)
	return ou
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCurrency(s *string) *OrderUpdate {
	if s != nil {
		ou.SetCurrency(*s)
	}
	return ou
}

// SetStatus sets the "status" field.
func (ou *OrderUpdate) SetStatus(o order.Status) *OrderUpdate {
	ou.mutation.SetStatus(o)
	return ou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableStatus(o *order.Status) *OrderUpdate {
	if o != nil {
		ou.SetStatus(*o)
	}
	return ou
}

// SetPaymentMethod sets the "payment_method" field.
func (ou *OrderUpdate) SetPaymentMethod(s string) *OrderUpdate {
	ou.mutation.SetPaymentMethod(s)
	return ou
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (ou *OrderUpdate) SetNillablePaymentMethod(s *string) *OrderUpdate {
	if s != nil {
		ou.SetPaymentMethod(*s)
	}
	return ou
}

// SetPaymentID sets the "payment_id" field.
func (ou *OrderUpdate) SetPaymentID(s string) *OrderUpdate {
	ou.mutation.SetPaymentID(s)
	return ou
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillablePaymentID(s *string) *OrderUpdate {
	if s != nil {
		ou.SetPaymentID(*s)
	}
	return ou
}

// ClearPaymentID clears the value of the "payment_id" field.
func (ou *OrderUpdate) ClearPaymentID() *OrderUpdate {
	ou.mutation.ClearPaymentID()
	return ou
}

// SetInvoiceID sets the "invoice_id" field.
func (ou *OrderUpdate) SetInvoiceID(s string) *OrderUpdate {
	ou.mutation.SetInvoiceID(s)
	return ou
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableInvoiceID(s *string) *OrderUpdate {
	if s != nil {
		ou.SetInvoiceID(*s)
	}
	return ou
}

// ClearInvoiceID clears the value of the "invoice_id" field.
func (ou *OrderUpdate) ClearInvoiceID() *OrderUpdate {
	ou.mutation.ClearInvoiceID()
	return ou
}

// SetCredits sets the "credits" field.
func (ou *OrderUpdate) SetCredits(i int) *OrderUpdate {
	ou.mutation.ResetCredits()
	ou.mutation.SetCredits(i)
	return ou
}

// SetNillableCredits sets the "credits" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCredits(i *int) *OrderUpdate {
	if i != nil {
		ou.SetCredits(*i)
	}
	return ou
}

// AddCredits adds i to the "credits" field.
func (ou *OrderUpdate) AddCredits(i int) *OrderUpdate {
	ou.mutation.AddCredits(i)
	return ou
}

// SetBillingAddress sets the "billing_address" field.
func (ou *OrderUpdate) SetBillingAddress(m map[string]interface{}) *OrderUpdate {
	ou.mutation.SetBillingAddress(m)
	return ou
}

// ClearBillingAddress clears the value of the "billing_address" field.
func (ou *OrderUpdate) ClearBillingAddress() *OrderUpdate {
	ou.mutation.ClearBillingAddress()
	return ou
}

// SetItems sets the "items" field.
func (ou *OrderUpdate) SetItems(m []map[string]interface{}) *OrderUpdate {
	ou.mutation.SetItems(m)
	return ou
}

// AppendItems appends m to the "items" field.
func (ou *OrderUpdate) AppendItems(m []map[string]interface{}) *OrderUpdate {
	ou.mutation.AppendItems(m)
	return ou
}

// ClearItems clears the value of the "items" field.
func (ou *OrderUpdate) ClearItems() *OrderUpdate {
	ou.mutation.ClearItems()
	return ou
}

// SetMetadata sets the "metadata" field.
func (ou *OrderUpdate) SetMetadata(m map[string]interface{}) *OrderUpdate {
	ou.mutation.SetMetadata(m)
	return ou
}

// ClearMetadata clears the value of the "metadata" field.
func (ou *OrderUpdate) ClearMetadata() *OrderUpdate {
	ou.mutation.ClearMetadata()
	return ou
}

// SetProcessedAt sets the "processed_at" field.
func (ou *OrderUpdate) SetProcessedAt(t time.Time) *OrderUpdate {
	ou.mutation.SetProcessedAt(t)
	return ou
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableProcessedAt(t *time.Time) *OrderUpdate {
	if t != nil {
		ou.SetProcessedAt(*t)
	}
	return ou
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ou *OrderUpdate) ClearProcessedAt() *OrderUpdate {
	ou.mutation.ClearProcessedAt()
	return ou
}

// SetExpiresAt sets the "expires_at" field.
func (ou *OrderUpdate) SetExpiresAt(t time.Time) *OrderUpdate {
	ou.mutation.SetExpiresAt(t)
	return ou
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableExpiresAt(t *time.Time) *OrderUpdate {
	if t != nil {
		ou.SetExpiresAt(*t)
	}
	return ou
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (ou *OrderUpdate) ClearExpiresAt() *OrderUpdate {
	ou.mutation.ClearExpiresAt()
	return ou
}

// SetFailureReason sets the "failure_reason" field.
func (ou *OrderUpdate) SetFailureReason(s string) *OrderUpdate {
	ou.mutation.SetFailureReason(s)
	return ou
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableFailureReason(s *string) *OrderUpdate {
	if s != nil {
		ou.SetFailureReason(*s)
	}
	return ou
}

// ClearFailureReason clears the value of the "failure_reason" field.
func (ou *OrderUpdate) ClearFailureReason() *OrderUpdate {
	ou.mutation.ClearFailureReason()
	return ou
}

// SetExternalOrderID sets the "external_order_id" field.
func (ou *OrderUpdate) SetExternalOrderID(s string) *OrderUpdate {
	ou.mutation.SetExternalOrderID(s)
	return ou
}

// SetNillableExternalOrderID sets the "external_order_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableExternalOrderID(s *string) *OrderUpdate {
	if s != nil {
		ou.SetExternalOrderID(*s)
	}
	return ou
}

// ClearExternalOrderID clears the value of the "external_order_id" field.
func (ou *OrderUpdate) ClearExternalOrderID() *OrderUpdate {
	ou.mutation.ClearExternalOrderID()
	return ou
}

// SetUpdatedAt sets the "updated_at" field.
func (ou *OrderUpdate) SetUpdatedAt(t time.Time) *OrderUpdate {
	ou.mutation.SetUpdatedAt(t)
	return ou
}

// Mutation returns the OrderMutation object of the builder.
func (ou *OrderUpdate) Mutation() *OrderMutation {
	return ou.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ou *OrderUpdate) Save(ctx context.Context) (int, error) {
	ou.defaults()
	return withHooks(ctx, ou.sqlSave, ou.mutation, ou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ou *OrderUpdate) SaveX(ctx context.Context) int {
	affected, err := ou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ou *OrderUpdate) Exec(ctx context.Context) error {
	_, err := ou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ou *OrderUpdate) ExecX(ctx context.Context) {
	if err := ou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ou *OrderUpdate) defaults() {
	if _, ok := ou.mutation.UpdatedAt(); !ok {
		v := order.UpdateDefaultUpdatedAt()
		ou.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ou *OrderUpdate) check() error {
	if v, ok := ou.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`ent: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if v, ok := ou.mutation.GetType(); ok {
		if err := order.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Order.type": %w`, err)}
		}
	}
	if v, ok := ou.mutation.Amount(); ok {
		if err := order.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Order.amount": %w`, err)}
		}
	}
	if v, ok := ou.mutation.Currency(); ok {
		if err := order.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Order.currency": %w`, err)}
		}
	}
	if v, ok := ou.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Order.status": %w`, err)}
		}
	}
	if v, ok := ou.mutation.PaymentMethod(); ok {
		if err := order.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Order.payment_method": %w`, err)}
		}
	}
	if v, ok := ou.mutation.PaymentID(); ok {
		if err := order.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "Order.payment_id": %w`, err)}
		}
	}
	if v, ok := ou.mutation.InvoiceID(); ok {
		if err := order.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "Order.invoice_id": %w`, err)}
		}
	}
	if v, ok := ou.mutation.Credits(); ok {
		if err := order.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "Order.credits": %w`, err)}
		}
	}
	if v, ok := ou.mutation.FailureReason(); ok {
		if err := order.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "Order.failure_reason": %w`, err)}
		}
	}
	if v, ok := ou.mutation.ExternalOrderID(); ok {
		if err := order.ExternalOrderIDValidator(v); err != nil {
			return &ValidationError{Name: "external_order_id", err: fmt.Errorf(`ent: validator failed for field "Order.external_order_id": %w`, err)}
		}
	}
	return nil
}

func (ou *OrderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ou.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(order.Table, order.Columns, sqlgraph.NewFieldSpec(order.FieldID, field.TypeUUID))
	if ps := ou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ou.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
	}
	if value, ok := ou.mutation.UserID(); ok {
		_spec.SetField(order.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ou.mutation.PackageID(); ok {
		_spec.SetField(order.FieldPackageID, field.TypeUUID, value)
	}
	if ou.mutation.PackageIDCleared() {
		_spec.ClearField(order.FieldPackageID, field.TypeUUID)
	}
	if value, ok := ou.mutation.PlanID(); ok {
		_spec.SetField(order.FieldPlanID, field.TypeUUID, value)
	}
	if ou.mutation.PlanIDCleared() {
		_spec.ClearField(order.FieldPlanID, field.TypeUUID)
	}
	if value, ok := ou.mutation.GetType(); ok {
		_spec.SetField(order.FieldType, field.TypeEnum, value)
	}
	if value, ok := ou.mutation.Amount(); ok {
		_spec.SetField(order.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.AddedAmount(); ok {
		_spec.AddField(order.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.Currency(); ok {
		_spec.SetField(order.FieldCurrency, field.TypeString, value)
	}
	if value, ok := ou.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ou.mutation.PaymentMethod(); ok {
		_spec.SetField(order.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := ou.mutation.PaymentID(); ok {
		_spec.SetField(order.FieldPaymentID, field.TypeString, value)
	}
	if ou.mutation.PaymentIDCleared() {
		_spec.ClearField(order.FieldPaymentID, field.TypeString)
	}
	if value, ok := ou.mutation.InvoiceID(); ok {
		_spec.SetField(order.FieldInvoiceID, field.TypeString, value)
	}
	if ou.mutation.InvoiceIDCleared() {
		_spec.ClearField(order.FieldInvoiceID, field.TypeString)
	}
	if value, ok := ou.mutation.Credits(); ok {
		_spec.SetField(order.FieldCredits, field.TypeInt, value)
	}
	if value, ok := ou.mutation.AddedCredits(); ok {
		_spec.AddField(order.FieldCredits, field.TypeInt, value)
	}
	if value, ok := ou.mutation.BillingAddress(); ok {
		_spec.SetField(order.FieldBillingAddress, field.TypeJSON, value)
	}
	if ou.mutation.BillingAddressCleared() {
		_spec.ClearField(order.FieldBillingAddress, field.TypeJSON)
	}
	if value, ok := ou.mutation.Items(); ok {
		_spec.SetField(order.FieldItems, field.TypeJSON, value)
	}
	if value, ok := ou.mutation.AppendedItems(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, order.FieldItems, value)
		})
	}
	if ou.mutation.ItemsCleared() {
		_spec.ClearField(order.FieldItems, field.TypeJSON)
	}
	if value, ok := ou.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
	}
	if ou.mutation.MetadataCleared() {
		_spec.ClearField(order.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ou.mutation.ProcessedAt(); ok {
		_spec.SetField(order.FieldProcessedAt, field.TypeTime, value)
	}
	if ou.mutation.ProcessedAtCleared() {
		_spec.ClearField(order.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ou.mutation.ExpiresAt(); ok {
		_spec.SetField(order.FieldExpiresAt, field.TypeTime, value)
	}
	if ou.mutation.ExpiresAtCleared() {
		_spec.ClearField(order.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := ou.mutation.FailureReason(); ok {
		_spec.SetField(order.FieldFailureReason, field.TypeString, value)
	}
	if ou.mutation.FailureReasonCleared() {
		_spec.ClearField(order.FieldFailureReason, field.TypeString)
	}
	if value, ok := ou.mutation.ExternalOrderID(); ok {
		_spec.SetField(order.FieldExternalOrderID, field.TypeString, value)
	}
	if ou.mutation.ExternalOrderIDCleared() {
		_spec.ClearField(order.FieldExternalOrderID, field.TypeString)
	}
	if value, ok := ou.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{order.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ou.mutation.done = true
	return n, nil
}

// OrderUpdateOne is the builder for updating a single Order entity.
type OrderUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *OrderMutation
}

// SetOrderNumber sets the "order_number" field.
func (ouo *OrderUpdateOne) SetOrderNumber(s string) *OrderUpdateOne {
	ouo.mutation.SetOrderNumber(s)
	return ouo
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableOrderNumber(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetOrderNumber(*s)
	}
	return ouo
}

// SetUserID sets the "user_id" field.
func (ouo *OrderUpdateOne) SetUserID(u uuid.UUID) *OrderUpdateOne {
	ouo.mutation.SetUserID(u)
	return ouo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableUserID(u *uuid.UUID) *OrderUpdateOne {
	if u != nil {
		ouo.SetUserID(*u)
	}
	return ouo
}

// SetPackageID sets the "package_id" field.
func (ouo *OrderUpdateOne) SetPackageID(u uuid.UUID) *OrderUpdateOne {
	ouo.mutation.SetPackageID(u)
	return ouo
}

// SetNillablePackageID sets the "package_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillablePackageID(u *uuid.UUID) *OrderUpdateOne {
	if u != nil {
		ouo.SetPackageID(*u)
	}
	return ouo
}

// ClearPackageID clears the value of the "package_id" field.
func (ouo *OrderUpdateOne) ClearPackageID() *OrderUpdateOne {
	ouo.mutation.ClearPackageID()
	return ouo
}

// SetPlanID sets the "plan_id" field.
func (ouo *OrderUpdateOne) SetPlanID(u uuid.UUID) *OrderUpdateOne {
	ouo.mutation.SetPlanID(u)
	return ouo
}

// SetNillablePlanID sets the "plan_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillablePlanID(u *uuid.UUID) *OrderUpdateOne {
	if u != nil {
		ouo.SetPlanID(*u)
	}
	return ouo
}

// ClearPlanID clears the value of the "plan_id" field.
func (ouo *OrderUpdateOne) ClearPlanID() *OrderUpdateOne {
	ouo.mutation.ClearPlanID()
	return ouo
}

// SetType sets the "type" field.
func (ouo *OrderUpdateOne) SetType(o order.Type) *OrderUpdateOne {
	ouo.mutation.SetType(o)
	return ouo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableType(o *order.Type) *OrderUpdateOne {
	if o != nil {
		ouo.SetType(*o)
	}
	return ouo
}

// SetAmount sets the "amount" field.
func (ouo *OrderUpdateOne) SetAmount(i int64) *OrderUpdateOne {
	ouo.mutation.ResetAmount()
	ouo.mutation.SetAmount(i)
	return ouo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableAmount(i *int64) *OrderUpdateOne {
	if i != nil {
		ouo.SetAmount(*i)
	}
	return ouo
}

// AddAmount adds i to the "amount" field.
func (ouo *OrderUpdateOne) AddAmount(i int64) *OrderUpdateOne {
	ouo.mutation.AddAmount(i)
	return ouo
}

// SetCurrency sets the "currency" field.
func (ouo *OrderUpdateOne) SetCurrency(s string) *OrderUpdateOne {
	ouo.mutation.SetCurrency(s)
	return ouo
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCurrency(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetCurrency(*s)
	}
	return ouo
}

// SetStatus sets the "status" field.
func (ouo *OrderUpdateOne) SetStatus(o order.Status) *OrderUpdateOne {
	ouo.mutation.SetStatus(o)
	return ouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableStatus(o *order.Status) *OrderUpdateOne {
	if o != nil {
		ouo.SetStatus(*o)
	}
	return ouo
}

// SetPaymentMethod sets the "payment_method" field.
func (ouo *OrderUpdateOne) SetPaymentMethod(s string) *OrderUpdateOne {
	ouo.mutation.SetPaymentMethod(s)
	return ouo
}

// SetNillablePaymentMethod sets the "payment_method" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillablePaymentMethod(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetPaymentMethod(*s)
	}
	return ouo
}

// SetPaymentID sets the "payment_id" field.
func (ouo *OrderUpdateOne) SetPaymentID(s string) *OrderUpdateOne {
	ouo.mutation.SetPaymentID(s)
	return ouo
}

// SetNillablePaymentID sets the "payment_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillablePaymentID(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetPaymentID(*s)
	}
	return ouo
}

// ClearPaymentID clears the value of the "payment_id" field.
func (ouo *OrderUpdateOne) ClearPaymentID() *OrderUpdateOne {
	ouo.mutation.ClearPaymentID()
	return ouo
}

// SetInvoiceID sets the "invoice_id" field.
func (ouo *OrderUpdateOne) SetInvoiceID(s string) *OrderUpdateOne {
	ouo.mutation.SetInvoiceID(s)
	return ouo
}

// SetNillableInvoiceID sets the "invoice_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableInvoiceID(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetInvoiceID(*s)
	}
	return ouo
}

// ClearInvoiceID clears the value of the "invoice_id" field.
func (ouo *OrderUpdateOne) ClearInvoiceID() *OrderUpdateOne {
	ouo.mutation.ClearInvoiceID()
	return ouo
}

// SetCredits sets the "credits" field.
func (ouo *OrderUpdateOne) SetCredits(i int) *OrderUpdateOne {
	ouo.mutation.ResetCredits()
	ouo.mutation.SetCredits(i)
	return ouo
}

// SetNillableCredits sets the "credits" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCredits(i *int) *OrderUpdateOne {
	if i != nil {
		ouo.SetCredits(*i)
	}
	return ouo
}

// AddCredits adds i to the "credits" field.
func (ouo *OrderUpdateOne) AddCredits(i int) *OrderUpdateOne {
	ouo.mutation.AddCredits(i)
	return ouo
}

// SetBillingAddress sets the "billing_address" field.
func (ouo *OrderUpdateOne) SetBillingAddress(m map[string]interface{}) *OrderUpdateOne {
	ouo.mutation.SetBillingAddress(m)
	return ouo
}

// ClearBillingAddress clears the value of the "billing_address" field.
func (ouo *OrderUpdateOne) ClearBillingAddress() *OrderUpdateOne {
	ouo.mutation.ClearBillingAddress()
	return ouo
}

// SetItems sets the "items" field.
func (ouo *OrderUpdateOne) SetItems(m []map[string]interface{}) *OrderUpdateOne {
	ouo.mutation.SetItems(m)
	return ouo
}

// AppendItems appends m to the "items" field.
func (ouo *OrderUpdateOne) AppendItems(m []map[string]interface{}) *OrderUpdateOne {
	ouo.mutation.AppendItems(m)
	return ouo
}

// ClearItems clears the value of the "items" field.
func (ouo *OrderUpdateOne) ClearItems() *OrderUpdateOne {
	ouo.mutation.ClearItems()
	return ouo
}

// SetMetadata sets the "metadata" field.
func (ouo *OrderUpdateOne) SetMetadata(m map[string]interface{}) *OrderUpdateOne {
	ouo.mutation.SetMetadata(m)
	return ouo
}

// ClearMetadata clears the value of the "metadata" field.
func (ouo *OrderUpdateOne) ClearMetadata() *OrderUpdateOne {
	ouo.mutation.ClearMetadata()
	return ouo
}

// SetProcessedAt sets the "processed_at" field.
func (ouo *OrderUpdateOne) SetProcessedAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetProcessedAt(t)
	return ouo
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableProcessedAt(t *time.Time) *OrderUpdateOne {
	if t != nil {
		ouo.SetProcessedAt(*t)
	}
	return ouo
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ouo *OrderUpdateOne) ClearProcessedAt() *OrderUpdateOne {
	ouo.mutation.ClearProcessedAt()
	return ouo
}

// SetExpiresAt sets the "expires_at" field.
func (ouo *OrderUpdateOne) SetExpiresAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetExpiresAt(t)
	return ouo
}

// SetNillableExpiresAt sets the "expires_at" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableExpiresAt(t *time.Time) *OrderUpdateOne {
	if t != nil {
		ouo.SetExpiresAt(*t)
	}
	return ouo
}

// ClearExpiresAt clears the value of the "expires_at" field.
func (ouo *OrderUpdateOne) ClearExpiresAt() *OrderUpdateOne {
	ouo.mutation.ClearExpiresAt()
	return ouo
}

// SetFailureReason sets the "failure_reason" field.
func (ouo *OrderUpdateOne) SetFailureReason(s string) *OrderUpdateOne {
	ouo.mutation.SetFailureReason(s)
	return ouo
}

// SetNillableFailureReason sets the "failure_reason" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableFailureReason(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetFailureReason(*s)
	}
	return ouo
}

// ClearFailureReason clears the value of the "failure_reason" field.
func (ouo *OrderUpdateOne) ClearFailureReason() *OrderUpdateOne {
	ouo.mutation.ClearFailureReason()
	return ouo
}

// SetExternalOrderID sets the "external_order_id" field.
func (ouo *OrderUpdateOne) SetExternalOrderID(s string) *OrderUpdateOne {
	ouo.mutation.SetExternalOrderID(s)
	return ouo
}

// SetNillableExternalOrderID sets the "external_order_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableExternalOrderID(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetExternalOrderID(*s)
	}
	return ouo
}

// ClearExternalOrderID clears the value of the "external_order_id" field.
func (ouo *OrderUpdateOne) ClearExternalOrderID() *OrderUpdateOne {
	ouo.mutation.ClearExternalOrderID()
	return ouo
}

// SetUpdatedAt sets the "updated_at" field.
func (ouo *OrderUpdateOne) SetUpdatedAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetUpdatedAt(t)
	return ouo
}

// Mutation returns the OrderMutation object of the builder.
func (ouo *OrderUpdateOne) Mutation() *OrderMutation {
	return ouo.mutation
}

// Where appends a list predicates to the OrderUpdate builder.
func (ouo *OrderUpdateOne) Where(ps ...predicate.Order) *OrderUpdateOne {
	ouo.mutation.Where(ps...)
	return ouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouo *OrderUpdateOne) Select(field string, fields ...string) *OrderUpdateOne {
	ouo.fields = append([]string{field}, fields...)
	return ouo
}

// Save executes the query and returns the updated Order entity.
func (ouo *OrderUpdateOne) Save(ctx context.Context) (*Order, error) {
	ouo.defaults()
	return withHooks(ctx, ouo.sqlSave, ouo.mutation, ouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouo *OrderUpdateOne) SaveX(ctx context.Context) *Order {
	node, err := ouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouo *OrderUpdateOne) Exec(ctx context.Context) error {
	_, err := ouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouo *OrderUpdateOne) ExecX(ctx context.Context) {
	if err := ouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouo *OrderUpdateOne) defaults() {
	if _, ok := ouo.mutation.UpdatedAt(); !ok {
		v := order.UpdateDefaultUpdatedAt()
		ouo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ouo *OrderUpdateOne) check() error {
	if v, ok := ouo.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`ent: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.GetType(); ok {
		if err := order.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Order.type": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.Amount(); ok {
		if err := order.AmountValidator(v); err != nil {
			return &ValidationError{Name: "amount", err: fmt.Errorf(`ent: validator failed for field "Order.amount": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.Currency(); ok {
		if err := order.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Order.currency": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Order.status": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.PaymentMethod(); ok {
		if err := order.PaymentMethodValidator(v); err != nil {
			return &ValidationError{Name: "payment_method", err: fmt.Errorf(`ent: validator failed for field "Order.payment_method": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.PaymentID(); ok {
		if err := order.PaymentIDValidator(v); err != nil {
			return &ValidationError{Name: "payment_id", err: fmt.Errorf(`ent: validator failed for field "Order.payment_id": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.InvoiceID(); ok {
		if err := order.InvoiceIDValidator(v); err != nil {
			return &ValidationError{Name: "invoice_id", err: fmt.Errorf(`ent: validator failed for field "Order.invoice_id": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.Credits(); ok {
		if err := order.CreditsValidator(v); err != nil {
			return &ValidationError{Name: "credits", err: fmt.Errorf(`ent: validator failed for field "Order.credits": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.FailureReason(); ok {
		if err := order.FailureReasonValidator(v); err != nil {
			return &ValidationError{Name: "failure_reason", err: fmt.Errorf(`ent: validator failed for field "Order.failure_reason": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.ExternalOrderID(); ok {
		if err := order.ExternalOrderIDValidator(v); err != nil {
			return &ValidationError{Name: "external_order_id", err: fmt.Errorf(`ent: validator failed for field "Order.external_order_id": %w`, err)}
		}
	}
	return nil
}

func (ouo *OrderUpdateOne) sqlSave(ctx context.Context) (_node *Order, err error) {
	if err := ouo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(order.Table, order.Columns, sqlgraph.NewFieldSpec(order.FieldID, field.TypeUUID))
	id, ok := ouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Order.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, order.FieldID)
		for _, f := range fields {
			if !order.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != order.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouo.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
	}
	if value, ok := ouo.mutation.UserID(); ok {
		_spec.SetField(order.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := ouo.mutation.PackageID(); ok {
		_spec.SetField(order.FieldPackageID, field.TypeUUID, value)
	}
	if ouo.mutation.PackageIDCleared() {
		_spec.ClearField(order.FieldPackageID, field.TypeUUID)
	}
	if value, ok := ouo.mutation.PlanID(); ok {
		_spec.SetField(order.FieldPlanID, field.TypeUUID, value)
	}
	if ouo.mutation.PlanIDCleared() {
		_spec.ClearField(order.FieldPlanID, field.TypeUUID)
	}
	if value, ok := ouo.mutation.GetType(); ok {
		_spec.SetField(order.FieldType, field.TypeEnum, value)
	}
	if value, ok := ouo.mutation.Amount(); ok {
		_spec.SetField(order.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.AddedAmount(); ok {
		_spec.AddField(order.FieldAmount, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.Currency(); ok {
		_spec.SetField(order.FieldCurrency, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ouo.mutation.PaymentMethod(); ok {
		_spec.SetField(order.FieldPaymentMethod, field.TypeString, value)
	}
	if value, ok := ouo.mutation.PaymentID(); ok {
		_spec.SetField(order.FieldPaymentID, field.TypeString, value)
	}
	if ouo.mutation.PaymentIDCleared() {
		_spec.ClearField(order.FieldPaymentID, field.TypeString)
	}
	if value, ok := ouo.mutation.InvoiceID(); ok {
		_spec.SetField(order.FieldInvoiceID, field.TypeString, value)
	}
	if ouo.mutation.InvoiceIDCleared() {
		_spec.ClearField(order.FieldInvoiceID, field.TypeString)
	}
	if value, ok := ouo.mutation.Credits(); ok {
		_spec.SetField(order.FieldCredits, field.TypeInt, value)
	}
	if value, ok := ouo.mutation.AddedCredits(); ok {
		_spec.AddField(order.FieldCredits, field.TypeInt, value)
	}
	if value, ok := ouo.mutation.BillingAddress(); ok {
		_spec.SetField(order.FieldBillingAddress, field.TypeJSON, value)
	}
	if ouo.mutation.BillingAddressCleared() {
		_spec.ClearField(order.FieldBillingAddress, field.TypeJSON)
	}
	if value, ok := ouo.mutation.Items(); ok {
		_spec.SetField(order.FieldItems, field.TypeJSON, value)
	}
	if value, ok := ouo.mutation.AppendedItems(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, order.FieldItems, value)
		})
	}
	if ouo.mutation.ItemsCleared() {
		_spec.ClearField(order.FieldItems, field.TypeJSON)
	}
	if value, ok := ouo.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
	}
	if ouo.mutation.MetadataCleared() {
		_spec.ClearField(order.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ouo.mutation.ProcessedAt(); ok {
		_spec.SetField(order.FieldProcessedAt, field.TypeTime, value)
	}
	if ouo.mutation.ProcessedAtCleared() {
		_spec.ClearField(order.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.ExpiresAt(); ok {
		_spec.SetField(order.FieldExpiresAt, field.TypeTime, value)
	}
	if ouo.mutation.ExpiresAtCleared() {
		_spec.ClearField(order.FieldExpiresAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.FailureReason(); ok {
		_spec.SetField(order.FieldFailureReason, field.TypeString, value)
	}
	if ouo.mutation.FailureReasonCleared() {
		_spec.ClearField(order.FieldFailureReason, field.TypeString)
	}
	if value, ok := ouo.mutation.ExternalOrderID(); ok {
		_spec.SetField(order.FieldExternalOrderID, field.TypeString, value)
	}
	if ouo.mutation.ExternalOrderIDCleared() {
		_spec.ClearField(order.FieldExternalOrderID, field.TypeString)
	}
	if value, ok := ouo.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &Order{config: ouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{order.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouo.mutation.done = true
	return _node, nil
}

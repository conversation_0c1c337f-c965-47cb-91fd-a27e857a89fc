// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/social-content-ai/notification-service/ent/notificationtemplate"
)

// NotificationTemplate is the model entity for the NotificationTemplate schema.
type NotificationTemplate struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// Event type this template is for
	EventType string `json:"event_type,omitempty"`
	// Template name
	Name string `json:"name,omitempty"`
	// Template description
	Description string `json:"description,omitempty"`
	// Channel-specific templates (email, sms, push, etc.)
	Templates map[string]interface{} `json:"templates,omitempty"`
	// Whether the template is active
	IsActive bool `json:"is_active,omitempty"`
	// List of variables used in the template
	Variables []string `json:"variables,omitempty"`
	// Default channels for this template
	DefaultChannels []string `json:"default_channels,omitempty"`
	// Default priority for notifications using this template
	DefaultPriority notificationtemplate.DefaultPriority `json:"default_priority,omitempty"`
	// Template language
	Language string `json:"language,omitempty"`
	// Template version
	Version string `json:"version,omitempty"`
	// Additional template metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// User who created the template
	CreatedBy string `json:"created_by,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*NotificationTemplate) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notificationtemplate.FieldTemplates, notificationtemplate.FieldVariables, notificationtemplate.FieldDefaultChannels, notificationtemplate.FieldMetadata:
			values[i] = new([]byte)
		case notificationtemplate.FieldIsActive:
			values[i] = new(sql.NullBool)
		case notificationtemplate.FieldEventType, notificationtemplate.FieldName, notificationtemplate.FieldDescription, notificationtemplate.FieldDefaultPriority, notificationtemplate.FieldLanguage, notificationtemplate.FieldVersion, notificationtemplate.FieldCreatedBy:
			values[i] = new(sql.NullString)
		case notificationtemplate.FieldCreatedAt, notificationtemplate.FieldUpdatedAt, notificationtemplate.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case notificationtemplate.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the NotificationTemplate fields.
func (nt *NotificationTemplate) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notificationtemplate.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				nt.ID = *value
			}
		case notificationtemplate.FieldEventType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field event_type", values[i])
			} else if value.Valid {
				nt.EventType = value.String
			}
		case notificationtemplate.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				nt.Name = value.String
			}
		case notificationtemplate.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				nt.Description = value.String
			}
		case notificationtemplate.FieldTemplates:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field templates", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nt.Templates); err != nil {
					return fmt.Errorf("unmarshal field templates: %w", err)
				}
			}
		case notificationtemplate.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				nt.IsActive = value.Bool
			}
		case notificationtemplate.FieldVariables:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field variables", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nt.Variables); err != nil {
					return fmt.Errorf("unmarshal field variables: %w", err)
				}
			}
		case notificationtemplate.FieldDefaultChannels:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field default_channels", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nt.DefaultChannels); err != nil {
					return fmt.Errorf("unmarshal field default_channels: %w", err)
				}
			}
		case notificationtemplate.FieldDefaultPriority:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field default_priority", values[i])
			} else if value.Valid {
				nt.DefaultPriority = notificationtemplate.DefaultPriority(value.String)
			}
		case notificationtemplate.FieldLanguage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field language", values[i])
			} else if value.Valid {
				nt.Language = value.String
			}
		case notificationtemplate.FieldVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				nt.Version = value.String
			}
		case notificationtemplate.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nt.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case notificationtemplate.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				nt.CreatedBy = value.String
			}
		case notificationtemplate.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				nt.CreatedAt = value.Time
			}
		case notificationtemplate.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				nt.UpdatedAt = value.Time
			}
		case notificationtemplate.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				nt.DeletedAt = value.Time
			}
		default:
			nt.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the NotificationTemplate.
// This includes values selected through modifiers, order, etc.
func (nt *NotificationTemplate) Value(name string) (ent.Value, error) {
	return nt.selectValues.Get(name)
}

// Update returns a builder for updating this NotificationTemplate.
// Note that you need to call NotificationTemplate.Unwrap() before calling this method if this NotificationTemplate
// was returned from a transaction, and the transaction was committed or rolled back.
func (nt *NotificationTemplate) Update() *NotificationTemplateUpdateOne {
	return NewNotificationTemplateClient(nt.config).UpdateOne(nt)
}

// Unwrap unwraps the NotificationTemplate entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (nt *NotificationTemplate) Unwrap() *NotificationTemplate {
	_tx, ok := nt.config.driver.(*txDriver)
	if !ok {
		panic("ent: NotificationTemplate is not a transactional entity")
	}
	nt.config.driver = _tx.drv
	return nt
}

// String implements the fmt.Stringer.
func (nt *NotificationTemplate) String() string {
	var builder strings.Builder
	builder.WriteString("NotificationTemplate(")
	builder.WriteString(fmt.Sprintf("id=%v, ", nt.ID))
	builder.WriteString("event_type=")
	builder.WriteString(nt.EventType)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(nt.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(nt.Description)
	builder.WriteString(", ")
	builder.WriteString("templates=")
	builder.WriteString(fmt.Sprintf("%v", nt.Templates))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", nt.IsActive))
	builder.WriteString(", ")
	builder.WriteString("variables=")
	builder.WriteString(fmt.Sprintf("%v", nt.Variables))
	builder.WriteString(", ")
	builder.WriteString("default_channels=")
	builder.WriteString(fmt.Sprintf("%v", nt.DefaultChannels))
	builder.WriteString(", ")
	builder.WriteString("default_priority=")
	builder.WriteString(fmt.Sprintf("%v", nt.DefaultPriority))
	builder.WriteString(", ")
	builder.WriteString("language=")
	builder.WriteString(nt.Language)
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(nt.Version)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", nt.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(nt.CreatedBy)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(nt.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(nt.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(nt.DeletedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// NotificationTemplates is a parsable slice of NotificationTemplate.
type NotificationTemplates []*NotificationTemplate

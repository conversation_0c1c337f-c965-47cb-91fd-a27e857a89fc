# Notification Service

Notification Service là một microservice chuyên xử lý việc gửi thông báo đa kênh trong hệ thống Social Content AI. Service hỗ trợ gửi thông báo qua email, SMS, push notification, WebSocket và in-app notifications.

## 🚀 Tính năng chính

### ✅ Multi-Channel Notifications
- **Email** - SMTP integration với template support
- **SMS** - Provider integration (Twilio, etc.)
- **Push Notifications** - Firebase/APNS integration
- **WebSocket** - Real-time notifications
- **In-App** - Database-stored notifications

### ✅ Template System
- Dynamic template rendering với variables
- Multi-language support
- Template versioning và management
- Preview và testing capabilities

### ✅ User Preferences
- Channel-specific preferences
- Event-type preferences
- Quiet hours configuration
- Language và timezone settings

### ✅ Event-Driven Architecture
- Kafka integration cho event processing
- Multiple topic support
- Dead letter queue handling
- Retry mechanisms

### ✅ Real-time Features
- WebSocket connections management
- User-specific notifications
- Broadcasting capabilities
- Connection pooling

### ✅ Analytics & Monitoring
- Delivery tracking và status
- Performance metrics
- User engagement analytics
- System health monitoring

## 🏗️ Architecture

### Database Support
- **PostgreSQL** - Production database
- **SQLite** - Development và testing
- Automatic migrations
- Multi-database pattern

### API Interfaces
- **gRPC** - Inter-service communication
- **REST API** - External integrations
- **WebSocket** - Real-time connections
- **Webhooks** - Delivery status callbacks

### Configuration
- Environment-based configuration
- YAML config files
- Environment variable overrides
- Feature flags support

## 📦 Installation

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- PostgreSQL hoặc SQLite
- Kafka (optional, for event processing)

### Development Setup

1. **Clone repository**
```bash
git clone <repository-url>
cd notification-service
```

2. **Install dependencies**
```bash
go mod download
```

3. **Setup configuration**
```bash
cp config/development.yaml.example config/development.yaml
# Edit config/development.yaml với settings của bạn
```

4. **Run with Docker Compose**
```bash
docker-compose up -d
```

5. **Run locally**
```bash
go run main.go
```

### Production Deployment

1. **Build Docker image**
```bash
docker build -t notification-service:latest .
```

2. **Deploy với environment variables**
```bash
docker run -d \
  -p 8086:8086 \
  -p 50056:50056 \
  -e DATABASE_TYPE=postgres \
  -e DATABASE_HOST=your-postgres-host \
  -e DATABASE_NAME=notification_db \
  -e DATABASE_USER=your-user \
  -e DATABASE_PASSWORD=your-password \
  notification-service:latest
```

## 🔧 Configuration

### Database Configuration
```yaml
database:
  type: postgres  # postgres hoặc sqlite
  host: localhost
  port: 5432
  name: notification_service_db
  user: postgres
  password: postgres
  ssl_mode: disable
  
  # SQLite configuration
  sqlite_path: ./data/notification_service.db
```

### Email Configuration
```yaml
email:
  smtp_host: smtp.gmail.com
  smtp_port: 587
  username: <EMAIL>
  password: your-app-password
  from_email: <EMAIL>
  from_name: Social Content AI
  use_tls: true
```

### Kafka Configuration
```yaml
kafka:
  brokers:
    - localhost:9092
  group_id: notification-service
  topics:
    notification_events: notification.events
    user_events: user.events
    content_events: content.events
```

## 📚 API Documentation

### REST API Endpoints

#### Notifications
- `POST /api/v1/notifications` - Send notification
- `POST /api/v1/notifications/templated` - Send templated notification
- `POST /api/v1/notifications/bulk` - Send bulk notifications
- `GET /api/v1/notifications` - List notifications
- `GET /api/v1/notifications/:id` - Get notification
- `PUT /api/v1/notifications/:id/read` - Mark as read
- `DELETE /api/v1/notifications/:id` - Delete notification

#### Preferences
- `GET /api/v1/preferences` - Get user preferences
- `PUT /api/v1/preferences` - Update preferences
- `POST /api/v1/preferences/reset` - Reset to defaults

#### Templates
- `GET /api/v1/templates` - List templates
- `POST /api/v1/templates` - Create template
- `GET /api/v1/templates/:id` - Get template
- `PUT /api/v1/templates/:id` - Update template
- `DELETE /api/v1/templates/:id` - Delete template

#### WebSocket
- `GET /ws` - WebSocket connection endpoint

### gRPC API

Service được định nghĩa trong `proto-shared/notification/v1/notification.proto`:

```protobuf
service NotificationService {
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc SendTemplatedNotification(SendTemplatedNotificationRequest) returns (SendNotificationResponse);
  rpc SendBulkNotification(SendBulkNotificationRequest) returns (SendBulkNotificationResponse);
  rpc GetNotification(GetNotificationRequest) returns (NotificationResponse);
  rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse);
  rpc MarkAsRead(MarkAsReadRequest) returns (google.protobuf.Empty);
  rpc GetDeliveryStatus(GetDeliveryStatusRequest) returns (DeliveryStatusResponse);
}
```

## 🔄 Event Processing

Service lắng nghe các events từ Kafka topics:

### Supported Events
- `user.events` - User lifecycle events
- `content.events` - Content generation events
- `billing.events` - Payment và subscription events
- `notification.events` - Direct notification commands

### Event Examples

**User Registered Event**
```json
{
  "event_type": "user_registered",
  "user_id": "user-123",
  "data": {
    "user_name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

**Content Generated Event**
```json
{
  "event_type": "content_generated",
  "user_id": "user-123",
  "data": {
    "content_id": "content-456",
    "content_title": "My Blog Post",
    "content_type": "blog"
  }
}
```

## 🧪 Testing

### Unit Tests
```bash
go test ./...
```

### Integration Tests
```bash
go test -tags=integration ./...
```

### Load Testing
```bash
# Sử dụng k6 hoặc artillery
k6 run tests/load/notification-load-test.js
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:8086/health
```

### Metrics
- Prometheus metrics: `http://localhost:9096/metrics`
- Grafana dashboard: `http://localhost:3000`

### Key Metrics
- Notification delivery rate
- Channel performance
- Template rendering time
- WebSocket connection count
- Queue processing latency

## 🔒 Security

### Authentication
- JWT token validation
- User-specific data access
- Admin role permissions

### Rate Limiting
- Per-user rate limits
- Channel-specific limits
- Burst protection

### Data Protection
- Encrypted sensitive data
- Audit logging
- GDPR compliance features

## 🚀 Performance

### Optimizations
- Connection pooling
- Template caching
- Batch processing
- Async delivery

### Scalability
- Horizontal scaling support
- Load balancing ready
- Database read replicas
- Queue partitioning

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Implement changes với tests
4. Submit pull request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 📞 Support

- Documentation: [Wiki](link-to-wiki)
- Issues: [GitHub Issues](link-to-issues)
- Slack: #notification-service
